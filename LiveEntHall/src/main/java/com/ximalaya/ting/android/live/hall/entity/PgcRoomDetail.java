package com.ximalaya.ting.android.live.hall.entity;


import static com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType.TYPE_PGC;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo.FansGroupStatusCode.TYPE_JOINED;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.framework.util.XmPictureUrlUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.lib.stream.data.IPlaySourceInfo;

import org.json.JSONObject;

import java.util.List;

/**
 * 娱乐派对直播间详细信息。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2019/4/2
 */
public class PgcRoomDetail extends BaseRoomDetail implements IPlaySourceInfo {
    /**
     * 玩法介绍
     */
    public String ruleInfo;
    /**
     * 聊天室 id
     */
    public long chatId;
    /**
     * 房间号
     */
    public long fmId;
    /**
     * 房主 id
     */
    public long roomUid;
    /**
     * 房主昵称
     */
    public String masterNicName;
    /**
     * 房主头像
     */
    public String uidAvater;
    /**
     * 娱乐厅名称
     */
    public String title;
    /**
     * 直播封面(大)
     */
    public String largeCoverUrl;
    /**
     * 直播封面(中)
     */
    public String middleCoverUrl;
    /**
     * 直播封面(小)
     */
    public String smallCoverUrl;
    /**
     * 直播封面(原)
     */
    public String coverPath;
    /**
     * 封面评分提示
     */
    public String coverQualityLevelTips;
    /**
     * 在线人数
     */
    public int onlineCount;
    /**
     * 热度值
     */
    public int hotNum;

    /**
     * 直播间背景图片
     */
    public String bgImagePath;

    /**
     * 娱乐厅喜爱值
     */
    public long xiaizhi;

    /**
     * 1 代表 PGC 聊天室，6代表 UGC 直播间
     */
    public int mode;

    /**
     * 房间贵族信息
     */
    public NobleClubVo nobleClubVo;

    /**
     * 房间相册信息
     */
    public List<EntRoomAlbumTitle> photoAlbumTitles;

    /**
     * 房间拉流地址
     */
    public StreamUrls streamUrls;

    /**
     * 粉丝团信息
     */
    public LiveUserInfo.FansClubVoBean fansClubVo;

    public PgcRoomDetail() {

    }

    public PgcRoomDetail(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);

            if (jsonObject.has("ruleInfo")) {
                this.ruleInfo = jsonObject.optString("ruleInfo");
            }

            if (jsonObject.has("chatId")) {
                this.chatId = jsonObject.optLong("chatId");
            }

            if (jsonObject.has("fmId")) {
                this.fmId = jsonObject.optLong("fmId");
            }

            if (jsonObject.has("roomId")) {
                this.roomId = jsonObject.optLong("roomId");
            }

            if (jsonObject.has("roomUid")) {
                this.roomUid = jsonObject.optLong("roomUid");
            }

            if (jsonObject.has("masterNicName")) {
                this.masterNicName = jsonObject.optString("masterNicName");
            }

            if (jsonObject.has("title")) {
                this.title = jsonObject.optString("title");
            }
            if (jsonObject.has("largeCoverUrl")) {
                this.largeCoverUrl = jsonObject.optString("largeCoverUrl");
            }
            if (jsonObject.has("middleCoverUrl")) {
                this.middleCoverUrl = jsonObject.optString("middleCoverUrl");
            }
            if (jsonObject.has("smallCoverUrl")) {
                this.smallCoverUrl = jsonObject.optString("smallCoverUrl");
            }
            if (jsonObject.has("onlineCount")) {
                this.onlineCount = jsonObject.optInt("onlineCount");
            }

            if (jsonObject.has("hotNum")) {
                this.hotNum = jsonObject.optInt("hotNum");
            }

            if (jsonObject.has("bgImagePath")) {
                this.bgImagePath = jsonObject.optString("bgImagePath");
            }

            if (jsonObject.has("coverPath")) {
                this.coverPath = jsonObject.optString("coverPath");
            }

            if (jsonObject.has("coverQualityLevelTips")) {
                this.coverQualityLevelTips = jsonObject.optString("coverQualityLevelTips");
            }

            if (jsonObject.has("xiaizhi")) {
                this.xiaizhi = jsonObject.optLong("xiaizhi");
            }

            if (jsonObject.has("mode")) {
                this.mode = jsonObject.optInt("mode");
            }

            if (jsonObject.has("nobleClubVo")) {
                this.nobleClubVo = new NobleClubVo(jsonObject.optString("nobleClubVo"));
            }
            if (jsonObject.has("fansClubVo")) {
                this.fansClubVo = new LiveUserInfo.FansClubVoBean(jsonObject.optString("fansClubVo"));
            }

            if (jsonObject.has("uidAvater")) {
                this.uidAvater = jsonObject.optString("uidAvater");
            }

            if (jsonObject.has("fansRankUrl")) {
                this.fansRankUrl = jsonObject.optString("fansRankUrl");
            }

            if (jsonObject.has("photoAlbumTitles")) {
                this.photoAlbumTitles = LiveGsonUtils.sGson.fromJson(
                        jsonObject.optString("photoAlbumTitles"),
                        new TypeToken<List<EntRoomAlbumTitle>>() {
                        }.getType()
                );
            }

            if (jsonObject.has("audiencePlayResp")) {
                this.streamUrls = LiveGsonUtils.sGson.fromJson(jsonObject.optString("audiencePlayResp"), StreamUrls.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 在线贵族信息
     */
    public static class NobleClubVo {
        /**
         * 直播间内当前贵族数量
         */
        public int count;

        public NobleClubVo(String s) {
            try {
                JSONObject jsonObject = new JSONObject(s);
                count = jsonObject.optInt("count");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @NonNull
        @Override
        public String toString() {
            return "NobleClubVo{count=" + count + "}";
        }
    }

    @Override
    public long getStreamUid() {
        return roomUid;
    }

    @Override
    public String getHostNickname() {
        return masterNicName;
    }

    @Override
    public String getHostAvatar() {
        return uidAvater;
    }

    @Override
    public String largeCoverUrl() {
        return largeCoverUrl;
    }

    @Override
    public String middleCoverUrl() {
        return middleCoverUrl;
    }

    @Override
    public String smallCoverUrl() {
        return smallCoverUrl;
    }

    @Override
    public String title() {
        return title;
    }

    @Override
    public String trackInfo() {
        return ruleInfo;
    }

    @Override
    public long getRoomId() {
        return roomId;
    }

    @Override
    public long getLiveId() {
        return -1;
    }

    @Override
    @BaseScrollConstant.LiveRoomBizType
    public int getLiveType() {
        return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC;
    }

    @Override
    public long getChatId() {
        return chatId;
    }

    @Override
    public long getHostUid() {
        return roomUid;
    }

    @Override
    public String getAnchorName() {
        return masterNicName;
    }

    @Override
    @PersonLiveBase.LiveStatus
    public int getStatus() {
        return PersonLiveBase.LIVE_STATUS_ING;
    }

    @Override
    public String getAnchorAvatar() {
        return uidAvater;
    }

    @Override
    public String getLargeAvatar() {
        return uidAvater;
    }

    @Override
    public String getRoomTitle() {
        return title;
    }

    @Override
    public int getOnlineCount() {
        return onlineCount;
    }

    @Override
    public int getParticipateCount() {
        return hotNum;
    }

    @Override
    public String getSmallCoverPath() {
        return !TextUtils.isEmpty(smallCoverUrl) ? smallCoverUrl : "";
    }

    @Override
    public String getLargeCoverPath() {
        return !TextUtils.isEmpty(largeCoverUrl) ? largeCoverUrl : "";
    }

    @Override
    public String getBgImage() {
        return !TextUtils.isEmpty(bgImagePath) ? bgImagePath : "";
    }

    @Override
    public long getFMId() {
        return fmId;
    }

    @Override
    public int getMediaType() {
        return TYPE_PGC;
    }

    @Override
    @BaseScrollConstant.LiveRoomBizType
    public int getRoomBizType() {
        return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC;
    }

    @Override
    public int getRoomSubBizType() {
        return mode;
    }

    @Override
    public boolean isAnonymousRoom() {
        return false;
    }

    @Override
    public String getPlayUrl() {
        if (streamUrls == null || ToolUtil.isEmptyCollects(streamUrls.getFlvUrls())) {
            return "";
        }
        return streamUrls.getFlvUrls().get(0);
    }

    @Override
    public String getRoomNormalBackgroundUrl() {
        if (!TextUtils.isEmpty(getBgImage()) && !"mp4".equalsIgnoreCase(XmPictureUrlUtil.getFileSuffix(getBgImage()))) {
            return getBgImage();
        }
        return super.getRoomNormalBackgroundUrl();
    }

    @Override
    public String getRoomDynamicBackgroundUrl() {
        if (!TextUtils.isEmpty(getBgImage()) && "mp4".equalsIgnoreCase(XmPictureUrlUtil.getFileSuffix(getBgImage()))) {
            return getBgImage();
        }
        return super.getRoomDynamicBackgroundUrl();
    }

    @Override
    public String getRoomName() {
        return title;
    }

    public long getRoomUid() {
        return roomUid;
    }

    @Override
    public LiveUserInfo.FansClubVoBean getRoomFansClubVo() {
        return fansClubVo;
    }

    @Override
    public void updateFansClubCount(int count) {
        if (fansClubVo != null) {
            fansClubVo.setCount(count);
        }
    }

    @Override
    public void updateFansClubJoinStatus(boolean isJoined) {
        if (fansClubVo != null) {
            fansClubVo.setCode(TYPE_JOINED);
        }
    }

    @Override
    public void updateOnlineNobleCount(int count) {
        if (nobleClubVo != null) {
            nobleClubVo.count = count;
        }
    }
}
