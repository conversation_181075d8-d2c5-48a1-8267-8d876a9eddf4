package com.ximalaya.ting.android.live.hall.components.header;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveMoreLiveNotifyMsg.TYPE_LIVING;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType.TYPE_FULL_HEADER;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType.TYPE_TOP_TWO_HEADER;
import static com.ximalaya.ting.android.live.common.lib.entity.RevenueMedalType.FANS_CLUB;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.extension.ResourceExt;
import com.ximalaya.ting.android.host.util.ui.VerticalSlideUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.biz.mode.component.IComponentContainer;
import com.ximalaya.ting.android.live.biz.mode.component.IHeaderComponent;
import com.ximalaya.ting.android.live.biz.mode.data.CommonProtoConstant;
import com.ximalaya.ting.android.live.biz.mode.util.LiveCommonUtil;
import com.ximalaya.ting.android.live.common.component.noble.icons.LiveNobleIconMaker;
import com.ximalaya.ting.android.live.common.dialog.web.ProvideForH5CustomerDialogFragment;
import com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansClubStatusModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveMoreLiveNotifyMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.broadcast.LiveLocalBroadcastManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveContextUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveDateUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveFormatUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveNumberUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper;
import com.ximalaya.ting.android.live.common.view.CountdownRankTagView;
import com.ximalaya.ting.android.live.common.view.LiveCycleScrollTagView;
import com.ximalaya.ting.android.live.common.view.clearScreen.LiveClearScreenManager;
import com.ximalaya.ting.android.live.common.view.more.LiveMoreLiveView;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.live.hall.R;
import com.ximalaya.ting.android.live.hall.components2.BaseEntComponent;
import com.ximalaya.ting.android.live.hall.components2.IBaseEntHostInteraction;
import com.ximalaya.ting.android.live.hall.data.CommonRequestForLiveEnt;
import com.ximalaya.ting.android.live.hall.entity.PgcRoomDetail;
import com.ximalaya.ting.android.live.hall.entity.RoomPlayRuleInfo;
import com.ximalaya.ting.android.live.hall.entity.rank.RankDetail;
import com.ximalaya.ting.android.live.hall.fragment.IEntHallRoom;
import com.ximalaya.ting.android.live.hall.fragment.LivePGCRoomFragment;
import com.ximalaya.ting.android.live.hall.manager.ent.IEntMessageManager;
import com.ximalaya.ting.android.live.hall.view.dialog.EntSponsorBottomDialog;
import com.ximalaya.ting.android.live.hall.view.dialog.PgcRoomPlayRuleDialogFragment;
import com.ximalaya.ting.android.live.hall.view.rank.annotation.RankBackendType;
import com.ximalaya.ting.android.live.hall.view.rank.entity.RankGloryData;
import com.ximalaya.ting.android.live.host.components.header.IRoomHeaderComponent;
import com.ximalaya.ting.android.live.host.componentview.header.giftrank.RoomGiftRankLayout;
import com.ximalaya.ting.android.live.host.componentview.header.sponsor.RoomSponsorLayout;
import com.ximalaya.ting.android.live.host.fragment.morelive.MoreLiveDialogFragment;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.live.host.view.header.LiveAnchorHeaderView;
import com.ximalaya.ting.android.live.host.view.header.LiveHeaderType;
import com.ximalaya.ting.android.live.host.view.header.LiveRoomStatusView;
import com.ximalaya.ting.android.live.host.view.seat.ISeatGiftView;
import com.ximalaya.ting.android.live.host.view.seat.LiveSeatGiftWebpView;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatFansIntimacyMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansRankMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineStatusMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonFansGroupMsg;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.NotNull;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Locale;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * PGC 房间 header 组件。
 * <p>
 * TODO 娱乐派对和UGC聊天室热度数据，由客户端轮询改为服务端推送，参考个播方案
 *
 * <AUTHOR>
 */
public class PgcHeaderComponent extends BaseEntComponent implements IHeaderComponent, IRoomHeaderComponent, View.OnClickListener {

    private static final String TAG = "EntHeaderComponent";
    protected IEntHallRoom.IView mRootComponent;
    private LiveUserInfo mUserInfo;

    private View mContianerView;

    /**
     * 包含 UI 头部第三行 (红包) 的容器
     */
    private View mHeaderAllView;

    private TextView mFmNumberTv;

    private long mSponsorUid = 0L;

    private RoomSponsorLayout roomSponsorLayout;

    private LiveMoreLiveView mMoreLiveView;
    @Nullable
    private LiveMoreLiveNotifyMsg mMoreLiveInfo;

    private View mCloseView;
    private View mMiniView;
    /**
     * 小时榜轮播布局
     */
    private LiveCycleScrollTagView<View> mScrollHourlyRank;

    //已经收藏该娱乐厅
    private boolean alreadyFavorite;
    private boolean isRequestingOnlineCount;
    private IEntMessageManager mEntMessageManager;

    /**
     * 头部Pgc房间信息View
     */
    private LiveAnchorHeaderView mPgcRoomHeaderView;
    /**
     * 公告
     */
    private TextView mRoomPlayRuleView;
    /**
     * 粉丝团百人团弹出动画
     */
    private LiveSeatGiftWebpView mLiveentFansWebpView;
    /**
     * 在线榜单和冠名 parent view
     */
    private ConstraintLayout mLiveentHeaderIconCl;
    /**
     * p层
     */
    private EntHeaderPresenter entHeaderPresenter;

    private RoomGiftRankLayout roomGiftRankLayout;

    /**
     * 当前用户类型
     */
    private int onlineMicUserType = -1;
    /**
     * 在线榜人数
     */
    private int rankSize = 0;

    /**
     * 粉丝团 百千人阈值
     */
    private static final int LIVE_FANS_100 = 100;

    private static final int LIVE_FANS_1000 = 1000;

    private final LiveNobleIconMaker liveNobleIconMaker = new LiveNobleIconMaker();

    /**
     * 粉丝百、千团动画地址
     */
    private String liveFans100PopUrl;
    /**
     * 粉丝团 百千团人数
     */
    private int liveFansPopNumber;

    @Override
    public void onCreate() {
        super.onCreate();
        mEntMessageManager = (IEntMessageManager) getManager(IEntMessageManager.NAME);
        LiveLocalBroadcastManager.register(
                LiveLocalBroadcastManager.ACTION.UPDATE_FAVORITE_STATE, mFavoriteStateReceiver
        );
        mRootComponent = (IEntHallRoom.IView) mFragment;
    }

    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        LiveClearScreenManager.getInstance().addClearViews(view);
        entHeaderPresenter = new EntHeaderPresenter(this, (BaseFragment2) getFragment());
        initView(view);
        LiveViewPositionManager.getInstance().registerLayoutChangeListener(
                TYPE_TOP_TWO_HEADER, mContianerView
        );
        LiveViewPositionManager.getInstance().registerLayoutChangeListener(
                TYPE_FULL_HEADER, mHeaderAllView
        );
    }

    protected void initView(View view) {
        mContianerView = view;
        mHeaderAllView = findViewById(R.id.liveent_room_header_all);

        mPgcRoomHeaderView = findViewById(R.id.live_room_info);
        mPgcRoomHeaderView.bindHeaderOnClickListener(this);
        mPgcRoomHeaderView.updateContentByHeaderType(LiveHeaderType.HEADER_TYPE_PGC);

        mFmNumberTv = findViewById(R.id.live_ent_room_fm_number);

        roomSponsorLayout = findViewById(R.id.live_sponsor_head_layout);
        roomSponsorLayout.setOnClickListener(this);
        roomSponsorLayout.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);

        mMoreLiveView = findViewById(R.id.live_ent_header_more_live);
        if (mMoreLiveView != null) {
            mMoreLiveView.setEventListener(new LiveMoreLiveView.MoreLiveViewListener() {
                @Override
                public boolean canShowGuide() {
                    if (getRoomCore() == null) {
                        return false;
                    }
                    return getRoomCore().getRoomCoreStayTime() > LiveMoreLiveView.COMMON_DELAY;
                }

                @Override
                public void onPresentInfoChanged(@Nullable LiveMoreLiveNotifyMsg msg) {
                    mMoreLiveInfo = msg;
                }

                @Override
                public void onClick() {
                    if (mMoreLiveInfo == null || TextUtils.isEmpty(mMoreLiveInfo.getIting())) {
                        clickMoreLive();
                    } else {
                        LiveCommonITingUtil.handleITing(getActivity(), mMoreLiveInfo.getIting());
                    }
                }
            });
        }

        mLiveentHeaderIconCl = findViewById(R.id.liveent_header_icon_cl);
        mLiveentFansWebpView = findViewById(R.id.liveent_right_fans_webp_pop);
        mLiveentFansWebpView.setISeatGiftView(new ISeatGiftView() {
            @Override
            public void playFinish() {
                mLiveentFansWebpView.setVisibility(View.GONE);
            }

            @Override
            public void playError() {
                mLiveentFansWebpView.setVisibility(View.GONE);
            }
        });
        mCloseView = findViewById(R.id.live_ent_room_close);
        mCloseView.setOnClickListener(v -> {
            if (OneClickHelper.getInstance().onClick(v)) {
                onCloseRoomClick();
            }
        });

        mMiniView = findViewById(R.id.live_ent_room_mini);

        if (mMiniView != null) {
            boolean supportMini = LiveCommonUtil.isSupportMini(mRootComponent.getPlaySource(),
                    mRootComponent.getActivity());
            mMiniView.setVisibility(supportMini ? View.VISIBLE : View.GONE);
            mMiniView.setOnClickListener(this);
        }

        roomGiftRankLayout = findViewById(R.id.live_room_gift_rank_layout);
        roomGiftRankLayout.setShowEmptyDefault(true);
        roomGiftRankLayout.showEmptyDefault();

        roomGiftRankLayout.setGiftRankViewClickListener((v, item, msgItem, position) -> {
            if (OneClickHelper.getInstance().onClick(v)) {
                try {
                    getComponentInteraction(IHeaderComponentInteraction.class).showGiftRankH5Page();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                // PGC 房间详情页-财富榜入口  点击事件
                new XMTraceApi.Trace()
                        .click(40101)
                        .put("currPage", "pgcRoom")
                        .put("roomId", getRoomId() + "")
                        .put("anchorId", getHostData().getHostUid() + "")
                        .createTrace();
            }
        });
        roomGiftRankLayout.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
        mRoomPlayRuleView = findViewById(R.id.live_ent_room_play_rule);
        Drawable topicDrawable = ContextCompat.getDrawable(
                getContext(), com.ximalaya.ting.android.live.common.R.drawable.live_common_room_header_topic
        );
        if (topicDrawable != null && mRoomPlayRuleView != null) {
            topicDrawable.setBounds(
                    0, 0,
                    BaseUtil.dp2px(getContext(), 12),
                    BaseUtil.dp2px(getContext(), 12)
            );
            mRoomPlayRuleView.setCompoundDrawables(topicDrawable, null, null, null);
            mRoomPlayRuleView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (OneClickHelper.getInstance().onClick(v)) {
                        showPlayRuleDialog();
                    }
                }
            });
        }

        mScrollHourlyRank = findViewById(R.id.live_st_ent_hourly_rank);
    }


    /**
     * 获取填充财富周榜榜单前三，填充头部前三头像
     */
    private void requestAndShowWeekRank(long roomId) {
        CommonRequestForLiveEnt.getRankInfo(
                getRoomId(), RankBackendType.WEALTH_EVERY_WEEK,
                new IDataCallBack<RankDetail>() {
                    @Override
                    public void onSuccess(@Nullable RankDetail rank) {
                        if (getRoomId() != roomId) {
                            Logger.e("qmc", "房间切换，榜单数据过时");
                            return;
                        }

                        if (!canUpdateUi()) {
                            return;
                        }
                        if (rank == null || rank.list == null) {
                            rankSize = 0;
                        } else {
                            rankSize = rank.list.size();
                        }
                        if (rank == null) {
                            roomGiftRankLayout.updateGiftRankHttpRsp(null);
                        } else {
                            roomGiftRankLayout.updateGiftRankHttpRsp(rank.list);
                        }
                        checkFansCount();
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showDebugFailToast("获取榜单前三失败(" + code + "): " + message);
                    }
                }
        );
    }

    /**
     * 请求小时榜信息接口，显示小时榜
     */
    private void showHourlyRank() {
        CommonRequestForLiveEnt.getRoomGloryRankInfo(
                getRoomId(),
                new IDataCallBack<RankGloryData>() {
                    @Override
                    public void onSuccess(@Nullable final RankGloryData rank) {
                        if (mScrollHourlyRank == null || rank == null || !rank.needShow()) {
                            ViewStatusUtil.setVisible(View.GONE, mScrollHourlyRank);
                            return;
                        }
                        runAfterViewInflate();
                        ViewStatusUtil.setVisible(View.VISIBLE, mScrollHourlyRank);
                        if (mFragment != null && canUpdateUi()) {
                            refreshHourlyRank(rank);
                            mScrollHourlyRank.setTagViewClickListener(new Function1<View, Unit>() {
                                @Override
                                public Unit invoke(View view) {
                                    // 跳转 h5，地址增加房间信息参数
                                    String url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(
                                            rank.url, "roomId=" + getRoomId()
                                    );
                                    url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(
                                            url, "anchorUid=" + getHostData().getHostUid()
                                    );
                                    url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(
                                            url, "appId=5"
                                    );
                                    if (OneClickHelper.getInstance().onClick(view)) {
                                        if (NativeHybridFragment.isItingScheme(url)) {
                                            LiveCommonITingUtil.handleITing(mFragment.getActivity(), url);
                                        } else {
                                            openHourlyRankDialog(url);
                                        }

                                        // PGC-左上角派对荣耀榜(改名小时榜)入口  点击事件
                                        new XMTraceApi.Trace()
                                                .click(40135)
                                                .put("currPage", "LiveUGCRoomFragment")
                                                .put("roomId", getRoomId() + "")
                                                .put("anchorId", getHostData().getHostUid() + "")
                                                .createTrace();
                                    }
                                    return null;
                                }
                            });
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        ViewStatusUtil.setVisible(View.GONE, mScrollHourlyRank);
                        CustomToast.showDebugFailToast("获取小时榜入口失败(" + code + "): " + message);
                    }
                }
        );
    }

    /**
     * ------------ 小时榜标签相关 开始 ------------
     */

    @Override
    public void updateHourlyRankInfo(int rank) {
        // 系统推送消息，排名发生变化
        currentRank = rank;
        updateHourlyRankScrollView(false);
    }

    // 小时榜默认的倒计时间隔，15 分钟，则每小时的最后 15 分钟开始倒计时
    private int hourlyRankCountdownSeconds = 15 * 60;
    private int currentRank = -1;

    private void refreshHourlyRank(@NotNull RankGloryData rank) {
        hourlyRankCountdownSeconds = rank.countdownSeconds;
        currentRank = rank.hourlyRankNum;

        fixedHourlyRankIconUrl = rank.icon;
        fixedHourlyRankDesc = rank.name;
        leftSeconds = rank.remainingSeconds;

        updateHourlyRankScrollView(true);
        startTimeCountdownTask(rank.remainingSeconds);
    }

    private LiveHelper.ScheduledExecutor countdownExecutor;
    private long leftSeconds = 0;
    private static final int DEFAULT_FULL_COUNTDOWN_SECONDS = 60 * 60;

    /**
     * 开启整点倒计时任务
     *
     * @param remainSeconds 接口请求服务器当前剩余整点倒计时秒数
     */
    private void startTimeCountdownTask(int remainSeconds) {
        if (countdownExecutor != null) countdownExecutor.stop();

        if (remainSeconds <= 0) {
            // 小时整点倒计时结束，重新开始
            startTimeCountdownTask(DEFAULT_FULL_COUNTDOWN_SECONDS);
            return;
        }

        countdownExecutor = new LiveHelper.ScheduledExecutor.Builder()
                .mMainThreadRunnable(new Runnable() {
                    @Override
                    public void run() {
                        if (mFragment == null || !canUpdateUi() || countdownExecutor == null) {
                            return;
                        }

                        leftSeconds = countdownExecutor.getSumInMs() / 1000;
                        updateHourlyRankScrollView(false);
                    }
                })
                .mInitDelayInMs(1000)
                .mPeriodInMs(1000)
                .mSumInMs(remainSeconds * 1000L)
                .build();
        countdownExecutor.startCountDown();
    }

    // 开启小时榜入口时，固定会显示的小时榜 tagView
    private TextView fixedHourlyRankView;
    private String fixedHourlyRankIconUrl;
    private String fixedHourlyRankDesc;

    private void addDefaultHourlyRankView() {
        if (fixedHourlyRankView == null && getContext() != null) {
            fixedHourlyRankView = new TextView(getContext());
            fixedHourlyRankView.setCompoundDrawablePadding(ResourceExt.getDp(3));
            fixedHourlyRankView.setBackgroundResource(R.drawable.live_bg_ent_love_value);
            fixedHourlyRankView.setGravity(Gravity.CENTER_VERTICAL);
            fixedHourlyRankView.setPadding(
                    ResourceExt.getDp(8),
                    ResourceExt.getDp(3),
                    ResourceExt.getDp(8),
                    ResourceExt.getDp(3)
            );
            fixedHourlyRankView.setTextColor(Color.parseColor("#CCFFFFFF"));
            fixedHourlyRankView.setTextSize(12);
        }

        final Drawable endDrawable = ContextCompat.getDrawable(
                getContext(), com.ximalaya.ting.android.live.common.R.drawable.live_common_room_header_next
        );
        if (endDrawable != null) {
            endDrawable.setBounds(
                    0, 0,
                    ResourceExt.getDp(5), ResourceExt.getDp(7)
            );
        }
        fixedHourlyRankView.setCompoundDrawables(null, null, endDrawable, null);

        fixedHourlyRankView.setText(fixedHourlyRankDesc);
        // 填充图标
        if (!TextUtils.isEmpty(fixedHourlyRankIconUrl)) {
            ImageManager.DisplayCallback cb = new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (mFragment == null || !canUpdateUi() ||
                            getContext() == null || fixedHourlyRankView == null) {
                        return;
                    }
                    Drawable icon = new BitmapDrawable(
                            getContext().getResources(), bitmap
                    );
                    int size = BaseUtil.dp2px(getContext(), 12);
                    icon.setBounds(0, 0, size, size);
                    fixedHourlyRankView.setCompoundDrawables(icon, null, endDrawable, null);
                }
            };
            ImageManager.from(getContext()).downloadBitmap(fixedHourlyRankIconUrl, cb);
        }

        // 添加到滚动 TagView 中
        if (fixedHourlyRankView != null) {
            if (!mScrollHourlyRank.isTagViewAdded(fixedHourlyRankView)) {
                mScrollHourlyRank.addTagView(fixedHourlyRankView, false);
            }
        }
    }

    // 开启小时榜入口时，名次和倒计时小时榜 tagView
    private CountdownRankTagView countdownHourlyRankTagView;

    private void addCountdownHourlyRankTagView(boolean init) {
        if (countdownHourlyRankTagView == null && getContext() != null) {
            countdownHourlyRankTagView = new CountdownRankTagView(getContext());
        }

        if (countdownHourlyRankTagView != null) {
            if (!mScrollHourlyRank.isTagViewAdded(countdownHourlyRankTagView)) {
                mScrollHourlyRank.addTagView(countdownHourlyRankTagView);
                if (!init && !mScrollHourlyRank.isCurrentTagView(countdownHourlyRankTagView)) {
                    mScrollHourlyRank.scrollToNextImmediately();
                }
            }
        }
    }

    private void removeCountdownHourlyRankTagView() {
        if (mScrollHourlyRank != null && countdownHourlyRankTagView != null) {
            mScrollHourlyRank.removeTagViewAndScrollToNextImmediately(countdownHourlyRankTagView);
        }
    }

    /**
     * 更新小时榜滚动 TagView
     */
    private void updateHourlyRankScrollView(boolean init) {
        runAfterViewInflate();
        if (currentRank > 30 || currentRank <= 0) {
            // 名次不配显示排名，移除排名 Tag
            mScrollHourlyRank.resumeScroll(false);
            removeCountdownHourlyRankTagView();
            addDefaultHourlyRankView();
        } else {
            // 需要显示排名 Tag
            addCountdownHourlyRankTagView(init);
            addDefaultHourlyRankView();
            updateHourlyRankCountdownView(init);
        }
    }

    /**
     * 更新小时榜倒计时 TagView (非默认样式 Tag)
     */
    private void updateHourlyRankCountdownView(boolean init) {
        runAfterViewInflate();
        if (countdownHourlyRankTagView == null || mScrollHourlyRank == null) return;
        if (leftSeconds <= hourlyRankCountdownSeconds) {
            // 显示倒计时文案
            countdownHourlyRankTagView.setCountdown(
                    LiveTimeUtil.formattedTime(leftSeconds)
            );
            if (!init && !mScrollHourlyRank.isCurrentTagView(countdownHourlyRankTagView)) {
                mScrollHourlyRank.scrollToNextImmediately(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        mScrollHourlyRank.pauseScroll();
                        return null;
                    }
                });
            } else {
                mScrollHourlyRank.pauseScroll();
            }
        } else {
            // 仅显示排名
            countdownHourlyRankTagView.setCountdown("");
        }

        countdownHourlyRankTagView.setCountdownDesc(
                "小时之星第" + currentRank + "名"
        );

        if (leftSeconds <= 0) {
            // 整点倒计时结束，重新开始整点倒计时
            currentRank = -1;
            // 倒计时期间因为当前 TagView 必定时倒计时 TagView，移除时会自动触发滚动，所以恢复滚动无需触发滚动
            mScrollHourlyRank.resumeScroll(false);
            removeCountdownHourlyRankTagView();
            startTimeCountdownTask(DEFAULT_FULL_COUNTDOWN_SECONDS);
        }
    }

    /**
     * ------------ 小时榜标签相关 结束 ------------
     */

    private ProvideForH5CustomerDialogFragment mH5PageDialogFragment;
    private static final String TAG_H5_PAGE_DIALOG_FRAGMENT = "H5PageDialogFragment";

    private void openHourlyRankDialog(String url) {
        if (null == mFragment || !mFragment.isAdded() || mFragment.isRemoving()
                || mFragment.isDetached()) {
            return;
        }

        FragmentManager fragmentManager = mFragment.getChildFragmentManager();
        FragmentTransaction fragTransaction = fragmentManager.beginTransaction();
        Fragment fragment = fragmentManager.findFragmentByTag(TAG_H5_PAGE_DIALOG_FRAGMENT);

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, ProvideForH5CustomerDialogFragment.POSITION_BOTTOM);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, BaseUtil.px2dip(getContext(), getDialogHeight(getContext())));
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, ProvideForH5CustomerDialogFragment.SHOWCLOSE_NO);

        mH5PageDialogFragment = ProvideForH5CustomerDialogFragment.newInstance(bundle);

        try {
            if (fragment != null) {
                // 防止重复显示对话框，移除正在显示的对话框
                fragTransaction.remove(fragment);
                fragTransaction.commitNowAllowingStateLoss();
            }

            mH5PageDialogFragment.showNow(fragmentManager, TAG_H5_PAGE_DIALOG_FRAGMENT);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void bindData(@NonNull PgcRoomDetail hostData) {
        super.bindData(hostData);
        updateViewByDetail();
    }

    protected void updateViewByDetail() {
        if (getHostData() == null) {
            return;
        }
        runAfterViewInflate();
        traceExposed();
        if (showSponsorEntry()) {
            traceSponsorExposed();
            updateRoomSponsor(mSponsorUid);
        }
        requestAndShowWeekRank(getRoomId());
        showHourlyRank();

        mPgcRoomHeaderView.setPgcRoomInfo(getHostData().title, getHostData().middleCoverUrl);
        UIStateUtil.safelySetText(mFmNumberTv,
                String.format(Locale.CHINA, "FM %d", getHostData().fmId));
        updateOnlineCount(getHostData().hotNum);

        initMoreLiveView();
    }

    /**
     * 根绝房间详情返回值
     * 判断粉丝亲密度信息展示相关内容
     */
    private void checkFansIntimacy() {
        if (getHostData() == null || getHostData() == null || getHostData().fansClubVo == null
                || getHostData().fansClubVo.getAdditionTimes() <= 0) {
            return;
        }
        if (getFansCode() == LiveUserInfo.FansGroupStatusCode.TYPE_JOINED &&
                System.currentTimeMillis() < getHostData().getRoomFansClubVo().getAdditionEndAt()) {
            mPgcRoomHeaderView.updateFansIntimacy(getHostData().fansClubVo.getAdditionTimes(),
                    getHostData().fansClubVo.getAdditionEndAt(), getRoomBizType(), false);
            HandlerManager.postOnUIThreadDelay(fansIntimacyRunnable, 5000);
        }
    }

    /**
     * 延迟5s 判断亲密度 是否要发送系统消息
     */
    private final Runnable fansIntimacyRunnable = new Runnable() {
        @Override
        public void run() {
            //判断是否需要发送im消息
            String key = PreferenceConstantsInLive.LIVE_KEY_FANS_INTIMACY_IM_TAG + getRoomId() +
                    LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis());
            boolean needSendImMsg = MmkvCommonUtil.getInstance(getContext()).getBoolean(key, false);
            // 如果当天 该直播间 没有发送过im消息，则发送
            if (!needSendImMsg) {
                MmkvCommonUtil.getInstance(getContext()).saveBoolean(key, true);
                try {
                    getComponentInteraction(IHeaderComponentInteraction.class).sendFansIntimacyMessage(
                            "粉丝团亲密度已开启" + LiveFormatUtils.INSTANCE.formatDouble(getHostData().getRoomFansClubVo().getAdditionTimes()) + "倍加成，有效期至" +
                                    LiveDateUtils.convertTimestampToDateString2(getHostData().getRoomFansClubVo().getAdditionEndAt()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    /**
     * 检查粉丝团人数进度
     */
    private void checkFansCount() {
        if (getHostData() == null || getHostData() == null || getHostData().fansClubVo == null
                || TextUtils.isEmpty(getHostData().fansClubVo.getActiveOrGuardIcon())) {
            return;
        }
        if (getHostData().fansClubVo.getActiveOrGuardCount() < 100) {
            return;
        }
        if (getHostData().fansClubVo.getActiveOrGuardCount() >= LIVE_FANS_1000) {
            //如果当天展示过，则不展示动画
            if (MmkvCommonUtil.getInstance(getContext())
                    .getString(PreferenceConstantsInLive.LIVE_KEY_PGC_FANS_POP_1000_TAG + getRoomId())
                    .equals(LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()))) {
                return;
            }
        } else if (getHostData().fansClubVo.getActiveOrGuardCount() >= LIVE_FANS_100) {
            //如果当天展示过，则不展示动画
            if (MmkvCommonUtil.getInstance(getContext())
                    .getString(PreferenceConstantsInLive.LIVE_KEY_PGC_FANS_POP_100_TAG + getRoomId())
                    .equals(LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()))) {
                return;
            }
        }
        //每个用户每天每个直播间只能展示一次粉丝百人团
        liveFans100PopUrl = getHostData().fansClubVo.getActiveOrGuardIcon();
        liveFansPopNumber = getHostData().fansClubVo.getActiveOrGuardCount();
        //官方直播间不展示粉丝百人团
        if (!isOfficialLive() && !TextUtils.isEmpty(liveFans100PopUrl)) {
            HandlerManager.postOnUIThreadDelay(fansPopRunnable, 5000);
        }
    }

    /**
     * 粉丝百人团任务
     */
    private final Runnable fansPopRunnable = new Runnable() {
        @Override
        public void run() {
            playFansViewProgressAnim(liveFans100PopUrl, liveFansPopNumber);
        }
    };

    /**
     * 播放粉丝百人、千人团动画
     *
     * @param url 动画图片地址
     */
    @Override
    public void playFansViewProgressAnim(String url, int number) {
        if (canUpdateUi() && !TextUtils.isEmpty(url)) {
            getHostData().fansClubVo.setActiveOrGuardIcon(url);
            getHostData().fansClubVo.setActiveOrGuardCount(number);
            //该方法可能为信令调用，不做一天只展示一次限制
            if (number >= LIVE_FANS_1000) {
                MmkvCommonUtil.getInstance(getContext())
                        .saveString(PreferenceConstantsInLive.LIVE_KEY_PGC_FANS_POP_1000_TAG + getRoomId(),
                                LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()));
            } else if (number >= LIVE_FANS_100) {
                MmkvCommonUtil.getInstance(getContext())
                        .saveString(PreferenceConstantsInLive.LIVE_KEY_PGC_FANS_POP_100_TAG + getRoomId(),
                                LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()));
            }
            if (rankSize > 1) {
                //如果榜单人数大于1，动画在榜单上展示
                ConstraintSet cs = new ConstraintSet();
                cs.clone(mLiveentHeaderIconCl);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.START, roomGiftRankLayout.getId(), ConstraintSet.START);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.END, roomGiftRankLayout.getId(), ConstraintSet.END);
                cs.applyTo(mLiveentHeaderIconCl);
            } else {
                //如果榜单人数小于等于1，动画居中展示
                ConstraintSet cs = new ConstraintSet();
                cs.clone(mLiveentHeaderIconCl);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START);
                cs.connect(mLiveentFansWebpView.getId(), ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END);
                cs.applyTo(mLiveentHeaderIconCl);
            }
            mLiveentFansWebpView.setVisibility(VISIBLE);
            mLiveentFansWebpView.setLoopCount(2);
            mLiveentFansWebpView.playNormalAnimation(url);
        }
    }


    /**
     * 更新人气热度值
     *
     * @param hotNum 人气热度值
     */
    protected void updateOnlineCount(long hotNum) {
        runAfterViewInflate();
        if (hotNum <= 0) {
            hotNum = 0;
        }
        mPgcRoomHeaderView.setPgcHeaderIcon();
        mPgcRoomHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);
        mPgcRoomHeaderView.setOnlineCount(hotNum);
    }

    /**
     * 更新用户信息
     *
     * @param currentUserInfo 当前登录用户信息
     */
    @Override
    public void updateCurrentUserInfo(LiveUserInfo currentUserInfo) {
        mUserInfo = currentUserInfo;
        if (mUserInfo != null) {
            //收到用户信息后，更新粉丝团折扣
            String textStr = mUserInfo.getFansClubInfo() != null && mUserInfo.getFansClubInfo().isOnSale()
                    ? "粉团" + mUserInfo.getFansClubInfo().getDiscountNum() + "折特惠" : mPgcRoomHeaderView.getFansPopText();
            mPgcRoomHeaderView.setFansPopText(textStr);
        }

        if (checkFansPointByMMKV()) {
            mPgcRoomHeaderView.getFollowDot().setVisibility(VISIBLE);
        }
        checkFansIntimacy();
    }

    @Override
    public void updateRoomSponsor(long sponsorUid) {
        mSponsorUid = sponsorUid;
        if (roomSponsorLayout == null || roomSponsorLayout.getVisibility() != View.VISIBLE) return;
        if (getContext() == null) return;
        runAfterViewInflate();
        roomSponsorLayout.setSponsorUid(sponsorUid);
    }

    @Override
    public void init(IComponentContainer rootComponent, ViewGroup containerView, View rootView, long roomId) {
        // ignored
    }

    @Override
    public void bindData(ILiveRoomDetail roomDetail) {
        // ignored
    }

    private int getFansCode() {
        if (mUserInfo == null || mUserInfo.getFansClubInfo() == null) {
            return LiveUserInfo.FansGroupStatusCode.TYPE_UNOPEN;
        }
        return mUserInfo.getFansClubInfo().getCode();
    }

    @Override
    public void onStreamState(boolean normal) {

    }

    /**
     * 当前用户状态变化
     *
     * @param userStreamType 用户状态
     */
    @Override
    public void onCurrentUserStreamTypeChanged(int userStreamType) {
        //如果用户状态没有变化不更新UI
        if (onlineMicUserType == userStreamType) {
            return;
        }
        //用户上麦 变为主播身份，显示粉丝团，不设置等级
        onlineMicUserType = userStreamType;
        if (userStreamType == CommonProtoConstant.CommonUserType.USER_TYPE_MICUSER ||
                userStreamType == CommonProtoConstant.CommonUserType.USER_TYPE_PRESIDE) {
            if (mPgcRoomHeaderView != null && mUserInfo != null && mUserInfo.getFansClubInfo() != null &&
                    mUserInfo.getFansClubInfo().getCode() != LiveUserInfo.FansGroupStatusCode.TYPE_UNDEFINED) {
                mPgcRoomHeaderView.setChildFansStatus(true, LiveUserInfo.FansGroupStatusCode.TYPE_JOINED, false, true);
                mPgcRoomHeaderView.setChildFansGrade("");
            }
        } else if (userStreamType == CommonProtoConstant.CommonUserType.USER_TYPE_AUDIENCE) {
            if (mPgcRoomHeaderView != null && mUserInfo != null && mUserInfo.getFansClubInfo() != null) {
                //如果是房主下麦，依然显示主播粉丝团图标
                if (getHostData().roomUid == UserInfoMannage.getUid()) {
                    mPgcRoomHeaderView.setChildFansStatus(true, LiveUserInfo.FansGroupStatusCode.TYPE_JOINED, false, true);
                    mPgcRoomHeaderView.setChildFansGrade("");
                } else {
                    //不是房主 下麦就重新刷新粉丝团状态
                    mPgcRoomHeaderView.setChildFansStatus(alreadyFavorite, getFansCode(), false, getFansActiveStatus());
                    if (mUserInfo.getFansClubInfo().getFansGrade() > 0) {
                        mPgcRoomHeaderView.setChildFansGrade(String.valueOf(mUserInfo.getFansClubInfo().getFansGrade()));
                    }
                }

            }
        }
    }

    /**
     * 检查粉丝团点 是否显示
     */
    private boolean checkFansPointByMMKV() {
        if (getHostData() == null) {
            return false;
        }
        return getHostData().getHostUid() > 0 && MmkvCommonUtil.getInstance(getContext()).
                getBoolean(PreferenceConstantsInLive.LIVE_ENT_KEY_SHOW_PGC_FANS_POINT, true);
    }

    @Override
    public void startReqOnlineCount() {
        if (isRequestingOnlineCount) {
            return;
        }
        stopReqOnlineCount();

        isRequestingOnlineCount = true;

        HandlerManager.obtainMainHandler().post(mReqOnlineCountRunnable);
    }

    private void stopReqOnlineCount() {
        if (!isRequestingOnlineCount) {
            return;
        }
        isRequestingOnlineCount = false;
        HandlerManager.obtainMainHandler().removeCallbacks(mReqOnlineCountRunnable);
    }

    @Override
    public void onSwitchRoom(long newRoomId, Bundle newArgs) {
        super.onSwitchRoom(newRoomId, newArgs);
        resetHeaderData();
        mLiveentFansWebpView.stopNormalAnimation();
        roomSponsorLayout.setSponsorUid(0);
        roomGiftRankLayout.hideRankAvatarViews();
        if (mMoreLiveView != null) {
            mMoreLiveView.clearData();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        resetHeaderData();
        HandlerManager.removeCallbacks(fansIntimacyRunnable);
        LiveLocalBroadcastManager.unregister(mFavoriteStateReceiver);
        if (null != mMoreLiveView) {
            mMoreLiveView.setEventListener(null);
        }
        liveNobleIconMaker.destroy();
        LiveViewPositionManager.getInstance().unregisterLayoutChangeListener(
                TYPE_TOP_TWO_HEADER, mContianerView
        );
        LiveViewPositionManager.getInstance().unregisterLayoutChangeListener(
                TYPE_FULL_HEADER, mHeaderAllView
        );
    }

    private void resetHeaderData() {
        if (mPgcRoomHeaderView != null) {
            mPgcRoomHeaderView.stopFollowAttentionEndAction();
        }
        if (countdownExecutor != null) {
            countdownExecutor.stop();
            countdownExecutor = null;
        }
        if (mScrollHourlyRank != null) {
            countdownHourlyRankTagView = null;
            fixedHourlyRankView = null;
            mScrollHourlyRank.destroyView();
        }
        stopReqOnlineCount();
        if (mSponsorDialogWrapper != null) {
            mSponsorDialogWrapper.dismiss();
            mSponsorDialogWrapper = null;
        }

        if (mH5PageDialogFragment != null) {
            mH5PageDialogFragment.dismissAllowingStateLoss();
            mH5PageDialogFragment = null;
        }

        HandlerManager.removeCallbacks(fansPopRunnable);
        // 移除粉丝团引导动画
        HandlerManager.removeCallbacks(mFansJoinTask);
    }

    private void traceSponsorExposed() {
        // PGC 房间详情页-冠名入口  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(45764)
                .setServiceId("slipPage")
                .put("currPage", "pgcRoom")
                .put("roomId", getRoomId() + "")
                .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                .createTrace();
    }


    /**
     * 头部粉丝团 + 关注入口曝光
     */
    private void traceExposed() {
        // 头部粉丝团入口曝光
        new XMTraceApi.Trace()
                .setMetaId(33359)
                .setServiceId("slipPage")
                .put("currPage", "liveRoom")
                .put("roomMode", LiveRecordInfoManager.getInstance().getEntOrUgcRoomMode())
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                .createTrace();
    }

    private final Runnable mReqOnlineCountRunnable = new Runnable() {
        @Override
        public void run() {
            LiveHelper.Log.i("online-count: mReqOnlineCountRunnable run ");
            if (!isRequestingOnlineCount || mEntMessageManager == null) {
                return;
            }

            mEntMessageManager.reqRoomOnlineCount(new ChatRoomConnectionManager.ISendResultCallback<CommonChatRoomOnlineStatusMessage>() {
                @Override
                public void onSuccess(@Nullable CommonChatRoomOnlineStatusMessage commonChatRoomOnlineStatusMessage) {
                    if (commonChatRoomOnlineStatusMessage != null) {
                        updateOnlineCount(commonChatRoomOnlineStatusMessage.hotValue);
                    }
                }

                @Override
                public void onError(int i, String s) {

                }
            });

            HandlerManager.obtainMainHandler().postDelayed(mReqOnlineCountRunnable, REQUEST_ONLINE_COUNT_DELAY);
        }
    };

    /**
     * 打开房间玩法说明弹窗
     */
    private void showPlayRuleDialog() {
        if (getHostData() == null) {
            return;
        }

        long roomId = getHostData().getRoomId();
        String title = getHostData().title;
        String ruleInfo = getHostData().ruleInfo;
        boolean hasFavorited = alreadyFavorite;

        PgcRoomPlayRuleDialogFragment.show(
                getContext(), mFragment.getChildFragmentManager(),
                new RoomPlayRuleInfo(roomId, title, ruleInfo, hasFavorited)
        );
        // PGC直播间-玩法按钮  点击事件
        new XMTraceApi.Trace()
                .click(60877) // 用户点击时上报
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForClick())
                .createTrace();
    }

    /**
     * 本地广播接收器，用于更新房间关注状态
     */
    private final BroadcastReceiver mFavoriteStateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (!LiveLocalBroadcastManager.ACTION.UPDATE_FAVORITE_STATE.equals(intent.getAction())) {
                return;
            }

            boolean favorite = intent.getBooleanExtra(LiveLocalBroadcastManager.EXTRA.FAVORITE,
                    false);
            if (mFragment != null && canUpdateUi()) {
                updateFavoriteState(favorite, true);
            }

            try {
                IHeaderComponentInteraction iHeaderComponentInteraction = getComponentInteraction(IHeaderComponentInteraction.class);
                iHeaderComponentInteraction.updateFavoriteState(favorite);
                iHeaderComponentInteraction.updateCollectItemState(favorite);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    /**
     * 更新周榜信息
     *
     * @param rankMessage 周榜榜单
     */
    public void updateWeekRankInfo(CommonChatRoomFansRankMessage rankMessage) {
        roomGiftRankLayout.onChatGiftRankChanged(rankMessage);
    }

    /**
     * 收到粉丝团信息
     *
     * @param fansGroupMsg CommonFansGroupMsg
     */
    @Override
    public void onReceiveFansGroupMsg(CommonFansGroupMsg fansGroupMsg) {
        if (fansGroupMsg == null || mPgcRoomHeaderView == null || mUserInfo == null
                || !canUpdateUi() || mUserInfo.getFansClubInfo() == null) {
            return;
        }
        switch (fansGroupMsg.type) {
            case CommonFansGroupMsg.FansMsgType.TYPE_QUIT:  //退出粉丝团
                mUserInfo.getFansClubInfo().setCode(LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN);
                mPgcRoomHeaderView.setChildFansStatus(false, LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN, false, false);
                break;
            case CommonFansGroupMsg.FansMsgType.TYPE_OPEN:
                mUserInfo.getFansClubInfo().setCode(LiveUserInfo.FansGroupStatusCode.TYPE_JOINED);
                mUserInfo.getFansClubInfo().setActive(true);

                mPgcRoomHeaderView.openFansUpgradePop(R.string.livecomm_congratulations_on_opening_fan_club, () -> {
                    if (!canUpdateUi()) {
                        return;
                    }
                    mPgcRoomHeaderView.setChildFansStatus(true, getFansCode(), false, getFansActiveStatus());
                    if (hasCurrentOnMic()) {
                        mPgcRoomHeaderView.setChildFansGrade("");
                    } else {
                        mPgcRoomHeaderView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                    }
                });


                break;
            case CommonFansGroupMsg.FansMsgType.TYPE_UPGRADE:
                mUserInfo.getFansClubInfo().setCode(LiveUserInfo.FansGroupStatusCode.TYPE_JOINED);

                // 升级动画
                mPgcRoomHeaderView.openFansUpgradePop(R.string.livecomm_congratulations_on_your_fan_club_upgrade, () -> {
                    if (!canUpdateUi()) {
                        return;
                    }
                    mPgcRoomHeaderView.setChildFansStatus(true, getFansCode(), false, getFansActiveStatus());
                    if (hasCurrentOnMic()) {
                        mPgcRoomHeaderView.setChildFansGrade("");
                    } else {
                        mPgcRoomHeaderView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                    }
                });
                break;
            default:
                break;
        }
    }

    @Override
    public void updateFansClubActiveStatus(LiveFansClubStatusModel message) {
        IHeaderComponent.super.updateFansClubActiveStatus(message);
        if (message == null || mUserInfo == null || mUserInfo.getFansClubInfo() == null) {
            return;
        }
        if (message.getType() == 1) { //粉丝状态变为活跃状态
            mUserInfo.getFansClubInfo().setActive(true);
            mPgcRoomHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, true);
        }
    }

    @Override
    public void onReceiveUpdateFansIntimacyMsg(CommonChatFansIntimacyMsg msg) {
        if (msg == null || mUserInfo == null || mUserInfo.getFansClubInfo() == null) {
            return;
        }
        runAfterViewInflate();
        // 如果没有加入粉丝团 不触发亲密度
        if (getHostData().getRoomFansClubVo().getCode() != LiveUserInfo.FansGroupStatusCode.TYPE_JOINED) {
            return;
        }
        mPgcRoomHeaderView.updateFansIntimacy(msg);
    }

    @Override
    public void onJumpOtherPage() {
        if (null != mH5PageDialogFragment) {
            mH5PageDialogFragment.dismiss();
        }
        if (null != mSponsorDialogWrapper) {
            mSponsorDialogWrapper.dismiss();
        }
    }

    /**
     * 房间收藏状态变化
     *
     * @param favorite   收藏状态
     * @param initiative 是否是主动行为
     */
    @Override
    public void updateFavoriteState(boolean favorite, boolean initiative) {
        LiveHelper.Log.i(TAG, "updateFavoriteState favorite:" + favorite + ",initiative:" + initiative);
        runAfterViewInflate();
        alreadyFavorite = favorite;
        if (mPgcRoomHeaderView == null) {
            return;
        }

        //判断有粉丝团权限
        if (mUserInfo != null && mUserInfo.getFansClubInfo() != null
                && mUserInfo.getFansClubInfo().getCode() != LiveUserInfo.FansGroupStatusCode.TYPE_UNDEFINED) {
            //关注状态变化 房主和麦上用户 粉丝入口展示主播状态
            if (getHostData() != null && (getHostData().roomUid == UserInfoMannage.getUid() || hasCurrentOnMic())) {
                //兜底，如果房主进入刚创建的房间，此时关注按钮可能隐藏，需要展示出来
                if (mPgcRoomHeaderView.getMFollowGuardView().getVisibility() == View.GONE) {
                    mPgcRoomHeaderView.setFollowVisibility(VISIBLE);
                }
                mPgcRoomHeaderView.setChildFansStatus(true, LiveUserInfo.FansGroupStatusCode.TYPE_JOINED, false, true);
                mPgcRoomHeaderView.setChildFansGrade("");
                return;
            }
            //关注状态
            if (favorite) {
                if (initiative) {
                    // 关注成功后，开始关注成功动画
                    onFollowSuccess();
                } else if (mUserInfo.getFansClubInfo().getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN) {
                    // 收藏了房间没有加入粉丝团，则展示加入粉丝团动画
                    startFansPop(5000);
                }
                mPgcRoomHeaderView.setChildFansStatus(true, getFansCode(), false, getFansActiveStatus());
                //等级不为0 ，在麦上的主播加团成功后 不更新等级数字
                if (hasCurrentOnMic()) {
                    mPgcRoomHeaderView.setChildFansGrade("");
                } else if (mUserInfo.getFansClubInfo().getFansGrade() > 0) {
                    mPgcRoomHeaderView.setChildFansGrade(String.valueOf(mUserInfo.getFansClubInfo().getFansGrade()));
                }
            } else {
                mPgcRoomHeaderView.setChildFansStatus(false, getFansCode(), false, getFansActiveStatus());
                //在麦上的主播加团成功后 不更新等级数字
                if (hasCurrentOnMic()) {
                    mPgcRoomHeaderView.setChildFansGrade("");
                } else {
                    mPgcRoomHeaderView.setChildFansGrade(String.valueOf(mUserInfo.getFansClubInfo().getFansGrade()));
                }
            }
        } else {
            //兼容旧版本pgc无粉丝团情况
            if (favorite) {
                mPgcRoomHeaderView.setFollowVisibility(GONE);
            } else {
                mPgcRoomHeaderView.setFollowVisibility(VISIBLE);
                mPgcRoomHeaderView.setChildFansStatus(false, LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN, false, false);
            }

        }
        if (!favorite) {
            // PGC直播间-头部关注按钮  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(60876)
                    // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .setServiceId("slipPage")
                    .put("currPage", "liveRoom")
                    .put("exploreType", "liveRoom")
                    .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForSlipPage())
                    .createTrace();
        }
    }

    /**
     * 关注成功回调
     */
    public void onFollowSuccess() {
        if (mPgcRoomHeaderView != null) {
            mPgcRoomHeaderView.startAttentionOkAnim(() -> {
                if (mPgcRoomHeaderView != null) {
                    if (checkIsNeedJoinFansAnim()) {
                        saveJoinFansToLocal();
                        startFansPop(200);
                        mPgcRoomHeaderView.setChildFansStatus(true, getFansCode(), true, getFansActiveStatus());
                    } else {
                        if (!openFansClubOrIsAnchorFans()) {
                            //不是粉丝团成员
                            mPgcRoomHeaderView.startRedHeartAnim();
                        }
                        mPgcRoomHeaderView.setChildFansStatus(true, getFansCode(), false, getFansActiveStatus());
                    }
                }
            });
        }
    }

    /**
     * delay ms 后开始展示粉丝团动画
     */
    private void startFansPop(long delay) {
        //关注且没有加入粉丝团，收听5s后展示引导加入粉丝团动画
        HandlerManager.postOnUIThreadDelay(mFansJoinTask, delay);
    }

    /**
     * 粉丝团加入动画Task
     */
    private final Runnable mFansJoinTask = new Runnable() {
        @Override
        public void run() {
            if (!canUpdateUi()) {
                return;
            }
            if (openFansClubOrIsAnchorFans()) {
                Logger.d("fansJoin", "isFollowAndNotFans() = false");
                return;
            }
            //用户没有关注房间，不展示加团动画
            if (!alreadyFavorite) {
                return;
            }
            if (hasCurrentOnMic()) {
                return;
            }
            Logger.d("fansJoin", "startJoinPopAnim");
            if (checkFansPointByMMKV()) {
                mPgcRoomHeaderView.getFollowDot().setVisibility(GONE);
            }
            mPgcRoomHeaderView.startJoinPopAnim(() -> {
                saveJoinFansToLocal();
                if (checkFansPointByMMKV()) {
                    mPgcRoomHeaderView.getFollowDot().setVisibility(VISIBLE);
                }
                HandlerManager.postOnUIThreadDelay(mFansJoinTask, LiveDateUtils.MIN);
            });
            if (mPgcRoomHeaderView != null) {
                saveJoinFansToLocal();
            }
        }
    };

    private void saveJoinFansToLocal() {
        if (getHostData() != null && getHostData().getHostUid() > 0) {
            String sb = PreferenceConstantsInLive.LIVE_ENT_KEY_SHOW_PGC_FANS_JOIN +
                    getHostData().getHostUid();
            LiveHelper.Log.i(TAG, "saveJoinFansToLocal  今天展示了POP动画，");
            MmkvCommonUtil.getInstance(getContext()).saveString(sb, String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 判断是否需要展示粉丝团动画
     */
    private boolean checkIsNeedJoinFansAnim() {
        if (!openFansClubOrIsAnchorFans()) {
            if (getHostData().getHostUid() > 0) {
                String lastTime = MmkvCommonUtil.getInstance(getContext()).
                        getStringCompat(PreferenceConstantsInLive.LIVE_ENT_KEY_SHOW_PGC_FANS_JOIN + getHostData().getHostUid());
                if (TextUtils.isEmpty(lastTime) || !LiveDateUtils.isSameDay(Long.parseLong(lastTime), System.currentTimeMillis())) {
                    //不是同一天 需要显示动画
                    LiveHelper.Log.i(TAG, "checkIsNeedJoinFansAnim    今天还未展示  需要展示动画");
                    return true;
                } else {
                    LiveHelper.Log.i(TAG, "checkIsNeedJoinFansAnim    今天已经展示  不需要再展示动画");
                }
            } else {
                LiveHelper.Log.i(TAG, "checkIsNeedJoinFansAnim   anchorUid不合法   不展示动画");
            }
        } else {
            LiveHelper.Log.i(TAG, "checkIsNeedJoinFansAnim   没有关注/没有开通粉丝团   不需要展示动画");
        }
        return false;
    }


    /**
     * 1、主播没有开发粉丝团，true
     * 2、加入当前主播的粉丝团成员，true
     */
    private boolean openFansClubOrIsAnchorFans() {
        if (mUserInfo == null || mUserInfo.getFansClubInfo() == null) {
            return false;
        }

        if (mUserInfo.getFansClubInfo().getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_UNOPEN) {
            return true;
        }

        if (mUserInfo.getFansClubInfo().getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_JOINED) {
            LiveHelper.Log.i(TAG, "isAnchorFans：TYPE_JOINED    是粉丝团成员");
            return true;

        } else if (mUserInfo.getFansClubInfo().getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN) {
            LiveHelper.Log.i(TAG, "isAnchorFans：TYPE_UNJOIN    不是粉丝团成员");
            return false;
        } else {
            LiveHelper.Log.i(TAG, "isAnchorFans：TYPE_UNOPEN    没开通粉丝团");
            return false;
        }
    }

    @Override
    public void destroy() {
        // ignored
    }


    private int getDialogHeight(Context context) {
        Context cxt = LiveContextUtil.getContextWithDefault(context);
        return BaseUtil.getScreenHeight(cxt) - BaseUtil.dp2px(cxt, 220);
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int viewId = v.getId();
        if (viewId == R.id.live_sponsor_head_layout) {
            showSponsorDialog();
        } else if (viewId == R.id.live_follow_tv) {
            //粉丝团关注文字点击事件
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(getContext());
                return;
            }
            if (getRoomId() <= 0) {
                CustomToast.showDebugFailToast("RoomId <= 0");
            }
            // PGC直播间-头部关注按钮  点击事件
            new XMTraceApi.Trace()
                    .click(60875) // 用户点击时上报
                    .put("currPage", "liveRoom")
                    .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForClick())
                    .createTrace();
            if (entHeaderPresenter != null) {
                entHeaderPresenter.requestFollow(getRoomId());
            }
        } else if (v.getId() == R.id.live_fans_fl || v.getId() == R.id.live_tv_fans_grade) {
            //粉丝团 + 和等级点击事件 ，都走同一个逻辑 打开粉丝团半屏
            //小黄点逻辑
            if (getHostData().getHostUid() > 0 && mPgcRoomHeaderView.getFollowDot().getVisibility() == View.VISIBLE) {
                MmkvCommonUtil.getInstance(getContext()).
                        saveBoolean(PreferenceConstantsInLive.LIVE_ENT_KEY_SHOW_PGC_FANS_POINT, false);
                mPgcRoomHeaderView.getFollowDot().setVisibility(View.GONE);
            }
            //打开粉丝团半屏
            showFansClubDialog();
        } else if (viewId == R.id.live_header_owner_icon || viewId == R.id.live_timing_layout) {
            try {
                long roomId = getHostData().getRoomId();
                String title = getHostData().title;
                String ruleInfo = getHostData().ruleInfo;
                boolean isFollowed = alreadyFavorite;

                RoomPlayRuleInfo info = new RoomPlayRuleInfo(roomId, title, ruleInfo, isFollowed);
                info.setMiddleCoverUrl(getHostData().middleCoverUrl);
                info.setSmallCoverUrl(getHostData().smallCoverUrl);
                info.setLargeCoverUrl(getHostData().largeCoverUrl);

                if (getHostData() != null && getHostData().fansClubVo != null) {
                    info.fansClubInfo = getHostData().fansClubVo;
                }

                if (getHostData().getRoomFansClubVo() != null) {
                    LiveUserInfo.FansClubVoBean fans = getHostData().getRoomFansClubVo();
                    if (fans.isValidByCode()) {
                        LiveUserInfo.RevenueMedalInfoBean fansInfo = new LiveUserInfo.RevenueMedalInfoBean();
                        fansInfo.setType(FANS_CLUB);
                        fansInfo.setTitle(getFansMedalCardTitle(fans.getCount()));
                        fansInfo.setSubTitle("粉丝团");
                        fansInfo.setBgImg(fans.getUserCardBgImg());
                        fansInfo.setIcon(fans.getUserCardIcon());
                        fansInfo.setTextColor(fans.getUserCardTextColor());
                        info.fansMedalInfo = fansInfo;
                    }
                }

                getComponentInteraction(IBaseEntHostInteraction.class).showRoomInfoDialog(info);
                // PGC直播间-顶部房间信息  点击事件
                new XMTraceApi.Trace()
                        .click(60902) // 用户点击时上报
                        .put("currPage", "liveRoom")
                        .createTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (viewId == R.id.live_ent_room_mini) {
            onMiniRoomClick();
        }
    }

    private String getFansMedalCardTitle(long num) {
        if (num < 10000) {
            return "{" + num + "}";
        }

        float shrink = LiveNumberUtil.coerceAtMost(num / 10000f, 9999.9f);
        DecimalFormat df = new DecimalFormat("#.0");
        df.setRoundingMode(RoundingMode.HALF_UP);

        return "{" + df.format(shrink) + "}万";
    }

    /**
     * 显示粉丝团弹窗
     */
    private void showFansClubDialog() {
        //如果开通儿童模式，不能打开粉丝团;此处判断防止入口遗漏能进入直播间
        if (LiveHostCommonUtil.checkChildrenModeOpen(getContext())) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getContext());
            return;
        }
        new XMTraceApi.Trace()
                .click(33358)
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();

        if (canUpdateUi()) {
            if (mUserInfo == null || mUserInfo.getFansClubInfo() == null) {
                return;
            }
            // 弹起粉丝团面板
            try {
                getComponentInteraction(IHeaderComponentInteraction.class).showFansClubDialog();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 当前用户是否在麦上 或是房主
     */
    private boolean hasCurrentOnMic() {
        try {
            if (null == getHostData()) {
                return false;
            }

            IHeaderComponentInteraction interaction = getComponentInteraction(IHeaderComponentInteraction.class);
            boolean isPreside = interaction.isCurrentLoginUserPreside();
            return interaction.isCurrentUserOnMic() ||
                    getHostData().roomUid == UserInfoMannage.getUid() || isPreside;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private VerticalSlideUtil.VerticalSlideWrapper<EntSponsorBottomDialog> mSponsorDialogWrapper;

    /**
     * 展示冠名弹窗
     */
    public void showSponsorDialog() {
        // PGC 房间详情页-冠名入口  点击事件
        new XMTraceApi.Trace()
                .click(45763)
                .put("currPage", "pgcRoom")
                .put("roomId", getRoomId() + "")
                .createTrace();

        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getContext());
            return;
        }

        if (mFragment == null || !canUpdateUi()) return;

        try {
            mSponsorDialogWrapper = EntSponsorBottomDialog.show(
                    (LivePGCRoomFragment) mFragment, mFragment.getChildFragmentManager()
            );
        } catch (Exception e) {
            LiveXdcsUtil.doXDCS(TAG, "创建冠名弹窗失败: " + e);
        }
    }

    protected void clickMoreLive() {
        if (!canUpdateUi() || null == getHostData()) {
            return;
        }

        showMoreLiveDialog(0, false, null);
    }

    /**
     * @noinspection SameParameterValue
     */
    private void showMoreLiveDialog(
            int pos, boolean fromSlide, @Nullable MoreLiveDialogFragment.RecParam recParam
    ) {
        try {
            IHeaderComponentInteraction headerComponentInteraction = getComponentInteraction(
                    IHeaderComponentInteraction.class
            );
            headerComponentInteraction.showMoreLiveDialog(pos, fromSlide, recParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据配置中心来控制更多直播按钮
     */
    private void initMoreLiveView() {
        if (getHostData() != null && getHostData().getHostUid() > 0) {
            String blackList = LiveSettingManager.getMoreLiveAnchorUidBlackList();
            String hostUid = getHostData().getHostUid() + "";
            if (blackList.contains(hostUid)) {
                ViewStatusUtil.setVisible(View.GONE, mMoreLiveView);
            } else {
                runAfterViewInflate();
                mMoreLiveView.setVisibility(VISIBLE);
            }
        }
    }

    /**
     * @return 是否展示冠名入口
     */
    protected boolean showSponsorEntry() {
        return true;
    }

    /**
     * 获取粉丝团活跃状态
     *
     * @return 粉丝团是否活跃
     */
    private boolean getFansActiveStatus() {
        if (getHostData() == null || mUserInfo == null || mUserInfo.getFansClubInfo() == null) {
            return true;
        }
        return mUserInfo.getFansClubInfo().isActive();
    }

    @Override
    public void onCloseRoomClick() {
        finishFragment();
    }

    @Override
    public void onMiniRoomClick() {
        try {
            IHeaderComponentInteraction iHeaderComponentInteraction = getComponentInteraction(IHeaderComponentInteraction.class);
            iHeaderComponentInteraction.miniEntRoom();


            if (getHostData() != null) {
                //ABTest下的最小化埋点
                new XMTraceApi.Trace()
                        .setMetaId(57212)
                        .setServiceId("click")
                        .put("currPage", "liveRoom")
                        .put("liveId", getHostData().getLiveId() + "")
                        .put("roomId", getHostData().getRoomId() + "")
                        .put("LiveBroadcastState", getHostData().getStatus() + "")
                        .put("liveRoomType", "5")
                        .put("anchorId", getHostUid() + "")
                        .createTrace();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isMiniRoomSupport() {
        return mMiniView != null && mMiniView.getVisibility() == VISIBLE;
    }

    @Override
    public void onReceiveMoreLiveMsg(LiveMoreLiveNotifyMsg msg) {
        if (mMoreLiveView != null) {
            mMoreLiveView.updateContent(msg);
        }
    }

    @Override
    public void slidShowMoreLive() {
        // 侧滑触发的展开更多直播面板
        if (mMoreLiveInfo == null) {
            showMoreLiveDialog(0, true, null);
            return;
        }

        MoreLiveDialogFragment.Tab recTab;
        if (mMoreLiveInfo.getType() == TYPE_LIVING) {
            recTab = MoreLiveDialogFragment.Tab.TAB_ATTENTION;
        } else {
            recTab = MoreLiveDialogFragment.Tab.TAB_RECOMMEND;
        }
        MoreLiveDialogFragment.RecParam param = new MoreLiveDialogFragment.RecParam(
                recTab, mMoreLiveInfo.getRecRoomId(), mMoreLiveInfo.getRecBizType()
        );
        showMoreLiveDialog(0, true, param);
    }
}
