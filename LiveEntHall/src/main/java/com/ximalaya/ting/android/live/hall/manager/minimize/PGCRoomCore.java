package com.ximalaya.ting.android.live.hall.manager.minimize;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.common.lib.logger.PgcBizLog;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.live.biz.roompk.entity.RoomBasePanelMsg;
import com.ximalaya.ting.android.live.biz.roompk.pkpanel.RoomPkStreamMixHandler;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.hall.entity.EntMediaSideInfo;
import com.ximalaya.ting.android.live.hall.entity.proto.CommonEntJoinRsp;
import com.ximalaya.ting.android.live.hall.manager.dispatcher.IEntMessageDispatcherManager;
import com.ximalaya.ting.android.live.hall.manager.dispatcher.impl.EntMessageDispatcherManagerImpl;
import com.ximalaya.ting.android.live.hall.manager.ent.IEntMessageManager;
import com.ximalaya.ting.android.live.hall.manager.ent.impl.EntMessageManagerImpl;
import com.ximalaya.ting.android.live.hall.manager.minimize.im.PGCRoomCoreIMService;
import com.ximalaya.ting.android.live.hall.manager.stream.EntMediaSideInfoManager;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseEntRoomCore;
import com.ximalaya.ting.android.live.host.manager.minimize.mode.CommonJoinRsp;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreLogger;
import com.ximalaya.ting.android.live.host.manager.roomcore.callback.ILeaveMicCallback;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.constant.BaseCommonProtoConstant;
import com.ximalaya.ting.android.live.lib.chatroom.entity.BaseCommonChatRsp;
import com.ximalaya.ting.android.live.lib.chatroom.entity.ent.CommonEntOnlineUserRsp;
import com.ximalaya.ting.android.live.lib.chatroom.entity.ent.CommonEntUserStatusSynRsp;
import com.ximalaya.ting.android.live.lib.stream.IStreamManager;
import com.ximalaya.ting.android.live.lib.stream.StreamManager;
import com.ximalaya.ting.android.live.lib.stream.publish.XmLiveRoom;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * PGC 最小化房间，保持推拉流，接受发送信令能力
 *
 * <AUTHOR>
 * @Email <EMAIL>
 */
public class PGCRoomCore extends BaseEntRoomCore {

    private static final String TAG = "PGCRoomCore";

    /**
     * 长连接娱乐厅信令消息分发管理
     */
    protected IEntMessageDispatcherManager mEntMessageDispatcherManager;

    /**
     * 娱乐厅信令管理
     */
    protected IEntMessageManager mEntMessageManager;

    protected RoomPkStreamMixHandler mPkStreamMixHandler = new RoomPkStreamMixHandler();

    PGCRoomCoreIMService pgcRoomCoreIMService;


    @Override
    public void initService() {
        super.initService();
        pgcRoomCoreIMService = new PGCRoomCoreIMService(this);
        getRoomServices().put("IPGCRoomCoreIMService", pgcRoomCoreIMService);
    }

    @Override
    protected void initBizManagers() {
        RoomCoreLogger.log(getClass().getSimpleName() + " 创建 EntMessageDispatcherManagerImpl");
        mEntMessageDispatcherManager = new EntMessageDispatcherManagerImpl(mConnectionManager);
        addManager(IEntMessageDispatcherManager.NAME, mEntMessageDispatcherManager);
        pgcRoomCoreIMService.setEntMessageDispatcherManager(mEntMessageDispatcherManager);
        RoomCoreLogger.log(getClass().getSimpleName() + " 创建 EntMessageManagerImpl");
        mEntMessageManager = new EntMessageManagerImpl(mConnectionManager);
        addManager(IEntMessageManager.NAME, mEntMessageManager);
    }

    protected void registerListener() {
        Logger.i(TAG, "registerListener");
        super.registerListener();
        RoomCoreLogger.log(getClass().getSimpleName() + "  registerListener");
        //UserStatusSync
        pgcRoomCoreIMService.addCurrentUserStatusSyncMessageReceivedListener(getCurrentUserMicStatusSyncMessageReceivedListener());
        //OnlineUser
        pgcRoomCoreIMService.addOnlineUserNotifyMessageReceivedListener(getCurrentOnLineUserMessageListener());

        pgcRoomCoreIMService.addRoomPKNotifyMessageReceivedListener(mEntRoomPkNotifyListener);
    }

    @Override
    protected void unregisterListener() {
        Logger.i(TAG, "unregisterListener");
        RoomCoreLogger.log(getClass().getSimpleName() + "  unregisterListener");
        super.unregisterListener();
        pgcRoomCoreIMService.removeCurrentUserStatusSyncMessageReceivedListener(getCurrentUserMicStatusSyncMessageReceivedListener());
        pgcRoomCoreIMService.removeOnlineUserNotifyMessageReceivedListener(getCurrentOnLineUserMessageListener());
        pgcRoomCoreIMService.removeRoomPKNotifyMessageReceivedListener(mEntRoomPkNotifyListener);
        stopSyncUserStatus();
        stopSendPresideTtlTask();
    }


    private final IEntMessageDispatcherManager.IEntMessageReceivedListener.IRoomPKNotifyMessageReceivedListener mEntRoomPkNotifyListener = new IEntMessageDispatcherManager.IEntMessageReceivedListener.IRoomPKNotifyMessageReceivedListener() {


        @Override
        public void onRoomPKMessageReceived(Object msg) {
            if (coreUiReceiveAndConsumeMessages()) {
                logFile("onRoomPKMessageReceived coreUiReceiveAndConsumeMessages == true");
                return;
            }
            logFile("onRoomPKMessageReceived coreUiReceiveAndConsumeMessages == false");
            if (msg instanceof RoomBasePanelMsg) {
                mPkStreamMixHandler.setPkPanelMessage(getRoomId(), (RoomBasePanelMsg) msg);
            }
        }
    };


    @Override
    public void getUserMicState(@NonNull ChatRoomConnectionManager.ISendResultCallback<CommonEntUserStatusSynRsp> callback) {
        if (mEntMessageManager == null) {
            callback.onError(0, "");
            return;
        }

        PgcBizLog.INSTANCE.writeLogFile(PgcBizLog.PGC_NET_DATA, "Client EntVirtualRoom getMyMicStatus() -> reqSyncUserStatus()");

        Logger.i(TAG, " getMyMicStatus");
        mEntMessageManager.reqSyncUserStatus(new ChatRoomConnectionManager.ISendResultCallback<CommonEntUserStatusSynRsp>() {
            @Override
            public void onSuccess(@Nullable CommonEntUserStatusSynRsp commonEntUserStatusSynRsp) {
                callback.onSuccess(commonEntUserStatusSynRsp);
            }

            @Override
            public void onError(int i, String s) {
                callback.onError(i, s);
            }
        });
    }

    @Override
    public void reqPresideTtl(@NonNull ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callback) {
        if (mEntMessageManager != null) {
            mEntMessageManager.reqPresideTtl(new ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp>() {
                @Override
                public void onSuccess(@Nullable BaseCommonChatRsp baseCommonEntRsp) {
                    callback.onSuccess(baseCommonEntRsp);
                }

                @Override
                public void onError(int code, String msg) {
                    callback.onError(code, msg);
                }
            });
        } else {
            callback.onError(0, "mEntMessageManager null");
        }
    }

    @Override
    protected void reqJoin(int userType, @NonNull ChatRoomConnectionManager.ISendResultCallback<CommonJoinRsp> callback) {
        super.reqJoin(userType, callback);
        if (mEntMessageManager == null) {
            callback.onError(0, "");
            return;
        }
        mEntMessageManager.reqJoin(0, userType, new ChatRoomConnectionManager.ISendResultCallback<CommonEntJoinRsp>() {
            @Override
            public void onSuccess(@Nullable CommonEntJoinRsp joinRsp) {
                callback.onSuccess(joinRsp);
            }

            @Override
            public void onError(int i, String s) {
                callback.onError(i, s);
            }
        });
    }


    @Override
    public void sendCurrentMicUserSoundLevelSeiAsync(int level, int userType, int micNo) {
        super.sendCurrentMicUserSoundLevelSeiAsync(level, userType, micNo);
        EntMediaSideInfo mediaSideInfo = new EntMediaSideInfo();
        mediaSideInfo.setType(EntMediaSideInfo.TYPE_VOLUME);
        mediaSideInfo.setContent(new EntMediaSideInfo.MediaSideInfoContent(
                getRoomId(), level, UserInfoMannage.getUid(), micNo, userType
        ));
        if (getMediaSideInfoManager() instanceof EntMediaSideInfoManager) {
            String json = ((EntMediaSideInfoManager) getMediaSideInfoManager()).toJson(mediaSideInfo);
            XmLiveRoom.sharedInstance(MainApplication.mAppInstance).sendMediaSideInfo(json);
            ((EntMediaSideInfoManager) getMediaSideInfoManager()).receiveMediaSideInfo(mediaSideInfo);
        }
    }


    @Override
    public void leaveMic() {
        super.leaveMic();
        if (!UserInfoMannage.hasLogined()) {
            return;
        }
        logFile("leaveMic");
        leaveMic((success, errorCode, errorMsg) -> {
            logFile("leaveMic success " + success);
        });
    }

    @Override
    public void leaveMic(@Nullable ILeaveMicCallback callback) {
        super.leaveMic(callback);
        logFile("leaveMic  ");
        if (!UserInfoMannage.hasLogined()) {
            if (callback != null) {
                callback.onLeaveResult(false, 0, "");
            }
            return;
        }
        IStreamManager streamManager = getStreamManager();
        if (streamManager == null || mEntMessageManager == null) {
            if (callback != null) {
                callback.onLeaveResult(false, 0, "");
            }
            return;
        }

        boolean isHost = streamManager.isHost();
        logFile("leaveMic isHost = " + isHost);

        if (isHost) {
            mEntMessageManager.reqUnPreside(new ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp>() {
                @Override
                public void onSuccess(BaseCommonChatRsp baseCommonChatRsp) {
                    logFile("leaveMic onSuccess ");
                    if (callback != null) {
                        callback.onLeaveResult(true, 0, "");
                    }
                }

                @Override
                public void onError(int errorCode, String errorMessage) {
                    logFile("leaveMic onError ");
                    HandlerManager.postOnUIThread(new Runnable() {
                        @Override
                        public void run() {
                            if (callback != null) {
                                callback.onLeaveResult(false, errorCode, errorMessage);
                            }
                        }
                    });
                }
            });
        } else {
            if (isMicConnected() || isWaitMicConnecting()) {
                mEntMessageManager.reqLeave(new ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp>() {
                    @Override
                    public void onSuccess(BaseCommonChatRsp baseCommonChatRsp) {
                        logFile("leaveMic onSuccess ");
                        if (callback != null) {
                            callback.onLeaveResult(true, 0, "");
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMessage) {
                        logFile("leaveMic onError " + errorCode + errorMessage);
                        HandlerManager.postOnUIThread(new Runnable() {
                            @Override
                            public void run() {
                                if (callback != null) {
                                    callback.onLeaveResult(false, errorCode, errorMessage);
                                }
                            }
                        });
                    }
                });
            } else {
                if (callback != null) {
                    callback.onLeaveResult(false, 0, "");
                }
            }
        }
    }

    @Override
    public void releaseMic() {
        XmLiveRoom.destroySharedInstance();
    }

    @NonNull
    @Override
    public IStreamManager createStreamManager() {
        EntMediaSideInfoManager mediaSideInfoManager = new EntMediaSideInfoManager();
        return new StreamManager(mediaSideInfoManager);
    }

    public IEntMessageManager getEntMessageManager() {
        return mEntMessageManager;
    }

    @Override
    public int getBusinessId() {
        return IBusinessIdConstants.BIZ_ID_PGC;
    }

    public PGCRoomCoreIMService getPgcRoomCoreIMService() {
        return pgcRoomCoreIMService;
    }

    @Override
    protected void reqOnlineUserListInner(@Nullable ChatRoomConnectionManager.ISendResultCallback<CommonEntOnlineUserRsp> callBack) {
        if (mEntMessageManager == null) {
            if (callBack != null) {
                callBack.onError(0, "");
            }
            return;
        }
        mEntMessageManager.reqOnlineUserList(new ChatRoomConnectionManager.ISendResultCallback<CommonEntOnlineUserRsp>() {
            @Override
            public void onSuccess(CommonEntOnlineUserRsp commonEntOnlineUserRsp) {
                if (callBack != null) {
                    callBack.onSuccess(commonEntOnlineUserRsp);
                }
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                if (callBack != null) {
                    callBack.onError(errorCode, errorMessage);
                }
            }
        });
    }

    @Override
    protected void reqSyncUserStatusInner(@Nullable ChatRoomConnectionManager.ISendResultCallback<CommonEntUserStatusSynRsp> callBack) {
        if (mEntMessageManager == null) {
            if (callBack != null) {
                callBack.onError(0, "");
            }
            return;
        }
        mEntMessageManager.reqSyncUserStatus(new ChatRoomConnectionManager.ISendResultCallback<CommonEntUserStatusSynRsp>() {
            @Override
            public void onSuccess(CommonEntUserStatusSynRsp commonEntOnlineUserRsp) {
                if (callBack != null) {
                    callBack.onSuccess(commonEntOnlineUserRsp);
                }
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                if (callBack != null) {
                    callBack.onError(errorCode, errorMessage);
                }
            }
        });
    }

    @NonNull
    @Override
    public String getCoreName() {
        return TAG;
    }

    @Override
    public void onHeadSetPlug(boolean isHeadSetOn) {
        super.onHeadSetPlug(isHeadSetOn);
        IXmMicService avService = getAvService();
        if (avService != null && avService.isPublish()) {
            // 耳机拔出来后，关闭耳返功能
            if (!isHeadSetOn) {
                avService.enableLoopback(false);
            } else {
                // 耳机插入后，用户已经开启耳返开关，则自动开启耳返功能
                if (getMineUserType() == BaseCommonProtoConstant.CommonUserType.USER_TYPE_PRESIDE
                        && getCurrentEnableLoopback()) {
                    avService.enableLoopback(true);
                    CustomToast.showToast("已开启耳返");
                }
            }
        }
    }
}
