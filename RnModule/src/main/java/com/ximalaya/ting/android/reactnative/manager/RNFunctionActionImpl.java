package com.ximalaya.ting.android.reactnative.manager;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;

import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactInstanceManagerBuilder;
import com.facebook.react.ReactPackage;
import com.facebook.react.ReactRootView;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.LifecycleState;
import com.facebook.react.packagerconnection.PackagerConnectionSettings;
import com.facebook.react.shell.MainReactPackage;
import com.ximalaya.reactnative.RNEnv;
import com.ximalaya.reactnative.XMReactNativeApi;
import com.ximalaya.reactnative.modules.XMReactPackage;
import com.ximalaya.reactnative.widgets.TipView;
import com.ximalaya.reactnative.widgets.XMReactRootView;
import com.ximalaya.reactnative.widgets.XMReactView;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.IRnViewLifeCycle;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.rn.RnMobDebugUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.search.SearchReactNativeSetting;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.base.IMediaPlayer;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.base.SimpleMediaPlayerFactory;
import com.ximalaya.ting.android.reactnative.RNApplication;
import com.ximalaya.ting.android.reactnative.fragment.ReactFragment;
import com.ximalaya.ting.android.reactnative.fragment.debug.DebugBundleListManager;
import com.ximalaya.ting.android.reactnative.fragment.debug.DebugReactPackage;
import com.ximalaya.ting.android.reactnative.fragment.debug.ReactTestFragment;
import com.ximalaya.ting.android.reactnative.modules.business.ReactViewDataHolder;
import com.ximalaya.ting.android.reactnative.trace.RNPerformance;
import com.ximalaya.ting.android.reactnative.trace.RNTrace;
import com.ximalaya.ting.android.reactnative.utils.AlarmUtil;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;
import com.ximalaya.ting.android.reactnative.widgets.ReactFragmentLoadingLayout;
import com.ximalaya.ting.android.reactnative.widgets.XMRNEmbeddedView;
import com.ximalaya.ting.android.reactnative.widgets.XmReactLifeCycleView;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * Created by Travis on 2018/6/6 上午11:30.
 *
 * <AUTHOR>
 */
public class RNFunctionActionImpl implements IRNFunctionRouter {
    private Boolean rnBundleSupportSetting;

    @Override
    public boolean isInit() {
        return RNApplication.getInstance().inited();
    }

    @Override
    public Class<? extends BaseFragment2> getRNFragmentClazz() {
        if (!RNUtils.isX86Arch()) {
            ReactRootView.sBegin = System.currentTimeMillis();
            return ReactFragment.class;
        }
        return null;
    }

    @Override
    public Class<? extends BaseFragment2> getRNTestFragmentClazz() {
        if (!RNUtils.isX86Arch()) {
            ReactRootView.sBegin = System.currentTimeMillis();
            return ReactTestFragment.class;
        }
        return null;
    }

    @Override
    public boolean isRNFragment(Fragment fragment) {
        return fragment instanceof ReactFragment || fragment instanceof ReactTestFragment;
    }

    @Override
    public boolean startRNPage(final Activity activity, Bundle data) {
        try {
            Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFragmentAction().startRNFragment(activity, "rn", data);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public void onReceiveAlarm(Context context, String action) {
        if (AppConstants.ACTION_RN_REPEAT_ALARM.equals(action) && AlarmUtil.getRepeatAlarmSetting(context) != null) {
            AlarmUtil.startRepeatAlarm(context);
        }
    }

    @Override
    public void onReceiveAlarm(Context context, Intent intent) {
        String action = intent.getAction();
        if (AppConstants.ACTION_RN_REPEAT_ALARM.equals(action) && AlarmUtil.getRepeatAlarmSetting(context) != null) {
            AlarmUtil.startRepeatAlarm(context);
        }
        if (AppConstants.ACTION_RN_COMMON_ALARM.equals(action) ||
                AppConstants.ACTION_RN_REPEAT_ALARM.equals(action)) {
            //播放声音
            String soundPath = intent.getStringExtra("soundPath");
            if (TextUtils.isEmpty(soundPath)) {
                return;
            }
            try {
                final IMediaPlayer player = SimpleMediaPlayerFactory.INS.createPlayer(context, SimpleMediaPlayerFactory.PlayerType.MEDIA_TYPE_SIMPLE);
                player.reset();
                player.setDataSource(soundPath);
                player.setOnPreparedListener(new IMediaPlayer.OnPreparedListener() {
                    @Override
                    public void onPrepared(IMediaPlayer iMediaPlayer) {
                        try {
                            player.start();
                        } catch (Throwable e) {
                            e.printStackTrace();
                        }
                    }
                });
                player.prepareAsync();
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }


    @Override
    public void onNewIntent(Activity activity, Intent intent) {
        if (activity instanceof MainActivity) {
            ManageFragment manageFragment = ((MainActivity) activity).getManageFragment();
            List<ManageFragment.MySoftReference> stacks = new ArrayList<>(manageFragment.mStacks);

            for (int i = stacks.size() - 1; i >= 0; i--) {
                ManageFragment.MySoftReference reference = stacks.get(i);
                Fragment fgm = reference.get();
                if ((fgm instanceof ReactFragment && TextUtils.equals(((ReactFragment) fgm).getBundleName(), "commonpayment")) || fgm instanceof ReactTestFragment) {
                    ((BaseFragment2) fgm).onNewIntent(intent);
                    break;
                }
            }

        }
    }

    @Override
    public View newXMReactView(BaseFragment2 fragment, String attachFragmentName) {
        Context context = BaseApplication.getMyApplicationContext();
        if (BaseApplication.getTopActivity() != null) {
            context = BaseApplication.getTopActivity();
        }

        //ReactLayout 使用 Fragment 的 loading

        // TODO: 2022/10/18 动态卡片是否使用全屏加载？根据位置判断，如果是第一个就这样，否则还是和普通一样加载
        final boolean useFragmentLoadingView = SearchReactNativeSetting.useFragmentLoadingView();

        final XMReactView xmReactView;
        if (useFragmentLoadingView) {
            xmReactView = new ReactFragmentLoadingLayout(context, fragment, null, null);
        } else {
            xmReactView = new XMReactView(context);
        }

        ReactViewDataHolder.getInstance().addView(fragment, attachFragmentName, xmReactView);
        return xmReactView;
    }

    @Override
    public IRnViewLifeCycle newXMReactViewLifeCycle() {
        Context context = BaseApplication.getMyApplicationContext();
        if (BaseApplication.getTopActivity() != null) {
            context = BaseApplication.getTopActivity();
        }

        return new XmReactLifeCycleView(context);
    }

    @Override
    public IRNEmbeddedView newRNEmbeddedView(Context context) {
        return newRNEmbeddedView(context, null);
    }

    @Override
    public IRNEmbeddedView newRNEmbeddedView(Context context, final IRNLoadingView customLoadingView) {
        if (context == null) {
            context = BaseApplication.getMyApplicationContext();
            if (BaseApplication.getTopActivity() != null) {
                context = BaseApplication.getTopActivity();
            }
        }

        TipView tipView = null;

        if (customLoadingView != null) {
            tipView = new TipView() {
                @Override
                public void showLoading() {
                    customLoadingView.showLoading();
                }

                @Override
                public void hideLoading() {
                    customLoadingView.hideLoading();
                }

                @Override
                public void showError(String s) {
                    customLoadingView.showError(s);
                }

                @Override
                public View getTipView() {
                    return customLoadingView.getTipView();
                }
            };
        }
        return new XMRNEmbeddedView(context, tipView);
    }

    @Override
    public boolean sameBundle(@NonNull View view, @NonNull String bundleName) {
        if (view instanceof XMReactView) {
            XMReactView xmReactView = (XMReactView) view;
            String loadedBundleName = xmReactView.getLoadedBundleName();
            Logger.d("z_rn_load_debug", "sameBundle , loadedBundleName: " + loadedBundleName);
            if (loadedBundleName != null) {
                return TextUtils.equals(loadedBundleName, bundleName);
            }
        }
        Object tag = view.getTag();
        Logger.d("z_rn_load_debug", "sameBundle , tag: " + tag);
        if (tag instanceof String) {
            return TextUtils.equals(tag.toString(), bundleName);
        }
        return false;
    }

    @Override
    public boolean checkBundleSupport(String bundleName) {

        if (rnBundleSupportSetting == null) {
            try {
                rnBundleSupportSetting = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "rn_bundle_support_check");
            } catch (Throwable ignore) {
            }
        }

        if (ConstantsOpenSdk.isDebug) {
            Log.d("z_test", "checkBundleSupport fromSetting:1 " + rnBundleSupportSetting);
        }
        if (rnBundleSupportSetting == null || !rnBundleSupportSetting) {
            //该功能没开启，默认支持
            return true;
        }

        if (RNEnv.application() == null) {
            //RN Application 还没初始化
            return true;
        }

        return XMReactNativeApi.checkBundleSupport(bundleName);
    }

    @Override
    public void loadBundle(View view, Activity activity,
                           final String bundleName, String viewSizeListenerKey,
                           Bundle bundle,
                           final ILoadBundleListener bundleListener,
                           final IRNFunctionRouter.ReactViewSizeChangeListener viewSizeChangeListener) {
        if (view instanceof XMReactView) {
            final XMReactView xmReactView = (XMReactView) view;

            final com.ximalaya.reactnative.ILoadBundleListener bundleLoadListener = new com.ximalaya.reactnative.ILoadBundleListener() {

                @Override
                public void onLoadingBundle(int i) {
                }

                @Override
                public void onLoadBundleSucceed() {
                    if (bundleListener != null) {
                        bundleListener.onLoadBundleSucceed();
                    }

                    String loadedBundleName = xmReactView.getLoadedBundleName();
                    String loadedBundleVersion = xmReactView.getLoadedBundleVersion();
                    RNPerformance.updateBundleInfo(loadedBundleName, loadedBundleVersion);
                    RNTrace.updateBundleInfo(loadedBundleName, loadedBundleVersion);

                    xmReactView.removeLoadBundleListener(this);
                }

                @Override
                public void onLoadBundleError() {
                    if (bundleListener != null) {
                        bundleListener.onLoadBundleError(bundleName);
                    }
                    xmReactView.removeLoadBundleListener(this);
                }
            };

            final boolean useFragmentLoadingView = SearchReactNativeSetting.useFragmentLoadingView();
            ReactViewSizeChangeListener changeListener = new ReactViewSizeChangeListener() {

                @Override
                public void onHeightChanged(int newHeight) {
                    if (viewSizeChangeListener != null) {
                        viewSizeChangeListener.onHeightChanged(newHeight);
                    }
                    if (useFragmentLoadingView && xmReactView instanceof ReactFragmentLoadingLayout) {
                        //在高度改变后再去掉加载进度
                        final ReactFragmentLoadingLayout loadingLayout = (ReactFragmentLoadingLayout)xmReactView;
                        loadingLayout.hideLoadingAfterDuration();
                    }
                }
            };

            ReactViewDataHolder.getInstance().addReactViewSizeChangeListener(viewSizeListenerKey, changeListener);

            RNPerformance.start(bundleName);

            xmReactView.addLoadBundleListener(bundleLoadListener);

            if (bundle != null && bundle.containsKey("setInitData")) {
                setInitData(bundle);
            }

            String debugSystemProperty = ToolUtil.getDebugSystemProperty("debug.xima.rn_load_by_ip", "-1");
            Logger.d("z_rn_load_debug", debugSystemProperty);
            if (ConstantsOpenSdk.isDebug && !debugSystemProperty.equals("-1")) {
                loadBundleByIp(xmReactView, debugSystemProperty, bundleName, bundle);
                return;
            }

//            addTestIpIfDebug(bundle);

            xmReactView.loadRNBundle(activity, bundleName, null, bundle);
        }
    }

    private void loadBundleByIp(XMReactView xmReactView, String serverIp, String bundleName, Bundle bundle) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(BaseApplication.getMyApplicationContext());
        SharedPreferences.Editor editor = preferences.edit();
        if (!com.ximalaya.ting.android.framework.arouter.utils.TextUtils.isEmpty(bundleName)) {
            editor.putString("debug_module_name", bundleName);
        }
        if (!com.ximalaya.ting.android.framework.arouter.utils.TextUtils.isEmpty(serverIp)) {
            editor.putString("debug_http_host", serverIp);
        }
        editor.commit();

        Context context = BaseApplication.getMyApplicationContext();

        ReactInstanceManagerBuilder builder = ReactInstanceManager.builder().setApplication(RNEnv.application())
                .addPackage(new MainReactPackage(RNEnv.buildRnOverrideColorScheme()))
                .addPackage(new XMReactPackage())
                .addPackage(new DebugReactPackage()).setUseDeveloperSupport(true)
                .setJSMainModulePath(new PackagerConnectionSettings(context).getLocalModuleName())
                .setInitialLifecycleState(LifecycleState.BEFORE_CREATE);
        List<ReactPackage> reactPackages = XMReactNativeApi.reactPackages();
        if (reactPackages != null && reactPackages.size() > 0) {
            builder.addPackages(reactPackages);
        }

        ReactInstanceManager manager = builder.build();

        XMReactRootView rootView = XMReactRootView.getReactRootView(BaseApplication.getTopActivity(), bundleName);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
        xmReactView.addView(rootView, 0, layoutParams);
        xmReactView.setTag(bundleName);

        rootView.setBundleName(bundleName);
        rootView.startReactApplication(manager, bundleName, bundle);

        ReactInstanceManager reactInstanceManager = rootView.getReactInstanceManager();
        if (reactInstanceManager != null) {
            reactInstanceManager.onHostResume(BaseApplication.getTopActivity(), null);
        }
    }

    /**
     * adb shell setprop debug.xmly.rn_test_ip 1
     * @param bundle
     */
    public static void addTestIpIfDebug(Bundle bundle, String requestBundleName) {
        if (!ConstantsOpenSdk.isDebug) {
            return;
        }

        RnMobDebugUtil.updateFragmentBundle(bundle, requestBundleName);
    }

    private void setInitData(Bundle bundle) {
        if (bundle == null) {
            return;
        }
        Bundle initData = RNUtils.getInitParam(BaseApplication.getMyApplicationContext(), bundle.getString("bundleName"));
        bundle.putBundle("initData", initData);
    }

    @Override
    public void saveBusinessDataForJS(String key, Bundle data) {
        ReactViewDataHolder.getInstance().addData(key, data);
    }

    @Override
    public Bundle getBusinessData(String key) {
        return ReactViewDataHolder.getInstance().getData(key);
    }

    @Override
    public void removeBusinessData(String key) {
        ReactViewDataHolder.getInstance().removeData(key);
    }

    @Override
    public int getViewHeightByConfig(String bundleName, int defaultHeight) {
        return ReactViewDataHolder.getInstance().getViewHeightByConfig(bundleName, defaultHeight);
    }

    @Override
    public void sendEvent(View view, String event, @Nullable Map<String, Object> params) {
        ReactContext context = null;
        if (view instanceof XMReactView) {
            XMReactView reactView = (XMReactView) view;
            context = reactView.getReactApplicationContext();
        }
        if (context != null) {
            WritableMap map = Arguments.makeNativeMap(params);
            RNUtils.sendEvent(context, event, map);
        }
    }

    @Override
    public void sendEvent(Context context, String event, @Nullable Map<String, Object> params) {
        if (context instanceof ReactContext) {
            WritableMap map = Arguments.makeNativeMap(params);
            RNUtils.sendEvent((ReactContext) context, event, map);
        }
    }

    @Override
    public void releaseBusinessDataAndListeners(BaseFragment2 fragment) {
        ReactViewDataHolder.getInstance().clearDataAndListeners(fragment);
    }

    @Override
    public void showDebugBundleListDialog(Context context) {
        DebugBundleListManager.showDialog(context);
    }

    @Override
    public void addDataListener(String bundleName, @Nullable IDataListener listener) {
        ReactViewDataHolder.getInstance().addDataListener(bundleName, listener);
    }

    @Override
    public void startRnPerformance(String bundleName) {
        RNPerformance.start(bundleName);
    }

    @Override
    public void stopRnPerformance() {
        RNPerformance.stop();
    }


}
