package com.ximalaya.ting.android.reactnative.playpage

import com.facebook.react.bridge.*
import com.facebook.react.module.annotations.ReactModule
import com.ximalaya.ting.android.framework.download.DownloadLiteManager.DownloadCallback
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.reactnative.utils.RNUtils
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.Boolean

@ReactModule(name = PlayPageSkinPackModule.NAME)
class PlayPageSkinPackModule(
    private val reactContext: ReactApplicationContext?
) : ReactContextBaseJavaModule(reactContext) {
    companion object {
        const val NAME = "XMPlayPageSkinPackageModule"
        private val PROMISE_RESULT_SUCCESS = Boolean.TRUE.toString()
        private val PROMISE_RESULT_FAIL = Boolean.FALSE.toString()
        private const val ERROR_CODE_UNZIP = "UNZIP_ERROR"
        private const val ERROR_CODE_DOWNLOAD = "DOWNLOAD_ERROR"
        private const val EVENT_ON_SKIN_DOWNLOAD_ERROR = "onSkinDownloadError"
        private const val EVENT_ON_SKIN_DOWNLOAD_COMPLETE = "onSkinDownloadComplete"
        private const val EVENT_ON_SKIN_DOWNLOAD_PROGRESS = "onSkinDownloadProgress"
        private const val EVENT_ON_SKIN_DOWNLOAD_START = "onSkinDownloadStart"
    }

    override fun getName() = NAME

    private fun mainFunc() = kotlin.runCatching {
        Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)?.functionAction
    }.getOrNull()


    @ReactMethod
    fun download(data: ReadableMap, promise: Promise) {
        val mainFunc = mainFunc()
        if (mainFunc == null) {
            promise.reject(PROMISE_RESULT_FAIL, "mainFunc is null")
            return
        }
        val skinId = data.getString("skinId")
        val skinUrl = data.getString("url")
        if (skinId.isNullOrEmpty() || skinUrl.isNullOrEmpty()) {
            promise.reject(PROMISE_RESULT_FAIL, "data.skinId or data.url is null")
            return
        }
        val startMap = Arguments.createMap()
        startMap.putString("skinId", skinId)
        RNUtils.sendEvent(reactApplicationContext, EVENT_ON_SKIN_DOWNLOAD_START, startMap)
        mainFunc.downloadSkinPack(skinId, skinUrl, object : DownloadCallback {
            override fun onError(url: String?) {
                val errorMap = Arguments.createMap()
                errorMap.putString("skinId", skinId)

                if (url?.startsWith("$ERROR_CODE_UNZIP-$skinId") == true) {
                    errorMap.putString("error",ERROR_CODE_UNZIP)
                    RNUtils.sendEvent(reactApplicationContext, EVENT_ON_SKIN_DOWNLOAD_ERROR, errorMap)
                } else if (url?.startsWith("$ERROR_CODE_DOWNLOAD-$skinId") == true) {
                    errorMap.putString("error",ERROR_CODE_DOWNLOAD)
                    RNUtils.sendEvent(reactApplicationContext, EVENT_ON_SKIN_DOWNLOAD_ERROR, errorMap)
                } else {
                    if (url?.startsWith(skinId) == true) {
                        errorMap.putString("error","undefined")
                        RNUtils.sendEvent(
                            reactApplicationContext,
                            EVENT_ON_SKIN_DOWNLOAD_ERROR,
                            errorMap
                        )
                    }
                }

            }

            var lastProgress = 0
            override fun onProgressUpdate(url: String?, percent: Int) {
                if (url == skinUrl) {
                    if (lastProgress == 0 || percent - lastProgress >= 1) {
                        val progressMap = Arguments.createMap()
                        progressMap.putString("skinId", skinId)
                        progressMap.putInt("progress", percent)
                        RNUtils.sendEvent(
                            reactApplicationContext,
                            EVENT_ON_SKIN_DOWNLOAD_PROGRESS,
                            progressMap
                        )
                        log(EVENT_ON_SKIN_DOWNLOAD_PROGRESS+",skinId:$skinId,progress:$percent")
                        lastProgress = percent;
//                        if (percent >= 100) {
//                            val completeMap = Arguments.createMap()
//                            completeMap.putString("skinId", skinId)
//                            RNUtils.sendEvent(
//                                reactApplicationContext,
//                                EVENT_ON_SKIN_DOWNLOAD_COMPLETE,
//                                completeMap
//                            )
//                            log(EVENT_ON_SKIN_DOWNLOAD_COMPLETE)
//                        }
                    }
                }
            }

            override fun onSuccess(url: String?) {
                log("onSuccess:url:$url, skinUrl:$skinUrl")
                if (url == skinUrl) {
                    val successMap = Arguments.createMap()
                    successMap.putString("skinId", skinId)
                    RNUtils.sendEvent(reactApplicationContext, EVENT_ON_SKIN_DOWNLOAD_COMPLETE, successMap)
                }
            }

        })
        promise.resolve(PROMISE_RESULT_SUCCESS)
    }

    @ReactMethod
    fun getSkinDownloadStatus(data: ReadableMap, promise: Promise) {
        val mainFunc = mainFunc()
        if (mainFunc == null) {
            promise.reject(PROMISE_RESULT_FAIL, "mainFunc is null")
            return
        }
        val skinId = data.getString("skinId")
        val skinUrl = data.getString("url")
        if (skinId.isNullOrEmpty() || skinUrl.isNullOrEmpty()) {
            promise.reject(PROMISE_RESULT_FAIL, "data.skinId or data.url is null")
            return
        }
        val status = mainFunc.getSkinDownloadStatus(skinId, skinUrl)
        if (status) {
            promise.resolve(PROMISE_RESULT_SUCCESS)
        } else {
            promise.reject(PROMISE_RESULT_FAIL,"skin did not download")
        }
    }

    @ReactMethod
    fun clearSkin(promise: Promise) {
        val mainFunc = mainFunc()
        if (mainFunc == null) {
            promise.reject(PROMISE_RESULT_FAIL, "mainFunc is null")
            return
        }
        mainFunc.clearPlaypageSkin()
        promise.resolve(PROMISE_RESULT_SUCCESS)
    }


    private fun log(msg: String) {
        Logger.d(NAME, msg)
    }
}