package com.ximalaya.ting.android.reactnative.playpage

import com.facebook.react.TurboReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.module.annotations.ReactModuleList
import com.facebook.react.module.model.ReactModuleInfo
import com.facebook.react.module.model.ReactModuleInfoProvider
import com.facebook.react.turbomodule.core.interfaces.TurboModule
import com.facebook.react.uimanager.ViewManager
import com.ximalaya.ting.android.reactnative.share.ReactPlayShareManager

@ReactModuleList(nativeModules = [
    PlayPageBridgeModule::class,
    XMPlayPageRNBridgeNew::class,
    PlayerBridgeModule::class,
    PlayPageAdBridgeModule::class,
    XMModalModule::class,
    PlayPageSkinPackModule::class
])
class PlayPagePackage : TurboReactPackage() {

    override fun getModule(name: String?, reactContext: ReactApplicationContext?): NativeModule? {
        val rnContext = reactContext?: return null
        return when (name) {
            PlayPageBridgeModule.NAME -> PlayPageBridgeModule(rnContext)
            XMPlayPageRNBridgeNew.NAME -> XMPlayPageRNBridgeNew(rnContext)
            PlayerBridgeModule.NAME -> PlayerBridgeModule(rnContext)
            PlayPageAdBridgeModule.NAME -> PlayPageAdBridgeModule(rnContext)
            XMModalModule.NAME -> XMModalModule(rnContext)
            YPlayPageYellowBarRnBridge.NAME -> YPlayPageYellowBarRnBridge(rnContext)
            PlayPageLargeAdRnBridge.NAME -> PlayPageLargeAdRnBridge(rnContext)
            PlayPageSkinPackModule.NAME -> PlayPageSkinPackModule(rnContext)
            else -> null
        }
    }

    override fun createViewManagers(p0: ReactApplicationContext): List<ViewManager<*, *>> {
        val viewManagers: MutableList<ViewManager<*, *>> = mutableListOf()
        viewManagers.add(ReactPlayShareManager())
        viewManagers.add(PlayPageAdViewManager())
        viewManagers.add(YPlayPageYellowBarViewManager())
        viewManagers.add(YPlayPageLargeAdViewManager())
        return viewManagers
    }

    override fun getReactModuleInfoProvider(): ReactModuleInfoProvider {
        return try {
            val reactModuleInfoProviderClass =
                Class.forName("com.ximalaya.ting.android.reactnative.playpage.PlayPagePackage\$ReactModuleInfoProvider")
            (reactModuleInfoProviderClass.newInstance() as ReactModuleInfoProvider)!!
        } catch (e: ClassNotFoundException) {
            val moduleList: List<Class<out NativeModule?>> = listOf(
                PlayPageBridgeModule::class.java,
                PlayPageAdBridgeModule::class.java,
                XMPlayPageRNBridgeNew::class.java,
                XMModalModule::class.java,
                YPlayPageYellowBarRnBridge::class.java,
                PlayPageLargeAdRnBridge::class.java,
                PlayPageSkinPackModule::class.java
            )
            val reactModuleInfoMap = moduleList.associate {
                val annotation = it.getAnnotation(ReactModule::class.java)
                annotation.name to ReactModuleInfo(
                    annotation.name,
                    it.name,
                    annotation.canOverrideExistingModule,
                    annotation.needsEagerInit,
                    annotation.hasConstants,
                    annotation.isCxxModule,
                    TurboModule::class.java.isAssignableFrom(it)
                )
            }
            ReactModuleInfoProvider { reactModuleInfoMap }
        } catch (e: InstantiationException) {
            throw RuntimeException(
                "No ReactModuleInfoProvider for CoreModulesPackage$\$ReactModuleInfoProvider", e
            )
        } catch (e: IllegalAccessException) {
            throw RuntimeException(
                "No ReactModuleInfoProvider for CoreModulesPackage$\$ReactModuleInfoProvider", e
            )
        }
    }
}