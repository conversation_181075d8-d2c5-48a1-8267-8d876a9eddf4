package com.ximalaya.ting.android.reactnative.modules;

import android.widget.FrameLayout;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.ad.RnInspireAdManager;
import com.ximalaya.ting.android.host.view.ad.RnInspireAdView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import java.util.Map;

/**
 * 为rn提供唤端广告
 */
public class InspireAdViewManager extends SimpleViewManager<FrameLayout> {
    private static final String NAME = "InspireAdView";

    private String lastOperationId;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    protected RnInspireAdView createViewInstance(final ThemedReactContext reactContext) {
        lastOperationId = null;
        return new RnInspireAdView(reactContext);
    }

    @ReactProp(name = "bindData")
    public void bindData(final RnInspireAdView layout, ReadableMap data) {
        int slotId = data.getInt("slot_id");
        int rewardCoin = data.getInt("rewardCoin");
        String sourceName = data.getString("sourceName");
        String operationId = data.hasKey("operationId") ? data.getString("operationId") : null;
        if (lastOperationId != null && lastOperationId.equals(operationId)) {
            return;
        }
        lastOperationId = operationId;

        RnInspireAdManager.loadRnInspireAd(layout, slotId, sourceName, rewardCoin, new RnInspireAdManager.ILoadInspireAdCallback() {
            @Override
            public void onLoadSuccess(INativeAd nativeAd, int adHeight) {
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onHeightChange height =" + adHeight);
                }
                WritableMap event = Arguments.createMap();
                event.putInt("height", adHeight);
                ThemedReactContext reactContext = (ThemedReactContext) layout.getContext();
                reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(layout.getId(), "onHeightChange", event);
            }

            @Override
            public void onLoadFail() {
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onHeightChange height =0");
                }
                WritableMap event = Arguments.createMap();
                event.putInt("height", 0);
                ThemedReactContext reactContext = (ThemedReactContext) layout.getContext();
                reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(layout.getId(), "onHeightChange", event);
            }

            @Override
            public void onAdClick() {
                ThemedReactContext reactContext = (ThemedReactContext) layout.getContext();
                reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(layout.getId(), "onAdClick", null);
            }
        });
    }

    @Override
    public Map<String, Object> getExportedCustomDirectEventTypeConstants() {
        return MapBuilder.<String, Object>builder()
                .put("onHeightChange", MapBuilder.of("registrationName", "onHeightChange"))
                .put("onAdClick", MapBuilder.of("registrationName", "onAdClick"))
                .build();
    }
}
