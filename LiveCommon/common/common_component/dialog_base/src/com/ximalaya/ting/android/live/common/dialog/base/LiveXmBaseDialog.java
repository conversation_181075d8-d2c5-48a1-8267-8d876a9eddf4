package com.ximalaya.ting.android.live.common.dialog.base;

import android.app.Dialog;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.live.common.lib.base.dialog_queue.LiveDialogFragmentManager;
import com.ximalaya.ting.android.live.common.view.clearScreen.LiveClearScreenManager;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * XmBaseDialog的live基类------请优先使用LiveBaseDialogFragment(本类逐渐过度废弃)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phone 15026804470
 */
public class LiveXmBaseDialog<T extends Dialog> extends XmBaseDialog<T> {

    private final String TAG = "LiveXmBaseDialog";
    public LiveXmBaseDialog(@NonNull Context context) {
        super(context);
    }

    public LiveXmBaseDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected LiveXmBaseDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    public void show() {
        Logger.d(TAG, "show()  " + this.getClass().getName());
        if (RoomDialogInterceptorManager.shouldInterceptor(TAG)) {
            return;
        }
        if (LiveClearScreenManager.getInstance().isIntercepetDialog(this.getClass().getName())) {
            Logger.d(TAG, "show()  return");
            return;
        }
        LiveDialogFragmentManager.INSTANCE.addNoQueueDialog(this);
        super.show();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        LiveDialogFragmentManager.INSTANCE.removeNoQueueDialog(this);
    }
}
