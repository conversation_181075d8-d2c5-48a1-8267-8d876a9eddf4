package com.ximalaya.ting.android.live.common.lib.gift.panel;

import static com.ximalaya.ting.android.live.common.lib.base.constants.LiveApmLogConstants.SEND_GIFT_OBJECT_NULL;
import static com.ximalaya.ting.android.live.common.lib.base.util.uppermodulehook.RoomCommonDebugConfigureKt.getGroupDebugConfig;
import static com.ximalaya.ting.android.live.common.lib.base.util.uppermodulehook.RoomCommonDebugConfigureKt.saveGroupDebugConfig;

import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFragmentAction;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.EncryptProxy;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.live.common.lib.GiftDataComparatorUsage;
import com.ximalaya.ting.android.live.common.lib.base.constants.ConsumeLimitResCode;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.structure.set.LimitLinkedHashSet;
import com.ximalaya.ting.android.live.common.lib.entity.GiftExtInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.CommonResponse;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftPanelActivityTabInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftSendResult;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.PackageInfo;
import com.ximalaya.ting.android.live.common.lib.manager.anim.download.LiveApmReport;
import com.ximalaya.ting.android.live.common.lib.utils.CollectionsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.gift.SendGiftHelper;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 礼物信息加载实现类，承载礼物、背包信息加载
 * 包含预加载逻辑，直播页初始化时；直播间内触发礼物查询时没有查到指定礼物时；
 *
 * <AUTHOR>
 */
public abstract class BaseGiftLoader<T extends SendGiftDialog<?>> {
    public static final String TAG = "GiftPreLoader";

    private static final Map<Class<?>, BaseGiftLoader<?>> registryMap = new ConcurrentHashMap<>();

    /**
     * 收到的礼物消息订单缓存，用于本地兜底去重，设置上限 200 条，溢出后丢弃最旧 20 条缓存
     */
    private static final LimitLinkedHashSet<String> mGiftMsgOrderCache =
            new LimitLinkedHashSet<>(200, 20);

    private T mBaseDialog;

    /**
     * 根据礼物 id 存储礼物信息，方便查询
     */
    private static final ArrayMap<Long, GiftInfoCombine.GiftInfo> mGiftInfoMap = new ArrayMap<>();

    /**
     * 礼物聚合类信息
     */
    private static GiftInfoCombine mGiftInfoCombine = new GiftInfoCombine();
    /**
     * 礼物/背包面板配置活动入口信息，如新用户1元首冲
     */
    private GiftPanelActivityTabInfo mActivityTabInfo;

    /**
     * 礼物信息 LiveData，用于同步组件间礼物信息
     */
    private MutableLiveData<GiftInfoCombine> mGiftInfoCombineLiveData;
    /**
     * 背包红点 LiveData，同步背包红点信息
     */
    private MutableLiveData<PackageInfo.RedPoint> mShowRedPointLiveData;

    private boolean mIsRequesting;

    @BaseScrollConstant.LiveRoomBizType
    private int mBizType;
    private long mLiveId;
    @GiftInfoCombine.GiftInfo.IGiftDynamicType
    private int mDynamicType;

    public boolean mShowPackageUseDetail;
    private boolean roomPackage;

    protected BaseGiftLoader() {

    }

    /**
     * 为子类提供单例
     *
     * @param clazz 子类 class
     * @param <P>   子类
     * @return 子类单例
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public synchronized static <P extends BaseGiftLoader<?>> P getInstance(final Class<P> clazz) {
        if (!registryMap.containsKey(clazz)) {
            P p = null;
            try {
                p = clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                e.printStackTrace();
            }

            registryMap.put(clazz, p);
            return p;
        }

        return (P) registryMap.get(clazz);
    }

    public synchronized static void release(final Class<?> clazz) {
        registryMap.remove(clazz);
        mGiftInfoMap.clear();
        mGiftMsgOrderCache.clear();
    }

    public void setDialog(T dialog) {
        this.mBaseDialog = dialog;
    }

    public T getDialog() {
        return mBaseDialog;
    }

    public String getReceiverUidList() {
        return "";
    }

    protected HashMap<String, String> buildLoadGiftListParams() {
        HashMap<String, String> params = new HashMap<>();

        params.put(ParamsConstantsInLive.GIFT_SHOW_TYPE, String.valueOf(getShowType()));
        if (mBaseDialog != null) {
            params.put(ParamsConstantsInLive.ROOM_ID, String.valueOf(mBaseDialog.getRoomId()));
        }

        return params;
    }

    public boolean isValidGift(@Nullable String giftOrderNum) {
        if (TextUtils.isEmpty(giftOrderNum)) return true;

        if (mGiftMsgOrderCache.contains(giftOrderNum)) {
            LiveXdcsUtil.doXDCS("Gift_Order", "重复礼物消息，订单号(" + giftOrderNum + ")");
            return false;
        }

        mGiftMsgOrderCache.add(giftOrderNum);
        return true;
    }

    /**
     * 获取礼物列表 强制更新
     */
    public MutableLiveData<GiftInfoCombine> updateGiftList() {
        updateLiveGiftList();
        return getGiftInfoCombineLiveData();
    }

    /**
     * 查询本地缓存礼物信息，如本地无缓存，返回 null，同时发起服务端请求，缓存最新数据
     *
     * @param giftId 礼物 id
     * @return 礼物完整信息
     */
    @Nullable
    public GiftInfoCombine.GiftInfo getGift(long giftId) {
        if (giftId <= 0) {
            return null;
        }
        GiftInfoCombine.GiftInfo info = mGiftInfoMap.get(giftId);
        if (info == null) {
            updateLiveGiftList();
            String report = "getGift1, giftId:" + giftId
                    + "| stackTrack:" + Log.getStackTraceString(new Throwable());
            LiveXdcsUtil.doXDCS("Gift_Null", report);
        } else {
            return info;
        }

        return null;
    }

    /**
     * 查询本地缓存礼物信息，不请求服务端接口，缓存最新数据
     *
     * @param giftId 礼物id
     * @return 礼物信息
     */
    public GiftInfoCombine.GiftInfo getLocalGift(long giftId) {
        if (giftId <= 0) {
            return null;
        }
        GiftInfoCombine.GiftInfo info = mGiftInfoMap.get(giftId);
        if (info == null) {
            String report = "getLocalGift, giftId:" + giftId
                    + "| stackTrack:" + Log.getStackTraceString(new Throwable());
            LiveXdcsUtil.doXDCS("Gift_Null", report);
        } else {
            return info;
        }
        return null;
    }

    /**
     * 查询礼物信息，如本地无缓存，则请求服务端数据
     *
     * @param giftId 礼物id
     * @param cb     查询结果回调
     */
    public void getGift(long giftId, OnGetGiftCallback cb) {
        if (giftId <= 0) {
            LiveXdcsUtil.doXDCS("Gift_Null", "getGift2, giftId = " + giftId
                    + ", stackTrack:" + Log.getStackTraceString(new Throwable()));
            if (null != cb) {
                cb.onError(giftId);
            }
            return;
        }

        GiftInfoCombine.GiftInfo info = mGiftInfoMap.get(giftId);
        if (info == null) {
            getGiftById(giftId, cb);
            String report = "getGift2 giftId:" + giftId
                    + "| stackTrack:" + Log.getStackTraceString(new Throwable());
            LiveXdcsUtil.doXDCS("Gift_Null", report);
        } else {
            cb.onSuccess(info);
        }
    }

    public void getGiftById(long giftId, OnGetGiftCallback cb) {
        HashMap<String, String> params = new HashMap<>();
        params.put("giftIds", String.valueOf(giftId));
        CommonRequestForCommon.getGiftInfoByGiftIds(params, new IDataCallBack<List<GiftInfoCombine.GiftInfo>>() {
            @Override
            public void onSuccess(@Nullable List<GiftInfoCombine.GiftInfo> data) {
                Logger.i(TAG, "id请求礼物信息: " + data);
                if (cb == null) {
                    return;
                }
                if (data != null && !CollectionsUtil.isEmpty(data)) {
                    for (GiftInfoCombine.GiftInfo giftInfo : data) {
                        if (giftInfo != null && giftInfo.id == giftId) {
                            mGiftInfoMap.put(giftInfo.id, giftInfo);
                            cb.onSuccess(giftInfo);
                            return;
                        }
                    }
                }
                Logger.i(TAG, "id请求礼物信息data是空的或者giftInfo.id != giftId: " + giftId + ",data:" + data);
                getLocalGiftAndCallback(giftId, cb);
            }

            @Override
            public void onError(int code, String message) {
                Logger.i(TAG, "id请求礼物信息onError:" + code + message);
                if (cb == null) {
                    return;
                }
                getLocalGiftAndCallback(giftId, cb);
            }
        });
    }

    /**
     * 已经请求过一次接口了，直接使用本地的结果来查询
     */
    private void getLocalGiftAndCallback(long giftId, @NotNull OnGetGiftCallback cb) {
        GiftInfoCombine.GiftInfo gift = getLocalGift(giftId);
        if (gift != null) {
            cb.onSuccess(gift);
        } else {
            cb.onError(giftId);
        }
    }

    /**
     * 查询礼物封面链接，如本地无缓存，返回空字符串
     *
     * @param giftId 礼物 id
     * @return 礼物封面链接
     */
    public String getGiftPath(long giftId) {
        if (giftId <= 0) {
            Logger.e(TAG, "obtainGiftUrl error , gift id = " + giftId);
            return "";
        }

        GiftInfoCombine.GiftInfo info = getLocalGift(giftId);
        String path = null;
        if (info != null) {
            if (!TextUtils.isEmpty(info.webpCoverPath)) {
                path = info.webpCoverPath;
            } else if (!TextUtils.isEmpty(info.coverPath)) {
                path = info.coverPath;
            }
        }
        Logger.d(TAG, "obtainGiftUrl , info = " + info);

        return path;
    }

    /**
     * 查询本地缓存的背包信息
     *
     * @param packageId 背包id
     * @return 背包信息
     */
    public PackageInfo.Item getPackageItem(long packageId) {
        if (mGiftInfoCombine != null && mGiftInfoCombine.packageInfo != null && !ToolUtil.isEmptyCollects(mGiftInfoCombine.packageInfo.items)) {
            for (PackageInfo.Item item : mGiftInfoCombine.packageInfo.items) {
                if (item != null && item.id == packageId) {
                    return item;
                }
            }
        }

        return null;
    }

    /**
     * 查询本地缓存的背包封面链接
     *
     * @param packageId 背包id
     * @return 背包封面链接
     */
    public String getPackageItemPath(long packageId) {
        if (mGiftInfoCombine != null && mGiftInfoCombine.packageInfo != null && !ToolUtil.isEmptyCollects(mGiftInfoCombine.packageInfo.items)) {
            for (PackageInfo.Item item : mGiftInfoCombine.packageInfo.items) {
                if (item != null && item.id == packageId) {
                    return item.avatar;
                }
            }
        }

        return null;
    }

    /**
     * 查询本地缓存的礼物名字
     *
     * @param giftId 礼物 id
     * @return 礼物名字
     */
    public String getGiftName(long giftId) {
        if (giftId <= 0) {
            return null;
        }

        return getLocalGift(giftId) != null ? getLocalGift(giftId).name : "礼物";
    }

    /**
     * 查询本地缓存的礼物扩展信息
     *
     * @param giftId 礼物id
     * @return 礼物扩展信息
     */
    public GiftExtInfo getGiftExtInfo(long giftId) {
        if (giftId <= 0) {
            return null;
        }
        GiftInfoCombine.GiftInfo info = getLocalGift(giftId);
        if (info == null) {
            return null;
        }
        return info.extInfo;
    }

    public int getBizType() {
        return mBizType;
    }

    public BaseGiftLoader<?> setBizType(@BaseScrollConstant.LiveRoomBizType int bizType) {
        this.mBizType = bizType;
        return this;
    }

    public long getLiveId() {
        return mLiveId;
    }

    public BaseGiftLoader<?> setLiveId(long mLiveId) {
        this.mLiveId = mLiveId;
        return this;
    }

    public long getRoomId() {
        return SendGiftHelper.getInstance().getRoomId();
    }

    @GiftInfoCombine.GiftInfo.IGiftDynamicType
    public int getDynamicType() {
        return mDynamicType;
    }

    /**
     * 因为如果是onReceiveComboBigGiftMessage则消息传的是小礼物的giftId和动画地址，因此动画类型要先从animationPath判断
     *
     * @param giftId        礼物id
     * @param animationPath 动画地址
     * @return mDynamicType
     */
    public BaseGiftLoader<?> setDynamicType(long giftId, String animationPath) {
        if (TextUtils.isEmpty(animationPath)) {
            this.mDynamicType = getLocalGift(giftId) != null ? getLocalGift(giftId).dynamicType : -1;
        } else {
            if (animationPath.endsWith(".mp4")) {
                this.mDynamicType = GiftInfoCombine.GiftInfo.IGiftDynamicType.GIFT_DYNAMIC_TYPE_MP4;
            } else if (animationPath.endsWith(".svga")) {
                this.mDynamicType = GiftInfoCombine.GiftInfo.IGiftDynamicType.GIFT_DYNAMIC_TYPE_SVGA;
            }
        }
        return this;
    }

    /**
     * 查询本地缓存礼物礼物大动画资源id，如本地无缓存，返回 0，同时发起服务端请求，缓存最新数据
     *
     * @param giftId 礼物id
     * @return 礼物大动画资源id
     */
    public long getGiftAnimationId(long giftId) {
        if (giftId <= 0) {
            return 0;
        }

        return getLocalGift(giftId) != null ? getLocalGift(giftId).animationId : 0;
    }

    public MutableLiveData<GiftInfoCombine> getGiftInfoCombineLiveData() {
        if (null == mGiftInfoCombineLiveData) {
            mGiftInfoCombineLiveData = new MutableLiveData<>();
        }
        return mGiftInfoCombineLiveData;
    }

    public MutableLiveData<PackageInfo.RedPoint> getShowRedPointLiveData() {
        if (null == mShowRedPointLiveData) {
            mShowRedPointLiveData = new MutableLiveData<>();
        }
        return mShowRedPointLiveData;
    }

    /**
     * 初始化礼物数据成功后获取背包信息，无论获取背包信息成功或失败都通知礼物面板
     */
    public void updatePackageInfo(boolean canRemove) {
        if (canRemove) {
            return;
        }
        if (isNeedPackage()) {

            Map<String, String> params = new HashMap<>();
            if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_LIVE) {
                params.put(
                        ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                        String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_LIVE)
                );
            } else if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_KTV) {
                params.put(
                        ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                        String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_KTV)
                );
            } else if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_HALL) {
                params.put(
                        ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                        String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_HALL)
                );
            } else if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_UGC) {
                params.put(
                        ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                        String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_UGC)
                );
            } else {
                params.put(
                        ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                        String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_LIVE)
                );
            }
            Logger.i(TAG, "请求背包 " + getRoomId());
            CommonRequestForCommon.getPackageInfo(getPackageListUrl(), params
                    , new IDataCallBack<PackageInfo>() {
                        @Override
                        public void onSuccess(@Nullable PackageInfo object) {
                            if (mGiftInfoCombine == null) {
                                mGiftInfoCombine = new GiftInfoCombine();
                            }
                            mGiftInfoCombine.packageInfo = object;
                            Logger.i(TAG, "背包礼物获取成功 roomId " + getRoomId());
                            Logger.i(TAG, "背包礼物获取成功 " + object);

                            if (object != null) {
                                mShowPackageUseDetail = object.showPackageOrderDetail;
                            }

                            if (isShowBrocadeBag()) {
                                updateRoomBrocadeBag();
                            } else {
                                setValue(mGiftInfoCombine);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            // 用缓存数据
                            if (null == mGiftInfoCombine.packageInfo) {
                                mGiftInfoCombine.packageInfo = new PackageInfo();
                            }
                            if (isShowBrocadeBag()) {
                                updateRoomBrocadeBag();
                            } else {
                                setValue(mGiftInfoCombine);
                            }
                        }
                    });
        } else {
            mGiftInfoCombine.packageInfo = new PackageInfo();
            setValue(mGiftInfoCombine);
        }
    }

    /**
     * 背包物品使用
     *
     * @param item           PackageInfo.Item
     * @param receiverUid    收礼人uid
     * @param num            数量
     * @param repeat         是否连击
     * @param conseUnifiedNo 连击标识
     * @param callback       连击回调接口
     */
    public void usePackageItem(final PackageInfo.Item item, long receiverUid, int num, boolean repeat, long conseUnifiedNo, BaseGiftLoader.PackageSendSessionCallback callback) {
        if (null != item && 0 < item.id) {
            int mediaType = LiveMediaType.TYPE_AUDIO;
            if (mBaseDialog != null && mBaseDialog.mMediaType == LiveMediaType.TYPE_VIDEO) {
                mediaType = LiveMediaType.TYPE_VIDEO;
            }
            // apm传递参数
            int bizType = 0;
            long liveId = 0, roomId = 0;
            if (mBaseDialog != null) {
                liveId = mBaseDialog.getLiveId();
                roomId = mBaseDialog.getRoomId();
                bizType = mBaseDialog.mBizType;
            }
            int finalBizType = bizType;
            long finalLiveId = liveId;
            long finalRoomId = roomId;
            roomPackage = item.hasRoomPackageTip;

            CommonRequestForCommon.usePackageItem(getUsePackageItemUrl()
                    , buildUsePackageItemParams(item.id, receiverUid, item.expireAtTimestamp, num, mediaType, item.referId, repeat, conseUnifiedNo, roomPackage),
                    new IDataCallBack<CommonResponse>() {
                        @Override
                        public void onSuccess(@Nullable CommonResponse object) {
                            // toast服务端返回的message
                            if (object != null && object.data != null && !TextUtils.isEmpty(object.data.msg)) {
                                CustomToast.showSuccessToast(object.data.msg);
                            }

                            // 除礼物外物品使用成功提示
                            if (PackageInfo.Item.isPackageGiftType(item)) {
                                if (object == null || object.data == null || TextUtils.isEmpty(object.data.msg)) {
                                    CustomToast.showSuccessToast("使用成功");
                                }
                            }
                            if (PackageInfo.Item.TYPE_DANMU_CARD == item.customInnerType) {
                                showKeyBoardPanel();
                            }
                            LiveApmReport.apmLogPackageUse(true, finalBizType, item.type, item.getId(), receiverUid, finalLiveId, finalRoomId, null, null);
                            // 使用成功，更新背包物品列表信息
                            if (roomPackage) {
                                updateRoomBrocadeBag();
                            } else {
                                updatePackageInfo(false);
                            }
                            if (callback != null) {
                                callback.onSuccess();
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            if (ConsumeLimitResCode.isNeedLimitPackageConsume(code)) {
                                //限制消费
                                MainActivity mainActivity = SendGiftHelper.getInstance().getMainActivity();
                                if (mainActivity != null) {
                                    LiveHelper.handleConsumeLimitWarning(mainActivity, code, message, null);
                                }
                            } else {
                                CustomToast.showFailToast(message);
                                // 物品过期，更新背包物品列表信息
                                if (code == 1009) {
                                    if (roomPackage) {
                                        updateRoomBrocadeBag();
                                    } else {
                                        updatePackageInfo(false);
                                    }
                                }
                                LiveApmReport.apmLogPackageUse(false, finalBizType, item.type, item.getId(), receiverUid, finalLiveId, finalRoomId, code, message);
                            }
                            if (callback != null) {
                                callback.onFail(code, message);
                            }
                        }
                    });
        }
    }

    /**
     * 是否背包连击礼物类型
     */
    private boolean isPackageGiftBatter(long referId) {
        GiftInfoCombine.GiftInfo gift = getGift(referId);
        if (gift == null) {
            CommonLiveLogger.d(TAG, "isPackageGiftBatter gift == null:" + referId);
            LiveXdcsUtil.doXDCS(TAG, "isPackageGiftBatter gift == null:" + referId);
            return false;
        }
        return gift.isConsecutive;
    }

    /**
     * 背包礼物是否支持连击
     *
     * @param referId 关联id
     * @return true 支持，false 不支持
     */
    public boolean isSupportPackageGiftBatter(long referId) {
        return isPackageGiftBatter(referId);
    }

    private void showKeyBoardPanel() {
        if (mBaseDialog == null) {
            return;
        }
        Intent intent = new Intent(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_OPEN_INPUT_COMPONENT);
        LocalBroadcastManager.getInstance(mBaseDialog.getContext()).sendBroadcast(intent);
    }

    public void sendGiftWithToken(GiftInfoCombine.GiftInfo giftInfo,
                                  int giftNum,
                                  long receiverId,
                                  boolean isLiveGiftType,
                                  boolean repeat,
                                  long conseUnifiedNo,
                                  String wordGiftContent,
                                  final GiftSendSessionCallback sendCallback) {
        final long clickTime = System.currentTimeMillis();

        HashMap<String, String> params = buildSendCommonGiftParams(giftNum, giftInfo
                , receiverId, isLiveGiftType, repeat, conseUnifiedNo);

        EncryptProxy.getGiftSignature(params);

        if (repeat) {
            params.put(ParamsConstantsInLive.CONSEUNIFIED_NO, conseUnifiedNo + "");
            params.put(ParamsConstantsInLive.CONSECUTIVE, "true");
        } else if (isLiveGiftType && giftInfo.isFansGift()) {
            params.put(ParamsConstantsInLive.CONSECUTIVE, "false");
            params.put(ParamsConstantsInLive.CONSEUNIFIED_NO, "");
        }

        // wordGiftContent 不需要签名
        buildUnSignatureSendCommonGiftParams(params, giftInfo, wordGiftContent);

        //orderType不签名
        params.put(ParamsConstantsInLive.ORDER_TYPE, String.valueOf(SendGiftHelper.getInstance().getOrderType()));

        if (!checkSendParams(giftInfo, giftNum)) {
            if (sendCallback != null) {
                sendCallback.onSendFail(-1, "请选择赠送人");
            }
            return;
        }

        // apm传递参数
        int bizType = SendGiftHelper.getInstance().getRoomBizType();
        long liveId = SendGiftHelper.getInstance().getLiveId();
        long roomId = SendGiftHelper.getInstance().getRoomId();

        CommonRequestForCommon.sendLiveHostGift(getSendGiftUrl(giftInfo.giftType), params
                , new IDataCallBack<GiftSendResult>() {
                    @Override
                    public void onSuccess(GiftSendResult object) {
                        Logger.i(TAG, "sendGiftWithToken request success, response result :"
                                + (object != null ? object.toString() : null));

                        if (object == null) {
                            LiveApmReport.apmLogGiftSend(false, bizType, giftInfo.giftType, repeat, giftInfo.id,
                                    UserInfoMannage.getUid(), liveId, roomId, SEND_GIFT_OBJECT_NULL, "object == null");
                            CustomToast.showFailToast("送礼失败");
                            return;
                        }

                        Logger.i(TAG, "sendGiftWithToken success" + object);
                        LiveApmReport.apmLogGiftSend(true, bizType, giftInfo.giftType, repeat, giftInfo.id,
                                UserInfoMannage.getUid(), liveId, roomId, null, null);

                        // 送礼慢请求信息上报
                        if (System.currentTimeMillis() - clickTime > 3000) {
                            String report = "SendGift_SlowTimeLog" +
                                    "| responseTime :" + (System.currentTimeMillis() - clickTime);
                            LiveXdcsUtil.doXDCS("LiveGiftSend", report);
                        }

                        if (sendCallback != null) {
                            sendCallback.onSendSuccess(object.rank, object.contribution);
                        }

                        LiveHelper.sRecordCurrentUserHasSentGift = true;
                    }

                    @Override
                    public void onError(int code, String message) {
                        Logger.i(TAG, "sendGiftWithToken onError" + code + message);
                        boolean isNeedReport = (code != 3604 && code != 3 && code != 8001);
                        if (isNeedReport) {
                            LiveApmReport.apmLogGiftSend(false, bizType, giftInfo.giftType, repeat, giftInfo.id,
                                    UserInfoMannage.getUid(), liveId, roomId, code, message);
                        }
                        if (sendCallback != null) {
                            sendCallback.onSendFail(code, message);
                        }
                        //送礼失败上报
                        String report = "SendGift_FailLog" +
                                "| ErrorCode: " + code +
                                "| ErrorMsg: " + (TextUtils.isEmpty(message) ? "" : message) +
                                "| responseTime :" + (System.currentTimeMillis() - clickTime);
                        LiveXdcsUtil.doXDCS("LiveGiftSend", report);
                    }

                });
    }

    /**
     * 不同业务场景礼物面板对应不同的showType，礼物面板请求礼物列表用
     */
    @ParamsConstantsInLive.ShowType
    public abstract String getShowType();

    public abstract boolean isNeedGift();

    public abstract boolean isNeedPackage();

    public abstract boolean isShowBrocadeBag();

    public boolean isNeedLoadGiftActivityTabInfo() {
        return true;
    }


    public abstract int getGiftCategory();

    public abstract int getPackageCategory();

    public abstract boolean isLiveTypeGift();

    /**
     * 是否支持批量连击礼物，如果不支持，即使配置了连击属性，选择了多个， 也不走连击
     **/
    public boolean supportBatchConsecutiveGift() {
        return false;
    }

    protected String getGiftListUrl() {
        return LiveUrlConstants.getInstance().getLiveGiftListBySendTypeUrl();
    }

    protected String getPackageListUrl() {
        return LiveUrlConstants.getInstance().getPackageInfoUrl();
    }

    protected String getSendGiftUrl(int giftType) {
        switch (giftType) {
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_BOX:
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_LOT:
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_LUCKY_SILK_BAG:
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_DIMENSION_DOOR:
                return LiveUrlConstants.getInstance().getSendBoxGiftUrl();
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_HIDE:
            default:
        }
        return LiveUrlConstants.getInstance().getSendLiveGiftUrl();
    }

    /**
     * 送礼参数（会被签名）
     *
     * @param giftNum        礼物数量
     * @param info           礼物信息
     * @param receiverId     接收者 id
     * @param isLiveGiftType 是否是直播礼物
     * @param repeat         是否连击
     * @param conseUnifiedNo conseUnifiedNo
     * @return params
     */
    protected HashMap<String, String> buildSendCommonGiftParams(int giftNum,
                                                                GiftInfoCombine.GiftInfo info,
                                                                long receiverId,
                                                                boolean isLiveGiftType,
                                                                boolean repeat,
                                                                long conseUnifiedNo) {
        HashMap<String, String> params = new HashMap<>();

        params.put(ParamsConstantsInLive.QUANTITY, giftNum + "");
        params.put(ParamsConstantsInLive.GIFT_ID, info.id + "");
        params.put(ParamsConstantsInLive.RECEIVER_UID, receiverId + "");
        long uid = UserInfoMannage.getUid();
        long time = System.currentTimeMillis();
        params.put(ParamsConstantsInLive.GIFT_TOKEN, uid + String.valueOf(time));
        params.put(ParamsConstantsInLive.ROOM_ID, SendGiftHelper.getInstance().getRoomId() + "");

        return params;
    }

    /**
     * 不需要签名的参数
     *
     * @param params params
     */
    private void buildUnSignatureSendCommonGiftParams(HashMap<String, String> params, GiftInfoCombine.GiftInfo giftInfo, String wordGiftContent) {
        if (!TextUtils.isEmpty(wordGiftContent) && giftInfo.isTxtGift()) {
            params.put("wordGiftContent", wordGiftContent);
        }
    }

    protected String getUsePackageItemUrl() {
        return LiveUrlConstants.getInstance().getUsePackageItemUrl();
    }

    protected Map<String, String> buildUsePackageItemParams(long itemId, long receiverUid, long expireAtTimestamp,
                                                            int num, int mediaType, long referId, boolean repeat, long conseUnifiedNo,
                                                            boolean roomPackage) {
        // 个播
        Map<String, String> params = new HashMap<>();

        params.put("itemId", String.valueOf(itemId));
        params.put("amount", String.valueOf(num));
        params.put("anchorUid", String.valueOf(receiverUid));
        if (expireAtTimestamp > 0) {
            params.put("expireAtTimestamp", String.valueOf(expireAtTimestamp));
        }
        if (mediaType > 0) {
            params.put("mediaType", String.valueOf(mediaType));
        }

        if (repeat) {
            params.put("conseUnifiedNo", String.valueOf(conseUnifiedNo));
        }
        params.put("roomPackage", String.valueOf(roomPackage));

        return params;
    }

    /**
     * 获取默认页(tab)下标，默认情况下以及数值越界时为 0
     *
     * @return 默认页(tab)下标
     */
    public abstract int getDefaultPageIndex();

    private void updateLiveGiftList() {
        if (mIsRequesting) {
            return;
        }

        if (!NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
            setValue(mGiftInfoCombine);
            return;
        }

        Logger.i(TAG, "updateLivePackageList");
        mIsRequesting = true;

        HashMap<String, String> params = buildLoadGiftListParams();

        CommonRequestForCommon.getGiftCombineBySendType(getGiftListUrl(), params, new
                IDataCallBack<GiftInfoCombine>() {
                    @Override
                    public void onSuccess(GiftInfoCombine combine) {
                        mIsRequesting = false;
                        Logger.i(TAG, "preLoad success " + combine);

                        if (combine == null) {
                            String cache = MmkvCommonUtil
                                    .getInstance(BaseApplication.getMyApplicationContext())
                                    .getStringCompat(PreferenceConstantsInLive
                                            .LIVE_KEY_GIFT_LIST_COMBINE_CACHE_FOR_CHAT_ROOM);
                            combine = GiftInfoCombine.parse(cache);
                        }

                        resolveGiftInfo(combine);
                    }

                    @Override
                    public void onError(int code, String message) {
                        mIsRequesting = false;
                        Logger.i(TAG, "preLoad onError " + code + message);
                        String cache = MmkvCommonUtil.getInstance(BaseApplication
                                .getMyApplicationContext()).getStringCompat
                                (PreferenceConstantsInLive
                                        .LIVE_KEY_GIFT_LIST_COMBINE_CACHE_FOR_CHAT_ROOM);
                        if (!TextUtils.isEmpty(cache)) {
                            GiftInfoCombine combine = GiftInfoCombine.parse(cache);

                            resolveGiftInfo(combine);
                        }
                    }
                }

        );
        if (isNeedLoadGiftActivityTabInfo()) {
            loadGiftPanelActivity();
        }
    }

    protected void loadGiftPanelActivity() {
        CommonRequestForCommon.getGiftPanelActivityItemInfo(LiveHelper.buildTimeParams(), new IDataCallBack<GiftPanelActivityTabInfo>() {
            @Override
            public void onSuccess(@Nullable GiftPanelActivityTabInfo data) {
                Logger.i(TAG, "GiftPanelActivityLoader preLoad success " + data);
                notifyGiftPanelActivityDataChange(data);
            }

            @Override
            public void onError(int code, String message) {
                Logger.i(TAG, "GiftPanelActivityLoader preLoad onError " + message);
            }
        });
    }

    /**
     * TODO 参数校验逻辑优化
     */
    protected boolean checkSendParams(GiftInfoCombine.GiftInfo info, int giftNum) {
        String errorMsg = null;
        SendGiftDialog<?> dialog = getDialog();

        if (null == dialog) {
            return true;
        }

        if (dialog.isLiveTypeGift()) {
            if (info == null) {
                errorMsg = "select gift null";
            } else if (info.id <= 0) {
                errorMsg = "select gift id <=0";
            } else if (dialog.getReceiverUid() <= 0) {
                errorMsg = "gift send target uid =0";
            } else if (dialog.getRoomId() <= 0) {
                errorMsg = "gift send target room =0";
            } else if (giftNum <= 0) {
                errorMsg = "selected gift num =0";
            }
        } else {
            errorMsg = "需要重写 checkSendParams";
        }
        if (!TextUtils.isEmpty(errorMsg)) {
            if (ConstantsOpenSdk.isDebug) {
                CustomToast.showFailToast(errorMsg);
                return false;
            }

            // 送礼失败上报
            String report = "SendGift_FailLog | Type: android | ErrorMsg: mSelectedGiftInfo = " +
                    (dialog.mBeanSelected == null ? " = null"
                            : LiveGsonUtils.sGson.toJson(dialog.mBeanSelected)) + "sendType = "
                    + getClass().getSimpleName();
            LiveXdcsUtil.doXDCS("LiveGiftSend", report);

        }
        return true;
    }

    private void resolveGiftInfo(GiftInfoCombine combine) {
        mGiftInfoCombine = combine;
        if (combine != null && combine.categoryGifts != null && !combine.categoryGifts.isEmpty()) {
            for (GiftInfoCombine.CategoryGift categoryGift : combine.categoryGifts) {
                if (categoryGift.tabGifts == null) {
                    continue;
                }

                for (GiftInfoCombine.CategoryGift.TabGift typedGift : categoryGift.tabGifts) {
                    if (typedGift.getGifts() == null) {
                        continue;
                    }

                    for (GiftInfoCombine.GiftInfo giftInfo : typedGift.getGifts()) {
                        mGiftInfoMap.put(giftInfo.id, giftInfo);
                    }
                }
            }

            if (isNeedPackage()) {
                updatePackageInfo(false);
            } else {
                if (isShowBrocadeBag()) {
                    // 这里去请求房间锦囊
                    updateRoomBrocadeBag();
                } else {
                    setValue(combine);
                }
            }
        }
    }

    /**
     * 请求房间锦囊接口
     */
    public void updateRoomBrocadeBag() {
        Map<String, String> params = new HashMap<>();
        if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_LIVE) {
            params.put(
                    ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                    String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_LIVE)
            );
        } else if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_KTV) {
            params.put(
                    ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                    String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_KTV)
            );
        } else if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_HALL) {
            params.put(
                    ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                    String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_HALL)
            );
        } else if (getPackageCategory() == ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_UGC) {
            params.put(
                    ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                    String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_UGC)
            );
        } else {
            params.put(
                    ParamsConstantsInLive.KEY_PACKAGE_CATEGORY,
                    String.valueOf(ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_LIVE)
            );
        }
        params.put("roomId", String.valueOf(getRoomId()));
        Logger.i(TAG, "请求锦囊 roomId " + getRoomId());
        CommonRequestForCommon.getRoomPackageInfo(LiveUrlConstants.getInstance().getRoomPackageInfoUrl(), params, new IDataCallBack<PackageInfo>() {
            @Override
            public void onSuccess(@Nullable PackageInfo object) {
                if (mGiftInfoCombine == null) {
                    mGiftInfoCombine = new GiftInfoCombine();
                }
                mGiftInfoCombine.brocadeBagInfo = object;
                Logger.i(TAG, "锦囊礼物获取成功 roomId " + getRoomId());
                Logger.i(TAG, "锦囊礼物获取成功 " + object);

                setValue(mGiftInfoCombine);
            }

            @Override
            public void onError(int code, String message) {
                // 用缓存数据
                if (null == mGiftInfoCombine.brocadeBagInfo) {
                    mGiftInfoCombine.brocadeBagInfo = new PackageInfo();
                }
                setValue(mGiftInfoCombine);
            }
        });

    }

    /**
     * 活动入口数据请求成功
     * 根据当前礼物和背包数据，确认是否要刷新
     **/
    protected void notifyGiftPanelActivityDataChange(GiftPanelActivityTabInfo info) {
        if (mActivityTabInfo != null && mActivityTabInfo.dataMd5 != null && info != null && info.dataMd5 != null) {
            if (TextUtils.equals(mActivityTabInfo.dataMd5, info.dataMd5)) {
                Logger.d(TAG, "notifyGiftPanelActivityDataChange 礼物数据未变化");
                return;
            }
        }

        mActivityTabInfo = info;
        if (mGiftInfoCombine != null) {
            if (mGiftInfoCombine.categoryGifts == null || mGiftInfoCombine.categoryGifts.isEmpty()) {
                //没有礼物，不刷新
                return;
            }
            //有礼物
            if (isNeedPackage()) {
                if (mGiftInfoCombine.packageInfo != null) {
                    //需要加载背包，并且有背包数据，刷新
                    setValue(mGiftInfoCombine);
                }
            } else {
                //不需要加载背包，刷新
                setValue(mGiftInfoCombine);
            }
        }
    }

    /**
     * 通知礼物刷新时，绑定上面板里边的活动入口数据，
     * 课程直播暂不支持
     **/
    private void updateActivityInfoToGiftCombineBeforeNotify(GiftInfoCombine value) {
        if (isNeedLoadGiftActivityTabInfo() && mActivityTabInfo != null) {
            value.activityTabInfo = mActivityTabInfo;
        } else {
            value.activityTabInfo = null;
        }
    }

    /**
     * 接收端得到的是原始数据的拷贝，保证各端操作不会相互影响
     *
     * @param combine 原始数据
     */
    private void setValue(GiftInfoCombine combine) {
        if (null != combine) {
            MyAsyncTask.execute(() -> {
                internalSetValue(combine);
            });

        } else {
            HandlerManager.postOnUIThread(() -> {
                PackageInfo.RedPoint redPoint = new PackageInfo.RedPoint(false);
                getShowRedPointLiveData().setValue(redPoint);

                PackageInfo.RedPoint silkBagPoint = new PackageInfo.RedPoint(false, true, getRoomId());
                RoomRedPointManager.getInstance().getBrocadeBagRedPointLiveData().setValue(silkBagPoint);
            });

            HandlerManager.postOnUIThread(() -> getGiftInfoCombineLiveData().setValue(null));
        }
    }

    private void internalSetValue(GiftInfoCombine combine) {
        try {
            String json = LiveGsonUtils.sGson.toJson(combine);

            // 将源数据的红点置为 false，这样取缓存数据的时候就不会读到红点了
            if (null != combine.packageInfo) {
                combine.packageInfo.showRedPoint = false;
            }
            if (null != combine.brocadeBagInfo) {
                combine.brocadeBagInfo.showRedPoint = false;
            }

            final GiftInfoCombine value = LiveGsonUtils.sGson.fromJson(json, GiftInfoCombine.class);

            updateActivityInfoToGiftCombineBeforeNotify(value);

            GiftInfoCombine oldValue = getGiftInfoCombineLiveData().getValue();

            //初始化新获得的数据
            List<GiftInfoCombine.CategoryGift> giftList = value.categoryGifts;
            if (!CollectionsUtil.isEmpty(giftList)) {
                for (int i = 0; i < giftList.size(); i++) {
                    GiftInfoCombine.CategoryGift categoryGift = giftList.get(i);
                    List<GiftInfoCombine.CategoryGift.TabGift> tabGifts = categoryGift.tabGifts;
                    for (int j = 0; j < tabGifts.size(); j++) {
                        GiftInfoCombine.CategoryGift.TabGift tabGift = tabGifts.get(j);
                        List<GiftInfoCombine.GiftInfo> gifts = tabGift.getGifts();
                        for (int k = 0; k < gifts.size(); k++) {
                            GiftInfoCombine.GiftInfo gift = gifts.get(k);
                            gift.initRelationGift();
                        }
                    }
                }
            }
            if (oldValue != null) {
                //如果有老数据，就把老数据的选中状态同步过来
                List<GiftInfoCombine.CategoryGift> oldGiftList = oldValue.categoryGifts;
                if (!CollectionsUtil.isEmpty(oldGiftList)) {
                    for (int i = 0; i < oldGiftList.size(); i++) {
                        GiftInfoCombine.CategoryGift categoryGift = oldGiftList.get(i);
                        List<GiftInfoCombine.CategoryGift.TabGift> tabGifts = categoryGift.tabGifts;
                        for (int j = 0; j < tabGifts.size(); j++) {
                            GiftInfoCombine.CategoryGift.TabGift tabGift = tabGifts.get(j);
                            List<GiftInfoCombine.GiftInfo> gifts = tabGift.getGifts();
                            for (int k = 0; k < gifts.size(); k++) {
                                GiftInfoCombine.GiftInfo gift = gifts.get(k);
                                List<GiftInfoCombine.GiftInfo> relatedGifts = gift.getRelatedGifts();
                                if (relatedGifts != null && relatedGifts.size() > 1) {
                                    for (int l = 0; l < relatedGifts.size(); l++) {
                                        GiftInfoCombine.GiftInfo relationGift = relatedGifts.get(l);
                                        if (relationGift.relationSelected) {
                                            value.setRelationSelect(categoryGift.category, relationGift.id);
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
            }

            if (null != value.packageInfo) {
                Logger.i("redPoint", "setValue " + value.packageInfo.showRedPoint);
            }

            HandlerManager.postOnUIThread(() -> {
                Logger.i("loadGift", "redPoint setValue");
                if (getGroupDebugConfig("下一次请求背包有小红点", "gift")) {
                    saveGroupDebugConfig("下一次请求背包有小红点", "gift", false);
                    getShowRedPointLiveData()
                            .setValue(new PackageInfo.RedPoint(true));
                } else {
                    getShowRedPointLiveData()
                            .setValue(new PackageInfo.RedPoint(null != value.packageInfo && value.packageInfo.showRedPoint));
                }
            });
            if (value.brocadeBagInfo != null) {
                Logger.i("redPoint", "请求接口，是否需要展示小红点 " + value.packageInfo.showRedPoint);
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        if (getGroupDebugConfig("下一次请求锦囊有小红点", "gift")) {
                            saveGroupDebugConfig("下一次请求锦囊有小红点", "gift", false);
                            RoomRedPointManager.getInstance().getBrocadeBagRedPointLiveData()
                                    .setValue(new PackageInfo.RedPoint(true, true, getRoomId()));
                        } else {
                            RoomRedPointManager.getInstance().getBrocadeBagRedPointLiveData()
                                    .setValue(new PackageInfo.RedPoint(null != value.brocadeBagInfo && value.brocadeBagInfo.showRedPoint, true, getRoomId()));
                        }
                    }
                });
            }

            HandlerManager.postOnUIThread(() -> {
                Logger.i("loadGift", "giftList setValue");
                getGiftInfoCombineLiveData().setValue(value);
            });
        } catch (Exception e) {
            if (ConstantsOpenSdk.isDebug) {
                CustomToast.showDebugFailToast("GiftInfoCombine parse error on setValue!");
            } else {
                e.printStackTrace();
            }
        }
    }

    /**
     * 背包连击使用
     *
     * @param item          背包物品
     * @param receiverUid   接收者uid
     * @param giftNum       使用数量
     * @param repeatHitHand 连击交互回调
     */
    public void consecutiveUsePackageItem(
            PackageInfo.Item item,
            long receiverUid,
            int giftNum,
            SendGiftDialog.IInteractionFragment repeatHitHand,
            String wordGiftContent
    ) {
        if (item == null) {
            return;
        }
        // 参数列表
        final BaseGiftLoader.GiftHitRecord session = new BaseGiftLoader.GiftHitRecord();
        long consecutiveNo = System.currentTimeMillis();
        GiftInfoCombine.GiftInfo giftInfo = getGift(item.referId);
        session.giftId = item.referId;
        session.giftNum = giftNum;
        session.chatId = SendGiftHelper.getInstance().getChatId();
        session.liveId = getLiveId();
        session.receiverUid = receiverUid;
        session.roomId = getRoomId();
        session.wordGiftContent = wordGiftContent;
        session.conseUnifiedNo = consecutiveNo;
        if (giftInfo != null) {
            session.isConsecutive = giftInfo.isConsecutive;
            session.giftType = giftInfo.giftType;
        }
        if (mBaseDialog != null) {
            session.sendGiftType = mBaseDialog.mSendType;
            session.friendMicUid = mBaseDialog.mFriendsMicUid;
            session.ownerUid = mBaseDialog.getOwnerUid();
        }
        String receiverUidList = getReceiverUidList();
        if (!TextUtils.isEmpty(receiverUidList)) {
            session.receiverUidList = getReceiverUidList();
        }

        if (repeatHitHand != null) {
            repeatHitHand.repeat(session, item);
        } else {
            CustomToast.showDebugFailToast("mRepeatHitHand == null!");
        }
    }

    /**
     * 每次连击送礼记录,此处会保存每次连击送礼的次数，以及网络请求的回调次数，
     * 当
     * 1：回调次数大于点击次数
     * 2：成功次数大于0
     * 3：连击按钮消失
     * 三者同时满足时，进行连击结束的网络请求，可以保证聊天室收到连击总数正确。
     **/
    public static class GiftHitRecord {
        private int successCount;
        private int sendCount = 0;
        private int callBackCount = 0;
        public boolean isRepeatDialogDisMissed = false;
        public long giftId;
        public int orderType;
        public int giftNum;
        public long receiverUid;
        public long chatId;
        public long roomId;
        public long liveId;
        public double price;
        public boolean isConsecutive;
        public long conseUnifiedNo;
        public long friendMicUid;
        public int giftType;
        public int sendGiftType;
        public long ownerUid;
        public String receiverUidList;
        public int reqRetryCount;
        // 文字礼物的文案，如果是文字礼物时，此文案必有
        public String wordGiftContent;

        public boolean isCallBackComplete() {
            return callBackCount >= sendCount && successCount > 0;
        }

        public void addSendCount() {
            sendCount++;
        }

        public int getSendCount() {
            return sendCount;
        }

        public int getGiftNum() {
            return giftNum;
        }

        public void addSuccessCallbackCount() {
            successCount++;
            addCallbackCount();
        }

        public void addCallbackCount() {
            callBackCount++;
        }

        public void tryStopRequest() {
            if (isCallBackComplete() && isRepeatDialogDisMissed) {
                RepeatGiftFragment.makeStopHitRequest(this);
            }
        }

        public GiftHitRecord() {
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            GiftHitRecord that = (GiftHitRecord) o;

            if (giftId != that.giftId) return false;
            return giftNum == that.giftNum;

        }

        @Override
        public int hashCode() {
            int result = Long.hashCode(giftId);
            result = 31 * result + giftNum;
            return result;
        }
    }

    public interface GiftSendSessionCallback {
        void onSendSuccess(int currentRank, double contribution);

        void onSendFail(int code, String message);
    }

    public interface PackageSendSessionCallback {
        void onSuccess();

        void onFail(int code, String message);
    }

    public interface OnGetGiftCallback {
        void onSuccess(GiftInfoCombine.GiftInfo info);

        void onError(long giftId);
    }
}
