package com.ximalaya.ting.android.live.common.view.dialog;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.dialog.base.LiveXmBaseDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * 带有菜单的底部弹框
 * <p>
 * 可以配置 标题，底部取消，和字体颜色，按钮配套图片等
 *
 * <AUTHOR>
 */
public class BottomMenuClickDialog extends LiveXmBaseDialog {

    /**
     * 外部传输的设置项目
     */
    private final boolean isNeedTitle;
    private final CharSequence mTitleContent;
    private final boolean isNeedCloseBtn;

    private List<MenuItemModel> mMenuListData;

    private final ItemViewClickListener mItemListener;


    private TextView mTvTitle;
    private TextView mTvCancel;
    private View mLineFirst;
    private View mLineLast;

    private ListView mListView;

    private MenuAdapter mAdapter;


    public BottomMenuClickDialog(@NonNull Activity act,
                                 boolean isNeedTitle, CharSequence title,
                                 boolean isNeedCloseBtn,
                                 List<MenuItemModel> data,
                                 ItemViewClickListener listener) {
        super(act, com.ximalaya.ting.android.host.R.style.host_bottom_action_dialog);
        this.isNeedTitle = isNeedTitle;
        this.mTitleContent = title;
        this.isNeedCloseBtn = isNeedCloseBtn;
        this.mMenuListData = data;
        this.mItemListener = listener;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.livecomm_dialog_bottom_menu_click);
        Window dialogWindow = getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            dialogWindow.setGravity(Gravity.BOTTOM);
            dialogWindow.setAttributes(lp);
            dialogWindow.setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_bottom_slide_and_fade_animation);
        }

        initView();
    }

    private void initView() {
        mListView = findViewById(R.id.live_menu_list);
        mLineFirst = findViewById(R.id.live_divide_first);
        mLineLast = findViewById(R.id.live_divide_last);

        mTvTitle = findViewById(R.id.live_tv_title_content);
        mTvCancel = findViewById(R.id.live_close_btn);

        //显示标题
        if (isNeedTitle && !TextUtils.isEmpty(mTitleContent)) {
            mTvTitle.setVisibility(View.VISIBLE);
            mLineFirst.setVisibility(View.VISIBLE);
            mTvTitle.setText(mTitleContent);
        } else {
            mTvTitle.setVisibility(View.GONE);
            mLineFirst.setVisibility(View.GONE);
        }

        //关闭按钮显示
        if (isNeedCloseBtn) {
            mLineLast.setVisibility(View.VISIBLE);
            mTvCancel.setVisibility(View.VISIBLE);
        } else {
            mLineLast.setVisibility(View.GONE);
            mTvCancel.setVisibility(View.GONE);
        }

        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mItemListener instanceof ICancelClickListener) {
                    ((ICancelClickListener) mItemListener).onCancel();
                }
                dismiss();
            }
        });

        //显示菜单list
        if (mMenuListData == null) {
            mMenuListData = new ArrayList<>();
        }
        mAdapter = new MenuAdapter(getContext(), mMenuListData);

        mListView.setAdapter(mAdapter);

        if (mItemListener != null) {
            mAdapter.setItemClickListener(mItemListener);
        }
    }


    static class MenuAdapter extends HolderAdapter<MenuItemModel> {
        private ItemViewClickListener mItemClickListener;

        public void setItemClickListener(ItemViewClickListener mItemClickListener) {
            this.mItemClickListener = mItemClickListener;
        }

        @Override
        public void setListData(List<MenuItemModel> data) {
            super.setListData(data);
        }

        public MenuAdapter(Context context, List<MenuItemModel> listData) {
            super(context, listData);
        }

        @Override
        public void onClick(View view, MenuItemModel itemHolder, int position, BaseViewHolder holder) {
            if (mItemClickListener != null) {
                mItemClickListener.OnItemViewClick(view, position);
            }
        }

        @Override
        public int getConvertViewId() {
            return R.layout.livecomm_item_dialog_bottom_menu_click;
        }

        @Override
        public BaseViewHolder buildHolder(View convertView) {
            ViewHolder holder = new ViewHolder();
            holder.container = convertView.findViewById(R.id.live_ll_content);
            holder.content = convertView.findViewById(R.id.live_tv_item_content);
            holder.icon = convertView.findViewById(R.id.live_iv_item_icon);
            holder.border = convertView.findViewById(R.id.live_divide_item);
            return holder;
        }

        @Override
        public void bindViewDatas(BaseViewHolder holder, MenuItemModel itemHolder, int position) {
            ViewHolder holder1 = (ViewHolder) holder;

            //设置按钮文字
            holder1.content.setTextColor(itemHolder.itemTextColor);
            holder1.content.setTextSize(TypedValue.COMPLEX_UNIT_SP, (itemHolder.itemTextSize > 0 ? itemHolder.itemTextSize : 16));
            holder1.content.setText(itemHolder.itemTxt);


            //设置按钮图片
            if (itemHolder.itemPicResId > 0) {
                holder1.icon.setVisibility(View.VISIBLE);
                ImageManager.from(context).displayImage(holder1.icon, null, itemHolder.itemPicResId,
                        BaseUtil.dp2px(context, itemHolder.itemPicSize), BaseUtil.dp2px(context, itemHolder.itemPicSize));
                holder1.container.setGravity(Gravity.START);
            } else {
                holder1.icon.setVisibility(View.GONE);
                holder1.container.setGravity(Gravity.CENTER);
            }

            //item边界线
            if (position == listData.size() - 1) {
                holder1.border.setVisibility(View.GONE);
            } else {
                holder1.border.setVisibility(View.VISIBLE);
            }

            setClickListener(holder1.container, itemHolder, position, holder1);
        }
    }

    public static class ViewHolder extends HolderAdapter.BaseViewHolder {
        private LinearLayout container;
        public ImageView icon;
        public TextView content;
        public View border;
    }

    public static class MenuItemModel {

        public String itemTxt;

        public int itemTextColor;

        public int itemTextSize;

        public int itemPicResId;

        public int itemPicSize;


        public MenuItemModel(String itemTxt, int itemTextColor, int itemTextSize, int itemPicResId, int itemPicSize) {

            this.itemTxt = itemTxt;
            this.itemTextColor = itemTextColor;
            this.itemTextSize = itemTextSize;
            this.itemPicResId = itemPicResId;
            this.itemPicSize = itemPicSize;

        }

    }

    public interface ItemViewClickListener {
        void OnItemViewClick(View view, int position);
    }

    public interface ICancelClickListener extends ItemViewClickListener {
        void onCancel();
    }


    public static class MenuDialogBuilder {
        /**
         * 外部传输的设置项目
         */
        private final Activity act;

        private boolean isNeedTitle;
        private CharSequence mTitleContent;
        private boolean isNeedCloseBtn;

        private List<MenuItemModel> mMenuListData;

        private ItemViewClickListener mItemListener;


        public MenuDialogBuilder(@NonNull Activity activity) {
            act = activity;
        }

        public MenuDialogBuilder setTitle(CharSequence title) {
            if (!TextUtils.isEmpty(title)) {
                isNeedTitle = true;
                mTitleContent = title;
            } else {
                isNeedTitle = false;
            }
            return this;
        }

        public MenuDialogBuilder setIsNeedCloseBtn(boolean isNeedCloseBtn) {
            this.isNeedCloseBtn = isNeedCloseBtn;
            return this;
        }

        public MenuDialogBuilder setMenuListData(List<MenuItemModel> data) {
            this.mMenuListData = data;
            return this;
        }

        public MenuDialogBuilder setItemClickListener(ItemViewClickListener listener) {
            this.mItemListener = listener;
            return this;
        }


        public BottomMenuClickDialog createDialog() {
            if (act == null || mMenuListData == null) {
                return null;
            }

            BottomMenuClickDialog menuClickDialog = new BottomMenuClickDialog(act, isNeedTitle, mTitleContent,
                    isNeedCloseBtn, mMenuListData, mItemListener);


            return menuClickDialog;


        }


    }

}
