package com.ximalaya.ting.android.live.common.lib.base.constants;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 直播流媒体类型
 *
 * <AUTHOR>
 * @since 2021-04-15 16:23
 */
@IntDef
@Retention(RetentionPolicy.SOURCE)
public @interface LiveMediaType {
    /**
     * 音频直播
     */
    int TYPE_AUDIO = 1;
    /**
     * 视频直播
     */
    int TYPE_VIDEO = 2;

    /**
     * 娱乐厅
     */
    int TYPE_PGC = 3;
}
