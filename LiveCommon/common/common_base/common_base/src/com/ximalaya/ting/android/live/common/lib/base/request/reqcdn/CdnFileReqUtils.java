package com.ximalaya.ting.android.live.common.lib.base.request.reqcdn;

import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.record.DownloadManager;
import com.ximalaya.ting.android.live.common.lib.base.request.RetryDownloadTask;
import com.ximalaya.ting.android.live.common.lib.base.request.SimpleDownloadTask;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * cdn文件请求模式设置的常量
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2022/12/1 10:40
 */
public class CdnFileReqUtils {

    public static final String KEY_LIVE_ALL_TEMPS = "live_all_temps";


    public static String getExternCdnFileDownloadDir() {
        String cachePath;
        Context context = MainApplication.getMyApplicationContext();
        try {
            cachePath = context.getExternalFilesDir(null).getAbsolutePath() + "/cdn_req";
        } catch (Exception e) {
            e.printStackTrace();
            cachePath = context.getFilesDir().getPath() + "/cdn_req";
        }
        File file = new File(cachePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        return cachePath;
    }

    /**
     * 获取cdn下载的直播全部礼物资源信息的存储路径
     */
    public static String getLiveAllTemplatesStoreDir() {
        return getExternCdnFileDownloadDir() + File.separator + KEY_LIVE_ALL_TEMPS;
    }


    public static String getCdnFileStoreDirPath(CdnFileDownloadInfo info) {

        if (info == null || TextUtils.isEmpty(info.unpackName)) {
            return null;
        }

        String dirName = info.unpackName.replace(".", "-");
        String md5Str = TextUtils.isEmpty(info.unpackMd5) ? "0" : info.unpackMd5;
        return getExternCdnFileDownloadDir() + File.separator + dirName + "__" + md5Str;
    }

    public static String getCdnFileStoreFileName(CdnFileDownloadInfo info) {

        if (info == null || TextUtils.isEmpty(info.unpackName)) {
            return null;
        }

        String md5Str = TextUtils.isEmpty(info.unpackMd5) ? "0" : info.unpackMd5;
        String name = info.unpackName.split("\\.")[0];
        return name + "__" + md5Str;
    }

    public static String getCdnFileStorePath(CdnFileDownloadInfo info) {
        if (info == null || TextUtils.isEmpty(info.unpackName)) {
            return null;
        }

        return getCdnFileStoreDirPath(info) + File.separator + getCdnFileStoreFileName(info);
    }


    /**
     * 解析得出文件名中保存的md5
     * @param fileName 文件名
     * @return 保存的md5
     */
    public static String parseMd5FromFileName(String fileName) {
        String[] arry = fileName.split("__");
        if (arry.length < 2) {
            return null;
        } else {
            return arry[1];
        }
    }


    public static void downloadCdnFileProcess(CdnFileDownloadInfo downloadInfo,
                                              RetryDownloadTask.DownloadCallback callback) {

        Context context = BaseApplication.getMyApplicationContext();

        String downloadUrl = downloadInfo.address;
        int i = downloadUrl.lastIndexOf("/");
        String downloadFileName = downloadUrl.substring(i+1);

        boolean isZip = downloadFileName.endsWith(".zip");

        String cdnFileStoreDirPath = getCdnFileStoreDirPath(downloadInfo);
        String tempDownloadPath = cdnFileStoreDirPath + File.separator + "download_temp";

        String targetFilePath = getCdnFileStorePath(downloadInfo);

        SimpleDownloadTask downloadTask = new SimpleDownloadTask(context, downloadUrl,
                tempDownloadPath, downloadFileName,
                new SimpleDownloadTask.DownloadCallback() {
                    @Override
                    public void onSuccess() {

                        boolean isDone = false;

                        try{

                            //下载文件所在
                            File downloadResFile = new File(tempDownloadPath + File.separator + downloadFileName);
                            if (isZip) {
                                String zipFilePath = downloadResFile.getAbsolutePath();
                                unzipFile(zipFilePath, new File(tempDownloadPath));
                                downloadResFile.delete();
                            }

                            File srcFile = isZip ? new File(tempDownloadPath, downloadInfo.unpackName) : downloadResFile;
                            File targetFile = new File(targetFilePath);
                            if (targetFile.exists()) {
                                targetFile.delete();
                            }

                            FileUtil.renameFile(srcFile, targetFile);

                            if (targetFile.exists() && targetFile.length() > 0) {
                                isDone = true;
                            }

                            File tempfile = new File(tempDownloadPath);
                            FileUtil.deleteFileDir(tempfile);

                        } catch (Exception e) {
                            e.printStackTrace();
                            isDone = false;
                        }

                        if (isDone && callback != null) {



                            callback.onSuccess();
                        }

                    }

                    @Override
                    public void onFailed() {
                        if (callback != null) {
                            callback.onFailed();
                        }
                    }

                    @Override
                    public void onProgress(int progress) {
                        if (callback != null) {
                            callback.onProgress(progress);
                        }
                    }
                });

        DownloadManager.getInstance().download(downloadTask, true);

    }


    public static boolean unzipFile(String filePath, File dstDir) {
        try {
            ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(new File(filePath)));
            boolean ret = unzipFile(zipInputStream, dstDir);
            Logger.d("FileUtil", "unzipFile ret =" + ret);
            return ret;

        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.d("FileUtil", "unzipFile ret =" + false);

        return false;
    }

    public static boolean unzipFile(ZipInputStream zipInputStream, File dstDir) {

        try {
            if (dstDir.exists()) {
                dstDir.delete();
            }
            dstDir.mkdirs();
            if (null == zipInputStream) {
                return false;
            }
            ZipEntry entry;
            String name;
            do {
                entry = zipInputStream.getNextEntry();
                if (null != entry) {
                    name = entry.getName();
                    if (entry.isDirectory()) {
                        name = name.substring(0, name.length() - 1);
                        File folder = new File(dstDir, name);
                        folder.mkdirs();

                    } else {
                        //   {zh} 否则创建文件,并输出文件的内容     {en} Otherwise create the file and output the contents of the file
                        File file = new File(dstDir, name);
                        file.getParentFile().mkdirs();
                        file.createNewFile();
                        FileOutputStream out = new FileOutputStream(file);
                        int len;
                        byte[] buffer = new byte[1024];
                        while ((len = zipInputStream.read(buffer)) != -1) {
                            out.write(buffer, 0, len);
                            out.flush();
                        }
                        out.close();

                    }
                }

            } while (null != entry);

        } catch (Exception e) {
            e.printStackTrace();
            return false;

        } finally {
            if (zipInputStream != null) {
                try {
                    zipInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }


            }

        }
        return true;

    }



    @WorkerThread
    public static int changeFileContent(String filePath, @NonNull String curCotent) {

        File srcFile = new File(filePath);
        if (srcFile.exists() && srcFile.length() > 0) {
            try {

                FileWriter fileWriter = new FileWriter(srcFile);
                fileWriter.write(curCotent);

                fileWriter.flush();
                fileWriter.close();

                return 1;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return -1;

    }

    @WorkerThread
    public static int changeFileContent(String filePath, @NonNull byte[] inputByteData) {

        File srcFile = new File(filePath);
        if (srcFile.exists() && srcFile.length() > 0) {
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(srcFile);
                fos.write(inputByteData);
                fos.flush();
                return 1;
            } catch (FileNotFoundException e) {
                return -2;
            } catch (IOException e) {
                return -3;
            }
            finally{
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        return -1;

    }









    @WorkerThread
    public static String md5(File file) {
        try {
            MessageDigest m = MessageDigest.getInstance("MD5");
            m.reset();
            FileInputStream fis = new FileInputStream(file);
            byte[] buf = new byte[1024 * 4]; // 4k buffer
            int l;
            while ((l = fis.read(buf, 0, buf.length)) != -1) {
                m.update(buf, 0, l);
            }
            fis.close();
            String md5 = byteArrayToHexString(m.digest());
            return md5;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder resultSb = new StringBuilder();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        String[] hexDigits = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};
        int n = b;
        if (n < 0) {
            n = 0x100 + n;
        }
        int d1 = n >> 4;
        int d2 = n & 0xF;
        return hexDigits[d1] + hexDigits[d2];
    }


    public static byte[] encryptXOR(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        int len = bytes.length;
        int key = 0x12;
        for (int i = 0; i < len; i++) {
            bytes[i] = (byte) (bytes[i] ^ key);
            key = bytes[i];
        }
        return bytes;
    }

    public static byte[] decryptXOR(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        int len = bytes.length;
        int key = 0x12;
        for (int i = len - 1; i > 0; i--) {
            bytes[i] = (byte) (bytes[i] ^ bytes[i - 1]);
        }
        bytes[0] = (byte) (bytes[0] ^ key);
        return bytes;
    }


}
