package com.ximalaya.ting.android.live.common.lib.base.request.reqcdn;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * cdn文件下载信息
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2022/11/30 15:11
 */

@Keep
public class CdnFileDownloadInfo {

    /**
     * "address":"",//地址
     * "fileMd5":"",//文件MD5
     * "fileName":"",//文件名
     * "unpackName":"",//解压之后文件名
     */

    public String address;
    public String fileMd5;
    public String unpackMd5;
    public String unpackName;

    public String fileName;

    public String fileSize;

    @Override
    public String toString() {
        return "CdnFileDownloadInfo{" +
                "address='" + address + '\'' +
                ", fileMd5='" + fileMd5 + '\'' +
                ", unpackMd5='" + unpackMd5 + '\'' +
                ", unpackName='" + unpackName + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize='" + fileSize + '\'' +
                '}';
    }
}
