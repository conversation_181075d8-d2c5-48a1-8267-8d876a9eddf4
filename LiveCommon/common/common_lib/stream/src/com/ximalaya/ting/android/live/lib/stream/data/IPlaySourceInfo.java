package com.ximalaya.ting.android.live.lib.stream.data;

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.live.common.lib.base.constants.RoomModeType;

/**
 * 拉流需要的信息
 *
 * <AUTHOR>
 */
public interface IPlaySourceInfo {

    /**
     * 直播是主播uid，娱乐厅里是房主uid
     *
     * @return 直播是主播uid，娱乐厅里是房主uid
     */
    long getStreamUid();

    /**
     * 直播是主播昵称，娱乐厅里是房主昵称
     *
     * @return 直播是主播昵称，娱乐厅里是房主昵称
     */
    String getHostNickname();

    /**
     * 直播是主播昵称，娱乐厅里是房主昵称
     *
     * @return 直播是主播昵称，娱乐厅里是房主昵称
     */
    String getHostAvatar();

    /**
     * 直播封面大图
     *
     * @return 直播封面大图
     */
    String largeCoverUrl();

    /**
     * 直播封面中图
     *
     * @return 直播封面中图
     */
    String middleCoverUrl();

    /**
     * 直播封面小图
     *
     * @return 直播封面小图
     */
    String smallCoverUrl();

    /**
     * 直播标题
     *
     * @return 直播标题
     */
    String title();

    /**
     * 娱乐厅规则信息
     *
     * @return 娱乐厅规则信息
     */
    String trackInfo();

    /**
     * 直播间id
     *
     * @return 直播间id
     */
    long getRoomId();

    /**
     * 直播类型
     *
     * @return 直播类型
     */
    @BaseScrollConstant.LiveRoomBizType
    int getLiveType();

    /**
     * 直播状态
     *
     * @return 直播状态
     */
    @PersonLiveBase.LiveStatus
    int getStatus();

    default boolean isFromNewHomePage() {
        return false;
    }

    default boolean needInterceptNetChangeAutoPlay(){
        return false;
    }

    @RoomModeType
    default int getRoomModelType(){
        return RoomModeType.ROOM_MODEL_DEFAULT;
    }
}
