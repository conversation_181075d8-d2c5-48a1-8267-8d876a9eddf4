package com.ximalaya.ting.android.live.lib.stream.medainfo.impl;

import android.text.TextUtils;

import com.ximalaya.ting.android.common.lib.logger.ILibLogger;
import com.ximalaya.ting.android.host.live.asr.LiveMediaAsrInfo;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.live.common.lib.utils.eventbus.LiveEventCenter;
import com.ximalaya.ting.android.live.common.lib.utils.thread.LiveWorkerThreadUtil;
import com.ximalaya.ting.android.live.common.sound.effect.adapter.SoundEffectAdapter;
import com.ximalaya.ting.android.live.lib.stream.medainfo.IMediaSideInfoManager;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.LiveAreaEffectInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.PkAndMicInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.PremiereSeiInfo;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 媒体次要信息管理器，管理媒体次要信息接收和发送
 *
 * <AUTHOR>
 */
public abstract class MediaSideInfoManager<T> implements IMediaSideInfoManager<T> {

    /**
     * sei 接收者
     */
    protected CopyOnWriteArraySet<IMediaSideInfoReceiver<T>> mMediaSideInfoReceivers;

    /**
     * 日志
     */
    private ILibLogger libLogger;

    public void setLibLogger(ILibLogger libLogger) {
        this.libLogger = libLogger;
    }

    public ILibLogger getLibLogger() {
        if (libLogger == null) {
            return ILibLogger.DefaultLogger.INSTANCE;
        }
        return libLogger;
    }

    @Override
    public void addMediaSideInfoReceiver(IMediaSideInfoReceiver<T> receiver) {
        if (mMediaSideInfoReceivers == null) {
            mMediaSideInfoReceivers = new CopyOnWriteArraySet<>();
        }

        mMediaSideInfoReceivers.add(receiver);
    }

    @Override
    public void removeMediaSideInfoReceiver(IMediaSideInfoReceiver<T> receiver) {
        if (mMediaSideInfoReceivers == null) {
            mMediaSideInfoReceivers = new CopyOnWriteArraySet<>();
        }

        mMediaSideInfoReceivers.remove(receiver);
    }

    @Override
    public void receiveMediaSideInfoJson(String str) {
        LiveWorkerThreadUtil.executeOnSingleThreadExecutor(new Runnable() {
            @Override
            public void run() {
                try {
                    // 主播拿到观众的sei
                    getLibLogger().log(getClass().getSimpleName() + " receiveMediaSideInfoJson:" + str);
                    Logger.i("ymc_SEI", "receiveMediaSideInfoJson, newSei = " + str + ", threadName = " + Thread.currentThread());

                    JSONObject jsonObject = new JSONObject(str);
                    if (jsonObject.optInt("type") == 4) {
                        receiveMediaSideInfoMic(str);
                    } else if (jsonObject.optInt("type") == 5) {
                        receiveMediaSideInfoPremiere(jsonObject.optString("content"));
                    } else if (jsonObject.optInt("type") == 6) {
                        receiveMediaSideAsrInfo(jsonObject.optString("content"));
                    } else if (jsonObject.optInt("type") == 7) {
                        receiveMediaEffectInfo(str);
                    } else {
                        receiveMediaSideInfo(fromJson(str));
                    }
                } catch (Exception e) {
                    Logger.i("SEI", "receiveMediaSideInfoJson, newSei = " + str);
                }
            }
        });
    }

    /**
     * 处理 音效
     */
    private void receiveMediaEffectInfo(String str) {
        LiveAreaEffectInfo effectInfo = GsonUtils.parseJson(str, LiveAreaEffectInfo.class);
        if (effectInfo == null || effectInfo.getContent() == null) {
            return;
        }
        LiveEventCenter.postSoundEffectAnimEvent(SoundEffectAdapter.SOUND_EFFECTS[effectInfo.getContent().getId()]);
    }

    private void receiveMediaSideInfoMic(String str) {
        if (ToolUtil.isEmptyCollects(mMediaSideInfoReceivers)) {
            return;
        }

        for (IMediaSideInfoReceiver<T> receiver : mMediaSideInfoReceivers) {
            HandlerManager.postOnMainAuto(() -> receiver.onMediaSideInfo(GsonUtils.parseJson(str, PkAndMicInfo.class)));
        }
    }

    private void receiveMediaSideInfoPremiere(String str) {
        if (ToolUtil.isEmptyCollects(mMediaSideInfoReceivers)) {
            return;
        }

        if (TextUtils.isEmpty(str)) {
            return;
        }

        for (IMediaSideInfoReceiver<T> receiver : mMediaSideInfoReceivers) {
            HandlerManager.postOnMainAuto(() -> receiver.onMediaSideInfoPremiere(GsonUtils.parseJson(str, PremiereSeiInfo.class)));
        }
    }


    private void receiveMediaSideAsrInfo(String str) {
        if (ToolUtil.isEmptyCollects(mMediaSideInfoReceivers)) {
            return;
        }

        if (TextUtils.isEmpty(str)) {
            return;
        }

        for (IMediaSideInfoReceiver<T> receiver : mMediaSideInfoReceivers) {
            HandlerManager.postOnMainAuto(() -> receiver.onMediaSideAsrInfo(GsonUtils.parseJson(str, LiveMediaAsrInfo.MediaAsrInfoContent.class)));
        }
    }

    @Override
    public void receiveMediaSideInfoJson(String str, int timestamp) {
        receiveMediaSideInfoJson(str);
    }


    @Override
    public void receiveMediaSideInfo(final T data) {
        if (ToolUtil.isEmptyCollects(mMediaSideInfoReceivers)) {
            return;
        }

        for (IMediaSideInfoReceiver<T> receiver : mMediaSideInfoReceivers) {
            HandlerManager.postOnMainAuto(() -> receiver.onRecMediaSideInfo(data));
        }
    }

    @Override
    public void onStart() {

    }

    @Override
    public void onStop() {
        if (mMediaSideInfoReceivers != null) {
            mMediaSideInfoReceivers.clear();
            mMediaSideInfoReceivers = null;
        }
    }
}
