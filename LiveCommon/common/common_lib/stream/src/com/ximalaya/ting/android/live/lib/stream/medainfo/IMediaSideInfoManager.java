package com.ximalaya.ting.android.live.lib.stream.medainfo;

import com.ximalaya.ting.android.host.live.asr.LiveMediaAsrInfo;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IManager;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.PkAndMicInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.PremiereSeiInfo;

/**
 * 媒体次要信息管理器，管理媒体次要信息接收和发送
 *
 * <AUTHOR>
 */
public interface IMediaSideInfoManager<T> extends IManager {

    T fromJson(String json);

    String toJson(T mediaSideInfo);

    void addMediaSideInfoReceiver(IMediaSideInfoReceiver<T> receiver);

    void removeMediaSideInfoReceiver(IMediaSideInfoReceiver<T> receiver);

    void receiveMediaSideInfoJson(String str);

    /**
     * 为歌词同步而外加的方法
     *
     * @param str       媒体次要信息
     * @param timestamp 用于标识当前 str 数据 显示的时间戳
     */
    void receiveMediaSideInfoJson(String str, int timestamp);

    void receiveMediaSideInfo(T str);

    /**
     * 媒体次要信息接收者
     */
    interface IMediaSideInfoReceiver<T> {
        default void onRecMediaSideInfo(T mediaSideInfo) {

        }

        default void onMediaSideInfo(PkAndMicInfo mediaSideInfo) {

        }

        default void onMediaSideInfoPremiere(PremiereSeiInfo mediaSideInfo) {

        }

        default void onMediaSideAsrInfo(LiveMediaAsrInfo.MediaAsrInfoContent mediaSideInfo) {

        }
    }

}
