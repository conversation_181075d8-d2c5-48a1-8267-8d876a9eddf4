package com.ximalaya.ting.android.live.lib.stream;

import com.ximalaya.ting.android.common.lib.logger.ILibLogger;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.common.lib.base.listener.IStateListener;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonStreamSdkInfo;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IManager;
import com.ximalaya.ting.android.live.lib.stream.data.IPlaySourceInfo;
import com.ximalaya.ting.android.live.lib.stream.data.IPublishUserInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.IMediaSideInfoManager;
import com.ximalaya.ting.android.live.lib.stream.mediaplayer.manager.ILiveMediaPlayerManager;
import com.ximalaya.ting.android.player.XMediaplayerImpl;

/**
 * 推拉流管理器
 * 没有上麦时，拉流；
 * 上麦后，推流，还得区分是主持人还是其他麦上人
 *
 * <AUTHOR>
 */
public interface IStreamManager extends IManager {

    String NAME = "IStreamManager";

    /**
     * 视为正在说话的最小音量
     */
    int MIN_SPEAKING_VOLUME_LEVEL = 5;

    /**
     * 设置房间及流地址相关信息
     *
     * @param playSourceInfo 直播间相关信息
     * @param businessId     参见IBusinessIdConstants
     * @param roomPlayType   如：PlayableModel.KIND_ENT_FLY，PlayableModel.KIND_KTV_FLY
     * @param playUrl        拉流地址
     * @param mediaType      参见 XMediaplayerImpl.FlvType
     */
    void setMediaSourceAndRoomDetail(
            IPlaySourceInfo playSourceInfo,
            @IBusinessIdConstants int businessId,
            String roomPlayType,
            String playUrl,
            @XMediaplayerImpl.FlvType int mediaType
    );

    /**
     * 设置直播间相关信息
     *
     * @param playSourceInfo 直播间相关信息
     */
    void setRoomDetail(IPlaySourceInfo playSourceInfo);

    /**
     * 开始拉流播放，不论当前是否正在播放
     */
    void startPlayStream();


    /**
     * 停止拉流
     */
    void stopPlayStream();

    /**
     * 开始推流
     *
     * @param sdkInfo         推流相关信息
     * @param publishCallback 推流回调
     */
    void publishStream(CommonStreamSdkInfo sdkInfo, IStreamManager.IPublishCallback publishCallback);

    /**
     * 注册推流回调
     *
     * @param publishCallback 推流回调
     */
    void addPublishCallback(IPublishCallback publishCallback);

    /**
     * 注销推流回调
     *
     * @param publishCallback 推流回调
     */
    void removePublishCallback(IPublishCallback publishCallback);

    /**
     * 异常情况下，重试推流
     */
    void retryPublishStream();

    /**
     * 停止推流
     *
     * @param stopMix true 停止推流，停止混流 false 停止推流，不停止混流
     */
    void stopPublishStream(boolean stopMix);

    /**
     * 注册拉流状态监听器
     *
     * @param stateListener 拉流状态监听器
     */
    void addStreamPlayStateListener(IStateListener<Integer> stateListener);

    /**
     * 注销拉流状态监听器
     *
     * @param stateListener 注销状态监听器
     */
    void removeStreamPlayStateListener(IStateListener<Integer> stateListener);

    /**
     * 获取直播拉流播放器
     *
     * @return 直播拉流播放器
     */
    ILiveMediaPlayerManager getPlayManager();

    /**
     * 获取 SEI 管理器
     *
     * @return SEI 管理器
     */
    IMediaSideInfoManager getMediaSideInfoManager();

    /**
     * 设置当前登录用户信息
     *
     * @param myUserInfo 当前登录用户信息
     */
    void setCurrentLoginUserInfo(IPublishUserInfo myUserInfo);

    /**
     * 是否推流中
     *
     * @return true 推流中 false 未推流
     */
    boolean isPublishStarted();

    /**
     * 停止推流，然后开始拉流
     */
    void stopPublishAndPlay();

    /**
     * 麦克风开关
     *
     * @param enableMic true:开启, false:关闭
     */
    void enableMic(boolean enableMic);

    /**
     * 混音开关
     *
     * @param enableAux 开启/关闭混音开关，true 表示开启混音，false 表示关闭混音；默认为 false（关闭混音）
     */
    void enableAux(boolean enableAux);

    void destroy(boolean stopPullStream);

    boolean isHost();

    boolean isPlayThisRoomStream(long roomId);

    boolean isPlaying();

    void updateCdnStatus(int cdnStatus);

    void setLibLogger(ILibLogger mLibLogger);

    /**
     * 推流回调
     */
    interface IPublishCallback {

        void onStartResult(boolean success, int stateCode);

        void onMixStreamResult(boolean success, int stateCode);

        void onCaptureSoundLevel(int level);

        void onKickOut();

        void onDisconnect();

        void onReconnect();

        void onAfterInitSdk();

    }
}
