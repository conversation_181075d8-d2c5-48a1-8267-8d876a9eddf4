package com.ximalaya.ting.android.live.lib.stream;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.common.lib.logger.ILibLogger;
import com.ximalaya.ting.android.common.lib.logger.PgcBizLog;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.common.lib.base.listener.IStateListener;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonStreamSdkInfo;
import com.ximalaya.ting.android.live.lib.stream.data.IPlaySourceInfo;
import com.ximalaya.ting.android.live.lib.stream.data.IPublishUserInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.IMediaSideInfoManager;
import com.ximalaya.ting.android.live.lib.stream.medainfo.impl.MediaSideInfoManager;
import com.ximalaya.ting.android.live.lib.stream.mediaplayer.manager.ILiveMediaPlayerManager;
import com.ximalaya.ting.android.live.lib.stream.mediaplayer.manager.LiveMediaPlayerManager;
import com.ximalaya.ting.android.live.lib.stream.mediaplayer.player.ILivePlayerStatusListener;
import com.ximalaya.ting.android.live.lib.stream.preloadplayer.PreloadPlayerManager;
import com.ximalaya.ting.android.live.lib.stream.publish.XmLiveRoom;
import com.ximalaya.ting.android.live.lib.stream.util.XmAvKeyUtil;
import com.ximalaya.ting.android.liveav.lib.constant.Role;
import com.ximalaya.ting.android.liveav.lib.data.MixStreamConfig;
import com.ximalaya.ting.android.liveav.lib.impl.zego.data.ZegoJoinRoomConfig;
import com.ximalaya.ting.android.liveav.lib.util.log.LiveLogUtil;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.player.XMediaplayerImpl;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.SoftReference;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 推拉流管理器
 *
 * <AUTHOR>
 */
public class StreamManager implements IStreamManager, ILivePlayerStatusListener {
    /**
     * 标签
     */
    public static final String TAG = "StreamManager";

    ILibLogger mLibLogger = ILibLogger.DefaultLogger.INSTANCE;

    private final ILiveMediaPlayerManager mLiveMediaPlayerManager;

    private final IMediaSideInfoManager<?> mMediaSideInfoManager;

    private IPlaySourceInfo mRoomDetail;
    private IPublishUserInfo mUserInfo;
    @IBusinessIdConstants
    private int mBusinessId;
    private String mRoomPlayType;
    private String mPlayUrl;
    @XMediaplayerImpl.FlvType
    private int mMediaType;

    private CommonStreamSdkInfo mStreamSdkInfo;

    private final CopyOnWriteArraySet<SoftReference<IPublishCallback>> publishCallbackRefs = new CopyOnWriteArraySet<>();

    private final List<IStateListener<Integer>> mListeners = new CopyOnWriteArrayList<>();

    public StreamManager(MediaSideInfoManager<?> mediaSideInfoManager) {
        mMediaSideInfoManager = mediaSideInfoManager;
        mLiveMediaPlayerManager = LiveMediaPlayerManager.getInstance();
    }

    public void setLibLogger(ILibLogger libLogger) {
        this.mLibLogger = libLogger;
    }

    public ILibLogger getLibLogger() {
        if (mLibLogger == null) {
            return ILibLogger.DefaultLogger.INSTANCE;
        }
        return mLibLogger;
    }

    @Override
    public void setMediaSourceAndRoomDetail(
            IPlaySourceInfo playSourceInfo,
            @IBusinessIdConstants int businessId,
            String roomPlayType,
            String playUrl,
            @XMediaplayerImpl.FlvType int mediaType
            ) {
        LiveLogUtil.log("hzx-playstream", "StreamManager setMediaSourceAndRoomDetail");

        if (TextUtils.isEmpty(playUrl)) {
            LiveLogUtil.log("hzx-playstream", "StreamManager playUrl return");
            return;
        }
        mRoomDetail = playSourceInfo;
        mBusinessId = businessId;
        if (!playUrl.equals(mLiveMediaPlayerManager.getPlayUrl()) && mRoomDetail != null) {
            mLiveMediaPlayerManager.destroy(true, mBusinessId, mRoomDetail.getRoomId());
        }
        mPlayUrl = playUrl;
        mRoomPlayType = roomPlayType;
        mMediaType = mediaType;
        mLiveMediaPlayerManager.registerLivePlayerStatusListener(this);
        //判断预加载
        if (PreloadPlayerManager.getInstance().getCurrentLiveRecord() != null && !TextUtils.isEmpty(PreloadPlayerManager.getInstance().getCurrentLiveRecord().getPlayUrl()) &&
                PreloadPlayerManager.getInstance().getCurrentLiveRecord().getPlayUrl().equals(mPlayUrl)) {
            Logger.i("duruochen--preload", "资源已预加载");
            mPlayUrl = PreloadPlayerManager.getInstance().getCurrentLiveRecord().getPlayUrl();
            getPlayManager().updateNotification(mRoomDetail, mRoomPlayType);
            PreloadPlayerManager.getInstance().clearCurrentLiveRecord();
            LiveLogUtil.log("hzx-playstream", "StreamManager 资源已预加载");
        } else {
            LiveLogUtil.log("hzx-playstream", "StreamManager start");
            getPlayManager().setMediaSourceAndRoomDetail(mRoomDetail, mBusinessId, mRoomPlayType, mPlayUrl, mMediaType, true);
        }
    }

    @Override
    public void setRoomDetail(IPlaySourceInfo playSourceInfo) {
        mRoomDetail = playSourceInfo;
    }

    @Override
    public void addStreamPlayStateListener(IStateListener<Integer> stateListener) {
        if (!mListeners.contains(stateListener)) {
            mListeners.add(stateListener);
        }
    }

    @Override
    public void removeStreamPlayStateListener(IStateListener<Integer> stateListener) {
        mListeners.remove(stateListener);
    }

    @Override
    public void startPlayStream() {
        getLibLogger().log(getClass().getSimpleName() + " startPlayStream", true);
        LiveLogUtil.log("hzx-playstream", "startPlayStream");

        if (TextUtils.isEmpty(mPlayUrl) || !mPlayUrl.equals(getPlayManager().getPlayUrl())) {
            LiveLogUtil.log("hzx-playstream", "已切换到下一个直播间(由于信令时序问题，快速从娱乐厅上下滑切换到其他直播间会打印)");
            return;
        }
        if (getPlayManager().isPlaying()) {
            LiveLogUtil.log("hzx-playstream", "正在播放音频，直接return startPlayStream");
            return;
        }
        LiveLogUtil.log("hzx-playstream", "startPlayStream start");
        getPlayManager().registerLivePlayerStatusListener(this);
        getPlayManager().setMediaSourceAndRoomDetail(mRoomDetail, mBusinessId, mRoomPlayType, mPlayUrl, mMediaType, true);
    }


    @Override
    public void stopPlayStream() {
        getPlayManager().stopPlayLive(mBusinessId, mRoomDetail != null ? mRoomDetail.getRoomId() : 0);
    }

    @Override
    public void publishStream(CommonStreamSdkInfo sdkInfo, IPublishCallback publishCallback) {
        if (mRoomDetail == null || sdkInfo == null) {
            LiveXdcsUtil.doXDCS(TAG, "推流失败，直播间详情数据异常", true);
            CustomToast.showDebugFailToast("推流失败，直播间详情数据异常");
            if (null != publishCallback) {
                publishCallback.onStartResult(false, -1);
            }
            return;
        }
        LiveXdcsUtil.doXDCS(TAG, "开始推流 publishStream ", true);
        getLibLogger().log(getClass().getSimpleName() + " 开始推流 publishStream ", true);
        mStreamSdkInfo = sdkInfo;
        addPublishCallback(publishCallback);

        long uid = mRoomDetail.getStreamUid();
        sdkInfo.mMixId = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(
                sdkInfo.mMixId, "userId=" + uid
        );
        if (!TextUtils.isEmpty(sdkInfo.singleMixId)) {
            sdkInfo.singleMixId = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(
                    sdkInfo.singleMixId, "userId=" + uid
            );
        }

        initXmAvSdk(sdkInfo);
    }

    /**
     * 初始化喜马推流SDK
     */
    private void initXmAvSdk(CommonStreamSdkInfo sdkInfo) {
        // 初始化喜马推流SDK
        String appId = sdkInfo.mSdkAppId;
        String appKey = new String(XmAvKeyUtil.decryptSignKey(sdkInfo.mSdkAppKey), StandardCharsets.ISO_8859_1);
        long userId;
        if (mUserInfo == null) {
            userId = UserInfoMannage.getUid();
        } else {
            userId = mUserInfo.getPublisherUid();
        }
        getLibLogger().log(getClass().getSimpleName() + " initXmAvSdk");
        XmLiveRoom.sharedInstance(MainApplication.mAppInstance).init(appId, appKey, sdkInfo.mBizId, userId, new XmLiveRoom.IInitCallback() {

            @Override
            public void onSuccess() {
                // 初始化成功后通知

                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onAfterInitSdk();
                    }
                }

                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " initXmAvSdk onSuccess", true);
                // 初始化成功后开始推流  PGC、UGC推流
                ZegoJoinRoomConfig joinRoomConfig = new ZegoJoinRoomConfig();
                joinRoomConfig.setRoomId(sdkInfo.mChannelName);
                joinRoomConfig.setStreamId(sdkInfo.mStreamId);
                joinRoomConfig.setMixId(sdkInfo.mMixId);
                joinRoomConfig.setRole(sdkInfo.isPreside ? Role.ANCHOR : Role.AUDIENCE);
                joinRoomConfig.setUserId(String.valueOf(userId));
                joinRoomConfig.setMixStreamConfig(getEntOrUgcMixStreamConfig());

                XmLiveRoom.sharedInstance(MainApplication.mAppInstance).setBusinessMode(sdkInfo.mBusinessMode);
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " initXmAvSdk onSuccess XmLiveRoom join room ", true);
                XmLiveRoom.sharedInstance(MainApplication.mAppInstance).joinRoom(joinRoomConfig, true);
            }

            @Override
            public void onError(int code, String msg) {
                Logger.e(TAG, "XmAVSdk.getInstance().init, code = " + code + ", msg = " + msg);
                CustomToast.showFailToast("推流初始化失败" + code);
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " initXmAvSdk onError " + code + msg, true);
            }
        });
        XmLiveRoom.sharedInstance(MainApplication.mAppInstance).setXmLiveRoomListener(new XmLiveRoom.IXmLiveRoomListener() {

            @Override
            public void onError(int errCode, String errMsg) {
                Logger.e(TAG, "XmLiveRoom，onError, errCode = " + errCode + ", errMsg = " + errMsg);
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onError", true);
                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onStartResult(false, errCode);
                    }
                }
            }

            @Override
            public void onKickOut() {
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onKickOut", true);
                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onKickOut();
                    }
                }
            }

            @Override
            public void onDisconnect() {
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onDisconnect", true);

                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onDisconnect();
                    }
                }
            }

            @Override
            public void onReconnect() {
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onReconnect", true);

                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onReconnect();
                    }
                }
            }

            @Override
            public void onJoinRoom() {
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onJoinRoom", true);

                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onStartResult(true, 0);
                    }
                }

                if (mStreamSdkInfo != null) {
                    getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onJoinRoom addPublishCdnUrl", true);
                    XmLiveRoom.sharedInstance(MainApplication.mAppInstance).addPublishCdnUrl(mStreamSdkInfo.mStreamId, mStreamSdkInfo.singleMixId, code -> {
                        getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onJoinRoom onPublisherUpdateCdnUrlResult code " + code, true);
                        Logger.i(TAG, "addPublishCdnUrl, code = " + code);
                    });
                }
            }

            @Override
            public void onLeaveRoom() {
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onLeaveRoom", true);
                if (mStreamSdkInfo != null) {
                    getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onLeaveRoom removePublishCdnUrl");
                    XmLiveRoom.sharedInstance(MainApplication.mAppInstance).removePublishCdnUrl(mStreamSdkInfo.mStreamId, mStreamSdkInfo.singleMixId, code -> {
                        getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onLeaveRoom onPublisherUpdateCdnUrlResult code " + code);
                        Logger.i(TAG, "removePublishCdnUrl, code = " + code);
                    });
                }
            }

            @Override
            public void onMixStreamConfigUpdate() {
                getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " IXmLiveRoomListener onMixStreamConfigUpdate", true);

                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onMixStreamResult(true, 0);
                    }
                }

            }

            long mLastUpdateTime;

            @Override
            public void onCaptureSoundLevel(int level) {

                for (SoftReference<IPublishCallback> reference : publishCallbackRefs) {
                    if (reference != null && reference.get() != null) {
                        reference.get().onCaptureSoundLevel(level);
                    }
                }
                if (!publishCallbackRefs.isEmpty()) {

                    if (System.currentTimeMillis() - mLastUpdateTime < 2 * 1000) {
                        return;
                    }

                    PgcBizLog.INSTANCE.writeLogFile(TAG, "onCaptureSoundLevel " + level + ", callback release");

                    mLastUpdateTime = System.currentTimeMillis();
                }
            }

            @Override
            public void onReceiveMediaSideInfo(String data) {
                if (mMediaSideInfoManager != null) {
                    mMediaSideInfoManager.receiveMediaSideInfoJson(data);
                }
            }
        });
    }

    @Override
    public void addPublishCallback(IPublishCallback publishCallback) {
        if (publishCallback == null) {
            return;
        }
        for (SoftReference<IPublishCallback> ref : publishCallbackRefs) {
            if (ref != null && ref.get() == publishCallback) {
                return;
            }
        }
        publishCallbackRefs.add(new SoftReference<>(publishCallback));
        getLibLogger().log("addPublishCallback publishCallbackRefs = " + publishCallbackRefs);
    }

    @Override
    public void removePublishCallback(IPublishCallback publishCallback) {
        if (publishCallback == null) {
            return;
        }
        List<SoftReference<IPublishCallback>> removeRef = new ArrayList<>();
        for (SoftReference<IPublishCallback> ref : publishCallbackRefs) {
            if (ref == null || ref.get() == publishCallback) {
                removeRef.add(ref);
            }
        }
        for (SoftReference<IPublishCallback> ref : removeRef) {
            publishCallbackRefs.remove(ref);
        }
        getLibLogger().log("removePublishCallback publishCallbackRefs = " + publishCallbackRefs);
    }

    @Override
    public void retryPublishStream() {
        CustomToast.showDebugFailToast("重新推流");

        publishStream(mStreamSdkInfo, null);
    }


    @Override
    public void stopPublishStream(boolean stopMix) {
        getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " stopPublishStream stopMix " + stopMix);
        XmLiveRoom.sharedInstance(MainApplication.mAppInstance).leaveRoom(stopMix);
    }

    @Override
    public IMediaSideInfoManager<?> getMediaSideInfoManager() {
        return mMediaSideInfoManager;
    }

    @Override
    @NonNull
    public ILiveMediaPlayerManager getPlayManager() {
        return mLiveMediaPlayerManager;
    }

    @Override
    public void setCurrentLoginUserInfo(IPublishUserInfo myUserInfo) {
        mUserInfo = myUserInfo;
    }

    @Override
    public boolean isPublishStarted() {
        return XmLiveRoom.sharedInstance(MainApplication.mAppInstance).isPublish();
    }

    @Override
    public void stopPublishAndPlay() {
        getLibLogger().log(StreamManager.this.getClass().getSimpleName() + " stopPublishAndPlay ");
        boolean playing = getPlayManager().isPlaying();
        Logger.i(TAG, "Stream stopPublishAndPlay playing? " + playing);
        if (isPublishStarted()) {
            stopPublishStream(false);
            startPlayStream();
            return;
        }

        if (!playing) {
            startPlayStream();
        }
    }

    @Override
    public void enableMic(boolean enableMic) {
        XmLiveRoom.sharedInstance(MainApplication.mAppInstance).enableMic(enableMic);
    }

    @Override
    public void enableAux(boolean enableAux) {
        XmLiveRoom.sharedInstance(MainApplication.mAppInstance).enableAux(enableAux);
    }

    @Override
    public void onStart() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void destroy(boolean stopPullStream) {

        mLiveMediaPlayerManager.destroy(stopPullStream, mBusinessId, mRoomDetail != null ? mRoomDetail.getRoomId() : 0);
        mLiveMediaPlayerManager.unregisterLivePlayerStatusListener(this);
        if (mMediaSideInfoManager != null) {
            mMediaSideInfoManager.onStop();
        }
    }

    @Override
    public boolean isHost() {
        return XmLiveRoom.sharedInstance(MainApplication.mAppInstance).isHost();
    }

    @Override
    public boolean isPlayThisRoomStream(long roomId) {
        return getPlayManager().isPlayThisRoomStream(roomId);
    }

    @Override
    public boolean isPlaying() {
        return getPlayManager().isPlaying();
    }


    @Override
    public void updateCdnStatus(int cdnStatus) {
        getPlayManager().updateCdnStatus(cdnStatus);
    }


    @Override
    public void onPlayStart(String playUrl) {
        for (IStateListener<Integer> listener : mListeners) {
            listener.onStateChanged(PlayerConstants.PLAYSTATE_PLAYING);
        }
    }

    @Override
    public void onPlayPause() {
        for (IStateListener<Integer> listener : mListeners) {
            listener.onStateChanged(PlayerConstants.PLAYSTATE_PAUSE);
        }
    }

    @Override
    public void onPlayStop() {
        for (IStateListener<Integer> listener : mListeners) {
            listener.onStateChanged(PlayerConstants.PLAYSTATE_PAUSE);
        }
    }

    @Override
    public void onPlayComplete() {
        for (IStateListener<Integer> listener : mListeners) {
            listener.onStateChanged(PlayerConstants.PLAYSTATE_END);
        }
    }

    @Override
    public void onPrepared() {
    }

    @Override
    public void onMediaSwitch(@Nullable PlayableModel lastModel, @Nullable PlayableModel curModel) {

    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
    }

    @Override
    public boolean onError(@Nullable XmPlayerException exception) {
        for (IStateListener<Integer> listener : mListeners) {
            listener.onStateChanged(PlayerConstants.PLAYSTATE_ERROR);
        }
        return false;
    }

    @Override
    public void onReceiveSeiJson(@Nullable String seiJson, int timestamp, long bufferDuration) {
        if (mMediaSideInfoManager != null) {
            mMediaSideInfoManager.receiveMediaSideInfoJson(seiJson, timestamp);
        }
    }

    @Override
    public void onRotationChanged(int rotationAngle) {
    }

    @Override
    public void onVideoSizeChanged(int width, int height) {

    }

    @Override
    public void onRenderingStart(String playUrl) {
    }

    private MixStreamConfig getEntOrUgcMixStreamConfig() {
        MixStreamConfig config = new MixStreamConfig();
        config.outputFps = 1;
        config.outputWidth = 2;
        config.outputHeight = 2;
        return config;
    }
}
