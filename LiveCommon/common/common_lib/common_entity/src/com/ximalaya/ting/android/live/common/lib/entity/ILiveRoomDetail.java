package com.ximalaya.ting.android.live.common.lib.entity;

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;

/**
 * 直播间详情信息接口。
 *
 * <AUTHOR>
 */
public interface ILiveRoomDetail {
    /**
     * 获取直播间id
     *
     * @return 直播间id
     */
    long getRoomId();

    /**
     * 获取直播间场次 id
     *
     * @return 直播间场次 id
     */
    long getLiveId();

    /**
     * 获取聊天室id，将来会统一使用 roomId，目前用在直播、K歌房和娱乐厅中；视频直播不再使用
     *
     * @return 聊天室id
     */
    long getChatId();

    /**
     * 获取主播id
     *
     * @return 主播id
     */
    long getHostUid();

    /**
     * 获取主播昵称
     *
     * @return 主播昵称
     */
    String getAnchorName();

    /**
     * 获取主播头像
     *
     * @return 主播头像
     */
    String getAnchorAvatar();

    /**
     * 获取主播头像
     *
     * @return 主播头像
     */
    String getLargeAvatar();

    /**
     * 获取直播状态
     *
     * @return 1 直播结束；5 直播预告；9 直播中
     */
    @PersonLiveBase.LiveStatus
    int getStatus();

    /**
     * 设置直播状态
     * 个播 课程直播用 PGC永远直播中
     *
     * @param status 直播状态
     */
    default void setLiveStatus(@PersonLiveBase.LiveStatus int status) {
    }

    /**
     * 获取业务类型
     *
     * @return 业务类型
     */
    @BaseScrollConstant.LiveRoomBizType
    int getRoomBizType();

    /**
     * 获取子业务类型，对于个播是分类id，对于PGC聊天室是玩法mode，对于UGC聊天室是玩法recordMode
     *
     * @return 子业务类型
     */
    int getRoomSubBizType();


    /**
     * 是否匿名直播间
     *
     * @return true 是 false 否
     */
    boolean isAnonymousRoom();

    /**
     * 获取直播媒体类型  1：音频  2：视频
     *
     * @return 1：音频  2：视频
     */
    @LiveMediaType
    int getMediaType();

    /**
     * 获取详情里返回的房间榜单H5地址
     *
     * @return 房间榜单H5地址
     */
    String getRoomGiftRankPopUrl();

    /**
     * 设置房间禁言状态，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @param isForbidden true 房间被禁言 false 房间未被禁言
     */
    default void setRoomForbidden(boolean isForbidden) {

    }

    /**
     * 房间是否已禁言，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return true 房间被禁言 false 房间未被禁言
     */
    default boolean isRoomForbidden() {
        return false;
    }

    /**
     * 房间禁言提示消息，目前个播音频直播、个播视频直播在使用
     *
     * @return 房间禁言提示消息
     */
    default String getRoomForbiddenHint() {
        return "";
    }


    /**
     * 获取直播实际开始时间，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return 直播实际开始时间
     */
    default long getLiveStartAt() {
        return 0;
    }

    /**
     * 获取直播预计开始时间，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return 直播预计开始时间
     */
    default long getLivePlanToStartAt() {
        return 0;
    }

    /**
     * 获取直播实际结束时间，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return 直播实际结束时间
     */
    default long getLiveStopAt() {
        return 0;
    }

    /**
     * 用户是否已预约，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return true 已预约 false 未预约
     */
    default boolean isBooking() {
        return false;
    }

    /**
     * 本场是否预约
     *
     * @return true 已预约 false 未预约
     */
    default boolean isBookingThisRecord() {
        return false;
    }

    /**
     * 更新直播间公告
     *
     * @param desc 直播间公告
     */
    default void updateDescription(String desc) {

    }

    /**
     * 获取直播间公告
     *
     * @return 直播间公告
     */
    default String getDescription() {
        return "";
    }

    /**
     * 获取直播间标题
     *
     * @return 直播间标题
     */
    String getRoomTitle();

    /**
     * 获取直播间当前在线人数
     *
     * @return 直播间当前在线人数
     */
    int getOnlineCount();

    /**
     * 获取直播总参与人数
     *
     * @return 直播总参与人数
     */
    int getParticipateCount();

    /**
     * 个播和课程直播更新关注主播状态
     *
     * @param followed true 已关注主播 false 未关注主播
     */
    default void setFollowed(boolean followed) {

    }

    /**
     * 个播和课程直播用户是否已关注主播
     *
     * @return true 已关注主播 false 未关注主播
     */
    default boolean isFollowed() {
        return false;
    }

    /**
     * 获取直播小封面图
     *
     * @return 直播小封面图
     */
    String getSmallCoverPath();

    /**
     * 获取直播大封面
     *
     * @return 直播大封面
     */
    String getLargeCoverPath();

    /**
     * 是否有回看，目前课程直播在使用
     *
     * @return true 有回看 false 无回看
     */
    default boolean hasPlayBack() {
        return false;
    }

    /**
     * 获取直播回看地址，目前课程直播在使用
     *
     * @return 直播回看地址
     */
    default String getPlayBackPath() {
        return "";
    }

    /**
     * 获取直播间背景图
     *
     * @return 直播间背景图
     */
    String getBgImage();

    /**
     * 是否开启带货，目前课程直播在使用
     *
     * @return true 开启带货 false 未开启带货
     */
    default boolean isOpenGoods() {
        return false;
    }

    /**
     * 获取FM号
     *
     * @return FM号
     */
    long getFMId();

    /**
     * 粉丝团信息
     *
     * @return 粉丝团信息
     */
    default LiveUserInfo.FansClubVoBean getRoomFansClubVo() {
        return null;
    }

    /**
     * 更新粉丝团人数
     *
     * @param count 粉丝团人数
     */
    default void updateFansClubCount(int count) {
    }

    /**
     * 更新粉丝团等级
     *
     * @param grade 粉丝团等级
     */
    default void updateFansClubGrade(int grade) {
    }

    /**
     * 更新粉丝团加入状态
     *
     * @param isJoined true 已加入粉丝团 false 未加入粉丝团
     */
    default void updateFansClubJoinStatus(boolean isJoined) {
    }

    /**
     * 是否有商品，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return true 有商品 false 无商品
     */
    default boolean isHasGoods() {
        return false;
    }

    /**
     * 购物车动效Url，目前个播音频直播、个播视频直播和课程直播在使用
     *
     * @return 购物车动效Url
     */
    default String getCarGifUrl() {
        return "";
    }

    /**
     * 获取直播间话题，目前课程直播在使用
     *
     * @return 直播间话题
     */
    default String getTopic() {
        return "";
    }

    /**
     * 获取直播间话题id，目前课程直播在使用
     *
     * @return 直播间话题id
     */
    default long getTopicId() {
        return 0;
    }

    /**
     * 更新直播间话题id，目前课程直播在使用
     *
     * @param topicId 直播间话题id
     */
    default void updateTopicId(long topicId) {

    }

    /**
     * 更新直播间话题，目前课程直播在使用
     *
     * @param topic 直播间话题
     */
    default void updateTopic(String topic) {

    }

    /**
     * 更新在线贵族人数
     *
     * @param count 在线贵族人数
     */
    default void updateOnlineNobleCount(int count) {

    }

    /**
     * 是否可预约，目前课程直播在使用
     *
     * @return true 可预约 false 不可预约
     */
    default boolean isCanBooking() {
        return false;
    }

    /**
     * 获取拉流地址
     *
     * @return 拉流地址
     */
    default String getPlayUrl() {
        return "";
    }

    /**
     * 获取视频横屏还是竖屏
     *
     * @return true 横屏 false 竖屏
     */
    default boolean isHorizontal() {
        return true;
    }

    /**
     * 是否有观看权限
     *
     * @return true 可以观看 false 不能观看
     */
    default boolean canPlayLive() {
        return true;
    }
}
