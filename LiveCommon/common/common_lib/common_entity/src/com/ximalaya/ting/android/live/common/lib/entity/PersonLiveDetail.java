package com.ximalaya.ting.android.live.common.lib.entity;

import static com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo.FansGroupStatusCode.TYPE_JOINED;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArraySet;

import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.RoomModeType;
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomTopHeadlinesMsg;
import com.ximalaya.ting.android.live.lib.stream.live.ILivePlaySourceInfo;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * 直播间数据信息
 * 1.调用/v13/live/room，查询直播间详情
 * 2.调用/v8/live/userinfo，查询当前登录用户信息
 *
 * <AUTHOR>
 * @wiki 接口文档：
 * 1.<a href="http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/lamia/%E9%9F%B3%E8%A7%86%E9%A2%91%E6%95%B4%E5%90%88/api/%E6%9F%A5%E8%AF%A2%E7%9B%B4%E6%92%AD%E9%97%B4%E5%9F%BA%E7%A1%80%E4%BF%A1%E6%81%AF%E6%8E%A5%E5%8F%A3.md">直播间基础信息接口文档</a>
 * 2.<a href="http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/lamia/%E9%9F%B3%E8%A7%86%E9%A2%91%E6%95%B4%E5%90%88/api/%E6%9F%A5%E8%AF%A2%E4%B8%BB%E6%92%AD%E6%88%96%E8%80%85%E7%94%A8%E6%88%B7%E4%BF%A1%E6%81%AF%E6%8E%A5%E5%8F%A3.md">查询用户信息接口文档</a>
 */
public class PersonLiveDetail extends BaseRoomDetail {

    /**
     * 房间禁言时本地默认文案，支持服务端配置
     */
    private static final String TEXT_COMMENT_CLOSED_DEFAULT = "本场直播不予评论";

    /**
     * 新增流播放地址
     */
    private String playUrl;
    /**
     * 本场直播信息
     */
    private volatile LiveRecordInfo liveRecordInfo;
    /**
     * 主播信息
     */
    private volatile LiveAnchorInfo liveAnchorInfo;
    /**
     * 通过用户信息接口拉的当前用户信息
     */
    private LiveUserInfo mMyselfUserInfo;
    /**
     * 是否关注了主播
     */
    private boolean followAnchorFlag;
    /**
     * 匿名直播间flag
     */
    private boolean anonymousRoomFlag;
    /**
     * *    个播：直播间的粉丝团信息，目前：
     * *             "active":true,
     * *             "code":0, //-1  主播未开通粉丝团 code:0  未加入粉丝团 code:1  粉丝团成员
     * *             "fansGrade":0, //粉团等级
     * *             "fansIconId":1212,//粉团标签
     * *    这几个数据在userinfo接口中
     */
    public LiveUserInfo.FansClubVoBean roomFansClubVo;

    /**
     * LiveVideoRoom&&视频直播专用：当前用户的粉丝牌信息
     */
    private LiveUserInfo.FansClubVoBean currentUserFansClubVoBean;
    /**
     * 当前房间在线贵族数
     */
    private NobleClubVo mOnlineNoble;
    /**
     * 当前聊天室配置信息，暂时包括直播间评论全体禁言配置
     */
    private ChatRoomVoBean chatRoomVo;


    /**
     * pk 排位信息
     */
    private PKRankInfo pkRankInfo;

    /**
     * 头条信息
     */
    private CommonChatRoomTopHeadlinesMsg topMsg;

    /**
     * 新头条信息
     */
    private LiveHeadAnchor headAnchor;

    /**
     * 守护信息
     */
    private GuardInfoVO guardInfoVO;

    /**
     * 是否是今天首次评论
     */
    private boolean isFirstComment;

    /**
     * 是否是主播庆会
     */
    private boolean celebrationFlag;

    /**
     * 主播庆会返回值：
     * celebrationType 0:生日场  1：纪念日
     */
    @CelebrationType
    private int celebrationType;

    /**
     * 是否正在请求当前用户信息
     */
    private volatile boolean myInfoRequesting = false;


    /**
     * 小时榜更新信息
     */
    private List<RankInfoBean> rankInfos;

    public List<RankInfoBean> getRankInfos() {
        return rankInfos;
    }

    /**
     * 是否是官方直播间
     */
    private boolean officialRoomFlag;

    /**
     * 官方直播间数据
     */
    private OfficialLiveInfo officialInfo;

    /**
     * 是否是首映室直播间
     */
    private boolean premiereFlag;

    /**
     * 构造函数
     *
     * @param json data
     */
    public PersonLiveDetail(String json) {
        try {
            JSONObject ob = new JSONObject(json);
            if (ob.has("recordInfo")) {
                setLiveRecordInfo(new LiveRecordInfo(ob.optString("recordInfo")));
                roomId = getRoomId();
            }
            if (ob.has("userInfo")) {
                setLiveUserInfo(new LiveAnchorInfo(ob.optString("userInfo")));
            }
            if (ob.has("playUrl")) {
                playUrl = ob.optString("playUrl");
            }
            if (ob.has("fansClubVo")) {
                roomFansClubVo = new LiveUserInfo.FansClubVoBean(ob.optString("fansClubVo"));
            }

            if (ob.has("nobleClubVo")) {
                mOnlineNoble = new NobleClubVo(ob.optString("nobleClubVo"));
            }

            if (ob.has("chatRoomVo")) {
                chatRoomVo = new ChatRoomVoBean(ob.optString("chatRoomVo"));
            }

            if (ob.has("pkRankInfo")) {
                pkRankInfo = PKRankInfo.parse(ob.optString("pkRankInfo"));
            }

            if (ob.has("topMsg")) {
                topMsg = CommonChatRoomTopHeadlinesMsg.parse(ob.optString("topMsg"));
            }

            if (ob.has("isFirstComment")) {
                setFirstComment(ob.optBoolean("isFirstComment"));
            }

            if (ob.has("headAnchor")) {
                headAnchor = GsonUtils.parseJson(ob.optString("headAnchor"), LiveHeadAnchor.class);
            }

            if (ob.has("officialInfo")) {
                officialInfo = GsonUtils.parseJson(ob.optString("officialInfo"), OfficialLiveInfo.class);
            }

            if (ob.has("birthdayPartyFlag")) {
                celebrationFlag = ob.optBoolean("birthdayPartyFlag");
            }
            if (ob.has("celebrationType")) {
                celebrationType = ob.optInt("celebrationType");
            }
            if (ob.has("officialRoomFlag")) {
                officialRoomFlag = ob.optBoolean("officialRoomFlag");
            }
            if (ob.has("premiereFlag")) {
                premiereFlag = ob.optBoolean("premiereFlag");
            }
            if (ob.has("followAnchorFlag")) {
                followAnchorFlag = ob.optBoolean("followAnchorFlag");
            }
            if (ob.has("anonymousRoomFlag")) {
                anonymousRoomFlag = ob.optBoolean("anonymousRoomFlag");
            }
            JSONArray infos = ob.optJSONArray("rankInfos");
            if (infos != null) {
                rankInfos = new ArrayList<>();
                for (int len = infos.length(), i = 0; i < len; i++) {
                    RankInfoBean rankInfoBean = new RankInfoBean(infos.optString(i));
                    rankInfos.add(rankInfoBean);
                }
            }

            if (ob.has("guardInfo")) {
                guardInfoVO = GuardInfoVO.parse(ob.optString("guardInfo"));
            }

            if (ob.has("pendantRollFlag")) {
                setPendantRollFlag(ob.optBoolean("pendantRollFlag"));
            }

            if (ob.has("fansRankUrl")) {
                fansRankUrl = ob.optString("fansRankUrl");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public LiveHeadAnchor getHeadAnchor() {
        return headAnchor;
    }

    public void setHeadAnchor(LiveHeadAnchor headAnchor) {
        this.headAnchor = headAnchor;
    }


    public boolean isFirstComment() {
        return isFirstComment;
    }

    public void setFirstComment(boolean firstComment) {
        isFirstComment = firstComment;
    }

    public boolean isCelebrationFlag() {
        return celebrationFlag;
    }

    public void setCelebrationFlag(boolean celebrationFlag) {
        this.celebrationFlag = celebrationFlag;
    }

    @CelebrationType
    public int getCelebrationType() {
        return celebrationType;
    }

    public void setCelebrationType(int celebrationType) {
        this.celebrationType = celebrationType;
    }

    public String getFansClubHtmlUrl() {
        if (roomFansClubVo == null) {
            return "";
        }
        return roomFansClubVo.getFansClubHtmlUrl();
    }

    public ChatRoomVoBean getChatRoomVo() {
        return chatRoomVo;
    }

    /**
     * 查询当前登录用户发言气泡
     *
     * @return 当前登录用户发言气泡
     */
    public int getCurrentUserBubbleType() {
        if (mMyselfUserInfo != null) {
            return mMyselfUserInfo.getBubbleType();
        }
        return 0;
    }

    /**
     * 查询当前登录用户头像挂件
     *
     * @return 当前登录用户头像挂件
     */
    public int getCurrentUserHangerType() {
        if (mMyselfUserInfo != null) {
            return mMyselfUserInfo.getHangerType();
        }
        return 0;
    }

    /**
     * 查询当前登录用户是否是新用户或者30天未登录
     */
    public boolean isNewUser() {
        if (mMyselfUserInfo != null) {
            return mMyselfUserInfo.isNewUser();
        }
        return false;
    }

    /**
     * 查询当前登录用户财富等级
     *
     * @return 当前登录用户财富等级
     */
    public int getCurrentUserWealthGrade() {
        if (mMyselfUserInfo != null && mMyselfUserInfo.getWealthGrade() != null) {
            return mMyselfUserInfo.getWealthGrade().getGrade();
        }
        return 0;
    }

    public boolean isOfficialRoomFlag() {
        return officialRoomFlag;
    }

    public void setOfficialRoomFlag(boolean officialRoomFlag) {
        this.officialRoomFlag = officialRoomFlag;
    }

    public OfficialLiveInfo getOfficialInfo() {
        return officialInfo;
    }

    public boolean isPremiereFlag() {
        return premiereFlag;
    }

    public void setPremiereFlag(boolean premiereFlag) {
        this.premiereFlag = premiereFlag;
    }

    public boolean currentLoginUserIsAdmin() {
        if (mMyselfUserInfo != null) {
            return mMyselfUserInfo.isOperatorIsAdmin();
        }
        return false;
    }

    public LiveRecordInfo getLiveRecordInfo() {
        return liveRecordInfo;
    }

    public PKRankInfo getPkRankInfo() {
        return pkRankInfo;
    }

    public void setLiveRecordInfo(LiveRecordInfo liveRecordInfo) {
        this.liveRecordInfo = liveRecordInfo;
    }

    public LiveAnchorInfo getLiveAnchorInfo() {
        return liveAnchorInfo;
    }

    public void setLiveUserInfo(LiveAnchorInfo liveUserInfo) {
        this.liveAnchorInfo = liveUserInfo;
    }

    public NobleClubVo getOnlineNoble() {
        return mOnlineNoble;
    }

    public GuardInfoVO getGuardInfoVO() {
        return guardInfoVO;
    }

    @NonNull
    @Override
    public String toString() {
        return "PersonLiveDetail{" +
                "liveRecordInfo=" + liveRecordInfo +
                ", liveUserInfo=" + liveAnchorInfo +
                ", mMyselfUserInfo=" + mMyselfUserInfo +
                ", roomFansClubVo=" + roomFansClubVo +
                ", currentUserFansClubVoBean=" + currentUserFansClubVoBean +
                ", mOnlineNoble=" + mOnlineNoble +
                ", chatRoomVo=" + chatRoomVo +
                ", pkRankInfo=" + pkRankInfo +
                ", topMsg=" + topMsg +
                ", headAnchor=" + headAnchor +
                ", guardInfoVO=" + guardInfoVO +
                ", isFirstComment=" + isFirstComment +
                ", birthdayPartyFlag=" + celebrationFlag +
                ", celebrationType=" + celebrationType +
                ", myInfoRequesting=" + myInfoRequesting +
                ", rankInfos=" + rankInfos +
                ", mLivePlaySourceInfo=" + mLivePlaySourceInfo +
                '}';
    }

    /**
     * 查询当前登录用户的勋章信息
     *
     * @return 当前登录用户的勋章信息
     */
    public LiveUserInfo.MedalInfo getCurrentUserMedalInfo() {
        if (mMyselfUserInfo != null) {
            return mMyselfUserInfo.getMedalInfo();
        }
        return null;
    }

    @Override
    public long getRoomId() {
        return liveRecordInfo != null ? liveRecordInfo.roomId : -1;
    }

    public boolean isFollowed() {
        boolean follow = followAnchorFlag;
        if (mMyselfUserInfo != null) {
            follow = mMyselfUserInfo.isFollowed();
        }
        return follow;
    }

    public long getLiveId() {
        return liveRecordInfo != null ? liveRecordInfo.id : -1;
    }

    @Override
    public long getHostUid() {
        return liveAnchorInfo != null ? liveAnchorInfo.uid : 0;
    }

    public String getHostNickname() {
        return liveAnchorInfo != null ? liveAnchorInfo.nickname : "";
    }

    @Override
    public long getChatId() {
        return liveRecordInfo != null ? liveRecordInfo.chatId : -1;
    }

    @Override
    @PersonLiveBase.LiveStatus
    public int getStatus() {
        return liveRecordInfo != null ? liveRecordInfo.status : PersonLiveBase.LIVE_STATUS_UNSPECIFIED;
    }

    @Override
    @BaseScrollConstant.LiveRoomBizType
    public int getRoomBizType() {
        return liveRecordInfo != null ? liveRecordInfo.bizType : BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO;
    }

    @Override
    public int getRoomSubBizType() {
        return liveRecordInfo != null ? liveRecordInfo.categoryId : 0;
    }

    @Override
    public boolean isAnonymousRoom() {
        return anonymousRoomFlag;
    }

    public CommonChatRoomTopHeadlinesMsg getTopMsg() {
        return topMsg;
    }

    @Override
    public void setRoomForbidden(boolean isForbidden) {
        if (getChatRoomVo() == null) {
            return;
        }

        getChatRoomVo().commentClosed = isForbidden;
    }

    @Override
    public boolean isRoomForbidden() {
        return getChatRoomVo() != null && getChatRoomVo().commentClosed;
    }

    @Override
    public String getRoomForbiddenHint() {
        if (getChatRoomVo() == null || TextUtils.isEmpty(getChatRoomVo().commentClosedMsg)) {
            return TEXT_COMMENT_CLOSED_DEFAULT;
        }

        return getChatRoomVo().commentClosedMsg;
    }

    @Override
    public void setLiveStatus(@PersonLiveBase.LiveStatus int status) {
        if (getLiveRecordInfo() == null) {
            return;
        }

        getLiveRecordInfo().status = status;
    }

    @Override
    public long getLiveStartAt() {
        return getLiveRecordInfo() == null ? 0 : getLiveRecordInfo().actualStartAt;
    }

    @Override
    public long getLivePlanToStartAt() {
        return getLiveRecordInfo() == null ? 0 : getLiveRecordInfo().startAt;
    }

    @Override
    public long getLiveStopAt() {
        return getLiveRecordInfo() == null ? 0 : getLiveRecordInfo().actualStopAt;
    }

    @Override
    public boolean isBooking() {
        // 个人直播关注则代表预约
        return isFollowed();
    }

    @Override
    public boolean isBookingThisRecord() {
        return getLiveRecordInfo() != null && getLiveRecordInfo().book;
    }

    @Override
    public void updateDescription(String desc) {
        if (getLiveRecordInfo() == null) {
            return;
        }

        getLiveRecordInfo().description = desc;
    }

    @Override
    public String getDescription() {
        return getLiveRecordInfo() == null ? "" : getLiveRecordInfo().description;
    }

    @Override
    public String getAnchorAvatar() {
        return liveAnchorInfo != null ? liveAnchorInfo.middleAvatar : "";
    }

    @Override
    public String getLargeAvatar() {
        return liveAnchorInfo != null ? liveAnchorInfo.largeAvatar : "";
    }

    @Override
    public String getAnchorName() {
        return getHostNickname();
    }

    @Override
    public String getRoomTitle() {
        return getLiveRecordInfo() == null ? "" : getLiveRecordInfo().name;
    }

    @Override
    public int getOnlineCount() {
        return (int) (getLiveRecordInfo() == null ? 0 : getLiveRecordInfo().onlineCount);
    }

    @Override
    public int getParticipateCount() {
        return (int) (getLiveRecordInfo() == null ? 0 : getLiveRecordInfo().playCount);
    }

    @Override
    public void setFollowed(boolean followed) {
        followAnchorFlag = followed;
        if (mMyselfUserInfo == null) {
            return;
        }

        mMyselfUserInfo.setFollowed(followed);
    }

    @Override
    public String getSmallCoverPath() {
        return getLiveRecordInfo() == null ? "" : getLiveRecordInfo().coverSmall;
    }

    @Override
    public String getLargeCoverPath() {
        return getLiveRecordInfo() == null ? "" : getLiveRecordInfo().coverLarge;
    }

    @Override
    public String getBgImage() {
        if (liveAnchorInfo != null && !TextUtils.isEmpty(liveAnchorInfo.bgImagePath)) {
            return liveAnchorInfo.bgImagePath;
        }
        return "";
    }

    @Override
    public long getFMId() {
        return liveRecordInfo != null ? liveRecordInfo.fmId : 0;
    }

    @Override
    @LiveMediaType
    public int getMediaType() {
        return getLiveRecordInfo() != null ? getLiveRecordInfo().mediaType : LiveMediaType.TYPE_AUDIO;
    }

    @Override
    public LiveUserInfo.FansClubVoBean getRoomFansClubVo() {
        return currentUserFansClubVoBean;
    }

    @Override
    public void updateFansClubCount(int count) {
        if (roomFansClubVo != null) {
            roomFansClubVo.setCount(count);
        }
    }

    @Override
    public void updateFansClubJoinStatus(boolean isJoined) {
        if (roomFansClubVo != null) {
            roomFansClubVo.setCode(TYPE_JOINED);
        }
    }

    @Override
    public boolean isHasGoods() {
        return getLiveRecordInfo() != null && getLiveRecordInfo().hasGoods;
    }

    @Override
    public String getCarGifUrl() {
        return getLiveRecordInfo() != null ? getLiveRecordInfo().cartGifUrl : "";
    }

    @Override
    public String getPlayUrl() {
        return playUrl;
    }

    @Override
    public boolean isHorizontal() {
        return getLiveRecordInfo() == null || getLiveRecordInfo().horizontalFlag;
    }

    /**
     * 直播场次信息
     */
    public static class LiveRecordInfo {
        public long id;                 // 场次id
        public long roomId;             // 场次所属直播间id
        public long chatId;             // 场次对应聊天室id
        public String name;             // 场次标题
        public String coverLarge;       // 大封面图 640*640
        public String coverMiddle;      // 中封面图 290*290
        public String coverSmall;       // 小封面图 86*86
        public long startAt;            // 140002334133, 开始时间
        public long endAt;              // 140002334133, 结束时间
        public int categoryId;          // 46 生活, 直播二级分类id
        public long actualStartAt;      // 140233223243, 实际开始时间
        public long actualStopAt;       // 140233456243, 实际结束时间
        public String description;      // 简介
        @PersonLiveBase.LiveStatus
        public int status;              // 直播状态， 1:已结束、5:预告、9:直播中
        public long onlineCount;        // 在线人数
        public long playCount;          // 参与人次
        public boolean isSaveTrack;     // 是否保存了回听
        public boolean book;            // 是否订阅
        public int fansIncrCnt;         // 增加的粉丝数
        public boolean notifyFans;      // 是否提醒粉丝
        public long fmId;               // 直播房间号
        public long msgCount;           // 本场次消息总数，场次为结束状态时返回该值
        public @BaseScrollConstant.LiveRoomBizType
        int bizType = BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO;         // 直播业务类型
        public int liveType;            // 新增字段liveType
        @LiveMediaType
        public int mediaType;           // 直播媒体类型  1：音频 2：视频
        public boolean horizontalFlag = true; // 横屏视频直播标志位，默认横屏
        public boolean alive;           // cdn流是否正常
        public long tagId;              //话题Id
        public String hotTopicTitle;    //话题title
        /**
         * 热度值字段
         */
        public long hotScore;
        /**
         * 热度值图标
         */
        public String hotScoreIconPath;

        public String cartGifUrl;   //购物车图标动图url地址
        public boolean hasGoods;    //直播间是否有商品上架
        public @IDeviceType
        int deviceType;      //检查当前直播推流方式 1手机 2电脑 3第三方推流

        public LiveRecordInfo(String json) {
            try {
                JSONObject ob = new JSONObject(json);
                isSaveTrack = ob.optBoolean("isSaveTrack");
                name = ob.optString("name");
                hotScoreIconPath = ob.optString("hotScoreIconPath");
                id = ob.optLong("id");
                roomId = ob.optLong("roomId");
                chatId = ob.optLong("chatId");
                hotScore = ob.optLong("hotScore");
                coverLarge = ob.optString("coverLarge");
                coverMiddle = ob.optString("coverMiddle");
                coverSmall = ob.optString("coverSmall");
                startAt = ob.optLong("startAt");
                endAt = ob.optLong("endAt");
                categoryId = ob.optInt("categoryId");
                actualStartAt = ob.optLong("actualStartAt");
                actualStopAt = ob.optLong("actualStopAt");
                description = ob.optString("description");
                status = ob.optInt("status");
                fansIncrCnt = ob.optInt("fansIncrCnt");
                notifyFans = ob.optBoolean("notifyFans");
                alive = ob.optBoolean("alive");
                book = ob.optBoolean("book");
                if (ob.has("onlineCount")) {
                    onlineCount = ob.optLong("onlineCount");
                }
                if (ob.has("playCount")) {
                    playCount = ob.optLong("playCount");
                }
                fmId = ob.optLong("fmId");

                if (ob.has("msgCount")) {
                    msgCount = ob.optLong("msgCount");
                }
                if (ob.has("bizType")) {
                    bizType = ob.optInt("bizType");
                }
                if (ob.has("liveType")) {
                    liveType = ob.optInt("liveType");
                }
                if (ob.has("mediaType")) {
                    mediaType = ob.optInt("mediaType");
                }
                if (ob.has("horizontalFlag")) {
                    horizontalFlag = ob.optBoolean("horizontalFlag");
                }
                if (ob.has("tagId")) {
                    tagId = ob.optLong("tagId");
                }
                if (ob.has("hotTopicTitle")) {
                    hotTopicTitle = ob.getString("hotTopicTitle");
                }
                if (ob.has("cartGifUrl")) {
                    cartGifUrl = ob.optString("cartGifUrl");
                }

                if (ob.has("hasGoods")) {
                    hasGoods = ob.optBoolean("hasGoods");
                }
                if (ob.has("deviceType")) {
                    deviceType = ob.optInt("deviceType");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        public LiveRecordInfo() {

        }

        @NonNull
        @Override
        public String toString() {
            return "LiveRecordInfo{" +
                    "id=" + id +
                    ", roomId=" + roomId +
                    ", chatId=" + chatId +
                    ", name='" + name + '\'' +
                    ", coverLarge='" + coverLarge + '\'' +
                    ", coverMiddle='" + coverMiddle + '\'' +
                    ", coverSmall='" + coverSmall + '\'' +
                    ", startAt=" + startAt +
                    ", endAt=" + endAt +
                    ", categoryId=" + categoryId +
                    ", actualStartAt=" + actualStartAt +
                    ", actualStopAt=" + actualStopAt +
                    ", description='" + description + '\'' +
                    ", status=" + status +
                    ", onlineCount=" + onlineCount +
                    ", playCount=" + playCount +
                    ", isSaveTrack=" + isSaveTrack +
                    ", fansIncrCnt=" + fansIncrCnt +
                    ", notifyFans=" + notifyFans +
                    ", fmId=" + fmId +
                    ", msgCount=" + msgCount +
                    ", bizType=" + bizType +
                    ", liveType=" + liveType +
                    ", mediaType=" + mediaType +
                    ", horizontalFlag=" + horizontalFlag +
                    ", cartGifUrl=" + cartGifUrl +
                    ", hasGoods=" + hasGoods +
                    ", deviceType=" + deviceType +
                    '}';
        }
    }

    /**
     * 主播信息
     */
    public static class LiveAnchorInfo extends LiveUserInfo {

        /**
         * 财富等级
         */
        public int wealthGrade;
        /**
         * 关注数
         */
        public long followingCount;

        /**
         * 直播背景
         */
        public String bgImagePath;
        /**
         * 直播间支持动态mp4视频背景
         */
        public String dynamicBgUrl;
        /**
         * 主播是否申请并成功开通了粉丝团，true代表已开通
         */
        public boolean hasFansClub;

        /**
         * 直播间头像框装扮（服务端下发的类生日场装扮，但优先级比生日场低）
         */
        @Nullable
        public LiveRoomAvatarDecorate roomAvatarDecorate;

        public LiveAnchorInfo() {
        }

        public LiveAnchorInfo(String json) {
            try {
                JSONObject ob = new JSONObject(json);
                uid = ob.optLong("uid");
                nickname = ob.optString("nickname");
                description = ob.optString("description");
                avatar = ob.optString("avatar");
                largeAvatar = ob.optString("largeAvatar");
                middleAvatar = ob.optString("middleAvatar");
                smallAvatar = ob.optString("smallAvatar");
                isVerify = ob.optBoolean("isVerify");
                followerCount = ob.optLong("followerCount");
                followingCount = ob.optLong("followingCount");
                bgImagePath = ob.optString("bgImagePath");
                dynamicBgUrl = ob.optString("dynamicBgUrl");
                wealthGrade = ob.optInt("wealthGrade");
                if (ob.has("hasFansClub")) {
                    hasFansClub = ob.optBoolean("hasFansClub", false);
                }
                JSONObject avatarDecorate = ob.optJSONObject("specialAvatarBox");
                if (avatarDecorate != null) {
                    int type = avatarDecorate.optInt("type", 0);
                    int templateId = avatarDecorate.optInt("templateId", -1);
                    roomAvatarDecorate = new LiveRoomAvatarDecorate(type, templateId);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @NonNull
        @Override
        public String toString() {
            return "LiveUserInfo{" +
                    "uid=" + uid +
                    ", nickname='" + nickname + '\'' +
                    ", avatar='" + avatar + '\'' +
                    ", largeAvatar='" + largeAvatar + '\'' +
                    ", middleAvatar='" + middleAvatar + '\'' +
                    ", smallAvatar='" + smallAvatar + '\'' +
                    ", wealthGrade=" + wealthGrade +
                    ", followerCount=" + followerCount +
                    ", followingCount=" + followingCount +
                    ", description='" + description + '\'' +
                    ", bgImagePath='" + bgImagePath + '\'' +
                    ", dynamicBgUrl='" + dynamicBgUrl + '\'' +
                    '}';
        }
    }

    /**
     * 在线贵族信息
     */
    public static class NobleClubVo {
        /**
         *
         */
        public String nobleClubHtmlUrl;
        /**
         * 直播间内当前贵族数量
         */
        public int count;

        public NobleClubVo(String s) {
            try {
                JSONObject jsonObject = new JSONObject(s);
                nobleClubHtmlUrl = jsonObject.optString("nobleClubHtmlUrl");
                count = jsonObject.optInt("count");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @NonNull
        @Override
        public String toString() {
            return "NobleClubVo{nobleClubHtmlUrl=" + nobleClubHtmlUrl + ", count=" + count + "}";
        }
    }

    public static class ChatRoomVoBean {
        public long chatId;
        public boolean commentClosed;
        public String commentClosedMsg;

        public ChatRoomVoBean(String s) {
            try {
                JSONObject jsonObject = new JSONObject(s);
                chatId = jsonObject.optLong("chatId");
                commentClosed = jsonObject.optBoolean("commentClosed");
                commentClosedMsg = jsonObject.optString("commentClosedMsg");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @NonNull
        @Override
        public String toString() {
            return "ChatRoomVoBean{" +
                    "chatId=" + chatId +
                    ", commentClosed=" + commentClosed +
                    ", commentClosedMsg='" + commentClosedMsg + '\'' +
                    '}';
        }
    }

    public static class RankInfoBean {
        private int type;
        private String msg;
        public static int TYPE_HOUR = 1;

        public RankInfoBean(String s) {
            try {
                JSONObject jsonObject = new JSONObject(s);
                type = jsonObject.optInt("type");
                msg = jsonObject.optString("msg");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @NonNull
        @Override
        public String toString() {
            return "RankInfoBean{" +
                    "type=" + type +
                    ", msg='" + msg + '\'' +
                    '}';
        }

        public int getType() {
            return type;
        }

        public String getMsg() {
            return msg;
        }
    }

    public static class GuardInfoVO {
        //守护神uid
        private long uid;
        private int guardType;
        private String name;
        //守护神头像
        private String avatar;
        //守护数量
        private long count;
        // 周榜
        private String weeklyRankUrl;
        // 守护详情(开通页)
        private String guardDetailUrl;

        public static GuardInfoVO parse(String json) {
            GuardInfoVO guardInfo = new GuardInfoVO();
            try {
                JSONObject jsonObject = new JSONObject(json);
                if (jsonObject.has("uid")) {
                    guardInfo.uid = jsonObject.optLong("uid");
                }
                if (jsonObject.has("name")) {
                    guardInfo.name = jsonObject.optString("name");
                }
                if (jsonObject.has("guardType")) {
                    guardInfo.guardType = jsonObject.optInt("guardType");
                }
                if (jsonObject.has("avatar")) {
                    guardInfo.avatar = jsonObject.optString("avatar");
                }
                if (jsonObject.has("count")) {
                    guardInfo.count = jsonObject.optLong("count");
                }
                if (jsonObject.has("weeklyRankUrl")) {
                    guardInfo.weeklyRankUrl = jsonObject.optString("weeklyRankUrl");
                }
                if (jsonObject.has("guardDetailUrl")) {
                    guardInfo.guardDetailUrl = jsonObject.optString("guardDetailUrl");
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return guardInfo;
        }

        public Long getUid() {
            return uid;
        }

        public void setUid(Long uid) {
            this.uid = uid;
        }

        public int getGuardType() {
            return guardType;
        }

        public void setGuardType(int guardType) {
            this.guardType = guardType;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }

        public String getWeeklyRankUrl() {
            return weeklyRankUrl;
        }

        public String getGuardDetailUrl() {
            return guardDetailUrl;
        }

        @NonNull
        @Override
        public String toString() {
            return "GuardInfoVO{" +
                    "uid=" + uid +
                    ", guardType=" + guardType +
                    ", name='" + name + '\'' +
                    ", avatar='" + avatar + '\'' +
                    ", count=" + count +
                    ", weeklyRankUrl='" + weeklyRankUrl + '\'' +
                    ", guardDetailUrl='" + guardDetailUrl + '\'' +
                    '}';
        }
    }

    private final ArraySet<IDataCallBack<LiveUserInfo>> mMySelfUserInfoCallbacks = new ArraySet<>();

    private void notifyMySelfInfoCallback(LiveUserInfo info) {
        Iterator<IDataCallBack<LiveUserInfo>> iterator = mMySelfUserInfoCallbacks.iterator();
        for (; iterator.hasNext(); ) {
            IDataCallBack<LiveUserInfo> callBack = iterator.next();
            callBack.onSuccess(info);
        }
        mMySelfUserInfoCallbacks.clear();
    }

    /***
     *  获取我的用户信息，从中获取财富等级等信息
     **/
    public void loadMyUserInfoSyncOrAsync(boolean reRequest, final IDataCallBack<LiveUserInfo>
            userInfoIDataCallBack) {
        if (!reRequest && mMyselfUserInfo != null && userInfoIDataCallBack != null) {
            userInfoIDataCallBack.onSuccess(mMyselfUserInfo);
            return;
        }
        if (myInfoRequesting) {
            if (userInfoIDataCallBack != null) {
                mMySelfUserInfoCallbacks.add(userInfoIDataCallBack);
            }
            return;
        }
        if (UserInfoMannage.hasLogined() && UserInfoMannage.getUid() > 0) {
            myInfoRequesting = true;
            loadUserInfoAsync(UserInfoMannage.getUid(), new IDataCallBack<LiveUserInfo>() {
                @Override
                public void onSuccess(@Nullable LiveUserInfo object) {
                    myInfoRequesting = false;
                    handleUserSelfInfo(object);
                    notifyMySelfInfoCallback(object);
                    if (userInfoIDataCallBack != null) {
                        userInfoIDataCallBack.onSuccess(object);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    myInfoRequesting = false;
                    notifyMySelfInfoCallback(null);
                }
            });
        }
    }

    public void handleUserSelfInfo(@Nullable LiveUserInfo object) {
        if (object != null) {
            mMyselfUserInfo = object;
            LiveUserInfo.FansClubVoBean userClubInfo = object.getFansClubInfo();
            if (userClubInfo != null) {
                roomFansClubVo.setActive(userClubInfo.isActive());
                roomFansClubVo.setClubName(userClubInfo.getClubName());
                roomFansClubVo.setCode(userClubInfo.getCode());
                roomFansClubVo.setFansGrade(userClubInfo.getFansGrade());
                roomFansClubVo.setClubIconId(userClubInfo.getClubIconId());
            }
            //fansClubVoBean是LiveVideoRoom使用userinfo接口中的数据！！！
            currentUserFansClubVoBean = object.getFansClubInfo();
        }
    }

    public void loadUserInfoAsync(long targetUid, final IDataCallBack<LiveUserInfo> iDataCallBack) {
        if (liveRecordInfo == null) {
            if (iDataCallBack != null) {
                iDataCallBack.onSuccess(null);
            }
            return;
        }
        CommonRequestForCommon.queryUserInfoInLiveRoom(liveRecordInfo.roomId, targetUid,
                new IDataCallBack<LiveUserInfo>() {
                    @Override
                    public void onSuccess(final LiveUserInfo object) {
                        Logger.log("loadUserInfoAsync onSuccess " + object);
                        if (iDataCallBack != null) {
                            iDataCallBack.onSuccess(object);
                        }
                    }

                    @Override
                    public void onError(int code, final String message) {
                        Logger.log("requestUserInfoInner error" + code + message);
                        if (iDataCallBack != null) {
                            iDataCallBack.onError(code, message);
                        }
                    }

                });
    }

    /**
     * PK 排位信息
     */
    public static class PKRankInfo {
        public int grade;
        public String icon;
        public int seasonId;

        public PKRankInfo() {

        }

        public PKRankInfo(int grade, String icon) {
            this.grade = grade;
            this.icon = icon;
        }

        public static PKRankInfo parse(String json) {
            PKRankInfo pkRankInfo = new PKRankInfo();
            try {
                JSONObject jsonObject = new JSONObject(json);
                if (jsonObject.has("grade")) {
                    pkRankInfo.grade = jsonObject.optInt("grade");
                }
                if (jsonObject.has("seasonId")) {
                    pkRankInfo.seasonId = jsonObject.optInt("seasonId");
                }
                if (jsonObject.has("icon")) {
                    pkRankInfo.icon = jsonObject.optString("icon");
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return pkRankInfo;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            PKRankInfo that = (PKRankInfo) o;

            if (grade != that.grade) return false;
            return Objects.equals(icon, that.icon);
        }

        @Override
        public int hashCode() {
            int result = grade;
            result = 31 * result + (icon != null ? icon.hashCode() : 0);
            return result;
        }
    }

    private ILivePlaySourceInfo mLivePlaySourceInfo;

    public ILivePlaySourceInfo getLivePlaySourceInfo() {
        if (mLivePlaySourceInfo == null) {
            mLivePlaySourceInfo = new LiveStreamInfo(this);
        }
        return mLivePlaySourceInfo;
    }

    private static class LiveStreamInfo implements ILivePlaySourceInfo {
        private final PersonLiveDetail mDetail;

        private LiveStreamInfo(@NonNull PersonLiveDetail detail) {
            mDetail = detail;
        }

        @Override
        public long getLiveId() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().id : 0;
        }

        @Override
        public int getStatus() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().status : PersonLiveBase.LIVE_STATUS_UNSPECIFIED;
        }

        @Override
        public long getStreamUid() {
            return mDetail.getLiveAnchorInfo() != null ? mDetail.getLiveAnchorInfo().uid : 0;
        }

        @Override
        public String getHostNickname() {
            return mDetail.getLiveAnchorInfo() != null ? mDetail.getLiveAnchorInfo().nickname : "";
        }

        @Override
        public String getHostAvatar() {
            return mDetail.getLiveAnchorInfo() != null ? mDetail.getLiveAnchorInfo().avatar : "";
        }

        @Override
        public String largeCoverUrl() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().coverLarge : "";
        }

        @Override
        public String middleCoverUrl() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().coverMiddle : "";
        }

        @Override
        public String smallCoverUrl() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().coverSmall : "";
        }

        @Override
        public String title() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().name : "";
        }

        @Override
        public String trackInfo() {
            return "";
        }

        @Override
        public long getRoomId() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().roomId : 0;
        }

        @Override
        @BaseScrollConstant.LiveRoomBizType
        public int getLiveType() {
            return mDetail.getLiveRecordInfo() != null ? mDetail.getLiveRecordInfo().bizType : BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO;
        }

        @Override
        public boolean needInterceptNetChangeAutoPlay() {
            return mDetail.isPremiereFlag();
        }

        @Override
        public int getRoomModelType() {
            if (mDetail.isPremiereFlag()) {
                return RoomModeType.ROOM_MODEL_PREMIERE;
            } else if (mDetail.isOfficialRoomFlag()) {
                return RoomModeType.ROOM_MODEL_OFFICIAL;
            }
            return RoomModeType.ROOM_MODEL_DEFAULT;
        }
    }

    /**
     * 当前用户是否加入主播粉丝团
     *
     * @return true 已加入 false 未加入
     */
    public boolean currentLoginUserIsJoinFansClub() {
        if (currentUserFansClubVoBean != null) {
            return currentUserFansClubVoBean.isJoinFansClub();
        }
        return false;
    }

    @Override
    public String getRoomNormalBackgroundUrl() {
        return getBgImage();
    }

    @Override
    public String getRoomDynamicBackgroundUrl() {
        return liveAnchorInfo != null ? liveAnchorInfo.dynamicBgUrl : "";
    }

    @Override
    public boolean isFollowAnchor() {
        return isFollowed();
    }

    @Override
    public String getRoomName() {
        return getLiveRecordInfo() != null ? getLiveRecordInfo().name : super.getRoomName();
    }

    @Override
    public void updateOnlineNobleCount(int count) {
        if (mOnlineNoble != null) {
            mOnlineNoble.count = count;
        }
    }

    @Override
    public int getCategoryId() {
        return liveAnchorInfo == null ? 0 : liveRecordInfo.categoryId;
    }
}
