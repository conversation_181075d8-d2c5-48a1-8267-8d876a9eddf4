package com.ximalaya.ting.android.live.common.lib.http;


import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.collection.ArrayMap;

import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.reqcdn.CdnFileRequestHelper;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.download_control.model.RemoteDownloadSettingInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.gift.panel.util.MemoryMonitorUtils;
import com.ximalaya.ting.android.live.common.lib.model.LiveTemplateModel;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveStringUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.io.IOException;
import java.io.StringReader;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 直播间模板 网络请求
 *
 * <AUTHOR>
 */
public class CommonRequestForTemplate extends CommonRequestM {

    private static final  String TAG = "CommonRequestForTemplate";

    public static void getAllTemplateByCdnFile(int bizType, IDataCallBack<LiveTemplateModel> callBack) {
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put("resolutionType", LiveHelper.getMp4SourceQuality() + "");
        params.put("bizType", String.valueOf(bizType));

        CdnFileRequestHelper.INSTANCE.requestAllLiveTemplateInfoStr(params, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String content) {
                MyAsyncTask.execute(() -> {
                    LiveTemplateModel templateModel = null;

                    MemoryMonitorUtils.ParseMonitor monitor = new MemoryMonitorUtils.ParseMonitor("解析模版信息接口");
                    try {
                        monitor.startParsing();
                        if (LiveSettingManager.templateInfoStreamParseOpen()) {
                            templateModel = parseStreamDataStrToTemplateInfoByCnd(content);
                        } else {
                            templateModel = convertDataStrToTemplateInfo(content);
                        }
                        monitor.endParsing();
                        CommonLiveLogger.d(TAG, "直接解析json耗费内存和时间\n:" + monitor.getReport());
                    } catch (Throwable e) {
                        CommonLiveLogger.e(TAG, "流式解析异常:" + e);
                        CrashReport.postCatchedException(e);
                        try {
                            // 流式解析降级兜底
                            templateModel = convertDataStrToTemplateInfo(content);
                        } catch (Throwable ex) {
                            e.printStackTrace();
                            CrashReport.postCatchedException(ex);
                        }
                    }

                    LiveTemplateModel finalTemplateModel = templateModel;
                    HandlerManager.postOnUIThread(() -> {
                        if (finalTemplateModel != null) {
                            if (callBack != null) {
                                callBack.onSuccess(finalTemplateModel);
                            }
                        } else {
                            if (callBack != null) {
                                callBack.onError(-1, "服务返回数据异常！");
                            }
                        }
                    });
                });
            }

            @Override
            public void onError(int code, String message) {
                if (callBack != null) {
                    callBack.onError(code, message);
                }

                LiveXdcsUtil.doXDCS("LiveCdnReqFail",
                        "GetAllTemplateByCdnFile Fail! ErrCode=" + code + ", ErrMsg=" + message);
            }
        });
    }

    /**
     * 流式解析，CDN文件没有data数据结构
     */
    private static LiveTemplateModel parseStreamDataStrToTemplateInfoByCnd(String dataStr) throws Exception {
        if (TextUtils.isEmpty(dataStr)) {
            return null;
        }

        Logger.i(TAG, "Using streaming parse for large JSON");
        Logger.i(TAG, "JSON memory size in Byte: " + LiveStringUtil.calculateStringMemorySize(dataStr));

        try (JsonReader reader = new JsonReader(new StringReader(dataStr))) {
            reader.setLenient(true); // 宽松模式
            // 直接解析key-value结构
            return parseTemplateDataNodeManually(reader);
        } catch (IOException e) {
            Logger.e("StreamParseUtils", "JsonReader解析失败", e);
            throw new Exception("Failed to parse JSON with streaming", e);
        }
    }

    /**
     * 查询模板信息
     * <a href="http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/tags/%E6%A8%A1%E7%89%88api%E8%AF%B4%E6%98%8E.md">接口文档</a>
     *
     * @param callBack 请求结果回调信息
     */
    public static void getAllTemplate(int bizType, IDataCallBack<LiveTemplateModel> callBack) {
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put("resolutionType", LiveHelper.getMp4SourceQuality() + "");
        params.put("bizType", String.valueOf(bizType));

        baseGetRequest(LiveTemplateUrlConstants.getInstance().getAllTemplateUrl(), params, callBack, content -> {
            try {
                if (LiveSettingManager.templateInfoStreamParseOpen()) {
                    // 流式解析
                    return parseStreamDataStrToTemplateInfo(content);
                } else {
                    // 普通解析
                    return convertJsonStrToTemplateInfo(content);
                }
            } catch (Throwable err) {
                err.printStackTrace();
                CrashReport.postCatchedException(err);
                try {
                    // 流式解析降级兜底
                    return convertJsonStrToTemplateInfo(content);
                } catch (Throwable e) {
                    e.printStackTrace();
                    CrashReport.postCatchedException(err);
                }
            }

            return null;
        });

    }


    @WorkerThread
    private static LiveTemplateModel convertJsonStrToTemplateInfo(String content) throws Exception {
        if (TextUtils.isEmpty(content)) return null;

        Logger.i(TAG, "sGson直接解析");
        Logger.i(TAG, "JSON memory size in Byte: " + LiveStringUtil.calculateStringMemorySize(content));

        JSONObject jsonObject = new JSONObject(content);
        if (!jsonObject.has("ret") || jsonObject.optInt("ret") != 0) {
            return null;
        }

        String data = jsonObject.optString("data");

        if (TextUtils.isEmpty(data)) {
            return null;
        }

        return convertDataStrToTemplateInfo(data);
    }

    /**
     * 流式解析，更节省解析时间
     */
    private static LiveTemplateModel parseStreamDataStrToTemplateInfo(String dataStr) throws Exception {
        if (TextUtils.isEmpty(dataStr)) {
            return null;
        }

        Logger.i(TAG, "Using streaming parse for large JSON");
        Logger.i(TAG, "JSON memory size in Byte: " + LiveStringUtil.calculateStringMemorySize(dataStr));

        try (JsonReader reader = new JsonReader(new StringReader(dataStr))) {
            reader.setLenient(true); // 宽松模式
            // 使用JsonReader + Gson解析（减少内存占用）
            return parseTemplateInfoWithStreaming(reader);
        } catch (IOException e) {
            Logger.e("StreamParseUtils", "JsonReader解析失败", e);
            throw new Exception("Failed to parse JSON with streaming", e);
        }
    }

    /**
     * 解析data对象
     */
    private static LiveTemplateModel parseTemplateInfoWithStreaming(JsonReader reader) throws IOException {
        reader.beginObject();
        while (reader.hasNext()) {
            String name = reader.nextName();
            if ("data".equals(name)) {
                // 找到data节点，手动解析
                return parseTemplateDataNodeManually(reader);
            } else {
                reader.skipValue();
            }
        }
        reader.endObject();
        return null;
    }

    /**
     * 手动解析data节点，构建LiveTemplateModel对象
     */
    private static LiveTemplateModel parseTemplateDataNodeManually(JsonReader reader) throws IOException {
        LiveTemplateModel liveTemplateModel = new LiveTemplateModel();

        reader.beginObject();
        while (reader.hasNext()) {
            // 读取模板ID作为键
            String templateId = reader.nextName();

            try {
                // 开始解析模板详情对象
                reader.beginObject();
                LiveTemplateModel.TemplateDetail templateDetail = new LiveTemplateModel.TemplateDetail();

                while (reader.hasNext()) {
                    String fieldName = reader.nextName();

                    switch (fieldName) {
                        case "id":
                            templateDetail.setId(reader.nextLong());
                            break;
                        case "name":
                            templateDetail.setName(reader.nextString());
                            break;
                        case "type":
                            templateDetail.setType(String.valueOf(reader.nextInt()));
                            break;
                        case "core":
                            templateDetail.setCore(reader.nextBoolean());
                            break;
                        case "bgImagePath":
                            templateDetail.setBgImagePath(reader.nextString());
                            break;
                        case "bgImageURPath":
                            templateDetail.setBgImageURPath(reader.nextString());
                            break;
                        case "bgImageDRPath":
                            templateDetail.setBgImageDRPath(reader.nextString());
                            break;
                        case "svgMd5":
                            templateDetail.setSvgMd5(reader.nextString());
                            break;
                        case "svgPath":
                            templateDetail.setSvgPath(reader.nextString());
                            break;
                        case "mp4Path":
                            templateDetail.setMp4Path(reader.nextString());
                            break;
                        case "mp4Md5":
                            templateDetail.setMp4Md5(reader.nextString());
                            break;
                        case "h265Mp4Path":
                            templateDetail.setH265Mp4Path(reader.nextString());
                            break;
                        case "h265Mp4Md5":
                            templateDetail.setH265Mp4Md5(reader.nextString());
                            break;
                        case "iconPath":
                            templateDetail.setIconPath(reader.nextString());
                            break;
                        case "dynamicEffectPath":
                            templateDetail.setDynamicEffectPath(reader.nextString());
                            break;
                        case "textColor":
                            templateDetail.setTextColor(reader.nextString());
                            break;
                        case "texContent":
                            templateDetail.setTexContent(reader.nextString());
                            break;
                        case "redirectUrl":
                            templateDetail.setRedirectUrl(reader.nextString());
                            break;
                        case "redirectType":
                            templateDetail.setRedirectType(reader.nextInt());
                            break;
                        case "padding":
                            templateDetail.setPadding(reader.nextString());
                            break;
                        case "bgImageMd5":
                            templateDetail.setBgImageMd5(reader.nextString());
                            break;
                        case "buttonImage":
                            templateDetail.setButtonImage(reader.nextString());
                            break;
                        case "stylePic":
                            templateDetail.setStylePic(reader.nextString());
                            break;
                        case "giftAnimationType":
                            templateDetail.setGiftAnimationType(reader.nextInt());
                            break;
                        case "evaMp4Path":
                            templateDetail.setEvaMp4Path(reader.nextString());
                            break;
                        case "evaMp4Md5":
                            templateDetail.setEvaMp4Md5(reader.nextString());
                            break;
                        case "interActFirstPath":
                            templateDetail.setInterActFirstPath(reader.nextString());
                            break;
                        case "interActSecondPath":
                            templateDetail.setInterActSecondPath(reader.nextString());
                            break;
                        case "interActThirdPath":
                            templateDetail.setInterActThirdPath(reader.nextString());
                            break;
                        case "interActFourthPath":
                            templateDetail.setInterActFourthPath(reader.nextString());
                            break;
                        case "interActPlayTime":
                            templateDetail.setInterActPlayTime(reader.nextInt());
                            break;
                        // 复杂对象字段,还是用gson解析
                        case "gradientColor":
                            templateDetail.setGradientColor(LiveGsonUtils.sGson.fromJson(reader, new TypeToken<List<String>>() {
                            }.getType()));
                            break;
                        case "contentRules":
                            templateDetail.setContentRules(LiveGsonUtils.sGson.fromJson(reader,
                                    new TypeToken<List<LiveTemplateModel.TemplateDetail.FloatContentBean>>() {
                                    }.getType()));
                            break;
                        case "animation":
                            templateDetail.setAnimation(LiveGsonUtils.sGson.fromJson(reader, LiveTemplateModel.TemplateDetail.EnterAnimation.class));
                            break;
                        case "redPacket":
                            templateDetail.setRedPacket(LiveGsonUtils.sGson.fromJson(reader, LiveTemplateModel.TemplateDetail.RedPacket.class));
                            break;
                        default:
                            // 跳过未知字段
                            reader.skipValue();
                            break;
                    }
                }
                reader.endObject();

                // 将解析结果添加到模型中
                liveTemplateModel.mIdTemplateMap.put(templateId, templateDetail);
                // 记录下载顺序
                liveTemplateModel.addDownloadOrder(templateId);

            } catch (Exception e) {
                e.printStackTrace();
                // 如果解析单个模板出错，跳过该模板继续解析其他模板
                reader.skipValue();
                CommonLiveLogger.e(TAG, "解析模板 " + templateId + " 失败: " + e.getMessage());
            }
        }
        reader.endObject();

        return liveTemplateModel;
    }

    /**
     * gson解析全量模版接口，返回LiveTemplateModel对象
     */
    private static LiveTemplateModel convertDataStrToTemplateInfo(String dataStr) throws Exception {
        LiveTemplateModel liveTemplateModel = new LiveTemplateModel();

        JSONObject dataObj = new JSONObject(dataStr);
        Iterator<String> keys = dataObj.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            if (dataObj.get(key) instanceof JSONObject) {
                try {
                    String value = dataObj.get(key).toString();
                    LiveTemplateModel.TemplateDetail templateDetail = CommonRequestM.sGson.fromJson(value, LiveTemplateModel.TemplateDetail.class);

                    //记录数据
                    liveTemplateModel.mIdTemplateMap.put(key, templateDetail);
                    //记录数据顺序
                    liveTemplateModel.addDownloadOrder(key);

                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showDebugFailToast(e.getMessage());
                }
            }
        }

        return liveTemplateModel;
    }


    /**
     * 查询单个模板信息
     */
    public static void getTemplateDetail(long id, IDataCallBack<LiveTemplateModel> callBack) {
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put("id", String.valueOf(id));
        params.put("resolutionType", LiveHelper.getMp4SourceQuality() + "");

        baseGetRequest(
                LiveTemplateUrlConstants.getInstance().getSingleTemplateUrl(),
                params,
                callBack,
                content -> {
                    if (TextUtils.isEmpty(content)) {
                        return null;
                    }

                    LiveTemplateModel liveTemplateModel = new LiveTemplateModel();

                    JSONObject jsonObject = new JSONObject(content);
                    if (!jsonObject.has("ret") || jsonObject.optInt("ret") != 0) {
                        return null;
                    }

                    String data = jsonObject.optString("data");
                    JSONObject dataObj = new JSONObject(data);
                    String key = dataObj.optString("id");
                    String value = jsonObject.optString("data");
                    try {
                        LiveTemplateModel.TemplateDetail templateDetail = LiveGsonUtils.sGson.fromJson(value, LiveTemplateModel.TemplateDetail.class);

                        liveTemplateModel.mIdTemplateMap.put(key, templateDetail);

                        Logger.i(TAG, templateDetail + "");
                    } catch (Exception e) {
                        e.printStackTrace();
                        CustomToast.showDebugFailToast(e.getMessage());
                    }

                    return liveTemplateModel;
                }
        );
    }


    /**
     * 查询资源下载的配置策略信息
     */
    public static void getTemplateStrategyRemote(IDataCallBack<RemoteDownloadSettingInfo> callBack) {

        baseGetRequest(
                LiveTemplateUrlConstants.getInstance().getTemplateDownloadStrategyUrl(),
                null,
                callBack,
                content -> {

                    if (TextUtils.isEmpty(content)) return null;

                    JSONObject jsonObject = new JSONObject(content);
                    if (!jsonObject.has("ret") || jsonObject.optInt("ret") != 0)
                        return null;

                    if (jsonObject.has("data")) {

                        try {

                            RemoteDownloadSettingInfo res = sGson.fromJson(jsonObject.optString("data"),
                                    RemoteDownloadSettingInfo.class);

                            if (res != null) {
                                Logger.d("LiveTemplateDownload", "GetTemplateStrategyRemote, Res= " + res);
                            }
                            return res;

                        } catch (Exception e) {
                            e.printStackTrace();
                            CustomToast.showDebugFailToast(e.getMessage());
                        }

                    }

                    return null;
                }
        );
    }

    /**
     * 获取全量礼物模版信息
     *
     * @param bizType 业务类型
     */
    public static void getAllGiftInfoByCdnFile(int bizType, IDataCallBack<ArrayMap<Long, GiftInfoCombine.GiftInfo>> callBack) {
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put(ParamsConstantsInLive.GIFT_SHOW_TYPE, matchShowType(bizType));
        CdnFileRequestHelper.INSTANCE.requestGiftInfoStr(params, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String data) {
                CommonLiveLogger.d(TAG, "onSuccess: " + data);
                ArrayMap<Long, GiftInfoCombine.GiftInfo> arrayMap = new ArrayMap<>();
                MemoryMonitorUtils.ParseMonitor monitor = new MemoryMonitorUtils.ParseMonitor("解析礼物信息全量接口");
                monitor.startParsing();
                try (JsonReader reader = new JsonReader(new StringReader(data))) {
                    reader.setLenient(true); // 宽松模式
                    reader.beginObject();
                    while (reader.hasNext()) {
                        String giftId = reader.nextName();
                        GiftInfoCombine.GiftInfo giftInfo = sGson.fromJson(reader, GiftInfoCombine.GiftInfo.class);
                        try {
                            arrayMap.put(Long.valueOf(giftId), giftInfo);
                        } catch (Exception e) {
                            CommonLiveLogger.e(TAG, "解析礼物ID " + giftId + " 失败: " + e.getMessage());
                        }
                    }
                    reader.endObject();
                } catch (Exception e) {
                    e.printStackTrace();
                    CommonLiveLogger.e(TAG, "json 解析异常: " + e.getMessage());
                    LiveXdcsUtil.doXDCS("LiveCdnReqFail",  "文件解析异常:" + e);
                }
                monitor.endParsing();
                CommonLiveLogger.d(TAG, "gson解析: " + monitor.getReport());
                if (callBack != null) {
                    callBack.onSuccess(arrayMap);
                }
            }

            @Override
            public void onError(int code, String message) {
                CommonLiveLogger.e(TAG, "getAllGiftInfoByCdnFile error: " + code + ",message: " + message);
                if (callBack != null) {
                    callBack.onError(code, message);
                }
            }
        });
    }

    private static String matchShowType(int bizType) {
        switch (bizType) {
            case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO:
                return ParamsConstantsInLive.GIFT_SHOW_TYPE_VIDEO;
            case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC:
                return ParamsConstantsInLive.GIFT_SHOW_TYPE_HALL;
            case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE:
                return ParamsConstantsInLive.GIFT_SHOW_TYPE_COURSE_VIDEO;
            default:
                return ParamsConstantsInLive.GIFT_SHOW_TYPE_LIVE;
        }
    }


}
