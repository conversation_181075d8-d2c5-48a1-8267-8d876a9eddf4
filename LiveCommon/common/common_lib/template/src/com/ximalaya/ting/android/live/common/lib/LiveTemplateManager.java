package com.ximalaya.ting.android.live.common.lib;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.live.LiveH265SwitchSetting;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.download_control.XmLiveResDownloadSetting;
import com.ximalaya.ting.android.live.common.lib.download_control.api.ILoadDownloadSettingCallback;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.http.CommonRequestForTemplate;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.model.ChosenResDownloadInfo;
import com.ximalaya.ting.android.live.common.lib.model.LiveTemplateModel;
import com.ximalaya.ting.android.live.common.lib.util.LiveTemplateDownloadUtil;
import com.ximalaya.ting.android.live.common.lib.util.LiveTemplateTraceEvent;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.INetworkChangeListener;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 直播间模板管理器。模版资源包括弹幕、气泡、进场通知、头像挂件、飘屏、大动画、进场特效、表情、麦上表情、座位框、
 * 粉丝团个性化标签、礼物特效资源、麦位波等。
 * <p>
 * 主要职责：
 * 1、管理模版资源信息同步。
 * 2、管理部分模版资源下载，如礼物特效资源、头像框、气泡、进场特效等。
 * <p>
 * 具体参考文档：https://alidocs.dingtalk.com/i/team/Y7kmb5pMeJe3XLq2/docs/Y7kmb6Y70bMWmLq2?corpId=ding51f195092fd77474&iframeQuery=
 *
 * <AUTHOR>
 * @server 周乐乐
 * @wiki http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/tags/%E6%A8%A1%E7%89%88api%E8%AF%B4%E6%98%8E.md
 */
public class LiveTemplateManager implements ILiveTemplateManager, INetworkChangeListener {

    public static final String TAG = LiveTemplateManager.class.getSimpleName();

    /**
     * 模版同步请求频率，10 秒内最多请求同步一次
     */
    private static final long REQUEST_TEMPLATE_INTERVAL = 10 * 1000;

    /**
     * 服务端同步到的模版数据
     */
    private LiveTemplateModel mLiveTemplateData;

    private ArrayMap<Long, GiftInfoCombine.GiftInfo> mGiftInfoMapData = new ArrayMap<>();

    /**
     * 是否正在请求模版信息标识
     */
    private boolean mTemplateAllRequesting;
    /**
     * 上次拉取模板的时间
     */
    private long mLastRequestTemplateTime;

    public static final int MSG_HANDLE_TEMPLATE_MAP = 0x100;

    private final Handler mTempHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == MSG_HANDLE_TEMPLATE_MAP) {
                if (mLiveTemplateData != null && mLiveTemplateData.mIdTemplateMap != null) {
                    MyAsyncTask.execute(() -> handleResult(mLiveTemplateData));
                }
            }
        }
    };

    public void removeTempHandlerSyncMsg() {
        if (mTempHandler.hasMessages(MSG_HANDLE_TEMPLATE_MAP)) {
            mTempHandler.removeMessages(MSG_HANDLE_TEMPLATE_MAP);
            Logger.d(XmLiveResDownloadSetting.INSTANCE.getTAG(), "离开直播页面，取消预定的全量下载操作");
        }
    }

    /**
     * 模版管理器单例
     */
    private static volatile LiveTemplateManager sTemplateManager;

    /**
     * 记录网络状态信息
     */
    private NetworkType.NetWorkType mLastNetType;

    private LiveTemplateManager() {}

    public static LiveTemplateManager getInstance() {
        if (sTemplateManager == null) {
            synchronized (LiveTemplateManager.class) {
                if (sTemplateManager == null) {
                    sTemplateManager = new LiveTemplateManager();
                    NetworkType.addNetworkChangeListener(sTemplateManager);
                }
            }
        }
        return sTemplateManager;
    }

    /**
     * 预加载全量礼物信息
     * @param bizType 业务类型
     */
    public void preloadGiftInfoByCndFile(int bizType) {
        CommonRequestForTemplate.getAllGiftInfoByCdnFile(bizType, new IDataCallBack<ArrayMap<Long, GiftInfoCombine.GiftInfo>>() {
            @Override
            public void onSuccess(@Nullable ArrayMap<Long, GiftInfoCombine.GiftInfo> data) {
                mGiftInfoMapData = data;
                // code == 0 就是ok成功
                LiveTemplateTraceEvent.getGiftInfoByCdnReport(0, bizType);

            }

            @Override
            public void onError(int code, String message) {
                LiveTemplateTraceEvent.getGiftInfoByCdnReport(code, bizType);
                LiveXdcsUtil.doXDCS("LiveCdnReqFail",
                        "getAllGiftInfoByCdnFile Fail! ErrCode=" + code + ", ErrMsg=" + message);
            }
        });
    }

    /**
     * 请求指定模版资源结果异步回调
     */
    public interface IRequestTemplateDetailCallback {
        /**
         * 成功回调
         *
         * @param templateDetail 模版信息
         */
        void onSuccess(LiveTemplateModel.TemplateDetail templateDetail);

        /**
         * 失败回调
         */
        void onError(int code, String message);
    }

    @Override
    public LiveTemplateModel.TemplateDetail.EnterAnimation getEnterAnimTemplateById(String id) {
        LiveTemplateModel.TemplateDetail template = getTemplateById(id);
        if (template != null) {
            return template.getAnimation();
        }

        return null;
    }

    /**
     * 从本地缓存中查询指定模版资源信息
     *
     * @param id 模版资源id
     * @return 模版资源信息
     */
    @Nullable
    public LiveTemplateModel.TemplateDetail getTemplateById(String id) {
        if (mLiveTemplateData == null || mLiveTemplateData.mIdTemplateMap == null ||
                mLiveTemplateData.mIdTemplateMap.isEmpty()
        ) {
            sync();
            return null;
        }

        return mLiveTemplateData.getTemplateById(id);
    }

    /**
     * 根据模版id获取模版数据结构对象
     *
     * @param id                       templateId
     * @param liveTemplateByIdCallback 异步请求单模版接口
     */
    public void getTemplateByIdCallback(long id, ILiveTemplateByIdCallback liveTemplateByIdCallback) {
        String strTemplateId = String.valueOf(id);

        if (mLiveTemplateData == null
                || mLiveTemplateData.mIdTemplateMap == null
                || mLiveTemplateData.mIdTemplateMap.isEmpty()
                || mLiveTemplateData.getTemplateById(strTemplateId) == null
        ) {
            requestTemplateDetailById(id, new IRequestTemplateDetailCallback() {
                @Override
                public void onSuccess(LiveTemplateModel.TemplateDetail templateDetail) {
                    if (liveTemplateByIdCallback != null) {
                        liveTemplateByIdCallback.onSuccess(templateDetail);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (liveTemplateByIdCallback != null) {
                        liveTemplateByIdCallback.onFailed("单模版接口请求错误code:" + code + ",message:" + message);
                    }

                }
            });
        } else {
            if (liveTemplateByIdCallback != null) {
                liveTemplateByIdCallback.onSuccess(mLiveTemplateData.getTemplateById(strTemplateId));
            }
        }
    }

    /**
     * 向服务端请求指定模版资源信息
     *
     * @param templateId 模版id
     * @param callback   请求指定模版资源结果异步回调
     */
    public void requestTemplateDetailById(long templateId,
                                          IRequestTemplateDetailCallback callback) {
        CommonRequestForTemplate.getTemplateDetail(templateId, new IDataCallBack<LiveTemplateModel>() {
            @Override
            public void onSuccess(@Nullable LiveTemplateModel object) {
                log("getSingleTemplate onSuccess: " + object);

                if (object == null || object.mIdTemplateMap == null) {
                    callback.onError(-1, "LiveTemplateModel == null");
                    return;
                }

                Map<String, LiveTemplateModel.TemplateDetail> templateMap = object.mIdTemplateMap;
                LiveTemplateModel.TemplateDetail templateDetail = templateMap.get(String.valueOf(templateId));

                if (mLiveTemplateData == null) {
                    mLiveTemplateData = new LiveTemplateModel();
                }
                if (null != templateDetail) {
                    mLiveTemplateData.mIdTemplateMap.put(String.valueOf(templateId), templateDetail);
                }
                callback.onSuccess(templateDetail);

            }

            @Override
            public void onError(int code, String message) {
                log("getSingleTemplate onError: " + code + ", " + message);

                if (callback != null) {
                    callback.onError(code, message);
                }
            }
        });
    }


    @Override
    public LiveTemplateModel.TemplateDetail getTemplateById(long id) {
        return getTemplateById(String.valueOf(id));
    }

    @Override
    public LiveTemplateModel.TemplateDetail getTemplateById(int id) {
        return getTemplateById(String.valueOf(id));
    }

    /**
     * 查询得到该动画资源的下载选择
     *
     * @param isSupportH265 设备是否支持H265
     * @return 动画资源的下载选择
     */
    private ChosenResDownloadInfo chooseDownloadPath(boolean isSupportH265, LiveTemplateModel.TemplateDetail templateDetail) {

        if (fileExist(templateDetail)) {
            return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.FILE_EXIST, null, null);
        }
        if (LiveSettingManager.getEvaSwitchConfig()) {
            if (!TextUtils.isEmpty(templateDetail.getEvaMp4Path()) && !TextUtils.isEmpty(templateDetail.getEvaMp4Md5())) {
                return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_EVA, templateDetail.getEvaMp4Path(), templateDetail.getEvaMp4Md5());
            }
        }

        if (isSupportH265 && (!TextUtils.isEmpty(templateDetail.getH265Mp4Path()) && !TextUtils.isEmpty(templateDetail.getH265Mp4Md5()))) {
            return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_H265_MP4, templateDetail.getH265Mp4Path(), templateDetail.getH265Mp4Md5());

        } else if (!TextUtils.isEmpty(templateDetail.getMp4Path()) && !TextUtils.isEmpty(templateDetail.getMp4Md5())) {
            return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_H264_MP4, templateDetail.getMp4Path(), templateDetail.getMp4Md5());

        } else if (!TextUtils.isEmpty(templateDetail.getSvgPath()) && !TextUtils.isEmpty(templateDetail.getSvgMd5())) {
            return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_SVGA, templateDetail.getSvgPath(), templateDetail.getSvgMd5());

        } else if (!TextUtils.isEmpty(templateDetail.getBgImagePath()) && !TextUtils.isEmpty(templateDetail.getBgImageMd5())) {
            int type = templateDetail.dynamicType(templateDetail.getBgImagePath());
            if (type == GiftInfoCombine.GiftInfo.IGiftDynamicType.GIFT_DYNAMIC_TYPE_MP4) {
                return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_H264_MP4, templateDetail.getBgImagePath(), templateDetail.getBgImageMd5());
            } else if (type == GiftInfoCombine.GiftInfo.IGiftDynamicType.GIFT_DYNAMIC_TYPE_SVGA) {
                return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_SVGA, templateDetail.getBgImagePath(), templateDetail.getBgImageMd5());
            }
        }
        // 兼容type=7的数据结构,如果外层h265仍然不存在，则取animation数据结构中的path
        if (LiveTemplateModel.TemplateType.TYPE_ENTER_ANIM.equals(templateDetail.getType())) {
            LiveTemplateModel.TemplateDetail.EnterAnimation animation = templateDetail.getAnimation();
            if (animation != null) {
                if (!TextUtils.isEmpty(animation.path) && !TextUtils.isEmpty(animation.md5)) {
                    return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.CHOOSE_H264_MP4, animation.path, animation.md5);
                }
            }
        }

        return new ChosenResDownloadInfo(ChosenResDownloadInfo.DownloadChoiceCode.ERROR, null, null);

    }

    /**
     * 文件是否已经存在
     *
     * @param templateDetail LiveTemplateModel.TemplateDetail
     * @return exist: true else: false
     */
    private boolean fileExist(LiveTemplateModel.TemplateDetail templateDetail) {
        boolean evaFileExist = false;
        if (LiveSettingManager.getEvaSwitchConfig()) {
            evaFileExist = LiveTemplateDownloadUtil.checkDownloadedFileExistByUrl(templateDetail.getEvaMp4Path(), templateDetail.getId());
        }
        return LiveTemplateDownloadUtil.checkDownloadedFileExistByUrl(templateDetail.getMp4Path(), templateDetail.getId())
                || LiveTemplateDownloadUtil.checkDownloadedFileExistByUrl(templateDetail.getH265Mp4Path(), templateDetail.getId())
                || LiveTemplateDownloadUtil.checkDownloadedFileExistByUrl(templateDetail.getSvgPath(), templateDetail.getId())
                || evaFileExist;
    }

    @Override
    public String getLocalPathByUrl(Context context, String url, long templateId) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }

        File localFile = LiveTemplateDownloadUtil.getDownloadedFileByUrl(url, templateId);
        if (localFile == null || localFile.length() <= 0) {
            return null;
        }

        localFile.setLastModified(System.currentTimeMillis());
        return localFile.getPath();
    }

    /**
     * 向服务端查询模版资源，并在wifi下预加载指定模版资源，如礼物特效资源、贵族进场动画资源、进场特效资源
     *
     * @param bizType 互动娱乐子业务类型
     */
    public void fetchTemplateInfoAndDownloadSpecificResourceInWifi(@BaseScrollConstant.LiveRoomBizType int bizType) {
        if (mTemplateAllRequesting) {
            return;
        }

        mTemplateAllRequesting = true;
        mLastRequestTemplateTime = System.currentTimeMillis();

        fetchTemplateDownloadAllInfo(bizType, new IDataCallBack<LiveTemplateModel>() {
            @Override
            public void onSuccess(@Nullable LiveTemplateModel object) {
                log("getAllTemplate onSuccess: " + object);
                mTemplateAllRequesting = false;

                if (object == null || object.mIdTemplateMap == null) {
                    return;
                }

                if (mLiveTemplateData == null) {
                    mLiveTemplateData = object;
                } else {
                    // 增量更新mLiveTemplateData的值
                    mLiveTemplateData.mIdTemplateMap.putAll(object.mIdTemplateMap);
                    mLiveTemplateData.addDownloadOrderList(object.mDownloadOrderList);
                }

                if (!mTempHandler.hasMessages(MSG_HANDLE_TEMPLATE_MAP)) {
                    mTempHandler.sendEmptyMessageDelayed(MSG_HANDLE_TEMPLATE_MAP,
                            XmLiveResDownloadSetting.INSTANCE.getEnterPageStartrDownloadDelay() * 1000L);

                    Logger.d(XmLiveResDownloadSetting.INSTANCE.getTAG(), "进入直播页面，延迟 "
                            + XmLiveResDownloadSetting.INSTANCE.getEnterPageStartrDownloadDelay() + "秒 开始直播全量下载！");
                }
            }

            @Override
            public void onError(int code, String message) {
                log("getAllTemplate onError: " + code + ", " + message);
                mTemplateAllRequesting = false;
            }
        });
    }

    public void getSingleTemplateById(long id, IDataCallBack<LiveTemplateModel> callBack) {
        CommonRequestForTemplate.getTemplateDetail(id, new IDataCallBack<LiveTemplateModel>() {
            @Override
            public void onSuccess(@Nullable LiveTemplateModel object) {
                log("getSingleTemplate onSuccess: " + object);

                if (object == null || object.mIdTemplateMap == null) {
                    callBack.onError(-1, "获取模板数据为空");
                    return;
                }

                Map<String, LiveTemplateModel.TemplateDetail> templateMap = object.mIdTemplateMap;
                LiveTemplateModel.TemplateDetail templateDetail = templateMap.get(String.valueOf(id));

                if (mLiveTemplateData == null) {
                    mLiveTemplateData = new LiveTemplateModel();
                }
                if (null != templateDetail) {
                    mLiveTemplateData.mIdTemplateMap.put(String.valueOf(id), templateDetail);
                }

                if (callBack != null) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                log("getSingleTemplate onError: " + code + ", " + message);

                if (callBack != null) {
                    callBack.onError(code, message);
                }
            }
        });
    }

    /**
     * 处理同步好的模板数据，包括：选择最佳资源（yyeva > h265 mp4 > h264 mp4 > svga），并在 wifi 下执行下载操作
     *
     * @param templateData 模板数据
     */
    private synchronized void handleResult(LiveTemplateModel templateData) {
        Map<String, LiveTemplateModel.TemplateDetail> templateMap = templateData.mIdTemplateMap;
        List<String> idOrderList = templateData.mDownloadOrderList;

        Logger.d(XmLiveResDownloadSetting.INSTANCE.getTAG(),
                "mIdTemplateMap size: " + templateMap.size() + ", mDownloadOrderList size: " + idOrderList.size());

        // 非核心资源map
        List<LiveTemplateModel.TemplateDetail> unCoreList = new ArrayList<>();

        //用户设备是否支持使用H265解码
        boolean isSupportH265 = LiveH265SwitchSetting.isUseH265LivePlay();

        for (String key : idOrderList) {
            LiveTemplateModel.TemplateDetail templateDetail = templateMap.get(key);
            if (templateDetail == null) {
                continue;
            }

            if (!templateDetail.isCore()) {
                //非核心资源，延迟处理
                unCoreList.add(templateDetail);
                continue;
            }

            boolean preload = NetworkType.isConnectToWifi(MainApplication.getMyApplicationContext());
            downloadAnimationResource(key, templateDetail, preload, isSupportH265);
        }

        log("非核心资源有" + unCoreList.size() + "条， 核心资源有" + (templateMap.size() - unCoreList.size()) + "条");

        //配置中心，是否全量下载资源
        boolean liveResourceFullDownload = LiveSettingManager.getLiveResourceFullDownload();

        log("全量下载开关：" + liveResourceFullDownload);

        if (!unCoreList.isEmpty()) {
            for (LiveTemplateModel.TemplateDetail templateDetail : unCoreList) {
                if (templateDetail == null) {
                    continue;
                }
                boolean preload = NetworkType.isConnectToWifi(MainApplication.getMyApplicationContext()) && liveResourceFullDownload;
                downloadAnimationResource(templateDetail.getId() + "", templateDetail, preload, isSupportH265);
            }
        }
    }

    /**
     * 下载动画资源
     *
     * @param key            模板id
     * @param templateDetail 模板详情
     * @param preload        预加载
     * @param isSupportH265  设备是否支持H265
     */
    private void downloadAnimationResource(String key,
                                           LiveTemplateModel.TemplateDetail templateDetail,
                                           boolean preload,
                                           boolean isSupportH265) {
        log("templateDetailType" + templateDetail.getType() + ", preload = " + preload);

        String type = templateDetail.getType();
        if (LiveTemplateModel.TemplateType.TYPE_GIFT_ANIMATION.equals(type)
                || LiveTemplateModel.TemplateType.TYPE_ENTER_ANIM.equals(type)
                || LiveTemplateModel.TemplateType.TYPE_ANIMATION_SVG_MP4.equals(type)
                || LiveTemplateModel.TemplateType.TYPE_MIC_WAVE.equals(type)) {
            // 下载大礼物动画
            download265First(key, templateDetail, preload, isSupportH265);
        } else if (LiveTemplateModel.TemplateType.TYPE_ENTER_WELCOME.equals(type)) {
            // 类型为3的svga资源存在在bgImagePath字段上，后续服务端统一字段维护后，这段逻辑可以交给上面if条件统一处理
            downloadBgImagePath(templateDetail, key, preload);
        }
    }

    private void downloadBgImagePath(LiveTemplateModel.TemplateDetail templateDetail, String key, boolean preload) {
        try {
            if (!TextUtils.isEmpty(templateDetail.getBgImagePath())) {
                log("get TYPE_ENTER_ANIM onSuccess, save to map:( " + key + ", " + templateDetail.getBgImagePath() + ")");
                TemplateDownloadManager.getInstance().downloadAnimResIfNotExist(
                        MainApplication.getMyApplicationContext(),
                        templateDetail.getBgImagePath(),
                        preload,
                        null,
                        templateDetail.getId(),
                        templateDetail.getBgImageMd5()
                );
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void download265First(String key,
                                  LiveTemplateModel.TemplateDetail templateDetail,
                                  boolean preload,
                                  boolean isSupportH265) {
        ChosenResDownloadInfo chosenResDownloadInfo = chooseDownloadPath(isSupportH265, templateDetail);
        if (chosenResDownloadInfo.code == ChosenResDownloadInfo.DownloadChoiceCode.FILE_EXIST) {

            log("get TYPE_GIFT_ANIMATION Downloaded, File already exist! key=" + key);

        } else if (chosenResDownloadInfo.code == ChosenResDownloadInfo.DownloadChoiceCode.ERROR) {

            log("get TYPE_GIFT_ANIMATION Download Fail, Info Wrong! TemplateDetail: ");

        } else {
            //进行下载操作
            TemplateDownloadManager.getInstance().downloadAnimResIfNotExist(MainApplication.getMyApplicationContext(), chosenResDownloadInfo.downloadUrl, preload,
                    TemplateDownloadImpl.getInstance(
                            templateDetail.getId(),
                            templateDetail.getGiftAnimationType(),
                            templateDetail.isCore()),
                    templateDetail.getId(),
                    LiveTemplateDownloadUtil.getMd5(templateDetail.getId(), chosenResDownloadInfo.downloadUrl)
            );
        }
    }

    /**
     * 仅向服务端查询模版信息，更新本地缓存模版信息，不下载指定资源
     *
     * @param bizType 互动娱乐子业务类型
     */
    public void fetchTemplateInfo(@BaseScrollConstant.LiveRoomBizType int bizType) {
        syncInternal(bizType);
    }

    /**
     * 根据模板 id 查询动态气泡的信息
     *
     * @param templateId 模板 id
     * @return 动态气泡信息
     */
    @Nullable
    public LiveTemplateModel.TemplateDetail getDynamicBubbleInfoById(int templateId) {
        LiveTemplateModel.TemplateDetail result = null;
        if (mLiveTemplateData != null && mLiveTemplateData.mIdTemplateMap != null) {
            result = mLiveTemplateData.mIdTemplateMap.get(String.valueOf(templateId));
            if (result != null && TextUtils.isEmpty(result.getBgImagePath())) {
                // 气泡底图为空，无须加载右侧动图，直接返回 null
                result = null;
            }
        }

        if (result == null) {
            sync();
        }

        return result;
    }

    /**
     * 根据模版id查询消息气泡图片链接/头像挂件图片链接
     *
     * @param templateId 模版id
     * @return 气泡图片链接/头像挂件图片链接
     */
    public String getBubbleOrAvatarDecorationUrlById(int templateId) {
        String url = "";
        if (mLiveTemplateData != null && mLiveTemplateData.mIdTemplateMap != null) {
            LiveTemplateModel.TemplateDetail templateDetail = mLiveTemplateData.mIdTemplateMap.get(String.valueOf(templateId));
            if (templateDetail != null) {
                url = templateDetail.getBgImagePath();
            }
        }

        if (!TextUtils.isEmpty(url)) {
            return url;
        }

        sync();

        return null;
    }

    public static void log(String msg) {
        Logger.i(TAG, msg);
    }

    /**
     * 根据模版 id 获取座位框 url
     *
     * @param id 模版id
     * @return 座位框 url
     */
    public String getSeatDecorateUrlBySeatId(String id) {
        LiveTemplateModel.TemplateDetail templateDetail = getTemplateById(id);
        if (null == templateDetail) {
            sync();
            return null;
        }

        String decorateUrl = templateDetail.getBgImagePath();

        if (TextUtils.isEmpty(decorateUrl)) {
            sync();
            return null;
        }

        return decorateUrl;
    }

    /**
     * 限制接口请求频率，避免请求过多
     */
    private void sync() {
        if (System.currentTimeMillis() - mLastRequestTemplateTime > REQUEST_TEMPLATE_INTERVAL) {
            syncInternal(LiveRecordInfoManager.getInstance().getLiveRoomType());
        }
    }

    /**
     * 仅查询模版信息，不做下载
     */
    private void syncInternal(@BaseScrollConstant.LiveRoomBizType int bizType) {
        if (mTemplateAllRequesting) {
            return;
        }

        mTemplateAllRequesting = true;
        mLastRequestTemplateTime = System.currentTimeMillis();

        fetchTemplateDownloadAllInfo(bizType, new IDataCallBack<LiveTemplateModel>() {
            @Override
            public void onSuccess(@Nullable LiveTemplateModel object) {
                log("syncInternal onSuccess: " + object);
                mTemplateAllRequesting = false;
                if (mLiveTemplateData == null) {
                    mLiveTemplateData = object;
                    return;
                }
                // 增量更新mLiveTemplateData的值
                if (object != null) {
                    mLiveTemplateData.mIdTemplateMap.putAll(object.mIdTemplateMap);
                    MyAsyncTask.execute(new Runnable() {
                        @Override
                        public void run() {
                            mLiveTemplateData.addDownloadOrderList(object.mDownloadOrderList);
                        }
                    });

                }
            }

            @Override
            public void onError(int code, String message) {
                log("syncInternal onError: " + code + ", " + message);
                mTemplateAllRequesting = false;
            }
        });
    }

    @Override
    public void onNetworkChanged(Context context, Intent intent, NetworkType.NetWorkType netWorkType, int currentOperator) {
        if (mLastNetType == netWorkType) {
            //过滤重复
            return;
        }
        mLastNetType = netWorkType;

        if (NetworkType.isConnectMOBILE(context)) {
            //暂停下载模板资源
            TemplateDownloadManager.getInstance().pauseDownloadTask();
        }
    }


    /**
     * 拉取资源清单列表之前，进行一次下载配置更新
     */
    private void fetchTemplateDownloadAllInfo(int bizType, @NonNull IDataCallBack<LiveTemplateModel> callBack) {

        //直播资源下载策略同步
        XmLiveResDownloadSetting.INSTANCE.refreshLiveDownloadSetting(new ILoadDownloadSettingCallback() {
            @Override
            public void onGetResult(boolean isSuccess, String msg) {
                // 无论成功失败，都需要进行后续的操作
                // 此处进行记录即可
                Logger.d(TAG, isSuccess ? "获取下载控制策略成功！" : "获取下载控制策略失败！" + msg);

                // 经过AB验证后，全部转向cdn文件下放方式
                loadAllTemplateByCdnStyle(bizType, callBack);
            }
        });

    }

    /**
     * cdn模式请求模板信息，失败后回退 http 模式请求
     *
     * @param bizType  分辨率类型
     * @param callBack 请求结果回调
     */
    private void loadAllTemplateByCdnStyle(int bizType, @NonNull IDataCallBack<LiveTemplateModel> callBack) {
        CommonRequestForTemplate.getAllTemplateByCdnFile(bizType, new IDataCallBack<LiveTemplateModel>() {
            @Override
            public void onSuccess(@Nullable LiveTemplateModel data) {
                callBack.onSuccess(data);
            }

            @Override
            public void onError(int code, String message) {
                Logger.d(TAG, "Cdn获取下载控制策略失败！回退Http请求方式...");

                CommonRequestForTemplate.getAllTemplate(bizType, callBack);

                //直播模版技术排查埋点
                LiveTemplateTraceEvent.getTemplateByCdnReport(code);
            }
        });
    }


}
