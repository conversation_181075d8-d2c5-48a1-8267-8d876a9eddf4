package com.ximalaya.ting.android.live.common.lib;

import android.annotation.SuppressLint;

import androidx.collection.ArrayMap;

import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Objects;

/**
 * 礼物数据比较器
 * 用于比较 LiveTemplateManager 的 mGiftInfoMapData 和 BaseGiftLoader 的 mGiftInfoMap 数据一致性
 *
 * <AUTHOR>
 */
public class GiftDataComparator {

    private static final String TAG = "GiftDataComparator";


    /**
     * 执行详细的比较操作
     */
    public static ComparisonResult performComparison(ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData,
                                                     ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftData) {
        ComparisonResult.Builder resultBuilder = new ComparisonResult.Builder();
        resultBuilder.setSourceData(liveTemplateData, baseGiftData);

        // 1. 比较 size 是否一致
        boolean sizeConsistent = compareSizes(liveTemplateData, baseGiftData, resultBuilder);
        resultBuilder.setSizeConsistent(sizeConsistent);

        // 2. 比较字段（key）是否一致
        boolean keysConsistent = compareKeys(liveTemplateData, baseGiftData, resultBuilder);
        resultBuilder.setKeysConsistent(keysConsistent);

        // 3. 比较字段内容是否一致
        boolean contentConsistent = compareContent(liveTemplateData, baseGiftData, resultBuilder);
        resultBuilder.setContentConsistent(contentConsistent);

        boolean overallConsistent = sizeConsistent && keysConsistent && contentConsistent;
        resultBuilder.setOverallConsistent(overallConsistent);

        return resultBuilder.build();
    }

    /**
     * 比较两个 Map 的大小
     */
    private static boolean compareSizes(ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData,
                                       ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftData,
                                       ComparisonResult.Builder resultBuilder) {
        int liveSize = liveTemplateData.size();
        int baseSize = baseGiftData.size();

        if (liveSize != baseSize) {
            String message = String.format("Size difference: LiveTemplateManager=%d, BaseGiftLoader=%d", liveSize, baseSize);
            resultBuilder.addSizeDifference(message);
            Logger.w(TAG, message);
            return false;
        }

        Logger.d(TAG, "Size comparison: Both maps have " + liveSize + " items");
        return true;
    }

    /**
     * 比较两个 Map 的 key 集合
     */
    private static boolean compareKeys(ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData,
                                      ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftData,
                                      ComparisonResult.Builder resultBuilder) {
        boolean keysConsistent = true;

        // 检查 LiveTemplateManager 中存在但 BaseGiftLoader 中不存在的 key
        for (Long key : liveTemplateData.keySet()) {
            if (!baseGiftData.containsKey(key)) {
                String message = String.format("Key %d exists in LiveTemplateManager but not in BaseGiftLoader", key);
                resultBuilder.addKeyDifference(message);
                Logger.w(TAG, message);
                keysConsistent = false;
            }
        }

        // 检查 BaseGiftLoader 中存在但 LiveTemplateManager 中不存在的 key
        for (Long key : baseGiftData.keySet()) {
            if (!liveTemplateData.containsKey(key)) {
                String message = String.format("Key %d exists in BaseGiftLoader but not in LiveTemplateManager", key);
                resultBuilder.addKeyDifference(message);
                Logger.w(TAG, message);
                keysConsistent = false;
            }
        }

        if (keysConsistent) {
            Logger.d(TAG, "Key comparison: All keys are consistent");
        }

        return keysConsistent;
    }

    /**
     * 比较两个 Map 中相同 key 对应的内容
     */
    private static boolean compareContent(ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData,
                                         ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftData,
                                         ComparisonResult.Builder resultBuilder) {
        boolean contentConsistent = true;

        // 只比较两个 Map 中都存在的 key
        for (Long key : liveTemplateData.keySet()) {
            if (baseGiftData.containsKey(key)) {
                GiftInfoCombine.GiftInfo liveInfo = liveTemplateData.get(key);
                GiftInfoCombine.GiftInfo baseInfo = baseGiftData.get(key);

                String difference = compareGiftInfo(key, liveInfo, baseInfo);
                if (difference != null) {
                    resultBuilder.addContentDifference(difference);
                    Logger.w(TAG, difference);
                    contentConsistent = false;
                }
            }
        }

        if (contentConsistent) {
            Logger.d(TAG, "Content comparison: All gift info content is consistent");
        }

        return contentConsistent;
    }

    /**
     * 比较两个 GiftInfo 对象的内容
     */
    private static String compareGiftInfo(Long giftId, GiftInfoCombine.GiftInfo liveInfo, GiftInfoCombine.GiftInfo baseInfo) {
        if (liveInfo == null && baseInfo == null) {
            return null;
        }

        if (liveInfo == null) {
            return String.format("Gift %d: LiveTemplateManager=null, BaseGiftLoader=exists", giftId);
        }

        if (baseInfo == null) {
            return String.format("Gift %d: LiveTemplateManager=exists, BaseGiftLoader=null", giftId);
        }

        StringBuilder differences = new StringBuilder();

        // 比较基础字段
        compareBasicFields(giftId, liveInfo, baseInfo, differences);

        // 比较路径字段
        comparePathFields(giftId, liveInfo, baseInfo, differences);

        // 比较类型字段
        compareTypeFields(giftId, liveInfo, baseInfo, differences);

        // 比较状态字段
        compareStatusFields(giftId, liveInfo, baseInfo, differences);

        // 比较其他字段
        compareOtherFields(giftId, liveInfo, baseInfo, differences);

        return differences.length() > 0 ? differences.toString() : null;
    }

    /**
     * 比较基础字段
     */
    private static void compareBasicFields(Long giftId, GiftInfoCombine.GiftInfo liveInfo, GiftInfoCombine.GiftInfo baseInfo, StringBuilder differences) {
        if (liveInfo.id != baseInfo.id) {
            differences.append(String.format("Gift %d: id difference (%d vs %d)\n", giftId, liveInfo.id, baseInfo.id));
        }

        if (!Objects.equals(liveInfo.name, baseInfo.name)) {
            differences.append(String.format("Gift %d: name difference ('%s' vs '%s')\n", giftId, liveInfo.name, baseInfo.name));
        }

        if (Double.compare(liveInfo.xiDiamondWorth, baseInfo.xiDiamondWorth) != 0) {
            differences.append(String.format("Gift %d: xiDiamondWorth difference (%.2f vs %.2f)\n", giftId, liveInfo.xiDiamondWorth, baseInfo.xiDiamondWorth));
        }
    }

    /**
     * 比较路径字段
     */
    private static void comparePathFields(Long giftId, GiftInfoCombine.GiftInfo liveInfo, GiftInfoCombine.GiftInfo baseInfo, StringBuilder differences) {
        if (!Objects.equals(liveInfo.coverPath, baseInfo.coverPath)) {
            differences.append(String.format("Gift %d: coverPath difference ('%s' vs '%s')\n", giftId, liveInfo.coverPath, baseInfo.coverPath));
        }

        if (!Objects.equals(liveInfo.webpCoverPath, baseInfo.webpCoverPath)) {
            differences.append(String.format("Gift %d: webpCoverPath difference ('%s' vs '%s')\n", giftId, liveInfo.webpCoverPath, baseInfo.webpCoverPath));
        }
    }

    /**
     * 比较类型字段
     */
    private static void compareTypeFields(Long giftId, GiftInfoCombine.GiftInfo liveInfo, GiftInfoCombine.GiftInfo baseInfo, StringBuilder differences) {
        if (liveInfo.dynamicType != baseInfo.dynamicType) {
            differences.append(String.format("Gift %d: dynamicType difference (%d vs %d)\n", giftId, liveInfo.dynamicType, baseInfo.dynamicType));
        }

        if (liveInfo.giftType != baseInfo.giftType) {
            differences.append(String.format("Gift %d: giftType difference (%d vs %d)\n", giftId, liveInfo.giftType, baseInfo.giftType));
        }
    }

    /**
     * 比较状态字段
     */
    private static void compareStatusFields(Long giftId, GiftInfoCombine.GiftInfo liveInfo, GiftInfoCombine.GiftInfo baseInfo, StringBuilder differences) {
        if (liveInfo.isErase != baseInfo.isErase) {
            differences.append(String.format("Gift %d: isErase difference (%b vs %b)\n", giftId, liveInfo.isErase, baseInfo.isErase));
        }

        if (liveInfo.isConsecutive != baseInfo.isConsecutive) {
            differences.append(String.format("Gift %d: isConsecutive difference (%b vs %b)\n", giftId, liveInfo.isConsecutive, baseInfo.isConsecutive));
        }

    }

    /**
     * 比较其他字段
     */
    private static void compareOtherFields(Long giftId, GiftInfoCombine.GiftInfo liveInfo, GiftInfoCombine.GiftInfo baseInfo, StringBuilder differences) {
        if (liveInfo.animationId != baseInfo.animationId) {
            differences.append(String.format("Gift %d: animationId difference (%d vs %d)\n", giftId, liveInfo.animationId, baseInfo.animationId));
        }

        if (liveInfo.extInfo == null || baseInfo.extInfo == null) {
            if (liveInfo.extInfo != null || baseInfo.extInfo != null) {
                differences.append(String.format("Gift %d: extInfo difference (%d vs %d)\n", giftId, liveInfo.extInfo, baseInfo.extInfo));
            }
        }

        if (liveInfo.extInfo != null && baseInfo.extInfo != null) {
            if (!liveInfo.extInfo.getData().equals(baseInfo.extInfo.getData()) || !liveInfo.extInfo.getType().equals(baseInfo.extInfo.getType())) {
                differences.append(String.format("Gift %d: extInfo difference (%d vs %d)\n", giftId, liveInfo.extInfo, baseInfo.extInfo));
            }
        }
    }

    /**
     * 比较结果类
     */
    public static class ComparisonResult {
        private final boolean overallConsistent;
        private final boolean sizeConsistent;
        private final boolean keysConsistent;
        private final boolean contentConsistent;
        
        private final String sizeDifferences;
        private final String keyDifferences;
        private final String contentDifferences;
        
        private final ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData;
        private final ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftLoaderData;

        private ComparisonResult(Builder builder) {
            this.overallConsistent = builder.overallConsistent;
            this.sizeConsistent = builder.sizeConsistent;
            this.keysConsistent = builder.keysConsistent;
            this.contentConsistent = builder.contentConsistent;
            this.sizeDifferences = builder.sizeDifferences.toString();
            this.keyDifferences = builder.keyDifferences.toString();
            this.contentDifferences = builder.contentDifferences.toString();
            this.liveTemplateData = builder.liveTemplateData;
            this.baseGiftLoaderData = builder.baseGiftLoaderData;
        }

        public boolean isOverallConsistent() {
            return overallConsistent;
        }

        public boolean isSizeConsistent() {
            return sizeConsistent;
        }

        public boolean isKeysConsistent() {
            return keysConsistent;
        }

        public boolean isContentConsistent() {
            return contentConsistent;
        }

        public String getSizeDifferences() {
            return sizeDifferences;
        }

        public String getKeyDifferences() {
            return keyDifferences;
        }

        public String getContentDifferences() {
            return contentDifferences;
        }

        public String getAllDifferences() {
            StringBuilder all = new StringBuilder();
            if (!sizeDifferences.isEmpty()) {
                all.append("Size Differences:\n").append(sizeDifferences).append("\n");
            }
            if (!keyDifferences.isEmpty()) {
                all.append("Key Differences:\n").append(keyDifferences).append("\n");
            }
            if (!contentDifferences.isEmpty()) {
                all.append("Content Differences:\n").append(contentDifferences).append("\n");
            }
            return all.toString();
        }

        public ArrayMap<Long, GiftInfoCombine.GiftInfo> getLiveTemplateData() {
            return liveTemplateData;
        }

        public ArrayMap<Long, GiftInfoCombine.GiftInfo> getBaseGiftLoaderData() {
            return baseGiftLoaderData;
        }

        @Override
        public String toString() {
            return String.format("ComparisonResult{overallConsistent=%b, sizeConsistent=%b, keysConsistent=%b, contentConsistent=%b}",
                    overallConsistent, sizeConsistent, keysConsistent, contentConsistent);
        }

        /**
         * 构建器类
         */
        static class Builder {
            private boolean overallConsistent;
            private boolean sizeConsistent;
            private boolean keysConsistent;
            private boolean contentConsistent;
            
            private final StringBuilder sizeDifferences = new StringBuilder();
            private final StringBuilder keyDifferences = new StringBuilder();
            private final StringBuilder contentDifferences = new StringBuilder();
            
            private ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData;
            private ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftLoaderData;

            Builder setOverallConsistent(boolean overallConsistent) {
                this.overallConsistent = overallConsistent;
                return this;
            }

            Builder setSizeConsistent(boolean sizeConsistent) {
                this.sizeConsistent = sizeConsistent;
                return this;
            }

            Builder setKeysConsistent(boolean keysConsistent) {
                this.keysConsistent = keysConsistent;
                return this;
            }

            Builder setContentConsistent(boolean contentConsistent) {
                this.contentConsistent = contentConsistent;
                return this;
            }

            Builder addSizeDifference(String difference) {
                sizeDifferences.append(difference).append("\n");
                return this;
            }

            Builder addKeyDifference(String difference) {
                keyDifferences.append(difference).append("\n");
                return this;
            }

            Builder addContentDifference(String difference) {
                contentDifferences.append(difference).append("\n");
                return this;
            }

            Builder setSourceData(ArrayMap<Long, GiftInfoCombine.GiftInfo> liveTemplateData,
                                ArrayMap<Long, GiftInfoCombine.GiftInfo> baseGiftLoaderData) {
                this.liveTemplateData = liveTemplateData;
                this.baseGiftLoaderData = baseGiftLoaderData;
                return this;
            }

            ComparisonResult build() {
                return new ComparisonResult(this);
            }
        }
    }
} 