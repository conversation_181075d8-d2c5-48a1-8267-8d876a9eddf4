package com.ximalaya.ting.android.live.common.lib;

import androidx.collection.ArrayMap;

import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * GiftDataComparator 使用示例
 * 展示如何使用礼物数据比较器
 * 
 * <AUTHOR>
 */
public class GiftDataComparatorUsage {

    private static final String TAG = "GiftDataComparatorUsage";

    /**
     * 使用示例1：基本比较
     */
    public static void basicComparisonExample(ArrayMap<Long, GiftInfoCombine.GiftInfo> giftInfoMapData, ArrayMap<Long, GiftInfoCombine.GiftInfo> data2) {
        // 执行比较
        GiftDataComparator.ComparisonResult result = GiftDataComparator.performComparison(giftInfoMapData, data2);

        // 检查总体一致性
        if (result.isOverallConsistent()) {
            Logger.i(TAG, "✅ 礼物数据完全一致");
        } else {
            Logger.w(TAG, "❌ 礼物数据存在不一致");
            
            // 逐层检查差异
            checkDetailedDifferences(result);
        }
    }

    /**
     * 使用示例2：详细差异检查
     */
    public static void detailedComparisonExample(ArrayMap<Long, GiftInfoCombine.GiftInfo> giftInfoMapData, ArrayMap<Long, GiftInfoCombine.GiftInfo> data2) {
        GiftDataComparator.ComparisonResult result = GiftDataComparator.performComparison(giftInfoMapData, data2);

        // 分层检查
        Logger.i(TAG, "=== 礼物数据比较结果 ===");
        Logger.i(TAG, String.format("总体一致性: %s", result.isOverallConsistent() ? "✅" : "❌"));
        
        // 1. Size 比较
        Logger.i(TAG, String.format("Size 一致性: %s", result.isSizeConsistent() ? "✅" : "❌"));
        if (!result.isSizeConsistent()) {
            Logger.w(TAG, "Size 差异:\n" + result.getSizeDifferences());
        }

        // 2. Keys 比较  
        Logger.i(TAG, String.format("Keys 一致性: %s", result.isKeysConsistent() ? "✅" : "❌"));
        if (!result.isKeysConsistent()) {
            Logger.w(TAG, "Keys 差异:\n" + result.getKeyDifferences());
        }

        // 3. Content 比较
        Logger.i(TAG, String.format("Content 一致性: %s", result.isContentConsistent() ? "✅" : "❌"));
        if (!result.isContentConsistent()) {
            Logger.w(TAG, "Content 差异:\n" + result.getContentDifferences());
        }

        // 获取所有差异
        String allDifferences = result.getAllDifferences();
        if (!allDifferences.isEmpty()) {
            Logger.w(TAG, "所有差异详情:\n" + allDifferences);
        }
    }

    /**
     * 检查详细差异
     */
    private static void checkDetailedDifferences(GiftDataComparator.ComparisonResult result) {
        if (!result.isSizeConsistent()) {
            Logger.w(TAG, "📊 Size 不一致:");
            Logger.w(TAG, result.getSizeDifferences());
        }

        if (!result.isKeysConsistent()) {
            Logger.w(TAG, "🔑 Keys 不一致:");
            Logger.w(TAG, result.getKeyDifferences());
        }

        if (!result.isContentConsistent()) {
            Logger.w(TAG, "📝 Content 不一致:");
            Logger.w(TAG, result.getContentDifferences());
        }
    }
} 