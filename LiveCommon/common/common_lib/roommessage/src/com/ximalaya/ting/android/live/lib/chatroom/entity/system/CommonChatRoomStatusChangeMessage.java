package com.ximalaya.ting.android.live.lib.chatroom.entity.system;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.model.live.PersonLiveBase;

/**
 * 直播状态变更消息。
 * {
 *      status: 1, // 场次 id
 *      reason: "您的直播违规", // 直播状态变化描述，如"您的直播违规"
 *      triggerAlert: true,// 是否触发弹窗，避免其他地方触发弹窗
 *      alertTitle: "提示",// 弹窗标题
 *      alertText: "您好，系统检测到您的直播间无有效互动，判定您的直播间处于挂播状态，请您在直播间发送一条聊天消息。若超过 5 分钟未响应，将隐藏直播间 72 小时",// 弹窗内容
 *      alertBtnTitle: "我知道了",// 弹窗按钮文字
 *      "type": 5, // 对话框type
 * }
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @since 2019/4/6
 */
public class CommonChatRoomStatusChangeMessage {

    /**
     * 直播业务状态：1 直播结束，5 直播预告， 9 直播中
     */
    @PersonLiveBase.LiveStatus
    public int status;
    /**
     * 直播状态变化描述，如"您的直播违规"
     */
    public String reason;

    /**
     * 审核关播，支持弹窗
     * 是否触发弹窗，避免其他地方触发弹窗
     */
    public boolean triggerAlert;

    /**
     * 标题
     */
    public String alertTitle;

    /**
     * 按钮文字
     */
    public String alertBtnTitle;
    /**
     * 内容
     */
    public String alertText;
    /**
     * 对话框类型
     */
    public int type;


    @NonNull
    @Override
    public String toString() {
        return "CommonChatRoomStatusChangeMessage{" +
                "status=" + status +
                ", reason='" + reason + '\'' +
                ", triggerAlert=" + triggerAlert +
                ", alertTitle='" + alertTitle + '\'' +
                ", alertBtnTitle='" + alertBtnTitle + '\'' +
                ", alertText='" + alertText + '\'' +
                ", type=" + type +
                '}';
    }

}
