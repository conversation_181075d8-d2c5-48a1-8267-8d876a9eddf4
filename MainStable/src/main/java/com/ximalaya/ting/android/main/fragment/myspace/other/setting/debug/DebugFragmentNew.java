package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ABTestItem;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AgentLogShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AlwayFreeFlowToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AnyUrlViewToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ApmCpuUseDebug;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ApmRemoteDebug;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ApmShareLog;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ApmStartUpItem;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ApmUploadLog;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppDebugInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppHotFixInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppHotFixToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppInfoToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppNetworkPing;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppRNBundleListInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppRNEngineToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppRNLocalCompileToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppRNSearchInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppTemplateInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AppTemplateToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AudioPlayerLogShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AudioPlayerLogToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.AutoSizeToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.BlockDnsToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.BundleDownloadDelayMockToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.BundleDownloadErrorMockToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.BundleInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.CommonLogShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.CommonLogToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ConfigCenterClear;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ConfigCenterShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ConfigCenterUpdate;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.CookieControllerInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.CookieHttp2OptimizeToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.DailyNewsShortCutChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.DnsInterceptorToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.DownloadDomainReplaceCdnToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.DubLogShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.DumpFileStateTraceInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.FixThirdPushChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.HarmonyPlayerChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.HomeRNToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.HttpProxyControllerToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.HttpsRecordToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.HttpsSwitchToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.IMNewProtocolToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.InnerDebugInfo;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.IpvxSelectToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.JPUshScenariosChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.LaunchADFPSToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.LaunchHomePreloadToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.LiveAudioDeviceModeItem;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.LiveH265SwitchChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.LiveVerifySwitchChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.MediaPlayCacheEnableToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.MethodTraceShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.MethodTraceToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.NetDiagnoseResultToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.NetworkCaptureToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.NetworkInfoShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.NewUser;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.NoPermissionNotificationChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PhotoCompressToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PlayPageSetting;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PlayPageSkinDebug;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PlayPageStartOpt;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PrivacyRiskCollect;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PrivacyRiskCollectToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PushGuardPlayerChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.PushGuardRecommendToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.RecordLogShare;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.RecordLogToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ScreenshotEnableToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.SdkPcdnToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ShortPlaySupportNewBuy;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.ShownotesChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.TrackDecoder;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.UseNewPlayerToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.VideoPlayerChoose;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.VideoPreview;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.VideoToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.VolumeGainLocalToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.WebViewKernelItem;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.WireControlToggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.X5Toggle;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.XLogSingleDebug;
import com.ximalaya.ting.android.main.stable.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.util.OsUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * app 诊断页面
 *
 * <AUTHOR>
 */
public class DebugFragmentNew extends BaseFragment2 implements View.OnClickListener {
    private static final String TAG = "DebugFragment";

    private RecyclerView mRecyclerView;
    private DebugCategoryAdapter mDebugCategoryAdapter;

    private List<List<IDebugItem>> mDebugCategoryList;


    private List<String> whitleList = Arrays.asList("c51b0976-5133-3558-bc8b-10f4b4e80973");
    private int titleClickTime;

    public DebugFragmentNew() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_FRAMELAYOUT, null, com.ximalaya.ting.android.host.R.color.host_color_f3f4f5);
    }

    @Override
    protected String getPageLogicName() {
        return "app诊断工具";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("应用诊断工具");
        mRecyclerView = findViewById(R.id.main_rv_items);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        mRecyclerView.setLayoutManager(linearLayoutManager);
        mDebugCategoryAdapter = initDebugCategory();
        if (mDebugCategoryAdapter != null) {
            mRecyclerView.setAdapter(mDebugCategoryAdapter);
        }
        findViewById(R.id.main_title_bar).setOnClickListener(this);
    }

    protected DebugCategoryAdapter initDebugCategory() {
        mDebugCategoryList = createDefaultCategoryList();
        return new DebugCategoryAdapter(mDebugCategoryList);
    }

    private List<List<IDebugItem>> createDefaultCategoryList() {
        List<List<IDebugItem>> arrayData = new ArrayList<>();

        List<IDebugItem> appInfoList = new ArrayList<>();
        appInfoList.add(new AppDebugInfo());
        appInfoList.add(new AppTemplateInfo());
        appInfoList.add(new AppTemplateToggle());
        appInfoList.add(new BundleDownloadErrorMockToggle());
        appInfoList.add(new BundleDownloadDelayMockToggle());
        appInfoList.add(new LaunchADFPSToggle());
        appInfoList.add(new LaunchHomePreloadToggle());
        appInfoList.add(new HomeRNToggle());
        arrayData.add(appInfoList);

        List<IDebugItem> reactNative = new ArrayList<>();
        reactNative.add(new AppRNBundleListInfo());
        reactNative.add(new AppRNSearchInfo());
        reactNative.add(new AppRNEngineToggle());
        reactNative.add(new AppRNLocalCompileToggle());
        arrayData.add(reactNative);

        List<IDebugItem> network = new ArrayList<>();
        network.add(new AppNetworkPing());
        network.add(new NetworkCaptureToggle());
        network.add(new NetworkInfoShare());
        network.add(new DnsInterceptorToggle());
        network.add(new HttpsRecordToggle());
        if(ConstantsOpenSdk.isDebug) {
            network.add(new HttpsSwitchToggle());
            network.add(new IMNewProtocolToggle());
        }
        network.add(new IpvxSelectToggle());
        network.add(new BlockDnsToggle());
        network.add(new AlwayFreeFlowToggle());
        network.add(new NetDiagnoseResultToggle());
        arrayData.add(network);

        List<IDebugItem> logInfo = new ArrayList<>();
        logInfo.add(new CommonLogToggle());
        logInfo.add(new CommonLogShare());
        logInfo.add(new RecordLogToggle());
        logInfo.add(new RecordLogShare());
        logInfo.add(new AudioPlayerLogToggle());
        logInfo.add(new AudioPlayerLogShare());
        logInfo.add(new DubLogShare());
        logInfo.add(new AgentLogShare());
        arrayData.add(logInfo);

        List<IDebugItem> configCenter = new ArrayList<>();
        configCenter.add(new ConfigCenterClear());
        configCenter.add(new ConfigCenterShare());
        configCenter.add(new ConfigCenterUpdate());
        arrayData.add(configCenter);

        List<IDebugItem> apm = new ArrayList<>();
        apm.add(new ApmStartUpItem());
        apm.add(new ApmRemoteDebug());
        apm.add(new ApmUploadLog());
        apm.add(new ApmShareLog());
        apm.add(new ApmCpuUseDebug());
        apm.add(new MethodTraceToggle());
        apm.add(new MethodTraceShare());
        if (ConstantsOpenSdk.isDebug) {
            apm.add(new DumpFileStateTraceInfo());
        }
        arrayData.add(apm);

        List<IDebugItem> safe = new ArrayList<>();
        safe.add(new ScreenshotEnableToggle());
        if (ConstantsOpenSdk.isDebug) {
            safe.add(new PrivacyRiskCollectToggle());
            safe.add(new PrivacyRiskCollect());
        }
        arrayData.add(safe);

//        List<IDebugItem> flexbox = new ArrayList<>();
//        flexbox.add(new FlexboxToggle());
//        arrayData.add(flexbox);

        List<IDebugItem> webView = new ArrayList<>();
        webView.add(new WebViewKernelItem());
        webView.add(new X5Toggle());
        webView.add(new PhotoCompressToggle());
        webView.add(new AnyUrlViewToggle());
        arrayData.add(webView);

        List<IDebugItem> hotfix = new ArrayList<>();
        hotfix.add(new AppHotFixToggle());
        hotfix.add(new AppHotFixInfo());
        arrayData.add(hotfix);

        List<IDebugItem> playPage = new ArrayList<>();
        playPage.add(new PlayPageSetting());
        playPage.add(new PlayPageSkinDebug());
        playPage.add(new ShownotesChoose());
        playPage.add(new PlayPageStartOpt());
        arrayData.add(playPage);

        List<IDebugItem> player = new ArrayList<>();
        player.add(new VideoToggle());
        player.add(new DownloadDomainReplaceCdnToggle());
        player.add(new SdkPcdnToggle());
        player.add(new WireControlToggle());
        player.add(new VolumeGainLocalToggle(() -> {
            if (canUpdateUi() && mDebugCategoryAdapter != null) {
                mDebugCategoryAdapter.notifyDataSetChanged();
            }
        }));
        arrayData.add(player);

        List<IDebugItem> other = new ArrayList<>();
        other.add(new TrackDecoder());
        other.add(new VideoPlayerChoose());
        other.add(new MediaPlayCacheEnableToggle());
        other.add(new UseNewPlayerToggle());
        other.add(new XLogSingleDebug());
        other.add(new BundleInfo());
        if (ConstantsOpenSdk.isDebug) {
            other.add(new CookieHttp2OptimizeToggle());
            other.add(new CookieControllerInfo());
            other.add(new HttpProxyControllerToggle());
            other.add(new AppInfoToggle());
        }

        if (isWhiteDevices() || ConstantsOpenSdk.isDebug) {
            other.add(new InnerDebugInfo());
        }
        other.add(new DailyNewsShortCutChoose());

        if (other.size() > 0) {
            arrayData.add(other);
        }
        List<IDebugItem> push = new ArrayList<>();
        if (!OsUtil.isHarmonyOs()) {
            push.add(new FixThirdPushChoose());
        }
        push.add(new NoPermissionNotificationChoose());
        push.add(new PushGuardPlayerChoose());
        push.add(new PushGuardRecommendToggle());
        push.add(new HarmonyPlayerChoose());
        push.add(new JPUshScenariosChoose());
        arrayData.add(push);

        arrayData.add(Collections.singletonList(new ABTestItem(() -> {
            if (canUpdateUi() && mDebugCategoryAdapter != null) {
                mDebugCategoryAdapter.notifyDataSetChanged();
            }
        })));

        List<IDebugItem> live = new ArrayList<>();
        live.add(new LiveAudioDeviceModeItem());
        if (ConstantsOpenSdk.isDebug) {
            live.add(new LiveVerifySwitchChoose());//直播认证中间件使用与否
        }
        live.add(new LiveH265SwitchChoose());
        arrayData.add(live);

        List<IDebugItem> screenAdapt = new ArrayList<>();
        screenAdapt.add(new AutoSizeToggle());
        arrayData.add(screenAdapt);

        if (ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.KEY_OPEN_CREATE_NEW_DEVICE, true)) {
            List<IDebugItem> newUser = new ArrayList<>();
            newUser.add(new NewUser());
            arrayData.add(newUser);
        }

        List<IDebugItem> videoDebug = new ArrayList<>();
        videoDebug.add(new VideoPreview());
        videoDebug.add(new ShortPlaySupportNewBuy());
        arrayData.add(videoDebug);

        return arrayData;
    }

    private boolean isWhiteDevices() {
        String deviceToken = DeviceUtil.getDeviceToken(getContext());
        if (TextUtils.isEmpty(deviceToken)) {
            return false;
        }

        if (whitleList != null && whitleList.contains(deviceToken)) {
            return true;
        }
        String whiteListStr = ConfigureCenter.getInstance().getString(CConstants.Group_sys.GROUP_NAME, CConstants.Group_sys.ITEM_INNER_DEBUG_WHITE_LIST, "");
        if (!TextUtils.isEmpty(whiteListStr)) {
            String[] whiteListArray = whiteListStr.split(";");
            for (String white : whiteListArray) {
                if (TextUtils.equals(white, deviceToken)) {
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        new DebugKotlinClass().test();
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_debug_layout_new;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_title_bar) {
            titleClickTime++;
            if (titleClickTime > 5) {
                startFragment(new InnerDebugFragment());
            }
            resetClick(v);
        }
    }

    private Runnable titleClickTimeReset = new Runnable() {
        @Override
        public void run() {
            titleClickTime = 0;
        }
    };

    private void resetClick(View view) {
        view.removeCallbacks(titleClickTimeReset);
        view.postDelayed(titleClickTimeReset, 1000);
    }


    class DebugCategoryViewHolder extends RecyclerView.ViewHolder {
        TextView titleNameTv;
        RecyclerView itemDebugRv;
        DebugItemAdapter itemAdapter;

        public DebugCategoryViewHolder(View itemView) {
            super(itemView);
            titleNameTv = itemView.findViewById(R.id.main_debug_category_name);
            itemDebugRv = itemView.findViewById(R.id.main_debug_category_container);
            itemAdapter = new DebugItemAdapter();

//            itemDebugRv.addItemDecoration(CommItemDecoration.createVertical(itemView.getContext(), R.color.main_color_f3f4f5, 0.8f));
            itemDebugRv.setLayoutManager(new LinearLayoutManager(itemView.getContext(), LinearLayoutManager.VERTICAL, false));
            itemDebugRv.setAdapter(itemAdapter);
        }

        public void onBind(List<IDebugItem> iDebugItems, int position) {
            if (TextUtils.isEmpty(titleNameTv.getText())) {
                titleNameTv.setText(iDebugItems.get(0).getCategory().getName());
            }
            itemAdapter.setData(iDebugItems);
            itemAdapter.notifyDataSetChanged();
        }
    }

    class DebugCategoryAdapter extends RecyclerView.Adapter<DebugCategoryViewHolder> {
        List<List<IDebugItem>> mDebugCategoryListData;

        public DebugCategoryAdapter(List<List<IDebugItem>> debugCategories) {
            this.mDebugCategoryListData = debugCategories;
        }

        @NonNull
        @Override
        public DebugCategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_debug_common_category, parent, false);
            return new DebugCategoryViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull DebugCategoryViewHolder holder, int position) {
            List<IDebugItem> debugItems = getItem(position);
            holder.onBind(debugItems, position);
        }

        @Override
        public int getItemViewType(int position) {
            List<IDebugItem> iDebugItem = getItem(position);
            if (!ToolUtil.isEmptyCollects(iDebugItem) && iDebugItem.get(0) != null) {
                return iDebugItem.get(0).getCategory().getType();
            }
            return 0;
        }

        public List<IDebugItem> getItem(int position) {
            if (ToolUtil.isEmptyCollects(mDebugCategoryListData) || mDebugCategoryListData.size() <= position) {
                return null;
            }
            return mDebugCategoryListData.get(position);
        }

        public List<List<IDebugItem>> getData() {
            return mDebugCategoryListData;
        }

        @Override
        public int getItemCount() {
            return ToolUtil.isEmptyCollects(mDebugCategoryListData) ? 0 : mDebugCategoryListData.size();
        }
    }

    public interface INotifyDataCallback {
        void notifyView();
    }

}
