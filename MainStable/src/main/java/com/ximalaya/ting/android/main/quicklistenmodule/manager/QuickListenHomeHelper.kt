package com.ximalaya.ting.android.main.quicklistenmodule.manager

import android.view.View
import com.google.gson.reflect.TypeToken
import com.libra.LruCache
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.FileUtil
import com.ximalaya.ting.android.host.data.model.Behavior
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.manager.doc.DocLoadResult
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager
import com.ximalaya.ting.android.host.manager.record.DownloadManager
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.view.other.MyViewPagerCanDisableFillNeighbourTab
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider.TrackPosition
import com.ximalaya.ting.android.main.quicklistenmodule.fragment.LAST_REQUEST_TIME_KEY
import com.ximalaya.ting.android.main.quicklistenmodule.fragment.QUICK_LISTEN_LAST_REQUEST_TIME
import com.ximalaya.ting.android.main.util.SimpleDownloadTask
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.model.album.Announcer
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.util.AsyncGson
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmriskdatacollector.util.FileIoUtils
import com.ximalaya.ting.android.xmutil.Logger
import java.io.File
import java.util.LinkedList

/**
 * Created by nali on 2025/6/27.
 * <AUTHOR>
 */
object QuickListenHomeHelper {
    
    const val ELEMENT_TYPE_XIMA_TEN: String = "XimaTen"
    const val ELEMENT_TYPE_TRACK_COLLECTION: String = "TrackCollection"
    const val ELEMENT_TYPE_TRACK: String = "Track"
    const val KEY_ELEMENT_TYPE: String = "elementType"
    const val KEY_SUB_ELEMENTS: String = "subElements"
    const val KEY_BIZ_TYPE: String = "bizType"

    val mDocLruCache = LruCache<Long, DocLoadResult>(10)

    fun trackIndexInList(tracks: List<Track>, trackId: Long): Int {
        if (!ToolUtil.isEmptyCollects(tracks)) {
            for (i in tracks.indices) {
                val item = tracks[i]
                if (item != null && item.dataId == trackId) {
                    return i
                }
            }
        }
        return -1
    }

    fun convertQuickListenModelToTrack(quickListenListData: List<QuickListenModel>): List<Track> {
        if (quickListenListData == null || quickListenListData.size == 0) {
            return ArrayList()
        }
        val result: MutableList<Track> = LinkedList()
        for (model in quickListenListData) {
            var refId = ""
            val elementType = model.elementType ?: ""
            if (model.isXimaTen() || model.isTrackCollect()) {
                if ((model.subElements?.size ?: 0) > 0) {
                    model.subElements?.forEachIndexed { index, quickListenModel ->
                        if (index == 0) {
                            refId = (quickListenModel.refId ?: 0).toString()
                        }
                        parseTrackM(quickListenModel, refId, elementType)?.let {
                            result.add(it)
                        }
                    }
                }
            } else {
                parseTrackM(model, refId, elementType)?.let {
                    result.add(it)
                }
            }
        }
        return result
    }

    private fun parseTrackM(model: QuickListenModel, refId: String?, elementType: String): TrackM? {
        if (model.bizType == ELEMENT_TYPE_TRACK) {
            try {
                val track = TrackM()
                // 音频ID
                track.dataId = model.refId ?: 0
                // 标题
                track.trackTitle = model.title
                // 封面
                track.coverUrlLarge = model.cover
                // 来源类型（可根据业务自定义）
                if (ELEMENT_TYPE_XIMA_TEN == elementType) {
                    track.trackFrom = 1 // 例如1代表QuickListen
                } else if (ELEMENT_TYPE_TRACK_COLLECTION == elementType) {
                    track.trackFrom = 4
                    track.collectionId = refId
                } else {
                    track.trackFrom = 2
                }

                // 主播信息
                if (model.anchor != null) {
                    val announcer = Announcer()
                    announcer.announcerId = model.anchor?.uid ?: 0
                    announcer.nickname = model.anchor?.nickName
                    announcer.avatarUrl = model.anchor?.logo
                    track.announcer = announcer
                    track.uid = model.anchor?.uid ?: 0
                }

                // 专辑信息
                if (model.surElement != null) {
                    val album = SubordinatedAlbum()
                    album.albumId = model.surElement?.refId ?: 0
                    album.albumTitle = model.surElement?.title
                    album.coverUrlLarge = model.surElement?.cover
                    track.setAlbum(album)
                }

                // 额外信息
                track.quickListenHeadSkip = model.extraInfo?.startTime ?: 0 // 快听起始时间
                track.isFirstTrack = model.extraInfo?.isFirstTrack ?: false
                track.isAigc = model.extraInfo?.aigc ?: false
                track.isCollect = model.extraInfo?.isCollect ?: false

                // 推荐/埋点等信息（如有）
                if (model.ubtV2 != null) {
                    track.ubtV2 = model.ubtV2
                    track.ubtTraceId = model.ubtV2?.get("ubtTraceId")
                    track.recSource = model.ubtV2?.get("rec_src")
                    track.recTrack = model.ubtV2?.get("rec_track")
                }
                return track
            } catch (e: Exception) {
                Logger.e("XimaTenDataUtil", "convertQuickListenModelToTrack error: " + e.message)
            }
        }

        return null
    }

    private fun getDirPath(): String? {
        var filePath: String? = null
        try {
            val file = File(
                ToolUtil.getCtx().filesDir.absolutePath, "quick_listen"
            )
            if (!file.exists()) {
                file.mkdirs()
            }
            filePath = file.absolutePath
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return filePath
    }

    fun downloadVideo(downloadCallBack: SimpleDownloadTask.DownloadCallback) {
        val url = "https://aod.cos.tx.xmcdn.com/storages/ce54-audiofreehighqps/84/D5/GAqhp50L824CAAv5egOqVyM4.mp4"
        val fileName = "quick_listen_animtor.mp4"
        if (StringUtil.isEmpty(url) || StringUtil.isEmpty(fileName)) {
            return
        }
        val dirPath: String = getDirPath()
            ?: return
        val file = File(dirPath, fileName)
        if (file.exists()) {
            downloadCallBack.onSuccess()
            return
        }

        val downloadingTempFileName = "quick_listen_animtor_temp.mp4"
        val simpleDownloadTask = SimpleDownloadTask(ToolUtil.getCtx(), url, dirPath, downloadingTempFileName, object : SimpleDownloadTask.DownloadCallback {
            override fun onSuccess() {
                val downloadTempFile = File(dirPath, downloadingTempFileName)
                FileUtil.renameFile(downloadTempFile, file)
                downloadCallBack.onSuccess()
            }

            override fun onFailed() {
            }

            override fun onProgress(progress: Int) {
            }
        })
        DownloadManager.getInstance().download(simpleDownloadTask, true)
    }

    fun getDownloadVideoFilePath(): String {
        return File(getDirPath(), "quick_listen_animtor.mp4").absolutePath
    }

    fun handlePlayOrPause() {
        XmPlayerManager.getInstance(ToolUtil.getCtx()).let {
            if (it.isPlaying) {
                QuickListenDataManager.getInstance().playOrPause(true, "quick_listen")
                it.pause(PauseReason.Business.QUICK_LISTEN_USER_PAUSE)
            } else {
                it.play()
            }
        }
    }

    fun getViewPager(view: View): MyViewPagerCanDisableFillNeighbourTab? {
        var parent = view.parent
        while (parent != null) {
            if (parent is MyViewPagerCanDisableFillNeighbourTab) {
                return parent
            }
            parent = parent.parent
        }
        return null
    }

    // 计算高亮位置
    fun calculateHighlightPositions(data: QuickListenModel) {
        if ((data.totalDuration ?: 0) > 0) {
            return
        }

        // 检查数据有效性
        if (data.subElements.isNullOrEmpty()) {
            return
        }

        // 计算总时长
        var totalDuration = 0L

        for (item in data.subElements!!) {
            totalDuration += item.duration ?: 0L
        }

        // 如果总时长为0，直接返回
        if (totalDuration == 0L) {
            return
        }

        data.totalDuration = totalDuration
    }

    // 根据进度值获取对应的声音和位置
    fun getTrackAndPositionByProgress(data: QuickListenModel, progress: Float): TrackPosition? {
        // 检查数据有效性
        if (data.subElements.isNullOrEmpty() || (data.totalDuration ?: 0) <= 0) {
            return null
        }

        // 计算当前进度对应的时间点（毫秒）
        val currentTime = (progress * (data.totalDuration ?: 0)).toLong()

        // 找到当前时间点对应的声音
        var accumulatedTime = 0L
        for (i in data.subElements!!.indices) {
            val item = data.subElements!![i]
            val itemDuration = item.duration ?: 0L

            if (currentTime <= accumulatedTime + itemDuration) {
                // 计算在当前声音中的相对进度
                val relativeTime = currentTime - accumulatedTime
                return TrackPosition(
                    item,
                    position = relativeTime,
                    index = i,
                )
            }
            accumulatedTime += itemDuration
        }

        // 如果没找到（进度为1），返回第一个声音的开始位置
        return TrackPosition(
            data,
            position = 0L,
            index = 0
        )
    }

    fun isFirstEntryToday(): Boolean {
        if (ToolUtil.getDebugSystemProperty("debug.quick_listen.enabledailyshow", "-1") == "1") {
            return true
        }

        val nowDateStr = DateTimeUtil.getNowDateStr()
        if (MMKVUtil.getInstance().getString("quick_listen_last_entry_time") == nowDateStr) {
            return false
        }

        MMKVUtil.getInstance().saveString("quick_listen_last_entry_time", nowDateStr)
        return true
    }

    val realDisplayHeight by lazy {
        val scale = ToolUtil.getCtx().resources.displayMetrics.density
        val realDisplayHeight = BaseUtil.getScreenHeight(ToolUtil.getCtx()) / scale
        Logger.log("QuickListenHomeHelper : realDisplayHeight=${realDisplayHeight}")
        realDisplayHeight
    }

    fun isSmallScreenDevice(): Boolean {
        return realDisplayHeight > 1 && realDisplayHeight < 750
    }

    fun isMediumScreenDevice(): Boolean {
        return realDisplayHeight >= 750 && realDisplayHeight < 812
    }

    fun isLargeScreenDevice(): Boolean {
        return realDisplayHeight >= 812
    }

    fun isOverMediumScreenDevice(): Boolean {
        return realDisplayHeight >= 750
    }

    // 从QuickListenItemPageFragment.kt 中copy的
    fun checkNeedRefresh(): Boolean {
        val playTrackType = getCurPlayTrackType()
        if (playTrackType == 3) {
            // 播放非快听声音，下次重新进入就刷新
            // console.log('checkNeedRefresh [非快听声音] return true')
            return true
        }

        // 快听声音，一小时内不刷新
        val lastRequestTime = MMKVUtil.getInstance().getLong(QUICK_LISTEN_LAST_REQUEST_TIME, 0L)
        if (Math.abs(System.currentTimeMillis() - lastRequestTime) < 1 * 3600 * 1_000) {
            // 一小时内，不刷新
            // console.log('checkNeedRefresh 一小时内，不刷新')
            return false
        }

        // 内容卡、十条卡，获取上次请求时间，如果在当天内，就不需要刷新
        val lastRequestTime2 = MMKVUtil.getInstance().getLong(LAST_REQUEST_TIME_KEY, 0L)
        // console.log('checkNeedRefresh s2 [十条卡] lastRequestTime (from storage) >>> ', lastRequestTime)
        if (DateTimeUtil.isAnotherDay(lastRequestTime2)) {
            // console.log('checkNeedRefresh s2 [十条卡] 不是一天，刷新.')
            return true
        }
        return false
    }

    private fun getCurPlayTrackType(): Int {
        val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
        val quickListen: Boolean
        if (playerManager.isPlayListSet) {
            quickListen = playerManager.isQuickListen
        } else {
            quickListen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_LAST_IS_QUICK_LISTEN, false)
            Logger.d("quicklisten", "playList还没设置")
        }
        val currSound = playerManager.currSound
        Logger.d("quicklisten", "getBundleArgs  quickListen: $quickListen")
        var playTrackType = 3
        if (quickListen && currSound is Track) {
            val trackFrom = currSound.trackFrom
            Logger.d("quicklisten", "getBundleArgs  trackFrom: $trackFrom")

            if (trackFrom > 0) {
                if (trackFrom == 4) {
                    playTrackType = 2
                } else {
                    playTrackType = trackFrom
                }
            }
        }
        return playTrackType
    }

    const val PLAYED_TRACK_BEHAVIORS_KEY = "daily_news_played_track_behaviors_2"

    private val trackBehaviors = mutableListOf<Behavior>()
    private val playedTrackBehaviors = mutableListOf<Behavior>()
    private var waitReportBehaviorSize = 0
    private var lastTrackBehavior: Behavior? = null

    fun initPlayedTrackBehaviors() {
        val cache = MMKVUtil.getInstance().getString(PLAYED_TRACK_BEHAVIORS_KEY)
        if (cache.isAvailable()) {
            playedTrackBehaviors.clear()
            val type = (object : TypeToken<List<Behavior>>() {}).type
            AsyncGson<List<Behavior>>().fromJson(cache, type, object : AsyncGson.IResult<List<Behavior>> {
                override fun postResult(result: List<Behavior>?) {
                    if (result?.isNotEmpty() == true) {
                        playedTrackBehaviors.addAll(result)
                        clearOldBehaviors("init")
                    }
                }

                override fun postException(e: java.lang.Exception?) {
                }
            })
        }
    }

    fun clearBehavior() {
        trackBehaviors.clear()
        playedTrackBehaviors.clear()
    }

    private fun clearOldBehaviors(source: String) {
        val oneDayAgo = System.currentTimeMillis() - 24 * 60 * 60 * 1000 // 24小时，毫秒为单位

        // 清理trackBehaviors中的旧行为
        trackBehaviors.removeAll { behavior ->
            (behavior.ts ?: 0) < oneDayAgo
        }

        Logger.d("quicklisten", "initPlayedTrackBehaviors playedTrackBehaviorsCountBefore: ${playedTrackBehaviors.size}, source: $source")

        if (playedTrackBehaviors.isEmpty()) {
            return
        }

        // 清理playedTrackBehaviors中的旧行为
        playedTrackBehaviors.removeAll { behavior ->
            try {
                (behavior.ts ?: 0) < oneDayAgo
            } catch (e: Exception) {
                Logger.e("quicklisten", "Error parsing behavior: $e")
                false // 移除无效条目
            }
        }

        // 如果playedTrackBehaviors数量超过最大限制，保留最新的数据
        val MAX_PLAYED_TRACK_BEHAVIORS_COUNT = 40 // 根据实际需求调整
        if (playedTrackBehaviors.size > MAX_PLAYED_TRACK_BEHAVIORS_COUNT) {
            val startIndex = playedTrackBehaviors.size - MAX_PLAYED_TRACK_BEHAVIORS_COUNT
            val tempList = playedTrackBehaviors.subList(startIndex, playedTrackBehaviors.size).toMutableList()
            playedTrackBehaviors.clear()
            playedTrackBehaviors.addAll(tempList)
        }
    }

    fun saveBehaviorToLocal(
        trackId: Long,
        behaveType: String = "播放",
    ) {
        Logger.log("trackBehaviors saveBehaviorToLocal >>> trackId: $trackId, behaveType: $behaveType")

        // 查找当前track的行为
        val currentTrackBehavior = trackBehaviors.find { it.refId == trackId }

        if (currentTrackBehavior != null) {
            val endTime = System.currentTimeMillis()
            currentTrackBehavior.playEndTime = endTime
            if (currentTrackBehavior.playStartTime == 0L) {
                currentTrackBehavior.playStartTime = endTime
            }
            // 计算总播放时间 = 累计暂停前的播放时间 + 最后一次播放时间 - 累计暂停时间
            currentTrackBehavior.playDuration = (currentTrackBehavior.playBeforePauseTime ?: 0) + (endTime - (currentTrackBehavior.playStartTime ?: 0)) - (currentTrackBehavior.totalPauseTime ?: 0)

            currentTrackBehavior.playDuration = ((currentTrackBehavior.playDuration?: 0) / 1000).toLong()

            if (behaveType == "点赞" || behaveType == "负反馈") {
                // 点赞、负反馈，新建一条数据
                val newBehavior = currentTrackBehavior.copy(behaveType = behaveType)
                playedTrackBehaviors.add(newBehavior)
                return
            } else {
                currentTrackBehavior.behaveType = behaveType
                // 使用字符串类型，避免时间戳类型传递到原生有改变
                playedTrackBehaviors.add(currentTrackBehavior)
            }

            AsyncGson<String>().toJson(playedTrackBehaviors, object : AsyncGson.IResult<String> {
                override fun postResult(result: String?) {
                    saveSequence(result)
                    MMKVUtil.getInstance().saveString(PLAYED_TRACK_BEHAVIORS_KEY, result)
                    clearOldBehaviors("report")
                }

                override fun postException(e: java.lang.Exception?) {

                }
            })
        } else {
            Logger.log("trackBehaviors saveBehaviorToLocal >>> 没有保存行为数据")
        }
    }

    private fun saveSequence(behaviors: String?) {
        var success = false
        try {
            if (behaviors.isAvailable()) {
                MMKVUtil.getInstance().saveString(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE, behaviors)
                MMKVUtil.getInstance().saveLong(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE_SAVE_TIME, System.currentTimeMillis())
                success = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        if (!success) {
            MMKVUtil.getInstance().removeByKey(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE)
            MMKVUtil.getInstance().removeByKey(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE_SAVE_TIME)
        }
    }

    fun getCurPlayingId(isPlayStart: Int = 1, listData: List<QuickListenModel>?) {
        if (listData == null) {
            return
        }
        saveBehavior(PlayTools.getCurTrackId(ToolUtil.getCtx()), isPlayStart, listData)
    }

    private fun saveBehavior(trackId: Long, isPlayStart: Int, listData: List<QuickListenModel>) {
        Logger.log("trackBehaviors saveBehavior >>> trackId: $trackId, isPlayStart: $isPlayStart")
        
        // 查找当前声音的行为记录
        var currentTrackBehavior = trackBehaviors.find { it.refId == trackId }
        
        Logger.log("trackBehaviors: $isPlayStart >>> $currentTrackBehavior, $trackId")
        
        when (isPlayStart) {
            1 -> {
                // 开始播放
                Logger.log("trackBehaviors checkPlayTrackId >>> ${lastTrackBehavior?.refId}, $trackId")
                
                // 如果上一个声音不是当前声音，保存上一个声音的行为
                if (lastTrackBehavior?.refId != trackId) {
                    lastTrackBehavior?.refId?.let {
                        saveBehaviorToLocal(it)
                    }
                }
                
                if (currentTrackBehavior == null) {
                    // 首次播放，创建新记录
                    if (listData?.isEmpty() == true) {
                        return
                    }
                    val index = QuickListenManager.getQuickListenListIndexByTrackId(trackId, listData)
                    if (index < 0) {
                        return
                    }
                    val currentTrack = listData[index]

                    Logger.log("trackBehaviors: $isPlayStart create new>> , currentTrack: $trackId")
                    
                    if (currentTrack != null) {
                        currentTrackBehavior = Behavior(
                            refId = trackId,
                            duration = currentTrack?.duration ?: 0L,
                            playDuration = 0L,
                            bizType = "Track",
                            behaveType = "播放",
                            ts = System.currentTimeMillis(),
                            playStartTime = System.currentTimeMillis(),
                            playPauseTime = 0L,
                            playBeforePauseTime = 0L,
                            playEndTime = 0L,
                            totalPauseTime = 0L
                        )
                        trackBehaviors.add(currentTrackBehavior)
                        Logger.log("trackBehaviors22: $isPlayStart push new data ${currentTrackBehavior.duration}")
                    }
                } else {
                    // 继续播放，更新开始时间
                    Logger.log("trackBehaviors: $isPlayStart continue play>> ")
                    currentTrackBehavior.playStartTime = System.currentTimeMillis()
                }
                
                lastTrackBehavior = currentTrackBehavior
                Logger.log("trackBehaviors checkPlayTrackId [updateTrackId] >>> ${lastTrackBehavior?.refId}")
            }
            
            2 -> {
                // 暂停
                currentTrackBehavior?.let { behavior ->
                    val pauseTime = System.currentTimeMillis()
                    behavior.playPauseTime = pauseTime
                    // 累加本次暂停前的播放时间
                    behavior.playBeforePauseTime = behavior.playBeforePauseTime?.plus(pauseTime - (behavior.playStartTime ?: 0))
                    // 累加本次暂停时间
                    behavior.totalPauseTime = behavior.totalPauseTime?.plus(pauseTime - (behavior.playStartTime ?: 0))
                }
                Logger.log("trackBehaviors pause >>> $currentTrackBehavior, $playedTrackBehaviors")
            }
            
            3 -> {
                // 停止
                saveBehaviorToLocal(trackId)
            }
        }
    }

    fun getPlayDuration(data: QuickListenModel): Int {
        trackBehaviors.firstOrNull { it.refId == data.refId }?.let {
            if (it.playDuration == 0L && (it.playStartTime ?: 0) > 0) {
                it.playDuration = System.currentTimeMillis() - (it.playStartTime ?: 0)
            }
            return (((it.playDuration ?: 0) / 1000)).toInt()
        }

        return 0
    }

}