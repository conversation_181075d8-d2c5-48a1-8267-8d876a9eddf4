package com.ximalaya.ting.android.main.quicklistenmodule.iinterface

import androidx.annotation.Keep
import com.ximalaya.ting.android.main.quicklistenmodule.view.CustomSwipeRefreshLayout
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener

/**
 * Created by nali on 2025/6/23.
 * <AUTHOR>
 */
interface IQuickListenHomePageControl {
    fun onPagePull(pullDistance: Float)

    fun onRefresh(refreshView: CustomSwipeRefreshLayout)

    fun onRefreshComplete(childPosition: Int)

    fun changeTabTheme(isBlue: Boolean)

    fun addPlayerStatusListener(listen: IXmPlayerStatusListener)

    fun removePlayerStatusListener(listen: IXmPlayerStatusListener)

    fun onChildPageSelected(childPosition: Int, position: Int)

    fun childPosition(): Int

    fun curTabId(): Long

    fun sendAiAgentQuery(sug: String?)

    fun sendCardListShow(cardType: String, refId: String)

    fun sendMoreControlShow(dataString: String)

    fun getFrom(): String?

    fun saveBehaviorToLocal(trackId: Long, behaveType: String)

    fun negativeXimaTenData(trackId: Long, positionNew: Int, itemPosition: Int)

    fun negativeSoundCard(trackId: Long, positionNew: Int, itemPosition: Int)

    fun setParentCurrentItem(position: Int)
}
