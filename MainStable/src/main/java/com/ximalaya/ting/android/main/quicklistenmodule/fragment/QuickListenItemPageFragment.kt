package com.ximalaya.ting.android.main.quicklistenmodule.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import androidx.viewpager2.widget.ViewPager2
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.util.ViewUtil.PADDING_TOP
import com.ximalaya.ting.android.host.data.model.QuickElementTypeMap
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.TrackCollectManager
import com.ximalaya.ting.android.host.manager.quicklisten.IItemFragmentCallback
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager
import com.ximalaya.ting.android.host.manager.track.AgentRadioCollectListener
import com.ximalaya.ting.android.host.manager.track.AgentRadioEventManage
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage.TrackLikeStatusListener
import com.ximalaya.ting.android.host.model.quicklisten.ELEMENT_TYPE_RECOMMEND
import com.ximalaya.ting.android.host.util.XimaTenDataUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.QuickListenVerticalPagerAdapter
import com.ximalaya.ting.android.main.quicklistenmodule.iinterface.IQuickListenHomePageControl
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenConfigManager
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenTraceUtil
import com.ximalaya.ting.android.main.quicklistenmodule.view.CustomSwipeRefreshLayout
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject


/**
 * Created by nali on 2025/6/20.
 * <AUTHOR>
 */
private const val TAG = "QuickListenItemPageFragment"

private const val QUICK_LISTEN_LAST_REQUEST_TIME_PREFIX = "key_quick_listen_last_request_time_"
const val QUICK_LISTEN_LAST_REQUEST_TIME = "key_quick_listen_last_request_time"
// 存储键名
private const val LAST_ENTRY_DATE_KEY = "key_daily_news_last_entry_date_2"
// 上次请求时间
const val LAST_REQUEST_TIME_KEY = "key_daily_news_last_request_time_2"
private const val quick_listen_agent_guide_show_time = "key_quick_listen_agent_guide_show_time"

class QuickListenItemPageFragment: BaseFragment2() {
    private val mMainViewPager: ViewPager2 by lazy { findViewById<ViewPager2>(R.id.main_view_pager) }
    private val mMainSwiperRefresh: CustomSwipeRefreshLayout by lazy { findViewById<CustomSwipeRefreshLayout>(R.id.main_swipe_refresh) }
    private val mTitleBar: View by lazy { findViewById<View>(R.id.main_title_bar) }
    private val mRootLay: View by lazy { findViewById<View>(R.id.main_root_layout) }

    private val mBottomHeight: Int = 116.dp
    private val mAdapter = QuickListenVerticalPagerAdapter(this)
    private var mChildPosition: Int = 0
    private var mSelectPagePositionByTrackId: Long = 0

    private var mMaxCardIndex = -1
    private var agentGuideDateLimitRef = 0
    private var agentGuideCountLimitRef = 0
    private var mTabId = -1L
    private var mTabName = ""
    private var mTabType: String? = null
    private var notifyPageChange = true

    private var mPageState: Int = ViewPager2.SCROLL_STATE_IDLE
    private var mNotifyCurRunnable: Runnable? = null
    private var mNotifyAdjacencyRunnable: Runnable? = null

    override fun getPageLogicName(): String = this::class.java.simpleName

    override fun initUi(savedInstanceState: Bundle?) {
        mChildPosition = arguments?.getInt("childPosition") ?: 0
        mTabId = arguments?.getLong("tabId", -1L) ?: -1L
        mTabName = arguments?.getString("tabName", "") ?: ""
        mTabType = arguments?.getString("elementType", null) ?: null
        mSelectPagePositionByTrackId = arguments?.getLong("selectPagePositionByTrackId") ?: 0

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            mTitleBar.layoutParams?.height = (mTitleBar.layoutParams?.height ?: 0) + BaseUtil.getStatusBarHeight(context)
            ViewUtil.onlySetViewPaddingOne(mTitleBar, BaseUtil.getStatusBarHeight(context), PADDING_TOP)
        }

        mMainViewPager.orientation = ViewPager2.ORIENTATION_VERTICAL
        val recyclerView: RecyclerView = mMainViewPager.getChildAt(0) as RecyclerView
        recyclerView.setPadding(mMainViewPager.getPaddingLeft(), 0, 0, mBottomHeight)
        recyclerView.setClipToPadding(false)
        (recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        mMainViewPager.offscreenPageLimit = 1
        mMainViewPager.adapter = mAdapter

        mMainSwiperRefresh.setOnRefreshListener(object : CustomSwipeRefreshLayout.OnRefreshListener {
            override fun onRefresh() {
                getHomePageControl()?.onRefresh(mMainSwiperRefresh)
                loadDataImpl(true)
            }

            override fun onPullProgressListener(pullDistance: Float) {
                getHomePageControl()?.onPagePull(pullDistance)
            }
        })

        mMainViewPager.adapter?.registerAdapterDataObserver(object :
            RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                super.onChanged()

                if (notifyPageChange) {
                    getHomePageControl()?.onChildPageSelected(mChildPosition, mMainViewPager.currentItem)
                }

                getQuickListenListData().getOrNull(mMainViewPager.currentItem)?.let {
                    setTabTheme(it.isXimaTen())
                }
            }
        })

        mMainViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            var lastPosition = -1
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                setMaxCardIndex(position, "")

                updateTrackInfoAndPlay(position)
                if (notifyPageChange) {
                    if (lastPosition != -1) {
                        QuickListenTraceUtil.trace67649(PlayTools.getCurTrackId(ToolUtil.getCtx()),
                            getQuickListenListData(), lastPosition, false, lastPosition < position, mTabId)
                    }

                    QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                        getQuickListenListData().getOrNull(position))

                    getHomePageControl()?.onChildPageSelected(mChildPosition, position)
                }

                getQuickListenListData().getOrNull(position)?.let {
                    setTabTheme(it.isXimaTen())

                    if (it.isAgentGuide()) {
                        QuickListenTraceUtil.trace68820(mTabId)
                    } else if (it.isTrackCollect()) {
                        QuickListenTraceUtil.trace68571(
                            QuickListenManager.getCurQuickListenModel(it)?.refId ?: 0,
                            getQuickListenListData(),
                            position,
                            mTabId,
                            getHomePageControl()?.getFrom() ?: ""
                        )
                    }
                }

                lastPosition = position
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                mPageState = state
                if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    if (mNotifyAdjacencyRunnable != null) {
                        mNotifyCurRunnable = null
                        mNotifyAdjacencyRunnable?.run()
                        mNotifyAdjacencyRunnable = null
                    } else if (mNotifyCurRunnable != null) {
                        mNotifyCurRunnable?.run()
                        mNotifyCurRunnable = null
                    }
                }
            }
        })

        AlbumEventManage.addListener(mCollectListener)
        LikeTrackManage.addListener(mTrackLikeStatusListener)
        AgentRadioEventManage.addListener(mAgentRadioCollectListener)
        TrackCollectManager.getInstance().addListener(mTrackCollectListener)

        runCatching {
            val isRecommend = mTabType == ELEMENT_TYPE_RECOMMEND
//            {
//                "SUPPORT_AGENT_GUIDE":true,
//                "VERSION_AGENT_GUIDE":["0.0.101"],
//                "AGENT_GUIDE_DATE_LIMIT":3,
//                "AGENT_GUIDE_COUNT_LIMIT":10
//            }
            val config = ConfigureCenter.getInstance().getString("toc", "quick_listen_rn_config")
            val jsonConfig = JSONObject(config)
            val dateLimit = jsonConfig.optInt("AGENT_GUIDE_DATE_LIMIT", 0)
            val countLimit = jsonConfig.optInt("AGENT_GUIDE_COUNT_LIMIT", 0)
            if (jsonConfig.optBoolean("SUPPORT_AGENT_GUIDE", false)) {
                val agent_config_version = QuickListenConfigManager.getInstance().agent_config_version
                val versionArr = jsonConfig.optJSONArray("VERSION_AGENT_GUIDE")
                var enable = false
                if (versionArr != null && versionArr.length() > 0) {
                    for (i in 0 until versionArr.length()) {
                        val version = versionArr.optString(i, "")
                        if (version == agent_config_version) {
                            enable = true
                        }
                    }
                }
                updateConfigAgent(enable, dateLimit, countLimit)
            } else {
                updateConfigAgent(false, dateLimit, countLimit)
            }
        }.onFailure { it.printStackTrace() }
    }

    private fun updateConfigAgent(enable: Boolean, dateLimit: Int, countLimit: Int) {
        agentGuideDateLimitRef = dateLimit
        agentGuideCountLimitRef = countLimit
        if (!enable) {
            agentGuideDateLimitRef = -1
            agentGuideCountLimitRef = -1
        }
    }

    private fun updateTrackInfoAndPlay(position: Int) {
        if (mTabType != ELEMENT_TYPE_RECOMMEND) {
            return
        }
        val card = mAdapter.getItemOrNull(position)
        if (card == null) {
            return
        }
        val curTime = System.currentTimeMillis()
        if (card.isAgentGuide()) {
            setAgentGuideShowTime(curTime)
        }
        // 判断是否适合引导创建AI电台
        val supportAgentGuideRef = QuickListenConfigManager.getInstance().supportAgentGuideRef
        if (supportAgentGuideRef != true) {
            return
        }
        val lastShowTime = getAgentGuideShowTime()
        val dayMs = 24 * 60 * 60 * 1000;
        if (lastShowTime > 0 &&
            agentGuideDateLimitRef > 0 &&
            curTime - lastShowTime < agentGuideDateLimitRef * dayMs
        ) {
            Logger.d(TAG, "setNeedAgentGuide - ${QuickListenConfigManager.getInstance().needAgentGuideRef}")
            if (QuickListenConfigManager.getInstance().needAgentGuideRef) {
                QuickListenConfigManager.getInstance().setNeedAgentGuide(false)
            }
            // 在冷却期内，不显示引导
            QuickListenConfigManager.getInstance().setNeedAgentGuide(false)
            return
        }

        if (agentGuideCountLimitRef > 0 && (mMaxCardIndex+1) >= agentGuideCountLimitRef && !QuickListenConfigManager.getInstance().needAgentGuideRef) {
            Logger.d(TAG, "setNeedAgentGuide2 - ${QuickListenConfigManager.getInstance().needAgentGuideRef}")
            QuickListenConfigManager.getInstance().setNeedAgentGuide(true)
        }
    }

    private fun setAgentGuideShowTime(time: Long) {
        MMKVUtil.getInstance().saveLong(quick_listen_agent_guide_show_time, time)
    }

    private fun getAgentGuideShowTime(): Long {
        return MMKVUtil.getInstance().getLong(quick_listen_agent_guide_show_time, 0L)
    }

    // 记录卡片滑动的最大深度，用于负反馈时移除列表操作
    private fun setMaxCardIndex(index: Int, source: String) {
        /// 从+2卡片开始移除
        val tNewIndex = index
        if (tNewIndex >= 0 && mMaxCardIndex < tNewIndex) {
//            console.log(`setMaxCardIndex >>> ${tNewIndex} ${source} old index >>> ${maxCardIndexRef.current}`)
            mMaxCardIndex = tNewIndex
        }
    }

    override fun loadData() {
        loadDataImpl(false)
    }

    private var mIsLoading = false

    private fun loadDataImpl(isRefresh: Boolean = false) {
        if (mIsLoading) {
            return
        }
        mIsLoading = true

        val firstLoading = !isRefresh

        if (firstLoading) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }

        if (isRefresh) {
            XmRequestPage.resetPageUniqueRequestId(QuickListenTraceUtil.getRequestPageId(mTabId))
        }

        val useCache = firstLoading && QuickListenDataManager.getInstance().canUseCache(mTabId, arguments?.getBoolean("openFragmentBeforeIsPlaying"))
        QuickListenDataManager.getInstance().requestXimaTenAndPlayNew(useCache, true, true, null, mTabId, mTabType == ELEMENT_TYPE_RECOMMEND, object : IDataCallBack<List<JSONObject?>?> {
            override fun onSuccess(data: List<JSONObject?>?) {
                mIsLoading = false
                if (isRefresh) {
                    mMainSwiperRefresh.isRefreshing = false
                    getHomePageControl()?.onRefreshComplete(mChildPosition)
                }
                val listData = XimaTenDataUtil.convertDataToQuickListenModel(data)
                if (listData == null || listData.isEmpty()) {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                    Logger.d(TAG, "loadData onSuccess data is null or empty")
                    return
                }
                onPageLoadingCompleted(LoadCompleteType.OK)
                // 十条卡，声音小于5条时，应该隐藏十条卡
                // 过滤掉不符合要求的 elementType
                var resListData = listData.filter { item ->
                    val result = if (item.isXimaTen()) {
                        (item.subElements?.size ?: 0) >= 5
                    } else {
                        true
                    }
                    result
                }.filter { item ->
                    item.elementType.isNullOrEmpty() || QuickElementTypeMap.contains(item.elementType)
                }

                Logger.d(TAG, "loadData onSuccess data size ${data?.size}, result size ${resListData.size}")

                // 数据去重：普通内容卡用refId，十条卡用title
                val elementSet = hashSetOf<Long>()
                val ximaTenSet = hashSetOf<String?>()
                if (useCache && resListData.isNotEmpty()) {
                    resListData = resListData.filter {
                        if (it.isXimaTen()) {
                            // 十条卡，使用title去重
                            if (!ximaTenSet.contains(it.title)) {
                                ximaTenSet.add(it.title)
                                true
                            } else {
                                false
                            }
                        } else {
                            // 普通内容卡，使用refId去重
                            if (!elementSet.contains(it.refId ?: 0L)) {
                                elementSet.add(it.refId ?: 0L)
                                true
                            } else {
                                false
                            }
                        }
                    }
                }
                Logger.d(TAG, "loadData onSuccess2 data size ${data?.size}, result size ${resListData.size}")

                // 新请求的数据，设置上次请求时间
                if (useCache == false) {
//                    MMKVUtil.getInstance().saveLong(LAST_REQUEST_TIME_KEY, System.currentTimeMillis())
//                    MMKVUtil.getInstance().saveLong(QUICK_LISTEN_LAST_REQUEST_TIME, System.currentTimeMillis())

                    MMKVUtil.getInstance().saveLong("$LAST_REQUEST_TIME_KEY$mTabId", System.currentTimeMillis())
                    MMKVUtil.getInstance().saveLong("$QUICK_LISTEN_LAST_REQUEST_TIME_PREFIX$mTabId", System.currentTimeMillis())

                    if (mSelectPagePositionByTrackId > 0) {
                        notifyPageChange = false
                    }

                    mMainViewPager.setCurrentItem(0, false)
                    mAdapter.setList(resListData)
                    notifyPageChange = true
                    if (mSelectPagePositionByTrackId > 0) {
                        val position = QuickListenManager.getQuickListenListIndexByTrackId(mSelectPagePositionByTrackId, resListData)
                        Logger.log("QuickListenItemPageFragment : onSuccess playWithTrackIdSceneId getQuickListenListIndexByTrackId $position   $mSelectPagePositionByTrackId")
                        if (position >= 0) {
                            mMainViewPager.setCurrentItem(position, false)
                        }
                        mSelectPagePositionByTrackId = 0
                    }
                } else {
                    if (mSelectPagePositionByTrackId > 0) {
                        notifyPageChange = false
                    }
                    mMainViewPager.setCurrentItem(0, false)
                    mAdapter.setList(resListData)
                    notifyPageChange = true
                    // resetCardIndex(res)
                    if (mSelectPagePositionByTrackId > 0) {
                        val position = QuickListenManager.getQuickListenListIndexByTrackId(mSelectPagePositionByTrackId, resListData)
                        Logger.log("QuickListenItemPageFragment : onSuccess playWithTrackIdSceneId  getQuickListenListIndexByTrackId $position  $mSelectPagePositionByTrackId")
                        if (position >= 0) {
                            mMainViewPager.setCurrentItem(position, false)
                        }
                        mSelectPagePositionByTrackId = 0
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
                mIsLoading = false

                if (isRefresh) {
                    mMainSwiperRefresh.isRefreshing = false
                    getHomePageControl()?.onRefreshComplete(mChildPosition)
                }
                Logger.e(TAG, "onError code: $code, message: $message")
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
            }
        },
            object : IItemFragmentCallback {
                override fun play(tabId: Long, autoPlay: Boolean) {
                    val curTabId = getHomePageControl()?.curTabId() ?: -1L
                    if (curTabId == tabId) {
                        val trackId =
                            QuickListenDataManager.getInstance().getLastTabPlayTrackId(tabId)
                        val data = QuickListenDataManager.getInstance().cache(tabId)
                        PlayTools.playQuickListenListWithTrackId(data, trackId, false, tabId)
                    }
                }
            })
//        val homePageTabData = XimaTenDataManager.getInstance().getItemTabData()
//        mAdapter.setList(homePageTabData)
    }

    private fun getCurPlayTrackType(): Int {
        val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
        val quickListen: Boolean
        if (playerManager.isPlayListSet) {
            quickListen = playerManager.isQuickListen
        } else {
            quickListen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_LAST_IS_QUICK_LISTEN, false)
            Logger.d("quicklisten", "playList还没设置")
        }
        val currSound = playerManager.currSound
        Logger.d("quicklisten", "getBundleArgs  quickListen: $quickListen")
        var playTrackType = 3
        if (quickListen && currSound is Track) {
            val trackFrom = currSound.trackFrom
            Logger.d("quicklisten", "getBundleArgs  trackFrom: $trackFrom")

            if (trackFrom > 0) {
                if (trackFrom == 4) {
                    playTrackType = 2
                } else {
                    playTrackType = trackFrom
                }
            }
        }
        return playTrackType
    }

    private fun checkNeedRefresh(): Boolean {
        val isRefreshing = mMainSwiperRefresh.isRefreshing
        if (isRefreshing) {
            // console.log('checkNeedRefresh [用户主动刷新] return true')
            return true
        }
        val playTrackType = getCurPlayTrackType()
        if (playTrackType == 3) {
            // 播放非快听声音，下次重新进入就刷新
            // console.log('checkNeedRefresh [非快听声音] return true')
            return true
        }

        // 快听声音，一小时内不刷新
        val lastRequestTime = MMKVUtil.getInstance().getLong("$QUICK_LISTEN_LAST_REQUEST_TIME_PREFIX$mTabId", 0L)
        if (Math.abs(System.currentTimeMillis() - lastRequestTime) < 1 * 3600 * 1_000) {
            // 一小时内，不刷新
            // console.log('checkNeedRefresh 一小时内，不刷新')
            return false
        }

        // 内容卡、十条卡，获取上次请求时间，如果在当天内，就不需要刷新
        val lastRequestTime2 = MMKVUtil.getInstance().getLong("$LAST_REQUEST_TIME_KEY$mTabId", 0L)
        // console.log('checkNeedRefresh s2 [十条卡] lastRequestTime (from storage) >>> ', lastRequestTime)
        if (DateTimeUtil.isAnotherDay(lastRequestTime2)) {
            // console.log('checkNeedRefresh s2 [十条卡] 不是一天，刷新.')
            return true
        }
        // console.log('checkNeedRefresh s2 [十条卡]当天，不刷新')
        return false
    }

    override fun getContainerLayoutId(): Int = R.layout.main_quick_listen_item_page_fragment

    fun getHomePageControl(): IQuickListenHomePageControl? {
        return (parentFragment as? QuickListenHomePageFragment)?.mPageControl
    }

    fun getRefreshView(): CustomSwipeRefreshLayout {
        return mMainSwiperRefresh
    }

    fun getCurrQuickListenModel(): QuickListenModel? {
        if (mAdapter.itemCount <= 0) {
            return null
        }
        return mAdapter.getItem(mMainViewPager.currentItem)
    }

    fun getCurrentItem(): Int {
        return mMainViewPager.currentItem
    }

    fun notifyCurShowChange() {
        val runnable = Runnable {
            mAdapter.notifyItemChanged(mMainViewPager.currentItem)
        }

        mNotifyCurRunnable = null
        if (mPageState == ViewPager2.SCROLL_STATE_IDLE) {
            runnable.run()
        } else {
            mNotifyCurRunnable = runnable
        }
    }

    fun notifyAdjacencyItemChange() {
        val runnable = Runnable {
            val currentItem = mMainViewPager.currentItem
            var prevItem = currentItem - 1
            if (prevItem < 0) {
                prevItem = 0
            }
            var nextItem = currentItem + 1
            if (nextItem >= mAdapter.itemCount) {
                nextItem = mAdapter.itemCount - 1
            }
            mAdapter.notifyItemRangeChanged(prevItem, nextItem - prevItem + 1)
        }

        mNotifyAdjacencyRunnable = null
        if (mPageState == ViewPager2.SCROLL_STATE_IDLE) {
            runnable.run()
        } else {
            mNotifyAdjacencyRunnable = runnable
        }
    }

    fun getQuickListenListData(): List<QuickListenModel> {
        return mAdapter.data
    }

    fun updateDataList(list: List<JSONObject>?, index: Int, tabId: Long, needPlay: Boolean, willPlayRefId: Long) {
        if (list == null || list.size <= 0) {
            return
        }
        val targetIndex = if (index >= list.size) 0 else Math.max(0, index)
        MyAsyncTask.execute {
            val listData = XimaTenDataUtil.convertDataToQuickListenModel(list)
            val tracks = if (needPlay) PlayTools.convertToCommonTrackList(list, tabId) else null
            postOnUiThread {
                mAdapter.setList(listData)
                setCurrentItem(targetIndex)
                if (needPlay) {
                    val targetRefId = QuickListenDataManager.getInstance().getPlayIndex(list, targetIndex)
                    val playIndex = tracks?.tracks?.indexOfFirst { it.dataId == targetRefId } ?: 0
                    QuickListenDataManager.getInstance().logToFile(TAG, "QuickListenDataManager willPlayRefId=$willPlayRefId, targetRefId=$targetRefId, playIndex=$playIndex")
                    PlayTools.playQuickListenListNew(tracks, playIndex, false)
                }
            }
        }
    }

    fun insertDataList(list: List<JSONObject>?) {
        if (list == null || list.size <= 0) {
            return
        }
        MyAsyncTask.execute {
            val listData = XimaTenDataUtil.convertDataToQuickListenModel(list)
            postOnUiThread {
                mAdapter.addData(listData)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        AlbumEventManage.removeListener(mCollectListener)
        LikeTrackManage.removeListener(mTrackLikeStatusListener)
        AgentRadioEventManage.removeListener(mAgentRadioCollectListener)
        TrackCollectManager.getInstance().removeListener(mTrackCollectListener)
    }

    private var mLoadingView: View? = null

    override fun getLoadingView(): View {
        var loadingView = mLoadingView
        if (loadingView != null) {
            return loadingView
        }
        loadingView = LayoutInflater.from(context)
            .inflate(R.layout.main_quick_listen_item_loading_bg_view, null, false)
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewUtil.onlySetViewPaddingOne(loadingView, BaseUtil.getStatusBarHeight(context) +
                    resourcesSafe.getDimensionPixelSize(R.dimen.host_title_bar_height), PADDING_TOP
            )
        }
        mLoadingView = loadingView
        return loadingView
    }

    private var mNoContentView: View? = null

    override fun getNoContentView(): View {
        var noContentView = mNoContentView
        if (noContentView != null) {
            return noContentView
        }
        noContentView = LayoutInflater.from(context)
            .inflate(R.layout.main_quick_listen_item_net_error_bg_view, null, false)

        if (mTabType == ELEMENT_TYPE_RECOMMEND) {
            val bgIv = noContentView.findViewById<ImageView>(R.id.main_quick_listen_error_bg_iv)
            (bgIv.layoutParams as? ViewGroup.MarginLayoutParams?)?.also {
                val height = (BaseUtil.getScreenWidth(context) / 1125f * 270f).toInt()
                it.height = if (height <= 0) 70.dp else height
                bgIv.layoutParams = it
            }
            noContentView.findViewById<View>(R.id.main_quick_listen_btn_no_net).setOnClickListener {
                loadDataImpl(true)
            }
        } else {
            val bgIv = noContentView.findViewById<ImageView>(R.id.main_quick_listen_error_bg_iv)
            noContentView.findViewById<TextView>(R.id.main_quick_listen_tv_no_net_tips)?.let {
                it.text = "暂无更多内容"
            }
            noContentView.findViewById<TextView>(R.id.main_quick_listen_tv_no_net_sub_tips)?.let {
                it.visibility = View.GONE
            }

            (bgIv.layoutParams as? ViewGroup.MarginLayoutParams?)?.also {
                val height = (BaseUtil.getScreenWidth(context) / 1125f * 270f).toInt()
                it.height = if (height <= 0) 70.dp else height
                bgIv.layoutParams = it
            }
            noContentView.findViewById<TextView>(R.id.main_quick_listen_btn_no_net)?.let {
                it.text = "去「推荐」收听"
                it.setOnClickListener {
                    getHomePageControl()?.setParentCurrentItem(0)
                }
            }
        }

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewUtil.onlySetViewPaddingOne(noContentView, BaseUtil.getStatusBarHeight(context) +
                    resourcesSafe.getDimensionPixelSize(R.dimen.host_title_bar_height), PADDING_TOP
            )
        }
        mNoContentView = noContentView
        return noContentView
    }

    private var mNetErrorView: View? = null

    override fun getNetworkErrorView(): View {
        var netErrorView = mNetErrorView
        if (netErrorView != null) {
            return netErrorView
        }
        netErrorView = LayoutInflater.from(context)
            .inflate(R.layout.main_quick_listen_item_net_error_bg_view, null, false)
        val bgIv = netErrorView.findViewById<ImageView>(R.id.main_quick_listen_error_bg_iv)
        (bgIv.layoutParams as? ViewGroup.MarginLayoutParams?)?.also {
            val height = (BaseUtil.getScreenWidth(context) / 1125f * 270f).toInt()
            it.height = if (height <= 0) 70.dp else height
            bgIv.layoutParams = it
        }
        netErrorView.findViewById<View>(R.id.main_quick_listen_btn_no_net).setOnClickListener {
            loadDataImpl(true)
        }
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewUtil.onlySetViewPaddingOne(netErrorView, BaseUtil.getStatusBarHeight(context) +
                    resourcesSafe.getDimensionPixelSize(R.dimen.host_title_bar_height), PADDING_TOP
            )
        }
        mNetErrorView = netErrorView
        return netErrorView
    }

    override fun addLoadStateView(
        parent: ViewGroup?,
        addView: View?,
        lp: LayoutParams?,
        type: LoadCompleteType?,
    ) {
        if (type == LoadCompleteType.LOADING || type == LoadCompleteType.NOCONTENT || type == LoadCompleteType.NETWOEKERROR) {
            super.addLoadStateView(
                parent,
                addView,
                LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT),
                type
            )
        } else {
            super.addLoadStateView(parent, addView, lp, type)
        }
    }

    private val mTrackLikeStatusListener = TrackLikeStatusListener { isLikeNow, trackId ->
        var needUpdate = false
        getQuickListenListData().forEach {
            if (it.isXimaTen() || it.isTrackCollect()) {
                it.subElements?.firstOrNull { it.refId == trackId }?.extraInfo?.let {
                    it.isLike = isLikeNow
                    it.likeStatus = if (isLikeNow) 1 else 0
                    needUpdate = true
                }
            } else if (it.refId == trackId) {
                it.extraInfo?.isLike = isLikeNow
                needUpdate = true
            }
        }

        if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
            notifyCurShowChange()
        }
    }

    private val mCollectListener = AlbumEventManage.CollectListener { collect, id ->
        var needUpdate = false
        getQuickListenListData().forEach {
            if (!it.isAgentRadio() && it.surElement?.refId == id) {
                it.surElement?.interact?.subscribed = if (collect) 1 else 0
                needUpdate = true
            }
        }

        if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
            notifyCurShowChange()
        }
    }

    private val mAgentRadioCollectListener: AgentRadioCollectListener = object :
        AgentRadioCollectListener {
        override fun onFail(collect: Boolean, id: Long) {

        }

        override fun onCollectChanged(collect: Boolean, id: Long) {
            var needUpdate = false
            getQuickListenListData().forEach {
                if (it.isAgentRadio() && it.surElement?.refId == id) {
                    it.surElement?.interact?.subscribed = if (collect) 1 else 0
                    needUpdate = true
                }
            }

            if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
                notifyCurShowChange()
            }
        }
    }

    private val mTrackCollectListener = TrackCollectManager.CollectListener { collect, trackId ->
        var needUpdate = false
        getQuickListenListData().forEach {
            if (it.isXimaTen() || it.isTrackCollect()) {
                it.subElements?.firstOrNull { it.refId == trackId }?.extraInfo?.let {
                    it.isCollect = collect
                    needUpdate = true
                }
            } else if (it.refId == trackId) {
                it.extraInfo?.isCollect = collect
                needUpdate = true
            }
        }

        if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
            notifyCurShowChange()
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)

        if (!isVisibleToUser && isResumed) {
            notifyAdjacencyItemChange()

            if (mAdapter.itemCount > 0) {
                QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                    getQuickListenListData().getOrNull(mMainViewPager.currentItem))
            }
        }

        if (isVisibleToUser) {
            onPageView()
        } else {
            onPageExit()
        }
    }

    fun setCurrentItem(index: Int) {
        if (mAdapter.itemCount > index) {
            this.notifyPageChange = false
            QuickListenTraceUtil.trace67649(PlayTools.getCurTrackId(ToolUtil.getCtx()),
                getQuickListenListData(), mMainViewPager.currentItem, true, true, mTabId)

            QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                getQuickListenListData().getOrNull(index))

            mMainViewPager.setCurrentItem(index, true)
            this.notifyPageChange = true
        }
    }

    fun getTabId(): Long {
        return mTabId
    }

    fun getTabTitle(): String {
        return mTabName
    }

    override fun onMyResume() {
        super.onMyResume()
        onPageView()

        if (mAdapter.itemCount > 0) {
            QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                getQuickListenListData().getOrNull(mMainViewPager.currentItem))
        }
    }

    override fun onPause() {
        super.onPause()
        if (userVisibleHint) {
            onPageExit()
        }
    }

    fun getShowFrom(): String {
        return getHomePageControl()?.getFrom() ?: ""
    }

    private var visible = false

    private fun onPageView() {
        if (!isAdded) {
            return
        }
        if (!visible) {
            QuickListenTraceUtil.trace67645(mTabId, mTabName,
                getHomePageControl()?.getFrom() ?: "", this)
        }
        visible = true
    }

    private fun onPageExit() {
        if (visible) {
            QuickListenTraceUtil.trace67646(mTabId, mTabName,
                getHomePageControl()?.getFrom() ?: "", this)
        }
        visible = false
    }

    private fun setTabTheme(isBlue: Boolean) {
        getHomePageControl()?.changeTabTheme(isBlue)

        if (BaseFragmentActivity.sIsDarkMode || isBlue) {
            if (isBlue) {
                mTitleBar.setBackgroundResource(R.color.host_color_1b4b9f)
            } else {
                mTitleBar.setBackgroundResource(R.color.host_color_222426)
            }
        } else {
            mTitleBar.setBackgroundResource(R.color.host_color_f0f1f3)
        }
    }

    override fun onPageLoadingCompleted(loadCompleteType: LoadCompleteType?) {
        if (loadCompleteType == LoadCompleteType.OK) {
            mRootLay.setBackgroundResource(R.color.host_color_d3d4d4_323232)
        } else {
            setTabTheme(false)
        }
        super.onPageLoadingCompleted(loadCompleteType)
    }
}