package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.CompoundButton;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugItemAdapter;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugType;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
public class PlayPageSkinDebug extends BaseDebugItem {
    @Override
    public String getName() {
        return "播放页皮肤";
    }

    @Override
    public DebugType getCategory() {
        return DebugType.CATEGORY_PLAYPAGE;
    }

    @Override
    boolean showToggle() {
        return false;
    }

    @Override
    boolean showArrow() {
        return false;
    }

    @Override
    public void bindView(DebugItemAdapter.BaseDebugItemViewHolder holder) {
        super.bindView(holder);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPlayPageConfigDialog();
            }
        });
    }

    private void showPlayPageConfigDialog() {
        Activity activity = BaseApplication.getTopActivity();

        IMainFunctionAction action = null;
        try {
            MainActionRouter router = Router.getActionRouter(Configure.BUNDLE_MAIN);
            if (router != null) {
                action = router.getFunctionAction();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (action != null) {
            action.startPlayPageDebugFragment();
        }
    }

    @Override
    int getIconResId() {
        return 0;
    }
}
