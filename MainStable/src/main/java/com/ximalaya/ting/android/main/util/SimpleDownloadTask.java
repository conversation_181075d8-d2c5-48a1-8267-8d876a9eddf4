package com.ximalaya.ting.android.main.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.manager.record.BaseDownloadTask;
import com.ximalaya.ting.android.host.manager.record.DownloadManager;
import com.ximalaya.ting.android.host.manager.record.TaskExecutor;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.net.HttpURLConnection;

/**
 * 通用资源下载任务
 *
 * <AUTHOR>
 */
public class SimpleDownloadTask extends BaseDownloadTask {

    private String mUrl;
    private String mFilePath;
    private final String mFileName;
    private Context mContext;
    private DownloadCallback mCallback;
    private int mRetry;
    private static final int MAX_RETRY_COUNT = 5;

    public SimpleDownloadTask(Context context, String url, String path, String name, DownloadCallback callback) {
        this.mContext = context;
        this.mCallback = callback;
        this.mUrl = url;
        this.mFilePath = path;
        this.mFileName = name;
    }

    @Override
    public String getLocalPath() {
        return this.mFilePath;
    }

    @Override
    public String getLocalName() {
        return this.mFileName;
    }

    @Override
    public String getDownloadUrl() {
        return this.mUrl;
    }

    @Override
    public boolean isRefresh() {
        return false;
    }

    @Override
    public void handleStartDownload() {

    }

    @Override
    public void handleStopDownload() {
        mCallback.onFailed();
    }


    @Override
    public void handleUpdateDownload(final long curr, final long total) {
        mCallback.onProgress((int) (curr * 100 / total));
    }

    @Override
    public void handleCompleteDownload() {
        mCallback.onSuccess();
    }

    @Override
    public void handleDownloadError(Exception e, int what, int extra) {
//        if (NetworkType.getNetWorkType(mContext) == NetworkType.NetWorkType.NETWORKTYPE_INVALID) {
            stop();
            if (mRetry < MAX_RETRY_COUNT) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
                mRetry++;
                run();
            } else {
                mCallback.onFailed();
            }
//        } else {
//            mCallback.onFailed();
//        }
    }

    public interface DownloadCallback {
        void onSuccess();

        void onFailed();

        void onProgress(int progress);
    }

    @Override
    public void run() {
        Thread.currentThread().setName("BaseDownloadTask-" + getLocalName());
        mState = STATE_RUNNING;
        mStop = false;
        RandomAccessFile file = null;
        HttpURLConnection conn = null;
        InputStream is = null;
        TaskExecutor executor = null;
        try {
            executor = DownloadManager.getInstance().getTaskExecutor();
            executor.setCurrentTask(this);
            executor.handTaskStart(this);
            handleStartDownload();
            File dir = new File(getLocalPath());
            if (dir.isFile()) {
                dir.delete();
            }
            if (!dir.exists()) {
                dir.mkdirs();
            }
            file = new RandomAccessFile(getLocalPath() + File.separator
                    + getLocalName(), "rwd");
            Logger.log("BaseDownloadTask 待保存文件" + file);
            if (isRefresh()) {
                file.setLength(0);
            }
            mDownloaded = file.length();
            IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
            Config config = null;
            if (freeFlowService != null) {
                config = freeFlowService.createConfig();
            }
            conn = FreeFlowServiceUtil.getHttpURLConnection(config, getDownloadUrl(), Config.METHOD_GET, new IFreeFlowService.ISetHttpUrlConnectAttribute() {
                @SuppressLint("DefaultLocale")
                @Override
                public void setHttpUrlConnectAttributes(@NonNull HttpURLConnection urlConnection) {
                    urlConnection.setRequestProperty("Accept", "*/*");
                    urlConnection.setRequestProperty("Range", String.format("bytes=%d-", mDownloaded));
                }
            });

            if (mStop || conn == null) {
                mState = STATE_STOPPED;
                handleStopDownload();
                executor.handTaskStop(this);
                return;
            }
            conn.connect();
            long leftLength = 0;
            int resCode = conn.getResponseCode();
            // 已下载完成的文件 再次请求会返回400？
            boolean isDeal = (resCode == 416 || resCode == 400) && mDownloaded > 0;
            if (isDeal) {
                mProgress = 100;
                mState = STATE_COMPLETED;
                handleCompleteDownload();
                executor.handTaskComplete(this);
                return;
            }
            String lenStr = conn.getHeaderField("Content-Length");
            if (TextUtils.isEmpty(lenStr)) {
                mState = STATE_FAILED;
                handleDownloadError(null, 0, 0);
                executor.handTaskFaile(this);
                return;
            }
            leftLength = Long.parseLong(lenStr);
            if (leftLength <= 0 && file.length() > 0) {
                mProgress = 100;
                mState = STATE_COMPLETED;
                handleCompleteDownload();
                executor.handTaskComplete(this);
                return;
            }
            if (leftLength <= 0 && file.length() == 0) {
                mState = STATE_FAILED;
                handleDownloadError(null, 0, 0);
                executor.handTaskFaile(this);
                return;
            }
            mTotal = leftLength + mDownloaded;
            byte[] buff = new byte[BUFF_SIZE];
            int n;
            is = conn.getInputStream();
            long last = 0;
            long pkg = (long) (mTotal * 0.02f);
            while (!mStop && (n = is.read(buff)) > 0) {
                file.seek(mDownloaded);
                file.write(buff, 0, n);
                leftLength -= n;
                mDownloaded += n;
                mProgress = (int) (100 * mDownloaded / (float) mTotal);
                handleUpdateDownload(mDownloaded, mTotal);
                if (mDownloaded - last >= pkg) {
                    last = mDownloaded;
                    executor.handTaskupdate(this);
                }
                mRetry = 0;
            }
            if (mStop && mDownloaded != mTotal) {
                mState = STATE_STOPPED;
                handleStopDownload();
                executor.handTaskStop(this);
            } else if (mDownloaded == mTotal) {
                mProgress = 100;
                mState = STATE_COMPLETED;
                handleCompleteDownload();
                executor.handTaskComplete(this);
            } else {
                mState = STATE_FAILED;
                handleDownloadError(null, ERR_FILE_SIZE_ERROR, 0);
                executor.handTaskFaile(this);
            }
        } catch (Exception e) {
            e.printStackTrace();
            mState = STATE_FAILED;
            handleDownloadError(e, 0, 0);
            if (executor != null)
                executor.handTaskFaile(this);
        } finally {
            if (file != null) {
                try {
                    file.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (conn != null) {
                conn.disconnect();
            }
            if (executor != null) {
                executor.removeCurrentTask(this);
            }
        }
    }
}
