<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    tools:background="@color/host_color_1b4b9f"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/main_point"
        android:textColor="@color/main_color_66ffffff"
        android:src="@drawable/main_quick_listen_circle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/host_y12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/main_desc_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/main_point"
        android:layout_width="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="55dp"
        android:layout_marginStart="8dp"
        android:ellipsize="end"
        android:textSize="16sp"
        android:maxLines="2"
        android:textColor="@color/host_color_ffffff"
        tools:text="啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦" />

    <TextView
        android:id="@+id/main_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@id/main_desc_title"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"
        android:includeFontPadding="false"
        android:alpha="0.65"
        android:lineSpacingExtra="7dp"
        android:layout_marginEnd="7dp"
        android:maxLines="7"
        android:minLines="3"
        android:ellipsize="end"
        android:textColor="@color/host_color_ffffff"
        app:layout_constraintTop_toBottomOf="@id/main_desc_title"
        tools:text="尽管奔驰在中国市场销量下滑，但其宣布明年追加140亿投资，显示出对中国市场的长期信心。奔驰通过持续大规模投资，推动本土化研发和生产，如北京研发中心成为全球创新双核之一，以及北京工厂的高效生产能力。此外，奔驰与华为合作开发智能驾驶系统，展现了“中国研发全。显示出对中国市场的长期信心。奔驰通过持续大规模投资，推动" />

    <LinearLayout
        android:background="@drawable/main_bg_0affffff_radius_4"
        app:layout_constraintTop_toBottomOf="@id/main_desc"
        app:layout_constraintStart_toStartOf="@id/main_desc"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/host_y18"
        android:layout_height="wrap_content" >

        <LinearLayout
            android:id="@+id/main_like_lay"
            android:layout_width="wrap_content"
            android:paddingTop="8dp"
            android:paddingStart="8dp"
            android:paddingBottom="8dp"
            android:gravity="center"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/main_like"
                android:src="@drawable/main_quick_listen_like_selector"
                android:tint="@color/host_color_ffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/main_like_title"
                android:text="点赞"
                android:textSize="11sp"
                android:alpha="0.6"
                android:paddingEnd="8dp"
                android:includeFontPadding="false"
                android:textColor="@color/host_color_ffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <View
                android:layout_width="1px"
                android:background="@color/host_color_ffffff"
                android:alpha="0.2"
                android:layout_height="11sp">
            </View>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/main_unlike_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="8dp">

            <ImageView
                android:id="@+id/main_unlike"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/main_quick_listen_unlike_selector" />

            <TextView
                android:id="@+id/main_unlike_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.6"
                android:includeFontPadding="false"
                android:text="我不喜欢"
                android:textColor="@color/host_color_ffffff"
                android:textSize="11sp" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>