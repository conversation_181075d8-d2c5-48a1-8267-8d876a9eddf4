def pluginEnable = true
def pluginEnableInDebug = true
def pluginLogLevel = "DEBUG"

//Properties localProperties = new Properties()
//localProperties.load(rootProject.file('local.properties').newDataInputStream())
//def bf = localProperties.getProperty("buildFast")
//
//if (buildFast.to) {
pluginEnable = !(buildFast.toBoolean()) && !(closeNormalPlugin.toBoolean())
//}

apply from: rootProject.rootDir.absolutePath + '/config_plugin.gradle'
apply plugin: 'bytex'
ByteX {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    //是否需要让ByteX构建增量编译的graph缓存(生成在$project/app/build/ByteX/variant/graphCache.json路径下)，
    //对于release构建它通常是没有用的，而且耗时耗内存，甚至会造成oom问题(抖音大约需要最少300MB的内存耗费)
    //每个插件都可以单独配置这个开关，如果多个插件是联合在一块执行，则该值用个插件值进行与操作(& and),默认true
    //whether to let ByteX generate graph cache(locate at $project/app/build/ByteX/variant/graphCache.json)
    //which is used by incremental build .It is useless while release build, it costs much time and memory,
    //and may even cause oom.
    //Each plugin can configure this switch separately. If multiple plugins are executed jointly, the final
    // value is ANDed with each plugin value.True by default
    shouldSaveCache true
    logLevel pluginLogLevel
}


def commonWhitelist = [
        'com/ximalaya/ting/android/object/monitor',
        'com/ximalaya/xiaoya/sdk',
        'com/ximalaya/ting/android/xmloader',
        'com/alipay/apmobilesecuritysdk/common',
        'com/ximalaya/ting/android/xmevilmethodmonitor',
        'com/ximalaya/ting/android/xmuimonitorbase',
        'com/ximalaya/ting/android/preciseye/csj/aspect/CSJSplashAspect',
        'com/ximalaya/ting/android/preciseye/csj/aspect/CSJFullVideoAspect',
        "com/ximalaya/ting/android/preciseye/csj/aspect/CSJNativeExpressAspect",
        'com/ximalaya/ting/android/preciseye/csj/aspect/CSJRewardVideoAspect',
        'com/bytedance/frameworks/baselib/network/http/ok3/impl'
]

apply plugin: 'auto-track'
auto_track {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel
    enableTracePoint isOpenPointSdk.toBoolean()

    whiteList = []
    whiteList.addAll(commonWhitelist)

    // 打点库需要剔除自己的点击事件，不然不能点击了
    pointWhitelist = [
            'com/xmly/kshdebug',
    ]

}


apply plugin: 'firework-plugin'
firework {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)
}

apply plugin: 'layout-inflater-plugin'
layout_inflater {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = [
            'com/ximalaya/commonaspectj/LayoutInflaterAgent'
    ]
    whiteList.addAll(commonWhitelist)

}

apply plugin: 'view-monitor-plugin'
view_monitor {
    // todo 记得关掉
    enable false
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = [
            'com/ximalaya/ting/android/xmbattery/monitor/feature/ViewOperationAgent'
    ]
    whiteList.addAll(commonWhitelist)

}

apply plugin: 'thread-monitor-plugin'
thread_monitor {
    // todo 记得关掉
    enable false
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    printLog = true
    enableThreadPoolOptimized = true
    enableScheduleThreadPoolOptimized = false

    whiteList = []
    whiteList.addAll(commonWhitelist)
}

apply plugin: 'remote-log-plugin'
remote_log {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel
    include = [
            'com/ximalaya/'
    ]
    whiteList = []
    whiteList.addAll(commonWhitelist)

}

apply plugin: 'net-monitor-plugin'
net_monitor {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = [
            'com/ximalaya/ting/android/object/monitor',
//            'com/ximalaya/xiaoya/sdk',
//            'com/ximalaya/ting/android/xmloader',
            'com/alipay/apmobilesecuritysdk/common',
//            'com/ximalaya/ting/android/xmevilmethodmonitor',
            'com/ximalaya/ting/android/xmuimonitorbase',
            'com/alibaba/security/realidentity/build/kb',
            'com/ximalaya/ting/android/xmnetmonitor/core/NetworkMonitorAspect', // 剔除它本身的调用
            'com/bytedance/frameworks/baselib/network/http/ok3/impl',
    ]

}

apply plugin: 'open-sdk-okhttp-plugin'
open_sdk {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = [
            'com/ximalaya/ting/android/opensdk/httputil/DnsAspect',
            'com/ximalaya/ting/android/opensdk/httputil/SSLAspect'
    ]
    whiteList.addAll(commonWhitelist)

}

apply plugin: 'cpu-monitor-plugin'
cpu_monitor {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)

}


apply plugin: 'startup-monitor-plugin'
startup_monitor {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)

    whiteList.add("okhttp3/")
    whiteList.add("androidx/")
}

apply plugin: 'fragment-trace-plugin'
fragment_trace {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)

}

apply plugin: 'xm-class-hook-plugin'
xm_class_hook {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)
}


apply plugin: 'check-res-plugin'
check_base_res {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel
    replace false

    whiteList = []
    whiteList.addAll(commonWhitelist)
    String basePack = "com/ximalaya/ting/android/"
    // 注意后面不要跟 "/"
    include = [
    ]


    Properties localProperties = new Properties();
    localProperties.load(project.rootProject.file('local.properties').newDataInputStream())
    def dy = []
    def buildSoBundles = []
    def localBuildSo = localProperties.getProperty("BUILD_SO_BUNDLE", null)
    if (localBuildSo != null) {// 有local 时，说明是本地打包
        buildSoBundles = Arrays.asList(localBuildSo.split(","))
    } else {// 没有local 时，线上打包
        buildSoBundles = Arrays.asList(BUILD_TO_SO_BUNDLES.split(","))
    }
    //
    if (rootProject.hasProperty("pBundles")) {
        buildSoBundles = []
        def bds = rootProject.property("pBundles").toString()
        if (bds != "") {
            buildSoBundles.addAll(Arrays.asList(bds.toString().split(",")))
        }
    }
    def selectedBundles = []
    def soBundle = [] // 将编译成so 剔除来
    if (project.hasProperty("buildBundles")) {
        selectedBundles.addAll(Arrays.asList(project.property("buildBundles").toString().split(",")))
        if (selectedBundles.size() > 0) {
            def soBundles = Arrays.asList(BUILD_TO_SO_BUNDLES.split(","))
            selectedBundles.each {
                if (soBundles.contains(it)) {
                    soBundle.add(it)
                }
            }
        }
    }

    if (soBundle.size() > 0) {
        buildSoBundles = soBundle
    }
//
//    BUILD_TO_SO_BUNDLES.split(",").each { String name ->
//        def n = "${basePack}${name}"
//        include.add(n.toString())
//    }

    if (buildSoBundles.size() > 0) {
        buildSoBundles.each {
            def n = "${basePack}${it.toString()}"
            dy.add(n)
        }
    }

    dy.each {
        include.add(it.toString())
    }

    include.add("com/io/tl/android")
    include.add("b/a/a/a")

    include.each {
        println("xxx: " + it)
    }

    // 项目跟目录
    resFilePath = 'TingMainHost/TingMainApp/constant-resource/app-public.txt'
}

apply plugin: 'bytedance-debug-plugin'
bytedance_debug {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)
}

apply plugin: 'bytedance-sdk-ad-plugin'
bytedance_ad_plugin {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = []
    whiteList.addAll(commonWhitelist)
}

apply plugin: 'telephone-manager-plugin'
telephone_manager_call {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = [
            "com/ximalaya/ting/android/host/util/common/TelephonyManagerAspectJ"
    ]

//    whiteList.addAll(commonWhitelist)
}

apply plugin: 'webview-plugin'
webviewExtension {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel
    replaceWebViewClientClazz "com/ximalaya/ting/android/xmutil/webview/FixCrashWebViewClient"
    defaultWebViewClazz "com/ximalaya/ting/android/xmutil/webview/WebViewAutoSetWebClient"
    webViewLoadUrlListenerClazz "com/ximalaya/ting/android/xmutil/webview/WebViewLoadUrlListener"
    userAgentClazz "com/ximalaya/ting/android/host/util/fixWebview/WebUtil"
    userAgentClazzFilter = ["com/ximalaya/ting/android/host/util/fixWebview/WebUtil"]
}

apply plugin: 'httpconnection-plugin'
HttpConnectionHookExtension {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel

    whiteList = [
            "com/ximalaya/ting/android/xmnetmonitor/urlconnection/HttpConnectionHook"
    ]
    whiteList.addAll(commonWhitelist)
}

apply plugin: 'bytex.sourcefile'
SourceFile {
    enable pluginEnable
    enableInDebug pluginEnableInDebug
    logLevel pluginLogLevel
    deleteSourceFile true // whether to delete SourceFile info
    deleteLineNumber true // whether to delete LineNumber info
}

apply plugin: 'bytex.shrink_r_class'
shrinkR {
    enable true
    enableInDebug false
    logLevel "DEBUG"
    keepList = [
            // keep android.support.constraint.R里所有id
            "android.support.constraint.R.id",
            // keep 所有以im_e为前缀的drawable字段
            "R.drawable.im_e+",
//            "com.ximalaya.ting.android.adsdk.businessshell.R.styleable",
            "com.ximalaya.ting.android.adsdk.businessshell.R",
            "com.ximalaya.ting.android.adsdk.businessshell.R.*",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$layout",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$string",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$drawable",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$style",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$id",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$xml",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$color",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$array",
            "com.ximalaya.ting.android.adsdk.businessshell.R\$styleable",

            "com.android.internal.R\$styleable",
            "com.android.internal.R\$dimen",
            "com.android.internal.R\$color",
            "com.android.internal.R\$id",

            "android.R\$styleable",
            "com.xmly.apk.R\$layout",

            "com.tencent.qqmini.R\$anim",
            "com.tencent.qqmini.R\$attr",
            "com.tencent.qqmini.R\$bool",
            "com.tencent.qqmini.R\$color",
            "com.tencent.qqmini.R\$dimen",
            "com.tencent.qqmini.R\$drawable",
            "com.tencent.qqmini.R\$id",
            "com.tencent.qqmini.R\$integer",
            "com.tencent.qqmini.R\$interpolator",
            "com.tencent.qqmini.R\$layout",
            "com.tencent.qqmini.R\$string",
            "com.tencent.qqmini.R\$style",
            "com.tencent.qqmini.R\$styleable",
            "com.tencent.qqmini.R\$xml",
            "com.tencent.qqmini.R",
            "com.tencent.qqmini.minigame.external.R\$anim",
            "com.tencent.qqmini.minigame.external.R\$attr",
            "com.tencent.qqmini.minigame.external.R\$bool",
            "com.tencent.qqmini.minigame.external.R\$color",
            "com.tencent.qqmini.minigame.external.R\$dimen",
            "com.tencent.qqmini.minigame.external.R\$drawable",
            "com.tencent.qqmini.minigame.external.R\$id",
            "com.tencent.qqmini.minigame.external.R\$integer",
            "com.tencent.qqmini.minigame.external.R\$interpolator",
            "com.tencent.qqmini.minigame.external.R\$layout",
            "com.tencent.qqmini.minigame.external.R\$string",
            "com.tencent.qqmini.minigame.external.R\$style",
            "com.tencent.qqmini.minigame.external.R\$styleable",
            "com.tencent.qqmini.minigame.external.R\$xml",
            "com.tencent.qqmini.minigame.external.R",
            "com.tencent.qqmini.minigame.opensdk.R\$anim",
            "com.tencent.qqmini.minigame.opensdk.R\$attr",
            "com.tencent.qqmini.minigame.opensdk.R\$bool",
            "com.tencent.qqmini.minigame.opensdk.R\$color",
            "com.tencent.qqmini.minigame.opensdk.R\$dimen",
            "com.tencent.qqmini.minigame.opensdk.R\$drawable",
            "com.tencent.qqmini.minigame.opensdk.R\$id",
            "com.tencent.qqmini.minigame.opensdk.R\$integer",
            "com.tencent.qqmini.minigame.opensdk.R\$interpolator",
            "com.tencent.qqmini.minigame.opensdk.R\$layout",
            "com.tencent.qqmini.minigame.opensdk.R\$string",
            "com.tencent.qqmini.minigame.opensdk.R\$style",
            "com.tencent.qqmini.minigame.opensdk.R\$styleable",
            "com.tencent.qqmini.minigame.opensdk.R\$xml",
            "com.tencent.qqmini.minigame.opensdk.R",
            "com.tencent.qqmini.union.ad.R\$anim",
            "com.tencent.qqmini.union.ad.R\$attr",
            "com.tencent.qqmini.union.ad.R\$bool",
            "com.tencent.qqmini.union.ad.R\$color",
            "com.tencent.qqmini.union.ad.R\$dimen",
            "com.tencent.qqmini.union.ad.R\$drawable",
            "com.tencent.qqmini.union.ad.R\$id",
            "com.tencent.qqmini.union.ad.R\$integer",
            "com.tencent.qqmini.union.ad.R\$interpolator",
            "com.tencent.qqmini.union.ad.R\$layout",
            "com.tencent.qqmini.union.ad.R\$string",
            "com.tencent.qqmini.union.ad.R\$style",
            "com.tencent.qqmini.union.ad.R\$styleable",
            "com.tencent.qqmini.union.ad.R\$xml",
            "com.tencent.qqmini.union.ad.R",

            "com.tencent.qqmini.minigame.R",
            "com.tencent.qqmini.minigame.R\$anim",
            "com.tencent.qqmini.minigame.R\$attr",
            "com.tencent.qqmini.minigame.R\$bool",
            "com.tencent.qqmini.minigame.R\$color",
            "com.tencent.qqmini.minigame.R\$dimen",
            "com.tencent.qqmini.minigame.R\$drawable",
            "com.tencent.qqmini.minigame.R\$id",
            "com.tencent.qqmini.minigame.R\$layout",
            "com.tencent.qqmini.minigame.R\$string",
            "com.tencent.qqmini.minigame.R\$style",
            "com.tencent.qqmini.minigame.R\$styleable",
            "com.tencent.qqmini.minigame.R\$xml",

            "com.tencent.qqmini.mmkv.R",

            "com.tencent.qqmini.sdk.launcher.R",
            "com.tencent.qqmini.sdk.launcher.R\$anim",
            "com.tencent.qqmini.sdk.launcher.R\$attr",
            "com.tencent.qqmini.sdk.launcher.R\$drawable",
            "com.tencent.qqmini.sdk.launcher.R\$id",
            "com.tencent.qqmini.sdk.launcher.R\$layout",
            "com.tencent.qqmini.sdk.launcher.R\$string",
            "com.tencent.qqmini.sdk.launcher.R\$style",
            "com.tencent.qqmini.sdk.launcher.R\$styleable",

            "com.tencent.qqmini.sdk.proto.R",
            "com.tencent.qqmini.sdk.proto.R\$styleable",

            "com.tencent.qqmini.sdk.server.R",
            "com.tencent.qqmini.sdk.server.R\$anim",
            "com.tencent.qqmini.sdk.server.R\$attr",
            "com.tencent.qqmini.sdk.server.R\$color",
            "com.tencent.qqmini.sdk.server.R\$dimen",
            "com.tencent.qqmini.sdk.server.R\$drawable",
            "com.tencent.qqmini.sdk.server.R\$id",
            "com.tencent.qqmini.sdk.server.R\$layout",
            "com.tencent.qqmini.sdk.server.R\$string",
            "com.tencent.qqmini.sdk.server.R\$style",
            "com.tencent.qqmini.sdk.server.R\$styleable",
            "com.tencent.qqmini.sdk.server.R\$xml",

            "com.tencent.qqmini.sdk.R",
            "com.tencent.qqmini.sdk.R\$anim",
            "com.tencent.qqmini.sdk.R\$attr",
            "com.tencent.qqmini.sdk.R\$color",
            "com.tencent.qqmini.sdk.R\$dimen",
            "com.tencent.qqmini.sdk.R\$drawable",
            "com.tencent.qqmini.sdk.R\$id",
            "com.tencent.qqmini.sdk.R\$layout",
            "com.tencent.qqmini.sdk.R\$string",
            "com.tencent.qqmini.sdk.R\$style",
            "com.tencent.qqmini.sdk.R\$styleable",
            "com.tencent.qqmini.sdk.R\$xml",

            "com.leto.game.sdk.R",
            "com.leto.game.sdk.R\$layout",
            "com.leto.game.sdk.R\$id",
            "com.leto.game.sdk.R\$style",
            "com.leto.game.sdk.R\$drawable",
            "com.leto.game.sdk.R\$color",
            "com.leto.game.sdk.R\$attr",
            "com.leto.game.sdk.R\$string",
            "com.leto.game.sdk.R\$dimen",
            "com.leto.game.sdk.R\$attr",
            "com.leto.game.sdk.R\$xml",
            "com.leto.game.sdk.R\$raw",
            "com.leto.game.sdk.R\$styleable",
            "com.leto.game.sdk.R\$menu",

            "com.bun.miitmdid.R\$id",
            "com.bun.miitmdid.R\$style",
            "com.bun.miitmdid.R\$styleable",
            "com.bun.miitmdid.R\$layout",
            "com.bun.miitmdid.R\$interpolator",
            "com.bun.miitmdid.R\$drawable",
            "com.bun.miitmdid.R\$color",
            "com.bun.miitmdid.R\$bool",
            "com.bun.miitmdid.R\$attr",
            "com.bun.miitmdid.R\$string",

            "com.mcto.sspsdk.R\$id",

            "com.ximalaya.ting.android.R\$layout",
            "com.xmly.apk.R\$layout",
            "androidx.constraintlayout.widget.R\$id",

            "com.ximalaya.ting.android.hybridview.R",
            "com.ximalaya.ting.android.hybridview.R\$styleable",

            // 兜底
            "com.io.tl.android.R",
            "com.io.tl.android.R\$bool",
            "com.io.tl.android.R\$color",
            "org.chromium.net.so.R",
            "com.smartdevicelink.proxy.ex.R\$drawable",
            "cn.com.spdb.spdbpay.R",
            "com.jdpaysdk.author.R\$string",
            "com.jdpaysdk.author.R\$anim",
            "com.io.tl.android.R\$string",
            "com.jdpaysdk.author.R\$dimen",
            "com.bytedance.frameworks.baselib.network.R\$dimen",
            "com.bytedance.speech.speechengine.R\$layout",

            "com.airbnb.viewmodeladapter.R\$anim",
            "com.airbnb.viewmodeladapter.R\$animator",
            "com.airbnb.viewmodeladapter.R\$attr",
            "com.airbnb.viewmodeladapter.R\$bool",
            "com.airbnb.viewmodeladapter.R\$color",
            "com.airbnb.viewmodeladapter.R\$dimen",
            "com.airbnb.viewmodeladapter.R\$drawable",
            "com.airbnb.viewmodeladapter.R\$id",
            "com.airbnb.viewmodeladapter.R\$integer",
            "com.airbnb.viewmodeladapter.R\$interpolator",
            "com.airbnb.viewmodeladapter.R\$layout",
            "com.airbnb.viewmodeladapter.R\$plurals",
            "com.airbnb.viewmodeladapter.R\$string",
            "com.airbnb.viewmodeladapter.R\$style",
            "com.airbnb.viewmodeladapter.R\$styleable",
            "com.airbnb.viewmodeladapter.R\$xml",
            "com.airbnb.viewmodeladapter.R",

            "com.tmall.wireless.tangram.core.R\$anim",
            "com.tmall.wireless.tangram.core.R\$animator",
            "com.tmall.wireless.tangram.core.R\$attr",
            "com.tmall.wireless.tangram.core.R\$bool",
            "com.tmall.wireless.tangram.core.R\$color",
            "com.tmall.wireless.tangram.core.R\$dimen",
            "com.tmall.wireless.tangram.core.R\$drawable",
            "com.tmall.wireless.tangram.core.R\$id",
            "com.tmall.wireless.tangram.core.R\$integer",
            "com.tmall.wireless.tangram.core.R\$interpolator",
            "com.tmall.wireless.tangram.core.R\$layout",
            "com.tmall.wireless.tangram.core.R\$menu",
            "com.tmall.wireless.tangram.core.R\$plurals",
            "com.tmall.wireless.tangram.core.R\$string",
            "com.tmall.wireless.tangram.core.R\$style",
            "com.tmall.wireless.tangram.core.R\$styleable",
            "com.tmall.wireless.tangram.core.R\$xml",
            "com.tmall.wireless.tangram.core.R",

            "com.flyco.tablayout.R\$anim",
            "com.flyco.tablayout.R\$animator",
            "com.flyco.tablayout.R\$attr",
            "com.flyco.tablayout.R\$bool",
            "com.flyco.tablayout.R\$color",
            "com.flyco.tablayout.R\$dimen",
            "com.flyco.tablayout.R\$drawable",
            "com.flyco.tablayout.R\$id",
            "com.flyco.tablayout.R\$integer",
            "com.flyco.tablayout.R\$interpolator",
            "com.flyco.tablayout.R\$layout",
            "com.flyco.tablayout.R\$plurals",
            "com.flyco.tablayout.R\$string",
            "com.flyco.tablayout.R\$style",
            "com.flyco.tablayout.R\$styleable",
            "com.flyco.tablayout.R\$xml",
            "com.flyco.tablayout.R",

            "com.google.android.material.R\$anim",
            "com.google.android.material.R\$animator",
            "com.google.android.material.R\$attr",
            "com.google.android.material.R\$bool",
            "com.google.android.material.R\$color",
            "com.google.android.material.R\$dimen",
            "com.google.android.material.R\$drawable",
            "com.google.android.material.R\$id",
            "com.google.android.material.R\$integer",
            "com.google.android.material.R\$interpolator",
            "com.google.android.material.R\$layout",
            "com.google.android.material.R\$plurals",
            "com.google.android.material.R\$string",
            "com.google.android.material.R\$style",
            "com.google.android.material.R\$styleable",
            "com.google.android.material.R\$xml",
            "com.google.android.material.R",

            "com.yalantis.ucrop.R\$anim",
            "com.yalantis.ucrop.R\$attr",
            "com.yalantis.ucrop.R\$bool",
            "com.yalantis.ucrop.R\$color",
            "com.yalantis.ucrop.R\$dimen",
            "com.yalantis.ucrop.R\$drawable",
            "com.yalantis.ucrop.R\$id",
            "com.yalantis.ucrop.R\$integer",
            "com.yalantis.ucrop.R\$interpolator",
            "com.yalantis.ucrop.R\$layout",
            "com.yalantis.ucrop.R\$menu",
            "com.yalantis.ucrop.R\$string",
            "com.yalantis.ucrop.R\$style",
            "com.yalantis.ucrop.R\$styleable",
            "com.yalantis.ucrop.R",




            "androidx.constraintlayout.widget.R\$anim",
            "androidx.constraintlayout.widget.R\$attr",
            "androidx.constraintlayout.widget.R\$bool",
            "androidx.constraintlayout.widget.R\$color",
            "androidx.constraintlayout.widget.R\$dimen",
            "androidx.constraintlayout.widget.R\$drawable",
            "androidx.constraintlayout.widget.R\$integer",
            "androidx.constraintlayout.widget.R\$interpolator",
            "androidx.constraintlayout.widget.R\$layout",
            "androidx.constraintlayout.widget.R\$string",
            "androidx.constraintlayout.widget.R\$style",
            "androidx.constraintlayout.widget.R\$styleable",
            "androidx.constraintlayout.widget.R",



            "com.lihang.R\$anim",
            "com.lihang.R\$attr",
            "com.lihang.R\$bool",
            "com.lihang.R\$color",
            "com.lihang.R\$dimen",
            "com.lihang.R\$drawable",
            "com.lihang.R\$id",
            "com.lihang.R\$integer",
            "com.lihang.R\$interpolator",
            "com.lihang.R\$layout",
            "com.lihang.R\$string",
            "com.lihang.R\$style",
            "com.lihang.R\$styleable",
            "com.lihang.R",
            "com.lihang.R\$c",





            "com.dinuscxj.progressbar.R\$anim",
            "com.dinuscxj.progressbar.R\$attr",
            "com.dinuscxj.progressbar.R\$bool",
            "com.dinuscxj.progressbar.R\$color",
            "com.dinuscxj.progressbar.R\$dimen",
            "com.dinuscxj.progressbar.R\$drawable",
            "com.dinuscxj.progressbar.R\$id",
            "com.dinuscxj.progressbar.R\$integer",
            "com.dinuscxj.progressbar.R\$interpolator",
            "com.dinuscxj.progressbar.R\$layout",
            "com.dinuscxj.progressbar.R\$string",
            "com.dinuscxj.progressbar.R\$style",
            "com.dinuscxj.progressbar.R\$styleable",
            "com.dinuscxj.progressbar.R",
            "com.tmall.wireless.virtualview.R\$anim",
            "com.tmall.wireless.virtualview.R\$attr",
            "com.tmall.wireless.virtualview.R\$bool",
            "com.tmall.wireless.virtualview.R\$color",
            "com.tmall.wireless.virtualview.R\$dimen",
            "com.tmall.wireless.virtualview.R\$drawable",
            "com.tmall.wireless.virtualview.R\$id",
            "com.tmall.wireless.virtualview.R\$integer",
            "com.tmall.wireless.virtualview.R\$interpolator",
            "com.tmall.wireless.virtualview.R\$layout",
            "com.tmall.wireless.virtualview.R\$string",
            "com.tmall.wireless.virtualview.R\$style",
            "com.tmall.wireless.virtualview.R\$styleable",
            "com.tmall.wireless.virtualview.R",
            "com.alibaba.gaiax.adapter.R\$anim",
            "com.alibaba.gaiax.adapter.R\$attr",
            "com.alibaba.gaiax.adapter.R\$bool",
            "com.alibaba.gaiax.adapter.R\$color",
            "com.alibaba.gaiax.adapter.R\$dimen",
            "com.alibaba.gaiax.adapter.R\$drawable",
            "com.alibaba.gaiax.adapter.R\$id",
            "com.alibaba.gaiax.adapter.R\$integer",
            "com.alibaba.gaiax.adapter.R\$layout",
            "com.alibaba.gaiax.adapter.R\$string",
            "com.alibaba.gaiax.adapter.R\$style",
            "com.alibaba.gaiax.adapter.R\$styleable",
            "com.alibaba.gaiax.adapter.R",




            "com.youzan.androidsdk.basic.R\$attr",
            "com.youzan.androidsdk.basic.R\$bool",
            "com.youzan.androidsdk.basic.R\$color",
            "com.youzan.androidsdk.basic.R\$dimen",
            "com.youzan.androidsdk.basic.R\$drawable",
            "com.youzan.androidsdk.basic.R\$id",
            "com.youzan.androidsdk.basic.R\$integer",
            "com.youzan.androidsdk.basic.R\$layout",
            "com.youzan.androidsdk.basic.R\$string",
            "com.youzan.androidsdk.basic.R\$style",
            "com.youzan.androidsdk.basic.R\$styleable",
            "com.youzan.androidsdk.basic.R",




            "com.xmly.android.abcpaymodule.R\$anim",
            "com.xmly.android.abcpaymodule.R\$attr",
            "com.xmly.android.abcpaymodule.R\$bool",
            "com.xmly.android.abcpaymodule.R\$color",
            "com.xmly.android.abcpaymodule.R\$dimen",
            "com.xmly.android.abcpaymodule.R\$drawable",
            "com.xmly.android.abcpaymodule.R\$id",
            "com.xmly.android.abcpaymodule.R\$integer",
            "com.xmly.android.abcpaymodule.R\$interpolator",
            "com.xmly.android.abcpaymodule.R\$layout",
            "com.xmly.android.abcpaymodule.R\$string",
            "com.xmly.android.abcpaymodule.R\$style",
            "com.xmly.android.abcpaymodule.R\$styleable",
            "com.xmly.android.abcpaymodule.R",
            "com.xmly.android.spdbpaymodule.R\$anim",
            "com.xmly.android.spdbpaymodule.R\$attr",
            "com.xmly.android.spdbpaymodule.R\$bool",
            "com.xmly.android.spdbpaymodule.R\$color",
            "com.xmly.android.spdbpaymodule.R\$dimen",
            "com.xmly.android.spdbpaymodule.R\$drawable",
            "com.xmly.android.spdbpaymodule.R\$id",
            "com.xmly.android.spdbpaymodule.R\$integer",
            "com.xmly.android.spdbpaymodule.R\$interpolator",
            "com.xmly.android.spdbpaymodule.R\$layout",
            "com.xmly.android.spdbpaymodule.R\$string",
            "com.xmly.android.spdbpaymodule.R\$style",
            "com.xmly.android.spdbpaymodule.R\$styleable",
            "com.xmly.android.spdbpaymodule.R",
            "com.xmly.android.bocpaymodule.R\$anim",
            "com.xmly.android.bocpaymodule.R\$attr",
            "com.xmly.android.bocpaymodule.R\$bool",
            "com.xmly.android.bocpaymodule.R\$color",
            "com.xmly.android.bocpaymodule.R\$dimen",
            "com.xmly.android.bocpaymodule.R\$drawable",
            "com.xmly.android.bocpaymodule.R\$id",
            "com.xmly.android.bocpaymodule.R\$integer",
            "com.xmly.android.bocpaymodule.R\$interpolator",
            "com.xmly.android.bocpaymodule.R\$layout",
            "com.xmly.android.bocpaymodule.R\$string",
            "com.xmly.android.bocpaymodule.R\$style",
            "com.xmly.android.bocpaymodule.R\$styleable",
            "com.xmly.android.bocpaymodule.R",




            "com.huawei.android.hms.push.R\$color",
            "com.huawei.android.hms.push.R\$id",
            "com.huawei.android.hms.push.R\$layout",
            "com.huawei.android.hms.push.R\$string",
            "com.huawei.android.hms.push.R\$style",
            "com.huawei.android.hms.push.R",
            "com.huawei.android.hms.openid.R\$color",
            "com.huawei.android.hms.openid.R\$id",
            "com.huawei.android.hms.openid.R\$layout",
            "com.huawei.android.hms.openid.R\$string",
            "com.huawei.android.hms.openid.R\$style",
            "com.huawei.android.hms.openid.R",
            "com.huawei.android.hms.base.R\$color",
            "com.huawei.android.hms.base.R\$id",
            "com.huawei.android.hms.base.R\$layout",
            "com.huawei.android.hms.base.R\$string",
            "com.huawei.android.hms.base.R\$style",
            "com.huawei.android.hms.base.R",
            "com.huawei.hms.base.availableupdate.R\$color",
            "com.huawei.hms.base.availableupdate.R\$id",
            "com.huawei.hms.base.availableupdate.R\$layout",
            "com.huawei.hms.base.availableupdate.R\$string",
            "com.huawei.hms.base.availableupdate.R\$style",
            "com.huawei.hms.base.availableupdate.R",
            "com.huawei.hms.base.device.R",
            "com.huawei.agconnect.core.R",


            "com.dooboolab.audiorecorderplayer.R\$anim",
            "com.dooboolab.audiorecorderplayer.R\$animator",
            "com.dooboolab.audiorecorderplayer.R\$attr",
            "com.dooboolab.audiorecorderplayer.R\$bool",
            "com.dooboolab.audiorecorderplayer.R\$color",
            "com.dooboolab.audiorecorderplayer.R\$dimen",
            "com.dooboolab.audiorecorderplayer.R\$drawable",
            "com.dooboolab.audiorecorderplayer.R\$id",
            "com.dooboolab.audiorecorderplayer.R\$integer",
            "com.dooboolab.audiorecorderplayer.R\$interpolator",
            "com.dooboolab.audiorecorderplayer.R\$layout",
            "com.dooboolab.audiorecorderplayer.R\$plurals",
            "com.dooboolab.audiorecorderplayer.R\$string",
            "com.dooboolab.audiorecorderplayer.R\$style",
            "com.dooboolab.audiorecorderplayer.R\$styleable",
            "com.dooboolab.audiorecorderplayer.R\$xml",
            "com.dooboolab.audiorecorderplayer.R",




            "me.relex.photodraweeview.R\$attr",
            "me.relex.photodraweeview.R\$color",
            "me.relex.photodraweeview.R\$dimen",
            "me.relex.photodraweeview.R\$drawable",
            "me.relex.photodraweeview.R\$id",
            "me.relex.photodraweeview.R\$integer",
            "me.relex.photodraweeview.R\$layout",
            "me.relex.photodraweeview.R\$string",
            "me.relex.photodraweeview.R\$style",
            "me.relex.photodraweeview.R\$styleable",
            "me.relex.photodraweeview.R",


            "com.youzan.androidsdk.R\$attr",
            "com.youzan.androidsdk.R\$color",
            "com.youzan.androidsdk.R\$dimen",
            "com.youzan.androidsdk.R\$drawable",
            "com.youzan.androidsdk.R\$id",
            "com.youzan.androidsdk.R\$integer",
            "com.youzan.androidsdk.R\$layout",
            "com.youzan.androidsdk.R\$string",
            "com.youzan.androidsdk.R\$style",
            "com.youzan.androidsdk.R\$styleable",
            "com.youzan.androidsdk.R",


            "com.tencent.mm.opensdk.R",




            "com.sina.util.dnscache.R\$id",
            "com.sina.util.dnscache.R\$layout",
            "com.sina.util.dnscache.R\$menu",
            "com.sina.util.dnscache.R\$string",
            "com.sina.util.dnscache.R",
            "com.meituan.android.walle.R",
            "com.unionpaysdk.R",
            "com.ccbsdk.R\$anim",
            "com.ccbsdk.R\$attr",
            "com.ccbsdk.R\$bool",
            "com.ccbsdk.R\$color",
            "com.ccbsdk.R\$dimen",
            "com.ccbsdk.R\$drawable",
            "com.ccbsdk.R\$id",
            "com.ccbsdk.R\$integer",
            "com.ccbsdk.R\$layout",
            "com.ccbsdk.R\$mipmap",
            "com.ccbsdk.R\$string",
            "com.ccbsdk.R\$style",
            "com.ccbsdk.R\$styleable",
            "com.ccbsdk.R\$xml",
            "com.ccbsdk.R",
            "com.bytedance.hume.readapk.R",
            "com.gaoding.editor.image.R\$anim",
            "com.gaoding.editor.image.R\$attr",
            "com.gaoding.editor.image.R\$bool",
            "com.gaoding.editor.image.R\$color",
            "com.gaoding.editor.image.R\$dimen",
            "com.gaoding.editor.image.R\$drawable",
            "com.gaoding.editor.image.R\$id",
            "com.gaoding.editor.image.R\$integer",
            "com.gaoding.editor.image.R\$interpolator",
            "com.gaoding.editor.image.R\$layout",
            "com.gaoding.editor.image.R\$raw",
            "com.gaoding.editor.image.R\$string",
            "com.gaoding.editor.image.R\$style",
            "com.gaoding.editor.image.R\$styleable",
            "com.gaoding.editor.image.R",
            "com.bytedance.sdk.openadsdk.R\$anim",
            "com.bytedance.sdk.openadsdk.R\$attr",
            "com.bytedance.sdk.openadsdk.R\$bool",
            "com.bytedance.sdk.openadsdk.R\$color",
            "com.bytedance.sdk.openadsdk.R\$dimen",
            "com.bytedance.sdk.openadsdk.R\$drawable",
            "com.bytedance.sdk.openadsdk.R\$id",
            "com.bytedance.sdk.openadsdk.R\$integer",
            "com.bytedance.sdk.openadsdk.R\$layout",
            "com.bytedance.sdk.openadsdk.R\$mipmap",
            "com.bytedance.sdk.openadsdk.R\$raw",
            "com.bytedance.sdk.openadsdk.R\$string",
            "com.bytedance.sdk.openadsdk.R\$style",
            "com.bytedance.sdk.openadsdk.R\$styleable",
            "com.bytedance.sdk.openadsdk.R",
            "com.qq.e.R\$anim",
            "com.qq.e.R\$drawable",
            "com.qq.e.R\$integer",
            "com.qq.e.R\$string",
            "com.qq.e.R\$style",
            "com.qq.e.R\$xml",
            "com.qq.e.R",
            "com.jd.ad.sdk.multi.R\$anim",
            "com.jd.ad.sdk.multi.R\$animator",
            "com.jd.ad.sdk.multi.R\$attr",
            "com.jd.ad.sdk.multi.R\$bool",
            "com.jd.ad.sdk.multi.R\$color",
            "com.jd.ad.sdk.multi.R\$dimen",
            "com.jd.ad.sdk.multi.R\$drawable",
            "com.jd.ad.sdk.multi.R\$id",
            "com.jd.ad.sdk.multi.R\$integer",
            "com.jd.ad.sdk.multi.R\$interpolator",
            "com.jd.ad.sdk.multi.R\$layout",
            "com.jd.ad.sdk.multi.R\$string",
            "com.jd.ad.sdk.multi.R\$style",
            "com.jd.ad.sdk.multi.R\$styleable",
            "com.jd.ad.sdk.multi.R",
            "tv.danmaku.ijk.media.player.R\$raw",
            "tv.danmaku.ijk.media.player.R",
            "com.romainpiel.shimmer.R\$attr",
            "com.romainpiel.shimmer.R\$drawable",
            "com.romainpiel.shimmer.R\$styleable",
            "com.romainpiel.shimmer.R",
            "com.xmly.xlogdecodelibnoop.R",
            "androidx.lifecycle.ktx.R",
            "androidx.lifecycle.viewmodel.ktx.R",
            "com.qimiaosiwei.android.h5offline.R",
            "com.qimiaosiwei.android.download.R",


            "com.yy.yyeva.R\$string",
            "com.yy.yyeva.R",


            "com.cmic.mixsdk.R\$attr",
            "com.cmic.mixsdk.R\$drawable",
            "com.cmic.mixsdk.R\$id",
            "com.cmic.mixsdk.R\$layout",
            "com.cmic.mixsdk.R\$mipmap",
            "com.cmic.mixsdk.R\$string",
            "com.cmic.mixsdk.R\$styleable",
            "com.cmic.mixsdk.R",


            "dk.madslee.imageCapInsets.R\$anim",
            "dk.madslee.imageCapInsets.R\$attr",
            "dk.madslee.imageCapInsets.R\$bool",
            "dk.madslee.imageCapInsets.R\$color",
            "dk.madslee.imageCapInsets.R\$dimen",
            "dk.madslee.imageCapInsets.R\$drawable",
            "dk.madslee.imageCapInsets.R\$id",
            "dk.madslee.imageCapInsets.R\$integer",
            "dk.madslee.imageCapInsets.R\$layout",
            "dk.madslee.imageCapInsets.R\$string",
            "dk.madslee.imageCapInsets.R\$style",
            "dk.madslee.imageCapInsets.R\$styleable",
            "dk.madslee.imageCapInsets.R\$xml",
            "dk.madslee.imageCapInsets.R",

            "com.youzan.systemweb.R\$layout",
            "com.youzan.systemweb.R\$string",
            "com.youzan.systemweb.R",
            "com.youzan.jsbridge.R\$string",
            "com.youzan.jsbridge.R",
            "androidx.documentfile.R",
            "androidx.print.R",
            "androidx.exifinterface.R",

            "com.bumptech.glide.gifdecoder.R",
            "android.support.rastermill.R",
            "com.facebook.yogajni.R",
            "com.tencent.bugly.R\$string",
            "com.tencent.bugly.R",
            "com.umeng.umcrash.R",
            "master.flame.danmaku.R",
            "com.dylanjiang.xmlinearalloc.R",
            "com.alipay.sdk.R",
            "com.tmall.ultraviewpager.R\$attr",
            "com.tmall.ultraviewpager.R\$bool",
            "com.tmall.ultraviewpager.R\$color",
            "com.tmall.ultraviewpager.R\$dimen",
            "com.tmall.ultraviewpager.R\$drawable",
            "com.tmall.ultraviewpager.R\$id",
            "com.tmall.ultraviewpager.R\$integer",
            "com.tmall.ultraviewpager.R\$layout",
            "com.tmall.ultraviewpager.R\$string",
            "com.tmall.ultraviewpager.R\$style",
            "com.smartdevicelink.R\$drawable",
            "com.tmall.ultraviewpager.R\$styleable",
            "com.tmall.ultraviewpager.R",
            "com.facebook.imagepipeline.backends.okhttp3.R",
            "androidx.annotation.experimental.R",
            "com.bytedance.android.bytehook.R",
            "com.youzan.spiderman.R\$string",
            "com.youzan.spiderman.R",
            "com.bytedance.sdk.open.douyin.R\$attr",
            "com.bytedance.sdk.open.douyin.R\$color",
            "com.bytedance.sdk.open.douyin.R\$dimen",
            "com.bytedance.sdk.open.douyin.R\$drawable",
            "com.bytedance.sdk.open.douyin.R\$id",
            "com.bytedance.sdk.open.douyin.R\$layout",
            "com.bytedance.sdk.open.douyin.R\$string",
            "com.bytedance.sdk.open.douyin.R\$style",
            "com.bytedance.sdk.open.douyin.R\$styleable",
            "com.bytedance.sdk.open.douyin.R",
            "com.getui.gtc.R",
            "com.igexin.push.R\$string",
            "com.igexin.push.R",
            "com.hihonor.push.sdk.R",
            "cn.jcore.client.android.R",
            "cn.jpush.client.android.R\$drawable",
            "cn.jpush.client.android.R\$id",
            "cn.jpush.client.android.R\$layout",
            "cn.jpush.client.android.R\$string",
            "cn.jpush.client.android.R\$style",
            "cn.jpush.client.android.R",
            "cn.jiguang.plugin.xiaomi.R",
            "cn.jiguang.plugin.honor.R",
            "cn.jiguang.plugin.huawei.R",
            "cn.jiguang.plugin.vivo.R",
            "cn.jiguang.plugin.oppo.R",
            "com.heytap.mcssdk.R\$string",
            "com.heytap.mcssdk.R",



            "com.squareup.curtains.R",
            "com.bun.miitmdid.R\$anim",
            "com.bun.miitmdid.R\$dimen",
            "com.bun.miitmdid.R\$integer",
            "com.bun.miitmdid.R",
            "com.tencent.liteav.sdk.R",
            "com.facebook.imagepipeline.animated.R\$attr",
            "com.facebook.imagepipeline.animated.R\$id",
            "com.facebook.imagepipeline.animated.R\$styleable",
            "com.facebook.imagepipeline.animated.R",
            "com.facebook.animated.drawable.R\$attr",
            "com.facebook.animated.drawable.R\$id",
            "com.facebook.animated.drawable.R\$styleable",
            "com.facebook.animated.drawable.R",
            "com.facebook.drawee.R\$attr",
            "com.facebook.drawee.R\$id",
            "com.facebook.drawee.R\$styleable",
            "com.facebook.drawee.R",
            "com.facebook.nativefilters.R",
            "com.facebook.imagepipeline.R",
            "com.facebook.nativeimagetranscoder.R",
            "com.facebook.imagepipelinebase.R",
            "com.facebook.soloader.R",
            "com.facebook.fbcore.R",
            "com.sina.weibo.R\$color",
            "com.sina.weibo.R\$drawable",
            "com.sina.weibo.R\$xml",
            "com.sina.weibo.R",
            "cn.fly.verify.R\$string",
            "cn.fly.verify.R\$style",
            "cn.fly.verify.R\$xml",
            "cn.fly.verify.R",
            "com.baidu.mobads.proxy.R\$drawable",
            "com.baidu.mobads.proxy.R\$id",
            "com.baidu.mobads.proxy.R\$layout",
            "com.baidu.mobads.proxy.R\$style",
            "com.baidu.mobads.proxy.R\$xml",
            "com.baidu.mobads.proxy.R",
            "com.xingin.xhssharesdk.R\$layout",
            "com.xingin.xhssharesdk.R",
            "com.getkeepsafe.relinker.R",
            "com.geetest.captcha.R\$string",
            "com.geetest.captcha.R\$style",
            "com.geetest.captcha.R",
            "com.bytedance.sdk.open.aweme.R\$attr",
            "com.bytedance.sdk.open.aweme.R\$color",
            "com.bytedance.sdk.open.aweme.R\$dimen",
            "com.bytedance.sdk.open.aweme.R\$drawable",
            "com.bytedance.sdk.open.aweme.R\$id",
            "com.bytedance.sdk.open.aweme.R\$layout",
            "com.bytedance.sdk.open.aweme.R\$string",
            "com.bytedance.sdk.open.aweme.R\$style",
            "com.bytedance.sdk.open.aweme.R\$styleable",
            "com.bytedance.sdk.open.aweme.R",
            "com.huawei.hms.stats.R",
            "com.huawei.hms.framework.network.grs.R",
            "com.huawei.hms.framework.network.frameworkcompat.R",
            "com.huawei.secure.android.common.base.R",
            "com.huawei.hms.hatool.R",
            "com.huawei.secure.android.common.encrypt.R",
            "net.butterflytv.rtmp_client.R",
            "com.huawei.hms.baselegacyapi.R\$color",
            "com.huawei.hms.baselegacyapi.R\$id",
            "com.huawei.hms.baselegacyapi.R\$layout",
            "com.huawei.hms.baselegacyapi.R\$string",
            "com.huawei.hms.baselegacyapi.R\$style",
            "com.huawei.hms.baselegacyapi.R",
            "com.huawei.hms.base.ui.R\$color",
            "com.huawei.hms.base.ui.R\$id",
            "com.huawei.hms.base.ui.R\$layout",
            "com.huawei.hms.base.ui.R\$string",
            "com.huawei.hms.base.ui.R\$style",
            "com.huawei.hms.base.ui.R",
            "com.huawei.hms.log.R",
            "com.huawei.secure.android.common.ssl.R",
            "com.huawei.hms.framework.common.R",
            "com.bytedance.frameworks.baselib.network.ok3.R\$dimen",
            "com.smartdevicelink.proxy.ex.R",
            "cn.com.spdb.spdbpay.R\$layout",
            "com.maplehaze.adsdk.R",
            "com.bytedance.speech.speechengine.R\$string",
            "com.io.tl.android.R\$id",
            "com.maplehaze.adsdk.ext.R\$drawable",
            "com.mcto.sspsdk.R\$drawable",
            "com.alibaba.security.realidentity.included.R\$anim",
            "com.alibaba.wireless.security.securitybodysdk.R\$string",
            "com.alibaba.security.realidentity.included.R\$color",
            "com.alibaba.wireless.security.securitybodysdk.R",
            "com.bytedance.common.utility.R\$string",
            "com.io.tl.android.R\$layout",
            "com.alibaba.security.realidentity.included.R\$id",
            "com.smartdevicelink.R",
            "com.jdpaysdk.author.R\$layout",
            "com.io.tl.android.R\$xml",
            "com.maplehaze.adsdk.R\$drawable",
            "com.maplehaze.adsdk.R\$xml",
            "com.alibaba.security.realidentity.included.R\$raw",
            "cn.com.spdb.spdbpay.R\$string",
            "com.alibaba.sdk.android.oss_android_sdk.R\$string",
            "com.alibaba.wireless.security.open.middletier.R\$string",
            "com.alibaba.sdk.android.oss_android_sdk.R",
            "com.alibaba.security.realidentity.included.R\$layout",
            "com.alibaba.wireless.security.R\$string",
            "com.alibaba.security.realidentity.included.R\$string",
            "com.bytedance.frameworks.baselib.network.cronet.R\$string",




            "com.alibaba.security.realidentity.included.R",
            "com.alibaba.wireless.security.open.middletier.R",
            "com.bytedance.speech.speechengine.R\$id",
            "cn.com.spdb.spdbpay.R\$id",
            "com.kwad.sdk.R\$dimen",
            "com.kwad.sdk.R\$attr",
            "com.leto.game.sdk.R\$raw",
            "com.jdpaysdk.author.R\$style",
            "com.bytedance.common.wschannel.R\$string",
            "com.alibaba.security.realidentity.included.R\$drawable",
            "com.alibaba.wireless.security.R",
            "com.alibaba.security.realidentity.included.R\$style",
            "cn.com.spdb.spdbpay.R\$drawable",
            "com.kwad.sdk.R\$drawable",
            "com.io.tl.android.R\$styleable",
            "com.bytedance.frameworks.baselib.network.cronet.R\$dimen",
            "com.kwad.sdk.R\$layout",
            "com.huawei.hicarsdk.R",
            "com.maplehaze.adsdk.R\$dimen",
            "cn.com.spdb.spdbpay.R\$style",
            "com.jdpaysdk.author.R\$color",
            "com.maplehaze.adsdk.R\$color",
            "com.maplehaze.adsdk.ext.R\$style",
            "com.maplehaze.adsdk.ext.R\$styleable",
            "com.bef.effectsdk.R",
            "com.jdpaysdk.author.R",
            "com.bytedance.retrofit2.R",
            "com.maplehaze.adsdk.ext.R\$xml",
            "com.bytedance.boringssl.so.R",
            "org.chromium.net.R\$dimen",
            "com.leto.game.sdk.R\$styleable",
            "com.maplehaze.adsdk.ext.R\$attr",
            "com.io.tl.android.R\$style",
            "com.kwad.sdk.R\$raw",
            "com.huawei.hiai.vision.R",
            "com.mcto.sspsdk.R\$styleable",
            "com.maplehaze.adsdk.ext.R\$string",
            "com.io.tl.android.R\$anim",
            "com.mcto.sspsdk.R\$dimen",
            "com.mcto.sspsdk.R\$string",
            "com.maplehaze.adsdk.R\$layout",
            "com.huawei.hicarsdk.R\$string",
            "com.kwad.sdk.R\$xml",
            "com.bytedance.boringssl.so.R\$string",
            "com.kwad.sdk.R\$string",
            "com.mcto.sspsdk.R",
            "com.taobao.android.windvane_mini.R\$string",
            "com.bytedance.frameworks.baselib.network.ok3.R\$string",
            "com.bytedance.frameworks.baselib.network.ok3.R",
            "com.maplehaze.adsdk.R\$raw",
            "com.jdpaysdk.author.R\$drawable",
            "cn.com.spdb.spdbpay.R\$color",
            "com.jdpaysdk.author.R\$id",
            "com.bytedance.ttnet.R\$layout",
            "com.leto.game.sdk.R\$menu",
            "com.maplehaze.adsdk.ext.R\$raw",
            "com.github.mikephil.charting.R",
            "org.chromium.net.so.R\$string",
            "com.maplehaze.adsdk.ext.R\$dimen",
            "com.io.tl.android.R\$dimen",
            "com.maplehaze.adsdk.R\$style",
            "com.alibaba.security.biometrics.R\$drawable",
            "com.mcto.sspsdk.R\$attr",
            "com.io.tl.android.R\$attr",
            "com.io.tl.android.R",
            "com.mcto.sspsdk.R\$color",
            "com.maplehaze.adsdk.R\$id",

            "com.maplehaze.adsdk.R\$attr",
            "com.alibaba.security.biometrics.R\$raw",
            "com.mcto.sspsdk.R\$layout",
            "com.alibaba.security.realidentity.R",
            "com.taobao.android.windvane_ucweb.R\$string",
            "com.io.tl.android.R\$integer",
            "com.io.tl.android.R\$drawable",
            "com.jieli.jl_bt_ota.R",
            "com.alibaba.security.realidentity.R\$layout",
            "com.alibaba.security.biometrics.R\$style",
            "com.kwad.sdk.R\$styleable",
            "com.bytedance.frameworks.baselib.network.R\$string",
            "com.kwad.sdk.R\$color",
            "com.alibaba.security.biometrics.jni.R",
            "com.alibaba.security.biometrics.R\$layout",

            "com.alibaba.security.biometrics.R\$color",
            "com.maplehaze.adsdk.R\$string",
            "com.maplehaze.adsdk.ext.R\$id",
            "com.maplehaze.adsdk.R\$styleable",
            "com.bytedance.speech.speechengine.R\$dimen",
            "com.bytedance.common.wschannel.R\$dimen",
            "com.bytedance.ttnet.R\$dimen",
            "com.bytedance.frameworks.baselib.network.cronet.R",
            "com.bytedance.ttnet.R\$string",
            "org.chromium.net.R\$string",
            "com.bytedance.ttnet.R\$id",
            "com.bytedance.common.utility.R\$dimen",
            "com.bytedance.speech.speechengine.R",
            "org.chromium.net.R",
            "com.bytedance.common.utility.R",
            "com.bytedance.ttnet.R",
            "com.bytedance.frameworks.baselib.network.R",
            "com.bytedance.retrofit2.R\$string",
            "com.bytedance.common.wschannel.R",
            "com.alibaba.security.biometrics.R\$id",
            "com.alibaba.security.realidentity.R\$string",
            "com.leto.game.sdk.R\$anim",
            "com.kwad.sdk.R\$id",
            "com.maplehaze.adsdk.ext.R\$layout",
            "com.maplehaze.adsdk.ext.R",
            "com.maplehaze.adsdk.ext.R\$color",
            "com.taobao.android.windvane_ucweb.R",
            "com.leto.game.sdk.R",
            "com.kwad.sdk.R",
            "com.mcto.sspsdk.R\$style",
            "com.mcto.sspsdk.R\$id",
            "android.taobao.windvane.extra.R",
            "androidx.tracing.R",
            "com.taobao.android.windvane_mix.R\$string",
            "com.alibaba.security.realidentity.R\$color",
            "com.taobao.uc.jsi.R\$string",
            "com.alibaba.security.biometrics.R\$string",
            "com.alibaba.security.realidentity.common.R",

            "com.alibaba.security.biometrics.R\$anim",

            "com.alibaba.security.biometrics.service.R",
            "com.alibaba.security.biometrics.R",
            "com.alibaba.security.realidentity.R\$drawable",
            "com.alibaba.security.realidentity.R\$id",
            "com.taobao.android.windvane_core.R",
            "com.taobao.uc.R",
            "com.taobao.android.windvane_mix.R",
            "com.taobao.android.windvane_core.R\$string",
            "com.taobao.android.jsbridge.R",
            "com.taobao.uc.jsi.R",
            "com.taobao.android.jsbridge.R\$string",
            "android.taobao.windvane.extra.R\$string",
            "com.taobao.android.windvane_mini.R",
            "com.taobao.uc.R\$string",

            "com.tencent.mobileqq.tritonaudio.R",
            "com.tencent.mobileqq.triton.sdk.R",
            "com.tencent.mobileqq.triton.R",
            "com.tencent.qqmini.mmkv.R",
            "com.tencent.qqmini.minigame.R",
            "com.tencent.qqmini.minigame.R\$xml",
            "com.tencent.qqmini.minigame.R\$style",
            "com.tencent.qqmini.minigame.R\$string",
            "com.tencent.qqmini.minigame.R\$layout",
            "com.tencent.qqmini.minigame.R\$id",
            "com.tencent.qqmini.minigame.R\$drawable",
            "com.tencent.qqmini.minigame.R\$dimen",
            "com.tencent.qqmini.minigame.R\$color",
            "com.tencent.qqmini.minigame.R\$attr",
            "com.tencent.qqmini.minigame.R\$anim",
            "com.tencent.qqmini.sdk.proto.R",
            "com.tencent.qqmini.sdk.launcher.R",
            "com.tencent.qqmini.sdk.launcher.R\$style",
            "com.tencent.qqmini.sdk.launcher.R\$string",
            "com.tencent.qqmini.sdk.launcher.R\$layout",
            "com.tencent.qqmini.sdk.launcher.R\$id",
            "com.tencent.qqmini.sdk.launcher.R\$drawable",
            "com.tencent.qqmini.sdk.launcher.R\$attr",
            "com.tencent.qqmini.sdk.launcher.R\$anim",
            "com.tencent.qqmini.sdk.R",
            "com.tencent.qqmini.sdk.R\$xml",
            "com.tencent.qqmini.sdk.R\$style",
            "com.tencent.qqmini.sdk.R\$string",
            "com.tencent.qqmini.sdk.R\$layout",
            "com.tencent.qqmini.sdk.R\$id",
            "com.tencent.qqmini.sdk.R\$drawable",
            "com.tencent.qqmini.sdk.R\$dimen",
            "com.tencent.qqmini.sdk.R\$color",
            "com.tencent.qqmini.sdk.R\$attr",
            "com.tencent.qqmini.sdk.server.R",
            "com.tencent.qqmini.sdk.server.R\$xml",
            "com.tencent.qqmini.sdk.R\$anim",
            "com.tencent.qqmini.sdk.server.R\$style",
            "com.tencent.qqmini.sdk.server.R\$string",
            "com.tencent.qqmini.sdk.server.R\$layout",
            "com.tencent.qqmini.sdk.server.R\$id",
            "com.tencent.qqmini.sdk.server.R\$dimen",
            "com.tencent.qqmini.sdk.server.R\$drawable",
            "com.tencent.qqmini.sdk.server.R\$anim",
            "com.tencent.qqmini.sdk.server.R\$attr",
            "com.tencent.qqmini.sdk.server.R\$color"
    ]
    resCheck {
        enable false // 无用资源检查的开关
        // 根据资源所在的路径做模糊匹配（因为第三方库用到的冗余资源没法手动删）
        onlyCheck = [
                // 只检查主工程里的资源
                "app/build"
        ]
        // 检查白名单。这些资源就算是冗余资源也不会report出来
        keepRes = [
                "R.drawable.ic_list_dou_order",
                "R.string.snapchat_tiktok_client_id",
                "R.string.snapchat_musically_client_id",
                "R.string.fb_account_kit_client_token",
                "R.string.mapbox_*",
                "R.string.kakao*",
                "R.dimen",
                "R.color",
                "R.animator",
                "R.integer",
                "R.bool",
                "R.style",
                "R.styleable",
                "R.attr",
                "R.xml",
                "R.array",
                "R.string",

                "com.ximalaya.ting.android.adsdk.businessshell.R.styleable",
                "com.ximalaya.ting.android.adsdk.businessshell.R.id",
                "com.bun.miitmdid.R.*",

                //可视化埋点需要保留id和layout
                "R.id.*",
                "R.layout.*",
                // for your icon
                "R.drawable.ic_launcher",
                "R.layout.view_notify_dark_play",
                "R.drawable.notification_icon",
                "R.drawable.host_theme_bottom_tab_background",

                "R.drawable.reflect_player_cover_default",
                "R.drawable.reflect_player_btn_fullsize",
                "R.drawable.notification_ting",
                "R.drawable.notification_icon",
                "R.drawable.notify_global_play_default",
                "R.drawable.notify_push_small",
                "R.drawable.notify_notification_icon",
                "R.drawable.push",
                "R.drawable.push_small",
                "R.layout.component*",

                // geetest 极验开始
                "R.string.gt4_*",
                "R.style.gt4_*",
                // geetest 极验结束

                // for fabric
                //"R.string.com.crashlytics.*",
                // for google-services
                "R.string.google_app_id",
                "R.string.gcm_defaultSenderId",
                "R.string.default_web_client_id",
                "R.string.ga_trackingId",
                "R.string.firebase_database_url",
                "R.string.google_api_key",
                "R.string.google_crash_reporting_api_key",

                //for component
                "R.layout.component*",
                "R.layout.hybrid*",
                "R.drawable.component*",
                "R.id.comp*",
                "R.drawable.host_image_share",

                //穿山甲sdk资源
                //穿山甲sdk资源-start
                "R.anim.tt*",
                "R.attr.tt*",
                "R.color.tt*",
                "R.dimen.tt*",
                "R.drawable.tt*",
                "R.id.tt*",
                "R.layout.tt*",
                "R.string.tt*",
                "R.style.tt*",
                "R.anim.abc*",
                "R.attr.abc*",
                "R.color.abc*",
                "R.dimen.abc*",
                "R.drawable.abc*",
                "R.id.abc*",
                "R.layout.abc*",
                "R.string.abc*",
                "R.style.abc*",
                "R.anim.Base*",
                "R.attr.Base*",
                "R.color.Base*",
                "R.dimen.Base*",
                "R.drawable.Base*",
                "R.id.Base*",
                "R.layout.Base*",
                "R.string.Base*",
                "R.style.Base*",
                "R.anim.abc_fade_in",
                "R.anim.abc_fade_out",
                "R.anim.abc_grow_fade_in_from_bottom",
                "R.anim.abc_popup_enter",
                "R.anim.abc_popup_exit",
                "R.anim.abc_shrink_fade_out_from_bottom",
                "R.anim.abc_slide_in_bottom",
                "R.anim.abc_slide_in_top",
                "R.anim.abc_slide_out_bottom",
                "R.anim.abc_slide_out_top",
                "R.anim.abc_tooltip_enter",
                "R.anim.abc_tooltip_exit",
                "R.attr.actionBarDivider",
                "R.attr.actionBarItemBackground",
                "R.attr.actionBarPopupTheme",
                "R.attr.actionBarSize",
                "R.attr.actionBarSplitStyle",
                "R.attr.actionBarStyle",
                "R.attr.actionBarTabBarStyle",
                "R.attr.actionBarTabStyle",
                "R.attr.actionBarTabTextStyle",
                "R.attr.actionBarTheme",
                "R.attr.actionBarWidgetTheme",
                "R.attr.actionButtonStyle",
                "R.attr.actionDropDownStyle",
                "R.attr.actionLayout",
                "R.attr.actionMenuTextAppearance",
                "R.attr.actionMenuTextColor",
                "R.attr.actionModeBackground",
                "R.attr.actionModeCloseButtonStyle",
                "R.attr.actionModeCloseDrawable",
                "R.attr.actionModeCopyDrawable",
                "R.attr.actionModeCutDrawable",
                "R.attr.actionModeFindDrawable",
                "R.attr.actionModePasteDrawable",
                "R.attr.actionModePopupWindowStyle",
                "R.attr.actionModeSelectAllDrawable",
                "R.attr.actionModeShareDrawable",
                "R.attr.actionModeSplitBackground",
                "R.attr.actionModeStyle",
                "R.attr.actionModeWebSearchDrawable",
                "R.attr.actionOverflowButtonStyle",
                "R.attr.actionOverflowMenuStyle",
                "R.attr.actionProviderClass",
                "R.attr.actionViewClass",
                "R.attr.activityChooserViewStyle",
                "R.attr.alertDialogButtonGroupStyle",
                "R.attr.alertDialogCenterButtons",
                "R.attr.alertDialogStyle",
                "R.attr.alertDialogTheme",
                "R.attr.allowStacking",
                "R.attr.alpha",
                "R.attr.alphabeticModifiers",
                "R.attr.arrowHeadLength",
                "R.attr.arrowShaftLength",
                "R.attr.autoCompleteTextViewStyle",
                "R.attr.autoSizeMaxTextSize",
                "R.attr.autoSizeMinTextSize",
                "R.attr.autoSizePresetSizes",
                "R.attr.autoSizeStepGranularity",
                "R.attr.autoSizeTextType",
                "R.attr.background",
                "R.attr.backgroundSplit",
                "R.attr.backgroundStacked",
                "R.attr.backgroundTint",
                "R.attr.backgroundTintMode",
                "R.attr.barLength",
                "R.attr.borderlessButtonStyle",
                "R.attr.buttonBarButtonStyle",
                "R.attr.buttonBarNegativeButtonStyle",
                "R.attr.buttonBarNeutralButtonStyle",
                "R.attr.buttonBarPositiveButtonStyle",
                "R.attr.buttonBarStyle",
                "R.attr.buttonGravity",
                "R.attr.buttonIconDimen",
                "R.attr.buttonPanelSideLayout",
                "R.attr.buttonStyle",
                "R.attr.buttonStyleSmall",
                "R.attr.buttonTint",
                "R.attr.buttonTintMode",
                "R.attr.checkboxStyle",
                "R.attr.checkedTextViewStyle",
                "R.attr.closeIcon",
                "R.attr.closeItemLayout",
                "R.attr.collapseContentDescription",
                "R.attr.collapseIcon",
                "R.attr.color",
                "R.attr.colorAccent",
                "R.attr.colorBackgroundFloating",
                "R.attr.colorButtonNormal",
                "R.attr.colorControlActivated",
                "R.attr.colorControlHighlight",
                "R.attr.colorControlNormal",
                "R.attr.colorError",
                "R.attr.colorPrimary",
                "R.attr.colorPrimaryDark",
                "R.attr.colorSwitchThumbNormal",
                "R.attr.commitIcon",
                "R.attr.contentDescription",
                "R.attr.contentInsetEnd",
                "R.attr.contentInsetEndWithActions",
                "R.attr.contentInsetLeft",
                "R.attr.contentInsetRight",
                "R.attr.contentInsetStart",
                "R.attr.contentInsetStartWithNavigation",
                "R.attr.controlBackground",
                "R.attr.coordinatorLayoutStyle",
                "R.attr.customNavigationLayout",
                "R.attr.defaultQueryHint",
                "R.attr.dialogCornerRadius",
                "R.attr.dialogPreferredPadding",
                "R.attr.dialogTheme",
                "R.attr.displayOptions",
                "R.attr.divider",
                "R.attr.dividerHorizontal",
                "R.attr.dividerPadding",
                "R.attr.dividerVertical",
                "R.attr.drawableSize",
                "R.attr.drawerArrowStyle",
                "R.attr.dropDownListViewStyle",
                "R.attr.dropdownListPreferredItemHeight",
                "R.attr.editTextBackground",
                "R.attr.editTextColor",
                "R.attr.editTextStyle",
                "R.attr.elevation",
                "R.attr.expandActivityOverflowButtonDrawable",
                "R.attr.firstBaselineToTopHeight",
                "R.attr.font",
                "R.attr.fontFamily",
                "R.attr.fontProviderAuthority",
                "R.attr.fontProviderCerts",
                "R.attr.fontProviderFetchStrategy",
                "R.attr.fontProviderFetchTimeout",
                "R.attr.fontProviderPackage",
                "R.attr.fontProviderQuery",
                "R.attr.fontStyle",
                "R.attr.fontVariationSettings",
                "R.attr.fontWeight",
                "R.attr.gapBetweenBars",
                "R.attr.goIcon",
                "R.attr.height",
                "R.attr.hideOnContentScroll",
                "R.attr.homeAsUpIndicator",
                "R.attr.homeLayout",
                "R.attr.icon",
                "R.attr.iconTint",
                "R.attr.iconTintMode",
                "R.attr.iconifiedByDefault",
                "R.attr.imageButtonStyle",
                "R.attr.indeterminateProgressStyle",
                "R.attr.initialActivityCount",
                "R.attr.isLightTheme",
                "R.attr.itemPadding",
                "R.attr.keylines",
                "R.attr.lastBaselineToBottomHeight",
                "R.attr.layout",
                "R.attr.layout_anchor",
                "R.attr.layout_anchorGravity",
                "R.attr.layout_behavior",
                "R.attr.layout_dodgeInsetEdges",
                "R.attr.layout_insetEdge",
                "R.attr.layout_keyline",
                "R.attr.lineHeight",
                "R.attr.listChoiceBackgroundIndicator",
                "R.attr.listDividerAlertDialog",
                "R.attr.listItemLayout",
                "R.attr.listLayout",
                "R.attr.listMenuViewStyle",
                "R.attr.listPopupWindowStyle",
                "R.attr.listPreferredItemHeight",
                "R.attr.listPreferredItemHeightLarge",
                "R.attr.listPreferredItemHeightSmall",
                "R.attr.listPreferredItemPaddingLeft",
                "R.attr.listPreferredItemPaddingRight",
                "R.attr.logo",
                "R.attr.logoDescription",
                "R.attr.maxButtonHeight",
                "R.attr.measureWithLargestChild",
                "R.attr.multiChoiceItemLayout",
                "R.attr.navigationContentDescription",
                "R.attr.navigationIcon",
                "R.attr.navigationMode",
                "R.attr.numericModifiers",
                "R.attr.overlapAnchor",
                "R.attr.paddingBottomNoButtons",
                "R.attr.paddingEnd",
                "R.attr.paddingStart",
                "R.attr.paddingTopNoTitle",
                "R.attr.panelBackground",
                "R.attr.panelMenuListTheme",
                "R.attr.panelMenuListWidth",
                "R.attr.popupMenuStyle",
                "R.attr.popupTheme",
                "R.attr.popupWindowStyle",
                "R.attr.preserveIconSpacing",
                "R.attr.progressBarPadding",
                "R.attr.progressBarStyle",
                "R.attr.queryBackground",
                "R.attr.queryHint",
                "R.attr.radioButtonStyle",
                "R.attr.ratingBarStyle",
                "R.attr.ratingBarStyleIndicator",
                "R.attr.ratingBarStyleSmall",
                "R.attr.searchHintIcon",
                "R.attr.searchIcon",
                "R.attr.searchViewStyle",
                "R.attr.seekBarStyle",
                "R.attr.selectableItemBackground",
                "R.attr.selectableItemBackgroundBorderless",
                "R.attr.showAsAction",
                "R.attr.showDividers",
                "R.attr.showText",
                "R.attr.showTitle",
                "R.attr.singleChoiceItemLayout",
                "R.attr.spinBars",
                "R.attr.spinnerDropDownItemStyle",
                "R.attr.spinnerStyle",
                "R.attr.splitTrack",
                "R.attr.srcCompat",
                "R.attr.state_above_anchor",
                "R.attr.statusBarBackground",
                "R.attr.subMenuArrow",
                "R.attr.submitBackground",
                "R.attr.subtitle",
                "R.attr.subtitleTextAppearance",
                "R.attr.subtitleTextColor",
                "R.attr.subtitleTextStyle",
                "R.attr.suggestionRowLayout",
                "R.attr.switchMinWidth",
                "R.attr.switchPadding",
                "R.attr.switchStyle",
                "R.attr.switchTextAppearance",
                "R.attr.textAllCaps",
                "R.attr.textAppearanceLargePopupMenu",
                "R.attr.textAppearanceListItem",
                "R.attr.textAppearanceListItemSecondary",
                "R.attr.textAppearanceListItemSmall",
                "R.attr.textAppearancePopupMenuHeader",
                "R.attr.textAppearanceSearchResultSubtitle",
                "R.attr.textAppearanceSearchResultTitle",
                "R.attr.textAppearanceSmallPopupMenu",
                "R.attr.textColorAlertDialogListItem",
                "R.attr.textColorSearchUrl",
                "R.attr.theme",
                "R.attr.thickness",
                "R.attr.thumbTextPadding",
                "R.attr.thumbTint",
                "R.attr.thumbTintMode",
                "R.attr.tickMark",
                "R.attr.tickMarkTint",
                "R.attr.tickMarkTintMode",
                "R.attr.tint",
                "R.attr.tintMode",
                "R.attr.title",
                "R.attr.titleMargin",
                "R.attr.titleMarginBottom",
                "R.attr.titleMarginEnd",
                "R.attr.titleMarginStart",
                "R.attr.titleMarginTop",
                "R.attr.titleMargins",
                "R.attr.titleTextAppearance",
                "R.attr.titleTextColor",
                "R.attr.titleTextStyle",
                "R.attr.toolbarNavigationButtonStyle",
                "R.attr.toolbarStyle",
                "R.attr.tooltipForegroundColor",
                "R.attr.tooltipFrameBackground",
                "R.attr.tooltipText",
                "R.attr.track",
                "R.attr.trackTint",
                "R.attr.trackTintMode",
                "R.attr.ttcIndex",
                "R.attr.viewInflaterClass",
                "R.attr.voiceIcon",
                "R.attr.windowActionBar",
                "R.attr.windowActionBarOverlay",
                "R.attr.windowActionModeOverlay",
                "R.attr.windowFixedHeightMajor",
                "R.attr.windowFixedHeightMinor",
                "R.attr.windowFixedWidthMajor",
                "R.attr.windowFixedWidthMinor",
                "R.attr.windowMinWidthMajor",
                "R.attr.windowMinWidthMinor",
                "R.attr.windowNoTitle",
                "R.color.abc_background_cache_hint_selector_material_dark",
                "R.color.abc_background_cache_hint_selector_material_light",
                "R.color.abc_btn_colored_borderless_text_material",
                "R.color.abc_btn_colored_text_material",
                "R.color.abc_color_highlight_material",
                "R.color.abc_hint_foreground_material_dark",
                "R.color.abc_hint_foreground_material_light",
                "R.color.abc_input_method_navigation_guard",
                "R.color.abc_primary_text_disable_only_material_dark",
                "R.color.abc_primary_text_disable_only_material_light",
                "R.color.abc_primary_text_material_dark",
                "R.color.abc_primary_text_material_light",
                "R.color.abc_search_url_text",
                "R.color.abc_search_url_text_normal",
                "R.color.abc_search_url_text_pressed",
                "R.color.abc_search_url_text_selected",
                "R.color.abc_secondary_text_material_dark",
                "R.color.abc_secondary_text_material_light",
                "R.color.abc_tint_btn_checkable",
                "R.color.abc_tint_default",
                "R.color.abc_tint_edittext",
                "R.color.abc_tint_seek_thumb",
                "R.color.abc_tint_spinner",
                "R.color.abc_tint_switch_track",
                "R.color.accent_material_dark",
                "R.color.accent_material_light",
                "R.color.background_floating_material_dark",
                "R.color.background_floating_material_light",
                "R.color.background_material_dark",
                "R.color.background_material_light",
                "R.color.bright_foreground_disabled_material_dark",
                "R.color.bright_foreground_disabled_material_light",
                "R.color.bright_foreground_inverse_material_dark",
                "R.color.bright_foreground_inverse_material_light",
                "R.color.bright_foreground_material_dark",
                "R.color.bright_foreground_material_light",
                "R.color.button_material_dark",
                "R.color.button_material_light",
                "R.color.dim_foreground_disabled_material_dark",
                "R.color.dim_foreground_disabled_material_light",
                "R.color.dim_foreground_material_dark",
                "R.color.dim_foreground_material_light",
                "R.color.error_color_material_dark",
                "R.color.error_color_material_light",
                "R.color.foreground_material_dark",
                "R.color.foreground_material_light",
                "R.color.highlighted_text_material_dark",
                "R.color.highlighted_text_material_light",
                "R.color.material_blue_grey_800",
                "R.color.material_blue_grey_900",
                "R.color.material_blue_grey_950",
                "R.color.material_deep_teal_200",
                "R.color.material_deep_teal_500",
                "R.color.material_grey_100",
                "R.color.material_grey_300",
                "R.color.material_grey_50",
                "R.color.material_grey_600",
                "R.color.material_grey_800",
                "R.color.material_grey_850",
                "R.color.material_grey_900",
                "R.color.notification_action_color_filter",
                "R.color.notification_icon_bg_color",
                "R.color.notification_material_background_media_default_color",
                "R.color.primary_dark_material_dark",
                "R.color.primary_dark_material_light",
                "R.color.primary_material_dark",
                "R.color.primary_material_light",
                "R.color.primary_text_default_material_dark",
                "R.color.primary_text_default_material_light",
                "R.color.primary_text_disabled_material_dark",
                "R.color.primary_text_disabled_material_light",
                "R.color.ripple_material_dark",
                "R.color.ripple_material_light",
                "R.color.secondary_text_default_material_dark",
                "R.color.secondary_text_default_material_light",
                "R.color.secondary_text_disabled_material_dark",
                "R.color.secondary_text_disabled_material_light",
                "R.color.switch_thumb_disabled_material_dark",
                "R.color.switch_thumb_disabled_material_light",
                "R.color.switch_thumb_material_dark",
                "R.color.switch_thumb_material_light",
                "R.color.switch_thumb_normal_material_dark",
                "R.color.switch_thumb_normal_material_light",
                "R.color.tooltip_background_dark",
                "R.color.tooltip_background_light",
                "R.color.tt_appdownloader_notification_material_background_color",
                "R.color.tt_appdownloader_notification_title_color",
                "R.color.tt_appdownloader_s1",
                "R.color.tt_appdownloader_s13",
                "R.color.tt_appdownloader_s18",
                "R.color.tt_appdownloader_s4",
                "R.color.tt_appdownloader_s8",
                "R.color.ttdownloader_transparent",
                "R.dimen.abc_action_bar_content_inset_material",
                "R.dimen.abc_action_bar_content_inset_with_nav",
                "R.dimen.abc_action_bar_default_height_material",
                "R.dimen.abc_action_bar_default_padding_end_material",
                "R.dimen.abc_action_bar_default_padding_start_material",
                "R.dimen.abc_action_bar_elevation_material",
                "R.dimen.abc_action_bar_icon_vertical_padding_material",
                "R.dimen.abc_action_bar_overflow_padding_end_material",
                "R.dimen.abc_action_bar_overflow_padding_start_material",
                "R.dimen.abc_action_bar_stacked_max_height",
                "R.dimen.abc_action_bar_stacked_tab_max_width",
                "R.dimen.abc_action_bar_subtitle_bottom_margin_material",
                "R.dimen.abc_action_bar_subtitle_top_margin_material",
                "R.dimen.abc_action_button_min_height_material",
                "R.dimen.abc_action_button_min_width_material",
                "R.dimen.abc_action_button_min_width_overflow_material",
                "R.dimen.abc_alert_dialog_button_bar_height",
                "R.dimen.abc_alert_dialog_button_dimen",
                "R.dimen.abc_button_inset_horizontal_material",
                "R.dimen.abc_button_inset_vertical_material",
                "R.dimen.abc_button_padding_horizontal_material",
                "R.dimen.abc_button_padding_vertical_material",
                "R.dimen.abc_cascading_menus_min_smallest_width",
                "R.dimen.abc_config_prefDialogWidth",
                "R.dimen.abc_control_corner_material",
                "R.dimen.abc_control_inset_material",
                "R.dimen.abc_control_padding_material",
                "R.dimen.abc_dialog_corner_radius_material",
                "R.dimen.abc_dialog_fixed_height_major",
                "R.dimen.abc_dialog_fixed_height_minor",
                "R.dimen.abc_dialog_fixed_width_major",
                "R.dimen.abc_dialog_fixed_width_minor",
                "R.dimen.abc_dialog_list_padding_bottom_no_buttons",
                "R.dimen.abc_dialog_list_padding_top_no_title",
                "R.dimen.abc_dialog_min_width_major",
                "R.dimen.abc_dialog_min_width_minor",
                "R.dimen.abc_dialog_padding_material",
                "R.dimen.abc_dialog_padding_top_material",
                "R.dimen.abc_dialog_title_divider_material",
                "R.dimen.abc_disabled_alpha_material_dark",
                "R.dimen.abc_disabled_alpha_material_light",
                "R.dimen.abc_dropdownitem_icon_width",
                "R.dimen.abc_dropdownitem_text_padding_left",
                "R.dimen.abc_dropdownitem_text_padding_right",
                "R.dimen.abc_edit_text_inset_bottom_material",
                "R.dimen.abc_edit_text_inset_horizontal_material",
                "R.dimen.abc_edit_text_inset_top_material",
                "R.dimen.abc_floating_window_z",
                "R.dimen.abc_list_item_padding_horizontal_material",
                "R.dimen.abc_panel_menu_list_width",
                "R.dimen.abc_progress_bar_height_material",
                "R.dimen.abc_search_view_preferred_height",
                "R.dimen.abc_search_view_preferred_width",
                "R.dimen.abc_seekbar_track_background_height_material",
                "R.dimen.abc_seekbar_track_progress_height_material",
                "R.dimen.abc_select_dialog_padding_start_material",
                "R.dimen.abc_switch_padding",
                "R.dimen.abc_text_size_body_1_material",
                "R.dimen.abc_text_size_body_2_material",
                "R.dimen.abc_text_size_button_material",
                "R.dimen.abc_text_size_caption_material",
                "R.dimen.abc_text_size_display_1_material",
                "R.dimen.abc_text_size_display_2_material",
                "R.dimen.abc_text_size_display_3_material",
                "R.dimen.abc_text_size_display_4_material",
                "R.dimen.abc_text_size_headline_material",
                "R.dimen.abc_text_size_large_material",
                "R.dimen.abc_text_size_medium_material",
                "R.dimen.abc_text_size_menu_header_material",
                "R.dimen.abc_text_size_menu_material",
                "R.dimen.abc_text_size_small_material",
                "R.dimen.abc_text_size_subhead_material",
                "R.dimen.abc_text_size_subtitle_material_toolbar",
                "R.dimen.abc_text_size_title_material",
                "R.dimen.abc_text_size_title_material_toolbar",
                "R.dimen.compat_button_inset_horizontal_material",
                "R.dimen.compat_button_inset_vertical_material",
                "R.dimen.compat_button_padding_horizontal_material",
                "R.dimen.compat_button_padding_vertical_material",
                "R.dimen.compat_control_corner_material",
                "R.dimen.compat_notification_large_icon_max_height",
                "R.dimen.compat_notification_large_icon_max_width",
                "R.dimen.disabled_alpha_material_dark",
                "R.dimen.disabled_alpha_material_light",
                "R.dimen.highlight_alpha_material_colored",
                "R.dimen.highlight_alpha_material_dark",
                "R.dimen.highlight_alpha_material_light",
                "R.dimen.hint_alpha_material_dark",
                "R.dimen.hint_alpha_material_light",
                "R.dimen.hint_pressed_alpha_material_dark",
                "R.dimen.hint_pressed_alpha_material_light",
                "R.dimen.notification_action_icon_size",
                "R.dimen.notification_action_text_size",
                "R.dimen.notification_big_circle_margin",
                "R.dimen.notification_content_margin_start",
                "R.dimen.notification_large_icon_height",
                "R.dimen.notification_large_icon_width",
                "R.dimen.notification_main_column_padding_top",
                "R.dimen.notification_media_narrow_margin",
                "R.dimen.notification_right_icon_size",
                "R.dimen.notification_right_side_padding_top",
                "R.dimen.notification_small_icon_background_padding",
                "R.dimen.notification_small_icon_size_as_large",
                "R.dimen.notification_subtext_size",
                "R.dimen.notification_top_pad",
                "R.dimen.notification_top_pad_large_text",
                "R.dimen.subtitle_corner_radius",
                "R.dimen.subtitle_outline_width",
                "R.dimen.subtitle_shadow_offset",
                "R.dimen.subtitle_shadow_radius",
                "R.dimen.tooltip_corner_radius",
                "R.dimen.tooltip_horizontal_padding",
                "R.dimen.tooltip_margin",
                "R.dimen.tooltip_precise_anchor_extra_offset",
                "R.dimen.tooltip_precise_anchor_threshold",
                "R.dimen.tooltip_vertical_padding",
                "R.dimen.tooltip_y_offset_non_touch",
                "R.dimen.tooltip_y_offset_touch",
                "R.drawable.abc_ab_share_pack_mtrl_alpha",
                "R.drawable.abc_action_bar_item_background_material",
                "R.drawable.abc_btn_borderless_material",
                "R.drawable.abc_btn_check_material",
                "R.drawable.abc_btn_check_to_on_mtrl_000",
                "R.drawable.abc_btn_check_to_on_mtrl_015",
                "R.drawable.abc_btn_colored_material",
                "R.drawable.abc_btn_default_mtrl_shape",
                "R.drawable.abc_btn_radio_material",
                "R.drawable.abc_btn_radio_to_on_mtrl_000",
                "R.drawable.abc_btn_radio_to_on_mtrl_015",
                "R.drawable.abc_btn_switch_to_on_mtrl_00001",
                "R.drawable.abc_btn_switch_to_on_mtrl_00012",
                "R.drawable.abc_cab_background_internal_bg",
                "R.drawable.abc_cab_background_top_material",
                "R.drawable.abc_cab_background_top_mtrl_alpha",
                "R.drawable.abc_control_background_material",
                "R.drawable.abc_dialog_material_background",
                "R.drawable.abc_edit_text_material",
                "R.drawable.abc_ic_ab_back_material",
                "R.drawable.abc_ic_arrow_drop_right_black_24dp",
                "R.drawable.abc_ic_clear_material",
                "R.drawable.abc_ic_commit_search_api_mtrl_alpha",
                "R.drawable.abc_ic_go_search_api_material",
                "R.drawable.abc_ic_menu_copy_mtrl_am_alpha",
                "R.drawable.abc_ic_menu_cut_mtrl_alpha",
                "R.drawable.abc_ic_menu_overflow_material",
                "R.drawable.abc_ic_menu_paste_mtrl_am_alpha",
                "R.drawable.abc_ic_menu_selectall_mtrl_alpha",
                "R.drawable.abc_ic_menu_share_mtrl_alpha",
                "R.drawable.abc_ic_search_api_material",
                "R.drawable.abc_ic_star_black_16dp",
                "R.drawable.abc_ic_star_black_36dp",
                "R.drawable.abc_ic_star_black_48dp",
                "R.drawable.abc_ic_star_half_black_16dp",
                "R.drawable.abc_ic_star_half_black_36dp",
                "R.drawable.abc_ic_star_half_black_48dp",
                "R.drawable.abc_ic_voice_search_api_material",
                "R.drawable.abc_item_background_holo_dark",
                "R.drawable.abc_item_background_holo_light",
                "R.drawable.abc_list_divider_material",
                "R.drawable.abc_list_divider_mtrl_alpha",
                "R.drawable.abc_list_focused_holo",
                "R.drawable.abc_list_longpressed_holo",
                "R.drawable.abc_list_pressed_holo_dark",
                "R.drawable.abc_list_pressed_holo_light",
                "R.drawable.abc_list_selector_background_transition_holo_dark",
                "R.drawable.abc_list_selector_background_transition_holo_light",
                "R.drawable.abc_list_selector_disabled_holo_dark",
                "R.drawable.abc_list_selector_disabled_holo_light",
                "R.drawable.abc_list_selector_holo_dark",
                "R.drawable.abc_list_selector_holo_light",
                "R.drawable.abc_menu_hardkey_panel_mtrl_mult",
                "R.drawable.abc_popup_background_mtrl_mult",
                "R.drawable.abc_ratingbar_indicator_material",
                "R.drawable.abc_ratingbar_material",
                "R.drawable.abc_ratingbar_small_material",
                "R.drawable.abc_scrubber_control_off_mtrl_alpha",
                "R.drawable.abc_scrubber_control_to_pressed_mtrl_000",
                "R.drawable.abc_scrubber_control_to_pressed_mtrl_005",
                "R.drawable.abc_scrubber_primary_mtrl_alpha",
                "R.drawable.abc_scrubber_track_mtrl_alpha",
                "R.drawable.abc_seekbar_thumb_material",
                "R.drawable.abc_seekbar_tick_mark_material",
                "R.drawable.abc_seekbar_track_material",
                "R.drawable.abc_spinner_mtrl_am_alpha",
                "R.drawable.abc_spinner_textfield_background_material",
                "R.drawable.abc_switch_thumb_material",
                "R.drawable.abc_switch_track_mtrl_alpha",
                "R.drawable.abc_tab_indicator_material",
                "R.drawable.abc_tab_indicator_mtrl_alpha",
                "R.drawable.abc_text_cursor_material",
                "R.drawable.abc_text_select_handle_left_mtrl_dark",
                "R.drawable.abc_text_select_handle_left_mtrl_light",
                "R.drawable.abc_text_select_handle_middle_mtrl_dark",
                "R.drawable.abc_text_select_handle_middle_mtrl_light",
                "R.drawable.abc_text_select_handle_right_mtrl_dark",
                "R.drawable.abc_text_select_handle_right_mtrl_light",
                "R.drawable.abc_textfield_activated_mtrl_alpha",
                "R.drawable.abc_textfield_default_mtrl_alpha",
                "R.drawable.abc_textfield_search_activated_mtrl_alpha",
                "R.drawable.abc_textfield_search_default_mtrl_alpha",
                "R.drawable.abc_textfield_search_material",
                "R.drawable.abc_vector_test",
                "R.drawable.notification_action_background",
                "R.drawable.notification_bg",
                "R.drawable.notification_bg_low",
                "R.drawable.notification_bg_low_normal",
                "R.drawable.notification_bg_low_pressed",
                "R.drawable.notification_bg_normal",
                "R.drawable.notification_bg_normal_pressed",
                "R.drawable.notification_icon_background",
                "R.drawable.notification_template_icon_bg",
                "R.drawable.notification_template_icon_low_bg",
                "R.drawable.notification_tile_bg",
                "R.drawable.notify_panel_notification_icon_bg",
                "R.drawable.tooltip_frame_dark",
                "R.drawable.tooltip_frame_light",
                "R.drawable.tt_appdownloader_action_bg",
                "R.drawable.tt_appdownloader_action_new_bg",
                "R.drawable.tt_appdownloader_ad_detail_download_progress",
                "R.drawable.tt_appdownloader_detail_download_success_bg",
                "R.drawable.tt_appdownloader_download_progress_bar_horizontal",
                "R.drawable.tt_appdownloader_download_progress_bar_horizontal_new",
                "R.drawable.tt_appdownloader_download_progress_bar_horizontal_night",
                "R.drawable.ttdownloader_bg_appinfo_btn",
                "R.drawable.ttdownloader_bg_appinfo_dialog",
                "R.drawable.ttdownloader_bg_button_blue_corner",
                "R.drawable.ttdownloader_bg_kllk_btn1",
                "R.drawable.ttdownloader_bg_kllk_btn2",
                "R.drawable.ttdownloader_bg_transparent",
                "R.drawable.ttdownloader_bg_white_corner",
                "R.drawable.ttdownloader_dash_line",
                "R.drawable.ttdownloader_icon_back_arrow",
                "R.drawable.ttdownloader_icon_download",
                "R.drawable.ttdownloader_icon_yes",
                "R.id.action0",
                "R.id.action_bar",
                "R.id.action_bar_activity_content",
                "R.id.action_bar_container",
                "R.id.action_bar_root",
                "R.id.action_bar_spinner",
                "R.id.action_bar_subtitle",
                "R.id.action_bar_title",
                "R.id.action_container",
                "R.id.action_context_bar",
                "R.id.action_divider",
                "R.id.action_image",
                "R.id.action_menu_divider",
                "R.id.action_menu_presenter",
                "R.id.action_mode_bar",
                "R.id.action_mode_bar_stub",
                "R.id.action_mode_close_button",
                "R.id.action_text",
                "R.id.actions",
                "R.id.activity_chooser_view_content",
                "R.id.add",
                "R.id.alertTitle",
                "R.id.async",
                "R.id.blocking",
                "R.id.bottom",
                "R.id.buttonPanel",
                "R.id.cancel_action",
                "R.id.cancel_tv",
                "R.id.checkbox",
                "R.id.chronometer",
                "R.id.confirm_tv",
                "R.id.content",
                "R.id.contentPanel",
                "R.id.custom",
                "R.id.customPanel",
                "R.id.dash_line",
                "R.id.decor_content_parent",
                "R.id.default_activity_button",
                "R.id.edit_query",
                "R.id.end",
                "R.id.end_padder",
                "R.id.expand_activities_button",
                "R.id.expanded_menu",
                "R.id.forever",
                "R.id.group_divider",
                "R.id.home",
                "R.id.icon",
                "R.id.icon_group",
                "R.id.image",
                "R.id.info",
                "R.id.italic",
                "R.id.iv_app_icon",
                "R.id.iv_detail_back",
                "R.id.iv_privacy_back",
                "R.id.left",
                "R.id.line",
                "R.id.line1",
                "R.id.line3",
                "R.id.listMode",
                "R.id.list_item",
                "R.id.ll_download",
                "R.id.media_actions",
                "R.id.message",
                "R.id.message_tv",
                "R.id.multiply",
                "R.id.none",
                "R.id.normal",
                "R.id.notification_background",
                "R.id.notification_main_column",
                "R.id.notification_main_column_container",
                "R.id.parentPanel",
                "R.id.permission_list",
                "R.id.privacy_webview",
                "R.id.progress_circular",
                "R.id.progress_horizontal",
                "R.id.radio",
                "R.id.right",
                "R.id.right_icon",
                "R.id.right_side",
                "R.id.screen",
                "R.id.scrollIndicatorDown",
                "R.id.scrollIndicatorUp",
                "R.id.scrollView",
                "R.id.search_badge",
                "R.id.search_bar",
                "R.id.search_button",
                "R.id.search_close_btn",
                "R.id.search_edit_frame",
                "R.id.search_go_btn",
                "R.id.search_mag_icon",
                "R.id.search_plate",
                "R.id.search_src_text",
                "R.id.search_voice_btn",
                "R.id.select_dialog_listview",
                "R.id.shortcut",
                "R.id.spacer",
                "R.id.split_action_bar",
                "R.id.src_atop",
                "R.id.src_in",
                "R.id.src_over",
                "R.id.start",
                "R.id.status_bar_latest_event_content",
                "R.id.submenuarrow",
                "R.id.submit_area",
                "R.id.tabMode",
                "R.id.tag_transition_group",
                "R.id.tag_unhandled_key_event_manager",
                "R.id.tag_unhandled_key_listeners",
                "R.id.text",
                "R.id.text2",
                "R.id.textSpacerNoButtons",
                "R.id.textSpacerNoTitle",
                "R.id.time",
                "R.id.title",
                "R.id.titleDividerNoCustom",
                "R.id.title_bar",
                "R.id.title_template",
                "R.id.top",
                "R.id.topPanel",
                "R.id.tt_appdownloader_action",
                "R.id.tt_appdownloader_desc",
                "R.id.tt_appdownloader_download_progress",
                "R.id.tt_appdownloader_download_progress_new",
                "R.id.tt_appdownloader_download_size",
                "R.id.tt_appdownloader_download_status",
                "R.id.tt_appdownloader_download_success",
                "R.id.tt_appdownloader_download_success_size",
                "R.id.tt_appdownloader_download_success_status",
                "R.id.tt_appdownloader_download_text",
                "R.id.tt_appdownloader_icon",
                "R.id.tt_appdownloader_root",
                "R.id.tv_app_detail",
                "R.id.tv_app_developer",
                "R.id.tv_app_name",
                "R.id.tv_app_privacy",
                "R.id.tv_app_version",
                "R.id.tv_empty",
                "R.id.tv_give_up",
                "R.id.tv_permission_description",
                "R.id.tv_permission_title",
                "R.id.uniform",
                "R.id.up",
                "R.id.wrap_content",
                "R.integer.abc_config_activityDefaultDur",
                "R.integer.abc_config_activityShortDur",
                "R.integer.cancel_button_image_alpha",
                "R.integer.config_tooltipAnimTime",
                "R.integer.status_bar_notification_info_maxnum",
                "R.layout.abc_action_bar_title_item",
                "R.layout.abc_action_bar_up_container",
                "R.layout.abc_action_menu_item_layout",
                "R.layout.abc_action_menu_layout",
                "R.layout.abc_action_mode_bar",
                "R.layout.abc_action_mode_close_item_material",
                "R.layout.abc_activity_chooser_view",
                "R.layout.abc_activity_chooser_view_list_item",
                "R.layout.abc_alert_dialog_button_bar_material",
                "R.layout.abc_alert_dialog_material",
                "R.layout.abc_alert_dialog_title_material",
                "R.layout.abc_cascading_menu_item_layout",
                "R.layout.abc_dialog_title_material",
                "R.layout.abc_expanded_menu_layout",
                "R.layout.abc_list_menu_item_checkbox",
                "R.layout.abc_list_menu_item_icon",
                "R.layout.abc_list_menu_item_layout",
                "R.layout.abc_list_menu_item_radio",
                "R.layout.abc_popup_menu_header_item_layout",
                "R.layout.abc_popup_menu_item_layout",
                "R.layout.abc_screen_content_include",
                "R.layout.abc_screen_simple",
                "R.layout.abc_screen_simple_overlay_action_mode",
                "R.layout.abc_screen_toolbar",
                "R.layout.abc_search_dropdown_item_icons_2line",
                "R.layout.abc_search_view",
                "R.layout.abc_select_dialog_material",
                "R.layout.abc_tooltip",
                "R.layout.notification_action",
                "R.layout.notification_action_tombstone",
                "R.layout.notification_media_action",
                "R.layout.notification_media_cancel_action",
                "R.layout.notification_template_big_media",
                "R.layout.notification_template_big_media_custom",
                "R.layout.notification_template_big_media_narrow",
                "R.layout.notification_template_big_media_narrow_custom",
                "R.layout.notification_template_custom_big",
                "R.layout.notification_template_icon_group",
                "R.layout.notification_template_lines_media",
                "R.layout.notification_template_media",
                "R.layout.notification_template_media_custom",
                "R.layout.notification_template_part_chronometer",
                "R.layout.notification_template_part_time",
                "R.layout.select_dialog_item_material",
                "R.layout.select_dialog_multichoice_material",
                "R.layout.select_dialog_singlechoice_material",
                "R.layout.support_simple_spinner_dropdown_item",
                "R.layout.tt_appdownloader_notification_layout",
                "R.layout.ttdownloader_activity_app_detail_info",
                "R.layout.ttdownloader_activity_app_privacy_policy",
                "R.layout.ttdownloader_dialog_appinfo",
                "R.layout.ttdownloader_dialog_select_operation",
                "R.layout.ttdownloader_item_permission",
                "R.string.abc_action_bar_home_description",
                "R.string.abc_action_bar_up_description",
                "R.string.abc_action_menu_overflow_description",
                "R.string.abc_action_mode_done",
                "R.string.abc_activity_chooser_view_see_all",
                "R.string.abc_activitychooserview_choose_application",
                "R.string.abc_capital_off",
                "R.string.abc_capital_on",
                "R.string.abc_font_family_body_1_material",
                "R.string.abc_font_family_body_2_material",
                "R.string.abc_font_family_button_material",
                "R.string.abc_font_family_caption_material",
                "R.string.abc_font_family_display_1_material",
                "R.string.abc_font_family_display_2_material",
                "R.string.abc_font_family_display_3_material",
                "R.string.abc_font_family_display_4_material",
                "R.string.abc_font_family_headline_material",
                "R.string.abc_font_family_menu_material",
                "R.string.abc_font_family_subhead_material",
                "R.string.abc_font_family_title_material",
                "R.string.abc_menu_alt_shortcut_label",
                "R.string.abc_menu_ctrl_shortcut_label",
                "R.string.abc_menu_delete_shortcut_label",
                "R.string.abc_menu_enter_shortcut_label",
                "R.string.abc_menu_function_shortcut_label",
                "R.string.abc_menu_meta_shortcut_label",
                "R.string.abc_menu_shift_shortcut_label",
                "R.string.abc_menu_space_shortcut_label",
                "R.string.abc_menu_sym_shortcut_label",
                "R.string.abc_prepend_shortcut_label",
                "R.string.abc_search_hint",
                "R.string.abc_searchview_description_clear",
                "R.string.abc_searchview_description_query",
                "R.string.abc_searchview_description_search",
                "R.string.abc_searchview_description_submit",
                "R.string.abc_searchview_description_voice",
                "R.string.abc_shareactionprovider_share_with",
                "R.string.abc_shareactionprovider_share_with_application",
                "R.string.abc_toolbar_collapse_description",
                "R.string.app_name",
                "R.string.search_menu_title",
                "R.string.status_bar_notification_info_overflow",
                "R.string.tt_appdownloader_button_cancel_download",
                "R.string.tt_appdownloader_button_queue_for_wifi",
                "R.string.tt_appdownloader_button_start_now",
                "R.string.tt_appdownloader_download_percent",
                "R.string.tt_appdownloader_download_remaining",
                "R.string.tt_appdownloader_download_unknown_title",
                "R.string.tt_appdownloader_duration_hours",
                "R.string.tt_appdownloader_duration_minutes",
                "R.string.tt_appdownloader_duration_seconds",
                "R.string.tt_appdownloader_jump_unknown_source",
                "R.string.tt_appdownloader_label_cancel",
                "R.string.tt_appdownloader_label_cancel_directly",
                "R.string.tt_appdownloader_label_ok",
                "R.string.tt_appdownloader_label_reserve_wifi",
                "R.string.tt_appdownloader_notification_download",
                "R.string.tt_appdownloader_notification_download_complete_open",
                "R.string.tt_appdownloader_notification_download_complete_with_install",
                "R.string.tt_appdownloader_notification_download_complete_without_install",
                "R.string.tt_appdownloader_notification_download_continue",
                "R.string.tt_appdownloader_notification_download_delete",
                "R.string.tt_appdownloader_notification_download_failed",
                "R.string.tt_appdownloader_notification_download_install",
                "R.string.tt_appdownloader_notification_download_open",
                "R.string.tt_appdownloader_notification_download_pause",
                "R.string.tt_appdownloader_notification_download_restart",
                "R.string.tt_appdownloader_notification_download_resume",
                "R.string.tt_appdownloader_notification_download_space_failed",
                "R.string.tt_appdownloader_notification_download_waiting_net",
                "R.string.tt_appdownloader_notification_download_waiting_wifi",
                "R.string.tt_appdownloader_notification_downloading",
                "R.string.tt_appdownloader_notification_install_finished_open",
                "R.string.tt_appdownloader_notification_insufficient_space_error",
                "R.string.tt_appdownloader_notification_need_wifi_for_size",
                "R.string.tt_appdownloader_notification_no_internet_error",
                "R.string.tt_appdownloader_notification_no_wifi_and_in_net",
                "R.string.tt_appdownloader_notification_paused_in_background",
                "R.string.tt_appdownloader_notification_pausing",
                "R.string.tt_appdownloader_notification_prepare",
                "R.string.tt_appdownloader_notification_request_btn_no",
                "R.string.tt_appdownloader_notification_request_btn_yes",
                "R.string.tt_appdownloader_notification_request_message",
                "R.string.tt_appdownloader_notification_request_title",
                "R.string.tt_appdownloader_notification_waiting_download_complete_handler",
                "R.string.tt_appdownloader_resume_in_wifi",
                "R.string.tt_appdownloader_tip",
                "R.string.tt_appdownloader_wifi_recommended_body",
                "R.string.tt_appdownloader_wifi_recommended_title",
                "R.string.tt_appdownloader_wifi_required_body",
                "R.string.tt_appdownloader_wifi_required_title",
                "R.style.AlertDialog_AppCompat",
                "R.style.AlertDialog_AppCompat_Light",
                "R.style.Animation_AppCompat_Dialog",
                "R.style.Animation_AppCompat_DropDownUp",
                "R.style.Animation_AppCompat_Tooltip",
                "R.style.Base_AlertDialog_AppCompat",
                "R.style.Base_AlertDialog_AppCompat_Light",
                "R.style.Base_Animation_AppCompat_Dialog",
                "R.style.Base_Animation_AppCompat_DropDownUp",
                "R.style.Base_Animation_AppCompat_Tooltip",
                "R.style.Base_DialogWindowTitleBackground_AppCompat",
                "R.style.Base_DialogWindowTitle_AppCompat",
                "R.style.Base_TextAppearance_AppCompat",
                "R.style.Base_TextAppearance_AppCompat_Body1",
                "R.style.Base_TextAppearance_AppCompat_Body2",
                "R.style.Base_TextAppearance_AppCompat_Button",
                "R.style.Base_TextAppearance_AppCompat_Caption",
                "R.style.Base_TextAppearance_AppCompat_Display1",
                "R.style.Base_TextAppearance_AppCompat_Display2",
                "R.style.Base_TextAppearance_AppCompat_Display3",
                "R.style.Base_TextAppearance_AppCompat_Display4",
                "R.style.Base_TextAppearance_AppCompat_Headline",
                "R.style.Base_TextAppearance_AppCompat_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Large",
                "R.style.Base_TextAppearance_AppCompat_Large_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large",
                "R.style.Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small",
                "R.style.Base_TextAppearance_AppCompat_Medium",
                "R.style.Base_TextAppearance_AppCompat_Medium_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Menu",
                "R.style.Base_TextAppearance_AppCompat_SearchResult",
                "R.style.Base_TextAppearance_AppCompat_SearchResult_Subtitle",
                "R.style.Base_TextAppearance_AppCompat_SearchResult_Title",
                "R.style.Base_TextAppearance_AppCompat_Small",
                "R.style.Base_TextAppearance_AppCompat_Small_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Subhead",
                "R.style.Base_TextAppearance_AppCompat_Subhead_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Title",
                "R.style.Base_TextAppearance_AppCompat_Title_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Tooltip",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Menu",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Title",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle",
                "R.style.Base_TextAppearance_AppCompat_Widget_ActionMode_Title",
                "R.style.Base_TextAppearance_AppCompat_Widget_Button",
                "R.style.Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored",
                "R.style.Base_TextAppearance_AppCompat_Widget_Button_Colored",
                "R.style.Base_TextAppearance_AppCompat_Widget_Button_Inverse",
                "R.style.Base_TextAppearance_AppCompat_Widget_DropDownItem",
                "R.style.Base_TextAppearance_AppCompat_Widget_PopupMenu_Header",
                "R.style.Base_TextAppearance_AppCompat_Widget_PopupMenu_Large",
                "R.style.Base_TextAppearance_AppCompat_Widget_PopupMenu_Small",
                "R.style.Base_TextAppearance_AppCompat_Widget_Switch",
                "R.style.Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem",
                "R.style.Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item",
                "R.style.Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle",
                "R.style.Base_TextAppearance_Widget_AppCompat_Toolbar_Title",
                "R.style.Base_ThemeOverlay_AppCompat",
                "R.style.Base_ThemeOverlay_AppCompat_ActionBar",
                "R.style.Base_ThemeOverlay_AppCompat_Dark",
                "R.style.Base_ThemeOverlay_AppCompat_Dark_ActionBar",
                "R.style.Base_ThemeOverlay_AppCompat_Dialog",
                "R.style.Base_ThemeOverlay_AppCompat_Dialog_Alert",
                "R.style.Base_ThemeOverlay_AppCompat_Light",
                "R.style.Base_Theme_AppCompat",
                "R.style.Base_Theme_AppCompat_CompactMenu",
                "R.style.Base_Theme_AppCompat_Dialog",
                "R.style.Base_Theme_AppCompat_DialogWhenLarge",
                "R.style.Base_Theme_AppCompat_Dialog_Alert",
                "R.style.Base_Theme_AppCompat_Dialog_FixedSize",
                "R.style.Base_Theme_AppCompat_Dialog_MinWidth",
                "R.style.Base_Theme_AppCompat_Light",
                "R.style.Base_Theme_AppCompat_Light_DarkActionBar",
                "R.style.Base_Theme_AppCompat_Light_Dialog",
                "R.style.Base_Theme_AppCompat_Light_DialogWhenLarge",
                "R.style.Base_Theme_AppCompat_Light_Dialog_Alert",
                "R.style.Base_Theme_AppCompat_Light_Dialog_FixedSize",
                "R.style.Base_Theme_AppCompat_Light_Dialog_MinWidth",
                "R.style.Base_V21_ThemeOverlay_AppCompat_Dialog",
                "R.style.Base_V21_Theme_AppCompat",
                "R.style.Base_V21_Theme_AppCompat_Dialog",
                "R.style.Base_V21_Theme_AppCompat_Light",
                "R.style.Base_V21_Theme_AppCompat_Light_Dialog",
                "R.style.Base_V22_Theme_AppCompat",
                "R.style.Base_V22_Theme_AppCompat_Light",
                "R.style.Base_V23_Theme_AppCompat",
                "R.style.Base_V23_Theme_AppCompat_Light",
                "R.style.Base_V26_Theme_AppCompat",
                "R.style.Base_V26_Theme_AppCompat_Light",
                "R.style.Base_V26_Widget_AppCompat_Toolbar",
                "R.style.Base_V28_Theme_AppCompat",
                "R.style.Base_V28_Theme_AppCompat_Light",
                "R.style.Base_V7_ThemeOverlay_AppCompat_Dialog",
                "R.style.Base_V7_Theme_AppCompat",
                "R.style.Base_V7_Theme_AppCompat_Dialog",
                "R.style.Base_V7_Theme_AppCompat_Light",
                "R.style.Base_V7_Theme_AppCompat_Light_Dialog",
                "R.style.Base_V7_Widget_AppCompat_AutoCompleteTextView",
                "R.style.Base_V7_Widget_AppCompat_EditText",
                "R.style.Base_V7_Widget_AppCompat_Toolbar",
                "R.style.Base_Widget_AppCompat_ActionBar",
                "R.style.Base_Widget_AppCompat_ActionBar_Solid",
                "R.style.Base_Widget_AppCompat_ActionBar_TabBar",
                "R.style.Base_Widget_AppCompat_ActionBar_TabText",
                "R.style.Base_Widget_AppCompat_ActionBar_TabView",
                "R.style.Base_Widget_AppCompat_ActionButton",
                "R.style.Base_Widget_AppCompat_ActionButton_CloseMode",
                "R.style.Base_Widget_AppCompat_ActionButton_Overflow",
                "R.style.Base_Widget_AppCompat_ActionMode",
                "R.style.Base_Widget_AppCompat_ActivityChooserView",
                "R.style.Base_Widget_AppCompat_AutoCompleteTextView",
                "R.style.Base_Widget_AppCompat_Button",
                "R.style.Base_Widget_AppCompat_ButtonBar",
                "R.style.Base_Widget_AppCompat_ButtonBar_AlertDialog",
                "R.style.Base_Widget_AppCompat_Button_Borderless",
                "R.style.Base_Widget_AppCompat_Button_Borderless_Colored",
                "R.style.Base_Widget_AppCompat_Button_ButtonBar_AlertDialog",
                "R.style.Base_Widget_AppCompat_Button_Colored",
                "R.style.Base_Widget_AppCompat_Button_Small",
                "R.style.Base_Widget_AppCompat_CompoundButton_CheckBox",
                "R.style.Base_Widget_AppCompat_CompoundButton_RadioButton",
                "R.style.Base_Widget_AppCompat_CompoundButton_Switch",
                "R.style.Base_Widget_AppCompat_DrawerArrowToggle",
                "R.style.Base_Widget_AppCompat_DrawerArrowToggle_Common",
                "R.style.Base_Widget_AppCompat_DropDownItem_Spinner",
                "R.style.Base_Widget_AppCompat_EditText",
                "R.style.Base_Widget_AppCompat_ImageButton",
                "R.style.Base_Widget_AppCompat_Light_ActionBar",
                "R.style.Base_Widget_AppCompat_Light_ActionBar_Solid",
                "R.style.Base_Widget_AppCompat_Light_ActionBar_TabBar",
                "R.style.Base_Widget_AppCompat_Light_ActionBar_TabText",
                "R.style.Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse",
                "R.style.Base_Widget_AppCompat_Light_ActionBar_TabView",
                "R.style.Base_Widget_AppCompat_Light_PopupMenu",
                "R.style.Base_Widget_AppCompat_Light_PopupMenu_Overflow",
                "R.style.Base_Widget_AppCompat_ListMenuView",
                "R.style.Base_Widget_AppCompat_ListPopupWindow",
                "R.style.Base_Widget_AppCompat_ListView",
                "R.style.Base_Widget_AppCompat_ListView_DropDown",
                "R.style.Base_Widget_AppCompat_ListView_Menu",
                "R.style.Base_Widget_AppCompat_PopupMenu",
                "R.style.Base_Widget_AppCompat_PopupMenu_Overflow",
                "R.style.Base_Widget_AppCompat_PopupWindow",
                "R.style.Base_Widget_AppCompat_ProgressBar",
                "R.style.Base_Widget_AppCompat_ProgressBar_Horizontal",
                "R.style.Base_Widget_AppCompat_RatingBar",
                "R.style.Base_Widget_AppCompat_RatingBar_Indicator",
                "R.style.Base_Widget_AppCompat_RatingBar_Small",
                "R.style.Base_Widget_AppCompat_SearchView",
                "R.style.Base_Widget_AppCompat_SearchView_ActionBar",
                "R.style.Base_Widget_AppCompat_SeekBar",
                "R.style.Base_Widget_AppCompat_SeekBar_Discrete",
                "R.style.Base_Widget_AppCompat_Spinner",
                "R.style.Base_Widget_AppCompat_Spinner_Underlined",
                "R.style.Base_Widget_AppCompat_TextView_SpinnerItem",
                "R.style.Base_Widget_AppCompat_Toolbar",
                "R.style.Base_Widget_AppCompat_Toolbar_Button_Navigation",
                "R.style.Platform_AppCompat",
                "R.style.Platform_AppCompat_Light",
                "R.style.Platform_ThemeOverlay_AppCompat",
                "R.style.Platform_ThemeOverlay_AppCompat_Dark",
                "R.style.Platform_ThemeOverlay_AppCompat_Light",
                "R.style.Platform_V21_AppCompat",
                "R.style.Platform_V21_AppCompat_Light",
                "R.style.Platform_V25_AppCompat",
                "R.style.Platform_V25_AppCompat_Light",
                "R.style.Platform_Widget_AppCompat_Spinner",
                "R.style.RtlOverlay_DialogWindowTitle_AppCompat",
                "R.style.RtlOverlay_Widget_AppCompat_ActionBar_TitleItem",
                "R.style.RtlOverlay_Widget_AppCompat_DialogTitle_Icon",
                "R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem",
                "R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup",
                "R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut",
                "R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow",
                "R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_Text",
                "R.style.RtlOverlay_Widget_AppCompat_PopupMenuItem_Title",
                "R.style.RtlOverlay_Widget_AppCompat_SearchView_MagIcon",
                "R.style.RtlOverlay_Widget_AppCompat_Search_DropDown",
                "R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1",
                "R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2",
                "R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Query",
                "R.style.RtlOverlay_Widget_AppCompat_Search_DropDown_Text",
                "R.style.RtlUnderlay_Widget_AppCompat_ActionButton",
                "R.style.RtlUnderlay_Widget_AppCompat_ActionButton_Overflow",
                "R.style.TextAppearance_AppCompat",
                "R.style.TextAppearance_AppCompat_Body1",
                "R.style.TextAppearance_AppCompat_Body2",
                "R.style.TextAppearance_AppCompat_Button",
                "R.style.TextAppearance_AppCompat_Caption",
                "R.style.TextAppearance_AppCompat_Display1",
                "R.style.TextAppearance_AppCompat_Display2",
                "R.style.TextAppearance_AppCompat_Display3",
                "R.style.TextAppearance_AppCompat_Display4",
                "R.style.TextAppearance_AppCompat_Headline",
                "R.style.TextAppearance_AppCompat_Inverse",
                "R.style.TextAppearance_AppCompat_Large",
                "R.style.TextAppearance_AppCompat_Large_Inverse",
                "R.style.TextAppearance_AppCompat_Light_SearchResult_Subtitle",
                "R.style.TextAppearance_AppCompat_Light_SearchResult_Title",
                "R.style.TextAppearance_AppCompat_Light_Widget_PopupMenu_Large",
                "R.style.TextAppearance_AppCompat_Light_Widget_PopupMenu_Small",
                "R.style.TextAppearance_AppCompat_Medium",
                "R.style.TextAppearance_AppCompat_Medium_Inverse",
                "R.style.TextAppearance_AppCompat_Menu",
                "R.style.TextAppearance_AppCompat_SearchResult_Subtitle",
                "R.style.TextAppearance_AppCompat_SearchResult_Title",
                "R.style.TextAppearance_AppCompat_Small",
                "R.style.TextAppearance_AppCompat_Small_Inverse",
                "R.style.TextAppearance_AppCompat_Subhead",
                "R.style.TextAppearance_AppCompat_Subhead_Inverse",
                "R.style.TextAppearance_AppCompat_Title",
                "R.style.TextAppearance_AppCompat_Title_Inverse",
                "R.style.TextAppearance_AppCompat_Tooltip",
                "R.style.TextAppearance_AppCompat_Widget_ActionBar_Menu",
                "R.style.TextAppearance_AppCompat_Widget_ActionBar_Subtitle",
                "R.style.TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse",
                "R.style.TextAppearance_AppCompat_Widget_ActionBar_Title",
                "R.style.TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse",
                "R.style.TextAppearance_AppCompat_Widget_ActionMode_Subtitle",
                "R.style.TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse",
                "R.style.TextAppearance_AppCompat_Widget_ActionMode_Title",
                "R.style.TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse",
                "R.style.TextAppearance_AppCompat_Widget_Button",
                "R.style.TextAppearance_AppCompat_Widget_Button_Borderless_Colored",
                "R.style.TextAppearance_AppCompat_Widget_Button_Colored",
                "R.style.TextAppearance_AppCompat_Widget_Button_Inverse",
                "R.style.TextAppearance_AppCompat_Widget_DropDownItem",
                "R.style.TextAppearance_AppCompat_Widget_PopupMenu_Header",
                "R.style.TextAppearance_AppCompat_Widget_PopupMenu_Large",
                "R.style.TextAppearance_AppCompat_Widget_PopupMenu_Small",
                "R.style.TextAppearance_AppCompat_Widget_Switch",
                "R.style.TextAppearance_AppCompat_Widget_TextView_SpinnerItem",
                "R.style.TextAppearance_Compat_Notification",
                "R.style.TextAppearance_Compat_Notification_Info",
                "R.style.TextAppearance_Compat_Notification_Info_Media",
                "R.style.TextAppearance_Compat_Notification_Line2",
                "R.style.TextAppearance_Compat_Notification_Line2_Media",
                "R.style.TextAppearance_Compat_Notification_Media",
                "R.style.TextAppearance_Compat_Notification_Time",
                "R.style.TextAppearance_Compat_Notification_Time_Media",
                "R.style.TextAppearance_Compat_Notification_Title",
                "R.style.TextAppearance_Compat_Notification_Title_Media",
                "R.style.TextAppearance_Widget_AppCompat_ExpandedMenu_Item",
                "R.style.TextAppearance_Widget_AppCompat_Toolbar_Subtitle",
                "R.style.TextAppearance_Widget_AppCompat_Toolbar_Title",
                "R.style.ThemeOverlay_AppCompat",
                "R.style.ThemeOverlay_AppCompat_ActionBar",
                "R.style.ThemeOverlay_AppCompat_Dark",
                "R.style.ThemeOverlay_AppCompat_Dark_ActionBar",
                "R.style.ThemeOverlay_AppCompat_Dialog",
                "R.style.ThemeOverlay_AppCompat_Dialog_Alert",
                "R.style.ThemeOverlay_AppCompat_Light",
                "R.style.Theme_AppCompat",
                "R.style.Theme_AppCompat_CompactMenu",
                "R.style.Theme_AppCompat_DayNight",
                "R.style.Theme_AppCompat_DayNight_DarkActionBar",
                "R.style.Theme_AppCompat_DayNight_Dialog",
                "R.style.Theme_AppCompat_DayNight_DialogWhenLarge",
                "R.style.Theme_AppCompat_DayNight_Dialog_Alert",
                "R.style.Theme_AppCompat_DayNight_Dialog_MinWidth",
                "R.style.Theme_AppCompat_DayNight_NoActionBar",
                "R.style.Theme_AppCompat_Dialog",
                "R.style.Theme_AppCompat_DialogWhenLarge",
                "R.style.Theme_AppCompat_Dialog_Alert",
                "R.style.Theme_AppCompat_Dialog_MinWidth",
                "R.style.Theme_AppCompat_Light",
                "R.style.Theme_AppCompat_Light_DarkActionBar",
                "R.style.Theme_AppCompat_Light_Dialog",
                "R.style.Theme_AppCompat_Light_DialogWhenLarge",
                "R.style.Theme_AppCompat_Light_Dialog_Alert",
                "R.style.Theme_AppCompat_Light_Dialog_MinWidth",
                "R.style.Theme_AppCompat_Light_NoActionBar",
                "R.style.Theme_AppCompat_NoActionBar",
                "R.style.Widget_AppCompat_ActionBar",
                "R.style.Widget_AppCompat_ActionBar_Solid",
                "R.style.Widget_AppCompat_ActionBar_TabBar",
                "R.style.Widget_AppCompat_ActionBar_TabText",
                "R.style.Widget_AppCompat_ActionBar_TabView",
                "R.style.Widget_AppCompat_ActionButton",
                "R.style.Widget_AppCompat_ActionButton_CloseMode",
                "R.style.Widget_AppCompat_ActionButton_Overflow",
                "R.style.Widget_AppCompat_ActionMode",
                "R.style.Widget_AppCompat_ActivityChooserView",
                "R.style.Widget_AppCompat_AutoCompleteTextView",
                "R.style.Widget_AppCompat_Button",
                "R.style.Widget_AppCompat_ButtonBar",
                "R.style.Widget_AppCompat_ButtonBar_AlertDialog",
                "R.style.Widget_AppCompat_Button_Borderless",
                "R.style.Widget_AppCompat_Button_Borderless_Colored",
                "R.style.Widget_AppCompat_Button_ButtonBar_AlertDialog",
                "R.style.Widget_AppCompat_Button_Colored",
                "R.style.Widget_AppCompat_Button_Small",
                "R.style.Widget_AppCompat_CompoundButton_CheckBox",
                "R.style.Widget_AppCompat_CompoundButton_RadioButton",
                "R.style.Widget_AppCompat_CompoundButton_Switch",
                "R.style.Widget_AppCompat_DrawerArrowToggle",
                "R.style.Widget_AppCompat_DropDownItem_Spinner",
                "R.style.Widget_AppCompat_EditText",
                "R.style.Widget_AppCompat_ImageButton",
                "R.style.Widget_AppCompat_Light_ActionBar",
                "R.style.Widget_AppCompat_Light_ActionBar_Solid",
                "R.style.Widget_AppCompat_Light_ActionBar_Solid_Inverse",
                "R.style.Widget_AppCompat_Light_ActionBar_TabBar",
                "R.style.Widget_AppCompat_Light_ActionBar_TabBar_Inverse",
                "R.style.Widget_AppCompat_Light_ActionBar_TabText",
                "R.style.Widget_AppCompat_Light_ActionBar_TabText_Inverse",
                "R.style.Widget_AppCompat_Light_ActionBar_TabView",
                "R.style.Widget_AppCompat_Light_ActionBar_TabView_Inverse",
                "R.style.Widget_AppCompat_Light_ActionButton",
                "R.style.Widget_AppCompat_Light_ActionButton_CloseMode",
                "R.style.Widget_AppCompat_Light_ActionButton_Overflow",
                "R.style.Widget_AppCompat_Light_ActionMode_Inverse",
                "R.style.Widget_AppCompat_Light_ActivityChooserView",
                "R.style.Widget_AppCompat_Light_AutoCompleteTextView",
                "R.style.Widget_AppCompat_Light_DropDownItem_Spinner",
                "R.style.Widget_AppCompat_Light_ListPopupWindow",
                "R.style.Widget_AppCompat_Light_ListView_DropDown",
                "R.style.Widget_AppCompat_Light_PopupMenu",
                "R.style.Widget_AppCompat_Light_PopupMenu_Overflow",
                "R.style.Widget_AppCompat_Light_SearchView",
                "R.style.Widget_AppCompat_Light_Spinner_DropDown_ActionBar",
                "R.style.Widget_AppCompat_ListMenuView",
                "R.style.Widget_AppCompat_ListPopupWindow",
                "R.style.Widget_AppCompat_ListView",
                "R.style.Widget_AppCompat_ListView_DropDown",
                "R.style.Widget_AppCompat_ListView_Menu",
                "R.style.Widget_AppCompat_PopupMenu",
                "R.style.Widget_AppCompat_PopupMenu_Overflow",
                "R.style.Widget_AppCompat_PopupWindow",
                "R.style.Widget_AppCompat_ProgressBar",
                "R.style.Widget_AppCompat_ProgressBar_Horizontal",
                "R.style.Widget_AppCompat_RatingBar",
                "R.style.Widget_AppCompat_RatingBar_Indicator",
                "R.style.Widget_AppCompat_RatingBar_Small",
                "R.style.Widget_AppCompat_SearchView",
                "R.style.Widget_AppCompat_SearchView_ActionBar",
                "R.style.Widget_AppCompat_SeekBar",
                "R.style.Widget_AppCompat_SeekBar_Discrete",
                "R.style.Widget_AppCompat_Spinner",
                "R.style.Widget_AppCompat_Spinner_DropDown",
                "R.style.Widget_AppCompat_Spinner_DropDown_ActionBar",
                "R.style.Widget_AppCompat_Spinner_Underlined",
                "R.style.Widget_AppCompat_TextView_SpinnerItem",
                "R.style.Widget_AppCompat_Toolbar",
                "R.style.Widget_AppCompat_Toolbar_Button_Navigation",
                "R.style.Widget_Compat_NotificationActionContainer",
                "R.style.Widget_Compat_NotificationActionText",
                "R.style.Widget_Support_CoordinatorLayout",
                "R.style.tt_appdownloader_style_detail_download_progress_bar",
                "R.style.tt_appdownloader_style_notification_text",
                "R.style.tt_appdownloader_style_notification_title",
                "R.style.tt_appdownloader_style_progress_bar",
                "R.style.tt_appdownloader_style_progress_bar_new",
                "R.style.ttdownloader_translucent_dialog",
                //穿山甲sdk资源-end

                //闪验一键登录--start
                "R.anim.umcsdk_anim_loading",
                "R.drawable.authbackground_image",
                "R.drawable.login_bg_gray",
                "R.drawable.oauth_anim_loading_dialog",
                "R.drawable.oauth_loading_bg",
                "R.drawable.progress_bar_states",
                "R.drawable.selector_button_cucc",
                "R.drawable.sy_sdk_left",
                "R.drawable.sysdk_anim",
                "R.drawable.umcsdk_*",
                "R.drawable.umcsdk_checkbox_bg.xml",
                "R.layout.activity_ctcc_privacy_protocol",
                "R.layout.activity_oauth",
                "R.layout.cmcc_navigationbar_back_layout",
                "R.layout.oauth_loading_dialog",
                "R.layout.shanyan_*",
                "R.layout.sysdk_activity_onekey_login",
                "R.id.loading_parent",
                "R.id.oauth_loading_dialog_img",
                "R.id.loading",
                "R.id.oauth_loading_dialog_txt",
                "R.id.sysdk_cucc_login_layout",
                "R.id.cuc_webview",
                "R.id.navigation_bar",
                "R.id.oauth_back",
                "R.id.oauth_title",
                "R.id.oauth_content",
                "R.id.navigation_bar_line",
                "R.id.app_name",
                "R.id.other_login",
                "R.id.login_before_text",
                "R.id.service_and_privacy",
                "R.id.authorize_app",
                "R.id.is_agree",
                "R.id.shanyan_navigationbar_include",
                "R.id.sy_cucc_boby",
                "R.id.oauth_logo",
                "R.id.oauth_mobile_et",
                "R.id.brand",
                "R.id.oauth_login",
                "R.id.protocol",
                "R.id.shanyan_onkeylogin_loading",
                "R.id.agreement_title",
                "R.id.ctcc_agreement_back",
                "R.id.oauth_help",
                "R.id.baseweb_webview",
                "R.id.sysdk_ctcc_login_layout",
                "R.id.sysdk_login_boby",
                "R.id.sysdk_authority_finish",
                "R.id.sysdk_log_image",
                "R.id.tv_per_code",
                "R.id.bt_one_key_login",
                "R.id.shanyan_privacy_include",
                "R.id.sysdk_identify_tv",
                "R.id.shanyan_*",
                "R.anim.umcsdk*",
                "R.drawable.umcsdk*",
                "R.layout.layout_shanyan*",
                "R.id.shanyan_view*",
                //闪验一键登录--end

                //快手广告联盟--start
                "R.layout.ksad_*",
                "R.id.ksad_*",
                "R.style.ksad_*",
                "R.drawable.ksad_*",
                "R.string.ksad_*",
                "R.color.ksad_*",
                "R.attr.ksad_*",
                "R.dimen.ksad_*",
                //快手广告联盟--end
//            Huawei push
                "R.string.hms_*",
                "R.string.connect_server_fail_prompt_toast",
                "R.string.getting_message_fail_prompt_toast",
                "R.string.no_available_network_prompt_toast",
                "R.string.third_app_*",
                "R.string.upsdk_*",
                "R.style.upsdkDlDialog",
                "R.style.AppTheme",
                "R.style.AppBaseTheme",
                "R.dimen.upsdk_dialog_*",
                "R.color.upsdk_*",
                "R.layout.upsdk_*",
                "R.drawable.upsdk_*",
                "R.drawable.hms_*",
                "R.layout.hms_*",
                "R.id.hms_*",

//            Umeng sdk
                "R.anim.umeng*",
                "R.string.umeng*",
                "R.string.UM*",
                "R.string.tb_*",
                "R.layout.umeng*",
                "R.layout.socialize_*",
                "R.layout.*messager*",
                "R.layout.tb_*",
                "R.color.umeng*",
                "R.color.tb_*",
                "R.style.*UM*",
                "R.style.umeng*",
                "R.drawable.umeng*",
                "R.drawable.tb_*",
                "R.drawable.sina*",
                "R.drawable.qq_*",
                "R.drawable.tb_*",
                "R.id.umeng*",
                "R.id.*messager*",
                "R.id.progress_bar_parent",
                "R.id.socialize_*",
                "R.id.webView",
                /// host 中一些反射调用
                "R.drawable.ting",
                "R.drawable.notification_icon",
                "R.id.host_reflect_alarm_later_title",
                "R.drawable.reflect_player_btn_fullsize",
                "R.layout.host_reflect_appwidget_layout",
                "R.id.appwidget_playOrPause",
                "R.id.appwidget_pre",
                "R.id.appwidget_next",
                "R.id.appwidget_icon_small",
                "R.id.ll_open_app",
                "R.id.appwidget_playOrPause",
                "R.id.appwidget_name",
                "R.id.appwidget_title",
                "R.drawable.host_reflect_widget_pause",
                "R.drawable.host_reflect_widget_play",
                "R.drawable.reflect_player_cover_default",
                "R.drawable.host_shortcut_*",
                /// emotionUtil，表情
                "R.drawable.d_*",
                "R.drawable.f_*",
                "R.drawable.h_*",
                "R.drawable.l_*",
                "R.drawable.o_*",
                "R.drawable.w_*",

                "R.drawable.reflect_player_cover_default",
                // 阿里认证
                "R.drawable.yw_1222_146f",
                //

                // 小游戏 sdk 资源混淆
                "R.layout.leto_*",
                "R.id.leto_*",
                "R.style.leto_*",
                "R.drawable.leto_*",
                "R.string.leto_*",
                "R.color.leto_*",
                "R.attr.leto_*",
                "R.dimen.leto_*",
                "R.attr.mgc_*",
                "R.xml.leto_*",

                // 爱奇艺资源
                "R.layout.activity_qy_*",
                "R.layout.layout_qy_*",
                "R.id.qy_*",
                "R.string.qy_*",
                "R.dimen.qy_*",
                "R.styleable.qy_*",
                "R.drawable.qy_*",
                "R.color.qy_*",
                "R.style.QyCustomDialog",
                "R.style.Theme_QyLandingPage",
                "R.style.Theme_QyTrueView",
                "R.layout.activity_qy_trueview",

                // 广点通sdk
                "R.layout.gdt_ic*",
                "R.id.gdt_ic*",
                "R.style.gdt_ic*",
                "R.drawable.gdt_ic*",
                "R.drawable.gdt*",
                "R.string.gdt_ic*",
                "R.color.gdt_ic*",
                "R.attr.gdt_ic*",
                "R.dimen.gdt_ic*",
                "R.attr.gdt_ic*",
                "R.xml.gdt_ic*",

                // 小程序
                "R.anim.host_lite_*",
                // dsl
                "R.drawable.dsl_*",

                // 穿山甲 SDK包中whiteList.txt 白名单上的资源不支持混淆
                "R.anim.tt_dislike_animation_dismiss",
                "R.anim.tt_dislike_animation_show",
                "R.color.appdownloader_notification_material_background_color",
                "R.color.appdownloader_notification_title_color",
                "R.color.appdownloader_s1",
                "R.color.appdownloader_s13",
                "R.color.appdownloader_s18",
                "R.color.appdownloader_s4",
                "R.color.appdownloader_s8",
                "R.color.tt_app_detail_bg",
                "R.color.tt_app_detail_line_bg",
                "R.color.tt_app_detail_privacy_text_bg",
                "R.color.tt_app_detail_stroke_bg",
                "R.color.tt_cancle_bg",
                "R.color.tt_common_download_bg",
                "R.color.tt_common_download_btn_bg",
                "R.color.tt_dislike_dialog_background",
                "R.color.tt_dislike_transparent",
                "R.color.tt_divider",
                "R.color.tt_download_app_name",
                "R.color.tt_download_bar_background",
                "R.color.tt_download_bar_background_new",
                "R.color.tt_download_text_background",
                "R.color.tt_draw_btn_back",
                "R.color.tt_full_screen_skip_bg",
                "R.color.tt_header_font",
                "R.color.tt_heise3",
                "R.color.tt_listview",
                "R.color.tt_listview_press",
                "R.color.tt_rating_comment",
                "R.color.tt_rating_comment_vertical",
                "R.color.tt_rating_star",
                "R.color.tt_skip_red",
                "R.color.tt_ssxinbaise4",
                "R.color.tt_ssxinbaise4_press",
                "R.color.tt_ssxinheihui3",
                "R.color.tt_ssxinhongse1",
                "R.color.tt_ssxinmian1",
                "R.color.tt_ssxinmian11",
                "R.color.tt_ssxinmian15",
                "R.color.tt_ssxinmian6",
                "R.color.tt_ssxinmian7",
                "R.color.tt_ssxinmian8",
                "R.color.tt_ssxinxian11",
                "R.color.tt_ssxinxian11_selected",
                "R.color.tt_ssxinxian3",
                "R.color.tt_ssxinxian3_press",
                "R.color.tt_ssxinzi12",
                "R.color.tt_ssxinzi15",
                "R.color.tt_ssxinzi4",
                "R.color.tt_ssxinzi9",
                "R.color.tt_text_font",
                "R.color.tt_titlebar_background_dark",
                "R.color.tt_titlebar_background_ffffff",
                "R.color.tt_titlebar_background_light",
                "R.color.tt_trans_black",
                "R.color.tt_trans_half_black",
                "R.color.tt_transparent",
                "R.color.tt_video_player_text",
                "R.color.tt_video_player_text_withoutnight",
                "R.color.tt_video_playerbg_color",
                "R.color.tt_video_shadow_color",
                "R.color.tt_video_shaoow_color_fullscreen",
                "R.color.tt_video_time_color",
                "R.color.tt_video_traffic_tip_background_color",
                "R.color.tt_video_transparent",
                "R.color.tt_white",
                "R.color.ttdownloader_transparent",
                "R.dimen.tt_video_container_maxheight",
                "R.dimen.tt_video_container_minheight",
                "R.dimen.tt_video_cover_padding_horizon",
                "R.dimen.tt_video_cover_padding_vertical",
                "R.drawable.appdownloader_action_bg",
                "R.drawable.appdownloader_action_new_bg",
                "R.drawable.appdownloader_ad_detail_download_progress",
                "R.drawable.appdownloader_detail_download_success_bg",
                "R.drawable.appdownloader_download_progress_bar_horizontal",
                "R.drawable.appdownloader_download_progress_bar_horizontal_new",
                "R.drawable.appdownloader_download_progress_bar_horizontal_night",
                "R.drawable.tt_ad_backup_bk",
                "R.drawable.tt_ad_backup_bk2",
                "R.drawable.tt_ad_cover_btn_begin_bg",
                "R.drawable.tt_ad_cover_btn_draw_begin_bg",
                "R.drawable.tt_ad_download_progress_bar_horizontal",
                "R.drawable.tt_ad_logo",
                "R.drawable.tt_ad_logo_background",
                "R.drawable.tt_ad_logo_reward_full",
                "R.drawable.tt_ad_logo_small",
                "R.drawable.tt_ad_logo_small_rectangle",
                "R.drawable.tt_ad_skip_btn_bg",
                "R.drawable.tt_app_detail_back_btn",
                "R.drawable.tt_app_detail_bg",
                "R.drawable.tt_app_detail_black",
                "R.drawable.tt_app_detail_info",
                "R.drawable.tt_back_video",
                "R.drawable.tt_backup_btn_1",
                "R.drawable.tt_backup_btn_2",
                "R.drawable.tt_browser_download_selector",
                "R.drawable.tt_browser_progress_style",
                "R.drawable.tt_circle_solid_mian",
                "R.drawable.tt_close_move_detail",
                "R.drawable.tt_close_move_details_normal",
                "R.drawable.tt_close_move_details_pressed",
                "R.drawable.tt_comment_tv",
                "R.drawable.tt_common_download_bg",
                "R.drawable.tt_common_download_btn_bg",
                "R.drawable.tt_custom_dialog_bg",
                "R.drawable.tt_detail_video_btn_bg",
                "R.drawable.tt_dislike_bottom_seletor",
                "R.drawable.tt_dislike_cancle_bg_selector",
                "R.drawable.tt_dislike_dialog_bg",
                "R.drawable.tt_dislike_flowlayout_tv_bg",
                "R.drawable.tt_dislike_icon",
                "R.drawable.tt_dislike_icon2",
                "R.drawable.tt_dislike_middle_seletor",
                "R.drawable.tt_dislike_son_tag",
                "R.drawable.tt_dislike_top_bg",
                "R.drawable.tt_dislike_top_seletor",
                "R.drawable.tt_download_btn_bg",
                "R.drawable.tt_download_corner_bg",
                "R.drawable.tt_download_dialog_btn_bg",
                "R.drawable.tt_draw_back_bg",
                "R.drawable.tt_enlarge_video",
                "R.drawable.tt_forward_video",
                "R.drawable.tt_install_bk",
                "R.drawable.tt_install_btn_bk",
                "R.drawable.tt_leftbackbutton_titlebar_photo_preview",
                "R.drawable.tt_leftbackicon_selector",
                "R.drawable.tt_leftbackicon_selector_for_dark",
                "R.drawable.tt_lefterbackicon_titlebar",
                "R.drawable.tt_lefterbackicon_titlebar_for_dark",
                "R.drawable.tt_lefterbackicon_titlebar_press",
                "R.drawable.tt_lefterbackicon_titlebar_press_for_dark",
                "R.drawable.tt_mute",
                "R.drawable.tt_mute_btn_bg",
                "R.drawable.tt_new_pause_video",
                "R.drawable.tt_new_pause_video_press",
                "R.drawable.tt_new_play_video",
                "R.drawable.tt_normalscreen_loading",
                "R.drawable.tt_open_app_detail_download_btn_bg",
                "R.drawable.tt_open_app_detail_list_item",
                "R.drawable.tt_play_movebar_textpage",
                "R.drawable.tt_playable_btn_bk",
                "R.drawable.tt_playable_l_logo",
                "R.drawable.tt_playable_progress_style",
                "R.drawable.tt_refreshing_video_textpage",
                "R.drawable.tt_refreshing_video_textpage_normal",
                "R.drawable.tt_refreshing_video_textpage_pressed",
                "R.drawable.tt_reward_countdown_bg",
                "R.drawable.tt_reward_dislike_icon",
                "R.drawable.tt_reward_full_new_bar_bg",
                "R.drawable.tt_reward_full_new_bar_btn_bg",
                "R.drawable.tt_reward_full_video_backup_btn_bg",
                "R.drawable.tt_reward_video_download_btn_bg",
                "R.drawable.tt_seek_progress",
                "R.drawable.tt_seek_thumb",
                "R.drawable.tt_seek_thumb_fullscreen",
                "R.drawable.tt_seek_thumb_fullscreen_press",
                "R.drawable.tt_seek_thumb_fullscreen_selector",
                "R.drawable.tt_seek_thumb_normal",
                "R.drawable.tt_seek_thumb_press",
                "R.drawable.tt_shadow_btn_back",
                "R.drawable.tt_shadow_btn_back_withoutnight",
                "R.drawable.tt_shadow_fullscreen_top",
                "R.drawable.tt_shadow_lefterback_titlebar",
                "R.drawable.tt_shadow_lefterback_titlebar_press",
                "R.drawable.tt_shadow_lefterback_titlebar_press_withoutnight",
                "R.drawable.tt_shadow_lefterback_titlebar_withoutnight",
                "R.drawable.tt_shrink_fullscreen",
                "R.drawable.tt_shrink_video",
                "R.drawable.tt_skip_text_bg",
                "R.drawable.tt_splash_ad_logo",
                "R.drawable.tt_splash_mute",
                "R.drawable.tt_splash_unmute",
                "R.drawable.tt_star_empty_bg",
                "R.drawable.tt_star_full_bg",
                "R.drawable.tt_stop_movebar_textpage",
                "R.drawable.tt_suggestion_logo",
                "R.drawable.tt_titlebar_close_drawable",
                "R.drawable.tt_titlebar_close_for_dark",
                "R.drawable.tt_titlebar_close_press",
                "R.drawable.tt_titlebar_close_press_for_dark",
                "R.drawable.tt_titlebar_close_seletor",
                "R.drawable.tt_titlebar_close_seletor_for_dark",
                "R.drawable.tt_unmute",
                "R.drawable.tt_video_black_desc_gradient",
                "R.drawable.tt_video_close_drawable",
                "R.drawable.tt_video_loading_progress_bar",
                "R.drawable.tt_video_progress_drawable",
                "R.drawable.tt_video_traffic_continue_play_bg",
                "R.drawable.tt_white_lefterbackicon_titlebar",
                "R.drawable.tt_white_lefterbackicon_titlebar_press",
                "R.drawable.ttdownloader_bg_ad_corner_red_button",
                "R.drawable.ttdownloader_bg_ad_install_vivo",
                "R.drawable.ttdownloader_bg_ad_left_corner_gray",
                "R.drawable.ttdownloader_bg_ad_right_corner_gray",
                "R.drawable.ttdownloader_bg_ad_white_top_corner",
                "R.drawable.ttdownloader_bg_button_blue_corner",
                "R.drawable.ttdownloader_bg_huawei_btn1",
                "R.drawable.ttdownloader_bg_huawei_btn2",
                "R.drawable.ttdownloader_bg_kllk_btn1",
                "R.drawable.ttdownloader_bg_kllk_btn2",
                "R.drawable.ttdownloader_bg_phone_border",
                "R.drawable.ttdownloader_bg_phone_cover",
                "R.drawable.ttdownloader_bg_transparent",
                "R.drawable.ttdownloader_bg_vivo_btn1",
                "R.drawable.ttdownloader_bg_vivo_btn2",
                "R.drawable.ttdownloader_bg_white_corner",
                "R.drawable.ttdownloader_icon_finger",
                "R.id.app_icon_iv",
                "R.id.app_name_tv",
                "R.id.app_store_tv",
                "R.id.appdownloader_action",
                "R.id.appdownloader_desc",
                "R.id.appdownloader_download_progress",
                "R.id.appdownloader_download_progress_new",
                "R.id.appdownloader_download_size",
                "R.id.appdownloader_download_status",
                "R.id.appdownloader_download_success",
                "R.id.appdownloader_download_success_size",
                "R.id.appdownloader_download_success_status",
                "R.id.appdownloader_download_text",
                "R.id.appdownloader_icon",
                "R.id.appdownloader_root",
                "R.id.cancel_tv",
                "R.id.confirm_tv",
                "R.id.content_ll",
                "R.id.install_app_tv",
                "R.id.install_dialog_click_layout",
                "R.id.install_dialog_description",
                "R.id.install_hijack_view",
                "R.id.install_huawei_btn2",
                "R.id.install_kllk_btn1",
                "R.id.install_kllk_btn2",
                "R.id.kllk_install_tv",
                "R.id.local_install_hijack_layout",
                "R.id.tag_ignore",
                "R.id.tag_view_name",
                "R.id.tt_ad_container",
                "R.id.tt_ad_content_layout",
                "R.id.tt_ad_logo",
                "R.id.tt_app_detail_back_tv",
                "R.id.tt_app_developer_tv",
                "R.id.tt_app_name_tv",
                "R.id.tt_app_privacy_back_tv",
                "R.id.tt_app_privacy_title",
                "R.id.tt_app_privacy_tv",
                "R.id.tt_app_privacy_url_tv",
                "R.id.tt_app_version_tv",
                "R.id.tt_backup_draw_bg",
                "R.id.tt_battery_time_layout",
                "R.id.tt_browser_download_btn",
                "R.id.tt_browser_download_btn_stub",
                "R.id.tt_browser_progress",
                "R.id.tt_browser_titlebar_dark_view_stub",
                "R.id.tt_browser_titlebar_view_stub",
                "R.id.tt_browser_webview",
                "R.id.tt_browser_webview_loading",
                "R.id.tt_bu_close",
                "R.id.tt_bu_desc",
                "R.id.tt_bu_dislike",
                "R.id.tt_bu_download",
                "R.id.tt_bu_icon",
                "R.id.tt_bu_img",
                "R.id.tt_bu_img_1",
                "R.id.tt_bu_img_2",
                "R.id.tt_bu_img_3",
                "R.id.tt_bu_img_container",
                "R.id.tt_bu_img_content",
                "R.id.tt_bu_name",
                "R.id.tt_bu_score",
                "R.id.tt_bu_score_bar",
                "R.id.tt_bu_title",
                "R.id.tt_bu_video_container",
                "R.id.tt_bu_video_container_inner",
                "R.id.tt_bu_video_icon",
                "R.id.tt_bu_video_name1",
                "R.id.tt_bu_video_name2",
                "R.id.tt_bu_video_score",
                "R.id.tt_bu_video_score_bar",
                "R.id.tt_click_lower_non_content_layout",
                "R.id.tt_click_upper_non_content_layout",
                "R.id.tt_column_line",
                "R.id.tt_comment_backup",
                "R.id.tt_comment_close",
                "R.id.tt_comment_commit",
                "R.id.tt_comment_content",
                "R.id.tt_comment_number",
                "R.id.tt_comment_vertical",
                "R.id.tt_dislike_dialog_linearlayout",
                "R.id.tt_dislike_header_back",
                "R.id.tt_dislike_header_tv",
                "R.id.tt_dislike_line1",
                "R.id.tt_dislike_title_content",
                "R.id.tt_download_app_btn",
                "R.id.tt_download_app_detail",
                "R.id.tt_download_app_developer",
                "R.id.tt_download_app_privacy",
                "R.id.tt_download_app_version",
                "R.id.tt_download_btn",
                "R.id.tt_download_cancel",
                "R.id.tt_download_icon",
                "R.id.tt_download_layout",
                "R.id.tt_download_title",
                "R.id.tt_edit_suggestion",
                "R.id.tt_filer_words_lv",
                "R.id.tt_filer_words_lv_second",
                "R.id.tt_image",
                "R.id.tt_insert_ad_img",
                "R.id.tt_insert_ad_logo",
                "R.id.tt_insert_ad_text",
                "R.id.tt_insert_dislike_icon_img",
                "R.id.tt_insert_express_ad_fl",
                "R.id.tt_install_btn_no",
                "R.id.tt_install_btn_yes",
                "R.id.tt_install_content",
                "R.id.tt_install_title",
                "R.id.tt_item_desc_tv",
                "R.id.tt_item_select_img",
                "R.id.tt_item_title_tv",
                "R.id.tt_item_tv",
                "R.id.tt_item_tv_son",
                "R.id.tt_message",
                "R.id.tt_native_video_container",
                "R.id.tt_native_video_frame",
                "R.id.tt_native_video_img_cover",
                "R.id.tt_native_video_img_cover_viewStub",
                "R.id.tt_native_video_img_id",
                "R.id.tt_native_video_layout",
                "R.id.tt_native_video_play",
                "R.id.tt_native_video_titlebar",
                "R.id.tt_negtive",
                "R.id.tt_open_app_detail_layout",
                "R.id.tt_playable_ad_close",
                "R.id.tt_playable_ad_close_layout",
                "R.id.tt_playable_loading",
                "R.id.tt_playable_pb_view",
                "R.id.tt_playable_play",
                "R.id.tt_playable_progress_tip",
                "R.id.tt_playable_view",
                "R.id.tt_positive",
                "R.id.tt_privacy_layout",
                "R.id.tt_privacy_list",
                "R.id.tt_privacy_webview",
                "R.id.tt_rb_score",
                "R.id.tt_rb_score_backup",
                "R.id.tt_reward_ad_appname",
                "R.id.tt_reward_ad_appname_backup",
                "R.id.tt_reward_ad_download",
                "R.id.tt_reward_ad_download_backup",
                "R.id.tt_reward_ad_download_layout",
                "R.id.tt_reward_ad_icon",
                "R.id.tt_reward_ad_icon_backup",
                "R.id.tt_reward_browser_webview",
                "R.id.tt_reward_full_endcard_backup",
                "R.id.tt_reward_playable_loading",
                "R.id.tt_reward_root",
                "R.id.tt_rl_download",
                "R.id.tt_root_view",
                "R.id.tt_scroll_view",
                "R.id.tt_splash_ad_gif",
                "R.id.tt_splash_express_container",
                "R.id.tt_splash_skip_btn",
                "R.id.tt_splash_video_ad_mute",
                "R.id.tt_splash_video_container",
                "R.id.tt_title",
                "R.id.tt_titlebar_app_detail",
                "R.id.tt_titlebar_app_name",
                "R.id.tt_titlebar_app_privacy",
                "R.id.tt_titlebar_back",
                "R.id.tt_titlebar_close",
                "R.id.tt_titlebar_detail_layout",
                "R.id.tt_titlebar_developer",
                "R.id.tt_titlebar_dislike",
                "R.id.tt_titlebar_title",
                "R.id.tt_top_countdown",
                "R.id.tt_top_dislike",
                "R.id.tt_top_layout_proxy",
                "R.id.tt_top_mute",
                "R.id.tt_top_skip",
                "R.id.tt_video_ad_bottom_layout",
                "R.id.tt_video_ad_button",
                "R.id.tt_video_ad_button_draw",
                "R.id.tt_video_ad_close",
                "R.id.tt_video_ad_close_layout",
                "R.id.tt_video_ad_cover",
                "R.id.tt_video_ad_cover_center_layout",
                "R.id.tt_video_ad_cover_center_layout_draw",
                "R.id.tt_video_ad_covers",
                "R.id.tt_video_ad_finish_cover_image",
                "R.id.tt_video_ad_full_screen",
                "R.id.tt_video_ad_logo_image",
                "R.id.tt_video_ad_name",
                "R.id.tt_video_ad_replay",
                "R.id.tt_video_app_detail",
                "R.id.tt_video_app_detail_layout",
                "R.id.tt_video_app_name",
                "R.id.tt_video_app_privacy",
                "R.id.tt_video_back",
                "R.id.tt_video_btn_ad_image_tv",
                "R.id.tt_video_close",
                "R.id.tt_video_current_time",
                "R.id.tt_video_developer",
                "R.id.tt_video_draw_layout_viewStub",
                "R.id.tt_video_fullscreen_back",
                "R.id.tt_video_loading_cover_image",
                "R.id.tt_video_loading_progress",
                "R.id.tt_video_loading_retry",
                "R.id.tt_video_loading_retry_layout",
                "R.id.tt_video_play",
                "R.id.tt_video_progress",
                "R.id.tt_video_retry",
                "R.id.tt_video_retry_des",
                "R.id.tt_video_reward_bar",
                "R.id.tt_video_reward_container",
                "R.id.tt_video_seekbar",
                "R.id.tt_video_time_left_time",
                "R.id.tt_video_time_play",
                "R.id.tt_video_title",
                "R.id.tt_video_top_layout",
                "R.id.tt_video_top_title",
                "R.id.tt_video_traffic_continue_play_btn",
                "R.id.tt_video_traffic_continue_play_tv",
                "R.id.tt_video_traffic_tip_layout",
                "R.id.tt_video_traffic_tip_layout_viewStub",
                "R.id.tt_video_traffic_tip_tv",
                "R.id.web_frame",
                "R.integer.tt_video_progress_max",
                "R.layout.appdownloader_notification_layout",
                "R.layout.tt_activity_full_video",
                "R.layout.tt_activity_full_video_new_bar_3_style",
                "R.layout.tt_activity_full_video_newstyle",
                "R.layout.tt_activity_reward_and_full_video_bar",
                "R.layout.tt_activity_reward_and_full_video_new_bar",
                "R.layout.tt_activity_reward_video_newstyle",
                "R.layout.tt_activity_rewardvideo",
                "R.layout.tt_activity_rewardvideo_new_bar_3_style",
                "R.layout.tt_activity_ttlandingpage",
                "R.layout.tt_activity_ttlandingpage_playable",
                "R.layout.tt_activity_video_scroll_landingpage",
                "R.layout.tt_activity_videolandingpage",
                "R.layout.tt_app_detail_dialog",
                "R.layout.tt_app_detail_full_dialog",
                "R.layout.tt_app_detail_full_dialog_list_head",
                "R.layout.tt_app_detail_listview_item",
                "R.layout.tt_app_privacy_dialog",
                "R.layout.tt_backup_ad",
                "R.layout.tt_backup_ad1",
                "R.layout.tt_backup_ad2",
                "R.layout.tt_backup_banner_layout1",
                "R.layout.tt_backup_banner_layout2",
                "R.layout.tt_backup_banner_layout3",
                "R.layout.tt_backup_draw",
                "R.layout.tt_backup_feed_horizontal",
                "R.layout.tt_backup_feed_img_group",
                "R.layout.tt_backup_feed_img_small",
                "R.layout.tt_backup_feed_vertical",
                "R.layout.tt_backup_feed_video",
                "R.layout.tt_backup_full_reward",
                "R.layout.tt_backup_insert_layout1",
                "R.layout.tt_backup_insert_layout2",
                "R.layout.tt_backup_insert_layout3",
                "R.layout.tt_browser_download_layout",
                "R.layout.tt_browser_titlebar",
                "R.layout.tt_browser_titlebar_for_dark",
                "R.layout.tt_common_download_dialog",
                "R.layout.tt_custom_dailog_layout",
                "R.layout.tt_dialog_listview_item",
                "R.layout.tt_dislike_comment_layout",
                "R.layout.tt_dislike_dialog_layout",
                "R.layout.tt_dislike_dialog_layout1",
                "R.layout.tt_dislike_dialog_layout2",
                "R.layout.tt_dislike_flowlayout_tv",
                "R.layout.tt_insert_ad_layout",
                "R.layout.tt_install_dialog_layout",
                "R.layout.tt_native_video_ad_view",
                "R.layout.tt_native_video_img_cover_layout",
                "R.layout.tt_playable_loading_layout",
                "R.layout.tt_playable_view_layout",
                "R.layout.tt_splash_view",
                "R.layout.tt_top_full_1",
                "R.layout.tt_top_reward_1",
                "R.layout.tt_top_reward_dislike_2",
                "R.layout.tt_video_ad_cover_layout",
                "R.layout.tt_video_detail_layout",
                "R.layout.tt_video_draw_btn_layout",
                "R.layout.tt_video_play_layout_for_live",
                "R.layout.tt_video_traffic_tip",
                "R.layout.tt_video_traffic_tips_layout",
                "R.layout.ttdownloader_dialog_apk_install_guide",
                "R.layout.ttdownloader_dialog_reserve_wifi",
                "R.layout.ttdownloader_layout_install_hijack_huawei",
                "R.layout.ttdownloader_layout_install_hijack_kllk",
                "R.layout.ttdownloader_layout_install_hijack_vivo",
                "R.layout.ttdownloader_layout_install_hijack_xiaomi",
                "R.string.app_name",
                "R.string.appdownloader_button_cancel_download",
                "R.string.appdownloader_button_queue_for_wifi",
                "R.string.appdownloader_button_start_now",
                "R.string.appdownloader_download_percent",
                "R.string.appdownloader_download_remaining",
                "R.string.appdownloader_download_unknown_title",
                "R.string.appdownloader_duration_hours",
                "R.string.appdownloader_duration_minutes",
                "R.string.appdownloader_duration_seconds",
                "R.string.appdownloader_jump_unknown_source",
                "R.string.appdownloader_label_cancel",
                "R.string.appdownloader_label_ok",
                "R.string.appdownloader_notification_download",
                "R.string.appdownloader_notification_download_complete_open",
                "R.string.appdownloader_notification_download_complete_with_install",
                "R.string.appdownloader_notification_download_complete_without_install",
                "R.string.appdownloader_notification_download_delete",
                "R.string.appdownloader_notification_download_failed",
                "R.string.appdownloader_notification_download_install",
                "R.string.appdownloader_notification_download_open",
                "R.string.appdownloader_notification_download_pause",
                "R.string.appdownloader_notification_download_restart",
                "R.string.appdownloader_notification_download_resume",
                "R.string.appdownloader_notification_download_space_failed",
                "R.string.appdownloader_notification_download_waiting_net",
                "R.string.appdownloader_notification_download_waiting_wifi",
                "R.string.appdownloader_notification_downloading",
                "R.string.appdownloader_notification_install_finished_open",
                "R.string.appdownloader_notification_need_wifi_for_size",
                "R.string.appdownloader_notification_paused_in_background",
                "R.string.appdownloader_notification_pausing",
                "R.string.appdownloader_notification_prepare",
                "R.string.appdownloader_notification_request_btn_no",
                "R.string.appdownloader_notification_request_btn_yes",
                "R.string.appdownloader_notification_request_message",
                "R.string.appdownloader_notification_request_title",
                "R.string.appdownloader_notification_waiting_download_complete_handler",
                "R.string.appdownloader_tip",
                "R.string.appdownloader_wifi_recommended_body",
                "R.string.appdownloader_wifi_recommended_title",
                "R.string.appdownloader_wifi_required_body",
                "R.string.appdownloader_wifi_required_title",
                "R.string.tt_00_00",
                "R.string.tt_ad",
                "R.string.tt_ad_logo_txt",
                "R.string.tt_app_name",
                "R.string.tt_app_privacy_dialog_title",
                "R.string.tt_auto_play_cancel_text",
                "R.string.tt_cancel",
                "R.string.tt_comment_num",
                "R.string.tt_comment_num_backup",
                "R.string.tt_comment_score",
                "R.string.tt_common_download_app_detail",
                "R.string.tt_common_download_app_privacy",
                "R.string.tt_common_download_cancel",
                "R.string.tt_confirm_download",
                "R.string.tt_confirm_download_have_app_name",
                "R.string.tt_dislike_header_tv_back",
                "R.string.tt_dislike_header_tv_title",
                "R.string.tt_full_screen_skip_tx",
                "R.string.tt_label_cancel",
                "R.string.tt_label_ok",
                "R.string.tt_no_network",
                "R.string.tt_open_app_detail_developer",
                "R.string.tt_open_app_detail_privacy",
                "R.string.tt_open_app_detail_privacy_list",
                "R.string.tt_open_app_name",
                "R.string.tt_open_app_version",
                "R.string.tt_open_landing_page_app_name",
                "R.string.tt_permission_denied",
                "R.string.tt_playable_btn_play",
                "R.string.tt_request_permission_descript_external_storage",
                "R.string.tt_request_permission_descript_location",
                "R.string.tt_request_permission_descript_read_phone_state",
                "R.string.tt_reward_feedback",
                "R.string.tt_reward_screen_skip_tx",
                "R.string.tt_splash_skip_tv_text",
                "R.string.tt_tip",
                "R.string.tt_unlike",
                "R.string.tt_video_bytesize",
                "R.string.tt_video_bytesize_M",
                "R.string.tt_video_bytesize_MB",
                "R.string.tt_video_continue_play",
                "R.string.tt_video_dial_phone",
                "R.string.tt_video_dial_replay",
                "R.string.tt_video_download_apk",
                "R.string.tt_video_mobile_go_detail",
                "R.string.tt_video_retry_des_txt",
                "R.string.tt_video_without_wifi_tips",
                "R.string.tt_web_title_default",
                "R.string.tt_will_play",
                "R.style.DialogFullscreen",
                "R.style.EditTextStyle",
                "R.style.Theme_Dialog_TTDownload",
                "R.style.Theme_Dialog_TTDownloadOld",
                "R.style.appdownloader_style_detail_download_progress_bar",
                "R.style.appdownloader_style_notification_text",
                "R.style.appdownloader_style_notification_title",
                "R.style.appdownloader_style_progress_bar",
                "R.style.appdownloader_style_progress_bar_new",
                "R.style.tt_Widget_ProgressBar_Horizontal",
                "R.style.tt_back_view",
                "R.style.tt_custom_dialog",
                "R.style.tt_dislikeDialog",
                "R.style.tt_dislikeDialogAnimation",
                "R.style.tt_dislikeDialog_new",
                "R.style.tt_ss_popup_toast_anim",
                "R.style.tt_wg_insert_dialog",
                "R.style.tt_widget_gifView",
                "R.style.ttdownloader_translucent_dialog",

                // 广告SDK begin
                "R.layout.xm_ad_*",
                "R.id.xm_ad_*",
                "R.style.xm_ad_*",
                "R.drawable.xm_ad_*",
                "R.string.xm_ad_*",
                "R.color.xm_ad_*",
                "R.attr.xm_ad_*",
                "R.dimen.xm_ad_*",
                "R.xml.xm_ad_*",
                // 广告SDK end
                //华为捐赠start
                "R.string.hms*",
                "R.string.connect_server_fail_prompt_toast",
                "R.string.getting_message_fail_prompt_toast",
                "R.string.no_available_network_prompt_toast",
                "R.string.third_app_*",
                "R.string.upsdk_*",
                "R.layout.hms*",
                "R.layout.upsdk_*",
                "R.drawable.upsdk*",
                "R.color.upsdk*",
                "R.dimen.upsdk*",
                "R.style.upsdk*",
                "R.string.agc*",
                //华为捐赠end

                // jpush start
                "R.string.hms*",
                "R.string.connect_server_fail_prompt_toast",
                "R.string.getting_message_fail_prompt_toast",
                "R.string.no_available_network_prompt_toast",
                "R.string.third_app_*",
                "R.string.upsdk_*",
                "R.layout.hms*",
                "R.layout.upsdk_*",
                "R.drawable.upsdk*",
                "R.color.upsdk*",
                "R.dimen.upsdk*",
                "R.style.upsdk*",
                "R.string.agc*",
                "R.style.JPushTheme",
                "R.xml.jpush*",
                "R.drawable.jpush*",
                "R.layout.jpush*",
                "R.layout.push*",
                "R.string.jg*",
                "R.style.MyDialogStyle"
                // jpush end
        ]
    }

    assetsCheck {
        enable false // 冗余assets资源检查开关
        keepBySuffix = [
                ".model",
                ".otf",
                ".ttf"
        ]
        keepAssets = [
                "start_anim/",
                "Contour_2D/",
        ]
    }
}