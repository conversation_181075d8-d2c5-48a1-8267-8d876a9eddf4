Change Log
==========

Version 9.3.93.3 *(2025-8-6)*
-----------------------------
直播

* Feature【增长】【直播间内】优化背景审核速度
* Feature【增长】播放页-右上角直播入口-广告测试
* Feature【增长】买量用户激活-跳过兴趣卡
* Feature【增长】【AB实验固化】固化可视化实验
* Improve【技术优化】礼物接口&资源治理2期：将全量礼物资源打包成文件从 CDN 下载


Version 9.3.88.3 *(2025-7-30)*
-----------------------------
直播

* Improve【技术优化】上报特效播放失败至APM，其中 canUpdateUi=false 不上报


Version 9.3.87.3 *(2025-7-23)*
-----------------------------
直播

* Feature【增长】课程直播支持福利中心
* Feature【增长】直播间惊喜礼包：H5侧收听抽奖功能7月17日上线，对C端用户开放
* Feature【增长】抖音买量3.0：主播侧展示买量标识
* Feature【增长】直播tab-新增黑名单用户不自动进房
* Improve【技术优化】解析大Json OOM异常优化，包括礼物面板Json解析、模版资源Json解析场景
* Improve【技术优化】核心页面-直播首页单列信息流可用性监控
* Improve【技术优化】优化直播相册加载性能&配置中心支持配置uid切换至系统相册
* Improve【技术优化】日志系统规范优化&支持按设备维度开关打印日志，生产环境默认关闭开关


Version 9.3.84.3 *(2025-7-9)*
-----------------------------
直播

* Feature【增长】单列进房实验：关体验一致性
* Feature【增长】直播单列信息流：推荐整合，达到房间内外推荐内容一致体验
* Feature【增长】聊天室增加可视区v1：支持主题功能
* Feature【增长】直播间惊喜礼包：客户端C区引导，H5侧收听抽奖功能7月17日上线，对C端用户开放
* Feature【商业化】派对-新亲密关系：榜单功能
* Improve【技术优化】礼物接口&资源治理1期：giftMsg查询giftId失败：从定时全量改为实时单接口
* Improve【技术优化】派对麦位更新优化：优化音量SEI发送频繁，减少音浪 UI 无效刷新
* Improve【技术优化】进场座驾资源下载优化：本地无缓存则单接口查询下载渲染
* Improve【技术优化】Cronet 新增配置中心开关，更换AB实验（因Cronet在部分机型上崩溃问题，老实验已关闭，Bug本版本已修复）


Version 9.3.78.3 *(2025-6-25)*
-----------------------------
直播

* Feature【增长】聊天室支持自定义背景
* Feature【增长】单列信息流实验v3-增加直接进房频次
* Feature【增长】主播任务-C位领航计划
* Feature【商业化】派对-新亲密关系礼物
* Improve【体验优化】直播间历史消息展示优化
* Improve【技术优化】直播间运营位实现改造、优化
* Improve【技术优化】直播单列信息流 -> 直播间，单列信息流卡片 HomeDefaultRoomInfoView 音波动画未停止播放
* Fix 重置连线pk状态，修复pk按钮 可能为灰色问题


Version 9.3.75.3 *(2025-6-11)*
-----------------------------
直播

* Feature【增长】直播频道单列-直播频道推荐-子实验
* Feature【增长】直播频道-熟客直接进房
* Feature【增长】主题设置支持专辑卡
* Feature【增长】ASR支持主播侧开启/关闭
* Feature【商业化】高潜未付费方案


Version 9.3.69.3 *(2025-5-28)*
-----------------------------
直播

* Feature【增长】福利中心增加埋点&优化引导弹窗
* Feature【增长】抖音投放渠道增加退出关注引导
* Improve【技术优化】大礼物特效送出提示优化：https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdlgbqZ4sQvKbK4OWZd1wyK0
* Improve【技术优化】互动礼物体验问题优化（橡皮擦擦除动画， 闪烁问题等）
* Fix 修复直播间大动画多帧替换文字的时候，如果名字显示不下，还是保留...的显示方式


Version 9.3.66.3 *(2025-5-21)*
-----------------------------
直播

* Feature【增长】上下滑更多引导样式实验
* Feature【合规】人脸识别合规改造
* Feature【设计】首页屏效4期：https://alidocs.dingtalk.com/i/nodes/o14dA3GK8g5NEKnDU5MP2dybV9ekBD76
* Feature【商业化】针对飘屏支持财富等级可见配置
* Improve【技术优化】弱网情况下使用QUIC协议优化直播播放体验
* Improve【技术优化】固化流畅看播体验优化实验
* Improve【技术优化】解析长连接系统消息迁移至子线程
* Improve【技术优化】解析SEI迁移至子线程
* Improve【技术优化】固定个播头部高度、头部1行、2行、3行高度
* Improve【技术优化】直播间预告卡片根布局使用 LinearLayout，优化测量性能
* Improve【技术优化】MP4动态背景根布局改为 FrameLayout
* Fix 观众连麦中关播后无法退出直播间问题


Version 9.3.61.3 *(2025-5-12)*
-----------------------------
直播

* Feature【增长】个播支持主播自定义动态背景
* Feature【增长】播放页右上角-直播主播推荐理由-增加「暂停」时机曝光
* Improve【技术优化】包体积优化：Zego SDK so动态库支持动态下发
* Improve【技术优化】包体积优化：移除播客在线访谈业务
* Fix 解决打开app首次进入视频直播间无法全屏的问题


Version 9.3.60.3 *(2025-5-7)*
-----------------------------
直播

* Feature【增长】娱乐派对私信链路优化
* Feature【商业化】娱乐派对互动礼物
* Feature【商业化】任意门礼物
* Improve【技术优化】包体积优化：短剧业务插件化
* Improve【技术优化】包体积优化：火山ASR SDK插件化，支持动态下发大 SO 动态库
* Improve【技术优化】包体积优化：资源优化，本地大资源文件上传到云端（https://ops.ximalaya.com/gatekeeper/quick-upload）
* Improve【技术优化】包体积优化：音效文件从 TingMainHost 迁移到 MusicBundle
* Improve【技术优化】贵族、守护进场飘屏控件动画播放完成后停止播放 SVGA 动画
* Fix 机型兼容性问题：新排位PK迷雾动画未正确覆盖进度条问题
* Fix 设置直播间主播资料视图最小宽度，解决热度和小心心轮播导致的关注按钮 UI 抖动问题


Version ******** *(2025-4-16)*
-----------------------------
直播

* Feature【增长】单列信息流实验v2：用户进直播频道页后，直接进入直播间
* Feature【增长】红包支持订阅专辑遗留需求：1. 用户侧点击红包专辑封面，支持跳转专辑页面；2. 多个红包曝光埋点上报问题修复
* Feature【增长】信息卡支持配置图片、视频内容
* Feature【增长】红包/福袋增加未发放提示和处罚机制
* Feature【增长】【P端】公会批量上传BGM
* Feature【增长】【插入】抖音站外投放1.0 ：用户信息获取— 跟客户端 9.3.57-4.16号发版
* Feature【商业化】【插入】【背包】房间专属锦囊
* Fix 视频新排位PK面板没有展示出来问题
* Fix 新排位PK手机发热问题：动画view动态加载移除


Version 9.3.51.3 *(2025-4-2)*
-----------------------------
直播

* Feature【增长】直播间内信息卡一期
* Feature【增长】可视化-ASR功能体验优化、头像区增加交互能力
* Feature【增长】单列信息流v1.1：动态背景和分类页埋点
* Feature【增长】【P端】直播间专辑卡v1.1：专辑详情页和专辑挂件
* Feature【增长】【插入-埋点优化】声音播放页右上角曝光17493
* Improve【技术优化】互动玩法组件架构升级


Version 9.3.48.3 *(2025-3-19)*
-----------------------------
直播

* Feature【增长】【插入】红包接入新风控服务
* Feature【增长】【插入】以声音播放页为主-直播公私域入口迭代
* Feature【增长】【插入】【P端】直播间视觉区-专辑卡
* Feature【增长】【插入】红包支持订阅专辑
* Feature【增长】短剧功能开发-2.1期-VOC
* Feature【增长】短剧功能开发-2.1期-广告
* Improve【技术优化】头部榜单-父子组件改造
* Improve【技术优化】个播视频直播全屏弹幕组件MVI改造
* Fix 修复首页_直播-直播信息流点击埋点（67352）Bug：点击收起关注模块不上报


Version 9.3.42.3 *(2025-3-5)*
-----------------------------
直播

* Feature【增长】移动端防刷增加直播间token校验
* Feature【增长】【直播tab】单列信息流·一期
* Feature【增长】短剧二期-客户端开发
* Improve【技术优化】个播直播间内组件新架构改造-1期(个播优惠卷组件改造)
* Fix 从红包A直播间切换红包B直播间 在B抢红包，请求 roomId 不对问题
* Fix 修复关联礼物选中状态问题


Version 9.3.39.3 *(2025-2-20)*
-----------------------------
直播

* Feature【增长】可视化-个播直播间增加主视区：ASR
* Feature【增长】主播连麦、pk展示逻辑更正
* Feature【增长】【分发】大额红包功能升级
* Feature【商业化】排位PK优化-赛季与奖励优化
* Feature【商业化】排位PK道具优化


Version 9.3.33.3 *(2025-2-7)*
-----------------------------
直播

* Feature【增长】新增播放设置-支持设置定时和是否续播
* Improve【技术优化】直播 - APP首页【流量数据提效-埋点改造】- 配合数仓


Version 9.3.30.3 *(2025-1-16)*
-----------------------------
直播

* Feature【增长】【广告部门需求】直播广告投流数据透传到广告主落地页
* Feature【增长】【我的-历史】直播卡片更新，配合主站我的页改版
* Feature【商业化】直播福利中心（原通行证）
* Feature【短剧】短剧上下滑增加埋点
* Improve【技术优化】礼物面板图标 icon 加载优化（优先加载静态图片，选中态再加载动态图）
* Fix 调整礼物震动触发时机
* Fix 直播间长连接 RoomJoinReq 字段 xuid 传值错误


Version 9.3.24.3 *(2025-1-2)*
-----------------------------
直播

* Feature【短剧】一期
* Feature【增长】AI直播助手-主播任务权益置换
* Feature【增长】直播间预约改版优化
* Feature【增长】【主播端】底栏按钮隐藏逻辑：PK、连麦、连麦PK
* Feature【商业化】随机PK增加视频画面
* Feature【商业化】贵族过期提醒优化
* Improve【技术优化】长链进房请求中新增oaid、aid、imei、 idfa 四个字段


Version 9.3.21.3 *(2024-12-18)*
-----------------------------
直播

* Feature【增长】直播首页支持用户负反馈
* Feature【增长】课程直播增加轮播主播
* Feature【增长】【直播tab】定制年度活动频道
* Feature【商业化】心愿商店背包入口调整
* Feature【商业化】文字礼物
* Feature【商业化】cp关系调整升级和奖励
* Improve【体验优化】主播端直播中拍摄禁用
* Improve【体验优化】返回上一直播间按钮层级调整
* Improve【技术优化】直播热点流量监控：Cookie增加直播信息
* Improve【技术优化】Android进房加载流程优化-3期：PGC切换视频PK直播间画面抖动优化
* Fix PGC聊天室收听时长上报接口错误问题


Version 9.3.15.3 *(2024-12-4)*
-----------------------------
直播

* Feature【增长】直播间关播后自动跳转实验
* Feature【增长】直播间上下滑强化引导实验
* Feature【增长】直播间更多直播强化引导实验
* Feature【增长】【直播tab】新增封面边框
* Feature【增长】直播间“直播行为准则”文案更新
* Feature【商业化】批量使用道具
* Improve【技术优化】派对广告-接秒开地址
* Improve【技术优化】流畅看播体验优化策略调整：中低端默认打开流畅看播模式
* Improve【技术优化】直播进房加载时长优化-2期：头部组件异步，底部先展示输入框，AB实验固化


Version 9.3.12.3 *(2024-11-20)*
-----------------------------
直播

* Feature【增长】【2期】课程直播同步福袋功能
* Feature【增长】首页直播卡片接入负反馈
* Feature【增长】UGC直播代码下线
* Feature【增长】声音播放页新增直播中声音条
* Feature【增长】亲密度卡+亲密度后台
* Feature【增长】【分发】人工投放系统-扩容+扶持流量展示
* Feature【商业化】排位PK优化-流程优化
* Feature【商业化】上上签支持全麦打赏
* Improve【技术优化】直播流畅看播体验优化 1 期
* Improve【技术优化】直播间进房批量组件延迟加载
* Improve【技术优化】礼物资源主线程耗时治理
* Improve【技术优化】直播间首帧后加载房间
* Improve【技术优化】直播间拉流SEI解析优化
* Improve【技术优化】直播间接口请求延迟治理
* Improve【技术优化】直播首页预加载直播间资源


Version 9.3.6.3 *(2024-11-6)*
-----------------------------
直播

* Feature【增长】【红包风控】验证码升级
* Feature【商业化】运营位增加单帧停留时长
* Improve【技术优化】直播间底部工具栏组件优化
* Improve【技术优化】课程直播，用户侧更多，有红包入口且有小红点展示，外部的工具栏 支持小红点逻辑
* Improve【技术优化】课程直播是否付费的接口diablo-auth-web/v3/auth/check优化
* Improve【技术优化】184弹窗高度兼容优化
* Improve【技术优化】移除不用的 LIVEFans-NUr-SVG.ttf 字体文件
* Improve【技术优化】设备分级策略：上报手机 CPU 主频 cpuMaxFreqKHz
* Fix 修复pop条连击的时候，展示会被阻塞问题
* Fix 官方直播间-投放回传问题
* Fix 【双端不一致】个播和课程直播共用禁言弹窗


Version 9.3.3.3 *(2024-10-23)*
-----------------------------
直播

* Feature【增长】【1期】（先上红包）红包、福袋同步在课程直播
* Feature【增长】直播间分享组件优化（新增海报分享）
* Feature【增长】娱乐厅加入个人粉丝团
* Feature【商业化】PGC 聊天室新增 1v1 CP关系
* Feature【商业化】仙宠礼物不支持全麦打赏
* Improve【体验优化】课程直播回放&时移交互统一
* Improve【技术优化】直播间关播流量突增优化
* Improve【技术优化】消息/礼物消息幂等处理
* Improve【技术优化】组件化框架：支持父子组件
* Improve【技术优化】直播间内存采集基建


Version 9.2.96.3 *(2024-10-10)*
-----------------------------
直播

* Feature【增长】AI直播助手(第4期)-【APP】主播端设置项
* Feature【增长】AI直播助手(第4期)-【APP】主播端话题的历史页
* Feature【增长】【玩法】天选红包·一期
* Feature【增长】【直播间内】天选红包
* Feature【增长】【直播】首页直播卡新增标签能力
* Improve【技术优化】客户端184弹窗适配高度超出屏幕优化


Version 9.2.93.3 *(2024-9-19)*
-----------------------------
直播

* Feature【增长】直播首页Tab关注模块新增预约卡
* Feature【增长】退出直播间新增直播首页Tab引导提示弹窗
* Feature【商业化】全麦打赏二期-宝箱礼物：PGC聊天室支持全麦和单麦批量送，个播支持批量送
* Improve【技术优化】课程直播新组件体系改造
* Improve【技术优化】课程直播信息&组件分层
* Improve【技术优化】直播间头部主播信息及粉丝团优化


Version 9.2.87.3 *(2024-9-4)*
-----------------------------
直播

* Feature【增长】挂播检测&扼制公域分发
* Feature【增长】课程直播间-增加直播回放能力
* Feature【增长】课程直播间-增加直播时移功能
* Feature【商业化】1元首充-未充值用户礼物面板置顶首充礼包
* Improve【技术优化】公告短视频上传修改，以及服务端 H265 转码优化
* Fix 视频直播间管理员更多菜单无公告、禁言名单、发图片等入口问题


Version 9.2.84.3 *(2024-8-21)*
-----------------------------
直播

* Feature【增长】PGC 直播间信息 & 组件分层
* Feature【增长】【视频】官方直播间
* Feature【增长】清屏模式下，修改交互变成左右滑
* Feature【增长】APP开播下线UGC-Pia戏模式
* Feature【增长】【直播间公告】短视频
* Feature【增长】直播间信息卡-PGC主播补充礼物展馆模块
* Feature【增长】直播间信息卡-用户主态卡片上补充礼物展馆模块
* Feature【增长】【直播tab】关注模块排序按钮
* Feature【增长】【插入】首页直播卡片设计优化
* Feature【商业化】全麦打赏一期-背包礼物
* Feature【商业化】全麦打赏一期-连击礼物
* Feature【商业化】全麦打赏一期-背包面板下支持连击礼物
* Feature【商业化】1元首充礼包优化
* Improve【技术优化】H5Iting处理项目1期
* Fix 退出直播间 LiveRecordInfoManager 清除直播间缓存详情数据
* Fix 闪光时刻 pageExit 埋点 roomId/liveRoomType 值为空问题


Version 9.2.78.3 *(2024-8-7)*
-----------------------------
直播

* Feature【增长】AI助手818联动-【APP】主播端活动和设置入口
* Feature【增长】新首页 直播视频卡
* Feature【增长】818 直播大咖卡片
* Feature【商业化】冠名+周星礼物
* Feature【商业化】liveJsBridge 支持 PGC、课程直播打赏
* Improve【技术优化】礼物面板新增上方自定义 LiveGiftTopView 容器


Version 9.2.75.3 *(2024-7-24)*
-----------------------------
直播

* Feature【增长】直播间试点“闪光时刻”功能
* Feature【商业化】派对贵族坐席和用户榜单入口调整
* Feature【体验优化】粉团弹窗覆盖礼物动效
* Improve 184 弹窗软键盘行为修改，设置软键盘唤起时弹窗不被顶起
* Improve 过滤送礼失败 apm 上报 errorCode=3604（余额不足）数据
* Improve PGC进房冗余请求治理：https://alidocs.dingtalk.com/i/nodes/kDnRL6jAJM3A0q2eUBvm7qNzWyMoPYe1
* Fix PGC飘屏位置恒定
* Fix LiveTemplateManager 下载模块监听网络失效 Bug
* Fix 修复 H5 弹窗唤起输入法，个播间 UI 响应了输入面板唤起输入法的模式状态问题


Version 9.2.69.3 *(2024-7-10)*
-----------------------------
直播

* Feature【增长】【跨迭代】C端直播间主播信息卡优化
* Feature【商业化】守护复购提醒
* Feature【体验优化】直播间退出方案实验固化
* Improve【体验优化】直播间内特效动画播放逻辑优化
* Improve【体验优化】直播间断链重连后推送直播状态更新消息+主播未开播不推送断流更新消息
* Improve【技术优化】直播分层定位体系：屏幕绝对位置 -> 房间页面位置
* Improve【技术优化】长连接进入直播间直到登录成功后，后面直播间内重连才标记为重连


Version 9.2.66.3 *(2024-6-26)*
-----------------------------
直播

* Feature【增长】【体验一致】聊天室加投票玩法
* Feature【增长】【粉丝团】团员进度值
* Feature【增长】直播广告跳转至特定功能的直播间：福袋/红包/PK等
* Feature【商业化】上头条优化
* Improve【技术优化】直播间运营位挂件qps优化
* Improve【技术优化】直播相册优化（不再使用系统相册，使用直播相册）


Version 9.2.60.3 *(2024-6-13)*
-----------------------------
直播

* Feature【增长】直播间支持一键清屏
* Feature【增长】课程直播公告编辑与展示优化
* Feature【商业化】【周年庆】震动礼物
* Improve【技术优化】直播间动画格式优化svga转YYEVA
* Improve【技术优化】进房鉴权新增joinType字段
* Improve【技术优化】红包和粉丝团弹窗避让逻辑


Version 9.2.57.3 *(2024-5-29)*【51被小版本占用】
-----------------------------
直播

* Feature【增长】双击点赞-Native
* Feature【增长】聊天室加粉丝团
* Feature【增长】【礼包2.0】新用户礼包
* Feature【商业化】大客户特权升级
* Feature【商业化】心愿单优化
* Feature【商业化】心愿商店入口调整
* Improve【技术优化】聊天室麦上动效兼容静态图（jpg、png）播放结束隐藏


Version 9.2.48.3 *(2024-5-15)*
-----------------------------
直播

* Feature【增长】UGC一起聊开播入口关闭
* Feature【商业化】随机PK匹配规则调整2.0
* Feature【商业化】排位PK优化4.0.3：匹配优化
* Improve【技术优化】直播项目 KAE 移除
* Improve【体验优化】无障碍模式直播间适配
* Improve【体验优化】切换直播间断麦提示一致性优化
* Improve【体验优化】直播间关闭按钮迁移至第 3 层
* Improve【技术优化】内存治理
* Improve【技术优化】进场组件优化：进场特效
* Improve【技术优化】移除废弃的分享活动直播代码
* Fix 14303 偶现场景埋点位去重问题（第一次加载返回奇数个卡片，如21个，这时候，上滑加载更多，新返回的第一卡片的requestId为空，也就是每次尾部出现奇数个开票，再上拉加载更多，就会出现一条有问题的曝光）
* Fix 16668 课程直播公告按钮曝光未去重问题


Version 9.2.42.3 *(2024-5-6)*
-----------------------------
直播

* Feature【增长】AI直播助手-自动截屏功能（礼物大动画、消息消息type=25的大动画）应用试点主播
* Feature【增长】课程直播消息流优化
* Feature【商业化】守护团1.0
* Feature【商业化】【423活动】直播间房间特效
* Feature【商业化】【423活动】直播首页置顶banner展示位
* Feature【商业化】排位PK优化4.0.2：边框、头像框
* Improve【技术优化】【内存治理】直播4月常态化内存治理（修复内存泄露、收敛Gson创建、收敛Handler创建 、替换SP为MMKV、移除无用资源）
* Improve【技术优化】直播间位置信息统一优化，统一包含状态栏
* Improve【技术优化】视频最小化 音视频解码优化


Version 9.2.39.3 *(2024-4-17)*
-----------------------------
直播

* Feature【增长】直播频道页粉牌样式更新
* Feature【增长】视频，课程直播最小化
* Feature【增长】直播开播嵌入主站工具 &整合myclub入口
* Feature【增长】直播推荐卡片
* Feature【增长】直播提供预约直播接口给到其他业务方
* Feature【商业化】排位PK优化4.0.1
* Improve【设计优化】高级进场&礼物pop条的整体优化
* Improve【技术优化】【包大小优化】喜马直播本地资源优化(.9.png不压缩)
* Improve【技术优化】RoomCore 消息分发优化：直播状态变化时，再分发直播状态变更消息
* Fix 华为手机视频直播间预加载改用 TextureView


Version 9.2.33.3 *(2024-4-7)*
-----------------------------
直播

* Feature【增长】开播分类优化
* Feature【增长】粉团守护优化：弹窗兼容优化
* Feature【商业化】特效礼物玩法
* Feature【商业化】直播更多入口参数优化，服务端增加 bizType、roomId等参数
* Improve【技术优化】【APM】直播模块内存大盘完善
* Improve【技术优化】非连麦者在直播间接打电话时停止播放直播，接打电话结束后恢复播放直播
* Fix 搜索结果页改为先上报点击埋点，再执行跳转直播页面
* Fix 首次进直播间未操作上下滑就上报上下滑埋点问题（metaId=33393）
* Fix 直播间内跳转直播间 iting 链接参数丢失问题


Version 9.2.30.3 *(2024-3-20)*
-----------------------------
直播

* Feature【增长】AI直播助手-提示词的前端展示
* Feature【增长】直播间背景自定义
* Feature【增长】通行证与每日任务整合
* Feature【外部需求-喜播】喜马站内直播投放数据回传
* Improve【体验优化】直播间信息&组件分层【四期 & 五期】
* Improve【技术优化】统一直播发布预告和更新预告接口
* Improve【技术优化】Android飘屏支持webp动图显示
* Improve【技术优化】ZegoSDK升级


Version 9.2.24.3 *(2024-3-6)*
-----------------------------
直播

* Feature【增长】直播间公告 2 期优化预告交互体验
* Improve【体验优化】直播间信息&组件分层【三期】
* Improve【体验优化】优化6麦或9麦连麦框横纵向裂缝
* Improve【技术优化】直播间动画配置类型简化-麦上动画支持 webp 格式
* Fix 修复直播间弹幕资源模版未缓存时，弹幕不会加载样式的问题
* Fix Android 9 及以上系统版本应用推后台或熄屏后连麦或者推流麦克风权限系统回收问题


Version 9.2.21.3 *(2024-2-22)*
-----------------------------
直播

* Feature【增长】直播首页增加运营 Tab
* Feature【增长】官播间更改主播直播时间限制
* Feature【增长】老首页信息流样式变更一拖三
* Feature【增长】我的页-看过的直播加入播放历史
* Feature【商业化】旅行仙宠2.0
* Feature【商业化】直播间-助力周榜、月榜下架
* Feature【商业化】直播间-用户贡献榜优化
* Improve【技术优化】匿名直播间删除本地缓存，使用服务端数据同步
* Improve【技术优化】在个播直播间点击输入框时，如果长连接断开，则重连
* Fix 修复 PGC、UGC 房间 197 iTing 链接无法唤起资料卡的问题
* Fix 修复 PGC 直播间进房加载装扮麦位波，初始化后出现未说话但展示了静态的麦位波问题


Version 9.2.15.3 *(2024-1-31)*
-----------------------------
直播

* Feature【增长】匿名直播间
* Feature【增长】优化滑到的直播间逻辑，我的关注渠道进入直播间出关注直播间，直播首页品类Tab进入出同品类直播间
* Feature【增长】增加亮熄屏听看埋点
* Feature【增长】开播页卖货法务条款入口文案和 H5 链接支持配置化
* Feature【商业化】新春打年兽活动
* Feature【商业化】直播间用户榜单优化
* Feature【商业化】直播间守护优化
* Improve【体验优化】直播间无障碍模式适配
* Improve【体验优化】消息流手动滑动触顶埋点


Version 9.2.12.3 *(2024-1-17)*
-----------------------------
直播

* Feature【增长】大需求：主播连麦 9 麦
* Feature【商业化】个播音视频直播间主播榜单优化
* Improve【体验优化】大需求：直播间信息&组件分层 1 期& 2 期
* Improve【体验优化】退出拦截+弹窗
* Improve【体验优化】PGC取消关注优化
* Improve【技术优化】全站曝光埋点去重四期-直播侧曝光埋点去重


Version 9.2.6.3 *(2024-1-3)*
-----------------------------
直播

* Feature【增长】四人连麦：布局优化二期
* Feature【增长】主播任务C端入口改版
* Feature【商业化】特权礼物增加锁定样式
* Improve【体验优化】直播间断流优化，上报主播端推流失败原因
* Improve【技术优化】课程直播间观看回放进度上报方案优化：由每3s轮询上报改为离开直播间上报观看进度，减少接口调用
* Improve【技术优化】全站曝光埋点去重三期-直播侧曝光埋点去重
* Fix 直播间用户命中退出挽回提示上下滑实验时，直播间声音被暂停问题


Version 9.2.3.3 *(2023-12-20)*
-----------------------------
直播

* Feature【增长】公告二期：相册
* Feature【增长】关播切换优化（主要优化切换动画）
* Feature【增长】内 Push 亮屏推送上报信息优化（只上报展示事件，不再上报丢弃事件）
* Feature【增长】直播间用户退出挽回提示上下滑
* Improve【体验优化】派对房魅力值前三名高亮显示
* Improve【技术优化】直播间收听时长代码重构
* Improve【技术优化】直播首页页面错误率：只上报接口首次请求结果
* Improve【技术优化】全站曝光埋点去重二期-直播侧曝光埋点
* Improve【固化实验】直播间 C 区引导固化，实验 ID：8279


Version 9.1.96.3 *(2023-12-6)*
-----------------------------
直播

* Feature【增长】四人连麦：布局优化一期
* Feature【增长】直播内push新增亮屏推送 v1.0
* Feature【增长】直播外PUSH接付费渠道 v1.0
* Feature【增长】优惠券展示优化
* Feature【增长】客户端上报用户直播中收听时长埋点
* Feature【商业化】随机PK+AI变声
* Feature【商业化】聊天室背包支持连击礼物
* Feature【商业化】财富等级增加值展示问题优化
* Feature【安全合规】直播间送礼接口、使用背包接口添加 ticket 字段
* Improve【体验优化】Andrid直播间发图使用系统相册
* Improve【技术优化】主站全站曝光/点击去重项目-直播侧埋点
* Fix 聊天室连击按钮显示中适配键盘弹出逻辑
* Fix IMLog日志库 NPE：https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/77753185/report?pid=1&bundleId=&channelId=&version=all&tagList=&start=20&deviceId=&cpuArch=&date=last_7_day


Version 9.1.93.3 *(2023-11-22)*
-----------------------------
直播

* Feature【增长】个播间交友模式提高关注层级
* Feature【增长】首页信息流直播卡片新增算法埋点
* Feature【商业化】视频直播AI礼物
* Feature【主站】全局肚脐眼播控条实验
* Improve【体验优化】直播视觉优化2期_细节修复：https://alidocs.dingtalk.com/i/nodes/G1DKw2zgV2RXOoM1iG99LqaaVB5r9YAn?utm_scene=team_space
* Improve【体验优化】直播间网络变化 Toast 提示优化
* Improve【体验优化】去除一进入直播间的模式变更提示
* Improve【技术优化】Android 11 适配
* Improve【技术优化】有赞 SDK 初始化入口收敛
* Fix 用户从播放页进入课程直播间，退出直播间后长连接不断链问题
* Fix 直播间评论输入框输入优化（在输入时限制最大字符数，超出限制不予输入，且 Toast 提示）


Version 9.1.87.3 *(2023-11-8)*
-----------------------------
直播

* Feature【增长】官播间增加开播前 15 分钟询问策略
* Feature【增长】直播回放引导精华cut剪辑
* Feature【增长】ads oCPC转化目标新增 eDAU、付费
* Feature【增长】榜单添加""直播中标识"
* Feature【商业化】直播接入有赞电商-客户端有赞SDK升级&&购物入口配置云端化
* Feature【商业化】下线积分通行证认证
* Improve【体验优化】直播间视觉优化2期-PK面板改版
* Improve【体验优化】主播开播权限提醒优化
* Improve【技术优化】giftId未找到导致特效无法显示优化
* Improve【技术优化】直播间上下滑不再传递广告点击标识 CID 
* Improve【技术优化】课程直播秒开优化-AB实验：外 Push 唤端进入直播间播放，提前拉起播放进程
* Improve【技术优化】搜索结果直播入口、个人页直播中头像入口新增埋点：liveRoomType（直播类型）、roomId（直播房间ID）、liveId（直播场次ID）、anchorId（主播ID）
* Fix 播放专辑时，进入未开播的直播间，点击通知栏或点击通知栏的播放按钮，无法从原先进度续播
* Fix 最小化 PGC 房间后，播放外部APP（如QQ音乐），切换回喜马APP后声音焦点未抢回


Version 9.1.84.3 *(2023-10-11)*
-----------------------------
直播

* Feature【增长】首页信息流直播卡片新增推荐理由
* Feature【增长】召回承接收听礼
* Feature【增长】首页信息流直播卡片埋点（点击 38749，曝光 38750）更新，通过 size 字段区分一拖三卡片（size=3）、专辑样式(size=1)、视频大卡（无size）等
* Feature【商业化】年度预选赛PK
* Feature【商业化】礼物图鉴
* Feature【商业化】礼物墙入口改名为礼物展馆
* Improve【体验优化】直播间视觉优化_1期物料更替
* Improve【体验优化】更多直播交互优化：定位到上次浏览的 Tab 页
* Improve【体验优化】增加 C 区域引导避让关系（引导和 Pia 面板展开避让）
* Improve【体验优化】直播间通用弹窗黑/白名单根据用户 uid 进行频控
* Improve【技术优化】课程直播秒开优化
* Improve【技术优化】直播间最小化优化
* Improve【技术优化】新连麦组件改造
* Improve【技术优化】珠峰音效 SDK 更新
* Improve【技术优化】直播全量模版资源查询两种请求方式错误次数AB 固化
* Improve【技术优化】进直播间播放来源 PlaySource 排查补充


Version 9.1.78.3 *(2023-10-11)*
-----------------------------
直播

* Feature【增长】新增直播电台房间样式
* Feature【增长】主播纪念日扩展节日场景
* Feature【增长】预约流程优化
* Feature【增长】广告进房事件添加 cid 信息
* Feature【增长】聊天室显示“新人”标签
* Feature【增长】上下滑接推荐
* Feature【商业化】礼物面板优化
* Feature【B端】主播任务
* Feature【B端】课程直播自动开播功能
* Improve【体验优化】首充1元体验优化
* Improve【体验优化】系统消息过多
* Improve【体验优化】退出拦截弹窗AB测


Version 9.1.75.3 *(2023-9-14)*
-----------------------------
直播

* Feature【增长】弹幕小游戏的接入
* Feature【增长】长辈模式接入直播 1 期
* Feature【合规】消费冷静期
* Improve【体验优化】直播间消息流优化 3 期
* Improve【体验优化】背包物品使用体验优化

Version 9.1.69.3 *(2023-8-30)*
-----------------------------
直播

* Feature【分发】主播池新增分发策略选项
* Improve【体验优化】上下滑切换直播间交互优化
* Improve【体验优化】直播新首页样式 TAB 增加吸顶效果
* Improve【技术优化】课程直播秒开优化 1 期
* Fix 直播首页卡片曝光埋点问题

Version 9.1.66.3 *(2023-8-16)*
-----------------------------
直播

* Feature【商业化】未开播时限制打赏面板礼物
* Feature【课程直播】试听时长配置化
* Feature 【增长】首页信息流-喜马头条试点
* Improve【技术优化】从声音播放页进入直播间，退出直播间时不支持最小化
* Improve【技术优化】珊瑚海礼物类型请求接口异常兜底优化
* Improve【体验优化】AutoSize导致的部分机型大挂件显示有间隙修复
* Improve【体验优化】视频直播消息流高度优化

Version 9.1.60.3 *(2023-8-2)*
-----------------------------
直播

* Feature【广告流量优化】广告礼物增加直播间内引导
* Feature【合规】普通弹幕付费展示条数上线
* Feature【流量】主站首页信息流大卡样式优化
* Feature【C端】直播间新增外部推广功能
* Feature【C端】直播首页 UI 改版 AB 实验
* Feature【C端】个播新增拉流直播功能
* Improve【技术优化】直播首页优化下拉加载体验
* Improve【技术优化】直播间声音美化功能适配新版本SDK
* Improve【技术优化】聊天图片格式转码上传（HEIF兼容问题）
* Improve【体验优化】主播以观众身份进入直播间合规文案优化

Version 9.1.57.3 *(2023-7-19)*
-----------------------------
直播

* Feature【合规】消费提醒默认选项改为开启
* Feature【埋点】播放事件添加rec_track、rec_src埋点
* Feature【流量】直播增加付费指标回传至ads系统
* Improve【首映室】首映配置
* Improve【首映室】直播间分发&流量
* Improve【首映室】直播间内功能场景
* Improve【首映室】付费专辑&声音鉴权
* Improve【技术优化】直播卡顿APM上报数据模型调整
* Improve【技术优化】全量模版接口添加请求结果打点上报
* Improve【技术优化】直播首页和直播间过渡绘制优化
* Improve【体验优化】飘屏遮挡 FM 号
* Fix 修复个播最小化到肚脐眼，点击肚脐眼回到直播间时重新拉流问题

Version 9.1.51.3 *(2023-7-5)*
-----------------------------
直播

* Feature【合规】修改进房合规宣导文案
* Feature【课程直播】手势放大功能
* Improve【体验优化】首页的直播推荐卡片上的标签颜色需要改为纯色
* Improve【体验优化】tab标签选项卡的小矩形颜色都统一成 ff4444
* Improve【体验优化】消息流新增消息视图无障碍读屏适配
* Fix 修复直播间关播后拉流 404 错误问题
* Fix 修复 UGC Pia 戏房间房主最小化回到直播间，剧本状态未恢复的问题
* Fix 修复个播房间模式过滤错误问题：modeList 根据 modeStatus 过滤掉指定的 mode

Version 9.1.48.3 *(2023-6-25)*
-----------------------------
直播

* Feature【商业化】音频直播多人 PK
* Feature【C端】增加有效收听但未登录的奖励引导登录
* Feature【课程直播】课程直播提示优化
* Improve【体验优化】直播间消息流优化 1期 & 2期
* Improve【体验优化】首页的直播推荐卡片上的标签颜色需要改为纯色
* Improve【技术优化】华为 CDN 切换-PGC、UGC 切换
* Improve【技术优化】仙宠页面在折叠屏上画面显示异常适配

Version 9.1.42.3 *(2023-6-7)*
-----------------------------
直播

* Improve【技术优化】播放卡顿数据上报增加直播误报数据过滤
* Improve【技术优化】优化播放卡顿 AB 实验组 rebuffer 步进策略
* Improve【技术优化】直播秒开异常数据过滤
* Improve【技术优化】装扮中心已改为 H5 实现，移除 Native 不用代码
* Fix 修复 PGC/UGC 切换房间送礼送到上一个房间的问题
* Fix 修复 PGC 聊天室麦位波不显示问题
* Fix 修复 PK 选择页面弹窗因为复用导致页面显示错误
* Fix 修复交友模式礼物面板点击资料卡进入主页后会弹出礼物面板的问题

Version 9.1.39.3 *(2023-5-24)*
-----------------------------
直播

* Feature 【商业化】视频直播排位 PK
* Feature 【互动】AB测：多人连麦/PK 场景下弹多人关注弹窗
* Feature 【流量】广告渠道用户新增礼物奖励
* Improve【技术优化】直播间组件改造优化 2 期
* Improve【体验优化】小米 11，贵族人数显示未居中
* Improve【体验优化】直播间Android用户端全屏时，顶部FM号看不清，需要添加遮罩（用户端主播端都需要添加）
* Improve【技术优化】秒开数据新增 cdnIp、connectCost、dnsCost、requestCost、firstPackageCost、audioDecoderCreateCost 和 videoDecoderCreateCost 耗时统计数据
* Improve【技术优化】卡顿数据上报异常问题（上下滑场景将音频直播卡顿上报为视频直播卡顿问题）修复，同时新增 cdnIp、bitrate 数据
* Fix 修复课程直播 -粉团用户在直播间发送的消息，粉团勋章呈灰色

Version ******** *(2023-5-11)*
-----------------------------
直播

* Feature 【商业化】十二星宫
* Feature 【商业化】未开播情况下，用户不能打赏背包
* Feature 【流量】广告渠道用户新增礼物奖励
* Feature 【活动】星座玩法活动
* Feature 流量健康度：抑制背包主播
* Improve【技术优化】ExoPlayer判断直播的音视频类型
* Improve【技术优化】客户端 mobile 接口升级到 https

Version ******** *(2023-4-26)*
-----------------------------
直播

* Feature 【商业化】我的鱼塘
* Feature 【商业化】直播商城优化 1 期
* Feature 粉丝团优化：不定期降价和新增权益加成卡
* Feature 新用户关注主播推荐
* Feature 视频直播特效防遮挡Android(礼物&进场）
* Improve【技术优化】新增通话模式功能
* Improve【技术优化】主播长时间无推流弹框提醒
* Improve【技术优化】全屏首页场景下，视频与音频主播连线，音频主播显示为黑色图片，需要正常显示主播头像
* Improve【技术优化】个播交友模式使用 SEI 方式发送音量信息
* Improve【技术优化】新版直播首页秒开优化
* Improve【技术优化】升级即构 SDK
* Improve【技术优化】娱乐派对混流配置更新：https://alidocs.dingtalk.com/i/nodes/gvNG4YZ7JneMEjBdhegOa9XDV2LD0oRE?utm_scene=team_space
* Fix 主播点击结束直播，用户侧展示预告开播页面

Version 9.1.24.3 *(2023-4-12)*
-----------------------------
直播

* Feature 跨房间主播连线
* Feature 视频直播多人连麦 PK 2 期
* Feature 主站播放页改版直播入口相关改动
* Feature 优质封面应用：直播首页 -1 TAB 前 14 个位置只展示封面通过的直播间
* Feature 异常账号不能发红包
* Feature 未开播房间不能发红包
* Improve 去掉官方直播间在线人数展示
* Improve【技术优化】细化播放器拉流各环节耗时
* Improve【技术优化】播放器卡顿模型完成
* Improve【技术优化】ExoPlayer 内部判断 FLV 音频类型和视频类型

Version 9.1.21.3 *(2023-3-29)*
-----------------------------
直播

* Feature 直播首页改版
* Feature 每日任务/限时好礼 增加限时配置任务
* Feature 关注接口改造
* Feature 官方直播间 节目单定位当前主播及样式优化
* Feature【B端】直播开播服务协议替换
* Feature【B端】流量卡生效前直播封面不合格提醒
* Improve 心愿单查询接口轮询改消息推送，减轻服务压力
* Improve Android直播间内跳转秒开优化
* Fix 修复进场座驾未播完，切换直播间未走 switchRoom，导致 isBusy=true，进而导致特效播放不出来的问题

Version 9.1.15.3 *(2023-3-15)*
-----------------------------
直播

* Feature 聊天室主持增加踢出房间权限
* Feature 视频直播优化-横屏看播
* Feature 视频多人连麦 PK
* Feature 视频直播增加主播榜
* Feature 福袋 sprint-gift-lottery 接口迁移
* Improve Android KTV 插件初始化流程优化
* Fix 直播秒开通过入口传递的拉流地址播放拉流逻辑调整至页面 onCreate 生命周期后
* Fix 直播秒开数据 playSource 来源不对问题

Version 9.1.12.3 *(2023-3-1)*
-----------------------------
直播

* Feature 美声 SDK 对接
* Feature 表情包可支持音视频灵活扩展
* Feature 直播首页封面展示 WebP 视频
* Feature 主播开播前直播封面不合格提醒
* Feature 福袋 sprint-gift-lottery 接口迁移
* Improve 视频直播秒开优化：进直播间 iting 增加流信息
* Fix 切换直播间礼物面板直播间数据未更新问题
* Fix 视频PK胜负边框间距问题

Version 9.1.06.3 *(2023-2-15)*
-----------------------------
直播

* Feature【商业化】魔法阵礼物
* Feature 聊天室用户引导 1 期
* Feature 直播封面打分 1 期
* Feature【体验优化】直播间在线人数做隐藏
* Feature 直播中的麦位波统一
* Feature 直播间音频动态背景缺失蒙层遮罩
* Feature 视频直播 loading 样式优化
* Feature 直播间音频动态背景缺失蒙层遮罩
* Feature 从直播首页列表进入房间拉流秒开优化
* Feature 直播ads优化：长连接登录joinRoom的扩展字段新增 imei 和 oaid 信息
* Improve【技术优化】直播模板资源全量查询CDN 文件下载模式失败后降级为 http 接口模式

Version 9.1.03.3 *(2023-2-2)*
-----------------------------
直播

* Feature 灰掉的粉团勋章展示在信息流
* Improve【技术优化】JS 调用 Native 送礼能力问题：只支持个播普通送礼模式，不支持个播交友送礼和聊天室送礼
* Fix 主播端设置 mp4 动态背景不生效问题
* Fix JS 调用  Native 送礼余额不足时，首次有余额不足弹窗，再次送无余额不足弹窗

Version 9.0.96.3 *(2023-1-11)*
-----------------------------
直播

* Feature 音视频直播-【C端】许愿活动
* Feature 音视频直播-【C端】许愿活动拆分-新年弹幕降价
* Feature【体验优化】上下滑房间已结束，自动推荐下个房间的文案修改，原来：上个房间已结束，已为你自动推荐该内容 ，改成：直播间已关播，已为您自动跳过
* Improve【技术优化】将 http://liveroom.ximalaya.com/noble-web/page/nobleInfo 改为 https://liveroom.ximalaya.com/noble-web/page/nobleInfo
* Fix 个播直播间贵族用户无法发送贵族弹幕问题
* Fix 鸿蒙3.0 SurfaceView 切 TextureView 卡帧问题
* Fix 赠送小额礼物泡泡条显示异常问题
* Fix JS调用送礼接口缺少 roomId 和 chatId 公共参数问题

Version 9.0.93.3 *(2022-12-28)*
-----------------------------
直播

* Feature 音视频直播-【C端】每日任务
* Feature 音视频直播-【C端】上下滑动有惊喜V2.0
* Feature 音视频直播-【C端】新增关播状态页面
* Feature 音视频直播-【数据基建】主播直播数据优化
* Feature 音视频直播-【商业化】视频-指定PK-静音功能
* Improve【技术优化】进房接口优化暨模板接口优化
* Improve【技术优化】视频直播/课程直播控制层代码优化
* Improve【技术优化】直播动画模版资源请求优化
* Improve【技术优化】直播间H5弹窗容器梳理，目标：收敛至184弹窗
* Improve【技术优化】直播间加载速度优化和拉流秒开优化
* Feature【体验优化】待挽留粉团界面优化
* Feature【体验优化】退->加粉团频次限制视频
* Feature【体验优化】直播创建页面 弹起美颜设置隐藏头部布局
* Fix PGC/UGC/课程直播支持JS调用输入法键盘
* Fix 课程直播支持调用JS调用长连接发言消息

Version 9.0.87.3 *(2022-12-14)*
-----------------------------
直播

* Feature 音视频直播-【C端】新人引导“限时好礼”
* Feature 音视频直播-【C端】直播间动态气泡
* Feature 音视频直播-【数据基建】广告数据统计定位到具体房间
* Feature 音视频直播-【商业化】视频新增指定PK
* Improve【技术优化】飘屏富文本方案由Html方案改为Span方案，提升解析渲染性能
* Improve【技术优化】送礼方法整合统一
* Improve【体验优化】过滤弹幕卡使用消息
* Fix 我的粉丝团页面加载不出来（由一次请求限制500条数据改为支持分页加载）
* Fix 主播端设置屏幕常亮不生效问题
* Fix 直播间大挂件卷起状态时仍显示卷起按钮问题

Version 9.0.84.3 *(2022-11-30)*
-----------------------------
直播

* Feature 音视频直播-【C端】更多直播改版
* Feature 音视频直播-【C端】主站信息流直播卡片：支持视频直播且封面动态化
* Feature 音视频直播-【C端】视频美颜功能升级完善
* Feature 音视频直播-【C端】上下滑支持视频秀场直播
* Feature 音视频直播-【C端】主播侧支持开播上传多张封面图
* Feature 音视频直播-【C端】AB实验固化：直播首页糖葫芦AB实验ID=3777， 固化成2行糖葫芦
* Feature 音视频直播-【商业化】PK联动世界杯-PK发言波纹
* Feature 音视频直播-【商业化】礼物面板运营位交互优化（从礼物面板点击运营位出现运营位弹窗，在运营位弹窗消失后重新展示礼物面板）
* Feature 课程直播Ximi团改名喜马主播会员
* Feature【技术优化】进入结束直播间跳转至上下滑下一个房间
* Feature【技术优化】iting跳转到个播直播间（msgType=52）逻辑优化，删除废弃的现场直播代码
* Feature【技术优化】移除废弃K歌房相关代码，新版本K歌玩法已在UGC聊天室集成上线
* Feature【Fix】metaId=44096埋点positionNew字段双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
* Feature【Fix】修复直播首页娱乐派对AB测试对照组没有上报分流日志问题

Version 9.0.78.3 *(2022-11-16)*
-----------------------------
直播

* Feature 音视频直播-【C端】投票基础功能
* Feature 音视频直播-【C端】PK联调世界杯UI调整
* Feature【设计优化】直播UI细节修正
* Feature【技术优化】课程直播网络恢复自动播放功能
* Feature【技术优化】视频直播代码结构优化
* Feature【技术优化】聊天室doom-web基础服务接口改造，提升性能
* Feature【技术优化】H5页面https升级
* Feature【技术优化】如果开发者选项中关闭了动画，重置 ValueAnimator 动画时长缩放参数为 1，防止用户开发者选项中
* Feature【技术优化】Fix 修复视频直播PK布局异常问题
* Feature【技术优化】Fix 修复视频直播PK状态下礼物泡泡条位置异常问题

Version 9.0.75.3 *(2022-11-2)*
-----------------------------
直播

* Feature 音视频直播-【C端】直播首页弹框组件
* Feature 音视频直播-【C端】PK预言玩法
* Feature 音视频直播-【B端】APP开播协议更新提醒
* Feature 音视频直播-【B端】APP端主播侧直播数据重构一期
* Feature【技术优化】直播间埋点serviceId问题优化
* Feature【技术优化】直播间聊天组件整合优化
* Feature【技术优化】H5弹窗在客户端上优化适配，包括手机，折叠屏，PAD
* Feature【技术优化】直播侧自定义实现native支持js输入框
* Feature【技术优化】OPPO折叠屏适配-直播侧P1级问题修复

Version 9.0.69.3 *(2022-10-19)*
-----------------------------
直播

* Feature 音视频直播-【C端】APP首页直播卡片样式ABTest
* Feature 音视频直播-【C端】聊天室增加房间背景设置
* Feature 音视频直播-【C端】口令自定义
* Feature 音视频直播-【C端】直播用户满意度调研
* Feature 音视频直播-【商业化】仙宠降临1期（用户资料卡片增减仙宠入口）
* Feature 音视频直播-【商业化】通行证3.0
* Feature 音视频直播-【B端】PC/APP直播结束页支持满意度问卷调研
* Feature 音视频直播-【B端】直播-海外用户评论先审后发需求
* Feature 音视频直播-【B端】海外用户评论先审后发
* Feature【体验优化】直播间文字表情包宽高比适配
* Feature【体验优化】口令红包+口令福袋
* Feature【技术优化】直播美颜sdk替换升级为火山美颜SDK
* Feature【技术优化】背包面板跳转指定物品优化，礼物面板跳转到指定物品优化
* Feature【技术优化】OPPO折叠屏适配-直播测严重级别问题修复

Version 9.0.66.3 *(2022-9-28)*
-----------------------------
直播

* Feature 音视频直播-【C端】直播首页卡片样式ABTest
* Feature 音视频直播-【C端】【流量】专辑评论、个人页增加头像框曝光
* Feature 音视频直播-【C端】【设计优化】体验优化五期：直播首页榜单入口及首页聊天室Card样式优化
* Feature【技术优化】主播自己发送的消息多端同步可见
* Feature【技术优化】KTV三方SDK插件加载流程优化
* Feature【技术优化】数据基建问题-直播间playSource=0问题排查修复
* Feature【技术优化】音视频直播播放器ABTest固化，以及移除不再使用的播放类型KIND_LIVE_VIDEO

Version 9.0.60.3 *(2022-9-15)*
-----------------------------
直播

* Feature 音视频直播-【C端】官方直播间
* Feature 音视频直播-【C端】官方直播间分发
* Feature 音视频直播-【C端】课程直播间改版需求二期
* Feature 音视频直播-【C端】主播侧恢复展示收入
* Feature 音视频直播-【C端】聊天室增加表情包
* Feature 音视频直播-【C端】频道组件化接直播卡片
* Feature 音视频直播-【C端】直播卡片进首页信息流
* Feature 音视频直播-【C端】退出拦截弹窗优化
* Feature 音视频直播-【C端】视频直播改：个人头像的毛玻璃背景，等版本覆盖率上来后再上线，暂定9月27日上线
* Feature 音视频直播-【C端】直播卡片进主站首页信息流V2-更换实验
* Feature 音视频直播-【C端】【设计优化】体验优化四期：① 个播和聊天室进场加入低等级粉丝团牌子 ②进场提示样式与停留时间 ③高级进场两端不统一 ④键盘弹起时两端效果不一致
  ⑤送礼条位置不一致
* Feature 音视频直播-【C端】直播间私信未关注的人逻辑优化
* Feature 音视频直播-【B端】用户上麦接新实名认证逻辑
* Feature【技术优化】资源模版优化三期-非礼物特效播放队列优化
* Feature【技术优化】直播模板资源下载策略优化
* Feature【技术优化】SEI发送方式及解析兼容
* Feature【技术优化】UGC聊天室座位面板用户刷新
* Feature【技术优化】音视频直播间交友模式礼物面板和用户资料卡片交互优化

Version 9.0.57.3 *(2022-8-25)*
-----------------------------
直播

* Feature 音视频直播-【商业化】好运锦囊/福气锦囊
* Feature 音视频直播-【C端】直播首页糖葫芦从2行改为1行
* Feature 音视频直播-【C端】直播首页策略信息上报
* Feature 音视频直播-【C端】聊天室房间相册
* Feature 音视频直播-【C端】聊天室K歌房回退声网SDK方案
* Feature 音视频直播-【C端】泛直播带货支持付费直播
* Feature【技术优化】H265礼物特效资源
* Feature【技术优化】APM新增背包使用成功率
* Feature【技术优化】Android Pad 适配（184弹窗）
* Feature【技术优化】直播首页加载时长校准
* Feature【设计优化】聊天室红包动效优化

Version 9.0.52.3 *(2022-8-10)*
-----------------------------

* Feature【技术优化】中台SDK开启dtx
* Fix 切换房间挂件点击不能跳转问题
* Fix 修正直播首页加载时长mTracePauseTime计算

Version 9.0.51.3 *(2022-8-4)*
-----------------------------
直播

* Feature 音视频直播-【商业化】观众端PK中主播资料卡-去Ta直播间-新增埋点
* Feature 音视频直播-【C端】支持OBS第三方推流客户端画面自适应
* Feature 音视频直播-【C端】厅战PK增加随机匹配
* Feature 音视频直播-【C端】个播、聊天室、课程直播分享模板升级对接
* Feature 音视频直播-【C端】抢红包加实名认证
* Feature 音视频直播-【C端】弹幕开关关掉后还可以发弹幕开发完成
* Feature【技术优化】提升封面清晰度：开播上传封面分辨率提升至1000x1000
* Feature【技术优化】直播场景下播放器屏蔽耳机暂停操作
* Feature【技术优化】礼物关键指标APM上报问题修复
* Feature【技术优化】聊天室和课程直播对接新标签方案
* Feature【技术优化】聊天室用户行为日志中添加客户端时间clientTime
* Feature【技术优化】长连接SDK增加xuid
* Feature【政策合规】主播侧新增喜爱值榜和PK贡献榜
* Feature【设计优化】视频直播间背景-改为默认毛玻璃背景
* Feature【设计优化】音视频直播间-自动切换横竖屏
* Feature【设计优化】音视频直播间-音视频直播间大R进场(贵族和守护) 进场提示条改为窄版
* Feature【设计优化】个播和聊天室键盘输入框/弹幕/表情层级提升到特效层级上面
* Feature【设计优化】红包福袋动画优化
* Feature【设计优化】连击动画样式，替换为新的svga格式动画文件

Version 9.0.48.3 *(2022-7-14)*
-----------------------------
直播

* Feature 音视频直播-【商业化】背包优化（新增背包使用明细）
* Feature 音视频直播-【商业化】消费提醒
* Feature 音视频直播-【C端】上下滑有惊喜
* Feature 音视频直播-【C端】粉团权益日
* Feature 音视频直播-【C端】优化自动退团逻辑
* Feature 音视频直播-【C端】新版本K歌房-Zego方案
* Feature 音视频直播-【C端】娱乐派对房冠名二期
* Feature 音视频直播-【C端】课程直播改版一期
* Feature【技术优化】礼物关键指标APM上报问题修复
* Feature【技术优化】Android直播间中台SDK服务管理优化，连麦最小化优化
* Feature【技术优化】课程直播间对接新消息标签方案
* Feature【政策合规】实名认证组件和新增用户认证方式，聊天室连麦选择主播认证，直播连麦选择用户认证
* Feature【政策合规】PK高峰时段提醒弹窗
* Feature【设计优化】音视频直播间-消息流字色调整-降低高亮文字的突兀感，替换各消息流类型的字色，设计稿：https://lanhuapp.com/url/CKlUx-Ax6ZWe
* Feature【设计优化】音视频直播间-进场提示改至底部-保持原逻辑不变，交互同现有聊天室，设计稿：https://lanhuapp.com/url/CKlUx-Ax6ZWe
* Feature【设计优化】音视频直播间-进视频直播间，渐进式加载各块内容，设计稿：https://lanhuapp.com/url/CKlUx-Ax6ZWe

Version 9.0.42.3 *(2022-6-23)*
-----------------------------
直播

* Feature 音视频直播-商业化模板资源管理优化1期-下载策略优化
* Feature 音视频直播-商业化删除礼物墙引导
* Feature 音视频直播-商业化首充优化
* Feature 音视频直播-商业化会员x贵族升级活动-优化
* Feature 音视频直播-商业化抢头条覆盖问题
* Feature 音视频直播-C端评论撤回功能
* Feature 音视频直播-C端直播首页卡片 ABtest
* Feature 音视频直播-C端针对用户关注且有强关系的主播，内push限制频次降低
* Feature 音视频直播-C端直播首页针对新老用户分层分发
* Feature 音视频直播-C端福袋退款记录
* Feature 音视频直播-C端管理员可以发放福袋
* Feature 音视频直播-C端iting330：增加随机跳转pia戏的直播间
* Feature 音视频直播-C端直播分类列表页-限制上推荐位的主播，必须符合tab取数规则
* Feature 聊天室-厅战PK
* Feature 聊天室-贵族坐席底部主播信息交互优化
* Feature 聊天室-分享落地页链路优化
* Feature 聊天室-冠名一期
* Feature 泛直播-电商直播实时查看直播数据修改监听逻辑
* Feature【技术优化】系统消息（type=41）直播间底部菜单刷新优化
* Feature【设计优化】底栏&更多面板优化
* Feature【设计优化】直播间布局及icon优化
* Feature【设计优化】取消切换直播间时的PK面板提示动画
* Feature【设计优化】进直播间取消全量预加载动态表情/麦上动态表情，改为收到动态表情时实时下载渲染
* Feature【政策合规】直播未成年人提示弹窗增加倒计时优化（主APP，极速版APP，喜马直播APP）
* Feature【政策合规】聊天室榜单合规二期（主APP，极速版APP，喜马直播APP）

Version 9.0.41.3 *(2022-6-14)*
-----------------------------
直播

* Fix 聊天室SDK-连续两次登录聊天室长连接鉴权失败问题
* Fix 音视频播放器-PlayableModel equals 判断逻辑优化，解决娱乐派对声音播放串音问题
* Fix 音视频直播-解决连麦中最小化，返回直播间后拉cdn流导致回音
* Fix 音视频直播-直播间发送图片在部分机型加载本地图片失败

Version 9.0.39.3 *(2022-6-2)*
-----------------------------
直播

* Feature 音视频直播-商业化守护功能
* Feature 音视频直播-C端直播间→专辑，从专辑→直播，返回问题专项解决
* Feature 音视频直播-C端主播榜点击区域优化
* Feature 音视频直播-C端红包发放记录
* Feature 音视频直播-C端亲密度翻倍卡
* Feature 音视频直播-C端主播端评论区新增申请连麦消息和快捷接通操作
* Feature 音视频直播-C端直播动态2.0
* Feature 音视频直播-C端话题数据落盘
* Feature 音视频直播-C端红包发放记录接口增加安全处理
* Feature 音视频直播-C端专辑页推荐直播增加黑名单处理
* Feature 音视频直播-C端直播首页-活动糖葫芦支持配置时间，自动上下架
* Feature 音视频直播-C端福利中心签到弹窗支持跳转直播间并打开背包定位物品
* Feature 聊天室-麦位波
* Feature 聊天室-声音播放页退出拦截弹窗修改
* Feature 聊天室-抢帽子功能等级配置
* Feature 聊天室-抢帽子功能支持麦上主持
* Feature 泛直播-直播预约数据落库以及付费直播用户短信触达
* Feature 泛直播-粉丝团优化需求
* Feature【政策合规】个播PK功能合规优化
* Feature【政策合规】聊天室榜单
* Feature【政策合规】直播新增未成年人校验（主APP，极速版APP，喜马直播APP）
* Feature【政策合规】榜单+红包优化（主APP，极速版APP，喜马直播APP）
* Feature【政策合规】玩法榜单整改
* Feature【政策合规】打赏、主播榜单合规优化
* Feature【政策合规】聊天室增加团战PK次数限制
* Improve 技术优化-主站播放器支持视频播放，视频直播播放器改造
* Improve 技术优化-补全直播核心页面可用性监控（开播页面、预告页面、直播分类页-TAB页）

Version 9.0.36.3 *(2022-5-12)*
-----------------------------
直播

* Feature 音视频直播-直播首页改版5.0：①分列列表页卡片排序-支持运营可干预，服务端4.11开发完成 ②我的直播菜单区域app展示
* Feature 音视频直播-音视频直播跨房间PK玩法
* Feature 音视频直播-弹幕二期完善基础功能
* Feature 音视频直播-首页关注动态改版ABTest
* Feature 音视频直播-房间表情入口露出
* Feature 聊天室-派对心愿商店
* Feature 聊天室-支持声音礼物
* Feature 聊天室-个播/聊天室礼物面板UI优化
* Feature 聊天室-房间背景更换实时推送
* Feature 泛直播-课程直播专享/付费直播购买体验优化
* Feature 泛直播-课程直播回放数据支持上报
* Feature 泛直播-课程直播支持话题功能
* Feature 泛直播-课程直播间增加热度开关
* Feature 泛直播-课程直播H5直播间体验优化
* Improve 体验优化-直播间关闭按钮 ①点击区域不大 ②曲面屏问题
* Improve 技术优化-主站播放器支持视频播放，视频直播播放器改造
* Improve 技术优化-新增直播核心页面可用性监控

Version 9.0.30.3 *(2022-4-20)*
-----------------------------
直播

* Feature 音视频直播-直播间增加话题功能（原话题→改为公告）
* Feature 音视频直播-直播间「背景过期」场景处理
* Feature 音视频直播-禁止自打赏礼物
* Feature 音视频直播-PK模式选择入口配置中心新增默认逻辑
* Feature 音视频直播-福袋支持设置粉团等级
* Feature 音视频直播-福袋功能加风控
* Feature 聊天室-主站设计走查问题三期
* Feature 聊天室-贵族开通页面优化
* Feature 聊天室-礼物面板增加贵族开通入口
* Feature 聊天室-麦上表情则更加到聊天框
* Feature 聊天室-私信增加动效显示
* Feature 聊天室-团战PK
* Feature 聊天室-推荐位优先级逻辑优化
* Feature 聊天室-增加底部运营位及房间模式优化
* Feature 聊天室-贵族福利日适配聊天室
* Feature 泛直播-电商直播直播间购物车与讲解挂件优化
* Feature 泛直播-课程直播间支持直播间PV变为热度值
* Improve 技术优化-粉团收听时长修改计数逻辑

Version 9.0.27.3 *(2022-3-29)*
-----------------------------
直播

* Feature 音视频直播-商业化礼物墙2期
* Feature 音视频直播-C端弹幕卡奖励+弹幕应用
* Feature 音视频直播-C端直播间接入：客服【建议与投诉】入口 ，ABTest逐步放量
* Feature 音视频直播-C端粉丝团1期基础功能优化
* Feature 音视频直播-C端生日场申请页面体验优化
* Feature 音视频直播-C端分享页面更换链接优化
* Feature 音视频直播-C端热度外显：累计PV改成热度 及 分列列表页应用
* Feature 音视频直播-C端（私信优化）主播可给用户发送私信
* Feature 聊天室-榜单增加奖励自动下发
* Feature 聊天室-榜单规则入口、交互
* Feature 聊天室-PC端增加H5榜单交互
* Feature 聊天室-「话题」名称修改为「玩法」
* Feature 聊天室-欢迎语消息对麦上主播显示
* Feature 聊天室-退出拦截弹窗修改
* Feature 聊天室-新增派对小时榜
* Feature 聊天室-主站设计走查问题二期
* Feature 聊天室-关注功能优化
* Feature 聊天室-增加连击礼物
* Feature 泛直播-业务直播查询工具
* Feature 泛直播-直播通用榜单组件&3月策略会商财榜单需求
* Feature 泛直播-课程直播支持粉丝团功能
* Improve 技术优化-直播回放
* Improve 技术优化-电商直播打开卖货弹窗参数不取iting参数

Version 9.0.21.3 && 9.0.22.3 *(2022-3-8)*
-----------------------------
直播

* Feature 音视频直播-通行证优化
* Feature 音视频直播-抢头条
* Feature 音视频直播-贵族福利日玩法
* Feature 音视频直播-C端专辑推荐跟购物车商品推荐入口合并&切换
* Feature 音视频直播-C端开播页设置增加设置背景功能
* Feature 音视频直播-C端"表情包+1"2期
* Feature 音视频直播-C端福袋选礼物个数及交互优化
* Feature 音视频直播-C端开播资源位支持按音频直播和视频直播维度配置
* Feature 音视频直播-C端评论区支持上下滑ABTest固化（即删除ABTest代码，评论区支持上下滑）
* Feature 音视频直播-C端评论区支持输入框文案可配置
* Feature 音视频直播-C端首页卡片增加"标签"和"福袋/红包"的埋点信息上报
* Feature 音视频直播-C端红包提示优化
* Feature 音视频直播-C端主站设计走查问题修复1期
* Feature 音视频直播-C端音视频直播间视图层级分层优化
* Feature 音视频直播-C端增加连麦"发言"状态展示
* Feature 音视频直播-C端主播端开播保存上场直播"允许观众连麦"打开状态
* Feature 聊天室-更多按钮接入运营位配置
* Feature 聊天室-增加财富等级礼物
* Feature 聊天室-增加贵族功能
* Feature 聊天室-增加弹幕功能
* Feature 聊天室-PGC聊天室接入话题
* Feature 电商直播-修复视频直播横屏不展示"去购买飘屏"
* Feature 电商直播-支持关播结束页查看带货成交数据
* Improve 技术优化-房间查询大小挂件接口添加bizType参数说明

Version 9.0.18.3 && 9.0.19.3 *(2022-2-15)*
-----------------------------
直播

* Feature 音视频直播-通行证优化
* Feature 音视频直播-C端生日场功能
* Feature 音视频直播-C端主播邀请用户上麦功能
* Feature 音视频直播-C端支持不同的主播池子/房间配置生成不同的跳转iting
* Feature 音视频直播-C端首页直播卡片-运营位&流量卡支撑全民直播
* Feature 音视频直播-C端Push推送增强用户与主播关系（关注关系&粉丝团关系）
* Feature 音视频直播-C端主播榜/礼物墙弹窗修改为184弹窗
* Feature 聊天室-PGC底部工具栏统一排序及样式
* Feature 聊天室-红包类型增加关注红包
* Feature 聊天室-剧本作者信息显示修改
* Feature 电商直播-直播中支持开启购物车功能
* Feature 电商直播-横屏评论区优化
* Feature 音视频直播-背包物品、虚拟币支持跳转指定iting
* Feature 音视频直播/聊天室-实名认证弹窗文案优化
* Improve 音视频直播-增加话题违规提示

Version 9.0.12.3 *(2022-1-12)*
-----------------------------
直播

* Feature 音视频直播-商业化PK进度条图标支持配置下发
* Feature 音视频直播-商业化贵族在线列表和打赏榜点击头像后的交互体验统一
* Feature 音视频直播-商业化贵族进场动画速度调慢
* Feature 音视频直播-C端首次赠送粉团卡礼物自动加粉团
* Feature 音视频直播-C端「打招呼」按键优化
* Feature 音视频直播-C端声音播放页概率增强有「福袋」的直播间
* Feature 音视频直播-C端小时榜加进主播榜
* Feature 音视频直播-C端优化红包与评论奖励弹窗出现逻辑
* Feature 聊天室-抢帽子增加升级玩法
* Feature 聊天室-开播界面增加UGC类型
* Feature 聊天室-月榜入口隐藏
* Improve 课程直播-技术优化课程直播间发消息和收消息对接聊天室SDK

Version 9.0.9.3 *(2021-12-21)*
-----------------------------
直播

* Feature 音视频直播-首页改版3期
* Feature 音视频直播-评论区支持上下滑AB测试
* Feature 音视频直播-直播间接入Pia戏
* Feature 音视频直播-交友模式申请条件透明化
* Feature 音视频直播-视频端横竖屏2期
* Feature 音视频直播-首次评论有惊喜
* Feature 音视频直播-封面角标：功能类角标支持PK、交友模式、生日场
* Feature 音视频直播-开播页增加资源位&设置
* Feature 音视频直播-贵族在线列表优化
* Feature 聊天室-PC、APP-房间模式切换保留麦上用户
* Feature 聊天室-PC、APP-增加派对荣耀榜
* Feature 聊天室-PC、APP-增加在线榜
* Feature 聊天室-上麦用户标签显示
* Feature 聊天室-UGC房间活动弹窗
* Feature 聊天室-UGC、PGC、交友模式 用户上麦/ 被邀请上麦 进行实名认证
* Improve 音视频直播&课程直播-购物列表优化：①只取开播状态中的直播（个人音视&课程直播），②缓存问题：课程直播开播选择商品后，3-5分钟才出现在此列表中

Version 9.0.3.3 *(2021-11-30)*
-----------------------------
直播

* Feature 音视频直播-首页改版二期
* Feature 音视频直播-自打赏用户引导
* Feature 电商直播-直播接分销商品库
* Feature 电商直播-付费直播MVP
* Feature 聊天室-支持发言气泡
* Feature 聊天室-新增iting：随机跳转当前正在开播且人数最多的UGC类型房间；若人数最多的房间有多个，则随机跳转；若没有则跳转推荐tab页；房间类型1=一起聊、2=KTV、3=Pia戏
* Feature 聊天室-PGC/UGC 语音房举报需求优化
* Improve 音视频直播-开播收听1分钟「退出拦截弹窗」增加关闭按钮
* Improve 音视频直播-话题进场时可见
* Improve 音视频直播-聊天室/交友模式麦上用户音浪改用ZegoSdk接口实现

Version 9.0.1.3 *(2021-10-26)*
-----------------------------
直播

* Feature 音视频直播-直播首页标签增加「卖货」标签及标签样式优化
* Feature 音视频直播-返回上个直播间
* Feature 音视频直播-福袋需求
* Feature 音视频直播-主站app改版：搜索条挪上面，涉及直播「我的直播」入口变更
* Feature 音视频直播-商业化普通PK主播侧支持关闭对方语音
* Feature 音视频直播-商业化PK复仇玩法
* Feature 聊天室-剧本详情页的剧本来源和授权方式突出显示
* Feature 聊天室-专辑页/播放页直播状态显示
* Feature 聊天室-增加麦上头像框/座位框展示
* Feature 聊天室-互动列表页增加运营位
* Feature 聊天室-专辑页/播放页直播状态显示
* Feature 聊天室-剧本面板优化
* Feature 消息中心-支持直播状态展示
* Improve 技术优化-长链接支持活动类消息推送

Version 8.3.36.3 *(2021-9-28)*
-----------------------------
直播

* Feature 音视频直播-退出拦截弹窗
* Feature 音视频直播-专辑推荐优化
* Feature 音视频直播-短信召回
* Feature 音视频直播-严禁未成年人进房提示
* Feature 音视频直播-直播间增加私信功能
* Feature 音视频直播-热词优化
* Feature 音视频直播-卖货权限放开
* Feature 音视频直播-视频直播保存回听
* Feature 音视频直播-美颜SDK升级
* Feature 音视频直播-直播进播放礼物
* Feature 聊天室-Pia戏剧本筛选
* Feature 聊天室-Pia戏剧本搜索

Version 8.3.30.3 *(2021-9-8)*
-----------------------------
直播

* Feature 音视频直播-互娱（房间）关注数据上落盘看板建设
* Feature 音视频直播-首页增加直播糖葫芦
* Feature 音视频直播-直播首页增加推荐Tab及图标可配置化
* Feature 音视频直播-多业务房间支持上下滑切换
* Feature 音视频直播-抢红包点赞引导
* Feature 课程直播-专享直播支持试看
* Feature 聊天室-装扮中心+背包
* Feature 互动中台-长连接底层协议框架升级

Version 8.3.27.3 *(2021-8-19)*
-----------------------------
直播

* Feature 音视频直播-商业化礼物墙功能
* Feature 音视频直播-商业化财富等级扩级
* Feature 音视频直播-视频播放器升级优化
* Feature 音视频直播-支持配置限时下架表情
* Feature 音视频直播-热词+表情包
* Feature 音视频直播-红包界面优化全屏改半屏
* Feature 音视频直播-非Wi-Fi网络环境下载动画资源优化
* Feature 聊天室-开学季
* Feature 聊天室-运营位功能
* Feature 聊天室-BGM功能

Version 8.3.21.3 *(2021-8-3)*
-----------------------------
直播

* Feature 音视频直播-直播间优化（直播间上下滑动优化、左滑显示更多直播）
* Feature 音视频直播-红包交互动画优化
* Feature 音视频直播-优化关注->加粉丝团引导链路
* Feature 音视频直播-贵族体系支持皇帝+男爵贵族
* Feature 聊天室-Pia戏一期

Version 8.3.18.3 *(2021-7-12)*
-----------------------------
直播

* Feature 音视频直播-音视频整合2期上线
* Feature 聊天室-UGC房间体系上线

Version 8.3.12.3 *(2021-6-22)*
-----------------------------
直播

* Feature 音视频直播-背包自定义物品支持批量送配置
* Feature 音视频直播-夺宝玩法全新UI

Version 8.3.9.3 *(2021-6-3)*
-----------------------------
直播

* Feature 音视频直播-极速版时长宝箱
* Feature 音视频直播-1元首充

Version 8.3.3.3 *(2021-5-17)*
-----------------------------
直播

* Feature 音视频直播-音视频整合1期-主播侧
* Feature 音视频直播-幸运魔盒优化
* Improve 互动基础设施-视频播放器首帧、卡顿和播放错误监控

Version 8.0.1.3 *(2021-4-28)*
-----------------------------
直播

* Feature 音视频直播-账号页直播入口改版
* Feature 音视频直播-v2.5直播音视频连麦（主播-主播）
* Feature 音视频直播-v2.5直播音视频连麦（主播-主播）
* Improve 音视频直播-礼物运营后台优化和资源下载优化
* Feature 音视频直播-直播间进场官方公告位置修改
* Improve 音视频直播-PK面板点击对方主播头像直接进入直播间
* Feature 互动生态-发现页添加互动广场页
* Improve 互动生态-修复一起听反馈连麦问题

Version 7.3.27.3 *(2021-4-13)*
-----------------------------
直播

* Feature C端-开播页条款与直播预告小优化
* Feature C端-直播间进场官方公告位置修改

Version 7.3.23.3 *(2021-3-30)*
-----------------------------

直播

* Feature 商业化-PK星际争霸

Version 7.3.21.3 *(2021-3-23)*
-----------------------------

直播

* Feature C端-直播间在线列表
* Feature C端-视频直播观众-主播连麦
* Improve C端-直播无障碍模式体验改进
* Improve C端-直播间抢红包插件防御

Version 7.3.18.3 *(2021-3-8)*
-----------------------------

直播

* Feature C端-直播外接极速版，极速版版本号v2.1.6.3，2021年6月25日发包
* Feature C端-视频直播时移（如体育类型的直播中，可以拖拽进度到之前时间的直播内容）
* Feature C端-ZegoSdk升级，提升视频直播美颜效果
* Feature 商业化-相亲交友
* Feature 商业化-礼物面板连击特效

Version 7.3.9.3 *(2021-2-3)*
-----------------------------

直播

* Feature C端-直播间和聊天室系统通知引导语更新
* Feature C端-粉丝团勋章等级字体/送礼卡片PK模式礼物ICON放大优化
* Feature C端-音视频直播开播流程整合
* Feature C端-视频直播优化2.4
* Feature C端-一起听线上版本问题修复和优化（包括非房主主动暂停状态处理、声音互斥处理以及UI层面优化等）
* Feature C端-一起听线创建房间样式改版，提高点击率
* Feature C端-直播间踢人
* Feature C端-视频直播支持口令红包
* Feature C端-直播无障碍优化
* Feature 商业化-充消限制
* Feature 商业化-Pk合计包

Version 7.3.3.3 *(2021-1-14)*
-----------------------------

直播

* Feature C端-一起听支持微博渠道分享
* Feature C端-普通红包、定时红包入口合并
* Feature C端-半屏模式下大小挂件整合
* Feature 商业化-礼物抽奖玩法

Version 7.0.8.3 *(2021-1-4)*
-----------------------------

直播

* Feature C端-助眠直播优化
* Feature C端-发言红包
* Feature C端-红包换肤功能
* Feature C端-一起听全部房间列表增减推荐直播入口
* Feature C端-一起听相声馆
* Feature C端-进场通道整合优化
* Feature C端-粉丝团进场通知
* Feature C端-贵族的进场通知支持显示对应爵位的名称
* Feature C端-粉丝团数据/任务调整
* Feature 商业化-直播间上头条功能
* Feature 商业化-飘屏系统升级
* Feature 商业化-飘屏系统升级

Version 6.7.30.3 *(2020-12-17)*
-----------------------------

* Fix 在 6.7.27.3 版本基础上修复一些缺陷

Version 6.7.27.3 *(2020-12-14)*
-----------------------------

直播

* Feature C端-直播间赞助榜单移除总榜入口（未在部门大群中及时同步到运营，以后功能清单及时同步到相关方）
* Feature C端-青少年/儿童模式漏洞修复
* Feature C端-分享文案改动优化
* Feature C端-新手福利功能
* Feature C端-直播间弹起发言框优化（不打断飘屏和礼物特效播放）
* Feature C端-增加粉丝团升级弹窗
* Feature C端-更多操作栏红点提醒过多优化
* Feature C端-粉丝团入口交互、粉丝团改Tab页
* Feature C端-PK过程中手动退出直播处理逻辑完善
* Feature C端-信息流直播条兼容红包露出
* Feature C端-点击PK面板中匹配到的主播资料卡交互优化
* Feature C端-礼物按钮添加引导动效
* Feature 商业化-礼物栏附加活动运营内容
* Improve 技术优化-直播间发送图片去除url后面的宽和高
* Improve 技术优化-直播间内通用弹窗参数拼接去重处理
* Improve 技术优化-主播端指定PK搜索键盘优化
* Fix CDN切换后声音播放延迟问题
* Fix CDN切换后直播播放重试问题
* Fix 全站Gif/WebP动态渲染静止问题（ImageManager框架存在问题）
* Fix 直播主播端拨打电话结束后闭麦无效问题

Version 6.7.21.3 *(2020-11-16)*
-----------------------------

* Fix 在 6.7.18.3 版本基础上修复打开隐私弹窗无法点击同意按钮问题（兼容性问题）

Version 6.7.18.3 *(2020-11-12)*
-----------------------------

直播

* Feature C端-声音播放页直播入口优化
* Feature C端-直播间话题展示优化
* Feature C端-聊天室新增欢迎语
* Feature C端-搜索热词支持配置化
* Feature C端-App内打开H5直播引导页自动跳转到native的直播间
* Feature 商业化-小额礼物
* Feature 商业化-贵族3.0
* Feature 商业化-PK排位赛点击直播间底部工具栏送礼不再联调礼物
* Improve 技术优化-ExoPlayer播放器上线
* Fix PK面板时间往回跳问题

Version 6.7.12.3 *(2020-10-26)*
-----------------------------

直播

* Feature C端-聊天室新增概率玩法-奇妙波波球
* Feature C端-聊天室新增在线用户列表
* Feature C端-增加指定麦位送礼头像定位
* Feature C端-声音交友1.1优化
* Feature C端-声音交友2.0版本
* Feature C端-键盘发言后直接收起
* Feature C端-关注红包
* Feature C端-直播间头部加关注+加粉丝团动效交互调整
* Feature C端-直播间弹起键盘交互处理
* Feature C端-直播Tab页开播入口逻辑优化
* Feature 商业化-通行证1.1
* Feature 商业化-PK 排位赛2.1
* Feature 商业化-指定主播PK
* Feature 商业化-看世界2.0
* Improve 技术优化-混淆配置由Host迁移到Bundle

Version ******* *(2020-9-22)*
-----------------------------

直播

* Feature C端-在线访谈
* Feature C端-主播任务改版
* Feature C端-粉丝团及关注入口合并
* Feature C端-直播动态加入粉丝团
* Feature C端-直播间更多菜单项目由服务端配置
* Feature C端-开播前弹窗提醒《开播须知》
* Feature C端-直播间个人资料卡点击区域优化
* Feature C端-直播间普通进场通知无新进场消息时3s后自动隐藏
* Feature C端-视频直播主播端
* Feature C端-视频直播购物袋
* Improve 商业化-直播间通用弹窗
* Improve 商业化-充值模版
* Improve 商业化-贵族进场通知优化
* Improve 技术优化-移除旧的连麦逻辑
* Fix 未开播过的主播首次开播时，开播首页异常提示

Version ******* *(2020-8-31)*
-----------------------------

直播

* Feature C端-连麦最小化
* Feature C端-一起听1.1优化
* Feature C端-直播间举报
* Feature 商业化-礼物面板礼物缩略动画（gif/webp动图）
* Improve 商业化-通行证
* Improve 技术优化-邦邦安全治理
* Fix 未开播过的主播首次开播时，开播首页异常提示

Version 6.6.99.3 *(2020-8-17)*
-----------------------------

直播

* Feature C端-封面质量优化
* Feature C端-升级即构SDK20200623版本，支持视频直播
* Improve 商业化-Android礼物面板批量送箭头显示逻辑和批量送文案弹窗显示逻辑由v9接口中的customBatchInfos控制
* Improve 商业化-上上签开签动效提示文案部分优化（文字和文字背景）
* Fix 流量-直播埋点数据验证和修复问题
* Fix 直播听众端非礼物触发特效消息队列、弹幕消息队列、进场通知消息队列在页面销毁时没有Release问题

Version 6.6.93.3 *(2020-8-1)*
------------------------------

直播

* Feature 商业化-上上签
* Feature 商业化-一起去旅行
* Feature C端-一起听
* Feature C端-直播间增加专辑推广
* Feature C端-直播间快捷发言
* Feature B端-独家主播续约（公会侧）
* Improve C端-限制左右滑的直播间不出现引导弹窗
* Improve C端-礼物面板背包物品详情弹窗文字单行居中，多行居左显示
* Improve C端-幸运礼物进度条显示问题（当进度为93.6%时，进度条显示已满）
* Improve 技术优化-删除ZIP包下载和展示相关代码，已废弃
* Improve 技术优化-模版资源下载目录由Images改为gift/template，和特效资源统一管理

Version ******** *(2020-7-27)*
----------------------------

直播

* Feature直播间、语音房榜单优化
* Feature直播间切换规则限定
* Feature聊天室和电台房违规下架后踢出房间成员
* FeatureAndroidX库适配
* Improve语音房、聊天室礼物面板魅力值标签样式优化
* Fix 直播首页直播动态动效性能问题（当直播首页不可见时，停止播放动效）

Version ******** *(2020-7-6)*
------------------------------

直播

* Feature 财富等级2.0
* Feature PK排位赛新道具
* Feature 禁止已经实名认证的未成年用户充值和消费
* Feature 麦上动效2.0
* Improve 个性装扮支持取消装扮
* Improve Android Zego Sdk 适配，Sdk版本号20200520
* Improve Android直播/聊天室/电台房配乐改版（主App录音改版了配乐页面）
* Improve 直播文件上传改用分片上传库
* Fix Android夺包入口支持财富等级配置

视频直播

* Feature 视频直播竖屏支持
* Fix 视频直播挂件跳转失败问题（拼接查询参数错误导致的）

消息中心

* Improve IM SDK 接入 APM 网络监控
* Improve 消息中心文件上传改用分片上传库

Version 6.6.81.3 *(2020-6-16)*
------------------------------

直播

* Feature 禁止已经实名认证的未成年用户充值和消费
* Feature 主播解约续约
* Feature 语音房气泡
* Improve 夺宝商城优化-直播间打开夺宝页面时在url后面添加roomId和anchorUid，支持飘屏跳转
* Improve 聊天室发送消息增加CD值控制
* Improve 直播间web容器适配HybridNativeFragment
* Fix 快速连续赠送初级宝箱误触至尊宝箱

视频直播

* Feature 修改、查看直播间公告
* Feature 知识类目的直播聊天室可选择查看全部评论或仅查看主播和管理员评论
* Improve 聊天室适配分享、关注、公告修改类型的消息

Version 6.6.75.3 *(2020-5-28)*
------------------------------

直播

* Feature 直播开播标题随机优化
* Feature 直播主播违规强提醒
* Feature 商业化幸运礼物升级
* Feature 语音房邀请上麦
* Feature 语音房快速上麦
* Feature 直播、语音房、K歌房埋点
* Improve 连麦SDK接入直播
* Improve 语音房私信官方号同步
* Improve 直播LiveBundle Legacy拆分整合
* Fix 直播首页数据请求加载策略

视频直播

* Feature 视频直播间v1.1

Version 6.6.72.3 *(2020-4-29)*
------------------------------

* Feature 直播个人中心优化
* Feature 开播页引导主播优化封面和标题
* Feature 粉丝团动效优化
* Feature 直播间分享改版
* Feature 直播间埋点

* Improve UI改版遗留问题2期

语音玩法

* Feature 语音房私信

Version 6.6.66.3 *(2020-4-15)*
------------------------------

直播

* Feature 直播动态动效优化
* Improve 红包风控滑块支持自定义标题
* Improve 直播间首冲弹窗避让逻辑
* Improve 头像挂件支持WebP
* Improve UI改版遗留问题1期
* Improve 我的直播地址支持native和h5切换
* Improve 直播间查询喜钻接口升级到v4

语音玩法

* Feature 聊天室UI改版
* Feature 聊天室嘉宾模式优化

Version 6.6.63.3 *(2020-3-27)*
------------------------------

直播

* Feature 直播间UI改版
* Feature 粉丝团改版1期：样式改版+定制勋章为主
* Feature 直播间PK改版
* Feature 礼物支持按品类配置
* Improve 背包支持批量赠送
* Improve 直播接入聊天室SDK
* Improve 直播间聊天列表头像批量加载优化
* Improve 直播互动整合基本完成，遗留Legacy待整合

语音玩法

* Feature 帽子体系

Version 6.6.57.3 *(2020-3-16)*
------------------------------

直播

* Feature 直播间心愿单
* Feature 历史1元首冲
* Improve 直播互动个人资料卡整合

语音玩法

* Feature 语音房、电台房座位体系
* Feature 直播首页语音房卡片样式改版
* Feature 语音房新手引导
* Feature 语音房支持MP4动态背景
* Feature 我的守护

Version 6.6.54.3 *(2020-2-26)*
------------------------------

直播

* Fix 进场特效scaleType设置为inside

语音玩法

* Feature 语音房推荐关注弹窗

Version 6.6.48.3 *(2020-1-17)*
------------------------------

直播

* Feature 流量卡
* Feature 直播开播分类标签优化
* Feature 主播培训课程
* Improve 技术优化-直播互动礼物动效整合
* Improve 技术优化-直播间内小挂件弹窗优化

语音玩法

* Feature 语音房UI改版
* Feature 电台房新增守护
* Feature 黄金守护有效期间，续费触发房间内飘屏

Version 6.6.45.3 *(2019-12-26)*
------------------------------

直播

* 春节打开福利
* 流量卡
* 直播互动运营位整合
* 直播互动新增个性装扮入口

语音玩法

* 守护特权优化
* 电台主播任务
* 推送策略调整
* 个人页新增语音房入口
* 守护收礼物优化

Version 6.6.39.3 *(2019-12-12)*
------------------------------

直播

* 小挂件支持Android端星座抽奖配置
* 新版贵族入口地址替换
* "我的直播"页支持iting跳转
* 通用Web容器梳理整合，支持iting打开
* 解决礼物下载耗时问题

语音玩法

* 语音房支持红包
* 电台房青铜守护勋章优化

Version 6.6.36.3 *(2019-11-28)*
------------------------------

直播

* 幸运礼物
* 贵族体系优化
* 暗黑模式

语音玩法

* 语音房/K歌房支持分享
* 语音房进入直播动态
* 暗黑模式

Version 6.6.30.3 *(2019-11-18)*
------------------------------

直播

* 新用户进入直播间送免费物品
* 收听奖励
* 直播间大挂件支持轮播
* 打企鹅游戏
* 直播业务域名切换（mobile.ximalaya.com 切到 live.ximalaya.com）
* 背包支持全业务
* 删除活动直播代码

语音玩法

* 语音房流量支持（语音房进App推荐页和分类页）
* 语音房支持宝箱
* 语音房支持抽奖
* 语音房创建入口优化

Version 6.6.27.3 *(2019-11-1)*
------------------------------

直播

* Pk玩法优化（细节体验优化及增加迷雾护盾道具）
* 配乐业务插件化

语音玩法

* 语音房支持飘屏
* 电台模式黄金守护新增特效

Version 6.6.21.3 *(2019-10-18)*
------------------------------

直播

* 勋章墙域名修改，由mobile.ximalaya.com改为liveroom.ximalaya.com
* 首页直播Tab页引导弹屏优化，改为第二次进入直播Tab页时显示
* 背包支持过期

语音玩法

* 语音房支持发图片、支持@功能

Version 6.6.18.3 *(2019-09-10)*
------------------------------

直播

* 直播间进场通知添加签名
* 直播间收听奖励（入口屏蔽）

语音玩法

* 电台房1.2，主要功能点：电台房守护7天期限、黄金守护增加特权
* K歌房1.0

Version 6.6.12.3 *(2019-09-10)*
------------------------------

直播

* 直播首页支持电台房混流
* 直播间红包集成风控滑块验证

语音玩法

* 电台房1.1，主要功能点：支持进场特效、主持人自动开通守护团

Version 6.6.9.3 *(2019-08-29)*
------------------------------

直播

* 直播首页封面改版
* 直播首页榜单改版
* 直播首页语音房卡片改版
* 直播间热词
* 直播装扮中心
* 直播非诚勿扰1.0
* 支持主播以听众身份进入自己直播间
* 直播礼物面板运营位改版
* 指定直播间礼物

语音玩法

* 电台房1.0
* 娱乐厅运营位
* 麦上动效表情
* 主持人推送粉丝
* 我的直播-新增电台模式入口
* 我听页-娱乐厅导流

Version 6.6.3.3 *(2019-08-02)*
------------------------------

直播

* 直播首页改版
* 喜钻充值支持RN收银台、支持云闪付
* 贵族皇帝定制礼物
* 夺宝和商城
* 直播间图片裁剪
* 直播 lamia 接口重构
* 免费礼物
* 退出直播间弹窗优化
* 财富等级改版

语音玩法

* 娱乐厅首页改版
* 娱乐厅榜单改版
