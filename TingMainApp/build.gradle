plugins {
    id 'com.google.devtools.ksp' version "${KSP_VERSION}"
}
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-parcelize'
apply from: project.rootDir.absolutePath + '/util.gradle'
apply from: project.rootDir.absolutePath + '/config_plugin.gradle'
//apply plugin: 'kotlin-kapt'

apply from: "./publishAarForBuildAcc.gradle"

if (rootProject.ext.isBuildCommonRes.toBoolean()) {
    apply plugin: 'com.ximalaya.gradle.generatepublicxml'
}

if (!BUILD_PLUGIN_APK.toBoolean() && (!rootProject.ext.isBuildCommonRes.toBoolean()) && !buildFast.toBoolean()) {
    apply plugin: 'com.ximalaya.gradle.makeroute'
}

// todo
//dependencies {
//    implementation group: "com.tencent.matrix", name: "matrix-android-lib", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-android-commons", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-trace-canary", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-resource-canary-android", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-resource-canary-common", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-io-canary", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-sqlite-lint-android-sdk", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-battery-canary", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-hooks", version: MATRIX_VERSION, changing: true
//    implementation group: "com.tencent.matrix", name: "matrix-backtrace", version: MATRIX_VERSION, changing: true
//}

dependencies {
    // 本地不要在使用文件依赖,可以都上传到maven上进行依赖
//    api fileTree(include: '*.jar', dir: 'libs')

//    implementation(name: 'driskv2', ext: 'aar')
//    api files('libs/driskv2.aar')
    // 这个暂时不能上传到maven
//    api files('libs/epublib-core-latest.jar')
    api "com.ximalaya.ting.android.epublibcore:epublibcore-developer:1.0.0"

    api "third_sdk:du:1.0"
//    api "third_sdk:nanohttpd:2.3.1"
    api "third_sdk:PhotoView:1.0"
//    api "third_sdk:Qt:3.5.2_20220929"
    api "third_sdk:statisticservice:1.0"
    api "third_sdk:tinypinyin:2.0.2"
//    api "third_sdk:drisk:v2@aar"
    api 'io.reactivex.rxjava2:rxjava:2.2.19'
    api 'io.reactivex.rxjava2:rxandroid:2.1.0'
    annotationProcessor 'com.ximalaya.gradle:router-compiler-developer:1.0.0'
//    ksp rootProject.ext.xmDependencies.xmprocessor
    api rootProject.ext.xmDependencies.androidxFragment

    api project(path: ':XAndroidFramework:XFramework', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:CommonPayModule:WXPayModule', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:CommonPayModule:AliPayModule', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:CommonPayModule:CcbPayModule', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:CommonPayModule:AbcPayModule', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:CommonPayModule:SpdbPayModule', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:CommonPayModule:BocPayModule', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:XmRouterScheme', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:LocalServerService', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:EasyFloat', configuration: rootProject.configurationType)
//    api project(path: ':XAndroidFramework:VirtualView', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:Vlayout', configuration: rootProject.configurationType)
    api project(path: ':XAndroidFramework:Tangram', configuration: rootProject.configurationType)
//    api('com.alibaba.android:virtual-common:1.0.11') {
//        changing = true
//    }
    api 'com.ximalaya.ting.android.websocket:websocket-developer:1.0.3'

    api 'io.github.scwang90:refresh-layout-kernel:2.1.0'

//    api 'com.ximalaya.ting.android.xmgaiax:xmgaiax-Adapter-developer:0.4.7'
//    api "com.ximalaya.ting.android.xmgaiax:gaiax-Analyze-developer:0.4.7"
//    api ':XAndroidFramework:GaiaXAnalyze:GXAnalyzeAndroid'
//    api project(path: ':XAndroidFramework:GaiaXAndroidAdapter', configuration: rootProject.configurationType)
//    api project(path: ':XAndroidFramework:GaiaXAndroid', configuration: rootProject.configurationType)

    api 'com.ximalaya.ting.android.ucrop:ucrop-' + UCROP_REPO_BRANCH + ':' + UCROP_REPO_VRSION

    api 'com.huawei.agconnect:agconnect-core:1.7.3.300'
//    api 'com.huawei.hms:awareness:3.8.0.300'
    api "androidx.core:core:1.6.0"
    api 'com.ximalaya.ting.android.xuid:xuid-' + XUID_REPO_BRANCH + ':' + XUID_REPO_VRSION + '@aar'
    api 'com.ximalaya.ting.android.xticket:xticket-' + XTICKET_REPO_BRANCH + ':' + XTICKET_REPO_VRSION + '@aar'

    //阅读器模块拆分
    api 'io.github.youth5201314:banner:2.2.2'
    if (openBlockCanary.toBoolean() && isReleaseDebug.toBoolean()) {
        api rootProject.ext.xmDependencies.blockcanaryVersion
    } else {
        api rootProject.ext.xmDependencies.blockcanaryNoOpVersion
    }

    if (rootProject.isOpenBootMonitor.toBoolean()) {
        api 'com.ximalaya.ting.android.xmbootmonitor:xmbootmonitor-' + XmBootMonitor_REPO_BRANCE + ':' + XmBootMonitor_REPO_VERSION
    } else {
        api 'com.ximalaya.ting.android.xmbootmonitornoop:xmbootmonitornoop-' + XmBootMonitorNoOp_REPO_BRANCE + ':' + XmBootMonitorNoOp_REPO_VERSION
    }

    if (rootProject.isReleaseDebug.toBoolean()) {
        api 'com.ximalaya.privacyprotector:privacy_protector-' + XM_PRIVACY_PROTECTOR_BRANCH + ':' + XM_PRIVACY_PROTECTOR_VERSION
    } else {
        api 'com.ximalaya.privacyprotector:privacy_protector_no_op-developer:1.0.0'
    }

    if (rootProject.isReleaseDebug.toBoolean()) {
        api 'com.ximalaya.ting.android.lockwatcher:lockwatcher-' + LOCK_WATCHER_REPO_BRANCH + ':' + LOCK_WATCHER_REPO_VRSION
        api 'com.bytedance:bytehook:1.0.7'
    } else {
        api 'com.ximalaya.ting.android.lockwatchernoop:lockwatchernoop-' + LOCK_WATCHER_NO_OP_REPO_BRANCH + ':' + LOCK_WATCHER_NO_OP_REPO_VRSION
    }

    if (rootProject.openBTrace.toBoolean()) {
        implementation ("com.bytedance.btrace:rhea-core:2.0.3-rc02") {
//            exclude group: "org.nanohttpd", module: "nanohttpd"
        }
    }

    api 'com.ximalaya.ting.android.common:xperformance:1.0.6'

    api rootProject.ext.xmDependencies.multiDex
    api rootProject.ext.xmDependencies.wechatSdkVersion
    api rootProject.ext.xmDependencies.lottie
    api rootProject.ext.xmDependencies.cardview
    api rootProject.ext.xmDependencies.percent
    api rootProject.ext.xmDependencies.recyclerView
    api rootProject.ext.xmDependencies.material
    api rootProject.ext.xmDependencies.asynclayoutinflater

    api rootProject.ext.xmDependencies.zxing
    api 'com.ximalaya.ting.android.detect:detect-' + NEW_FRAMEWORK_REPO_BRANCH + ':' + EMULATOR_REPO_VERSION

    api 'com.ximalaya.ting.android.fileprotector:xmFileProtector-developer:1.0.4'

    api 'com.meituan.android.walle:library:1.1.6'
//    api 'dnsjava:dnsjava:3.4.2'

    api("com.ximalaya.ting.android:privacy_risk_collector-developer:1.1.2")


    api('com.ximalaya.ting.android.xmpushservice:xmpushservice-developer:' + XMPUSHSERVICE_REPO_VRSION) {
        exclude group: 'com.ximalaya.ting.android.encryptservice', module: 'encryptservice-developer'
        exclude group: 'androidx.annotation', module: 'annotation'
    }

    api 'com.ximalaya.ting.android.locationservice:location-' + LOCATION_REPO_BRANCH + ":" + LOCATION_SERVICE_VERSION
    if (openStetho) {
        implementation rootProject.ext.xmDependencies.stetho
    } else {
        implementation rootProject.ext.xmDependencies.stethoNoOp
    }
    api rootProject.ext.xmDependencies.constraintLayout
    api rootProject.ext.xmDependencies.rebound
//    api(name: 'uppro_auth_sdk', ext: 'aar')
    api "third_sdk:uppro_auth_sdk:1.0@aar"
//    api(name: 'ccbsdk-1.7.6-sec', ext: 'aar')
//    api files('libs/ccbsdk-1.7.6-sec.aar')
    api "third_sdk:ccbsdk:1.7.6-sec@aar"

//    api(name: 'humesdk-1.0.0', ext: 'aar')//头条分包SDK
    api "third_sdk:humesdk:1.0.0@aar"

    // 稿定封面模版 sdk
    api(group: 'com.gaoding.imageeditor', name: 'imageeditor', version: '1.0.5', ext: 'aar') {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'com.squareup.okio'
    }

    api 'javax.inject:javax.inject:1'
    api 'third_sdk:csj:6.7.1.6@aar'
    api 'third_sdk:gdt:4.630.1500@aar'
//    api 'third_sdk:baidu:9.371@aar'
    api 'third_sdk:jad_yun:2.4.2@aar'

    if (openLogView.toBoolean() && isReleaseDebug.toBoolean() && !buildFast) {
        api rootProject.ext.xmDependencies.logView
    }

    api ('com.ximalaya.ting.android.hybrid:hybridview-' + HYBRID_VIEW_REPO_BRANCH + ':' + HYBRID_VIEW_REPO_VRSION) {
        exclude group: 'com.ximalaya.ting.android.xmtrace', module: 'trace-developer'
    }
    api 'com.ximalaya.ting.android:xmpagemonitor:1.5.3'
    api 'com.ximalaya.ting.android:web_prerequest-developer:1.0.7'

//    implementation group: "com.tencent.matrix", name: 'matrix-traffic', version: '2.1.0'
//    implementation group: "com.tencent.matrix", name: 'matrix-android-lib', version: '2.1.0'

    api 'com.ximalaya.ting.android.packetcapture:packetcapture-' + PACKET_CAPTURE_REPO_BRANCH + ':' + PACKET_CAPTURE_REPO_VRSION

    api 'com.ximalaya.ting.android.alphamovie:alphamovie-' + ALPHAMOVIE_REPO_BRANCH + ':' + ALPHAMOVIE_REPO_VRSION
    api 'com.ximalaya.ting.android.upload:xmuploader-' + XM_OBJECT_UPLOAD_REPO_BRANCH + ':' + XM_OBJECT_UPLOAD_REPO_VERSION
    api 'com.ximalaya.ting.android.xmrecorder:xmrecorder-' + XM_RECORDER_REPO_BRANCH + ':' + XM_RECORDER_REPO_VERSION + '@aar'
    if (INCLUDE_PLAYER_SOURCE_COMPILE.toBoolean()) {
        api project(path: ':android-framework:platform:XmVideoPrj:XmVideo', configuration: rootProject.configurationType)
    } else {
        api 'com.ximalaya.ting.android.xmvideo:xmvideo-' + XM_VIDEO_REPO_BRANCH + ':' + XM_VIDEO_REPO_VERSION
//        api 'com.ximalaya.ting.android.ffmpeg:ffmpeg:0.1.0'
    }

    api 'com.ximalaya.ting.android.dex2oat:speed_dex2oat-' + SPEED_DEX2OAT_BRANCH + ':' + SPEED_DEX2OAT_VERSION
    api 'com.ximalaya.ting.android.xmlymmkv:xmlymmkv-' + XmMMKV_REPO_BRANCE + ':' + XmMMKV_REPO_VERSION

    api 'org.jsoup:jsoup:1.12.1'

    api 'com.ximalaya.ting.android.launcherBadge:launcherBadge-' + LAUNCHER_BADGE_REPO_BRANCH + ':' + LAUNCHER_BADGE_REPO_VRSION

    api 'com.ximalaya.commonaspectj:commonAspectj-dev:2.0.11'

    api 'com.ximalaya.ting.android.risk:verify-' + XM_RISK_VERIFY_REPO_BRANCH + ':' + XM_RISK_VERIFY_REPO_VERSION
    // 文字闪烁控件
    api 'com.romainpiel.shimmer:library:1.4.0@aar'

    if (isOpenXlogDecoder.toBoolean()) {
        api 'com.ximalaya.xlogdecode:xlogdecode-dev:1.0.6'
    } else {
        api 'com.ximalaya.xlogdecode:xlogdecode-noop-dev:1.0.2'
    }

//    api 'com.google.android.exoplayer:extension-okhttp:2.9.0'

    //热修复dexdiff算法
    api 'com.tencent.tinker:aosp-dexutils:1.9.14.3'
    api 'com.tencent.tinker:tinker-commons:1.9.14.3'

    api "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$KOTLIN_PLUGIN_VERSION"
    // Kotlin 协程
    api "org.jetbrains.kotlinx:kotlinx-coroutines-core:$KOTLIN_COROUTINE_VERSION"
    api "org.jetbrains.kotlinx:kotlinx-coroutines-android:$KOTLIN_COROUTINE_VERSION"

    // 下面的库引入后,会导致个人页加载不出来
    api 'androidx.core:core-ktx:1.3.2'
//    api "androidx.activity:activity-ktx:1.2.2"
//    api "androidx.fragment:fragment-ktx:1.3.3"
    api "androidx.lifecycle:lifecycle-runtime-ktx:2.2.0"
//    api "androidx.lifecycle:lifecycle-viewmodel-ktx:1.3.3"

    api 'androidx.fragment:fragment-ktx:1.2.5'


    // Wire运行时库
    api "com.squareup.wire:wire-runtime:2.3.0-RC1"
    // IM中台SDK
    api('com.ximalaya.ting.android.im:im-client-' + XMIMSDK_REPO_BRANCH + ':' + XMIMSDK_REPO_VERSION) {
//        exclude group: 'com.ximalaya.ting.android.xmnetmonitor', module: 'xmnetmonitor-developer'
//        exclude group: 'com.ximalaya.ting.android.im', module: 'im-core-developer'
    }
//    api('com.ximalaya.ting.android.im:im-core-developer:2.2.13') {
//        exclude group: 'com.ximalaya.ting.android.xmnetmonitor', module: 'xmnetmonitor-developer'
//        exclude group: 'com.google.code.gson', module: 'gson'
//    }

    // 直播中台SDK
    api 'com.ximalaya.ting.android.liveim:liveim-client-' + XMLIVESDK_IMCLIENTLIB_REPO_BRANCH + ':' + XMLIVESDK_IMCLIENTLIB_REPO_VERSION
    // 全域RTC sdk
    api 'com.ximalaya.ting.android.im:pubsub-' + XMRTCSDK_PUBSUB_REPO_BRANCH + ':' + XMRTCSDK_PUBSUB_REPO_VERSION
    // cronet二方库
    api "com.ximalaya.ting.android.liveim:liveim-cronetsdk-${XMCRONETSDK_REPO_BRANCH}:${XMCRONETSDK_REPO_VERSION}"

    // 离线资源库SDK
    api 'com.ximalaya.android.resource:api:' + XM_OFFLINE_RESOURCE_VERSION
    // 天眼SDK
    api 'com.ximalaya.ting.android.preciseye:preciseye-' + XM_PRECISEYE_BRANCH + ':' + XM_PRECISEYE_VERSION
    // 视频缓存
    api rootProject.ext.xmDependencies.videoCache

    api 'com.ximalaya.ting.android.ABTest:ABTest-developer:0.1.8'

    // 喜马广告SDK
    api 'com.ximalaya.ting.android.adsdk:adsdk-ximalaya_main_app_no_shell:1.2.15_11645'

//    api(name: 'AdSDK-release-1.2.17', ext: 'aar')


    api rootProject.ext.xmDependencies.xmgrowth

    // oaidsdk
    api 'com.ximalaya.ting.android.oaidsdk:oaidsdk-' + OAID_SDK_BRANCH + ':' + OAID_SDK_VERSION

    api 'com.ximalaya.ting.android.typeadapter:annotation-' + XM_TYPE_ADAPTER_BRANCH + ':' + XM_TYPE_ADAPTER_VERSION
    ksp rootProject.ext.xmDependencies.xmTypeAdapter
    //kapt 'com.ximalaya.ting.android.typeadapter:generator-' + XM_TYPE_ADAPTER_BRANCH + ':' + XM_TYPE_ADAPTER_VERSION

    // leakcanary dependency start
    if (rootProject.isReleaseDebug.toBoolean() && rootProject.isBuildLeakCanaryInDebug.toBoolean()) {
        api('com.squareup.leakcanary:leakcanary-android:2.9.1') {
            exclude group: 'com.squareup.okio', module: 'okio'
        }
    }
    // leakcanary system fix
    implementation('com.squareup.leakcanary:plumber-android:2.14')
    // leakcanary dependency end
    // 高斯模糊控件
    implementation 'com.github.mmin18:realtimeblurview:1.2.1'
    // koom dependency start
    implementation 'androidx.lifecycle:lifecycle-process:2.2.0'
//    implementation("com.ximalaya.ting.android.xmkoom:koom-monitor-base:" + XM_KOOM_VERSION) {
//        exclude group: 'androidx.lifecycle', module: 'lifecycle-process'
//    }
//    implementation("com.ximalaya.ting.android.xmkoom:koom-java-leak:" + XM_KOOM_VERSION) {
//        exclude group: 'com.ximalaya.ting.android.xmkoom', module: 'koom-monitor-base'
//    }
//    implementation("com.ximalaya.ting.android.xmkoom:koom-native-leak:" + XM_KOOM_VERSION) {
//        exclude group: 'com.ximalaya.ting.android.xmkoom', module: 'koom-monitor-base'
//    }
//    implementation "com.ximalaya.ting.android.xmkoom:xhook:" + XM_KOOM_VERSION
//    implementation("com.ximalaya.ting.android.xmkoom:koom-thread-leak:" + XM_KOOM_VERSION) {
//        exclude group: 'com.ximalaya.ting.android.xmkoom', module: 'koom-monitor-base'
//    }
    // koom dependency end

    // 有赞sdk  移除WebCache版本，修复空指针 特定fix版本
    api("com.youzanyun.open.mobile:basic:7.12.7") {
        // 有赞 okhttp版本 为 3.14.1 存在部分适配问题， 屏蔽掉
        exclude module: "okhttp"
    }

    //移动精准触达数据回传sdk
//    api(name: 'cmcc_rhlpt_android_2.3.1', ext: 'aar')
//    api files('libs/cmcc_rhlpt_android_2.3.1.aar')
    api "third_sdk:cmcc_rhlpt_android:2.3.1@aar"

    implementation 'com.ximalaya.ting.android:xmlint:1.0.1'

    implementation "com.airbnb.android:epoxy:$EPOXY_VERSION"
//    kapt "com.airbnb.android:epoxy-processor:$EPOXY_VERSION"

    testImplementation "junit:junit:4.13.2"
//    testImplementation "org.mockito:mockito-core:2.28.2"
//    testImplementation "org.mockito:mockito-inline:2.28.2"
//    testImplementation "org.mockito.kotlin:mockito-kotlin:$mockitoKotlinVersion"
    testImplementation 'org.mockito:mockito-inline:4.8.0'

    implementation 'com.ximalaya.xmaiagent.sdk:core:' + XM_AGENT_VERSION
    api 'com.ximalaya.xmaiagent.sdk:xmaiagent:' + XM_AGENT_VERSION
}

android {

    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName(), packageName: "com.ximalaya.ting.android"]
            }
        }
        consumerProguardFiles 'proguard-project.txt'

        manifestPlaceholders = [
                //必须要配置 PUSH_APPID, 跟商务申请
                GETUI_APP_ID: "vA6gYB1nHo6RhyTRXyAhe",
        ]
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src', 'src_host', 'src_ad', "build/generated/ksp/release"]
            resources.srcDirs = ['src', 'src_car']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }

        test {
            java.srcDirs = ['src_test/unit']
//            resources.srcDirs = ['src/test/resources']
        }

        release.setRoot('build-types/release')
    }

    lintOptions {
        // if true, stop the gradle build if errors are found
        abortOnError false
        lintConfig file("lint.xml")
    }

    compileOptions {
        sourceCompatibility rootProject.javaCompileVersion
        targetCompatibility rootProject.javaCompileVersion
    }

    //为所有的子项目设置一些通用配置
    subprojects {
        afterEvaluate {
            if (getPlugins().hasPlugin('android') ||
                    getPlugins().hasPlugin('android-library')) {
                android {
                    lintOptions {
//                        quiet true
                        abortOnError false
                        ignoreWarnings true
                        checkAllWarnings false
//                        checkReleaseBuilds false
                    }
                }
            }
        }
    }


    aaptOptions.cruncherEnabled = false

    dexOptions {
        maxProcessCount 4
        javaMaxHeapSize "2048M"
    }


    Properties localProperties = new Properties()
    localProperties.load(project.rootProject.file('local.properties').newDataInputStream())
    def devMode = localProperties.getProperty("DEV_BUNDLE_MODE", "false")
    println("devMode " + devMode)
    def buildFast = localProperties.getProperty("buildFast", "false")
    if (project.rootProject.hasProperty("DEV_BUNDLE_MODE")) {
        devMode = project.rootProject.property("DEV_BUNDLE_MODE").toString()
    }
    println("devMode " + devMode)
    def localBuildIn = localProperties.getProperty("BUILD_IN_BUNDLE", "") + "," + BUILD_IN_BUNDLE.toString()
    def localLdLib = localProperties.getProperty("BUILD_TO_LIB_BUNDLE", "") + "," + BUILD_TO_LIB_BUNDLE.toString()
    def localLdSd = localProperties.getProperty("BUILD_TO_SD_BUNDLE", "") + "," + BUILD_TO_SD_BUNDLE.toString()
    def localLdNet = localProperties.getProperty("BUILD_TO_NET_BUNDLE", "") + "," + BUILD_TO_NET_BUNDLE.toString()


    buildTypes {
        release {
            buildConfigField("String", "buildInBundle", "\"" + localBuildIn + "\"")
            buildConfigField("String", "loadFromLibBundle", "\"" + localLdLib + "\"")
            buildConfigField("String", "loadFromSdBundle", "\"" + localLdSd + "\"")
            buildConfigField("String", "loadFromNetBundle", "\"" + localLdNet + "\"")

            if (ALL_COMPILE_SRC.toBoolean()) {
                buildConfigField("String", "buildMode", "\"build_all_src\"")
            } else if (BUILD_HOST_APK.toBoolean()) {
                buildConfigField("String", "buildMode", "\"build_plugin_apk\"")
            } else {
                buildConfigField("String", "buildMode", "\"\"")
            }

            buildConfigField "boolean", "isDevMode", "${devMode.toBoolean()}"
            buildConfigField "String", "HOST_PACKAGE_NAME", "\"" + project.rootProject.ext.hostPackageName + "\""
            buildConfigField "boolean", "buildFast", "${buildFast.toBoolean()}"
            buildConfigField "String", "BUNDLE_BASE_VERSION", "\"" + hostBundleVersion + "\""
            buildConfigField "String", "HOST_APK_BUNDLES", "\"" + HOST_APK_BUNDLES + "\""
            buildConfigField "String", "OAID_SDK_VERSION", "\"" + OAID_SDK_VERSION + "\""
            if (rootProject.createPatchJar.toBoolean()) {
                def patch_version = ""
                if (project.hasProperty('PATCH_VERSION')) {
                    patch_version = project.property('PATCH_VERSION')
                }
                buildConfigField "String", "PATCH_TOKEN", "\"patch_${new Date().format("yyyy-MM-dd HH:mm", TimeZone.getTimeZone("GMT+08:00"))}_${rootProject.ext.versionName}_${patch_version}\""
            } else {
                buildConfigField "String", "PATCH_TOKEN", "\"patch_default\""
            }
        }
        debug {
            buildConfigField("String", "buildInBundle", "\"" + localBuildIn + "\"")
            buildConfigField("String", "loadFromLibBundle", "\"" + localLdLib + "\"")
            buildConfigField("String", "loadFromSdBundle", "\"" + localLdSd + "\"")
            buildConfigField("String", "loadFromNetBundle", "\"" + localLdNet + "\"")

            if (ALL_COMPILE_SRC.toBoolean()) {
                buildConfigField("String", "buildMode", "\"build_all_src\"")
            } else if (BUILD_HOST_APK.toBoolean()) {
                buildConfigField("String", "buildMode", "\"build_plugin_apk\"")
            } else {
                buildConfigField("String", "buildMode", "\"\"")
            }

            buildConfigField "boolean", "isDevMode", "${devMode.toBoolean()}"
            buildConfigField "String", "HOST_PACKAGE_NAME", "\"" + project.rootProject.ext.hostPackageName + "\""
            buildConfigField "boolean", "buildFast", "${buildFast.toBoolean()}"
            buildConfigField "String", "BUNDLE_BASE_VERSION", "\"" + hostBundleVersion + "\""
            buildConfigField "String", "HOST_APK_BUNDLES", "\"" + HOST_APK_BUNDLES + "\""
            if (rootProject.createPatchJar.toBoolean()) {
                def patch_version = ""
                if (project.hasProperty('PATCH_VERSION')) {
                    patch_version = project.property('PATCH_VERSION')
                }
                buildConfigField "String", "PATCH_TOKEN", "\"patch_${new Date().format("yyyy-MM-dd HH:mm", TimeZone.getTimeZone("GMT+08:00"))}_${rootProject.ext.versionName}_${patch_version}\""
            } else {
                buildConfigField "String", "PATCH_TOKEN", "\"patch_default\""
            }
            buildConfigField "String", "OAID_SDK_VERSION", "\"" + OAID_SDK_VERSION + "\""
        }
    }

    useLibrary 'org.apache.http.legacy'

    resourcePrefix 'host_'
}

//获取git commit 记录

String getCommitId(String dirName) {
    def gitDir = rootProject.projectDir.toString() + "/" + dirName
    def gitDirFile = new File(gitDir)

    if (!gitDirFile.exists() || !gitDirFile.isDirectory()) {
        return "not exists"
    }

    def cmd = "cd " + gitDir + " && git log | head -n 1"

    ByteArrayOutputStream stdOut = new ByteArrayOutputStream()
    exec {
        if (org.gradle.internal.os.OperatingSystem.current().isWindows()) {
            executable "cmd"
            args "/c", cmd
        } else {
            executable "sh"
            args "-c", cmd
        }
        standardOutput = stdOut
    }

    return stdOut
}

project.afterEvaluate {
    if (!rootProject.isReleaseDebug.toBoolean()) {
        File vf = file("./assets/version")
        if (!vf.exists()) {
            vf.createNewFile()
        }
        vf.write(rootProject.versionName)
    }
}
