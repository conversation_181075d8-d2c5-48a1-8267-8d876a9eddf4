<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="-12dp"
    android:clipChildren="false">

    <!-- 左边底部阴影 -->
    <ImageView
        android:id="@+id/host_iv_left_bg_shadow"
        android:layout_width="31dp"
        android:layout_height="44dp"
        android:layout_marginBottom="20dp"
        android:background="@mipmap/host_bg_3d_album_left_shadow"
        android:translationX="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/host_cl_cover_view"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/host_cl_cover_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        app:layout_constraintLeft_toRightOf="@+id/host_iv_left_bg_shadow"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/host_iv_bottom_bg_shadow"
            android:layout_width="0dp"
            android:layout_height="21dp"
            android:background="@mipmap/host_bg_3d_album_bottom_shadow"
            android:scaleType="fitXY"
            android:translationX="-8dp"
            android:translationY="-4dp"
            app:layout_constraintStart_toStartOf="@+id/album_cover_layout"
            app:layout_constraintTop_toBottomOf="@+id/album_cover_layout" />

        <!-- 主封面容器 -->
        <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
            android:id="@+id/album_cover_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="#00ff00"
            tools:layout_height="120dp"
            tools:layout_width="120dp" />

        <!-- 左边阴影 -->
        <ImageView
            android:id="@+id/host_iv_left_fg_shadow"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@mipmap/host_ic_3d_album_left_shadow"
            app:layout_constraintBottom_toBottomOf="@+id/album_cover_layout"
            app:layout_constraintStart_toStartOf="@+id/album_cover_layout"
            app:layout_constraintTop_toTopOf="@+id/album_cover_layout" />

        <!-- 上边阴影 -->
        <ImageView
            android:id="@+id/host_iv_bottom_fg_shadow"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@mipmap/host_ic_3d_album_bottom_shadow"
            app:layout_constraintBottom_toBottomOf="@+id/album_cover_layout"
            app:layout_constraintStart_toStartOf="@+id/album_cover_layout" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>