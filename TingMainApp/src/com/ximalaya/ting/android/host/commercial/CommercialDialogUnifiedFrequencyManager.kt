package com.ximalaya.ting.android.host.commercial

import android.content.Context
import android.media.AudioManager
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import androidx.annotation.RequiresApi
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk
import com.ximalaya.ting.android.framework.commoninterface.IRequestCallback
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.dialog.monitor.DialogMonitorReporter
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.iting.ITingOutsideSourceManager
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.universal.IReporter
import com.ximalaya.ting.android.host.util.CurrentPlayUtil
import com.ximalaya.ting.android.host.util.VividUtil
import com.ximalaya.ting.android.host.util.common.SceneInfoUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.audiomanager.DolbySupportDetector
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.freelisten.NewFreeListenLogManager
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.util.OsUtil
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by mark on 2024/5/15 10:45
 */
object CommercialDialogUnifiedFrequencyManager {
    const val SOURCE_PLAY = "playPage"
    const val SOURCE_HOME = "homePage"
    const val SOURCE_HOME_MOVE = "homePageAfterMove"
    const val SOURCE_ALBUM_PAGE = "albumPage"
    const val SOURCE_PLAYPAGE_AFTER_CLOSE_AD = "playPageAfterCloseAd"
    private const val IGNORE_TIME_GAP_MS = 10000L // 忽略的时间间隔：10秒

    @Volatile
    var mRequestingTrackId: Long = Long.MIN_VALUE
    var mLastRequestDialogTriple: Triple<String, Track?, Long>? = null
    var mLastShowedDialogPair: Pair<String?, Long>? = null
    private fun buildRequestParams(
        params: MutableMap<String, String>,
        source: String,
        track: Track?
    ): Long? {
        params["source"] = source
        var resultTrackId: Long? = null
        if (track != null) {
            if (track.dataId > 0) {
                params["trackId"] = track.dataId.toString()
                resultTrackId = track.dataId
            }
            if ((track.album?.albumId ?: 0) > 0) {
                params["albumId"] = track.album!!.albumId!!.toString()
            }
        } else if (source == SOURCE_HOME) {
            val currentTrackId = CurrentPlayUtil.getCurrentTrackId()
            val currentAlbumId = CurrentPlayUtil.getCurrentAlbumId()
            if (currentTrackId > 0) {
                params["trackId"] = currentTrackId.toString()
                resultTrackId = currentTrackId
            }
            if (currentAlbumId > 0) {
                params["albumId"] = currentAlbumId.toString()
            }
        }
        SceneInfoUtil.addNewFreeCrowdToParams(params)
        ITingOutsideSourceManager.addOutsideSourceParams(
            MainApplication.getMyApplicationContext(),
            params
        )
        return resultTrackId
    }

    fun requestDialogMaterial(source: String, track: Track? = null) {
        requestDialogMaterial(source, track, null, null)
    }

    fun requestDialogMaterial(
        source: String,
        track: Track? = null,
        fragment: BaseFragment2?,
        elseCallback: IHandleOk?
    ) {
        requestDialogMaterial(source, track, fragment, elseCallback, null)
    }

    fun requestDialogMaterial(
        source: String,
        requestParams: Map<String, String>?,
        track: Track? = null,
        fragment: BaseFragment2?,
        elseCallback: IHandleOk?
    ) {
        requestDialogMaterial(source, requestParams, track, fragment, elseCallback, null)
    }

    fun requestDialogMaterial(
        source: String,
        track: Track? = null,
        fragment: BaseFragment2?,
        elseCallback: IHandleOk?,
        callback: IRequestCallback<CommercialDialogData>?
    ){
        requestDialogMaterial(source, requestParams = null, track = track, fragment = fragment, elseCallback = elseCallback, callback = callback)
    }

    fun requestDialogMaterial(
        source: String,
        requestParams:Map<String,String>?,
        track: Track? = null,
        fragment: BaseFragment2?,
        elseCallback: IHandleOk?,
        callback: IRequestCallback<CommercialDialogData>?
    ) {
        if (ConfigureCenter.getInstance().getBool(
                CConstants.Group_android.GROUP_NAME,
                "CommercialDialogFrequencyRequest_Switch_Off",
                false
            )
        ) {
            // 配置中心增加配置项用于一键关闭统一频控弹窗
            logImportantMessage("CommercialDialogFrequencyRequest_Switch: switch off")
            elseCallback?.onReady()
            callback?.onError(-1, "开关未开启")
            return
        }
        logImportantMessage("requestDialogMaterial: source=$source, track=${track?.dataId}, mRequestingTrackId=$mRequestingTrackId")
        if (ignoreRepeatRequest(source, track)) {
            // 忽略重复请求
            elseCallback?.onReady()
            callback?.onError(-1, "忽略请求")
            logImportantMessage("requestDialogMaterial:ignoreRepeatRequest return true, source=$source, track=${track?.dataId}, mLastRequestDialogTriple=$mLastRequestDialogTriple")
            return
        }
        mLastRequestDialogTriple = Triple(source, track, System.currentTimeMillis())
        val params = mutableMapOf<String, String>()
        requestParams?.let {
            params.putAll(it)
        }
        mRequestingTrackId = buildRequestParams(params, source, track) ?: Long.MIN_VALUE
        HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUESTING)
        HighValueFireworkManager.updateSource(source)
        BaseApplication.getMyApplicationContext()?.let { context ->
            if (VividUtil.isSupportVivid()) {
                params["supportVivid"] = "1"
                params["supportPanoramic"] = "1"
                params["panoramicState"] =
                    if (TrackPlayQualityManager.getInstance().isFullDepth) "1" else "0"
            } else if (DolbySupportDetector.isDolbySupported(context)) {
                params["supportVivid"] = "0"
                params["supportPanoramic"] = "1"
                params["panoramicState"] =
                    if (TrackPlayQualityManager.getInstance().isFullDepth) "1" else "0"
            } else {
                params["supportVivid"] = "0"
                params["supportPanoramic"] = "0"
                params["panoramicState"] = "0"
            }
        }

        CommonRequestM.baseGetRequest(
            UrlConstants.getInstanse().commercialDialogFrequencyUrl,
            params,
            object : IDataCallBack<CommercialDialogData> {
                override fun onSuccess(data: CommercialDialogData?) {
                    callback?.onRequestFinish(data)
                    reportReceive(data?.popupId, source)

                    if (TextUtils.isEmpty(data?.popupId)) {
                        val switchOpen =
                            EasyConfigure.getBoolean("commercial_dialog_handle_0923", true)
                        if (switchOpen) {
                            HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUEST_FINISHED_NO_NEED_SHOW)
                            return
                        }
                    }

                    val trackIdFromData =
                        if (data?.trackId != null && data.trackId > 0) data.trackId else Long.MIN_VALUE
                    var canShow =
                        trackIdFromData == Long.MIN_VALUE || trackIdFromData == mRequestingTrackId
                    if (ConstantsOpenSdk.isDebug && ToolUtil.getDebugSystemProperty(
                            "debug.mark.test",
                            "0"
                        ) != "0"
                    ) {
                        canShow = true
                    }
                    logImportantMessage("requestDialogMaterial.onSuccess: data=${data?.popupId}, data.track=${data?.trackId}, track=${track?.dataId}, mRequestingTrackId=$mRequestingTrackId, canShow=$canShow")
                    if (canShow) {
                        data?.source = source
                        data?.track = track
                        if (ignoreRepeatResponse(data)) {
                            logImportantMessage("requestDialogMaterial.onSuccess: ignoreRepeatResponse return true, mLastShowedDialogPair:$mLastShowedDialogPair")
                            elseCallback?.onReady()
                            callback?.onError(-1, "已返回,忽略请求")
                            reportDialogNotShowReason("s2 忽略重复响应，${data?.popupId}")
                        } else {
                            var dataResult = data
//                            if (ConstantsOpenSdk.isDebug && ToolUtil.getDebugSystemProperty(
//                                    "debug.mark.test",
//                                    "0"
//                                ) == "1"
//                            ) {
//                                val buttons: MutableList<ActionButtonInfo> = mutableListOf()
//                                buttons.add(ActionButtonInfo("按钮", "https://www.baidu.com/", 2))
//
//                                dataResult = CommercialDialogData(
//                                    100, 100, "10502009", "",
//                                    "", "一二三四五六七吧", "一二三思", "", "", buttons, null
//                                )
//                            }

                            // 单独处理的弹窗
                            if (dataResult?.popupId == "10504001" || dataResult?.popupId == "10504002") {
                                logImportantMessage("requestDialogMaterial.onSuccess, special popup, callback:$callback")
                                HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUEST_FINISHED_NEED_SHOW)
                                callback?.onSuccess(data)
                                return
                            }
                            if (needPreloadVideoResourceOnly(dataResult?.popupId)) {
                                preloadVideoResourceOnly(dataResult) {
                                    val result =
                                        CommercialDialogProvider.showDialog(
                                            dataResult,
                                            fragment
                                        )
                                    if (result) {
                                        mLastShowedDialogPair =
                                            Pair(data?.popupId, System.currentTimeMillis())
                                    } else {
                                        logImportantMessage("preloadVideoResourceOnly.onSuccess: CommercialDialogProvider.showDialog return false")
                                        reportDialogNotShowReason("s4 after preload, CommercialDialogProvider.showDialog return false")
                                    }
                                }
                            } else if (needPreloadResource(dataResult?.popupId)) {
                                //需要先下载好资源
                                preloadResource(dataResult, fragment) {

                                    Logger.d(
                                        "z_dialog_commerical",
                                        "preloadResource finished >>> "
                                    )

                                    val result =
                                        CommercialDialogProvider.showDialog(
                                            dataResult,
                                            fragment
                                        )
                                    if (result) {
                                        mLastShowedDialogPair =
                                            Pair(data?.popupId, System.currentTimeMillis())
                                    } else {
                                        logImportantMessage("requestDialogMaterial.onSuccess: CommercialDialogProvider.showDialog return false")
                                        reportDialogNotShowReason("s3 after preload, CommercialDialogProvider.showDialog return false")
                                    }
                                }
                            } else {
                                val result =
                                    CommercialDialogProvider.showDialog(dataResult, fragment)
                                if (result) {
                                    mLastShowedDialogPair =
                                        Pair(data?.popupId, System.currentTimeMillis())
                                } else {
                                    logImportantMessage("requestDialogMaterial.onSuccess: CommercialDialogProvider.showDialog return false")
                                    reportDialogNotShowReason("s3 preload, CommercialDialogProvider.showDialog return false")
                                }
                            }
                        }
                    } else {
                        elseCallback?.onReady()
                        callback?.onError(-1, "不允许显示")
                        reportDialogNotShowReason("s1 声音ID不一致，${trackIdFromData}, $mRequestingTrackId")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    mRequestingTrackId = Long.MIN_VALUE
                    elseCallback?.onReady()
                    callback?.onError(code, message)
                    HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUEST_FINISHED_NO_NEED_SHOW)
                }

            }
        ) { content -> CommercialDialogData.parseDialogData(content) }
    }

    private fun reportReceive(popupId: String?, source: String?) {
        popupId ?: return
        source ?: return

        try {
            IReporter.DEFAULT.directReport("commercial_dialog_receive", popupId, source, null)
        } catch (e: Exception) {
        }
    }

    /**
     * 下载弹窗需要的资源
     */
    private fun preloadResource(
        dataResult: CommercialDialogData?,
        fragment: BaseFragment2?,
        function: () -> Unit
    ) {
        dataResult ?: return

        var imageUrl = dataResult.image
        if (BaseFragmentActivity2.sIsDarkMode && dataResult.darkImage != null) {
            imageUrl = dataResult.darkImage
        }
        Logger.d(
            "z_dialog_commerical",
            "preloadResource begin >>> ${dataResult.popupId}  : ${imageUrl}"
        )

        kotlin.runCatching {
            Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)?.functionAction?.preloadCommericalDialogResource(
                dataResult.popupId,
                imageUrl,
                fragment,
                function
            )
        }

    }
    private fun preloadVideoResourceOnly(dataResult: CommercialDialogData?, function: () -> Unit) {
        dataResult ?: return
        var video = dataResult.getExtraContextString("video")
        if (video.isNullOrEmpty()) {
            video = dataResult.getExtraContextString("video1")
        }
        if (video.isNullOrEmpty()) {
            return
        }
        Logger.d(
            "z_dialog_commerical",
            "preloadVideoResourceOnly begin >>> ${dataResult.popupId}  : ${video}"
        )
        kotlin.runCatching {
            Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)?.functionAction?.preloadCommercialDialogVideoResource(
                dataResult.popupId,
                video,
                function
            )
        }
    }

    /**
     * 这个弹窗是否需要先下载资源
     */
    private fun needPreloadResource(popupId: String?): Boolean {
        if (popupId == null) {
            return false
        }
        return when (popupId) {
            "10502009", "10504003", "10504005", "10506001", "10507001", "10507002","10501101" -> true
            else -> false
        }
    }
    private fun needPreloadVideoResourceOnly(popupId: String?): Boolean {
        if (popupId == null) {
            return false
        }
        return when (popupId) {
            "10501101", "10602001","10602002" -> true
            else -> false
        }
    }


    fun reportDialogShow(dialogData: CommercialDialogData) {
        dialogData.popupId ?: return
        dialogData.source ?: return
        val json = JSONObject()
        try {
            json.put("popupId", dialogData.popupId.toString())
            json.put("source", dialogData.source)
            json.put("albumId", dialogData.albumId ?: 0)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        CommonRequestM.basePostRequestJsonStr<Any>(
            UrlConstants.getInstanse().reportCommercialDialogShowUrl,
            json.toString(),
            null,
            null
        )
    }

    /**
     * 忽略重复请求
     * 规则：
     *  1. 请求的source和上次请求的source相同
     *  2. 请求的track和上次请求的track相同（均为空也判定为相同）
     *  3. 请求的时间间隔小于10秒
     *  满足以上条件，则忽略本次请求
     */
    private fun ignoreRepeatRequest(source: String, track: Track? = null): Boolean {
        mLastRequestDialogTriple ?: return false
        if (mLastRequestDialogTriple?.first == source) {
            val lastRequestTrack = mLastRequestDialogTriple?.second
            val checkTime = if ((track == null) xor (lastRequestTrack == null)) {
                false
            } else if ((track == null) and (lastRequestTrack == null)) {
                true
            } else if ((track != null) and (lastRequestTrack != null)) {
                track?.dataId == lastRequestTrack?.dataId
            } else {
                false
            }

            if (checkTime) {
                val lastRequestTime = mLastRequestDialogTriple?.third ?: 0L
                if (System.currentTimeMillis() - lastRequestTime < IGNORE_TIME_GAP_MS) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 忽略重复响应
     * 规则：
     * 1. 本次响应的popupId和上次响应的popupId相同
     * 2. 本次响应的时间距上次响应间隔小于10秒
     * 满足以上条件，则忽略本次响应
     */
    private fun ignoreRepeatResponse(dialogData: CommercialDialogData?): Boolean {
        dialogData ?: return false
        mLastShowedDialogPair ?: return false
        if (mLastShowedDialogPair?.first == dialogData.popupId) {
            val lastShowedTime = mLastShowedDialogPair?.second ?: 0L
            if (System.currentTimeMillis() - lastShowedTime < IGNORE_TIME_GAP_MS) {
                return true
            }
        }
        return false
    }

    fun logImportantMessage(message: String) {
        NewFreeListenLogManager.writeLog("CommercialDialogUnifiedFrequencyManager_$message")
    }

    private fun reportDialogNotShowReason(reason: String) {
        DialogMonitorReporter.reportDialogNotShowReason(reason)
        HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUEST_FINISHED_NO_NEED_SHOW)
    }
}