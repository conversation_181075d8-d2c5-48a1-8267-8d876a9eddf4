package com.ximalaya.ting.android.host.commercial

import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.opensdk.model.track.Track
import org.json.JSONArray
import org.json.JSONObject

/**
 * Created by mark on 2024/5/15 11:53
 */
data class CommercialDialogData(
    val albumId: Long = -1,
    val trackId: Long = -1,
    val popupId: String? = null,
    val sourceRet: String? = null,
    val policyGroup: String? = null,
    val mainText: String? = null,
    val subText: String? = null,
    val image: String? = null,
    val darkImage: String? = null,
    private val imageCollection:String? = null,
    val buttons: List<ActionButtonInfo>? = null,
    val context: JSONObject? = null,
    val ext: Ext? = null,
    val rawJSONString: String? = null
) {
    companion object {
        const val IMAGE_COLLECTION_SKU = "skuImage"
        const val IMAGE_COLLECTION_BACKGROUND = "backImage"
        const val IMAGE_COLLECTION_CARD_BACKGROUND= "cardBackImage"
        const val IMAGE_COLLECTION_PRIVILEGE = "privilegeImage"
        const val IMAGE_COLLECTION_LABEL = "labelImage"
        const val IMAGE_COLLECTION_SOUND = "soundImage"
        const val IMAGE_COLLECTION_SOUND_DYNAMIC = "soundDynamicImage"
        const val IMAGE_COLLECTION_BUTTON = "buttonImage"
        fun parseDialogData(content: String?): CommercialDialogData? {
            content ?: return null
            try {
                val json = JSONObject(content)
                val ret = json.optInt("ret", -1)
                if (ret != 0) {
                    return null
                }
                val dataJson = json.optJSONObject("data")
                dataJson ?: return null
                val buttons = mutableListOf<ActionButtonInfo>()
                if (dataJson.has("buttons")) {
                    val buttonsJsonArray = dataJson.optJSONArray("buttons")
                    buttonsJsonArray?.let {
                        for (i in 0 until it.length()) {
                            val buttonJson = it.optJSONObject(i)
                            if (buttonJson != null) {
                                buttons.add(
                                    ActionButtonInfo(
                                        buttonText = buttonJson.optString("buttonText", ""),
                                        buttonUrl = buttonJson.optString("buttonUrl", ""),
                                        actionType = buttonJson.optInt("actionType", -1)
                                    )
                                )
                            }
                        }
                    }
                }
                var imageCollection:String? = null
                if (dataJson.has("imageCollection")) {
                    imageCollection = dataJson.optString("imageCollection")
                }

                var ext: Ext? = null
                if (dataJson.has("ext")) {
                    val extJson = dataJson.optJSONObject("ext")
                    extJson?.let {
                        ext = Ext(
                            secondPopupId = extJson.optString("secondPopupId", ""),
                            albumCoverPath = extJson.optString("albumCoverPath", ""),
                            trackTitle = extJson.optString("trackTitle", ""),
                            buttonText = extJson.optString("buttonText", ""),
                            buttonUrl = extJson.optString("buttonUrl", ""),
                            image = extJson.optString("image", ""),
                            darkImage = extJson.optString("darkImage", ""),
                            totalDuration = extJson.optLong("totalDuration", ),
                            restDuration = extJson.optLong("restDuration", ),
                        )
                    }
                }
                return CommercialDialogData(
                    albumId = dataJson.optLong("albumId", -1),
                    trackId = dataJson.optLong("trackId", -1),
                    sourceRet = dataJson.optString("source", ""),
                    policyGroup = dataJson.optString("policyGroup", ""),
                    popupId = dataJson.optString("popupId", ""),
                    mainText = dataJson.optString("mainText", ""),
                    subText = dataJson.optString("subText", ""),
                    image = dataJson.optString("image", ""),
                    darkImage = dataJson.optString("darkImage", ""),
                    buttons = buttons,
                    imageCollection = imageCollection,
                    context = dataJson.optJSONObject("ext"),
                    ext = ext,
                    rawJSONString = content
                )
            } catch (e: Exception) {
                return null
            }
        }
    }

    var source: String? = null
        get() {
            return if (field != null)
                field
            else
                sourceRet
        }

    var track: Track? = null

    private var imageCollectionMap:MutableMap<String,ImageCollectionItem>? =null

    private fun initImageCollectionMap() {
        if (imageCollectionMap == null) {
            imageCollectionMap = mutableMapOf()
            if (!imageCollection.isNullOrEmpty()) {
                try {
                    val imageCollectionJson = JSONObject(imageCollection)
                    if (imageCollectionJson.length() > 0) {
                        imageCollectionJson.keys().forEach {
                            val value =
                                ImageCollectionItem.parse(imageCollectionJson.optJSONObject(it))
                            if (value != null) {
                                imageCollectionMap!![it] = value
                            }
                        }
                    }
                } catch (e: Exception) {

                }
            }
        }
    }

    fun getImageCollectionItem(key: String): ImageCollectionItem? {
        initImageCollectionMap()
        return imageCollectionMap?.get(key)
    }

    fun hasExtraContext(key: String): Boolean {
        this.context ?: return false
        return this.context.has(key)
    }

    fun getExtraContextString(key: String): String? {
        this.context ?: return ""
        return this.context.optString(key, "")
    }

    fun getExtraContextInt(key: String): Int? {
        this.context ?: return null
        var result:Int? = -1
        if (this.context.has(key)) {
            val temp = this.context.opt(key)
            if (temp is Int) {
                result = temp
            } else if (temp is String) {
                try {
                    result = Integer.parseInt(temp)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        return result
    }

    fun getExtraContextLong(key: String): Long? {
        this.context ?: return null
        var result:Long? = -1
        if (this.context.has(key)) {
            val temp = this.context.opt(key)
            if (temp is Long) {
                result = temp
            } else if (temp is String) {
                try {
                    result = java.lang.Long.parseLong(temp)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        return result
    }

    fun getExtraContextJSON(key: String): JSONObject? {
        this.context ?: return null
        var result: JSONObject? = null
        if (this.context.has(key)) {
            val temp = this.context.opt(key)
            if (temp is JSONObject) {
                result = temp
            } else if (temp is String) {
                try {
                    result = JSONObject(temp)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        return result
    }

    fun getExtraContextJSONArray(key: String): JSONArray? {
        this.context ?: return null
        var result: JSONArray? = null
        if (this.context.has(key)) {
            val temp = this.context.opt(key)
            if (temp is JSONArray) {
                result = temp
            } else if (temp is String) {
                try {
                    result = JSONArray(temp)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        return result
    }

    fun getExtraContextMap(key: String): Map<String, String>? {
        this.context ?: return null
        val json = getExtraContextJSON(key) ?: return null
        val map = mutableMapOf<String, String>()
        for (entry in json.keys()) {
            map[entry] = json.optString(entry)
        }
        return map
    }

//    fun getMapFromJSONString(jsonString: String?): Map<String, String> {
//        Logger.i("getMapFromJSON", "json:$jsonString")
//        jsonString ?: return emptyMap()
//        val map = mutableMapOf<String, String>()
//        kotlin.runCatching {
//            val json = JSONObject(jsonString)
//            for (entry in json.keys()) {
//                if (entry.isNullOrEmpty()) continue
//                map[entry] = json.optString(entry)
//            }
//        }.onFailure {
//            Logger.e("getMapFromJSON", "onFailure.json:$jsonString")
//        }
//        return map
//    }
}
data class ImageCollectionItem(
    val image: String? = null,
    val darkImage: String? = null,
    val height: String? = null,
    val width: String? = null,
    val coverValue: String? = null
) {
    companion object {
        fun parse(json: JSONObject?): ImageCollectionItem? {
            json ?: return null
            val item = ImageCollectionItem(
                image = json.optString("image"),
                darkImage = json.optString("darkImage"),
                height = json.optString("height"),
                width = json.optString("width"),
                coverValue = json.optString("coverValue")
            )
            if (item.isValid()) {
                return item
            } else {
                return null
            }
        }
    }

    fun getValidImage(): String? {
        var validImage = if (BaseFragmentActivity2.sIsDarkMode) {
            darkImage
        } else {
            image
        }
        if (validImage.isNullOrEmpty()) {
            if (!image.isNullOrEmpty()) {
                return image
            }
            if (!darkImage.isNullOrEmpty()) {
                return darkImage
            }
        }
        return validImage
    }

    fun isValid(): Boolean {
        return !getValidImage().isNullOrEmpty()
    }
}


data class ActionButtonInfo(
    val buttonText: String? = null,
    val buttonUrl: String? = null,
    val actionType: Int = -1
)

data class Ext (
    val secondPopupId: String? = null,
    val albumCoverPath: String? = null,
    val trackTitle: String? = null,
    val buttonText: String? = null,
    val buttonUrl: String? = null,
    val image: String? = null,
    val darkImage: String? = null,
    val totalDuration: Long? = null,
    val restDuration: Long? = null,

)


