package com.ximalaya.ting.android.host.commercial

import android.content.Context
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.dialog.monitor.DialogMonitorReporter
import com.ximalaya.ting.android.host.manager.firework.DialogDismissConfigManager
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.host.universal.IReporter
import org.json.JSONArray
import org.json.JSONObject
import java.lang.ref.WeakReference

/**
 * Created by mark on 2024/5/15 10:46
 */
class CommercialDialogProvider {
    companion object {
        private var CURRENT_DIALOG_REF: WeakReference<DialogFragment>? = null
        private fun getDefaultCommercialFrequencyDialogViewClassName(popupId: String): String? {
            if (popupId.isEmpty()) return null
            return "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_$popupId"
        }
        private const val DEFAULT_CONFIG_STR = "{\"playPage\":[],\"homePage\":[\"HomePageFragment\"],\"homePageAfterMove\":[\"HomePageFragment\"],\"albumPage\":[\"UniversalCustomAlbumFragment\",\"AlbumFragmentNew2\",\"AlbumFragmentNew3\"]}"
        private val SOURCE_MATCH_CONFIG = mutableMapOf<String,List<String>>()
        val mutableDialogMap = mutableMapOf<String, String>(
            "10503003" to "com.ximalaya.ting.android.main.dialog.commercial.VipSoundEffectAndQualityNotifyDialog",//礼包弹窗
            "10503004" to "com.ximalaya.ting.android.main.dialog.commercial.VipSoundEffectAndQualityNotifyDialog",//会员提醒弹窗
            "10503005" to "com.ximalaya.ting.android.main.dialog.commercial.VipSoundEffectAndQualityNotifyDialog",//转化弹窗
            "10504003" to "com.ximalaya.ting.android.main.dialog.commercial.FreeListenRewardDialog",// 畅听活跃用户-畅听礼遇弹窗
            "10504005" to "com.ximalaya.ting.android.main.dialog.commercial.FreeListenRedpackDialog",// 畅听活跃用户-权益升级弹窗
            "10504007" to "com.ximalaya.ting.android.main.dialog.commercial.FreeListenProtectionPeriodDialog",// 畅听新用户/召回用户保护期弹窗
            "10504008" to "com.ximalaya.ting.android.main.dialog.commercial.FreeListenProtectionPeriodDialog",// 畅听新用户/召回用户保护期弹窗
            "10505001" to "com.ximalaya.ting.android.main.dialog.commercial.FreeTransferToPaidDialog",// 免付一体化引导弹窗(专辑剩余20%)
            "10505002" to "com.ximalaya.ting.android.main.dialog.commercial.FreeTransferToPaidDialog",// 免付一体化引导弹窗（专辑剩余最后一条）
            "10506001" to "com.ximalaya.ting.android.main.dialog.commercial.SleepAidNotifyDialog",// 助眠权益体验通知弹窗
            "10502011" to "com.ximalaya.ting.android.main.dialog.commercial.VipPayCheckPointDialog", //vip付费卡点弹窗
            "10502012" to "com.ximalaya.ting.android.main.dialog.commercial.VipPayCheckPointDialog", //vip付费卡点弹窗
            "10507001" to "com.ximalaya.ting.android.main.dialog.commercial.SVipPayCheckPointDialog", //svip付费卡点弹窗
            "10507002" to "com.ximalaya.ting.android.main.dialog.commercial.SVipPayCheckPointDialog", //svip付费卡点弹窗
            "10509002" to "com.ximalaya.ting.android.main.dialog.commercial.PlatinumVipMotDialog",
            "10501101" to "com.ximalaya.ting.android.main.dialog.commercial.VipUserRetentionDialog",   //付费挽留弹窗
            "10506002" to "com.ximalaya.ting.android.main.dialog.commercial.VipDolbySoundQualityNotifyDialog",      // 杜比音质权益弹窗
            "10602001" to "com.ximalaya.ting.android.main.dialog.commercial.RedPack818ActivityDialog",  // 预热期818红包入口弹窗
            "10602002" to "com.ximalaya.ting.android.main.dialog.commercial.RedPack818ActivityDialog",   // 正式期818红包入口弹窗
            "10603001" to "com.ximalaya.ting.android.main.dialog.commercial.PlatinumVipAdFreePrivilegeDialog" // 白金会员会员免广转化弹窗
        )

        val mutableDialogViewMap = mutableMapOf<String, String>(
            "10502006" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10502001",
            "10502005" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10502002",
            "10502008" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10502003",
            "10502009" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10502009",
            "10503002" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10503001",
            "10503006" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10503001",
            "10503007" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10503001",
            "10503008" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10503001",
            "10503009" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10503001",
            "10502010" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10502003",
            "10508002" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10508001",
            "10501206" to "com.ximalaya.ting.android.main.dialog.commercial.CommercialFrequencyControlDialog_10501207"
        )

        @JvmStatic
        fun showDialog(data: CommercialDialogData?, baseFragment: BaseFragment2?): Boolean {
            if (data == null || data.popupId.isNullOrEmpty()) {
                try {
                    reportDialogNotShowReason("showDialog return s1, popupId ${data?.popupId}")
                    reportDialogNotShowReason("showDialog return s1.1,rawJSONString: ${data?.rawJSONString}")
                } catch (e: Throwable) {
                    if (ConstantsOpenSdk.isDebug) {
                        throw e
                    }
                }
                return false
            }
            CURRENT_DIALOG_REF?.get()?.dismissAllowingStateLoss()
            var fragmentManager: FragmentManager? = null
            var context: Context? = null
            var activity: FragmentActivity? = null
            if (baseFragment?.canUpdateUi() == true) {
                fragmentManager = baseFragment.childFragmentManager
                context = baseFragment.context
                activity = baseFragment.requireActivity()
            } else if (MainApplication.getMainActivity() is MainActivity) {
                fragmentManager =
                    (MainApplication.getMainActivity() as MainActivity).supportFragmentManager
                context = (MainApplication.getMainActivity() as MainActivity).context
                activity = MainApplication.getMainActivity() as MainActivity
            }
            if (fragmentManager != null && context != null) {
                CommercialDialogUnifiedFrequencyManager.logImportantMessage("showDialog:try to find target dialog or dialogView, popupId:${data?.popupId}")
                var dialog: ICommercialDialog? = null
                var tag = ""
                val type = data.popupId
                var innerShowFlag = false
                mutableDialogMap[type]?.let { className ->
                    tag = className
                    dialog = getDialog(className)
                    if (dialog == null) {
                        reportDialogNotShowReason("showDialog s4, getDialog return null, id: ${data.popupId}, className: $className")
                        return@let
                    }
                    dialog!!.setData(data)
                    innerShowFlag = dialog!!.canShow()
                }
                CommercialDialogUnifiedFrequencyManager.logImportantMessage("showDialog:find target dialog from dialogMap:$dialog")
                dialog ?: (mutableDialogViewMap[type]
                    ?: getDefaultCommercialFrequencyDialogViewClassName(data.popupId))?.let { className ->
                    tag = className
                    val view: ICommercialDialogView? = getDialogView(context, className)
                    if (view == null) {
                        reportDialogNotShowReason("showDialog s4, getDialogView return null, id: ${data.popupId}, className: $className")
                        return@let
                    }
                    dialog = getDefaultDialog()
                    dialog?.setData(data)
                    view.setData(data)
                    dialog?.bindDialogView(view.view)
                    view.bindDialogFunc(dialog as ICommercialDialogFunc)
                    innerShowFlag = view.canShow()
                    if (innerShowFlag) {
                        (dialog as? DialogFragment)?.lifecycle?.addObserver(object :
                            LifecycleEventObserver {
                            override fun onStateChanged(
                                source: LifecycleOwner,
                                event: Lifecycle.Event
                            ) {
                                when (event) {
                                    Lifecycle.Event.ON_DESTROY -> {
                                        view.onDestroyView()
                                        source.lifecycle.removeObserver(this)
                                    }
                                    Lifecycle.Event.ON_RESUME -> {
                                        view.onResume()
                                    }
                                    Lifecycle.Event.ON_PAUSE -> {
                                        view.onPause()
                                    }
                                    else -> {}
                                }
                            }
                        })
                    } else {
                        reportDialogNotShowReason("s4 innerShowFlag false")
                    }
                }
                val checkCanShow = checkCanShow(data.source, activity)
                CommercialDialogUnifiedFrequencyManager.logImportantMessage("showDialog:find target dialog from dialogViewMap:$dialog")
                CommercialDialogUnifiedFrequencyManager.logImportantMessage(
                    "showDialog:canShow: innerShowFlag=$innerShowFlag ,checkCanShow()=$checkCanShow"
                )
                if (innerShowFlag && checkCanShow) {
                    CommercialDialogUnifiedFrequencyManager.reportDialogShow(data)
                    HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUEST_FINISHED_NEED_SHOW)
                    setCurrentDialogRef(dialog as? DialogFragment)
                    dialog?.showDialog(fragmentManager, tag)
                    if (data.source == CommercialDialogUnifiedFrequencyManager.SOURCE_HOME && !ignoreAutoDismiss(
                            data
                        )
                    ) {
                        DialogDismissConfigManager.autoDismissAfterSeconds(
                            tag,
                            dialog,
                            getDismissDelaySecond()
                        )
                    }
                    return true
                } else {
                    reportDialogNotShowReason("showDialog return s2, innerShowFlag: ${innerShowFlag} and checkCanShow:${checkCanShow}, ${type}")
                }
            } else {
                reportDialogNotShowReason("s4 fragmentManagerOrContext null $fragmentManager  or $context")
            }
            return false
        }

        private fun ignoreAutoDismiss(dialogData: CommercialDialogData): Boolean {
            if (dialogData.popupId == "10501101" || dialogData.popupId == "10602001" || dialogData.popupId == "10602002") {
                // 首页该弹窗未登录用户不执行通用自动关闭逻辑，由弹窗内部自处理
                return true
            }
            return false
        }

        private fun getDismissDelaySecond(): Long {
            if (ConstantsOpenSdk.isDebug) {
                val prop = ToolUtil.getSystemProperty("debug.shixin.dialog_dismiss_delay", "-1")
                if (prop != "-1") {
                    return prop.toLong()
                }
            }
            return 10
        }

        private fun getDefaultDialog(): CommercialContainerDialogFragment {
            return CommercialContainerDialogFragment.newInstance()
        }

        private inline fun <reified T : ICommercialDialog> getDialog(className: String): T? {
            try {
                val clz = Class.forName(className)
                val constructorObj = clz.getConstructor()
                constructorObj.isAccessible = true
                val t = constructorObj.newInstance()
                if (t is T) {
                    return t
                }
                CommercialDialogUnifiedFrequencyManager.logImportantMessage(
                    "getDialog error,need ICommercialDialog, but $t"
                )
                DialogMonitorReporter.reportDialogNotShowReason("getDialog error,need ICommercialDialog, but $t")
                return null
            } catch (e: Exception) {
                CommercialDialogUnifiedFrequencyManager.logImportantMessage(
                    "exception occurs when getDialog by reflection: $e"
                )
                e.printStackTrace()
                DialogMonitorReporter.reportDialogNotShowReason("exception occurs when getDialog by reflection: $e")
            }
            return null
        }

        private inline fun <reified T : ICommercialDialogView> getDialogView(
            context: Context,
            className: String
        ): T? {
            try {
                val clz = Class.forName(className)
                val constructorObj = clz.getConstructor(Context::class.java)
                constructorObj.isAccessible = true
                val t = constructorObj.newInstance(context)
                if (t is T) {
                    return t
                }
                CommercialDialogUnifiedFrequencyManager.logImportantMessage(
                    "getDialogView error,need ICommercialDialogView, but $t"
                )
                DialogMonitorReporter.reportDialogNotShowReason("getDialogView error,need ICommercialDialog, but $t")
                return null
            } catch (e: Exception) {
                e.printStackTrace()
                CommercialDialogUnifiedFrequencyManager.logImportantMessage(
                    "exception occurs when getDialogView by reflection: $e"
                )
                DialogMonitorReporter.reportDialogNotShowReason("exception occurs when getDialogView by reflection: $e")
            }
            return null
        }

        /**
         * 统一判断当前是否可展示商业化弹窗
         */
        private inline fun checkCanShow(source: String?,activity: FragmentActivity?): Boolean {
            if (activity == null) {
                reportDialogNotShowReason("s5 activity is null")
                return false
            }
            if (activity.isFinishing) {
                reportDialogNotShowReason("s5 activity isFinishing")
                return false
            }
            if (!checkSourceMatch(source, activity)) {
                reportDialogNotShowReason("s5 source not matched fragment")
                return false
            }
            if (ViewUtil.haveDialogIsShowing(activity)) {
                if (HighValueFireworkManager.enableShowDialogWhenHaveShowing()) {
                    IReporter.DEFAULT.directReport(
                        "CommercialShowWhenHaveShow",
                        "show",
                        HighValueFireworkManager.getSource(),
                        null
                    )
                    return false
                }
                IReporter.DEFAULT.directReport(
                    "CommercialShowWhenHaveShow",
                    "notShow",
                    HighValueFireworkManager.getSource(),
                    null
                )
                reportDialogNotShowReason("s5 haveDialogIsShowing")
                return false
            }
            return true
        }
        private fun initSourceMatchConfig() {
            if (SOURCE_MATCH_CONFIG.isEmpty()) {
                var sourceConfigStr = ConfigureCenter.getInstance().getString(
                    CConstants.Group_android.GROUP_NAME,
                    "commercial_dialog_source_match_config", null
                )
                if (sourceConfigStr.isNullOrEmpty()) {
                    sourceConfigStr = DEFAULT_CONFIG_STR
                }
                try {
                    val jsonObject = JSONObject(sourceConfigStr)
                    val keys = jsonObject.keys()
                    keys.forEach {
                        val value = jsonObject.opt(it)
                        if (value is JSONArray) {
                            val list = mutableListOf<String>()
                            for (i in 0 until value.length()) {
                                val item = value.optString(i)
                                if (!item.isNullOrEmpty()) {
                                    list.add(item)
                                }
                            }
                            if (list.isNotEmpty()) {
                                SOURCE_MATCH_CONFIG[it] = list
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        private fun checkSourceMatch(source: String?, activity: FragmentActivity): Boolean {
            initSourceMatchConfig()
            source?:return true
            val sourceMatchList = SOURCE_MATCH_CONFIG[source]
            if (sourceMatchList?.isNotEmpty() == true) {
                val topFragmentName = getTopFragmentName(activity)
                if (topFragmentName.isNotEmpty() && sourceMatchList.contains(topFragmentName)) {
                    return true
                }
                return false
            }
            // 当前source不在 SOURCE_MATCH_CONFIG 中，直接返回true
            return true
        }

        private fun getTopFragmentName(activity: FragmentActivity):String  {
            if (activity !is MainActivity) {
                return ""
            }
            val mainActivity = activity as MainActivity
            val current = mainActivity.currentFragmentInManage
            var topFraName = ""
            if (current != null) {
                topFraName = current.javaClass.simpleName
            } else if (mainActivity.isPlayFragmentVisible()) {
                topFraName = "PlayFragmentNew";
            } else if (mainActivity.getTabFragmentManager() != null && mainActivity.getTabFragmentManager().getCurrFragment() != null) {
                topFraName = mainActivity.getTabFragmentManager().getCurrFragment().javaClass.simpleName
            }
            return topFraName;
        }


        private fun reportDialogNotShowReason(reason: String) {
            DialogMonitorReporter.reportDialogNotShowReason(reason)
            HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_REQUEST_FINISHED_NO_NEED_SHOW)
        }

        private fun setCurrentDialogRef(dialogFragment: DialogFragment?) {
            CURRENT_DIALOG_REF?.get()?.dismissAllowingStateLoss()
            CURRENT_DIALOG_REF = null
            dialogFragment ?: return
            CURRENT_DIALOG_REF = WeakReference(dialogFragment)
        }
    }
}