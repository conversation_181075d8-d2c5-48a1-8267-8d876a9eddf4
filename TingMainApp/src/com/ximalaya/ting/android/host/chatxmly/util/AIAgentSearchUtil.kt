package com.ximalaya.ting.android.host.chatxmly.util

import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/7/14
 */
object AIAgentSearchUtil {
    // /hub/front/v1的初次搜索结果
    var rnSearchResult = ""
    private var enableAiIcon: Boolean? = null
    private val enableAiIconEntrance: Boolean by lazy {
        ConfigureCenter.getInstance()
            .getBool(CConstants.Group_toc.GROUP_NAME, "key_search_ai_icon_enable", true)
    }

    fun isAiIconEnable(hasAiTab: Boolean): Boolean {
        val enable = ToolUtil.getDebugSystemProperty("debug.lmm.search_icon", "")
        if (enable == "0") {
            return false
        }
        if (enable == "1") {
            return true
        }
        if (!enableAiIconEntrance) {
            return false
        }
        if (enableAiIcon == null) {
            enableAiIcon = ABTest.getString("search_result_group", "0") == "1"
        }
        return !hasAiTab && enableAiIcon == true
    }

    fun eTraceIconShow(key: String, requestId: String) {
        // 搜索结果页-ai问入口  控件曝光
        XMTraceApi.Trace()
            .setMetaId(69314)
            .setServiceId("slipPage")
            .put("currPage", "searchResult")
            .put(XmRequestIdManager.XM_REQUEST_ID, requestId)
            .put(XmRequestIdManager.XM_CONT_ID, key)
            .put(XmRequestIdManager.XM_CONT_TYPE, "searchResult")
            .put("searchWord", key)
            .createTrace()
    }
}