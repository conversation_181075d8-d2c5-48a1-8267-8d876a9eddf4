package com.ximalaya.ting.android.host.manager.doc.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.text.*
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import com.ximalaya.ting.android.framework.activity.BaseActivity
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.fragment.BaseActivityLikeFragment
import com.ximalaya.ting.android.host.manager.doc.NatureParagraph
import com.ximalaya.ting.android.host.manager.doc.SubSentence
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.xmutil.Logger

@SuppressLint("AppCompatCustomView")
open class NatureParagraphDocView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {
    private val TAG = "DocNatureParagraphView"

    interface FocusRectListener {
        fun onFocusRectChanged(rect: RectF)
    }

    interface SelectedRectListener {
        fun onFocusRectChanged(rect: RectF, sentence: SubSentence)
    }

    var onFocusRectChangedListener: FocusRectListener? = null
    var onSelectedRectChangedListener: SelectedRectListener? = null

    private val TIMER_FREQURENCY: Long = 100L

    private var paragraphs = listOf<NatureParagraph>()

    private var mCurrentLine = 0
    private var mCurrentTime: Long = 0
    private var lastSentence: SubSentence? = null

    private var lastSelectedSentence: SubSentence? = null

    private var mTempo = 1f

    private val mIsDragging = false
    private var mLastTime: Long = 0

    private val highLightRectList = mutableListOf<RectF>()
    private val selectedRectList = mutableListOf<RectF>()

    private var wholeSpan: SpannableString? = null
    private val foregroundColorSpan = ForegroundColorSpan(0xFF5393FF.toInt())

    private var highlightPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = 0x17226ded.toInt()
        style = Paint.Style.FILL
    }

    private var selectedPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = if (BaseFragmentActivity.sIsDarkMode) 0x195393FF.toInt() else 0x19226ded.toInt()
        style = Paint.Style.FILL
    }

    private val paragraphGap = 28.dp
    private val highLightOffset = 4.dp

    fun setTimeFrequency(duration: Long) {
        resetXOffset()
    }

    fun setEntryList(entryList: List<NatureParagraph>) {
        paragraphs = entryList.toList()
        val text = entryList.joinToString(separator = ""){ it.text }

        val spannable = SpannableString(text)
        wholeSpan = spannable
        spannable.setSpan(
            ParagraphSpacingSpan(paragraphGap),
            0, text.length,
            Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )
        setText(spannable, BufferType.SPANNABLE)
    }

    private val mDrawLightLineTask = object : Runnable {
        override fun run() {
            if (paragraphs.isEmpty() || mIsDragging) {
                removeCallbacks(this)
                return
            }
            updateExactTime(mCurrentTime)
            postDelayed(this, TIMER_FREQURENCY)
            mCurrentTime = (mCurrentTime + (TIMER_FREQURENCY * mTempo)).toLong()
        }
    }

    private fun cancelUpdateTimeTask() {
        removeCallbacks(mDrawLightLineTask)
    }

    fun updateTime(time: Long, isDrag: Boolean) {
        if (paragraphs.isEmpty()) {
            return
        }
        mCurrentTime = time
        mLastTime = mCurrentTime
        if (mIsDragging) {
            removeCallbacks(mDrawLightLineTask)
            return
        }
        if (isDrag) {
            updateExactTime(mCurrentTime)
        } else {
            removeCallbacks(mDrawLightLineTask)
            postDelayed(mDrawLightLineTask, TIMER_FREQURENCY)
        }
    }

    fun updateLineWhenSeek(pos: Long) {
        mLastTime = pos
        updateExactTime(pos)
    }

    private fun updateExactTime(time: Long) {
        post {
            val paragraphIndex = findParagraphIndex(time)
            val sentence = findCurrentSentence(paragraphIndex, time)
            if (sentence != null && sentence.position != lastSentence?.position) {
                lastSentence = sentence

                highLightRectList.clear()
                val result = getFocusedRect(sentence)
                if (result.isNotEmpty()) {
                    highLightRectList.addAll(result)
                }
                (text as? Spannable)?.setSpan(
                    foregroundColorSpan,
                    sentence.position, sentence.word.length + sentence.position,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
                val rectF = result.firstOrNull()
                if (rectF != null) {
                    onFocusRectChangedListener?.onFocusRectChanged(rectF)
                }

                invalidate()
            }
        }
    }

    fun clearSelectedRect() {
        lastSelectedSentence = null
        selectedRectList.clear()
        invalidate()
    }

    fun updateSelectedRect(position: Int) {
        post {
            val paragraphIndex = findParagraphIndexForPosition(position)
            val sentence = findCurrentSentenceForPosition(paragraphIndex, position)
            if (sentence != null && sentence.position != lastSentence?.position) {
                lastSelectedSentence = sentence
                selectedRectList.clear()
                val result = getFocusedRect(sentence)
                if (result.isNotEmpty()) {
                    selectedRectList.addAll(result)
                }
                val rectF = result.firstOrNull()
                if (rectF != null) {
                    onSelectedRectChangedListener?.onFocusRectChanged(rectF, sentence)
                }
                invalidate()
            }
        }
    }

    fun Layout.getGlyphRect(line: Int, tv: TextView, out: RectF) {
        val left   = getLineLeft(line)
        val right  = getLineRight(line)
        val top    = getLineTop(line).toFloat()
        val base   = getLineBaseline(line).toFloat()

        val descent = getLineDescent(line).toFloat()
        val spacing = (tv.lineSpacingMultiplier - 1f) * tv.lineHeight + tv.lineSpacingExtra
        val bottom  = base + descent - spacing      // ← 只保留真正 descent

        // 加 padding / 扣 scroll
        out.set(
            left   + tv.totalPaddingLeft  - tv.scrollX,
            top    + tv.totalPaddingTop   - tv.scrollY,
            right  + tv.totalPaddingLeft  - tv.scrollX,
            bottom + tv.totalPaddingTop   - tv.scrollY
        )
    }

    private fun getFocusedRect(sentence: SubSentence): List<RectF> {
        val ly = layout ?: return emptyList()
        val start = sentence.position

        val startLine = ly.getLineForOffset(start)
        val endLine = ly.getLineForOffset(start + sentence.word.length - 1)

        val results = mutableListOf<RectF>()
        for (i in startLine..endLine) {
            val lineTop = ly.getLineTop(i).toFloat()
            val base   = ly.getLineBaseline(i).toFloat()
            val descent = ly.getLineDescent(i).toFloat()
            val spacing = (lineSpacingMultiplier - 1f) * lineHeight + lineSpacingExtra
            var lineBottom  = base + descent - spacing      // ← 只保留真正 descent

            val lastWordIndex = ly.getLineEnd(i) -1
            if (lastWordIndex >=0 && ly.text.getOrNull(lastWordIndex) == '\n') {
                lineBottom -= paragraphGap
            }

            val xOffset = if (i == startLine) {
                ly.getPrimaryHorizontal(start)
            } else {
                0f
            }

            val right = if (i != endLine) {
                ly.getLineWidth(i)
            } else {
                ly.getPrimaryHorizontal(start + sentence.word.length - 1)
            }
            results.add(RectF(xOffset, lineTop - highLightOffset, right, lineBottom + highLightOffset))
        }
        return results
    }

    private fun resetXOffset() {
        invalidate()
    }

    fun reset() {
        mCurrentLine = 0
        mLastTime = 0
        cancelUpdateTimeTask()

        mCurrentLine = 0
    }

    private fun findParagraphIndex(time: Long): Int {
        if (paragraphs.isEmpty()) {
            return 0
        }

        val targetIndex = paragraphs.binarySearch {
            if (it.start <= time && it.end >= time) {
                0
            } else if (it.start > time) {
                1
            } else {
                -1
            }
        }
        if (targetIndex >= 0) {
            return targetIndex
        }
        return 0
    }

    private fun findParagraphIndexForPosition(position: Int): Int {
        if (paragraphs.isEmpty()) {
            return 0
        }

        val targetIndex = paragraphs.binarySearch {
            val sentences = it.subSentences

            val start = sentences.firstOrNull()?.position ?: 0
            val last = sentences.lastOrNull()?.let { it.position + it.word.length } ?: 0

            if (start <= position && last >= position) {
                0
            } else if (start > position) {
                1
            } else {
                -1
            }
        }
        if (targetIndex >= 0) {
            return targetIndex
        }
        return 0
    }

    private fun findCurrentSentenceForPosition(paragraphIndex: Int, position: Int): SubSentence? {
        if (paragraphIndex >=0 && paragraphs.isNotEmpty() && paragraphs.size > paragraphIndex) {
            val target = paragraphs[paragraphIndex]
            val sentence = target.subSentences.find {
                it.position <= position && it.position + it.word.length >= position
            }
            if (sentence != null && sentence.word.isNotBlank()) {
                return sentence
            }
        }

        return null
    }

    private fun findCurrentSentence(paragraphIndex: Int, time: Long): SubSentence? {
        if (paragraphIndex >=0 && paragraphs.isNotEmpty() && paragraphs.size > paragraphIndex) {
            val target = paragraphs[paragraphIndex]
            val sentence = target.subSentences.find {
                it.start <= time && it.end >= time
            }
            if (sentence != null && sentence.word.isNotBlank()) {
                return sentence
            }
        }

        return null
    }

    private fun doXOffsetAnimation() {
//        val offset = paraHighLightRectF?.right?.toInt()?: 0
//        if (offset <= 1) {
//            xValueAnimator.cancel()
//            invalidate()
//            return
//        }
//        xValueAnimator.cancel()
//        xValueAnimator.setIntValues(mLastXOffset, offset)
//        xValueAnimator.start()
    }

    override fun onDraw(canvas: Canvas) {
        highLightRectList.forEach {
            canvas.drawRect(it, highlightPaint)
        }

        selectedRectList.forEach {
            canvas.drawRect(it, selectedPaint)
        }

        super.onDraw(canvas)
    }

    fun onDestroy() {
        cancelUpdateTimeTask()
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility != VISIBLE) {
            cancelUpdateTimeTask()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cancelUpdateTimeTask()
    }

    fun pause() {
        cancelUpdateTimeTask()
    }

    // 倍速变化了
    fun onTempoChanged(tempo: Float) {
        mTempo = tempo
        setTimeFrequency((TIMER_FREQURENCY / mTempo).toLong())
        removeCallbacks(mDrawLightLineTask)
        postDelayed(mDrawLightLineTask, TIMER_FREQURENCY)
    }

    // 拖动进度条的时候无需做翻滚动画
    fun updateCurLine(pos: Long, needUpdateXOffset: Boolean) {
        mCurrentLine = findParagraphIndex(pos)
        Logger.d(TAG, "updateCurLine; mCurrentLine= $mCurrentLine")
        mCurrentTime = pos
        mLastTime = mCurrentTime
        updateExactTime(mCurrentTime)
        invalidate()
    }
}