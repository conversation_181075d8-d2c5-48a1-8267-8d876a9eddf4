package com.ximalaya.ting.android.host.manager.bundleframework.route.action.main;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.download.DownloadLiteManager;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.IPromiseCallback;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.adapter.mulitviewtype.IMulitViewTypeAdapter;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.data.model.RnConfirmParam;
import com.ximalaya.ting.android.host.data.model.comment.TingReadComParamModel;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listener.IBaseMySpaceView;
import com.ximalaya.ting.android.host.listener.IBaseTempoListener;
import com.ximalaya.ting.android.host.listener.IColumnLargeAdProvider;
import com.ximalaya.ting.android.host.listener.IDataChangeCallback;
import com.ximalaya.ting.android.host.listener.IFeedBubbleItemClickListener;
import com.ximalaya.ting.android.host.listener.IFloatingPlayControlComponent;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.listener.IHomeRnNetAction;
import com.ximalaya.ting.android.host.listener.ILoadMineDataCallBack;
import com.ximalaya.ting.android.host.listener.IParaComListener;
import com.ximalaya.ting.android.host.listener.IPlayPageLargeAdProvider;
import com.ximalaya.ting.android.host.listener.ITingReadFloatingPlayControlComponent;
import com.ximalaya.ting.android.host.listener.ITomatoesClickCallback;
import com.ximalaya.ting.android.host.live.callback.IMiniDramaLockCallBack;
import com.ximalaya.ting.android.host.live.callback.ISoundTabsCallBack;
import com.ximalaya.ting.android.host.manager.NewShowNotesManager;
import com.ximalaya.ting.android.host.manager.NickNameSettingManager;
import com.ximalaya.ting.android.host.manager.ad.IAdEngineProviderExtend;
import com.ximalaya.ting.android.host.manager.ad.IMiddleAdViewShowCallback;
import com.ximalaya.ting.android.host.manager.ad.IPlayAdEngine;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.radio.IRadioFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.model.CodeReadResult;
import com.ximalaya.ting.android.host.model.VideoFeedBackTraceData;
import com.ximalaya.ting.android.host.model.account.HomePageModel;
import com.ximalaya.ting.android.host.model.account.HomePageModelNew;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.alarm.AlarmRecord;
import com.ximalaya.ting.android.host.model.album.AlbumCommentModel;
import com.ximalaya.ting.android.host.model.album.AlbumListenNote;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AlbumSimpleInfo;
import com.ximalaya.ting.android.host.model.anchor.Anchor;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.model.feed.DownloadKaChaBean;
import com.ximalaya.ting.android.host.model.kacha.KachaAIDocModel;
import com.ximalaya.ting.android.host.model.myspace.MineModuleItemInfo;
import com.ximalaya.ting.android.host.model.payment.UniversalPayment;
import com.ximalaya.ting.android.host.model.play.CommentModel;
import com.ximalaya.ting.android.host.model.play.DubDownloadInfo;
import com.ximalaya.ting.android.host.model.play.PlayPageBridgeResult;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.read.bean.TRMainPlayTheme;
import com.ximalaya.ting.android.host.read.ting.callback.ITRCreateMainPlayPageThemeCallBack;
import com.ximalaya.ting.android.host.recommend.IRecommendRnView;
import com.ximalaya.ting.android.host.socialModule.IFloatingFragmentDismissListener;
import com.ximalaya.ting.android.host.util.common.CommonBottomDialogUtilConstants;
import com.ximalaya.ting.android.host.view.lrcview.LrcEntry;
import com.ximalaya.ting.android.host.view.other.EmotionSelector;
import com.ximalaya.ting.android.main.adapter.album.IAlbumCallback;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.read.bean.TingParaBean;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;


/**
 * Created by jack.qin on 2017/3/9.
 *
 * <AUTHOR>
 */

public interface IMainFunctionAction extends IAction {

    int getSpecialColumnMTypeColumnSubjectConstants();

    int getCategoryContentFragmentFlagCityDataConstants();

    void gotoArtistPage(String url, Activity activity);

    void cancelPay(AbstractTrackAdapter adapter);

    AbstractTrackAdapter newPaidTrackAdapter(Context context, List<Track> tracks, int type, int playFrom);

    AbstractTrackAdapter newPaidTrackAdapter(Context context, List<Track> tracks, int type, int playFrom, String pageFrom);

    HolderAdapter<Radio> newRadioListAdapter(Context context, List<Radio> radios, BaseFragment fragment, boolean fromSearch);

    HolderAdapter<Anchor> newUserListAdapter(Context context, List<Anchor> list, BaseFragment fragment, int type);

    BaseAlbumAdapter newAlbumAdapter(Context context, List<Album> albums, int type, boolean chooseType, String keyWord, String searchId);

    HolderAdapter<Anchor> createSearchAttentionMeberAdapter(BaseFragment2 fragment2, List<Anchor> list, int type);

    BaseAdapter createWoTingRecommendAdapter(Context context, Activity activity, boolean isShowNoSubscribeType, List<Album> list, BaseFragment fragment, AdapterView.OnItemClickListener onItemClickListener);

    void shareTrack(FragmentActivity activity, Track trackM, int shareTypeTrack);

    void shareAlbumForPlc(FragmentActivity activity, AlbumM data, int shareTypeAlbum);

    void shareAlbumListenNote(FragmentActivity activity, AlbumM album, AlbumListenNote albumListenNote, int shareType);

    void shareVideo(FragmentActivity activity, Track trackM);

    void shareDub(FragmentActivity activity, Track trackM);

    void showPlayerPageShopDialog(long trackId, String title, String url);

    void preloadCommericalDialogResource(@org.jetbrains.annotations.Nullable String popupId,
                                         String imageUrl,
                                         @org.jetbrains.annotations.Nullable BaseFragment2 fragment,
                                         @NotNull Function0<Unit> function);
    void preloadCommercialDialogVideoResource(String popupId,
                                              String imageUrl,
                                              @NotNull Function0<Unit> function);
    /**
     * 本模块提供给外接访问的 fragment中使用的的常量，定义常量是请注明其意义
     */
    interface FragmentConstants {

        /**
         * 外界在启动QRCodeFragment 时，限制输入的参数种类 用以区分所传入的id是何种类型，而不是直接传入类型实例
         */
        int TYPE_PAID_ALBUM = 0;//付费专辑
        int TYPE_FREE_ALBUM = 1;//付费专辑
        int TYPE_PAID_TRACK = 2;//付费声音
        int TYPE_FREE_TRACK = 3;//免费声音
        int TYPE_ANCHOR = 4;//主播
        int TYPE_PAID_VIDEO = 5;
        int TYPE_FREE_VIDEO = 6;
        int TYPE_COMMUNITY = 7;//圈子
        int TYPE_COMMUNITY_ARTICLE = 8;//圈子帖子
        int TYPE_DUB_VIDEO = 9;//趣配音
        int TYPE_LIVE_ROOM = 10;//直播
        /**
         * end 外界在启动QRCodeFragment Constants
         */

        int CHOOSE_TYPE_TRACK = 1;//复用搜索页仅搜索声音
        int CHOOSE_TYPE_ALBUM = 2;//复用搜索页仅搜索专辑
        int CHOOSE_TYPE_ANCHOR = 3;//复用搜索页仅搜索主播

        int CHOOSE_TYPE_LISTEN_NOTE = 4; //听单
        int CHOOSE_TYPE_MC_SCHEDULE = 5;
        int CHOOSE_TYPE_MC_ROOM = 6;
    }


    /**
     * 登录账户
     *
     * @param loginFlag 登录标志
     * @param info      授权信息
     * @param activity
     * @param arguments
     * @param finish
     */
///    void login(int loginFlag, XmLoginInfo.AuthInfo info, Activity activity, Bundle arguments, boolean finish);

    /**
     * 获取专辑类型和是否购买信息
     *
     * @param albumId  专辑id
     * @param callback 异步回调接口
     */
    void getAlbumSimpleInfoById(long albumId, IDataCallBack<AlbumSimpleInfo> callback);

    /**
     * 获取购买专辑弹窗
     *
     * @param activity 上下文
     * @param albumId  专辑id
     * @param title    专辑title
     * @return
     */
    IMainFunctionAction.IBuyAlbumTip getBuyAlbumDialog(Activity activity, long albumId, String title, boolean isFromAlbumIntro);

    /**
     * 首页tab切换
     *
     * @param fragment 首页
     * @param fid      tab类型
     * @return
     */
    boolean switchHomeTab(BaseFragment fragment, int fid);

    void getHomePage(Map<String, String> homeParams, IDataCallBack<HomePageModel> iDataCallBack);

    void getHomePageNew(Map<String, String> homeParams, IDataCallBack<HomePageModelNew> iDataCallBack);

    void getPlayPageInfo(long trackId, HashMap<String, String> params, IDataCallBack<PlayingSoundInfo> iDataCallBack);

    boolean handleITing(Activity activity, Uri parse);//处理iting链接

    boolean handleITing(Activity activity, Uri parse, String resPosition);//处理iting链接

    boolean handleITing(Activity activity, PushModel model);//处理iting链接

    String getPageNameByMsgType(int msgType);

    BaseAlbumAdapter newAlbumAdapter(MainActivity activity, List<Album> listData);

    void sendComment(String trackId, String content, String parentId, String second, int contentType, IDataCallBack<CommentModel> callBack);


    boolean handleIting(Activity activity, Uri uri);

    /**
     * 添加已下载的apk到列表当中 <广告>
     *
     * @param url
     * @param filePath
     */
    void putAdStateManagerAlearDownloadMap(String url, String filePath);

    // 激励视频内部吊起其他解锁入口
    void enterOtherUnlock(Activity activity, boolean autoClick);

    interface IBuyAlbumTip {
        void showDialog();//显示弹窗
    }

    /**
     * 摇一摇广告的管理类
     *
     * @param context
     * @return
     */
    IYaoyiYaoManager getYaoyiYaoManagerInstance(Context context);

    interface IYaoyiYaoManager {
        void onPlayProgress(int currPos, int duration);

        void onPlayStart();

        void onServiceDestory();

        void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel);

        void onStartGetAdsInfo(int playMethod, boolean duringPlay, boolean paused);

        void onGetAdsInfo(AdvertisList ads);

        void onStartPlayAds(Advertis ad, int position);

        void onStartCommand();

        void onPlayPause();

        void onCompletePlayAds();

    }

    /**
     * 广告
     *
     * @param context
     * @param thirdAd
     * @param logType
     * @param positonName
     */
    void adRecord(Context context, final Advertis thirdAd, String logType, String positonName);

    /**
     * 广告
     *
     * @param context
     * @param thirdAd
     * @param logType
     * @param positonName
     */
    void handlerAdClick(Context context, final Advertis thirdAd, String logType, String positonName);

    /**
     * 广告
     *
     * @param context
     * @param thirdAd
     * @param logType
     * @param positonName
     * @param categoryId
     * @param index
     */
    void handlerAdClick(final Context context, final Advertis thirdAd, final String logType, final String positonName, int categoryId, int index);


    /**
     * 广告
     *
     * @param context
     * @param thirdAds
     * @param logType
     * @param positonName
     */
    void batchAdRecord(Context context, List<? extends Advertis> thirdAds, String logType, String positonName);

    void batchAdRecord(Context context, List<? extends Advertis> thirdAds, String logType, String positonName, int categoryId);

    void getUserFollower(int pageId, int pageSize, Integer type, Integer from,
                         IDataCallBack<ListModeBase<Anchor>> dataCallBack);

    /**
     * 请求广告焦点图
     *
     * @param appContext
     * @param categoryId
     * @param callBack
     */
    void getFocusAd(Context appContext, long categoryId, IDataCallBack<List<BannerModel>> callBack);

    /**
     * 重置定时关闭计划
     */
    void resetPlanTime(Context context);


    int getUnreadOfMySpace(Context context);

    /**
     * 拦截下载时的步骤
     */
    void interceptAdAppDownload(Activity activity, String title,@Nullable Advertis advertis, IHandleOk handleOk);

    /**
     * 重新打开app后再次确认是否继续下载
     */
    Dialog newAdAppDownloadRemindDialog(Activity activity, String title, IHandleOk handleOk ,IHandleOk handleCancle);

    abstract class AbstractHomePageFragment extends BaseFragment2 {
        public abstract void onRefresh();

        public abstract void reloadTabData(boolean recommendTab);

        public abstract boolean change2RecommendWhileInVipTab();
        public abstract void checkToShowNewTabGuideView();

        public abstract void notifyCustomizeDialogFinish();

        public abstract boolean recommendFragmentIsShowing();

        public abstract boolean recommendFragmentStaggeredIsShowing();

        public abstract boolean isFragmentOnTop();

        public abstract Fragment getCurTabFragment();

        public abstract boolean isRefreshBecauseOfTabClick();

        public abstract void tryToShowChatXmlyBubble();
        public abstract void refreshMsgCount();

        public abstract void loadTabData();

        public abstract int getContentTopHeight();
    }

    AbstractHomePageFragment newHomePageFragment();

    abstract class AbstractListenNoteFragment extends BaseFragment2 {
        public abstract void onRefresh();

        public abstract void clickRefresh();
    }

    interface IClickRefresh{
        public void clickRefresh();
    }

    abstract class AbstractVipFragment extends BaseFragment2 {

    }

    // 听更新和host交互的接口
    abstract class AbsEveryDayFragment extends BaseFragment2 {
        protected AbsEveryDayFragment() {
        }

        public AbsEveryDayFragment(boolean canSlide, SlideView.IOnFinishListener onFinishListener) {
        }

        public abstract boolean isEveryDayUpdateFragment();

        public abstract void reloadAndPlay(long timeline, long[] trackIds);

        public abstract void sort(boolean order, IDataCallBack callback);

        public abstract void hideFloatLayout();
    }

    abstract class AbstractFindingFragment extends BaseFragment2 {
        public abstract void onRefresh();
        public abstract void setTabClicked(boolean isChecked);
        public abstract void loadNetData();
        public abstract boolean onBackPressed();
    }

    interface IScrollViewFragment {
        void scrollToPosition(int position);
    }

    abstract class AbstractMySpaceFragment extends BaseFragment2 {

    }

    AbstractFindingFragment newFindingFragment();

    BaseFragment2 newMyspaceFragment(boolean isOpenOnSinglePage);

    // BaseFragment2 newVipTabsFragment();

    // 账号-我听合并
    abstract class AbstractMySpaceAndListenFragment extends BaseFragment2 {

    }

    BaseFragment2 newMySpaceAndListenFragment();

    void getNextTrackInChannel(Track track, Context context);

    /**
     * 跳转不知道类型的专辑页面
     *
     * @param activity          MainActivity 实例
     * @param albumId           专辑id
     * @param from              专辑打开来源 (默认 AlbumEventManage.FROM_OTHER)
     * @param playSource        专辑播放来源 (默认 ConstantOpenSDK.PLAY_FROM_OTHER)
     * @param recSrc            推荐字段
     * @param recTrack          推荐字段
     * @param unreadTrackNumber 专辑新增声音数（标new）默认-1
     */
    void startUnknownTypeAlbumFragment(MainActivity activity, long albumId, int from, int playSource, String recSrc, String recTrack, int unreadTrackNumber);

    /**
     * activity的onRequestPermissionsResult方法将值传过来
     */
    interface IPermissionsResult {
        void onRequestPermissionsResult(@NonNull Activity activity, int requestCode, @NonNull String[] permissions,
                                        @NonNull int[] grantResults);
    }

    /**
     * 需要检查权限的activity需要实现
     */
    interface ISetRequestPermissionCallBack {
        void setPermission(IPermissionsResult permissionsResult);
    }

    /**
     * 检查权限
     *
     * @param activity
     * @param requestPermissionCallBack
     * @param permissions               value<Integer> 表示字符资源的id ,如果为null表示用户点击禁止就不会弹出相关提示
     */
    boolean hasPermissionAndRequest(@NonNull final Activity activity,
                                    @NonNull ISetRequestPermissionCallBack requestPermissionCallBack,
                                    @NonNull final Map<String, Integer> permissions);

    /**
     * 权限申请的流程
     */
    interface IPermissionListener {
        // 已经存在权限或用户同意申请的权限
        void havedPermissionOrUseAgree();

        // 用户没有通过 或没有完全通过 返回的是没有通过的权限
        void userReject(Map<String, Integer> noRejectPermiss);
    }

    /**
     * 检查权限,并且有回调
     *
     * @param activity
     * @param requestPermissionCallBack
     * @param listener
     * @return
     */
    void checkPermission(@NonNull final Activity activity,
                         @NonNull ISetRequestPermissionCallBack requestPermissionCallBack,
                         @NonNull final Map<String, Integer> permissions, IPermissionListener listener);

    void getSpecialTingList(Context appContext, long categoryId, IDataCallBack<ListModeBase<Track>> callBack);

    void getShareAd(Map<String, String> params, final IDataCallBack<List<Advertis>> callBackM);

    /**
     * @param entrance           Configure.AnchorSkillEntranceId.RECORD
     *                           Configure.AnchorSkillEntranceId.PROGRAMME
     *                           Configure.AnchorSkillEntranceId.PROFIT
     *                           Configure.AnchorSkillEntranceId.LIVING
     *                           Configure.AnchorSkillEntranceId.COMMUNITY
     * @param extraClickListener 点击进入相应页面时会触发，可以添加埋点等自定义事件
     */
    void showAnchorSkillEntrance(@NonNull BaseFragment2 fra, @NonNull ViewGroup parentView,
                                 int entrance, @Nullable View.OnClickListener extraClickListener);

    void showNotificationDialogForEveryDayUpdate(@NonNull BaseFragment2 frag);

    void showCommonBottomDialog(TrackM track, int options, final CommonBottomDialogUtilConstants.Listener listener);

    /**
     * @param fra 返回当前的frag中的"MagneticView"(默认返回找到的第一个)
     * @return
     */
    void dismisssMagneticView(@NonNull BaseFragment2 fra);

    /**
     * 显示选择下载音质弹窗
     *
     * @param callBack 用户操作后的回调。
     *                 用户选择后回调onSuccess，传递的参数为null，
     *                 用户取消操作后回调onError
     */
    void showChooseTrackQualityDialog(Context context, Track track, IDataCallBack<Object> callBack);


    boolean createQRImage(String content,
                          int widthPix, int heightPix, int margin,
                          Bitmap logoBm, String filePath);


    /**
     * @param albumId 专辑id
     * @return 下载的专辑在专辑详情页是否是升序
     */
    boolean isAlbumAsc(Context context, long albumId);

    /**
     * 播放列表播放完成的提示音
     */
    boolean playListCompleteHint(@NonNull Context context, ICallback<Void> hintCompleteCallback);

    /**
     * 圈子或者贴子分享
     *
     * @param shareType          分享类型  圈子分享 ICustomShareContentType.SHARE_TYPE_COMMUNITY
     *                           贴子分享 ICustomShareContentType.SHARE_TYPE_COMMUNITY_POST
     * @param communityId        圈子ID   圈子分享，帖子分享必传
     * @param communityArticleId 帖子ID   帖子分享必传
     */
    void communityShareDialog(Activity activity, int shareType, long communityId, long communityArticleId, ShareManager.Callback clickCallback);

    /**
     * 明星圈分享
     */
    View starCommunityShareDialog(Activity activity, int shareType, long communityId, long communityArticleId, ShareManager.Callback clickCallback);

    void shareDubbing(Activity activity, Track track, boolean isCallForAct, ShareManager.Callback callback);

    void shareDubbing(Activity activity, Track track, boolean isCallForAct);

    void shareDubbing(Activity activity, ShareWrapContentModel model, boolean isCallForAct, ShareManager.Callback callback);

    Class getDubShowPPTPlayFragmentClass();

    /**
     * dubShowFragment 中正在播放的trackId， 是否和toPlayTrackId一致
     *
     * @param dubShowFragment
     * @param toPlayTrackId
     * @return
     */
    boolean isSameTrackId(BaseFragment dubShowFragment, long toPlayTrackId);

    void playDubShow(Context context, View view, Bundle bundle, boolean isVideo);

    interface ICommentTabFragment {
        View getInnerScrollView();
    }

    void requestFindHomePageDataAndReturnCalabashView(ViewGroup parentView, ITomatoesClickCallback callback, BaseFragment2 fragment2, IDataCallBack<View> viewIDataCallBack, IDataCallBack<Pair<Object, String>> dataCallBack, String oldJson);

    void requestFindHomePageData(final BaseFragment2 fragment2, final IDataCallBack<String> dataCallBack, final String oldJson);

    void dealFindHomePageDataClick(final BaseFragment2 fragment2,View view,String data ,int position);

    class DynamicCallbackData {
        public int commitCount;
        public boolean isPraise;
        public int praiseCount;
        public long feedId;
        public boolean deleted;
        public boolean followAnchor = true;
        public int currentDuration;
    }

    void disLike(Map<String, String> params, IDataCallBack<JSONObject> callBack);

    boolean isResumingRaidoContentFragment(BaseFragment fragment);

    /**
     * 是否选中了推荐
     * @param fragment
     * @return
     */
    boolean isRecommendFragmentVisible(Fragment fragment);

    interface IRadioAdManager {
        void playFragmentOnResume();

        void playFragmentOnPause();

        void adCoverClick();

        void adCloseBtnClick();

        void onPlayStart();

        void onPlayPause();

        void playFragmentSoundOnSoundSwitch(PlayableModel lastModel, PlayableModel curModel, boolean isfromUse);
    }

    IRadioAdManager getRadioAdManager(IRadioFragmentAction.IRadioFragment radioFragment);

    /**
     * 是否是专辑详情页面
     */
    boolean isAlbumFragmentNew(Fragment fragment);

    /**
     * 获取专辑页面的id，返回负值，说明fragmet不是专辑详情页面，或者专辑id不存在
     */
    long getAlbumIdFromAlbumFragmentNew(Fragment fragment);

    /**
     * fragment是否处于未成年人保护流程中的页面
     */
    boolean isInChildProtectFragmentFlow(Fragment fragment);

    boolean isInChildProtectTipFragment(Fragment fragment);

    interface IDialogDismiss extends IHandleOk {
        void onReady(Bitmap bitmap, Bitmap finalBitmap, int[] locations, int[] sizes);
    }

    void getVideoInfoById(long trackId, IDataCallBack<String[]> callBack, Track track);

    interface IVideoPlayManager {

        void dispatchScrollChange(int hashCode, int dx, int dy);

        void dispatchScrollStateChange(int hashCode, int state, int firstVisiblePosition, int lastVisiblePosition);

        void restCurrentPlayPosition();

        void stopListViewPlay(ListView listView);
    }

    interface IWaveCodeReadListener {

        void onContentReadOut(CodeReadResult result);
    }

    void downloadDubVideo(DubDownloadInfo info);

    void toOneKeyListen(Activity activity, boolean resetHeadlineTracks);

    void toOneKeyListen(Activity activity, long toChannelId, long toTrackId, boolean resetHeadlineTracks);

    void toOneKeyListen(Activity activity, long channelGroupId, long toChannelId, long toTrackId);

    void hideSearchDirectComment(BaseFragment fragment);

    boolean useNewAggregateRankPage();

    void startRankDetailPage(int clusterType, int categoryId, long rankListId);
    void startRankHomePage();

    boolean showMonthlyTicketVoteDialog(String source, long albumId, long anchorId, int initialTab);

    interface ICartoonDataListener {
        void setCartoonPlayData(Track track, PlayingSoundInfo soundInfo);
    }


    interface IInputBar {
        void init(EmotionSelector.IKeyboardListener2 keyboardListener, EmotionSelector.OnSendButtonClickListener sendButtonClickListener, int commentType, View maskView, EmotionSelector.Options options);
        void show(String comment, EmotionSelector.Options options);
        void hide();
        void clear(boolean deleteMem);
        void onCommentSent();
        void setMaxImgNum(int count);
        boolean isSyncTing();
        EmotionSelector.InputInfo getInputInfo();
    }

    IInputBar getInputBar(Activity activity);

    void getTingListDetailForPost(long albumId, IDataCallBack<AlbumListenNote> callback);

    /**
     * 对声音的评论进行点赞或取消点赞
     */
    void requestTrackCommentLikeOrUnLike(long trackId, long commentId, boolean isLike, IDataCallBack<Boolean> callBack);

    /**
     * 对专辑的评论进行点赞或取消点赞
     */
    void requestAlbumCommentLikeOrUnLike(boolean isLike, long albumId, long commentId, long commentUid, IDataCallBack<Boolean> callback);

    /**
     * 对听单评论进行点赞或取消点赞
     */
    void requestTingdanCommentLikeOrUnLick(boolean isLike, long recordId, long commentId, IDataCallBack<Boolean> callback);

    /**
     * 对免费专辑的回复进行删除
     */
    void requestDeleteFreeAlbumReply(long albumId, long replyId, IDataCallBack<Boolean> callback);

    /**
     * 对免费专辑的评论进行删除
     */
    void requestDeleteFreeAlbumComment(long albumId, long commentId, IDataCallBack<Boolean> callback);

    /**
     * 对付费专辑的回复进行删除
     */
    void requestDeleteAlbumReply(long albumId, long commentId, IDataCallBack<JSONObject> callback);

    /**
     * 删除声音的评论或回复
     */
    void requestDeleteTrackComment(long trackId, long commentID, IDataCallBack<Boolean> callback);

    /**
     * 对专辑的评论进行回复
     */
    void replyAlbumRate(long albumId, long commentId, String content, long commentUid, boolean syncType, IDataCallBack<AlbumCommentModel> callback);

    /**
     * 对声音的评论进行回复
     */
    void sendTrackComment(long trackId, String content, boolean isSyncTing, EmotionSelector.InputInfo inputInfo, long parentId, IDataCallBack<CommentModel> callback);

    /**
     * 对听单的评论进行回复
     */
    void replyTingDanComment(long trackId, String content, long parentId, IDataCallBack<CommentModel> callback);

    /**
     * 获取评论推送开关状态
     */
    void isCommentPushSettingOpen(IDataCallBack<Boolean> callBack);

    /**
     * 设置评论推送开关状态
     */
    void setCommentPushSetting(boolean isPush);
    void setReservePushSettingOpen(boolean onlyOpenReserve, boolean shouldCloseOtherSetting);

    /**
     * 获取直播通知推送开关状态
     */
    void isLivePushSettingOpen(IDataCallBack<Boolean> callBack);

    /**
     * 设置直播通知推送开关状态
     */
    void setLivePushSetting(boolean isPush);

    /**
     * 获取通知推送开关状态
     */
    void isPushSettingOpen(String pushKey, IDataCallBack<Boolean> callBack);

    void isPushSettingOpenIncludeLogoutState(String pushKey, IDataCallBack<Boolean> callBack);

    void setPushSettingOpenIncludeLogoutState(String pushKey, boolean isPush);

    void showSpringEventReminder(boolean showDialog);

    void deleteShortContentCacheFile();

    void showDownloadKaChaFragment(FragmentManager fragmentManager, DownloadKaChaBean bean);

    SharedPreferencesUtil getOnlyUseMainProcessSharePre(Context context);

    /**
     * 打开微信小程序
     * @param context
     * @param appId
     * @param path
     * @param type
     * @param callBack
     */
    void openWeChatLiteApp(Context context, String appId, String path, int type, IDataCallBack callBack);

    void collectTingList(long id, int type, IDataCallBack<Boolean> callback);

    void cancelCollectTingList(long id, IDataCallBack<Boolean> callback);

    void showFeedDislikeBubbleDialog(Activity activity, List<BaseDialogModel> data, IFeedBubbleItemClickListener listener, View view);

    IFloatingPlayControlComponent newFloatingPlayControlComponent(BaseFragment2 fragment
            , IFloatingPlayControlComponent.IFloatingControlBarActionListener listener);

    boolean isNewPlayFragment();

    boolean isPlayPagePlayingVideoWhilePaused();

    void tryToGetUserMission();

    void operateH5Dialog(String operation, String target);

    /**
     * 跳转扫码
     */
    BaseFragment2 goScan();


    IMulitViewTypeAdapter getMultiTypeAdapter(int type, IBaseMySpaceView baseMySpaceView);

    void showXimiGuideDialog(String ximiUrl);


    void addOrRemoveTempoListener(boolean add, IBaseTempoListener iBaseTempoListener);

    boolean showVipGuideView(BaseFragment2 frag, ViewGroup parent, AlbumM album);

    Pair<Float, String> updateTempo();

    void showTempoDialog(Context context);

    void showTempoDialog(Context context, int animalStyle);

    float getTempo();

    void setTempoByNum(float tempoNum);

    void setTempoIndexYPage(float tempo);

    void showVipFloatPurchaseDialog(BaseFragment2 fragment, String h5Url);

    void mineEntranceSyncHomePageEntrance();

    void loadLocalModelItemAndHomePageModel(ILoadMineDataCallBack.LoadMinePageDataCallback loadMinePageDataCallback);

    void loadModuleItemInfoList(ILoadMineDataCallBack.LoadDataFinishCallback<List<MineModuleItemInfo>> callback);

    String getSpKeyMySpaceFragmentHomeModel();

    void showFreshGiftFragment(FragmentManager fragmentManager);

    boolean audioPlayPageIsLargeDevice();

    IPlayAdEngine getPlayAdEngine(IAdEngineProviderExtend adEngineProvider);

    void startBuyFragment(BaseFragment2 fra2, AlbumM albumM, IFragmentFinish callbackFinish);

    Class getBoughtAlbumAdapter();

    void setBoughtAdapterListener(HolderAdapter adapter, IAlbumCallback callback);

    BaseDialogFragment newPlayNoCopyRightDialog();

    BaseDialogFragment newPlayNoCopyRightDialog(long trackId,String recSrc,String recTrack);

    void setListenTaskManagerTaskClick(boolean clicked);

    // 预加载巨幕广告
    void preloadBigScreenAd(boolean isHotLoad);

    // 我的作品页分享声音
    void shareMyTrack(Activity activity, Track track, int shareType, int subType);

    // 判断P拉C分享面板AB
    boolean checkAnchorShareDialogAB();

    void showNickNameSettingDialog(BaseFragment2 fragment, int source, NickNameSettingManager.INickNameDialogListener listener);

    // 新用户任务完成上报
    void tryToCommitMission(int type, Map<String, Object> params);

    // 在会员页上显示负反馈弹窗
    void showVipPageDislikeMenu(BaseFragment2 fragment, Track track, Map<String, String> defaultReasons, Map<String, String> traitsReasons, IDataCallBack<Object> callBack, boolean isFromVip);

    void showVipPageDislikeMenu(BaseFragment2 fragment, AlbumM album, Map<String, String> defaultReasons, Map<String, String> traitsReasons, IDataCallBack<Object> callBack, boolean isFromVip);

    Class newMyPrintFragment();

    Class newMyPrintFragmentV2();

    void startVideoPlayPage(@NonNull BaseFragment2 fromFragment, Bundle args, View fromView);

    void startVideoPlayPage(@NonNull MainActivity mainActivity, Bundle args, View fromView);

    void startPlayletPlayPage(@NonNull BaseFragment2 baseFragment2, Context context, long albumId);

    void showShareTipDialogFragment(BaseFragment2 fragment);

    void showAlbumOperationPanel(BaseFragment2 fragment, AlbumM album);

    void loadHomeTouchAd(int categoryId, BaseFragment2 fragment);

    void engageBuyAction(BaseFragment2 fragment, AlbumM album, IFragmentFinish finishCallBack, IAction.ICallback<Boolean> actionCallBack);

    // 因为定义的SubscribeRecommendAlbumMListWithDescription在mainBundle中，所以用Object
    void requireSubscribeRecommendFragmentData(long albumId, final IDataCallBack<Object> dataCallBack);

    boolean isMineSubscribePageLoading();

    void clearTangramBitmapCache();

    void startFragmentOnPlayPage(BaseFragment2 fragment);

    void removeFragmentOnPlayPage(BaseFragment2 fragment);

    void startTimelineCardFragmentOnPlayPage(boolean needAnim, int targetBizType, long targetBizId);

    void startVotePanelFragment(long trackId, long voteId, int srcPage, boolean showKeyboard,
                                long anchorCommentId, @Nullable Runnable actionOnFirstDismiss);

    void startSequentialVotePanelFragment(long trackId, long sequentialVoteId, int srcPage, @Nullable Runnable finishCallback);

    void startVideoCommentPanel(long videoId, @Nullable IFragmentFinish finishCallback);

    boolean isAlbumIdInLocalWhiteList(long albumId);

    interface IRadioAdManagerNew {
        void playFragmentOnResume();

        void playFragmentOnPause();

        void playFragmentOnDestroy();

        void onPlayStart();

        void onPlayPause();

        void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel);
    }

    IRadioAdManagerNew getRadioAdManagerNew(IRadioFragmentAction.IRadioFragmentNew radioFragment);

    @Deprecated void requestAndShowUniversalPaymentCombineActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension);
    void requestAndShowUniversalPaymentCombineActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension,boolean showAdFreeListen);

    void requestAndShowUniversalPaymentCombineActionsDialogForITing(BaseFragment2 fragment, String source, long albumId, HashMap<String, String> transmissionParams, boolean showAdFreeListen, String locatedTab);
    /**
     * 展示弹窗
     */
    void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension);

    void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension, boolean showAdFreeListen);

    void requestAndShowUniversalPaymentActionsDialog(Track track, String source);

    void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, Track track, String source,boolean isIndependent, boolean isManual);

    void requestAndShowUniversalPaymentActionsNonePurchaseDialog(BaseFragment2 fragment, String source, @Nullable String orderContext, @Nullable String itemExtension);

    void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, String source, long albumId, Track track, boolean showAdFreeListen);

    void showSingleAlbumBuyDialogWithForceReload(BaseFragment2 fragment, Album album);

    void showMorePanelDialog(BaseFragment2 fragment, int source , TrackM trackM);

    void notifyUserGiveUpAdUnlock(boolean manual);

    boolean hasPlayPermission(long trackId);

    void showNewUserDialogWithCheck(AlbumM album, boolean isOldAdUnlock);

    boolean isHomePageTabAndChannelListNewFragmentDialog(Fragment fragment);

    void showVoteDialog(long trackId, long albumId, long anchorId);

    void showRnCommonDialog(RnConfirmParam rnConfirmParam, IPromiseCallback callback);

    void showVoteDialogWithInitialTab(String sourcePage, long trackId, long albumId, long anchorId, int initialTabIndex);

    Object getPlayPageData();

    PlayingSoundInfo getPlayPageSoundInfoData();

    String getPlayPageViewData();

    String getXPlayPageCurrentTabInfo(IRNFunctionRouter.IRNEmbeddedView embeddedView);

    PlayPageBridgeResult showFollowSnackBarIfNeed(long trackId, int anchorRewardStatus, String anchorRewardUrl);
    PlayPageBridgeResult showCollectSnackBarIfNeed(long trackId);
    PlayPageBridgeResult showToListenGuideIfNeed(View startView);
    void showH5ToListenDialog(long trackId, String clickPlayIting, String trackTitle);

    //  public boolean monthlyTicketVote; // 能否投月票
    //        public long monthlyTicketCount; // 月票数
    void showPraiseSnackBarIfNeed(long trackId,long albumId,long uid,boolean  monthlyTicketVote,long monthlyTicketCount,IDataCallBack<Integer> callBack);
    void download();

    boolean shouldShowSubscribe();

    void refreshPlayPage();

    void startPlayPageAnimate(String biz, Runnable runnable, boolean raisePriority);

    @Nullable
    Map<String, String> getVideoPlayInfo();

    int getRealPlayProgress();

    @Nullable
    String getPraiseConfigForRN();

    void showFullScreenLottie(String url);

    void showAdDislikeBottomDialog(Context context, String positionId, Advertis advertis, String materialUrl, IDataCallBack dislikeSuccessCallBack);
    IColumnLargeAdProvider getColumnLargeAdProvider();
    IPlayPageLargeAdProvider getPlayPageLargeAdProvider();
    void doAnchorFollowFromRn(long anchorId, boolean isFollow, int bizType, int subBizType);

    void doAnchorFollowFromRn(long anchorId, long albumId, boolean isFollow, int bizType, int subBizType, IDataCallBack<Boolean> callBack);
    void doAnchorFollowFromRnCommon(long anchorId, long albumId, boolean isFollow, int bizType, int subBizType, String srcPage, IDataCallBack<Boolean> callBack);

    void doAlbumFavoriteFromRn(long albumId, String albumTitle , boolean isFavorite,int bizType,int followBizType , int followSubBizType);


    View getPlayShareView();

    /**
     * 获取当前播放音频还是视频
     *
     * @return 返回 0-声音 1-视频
     */
    int getPlayType();

    int getPlayType(IRNFunctionRouter.IRNEmbeddedView embeddedView);

    /**
     *
     * @return 返回0-否 1-是
     */
    int isFreeUnlockTrack();

    String getPlaypageColor();

    BaseDialogFragment tryToShowFreeListenEndGuideToNextAdDialog(Track track);

    boolean needShowExitPop();  //是否弹出新用户限免任务未完成离开弹窗
    void refreshHomepageRecommendFragment(); //新用户限免任务完成需要刷新首页的推荐页
    void refreshHomepageRecommendFragmentOnly(); // 提供外部刷新首页推荐页

    void refreshHomepageRecommendFragmentForCustomHome(); // 首页定制数据更新时刷新

    void setAutoOpenStatus(boolean show);

    boolean isNeedShowPlayList();

    void setAutoOpenToListenStatus(boolean show);

    boolean getAutoOpenToListenStatus();
    void showPlayListWhileInXPlayPage();
    void setAutoOpenToListenDeleteMode(boolean deleteMode);

    /**
     * XPlayFragment是否正在展示
     */
    boolean isXPlayFragmentRealVisible();

    /**
     * 下载后续相关
     */
    void deleteAlbumDownloadTask(long albumId); //删除本地保存的该专辑分批下载任务
    void deleteDownloadTask(); //删除本地保存的分批下载任务

    void showSubscribeUnlockSnackBar(long albumId);

    void buySinglePayAlbumTrack(Track track);

    void getTrainingRefundStatus(long albumId, int statusId, IDataCallBack<Integer> callBack);

    void showMasterVipAgreementConfirmDialog(IDataChangeCallback<String> callback, boolean needReShow);

    void refresshPlayPageNextBtnStatus();

    String getTempo2();

    void addTempoListener(IBaseTempoListener iBaseTempoListener);

    void removeTempoListener(IBaseTempoListener iBaseTempoListener);

    Fragment getCommentFragment(int srcPage, Track track);

    Fragment getTingReadParaCommentFragment(TingReadComParamModel model, boolean isOnlyShowSendCommentDialog, IParaComListener listener, IFloatingFragmentDismissListener dismissListener);

    ITingReadFloatingPlayControlComponent newTingReadFloatingPlayControlComponent(BaseFragment2 fragment
            , ITingReadFloatingPlayControlComponent.IFloatingControlBarActionListener listener);

    void onVideoNoLike(BaseFragment2 baseFragment2, VideoFeedBackTraceData traceData);

    void showListenTimeFragment(String source);

    BaseDialogFragment getFreeListenTimeRewardFragment(FreeListenConfigManager.PopUpPicture popUpPicture, int time, View.OnClickListener clickListener,
                                         View.OnClickListener closeListener);

    BaseDialogFragment getFreeListenTimeWatchVideoFragment(List<String> covers, View.OnClickListener clickListener, View.OnClickListener closeListener);

    View getShareCDPosterView(ShareWrapContentModel contentModel);

    View getShareStarPosterView(ShareWrapContentModel contentModel);

    void startVideoShare(ShareWrapContentModel contentModel);

    void startVideoClip(ShareWrapContentModel contentModel);

    void addRequestPlayPageParamData(Map<String, String> params);

    List<TingParaBean> getTingParaBeanData(PlayingSoundInfo soundInfo, List<LrcEntry> lrcList);

    boolean showNotePanelInXPlayFragment();

    void showSkipHeadTailDialog(Activity activity, long albumId);

    void requestAlbumListMiddleAd(AlbumM album);

    void showAlbumListMiddleAdView(ViewGroup viewGroup, AlbumM album, BaseFragment2 fragment2, IMiddleAdViewShowCallback callback);

    void refreshAlbumListMiddleAdView(ViewGroup viewGroup, AlbumM album, BaseFragment2 fragment2, IMiddleAdViewShowCallback callback);

    void notifyAlbumListMiddleAdDestroy();

    BaseDialogFragment getPlanTerminateFragmentNew();

    void showPlanTerminateDialogNew(FragmentManager fragmentManager);

    void createMainPlayPageThemeForTR(PlayingSoundInfo soundInfo, ITRCreateMainPlayPageThemeCallBack callback);

    TRMainPlayTheme createDefPlayPageThemeForTR();

    void seekTo(Context context, int progress);

    void forward15Second(Context context);

    void backward15Second(Context context);

    void showMoreActionDialog(BaseFragment2 baseFragment, PlayingSoundInfo soundInfo);

    void addReadBookInterceptBackListener(IReadBookInterceptBackListener listener);

    void removeReadBookInterceptBackListener();

    boolean isPlayPagePlayY(PlayingSoundInfo soundInfo);

    int checkPlayPageTabType(int focusTabType);

    void interceptTopSlideExit(Fragment fragment, boolean intercept);

    void preloadHomePageLayout(Runnable callback);

    void showAIDocShareDialog(PlayingSoundInfo soundInfo, String selectionContent, KachaAIDocModel kachaAIDocModel, FragmentManager manager);

    void startShowNotesDetailFragment(String sourceFrom, String clickPlayIting, long albumId, long trackId, long timeLine,
                                      boolean autoPlay, boolean openComment, Track playListTrack, CommonTrackList<Track> commonTrackList, int commonTrackListIndex,
                                      NewShowNotesManager.IShowNotesOnPlayClickHandler clickPlayHandler, long previewId, long showStartTime);
    void showFreeListenRewardDialogWithoutTrack(String sourceName,long albumId, BaseFragment2 baseFragment2, int layerType);

    void showFreeListenRewardDialogWithTrack(String sourceName, Track track, long trackId, long albumId, BaseFragment2 baseFragment2, int layerType);

    Class toBePlayedFragmentClass();

    List<TingParaBean> convertLrcData(PlayingSoundInfo soundInfo, List<LrcEntry> lrcList);

    boolean isYPlayPage(PlayingSoundInfo soundInfo);

    void checkAlbumPageShowFreeListenDialog(long albumId);

    void showOffSaleDialog(String currPage, long albumId, long trackId);

    BaseDialogFragment  getCommonInspireDialogFragment(int rewardedTime, int nextRewardTime, INativeAd nativeAd, boolean needReportShow, View.OnClickListener clickAdListener, View.OnClickListener closeAdListener);

    BaseDialogFragment getCommonRewardAgainDialogFragment(int rewardedTime, int nextRewardTime, View.OnClickListener clickListener);

    void toSimilarPage(Long albumId);

    void startSubscribeSearchFragment();

    /**
     * 短剧新购买 是否开启
     * @return boolean
     */
    boolean shortDramaIsNewBuyOpening(long albumId);

    /**
     * 加载当前用户剩余短剧权益 保存到全局
     * @param albumId 短剧id
     * @param callback 回调
     */
    void loadCurrentUserRemainShortDramaRightCount(long albumId, IDataCallBack<Long> callback);

    /**
     * 判断短剧状态
     * @param track Track
     * @return boolean
     */
    boolean isShortDrama(Track track);

    /**
     * 隐藏购买弹窗
     */
    void dismissBuyDialog();

    /**
     * 自动解锁短剧
     * @param track
     * @param callBack
     */
    void autoUnlock(Track track, IMiniDramaLockCallBack callBack);

    /**
     * 喜点单集购买
     * @return
     */
    boolean buySingleTrack(BaseFragment2 fragment, PlayingSoundInfo soundInfo);

    /**
     * 获取剧集 购买信息， 短剧是否支持 Playlet
     * @param trackId 短剧id
     */
    void getPlayPageTabAndInfoUseParamsMap(long trackId, ISoundTabsCallBack callBack);

    /**
     * 单点调用 购买弹窗 或调用激励视频 逻辑
     * @param track 专辑
     */
    void showMiniDramaBuyDialog(Track track);


    void editAlarmSettingFragment(AlarmRecord alarm);

    /**
     * 当前页面是不是推荐页
     */
    boolean topFragmentIsRecommend();

    boolean isMineTopVipNewStyle();

    void inviteWechatFriends();

    void getMobileAddressBookList(IDataCallBack<JSONObject> callBack);
    int getMineTopVipNewStylePageBgColor();

    IHomeRnNetAction getHomeRnNetAction();

    IRecommendRnView getRecommendRnItemView(Context context, String itemType);

    interface IQuickListenTempoDialogCallBack {
        void onSpeedSelected(float tempo);
    }

    void openQuickListenTempo(IQuickListenTempoDialogCallBack callBack);

    void gotoWifiFragment();

    boolean isNewSceneCard();

    void downloadSkinPack(String skinId, String skinUrl, DownloadLiteManager.DownloadCallback downloadCallback);

    boolean getSkinDownloadStatus(String skinId, String skinUrl);

    void clearPlaypageSkin();

    void startPlayPageDebugFragment();
}