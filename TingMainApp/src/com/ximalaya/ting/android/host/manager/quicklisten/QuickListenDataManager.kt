package com.ximalaya.ting.android.host.manager.quicklisten

import android.text.TextUtils
import android.util.Log
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.fragment.IQuickListenTabFragment
import com.ximalaya.ting.android.host.listener.SimpleOnPlayerStatusListener
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager.requestTenData
import com.ximalaya.ting.android.host.manager.TrackCollectManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.track.AgentRadioCollectListener
import com.ximalaya.ting.android.host.manager.track.AgentRadioEventManage
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.model.quicklisten.QuickListenTab
import com.ximalaya.ting.android.host.util.XimaTenDataUtil
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants
import com.ximalaya.ting.android.opensdk.player.manager.QuickListenForPlayProcessUtil
import com.ximalaya.ting.android.opensdk.player.service.IXmDataCallback
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONArray
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.LinkedList

/**
 * <AUTHOR>
 * @date 2025/7/4 11:07
 */
const val TAG = "QuickListenDataManager"

private const val TAB_ID = "id"
private const val TAB_ACCESS_TIME = "time"
private const val TAB_TRACK_ID = "trackId"
private const val MAX_TABS = 3
const val KEY_QUICK_LISTEN_LAST_TAB_ID = "quick_listen_last_tab_id"

class QuickListenDataManager private constructor() {
    companion object {
        const val QUICK_LISTEN_MMKV_FILE_NAME = "QUICK_LISTEN_SHORT_CONTENT_FEED_MMKV_FILE_NAME_0702"
        private const val QICK_LISTEN_CONTENT_DATA = "QUICK_LISTEN_CONTENT_DATA_0702"
        private const val XIMA_TEN_CONTENT_DATA_KEYS = "QUICK_LISTEN_CONTENT_DATA_0702_keys"
        const val QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS = "QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS_0702"
        const val QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS_KEY = "QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS_KEY_0702"
        private const val QUICK_LISTEN_PAUSE_STATUS = "KEY_QUICK_LISTEN_PAUSE_STATUS"

        private val mQuickListenDataManager = QuickListenDataManager()
        fun getInstance() = mQuickListenDataManager
    }

    private val mIXmDataCallback = object : IXmDataCallback {
        override fun asBinder() = null

        override fun onListChange() {
        }

        override fun onDataReady(
            list: MutableList<Track>?,
            hasMorePage: Boolean,
            isNextPage: Boolean,
        ) {
            HandlerManager.postOnBackgroundThread {
                listChange()
            }
        }

        override fun onError(code: Int, message: String?, isNextPage: Boolean) {
        }
    }

    private val mPlayStatusListener = object : SimpleOnPlayerStatusListener() {
        override fun onPlayStart() {
            super.onPlayStart()
            playOrPause(false)
        }
    }

    private val ximaTenPlayListMmkv: MMKVUtil
    private val mTabList = LinkedList<JSONObject>()
    private val mCacheMap = HashMap<Long, MutableList<JSONObject>>()
    private val mHasRequestSet = HashSet<Long>()
    init {
        ximaTenPlayListMmkv = MMKVUtil.getInstance(QUICK_LISTEN_MMKV_FILE_NAME)
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
            .addPlayListChangeListener(mIXmDataCallback)
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
            .addPlayerStatusListener(mPlayStatusListener)

        runCatching {
            val tabsContent = ximaTenPlayListMmkv.getString(XIMA_TEN_CONTENT_DATA_KEYS)
            logToFile(TAG, "cache-$tabsContent")
            val jsonArr = JSONArray(tabsContent ?: "[]")

            val content = ximaTenPlayListMmkv.getString(QICK_LISTEN_CONTENT_DATA)
            val contentJson = JSONObject(content)

            for (i in 0 until jsonArr.length()) {
                val itemJson = jsonArr.optJSONObject(i)
                if (itemJson == null) {
                    continue
                }
                val tabId = itemJson.optLong(TAB_ID, -1L)
                val lastAccessTime = itemJson.optLong(TAB_ACCESS_TIME, -1L)
                if (tabId > 0 && lastAccessTime > 0) {
                    val itemJsonArr = contentJson.optJSONArray("$tabId")
                    if (itemJsonArr != null && itemJsonArr.length() > 0) {
                        val list = mutableListOf<JSONObject>()
                        for (j in 0 until itemJsonArr.length()) {
                            val item = itemJsonArr.optJSONObject(j)
                            if (item != null) {
                                list.add(item)
                            }
                        }
                        if (list.size > 0) {
                            mTabList.add(itemJson)
                            mCacheMap[tabId] = list
                        }
                    }
                }
            }
            logContent("init data", mCacheMap)
            logToFile(TAG, "cache-2 size=${mCacheMap.keys.size},keys=${mCacheMap.keys}")
        }.onFailure {
            it.printStackTrace()
            logToFile(TAG, "cache-3 ${Log.getStackTraceString(it)}")
        }

        runCatching {
            val lastSaveTime = MMKVUtil.getInstance().getLong(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE_SAVE_TIME, 0)
            if (DateTimeUtil.isAnotherDay(lastSaveTime)) {
                MMKVUtil.getInstance().removeByKey(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE)
                MMKVUtil.getInstance().removeByKey(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_SEQUENCE_SAVE_TIME)
            }
        }

        AlbumEventManage.addListener { collect, id ->
            kotlin.runCatching {
                var found = false
                for (mutableEntry in mCacheMap) {
                    for (jsonObject in mutableEntry.value) {
                        if (jsonObject.optString("elementType") != "AgentRadio"
                            && jsonObject.optJSONObject("surElement")?.optLong("refId") == id
                        ) {
                            val interactJson =
                                jsonObject.optJSONObject("surElement")?.optJSONObject("interact")
                            interactJson?.put("subscribed", if (collect) 1 else 0)
                            found = true
                        }
                    }
                }
                savePlayList()
                if (found && collect) {
                    val fragment = mQuickListenFragmentWR?.get()
                    val tabId = fragment?.curTabId ?: -1L
                    val listData = cache(tabId)
                    var cardIndex = -1
                    var refId = -1L
                    listData?.forEachIndexed { index, jsonObject ->
                        if (jsonObject.optString("elementType") != "AgentRadio"
                            && jsonObject.optJSONObject("surElement")?.optLong("refId") == id) {
                            cardIndex = index
                            refId = jsonObject.optLong("refId", -1L) ?: -1L
                        }
                    }
                    val card = listData?.getOrNull(cardIndex)
                    if (fragment != null && tabId > 0 && cardIndex >= 0 && card != null && refId > 0) {
                        val ubtV2 = card.optJSONObject("ubtV2")?.optString("source") ?: ""
                        recommendQuickListen(fragment, tabId, refId, 1, ubtV2)
                    }
                }
            }.onFailure { it.printStackTrace() }
        }

        AgentRadioEventManage.addListener(object : AgentRadioCollectListener {
            override fun onCollectChanged(collect: Boolean, id: Long) {
                kotlin.runCatching {
                    for (mutableEntry in mCacheMap) {
                        for (jsonObject in mutableEntry.value) {
                            if (jsonObject.optString("elementType") == "AgentRadio"
                                && jsonObject.optJSONObject("surElement")?.optLong("refId") == id
                            ) {
                                val interactJson = jsonObject.optJSONObject("surElement")
                                    ?.optJSONObject("interact")
                                interactJson?.put("subscribed", if (collect) 1 else 0)
                            }
                        }
                    }
                    savePlayList()
                }.onFailure { it.printStackTrace() }
            }

            override fun onFail(collect: Boolean, id: Long) {
            }
        })

        LikeTrackManage.addListener { isLikeNow, trackId ->
            kotlin.runCatching {
                var found = false
                for (mutableEntry in mCacheMap) {
                    for (jsonObject in mutableEntry.value) {
                        if ("XimaTen" == jsonObject.optString("elementType")) {
                            val jsonArr = jsonObject.optJSONArray("subElements")
                            if (jsonArr != null) {
                                val size = jsonArr.length()
                                for (i in size - 1 downTo 0) {
                                    val jsonItem = jsonArr.optJSONObject(i)
                                    if (jsonItem != null && jsonItem.optLong("refId") == trackId) {
                                        jsonItem.optJSONObject("extraInfo")?.put("isLike", isLikeNow)
                                    }
                                }
                            }
                        } else if (jsonObject.optLong("refId") == trackId) {
                            jsonObject.optJSONObject("extraInfo")?.put("isLike", isLikeNow)
                            found = true
                        }
                    }
                }
                savePlayList()
                if (found && isLikeNow) {
                    val fragment = mQuickListenFragmentWR?.get()
                    val tabId = fragment?.curTabId ?: -1L
                    val listData = cache(tabId)
                    val cardIndex = getCardIndex(listData, trackId)
                    val card = listData?.getOrNull(cardIndex)
                    if (fragment != null && tabId > 0 && cardIndex >= 0 && card != null) {
                        val ubtV2 = card.optJSONObject("ubtV2")?.optString("source") ?: ""
                        recommendQuickListen(fragment, tabId, trackId, 2, ubtV2)
                    }
                }
            }.onFailure { it.printStackTrace() }
        }

        TrackCollectManager.getInstance().addListener { collect, id ->
            kotlin.runCatching {
                for (mutableEntry in mCacheMap) {
                    for (jsonObject in mutableEntry.value) {
                        if (jsonObject.optLong("refId") == id) {
                            jsonObject.optJSONObject("extraInfo")?.put("isCollect", collect)
                        }
                    }
                }
                savePlayList()
            }.onFailure { it.printStackTrace() }
        }
    }

    var mQuickListenFragmentWR: WeakReference<IQuickListenTabFragment>? = null
        private set

    fun setQuickListenFragment(frag: IQuickListenTabFragment) {
        mQuickListenFragmentWR = WeakReference<IQuickListenTabFragment>(frag)
    }

    private var mCallback: WeakReference<ICallback>? = null
    fun setCallback(callback: ICallback) {
        mCallback = WeakReference<ICallback>(callback)
    }

    private fun listChange() {
        // 列表同步
        if (!XimaTenDataUtil.isXimaTenPlayList()) {
            logToFile(TAG, "listChange-0")
            return
        }
        val quickListenTabId = XimaTenDataUtil.getQuickListenTabId()
        if (quickListenTabId <= 0) {
            logToFile(TAG, "listChange-0-1")
            return
        }
        if (mCacheMap.size <= 0) {
            logToFile(TAG, "listChange-1")
            return
        }
        val cacheList = mCacheMap[quickListenTabId]
        if (cacheList == null || cacheList.size <= 0) {
            logToFile(TAG, "listChange-2")
            return
        }
        val lastJson = cacheList[cacheList.size - 1]
        val refId = when (lastJson.optString("elementType")) {
            "XimaTen" -> {
                val arr = lastJson.optJSONArray("subElements")
                if (arr != null && arr.length() > 0) {
                    arr.optJSONObject(arr.length() - 1)?.optLong("refId") ?: 0L
                } else {
                    0L
                }
            }
            "TrackCollection" -> {
                val arr = lastJson.optJSONArray("subElements")
                if (arr != null && arr.length() > 0) {
                    arr.optJSONObject(arr.length() - 1)?.optLong("refId") ?: 0L
                } else {
                    0L
                }
            }
            else -> lastJson.optLong("refId", 0L)
        }
        if (refId <= 0) {
            logToFile(TAG, "listChange-3")
            return
        }
        if ("$refId" != ximaTenPlayListMmkv.getString(QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS_KEY)) {
            logToFile(TAG, "listChange-4")
            return
        }
        runCatching {
            val jsonArr =
                JSONArray(ximaTenPlayListMmkv.getString(QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS))
            val list = mutableListOf<JSONObject>()
            for (i in 0 until jsonArr.length()) {
                list.add(jsonArr.optJSONObject(i))
            }
            cacheData(list, true, quickListenTabId)
            mCallback?.get()?.playListChange(list, quickListenTabId)
            logToFile(TAG, "listChange-5 ${list.size}")
        }.onFailure {
            it.printStackTrace()
            logToFile(TAG, "listChange-6 ${Log.getStackTraceString(it)}")
        }
        ximaTenPlayListMmkv.removeByKey(QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS_KEY)
        ximaTenPlayListMmkv.removeByKey(QUICK_LISTEN_CONTENT_DATA_PLAY_PROCESS)
    }

    private var mPauseByUser: Pair<Long, Boolean>? = null
    fun playOrPause(targetPause: Boolean, reason: String? = null) {
        logToFile(TAG, "playOrPause targetPause=$targetPause $reason")
        if (targetPause) {
            val pair = Pair(System.currentTimeMillis(), true)
            mPauseByUser = pair
            ximaTenPlayListMmkv.saveString(QUICK_LISTEN_PAUSE_STATUS, "${pair.first}-${pair.second}")
        } else {
            mPauseByUser = null
            ximaTenPlayListMmkv.removeByKey(QUICK_LISTEN_PAUSE_STATUS)
        }
    }

    fun pauseByUser(): Boolean {
        kotlin.runCatching {
            if (ximaTenPlayListMmkv.containsKey(QUICK_LISTEN_PAUSE_STATUS)) {
                val result = ximaTenPlayListMmkv.getString(QUICK_LISTEN_PAUSE_STATUS)
                return result.contains("-true")
            } else {
                return false
            }
        }.onFailure { it.printStackTrace() }
        return false
    }

    fun getInitTab(forcePlaying: Boolean? = null): Long {
        val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
        var tabId = MMKVUtil.getInstance().getLong(KEY_QUICK_LISTEN_LAST_TAB_ID, -1L)
        var checkPlaying = false
        if (forcePlaying != null) {
            checkPlaying = forcePlaying
        } else if (playerManager.isPlaying) {
            checkPlaying = true
        }
        if (checkPlaying && playerManager.isQuickListen && tabId == playerManager.quickListenTabId) {
            return tabId
        } else {
            val lastTime = getLastTabAccessTime(tabId)
            if (DateTimeUtil.isAnotherDay(lastTime)) {
                tabId = -1L
            }
        }
        return tabId
    }

    fun getLastTabAccessTime(tabId: Long): Long {
        val tab = mTabList.find { it.optLong(TAB_ID, -1L) == tabId }
        if (tab == null) {
            return -1L
        }
        val lastSaveTime = tab.optLong(TAB_ACCESS_TIME)
        return lastSaveTime
    }

    fun cache(tabId: Long) = mCacheMap[tabId]

    fun cacheData(list: List<JSONObject>, append: Boolean, tabId: Long, deleteTrackId: List<Long>? = null) {
        logToFile(TAG, "cacheData ${list.size}, append=$append")
        var callBackListChange = false
        if (!append) {
            mCacheMap.remove(tabId)
        } else if (deleteTrackId?.isNotEmpty() == true) {
            val curTrackId = PlayTools.getCurTrackId(ToolUtil.getCtx())
            // 如果要删除的声音包含正在播放的声音,就需要跳过
            val index = deleteTrackId.indexOf(curTrackId)
            var realDeleTrackList = deleteTrackId
            if (index >= 0) {
                if (index + 1 >= deleteTrackId.size) {
                    realDeleTrackList = null
                } else {
                    realDeleTrackList = deleteTrackId.subList(index + 1, deleteTrackId.size)
                    logToFile(TAG, "removeId 包含当前正在播放的声音的id, 可能是接口返回慢了,用户滚动到下一个了")
                }
            }

            if (realDeleTrackList?.isNotEmpty() == true) {
                removeIds(realDeleTrackList, false)
                callBackListChange = true
            }
        }

        mTabList.removeAll { it.optLong(TAB_ID, -1) == tabId }
        mTabList.add(JSONObject().apply {
            put(TAB_ID, tabId)
            put(TAB_ACCESS_TIME, System.currentTimeMillis())
        })

        var listData = mCacheMap[tabId]
        if (listData == null) {
            listData = mutableListOf()
            mCacheMap[tabId] = listData
        }
        listData.addAll(list)

        savePlayList()

//        if (callBackListChange) {
//            mCallback?.get()?.updatePlayList(listData)
//        }
    }

    fun removeIds(trackIdList: List<Long>?, saveData: Boolean = true) {
        if (trackIdList == null || trackIdList.size <= 0) {
            logToFile(TAG, "removeIds fail")
            return
        }
        for (mutableEntry in mCacheMap) {
            val cacheList = mutableEntry.value
            trackIdList.forEach { trackId->
                cacheList.removeAll { item ->
                    if ("XimaTen" == item?.optString("elementType")) {
                        val jsonArr = item.optJSONArray("subElements")
                        if (jsonArr != null) {
                            val size = jsonArr.length()
                            for (i in size - 1 downTo 0) {
                                val jsonItem = jsonArr.optJSONObject(i)
                                if (jsonItem != null && jsonItem.optLong("refId") == trackId) {
                                    jsonArr.remove(i)
                                }
                            }
                        }
                        false
                    } else if (item?.optLong("refId") == trackId) {
                        true
                    } else {
                        false
                    }
                }
            }
            cacheList.getOrNull(0)?.let {
                if ("XimaTen" == it.optString("elementType")) {
                    if (it.optJSONObject("subElements")?.length() == 0) {
                        cacheList.removeAt(0)
                    }
                }
            }
        }
        if (saveData) {
            savePlayList()
        }
        logToFile(TAG, "removeIds result_size=${mCacheMap.size}, size=${trackIdList.size}")
    }

    fun insertListAndPlay(quickListenTabId: Long, trackId: Long, trackList: List<JSONObject>?, playCallback: IPlayCallback, afterSize: Int = 0, playNowId: Boolean = false): Int {
        if (trackList.isNullOrEmpty()) {
            logToFile(TAG, "insertListAndPlay fail")
            return -1
        }
        val cacheList = mCacheMap[quickListenTabId]
        if (cacheList == null) {
            logToFile(TAG, "insertListAndPlay fail 2")
            return -1
        }

        val trackIdSet = HashSet<Long>()
        trackList.forEach {
            trackIdSet.add(it.optLong("refId"))
        }

        val oriSize = cacheList.size
        cacheList.removeAll {
            trackIdSet.contains(it.optLong("refId"))
        }

        val insetIndex = cacheList.indexOfFirst { it.optLong("refId", -1L) == trackId }
        logToFile(TAG, "insertListAndPlay insetIndex=$insetIndex, size=${trackList.size}, oriSize=${oriSize}, curSize=${cacheList.size}")
        if (insetIndex < 0) {
            logToFile(TAG, "insertListAndPlay fail, trackId not found")
            return -1
        }
        val targetIndex = insetIndex + 1 + afterSize
        cacheList.addAll(targetIndex, trackList)
        savePlayList()

        val targetId = cacheList.getOrNull(targetIndex)?.optLong("refId", -1L) ?: -1L
//        PlayTools.playQuickListenListNew(cacheList, targetIndex, true, quickListenTabId)
        if (playNowId) {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).putTracksAfterTarget(targetId, XimaTenDataUtil.convertDataToTrack(trackList))
            playCallback.playQuickListenList(false, cacheList, insetIndex, trackId)
            logToFile(TAG, "insertListAndPlay success playNowId playIndex=${insetIndex}, targetId=${trackId}")
        } else {
            playCallback.playQuickListenList(true, cacheList, targetIndex, targetId)
            logToFile(TAG, "insertListAndPlay success playIndex=${targetIndex}, targetId=${targetId}")
        }
        return targetIndex
    }

    fun resetTabList(tabId: Long) {
        logToFile(TAG, "resetList $tabId")

        mTabList.removeAll { it.optLong(TAB_ID, -1L) == tabId }
        mCacheMap.remove(tabId)
        savePlayList(false)
    }

    private fun savePlayList(saveData: Boolean = true) {
        val tabList = JSONArray(mTabList)
        var totalData: JSONObject? = null
        if (saveData) {
            totalData = JSONObject()
            for (mutableEntry in mCacheMap) {
                totalData.put("${mutableEntry.key}", JSONArray(mutableEntry.value))
            }
        }
        MyAsyncTask.execute {
            runCatching {
                val oriSize = tabList.length()
                while (tabList.length() > MAX_TABS) {
                    totalData?.remove(tabList.getJSONObject(0).optLong(TAB_ID, -1L).toString())
                    tabList.remove(0)
                }
                val content = tabList.toString()
                ximaTenPlayListMmkv.saveString(XIMA_TEN_CONTENT_DATA_KEYS, content)
                logToFile(TAG, "saveTabIds oriSize=$oriSize, curSize=${mTabList.size}")
                logDetail("saveTabIds", content)
            }.onFailure {
                it.printStackTrace()
                logToFile(TAG, "saveTabIds error ${Log.getStackTraceString(it)}")
            }
            if (saveData) {
                runCatching {
                    val content = totalData.toString()
                    ximaTenPlayListMmkv.saveString(QICK_LISTEN_CONTENT_DATA, content)
                    logToFile(TAG, "savePlayList size=${totalData?.length()}")
                    logDetail("savePlayList", content)
                    logContent("savePlayList", totalData)
                }.onFailure {
                    it.printStackTrace()
                    logToFile(TAG, "savePlayList error ${Log.getStackTraceString(it)}")
                }
            }
        }
    }

    private fun logContent(message: String, cacheMap: HashMap<Long, MutableList<JSONObject>>) {
        if ("1" == ToolUtil.getDebugSystemProperty("debug.xmly.xima_ten_data_print", "-1")) {
            val totalData = JSONObject()
            for (mutableEntry in cacheMap) {
                totalData.put("${mutableEntry.key}", JSONArray(mutableEntry.value))
            }
            logContent(message, totalData)
        }
    }

    private fun logContent(message: String, totalData: JSONObject?) {
        if ("1" == ToolUtil.getDebugSystemProperty("debug.xmly.xima_ten_data_print", "-1")) {
            Logger.d("QuickListenDataManagerDebug", "$message begin")
            totalData?.keys()?.forEach { key ->
                val sb = StringBuilder()
                val itemArr = totalData.optJSONArray(key)
                itemArr?.let {
                    for (i in 0 until it.length()) {
                        val item = it.optJSONObject(i)
                        if (item != null) {
                            var title: String? = null
                            if ("XimaTen" == item?.optString("elementType")) {
                                val jsonArr = item.optJSONArray("subElements")
                                title = jsonArr?.optJSONObject(0)?.optString("title")
                            } else {
                                title = item.optString("title")
                            }
                            if (TextUtils.isEmpty(title)) {
                                Logger.e("QuickListenDataManagerDebug", "title null $item")
                            } else {
                                sb.append(title).append("\n")
                            }
                        } else {
                            Logger.e("QuickListenDataManagerDebug", "item null")
                        }
                    }
                }
                Logger.d("QuickListenDataManagerDebug", "key=$key, value=\n$sb")
            }
            Logger.d("QuickListenDataManagerDebug", "$message end")
        }
    }

    fun findDeleteTrackList(quickListenTabId: Long, removeTrackId: Long, maxCardIndex: Int): List<Long>? {
        val cacheList = mCacheMap[quickListenTabId]
        if (cacheList == null) {
            return null
        }
        if (cacheList.size > maxCardIndex + 1) {
            val trackArr = mutableListOf<Long>()
            for (i in maxCardIndex + 1 until cacheList.size) {
                trackArr.add(cacheList[i].optLong("refId"))
            }
            return trackArr
        }

        return null
    }

    fun negativeXimaTenDataAndPlayXiaoya(tabId: Long, refId: Long, playCallback: IPlayCallback): Int {
        val cacheData = mCacheMap[tabId]
        if (cacheData == null || cacheData.isEmpty()) {
            logToFile(TAG, "negativeXimaTenDataAndPlayXiaoya fail, cacheData is empty")
            return -1
        }
        val deleteIndex = cacheData.indexOfFirst { it.optLong("refId", -1L) == refId }
        if (deleteIndex < 0) {
            logToFile(TAG, "negativeXimaTenDataAndPlayXiaoya fail, refId not found")
            return -1
        }

        var willPlayRefId = cacheData.getOrNull(deleteIndex + 1)?.optLong("refId", -1L) ?: -1L
        if (willPlayRefId <= 0L) {
            willPlayRefId = cacheData.getOrNull(deleteIndex - 1)?.optLong("refId", -1L) ?: -1L
        }
        if (willPlayRefId <= 0L) {
            logToFile(TAG, "negativeXimaTenDataAndPlayXiaoya fail, willPlayRefId not found")
            return -1
        }

        cacheData.removeAt(deleteIndex)

        savePlayList()

        val playIndex = cacheData?.indexOfFirst { it.optLong("refId", -1L) == willPlayRefId }

        if (playIndex == null || playIndex < 0) {
            logToFile(
                TAG,
                "negativeXimaTenDataAndPlayXiaoya fail, dataList is null or playIndex not found"
            )
            return -1
        }
        playCallback.playQuickListenList(true, cacheData, playIndex, willPlayRefId)
//        PlayTools.playQuickListenListNew(cacheData, playIndex, true, tabId)
        logToFile(
            TAG,
            "negativeXimaTenDataAndPlayXiaoya success, willPlayRefId=$willPlayRefId, playIndex=$playIndex"
        )
        return playIndex
    }

    fun negativeXimaTenDataAndPlay(tabId: Long, refId: Long, callback: IPlayCallback) {
        val cacheData = mCacheMap[tabId]
        if (cacheData == null || cacheData.isEmpty()) {
            logToFile(TAG, "negativeXimaTenDataAndPlay fail, cacheData is empty")
            return
        }
        val deleteIndex = cacheData.indexOfFirst { it.optLong("refId", -1L) == refId }
        if (deleteIndex < 0) {
            logToFile(TAG, "negativeXimaTenDataAndPlay fail, refId not found")
            return
        }

        var willPlayRefId = cacheData.getOrNull(deleteIndex + 1)?.optLong("refId", -1L) ?: -1L
        if (willPlayRefId <= 0L) {
            willPlayRefId = cacheData.getOrNull(deleteIndex - 1)?.optLong("refId", -1L) ?: -1L
        }
        if (willPlayRefId <= 0L) {
            logToFile(TAG, "negativeXimaTenDataAndPlay fail, willPlayRefId not found")
            return
        }

        val trackArr = mutableListOf<Long>()
        if (cacheData.size > deleteIndex + 2) {
            for (i in deleteIndex + 2 until cacheData.size) {
                trackArr.add(cacheData[i].optLong("refId"))
            }
        }

        for (i in cacheData.size - 1 downTo deleteIndex) {
            if (i != deleteIndex + 1) {
                cacheData.removeAt(i)
            }
        }

        savePlayList()

        requestXimaTenAndPlayNew(
            false, false, false, trackArr, tabId, QuickListenTabsManager.INSTANCE.isRecommendTab(tabId),
            object : IDataCallBack<List<JSONObject?>?> {
                override fun onSuccess(data: List<JSONObject?>?) {
                    val dataList = cache(tabId)
                    val playIndex = dataList?.indexOfFirst { it.optLong("refId", -1L) == willPlayRefId }

                    if (dataList == null || playIndex == null || playIndex < 0) {
                        logToFile(TAG, "negativeXimaTenDataAndPlay fail, dataList is null or playIndex not found")
                        return
                    }
                    callback.playQuickListenList(true, dataList, playIndex, willPlayRefId)
//                    PlayTools.playQuickListenListNew(dataList, playIndex, true, tabId)
                    logToFile(TAG, "negativeXimaTenDataAndPlay success, willPlayRefId=$willPlayRefId, playIndex=$playIndex")
                }

                override fun onError(code: Int, message: String?) {
                    logToFile(TAG, "negativeXimaTenDataAndPlay error: code=$code, message=$message")
                }
            })
    }

    fun completePlay() {
        val xmPlayManager = XmPlayerManager.getInstance(ToolUtil.getCtx())
        if (xmPlayManager.isQuickListen && xmPlayManager.quickListenTabId > 0) {
            val trackId = PlayTools.getCurTrackId(ToolUtil.getCtx())
            val fragment = mQuickListenFragmentWR?.get()
            val tabId = fragment?.curTabId ?: -1L
            val listData = cache(tabId)
            val cardIndex = getCardIndex(listData, trackId)
            val card = listData?.getOrNull(cardIndex)
            if (fragment != null && tabId > 0 && cardIndex >= 0 && card != null && ("XimaTen" != card.optString("elementType"))) {
                val ubtV2 = card.optJSONObject("ubtV2")?.optString("source") ?: ""
                recommendQuickListen(fragment, tabId, trackId, 0, ubtV2)
            }
        }
    }

    fun share(trackId: Long) {
        val fragment = mQuickListenFragmentWR?.get()
        val tabId = fragment?.curTabId ?: -1L
        val listData = cache(tabId)
        val cardIndex = getCardIndex(listData, trackId)
        val card = listData?.getOrNull(cardIndex)
        if (fragment != null && tabId > 0 && cardIndex >= 0 && card != null) {
            val ubtV2 = card.optJSONObject("ubtV2")?.optString("source") ?: ""
            recommendQuickListen(fragment, tabId, trackId, 3, ubtV2)
        }
    }

    // 0: 播放超过60%  1:订阅  2:点赞  3:分享
    private fun recommendQuickListen(fragment: IQuickListenTabFragment, tabId: Long, refId: Long, behaveType: Int, source: String) {
        if (refId <= 0 || mHasRequestSet.contains(refId)) {
            return
        }
        mHasRequestSet.add(refId)
        val params = hashMapOf(
            "trackId" to "$refId",
            "behaveType" to "$behaveType",
            "source" to source
        )
        CommonRequestM.recommendQuickListen(params, object : IDataCallBack<List<JSONObject>> {
            override fun onSuccess(list: List<JSONObject>?) {
                logToFile(TAG, "recommendQuickListen success list=${list?.size}")
                if (list?.isNotEmpty() == true) {
                    insertListAndPlay(tabId, refId, list, object : IPlayCallback {
                        override fun playQuickListenList(needPlay: Boolean, playList: List<JSONObject>, position: Int, willPlayRefId: Long) {
                            fragment.updateTabListData(playList, position, tabId, needPlay, willPlayRefId)
                        }
                    }, 1, true)
                }
            }

            override fun onError(code: Int, message: String?) {
                logToFile(TAG, "recommendQuickListen error code=$code, message=$message")
            }
        })
    }

    fun getCardIndex(list: List<JSONObject>?, refId: Long): Int {
        list?.forEachIndexed { index, item ->
            if ("XimaTen" == item.optString("elementType")) {
                val jsonArr = item.optJSONArray("subElements")
                if (jsonArr != null) {
                    val size = jsonArr.length()
                    for (i in 0 until size) {
                        val jsonItem = jsonArr.optJSONObject(i)
                        if (jsonItem != null && jsonItem.optLong("refId") == refId) {
                            return index
                        }
                    }
                }
            } else if ("TrackCollection" == item.optString("elementType")) {
                val jsonArr = item.optJSONArray("subElements")
                if (jsonArr != null) {
                    val size = jsonArr.length()
                    for (i in 0 until size) {
                        val jsonItem = jsonArr.optJSONObject(i)
                        if (jsonItem != null && jsonItem.optLong("refId") == refId) {
                            return index
                        }
                    }
                }
            } else {
                if (item.optLong("refId") == refId) {
                    return index
                }
            }
        }
        return -1
    }

    fun getPlayIndex(list: List<JSONObject>?, index: Int): Long {
        val item = list?.getOrNull(index)
        if (item == null) {
            return 0
        }
        if ("XimaTen" == item.optString("elementType")) {
            val firstRefId =
                item.optJSONArray("subElements")?.optJSONObject(0)?.optLong("refId") ?: -1L
            val quickListenCollectIdTrackId =
                QuickListenForPlayProcessUtil.getQuickListenXimaTenId(ToolUtil.getCtx(), firstRefId)
            if (XmPlayerManager.getInstance(ToolUtil.getCtx())
                    .getHistoryPos(quickListenCollectIdTrackId) == PlayerConstants.PLAY_COMPLETE
            ) {
                return firstRefId
            }

            val jsonArr = item.optJSONArray("subElements")
            var playIndex = 0
            if (jsonArr != null) {
                val size = jsonArr.length()
                for (i in 0 until size) {
                    val jsonItem = jsonArr.optJSONObject(i)
                    if (jsonItem != null && jsonItem.optLong("refId") == quickListenCollectIdTrackId) {
                        return quickListenCollectIdTrackId
                    }
                }
            }
            return firstRefId
        } else if ("TrackCollection" == item.optString("elementType")) {
            val firstRefId =
                item.optJSONArray("subElements")?.optJSONObject(0)?.optLong("refId") ?: -1L
            val quickListenCollectIdTrackId =
                QuickListenForPlayProcessUtil.getQuickListenCollectIdTrackId(
                    ToolUtil.getCtx(),
                    "$firstRefId"
                )
            if (XmPlayerManager.getInstance(ToolUtil.getCtx())
                    .getHistoryPos(quickListenCollectIdTrackId) == PlayerConstants.PLAY_COMPLETE
            ) {
                return firstRefId
            }

            val jsonArr = item.optJSONArray("subElements")
            var playIndex = 0
            if (jsonArr != null) {
                val size = jsonArr.length()
                for (i in 0 until size) {
                    val jsonItem = jsonArr.optJSONObject(i)
                    if (jsonItem != null && jsonItem.optLong("refId") == quickListenCollectIdTrackId) {
                        return quickListenCollectIdTrackId
                    }
                }
            }
            return firstRefId
        } else {
            return item.optLong("refId")
        }
    }

    fun getCurQuickListenModel(listenModel: QuickListenModel): QuickListenModel? {
        if (listenModel.isXimaTen()) {
            val trackId = listenModel.subElements?.firstOrNull()?.refId ?: 0
            // 如果当前列表播放完了,就获取合集中的第一集
            val quickListenCollectIdTrackId = QuickListenForPlayProcessUtil.getQuickListenXimaTenId(ToolUtil.getCtx(), trackId)

            listenModel.subElements?.lastOrNull()?.let {ximaTenItem->
                if (ximaTenItem.refId == quickListenCollectIdTrackId) {
                    if (XmPlayerManager.getInstance(ToolUtil.getCtx()).getHistoryPos(ximaTenItem.refId) == PlayerConstants.PLAY_COMPLETE) {
                        return listenModel.subElements.firstOrNull()
                    }
                }
            }

            return listenModel.subElements?.firstOrNull { it.refId == quickListenCollectIdTrackId }
        } else if (listenModel.isTrackCollect()) {
            val collectionId = listenModel.subElements?.firstOrNull()?.refId

            // 如果当前列表播放完了,就获取合集中的第一集
            val quickListenCollectIdTrackId = QuickListenForPlayProcessUtil.getQuickListenCollectIdTrackId(ToolUtil.getCtx(), collectionId.toString())
            if (quickListenCollectIdTrackId > 0) {
                listenModel.subElements?.lastOrNull()?.let {
                    if (quickListenCollectIdTrackId == it.refId) {
                        if (XmPlayerManager.getInstance(ToolUtil.getCtx()).getHistoryPos(it.refId) == PlayerConstants.PLAY_COMPLETE) {
                            return listenModel.subElements.firstOrNull()
                        }
                    }
                }
                return listenModel.subElements?.firstOrNull { it.refId == quickListenCollectIdTrackId }
            }
        } else {
            return listenModel
        }
        return null
    }

    fun logDetail(tag: String?, message: String?) {
        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "$tag: $message")
        }
    }

    fun logToFile(tag: String?, message: String?) {
        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, message)
        }
        Logger.logToFile(tag, message)
    }

    fun requestTabContent(tabId: Long, isRecommend: Boolean, isPreload: Boolean) {
        val useCache: Boolean
        val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
        if (playerManager.isPlaying && playerManager.isQuickListen && tabId == playerManager.quickListenTabId) {
            // 快听声音正在播放，使用缓存
            useCache = true
        } else {
            // 非快听声音，检查是否需要刷新
            useCache = !DateTimeUtil.isAnotherDay(getLastTabAccessTime(tabId))
        }

        requestXimaTenAndPlayNew(useCache, false, true, null, tabId, isRecommend,
            object : IDataCallBack<List<JSONObject?>?> {
                override fun onSuccess(data: List<JSONObject?>?) {
                }

                override fun onError(code: Int, message: String?) {
                }
            }, null, isPreload)
    }

    fun canUseCache(tabId: Long, forcePlaying: Boolean? = null): Boolean {
        var useCache = false
        val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
        var checkPlaying = false
        if (forcePlaying != null) {
            checkPlaying = forcePlaying
        } else if (playerManager.isPlaying) {
            checkPlaying = true
        }
        if (checkPlaying && playerManager.isQuickListen && tabId == playerManager.quickListenTabId) {
            // 快听声音正在播放，使用缓存
            useCache = true
        } else {
            // 非快听声音，检查是否需要刷新
            useCache = !DateTimeUtil.isAnotherDay(getLastTabAccessTime(tabId))
        }
        return useCache
    }

    fun requestXimaTenAndPlayNew(useCache: Boolean, autoPlay: Boolean, refresh: Boolean, deleTrackIds: List<Long>?, tabId: Long, isRecommend: Boolean, callBack: IDataCallBack<List<JSONObject?>?>, itemFragmentPlayCallback: IItemFragmentCallback? = null, isPreload: Boolean? = false) {
        var fromCache = useCache
        Logger.d(TAG, "requestXimaTenAndPlayNew fromCache=$fromCache, autoPlay=$autoPlay, refresh=$refresh, tabId=$tabId")
        val startTime = System.currentTimeMillis()
        if (fromCache) {
            Logger.d(TAG, "fromCache diff1=" + (System.currentTimeMillis() - startTime))
            val data = cache(tabId)
            if (!ToolUtil.isEmptyCollects(data)) {
                val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
                if (autoPlay && !playerManager.isQuickListen) {
                    if (itemFragmentPlayCallback != null) {
                        itemFragmentPlayCallback.play(tabId, true)
                    } else {
                        Logger.d(TAG, "fromCache diff2=" + (System.currentTimeMillis() - startTime))
                        // 设置到播放进程
                        val trackId = getLastTabPlayTrackId(tabId)
                        PlayTools.playQuickListenListWithTrackId(data, trackId, false, tabId)
                        Logger.d(TAG, "fromCache diff3=" + (System.currentTimeMillis() - startTime))
                    }
                }
                callBack.onSuccess(data)
                return
            } else {
                fromCache = false
                resetTabList(tabId)
            }
        }
        if (refresh && isRecommend) {
            MMKVUtil.getInstance().saveString(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_LAST_PAGE_ITEMS, null)
        }
        val params = HashMap<String, Any>()
        params["tabId"] = tabId
        if (isRecommend) {
            if (refresh) {
                params["pageIndex"] = 1
            }
            if (!TextUtils.isEmpty(requestTenData)) {
                params["ximaTenAb"] = requestTenData
            }
            params[DTransferConstants.XIMA_TAB_RECOMMEND] = "1"
        }
        if (deleTrackIds != null && !deleTrackIds.isEmpty()) {
            params["discardItems"] = deleTrackIds
        }

        if (isPreload == true) {
            params["isPreload"] = isPreload
        }

        CommonRequestM.requestXimaTen(refresh, params, object : IDataCallBack<List<JSONObject>?> {
            override fun onSuccess(data: List<JSONObject>?) {
                Logger.d(TAG, "requestXimaTen net-request diff1=" + (System.currentTimeMillis() - startTime))
                if (data == null || data.isEmpty()) {
                    callBack.onSuccess(null)
                    Logger.d(TAG, "requestXimaTenAndPlay data is empty")
                    return
                }
                if (refresh) {
                    resetTabList(tabId)
                }
                // 将缓存数据清掉
                cacheData(data, !refresh, tabId, deleTrackIds)

                Logger.d(TAG, "requestXimaTen net-request diff2=" + (System.currentTimeMillis() - startTime))

                val playManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
                if (ToolUtil.isEmptyCollects(deleTrackIds)) {
                    if (autoPlay) {
                        // 设置到播放进程
                        if (itemFragmentPlayCallback != null) {
                            itemFragmentPlayCallback.play(tabId, true)
                        } else {
                            val trackId = getLastTabPlayTrackId(tabId)
                            PlayTools.playQuickListenListWithTrackId(data, trackId, false, tabId)
                        }
                    } else {
                        // 检查播放进程是否被同步，如果之前同步过的话，那会add到播放进程
                        if (playManager.isQuickListen && tabId == playManager.quickListenTabId) {
                            playManager.addTracksToPlayList(XimaTenDataUtil.convertDataToTrack(data))
                        } else {
                            Logger.logToFile(TAG, "requestXimaTenAndPlay isXimaTenTRack=false")
                        }
                    }
                } else {
                    if (autoPlay) {
                        if (itemFragmentPlayCallback != null) {
                            itemFragmentPlayCallback.play(tabId, true)
                        } else {
                            // 要删除的数据,需要重新设置播放列表
                            val dataNew: List<JSONObject>? = cache(tabId)
                            // 设置到播放进程
                            PlayTools.playQuickListenListNew(
                                dataNew,
                                XmPlayerManager.getInstance(ToolUtil.getCtx()).currentIndex,
                                true,
                                tabId
                            )
                        }
                    }
                }

                Logger.d(TAG, "requestXimaTen net-request diff3=" + (System.currentTimeMillis() - startTime))

                callBack.onSuccess(data)
            }

            override fun onError(code: Int, message: String) {
                callBack.onError(code, message)
            }
        })
    }

    fun getLastTabPlayTrackId(tabId: Long): Long {
        val tab = mTabList.find { it.optLong(TAB_ID, -1L) == tabId }
        if (tab == null) {
            return -1L
        }
        return tab.optLong(TAB_TRACK_ID, -1L)
    }

    fun onPlayStart(trackId: Long) {
        val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
        if (trackId > 0 && playerManager.isQuickListen) {
            val tabId = playerManager.quickListenTabId
            if (tabId > 0) {
                mTabList.removeAll { it.optLong(TAB_ID, -1) == tabId }
                mTabList.add(JSONObject().apply {
                    put(TAB_ID, tabId)
                    put(TAB_ACCESS_TIME, System.currentTimeMillis())
                    put(TAB_TRACK_ID, trackId)
                })
                savePlayList(false)
            }
        }
    }

    // @FIXME (这里需要实现下)
    fun getHomePageTabData(): List<QuickListenTab> {
        return listOf(
            QuickListenTab("推荐", -1),
            QuickListenTab("热点", 1),
            QuickListenTab("历史", 2),
            QuickListenTab("好书精读", 3),
            QuickListenTab("商业化", 4),
            QuickListenTab("热点1", 5),
            QuickListenTab("热点2", 6),
            QuickListenTab("热点3", 7),
            QuickListenTab("热点4", 8),
            QuickListenTab("热点5", 9),
            QuickListenTab("热点6", 10),
        )
    }

}

interface ICallback {
    fun playListChange(list: MutableList<JSONObject>, tabId: Long)
}

interface IItemFragmentCallback {
    fun play(tabId: Long, autoPlay: Boolean)
}