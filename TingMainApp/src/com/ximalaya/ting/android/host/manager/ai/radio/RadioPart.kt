package com.ximalaya.ting.android.host.manager.ai.radio

import androidx.annotation.Keep
import java.io.File

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/4/8
 */
@Keep
data class RadioPart(
    var radioId: String = "",
    var episodeId: String = "",
    var title: String = "",
    var messageId: String = "",
    var partId: String = "",
    var business: String = "",
    var trackId: Long = 0,
    var currentTime: Int = 0,
    var isPlaying: Boolean = false,
    var seekTime: Int = 0,
    var isOnline: Boolean = false,
    var url: String = "",
    var coverUrl: String = "",
    var duration: Int = 0,
    var aigc: Boolean = false
) {
    fun getFilePath(rootPath: String): String {
        return "$rootPath${File.separator}${messageId}${File.separator}${partId}"
    }
}