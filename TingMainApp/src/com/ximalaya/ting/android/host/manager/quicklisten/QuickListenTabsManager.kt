package com.ximalaya.ting.android.host.manager.quicklisten

import android.text.TextUtils
import androidx.core.util.Pair
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.quicklisten.ELEMENT_TYPE_RECOMMEND
import com.ximalaya.ting.android.host.model.quicklisten.QuickListenTab
import com.ximalaya.ting.android.host.model.quicklisten.parseTabs
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil

/**
 * <AUTHOR>
 * @date 2025/7/1 19:23
 */
private const val MMKV_FILE = "quick_listen_tab_file_0707"
private const val KEY_TABS = "quick_listen_tabs_0707"

class QuickListenTabsManager private constructor() {
    companion object {
        val INSTANCE = QuickListenTabsManager()
    }

    private val mTabs = mutableListOf<QuickListenTab>()

    fun init() {
        if (QuickListenTabAbManager.showAIListenTab()) {
            if (ToolUtil.getDebugSystemProperty("debug.xmly.quicklisten_tabs", "-1") == "1") {
                mTabs.addAll(
                    listOf(
                        QuickListenTab("推荐", -1),
                        QuickListenTab("热点", 1),
                        QuickListenTab("历史", 2),
                        QuickListenTab("好书精读", 3),
                        QuickListenTab("商业化", 4),
                        QuickListenTab("热点1", 5),
                        QuickListenTab("热点2", 6),
                        QuickListenTab("热点3", 7),
                        QuickListenTab("热点4", 8),
                        QuickListenTab("热点5", 9),
                        QuickListenTab("热点6", 10),
                    )
                )
            }
            MyAsyncTask.execute {
                val mmkv = MMKVUtil.getInstance(MMKV_FILE)
                val tabsString = mmkv.getString(KEY_TABS, null)
                if (!TextUtils.isEmpty(tabsString)) {
                    val tabs = parseTabs(tabsString)
                    if (tabs != null && tabs.first.isNotEmpty()) {
                        mTabs.clear()
                        mTabs.addAll(tabs.first)
                    }
                }
            }

            if (EasyConfigure.getBoolean("quick_listen_preload_data", false)) {
                requestTabs { listData: List<QuickListenTab>? ->
                    var tabId = QuickListenDataManager.getInstance().getInitTab()
                    if (tabId <= 0) {
                        tabId = listData?.getOrNull(0)?.id ?: -1L
                    }
                    if (tabId <= 0) {
                        QuickListenDataManager.getInstance().logToFile(TAG, "无法提前缓存")
                        return@requestTabs
                    }
                    val tab = listData?.find { it.id == tabId }
                    if (tab == null) {
                        QuickListenDataManager.getInstance().logToFile(TAG, "tabId对应的tab不存在: $tabId")
                        return@requestTabs
                    }
                    QuickListenDataManager.getInstance().logToFile(TAG, "requestTabContent tabId: $tabId")
                    QuickListenDataManager.getInstance()
                        .requestTabContent(tabId, tab.elementType == ELEMENT_TYPE_RECOMMEND, true)
                }
            }
        }
    }

    fun isRecommendTab(tabId: Long): Boolean {
        if (tabId < 0) {
            return false
        }
        val tab = mTabs.find { it.id == tabId }
        if (tab == null) {
            return false
        }
        return tab.elementType == ELEMENT_TYPE_RECOMMEND
    }

    fun requestTabs(callback: ((List<QuickListenTab>?) -> Unit)?) {
        val tabs = mTabs
        if (tabs.isNotEmpty()) {
            if (callback != null) {
                callback(ArrayList(tabs))
            }
            return
        }
        CommonRequestM.requestQuickListenTabs(object : IDataCallBack<Pair<List<QuickListenTab>, String>?> {
            override fun onSuccess(data: Pair<List<QuickListenTab>, String>?) {
                if (data == null || data.first.isNullOrEmpty()) {
                    if (callback != null) {
                        callback(ArrayList(mTabs))
                    }
                    return
                }
                mTabs.clear()
                mTabs.addAll(data.first)
                if (callback != null) {
                    callback(ArrayList(mTabs))
                }
                MyAsyncTask.execute {
                    val mmkv = MMKVUtil.getInstance(MMKV_FILE)
                    mmkv.saveString(KEY_TABS, data.second)
                }
            }

            override fun onError(code: Int, message: String?) {
                if (callback != null) {
                    callback(ArrayList(mTabs))
                }
            }
        })
    }

    fun getTabNameByTabId(tabId: Long): String {
        return mTabs.firstOrNull { it.id == tabId }?.title ?: "喜马快听"
    }
}