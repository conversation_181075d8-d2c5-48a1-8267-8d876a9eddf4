package com.ximalaya.ting.android.host.manager

import android.content.Intent
import android.net.Uri
import androidx.annotation.Keep
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.ai.AgentLottieUrlUtil
import com.ximalaya.ting.android.host.async.AsyncRequest
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ai.AiAgentTipManager
import com.ximalaya.ting.android.host.manager.ai.AiForceLoginManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.playlist.gson
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2024/9/1
 * Description：
 */

const val CHAT_OLD = -1
const val CHAT_XMLY = 1
const val CHAT_XIAOYA = 2

@Keep
data class AgentConfig(
    @SerializedName("matchAgent", alternate = ["agent"])
    val matchAgent: String = "",
    val oldSwitch: String = "",
    val agentContent: AgentContent? = null
) {

    fun isXiaoYaSugValid(): Boolean {
        return agentContent?.sug.isNullOrEmpty().not()
                && agentContent?.orgSug.isNullOrEmpty()
            .not() && agentContent?.sugType.isNullOrEmpty().not()
    }

    fun isBoBoStyleVaild(): Boolean {
        return isLoopAnimValid()
    }

    fun isLoopAnimValid(): Boolean {
        return agentContent?.lottieLoopUrl.isNullOrEmpty()
            .not() && agentContent?.lottieDarkLoopUrl.isNullOrEmpty().not()
    }
}

@Keep
data class AgentContent(
    /**
     * 小雅/波波推荐语
     * */
    var sug: String = "",
    val intent: String = "",
    /**
     * 波波开始动效
     */
    val lottieStartUrl: String = "",
    val lottieDarkStartUrl: String = "",
    /**
     * 波波循环动效
     */
    val lottieLoopUrl: String = "",
    val lottieDarkLoopUrl: String = "",
    /**
     * 小雅原始推荐语》》透传
     * */
    var orgSug: String = "",
    /**
     * 小雅推荐语类型》》》埋点
     * */
    var sugType: String = "",
    /**
     * 波波状态字段》》埋点
     * */
    val statusText: String = "",

    val sugTime: Int = 5,

    val trackId: Long = 0L
)

interface ChildXmlyTipChangeListener {
    fun onStyleChange()

    fun onRoleTypeChange()
}

@OptIn(DelicateCoroutinesApi::class)
object ChildXmlyTipManager {
    val xmRequestId =
        XmRequestIdManager.getInstance(BaseApplication.getMyApplicationContext()).requestId
    const val ACTION_CHANGE_CHAT_XMLY_TYPE = "action_change_chat_xmly_type"
    const val RN_SELECT_AGENT = "rn_select_agent" // 仅包含智能体选择
    const val RN_USE_OLD_SWITCH = "rn_use_old_switch" // 是否强制使用老版语音助手
    const val CHILD_XMLY_AGENT_CONFIG = "child_xmly_agent_config"

    // 废弃
    const val RN_SELECT_TYPE = "rn_select_type" // 包含老版语音助手
    const val ORIGIN_SWITCH_TO_SUPER_XIAOYA = "origin_switch_to_super_xiaoya"

    private var tipType = MMKVUtil.getInstance().getInt(RN_SELECT_AGENT, CHAT_OLD)
    private var hasAbInit = false
    var agentConfig: AgentConfig? = null
    private var changeListeners: MutableSet<ChildXmlyTipChangeListener> = mutableSetOf()

    fun addChangeListener(listener: ChildXmlyTipChangeListener) {
        changeListeners.add(listener)
    }

    fun removeChangeListener(listener: ChildXmlyTipChangeListener) {
        changeListeners.remove(listener)
    }

    const val SOURCE_SEARCH_AI = "source_search_ai"
    const val SOURCE_SEARCH_ICON_ENTRANCE = "searchResultAi"

    init {
        runCatching {
            agentConfig = gson.fromJson(
                MMKVUtil.getInstance().getString(CHILD_XMLY_AGENT_CONFIG, ""),
                AgentConfig::class.java
            )
        }.onFailure {
            dLog("local config 不存在")
        }
        getAgentConfigWithOldUserUpdate()
    }


    /**
     * 小雅/波波初始引导语
     * */
    fun getBubbleGuideLottieUrl(): String {
        val boboStyleValid = isBoBoType() && agentConfig?.isBoBoStyleVaild() == true
        return (if (boboStyleValid) {
            if (BaseFragmentActivity2.sIsDarkMode) agentConfig?.agentContent?.lottieDarkStartUrl else agentConfig?.agentContent?.lottieStartUrl
        } else if (isXiaoYaType()) {
            AgentLottieUrlUtil.xiaoya_guide
        } else {
            null
        }) ?: ""
    }

    /**
     * 小雅/波波循环动效
     * */
    fun getBubbleLoopLottieUrl(): String {
        val boboStyleValid = isBoBoType() && agentConfig?.isBoBoStyleVaild() == true
        return (if (boboStyleValid) {
            if (BaseFragmentActivity2.sIsDarkMode) agentConfig?.agentContent?.lottieDarkLoopUrl else agentConfig?.agentContent?.lottieLoopUrl
        } else if (isXiaoYaType()) {
            AgentLottieUrlUtil.xiaoya
        } else {
            null
        }) ?: ""
    }

    fun getAgentConfigWithOldUserUpdate() {
        hasAbInit = false
        // 旧版本兼容，移除ORIGIN_SWITCH_TO_SUPER_XIAOYA,RN_SELECT_TYPE，更换为RN_SELECT_AGENT , 理论上仅触发一次， 未来可删除
        if (UserInfoMannage.hasLogined() && MMKVUtil.getInstance()
                .containsKey(RN_SELECT_TYPE)
        ) {

            val type = MMKVUtil.getInstance().getInt(RN_SELECT_TYPE, CHAT_OLD)
            val forceOld = type == CHAT_OLD
            dLog("存在历史数据>>> updateAgentConfig forceOld=$forceOld, type=$type")

            MMKVUtil.getInstance().saveBoolean(RN_USE_OLD_SWITCH, forceOld)
            MMKVUtil.getInstance().saveInt(RN_SELECT_AGENT, type)

            MMKVUtil.getInstance().removeByKey(RN_SELECT_TYPE)
            MMKVUtil.getInstance().removeByKey(ORIGIN_SWITCH_TO_SUPER_XIAOYA)

            updateAgentConfig(type, forceOld)
        } else {
            dLog("不存在历史数据>>> getAgentConfig")
            getAgentConfig()
        }
    }

    fun refreshAgentConfig() {
        GlobalScope.launch {
            runCatching {
                AsyncRequest.get<AgentConfig?>(
                    UrlConstants.getInstanse().agentConfigUrl
                )
            }.onSuccess {
                agentConfig?.agentContent?.apply {
                    orgSug = it?.agentContent?.orgSug ?: ""
                    sugType = it?.agentContent?.sugType ?: ""
                    sug = it?.agentContent?.sug ?: ""
                }
                MMKVUtil.getInstance().saveString(CHILD_XMLY_AGENT_CONFIG, GsonUtils.toJson(it))
            }.onFailure {
                dLog("refreshLocalAgentConfig fail >>> $it")
            }
        }
    }

    fun updateAgentConfig() {
        getAgentConfig(null)
    }

    fun updateAgentConfig(
        agentType: Int?,
        forceOld: Boolean,
        callBack: IDataCallBack<Boolean>? = null
    ) {
        val agent = when (agentType) {
            CHAT_XMLY -> "BOBO"
            CHAT_XIAOYA -> "XIAOYA"
            else -> ""
        }
        GlobalScope.launch {
            runCatching {
                val map = mapOf(
                    "agentVoicePreferences" to mutableMapOf(
                        "oldSwitch" to forceOld.toString()
                    ).apply {
                        if (agent.isNotEmpty()) {
                            put("agent", agent)
                        }
                    }
                )
                AsyncRequest.postStr<String>(
                    UrlConstants.getInstanse().updateAgentConfigUrl,
                    GsonUtils.toJson(map)
                )
            }.onSuccess {
                getAgentConfig(callBack)
            }.onFailure {
                getAgentConfig(callBack)
            }
        }
    }

    private fun getAgentConfig(callBack: IDataCallBack<Boolean>? = null) {
        GlobalScope.launch {
            runCatching {
                AsyncRequest.get<AgentConfig?>(
                    UrlConstants.getInstanse().agentConfigUrl,mapOf(
                        "playerTrackId" to PlayTools.getCurTrackId(BaseApplication.getMyApplicationContext()).toString()
                    )
                )
//                if (!hasAbInit) {
//                    AsyncRequest.get<AgentConfig?>(
//                        UrlConstants.getInstanse().agentConfigUrl
//                    )
//                } else {
//                    AsyncRequest.get<AgentConfigWithOutAb?>(
//                        UrlConstants.getInstanse().agentConfigUrlWithoutAb
//                    )?.agentVoicePreferences
//                }
            }.onSuccess {
                hasAbInit = true
                val oldBoboSug = agentConfig?.agentContent?.sug ?: ""
                agentConfig = it
                dLog("getAgentConfig>>> $it")
//                val oldBoboSug = Random.nextInt(1000000).toString()
//                agentConfig = AgentConfig(
//                    matchAgent = "BOBO",
//                    oldSwitch = it?.oldSwitch ?: "",
//                    agentContent = AgentContent(
//                        "速速岁速速速速速速",
//                        "https://aod.cos.tx.xmcdn.com/storages/5608-audiofreehighqps/BD/88/GAqhF9kLndftAACLsAN0Jakl.json",
//                        "https://aod.cos.tx.xmcdn.com/storages/5608-audiofreehighqps/BD/88/GAqhF9kLndftAACLsAN0Jakl.json",
//                        "https://aod.cos.tx.xmcdn.com/storages/8c9c-audiofreehighqps/AE/DA/GKwRIasLndf0AACLswN0Ja1l.json",
//                        "https://aod.cos.tx.xmcdn.com/storages/8c9c-audiofreehighqps/AE/DA/GKwRIasLndf0AACLswN0Ja1l.json"
//                    )
//                )
                MMKVUtil.getInstance().saveString(CHILD_XMLY_AGENT_CONFIG, GsonUtils.toJson(it))
                val forceOld = it?.oldSwitch == "true"
                MMKVUtil.getInstance().saveBoolean(RN_USE_OLD_SWITCH, forceOld)
                val type = when (it?.matchAgent) {
                    "BOBO" -> CHAT_XMLY
                    "XIAOYA" -> CHAT_XIAOYA
                    else -> CHAT_OLD
                }
//                changeTypeByInt(type)
                if (tipType == type) {
                    val needAnim =
                        it?.isBoBoStyleVaild() == true && it.agentContent?.sug != oldBoboSug
                    dLog("${it?.isBoBoStyleVaild()}   ${it?.agentContent?.sug}  $oldBoboSug")
                    if (needAnim) {
                        changeListeners.forEach { s ->
                            s.onStyleChange()
                        }
                    }
                } else {
                    tipType = type
                    MMKVUtil.getInstance().saveInt(RN_SELECT_AGENT, tipType)
                    val intent = Intent(ACTION_CHANGE_CHAT_XMLY_TYPE)
                    LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext())
                        .sendBroadcast(intent)
                    changeListeners.forEach { s ->
                        s.onRoleTypeChange()
                    }
                }
                callBack?.onSuccess(true)
//                val curType = MMKVUtil.getInstance().getInt(RN_SELECT_TYPE, 0)
//                // 非旧版助手模式下，才允许服务端切换智能体
//                if (curType != CHAT_OLD) {
//                    changeTypeByInt(type)
//                }
            }.onFailure {
                XDCSCollectUtil.statErrorToXDCS("ChatXmly", "updateAgentConfig error")
            }
        }
    }

    /**
     * RN页面更换智能体
     * */
    fun changeTypeForRN(role: String?) {
        var typeTemp = CHAT_OLD
        when (role) {
            "bobo" -> {
                typeTemp = CHAT_XMLY
//                MMKVUtil.getInstance().saveInt("origin_switch_to_super_xiaoya", tipType)
            }

            "xiaoya" -> {
//                MMKVUtil.getInstance().saveInt("origin_switch_to_super_xiaoya", tipType)
                typeTemp = CHAT_XIAOYA
            }

            "old" -> {
                typeTemp = CHAT_OLD
            }
        }
        updateAgentConfig(typeTemp, false)
    }

//    private fun changeTypeByInt(type: Int) {
//        if (tipType == type) {
//            return
//        }
//        tipType = type
//        MMKVUtil.getInstance().saveInt(RN_SELECT_AGENT, tipType)
//        val intent = Intent(ACTION_CHANGE_CHAT_XMLY_TYPE)
//        LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext())
//            .sendBroadcast(intent)
//    }

    fun shouldShowTip(): Boolean {
        val tipType = showTipType()
        return tipType == CHAT_XMLY || tipType == CHAT_XIAOYA
    }

    fun isBoBoType(): Boolean {
        return tipType == CHAT_XMLY
    }

    fun isXiaoYaType(): Boolean {
        return tipType == CHAT_XIAOYA
    }

    fun useOldSwitch(): Boolean {
        return MMKVUtil.getInstance().getBoolean(RN_USE_OLD_SWITCH, false)
    }

    fun showTipType(): Int {
        val debugValue = ToolUtil.getSystemProperty("debug.sjc.chat_xmly_tip", "0")
        when (debugValue) {
            "1" -> {
                tipType = CHAT_XMLY
                return tipType
            }

            "2" -> {
                tipType = CHAT_OLD
                return tipType
            }

            "3" -> {
                tipType = CHAT_XIAOYA
                return tipType
            }
        }
        tipType = if (MMKVUtil.getInstance().getBoolean(RN_USE_OLD_SWITCH, false)) {
            CHAT_OLD
        } else {
            MMKVUtil.getInstance().getInt(RN_SELECT_AGENT, CHAT_OLD)
        }
        return tipType
//
//        val chatxmly = "new" == ABTest.getString("chatxmly_enter_ab", "")
//        MMKVUtil.getInstance()
//            .saveBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_PAGE_NEW_CHATXMLY, chatxmly)
//        if (chatxmly) {
//            tipType = CHAT_XMLY
//            return CHAT_XMLY
//        }
//        val xiaoya = "1" == ABTest.getString("xiaoya_mvp_ab", "-1")
//        if (xiaoya) {
//            tipType = CHAT_XIAOYA
//            return CHAT_XIAOYA
//        }
//        tipType = -1
//        return -1
    }

    private fun preLoadListDailySug() {
        GlobalScope.launch {
            runCatching {
                AsyncRequest.get<Any>(UrlConstants.getInstanse().getlistDailySugUrl(), null)
            }
        }
    }

    fun goChildChatXmly(currPage: String?) {
        goChildChatXmlyWithParams(currPage, null)
    }

    fun goChildChatXmlyWithParams(currPage: String?, params: Map<String, String>?) {
        if (AiForceLoginManager.isForceLogin() && !UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
            return
        }
        preLoadListDailySug()
        if (BaseApplication.getMainActivity() is MainActivity) {
            val tipType = showTipType()
            var url =
                if (tipType == CHAT_XIAOYA || SOURCE_SEARCH_AI == currPage) {
                    ConfigureCenter.getInstance().getString(
                        CConstants.Group_toc.GROUP_NAME,
                        "chatXiaoYa_door_iting",
                        "iting://open?msg_type=94&bundle=rn_ai_chat&agent=xiaoya"
                    )
                } else {
                    ConfigureCenter.getInstance().getString(
                        CConstants.Group_toc.GROUP_NAME,
                        "chatxmly_door_iting",
                        "iting://open?msg_type=94&bundle=rn_ai_chat&agent=bobo"
                    )
                }
            if (url.isNullOrEmpty()) {
                return
            }
            if (params.isNullOrEmpty().not()) {
                params?.forEach { (key, value) ->
                    url += "&$key=${if (key == "orgSug" && enableOrgEncode()) Uri.encode(value) else value}"
                }
            }
            NativeHybridFragment.start(
                BaseApplication.getMainActivity() as MainActivity,
                url + "&source=${currPage}",
                false
            )
            if (SOURCE_SEARCH_ICON_ENTRANCE == currPage) {
                XMTraceApi.Trace()
                    .click(69313) // 用户点击时上报
                    .put("currPage", "searchResult")
                    .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                    .put("searchWord", params?.get("orgSug") ?: "")
                    .createTrace()
            } else {
                val trackId =
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId
                        ?: 0L
                val albumId =
                    (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound as? Track)?.album?.albumId
                        ?: 0L
                // 新首页-chatxmly入口  点击事件
                XMTraceApi.Trace()
                    .click(65272) // 用户点击时上报
                    .put(
                        "agent",
                        if (ChildXmlyTipManager.tipType == CHAT_XMLY) "bobo" else "xiaoya"
                    )
                    .put(
                        "currPage",
                        currPage
                    ) // newHomePage 表示新首页 | search 表示搜索中间页 | searchResult 表示搜索结果页
                    .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                    .put(
                        "playStatus",
                        if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying) "1" else "0"
                    ) //  1 表示当前正在播放 0 表示没有在播放
                    .put("currTrackId", trackId.toString()) // 当前播放声音 id
                    .put("currAlbumId", albumId.toString()) // 当前播放声音的专辑 id，能拿到就传，拿不到就不传
                    .put("statusText", agentConfig?.agentContent?.statusText ?: "")
                    .put(
                        "hasDynamicGuide",
                        if (agentConfig?.isBoBoStyleVaild() == true) "1" else "0"
                    )
                    .put("sug", agentConfig?.agentContent?.sug ?: "")
                    .put("sugType", agentConfig?.agentContent?.sugType ?: "")
                    .put("orgSug", agentConfig?.agentContent?.orgSug ?: "")
                    .put("intent", agentConfig?.agentContent?.intent ?: "")
                    .createTrace()
            }
        }
    }

    fun childChatXmlyExplore(currPage: String?) {
        val trackId =
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId
                ?: 0L
        val albumId =
            (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound as? Track)?.album?.albumId
                ?: 0L
        // 新首页-chatxmly入口  控件曝光
        XMTraceApi.Trace()
            .setMetaId(65273)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", currPage)
            .put("agent", if (tipType == CHAT_XMLY) "bobo" else "xiaoya")
            .put(XmRequestIdManager.CONT_ID, "0")
            .put(XmRequestIdManager.CONT_TYPE, "chatxmly")
            .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
            .put(
                "playStatus",
                if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying) "1" else "0"
            ) //  1 表示当前正在播放 0 表示没有在播放
            .put("currTrackId", trackId.toString()) // 当前播放声音 id
            .put("currAlbumId", albumId.toString()) // 当前播放声音的专辑 id，能拿到就传，拿不到就不传
            .put("statusText", agentConfig?.agentContent?.statusText ?: "")
            .put("hasDynamicGuide", if (agentConfig?.isBoBoStyleVaild() == true) "1" else "0")
            .put("sug", agentConfig?.agentContent?.sug ?: "")
            .put("sugType", agentConfig?.agentContent?.sugType ?: "")
            .put("orgSug", agentConfig?.agentContent?.orgSug ?: "")
            .put("intent", agentConfig?.agentContent?.intent ?: "")
            .createTrace()
    }

    fun goAiRadio() {
        runCatching {
            val id =
                (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound as? Track)?.aiRadioId
                    ?: ""
            val episodeId =
                (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound as? Track)?.aiRadioEpisodeId
                    ?: ""
            XMTraceApi.Trace()
                .click(65274) // 用户点击时上报
            val activity = BaseApplication.getMainActivity() as? MainActivity ?: return
            NativeHybridFragment.start(
                activity,
                "${getConfigRadioUri()}&aiRadioId=$id&aiRadioEpisodeId=$episodeId",
                false
            )
        }.onFailure {
            dLog("goAiRadio error ${it.message}")
        }
    }

    fun getAgentSug(): String {
        return agentConfig?.agentContent?.sug ?: "想聊些什么呢"
    }

    fun getAgentSugTime(): Int {
        return agentConfig?.agentContent?.sugTime ?: 5
    }

    fun getIconSize(): Pair<Int, Int> {
        return Pair(
            if (isXiaoYaType()) 42.dp else 34.dp,
            34.dp
        )
    }

    private var aiRadioUri = ""
    private fun getConfigRadioUri(): String {
        if (aiRadioUri.isNotEmpty()) {
            return aiRadioUri
        }
        aiRadioUri = ConfigureCenter.getInstance().getString(
            CConstants.Group_toc.GROUP_NAME, "ai_radio_station_uri",
            "iting://open?msg_type=94&bundle=rn_ai_chat&agent=xiaoya&scene=Search&reuse=true"
        )
        val debugValue = ToolUtil.getDebugSystemProperty("debug.lmm.ai_radio_uri", "")
        if (debugValue.isNullOrEmpty().not()) {
            aiRadioUri += "&__debug=1&ip=$debugValue"
        }
        dLog("radioUri=$aiRadioUri")
        return aiRadioUri
    }

    private fun enableOrgEncode(): Boolean {
        if (ConstantsOpenSdk.isDebug) {
            return true
        }
        return ConfigureCenter.getInstance()
            .getBool(CConstants.Group_android.GROUP_NAME, "key_enable_org_enacode", false)
    }

    private fun dLog(msg: String) {
        Logger.d(AiAgentTipManager::class.simpleName, msg)
    }

    fun traceSugClick() {
        AiAgentTipManager.traceSugClick()
        // 新首页-searchText  点击事件
        XMTraceApi.Trace()
            .click(69239) // 用户点击时上报
            .put("currPage", "newHomePage")
            .apply {
                putCommonTrace(this)
            }
            .createTrace()
    }

    fun eTraceSugShow() {
        AiAgentTipManager.eTraceSugShow()
        // 新首页-searchText  控件曝光
        XMTraceApi.Trace()
            .setMetaId(69240)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newHomePage")
            .apply {
                putCommonTrace(this)
            }
            .createTrace()
    }

    private fun putCommonTrace(trace: XMTraceApi.Trace) {
        val trackId =
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId
                ?: 0L
        val albumId =
            (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound as? Track)?.album?.albumId
                ?: 0L
        trace
            .put("agent", if (tipType == CHAT_XMLY) "bobo" else "xiaoya")
            .put(XmRequestIdManager.CONT_ID, "0")
            .put(XmRequestIdManager.CONT_TYPE, "chatxmly")
            .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
            .put(
                "playStatus",
                if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying) "1" else "0"
            ) //  1 表示当前正在播放 0 表示没有在播放
            .put("currTrackId", trackId.toString()) // 当前播放声音 id
            .put("currAlbumId", albumId.toString()) // 当前播放声音的专辑 id，能拿到就传，拿不到就不传
            .put("statusText", agentConfig?.agentContent?.statusText ?: "")
            .put("hasDynamicGuide", if (agentConfig?.isBoBoStyleVaild() == true) "1" else "0")
            .put("sug", agentConfig?.agentContent?.sug ?: "")
            .put("sugType", agentConfig?.agentContent?.sugType ?: "")
            .put("orgSug", agentConfig?.agentContent?.orgSug ?: "")
            .put("intent", agentConfig?.agentContent?.intent ?: "")
    }
}