package com.ximalaya.ting.android.host.manager.ai.radio


import androidx.annotation.Keep


@Keep
data class EpisodePart(
    val albumId: Int = 0,
    val albumTitle: String = "",
    val business: String = "",
    val finish: String = "",
    val partId: String =  "",
    val playDuration: String = "",
    val playUrl: String = "",
    val text: String = "",
    val title: String = "",
    val trackCover: String = "",
    val trackDuration: Int = 0,
    val trackId: Long = 0,
    val trackIntro: String = "",
    val trackUpdateTime: Long = 0,
    val aigc: Boolean = false
)

@Keep
data class EpisodeInfoData(
    val business: String = "",
    val hasMoreAi: Boolean = false,
    val partList: List<EpisodePart> = listOf(),
    val totalUrlLink: Int = 0
)

