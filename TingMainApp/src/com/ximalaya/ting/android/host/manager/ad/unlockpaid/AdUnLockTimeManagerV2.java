package com.ximalaya.ting.android.host.manager.ad.unlockpaid;

import static com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager.SOURCE_NAME_REWARD_GET_VIP;
import static com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager.SOURCE_NAME_REWARD_GET_VIP_V2;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.ad.manager.AdStateReportManager;
import com.ximalaya.ting.android.ad.manager.HostCommonRtbSortUtil;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FragmentUtil;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.AdTokenManager;
import com.ximalaya.ting.android.host.manager.ad.RewardAgainAdManager;
import com.ximalaya.ting.android.host.manager.ad.ThirdAdLoadParams;
import com.ximalaya.ting.android.host.manager.ad.WebViewPreloadManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdUnlockListenOpenVipEventFragment;
import com.ximalaya.ting.android.host.manager.ad.videoad.HostRewardVideoRtbAdLoadManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.IRewardAdFragment;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBackExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.ImageAdFragment;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoConfigManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoReport;
import com.ximalaya.ting.android.host.manager.ad.videoad.VideoAdFragment;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.AddListenTimeBuilder;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AdStateData;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.ActionConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class AdUnLockTimeManagerV2 {
    private static final String TAG = "AdUnLockVipTrackManager";
    public static final int ERROR_CODE_DEFAULT = 0;
    public static final int ERROR_CODE_OPEN_VIP = 1;

    private static volatile AdUnLockTimeManagerV2 mInstance;

    private int currentAdIndex;

    private List<AdUnLockVipTrackAdvertis> currentAdvertisList = new ArrayList<>();

    private AdUnLockVipTrackAdvertis currentAd;
    private int currentAdId;
    private long currentAdResponseId;

    private int currentRewardTime;
    private long currentAlbumId;
    private long currentTrackId;
    private IAdUnLockStatusCallBack currentCallBack;

    private long adBeginShowTime;

    private static final int MAX_LOAD_TIME = 10000;

    private int maxAdLoadTime = MAX_LOAD_TIME; // 最长加载广告时间为10秒，超时报错

    private long startLoadAdTime; // 开始加载广告的时间
    private long startRequestTime;// 开始请求adx广告的时间

    private List<Long> commentShowTime = new ArrayList<>();
    private boolean isAdRewardVerify = false; // 是否达到奖励发放条件
    private boolean isAdPlayComplete; // 广告是否播放完成
    private int preRewardState ; // 0 未提前发放奖励 1 提前发放奖励成功 2提前发放奖励失败
    private int preRewardTimeMinutes = 0;
    private boolean isPreRewarding ;// 是否正在预发放奖励过程中
    private boolean needNotifyAdCloseWhenPreRewardEnd;
    private boolean isAdRequestOverTime = false;

    private boolean hasNotifyClose = false;

    private boolean hasHandleUserClick = false;

    private LoadingDialog loadingDialog;
    private RewardExtraParams currentExtraParams;
    private RewardExtraParams lastExtraParams;

    private String curSourceName = "";
    private String curStartSource = "";
    private int rewardTime; // 单位分钟
    private int clickRewardTime ;// 点击广告额外奖励的时长，单位分钟
    private int closeTime; // 单位s

    private boolean lastIsPlaying;
    private String cancelToast;
    private String sceneId;
    private String groupId;
    private boolean isEnterRewardVideo;

    private boolean isShowingRewardVideo;

    private  boolean isPausePlayByRewardVideo;
    private String xmRequestId;

    private AdUnLockTimeManagerV2() {
        registerBroadCast();
    }

    private void initConfig(int rewardTimeParam) {
        FreeListenConfigManager.ListenConfig listenConfig = FreeListenConfigManager.getInstance().getListenConfig();
        if (listenConfig != null) {
            rewardTime = listenConfig.rewardDuration / 60;
            clickRewardTime = listenConfig.clickRewardDuration / 60;
            closeTime = listenConfig.adViewDuration;
        } else {
            rewardTime = 60;
            clickRewardTime = 30;
            closeTime = 30;
        }
        if (rewardTimeParam >= 0) {
            rewardTime = rewardTimeParam; // 优先用外部接口传的奖励时间
        }
        sceneId = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SCENE_ID, "");
        groupId = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXP_ID, "");
    }

    public static AdUnLockTimeManagerV2 getInstance() {
        if (mInstance == null) {
            synchronized (AdUnLockTimeManagerV2.class) {
                if (mInstance == null) {
                    mInstance = new AdUnLockTimeManagerV2();
                }
            }
        }
        return mInstance;
    }

    public interface IAdUnLockStatusCallBack{
        void onRewardSuccess(AdUnLockVipTrackAdvertis currentAd, int rewardTimeMinutes);

        void onRewardFail(int code);
    }

    public long getCurrentTrackId() {
        return currentTrackId;
    }

    public long getCurrentAlbumId() {
        return currentAlbumId;
    }

    /**
     * 获取埋点唯一上报ID
     */
    public String getXmRequestId() {
        try {
            if (TextUtils.isEmpty(xmRequestId)) {
                return XmRequestIdManager.getInstance(MainApplication.getMyApplicationContext()).getRequestId();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return xmRequestId;
    }

    public String getCurrentAdId(RewardExtraParams extraParams) {
        try {
            if (extraParams != null && extraParams.getAdvertis() != null) {
                return extraParams.getAdvertis().getAdid() + "";
            }
            if (currentAd != null) {
                return currentAd.getAdid() + "";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public boolean isAdRewardVerify() {
        return isAdRewardVerify;
    }

    /**
     * 广告内部接口：激励视频倒计时结束,满足奖励条件
     */
    public void onCountDownFinish() {
        if(currentAd == null) return;
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SHOW_TIME,
                        AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .albumIdUseStr(currentAlbumId + "")
                        .trackId(currentTrackId+ "")
                        .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                        .adUserType(currentAd.getAdUserType())
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(curSourceName + "")
                        .startSource(curStartSource + "")
                        .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                        .showStyle(currentAd.getShowstyle() + "")
                        .sceneId(sceneId)
                        .groupId(groupId)
                        .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                        .build());
    }

    /**
     *
     * @param rewardTimeParam 奖励的时长，单位分钟，传-1时统一读配置
     * @param sourceName
     * @param callBack
     */
    public void unlockTrack(int rewardTimeParam, String sourceName, @NonNull IAdUnLockStatusCallBack callBack) {
        unlockTrack(rewardTimeParam, null, sourceName, callBack);
    }

    public void unlockTrack(int rewardTimeParam, RewardExtraParams rewardExtraParams, String sourceName, @NonNull IAdUnLockStatusCallBack callBack) {
        unlockTrack(rewardTimeParam, rewardExtraParams, sourceName, "", callBack);
    }

    /**
     *
     * @param rewardTimeParam 奖励的时长，单位分钟，传-1时统一读配置
     * @param sourceName
     * @param rewardExtraParams
     * @param callBack
     */
    public void unlockTrack(int rewardTimeParam, RewardExtraParams rewardExtraParams, String sourceName, String startSource, @NonNull IAdUnLockStatusCallBack callBack) {
        long trackId = 0;
        long albumId = 0;
        PlayableModel playableModel = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrSound();
        if (playableModel != null && playableModel instanceof Track) {
            trackId = playableModel.getDataId();
            Track track = ((Track) playableModel);
            if (track.getAlbum() != null) {
                albumId = track.getAlbum().getAlbumId();
            }
        }
        lastIsPlaying = XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying();
        XmPlayerManager.getInstance(ToolUtil.getCtx()).stopSoundPatchPlay();
        if (lastIsPlaying) {
            isPausePlayByRewardVideo = true;
            XmPlayerManager.getInstance(ToolUtil.getCtx()).pause(PauseReason.Business.AdUnlockPaidManagerV2);
        }
        initConfig(rewardTimeParam);
        unlockTrack(rewardTime, albumId, trackId, sourceName, startSource, callBack, rewardExtraParams, null);
    }

    /**
     * 吊起广告接口
     *
     * @param albumId 专辑id
     * @param trackId 声音id
     * @param callBack 广告状态回调
     */
    private void unlockTrack(int rewardTime, long albumId, long trackId, String sourceName, String startSource,
                            @NonNull IAdUnLockStatusCallBack callBack, RewardExtraParams rewardExtraParams, Activity lastActivity) {
        Log.d(TAG,"unlockTrack  albumId=" + albumId + " trackId=" + trackId);
        hasNotifyClose = false;
        isAdPlayComplete = false;
        isAdRewardVerify = false;
        hasHandleUserClick = false;
        commentShowTime.clear();
        preRewardState = 0;
        isPreRewarding = false;
        needNotifyAdCloseWhenPreRewardEnd = false;
        currentAd = null;
        currentAdId = 0;
        currentAdResponseId = 0;
        currentRewardTime = rewardTime;
        currentAlbumId = albumId;
        currentTrackId = trackId;
        curSourceName = sourceName;
        curStartSource = startSource;
        currentCallBack = callBack;
        Activity topActivity = lastActivity;
        if (topActivity == null) {
            topActivity = MainApplication.getMainActivity();
        }
        if (topActivity == null) {
            if (callBack != null) {
                callBack.onRewardFail(ERROR_CODE_DEFAULT);
            }
            return;
        }
        loadingDialog = new LoadingDialog(topActivity);
        loadingDialog.setTitle("正在努力加载中");
        loadingDialog.showIcon(true);
        lastExtraParams = rewardExtraParams;
        currentExtraParams = lastExtraParams;
        showLoadingDialog();
        getUnlockAdvertis( false, callBack);
    }

    // 请求广告物料
    private void getUnlockAdvertis(boolean duringPlay, IAdUnLockStatusCallBack callBack) {
        currentAdIndex = 0;
        currentAdvertisList.clear();

        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appid", "0");
        requestMap.put("name", AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
        requestMap.put("duringPlay", duringPlay + "");
        requestMap.put("isDisplayedInScreen",true + "");
        requestMap.put("freeListenType", currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? "1" : "0");
        if (!TextUtils.isEmpty(curSourceName)) {
            requestMap.put("sourceName", curSourceName);
        }
        if (!TextUtils.isEmpty(curStartSource)) {
            requestMap.put("startSource", curStartSource);
        }
        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            requestMap.put("pageMode", Advertis.PAGE_MODE_ELDERLY + "");
        } else {
            requestMap.put("pageMode", Advertis.PAGE_MODE_NORMAL + "");
        }
        requestMap.put("lastShowTime", MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getLong(PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_SHOW_TIME, 0) + "");

//        if (currentTrackId != 0) {
//            requestMap.put("trackId", currentTrackId + "");
//        }
//        if (currentAlbumId != 0) {
//            requestMap.put("album", currentAlbumId + "");
//        }
        requestMap.put("uid", UserInfoMannage.getUid() + "");
        requestMap.put("sceneId", sceneId);
        requestMap.put("groupId", groupId);
        String freeListenExt = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXT, "");
        if (!TextUtils.isEmpty(freeListenExt)) {
            try {
                requestMap.put("ext", URLEncoder.encode(freeListenExt, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
//        requestMap.put("ext", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXT, ""));
        maxAdLoadTime = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_MAX_LOAD_TIME, MAX_LOAD_TIME);
        isAdRequestOverTime = false;
        startRequestTime = System.currentTimeMillis();
        CountDownTimer countDownTimer = new CountDownTimer(AdUnLockTimeManagerV2.getInstance().getAdMaxLoadTime(), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未请求完成，认为此次广告加载失败
                isAdRequestOverTime = true;
                dismissLoadingDialog();
                Log.d("---RewardVideoAdManager", "getUnlockAdvertis timeout");
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 loadVideo fail request ad timeout");
                notifyAdClose(null, false);
            }
        };
        countDownTimer.start();
        AdRequest.getVipFreeAd(requestMap, new IDataCallBack<List<AdUnLockVipTrackAdvertis>>() {
            @Override
            public void onSuccess(@Nullable List<AdUnLockVipTrackAdvertis> object) {
                if (isAdRequestOverTime) {
                    return;
                }
                countDownTimer.cancel();
                if (object == null || object.size() == 0) {
                    FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 getUnlockAdvertis fail return null");
                    dismissLoadingDialog();
                    notifyAdClose(null ,false);
                    return;
                }
                AdUnlockUtil.updateAd(object);
                currentAdvertisList.addAll(object);
                xmRequestId = XmRequestIdManager.getInstance(MainApplication.getMyApplicationContext()).getRequestId();
                if(HostCommonRtbSortUtil.rtbRewardAdEnable(object)) {
                    maxAdLoadTime = MAX_LOAD_TIME - (int)(System.currentTimeMillis() - startRequestTime);
                    loadVideoAdParallel(object, 7000, true);
                } else if (object.get(0).isParallelMode()) {
                    int parallelRequestAllTimeOut = object.get(0).getSdkParallelRequestAllTimeOut();
                    if (parallelRequestAllTimeOut == 0) {
                        parallelRequestAllTimeOut = 10;// 兜底10s
                    }
                    maxAdLoadTime = parallelRequestAllTimeOut * 1000 - (int) (System.currentTimeMillis() - startRequestTime);
                    int parallelTime = object.get(0).getParallelTime() * 1000;
                    if (parallelTime == 0) {
                        parallelTime = 2000; // 兜底1秒
                    }
                    loadVideoAdParallel(object, parallelTime, false);
                } else {
                    maxAdLoadTime = maxAdLoadTime - (int)(System.currentTimeMillis() - startRequestTime);
                    loadVideoAd();
                }
            }

            @Override
            public void onError(int code, String message) {
                if (isAdRequestOverTime) {
                    return;
                }
                countDownTimer.cancel();
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 getUnlockAdvertis fail onError");
                notifyAdClose(null, false);
            }
        });
    }

    private void loadVideoAdParallel(List<AdUnLockVipTrackAdvertis> advertisList, int parallelMaxTime, boolean rtbEnable) {
        Log.d("---RewardVideoAdManager", "loadVideoAdParallel parallelMaxTime =" + parallelMaxTime + " maxLoadTime = " + maxAdLoadTime);
        startLoadAdTime = System.currentTimeMillis();
        if (currentCallBack == null) {
            return;
        }
        IVideoAdStatueCallBackExt videoAdStatueCallBack =  new IVideoAdStatueCallBackExt() {
            @Override
            public void onRewardVerify() {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onRewardVerify");
                Log.d(TAG, "onRewardVerify");
                isAdRewardVerify = true;
                preloadInspireAd();
                reportRewardVerify();
                AdStateReportManager.getInstance().onRewardVerify(currentAd, AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
            }

            boolean isAdClicked = false;
            @Override
            public void onAdLoad(AbstractThirdAd abstractThirdAd) {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdLoad");
                if(currentAd == null) return;
                if (AdUnlockUtil.isWebAd(currentExtraParams)) {
                   // 加载开始时关闭加载中弹窗
                    dismissLoadingDialog();
                }
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        currentAd, AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                .sdkType(AdManager.getSDKType(currentAd) + "")
                                .dspPositionId(currentAd.getDspPositionId())
                                .uid(UserInfoMannage.getUid() + "")
                                .albumIdUseStr(currentAlbumId + "")
                                .adUserType(currentAd.getAdUserType())
                                .trackId(currentTrackId+ "")
                                .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                .sourceName(curSourceName + "")
                                .startSource(curStartSource + "")
                                .showStyle(currentAd.getShowstyle() + "")
                                .sceneId(sceneId)
                                .groupId(groupId)
                                .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                .build());
                //曝光时机预加载
                WebViewPreloadManager.getInstance().preloadWhenAdExposure(currentAd);
            }

            @Override
            public void onAdLoadError(int code, String message) {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdLoadError code= " + code + " message =" + message);
                if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, true);
                } else {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, false);
                }
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 onAdLoadError");
                notifyAdClose(null, false);
            }

            @Override
            public void onAdPlayStart() {
                RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, true, false);
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdPlayStart");
                dismissLoadingDialog();
                adBeginShowTime = System.currentTimeMillis();
                isShowingRewardVideo = true;
                if (currentAd != null) {
                    AdManager.adRecord(MainApplication.getMyApplicationContext(),
                            currentAd, AdReportModel.newBuilder(
                                            AppConstants.AD_LOG_TYPE_SHOW_OB,
                                            AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                    .sdkType(AdManager.getSDKType(currentAd) + "")
                                    .dspPositionId(currentAd.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .albumIdUseStr(currentAlbumId + "")
                                    .trackId(currentTrackId + "")
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                    .sourceName(curSourceName + "")
                                    .startSource(curStartSource + "")
                                    .showStyle(currentAd.getShowstyle() + "")
                                    .sceneId(sceneId)
                                    .groupId(groupId)
                                    .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                    .build());
                }
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveLong(PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_SHOW_TIME, System.currentTimeMillis());
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveBoolean(PreferenceConstantsInHost.KEY_REWARD_VIDEO_HAS_USE, true);
                if (!TextUtils.isEmpty(curSourceName) &&
                        (curSourceName.contains(FreeListenConfigManager.PLAY_PAGE_DIALOG) || curSourceName.contains("rewardAgainDialog")
                                || curSourceName.contains(FreeListenConfigManager.AUTO_PLAY_PAGE_DIALOG) || curSourceName.contains(FreeListenConfigManager.AUTO_PLAY_PAGE_POP_DIALOG))) {
                    // 播放页入口观看激励视频，无论失败或者成功放回时不请求二曝贴片
                    isEnterRewardVideo = true;
                }
            }

            @Override
            public void onAdVideoClick(boolean isAutoClick, int clickAreaType, AdManager.IHasOpenOtherApp goMyWebCallBack) {
                if (isAdClicked && AdManager.isThirdAd(currentAd)) {
                    return;
                }
                if (currentAd == null) {
                    return;
                }
                AdReportModel.Builder builder =
                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                .sdkType(AdManager.getSDKType(currentAd) + "")
                                .dspPositionId(currentAd.getDspPositionId())
                                .uid(UserInfoMannage.getUid() + "")
                                .showStyle(currentAd.getShowstyle() + "")
                                .albumIdUseStr(currentAlbumId + "")
                                .trackId(currentTrackId + "")
                                .autoPoll(isAutoClick ? 2 : 1)
                                .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                .sceneId(sceneId)
                                .groupId(groupId)
                                .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                .startSource(curStartSource + "")
                                .sourceName(curSourceName + "");
                if (!AdManager.isThirdAd(currentAd) && !isAutoClick) {
                    // 横版喜马广告增加点击区域参数区分
                    builder.clickAreaType(clickAreaType);
                }
                if (AdManager.isThirdAd(currentAd) || currentAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT
                        || clickAreaType == 5) {
                    builder.ignoreTarget(true)
                            .onlyClickRecord(true);
                }
                // 点击样式需要做去重点击上报，防止触发三方反作弊机制
                if (isAdClicked && !isAutoClick && currentExtraParams != null &&
                        (currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK || currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK)) {
                    builder.onlyGotoClickNoRecord(true);
                }
                AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), currentAd,
                        goMyWebCallBack, builder.build());
                if (!isAutoClick) {
                    isAdClicked = true;
                }
            }

            @Override
            public void onAdClose(boolean isCustomCloseBtn) {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdClose isAdRewardVerify = " + isAdRewardVerify);
                // 三方activity finish的时候会调用该方法
                if (AdManager.isThirdAd(currentAd) && !hasHandleUserClick) {
                    if (isAdRewardVerify) {
                        // 已达到奖励条件，可解锁
                        notifyAdClose(null, true);
                    } else {
                        // 未达到奖励条件，不可解锁
                        notifyAdCancel();
                    }
                }
            }

            @Override
            public void onAdPlayComplete() {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdPlayComplete");
                int rewardType = AdUnlockUtil.getRewardType(currentAd);
                if (!AdManager.isThirdAd(currentAd) && rewardType != AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK && rewardType != AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
                    // 非点击样式，完播可以发放奖励
                    isAdRewardVerify = true;
                }
                isAdPlayComplete  = true;
                if(currentAd != null) {
                    AdManager.adRecord(MainApplication.getMyApplicationContext(),
                            currentAd, AdReportModel.newBuilder(
                                            AppConstants.AD_LOG_TYPE_SHOW_OB,
                                            AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                    .sdkType(AdManager.getSDKType(currentAd) + "")
                                    .dspPositionId(currentAd.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .albumIdUseStr(currentAlbumId + "")
                                    .trackId(currentTrackId + "")
                                    .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                                    .playFinish("1")
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                    .sourceName(curSourceName + "")
                                    .startSource(curStartSource + "")
                                    .showStyle(currentAd.getShowstyle() + "")
                                    .sceneId(sceneId)
                                    .groupId(groupId)
                                    .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                    .build());
                }
            }

            @Override
            public void onAdPlayError(int code, String message) {
                isAdRewardVerify = true;
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdPlayError code =" + code + " msg = " + message);
                if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, true);
                } else {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, false);
                }
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 onAdPlayError");
                if (!AdManager.isThirdAd(currentAd)) {
                    notifyAdClose(null, false);
                }
            }

            @Override
            public View.OnClickListener getCloseClickListener(Activity rewardActivity) {
                return new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Logger.logToFile("rewardVideo", "CloseClickListener click" +
                                " rewardActivity =" + rewardActivity + " isValid =" + ToolUtil.activityIsValid(rewardActivity) + " view =" + v);
                        hasHandleUserClick = true;
                        if (v == null) {
                            return;
                        }
                        dismissLoadingDialog();
                        if (v.getId() == R.id.host_reward_count_down) {
                            // 10秒倒计时已结束，可获取解锁权限
                            notifyAdClose(rewardActivity, true);
                        } else if (v.getId() == R.id.host_reward_close_real) {
                            if (isAdRewardVerify) {
                                // 视频已播放完成，可解锁
                                notifyAdClose(rewardActivity, true);
                            } else {
                                // 视频未播放完，且10秒倒计时未结束，不可解锁
                                finishAdActivityOrFragment(rewardActivity);
                                notifyAdCancel();
                            }
                        } else if (v.getId() == R.id.host_reward_vip){
                            if (currentAd != null && !TextUtils.isEmpty(currentAd.getVipPaymentLink()) && BaseApplication.getMainActivity() instanceof MainActivity) {
                                if (!(rewardActivity instanceof MainActivity)) {
                                    if (MainApplication.getMainActivity() instanceof MainActivity) {
                                        ((MainActivity) MainApplication.getMainActivity())
                                                .startFragment(AdUnlockListenOpenVipEventFragment.newInstance(currentExtraParams));
                                    }
                                    finishAdActivityOrFragment(rewardActivity);
                                }
                                NativeHybridFragment.start((MainActivity) BaseApplication.getMainActivity(), currentAd.getVipPaymentLink(), true);
                            }
                        }
                    }
                };
            }
        };
        long startRequestTime = System.currentTimeMillis();
        RewardVideoAdManager.IRewardAdLoadCallBack iRewardAdLoadCallBack = new RewardVideoAdManager.IRewardAdLoadCallBack() {
            @Override
            public void onAdLoadSuccess(AbstractRewardVideoAd rewardVideoAd) {
                Log.d("---RewardVideoAdManager", "onAdLoadSuccess rewardVideoAd" + rewardVideoAd);
                if (rewardVideoAd == null) {
                    dismissLoadingDialog();
                    FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 loadVideo fail loading timeout");
                    notifyAdClose(null, false);
                    return;
                }
                maxAdLoadTime = maxAdLoadTime - (int) (System.currentTimeMillis() - startRequestTime);
                realShowRewardAd(rewardVideoAd, videoAdStatueCallBack);
            }

            @Override
            public void onAdLoadError(int code, String message) {
                RewardVideoReport.reportAllFinish(null, System.currentTimeMillis() - startRequestTime, false, false);
                Log.d("---RewardVideoAdManager", "onAdLoadError message = " + message + " code = " + code);
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 loadVideo fail loading timeout");
                notifyAdClose(null, false);
            }
        };
        if (rtbEnable) {
            ThirdAdLoadParams thirdAdLoadParams = new ThirdAdLoadParams(AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION, startRequestTime);
            thirdAdLoadParams.setDspRequestTimeMs(Math.min(parallelMaxTime, maxAdLoadTime));
            HostRewardVideoRtbAdLoadManager.loadAd(advertisList, thirdAdLoadParams, iRewardAdLoadCallBack, videoAdStatueCallBack);
        } else {
            RewardVideoAdManager.getInstance().loadRewardVideoAdParallel(advertisList,
                    iRewardAdLoadCallBack, Math.min(parallelMaxTime, maxAdLoadTime), videoAdStatueCallBack);
        }

    }

    private void realShowRewardAd(AbstractRewardVideoAd rewardVideoAd, IVideoAdStatueCallBackExt videoAdStatueCallBack) {
        Log.d("---RewardVideoAdManager", " realShowRewardAd maxLoadTime = " + maxAdLoadTime);
        currentAd = (AdUnLockVipTrackAdvertis) rewardVideoAd.getAdvertis();
        if (currentAd == null){
            dismissLoadingDialog();
            FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 realShowRewardAd fail currentAd null");
            notifyAdClose(null, false);
            return;
        }
        currentAdId = currentAd.getAdid();
        currentAdResponseId = currentAd.getResponseId();
        RewardExtraParams extraParams;
        if (lastExtraParams != null) {
            extraParams = lastExtraParams;
        } else {
            extraParams = new RewardExtraParams();
        }
        extraParams.setPositionName(AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
        extraParams.setSourceName(curSourceName);
        extraParams.setRewardPageStatusCallBack(new RewardExtraParams.IRewardPageStatusCallBack() {
            @Override
            public void onPageResume(Activity activity, int source) {
                if (source == RewardExtraParams.IRewardPageStatusCallBack.SOURCE_FROM_OPEN_VIP_LISTENER_PAGE){
                    notifyAdCancel(true);
                } else if (UserInfoMannage.isVipUser()) {
                    finishAdActivityOrFragment(activity);
                    notifyAdCancel(true);
                }
            }

            @Override
            public void onRewardVerify() {
                isAdRewardVerify = true;
                preloadInspireAd();
                preReward();
                reportRewardVerify();
            }

            @Override
            public void onCommentShow(long commentTime) {
                commentShowTime.add(commentTime);
            }
        });
        extraParams.setCloseable(true);
        if (currentAd.getUnlockTimeV2() != 0) {
            extraParams.setCanCloseTime(currentAd.getUnlockTimeV2());
        } else {
            extraParams.setCanCloseTime(closeTime);
        }
        extraParams.setRewardTime(rewardTime);
        extraParams.setExtraClickRewardTime(clickRewardTime);
        extraParams.setVideoPlayOverTime(currentAd.getVideoDuration());
        extraParams.setXmVideoAdvertisModel(currentAd, AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
        int rewardType = AdUnlockUtil.getRewardType(currentAd);
        if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 && rewardType == AdUnlockUtil.REWARD_TYPE_DURATION_AND_CLICK) {
            // 全天领时长权益不支持时长点击叠加样式
            rewardType = AdUnlockUtil.REWARD_TYPE_DURATION;
        }
        extraParams.setUnlockType(rewardType);
        extraParams.setShakeEnable(AdUnlockUtil.isShakeEnable(currentAd, rewardType));
        currentExtraParams = extraParams;
        if (rewardType == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK || rewardType == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
            if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 6) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP);
            } else if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 8) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW);
            } else {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2);
            }
        } else {
            if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 6) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_VIP);
            } else if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 8) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW);
            } else {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_V2);
            }
        }
        initCancelToastAndConfigPosition();
        RewardVideoAdManager.getInstance().showRewardVideoAdReal(rewardVideoAd, BaseApplication.getMainActivity(), extraParams, videoAdStatueCallBack);
    }

    private void initCancelToastAndConfigPosition() {
        String realPositionName = "";
        if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 6) {
            realPositionName = RewardVideoConfigManager.POSITION_NAME_INCENTIVE_DURATION_VIP;
        } else if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 8) {
            realPositionName = RewardVideoConfigManager.POSITION_NAME_INCENTIVE_DURATION_FREE_AD;
        } else {
            if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5) {
                if (currentExtraParams.getRewardInfoType() == AddListenTimeBuilder.IRewardDurType.REWARD_TOMORROW_ALL_DAY) {
                    realPositionName = RewardVideoConfigManager.POSITION_NAME_INCENTIVE_DURATION_ALL_DAY_TOMORROW;
                } else {
                    realPositionName = RewardVideoConfigManager.POSITION_NAME_INCENTIVE_DURATION_ALL_DAY;
                }
            } else {
                realPositionName = RewardVideoConfigManager.POSITION_NAME_INCENTIVE_DURATION_UNLOCK_TIME;
            }
        }
        if (currentExtraParams != null) {
            currentExtraParams.setConfigPositionName(realPositionName);
        }
        cancelToast = RewardVideoConfigManager.getInstance().getFailToast(realPositionName);
        if (currentExtraParams != null && !TextUtils.isEmpty(currentExtraParams.getRewardFailToast())) {
            cancelToast = currentExtraParams.getRewardFailToast();
        }
        if (currentExtraParams != null && currentExtraParams.getRewardInfoType() == AddListenTimeBuilder.IRewardDurType.AUTO_VIDEO) {
            // 失败提示由上层回调根据具体情况展示
            cancelToast = "";
        }
    }

    private void loadVideoAd() {
        if (currentCallBack == null) {
            return;
        }
        if (currentAdIndex >= currentAdvertisList.size()) {
            dismissLoadingDialog();
            FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 loadVideo fail index max");
            notifyAdClose(null, false);
            return;
        }
        currentAd = currentAdvertisList.get(currentAdIndex);
        if (currentAd == null){
            dismissLoadingDialog();
            FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 loadVideoAd currentAd null");
            notifyAdClose(null, false);
            return;
        }
        currentAdId = currentAd.getAdid();
        currentAdResponseId = currentAd.getResponseId();
        String dspPotionId = currentAd.getDspPositionId();
        int adType = currentAd.getAdtype();
        int showStyle = currentAd.getShowstyle();

        int dspAdType = AdUnlockUtil.getDspAdType(adType, showStyle);
        if (dspAdType == -1) {
            // 类型不匹配
            notifyAdClose(null, false);
            return;
        }
        RewardExtraParams extraParams;
        if (lastExtraParams != null) {
            extraParams = lastExtraParams;
        } else {
            extraParams = new RewardExtraParams();
        }
        extraParams.setPositionName(AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
        extraParams.setSourceName(curSourceName);
        extraParams.setRewardPageStatusCallBack(new RewardExtraParams.IRewardPageStatusCallBack() {
            @Override
            public void onPageResume(Activity activity, int source) {
                if (source == RewardExtraParams.IRewardPageStatusCallBack.SOURCE_FROM_OPEN_VIP_LISTENER_PAGE){
                    notifyAdCancel(true);
                } else if (UserInfoMannage.isVipUser()) {
                    finishAdActivityOrFragment(activity);
                    notifyAdCancel(true);
                }
            }

            @Override
            public void onRewardVerify() {
                isAdRewardVerify = true;
                preloadInspireAd();
                preReward();
                reportRewardVerify();
            }

            @Override
            public void onCommentShow(long commentTime) {
                commentShowTime.add(commentTime);
            }
        });
        extraParams.setCloseable(true);
        if (currentAd.getUnlockTimeV2() != 0) {
            extraParams.setCanCloseTime(currentAd.getUnlockTimeV2());
        } else {
            extraParams.setCanCloseTime(closeTime);
        }
        extraParams.setRewardTime(rewardTime);
        extraParams.setExtraClickRewardTime(clickRewardTime);
        extraParams.setVideoPlayOverTime(currentAd.getVideoDuration());
        extraParams.setXmVideoAdvertisModel(currentAd, AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
        int rewardType = AdUnlockUtil.getRewardType(currentAd);
        if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 && rewardType == AdUnlockUtil.REWARD_TYPE_DURATION_AND_CLICK) {
            // 全天领时长权益不支持时长点击叠加样式
            rewardType = AdUnlockUtil.REWARD_TYPE_DURATION;
        }
        extraParams.setUnlockType(rewardType);
        extraParams.setShakeEnable(AdUnlockUtil.isShakeEnable(currentAd, rewardType));
        currentExtraParams = extraParams;
        if (rewardType == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK || rewardType == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
            if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 6) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP);
            } else if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 8) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW);
            } else {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2);
            }
        } else {
            if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 6) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_VIP);
            } else if (currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 8) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW);
            } else {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_V2);
            }
        }
        initCancelToastAndConfigPosition();
        // SDK请求进行上报
        AdStateReportManager.getInstance().onUnlockRequestBegin(currentAd,
                AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION, new AdStateReportManager.IAdStateBuilderInterceptor() {
                    @Override
                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                        builder.albumId(currentAlbumId + "");
                        builder.trackId(currentTrackId + "");
                        builder.uuid(UUID.randomUUID().toString());
                    }
                });
        startLoadAdTime = System.currentTimeMillis();
        RewardVideoAdManager.getInstance().loadRewardAd(BaseApplication.getMainActivity(), currentAd, dspPotionId, adType, dspAdType,
                extraParams,
                new IVideoAdStatueCallBackExt() {
                    @Override
                    public void onRewardVerify() {
                        Log.d(TAG, "onRewardVerify");
                        isAdRewardVerify = true;
                        preloadInspireAd();
                        reportRewardVerify();
                        AdStateReportManager.getInstance().onRewardVerify(currentAd, AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
                    }

                    boolean isAdClicked = false;
                    @Override
                    public void onAdLoad(AbstractThirdAd abstractThirdAd) {
                        // SDK请求返回
                        new XMTraceApi.Trace()
                                .pageView(32259, "videoAd")
                                .put("currPage", "videoAd")
                                .createTrace();
                        AdStateReportManager.getInstance().onSDKBackSuccess(currentAd,
                                startLoadAdTime, AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(currentAlbumId + "");
                                        builder.trackId(currentTrackId + "");
                                    }
                                });
                        if (AdUnlockUtil.isWebAd(currentExtraParams)) {
                            // 加载开始时关闭加载中弹窗
                            dismissLoadingDialog();
                        }
                        if (currentAd != null) {
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    currentAd, AdReportModel.newBuilder(
                                                    AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                                    AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                            .sdkType(AdManager.getSDKType(currentAd) + "")
                                            .dspPositionId(currentAd.getDspPositionId())
                                            .uid(UserInfoMannage.getUid() + "")
                                            .albumIdUseStr(currentAlbumId + "")
                                            .adUserType(currentAd.getAdUserType())
                                            .trackId(currentTrackId + "")
                                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                            .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                            .sourceName(curSourceName + "")
                                            .startSource(curStartSource + "")
                                            .showStyle(currentAd.getShowstyle() + "")
                                            .sceneId(sceneId)
                                            .groupId(groupId)
                                            .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                            .build());
                            //曝光时机预加载
                            WebViewPreloadManager.getInstance().preloadWhenAdExposure(currentAd);
                        }
                    }

                    @Override
                    public void onAdLoadError(int code, String message) {
                        Log.d(TAG, "onAdLoadError message = " + message + " code = " + code);
                        int status = AdStateReportManager.STATUS_REQUEST_TIMEOUT_OR_ERROR;
                        if (IVideoAdStatueCallBack.ERROR_CODE_NO_AD == code) {
                            status = AdStateReportManager.STATUS_SDK_NO_BACK;
                        }
                        AdStateReportManager.getInstance().onShowFail(currentAd, status, startLoadAdTime,
                                AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(currentAlbumId + "");
                                        builder.trackId(currentTrackId + "");
                                    }
                                });
                        if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                            // 加载超时时不顺延，直接返回错误
                            dismissLoadingDialog();
                            FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 loadVideo fail loading timeout");
                            notifyAdClose(null, false);
                        } else {
                            maxAdLoadTime = maxAdLoadTime - (int)(System.currentTimeMillis() - startLoadAdTime);
                            currentAdIndex++;
                            loadVideoAd();
                        }
                    }

                    @Override
                    public void onAdPlayStart() {
                        dismissLoadingDialog();
                        adBeginShowTime = System.currentTimeMillis();
                        isShowingRewardVideo = true;
                        if (currentAd != null) {
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    currentAd, AdReportModel.newBuilder(
                                                    AppConstants.AD_LOG_TYPE_SHOW_OB,
                                                    AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                            .sdkType(AdManager.getSDKType(currentAd) + "")
                                            .dspPositionId(currentAd.getDspPositionId())
                                            .uid(UserInfoMannage.getUid() + "")
                                            .albumIdUseStr(currentAlbumId + "")
                                            .trackId(currentTrackId + "")
                                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                            .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                            .sourceName(curSourceName + "")
                                            .startSource(curStartSource + "")
                                            .showStyle(currentAd.getShowstyle() + "")
                                            .sceneId(sceneId)
                                            .groupId(groupId)
                                            .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                            .build());
                        }
                        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveLong(PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_SHOW_TIME, System.currentTimeMillis());
                        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveBoolean(PreferenceConstantsInHost.KEY_REWARD_VIDEO_HAS_USE, true);
                        if (!TextUtils.isEmpty(curSourceName) &&
                                (curSourceName.contains(FreeListenConfigManager.PLAY_PAGE_DIALOG) || curSourceName.contains("rewardAgainDialog")
                                        || curSourceName.contains(FreeListenConfigManager.AUTO_PLAY_PAGE_DIALOG) || curSourceName.contains(FreeListenConfigManager.AUTO_PLAY_PAGE_POP_DIALOG))) {
                            // 播放页入口观看激励视频，无论失败或者成功放回时不请求二曝贴片
                            isEnterRewardVideo = true;
                        }
                    }

                    @Override
                    public void onAdVideoClick(boolean isAutoClick, int clickAreaType, AdManager.IHasOpenOtherApp goMyWebCallBack) {
                        if (isAdClicked && AdManager.isThirdAd(currentAd)) {
                            return;
                        }
                        if (currentAd != null) {
                            AdReportModel.Builder builder =
                                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                                    AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                            .sdkType(AdManager.getSDKType(currentAd) + "")
                                            .dspPositionId(currentAd.getDspPositionId())
                                            .uid(UserInfoMannage.getUid() + "")
                                            .showStyle(currentAd.getShowstyle() + "")
                                            .albumIdUseStr(currentAlbumId + "")
                                            .trackId(currentTrackId + "")
                                            .autoPoll(isAutoClick ? 2 : 1)
                                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                            .sceneId(sceneId)
                                            .groupId(groupId)
                                            .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                            .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                            .startSource(curStartSource + "")
                                            .sourceName(curSourceName + "");
                            if (!AdManager.isThirdAd(currentAd) && !isAutoClick) {
                                // 横版喜马广告增加点击区域参数区分
                                builder.clickAreaType(clickAreaType);
                            }
                            if (AdManager.isThirdAd(currentAd) || currentAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT
                                    || clickAreaType == 5) {
                                builder.ignoreTarget(true)
                                        .onlyClickRecord(true);
                            }
                            // 点击样式需要做去重点击上报，防止触发三方反作弊机制
                            if (isAdClicked && !isAutoClick && currentExtraParams != null &&
                                    (currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK || currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK)) {
                                builder.onlyGotoClickNoRecord(true);
                            }
                            AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), currentAd,
                                    goMyWebCallBack, builder.build());
                        }
                        if (!isAutoClick) {
                            isAdClicked = true;
                        }
                    }

                    @Override
                    public void onAdClose(boolean isCustomCloseBtn) {
                        Log.d(TAG, "onAdClose isAdRewardVerify = " + isAdRewardVerify);
                        // 三方activity finish的时候会调用该方法
                        isShowingRewardVideo = false;
                        if (AdManager.isThirdAd(currentAd) && !hasHandleUserClick) {
                            if (isAdRewardVerify) {
                                // 已达到奖励条件，可解锁
                                notifyAdClose(null, true);
                            } else {
                                // 未达到奖励条件，不可解锁
                                notifyAdCancel();
                            }
                        }
                    }

                    @Override
                    public void onAdPlayComplete() {
                        int rewardType = AdUnlockUtil.getRewardType(currentAd);
                        if (!AdManager.isThirdAd(currentAd) && rewardType != AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK && rewardType != AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
                            isAdRewardVerify = true;
                        }
                        isAdPlayComplete  = true;
                        if (currentAd != null) {
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    currentAd, AdReportModel.newBuilder(
                                                    AppConstants.AD_LOG_TYPE_SHOW_OB,
                                                    AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                                            .sdkType(AdManager.getSDKType(currentAd) + "")
                                            .dspPositionId(currentAd.getDspPositionId())
                                            .uid(UserInfoMannage.getUid() + "")
                                            .albumIdUseStr(currentAlbumId + "")
                                            .trackId(currentTrackId + "")
                                            .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                                            .playFinish("1")
                                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                            .freeListenType(currentExtraParams != null && currentExtraParams.getRewardTypeParam() == 5 ? 1 : 0)
                                            .sourceName(curSourceName + "")
                                            .startSource(curStartSource + "")
                                            .showStyle(currentAd.getShowstyle() + "")
                                            .sceneId(sceneId)
                                            .groupId(groupId)
                                            .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                                            .build());
                        }
                    }

                    @Override
                    public void onAdPlayError(int code, String message) {
                        isAdRewardVerify = true;
                        Log.d(TAG, "onAdPlayError code =" + code + " msg = " + message);
                        dismissLoadingDialog();
                        FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 onAdPlayError");
                        if (!AdManager.isThirdAd(currentAd)) {
                            notifyAdClose(null, false);
                        }
                    }

                    @Override
                    public View.OnClickListener getCloseClickListener(Activity rewardActivity) {
                        return new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Logger.logToFile("rewardVideo", "CloseClickListener click" +
                                        " rewardActivity =" + rewardActivity + " isValid =" + ToolUtil.activityIsValid(rewardActivity) + " view =" + v);
                                hasHandleUserClick = true;
                                if (v == null) {
                                    return;
                                }
                                dismissLoadingDialog();
                                if (v.getId() == R.id.host_reward_count_down) {
                                    // 10秒倒计时已结束，可获取解锁权限
                                    notifyAdClose(rewardActivity, true);
                                } else if (v.getId() == R.id.host_reward_close_real) {
                                    if (isAdRewardVerify) {
                                        notifyAdClose(rewardActivity, true);
                                    } else {
                                        // 视频未播放完，且10秒倒计时未结束，不可解锁
                                        finishAdActivityOrFragment(rewardActivity);
                                        notifyAdCancel();
                                    }
                                } else if (v.getId() == R.id.host_reward_vip){
                                    if (currentAd != null && !TextUtils.isEmpty(currentAd.getVipPaymentLink()) && BaseApplication.getMainActivity() instanceof MainActivity) {
                                        if (!(rewardActivity instanceof MainActivity)) {
                                            if (MainApplication.getMainActivity() instanceof MainActivity) {
                                                ((MainActivity) MainApplication.getMainActivity())
                                                        .startFragment(AdUnlockListenOpenVipEventFragment.newInstance(currentExtraParams));
                                            }
                                            finishAdActivityOrFragment(rewardActivity);
                                        }
                                        NativeHybridFragment.start((MainActivity) BaseApplication.getMainActivity(), currentAd.getVipPaymentLink(), true);
                                    }
                                }
                            }
                        };
                    }
                });
    }

    // 结束广告相关的三方activity或者fragment
    private void finishAdActivityOrFragment(Activity adActivity) {
        isShowingRewardVideo = false;
        if (currentExtraParams != null) {
            currentExtraParams.setVipFreeCloseAlertDialog(null);
            if (currentExtraParams.getCountDownTimer() != null) {
                currentExtraParams.getCountDownTimer().cancel();
                currentExtraParams.setCountDownTimer(null);
            }
            try {
                XmBaseDialog closeDialog = currentExtraParams.getVipFreeCloseAlertDialog();
                if (closeDialog != null && closeDialog.isShowing()) {
                    closeDialog.dismiss();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            currentExtraParams = null;
        }
        if (!ToolUtil.activityIsValid(adActivity)) {
            Logger.logToFile("rewardVideo", "finishAdActivityOrFragment isActivityValid == false return");
            return;
        }
        if (adActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) adActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof IRewardAdFragment) {
                ((IRewardAdFragment) currentFragment).finish();
            } else {
                Logger.logToFile("rewardVideo", "finishAdActivityOrFragment currentFragment not IRewardAdFragment");
                currentFragment = FragmentUtil.getShowingFragmentByClass((FragmentActivity) adActivity, ImageAdFragment.class);
                if (currentFragment == null) {
                    currentFragment = FragmentUtil.getShowingFragmentByClass((FragmentActivity) adActivity, VideoAdFragment.class);
                }
                Logger.logToFile("rewardVideo", "finishAdActivityOrFragment retry getCurrentFragment =" + currentFragment);
                if (currentFragment != null && currentFragment instanceof IRewardAdFragment) {
                    ((IRewardAdFragment) currentFragment).finish();
                }
            }
        } else {
            adActivity.finish();
        }
    }

    private void showLoadingDialog() {
        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private void dismissLoadingDialog() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            try {
                loadingDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void doAddTimeRequest(int realRewardTime, int rewardType, boolean isRealFinishTask, int rewardInfoType, boolean isFromPreReward) {
        if (!isFromPreReward && isPreRewarding) {
            // 正在预奖励中，不处理当前请求，等到预奖励结束后再处理当前请求
            needNotifyAdCloseWhenPreRewardEnd = true;
            return;
        }
        AddListenTimeBuilder builder = new AddListenTimeBuilder();
        builder.adId = currentAdId;
        builder.responseId = currentAdResponseId;
        builder.positionName = AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION;
        builder.startTime = adBeginShowTime;
        builder.endTime = System.currentTimeMillis();
        builder.type = rewardType;
        builder.rewardTime = realRewardTime * 60;
        builder.sourceName = curSourceName;
        builder.startSource = curStartSource;
        builder.fallBackReq = isRealFinishTask ? 0 : 1;
        builder.rewardDurType = rewardInfoType;
        if (builder.type == AddListenTimeBuilder.IType.ITING_DIRECT_WATCH_VIDEO) {
            builder.needIgnoreFreeListenV2OpenCheck = true;
        }
        builder.callback = new FreeListenTimeManager.IRewardResultCallback() {
            @Override
            public void onRewardSuccess(FreeListenTimeManager.TimeSyncResult data) {
                reportRewardRequest("result", true, "", isRealFinishTask);
                if (isFromPreReward) {
                    isPreRewarding = false;
                }
                if (isFromPreReward && !needNotifyAdCloseWhenPreRewardEnd) {
                    preRewardState = 1;
                    preRewardTimeMinutes = data.duration / 60;
                    return;
                }
                resetData();
                if (currentCallBack != null) {
                    currentCallBack.onRewardSuccess(currentAd, data.duration / 60);
                }
            }

            @Override
            public void onRewardFail(int code, String message) {
                reportRewardRequest("result", false, message, isRealFinishTask);
                if (isFromPreReward) {
                    isPreRewarding = false;
                }
                if (isFromPreReward && !needNotifyAdCloseWhenPreRewardEnd) {
                    preRewardState = 2;
                    return;
                }
                resetData();
                showFailToast("领取失败，请稍后重试", Toast.LENGTH_LONG);
                if (currentCallBack != null) {
                    currentCallBack.onRewardFail(ERROR_CODE_DEFAULT);
                }
            }
        };
        if (isFromPreReward) {
            isPreRewarding = true;
        }
        reportRewardRequest("request", true, "", isRealFinishTask);
        FreeListenTimeManager.addListenTime(builder);
    }

    private void notifyAdClose(Activity thirdSdkActivity, boolean isRealFinishTask) {
        int realRewardTime = getRealRewardTime();
        int rewardType = currentExtraParams == null ? 1 : currentExtraParams.getRewardTypeParam();
        int rewardInfoType = currentExtraParams == null ? 0 : currentExtraParams.getRewardInfoType();
        boolean noNeedRequest = currentExtraParams != null && !currentExtraParams.isNeedRequest();
        if (currentCallBack == null) {
            Logger.logToFile("rewardVideo", "notifyAdClose callback == null return");
            finishAdActivityOrFragment(thirdSdkActivity);
            return;
        }
        if (!TextUtils.isEmpty(curSourceName) && curSourceName.contains(FreeListenConfigManager.ALBUM_PAGE_DIALOG)) {
            isEnterRewardVideo = true; // 专辑页面观看激励视频，只有解锁成功返回时才屏蔽请求贴片
        }
        finishAdActivityOrFragment(thirdSdkActivity);
        if (hasNotifyClose) {
            return;
        }
        hasNotifyClose = true;
        if (noNeedRequest) {
            // 看多个视频解锁时， 不走服务端接口时长请求，直接回调成功
            resetData();
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    if (currentCallBack != null) {
                        currentCallBack.onRewardSuccess(currentAd, 0);
                    }
                }
            }, 200);
            return;
        }
        if (rewardType == 6 && (curSourceName.equals(SOURCE_NAME_REWARD_GET_VIP)|| curSourceName.equals(SOURCE_NAME_REWARD_GET_VIP_V2))) {
            getFreeListenVip();
        } else if (rewardType == 8 && curSourceName.equals(SOURCE_NAME_REWARD_GET_VIP_V2)) {
            doFreeAdRequest(false);
        } else {
            if (preRewardState == 1) {
                resetData();
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        if (currentCallBack != null) {
                            currentCallBack.onRewardSuccess(currentAd, preRewardTimeMinutes);
                        }
                    }
                }, 200);
            } else if (preRewardState == 2) {
                resetData();
                showFailToast("领取失败，请稍后重试", Toast.LENGTH_LONG);
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        if (currentCallBack != null) {
                            currentCallBack.onRewardFail(ERROR_CODE_DEFAULT);
                        }
                    }
                }, 200);
            } else {
                doAddTimeRequest(realRewardTime, rewardType, isRealFinishTask, rewardInfoType, false);
            }
        }
        reportOnAdClose(true, isRealFinishTask);
    }

    // 提前发放奖励，在倒计时结束或者点击时长满足条件之后
    private void preReward() {
        if (!ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "preRewardOptimize", true)) {
            return;
        }
        if (currentExtraParams != null && !currentExtraParams.isNeedRequest()) {
            return;
        }
        int realRewardTime = getRealRewardTime();
        int rewardType = currentExtraParams == null ? 1 : currentExtraParams.getRewardTypeParam();
        int rewardInfoType = currentExtraParams == null ? 0 : currentExtraParams.getRewardInfoType();
        if (!isNoAddTimeReward(rewardType)) {
            doAddTimeRequest(realRewardTime, rewardType, true, rewardInfoType, true);
        }
    }

    private boolean isNoAddTimeReward(int rewardType) {
//        if (TextUtils.isEmpty(curSourceName)) {
//            return false;
//        }
//        if (rewardType == 6 && (curSourceName.equals(SOURCE_NAME_REWARD_GET_VIP) || curSourceName.equals(SOURCE_NAME_REWARD_GET_VIP_V2))) {
//            getFreeListenVip();
//            return true;
//        } else if (rewardType == 8 && curSourceName.equals(SOURCE_NAME_REWARD_GET_VIP_V2)) {
//            doFreeAdRequest(false);
//            return true;
//        }
        //会员和免广时长奖励
        return rewardType == 6 || rewardType == 8;
    }

    private void getFreeListenVip(){
        FreeListenTimeManager.getFreeVip(BaseApplication.getMyApplicationContext(),
                UUID.randomUUID().toString(), currentAdId, currentAdResponseId, 0, System.currentTimeMillis(), curSourceName,
                new FreeListenTimeManager.IRewardResultCallback() {
                    @Override
                    public void onRewardSuccess(FreeListenTimeManager.TimeSyncResult data) {
                        resetData();
                        if (currentCallBack != null) {
                            currentCallBack.onRewardSuccess(currentAd, 0);
                            String toast = "已开启1天VIP会员体验";
                            if (data != null && !TextUtils.isEmpty(data.toast)) {
                                toast = data.toast;
                            }
                            showFailToast(toast, Toast.LENGTH_LONG);
                        }
                    }

                    @Override
                    public void onRewardFail(int code, String message) {
                        resetData();
                        if (currentCallBack != null) {
                            currentCallBack.onRewardFail(ERROR_CODE_DEFAULT);
                        }
                    }
                });
    }

    private void doFreeAdRequest(boolean isRetry) {
        AdManager.postRecord(new Runnable() {
            @Override
            public void run() {
                Context context = MainApplication.getMyApplicationContext();
                Map<String, String> maps = new HashMap<>();
                if (currentAd != null) {
                    maps.put("adId", currentAd.getAdid() + "");
                    currentAd.setShowTokenEnable(true);
                    maps.put("token", AdTokenManager.getInstance().getShowToken(currentAd));
                } else {
                    maps.put("adId",  "0");
                    maps.put("token", "reissue_token");
                    maps.put("reissue", "true");
                }
                if (lastExtraParams != null) {
                    int increaseFreeTime = lastExtraParams.getRewardFreeAdTime() > 0 ? lastExtraParams.getRewardFreeAdTime() : 30;
                    maps.put("increaseFreeTime", increaseFreeTime + "");
                }
                maps.put("freeTime", "0");
                EncryptUtil.getInstance(context).getPlaySignature(context, maps);
                maps.put("positionName",AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION);
                maps.put("fallbackReq", "0");
                AdRequest.hookForwardVideo(maps, new IDataCallBack<BaseResponse>() {
                    @Override
                    public void onSuccess(@Nullable BaseResponse object) {
                        if (object != null) {
                            if (object.getRet() == 0 && currentCallBack != null) {
                                currentCallBack.onRewardSuccess(currentAd, 0);
                                if (lastExtraParams != null) {
                                    String toast = String.format("已获取%d分钟免广告权益", lastExtraParams.getRewardFreeAdTime());
                                    showFailToast(toast, Toast.LENGTH_LONG);
                                }
                            } else {
                                showFailToast("领取免广告权益异常", Toast.LENGTH_LONG);
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (!isRetry) {
                            doFreeAdRequest(true);
                        } else {
                            showFailToast("领取免广告权益异常", Toast.LENGTH_LONG);
                        }
                    }
                });
            }
        });
    }

    private void reportRewardVerify() {
        if (currentAd == null) {
            return;
        }
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_REWARD_VERIFY, AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .rewardVideoDuration(!AdManager.isThirdAd(currentAd) ? currentAd.getVideoDuration() : 0)
                        .adUserType(currentAd.getAdUserType())
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(curSourceName)
                        .startSource(curStartSource)
                        .showStyle(currentAd.getShowstyle() + "")
                        .unlockType(currentAd.getUnlockType())
                        .rewardSuccess(true)
                        .sceneId(sceneId)
                        .groupId(groupId)
                        .build());
    }

    private void reportRewardRequest(String action, boolean success, String errorMessage, boolean isRealFinishTask) {
        if (currentAd == null) {
            return;
        }
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_REQUEST_REWARD,
                                AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .rewardVideoDuration(!AdManager.isThirdAd(currentAd) ? currentAd.getVideoDuration() : 0)
                        .adUserType(currentAd.getAdUserType())
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(curSourceName)
                        .startSource(curStartSource)
                        .showStyle(currentAd.getShowstyle() + "")
                        .unlockType(currentAd.getUnlockType())
                        .sceneId(sceneId)
                        .groupId(groupId)
                        .action(action)
                        .success(success ? 1 : 0)
                        .rewardSuccess(true)
                        .errorMsg(errorMessage)
                        .fallBackReq(isRealFinishTask ? 0 : 1)
                        .build());
    }

    private void reportOnAdClose(boolean rewardSuccess, boolean isRealFinishTask) {
        if (currentAd == null) {
            return;
        }
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_REWARD_CLOSE,
                        AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION)
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                        .playFinish(isAdPlayComplete ? "1" : "0")
                        .rewardSuccess(rewardSuccess)
                        .rewardVideoDuration(!AdManager.isThirdAd(currentAd) ? currentAd.getVideoDuration() : 0)
                        .adUserType(currentAd.getAdUserType())
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(curSourceName + "")
                        .startSource(curStartSource + "")
                        .showStyle(currentAd.getShowstyle() + "")
                        .unlockType(currentAd.getUnlockType())
                        .fallBackReq(isRealFinishTask ? 0 : 1)
                        .showCommentMss(getShowCommentMss())
                        .sceneId(sceneId)
                        .groupId(groupId)
                        .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()))
                        .build());
    }

    private String getShowCommentMss() {
        List<Long> copy = new ArrayList<>();
        copy.addAll(commentShowTime);
        if (!ToolUtil.isEmptyCollects(copy)) {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < copy.size(); i++) {
                result.append(copy.get(i));
                if (i != copy.size() - 1) {
                    result.append(",");
                }
            }
            return result.toString();
        }
        return null;
    }

    private int getRealRewardTime() {
        if (currentExtraParams != null) {
            if (currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_DURATION_AND_CLICK) {
                if (currentExtraParams.isExtraClickRewardSuccess()) {
                    return rewardTime + clickRewardTime;
                } else {
                    return rewardTime;
                }
            }
        }
        return rewardTime;
    }

    private void resetData(){
        replay();
        lastExtraParams = null;
    }

    private void replay() {
        if (lastIsPlaying) {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).play();
            isPausePlayByRewardVideo = false;
            lastIsPlaying = false;
        }
    }

    public void notifyAdCancel() {
        notifyAdCancel(false);
    }

    public void notifyAdCancel(boolean openVip) {
        if (currentCallBack == null) {
            return;
        }
        FreeListenLogManager.writeLog("AdUnLockTimeManagerV2 notifyAdCancel");
        if (openVip) {
            currentCallBack.onRewardFail(ERROR_CODE_OPEN_VIP);
        } else {
            currentCallBack.onRewardFail(ERROR_CODE_DEFAULT);
            showFailToast(cancelToast, Toast.LENGTH_LONG);
        }

        reportOnAdClose(false, false);
        resetData();
    }

    public int getAdMaxLoadTime()  {
        if (maxAdLoadTime < 0) {
            maxAdLoadTime = 0;
        }
        return maxAdLoadTime;
    }

    public void tryCloseVideoAd(){
        if (currentExtraParams != null) {
            currentExtraParams.setVipFreeCloseAlertDialog(null);
            if (currentExtraParams.getCountDownTimer() != null) {
                currentExtraParams.getCountDownTimer().cancel();
                currentExtraParams.setCountDownTimer(null);
            }
            try {
                XmBaseDialog closeDialog = currentExtraParams.getVipFreeCloseAlertDialog();
                if (closeDialog != null && closeDialog.isShowing()) {
                    closeDialog.dismiss();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            currentExtraParams = null;
        }
        Activity mainActivity = MainApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) mainActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof IRewardAdFragment) {
                ((IRewardAdFragment) currentFragment).finish();
                return;
            }
        }
        RewardVideoAdManager.getInstance().finishRecentActivity();
    }

    public boolean isPausePlayByRewardVideo() {
        return isPausePlayByRewardVideo;
    }

    public boolean isEnterRewardVideo() {
        return isEnterRewardVideo;
    }

    public void resetEnterRewardVideo() {
        isEnterRewardVideo = false;
    }

    public boolean isShowingRewardVideo() {
        return isShowingRewardVideo;
    }

    private void showFailToast(String message, int time) {
        if (TextUtils.isEmpty(message)) {
            return;
        }
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                CustomToast.showFailToast(message, time);
            }
        }, 500);
    }

    private void showFailToast(String message){
        showFailToast(message, ToastManager.LENGTH_SHORT);
    }

    /**
     * 注册广播
     */
    private void registerBroadCast() {
        //注册会员购买广播
        IntentFilter filter = new IntentFilter();
        filter.addAction(ActionConstants.ACTION_BOUGHT_VIP_SUCCESS);
        LocalBroadcastManager.getInstance(ToolUtil.getCtx()).registerReceiver(commonReceiver, filter);
    }

    private final BroadcastReceiver commonReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && !TextUtils.isEmpty(intent.getAction())) {
                if (TextUtils.equals(intent.getAction(), ActionConstants.ACTION_BOUGHT_VIP_SUCCESS)) {
                    //购买会员成功
                    UserInfoMannage.getInstance().updatePersonInfo(false);
                    XmPlayerManager.getInstance(ToolUtil.getCtx()).stopSoundPatchPlay();
                }
            }
        }
    };

    private void preloadInspireAd() {
        if (currentAd == null || currentAd.getAgainPopupType() == 0) {
            return;
        }
        if (RewardAgainAdManager.SOURCE_NAME_VIDEO_AD.equals(curSourceName)) {
            return;
        }
        RewardAgainAdManager.preloadInspireAd(currentAd.getAgainPopupType(), currentAd.getAgainDuration(), curSourceName,RewardAgainAdManager.SOURCE_NAME_NATIVE_AD);
    }
}
