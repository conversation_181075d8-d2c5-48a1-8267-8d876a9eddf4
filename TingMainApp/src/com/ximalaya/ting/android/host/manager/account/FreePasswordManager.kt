package com.ximalaya.ting.android.host.manager.account

import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.util.common.JsonUtil
import com.ximalaya.ting.android.host.util.kt.isNotAvailable
import com.ximalaya.ting.android.loginservice.LoginRequest
import com.ximalaya.ting.android.loginservice.LoginService
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin
import com.ximalaya.ting.android.loginservice.model.LogoutModel
import com.ximalaya.ting.android.loginservice.model.SimpleAuthInfo
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.AsyncGson
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.Exception

/**
 * Created by nali on 2024/4/19.
 * <AUTHOR>
 */
object FreePasswordManager {

    var mLogoutModel: LogoutModel? = null
    var mLastCheckTime: Long = 0

    @JvmStatic
    fun saveAuthCode(auth: LogoutModel) {
        mLogoutModel = auth
        JsonUtil.toJson(auth, object : JsonUtil.IResult {
            override fun execute(result: String?) {
                Logger.log("FreePasswordManager : saveAuthCode " + result)
                MMKVUtil.getEncryptedInstance().saveString(PreferenceConstantsInHost.KEY_LOGIN_AUTH_DATA, result)
            }
        })
    }

    // 是否可以打开免密码登录页面
    @JvmStatic
    fun needOpenFreePasswordPage(): Boolean {
        val needOpenAuthData = MMKVUtil.getEncryptedInstance().containsKey(PreferenceConstantsInHost.KEY_LOGIN_AUTH_DATA) || mLogoutModel != null
        val openAuthAb = ABTest.getBoolean("quick_login", true)
        Logger.log("FreePasswordManager : needOpenFreePasswordPage = $needOpenAuthData   ab=$openAuthAb")
        return needOpenAuthData && openAuthAb
    }

    // 移除免密码登录数据
    @JvmStatic
    fun removeAuthCode() {
        mLogoutModel = null
        Logger.log("FreePasswordManager : removeAuthCode")
        MMKVUtil.getEncryptedInstance().removeByKey(PreferenceConstantsInHost.KEY_LOGIN_AUTH_DATA)
    }

    // 每次冷启的时候执行下
    @JvmStatic
    fun checkAuthCode(callBack: IDataCallBack<LogoutModel>?) {
        val authCode = MMKVUtil.getEncryptedInstance().getString(PreferenceConstantsInHost.KEY_LOGIN_AUTH_DATA, "")
        if (authCode.isNotAvailable()) {
            callBack?.onError(604, "不存在authCode")
            return
        }
        AsyncGson<LogoutModel>().fromJson(authCode, LogoutModel::class.java, object :
            AsyncGson.IResult<LogoutModel> {
            override fun postResult(result: LogoutModel?) {
                if (result == null) {
                    callBack?.onError(604, "解析数据失败")
                    return
                }

                if (System.currentTimeMillis() - mLastCheckTime < 10 * 1000) {
                    callBack?.onSuccess(result)
                    return
                }

                val map = mapOf("authCode" to result.authCode)
                LoginRequest.freePassportCheck(LoginService.getInstance().rquestData, map, object :
                    IDataCallBackUseLogin<SimpleAuthInfo> {
                    override fun onSuccess(info: SimpleAuthInfo?) {
                        if (info?.ret == 0) {
                            mLastCheckTime = System.currentTimeMillis()
                            result.avatar = info?.avatar
                            result.nickname = info?.nickname
                            saveAuthCode(result)
                            callBack?.onSuccess(result)
                        } else if (info?.ret == -1) {
                            removeAuthCode()
                            callBack?.onError(604, "authCode失效了")
                        }
                    }

                    override fun onError(code: Int, message: String?) {
                        if (code == -1) {
                            removeAuthCode()
                        }
                        callBack?.onError(code, message ?: "接口请求失败")
                    }
                })
            }

            override fun postException(e: Exception?) {
                removeAuthCode()
                callBack?.onError(604, "解析数据异常")
            }
        })
    }
}