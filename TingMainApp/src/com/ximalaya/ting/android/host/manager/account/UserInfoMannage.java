/**
 * UserInfoMannage.java
 * com.ximalaya.ting.android.host.manager
 * <p>
 * Function： TODO
 * <p>
 * ver     date      		author
 * ──────────────────────────────────
 * 2015-11-24 		jack.qin
 * <p>
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.host.manager.account;

import static com.ximalaya.ting.android.host.constants.LoginByConstants.LOGIN_BY_MINE_CLICK;
import static com.ximalaya.ting.android.host.constants.LoginByConstants.LoginBy;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.core.app.ActivityCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.smtt.sdk.CookieManager;
import com.tencent.smtt.sdk.CookieSyncManager;
import com.umeng.analytics.MobclickAgent;
import com.ximalaya.ting.android.apm.XmApm;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.login.LoginActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListenerAndCallBackUseChange;
import com.ximalaya.ting.android.host.listener.IVipStatusChangeListener;
import com.ximalaya.ting.android.host.manager.OverseasUserVerifyManager;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.request.IHandlerCommonCookie;
import com.ximalaya.ting.android.host.manager.statistic.UserOneDateListenDuration;
import com.ximalaya.ting.android.host.manager.vip.UserVipInfoManager;
import com.ximalaya.ting.android.host.manager.vip.VipStateChangeManager;
import com.ximalaya.ting.android.host.model.account.HomePageModelNew;
import com.ximalaya.ting.android.host.model.account.LoginInfoModel;
import com.ximalaya.ting.android.host.preinstall.PreInstallUtil;
import com.ximalaya.ting.android.host.push.PushAccountService;
import com.ximalaya.ting.android.host.share.manager.ShareDialogDataManager;
import com.ximalaya.ting.android.host.util.LoginAccountsUtil;
import com.ximalaya.ting.android.host.util.LoginTraceUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.JsonUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.database.MmkvUserUtil;
import com.ximalaya.ting.android.host.util.live.XMYouZanSDKUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.model.LogoutModel;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.receive.NotificationLikeManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.opensdk.util.MultiProcessSharedPreferences;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmpushservice.XmPushManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 */
public class UserInfoMannage implements SharedPreferences.OnSharedPreferenceChangeListener {
    public static final String ACTION_UPDATE_USERINFO_DATA_SUCCESS = "action_update_userinfo_data_success";
    public static final String ACTION_UPDATE_USERINFO_DATA_FAIL = "action_update_userinfo_data_fail";

    private volatile LoginInfoModelNew mLoginInfoModel;
    private List<ILoginStatusChangeListener> mListeners = new CopyOnWriteArrayList<>();
    private final List<IVipStatusChangeListener> mVipChangeListener = new CopyOnWriteArrayList<>();
    private MultiProcessSharedPreferences mMultiProcessSharedPreferences;
    private static Object mInitLock = new Object();
    private volatile boolean mHasInit;
    private boolean isVipRequesting;
    public static String sTimeLog = null;
    public static boolean willGotoLoginByMine;
    public static boolean hasGotoLoginByMine;

    //不需要进行懒加载
    private volatile static UserInfoMannage userInfoManager;
    private Context mContext;
    private boolean hasUpdatePersonInfo = false;

    public static UserInfoMannage getSingleInstance(Context context) {
        if (userInfoManager == null) {
            synchronized (UserInfoMannage.class) {
                if (userInfoManager == null) {
                    userInfoManager = new UserInfoMannage(context);
                }
            }
        }
        return userInfoManager;
    }

    private UserInfoMannage(Context context) {
        mContext = context;
        mMultiProcessSharedPreferences = new MultiProcessSharedPreferences(
                context
                , PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
        mMultiProcessSharedPreferences.registerOnSharedPreferenceChangeListener(this);

        initUserInfoManage();
    }

    private static StringBuilder mStringBuilder = new StringBuilder();

    private static long mLastTime;

    public static void printTime(String step) {
        if (BaseUtil.isMainProcess(ToolUtil.getCtx())) {
            long curTime = System.currentTimeMillis();
            long time = curTime - mLastTime;
            mLastTime = curTime;
            if (mStringBuilder == null) {
                mStringBuilder = new StringBuilder();
            }
            try {
                mStringBuilder.append("step=").append(step).append("  time=").append(time).append("\r\n");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @WorkerThread
    private void initUserInfoManage() {
        long startTime = System.currentTimeMillis();
        mLastTime = startTime;
        String resultJson = SharedPreferencesUtil
                .getInstance(mContext)
                .getString(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
        printTime("step1");
        if (!TextUtils.isEmpty(resultJson)) {
            mMultiProcessSharedPreferences
                    .edit()
                    .putString(PreferenceConstantsInHost
                            .TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW, resultJson)
                    .apply();
            SharedPreferencesUtil
                    .getInstance(MainApplication.getMyApplicationContext())
                    .removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
            printTime("step2");
        }

        LoginInfoModelNew loginInfoModelNew = null;
        String userInfo = mMultiProcessSharedPreferences
                .getString(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT, "");
        printTime("step3");
        if (!TextUtils.isEmpty(userInfo)) {
            LoginInfoModel loginInfoModel = ToolUtil.parseLoginfo(userInfo);
            printTime("step4");
            if (loginInfoModel != null) {
                loginInfoModelNew = LoginHelper.fromOldLoginInfoModel(loginInfoModel);
                printTime("step5");
                new AsyncGson<String>().toJson(loginInfoModelNew, new AsyncGson.IResult<String>() {
                    @Override
                    public void postResult(String result) {
                        mMultiProcessSharedPreferences.edit().putString(PreferenceConstantsInHost
                                .TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW, result).apply();

                        mMultiProcessSharedPreferences.edit().remove(PreferenceConstantsInHost
                                .TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT).apply();
                    }

                    @Override
                    public void postException(Exception e) {

                    }
                });

                mLoginInfoModel = loginInfoModelNew;
            }
        } else {
            String userInfoNew = mMultiProcessSharedPreferences
                    .getString(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW, "");
            printTime("step6");
            if (!TextUtils.isEmpty(userInfoNew)) {
                mLoginInfoModel = loginInfoModelNew = new Gson().fromJson(userInfoNew, LoginInfoModelNew.class);
                printTime("step7");
            } else {
                if (BaseUtil.isMainProcess(mContext)) {
                    MMKVUtil instance =
                            MMKVUtil.getEncryptedInstance(PreferenceConstantsInHost.KEY_USER_INFO_MMKV_FILE_NAME);
                    userInfoNew =
                            instance.getString(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
                    printTime("step8");
                    if (!TextUtils.isEmpty(userInfoNew)) {
                        mMultiProcessSharedPreferences.edit().putString(PreferenceConstantsInHost
                                .TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW, userInfoNew).commit();
                        instance.removeByKey(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
                        instance.removeByKey(PreferenceConstantsInHost.KEY_USER_INFO_HAS_MIGRATION);
                        printTime("step9");
                        mLoginInfoModel = loginInfoModelNew = new Gson().fromJson(userInfoNew, LoginInfoModelNew.class);
                        printTime("step10");
                    }
                }
            }
        }
        printTime("step11");
        if (BaseUtil.isMainProcess(mContext)) {
            LoginAccountsUtil.getInstance().updateAccounts(mContext, true);
            printTime("step12");
        }

        if (loginInfoModelNew != null) {
            mLastUserId = loginInfoModelNew.getUid();
            CrashReport.setUserId(loginInfoModelNew.getUid() + "");
            MobclickAgent.onProfileSignIn(loginInfoModelNew.getUid() + "");
            printTime("step13");
        }

        synchronized (mInitLock) {
            mHasInit = true;
            mInitLock.notifyAll();
        }

        printTime("step14");
        if (System.currentTimeMillis() - startTime > 10000 && BaseUtil.isMainProcess(ToolUtil.getCtx()) && mStringBuilder != null) {
            Logger.log("UserInfoMannage : longLanuch " + mStringBuilder);
            sTimeLog = mStringBuilder.toString();
        }
        mStringBuilder = null;
    }

    public static @NonNull
    UserInfoMannage getInstance() {
        return userInfoManager;
    }

    public static String getToken() {
        if (UserInfoMannage.getInstance().getUser() != null) {
            return UserInfoMannage.getInstance().getUser().getToken();
        } else {
            return "";
        }
    }

    public static boolean hasLogined() {
        LoginInfoModelNew tempUser = getInstance().getUser();
        return tempUser != null && !TextUtils.isEmpty(tempUser.getToken());
    }

    public static void updateUserVipStatus() {
        if (!hasLogined()) { //未登录不用处理
            return;
        }
        UserVipInfoManager.requestVipInfo(true, false, BaseUtil.isMainProcess(BaseApplication.getMyApplicationContext()));
        CommonRequestM.getIsVip(new IDataCallBack<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean object) {
                if (object == null) {
                    return;
                }

                Logger.logToFile("updateUserVipStatus, isVip: " + object);

                if (getInstance().getUser() != null) {
                    getInstance().setIsVip(object);
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    // 此登录方法默认是全屏登录, 然后希望是半屏登录使用  LoginByConstants.LOGIN_BY_HALF_SCREEN
    public static void gotoLogin(Context context) {
        gotoLogin(context, LoginByConstants.LOGIN_BY_DEFUALT);
    }

    // 是否去了全屏登录
    public static boolean hasGotoFullScreenLogin = false;
    // 是否去了半屏登录
    public static boolean hasGotoHalfScreenLogin = false;
    // 从哪个页面登录的
    public static String fromLoginUrl = null;

    public static String fromUriParamName = "fromUri";

    public static void gotoLogin(Context context, @LoginBy int loginBy) {
        gotoLogin(context, loginBy, false, null);
    }

    public static void gotoLogin(Context context, @LoginBy int loginBy, Bundle bundle) {
        gotoLogin(context, loginBy, false, null, bundle, true);
    }

    public static void gotoLogin(Context context, @LoginBy int loginBy, boolean fromGuide, String openChannel) {
        gotoLogin(context, loginBy, fromGuide, openChannel, null, true);
    }


    public static void gotoLogin(Context context, @LoginBy int loginBy, LoginConfig loginConfig) {
        if (loginConfig != null) {
            gotoLogin(context, loginBy, loginConfig.fromGuide,
                    loginConfig.openChannel, loginConfig.bundle, loginConfig.useOneKeyLogin);
        }
    }


    public static void gotoLogin(Context context, @LoginBy int loginBy, boolean fromGuide,
                                 String openChannel, Bundle bundle, boolean useOneKeyLogin) {
        LoginDeferredActionHelper.getInstance().clearCache();
        if (context == null)
            return;

        if (loginBy == LOGIN_BY_MINE_CLICK) {
            // 全局页-唤起登录页  点击事件
            LoginTraceUtil.traceGotoLogin("normal", "我页底tab");
            willGotoLoginByMine = true;
        } else {
            LoginTraceUtil.traceGotoLogin("normal", "");
        }

        boolean canGotoOneKeyLogin = LoginHelper.canGotoOneKeyLoginPage(loginBy);

        if (useOneKeyLogin && !canGotoOneKeyLogin) {
            if (!ToolUtil.hasSimCard(context)) {
                LoginTraceUtil.traceOnOneKeyLoginShowOrError("normal",
                        LoginTraceUtil.QuickLoginError.NO_INSTALL_SIM_CARD.getErrorCode(),
                        LoginTraceUtil.QuickLoginError.NO_INSTALL_SIM_CARD.getErrorMessages());
            } else if (loginBy == LoginByConstants.LOGIN_BY_CHANGE_USER) {
                LoginTraceUtil.traceOnOneKeyLoginShowOrError("normal",
                        LoginTraceUtil.QuickLoginError.CHANGE_USER.getErrorCode(),
                        LoginTraceUtil.QuickLoginError.CHANGE_USER.getErrorMessages());
            }
        }

        if (useOneKeyLogin) {
            new UserTracking()
                    .setID("7385")
                    .setOneClickLogin(ActivityCompat.checkSelfPermission(context,
                            Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED
                            && ToolUtil.hasSimCard(context))
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, "oneClickLogin");

            new UserTracking()
                    .setID("7386")
                    .setOneClickLogin(canGotoOneKeyLogin)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, "useOneClickLogin");
        }

        if (!fromGuide && bundle == null && canGotoOneKeyLogin) {
            NewUserLoginManager.INSTANCE.shouldLoginWithNewUserLoginDialog(loginBy,
                    () -> loginWithNewUserLoginDialog(context, loginBy, openChannel),
                    () -> loginWithoutNewNewLoginDialog(canGotoOneKeyLogin, useOneKeyLogin, context, loginBy, fromGuide, openChannel, bundle));
        } else {
            loginWithoutNewNewLoginDialog(canGotoOneKeyLogin, useOneKeyLogin, context, loginBy, fromGuide, openChannel, bundle);
        }
    }

    private static void loginWithNewUserLoginDialog(Context context, int loginBy, String openChannel) {
        try {
            Router.getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.loginBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        try {
                            Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFunctionAction().
                                    openNewUserOneKeyLogin(context, loginBy, openChannel);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void loginWithoutNewNewLoginDialog(boolean canGotoOneKeyLogin, boolean useOneKeyLogin,
                                                      Context context, int loginBy, boolean fromGuide, String openChannel, Bundle bundle) {
        if (FreePasswordManager.needOpenFreePasswordPage()) {
            // 打开免密登录页
            gotoFullScreenLogin(context, loginBy, fromGuide, openChannel, bundle, false, true);
            return;
        }
        if (canGotoOneKeyLogin && useOneKeyLogin) {
            try {
                Router.getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
                    @Override
                    public void onInstallSuccess(BundleModel bundleModel) {
                        if (Configure.loginBundleModel.bundleName.equals(bundleModel.bundleName)) {
                            try {
                                Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFunctionAction().
                                        openOneKeyLogin(context, loginBy, fromGuide, openChannel, bundle);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    }

                    @Override
                    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            loginBySelfPage(context, loginBy, fromGuide, openChannel, bundle);
        }
    }

    public static void loginBySelfPage(Context context, @LoginBy int loginBy, boolean fromGuide,
                                       String openChannel, Bundle bundle) {
        boolean useHalfLogin = LoginByConstants.useHalfLoginBy(loginBy);

        if (!useHalfLogin) {
            gotoFullScreenLogin(context, loginBy, fromGuide, openChannel, bundle, false, false);
        } else {
            Class clazz = null;
            try {
                clazz = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getActivityAction().getHalfScreenLoginActivity();
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (clazz != null) {
                Intent intentT = new Intent(context, clazz);
                if (!(context instanceof Activity)) {
                    intentT.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                }
                intentT.putExtra(BundleKeyConstants.KEY_LOGIN_BY, loginBy);

                if (!TextUtils.isEmpty(openChannel)) {
                    intentT.putExtra(BundleKeyConstants.KEY_OPEN_CHANNEL, openChannel);
                }

                if (bundle != null) {
                    intentT.putExtras(bundle);
                }

                Activity topActivity = BaseApplication.getTopActivity();
                intentT.setPackage(context.getPackageName());
                hasGotoHalfScreenLogin = ToolUtil.checkIntentAndStartActivity(context, intentT);
                if (topActivity != null && !topActivity.isFinishing()) {
                    topActivity.overridePendingTransition(0, 0);
                }
            }
        }
    }

    // 全屏登录
    public static void gotoFullScreenLogin(Context context, int loginBy, boolean fromGuide,
                                           String openChannel, Bundle bundle, boolean useOneKey, boolean useFreePassword) {
        Intent intentT = new Intent(context, LoginActivity.class);
        if (!(context instanceof Activity)) {
            intentT.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        } else if (((Activity) context).isFinishing()) {
            return;
        }

        intentT.putExtra(BundleKeyConstants.KEY_LOGIN_BY, loginBy);
        if (fromGuide) {
            intentT.putExtra(BundleKeyConstants.KEY_LOGIN_FROM_GUIDE, fromGuide);
        }

        if (!TextUtils.isEmpty(openChannel)) {
            intentT.putExtra(BundleKeyConstants.KEY_OPEN_CHANNEL, openChannel);
        }

        intentT.putExtra(BundleKeyConstants.KEY_USE_QUICK_LOGIN_KEY, useOneKey);
        intentT.putExtra(BundleKeyConstants.KEY_USE_FREE_PASSWORD_KEY, useFreePassword);

        if (bundle != null) {
            intentT.putExtras(bundle);
        }
        intentT.setPackage(context.getPackageName());
        hasGotoFullScreenLogin = ToolUtil.checkIntentAndStartActivity(context, intentT, fromGuide);
    }

    public static String getChargeCookie() {
        String sb = String.valueOf(AppConstants.environmentId) + "&_token" + "=" +
                UserInfoMannage.getInstance().getUser().getUid() + "&" +
                UserInfoMannage.getInstance().getUser().getToken() + ";";
        return AppConstants.environmentId + "&remember_me=y;"
                + sb;
    }

    public static long getUid() {
        LoginInfoModelNew modelNew = UserInfoMannage.getInstance().getUser();
        if (modelNew != null) {
            return modelNew.getUid();
        } else {
            return 0;
        }
    }

    public static String getNickname() {
        String nickname = "";
        LoginInfoModelNew modelNew = UserInfoMannage.getInstance().getUser();
        if (modelNew != null) {
            nickname = modelNew.getNickname();
        }

        if (TextUtils.isEmpty(nickname)) {
            nickname = "";
        }

        return nickname;
    }

    public static void clearLogStatus(Context mContext) {
        if (mContext == null) return;
        SharedPreferencesUtil util = SharedPreferencesUtil
                .getInstance(mContext);

        PlayableModel currPlayModel = XmPlayerManager.getInstance(mContext).getCurrSound();
        if (currPlayModel != null && currPlayModel instanceof Track) {
            Track track = (Track) currPlayModel;
            if (track.isPaid()) {
                XmPlayerManager xmPlayerManager = XmPlayerManager.getInstance(mContext);
                // 先调pause是保证暂停埋点的正常上报
                xmPlayerManager.pause(PauseReason.Business.UserInfoManagerPause);

                xmPlayerManager.stop();
                xmPlayerManager.resetPlayList();

                IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
                if (historyManager != null) {
                    historyManager.clearPlayList();
                }
            }
        }

        //清除听友圈发布动态的缓存
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_WILL_POST_DYNAMIC_LIST);
        util.removeByKey(PreferenceConstantsInOpenSdk.SINA_ACCESS_TOKEN);
//        mContext.getContentResolver()
//                .notifyChange(
//                        DataContentObserver
//                                .getUriByType(DataContentObserver.TypeLoginOut),
//                        null);
        //Ntalker.getBaseInstance().logout();
        //清除web页cookie
        CookieSyncManager.createInstance(mContext);
        CookieManager cookieManager = CookieManager.getInstance();
        try {
            cookieManager.removeExpiredCookie();
            cookieManager.setAcceptCookie(true);
            cookieManager.removeSessionCookie();// 移除
            cookieManager.removeAllCookie();// 移除
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                cookieManager.flush();
            } else {
                CookieSyncManager.getInstance().sync();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        //清除缓存的登录信息
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_PASSWORD);
        util.removeByKey(PreferenceConstantsInOpenSdk.TIMELINE);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON);
        util.removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON_NEW);
        //清除账号页头像上的气泡信息
        util.removeByKey(PreferenceConstantsInHost.KEY_LOGIN_BUBBLE_SHOWN);
        util.removeByKey(PreferenceConstantsInHost.KEY_LOGIN_BUBBLE_SHOWN_VERSION);


        util.saveInt(PreferenceConstantsInHost.KEY_CATEGORY_CONTENT_GENDER, 9);//分类页重置用户选择的按钮开关
    }

    public static void logOut(Context mContext) {
        if (mContext == null) return;
        clearLogStatus(mContext);

        if (hasLogined()) {
            // 有赞7.0 新版登出方式
            XMYouZanSDKUtil.youZanLogout();

            // 解决退出登录的时候,token已经被清空了,导致退出登录时,服务端感知不到
            CommonRequestM.getInstanse().setHandlerCommonCookie(new IHandlerCommonCookie() {
                @Override
                public String handlerCommonCookie(String cookie, String url) {
                    CommonRequestM.getInstanse().setHandlerCommonCookie(null);
                    HandlerManager.postOnMainAuto(new Runnable() {
                        @Override
                        public void run() {
                            UserInfoMannage.getInstance().setUser(null);
                        }
                    });
                    return cookie;
                }
            });

            LoginRequest.logout(LoginService.getInstance().getRquestData(), new IDataCallBackUseLogin<LogoutModel>() {
                @Override
                public void onSuccess(@Nullable LogoutModel logoutModel) {
                    if (logoutModel != null && !TextUtils.isEmpty(logoutModel.getAuthCode())) {
                        FreePasswordManager.saveAuthCode(logoutModel);
                        if (System.currentTimeMillis() - TempDataManager.getInstance().getLong(BundleKeyConstants.KEY_LAST_LOGOUT_TIME) < 1000) {
                            UserInfoMannage.gotoLogin(ToolUtil.getCtx());
                        } else {
                            Logger.log("FreePasswordManager : 接口超时不跳转到登录页");
                        }
                    } else {
                        FreePasswordManager.removeAuthCode();
                    }
                }

                @Override
                public void onError(int code, String message) {
                    FreePasswordManager.removeAuthCode();
                }
            });
        } else {
            UserInfoMannage.getInstance().setUser(null);
        }
    }

    public void updatePersonInfo(boolean sendBroadCast) {
        Map<String, String> homeParams = new HashMap<>();
        homeParams.put("device", "android");
        homeParams.put("timestamp", "" + System.currentTimeMillis());
        int cOper = NetworkType.getOperator(mContext);
        if (NetworkType.OPERATOR_CMCC == cOper) {
            cOper = 0;
        } else if (NetworkType.OPERATOR_UNICOME == cOper
                || NetworkType.OPERATOR_TELECOM == cOper) {
            cOper = 1;
        } else {
            cOper = -1;
        }

        if (cOper != -1) {
            homeParams.put("cOper", cOper + "");
        }
        homeParams.put("uid", UserInfoMannage.getUid() + "");
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getHomePageNew(homeParams, new IDataCallBack<HomePageModelNew>() {
                @Override
                public void onSuccess(HomePageModelNew object) {
                    isVipRequesting = false;
                    if (object != null) {
                        refreshLoginInfo(object);
                        if (sendBroadCast) {
                            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext())
                                    .sendBroadcast(new Intent(ACTION_UPDATE_USERINFO_DATA_SUCCESS));
                        }
                        hasUpdatePersonInfo = true;
                    } else {
                        saveLoginResult();
                        sendUpdateFail(sendBroadCast);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    isVipRequesting = false;
                    saveLoginResult();
                    sendUpdateFail(sendBroadCast);
                }
            });
        } catch (Exception e) {
            isVipRequesting = false;
            e.printStackTrace();
            saveLoginResult();
            sendUpdateFail(sendBroadCast);
        }
    }

    private void sendUpdateFail(boolean sendBroadCast) {
        if (sendBroadCast) {
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext())
                    .sendBroadcast(new Intent(ACTION_UPDATE_USERINFO_DATA_FAIL));
        }
    }

    public void refreshLoginInfo(HomePageModelNew homePageModel) {
        waitInitData();
        if (mLoginInfoModel != null) {
            setPersonalInfo(homePageModel);
            saveLoginResult();
            SharedPreferencesUtil.getInstance(MainApplication.getMyApplicationContext())
                    .saveBoolean(PreferenceConstantsInHost.KEY_IS_NEWER_FROM_3_MONTH, homePageModel.isInThreeMonth());
        }
    }

    private void setPersonalInfo(HomePageModelNew model) {
        LoginInfoModelNew loginInfo = getUser();
        if (loginInfo == null) {
            return;
        }

        if (!TextUtils.isEmpty(model.getNickname()))
            loginInfo.setNickname(model.getNickname());
        if (!TextUtils.isEmpty(model.getMobileLargeLogo()))
            loginInfo.setMobileLargeLogo(model.getMobileLargeLogo());
        if (!TextUtils.isEmpty(model.getMobileMiddleLogo()))
            loginInfo.setMobileMiddleLogo(model.getMobileMiddleLogo());
        if (!TextUtils.isEmpty(model.getMobileSmallLogo()))
            loginInfo.setMobileSmallLogo(model.getMobileSmallLogo());

        loginInfo.setMobileLoginable(model.isMobileLoginable());

        VipStateChangeManager.checkIfMainVipStateChangeToValid(model.isVip() ,false);
        setIsVip(model.isVip());
        loginInfo.setVerified(model.isVerified());
        loginInfo.setVipStatus(model.getVipStatus());
        loginInfo.setVipLevel(model.getVipLevel());
        loginInfo.setMvpGrade(model.getMvpGrade());
        loginInfo.setAnchorCategoryId(model.getAnchorCategoryId());
        if (model.getAnchorIsOriginal() != null) {
            loginInfo.setAnchorIsOriginal(String.valueOf(model.getAnchorIsOriginal()));
        } else {
            loginInfo.setAnchorIsOriginal(null);
        }
        String userVipState = "";
        if (TextUtils.isEmpty(model.getParentPaidStatus())) {
            userVipState = "登录游客状态";
        } else {
            userVipState = model.getParentPaidStatus();
        }
        loginInfo.setUserVipState(userVipState);
        // 如果原来下载音质为超高音质，当用户vip过期，就自动降级到高清音质
        if (!model.isVip()) {
            resetDownloadTrackQuality();
        }
    }

    public boolean isVip() {
        LoginInfoModelNew userInfo = getUser();
        return userInfo != null && userInfo.isVip();
    }

    @Nullable
    public LoginInfoModelNew getUser() {
        waitInitData();
        return mLoginInfoModel;
    }

    private void waitInitData() {
        if (mHasInit) {
            return;
        }
        synchronized (mInitLock) {
            while (!mHasInit) {
                try {
                    mInitLock.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void setUser(@Nullable LoginInfoModelNew user) {
        waitInitData();
        isVipRequesting = false;
        if (mContext == null) {
            return;
        }
//        Logger.i("cf_test", Log.getStackTraceString(new Throwable()));
        if (user != null && user.getUid() > 0) {
            CrashReport.setUserId(user.getUid() + "");
            MobclickAgent.onProfileSignIn(user.getUid() + "");
        } else {
            CrashReport.setUserId(DeviceUtil.getDeviceToken(MainApplication.getMyApplicationContext()));
            MobclickAgent.onProfileSignIn(DeviceUtil.getDeviceToken(MainApplication.getMyApplicationContext()));
        }

        // 如果不是主进程则直接设置user 对象
        if (!BaseUtil.isMainProcess(mContext)) {
            this.mLoginInfoModel = user;
            return;
        }


        //用户退出登录
        if (this.mLoginInfoModel != null && user == null) {
            //清空有赞的用户信息
            XMYouZanSDKUtil.youZanLogout();
            if (mListeners != null) {
                LoginInfoModelNew olderUser = this.mLoginInfoModel;
                this.mLoginInfoModel = null;
                Iterator<ILoginStatusChangeListener> iterator = mListeners.iterator();
                while (iterator.hasNext()) {
                    ILoginStatusChangeListener l = iterator.next();
                    if (l != null) {
                        l.onLogout(olderUser);
                    } else {
                        iterator.remove();
                    }
                }
            }
            XmPlayerManager.getInstance(mContext).updateLoginInfoOnChange(false);

            XmApm.getInstance().changeUser(0);
            // 如果原来下载音质为超高音质，当用户登出时，就自动降级到高清音质
            resetDownloadTrackQuality();
            resetPreferValueIfLogoutOrUserChange();
            XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).useStatusChange(false, false);

            Logger.logToFile("UserInfoMannage", "用户退出登录了  " + Log.getStackTraceString(new Throwable()));
        } else if (this.mLoginInfoModel == null && user != null) {//由未登录状态，进行登录
            Logger.i("login", "login");
//            new XMTraceApi.Trace()
//                    .setUserId("uid", user.getUid())
//                    .put("loginTime", System.currentTimeMillis() + "")
//                    .createTrace();

            // 在listenner 触发前 更改mmkvUser的信息
            MmkvUserUtil.changeUser(user);

            if (mListeners != null) {
                Iterator<ILoginStatusChangeListener> iterator = mListeners.iterator();
                this.mLoginInfoModel = user;
                while (iterator.hasNext()) {
                    ILoginStatusChangeListener l = iterator.next();
                    if (l != null) {
                        Logger.i("login", "onLogin");
                        l.onLogin(user);
                    } else {
                        iterator.remove();
                    }
                }
            }

            XmPlayerManager.getInstance(mContext).useStatusChange(true, user.isVip());

            XmPlayerManager.getInstance(mContext).updateLoginInfoOnChange(true);
//			Downloader.getCurrentInstance().changeUser(user.getUid(), false);
            RouteServiceUtil.getDownloadService().userChange(user.getUid(), false);

            XmApm.getInstance().changeUser(user.getUid());

            onUserLogin();

        } else if (this.mLoginInfoModel != null && user != null && this.mLoginInfoModel.getUid() != user.getUid()) {//在登录状态，进行用户切换
            if (mListeners != null) {
                Iterator<ILoginStatusChangeListener> iterator = mListeners.iterator();
                LoginInfoModelNew oldUser = this.mLoginInfoModel;
                this.mLoginInfoModel = user;
                while (iterator.hasNext()) {
                    ILoginStatusChangeListener l = iterator.next();
                    if (l != null) {
//                        l.onUserChange(oldUser, user);
                        l.onLogin(user);
                        if (l instanceof ILoginStatusChangeListenerAndCallBackUseChange) {
                            l.onUserChange(oldUser, user);
                        }
                    } else {
                        iterator.remove();
                    }
                }
            }
            XmPlayerManager.getInstance(mContext).useStatusChange(true, user.isVip());
//			Downloader.getCurrentInstance().changeUser(user.getUid(), false);
            RouteServiceUtil.getDownloadService().userChange(user.getUid(), false);


            XmPlayerManager.getInstance(mContext).updateLoginInfoOnChange(true);

            XmApm.getInstance().changeUser(user.getUid());
            // 如果原来下载音质为超高音质，当用户切换到非vip账号时，就自动降级到高清音质
            if (!user.isVip()) {
                resetDownloadTrackQuality();
            }
            resetPreferValueIfLogoutOrUserChange();

            onUserLogin();
        }
        this.mLoginInfoModel = user;

        if (user != null) {
            LoginAccountsUtil.getInstance().buildAccount(user);
            LoginAccountsUtil.getInstance().saveAccounts(mContext);
        }

        //loginSuccess 方法必须在 this.user 赋值之后调用
        if (user != null && !TextUtils.isEmpty(user.getToken())) {
            isVipRequesting = true;
            loginSuccess(mContext);

            // 初始化ScoreManage，需要登录信息
            ScoreManage.getInstance(mContext);
            updatePersonInfo(true);
        }

        UserOneDateListenDuration.onUserLogin(mContext, user != null ? user.getUid() : 0);
        if (user == null) {
            saveLoginResult();
            LoginUtil.initOneKeyLoginSDK(mContext);
        }

        Logger.i("cf_test", "设置用户信息");
    }

    private void onUserLogin() {
        if (getUid() <= 0) {
            XDCSCollectUtil.statErrorToXDCS("loginSuccessUidSmall", Log.getStackTraceString(new Throwable()));
        }
    }

    private void resetDownloadTrackQuality() {
        if (RouteServiceUtil.getDownloadService().getTrackQualityLevel() >= Track.TRACK_QUALITY_HIGH_PLUS) {
            RouteServiceUtil.getDownloadService().setTrackQualityLevel(Track.TRACK_QUALITY_HIGH);
        }
    }

    /**
     * 当用户退出登录或切换账号时，清除本地的一些缓存
     **/
    private void resetPreferValueIfLogoutOrUserChange() {
        // 清除本地追更搜词缓存
        SharedPreferencesUtil.getInstance(mContext).saveString(
                PreferenceConstantsInHost.TINGMAIN_KEY_SEARCH_HISTORY_UPDATE_WORD, "");
        // 清除是否满足领取礼包条件的缓存
        SharedPreferencesUtil.getInstance(mContext).saveBoolean(
                PreferenceConstantsInHost.KEY_NEW_USER_GIFT_TAG, true);

        MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_HAS_REQUEST_NICK_NAME_INFO, false);
    }

    public void addLoginStatusChangeListener(ILoginStatusChangeListener listener) {
        if (mListeners == null) {
            mListeners = new CopyOnWriteArrayList<>();
        }
        if (!mListeners.contains(listener)) {
            mListeners.add(listener);
        }
    }

    public void removeLoginStatusChangeListener(
            ILoginStatusChangeListener listener) {
        if (mListeners == null) {
            return;
        }
        if (mListeners.contains(listener)) {
            mListeners.remove(listener);
        }
    }

    public void addVipStatusChangeListener(IVipStatusChangeListener listener) {
        if (!mVipChangeListener.contains(listener)) {
            mVipChangeListener.add(listener);
        }
    }

    public void removeVipStatusChangeListener(IVipStatusChangeListener listener) {
        if (listener != null) {
            mVipChangeListener.remove(listener);
        }
    }

    private void notifVipStatusChange(boolean isVip) {
        for (IVipStatusChangeListener vipStatusChangeListener : mVipChangeListener) {
            vipStatusChangeListener.onVipStatusChanged(isVip);
        }

        XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).useStatusChange(true, isVip);
    }

    public void loginSuccess(final Context context) {
        // 登录成功移除 authcode
        FreePasswordManager.removeAuthCode();
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    CommonRequestM.bindAppForQQ(context);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                XmPushManager.getInstance().updateUserInfo(context, new PushAccountService());


                // 未登陆用户登陆领奖需要同步优惠码 从 deviceId-》userId
                CommonRequestM.getInstanse().getStringRequest(UrlConstants.getInstanse().adSyncpromo(), new HashMap<String, String>(), null);


                //登录成功同步云历史
//                ICloudyHistory historyManager = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
//                if (historyManager != null) {
//                    Log.e("hhhhhhhhhh",UserInfoMannage.getUid() + ":" + ProcessUtils.getProcessName());
//                    historyManager.syncCloudHistory(true);
//                }

                //登录成功后同步儿童画像
                CommonRequestM.getInstanse().getStringRequest(UrlConstants.getInstanse().getSyncBabyInfoUrl(), new HashMap<>(), null);

                //登录成功后同步分享数据(deviceId -> uid)
                ShareDialogDataManager.INSTANCE.syncShareDataAfterLogin();
            }
        });

        Router.getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel == Configure.loginBundleModel) {
                    try {
                        Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFunctionAction().checkMultiAccountCombine();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        });

        OverseasUserVerifyManager.onUserLoginSuccess(getUser());
    }

    private void saveLoginResult() {
        if (mLoginInfoModel != null) {
            JsonUtil.toJson(UserInfoMannage.getInstance().getUser(), new JsonUtil.IResult() {
                @Override
                public void execute(String result) {
                    mMultiProcessSharedPreferences
                            .edit()
                            .putString(PreferenceConstantsInHost
                                    .TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW, result)
                            .commit();
                }
            });
        } else {
            mMultiProcessSharedPreferences
                    .edit()
                    .putString(PreferenceConstantsInHost
                            .TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW, "")
                    .commit();
        }
    }

    public static void goToAnchorVeriry(MainActivity mainActivity) {
        if (PreInstallUtil.isPreInstallApk(mainActivity)) {
            CustomToast.showFailToast(R.string.host_upgrade_and_retry);
            return;
        }

        try {
            Router.getActionByCallback(Configure.BUNDLE_ALIAUTH, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.aliAuthBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        String url = UrlConstants.getInstanse().getWebOfVerify();
                        Bundle bundle = new Bundle();
                        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
                        mainActivity.startFragment(NativeHybridFragment.class, bundle);
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private long mLastUserId;
    private boolean isFirstInit = true;

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
        if (!BaseUtil.isPlayerProcess(mContext)) {
            Logger.i("cf_test", "main_______更新userinfo");
            return;
        }
        Logger.i("cf_test", "player_______更新userinfo");
        if (PreferenceConstantsInHost.TINGMAIN_KEY_SHARED_PRE_LOGIN_RESULT_NEW.equals(key)) {
            String loginInfoString = mMultiProcessSharedPreferences.getString(key, "");
            if (!TextUtils.isEmpty(loginInfoString)) {
                new AsyncGson().fromJson(loginInfoString, LoginInfoModelNew.class, new AsyncGson.IResult() {
                    @Override
                    public void postResult(Object result) {
                        if (result instanceof LoginInfoModelNew) {
                            mLoginInfoModel = (LoginInfoModelNew) result;
                            Logger.i("cf_test", "player_______更新userinfo 成功");

                            // 因为播放进程更新登录状态比较慢,所以在确定设置了登录信息的地方再check一遍是否需要同步历史记录
                            if (mLastUserId != mLoginInfoModel.getUid()) {
                                mLastUserId = mLoginInfoModel.getUid();

                                //登录成功同步云历史
                                ICloudyHistory historyManager = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
                                if (historyManager != null) {
                                    historyManager.syncCloudHistory(true);
                                }

                                if (!isFirstInit) {
                                    XmPlayerService playerSrvice =
                                            XmPlayerService.getPlayerSrvice();
                                    if (playerSrvice != null) {
                                        Logger.logToFile("userinfo change, playTrackOnTrackPlayQualityLevelChange");
                                        playerSrvice.playTrackOnTrackPlayQualityLevelChange();
                                    }
                                }

                                NotificationLikeManager.INSTANCE.onUseLoginCheckIsLike();
                            }

                            isFirstInit = false;
                        }
                    }

                    @Override
                    public void postException(Exception e) {

                    }
                });
            } else {
                mLoginInfoModel = null;
                mLastUserId = 0;
                NotificationLikeManager.INSTANCE.onTrackLikeStateChange();
            }
        }
    }

    //
    public static String addParamaToFromUri(String url) {
        if (!TextUtils.isEmpty(url)) {
            if (!TextUtils.isEmpty(UserInfoMannage.fromLoginUrl)) {
                try {
                    return Uri.parse(url).buildUpon().appendQueryParameter(UserInfoMannage.fromUriParamName, UserInfoMannage.fromLoginUrl).build().toString();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return url;
    }

    public static boolean isVipUser() {
        if (UserInfoMannage.getInstance().getUser() != null) {
            return UserInfoMannage.getInstance().getUser().isVip();
        }
        return false;
    }

    public void setIsVip(boolean isVip) {
        if (UserInfoMannage.getInstance().getUser() != null) {
            boolean lastIsVip = UserInfoMannage.getInstance().getUser().isVip();
            UserInfoMannage.getInstance().getUser().setVip(isVip);
            if (lastIsVip != isVip) {
                notifVipStatusChange(isVip);
            }
        }
    }

    public static boolean isChildVipUser() {
        if (UserInfoMannage.getInstance().getUser() != null) {
            return UserInfoMannage.getInstance().getUser().isChildVip();
        }
        return false;
    }

    public void setIsChildVip(boolean isChildVip) {
        if (UserInfoMannage.getInstance().getUser() != null) {
            VipStateChangeManager.checkIfChildVipStateChangeToValid(isChildVip);
            UserInfoMannage.getInstance().getUser().setChildVip(isChildVip);
        }
    }

    public void setIsShadowChild(boolean shadowChild) {
        Logger.d("UserInfoManage", "setIsShadowChild " + shadowChild);
        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
        if (user != null) {
            user.setShadowChild(shadowChild);
        }
    }

    public void setIsXimiVip(boolean isXimiVip) {
        if (UserInfoMannage.getInstance().getUser() != null) {
            UserInfoMannage.getInstance().getUser().setXimiVip(isXimiVip);
        }
    }

    public void setUserVipState(String userVipState) {
        if (UserInfoMannage.getInstance().getUser() != null) {
            UserInfoMannage.getInstance().getUser().setUserVipState(userVipState);
        }
    }

    public void getUserVipState() {
        if (UserInfoMannage.getInstance().getUser() != null) {
            UserInfoMannage.getInstance().getUser().getUserVipState();
        }
    }

    public static boolean isXimiVipUser() {
        if (UserInfoMannage.getInstance().getUser() != null) {
            return UserInfoMannage.getInstance().getUser().isXimiVip();
        }
        return false;
    }

    public void refrashToken(String token) {
        if (mLoginInfoModel != null) {
            mLoginInfoModel.setToken(token);
        }

        saveLoginResult();
    }

    public boolean isUpdatePersonInfo() {
        return hasUpdatePersonInfo;
    }

    public boolean isVipRequesting() {
        return isVipRequesting;
    }
}
