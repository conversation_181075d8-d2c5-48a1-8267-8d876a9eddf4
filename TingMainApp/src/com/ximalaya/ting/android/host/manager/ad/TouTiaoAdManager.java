package com.ximalaya.ting.android.host.manager.ad;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.ArriveTraceManager;
import com.ximalaya.ting.android.host.manager.OuterItingManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.ChannelInfoRecordManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.ClipUtils;
import com.ximalaya.ting.android.host.util.OAIDUtil;
import com.ximalaya.ting.android.host.util.VersionUtil;
import com.ximalaya.ting.android.host.util.VipJumpPageUtils;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.EasyConfigure;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmgrowth.EBringUpPageUrlFrom;
import com.ximalaya.ting.android.xmgrowth.EInstallStatus;
import com.ximalaya.ting.android.xmgrowth.IBringUpPageCallback;
import com.ximalaya.ting.android.xmgrowth.XmGrowthManager;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.BASE64Encoder;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by nali on 2018/2/28.
 *
 * <AUTHOR>
 */

public class TouTiaoAdManager {
    public static boolean isRequestingActive = false;
    public static boolean isFirstActive = false;
    private static final long REQUEST_INTERVAL = 2419200000L; // 28天
    private static final String TAG = TouTiaoAdManager.class.getSimpleName();

    // feedback: 增长返回deeplink；command: 串码; recall: 召回
    private static final String TYPE_RECALL = "recall";

    // 是否要重新上传下OAID
    public static Boolean willUpdateOAID;

    public static boolean sHasHandleIting;

    public static Runnable sRetryDeepLinkTask;
    public static long sRetryWaitStartTime;

    public static Runnable sRetrySystemUADeepLinkTask;
    public static long sRetrySystemUAWaitStartTime;

    public static boolean needRequest(Context context, Intent intent) {
        // 如果是通过scheme启动，那就不去请求接口获取要跳转的页面了，避免同时打开两个页面
        if (intent != null && intent.getData() != null && !TextUtils.isEmpty(intent.getScheme())) {
            new XMTraceApi.Trace()
                    .setMetaId(32439)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("itingUrl", intent.getData().toString())
                    .createTrace();
            new XMTraceApi.Trace()
                    .setMetaId(32440)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "2")
                    .createTrace();
            statRecallRequest(false, 1);
            return false;
        }
        long lastRequestTime = MmkvCommonUtil.getInstance(context)
                .getLong(PreferenceConstantsInHost.TINGMAIN_KEY_QUERY_ITING_REQUEST_TIME, 0);
        boolean needRequest = (System.currentTimeMillis() - lastRequestTime) >= REQUEST_INTERVAL;
        if (!needRequest) {
            new XMTraceApi.Trace()
                    .setMetaId(32440)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "3")
                    .createTrace();
            statRecallRequest(false, 2);
        }
        return needRequest;
    }

    public static boolean needRetryQueryDeeplink(Context context, boolean hasIting, boolean hasOAIDWhileRequest) {
        /*
         * 补发的条件
         * -首次未获取到oaid
         * -android≥10.0
         * -用户安装类型为新安装&覆盖安装
         * -未获得返回的iting地址
         */
        boolean needRetry = !hasIting && !hasOAIDWhileRequest && (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
                && (ToolUtil.isFirstInstallApp(context) || VersionUtil.isNewVersion());
        // 补发deeplink请求  其他事件
        new XMTraceApi.Trace()
                .setMetaId(40820)
                .setServiceId("others")
                .put("loadOaidResult", hasOAIDWhileRequest ? "已加载" : "未加载")
                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                .put("reissueCondition", needRetry ? "满足" : "不满足")
                .put("waittime", "0")
                .createTrace();
        Logger.i(TAG, "needRetryQueryDeeplink: " + needRetry);
        return needRetry;
    }

    public static boolean needRetryQueryDeeplinkSystemUA(Context context, boolean hasIting, boolean hasSystemUA) {
        /*
         * 补发的条件
         * -首次未获取到ua
         * -用户安装类型为新安装&覆盖安装
         * -未获得返回的iting地址
         */
        boolean needRetry = !hasIting && !hasSystemUA && (ToolUtil.isFirstInstallApp(context) || VersionUtil.isNewVersion());

        // 补发deeplink请求  其他事件
        new XMTraceApi.Trace()
                .setMetaId(40820)
                .setServiceId("others")
                .put("loadOaidResult", hasSystemUA ? "已加载" : "未加载")
                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                .put("reissueCondition", needRetry ? "满足" : "不满足")
                .put("waittime", "0")
                .createTrace();
        Logger.d("queryItingV4", "needRetryQueryDeeplink: needRetrySystemUA=" + needRetry);
        return needRetry;
    }

    private static long getDeeplinkRetryMaxWaitTimeMs() {
        int maxWaitTimeSecond = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME,
                CConstants.Group_toc.ITEM_DEEPLINK_RETRY_MAX_WAIT_TIME_SECOND, 10);
        return maxWaitTimeSecond * 1000L;
    }

    /**
     *
     * @param context
     * @param dataCallBack 返回true表示需要外部处理其他信息
     */
    public static void queryItingUseChannelAndClipboard(Context context, IHandleOk dataCallBack) {
        queryItingUseChannelAndClipboard(context, dataCallBack, false, -1);
    }

    private static void queryItingUseChannelAndClipboard(Context context, IHandleOk dataCallBack, boolean isRetry, long retryWaitStartTime) {
        long waitTime = 0;
        if (isRetry) {
            waitTime = System.currentTimeMillis() - retryWaitStartTime;
            if (sHasHandleIting) {
                // 补发deeplink请求结果  其他事件
                new XMTraceApi.Trace()
                        .setMetaId(40821)
                        .setServiceId("others")
                        .put("installationStatus", String.valueOf(getInstallStatus(context)))
                        .put("requestResult", "已打开iting不请求")
                        .put("waittime", String.valueOf(waitTime))
                        .createTrace();
                return;
            }
            new XMTraceApi.Trace()
                    .setMetaId(40821)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "正常请求")
                    .put("waittime", String.valueOf(waitTime))
                    .createTrace();
        }

        MmkvCommonUtil.getInstance(context).saveLong(PreferenceConstantsInHost.TINGMAIN_KEY_QUERY_ITING_REQUEST_TIME,
                System.currentTimeMillis());
        if(!isFirstActive) {
            isFirstActive = SharedPreferencesUtil.getInstance(context).
                    getBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_IS_FIRST_ACTIVE, true);
        }

        int installStatus = getInstallStatus(context);
        final Map<String, String> params = new HashMap<>();
        params.put("installStatus", installStatus + "");

        String realContent = ClipUtils.getRealXmClipContent(context, true);
        if(!TextUtils.isEmpty(realContent)) {
            params.put("command", realContent);
        }

        ChannelInfoRecordManager.getInstance().clipContentUpdate(context, realContent);
        boolean hasOAID = !TextUtils.isEmpty(OAIDUtil.OAID);
        if (ConstantsOpenSdk.isDebug &&
                MmkvCommonUtil.getInstance(context).getBoolean(PreferenceConstantsInHost.ITEM_DEEPLINK_FIRST_REQUEST_NOT_PASS_OAID, false)) {
            hasOAID = false;
            MmkvCommonUtil.getInstance(context).saveBoolean(PreferenceConstantsInHost.ITEM_DEEPLINK_FIRST_REQUEST_NOT_PASS_OAID, false);
        }
        boolean finalHasOAID = hasOAID;

        boolean hasSystemUA = !TextUtils.isEmpty(DeviceUtil.getMemoryUserAgentByWebView());
        if (hasSystemUA) {
            try {
                params.put("sysua", BASE64Encoder.encode(DeviceUtil.getMemoryUserAgentByWebView()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            if (useV4Strategy()) {
                DeviceUtil.getUAByWebSettingsAsync(ToolUtil.getCtx());
            }
        }

        if (ConstantsOpenSdk.isDebug) {
            Logger.d("queryItingV4", "queryItingUseChannelAndClipboard hasOAID=" + hasOAID + "  hasSystemUA= "  +hasSystemUA + "   " + Log.getStackTraceString(new Throwable()));
        }

        IDataCallBack<String> callback = new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                String itingUrl = "";
                JSONObject jsonObject = ToolUtil.requestIsSuccess(object);
                if (jsonObject != null) {
                    try {
                        JSONObject data = jsonObject.optJSONObject("data");
                        boolean hasIting = false;
                        if(data != null) {
                            itingUrl = data.optString("itingUrl");

                            boolean isFromCommand = false;
                            if (!TextUtils.isEmpty(realContent) &&
                                    !TextUtils.isEmpty(data.optString("originalItingUrl"))) {
                                uploadItingInfo(realContent, data.optString("originalItingUrl"));
                                isFromCommand = true;
                            }

                            statRecallResult(true, itingUrl);
                            if(!TextUtils.isEmpty(itingUrl)) {
                                hasIting = true;
                                // 请求回来后发现已经有通过iting打开过页面，则不再去打开返回的iting
                                if (sHasHandleIting) {
                                    if (!isRetry) {
                                        new XMTraceApi.Trace()
                                                .setMetaId(32442)
                                                .setServiceId("others")
                                                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                                                .put("requestResult", "3")
                                                .put("itingUrl", itingUrl)
                                                .createTrace();
                                    } else {
                                        // 补发deeplink打开结果  其他事件
                                        new XMTraceApi.Trace()
                                                .setMetaId(40823)
                                                .setServiceId("others")
                                                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                                                .put("openResult", "不打开")
                                                .put("itingUrl", itingUrl)
                                                .createTrace();
                                    }
                                    return;
                                }
                                Activity mainActivity = MainApplication.getMainActivity();
                                if(mainActivity instanceof MainActivity) {
                                    if (!VipJumpPageUtils.isAllowJump()) {
                                        return;
                                    }
                                    VipJumpPageUtils.setAllowJumpPage(false);

                                    SharedPreferencesUtil.getInstance(context)
                                            .saveBoolean(PreferenceConstantsInHost.KEY_DEEP_LINK_NEED_SHOW_NEW_GIFT, true);
                                    OuterItingManager.INSTANCE.markOuterItingAction();
                                    ArriveTraceManager.onGotOuterIting(itingUrl, isFromCommand ?
                                            ArriveTraceManager.ITING_SOURCE_CLIPBOARD : ArriveTraceManager.ITING_SOURCE_DPLINK);
                                    toOtherPage((MainActivity) mainActivity, itingUrl, true, false, true);
                                    MainActivity.statOuterIting(itingUrl);

                                    String type = data.optString("type");
                                    if (TYPE_RECALL.equals(type)) {
                                        new XMTraceApi.Trace()
                                                .setMetaId(18059)
                                                .setServiceId("pageTurned")
                                                .put("srcUrl", itingUrl)
                                                .put("pageType", "")
                                                .put("pageId", "")
                                                .createTrace();
                                    }

                                    if (!isRetry) {
                                        new XMTraceApi.Trace()
                                                .setMetaId(32441)
                                                .setServiceId("others")
                                                .put("requestResult", "1")
                                                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                                                .put("itingUrl", itingUrl)
                                                .createTrace();
                                    } else {
                                        // 补发deeplink接收结果  其他事件
                                        new XMTraceApi.Trace()
                                                .setMetaId(40822)
                                                .setServiceId("others")
                                                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                                                .put("acceptResult", "成功")
                                                .put("itingUrl", itingUrl)
                                                .createTrace();
                                    }
                                    return;
                                }
                            }
                        }

                        if (useV4Strategy()) {
                            if (!isRetry) {
                                if (needRetryQueryDeeplink(context, hasIting, finalHasOAID)) {
                                    long waitStartTime = System.currentTimeMillis();
                                    sRetryWaitStartTime = waitStartTime;
                                    sRetryDeepLinkTask = () -> {
                                        queryItingUseChannelAndClipboard(context, null, true, waitStartTime);
                                    };

                                    if (!TextUtils.isEmpty(OAIDUtil.OAID)) {
                                        retryQueryItingAfterOAIDGot(ToolUtil.getCtx());
                                    }
                                }

                                if (needRetryQueryDeeplinkSystemUA(context, hasIting, hasSystemUA)) {
                                    long waitStartTime = System.currentTimeMillis();
                                    sRetrySystemUAWaitStartTime = waitStartTime;
                                    sRetrySystemUADeepLinkTask = () -> {
                                        queryItingUseChannelAndClipboard(context, null, true, waitStartTime);
                                    };

                                    if (!TextUtils.isEmpty(DeviceUtil.getMemoryUserAgentByWebView())) {
                                        retryQueryItingAfterSystemUAGot(ToolUtil.getCtx());
                                    }
                                }
                            }
                        } else if (!isRetry && needRetryQueryDeeplink(context, hasIting, finalHasOAID)) {
                            long waitStartTime = System.currentTimeMillis();
                            sRetryWaitStartTime = waitStartTime;
                            sRetryDeepLinkTask = () -> {
                                queryItingUseChannelAndClipboard(context, null, true, waitStartTime);
                            };
                            if (!TextUtils.isEmpty(OAIDUtil.OAID)) {
                                retryQueryItingAfterOAIDGot(ToolUtil.getCtx());
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                if(dataCallBack != null) {
                    dataCallBack.onReady();
                }

                if (!isRetry) {
                    new XMTraceApi.Trace()
                            .setMetaId(32441)
                            .setServiceId("others")
                            .put("requestResult", "2")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("itingUrl", "")
                            .createTrace();
                } else {
                    // 补发deeplink接收结果  其他事件
                    new XMTraceApi.Trace()
                            .setMetaId(40822)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("acceptResult", "成功")
                            .put("itingUrl", itingUrl)
                            .createTrace();
                }
                statRecallResult(true, itingUrl);
            }

            @Override
            public void onError(int code, String message) {
                if(dataCallBack != null) {
                    dataCallBack.onReady();
                }
                statRecallResult(false, "");
                if (!isRetry) {
                    new XMTraceApi.Trace()
                            .setMetaId(32441)
                            .setServiceId("others")
                            .put("requestResult", "2")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("itingUrl", "")
                            .createTrace();
                } else {
                    // 补发deeplink接收结果  其他事件
                    new XMTraceApi.Trace()
                            .setMetaId(40822)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("acceptResult", "失败")
                            .put("itingUrl", "")
                            .createTrace();
                }
            }
        };

        if (useV4Strategy()) {
            CommonRequestM.queryItingV4(params, callback);
        } else {
            CommonRequestM.queryIting(params, callback);
        }

        if (!isRetry) {
            new XMTraceApi.Trace()
                    .setMetaId(32440)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "1")
                    .createTrace();
            statRecallRequest(true, 0);
        }
    }
    
    public static void openCommandItingIfNeeded(Context context, String command) {
        if (TextUtils.isEmpty(command)) {
            return;
        }
        EInstallStatus installStatus = EInstallStatus.NORMAL;
        if (ToolUtil.isFirstInstallApp(context)) {
            installStatus = EInstallStatus.NEW_INSTALL;
        } else if (VersionUtil.isNewVersion()) {
            installStatus = EInstallStatus.HAS_UPGRADE;
        }
        XmGrowthManager.INSTANCE.getCommandPageUrl(context, installStatus, command, new IBringUpPageCallback() {

            @Override
            public void onRequest(boolean b, boolean b1) {
                new XMTraceApi.Trace()
                        .setMetaId(35885)
                        .setServiceId("others")
                        .put("installationStatus", String.valueOf(getInstallStatus(context)))
                        .put("requestResult", "3")
                        .put("type", "1")
                        .createTrace();
            }

            @Override
            public void onNoRequest() {

            }

            @Override
            public void onGotPageUrl(@Nullable String pageUrl, @Nullable EBringUpPageUrlFrom urlFrom, boolean hasOAID,
                                     @Nullable String originLink, boolean requestError) {
                boolean isFromCommand = false;
                if (urlFrom == EBringUpPageUrlFrom.COMMAND && !TextUtils.isEmpty(originLink)) {
                    isFromCommand = true;
                    uploadItingInfo(command, originLink);
                }
                if (!TextUtils.isEmpty(pageUrl)) {
                    Activity mainActivity = MainApplication.getMainActivity();
                    if (mainActivity instanceof MainActivity) {
                        sHasHandleIting = true;
                        OuterItingManager.INSTANCE.markOuterItingAction();
                        ArriveTraceManager.onGotOuterIting(pageUrl, isFromCommand ?
                                ArriveTraceManager.ITING_SOURCE_CLIPBOARD : ArriveTraceManager.ITING_SOURCE_DPLINK);
                        toOtherPage((MainActivity) mainActivity, pageUrl, true, true, false);
                        MainActivity.statOuterIting(pageUrl);
                    }
                }
                new XMTraceApi.Trace()
                        .setMetaId(35886)
                        .setServiceId("others")
                        .put("requestResult", requestError ? "2" : "1")
                        .put("installationStatus", String.valueOf(getInstallStatus(context)))
                        .put("itingUrl", pageUrl)
                        .put("type", "1")
                        .createTrace();
            }
        });
    }

    public static void queryItingByGrowthSdk(Context context, Intent intent, @NonNull IHandleOk callBack) {
        queryItingByGrowthSdk(context, intent, callBack, false, -1);
    }

    private static void queryItingByGrowthSdk(Context context, Intent intent, @NonNull IHandleOk callBack, boolean isRetry, long retryWaitStartTime) {
        long waitTime = 0;
        if (isRetry) {
            waitTime = System.currentTimeMillis() - retryWaitStartTime;
            if (sHasHandleIting) {
                // 补发deeplink请求结果  其他事件
                new XMTraceApi.Trace()
                        .setMetaId(40821)
                        .setServiceId("others")
                        .put("installationStatus", String.valueOf(getInstallStatus(context)))
                        .put("requestResult", "已打开iting不请求")
                        .put("waittime", String.valueOf(waitTime))
                        .createTrace();
                return;
            }
            new XMTraceApi.Trace()
                    .setMetaId(40821)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "正常请求")
                    .put("waittime", String.valueOf(waitTime))
                    .createTrace();
        }
        // 如果是通过scheme启动，那就不去请求接口获取要跳转的页面了，避免同时打开两个页面
        if (intent != null && intent.getData() != null && !TextUtils.isEmpty(intent.getScheme())) {
            // 补发走另外的埋点上报
            if (!isRetry) {
                new XMTraceApi.Trace()
                        .setMetaId(35885)
                        .setServiceId("others")
                        .put("installationStatus", String.valueOf(getInstallStatus(context)))
                        .put("requestResult", "2")
                        .createTrace();
                callBack.onReady();
            } else {
                // 补发deeplink请求结果  其他事件
                new XMTraceApi.Trace()
                        .setMetaId(40821)
                        .setServiceId("others")
                        .put("installationStatus", String.valueOf(getInstallStatus(context)))
                        .put("requestResult", "已打开iting不请求")
                        .put("waittime", String.valueOf(waitTime))
                        .createTrace();
            }
            return;
        }
        EInstallStatus installStatus = EInstallStatus.NORMAL;
        if (ToolUtil.isFirstInstallApp(context)) {
            installStatus = EInstallStatus.NEW_INSTALL;
        } else if (VersionUtil.isNewVersion()) {
            installStatus = EInstallStatus.HAS_UPGRADE;
        }
        String command = ClipUtils.getRealXmClipContent(context, true);
        ChannelInfoRecordManager.getInstance().clipContentUpdate(context, command);
        XmGrowthManager.INSTANCE.getBringUpPageUrl(context, installStatus, command, new IBringUpPageCallback() {
            @Override
            public void onGotPageUrl(@Nullable String pageUrl, @Nullable EBringUpPageUrlFrom urlFrom, boolean hasOAID, @Nullable String originLink, boolean requestError) {
                String statType = "";
                switch (urlFrom) {
                case DP:
                    statType = "0";
                    break;
                case COMMAND:
                    statType = "1";
                    break;
                }
                if (!isRetry) {
                    new XMTraceApi.Trace()
                            .setMetaId(35886)
                            .setServiceId("others")
                            .put("requestResult", TextUtils.isEmpty(pageUrl) ? "2" : "1")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("itingUrl", pageUrl)
                            .put("type", statType)
                            .createTrace();
                } else {
                    // 补发deeplink接收结果  其他事件
                    new XMTraceApi.Trace()
                            .setMetaId(40822)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("acceptResult", requestError ? "失败" : "成功")
                            .put("itingUrl", pageUrl)
                            .createTrace();
                }
                boolean isFromCommand = false;
                if (urlFrom == EBringUpPageUrlFrom.COMMAND && !TextUtils.isEmpty(originLink)) {
                    isFromCommand = true;
                    uploadItingInfo(command, originLink);
                }
                if (sHasHandleIting) {
                    callBack.onReady();
                    if (!isRetry) {
                        new XMTraceApi.Trace()
                                .setMetaId(35887)
                                .setServiceId("others")
                                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                                .put("requestResult", "3")
                                .put("itingUrl", pageUrl)
                                .createTrace();
                    } else {
                        // 补发deeplink打开结果
                        new XMTraceApi.Trace()
                                .setMetaId(40823)
                                .setServiceId("others")
                                .put("installationStatus", String.valueOf(getInstallStatus(context)))
                                .put("openResult", "不打开")
                                .put("itingUrl", pageUrl)
                                .createTrace();
                    }
                    statRecallRequest(false, 1);
                    return;
                }
                if (!TextUtils.isEmpty(pageUrl)) {
                    Activity mainActivity = MainApplication.getMainActivity();
                    if (mainActivity instanceof MainActivity) {
                        SharedPreferencesUtil.getInstance(context)
                                .saveBoolean(PreferenceConstantsInHost.KEY_DEEP_LINK_NEED_SHOW_NEW_GIFT, true);
                        OuterItingManager.INSTANCE.markOuterItingAction();
                        ArriveTraceManager.onGotOuterIting(pageUrl, isFromCommand ?
                                ArriveTraceManager.ITING_SOURCE_CLIPBOARD : ArriveTraceManager.ITING_SOURCE_DPLINK);
                        toOtherPage((MainActivity) mainActivity, pageUrl, true, true, isRetry);
                        MainActivity.statOuterIting(pageUrl);
                    }
                    statRecallRequest(false, 1);
                } else {
                    queryItingV3(context, callBack);
                    if (!isRetry && needRetryQueryDeeplink(context, false, hasOAID)) {
                        long waitStartTime = System.currentTimeMillis();
                        sRetryWaitStartTime = waitStartTime;
                        sRetryDeepLinkTask = () -> {
                            queryItingByGrowthSdk(context, intent, callBack, true, waitStartTime);
                        };
                    }
                }
            }

            @Override
            public void onRequest(boolean meetRequestInterval, boolean hasCommand) {
                // 满足时间间隔要求下方请求，传1，因为有串码下发请求传3
                String type = "";
                if (meetRequestInterval) {
                    type = "0";
                    if (hasCommand) {
                        type = "0,1";
                    }
                } else if (hasCommand) {
                    type = "1";
                }
                if (!isRetry) {
                    new XMTraceApi.Trace()
                            .setMetaId(35885)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("requestResult", meetRequestInterval ? "1" : "3")
                            .put("type", type)
                            .createTrace();
                }
                ArriveTraceManager.onStartGetIting();
            }

            @Override
            public void onNoRequest() {
                queryItingV3(context, callBack);
                if (!isRetry) {
                    new XMTraceApi.Trace()
                            .setMetaId(35885)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(context)))
                            .put("requestResult", "4")
                            .createTrace();
                }
            }
        }, isRetry);
    }

    private static void queryItingV3(Context context, @NonNull IHandleOk callback) {
        long lastRequestTime = MmkvCommonUtil.getInstance(context).getLong(
                PreferenceConstantsInHost.TINGMAIN_KEY_QUERY_ITING_V3_REQUEST_TIME, 0);
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRequestTime >= REQUEST_INTERVAL) {
            MmkvCommonUtil.getInstance(context).saveLong(
                    PreferenceConstantsInHost.TINGMAIN_KEY_QUERY_ITING_V3_REQUEST_TIME, currentTime);
            statRecallRequest(true, 0);
            // 目前这个接口只有回流用户的吊起了
            CommonRequestM.queryItingV3(new IDataCallBack<String>() {
                @Override
                public void onSuccess(@Nullable String itingUrl) {
                    if (!sHasHandleIting && !TextUtils.isEmpty(itingUrl) && !itingUrl.equals("null")) {
                        Activity mainActivity = MainApplication.getMainActivity();
                        if (mainActivity instanceof MainActivity) {
                            sHasHandleIting = true;
                            OuterItingManager.INSTANCE.markOuterItingAction();
                            ArriveTraceManager.onGotOuterIting(itingUrl, ArriveTraceManager.ITING_SOURCE_DPLINK);
                            toOtherPage((MainActivity) mainActivity, itingUrl, false, false, false);
                            MainActivity.statOuterIting(itingUrl);
                            new XMTraceApi.Trace()
                                    .setMetaId(18059)
                                    .setServiceId("pageTurned")
                                    .put("srcUrl", itingUrl)
                                    .put("pageType", "")
                                    .put("pageId", "")
                                    .createTrace();
                        }
                        statRecallResult(true, itingUrl);
                    } else {
                        statRecallResult(false, "");
                    }
                    callback.onReady();
                }

                @Override
                public void onError(int code, String message) {
                    callback.onReady();
                    statRecallResult(false, "");
                }
            });
        } else {
            callback.onReady();
            statRecallRequest(false, 2);
        }
    }

    public static void retryQueryItingAfterOAIDGot(Context context) {
        if (sRetryDeepLinkTask == null) {
            return;
        }
        Logger.i(TAG, "retryQueryItingAfterOAIDGot");
        long waitTime = System.currentTimeMillis() - sRetryWaitStartTime;
        if (waitTime > getDeeplinkRetryMaxWaitTimeMs()) {
            new XMTraceApi.Trace()
                    .setMetaId(40821)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "已超时不请求")
                    .put("waittime", String.valueOf(waitTime))
                    .createTrace();
            Logger.i(TAG, "retryQueryItingAfterOAIDGot timeout");
        } else {
            sRetryDeepLinkTask.run();
            Logger.i(TAG, "retryQueryItingAfterOAIDGot run");
        }
        sRetryDeepLinkTask = null;
        sRetryWaitStartTime = 0;
    }

    public static void retryQueryItingAfterSystemUAGot(Context context) {
        if (sRetrySystemUADeepLinkTask == null) {
            return;
        }
        Logger.i("queryItingV4", "retryQueryItingAfterSystemUAGot");
        long waitTime = System.currentTimeMillis() - sRetrySystemUAWaitStartTime;
        if (waitTime > getDeeplinkRetryMaxWaitTimeMs()) {
            new XMTraceApi.Trace()
                    .setMetaId(40821)
                    .setServiceId("others")
                    .put("installationStatus", String.valueOf(getInstallStatus(context)))
                    .put("requestResult", "已超时不请求")
                    .put("waittime", String.valueOf(waitTime))
                    .createTrace();
            Logger.i("queryItingV4", "retryQueryItingAfterSystemUAGot timeout");
        } else {
            sRetrySystemUADeepLinkTask.run();
            Logger.i("queryItingV4", "retryQueryItingAfterSystemUAGot run");
        }
        sRetrySystemUADeepLinkTask = null;
        sRetrySystemUAWaitStartTime = 0;
    }



    private static int getInstallStatus(Context context) {
        int installStatus = 3; // 1: 新安装；2: 覆盖安装；3: 已安装
        if (ToolUtil.isFirstInstallApp(context)) {
            installStatus = 1;
        } else if (VersionUtil.isNewVersion()) {
            installStatus = 2;
        }
        return installStatus;
    }

    private static void uploadItingInfo(String realContent, String url) {
        UserTracking ut = new UserTracking();
        String uid = "null";
        if (UserInfoMannage.hasLogined()) {
            uid = String.valueOf(UserInfoMannage.getUid());
        }
        ut.setUserId(uid);
        ut.setSrcPageUrl(url);
        HashMap<String, String> params = ut.getParams();
        if (params != null) {
            params.put("command", realContent);
            params.put("isFirstInstall", "" + ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext()));
        }
        ut.statIting(XDCSCollectUtil.APP_NAME_EVENT, "viewSuccess");
        SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveBoolean(PreferenceConstantsInHost.KEY_H5_STRING_UPLOADED, true);

        new XMTraceApi.Trace()
                .setMetaId(10060)
                .setServiceId("viewSuccess")
                .put("pageUrl", url)
                .put("command", realContent)
                .put("isFirstInstalled", ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext()) ? "true" : "false")
                .createTrace();
    }

    private static void statRecallRequest(boolean request, int noRequestReason) {
        // 请求了就传0吧
        // 1-iting已有；2-28天用户；3-其他
        // 召回用户判断接口  其他事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(43154)
                .setServiceId("others")
                .put("isSucceed", String.valueOf(request));
        if (noRequestReason > 0) {
            trace.put("noResultReason", String.valueOf(noRequestReason));
        }
        trace.createTrace();
    }

    private static void statRecallResult(boolean isSucceed, String iting) {
        // 召回用户接口返回  其他事件
        new XMTraceApi.Trace()
                .setMetaId(43153)
                .setServiceId("others")
                .put("isSucceed", String.valueOf(isSucceed))
                .put("content", iting)
                .createTrace();
    }

    private static void toOtherPage(MainActivity mainActivity, String tingUrl) {
        toOtherPage(mainActivity, tingUrl, true, false, false);
    }

    private static void toOtherPage(MainActivity mainActivity ,String tingUrl, boolean needStat, boolean byGrowthSdk, boolean isRetry) {
        if(mainActivity != null && !mainActivity.isFinishing()) {
            boolean hasOpen = true;
            if (tingUrl.startsWith("iting://")) {
                try {
                    hasOpen = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(mainActivity, Uri.parse(tingUrl));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (tingUrl.startsWith("http")) {
                Bundle bundle = new Bundle();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, tingUrl);
                mainActivity.startFragment(NativeHybridFragment.newInstance(bundle), null);
            } else if (tingUrl.startsWith("tel:") || tingUrl.startsWith("mailto:") || tingUrl.startsWith("sms:")) {
                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW);
                    intent.setData(Uri.parse(tingUrl));
                    mainActivity.startActivity(intent);
                } catch (ActivityNotFoundException e) {
                    e.printStackTrace();
                }
            }
            if (needStat) {
                if (!isRetry) {
                    if (!byGrowthSdk) {
                        new XMTraceApi.Trace()
                                .setMetaId(32442)
                                .setServiceId("others")
                                .put("installationStatus", String.valueOf(getInstallStatus(BaseApplication.getMyApplicationContext())))
                                .put("requestResult", "1")
                                .put("itingUrl", tingUrl)
                                .createTrace();
                    } else {
                        new XMTraceApi.Trace()
                                .setMetaId(35887)
                                .setServiceId("others")
                                .put("installationStatus", String.valueOf(getInstallStatus(BaseApplication.getMyApplicationContext())))
                                .put("requestResult", "1")
                                .put("itingUrl", tingUrl)
                                .createTrace();
                    }
                } else {
                    // 补发deeplink打开结果
                    new XMTraceApi.Trace()
                            .setMetaId(40823)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(BaseApplication.getMyApplicationContext())))
                            .put("openResult", hasOpen ? "打开成功" : "打开失败")
                            .put("itingUrl", tingUrl)
                            .createTrace();
                }
            }
        } else {
            if (needStat) {
                if (!isRetry) {
                    if (!byGrowthSdk) {
                        new XMTraceApi.Trace()
                                .setMetaId(32442)
                                .setServiceId("others")
                                .put("installationStatus", String.valueOf(getInstallStatus(BaseApplication.getMyApplicationContext())))
                                .put("requestResult", "2")
                                .put("itingUrl", tingUrl)
                                .createTrace();
                    } else {
                        new XMTraceApi.Trace()
                                .setMetaId(35887)
                                .setServiceId("others")
                                .put("installationStatus", String.valueOf(getInstallStatus(BaseApplication.getMyApplicationContext())))
                                .put("requestResult", "2")
                                .put("itingUrl", tingUrl)
                                .createTrace();
                    }
                } else {
                    // 补发deeplink打开结果
                    new XMTraceApi.Trace()
                            .setMetaId(40823)
                            .setServiceId("others")
                            .put("installationStatus", String.valueOf(getInstallStatus(BaseApplication.getMyApplicationContext())))
                            .put("openResult", "打开失败")
                            .put("itingUrl", tingUrl)
                            .createTrace();
                }
            }
        }
    }

    public static boolean useV4Strategy() {
        return EasyConfigure.getBoolean("query_iting_use_v4", true);
    }
}
