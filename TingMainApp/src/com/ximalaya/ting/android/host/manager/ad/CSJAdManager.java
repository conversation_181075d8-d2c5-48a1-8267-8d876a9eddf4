package com.ximalaya.ting.android.host.manager.ad;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdConstant;
import com.bytedance.sdk.openadsdk.TTAdLoadType;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.TTDrawFeedAd;
import com.bytedance.sdk.openadsdk.TTFeedAd;
import com.bytedance.sdk.openadsdk.TTFullScreenVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJDrawNativeThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJFullScreenVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJNativeThirdAd;
import com.ximalaya.ting.android.adsdk.activity.BaseDelegateActivity;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.adsdk.bridge.util.sdkinit.CSJSDKInitHelper;
import com.ximalaya.ting.android.adsdk.external.IBaseLoadListener;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.preciseye.OriginalAdParams;
import com.ximalaya.ting.android.preciseye.csj.CSJPrecisEyeListenerUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * Created by le.xin on 2020-02-27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class CSJAdManager {

    private CSJAdManager() {
    }

    public static CSJAdManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    public void preloadInit(Context context) {
        CSJSDKInitHelper.getInstance().preloadSDKAsync(context, AdSDKManager.getCSJInitParams());
    }

    private static class SingletonHolder {
        private static final CSJAdManager INSTANCE = new CSJAdManager();
    }

    public boolean checkInit(IBaseLoadListener loadListener) {
        return checkInit(1500, loadListener);
    }

    public boolean checkInit(int timeOut, IBaseLoadListener loadListener) {
        CSJSDKInitHelper.getInstance().checkSDKInit(ToolUtil.getCtx(), timeOut, AdSDKManager.getCSJInitParams(), null);

        return CSJSDKInitHelper.getInstance().checkInitSuccessAndNotifyLoadError(new IBaseLoadListener() {
            @Override
            public void onLoadError(int code, String message) {
                Logger.log("CSJAdManager SDK 初始化失败 code=" + code + "  message=" + message);
                if (loadListener != null) {
                    loadListener.onLoadError(code, message);
                }
            }
        });
    }

    /**
     * 穿山甲原生广告
     */
    public void loadCSJNativeAd(Context context, ThirdAdLoadParams thirdAdLoadParams, String posId,
                                Advertis advertis,
                                ILoadNativeAdHandler adHandler) {
        if (advertis == null || adHandler == null  || thirdAdLoadParams == null || TextUtils.isEmpty(thirdAdLoadParams.getPositionName())) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        Logger.log("xinle CSJAdManager : loadCSJNativeAd 加载开始" + advertis);

        if (!checkInit(null)) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        AdSlot.Builder builder = new AdSlot.Builder()
                .setCodeId(posId) //广告位id
                .setSupportDeepLink(true)
                .setImageAcceptedSize(16, 9)
                .setAdCount(1);

        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
            Logger.v("msg", " ---- 参与竞价csj 5 + " + advertis.getSlotAdm());
        }
        AdSlot adSlot = builder.build();

        TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(context);//baseContext
        // 建议为activity

        Logger.log("xinle CSJAdManager : loadCSJNativeAd 加载开始 打印posId  " + posId +  "  "+ advertis);


        mTTAdNative.loadFeedAd(adSlot, CSJPrecisEyeListenerUtil.getFeedAdListenerExtends(
                new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId()),
                new TTAdNative.FeedAdListener() {
            @Override
            public void onError(int i, String s) {
                Logger.log("xinle CSJAdManager : loadCSJNativeAd 加载失败  " + i + "   " + s + advertis);
                adHandler.loadNextGDT();
            }

            @Override
            public void onFeedAdLoad(List<TTFeedAd> list) {
                Logger.log("xinle CSJAdManager : loadCSJNativeAd 加载成功  onAdLoad" + advertis);
                if (ToolUtil.isEmptyCollects(list)) {

                    adHandler.loadNextGDT();
                } else {

                    adHandler.loadNativeAds(new CSJNativeThirdAd(advertis, list.get(0), posId));
                }
            }
        }));
    }

    /**
     * 加载穿山甲draw信息流广告
     * @param context
     * @param thirdAdLoadParams
     * @param posId
     * @param advertis
     * @param adHandler
     */
    public void loadCSJDrawNativeAd(Context context, ThirdAdLoadParams thirdAdLoadParams, String posId, Advertis advertis, ILoadNativeAdHandler adHandler) {
        if (advertis == null || adHandler == null  || thirdAdLoadParams == null || TextUtils.isEmpty(thirdAdLoadParams.getPositionName())) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        if (!checkInit(null)) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        AdSlot.Builder builder = new AdSlot.Builder()
                .setCodeId(posId) //广告位id
                .setSupportDeepLink(true)
                .setImageAcceptedSize(9, 16)
                .setAdCount(1);
        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
            Logger.v("msg", " ---- 参与竞价csj 5 + " + advertis.getSlotAdm());
        }
        AdSlot adSlot = builder.build();
        TTAdSdk.getAdManager().createAdNative(context).loadDrawFeedAd(adSlot, CSJPrecisEyeListenerUtil.getDrawFeedAdListenerExtends(
                new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId()),
                new TTAdNative.DrawFeedAdListener() {
                    @Override
                    public void onError(int i, String s) {
                        adHandler.loadNextGDT();
                    }

                    @Override
                    public void onDrawFeedAdLoad(List<TTDrawFeedAd> list) {
                        if (ToolUtil.isEmptyCollects(list)) {
                            adHandler.loadNextGDT();
                        } else {
                            TTDrawFeedAd ad = list.get(0);
                            ad.setCanInterruptVideoPlay(false);
                            adHandler.loadNativeAds(new CSJDrawNativeThirdAd(advertis, ad, posId));
                        }
                    }
                }));
    }

    /**
     * 加载穿山甲 模版渲染插屏广告
     * @param context
     * @param thirdAdLoadParams
     * @param posId
     * @param advertis
     * @param adHandler
     */
    public void loadFullScreenVideoAdAd(Context context, ThirdAdLoadParams thirdAdLoadParams, String posId,
                                Advertis advertis,
                                ILoadNativeAdHandler adHandler) {
        if (advertis == null || adHandler == null  || thirdAdLoadParams == null || TextUtils.isEmpty(thirdAdLoadParams.getPositionName())) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        Logger.log("xinle CSJAdManager : loadFullScreenVideoAdAd 加载开始" + advertis + "slotId == " + posId);

        if (!checkInit(null)) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        //创建TTAdNative对象，createAdNative(Context context) context需要传入Activity对象
        TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(context);

        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(posId)
                .setSupportDeepLink(true)
                .setAdLoadType(TTAdLoadType.LOAD)
                .build();

        mTTAdNative.loadFullScreenVideoAd(adSlot, new TTAdNative.FullScreenVideoAdListener() {
            //请求广告失败
            @Override
            public void onError(int code, String message) {
                adHandler.loadNextGDT();
            }

            //广告物料加载完成的回调
            @Override
            public void onFullScreenVideoAdLoad(TTFullScreenVideoAd ad) {

            }

            //广告视频/图片加载完成的回调，接入方可以在这个回调后展示广告
            @Override
            public void onFullScreenVideoCached(TTFullScreenVideoAd ttFullScreenVideoAd) {
                if (ttFullScreenVideoAd != null) {
                    Logger.log("xinle CSJAdManager : TTFullScreenVideoAd 加载成功  onAdLoad" + advertis);
                    adHandler.loadNativeAds(new CSJFullScreenVideoAd(advertis, ttFullScreenVideoAd, posId));

                    ttFullScreenVideoAd.setFullScreenVideoAdInteractionListener(new TTFullScreenVideoAd.FullScreenVideoAdInteractionListener() {
                        //广告的展示回调
                        @Override
                        public void onAdShow() {
                            AdReportModel.Builder builder = AdReportModel.newBuilder(
                                            AppConstants.AD_LOG_TYPE_SITE_SHOW, advertis.getPositionName())
                                    .sdkType(AdManager.getSDKType(advertis) + "")
                                    .dspPositionId(advertis.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .sourceName("playPageLiveEntry")
                                    .showStyle(advertis.getShowstyle() + "");
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    advertis, builder.build());
                        }

                        //广告的下载bar点击回调
                        @Override
                        public void onAdVideoBarClick() {
                            Logger.log("xinle CSJAdManager : ttFullScreenVideoAd onAdVideoBarClick  " );
                            if (advertis != null) {
                                AdReportModel.Builder builder = AdReportModel.newBuilder(
                                                AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                                advertis.getPositionName()).onlyClickRecord(true);
                                AdManager.handlerAdClick(context, advertis, builder.build());
                            }
                        }

                        //广告关闭的回调
                        @Override
                        public void onAdClose() {
                            Logger.log("xinle CSJAdManager : ttFullScreenVideoAd nAdClose  " );
                        }

                        //视频播放完毕的回调
                        @Override
                        public void onVideoComplete() {
                            Logger.log("xinle CSJAdManager : ttFullScreenVideoAd onVideoComplete  " );
                        }

                        //跳过视频播放，当点击跳过按钮时，开发者可根据项目实际情况做对应的逻辑处理，另点击跳过后会默认触发广告关闭回调
                        @Override
                        public void onSkippedVideo() {
                            Logger.log("xinle CSJAdManager : ttFullScreenVideoAd onSkippedVideo " );
                        }
                    });
                    Activity topActivity = BaseApplication.getMainActivity();
                    ttFullScreenVideoAd.showFullScreenVideoAd(topActivity, TTAdConstant.RitScenes.GAME_GIFT_BONUS, null);
                    ttFullScreenVideoAd = null;
                } else {
                    adHandler.loadNextGDT();
                }
            }

            @Override
            public void onFullScreenVideoCached() {}
        });

    }


    @SuppressLint("ResourceType")
    public static FrameLayout addMediaViewToView(RelativeLayout relativeLayout, ImageView videoCover) {
        if(relativeLayout == null || videoCover == null) {
            return null;
        }

        if(videoCover.getId() <= 0 || relativeLayout.indexOfChild(videoCover) < 0) {
            ToolUtil.throwIllegalNoLogicException();
        }

        relativeLayout.setVisibility(View.VISIBLE);

        View gdtMediaLay = relativeLayout.findViewById(R.id.host_gdt_ad_media_view);
        if(gdtMediaLay != null) {
            if(gdtMediaLay instanceof ViewGroup) {
                ((ViewGroup) gdtMediaLay).removeAllViews();
            }
            gdtMediaLay.setVisibility(View.INVISIBLE);
        }

        View xmMediaLay = relativeLayout.findViewById(R.id.host_xm_ad_media_view);
        if(xmMediaLay != null) {
            if(xmMediaLay instanceof ViewGroup) {
                ((ViewGroup) xmMediaLay).removeAllViews();
            }
            xmMediaLay.setVisibility(View.INVISIBLE);
        }

        FrameLayout csjMediaLay = relativeLayout.findViewById(R.id.host_csj_ad_media_view);
        if(csjMediaLay != null) {
            csjMediaLay.removeAllViews();
            csjMediaLay.setVisibility(View.VISIBLE);
            return csjMediaLay;
        }

        FrameLayout mediaView = new FrameLayout(relativeLayout.getContext());

        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.addRule(RelativeLayout.ALIGN_LEFT, videoCover.getId());
        params.addRule(RelativeLayout.ALIGN_RIGHT, videoCover.getId());
        params.addRule(RelativeLayout.ALIGN_TOP, videoCover.getId());
        params.addRule(RelativeLayout.ALIGN_BOTTOM, videoCover.getId());
        mediaView.setLayoutParams(params);
        mediaView.setId(R.id.host_csj_ad_media_view);

        relativeLayout.addView(mediaView, relativeLayout.indexOfChild(videoCover));

        return mediaView;
    }

}
