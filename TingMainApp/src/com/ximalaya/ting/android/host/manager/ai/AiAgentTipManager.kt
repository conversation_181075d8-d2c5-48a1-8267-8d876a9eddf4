package com.ximalaya.ting.android.host.manager.ai

import android.text.TextUtils
import androidx.annotation.Keep
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.async.AsyncRequest
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager.agentConfig
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/7/23
 */
object AiAgentTipManager {

    private const val TAG = "AiAgentTipManager"

    private val sugConfig: AgentSugConfig by lazy {
        GsonUtils.parseJson<AgentSugConfig>(
            ConfigureCenter.getInstance().getString(
                CConstants.Group_toc.GROUP_NAME,
                "agent_home_guid_frequency",
                ""
            ), AgentSugConfig::class.java
        ) ?: AgentSugConfig()
    }
    private const val CHAT_XMLY_BUBBLE_SHOW_COUNT_KEY: String = "chat_xmly_bubble_show_count_"
    private const val CHAT_XMLY_BUBBLE_SHOW_DATE_KEY: String = "chat_xmly_bubble_show_date_"

    fun canShowSug(): Boolean {
        if ((sugConfig.enable && ChildXmlyTipManager.isXiaoYaType()).not()) {
            Logger.i(TAG, "false sugConfig enable==false")
            return !hasShowTipToday()
        }

        val currentTime = System.currentTimeMillis()
        val lastShowTime = MMKVUtil.getInstance().getLong(CHAT_XMLY_BUBBLE_SHOW_DATE_KEY, 0)
        val today = DateTimeUtil.getDateStrByTm(currentTime)
        val lastShowDate = DateTimeUtil.getDateStrByTm(lastShowTime)

        // 如果是新的一天，重置计数
        if (today != lastShowDate && sugConfig.times > 0) {
            MMKVUtil.getInstance().saveInt(CHAT_XMLY_BUBBLE_SHOW_COUNT_KEY, 0)
            Logger.i(TAG, "true 今天首次且允许展示${sugConfig.times}次")
            return true
        }

        // 同一天内，检查时间间隔
        val timeDiffMinutes = (currentTime - lastShowTime) / (1000 * 60)
        if (timeDiffMinutes < sugConfig.duration) {
            Logger.i(TAG, "false 间隔${timeDiffMinutes}<${sugConfig.duration}分钟")
            return false
        }

        // 检查今天已显示次数
        val showCount = MMKVUtil.getInstance().getInt(CHAT_XMLY_BUBBLE_SHOW_COUNT_KEY, 0)
        Logger.i(
            TAG,
            "${showCount < sugConfig.times} showCount${showCount}<==>${sugConfig.times}times "
        )
        return showCount < sugConfig.times
    }

    /**
     * 标记已显示ChatXmly气泡
     */
    fun markSugShow() {
        if (sugConfig.enable && ChildXmlyTipManager.isXiaoYaType()) {
            val currentCount = MMKVUtil.getInstance().getInt(CHAT_XMLY_BUBBLE_SHOW_COUNT_KEY, 0)
            MMKVUtil.getInstance().saveInt(CHAT_XMLY_BUBBLE_SHOW_COUNT_KEY, currentCount + 1)
            // 存储当前时间戳
            MMKVUtil.getInstance()
                .saveLong(CHAT_XMLY_BUBBLE_SHOW_DATE_KEY, System.currentTimeMillis())
            Logger.i(TAG, "markSugShow >>> refresh agent config , has show count = $currentCount")
            ChildXmlyTipManager.refreshAgentConfig()
        } else {
//            // 配置关闭或无效时，使用原有逻辑
//            markShowTipToday()
            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .saveLong("homepage_child_xmly_tip", System.currentTimeMillis())
        }
    }

    private fun hasShowTipToday(): Boolean {
        if (ConstantsOpenSdk.isDebug && BaseUtil.getIntSystemProperties("debug.xima.home.show_bobo") == 1) {
            return false
        }
        val lastAutoPlayTime = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .getLong("homepage_child_xmly_tip", 0)
        val maxDay = ConfigureCenter.getInstance()
            .getInt(CConstants.Group_toc.GROUP_NAME, "chatxmly_homePage_limit_day", 1)
        return DateTimeUtil.calNaturalDiffDay(lastAutoPlayTime) < maxDay
    }


    /**
     * 处理气泡文字长度限制
     */
    fun getProcessSug(): String {
        val originalText = ChildXmlyTipManager.getAgentSug()
        if (TextUtils.isEmpty(originalText)) {
            return originalText
        }

        if (originalText.length <= 12) {
            return originalText
        }

        return originalText.substring(0, 12) + "..."
    }

    fun traceSugClick() {
        GlobalScope.launch {
            runCatching {
                AsyncRequest.post<Any>(
                    UrlConstants.getInstanse().sugLogClickUrl,
                    mapOf(
                        "sug" to (agentConfig?.agentContent?.sug ?: ""),
                        "orgSug" to (agentConfig?.agentContent?.orgSug ?: ""),
                        "sugType" to (agentConfig?.agentContent?.sugType ?: ""),
                        "trackId" to (agentConfig?.agentContent?.trackId?.toString() ?: ""),
                        "intent" to (agentConfig?.agentContent?.intent ?: "")
                    )
                )
            }.onFailure {
                dlog("logClick fail >>> $it")
            }
        }
    }

    fun eTraceSugShow() {
        GlobalScope.launch {
            runCatching {
                AsyncRequest.post<Any>(
                    UrlConstants.getInstanse().sugLogShowUrl,
                    mapOf(
                        "sug" to (agentConfig?.agentContent?.sug ?: ""),
                        "orgSug" to (agentConfig?.agentContent?.orgSug ?: ""),
                        "sugType" to (agentConfig?.agentContent?.sugType ?: ""),
                        "trackId" to (agentConfig?.agentContent?.trackId?.toString() ?: ""),
                        "intent" to (agentConfig?.agentContent?.intent ?: "")
                    )
                )
            }.onFailure {
                dlog("logShow fail >>> $it")
            }
        }
    }

    fun getPlayAgentIconDebugUri(url: String): String{
        return enhanceRnITingUrlForDebug("debug.lmm.play.agent", url)
    }

    private fun enhanceRnITingUrlForDebug(key: String, url: String): String {
        val debugValue = ToolUtil.getDebugSystemProperty(key, "")
        if (debugValue.isNullOrEmpty().not()) {
            return "$url&__debug=1&ip=$debugValue"
        }
        return url
    }

    private fun dlog(msg: String) {
        Logger.i(TAG, msg)
    }
}


@Keep
data class AgentSugConfig(
    val enable: Boolean = true,
    val times: Int = 5,
    val duration: Int = 240
)