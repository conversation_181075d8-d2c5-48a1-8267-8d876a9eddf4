package com.ximalaya.ting.android.host.manager.ai.radio

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/4/28
 */
interface IAiRadioPlay {

    fun isPlay(): Boolean

    fun playAiRadio(radioPart: RadioPart)

    fun play():Boolean

    fun clear()

    fun release()

    fun addPlayListener(listener: IRadioPlayerListener)

    fun removePlayListener(listener: IRadioPlayerListener)

    fun addPlayPart(parts: List<RadioPart>)
}