package com.ximalaya.ting.android.host.hybrid.providerSdk.busi;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listener.IWebViewResultCallback;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.vip.VipStateChangeManager;
import com.ximalaya.ting.android.host.universal.IReporter;
import com.ximalaya.ting.android.host.util.constant.ActionConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;


/**
 * Created by chengyun.wu on 2018/11/14.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class NotifyAppAction extends BaseAction {
    private static final String FILTER_PRODUCT_FROM_H5 = "productFromH5";

    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);

        String type = args.optString("type");
        long albumId = args.optLong("albumId");
        long activityId = args.optLong("activityId");
        long couponId = args.optLong("couponId");
        boolean isVip = args.optBoolean("isVip");

        report(type, args);

        if (TextUtils.equals("shoppingItem", type)) {
            JSONObject jsonObject = args.optJSONObject("data");
            String itemID = jsonObject.optString("itemID");
            String itemName = jsonObject.optString("itemName");
            Intent intent = new Intent(FILTER_PRODUCT_FROM_H5);
            Bundle bundle = new Bundle();
            bundle.putString("itemID", itemID);
            bundle.putString("itemName", itemName);
            intent.putExtra("shoppingItem", bundle);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("vipClubBoughtSuccess", type)) {
            Intent intent = new Intent(AppConstants.TYPE_VIP_CLUB_BOUGHT);
            JSONObject jsonObject = args.optJSONObject("data");
            long communityId = jsonObject.optLong("communityId");
            intent.putExtra(AppConstants.DATA_COMMUNITY_ID, communityId);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("vip", type)) {
            JSONObject jsonObject = args.optJSONObject("data");
            if (null != jsonObject && jsonObject.has("isVip")) {
                boolean result = jsonObject.optBoolean("isVip");
                if (result) {
                    // 购买儿童VIP
                    boolean resultKid = jsonObject.optBoolean("fromKid");
                    if (resultKid) {
                        VipStateChangeManager.checkIfNotifyChildVipStateChangeToValid(true);
                    } else {
                        VipStateChangeManager.checkIfNotifyMainVipStateChangeToValid(true);
                    }
                    UserInfoMannage.updateUserVipStatus(); //更新vip状态
                    Logger.logToFile("NotifyAppAction send buy vip broadcast");
                    Intent intent = new Intent();
                    intent.setAction(ActionConstants.ACTION_BOUGHT_VIP_SUCCESS);
                    LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);

                    if (resultKid){
                        Intent intentKid = new Intent();
                        intentKid.setAction(ActionConstants.ACTION_BOUGHT_KID_VIP_SUCCESS);
                        LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intentKid);
                    }
                }
            }
        } else if (TextUtils.equals("buyMaster", type)) {
            JSONObject jsonObject = args.optJSONObject("data");
            if (null != jsonObject && jsonObject.has("masterClassPayAction") && jsonObject.has("data")) {
                int action = jsonObject.optInt("masterClassPayAction", 3);
                String data = jsonObject.optString("data");
                Bundle bundle = new Bundle();
                if (!TextUtils.isEmpty(data)) {
                    bundle.putInt("masterClassPayAction", action);
                    bundle.putString("data", data);
                    Intent intent = new Intent();
                    intent.setAction(ActionConstants.ACTION_PLAY_PAGE_MASTER_HANDOUT_DATA);
                    intent.putExtras(bundle);
                    LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
                }
            }
        } else if (TextUtils.equals("vipsound", type)) {
            JSONObject jsonObject = args.optJSONObject("data");
            if (null != jsonObject && jsonObject.has("isVip")) {
                boolean result = jsonObject.optBoolean("isVip");
                if (result) {
                    Intent intent = new Intent();
                    intent.setAction(ActionConstants.ACTION_BOUGHT_VIP_SOUND_SUCCESS);
                    LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
                }
            }
        } else if (TextUtils.equals("mcScheduleSubscribe", type)) {
            JSONObject jsonObject = args.optJSONObject("data");
            long scheduleId = jsonObject.optLong("scheduleId");
            boolean subscribed = jsonObject.optBoolean("subscribed");
            Intent intent = new Intent(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_NOTIFY);
            Bundle bundle = new Bundle();
            bundle.putLong(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SCHEDULE_ID, scheduleId);
            bundle.putBoolean(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SUBSCRIBED, subscribed);
            intent.putExtra(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE, bundle);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("studyPunch", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_STUDY_PUNCH_NOTIFY);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("closeAlbum818Dialog", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_ALBUM_DIALOG_818_WEB_CLOSE_NOTIFY);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("child_anti_addiction", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_CHILD_ANTI_ADDICTION);
            CustomToast.showSuccessToast("校验成功！");
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("freeListenQuit", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.LOCAL_ACTION_FREE_LISTEN_QUIT);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("playpage_close_podcast_intro_page", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_PLAY_PAGE_POD_DOC_POP_DISMISS);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("closeBokeAlbumNotice", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_ANNOUNCEMENT_DIALOG_DISMISS);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("playpage_yellow_bar_open_vip_or_buy", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_PLAY_PAGE_POD_DOC_BUY_OPEN);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("open_play_list_page", type)) {
            Intent intent = new Intent();
            intent.setAction(ActionConstants.ACTION_PLAY_PAGE_OPEN_PLAY_LIST);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("playpage_feedback_ok", type)) {
            JSONObject jsonObject = args.optJSONObject("data");
            long trackId = 0;
            int typeSurvey = -1;
            if (jsonObject != null) {
                trackId = jsonObject.optLong("trackId");
                typeSurvey = jsonObject.optInt("type");
            }
            Intent intent = new Intent(ActionConstants.ACTION_TIP_HAS_SUBMIT_NOTIFY);
            Bundle bundle = new Bundle();
            bundle.putInt(ActionConstants.ACTION_TIP_SURVEY_TYPE, typeSurvey);
            bundle.putLong(ActionConstants.ACTION_TIP_SURVEY_TRACK_ID, trackId);
            intent.putExtra(ActionConstants.ACTION_TIP_SURVEY_BUNDLE, bundle);
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
        } else if (TextUtils.equals("h5_notifyapp_playpage_share_goldensentence", type)) {
            try {
                JSONObject jsonObject = args.optJSONObject("data");
                long trackId = 0;
                if (jsonObject != null) {
                    trackId = jsonObject.optLong("trackId");
                }
                Intent intent = new Intent("notifyapp_playpage_share_goldensentence");
                intent.putExtra("trackId", trackId);
                intent.putExtra("goldenContent", jsonObject.optString("goldenContent"));
                LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(intent);
            }catch (Throwable t) {}
        } else if (TextUtils.equals("h5_notifyapp_agentRefreshBoBoConfig", type)) {
            ChildXmlyTipManager.INSTANCE.updateAgentConfig();
        } else if (TextUtils.equals("newUserGift", type)) {
            try {
                final MainActionRouter router = Router.getActionRouter(Configure.BUNDLE_MAIN);
                if (null == router) {
                    return;
                }
                IMainFunctionAction functionAction = router.getFunctionAction();
                if (null == functionAction) {
                    return;
                }

                functionAction.refreshHomepageRecommendFragmentOnly();

            } catch (Throwable t) {
                t.printStackTrace();
            }
        }

        if (hybridContainer.getAttachFragment() instanceof NativeHybridFragment) {
            IWebViewResultCallback webViewResultCallback = ((NativeHybridFragment) hybridContainer.getAttachFragment()).getWebViewResultCallback();
            if (webViewResultCallback != null) {
                if (TextUtils.equals("couponOfAlbum", type)) {
                    webViewResultCallback.onWebViewResultCallback(true, type, albumId, couponId);
                } else if (TextUtils.equals("inviteQuora", type)) {
                    webViewResultCallback.onWebViewResultCallback(type);
                } else if (TextUtils.equals("xyPayFinish", type)) {
                    webViewResultCallback.onWebViewResultCallback(type);
                } else if (TextUtils.equals("checkinSuccess", type)) {
                    String action = "";
                    JSONObject dataJson = args.optJSONObject("data");
                    if (dataJson != null) {
                        albumId = dataJson.optLong("albumId");
                        action = dataJson.optString("action");
                    }
                    webViewResultCallback.onWebViewResultCallback(albumId, action);
                } else if (TextUtils.equals("parental_facial_recognition", type)) {
                    try {
                        JSONObject json = args.optJSONObject("data");
                        if (json != null) {
                            boolean result = json.optBoolean("result");
                            webViewResultCallback.onWebViewResultCallback(type, result);
                        }
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                } else if (TextUtils.equals("userResearch", type)) {
                    webViewResultCallback.onWebViewResultCallback(type);
                } else {
                    webViewResultCallback.onWebViewResultCallback(true, type, activityId);
                }
            }
            if (args != null && args.has("isVip") && UserInfoMannage.getInstance().getUser() != null) {
                UserInfoMannage.getInstance().setIsVip(isVip);
            }
            callback.callback(NativeResponse.success());
        }
    }

    private void report(String type, JSONObject args) {
        try {
            IReporter.DEFAULT.directReport("VipStatusNotifyFromJS", type, null, args.toString());
        } catch (Throwable ignore) {
        }
    }
}
