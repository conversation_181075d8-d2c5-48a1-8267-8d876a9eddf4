package com.ximalaya.ting.android.host.service;

import static com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost.KEY_LOCK_SCREEN_CHECKBOX_CHECKED;
import static com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost.KEY_LOCK_SCREEN_OPEN;

import android.app.Activity;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArraySet;
import androidx.collection.LongSparseArray;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.downloadservice.DownloadUtil;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadCallback;
import com.ximalaya.ting.android.downloadservice.base.IDownloadService;
import com.ximalaya.ting.android.downloadservice.database.DBDataSupport;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.earn.statistics.PushArrivedTraceManager;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.player.LocalMediaService;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.LockScreenActivity;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.EmergencyDialogFragment;
import com.ximalaya.ting.android.host.fragment.NoNetworkHintFragment;
import com.ximalaya.ting.android.host.fragment.offsale.OffSaleUtils;
import com.ximalaya.ting.android.host.fragment.play.PlayBarFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.ArriveTraceManager;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.EmergencyPlanManager;
import com.ximalaya.ting.android.host.manager.ILockScreenListener;
import com.ximalaya.ting.android.host.manager.OuterItingManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteShareManager;
import com.ximalaya.ting.android.host.manager.PlayTrackProgressManager;
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager;
import com.ximalaya.ting.android.host.manager.ToListenManager;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.activity.ActivityManager;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.adrtb.AdSdkRtbManager;
import com.ximalaya.ting.android.host.manager.ad.adrtb.SdkRtbSyncBatchUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2;
import com.ximalaya.ting.android.host.manager.alarm.AlarmHintPlayerManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MyclubActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.device.WiFiDeviceController;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenHostManager;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.history.AlbumFootPrintUtil;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.notification.PushManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.manager.play.AiSoundTransformManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.play.PlayRealTimeStatManager;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.play.timelinecard.PlayTimelineCardManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.soundpatch.SoundPatchController;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd;
import com.ximalaya.ting.android.host.model.payment.UniversalPayment;
import com.ximalaya.ting.android.host.model.play.PlayRecordNumDataModel;
import com.ximalaya.ting.android.host.play.util.PlayDataUtils;
import com.ximalaya.ting.android.host.service.xmremotecontrol.XmRemoteControlUtil;
import com.ximalaya.ting.android.host.socialModule.talkcard.PlayTimelineCardActionRecordManager;
import com.ximalaya.ting.android.host.util.CheckPlayManager;
import com.ximalaya.ting.android.host.util.DownloadTracksUtil;
import com.ximalaya.ting.android.host.util.LockScreenUtil;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.onekey.DailyNewsLogicManager;
import com.ximalaya.ting.android.host.util.onekey.DailyNewsUtil;
import com.ximalaya.ting.android.host.util.other.LocalConfigCenterHelper;
import com.ximalaya.ting.android.host.util.other.LocalTraceMarkPointHelper;
import com.ximalaya.ting.android.host.util.play.SubscribeUnlockUtil;
import com.ximalaya.ting.android.host.util.server.DownloadTools;
import com.ximalaya.ting.android.host.util.server.LiveStatisticsUtils;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayPageDocInfoProvider;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.manager.PlayBarUbtResourceManager;
import com.ximalaya.ting.android.opensdk.model.BaseInfoOnErrorModel;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.album.Announcer;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager.IOnPlayListChange;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.service.IMixPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerBaseInfoRequestListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListenerExtension;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.soundpatch.ICommercialSoundPatchControlStatusCallBack;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorStatisticManager;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayStatisticsUploaderManager;
import com.ximalaya.ting.android.opensdk.util.FreeListenV2Util;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.ToListenUtil;
import com.ximalaya.ting.android.player.XMediaPlayerConstants;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Set;


/**
 * <AUTHOR>
 */
public class TingLocalMediaService extends LocalMediaService
        implements IXmAdsStatusListener, IOnPlayListChange, IMixPlayerStatusListener,
        IXmPlayerBaseInfoRequestListener, ICommercialSoundPatchControlStatusCallBack, AlbumEventManage.CollectListener,
        IXmPlayerStatusListenerExtension {
    public static final String ACTION_PLAY_LIST_COMPLETE = "com.ximalaya.ting.android.host" +
            ".service.action_play_list_complete";

    public static final String NETTYPE_WIFI = "WIFI";
    public static final String NETTYPE_CMWAP = "2G/3G/4G";
    private int mCDN_TRAFFIC_CBATTERY;

    private IMainFunctionAction.IYaoyiYaoManager yaoyiYaoAdManage;
    private static TingLocalMediaService mInstanse;

    // 是否显示了播放错误的弹窗
    public static boolean isShowPlayError = false;
    public static DialogBuilder mPlayErrorDialog;
    public static long lastErrorTime;
    private String noCopyrightMsg = "版权方要求，该资源在该地区无法播放";
    private String mWorkRemovedMsg = "当前内容已下架，带来不便请您谅解~";
    private final LongSparseArray<AnchorAlbumAd> mAnchorAlbumAdMap = new LongSparseArray();
    private boolean willRestoreMapOnSwitch = true;
    //    private SkipModel mSkipModel;
    private BroadcastReceiver mScreenBroadcastReceiver;
    public static boolean mPauseByFlowDialog;
    private long updateHistoryTime;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd", Locale.getDefault());
    private boolean mHasInit;
    private static Track mLastLiveTrack;

    public static boolean mCurScreenOFF;    // 当前屏幕是否灭屏

    private boolean alreadyEnterPlayFragment;
    private ILockScreenListener mScreenListener;
    private Set<IStopPlayDueToFreeListenPermissionListener> mStopPlayDueToFreeListenPermissionListeners = new ArraySet<IStopPlayDueToFreeListenPermissionListener>();

    private TingLocalMediaService() {

    }

    @Override
    public void onMixStart() {
        // 清空XmPlayManager里的缓存歌曲
        if (mContext != null) {
            XmPlayerManager playerManager = XmPlayerManager.getInstance(mContext);
            if (playerManager != null) {
                playerManager.clearCurTrackCache();
            }
        }
    }

    @Override
    public void onMixPause() {

    }

    @Override
    public void onMixStop() {

    }

    @Override
    public void onMixComplete() {

    }

    @Override
    public void onMixProgressUpdate(int percent) {

    }

    @Override
    public void onMixError(String url, int code, String msg) {

    }

    @Override
    public void onMixStatusChanged(double key, boolean isPlaying, String state, long curPosition) {

    }

    @Override
    public void onMixSoundComplete(double key) {

    }

    @Override
    public void onTrackBaseInfoBackSuccess(Track track) {
        if (track == null || track.getType() == Track.TYPE_DUBSHOW) {
            return;
        }

        PlayableModel curPlaymodel = XmPlayerManager.getInstance(mContext).getCurrSound();
        if (curPlaymodel != null &&
                curPlaymodel instanceof Track &&
                curPlaymodel.getDataId() == track.getDataId()) {
            Track oldTrack = (Track) curPlaymodel;
            oldTrack.updateBaseInfoByTrack(track);

            if (mCurModel instanceof Track) {
                ((Track) mCurModel).updateBaseInfoByTrack(track);
            }

            XmPlayerManager.getInstance(mContext).updateTrackInPlayList(oldTrack);
            XmPlayerManager.getInstance(mContext).onPlayerObtainTrackInfo(oldTrack);

            if (alreadyEnterPlayFragment && checkPauseByFreeListenV2(track, "onTrackBaseInfoBackSuccess")) {
                return;
            }

            //更新下载声音的 isAuthorized 字段
            if (RouteServiceUtil.getDownloadService().isDownloaded(oldTrack)) {
                if (track.isPaid() && !track.isFree() && !track.isAuthorized()) {
                    CustomToast.showFailToast(R.string.host_sound_not_bought);
                }
                BaseDownloadTask downloadTask = RouteServiceUtil.getDownloadService().
                        queryTaskFromCacheById(oldTrack.getDataId());
                if (downloadTask != null && downloadTask.getTrack() != null) {
                    downloadTask.getTrack().setAuthorized(oldTrack.isAuthorized());
                    downloadTask.getTrack().setAuthorizedType(oldTrack.getAuthorizedType());
                    MyAsyncTask.execute(new Runnable() {
                        @Override
                        public void run() {
                            DBDataSupport.updateTrack(downloadTask.getTrack());
                        }
                    });
                }
            }
        }

    }

    @Override
    public void onTrackBaseInfoBackError(BaseInfoOnErrorModel onErrorModel) {
        if (onErrorModel == null) {
            return;
        }

        PlayErrorStatisticManager.getSingleInstance().onPlayServerResponseError(onErrorModel.code, onErrorModel.message);
        Track curTrack = PlayTools.getCurTrack(mContext);
        if (curTrack != null &&
                curTrack.getDataId() == onErrorModel.trackId) {
            if (onErrorModel.code == 927) {//没有版权
                noCopyrightMsg = onErrorModel.message;
                if (!PlayerManager.getInstanse().isPlayFragmentVisable()) {
                    // 播放页显示的情况下，播放有专门的提示，这边就不做提示了
                    CustomToast.showFailToast(onErrorModel.message);
                }
                Track track = curTrack;
                track.setHasCopyRight(false);
                track.setUpdateStatus(true);
                XmPlayerManager.getInstance(mContext).updateTrackInPlayList(track);
                PlayTools.pause(mContext, PauseReason.Common.CODE_927);
            }
            if (onErrorModel.code == 929 && (
                    !RouteServiceUtil.getDownloadService().isDownloaded(curTrack) || curTrack.isPayTrack())) {//没有版权
                mWorkRemovedMsg = onErrorModel.message;
                if (!PlayerManager.getInstanse().isPlayFragmentVisable()) {
                    // 播放页显示的情况下，播放有专门的提示，这边就不做提示了
                    CustomToast.showFailToast(onErrorModel.message);
                }
                Track track = curTrack;
                track.setHasRemoved(true);
                track.setUpdateStatus(true);
                XmPlayerManager.getInstance(mContext).updateTrackInPlayList(track);
                PlayTools.pause(mContext, PauseReason.Common.CODE_929);
            }
            if (onErrorModel.code == DTransferConstants.NEED_SUBSCRIBE_CODE) {
                curTrack.setSubscribeFreeAuthorized(false);
                XmPlayerManager.getInstance(mContext).updateTrackInPlayList(curTrack);
                if (PlayTools.isCurrentTrackPlaying(mContext, curTrack) &&
                        !PlayerManager.getInstanse().isPlayFragmentVisable() &&
                        curTrack.getAlbum() != null) {
                    PlayTools.pause(mContext, PauseReason.Common.CODE_707);
                    SubscribeUnlockUtil.INSTANCE.showGuideSnackBar(curTrack.getAlbum().getAlbumId());
                }
            }
        }
    }

    @Override
    public void onPlayingSoundPatchStart() {
    }

    @Override
    public void onPlayingSoundPatchStop() {
        PlayCompleteManager.INSTANCE.setCountDown();
    }

    @Override
    public void onNotPlayingSoundPatchStart() {
    }

    @Override
    public void onNotPlayingSoundPatchStop() {
    }

    @Override
    public void onCollectChanged(boolean collect, long id) {
        Track curTrack = PlayTools.getCurTrack(mContext);
        if (curTrack == null || curTrack.getAlbum() == null || curTrack.getAlbum().getAlbumId() != id || !collect) {
            return;
        }
        curTrack.setSubscribeFreeAuthorized(true);
        XmPlayerManager.getInstance(mContext).updateTrackInPlayList(curTrack);
    }

    @Override
    public void onRequestPlayUrlBegin() {

    }

    @Override
    public void onRequestPlayUrlSuccess() {

    }

    @Override
    public void onRequestPlayUrlError(int code, String message) {

    }

    @Override
    public void onAudioAuditionOver(Track track) {

    }

    @Override
    public void onChildAiTimbreUrlGet(boolean success, boolean isDownload, String type) {
        Logger.d(TAG, "TingLocalMediaService onChildAiTimbreUrlGet success=" + success + ", isDownload=" + isDownload + ", type=" + type);
        AiSoundTransformManager.INSTANCE.onChildAiTimbreUrlGet(success, isDownload, type);
    }

    private static class HolderClass {
        private final static TingLocalMediaService instance = new TingLocalMediaService();
    }

    public static synchronized TingLocalMediaService getInstance() {
        return HolderClass.instance;
    }

    @Override
    public void init(@NonNull final Context context, @NonNull XmPlayerManager playerManager) {
        if (mHasInit) {
            return;
        }
        mHasInit = true;
        super.init(context, playerManager);
        playerManager.addAdsStatusListener(this);
        playerManager.addPlayListChange(this);
        playerManager.addBaseInfoRequestListener(this);
        playerManager.addMixPlayerStatusListener(this);
        AlbumEventManage.addListener(this);
        RouteServiceUtil.getDownloadService().registerDownloadCallback(mDownloadCallback);
        SoundPatchController.registerSoundPatchControlStatusCallBack(this);
        PlayCompleteManager.INSTANCE.init();
        try {
            Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        new MyAsyncTask<Void, Void, Void>() {
                            @Override
                            protected Void doInBackground(Void... params) {
                                if (yaoyiYaoAdManage == null) {
                                    synchronized (TingLocalMediaService.class) {
                                        if (yaoyiYaoAdManage == null) {
                                            try {
                                                yaoyiYaoAdManage =
                                                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getYaoyiYaoManagerInstance(context);
                                                yaoyiYaoAdManage.onStartCommand();
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                }
                                return null;
                            }
                        }.myexec();
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                    }
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }

        addScreenChangeBroadCast();
        addBluetoothBroadcast();
        addSkipHeadTailFetchBroadCast();
        updateTodayPlayNumRecord(true);
        CheckPlayManager.init();
    }

    private void addScreenChangeBroadCast() {
        if (mScreenBroadcastReceiver == null) {
            mScreenBroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    Logger.d(TAG, action + " : " + System.currentTimeMillis());
                    boolean isOpenLockScreen = MmkvCommonUtil.getInstance(context).getBooleanCompat(KEY_LOCK_SCREEN_OPEN, true);
                    boolean isLockScreenCheckBoxChecked = MmkvCommonUtil.getInstance(context).getBooleanCompat(KEY_LOCK_SCREEN_CHECKBOX_CHECKED, true);
                    Logger.d(TAG, "ScreenChangeBroadCast action: " + action + ", isOpenLockScreen: " + isOpenLockScreen + ", isLockScreenCheckBoxChecked: " + isLockScreenCheckBoxChecked + " " + System.currentTimeMillis());
                    if (isLockScreenCheckBoxChecked && isOpenLockScreen) {
                        if (mScreenListener != null) {
                            mScreenListener.onScreenAction(action);
                        }
                        LockScreenUtil.checkIfStartLockScreenActivity(context, action);
                    }

                    mCurScreenOFF = Intent.ACTION_SCREEN_OFF.equals(action);
                }
            };
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            try {
                if (BaseApplication.mAppInstance != null) {
                    BaseApplication.mAppInstance.registerReceiver(mScreenBroadcastReceiver, filter);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void removeScreenChangeBroadCast() {
        if (mScreenBroadcastReceiver != null) {
            try {
                if (BaseApplication.mAppInstance != null) {
                    BaseApplication.mAppInstance.unregisterReceiver(mScreenBroadcastReceiver);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            mScreenBroadcastReceiver = null;
        }
    }

    private void addBluetoothBroadcast() {
        //BluetoothStateBroadcastReceiver.register(this);
    }

    public void removeBluetoothBroadCast() {
        //BluetoothStateBroadcastReceiver.unregister(this);
    }

    private void addSkipHeadTailFetchBroadCast() {
        PlayCompleteManager.INSTANCE.addSkipHeadTailFetchBroadcastReceiver();
    }

    private void removeSkipHeadTailFetchBroadCast() {
        PlayCompleteManager.INSTANCE.removeSkipHeadTailFetchBroadcastReceiver();
    }

    public IMainFunctionAction.IYaoyiYaoManager getYaoyiYaoAdManage() {
        if (yaoyiYaoAdManage == null) {
            synchronized (TingLocalMediaService.class) {
                if (yaoyiYaoAdManage == null) {
                    try {
                        yaoyiYaoAdManage =
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getYaoyiYaoManagerInstance(mContext);
                        yaoyiYaoAdManage.onStartCommand();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return yaoyiYaoAdManage;
    }

    @Override
    public boolean onError(XmPlayerException exception) {
        Logger.logToSd("TingLocalMediaService onError:" + exception);
        PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(false);
        PlayableModel playableModel = mPlayerManager.getCurrSound();
        if (playableModel != null) {
            PushArrivedTraceManager.INSTANCE.getInstance().reportStartPlayTrace(playableModel.getDataId(), false);
            try {
                if (Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE) != null
                        && (PlayTools.isPlayModelLive(playableModel)
                        || PlayTools.isPlayModelEntLive(playableModel)
                        || PlayTools.isPlayModelCourseLive(playableModel))) {
                    return super.onError(exception);
                }
                if (Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB) != null
                        && PlayTools.isPlayModelMyclubLive(playableModel)) {
                    Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB).getFunctionAction().onPlayLiveAudioError();
                    return super.onError(exception);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        boolean isConnectToNetwork = NetworkType.isConnectTONetWork(mContext);
        Logger.logToSd("TingLocalMediaService onError showDialog");
        isShowPlayError = true;
        lastErrorTime = System.currentTimeMillis();
        Activity act = MainApplication.getTopActivity();
        if (BaseUtil.isForegroundIsMyApplication(mContext) && act != null && !act.isFinishing()) {
            if (MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(PreferenceConstantsInHost.KEY_PLAY_ERROR_SHOW_DEBUG_DIALOG, false)
                    && exception != null && exception.getErrorTypeForDebug() > 0) {
                showErrorDialogForDebug(act, exception);
                return super.onError(exception);
            }

            if (exception != null
                    && (exception.getWhat() == 2010 || exception.getWhat() == 2011)
                    && PlayTools.isPlayModeRadioOrSchedule(playableModel)) {
                // 播放类型是广播或广播回听，且 exception.getWhat() == 2010 或 exception.getWhat() ==
                // 2011，给出Toast提示
                // (2010 回听内容已过期，请从广播标签重新查找此广播台收听当前直播节目;2011 电台临时下线/获取内容失败，请稍后再试)
                //电台流获取失败，请稍后再试
                if (exception.getWhat() == 2011 && exception.getMessage() != null && exception.getMessage().contains("电台流获取失败，请稍后再试")) {
                    showRadioErrorDialog(act);
                } else if (TextUtils.isEmpty(exception.getMessage())) {
                    CustomToast.showToast("获取内容失败，请稍后再试");
                } else {
                    CustomToast.showToast(exception.getMessage());
                }
            } else {
                if (!showEmergencyDialog(act)) {
                    if (exception != null) {
                        String errorMsg = exception.getMessage();
                        // 下载的文件被删除了
                        if (exception.getWhat() == XmPlayerException.ERROR_SAVE_PATH_NO_EXIT) {
                            showDownloadedSourceDeleteDialog(act, exception, null);
                        } else if (errorMsg != null && errorMsg.contains(XMediaPlayerConstants.PATH_DOWNLOADED_FILE_DELETE_SUCCESS)) {
                            deleteDownloadTask(act);
                            showDownloadedSourceDeleteDialog(act, exception, "下载的声音文件已损坏");
                        } else if (exception.getWhat() == 706) {
                            if (!XmAdsManager.isPlayFragmentShowing) {
                                CustomToast.showToast(exception.getMessage());
                            }
                        }
//                        else if (exception.getWhat() == DTransferConstants.NEED_SUBSCRIBE_CODE) {
//                            PlayTools.pause(mContext);
//                            if (!PlayerManager.getInstanse().isPlayFragmentVisable()) {
//                                if (playableModel instanceof Track && ((Track) playableModel).getAlbum()!=null) {
//                                    SubscribeUnlockUtil.INSTANCE.showGuideSnackBar(((Track) playableModel).getAlbum().getAlbumId());
//                                }
//                            }
//                        }
                        else if (!exception.isFromServiceError()) {
                            showPowerSaveDialog(act);
                        } else if (exception.getWhat() != 927 && exception.getWhat() != DTransferConstants.NEED_SUBSCRIBE_CODE) {
                            // 无版权的已经有toast提示了，不要再弹窗了
                            showRetryDialog(act, exception);
                        }
                    }
                }
            }
        } else if (!BaseUtil.isForegroundIsMyApplication(mContext)) {
            ToolUtil.cancelNotification(mContext, AppConstants.INFORM_NET_ERR);
            try {
                PendingIntent pi;
                if (isConnectToNetwork) {
                    Intent i = MainActivity.getMainActivityIntent(mContext);
                    i.putExtra(AppConstants.INTENT_PARCEL_CLASS_NAME,
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().getPlayFragmentClass().getName());

                    i.putExtra("willShowDialog", true);
                    pi = PendingIntent.getActivity(mContext, 0, i,
                            XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                } else {
                    Intent i = new Intent(Settings.ACTION_WIRELESS_SETTINGS);
                    pi = PendingIntent.getActivity(mContext, 0, i,
                            XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                }

                String ticker = isConnectToNetwork ?
                        (XmPlayerManager.getInstance(mContext).isOnlineSource() ?
                                "发生网络错误,已暂停播放" : "播放错误请重试")
                        : "当前网络不可用,点击检查你的网络设置";
                Notification notification = ToolUtil.createNotification(mContext, "提示",
                        ticker, ticker, pi);

                NotificationManager nm = SystemServiceManager.getNotificationManager(mContext);

                if (notification != null && nm != null) {
                    nm.notify(AppConstants.INFORM_NET_ERR, notification);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return super.onError(exception);
    }

    private boolean showEmergencyDialog(Activity activity) {
        if (!ToolUtil.activityIsValid(activity)) {
            return false;
        }
        if (EmergencyPlanManager.getInstance().getEmergencyAnnouncement(EmergencyPlanManager.POSITION_PLAY_PAGE) == null) {
            return false;
        }
        if (activity instanceof MainActivity) {
            EmergencyDialogFragment dialog = new EmergencyDialogFragment();
            dialog.show(((MainActivity) activity).getSupportFragmentManager(), "emergency");
            return true;
        }
        return false;
    }

    private static void deleteDownloadTask(Activity activity) {
        PlayableModel currSound = XmPlayerManager.getInstance(activity).getCurrSound();
        if (currSound instanceof Track) {
            BaseDownloadTask downloadTask = RouteServiceUtil.getDownloadService().queryTaskFromCacheById(currSound.getDataId());
            ((Track) currSound).setDownloadedSaveFilePath(null);
            XmPlayerManager.getInstance(activity).updateTrackDownloadUrlInPlayList((Track) currSound);
            if (downloadTask != null) {
                RouteServiceUtil.getDownloadService().deleteDownloadTask(downloadTask);
            }
        }
    }

    // 显示下载好的资源删除的弹窗
    private static void showDownloadedSourceDeleteDialog(Activity activity, XmPlayerException exception, String defaultMsg) {
        if (!ToolUtil.activityIsValid(activity)) {
            return;
        }

        String msg = exception != null ? exception.getMessage() : null;
        if (msg != null && (msg.length() > 100 || msg.contains("stackTrace"))) {
            // 这里主要是防止一些内部的错误日志被展示给用户
            msg = null;
        }
        if (msg == null) {
            msg = defaultMsg;
        }

        DialogBuilder dialogBuilder = new DialogBuilder(activity)
                .setMessage(msg == null ? "下载的声音文件已被其他清理软件误删" : msg)
                .setOkBtn("在线播放", new DialogCallback() {
                    @Override
                    public void onExecute() {
                        PlayableModel currSound =
                                XmPlayerManager.getInstance(activity).getCurrSound();
                        if (currSound instanceof Track) {
                            BaseDownloadTask downloadTask =
                                    RouteServiceUtil.getDownloadService().
                                            queryTaskFromCacheById(currSound.getDataId());

                            ((Track) currSound).setDownloadedSaveFilePath(null);

                            XmPlayerManager.getInstance(activity).updateTrackDownloadUrlInPlayList((Track) currSound);

                            if (downloadTask != null) {
                                RouteServiceUtil.getDownloadService().deleteDownloadTask(downloadTask);
                            }
                        }

                        XmPlayerManager.getInstance(activity).play();
                    }
                }).setCancelBtn("重新下载", new DialogCallback() {
                    @Override
                    public void onExecute() {
                        PlayableModel currSound =
                                XmPlayerManager.getInstance(activity).getCurrSound();
                        if (currSound instanceof Track) {
                            RouteServiceUtil.getDownloadService().addTask((Track) currSound, null);
                        }
                        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                            IMyListenFragmentAction fragAct = MyListenRouterUtil.getFragAction();
                            Activity act = BaseApplication.getMainActivity();
                            if (fragAct != null && act instanceof MainActivity) {
                                BaseFragment2 frag = fragAct.newDownloadingFragment();
                                if (frag != null) {
                                    ((MainActivity) act).startFragment(frag);
                                }
                            }
                        });
                    }
                });
        dialogBuilder.setcancelApplyToButton(false);
        dialogBuilder.showConfirm();
    }

    public static void showRadioErrorDialog(Activity activity) {
        if (!ToolUtil.activityIsValid(activity)) {
            return;
        }
        DialogBuilder dialogBuilder = new DialogBuilder(activity).setMessage("电台信号不稳，正在修复")
                .setOkBtn("确定", null).setCancelBtn("取消", null);
        dialogBuilder.showConfirm();
    }

    public static void showRetryDialog(Activity activity, @Nullable XmPlayerException exception) {
        if (!ToolUtil.activityIsValid(activity)) {
            return;
        }

        if (exception != null && exception.getWhat() == 929 && OffSaleUtils.isMatchAb()) {
            return;
        }

        String errorMsg = "播放出错,是否重试";

        if (exception != null && !TextUtils.isEmpty(exception.getMessage())) {
            errorMsg = exception.getMessage();
            if (errorMsg.contains("Unable to connect")) {
                errorMsg = "网络错误,请切换网络重试";
            } else if (errorMsg.contains("exo")) {
                errorMsg = "播放错误,请稍后重试";
            }
        }

        boolean isNetNoConnectError = exception != null && exception.getWhat() == XmPlayerException.ERROR_NO_NET;

        String buttonContent = isNetNoConnectError ? "检查网络" : "重试";

        DialogBuilder dialogBuilder = new DialogBuilder(activity).setMessage(errorMsg)
                .setOkBtn(buttonContent, new DialogCallback() {
                    @Override
                    public void onExecute() {
                        if (isNetNoConnectError) {
                            ToolUtil.checkIntentAndStartActivity(activity, new Intent(Settings.ACTION_WIRELESS_SETTINGS));
                        } else {
                            ToolUtil.cancelNotification(
                                    BaseApplication.getMyApplicationContext(),
                                    AppConstants.INFORM_NET_ERR);
                            XmPlayerManager.getInstance(activity).play();
                        }
                        userTrackOnErrorDialogButtonClicked("重试");
                    }
                }).setCancelBtn("取消", new DialogCallback() {
                    @Override
                    public void onExecute() {
                        userTrackOnErrorDialogButtonClicked("取消");
                    }
                });
        dialogBuilder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                resertPlayErrorSign();
            }
        });
        dialogBuilder.showConfirm();
        userTrackOnShowDialog("发生网络错误弹窗");
        mPlayErrorDialog = dialogBuilder;
    }

    private void showPowerSaveDialog(Activity activity) {
        String message = "1. 当前网络不可用，请先检查你的网络连接是否正常；\r\n\n2. 若仍提示网络错误，请尝试按照下方教程修改系统设置处理该问题。";

        DialogBuilder dialogBuilder = new DialogBuilder(activity).setMessage(message).setOkBtn(
                "查看如何设置", new DialogCallback() {
                    @Override
                    public void onExecute() {
                        if (activity != null && activity instanceof MainActivity) {
                            if (NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
                                String url = "https://pages.ximalaya.com/mkt/act/bcea0fdd4032ea51";
                                ((MainActivity) activity).startFragment(NativeHybridFragment.newInstance(url, true));
                            } else {
                                NoNetworkHintFragment fragment = new NoNetworkHintFragment();
                                ((MainActivity) activity).startFragment(fragment);
                            }
                        }
                        userTrackOnReasonDialogButtonClicked("查看如何关闭");
                    }
                }).setCancelBtn("取消", new DialogCallback() {
            @Override
            public void onExecute() {
                userTrackOnReasonDialogButtonClicked("取消");
            }
        });
        dialogBuilder.showConfirm();
        userTrackOnShowDialog("引导关闭省电功能弹窗");
    }

    @Override
    public void onSoundPlayComplete() {
        PlayableModel playableModel = XmPlayerManager.getInstance(mContext).getCurrSound();
        if (playableModel instanceof Track) {
            Track track = (Track) playableModel;

            PlayCompleteManager.INSTANCE.resetSkipTailTime(0);
            PlayCompleteManager.INSTANCE.playCompleteRecommendTrack(mContext, track);
            PlayCompleteManager.INSTANCE.resetAutoPlay();

            long trackId = playableModel.getDataId();
            SubordinatedAlbum album = ((Track) playableModel).getAlbum();
            long albumId = 0;
            if (album != null) {
                albumId = album.getAlbumId();
            }
            PlayCompleteRecommendManager.getInstance().reportOnSwitch(trackId, albumId, 100);

            if (PlayTools.isPlayModelMyclubReplay(playableModel)) {
                try {
                    Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                            .getFunctionAction().onPlayReplayAudioComplete(track.getLiveRoomId());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        if (playableModel instanceof Track && ElderlyModeManager.getInstance().isElderlyMode()) {
            ElderlyModeManager.getInstance().reportElderlyCoursePlayInfo((Track) playableModel, true);
        }
        checkClearDownloadedTrackFile(playableModel);
    }

    private void checkClearDownloadedTrackFile(PlayableModel playableModel) {
        if (!(playableModel instanceof Track)) {
            return;
        }
        Track track = (Track) playableModel;
        boolean isClearDownloadedTrack = MmkvCommonUtil.getInstance(mContext).getBoolean(PreferenceConstantsInHost.KEY_OPEN_CLEAR_DOWNLOAD, false);
        if (!isClearDownloadedTrack) {
            return;
        }
        RouteServiceUtil.getDownloadService().deleteDownloadedTasks(track);
        PlayPageDocInfoProvider.deleteCocFileAsync(mContext, track.getDataId());
    }

    @Override
    public void onBufferingStart() {
        super.onBufferingStart();
        LiveStatisticsUtils.doLiveStatisticsOnBufferStart(mPlayerManager.getCurrSound());
    }

    @Override
    public void onBufferingStop() {
        super.onBufferingStop();
        LiveStatisticsUtils.doLiveStatisticsOnBufferStop(mPlayerManager.getCurrSound());
    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onPlayProgress(currPos, duration);
        }
        super.onPlayProgress(currPos, duration);
        PlayCompleteManager.INSTANCE.onPlayProgress(currPos, duration);
        if (mPlayerManager != null && mCurModel instanceof Track) {
            boolean isStopTrack = false;
            Track track = (Track) mCurModel;
            if (FreeListenConfigManager.isFreeListenV2Open() && track.getAlbum() != null && track.getAlbum().supportFreeListen()) {
                // 符合畅听条件的，不走下面vip的检查
                // 畅听本身的权限检查在onPlayStart里面进行，播放过程中时长耗尽的话在播放进程里面会处理，所以这里不做任何事情
            } else {
                if (useNewVipCheck()) {
                    // track.canPlayTrack() 这个表示用户有权限。在用户是vip状态下，这个不会更新数据库的authorized状态为false
                    if (!track.canPlayTrack() && !PlayTools.checkVipCanPlayNew(track)) {
                        Logger.i(TAG, "0-id=" + track.getDataId() + ",title=" + track.getTrackTitle()
                                + ",userVip=" + track.isUserVip() + ",authorizedSource=" + track.getAuthorizedSource());
                        Logger.logToFile("new strategy, stop play because of authorized");
                        mPlayerManager.stop(PauseReason.StopBusiness.TingLocal_NO_AUTHORIZED);
                        isStopTrack = true;
                    } else if (track.isAuthorized() && track.getAuthorizedSource() == Track.AUTHORIZED_TYPE_CHILD_VIP_LISTEN) {
                        // 儿童特殊处理下
                        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
                        if (user != null) {
                            if (!user.isChildVip() && !user.isShadowChild()) {
                                Logger.logToFile("new strategy-1, stop play because of authorized");
                                mPlayerManager.stop(PauseReason.StopBusiness.TingLocal_NO_CHILD_VIP);
                                isStopTrack = true;
                            }
                        }
                    }
                } else {
                    if (!track.canPlayTrack() && !PlayTools.checkVipCanPlay(track)) {
                        Logger.i(TAG, "1-id=" + track.getDataId() + ",title=" + track.getTrackTitle()
                                + ",userVip=" + track.isUserVip() + ",authorizedSource=" + track.getAuthorizedSource());
                        Logger.logToFile("old strategy, stop play because of authorized");
                        mPlayerManager.stop(PauseReason.StopBusiness.TingLocal_NO_AUTHORIZED_OLD);
                        isStopTrack = true;
                    }
                }
            }

            if (!CheckPlayManager.INSTANCE.isAllowPlay(track)) {
                Logger.logToFile("超过7天没联网, 不允许播放");
                if (!isStopTrack) { // isStopTrack 不重复stop
                    mPlayerManager.stop(PauseReason.StopBusiness.TingLocal_CHECK_DOWNLOAD);
                }
            }
            if (!track.isKindOfLive()) {
                PlayCompleteRecommendManager.getInstance().handleOnProgress(currPos, duration);
                PlayCompleteManager.INSTANCE.loadPlayCompleteDataIfNeed(mContext, track, currPos, duration);
            }
        } else if (mPlayerManager != null) {
            PlayableModel playableModel = mPlayerManager.getCurrSound();
            if (playableModel instanceof Track && !playableModel.isKindOfLive()) {
                PlayCompleteManager.INSTANCE.loadPlayCompleteDataIfNeed(mContext, (Track) playableModel, currPos, duration);
            }
        }

        PlayRealTimeStatManager.INSTANCE.onPlayProgress(mCurModel, currPos, duration);
        try {
            if (PlayTools.isPlayModelMyclubReplay(mCurModel)) {
                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                        .getFunctionAction().checkReplayTail(mPlayerManager, currPos, duration);
            }
        } catch (Exception e) {
//            e.printStackTrace();
        }
        PlayTimelineCardManager.INSTANCE.onPlayProgress(currPos);
    }

    private Boolean useNewVipCheck = null;

    private boolean useNewVipCheck() {
        if (useNewVipCheck == null) {
            useNewVipCheck = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.ITEM_NEED_NEW_CHECK_VIP_STATUS, true);
        }
        return useNewVipCheck;
    }

    String lastCurUrl;

    @Override
    public void onPlayStart() {
        super.onPlayStart();
        Logger.d("f_tag","ting local media onPlayStart");
        PushManager.getInstance().onPlayStart();

        resertPlayErrorSign();

        addScreenChangeBroadCast();

        PlayBarFragment.ShowTipBroadCast.setTipsContent(MainApplication.getMyApplicationContext(), null);

        PlayableModel playableModel = mPlayerManager.getCurrSound(false);

        if (pauseByFreeListenV2(playableModel, "onPlayStart")) {
            Logger.e("z_freelisten", "TingLocalMediaService onPlayStart return >> pauseByFreeListenV2");
            return;
        } else {
            Logger.e("z_freelisten", "TingLocalMediaService onPlayStart >> pauseByFreeListenV2 return false");
        }

        if (playableModel instanceof Track) {
            PushArrivedTraceManager.INSTANCE.getInstance().reportStartPlayTrace(playableModel.getDataId(), true);
            int playCurrPositon = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).getPlayCurrPositon();
            PlayTrackProgressManager.putTrackStartProgress(playableModel.getDataId(), playCurrPositon);
        }

        if (playableModel instanceof Track && !((Track) playableModel).isHasCopyRight()) {
            PlayTools.pause(mContext, PauseReason.Common.NO_COPYRIGHT);
            CustomToast.showFailToast(noCopyrightMsg);
        }

        if (playableModel instanceof Track && ((Track) playableModel).isHasRemoved()) {
            PlayTools.pause(mContext, PauseReason.Common.HAS_REMOVED);
            CustomToast.showFailToast(mWorkRemovedMsg);
        }

        if (playableModel instanceof Track) {
            if (ChildProtectManager.isNeedOpenForbidPlayPage(mContext, (Track) playableModel)) {
                //属于未成年人保护页面，禁止打开播放页面
                PlayTools.pause(mContext, PauseReason.Common.CHILD_PROTECT_PAUSE_MAIN);
                ChildProtectManager.openForbidPlayPage((Track) playableModel);
            }
            PlayCompleteManager.INSTANCE.onPlayStart();
        }

        if (playableModel instanceof Track) {
            Announcer announcer = ((Track) playableModel).getAnnouncer();
            if (announcer != null && announcer.getAnnouncerId() == UserInfoMannage.getUid()
                    && UserInfoMannage.getUid() != 0) {
                String curUrl = XmPlayerManager.getInstance(mContext).getCurPlayUrl();
                XDCSCollectUtil.statErrorToXDCS(playableModel.getDataId() + "",
                        "onPlayStart url = " + (TextUtils.isEmpty(curUrl) ? "null" : curUrl));
            }
        }

        if (playableModel instanceof Track) {
            Track curTrack = (Track) playableModel;
            if (!curTrack.isWeikeSimplePlay) {

                if (!/*DownloadTools.trackIsDownloadedAndCheckFileIsExist
                 */RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(curTrack)
                        && NetworkUtils.isNetworkTypeNeedConfirm(NetworkUtils.getPlayType(curTrack))
                        && DownloadTools.showHintDialog) {
                    mPauseByFlowDialog = true;
                    PlayTools.pause(mContext, PauseReason.Common.NET_CONFIRM);
                    PlayTools.play(mContext);
                }

            }

        } else if (NetworkUtils.isNetworkTypeNeedConfirm(NetworkUtils.getPlayType(playableModel))
                && DownloadTools.showHintDialog) {
            PlayTools.pause(mContext, PauseReason.Common.NET_CONFIRM_2);
            PlayTools.play(mContext);
        }

        LiveStatisticsUtils.doLiveStatisticsOnPlayStart(playableModel);
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onPlayStart();
        }

        PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(true);

        if (playableModel != null && (PlayTools.isPlayModelLive(playableModel)
                || PlayTools.isPlayModelEntLive(playableModel))) {
            try {
                if (Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE) != null) {
                    Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().onLivePlayPause(false);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        AlarmHintPlayerManager alarmManager = AlarmHintPlayerManager.getInstance();
        if (alarmManager != null && alarmManager.isPlaying()) {
            alarmManager.stopPlay();
        }

        PlayRealTimeStatManager.INSTANCE.onPlayStart(playableModel);
        PlayBarUbtResourceManager.INSTANCE.onPlayStart(playableModel);

        if (playableModel instanceof Track) {
            if (XmPlayerManager.getInstance(mContext).isDLNAState()) {
                String curUrl = XmPlayerManager.getInstance(mContext).getCurPlayUrl();
                if (!TextUtils.isEmpty(curUrl) && curUrl.equals(lastCurUrl)) {
                    return;
                }
                WiFiDeviceController.pushVoice(mContext, ((Track) playableModel).isPayTrack());
                lastCurUrl = curUrl;
            }
        }

        if (playableModel instanceof Track) {
            Track track = (Track) playableModel;
            if ((track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY || track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) &&
                    (track.getChannelType() == ConstantsOpenSdk.CHANNEL_TYPE_DAILY_NEWS ||
                            track.getChannelType() == ConstantsOpenSdk.CHANNEL_TYPE_DAILY_WEIBO ||
                            track.getChannelType() == ConstantsOpenSdk.CHANNEL_TYPE_DAILY_CHANNEL_RECOMMEND) &&
                    track.getChannelId() > 0) {
                DailyNewsUtil.saveLastRequestTime(track.getChannelId());
            }
        }


        if (playableModel instanceof Track && ElderlyModeManager.getInstance().isElderlyMode()) {
            ElderlyModeManager.getInstance().reportElderlyCoursePlayInfo((Track) playableModel, false);
        }

        if (playableModel instanceof Track) {
            PlayCompleteShareManager.INSTANCE.onPlayStart((Track) playableModel);

            try {
                IMainFunctionAction iMainFunctionAction = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction();
                if (iMainFunctionAction != null && !iMainFunctionAction.hasPlayPermission(playableModel.getDataId())) {
                    XmPlayerManager.getInstance(mContext).stop(PauseReason.StopBusiness.TingLocal_NO_PERMISSION);
                }
            } catch (Throwable e) {

            }
            ArriveTraceManager.INSTANCE.onStartPlay(playableModel.getDataId());
        }

        if (mPlayerManager.isQuickListen() && playableModel instanceof Track) {
            long trackId = playableModel.getDataId();
            QuickListenDataManager.Companion.getInstance().onPlayStart(trackId);
        }

        // 拦截订阅解锁
        if (playableModel instanceof Track && !((Track) playableModel).isSubscribeFreeAuthorized()) {
            PlayTools.pause(mContext, PauseReason.Common.SubscribeFreeAuthorized);
            if (!PlayerManager.getInstanse().isPlayFragmentVisable() && ((Track) playableModel).getAlbum() != null
            ) {
                SubscribeUnlockUtil.INSTANCE.showGuideSnackBar(((Track) playableModel).getAlbum().getAlbumId());
            }
        }
        OuterItingManager.INSTANCE.notifyPlay();
    }

    /**
     * 是否被畅听逻辑拦截
     * @param playableModel
     * @return
     */
    private boolean pauseByFreeListenV2(PlayableModel playableModel, String msg) {
        if (playableModel == null) {
            return false;
        }

        if (playableModel instanceof  Track) {
            Track track = (Track) playableModel;
            return checkPauseByFreeListenV2(track, msg);
        }
        return false;
    }

    private boolean checkPauseByFreeListenV2(Track track, String msg) {
        if (track == null) {
            return false;
        }
        if (FreeListenConfigManager.isFreeListenV2Open()) {
            boolean isFreeListenAlbum = track.getAlbum() != null && track.getAlbum().supportFreeListen() && !UserInfoMannage.isVipUser();

            FreeListenLogManager.writeLog("TingLocalMediaService " + msg + " >>> isFreeListenAlbum: " + isFreeListenAlbum);

            if (isFreeListenAlbum) {
                if (!FreeListenV2Util.getInstance().checkTrackPermission(track)) {
                    Logger.logToFile("free listen v2, stop play because of authorized");

                    FreeListenLogManager.writeLog("TingLocalMediaService " + msg + " >>> checkTrackPermission return false, stop play");
                    Logger.logToFile("TingLocalMediaService checkTrackPermission return false msg=" + msg + " permissionSource ="
                            + track.getPermissionSource() + " isAuthorized =" + track.isAuthorizedOrigin());
                    mPlayerManager.stop(PauseReason.StopBusiness.TingLocal_FreeListenTimeRunOut);
                    if (AdUnLockTimeManagerV2.getInstance().isShowingRewardVideo()) {
                        // 激励视频展示过程中不能播放音贴
                        return true;
                    }
                    if (AdMakeVipLocalManager.getInstance().checkAdAudioCode()) {
                        mPlayerManager.playFreeListenTimeRunOutSoundPatch(false);
                    }
                    for (IStopPlayDueToFreeListenPermissionListener listener : mStopPlayDueToFreeListenPermissionListeners) {
                        listener.onStopPlayDueToFreeListenPermission();
                    }
                    return true;
                } else {
                    Logger.logToFile("TingLocalMediaService checkTrackPermission return true msg=" + msg + " permissionSource ="
                            + track.getPermissionSource() + " isAuthorized =" + track.isAuthorizedOrigin());
                    FreeListenLogManager.writeLog("TingLocalMediaService " + msg + " >>> checkTrackPermission return true");
                }
            }
        }
        return false;
    }

//    private void skip(int current, int duration) {
//        if (mSkipModel != null) {
//            PlayableModel curSound = mPlayerManager.getCurrSound();
//            if (curSound != null && curSound instanceof Track) {
//                SubordinatedAlbum album = ((Track) curSound).getAlbum();
//                if (album != null && album.getAlbumId() == mSkipModel.albumId) {
//                    if (mSkipModel.headSkip + mSkipModel.tailSkip > duration) {
//                        return;
//                    }
//
//                    if (current < mSkipModel.headSkip) {
//                        Logger.i("TingLocalMediaService", "跳过片头");
//                        XmPlayerManager.getInstance(mContext).seekTo(mSkipModel.headSkip);
//                    }
//
//                    if (duration - current <= mSkipModel.tailSkip && duration - current > 1000) {
//                        Logger.i("TingLocalMediaService", "跳过片尾");
//                        XmPlayerManager.getInstance(mContext).seekTo(duration - 1000);
//                    }
//                }
//            }
//        }
//    }

    @Override
    public void onPlayStop() {
        super.onPlayStop();
        PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(false);
    }

    @Override
    public void onPlayPause() {
        LiveStatisticsUtils.doLiveStatisticsOnPlayPause(mPlayerManager.getCurrSound());
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onPlayPause();
        }
        PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(false);

        PlayableModel playableModel = mPlayerManager.getCurrSound();
        if (playableModel != null
                && (PlayTools.isPlayModelLive(playableModel)
                || PlayTools.isPlayModelEntLive(playableModel))) {
            try {
                if (Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE) != null) {
                    Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().onLivePlayPause(true);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (System.currentTimeMillis() - updateHistoryTime > 5000) {
            updateHistoryTime = System.currentTimeMillis();
            ICloudyHistory historyManager = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
            if (historyManager != null) {
                historyManager.syncCloudHistory(false);
            }
        }

        PlayRealTimeStatManager.INSTANCE.onPlayPause();
    }

    @Override
    public String getDownloadPlayPath(Track track) {
        return super.getDownloadPlayPath(track);
    }

    @Override
    public void isOldTrackDownload(final Track track) {
        final Activity mActivity = BaseApplication.getTopActivity();
        if (track == null) {
            return;
        }
        if (mActivity == null || !(mActivity instanceof MainActivity))
            return;
        new DialogBuilder(mActivity).setMessage("播放器升级,该音频暂时无法播放,请选择")
                .setOkBtn("重新下载").setOkBtn(new DialogBuilder.DialogCallback() {

                    @Override
                    public void onExecute() {
                        if (NetworkType.getNetWorkType(mActivity) == NetworkType.NetWorkType.NETWORKTYPE_INVALID) {
                            CustomToast.showFailToast("没有网络");
                            return;
                        }
                        if (RouteServiceUtil.getDownloadService().checkNeedLogin()) {
                            return;
                        }
                        if (TextUtils.isEmpty(track.getDownloadUrl())) {
                            HashMap<String, String> params = new HashMap<>();
                            params.put("trackId", track.getDataId() + "");
                            params.put("startTime", "" + System.currentTimeMillis());
                            params.put("sendDataTime", "" + System.currentTimeMillis());
                            long downloadedSize = track.getDownloadedSize();
                            long downloadSize = track.getDownloadSize();
                            long percent = 0;
                            if (downloadSize != 0) {
                                percent = (downloadedSize * 100) / downloadSize;
                            }

                            params.put("downloadPercent", percent + "");
                            CommonRequestM.getInstanse().getDownloadTrackInfo(params,
                                    new IDataCallBack<Track>() {

                                        @Override
                                        public void onSuccess(Track object) {
                                            if (object != null) {
                                                object.setPlayCount(track.getPlayCount());
                                                object.setFavoriteCount(track
                                                        .getFavoriteCount());
                                                object.setCommentCount(track
                                                        .getCommentCount());

                                                object.setCoverUrlLarge(track.getCoverUrlLarge());
                                                object.setCoverUrlMiddle(track.getCoverUrlMiddle());
                                                object.setCoverUrlSmall(track.getCoverUrlSmall());

                                                if (!object.isPaid() && TextUtils.isEmpty(object.getDownloadUrl())) {
                                                    XDCSCollectUtil.statErrorToXDCS("download", "resource" +
                                                            "=DownloadedTrackListFragment1" + ";track={" + object.toString() + "}");
                                                }

                                                DownloadUtil.deleteTrackFile(track);
                                                RouteServiceUtil.getDownloadService().resetDownloadSavePath(track);
                                                if (RouteServiceUtil.getDownloadService().checkNeedLogin()) {
                                                    return;
                                                }
                                                if (!object.isPayTrack() || object.isAuthorized()) {
                                                    RouteServiceUtil.getDownloadService().addTask(object, new IDownloadService.IAddTaskCallBack() {
                                                        @Override
                                                        public void onAddTaskSuccess() {
                                                            CustomToast.showSuccessToast("重新加入下载列表");
                                                        }

                                                        @Override
                                                        public void onAddTaskFail() {
                                                        }
                                                    });
                                                } else {
                                                    CustomToast.showFailToast("重新下载失败");
                                                }
                                            }
                                        }

                                        @Override
                                        public void onError(int code, String message) {
                                            CustomToast.showFailToast("重新下载失败");
                                        }

                                    });
                        } else {
                            if (!track.isPaid() && TextUtils.isEmpty(track.getDownloadUrl())) {
                                XDCSCollectUtil.statErrorToXDCS("download", "resource" +
                                        "=DownloadedTrackListFragment2" + ";track={" + track.toString() + "}");
                            }
                            DownloadUtil.deleteTrackFile(track);
                            RouteServiceUtil.getDownloadService().resetDownloadSavePath(track);
                            RouteServiceUtil.getDownloadService().addTask(track, new IDownloadService.IAddTaskCallBack() {
                                @Override
                                public void onAddTaskSuccess() {
                                }

                                @Override
                                public void onAddTaskFail() {
                                }
                            });
                        }
                    }
                }).setCancelBtn("在线播放").setCancelBtn(new DialogBuilder.DialogCallback() {

                    @Override
                    public void onExecute() {
                        DownloadUtil.deleteTrackFile(track);
                        RouteServiceUtil.getDownloadService().resetDownloadSavePath(track);
                        XmPlayerManager.getInstance(mContext).play();
                    }
                }).setCancelable(true).setOutsideTouchCancel(true).setcancelApplyToButton(false).showConfirm();
    }

    @Override
    public void closeApp() {
        ActivityManager.finishAll(mContext);
    }

    public static void releaseStatic() {
        resertPlayErrorSign();
        if (HolderClass.instance != null) {
            HolderClass.instance.removeScreenChangeBroadCast();
            HolderClass.instance.removeBluetoothBroadCast();
            HolderClass.instance.removeSkipHeadTailFetchBroadCast();
            HolderClass.instance.mHasInit = false;
        }
    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        TingLocalMediaService.mPauseByFlowDialog = false;

        // 未购买会返回726 会导致多次切歌
//        if (PlanTerminateManager.getInstance().isTiming() && curModel != null && lastModel != null) {
//            PlanTerminateManager.getInstance().countDown();
//        }
        PlayDataUtils.INSTANCE.getAndRefreshSceneRequestParams(true);
        resertPlayErrorSign();
        PlayCompleteManager.INSTANCE.resetSkipTailTime(0);

        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onSoundSwitch(lastModel, curModel);
        }
        super.onSoundSwitch(lastModel, curModel);

        if (lastModel instanceof Track) {
            Track lastTrack = (Track) lastModel;
            lastTrack.setHasLoadPlayCompleteData(false);
        }

        if (alreadyEnterPlayFragment) {
            if (pauseByFreeListenV2(curModel, "onSoundSwitch")) {
                Logger.e("z_freelisten", "TingLocalMediaService onSoundSwitch return >> pauseByFreeListenV2");
                return;
            } else {
                Logger.e("z_freelisten", "TingLocalMediaService onSoundSwitch >> pauseByFreeListenV2 return false");
            }
        }

        PlayableModel playableModel = curModel;
        if (playableModel == null && lastModel instanceof Track) {
            Track lastTrack = (Track) lastModel;
            LoginInfoModelNew userInfo = UserInfoMannage.getInstance().getUser();
            if ((userInfo == null || (userInfo != null && !userInfo.isVip()))//不是会员
                    && !lastTrack.isAuthorized()//未购买
                    && lastTrack.getSampleDuration() > 0//部分试听
                    && (lastTrack.getVipFreeType() == 1 || lastTrack.isVipFree())//试听的是会员免费专辑 或
                // 会员专享专辑
            ) {
                String string = mContext.getString(R.string.host_free_over_be_vip);
                PlayBarFragment.ShowTipBroadCast.setTipsContent(mContext, string, 3000);
            } else if (((Track) lastModel).isAudition() &&
                    XmPlayerManager.getInstance(mContext).getPlayerStatus() == PlayerConstants.STATE_IDLE) {
                String string = mContext.getString(R.string.host_free_over_play_pay);
                PlayBarFragment.ShowTipBroadCast.setTipsContent(mContext, string, 4000);
            }
        }

        if (lastModel instanceof Track) {
            Track lastTrack = (Track) lastModel;
            if ((lastTrack.getIsFromToListenTrack() == 1 || lastTrack.getIsLastModelFromToListenTrack() == 1 || mPlayerManager.isPlayingPodCastPlayList()) &&
                    ToListenUtil.isListenComplete(false, mContext, lastTrack)) {
                ToListenManager.INSTANCE.deleteByIdInner(false, lastTrack.getDataId(), true, false, null);
            }
        }

        if (isAutoNext
                && lastModel instanceof Track
                && curModel instanceof Track
                && ((Track) lastModel).isFree()
                && !((Track) curModel).isAudition()
                && ((Track) curModel).getPlaySource() != ConstantsOpenSdk.PLAY_FROM_SEARCH
                && !((Track) curModel).isAuthorized()) {
            isAutoNext = false;

            SubordinatedAlbum lastAlbum = ((Track) lastModel).getAlbum();
            SubordinatedAlbum curAlbum = ((Track) curModel).getAlbum();
            if (lastAlbum != null
                    && curAlbum != null
                    && lastAlbum.getAlbumId() == curAlbum.getAlbumId()) {
                String string = mContext.getString(R.string.host_free_over_play_pay);
                PlayBarFragment.ShowTipBroadCast.setTipsContent(mContext, string, 4000);
            }
        }

        //以周为循环，若当周播放声音条数为16时，则在播放前出登录半层，引导用户登录
        if (curModel != null && curModel instanceof Track && ((Track) curModel).getType() != Track.TYPE_DUBSHOW && !curModel.isKindOfLive()) {
            checkShouldShowLogin();
        }

        if (curModel instanceof Track) {
            AnchorAlbumAd anchorAlbumAd = mAnchorAlbumAdMap.get(curModel.getDataId());
            mAnchorAlbumAdMap.remove(curModel.getDataId());
            if (AdManager.checkAnchorAdCanClick(anchorAlbumAd)) {
                AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), anchorAlbumAd
                        , anchorAlbumAd
                                .createAdReportModel(AppConstants.AD_LOG_TYPE_SITE_CLICK, 0).ignoreTarget(true).build());
            }
        }

//        HandlerManager.postOnBackgroundThread(new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    LocationService.getInstance().requestLocation(mContext,null);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        });

        if (curModel instanceof Track && PlayableModel.KIND_TRACK.equals(curModel.getKind())) {
            updateTodayPlayNumRecord(false);
        }

        if (lastModel instanceof Track && ElderlyModeManager.getInstance().isElderlyMode()) {
            ElderlyModeManager.getInstance().reportElderlyCoursePlayInfo((Track) lastModel, true);
        }

        handleOneKeyListenPlayMode(lastModel, curModel);
        if (isFromOneKeyPlay(curModel) && curModel instanceof Track) {
            Track oneKeyTrack = (Track) curModel;
            // 临时写死 下个迭代产品做优化
            DailyNewsLogicManager.INSTANCE.setMIsChildChannel(oneKeyTrack.getChannelGroupId() == 79);
        }

        PlayBarUbtResourceManager.INSTANCE.onSoundSwitch(playableModel);

        mIsSoundSwitch = true;
        if (lastModel instanceof Track && !TextUtils.isEmpty(((Track) lastModel).getDownloadedSaveFilePath())
                && curModel instanceof Track && !TextUtils.isEmpty(((Track) curModel).getDownloadedSaveFilePath())
                && isFromDownload((Track) lastModel) && isFromDownload((Track) curModel)) {
            // 这里基本大概率确定是下载的声音
            int listSize = XmPlayerManager.getInstance(mContext).getPlayListSize();
            if (listSize - XmPlayerManager.getInstance(mContext).getCurrentIndex() < 5) {
                Track lastTrack = XmPlayerManager.getInstance(mContext).getTrack(listSize - 1);
                addDownloadTracks(lastTrack);
            }
        }

        // 记录直播浏览历史
        if (curModel != null && curModel.isKindOfLive() && curModel instanceof Track) {
            mLastLiveTrack = (Track) curModel;
        } else {
            if (mLastLiveTrack != null && mLastLiveTrack.isKindOfLive()) {
                AlbumFootPrintUtil.recordLive(mLastLiveTrack);
                mLastLiveTrack = null;
            }
        }
        // 记录mc直播浏览历史
        if (curModel != null && curModel.isMyClubLiveTrack() && curModel instanceof Track) {
            AlbumFootPrintUtil.recordMyClubLive((Track) curModel);
        }

        PlayTimelineCardActionRecordManager.onSoundSwitch();
        if (curModel != null) {
            PlayTimelineCardManager.INSTANCE.setTimelineCardList(null, null);
        }

        //儿童Ai换声
        AiSoundTransformManager.INSTANCE.onSoundSwitch(lastModel, curModel);

        if (lastModel != null && lastModel.isKindOfChatXmly() && curModel != null && !curModel.isKindOfChatXmly()) {
            CustomToast.showToast("已自动退出AI智能播放模式");
        }
    }

    public static void addVideoLiveRecord(Track track) {
        if (track.isKindOfLive()) {
            mLastLiveTrack = track;
        }
    }

    // 特殊情况，当肚脐眼（播放历史）没有声音时，退出直播onSoundSwitch无法触发，此时改用此处记录最后一个直播
    public static void triggerLive() {
        if (mLastLiveTrack != null) {
            AlbumFootPrintUtil.recordLive(mLastLiveTrack);
            mLastLiveTrack = null;
        }
    }

    //更新直播track
    public static void updateLive(Track track) {
        if (mLastLiveTrack != null && track != null && track.getDataId() == mLastLiveTrack.getDataId()
                && track.getLiveRoomId() == mLastLiveTrack.getLiveRoomId()) {
            mLastLiveTrack = track;
        }
    }

    private boolean isFromDownload(Track track) {
        if (track == null) {
            return false;
        }
        return track.getDownloadFromType() == 1 || track.getDownloadFromType() == 2;
    }

    private boolean mIsRequestDownloadingTracks = false;
    private boolean mIsSoundSwitch = false;

    private void addDownloadTracks(Track lastTrack) {
        if (lastTrack == null || lastTrack.getDataId() <= 0) {
            return;
        }
        if (mIsRequestDownloadingTracks) {
            return;
        }
        mIsRequestDownloadingTracks = true;
        mIsSoundSwitch = false; // 这里已经置为false，那么在异步任务结束后发现再切歌，则说明声音列表可能发生改变，此时应该丢弃数据
        DownloadTracksUtil.getDownloadTracks(lastTrack, false, new IDataCallBack<List<Track>>() {
            @Override
            public void onSuccess(@Nullable List<Track> data) {
                if (!ToolUtil.isEmptyCollects(data)) {
                    if (!mIsSoundSwitch) {
                        List<Track> list = XmPlayerManager.getInstance(mContext).getPlayList();
                        if (!list.containsAll(data)) {
                            XmPlayerManager.getInstance(mContext).addTracksToPlayList(data);
                        }
                    }
                }
                mIsRequestDownloadingTracks = false;
            }

            @Override
            public void onError(int code, String message) {
                mIsRequestDownloadingTracks = false;
            }
        });
    }

    /**
     * 一键听的播放顺序和普通声音区分开
     */
    private void handleOneKeyListenPlayMode(PlayableModel lastModel, PlayableModel curModel) {
        if (!isFromOneKeyPlay(lastModel) && isFromOneKeyPlay(curModel)) {
            //普通声音切换到一键听
            //setPlayMode方法做了过滤，如果是一键听，则直接去mmkv的值，不用传过去的参数
            XmPlayerManager.getInstance(mContext).setPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_LIST);
        } else if (isFromOneKeyPlay(lastModel) && !isFromOneKeyPlay(curModel)) {
            //一键听切换到普通声音
            int playMode = PlayTools.getSavedPlayMode(mContext);
            XmPlayerManager.getInstance(mContext).setPlayMode(XmPlayListControl.PlayMode.getIndex(playMode));
        }
    }

    private boolean isFromOneKeyPlay(PlayableModel playableModel) {
        return playableModel instanceof Track &&
                (((Track) playableModel).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY ||
                        ((Track) playableModel).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) &&
                PlayableModel.KIND_TRACK.equals(playableModel.getKind());
    }

    private void checkShouldShowLogin() {
        if (UserInfoMannage.hasLogined()) {
            return;
        }
        String string =
                SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInHost
                        .KEY_LISTEN_COUNT_AND_DATE);
        int count = 0;
        long time = System.currentTimeMillis();
        if (!TextUtils.isEmpty(string) && string.contains("-")) {
            try {
                String[] split = string.split("-");
                long saveTime = Long.valueOf(split[0]);

                if (time - saveTime > 7 * 24 * 60 * 60 * 1000) {
                    count = 0;
                } else {
                    count = Integer.valueOf(split[1]) + 1;
                    time = saveTime;
                }

                if (count == 16) {
                    boolean isPlayCardFm = false;
                    if (BaseApplication.getTopActivity() instanceof MainActivity && ((MainActivity) BaseApplication.getTopActivity()).getCurrentTopFragment() != null) {
                        isPlayCardFm = ((MainActivity) BaseApplication.getTopActivity()).getCurrentTopFragment().getClass().getSimpleName().contains("RecommendAlbumCardFragment");
                    }
                    // 锁屏下不能弹出该页面 ,并且在后台的时候也不能弹出此页面, 并且播放卡片那里也不能出
                    if ((BaseApplication.getTopActivity() == null
                            || !(BaseApplication.getTopActivity() instanceof LockScreenActivity))
                            && BaseUtil.isForegroundIsMyApplication(mContext)
                            && !isPlayCardFm) {
                        UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_LISTEN_MORE);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        SharedPreferencesUtil.getInstance(mContext).saveString(PreferenceConstantsInHost
                .KEY_LISTEN_COUNT_AND_DATE, time + "-" + count);
    }

    private boolean isAutoNext = false;

    @Override
    public void onStartGetAdsInfo(int playMethod, boolean duringPlay, boolean isPaused) {
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onStartGetAdsInfo(playMethod, duringPlay, isPaused);
        }
    }

    @Override
    public void onGetAdsInfo(AdvertisList ads) {
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onGetAdsInfo(ads);
        }
    }

    @Override
    public void onAdsStartBuffering() {

    }

    @Override
    public void onAdsStopBuffering() {

    }

    @Override
    public void onStartPlayAds(Advertis ad, int position) {
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onStartPlayAds(ad, position);
        }
    }

    @Override
    public void onCompletePlayAds() {
        if (getYaoyiYaoAdManage() != null) {
            getYaoyiYaoAdManage().onCompletePlayAds();
        }
    }

    @Override
    public void onError(int what, int extra) {
    }

    public int getT() {
        mCDN_TRAFFIC_CBATTERY = SharedPreferencesUtil.getInstance(mContext)
                .getInt(PreferenceConstantsInHost.TINGMAIN_KEY_CDN_TRAFFIC_CBATTERY, 0);
        if (mCDN_TRAFFIC_CBATTERY != 0) {
            return mCDN_TRAFFIC_CBATTERY * 1000;
        } else {
            return 3600 * 1000;
        }
    }

    IDownloadCallback mDownloadCallback = new IDownloadCallback() {
        @Override
        public void onDownloadProgress(Track track) {
        }

        @Override
        public void onCancel(Track track) {
        }

        @Override
        public void onComplete(Track track) {
            ImageManager.from(mContext).downloadBitmap(track.getCoverUrlSmall(), null);
            ImageManager.from(mContext).downloadBitmap(track.getCoverUrlLarge(), null);
            if (track.getAlbum() != null) {
                ImageManager.from(mContext).downloadBitmap(track.getAlbum().getCoverUrlSmall(),
                        null);
            }
            if (RouteServiceUtil.getDownloadService().getUnfinishedTasks().isEmpty()) {
                CustomToast.showToast("已下载完成");
            }
        }

        @Override
        public void onUpdateTrack(Track track) {
        }

        @Override
        public void onStartNewTask(Track track) {
        }

        @Override
        public void onError(Track track) {
        }

        @Override
        public void onDelete() {
        }
    };

    private static void resertPlayErrorSign() {
        isShowPlayError = false;

        if (mPlayErrorDialog != null) {
            try {
                mPlayErrorDialog.cancle();
            } catch (Exception e) {
                e.printStackTrace();
            }
            mPlayErrorDialog = null;
        }
        lastErrorTime = 0;
    }

    public void setAnchorAlbumAdMap(LongSparseArray<AnchorAlbumAd> map) {
        mAnchorAlbumAdMap.clear();

        long key;
        for (int i = 0; i < map.size(); i++) {
            key = map.keyAt(i);
            mAnchorAlbumAdMap.put(key, map.get(key));
        }

        willRestoreMapOnSwitch = false;
    }

    @Override
    public void onPlayListChange() {
        if (willRestoreMapOnSwitch) {
            mAnchorAlbumAdMap.clear();
        }
    }

    private void userTrackOnReasonDialogButtonClicked(String message) {
        new UserTracking()
                .setPopupType("安卓引导关闭省电功能弹窗")
                .setItem("button")
                .setItemId(message)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_APP_PUSH_CLICK);
    }

    private static void userTrackOnErrorDialogButtonClicked(String message) {
        new UserTracking()
                .setPopupType("安卓发生网络错误弹窗")
                .setItem("button")
                .setItemId(message)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_APP_PUSH_CLICK);
    }

    private static void userTrackOnShowDialog(String type) {
        new UserTracking()
                .setPushType(type)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_APP_PUSH);
    }

    /**
     * 记录当天播放的声音数目
     */
    private void updateTodayPlayNumRecord(boolean isInit) {
        try {
            PlayRecordNumDataModel data;
            data = getTodayNumData();
            String result = "";
            if (data != null) {
                if (TextUtils.isEmpty(data.date) || !data.date.equals(sdf.format(new Date(System.currentTimeMillis())))) {
                    data.date = sdf.format(new Date(System.currentTimeMillis()));
                    data.num = 0;
                } else if (!isInit) {
                    data.num++;
                }
            } else {
                data = new PlayRecordNumDataModel(sdf.format(new Date(System.currentTimeMillis())), 0);
            }
            result = new Gson().toJson(data);
            saveTodayNumData(result);
        } catch (Exception ignored) {
        }
    }

    /**
     * 获取当天播放的声音数目
     */
    public PlayRecordNumDataModel getTodayNumData() {
        PlayRecordNumDataModel result = null;
        try {
            String dayRecord = SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInHost.KEY_TODYA_PLAY_NUM);
            if (!TextUtils.isEmpty(dayRecord)) {
                result = new Gson().fromJson(dayRecord, new TypeToken<PlayRecordNumDataModel>() {
                }.getType());
            }
        } catch (Exception ignored) {
        }
        return result;
    }

    /**
     * 保存当天播放的声音数目
     *
     * @param data {"date":"20191108","num":1}
     */
    public void saveTodayNumData(String data) {
        if (!TextUtils.isEmpty(data)) {
            SharedPreferencesUtil.getInstance(mContext).saveString(PreferenceConstantsInHost.KEY_TODYA_PLAY_NUM, data);
        }
    }

    @Override
    public boolean userIsVip() {
        return UserInfoMannage.isVipUser();
    }

    @Override
    public boolean isUseNewPlayFragment() {
        try {
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ConstantsOpenSdk.DEFALUT_USE_NEW_PLAY_FRA;
    }

    @Override
    public boolean isPlayFragmentForeground() {
        if (!BaseUtil.isForegroundIsMyApplication(BaseApplication.getMyApplicationContext())) {
            return false;
        }
        return PlayCompleteManager.INSTANCE.isOnScreen();
    }

    @Override
    public boolean lockScreenActivityIsShowing() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity instanceof LockScreenActivity) {
            return true;
        }
        return false;
    }

    private void showErrorDialogForDebug(Activity activity, XmPlayerException exception) {
        String message = "错误类型:" + exception.getErrorTypeForDebug() + ";" + "错误详情:" + exception.getErrorMessageForDebug();
        DialogBuilder dialogBuilder = new DialogBuilder(activity).setMessage(message).setOkBtn(
                "知道了", null).setCancelBtn("取消", null);
        dialogBuilder.showAlert();
    }

    @Override
    public long updateGDTRTBToken() {
        Logger.log("TingLocalMediaService : updateGDTRTBToken begin");
        String gdtRtbToken = AdSdkRtbManager.getInstance().getGdtRtbToken();
        long key = System.currentTimeMillis();
        MMKVUtil.getInstance(PreferenceConstantsInHost.THIRD_SDK_RTB_FILE_NAME).saveString(PreferenceConstantsInHost.KEY_GDT_RTB_TOKEN + key, gdtRtbToken);
        Logger.log("TingLocalMediaService : updateGDTRTBToken end");
        return key;
    }

    @Override
    public long updateGDTRTBSdkInfo(String dspId) {
        Logger.e("------msg_rtb", " ------ updateGDTRTBSdkInfo ！！！ - 播放进程 ！！！！！ 更新 sdk info  ");
        Logger.log("TingLocalMediaService : sdk info begin");
        String gdtRtbSdkInfo = AdSdkRtbManager.getInstance().getGdtSdkInfo(dspId);
        Logger.e("------msg_rtb", " ------ updateGDTRTBSdkInfo ！！！ - 播放进程 ！！！！！ 更新 gdtRtbSdkInfo = " + gdtRtbSdkInfo);
        long key = System.currentTimeMillis();
        MMKVUtil.getInstance(PreferenceConstantsInHost.THIRD_SDK_RTB_FILE_NAME).saveString(PreferenceConstantsInHost.KEY_GDT_RTB_SDK_INFO + key, gdtRtbSdkInfo);
        Logger.log("TingLocalMediaService : sdk info  end");
        return key;
    }

    @Override
    public long updateCSJRTBToken(int thirdAdType, String dspId, int adType) {
        Logger.log("TingLocalMediaService : updateCSJRTBToken begin");
        String csjRtbToken = AdSdkRtbManager.getInstance().getCsjRtbToken(thirdAdType, dspId, adType);
        long key = System.currentTimeMillis();
        MMKVUtil.getInstance(PreferenceConstantsInHost.THIRD_SDK_RTB_FILE_NAME).saveString(PreferenceConstantsInHost.KEY_CSJ_RTB_TOKEN + key, csjRtbToken);
        Logger.log("TingLocalMediaService : updateCSJRTBToken end");
        return key;
    }

    @Override
    public void batchAdRecord(List thirdAdList, List adReportModel) {
        try {
            AdManager.batchAdRecord(ToolUtil.getCtx(), thirdAdList,
                    (AdReportModel) adReportModel.get(0));
        } catch (Exception exception) {
            exception.printStackTrace();

            if (ConstantsOpenSdk.isDebug) {
                throw exception;
            }
        }
    }

    @Override
    public String queryConfigCenter(int queryType, String groupName, String itemName) {
        return LocalConfigCenterHelper.getConfigCenterResult(queryType, groupName, itemName);
    }

    @Override
    public String syncBatchGetToken(List adRtbModelList, int thirdAdType) {
        if (ToolUtil.isEmptyCollects(adRtbModelList)) {
            return null;
        }

        try {
            return SdkRtbSyncBatchUtil.INSTANCE.syncBatchGetToken(adRtbModelList, thirdAdType);
        } catch (Exception exception) {
            exception.printStackTrace();

            if (ConstantsOpenSdk.isDebug) {
                throw exception;
            }
        }
        return null;
    }

    @Override
    public String syncBatchGetGdtSdkInfo(List adRtbModelList) {
        if (ToolUtil.isEmptyCollects(adRtbModelList)) {
            return null;
        }

        try {
            return SdkRtbSyncBatchUtil.INSTANCE.syncBatchGetGdtSdkInfo(adRtbModelList);
        } catch (Exception exception) {
            exception.printStackTrace();

            if (ConstantsOpenSdk.isDebug) {
                throw exception;
            }
        }
        return null;
    }

    @Override
    public void callForTraceMarkPoint(int metaId, String paramJson) {
        LocalTraceMarkPointHelper.traceMarkPoint(metaId, paramJson);
    }

    @Override
    public void callForShowUniversalPaymentActionDialog(Track track) {
        if (null == track) {
            return;
        }
        try {
            MainActionRouter mainActionRouter = Router.getActionRouter(Configure.BUNDLE_MAIN);
            if (null != mainActionRouter) {
                IMainFunctionAction functionAction = mainActionRouter.getFunctionAction();
                if (null != functionAction) {
                    functionAction.requestAndShowUniversalPaymentActionsDialog(track, UniversalPayment.SOURCE_AFTER_SAMPLE);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void notifyFreeListenTimeOut() {
        FreeListenHostManager.getInstance().sendUnlockTimeOutBroadCast();
    }

    @Override
    public boolean isPlayingFreeListenAd() {
        return AdMakeVipLocalManager.getInstance().isPlayingUnlockAd();
    }

    @Override
    public String getNewTicket(int type) {
        return XuidManager.INSTANCE.getTicket(type);
    }

    @Override
    public void completePlay() {
        if (QuickListenTabAbManager.showAIListenTab()) {
            QuickListenDataManager.Companion.getInstance().completePlay();
        }
    }

    @Override
    public void setThirdUidAndAppKey(String thirdUid, String appKey) {
        XmRemoteControlUtil.setThirdUidAndAppKey(thirdUid, appKey);
    }

    public void setScreenListener(ILockScreenListener screenListener) {
        mScreenListener = screenListener;
    }

    public void addStopPlayDueToFreeListenPermissionListener(IStopPlayDueToFreeListenPermissionListener listener) {
        mStopPlayDueToFreeListenPermissionListeners.add(listener);
        alreadyEnterPlayFragment = true;
    }

    public void removeStopPlayDueToFreeListenPermissionListener(IStopPlayDueToFreeListenPermissionListener listener) {
        mStopPlayDueToFreeListenPermissionListeners.remove(listener);
    }

    public void notifyStopPlayDueToFreeListen() {
        if (mStopPlayDueToFreeListenPermissionListeners.isEmpty()) {
            return;
        }
        for (IStopPlayDueToFreeListenPermissionListener listener : mStopPlayDueToFreeListenPermissionListeners) {
            listener.onStopPlayDueToFreeListenPermission();
        }
    }

    public interface IStopPlayDueToFreeListenPermissionListener {
        void onStopPlayDueToFreeListenPermission();
    }
}
