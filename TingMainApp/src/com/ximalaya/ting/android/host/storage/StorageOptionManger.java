package com.ximalaya.ting.android.host.storage;

import android.text.TextUtils;

import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.exception.NonException;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RecordActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.SimpleBundleInstallCallback;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.opensdk.util.FileUtilBase;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.storage.IStoragePathManager;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

public class StorageOptionManger {
    private static long TOTAL_FILE_LENGTH_LIMIT = 3L * 1024 * 1024 * 1024 / 2;
    private static final String RECORD_PATH = "com.ximalaya.ting.android/files/Documents/ting/record";
    private static final String RECORD_PATH_COMPAT = "com.ximalaya.ting.android/files/ting/record"; // 兼容无Documents文件夹情况，内置存储录音和创建Documents文件夹失败
    private static final String RECORD_PPT_PIC_PATH = "com.ximalaya.ting.android/convert/download"; // ppt录音图片
    private static final String DOWNLOAD_PATH = "com.ximalaya.ting.android/files/download";
    private static final String DOC_PLAY_PAGE_PATH = "com.ximalaya.ting.android/files/playinfo";
    private static final String LIVE_GIFT_PATH = "com.ximalaya.ting.android/files/gift";
    private static final String LAST_AUTO_STORAGE_OPT_TIME_KEY = "last_auto_storage_opt_time";

    private static StorageOptionManger mStorageOptionManger;
    private IStoragePathManager mStoragePathManager;
    private List<File> mTotalFiles = new ArrayList<>();
    private String mInnerDir;
    private List<String> mOutDir = new ArrayList<>();
    private long mUtilSize;
    private long mTotalFilesSize;

    private static final List<String> BAN_AUTO_DELETE_DIR;
    private static final List<String> NEVER_DELETE_DIR;

    static {
        BAN_AUTO_DELETE_DIR = new ArrayList<>();
        BAN_AUTO_DELETE_DIR.add(RECORD_PATH);
        BAN_AUTO_DELETE_DIR.add(RECORD_PATH_COMPAT);
        BAN_AUTO_DELETE_DIR.add(RECORD_PPT_PIC_PATH);
        BAN_AUTO_DELETE_DIR.add(DOWNLOAD_PATH);
        BAN_AUTO_DELETE_DIR.add(LIVE_GIFT_PATH);
        BAN_AUTO_DELETE_DIR.add(DOC_PLAY_PAGE_PATH);

        NEVER_DELETE_DIR = new ArrayList<>();
        NEVER_DELETE_DIR.add("_business");//精细化管理后的路径不自动删
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/mmkv");
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/shared_prefs");
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/lib");
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/databases");
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/preload_path_forad");//广告
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/preload_info_path_forad");//广告
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/app_xmrn/commonpayment");//rn支付相关
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/lite_apps_folder/lite_app_core");//小程序
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/lite_apps_folder/v8");//小程序
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/app_dex");//热修复
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/app_webview");//webview
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/homePageCustomTabs");//自定义tab数据
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/cache/e7c3e4a2e2bcbc38486d96bb4be5b1f4");//服务端返回的所有tab数据，url连接md5，连接换了要换
        NEVER_DELETE_DIR.add("com.ximalaya.ting.android/files/play_page_skin");// 播放页皮肤包信息
    }

    public static StorageOptionManger getSingleInstance() {
        if (mStorageOptionManger == null) {
            synchronized (StorageOptionManger.class) {
                if (mStorageOptionManger == null) {
                    mStorageOptionManger = new StorageOptionManger();
                }
            }
        }
        return mStorageOptionManger;
    }

    private StorageOptionManger() {
        mStoragePathManager = RouterServiceManager.getInstance().getService(IStoragePathManager.class);
        mInnerDir = mStoragePathManager.getInnerStoragePath();
        List<String> pathList = mStoragePathManager.getVoldFilePaths();
        for (String path : pathList) {
            mOutDir.add(path.replace("/files/download", ""));
        }
        Logger.i("cf_test","TOTAL_FILE_LENGTH_LIMIT:___"+TOTAL_FILE_LENGTH_LIMIT);
    }

    public synchronized long getTotalStorageSize() {
        long totalSize = 0;
        if (!TextUtils.isEmpty(mInnerDir)) {
            totalSize += getDirSize(mInnerDir);
            Logger.i("cf_test", "mInnerDir:" + mInnerDir);
        }
        for (String outPath : mOutDir) {
            if (!TextUtils.isEmpty(outPath)) {
                totalSize += getDirSize(outPath);
                Logger.i("cf_test", "mOutDir:" + outPath);
            }
        }
        Logger.i("cf_test", "totalSize:" + totalSize / 1024 / 1024);
        mTotalFilesSize = totalSize;
        return totalSize;
    }

    public synchronized void deleteAllPrivateFiles() {
        deleteCacheFiles();
        getTotalPrivateFiles();
        if (mTotalFiles != null && mTotalFiles.size() > 0) {
            Iterator<File> iterator = mTotalFiles.iterator();
            while(iterator.hasNext()){
                File file = iterator.next();
                if (file!= null && file.exists()) {
                    file.delete();
                }
            }
        }
        mTotalFiles.clear();
    }

    public synchronized void deleteDownloadFiles(){
        getTotalPrivateFiles();
        if (mTotalFiles != null && mTotalFiles.size() > 0) {
            Iterator<File> iterator = mTotalFiles.iterator();
            while(iterator.hasNext()){
                File file = iterator.next();
                if (file!= null && !TextUtils.isEmpty(file.getAbsolutePath()) &&
                        file.getAbsolutePath().contains(DOWNLOAD_PATH) && file.exists()) {
                    file.delete();
                }
            }
        }
        mTotalFiles.clear();
    }

    public synchronized void deleteRecordFiles() {
        Router.getActionByCallback(Configure.BUNDLE_RECORD, (SimpleBundleInstallCallback) bundleModel -> {
            if (bundleModel == Configure.recordBundleModel) {
                try {
                    Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFunctionAction().removeAllRecordData();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public synchronized void deleteLiveGiftFiles() {
        for (String outPath : mOutDir) {
            File liveGiftFile = new File(outPath + File.separator + "files/gift");
            deleteFileDir(liveGiftFile);
        }
    }

    public synchronized void deleteNoBanFiles(){
        getTotalPrivateFiles();
        if (mTotalFiles != null && mTotalFiles.size() > 0) {
            Iterator<File> iterator = mTotalFiles.iterator();
            while(iterator.hasNext()){
                File file = iterator.next();
                if (file!= null && !isBanAutoDeleteFile(file) && file.exists()) {
                    file.delete();
                } else {
                    Logger.i("cf_test","isBanAutoDeleteFile:___"+file.getAbsolutePath());
                }   //注意这个地方
            }
            mTotalFiles.clear();
        }
    }

    private boolean isNeverDeleteFile(File file) {
        String filePath = file.getAbsolutePath();
        for (String banPath : NEVER_DELETE_DIR) {
            if (filePath.contains(banPath)) {
                return true;
            }
        }
        return false;
    }

    private boolean isBanAutoDeleteFile(File file) {
        String filePath = file.getAbsolutePath();
        for (String banPath : BAN_AUTO_DELETE_DIR) {
            if (filePath.contains(banPath)) {
                return true;
            }
        }
        return false;
    }

    private void getTotalPrivateFiles() {
        mTotalFiles.clear();
        if (!TextUtils.isEmpty(mInnerDir)) {
            getAllFiles(new File(mInnerDir));
        }
        for (String outPath : mOutDir) {
            if (!TextUtils.isEmpty(outPath)) {
                getAllFiles(new File(outPath));
            }
        }
        try {
            Collections.sort(mTotalFiles, new Comparator<File>() {
                @Override
                public int compare(File o1, File o2) {
                    if(o1 == null && o2 == null) {
                        return 0;
                    }
                    if(o1 == null) {
                        return -1;
                    }
                    if(o2 == null) {
                        return 1;
                    }
                    long compare = o1.lastModified() - o2.lastModified();
                    if (compare > 0){
                        return 1;
                    }
                    if (compare < 0){
                        return -1;
                    }
                    return 0;
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            CrashReport.postCatchedException(e);
        }
//        for (File file : mTotalFiles) {
//            Logger.i("cf_test", "文件名：————" + file.getAbsolutePath());
//        }
    }

    public void autoOptimizeDiskMemory(long autoOptSize) {
        long targetSize = autoOptSize + getDownloadSize() + getRecordFilesSize();
        deleteCacheFiles();
        XDCSCollectUtil.statErrorToXDCS("autoOptimizeDiskMemory","deleteCacheFiles");
        getTotalStorageSize();
        if (mTotalFilesSize < targetSize) {
            Logger.i("cf_test","mTotalFilesSize_is_too_small:__"+mTotalFilesSize);
            return;
        }
        Logger.i("cf_test","mTotalFilesSize_is_too_big:__"+mTotalFilesSize);
        getTotalPrivateFiles();
        for (File file : mTotalFiles) {
            if (isBanAutoDeleteFile(file)) {
                continue;
            }
            Logger.i("cf_test", file.getAbsolutePath() + "_______" +
                    "_______" + StringUtil.getTimeWithFormatLocal(file.lastModified(), false));
            mTotalFilesSize -= file.length();
            file.delete();
            if (mTotalFilesSize <= targetSize) {
                break;
            }
        }
        mTotalFiles.clear();
    }

    public synchronized void checkIfNeedAutoOptimizeDiskMemory() {
        long lastAutoOptTime = MMKVUtil.getInstance().getLong(LAST_AUTO_STORAGE_OPT_TIME_KEY);
        int autoStorageAptIntervalsDay = 1;
        try {
            autoStorageAptIntervalsDay = ConfigureCenter
                    .getInstance()
                    .getInt(CConstants.Group_android.GROUP_NAME
                            , CConstants.Group_android.ITEM_AUTO_STORAGE_APT_INTERVALS_DAY);

        } catch (NonException e) {
            e.printStackTrace();
            XDCSCollectUtil.statErrorToXDCS("autoOptimizeDiskMemory","autoStorageAptIntervalsDay：——————" + e.getMessage());
            return;
        }
        if (autoStorageAptIntervalsDay <= 0) {
            XDCSCollectUtil.statErrorToXDCS("autoOptimizeDiskMemory","autoStorageAptIntervalsDay <= 0");
            return;
        }
        if (lastAutoOptTime > 0 && System.currentTimeMillis() - lastAutoOptTime <= autoStorageAptIntervalsDay*24*60*60*1000){
            Logger.i("cf_test","is_not_need_optimize_DiskMemory____________");
            return;
        }
        Logger.i("cf_test","_need_optimize_DiskMemory____________");
        MMKVUtil.getInstance().saveLong(LAST_AUTO_STORAGE_OPT_TIME_KEY,System.currentTimeMillis());
        autoOptimizeDiskMemory(TOTAL_FILE_LENGTH_LIMIT);
    }

    private void deleteCacheFiles() {
        File innerCacheFile = new File(mInnerDir + File.separator + "cache");
        if (innerCacheFile != null && innerCacheFile.exists()) {
            Logger.i("cf_test", "innerCacheFile:____" + innerCacheFile.getAbsolutePath());
            deleteFileDir(innerCacheFile);
        }
        for (String outPath : mOutDir) {
            File outCacheFile = new File(outPath + File.separator + "cache");
            if (outCacheFile != null && outCacheFile.exists()) {
                deleteFileDir(outCacheFile);
            }
            //广告下载黑名单文件夹删除
            File adDownloadFile = new File(outPath + File.separator + "files/download/files/update");
            if (adDownloadFile != null && adDownloadFile.exists()){
                deleteFileDir(adDownloadFile);
            }
            //删除应用内升级未删除的apk文件/ting/update
            File apkDownloadFile = new File(FileUtilBase.getPrivateRootDir(BaseApplication.getMyApplicationContext()) + "/ting/update");
            if (apkDownloadFile != null && apkDownloadFile.exists()){
                deleteFileDir(apkDownloadFile);
            }
        }
    }

    public synchronized long getDownloadSize() {
        long downloadSize = 0;
        for (String outPath:mOutDir){
            String downloadPath = outPath + "/files/download";
            downloadSize += getDirSize(downloadPath);
        }
        for (String outPath:mOutDir){
            String downloadPath = outPath + "/files/playinfo";
            downloadSize += getDirSize(downloadPath);
        }
        return downloadSize;
    }

    public synchronized long getRecordFilesSize() {
        long recordSize = 0;
        for (String outPath : mOutDir) {
            String recordPath = outPath + "/files/Documents/ting/record";
            recordSize += getDirSize(recordPath);
        }
        if (mInnerDir != null) {
            String recordPath = mInnerDir + "/ting/record";
            recordSize += getDirSize(recordPath);
        }
        return recordSize;
    }

    public synchronized long getLiveGiftFilesSize(){
        long recordSize = 0;
        for (String outPath:mOutDir){
            String recordPath = outPath + "/files/gift";
            recordSize += getDirSize(recordPath);
        }
        return recordSize;
    }

    public long getNeverDeleteFilesSize(){
        long neverDeleteFilesSize = 0;
        return neverDeleteFilesSize;
    }

    private void deleteFileDir(File file) {
        if (file == null) {
            return;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null && files.length > 0){
                for (File f : files) {
                    deleteFileDir(f);
                }
            }
        }
        file.delete();
    }

    private void getAllFiles(File file) {
        if (file == null){
            return;
        }
        File[] files = file.listFiles();
        if (files == null || files.length == 0){
            return;
        }
        for (File f : files) {
            if (f == null){
                continue;
            }
            if (f.isDirectory()) {
                getAllFiles(f);
            } else if (f.isFile()) {
                if (!isNeverDeleteFile(f)){
                    //若是文件，直接打印
                    mTotalFiles.add(f);
                }
            }//若是目录，则递归打印该目录下的文件
        }
    }

    private long getDirSize(String filePath) {
        mUtilSize = 0;
        getFileSize(new File(filePath));
        return mUtilSize;
    }

    private void getFileSize(File file) {
        try {
            if (file.isFile()) {
                if (!isNeverDeleteFile(file)){
                    //如果是文件，获取文件大小累加
                    mUtilSize += file.length();
                }
            } else if (file.isDirectory()) {
                //获取目录中的文件及子目录信息
                File[] files = file.listFiles();
                if (files == null || files.length == 0) {
                    return;
                }
                for (int i = 0; i < files.length; i++) {
                    //调用递归遍历f1数组中的每一个对象
                    getFileSize(files[i]);
                }
            }
        } catch (OutOfMemoryError error) {
            error.printStackTrace();
        }
    }
}
