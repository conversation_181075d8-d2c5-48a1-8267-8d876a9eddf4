package com.ximalaya.ting.android.host.live.util

import android.text.TextUtils
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.util.VersionUtil
import com.ximalaya.ting.android.xmutil.Logger

/**
 * 直播上报投放等数据工具类。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @since 2025/4/10
 */
object LiveDataReportUtil {

    /**
     * 控制上报新安装应用用户激活事件
     * 1、新安装用户，且剪切板内容为空时，上报
     * 2、新安装用户，且剪切板内容不为空，且剪切板内容不能被喜马内其他场景消费时，上报
     */
    private var canReport: Boolean = true

    /**
     * 标记是否已经执行 iting，跳转进入了直播间
     */
    var hasHandleITing = false

    /**
     * 直播在巨量引擎投放的用户下载安装和激活APP上报。
     * 注意：必须在主站校验完成剪切板中的数据无效后才能上报，不阻断主站剪切板中有效数据处理跳转
     */
    @JvmStatic
    fun checkIsNewInstallAndReportActivateEvent() {
        try {
            val isNewInstall = VersionUtil.isNewInstall()
            val on = getReportActivateEventSwitch()
            Logger.d(
                "LiveDataReportUtil",
                "checkIsNewInstallAndReportActivateEvent, " +
                        "isNewInstall = $isNewInstall, " +
                        "on = $on, " +
                        "canReport = $canReport"
            )
            if (isNewInstall && on && canReport) {
                canReport = false
                Router.getActionByCallback(
                    Configure.BUNDLE_LIVE,
                    object : Router.IBundleInstallCallback {
                        override fun onInstallSuccess(bundleModel: BundleModel) {
                            if (!TextUtils.equals(
                                    Configure.liveBundleModel.bundleName,
                                    bundleModel.bundleName
                                )
                            ) {
                                return
                            }

                            Logger.d(
                                "LiveDataReportUtil",
                                "checkIsNewInstallAndReportActivateEvent onInstallSuccess, isNewInstall = $isNewInstall on = $on"
                            )

                            try {
                                Router.getActionRouter<LiveActionRouter>(Configure.BUNDLE_LIVE)
                                    ?.functionAction
                                    ?.checkIsNewInstallAndReportActivateEvent()
                            } catch (e1: Exception) {
                                e1.printStackTrace()
                            }

                        }

                        override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {

                        }

                        override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {

                        }
                    })
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @JvmStatic
    fun setCanReport(canReport: Boolean) {
        this.canReport = canReport
    }

    @JvmStatic
    private fun getReportActivateEventSwitch(): Boolean {
        return ConfigureCenter.getInstance().getBool(
            CConstants.Group_live.GROUP_NAME,
            "check_new_user_for_ad",
            false
        )
    }

}