package com.ximalaya.ting.android.host.model.payment.commerial.vipSku;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.SystemClock;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;
import android.view.View;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.util.CountDownTimeUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Keep
public class VipSkuItem implements VipPurchaseBaseInfo {

    public static final String TEXT_AGREEMENT_HEAD = "购买即视为同意《";
    public static final String TEXT_AGREEMENT_TAIL = "》";

    public String localJsonString;
    public String localOrderSource;
    public long localVipCategoryId;
    public long localVipSpuId;

    public String localDisclaimer;
    public VipSkuShelfInfo localVipSkuShelfInfo;

    @SerializedName("productId")
    public String productId;                            // 产品id
    @SerializedName("name")
    public String name;                                 // 产品名称
    @SerializedName("itemId")
    public String itemId;                               // itemId
    @SerializedName("description")
    public String description;                          // 商品卖点
    @SerializedName("unitPrice")
    public double unitPrice;                            // 价格
    @SerializedName("originPrice")
    public double originPrice;                          // 原价
    @SerializedName("payPrice")
    public double payPrice;                             // 售价
    @SerializedName("salesRemark")
    public String salesRemark;                          // 售卖标记
    @SerializedName("platinumVipUpgradeProductDtos")
    public List<UpgradeProductModel> upgradeProductVos; // 会员差价升级白金会员产品列表
    @SerializedName("showEnvelopeAnime")
    public boolean showEnvelopeAnime;
    @SerializedName("isEnvelopeActivityProduct")
    public boolean isEnvelopeActivityProduct;
    @SerializedName("properties")
    public VipSkuItemProperty properties;
    @SerializedName("presentDetails")
    public List<VipPresentDetail> presentDetails;       // 买赠列表
    @SerializedName("countDownTimestamp")
    public long countDownTimestamp;                     // 倒计时结束时间戳
    // 限时优惠
    @SerializedName("applyTimeLimited")
    public boolean applyTimeLimited;            // 是否适用限时折扣
    @SerializedName("activityProperty")
    public VipActivityProperty activityProperty;    //弹窗动效配置（倒计时、弹窗图片及角标文案）
    @SerializedName("activityPopupDto")
    public ActivityPopupDto activityPopupDto;   // 通知购买合并Vip Sku弹窗活动配置（倒计时、角标/角标文案）
    @SerializedName("showTimeLimitedPopWindow")
    public boolean showTimeLimitedPopWindow;    // 是否展示限时减免弹窗，true：展示，false：不展示
    @SerializedName("timeLimitedPopupImage")
    public String timeLimitedPopupImage;        // 限时减免弹窗素材(存在才可能需要弹窗)
    @SerializedName("present")
    public boolean present;                     // 是否有赠品
    @SerializedName("presentItemId")
    public String presentItemId;                // 赠品ID
    @SerializedName("subProduct")
    public VipSkuItem subProduct;
    @SerializedName("useCoupon")                // 是否有优惠券
    public boolean useCoupon;
    @SerializedName("couponId")
    public String couponId;
    @SerializedName("discountPrice")
    public double discountPrice;                // 使用优惠券后的价格
    @SerializedName("promotionPrice")
    public double promotionPrice;
    @SerializedName("discountRate")
    public String discountRate;
    @SerializedName("discountAmount")
    public double discountAmount;
    @SerializedName("promotionCode")
    public String promotionCode;
    @SerializedName("promotionTypeId")
    public int promotionTypeId;
    @SerializedName("discountTypeId")
    public int discountTypeId;
    @SerializedName("productBackground")
    public String productBackground;            // 商品背景图
    @SerializedName("productSkin")
    public String productSkin;    // 皮肤

    @SerializedName("ruleId")
    public String ruleId;                       // 规则id
    @SerializedName("timeLimitedAmount")
    public double timeLimitedAmount;            // 限时优惠金额
    @SerializedName("timeLimitedExpireTime")
    public long timeLimitedExpireTime;          // 限时优惠倒计时（较实际到期时间有提前）
    @SerializedName("timeLimitedMarkingToken")
    public String timeLimitedMarkingToken;      // 营销凭证
    @SerializedName("timeLimitedPromotionId")
    public long timeLimitedPromotionId;         // 促销规则
    @SerializedName("timeLimitedPromotionCode")
    public String timeLimitedPromotionCode;     // 促销凭证
    @SerializedName("signingChannels")
    public List<Integer> signingChannels;       // 连续订阅商品签约渠道,2-支付宝、6-花呗、3-微信支付、5-银联云闪付、8-联通支付
    @SerializedName("purchasePopUpsText")
    public VipPurchasePopUpsText purchasePopUpsText;
    @SerializedName("subscriptText")
    public String subscriptText;
    @SerializedName("subscriptLogo")
    public String subscriptLogo;
    @SerializedName("subscriptBackgroundColor")
    public String subscriptBackgroundColor;
    @SerializedName("subscriptTextColor")
    public String subscriptTextColor;
    @SerializedName("backgroundImageV2")
    public String backgroundImageV2;
    @SerializedName("sellingPointBackgroundColor")
    public String sellingPointBackgroundColor;

    // 页面埋点信息
    @SerializedName("groupId")
    public int groupId;
    @SerializedName("groupName")
    public String groupName;

    /******  2025.4.23 C拉C活动商品新增配置  ******/
    @SerializedName("tradeType")
    public int tradeType = -1;
    @SerializedName("productPageBannerImage")
    public String productPageBannerImage;
    @SerializedName("productPageBannerDarkImage")
    public String productPageBannerDarkImage;
    @SerializedName("productPagePopupImage")
    public String productPagePopupImage;
    @SerializedName("jumpUrl")
    public String jumpUrl;
    /******  2025.4.23 C拉C活动商品新增配置  ******/

    public boolean showPurchaseFreeDayPopWindow;    // 是否已经展示过买赠天数弹窗
    public boolean showBigPromotionAnimation;       // 是否已经展示过大促动画

    public int accessChannel; // 4:微信公众号签约
    public String returnUrl; // 儿童签约跳转地址
    public String jumpLinkAfterPurchase; //儿童会员购买完成后跳转Url

    public long localStartTimeOnDataGot = System.currentTimeMillis();
    public long localBaseTimeStamp = SystemClock.elapsedRealtime();

    private SkuDecorates localSkuDecorates;

    ///////////////////////////////////////////////////////////////////////////
    // SVIP 的字段
    ///////////////////////////////////////////////////////////////////////////
    public boolean isSVIPItem;
    public String svipRightUrl;             //svip权益图片
    public String svipRightDarkModeUrl;     //svip权益暗黑模式图片链接
    public String svipRightFoldableScreenUrl;     //svip权益-折叠屏暗黑模式图片链接
    public String svipRightFoldableScreenDarkModeUrl;     //svip权益-折叠屏暗黑模式图片链接
    public String saleRemark;               //售卖标记
    public int productStatusId;

    public SkuDecorates getSkuDecorates(){
        if (localSkuDecorates != null) {
            return localSkuDecorates;
        } else {
            SkuDecorates decorates = new SkuDecorates();
            boolean hasSet = false;
            if (activityPopupDto != null) {
                if (!TextUtils.isEmpty(activityPopupDto.labelText)) {
                    decorates.subscriptText = activityPopupDto.labelText;
                    decorates.subscriptLogo = activityPopupDto.subscriptLogo;
                    decorates.subscriptBackgroundColor = activityPopupDto.subscriptBackgroundColor;
                    decorates.subscriptTextColor = activityPopupDto.subscriptTextColor;
                    hasSet = true;
                } else if (!TextUtils.isEmpty(activityPopupDto.label)) {
                    decorates.labelImg = activityPopupDto.label;
                    hasSet = true;
                }
            }
            if (!hasSet && !TextUtils.isEmpty(subscriptText)) {
                decorates.subscriptText = subscriptText;
                decorates.subscriptLogo = subscriptLogo;
                decorates.subscriptBackgroundColor = subscriptBackgroundColor;
                decorates.subscriptTextColor = subscriptTextColor;
                hasSet = true;
            }
            if (!hasSet && properties != null) {
                if (!TextUtils.isEmpty(properties.labelText)) {
                    decorates.subscriptText = properties.labelText;
                } else if (!TextUtils.isEmpty(properties.label)) {
                    decorates.labelImg = properties.label;
                }
            }
            if(!TextUtils.isEmpty(backgroundImageV2)){
                decorates.backgroundImage = backgroundImageV2;
            } else if (isSVIPItem && !TextUtils.isEmpty(productSkin)) {
                //SVIP 的皮肤地址
                decorates.backgroundImage = productSkin;
            }

            if(!TextUtils.isEmpty(sellingPointBackgroundColor)){
                decorates.sellingPointBackgroundColor = sellingPointBackgroundColor;
            }
            if (applyTimeLimited) {
                decorates.decorateType = SkuDecorates.SKU_DECORATE_TYPE_SPECIAL_DISCOUNT;
            } else if (activityPurchaseFreeDayTimeLimitGetCountDown() > 0 || purchaseFreeDayCountDown() > 0) {
                decorates.decorateType = SkuDecorates.SKU_DECORATE_TYPE_PURCHASE_FREE_DAY;
            }
            localSkuDecorates = decorates;
            return decorates;
        }
    }

    public boolean applyPurchaseFreeDay() {
        //弹窗图片没有值不弹窗
        boolean hasActivityImageUrl = activityProperty != null && !TextUtils.isEmpty(activityProperty.bounceScreenImage);
        return hasActivityImageUrl;
    }

    /**
     * 新的423活动买赠是否应用
     * @return
     */
    public boolean applyActivityPurchaseFreeDay(){
        boolean hasActivityPopup = activityPopupDto != null && (!TextUtils.isEmpty(activityPopupDto.label) || !TextUtils.isEmpty(activityPopupDto.labelText));
        return hasActivityPopup;
    }

    /**
     * 新的423活动买赠活动限时优惠倒计时
     * @return
     */
    public long activityPurchaseFreeDayTimeLimitGetCountDown(){
        if(activityPopupDto == null){
            return -1;
        }
        return activityPopupDto.countDown;
    }

    public long purchaseFreeDayCountDown() {
        if (activityProperty == null) {
            return -1;
        }
        return activityProperty.countDown;
    }

    public boolean needShowPurchaseFreeDayPopWindow() {
        if (showPurchaseFreeDayPopWindow) {
            //已经展示过
            return false;
        }
        return applyPurchaseFreeDay() || applyActivityPurchaseFreeDay();
    }

    public boolean needShow818BigPromotionAnimation() {
        String debugSwitch =
                ToolUtil.getDebugSystemProperty("debug.mark.red_packet", "0");
        if (ConstantsOpenSdk.isDebug && "2".equals(debugSwitch)) {
            return true;
        }
        if (showEnvelopeAnime && !showBigPromotionAnimation) {
            return true;
        }
        return false;
    }
    public boolean needShowBigPromotionAnimation() {
//        if (!applyTimeLimited) {
//            return false;
//        }
        if (showBigPromotionAnimation)
            return false;
        String debugSwitch =
                ToolUtil.getDebugSystemProperty("debug.mark.red_packet", "0");
        if (ConstantsOpenSdk.isDebug && "1".equals(debugSwitch)) {
            return true;
        }
        long expireTime = timeLimitedExpireTime;
        long timeLeft = CountDownTimeUtil.getRealCountDownDuration(
                (expireTime - localStartTimeOnDataGot),
                localBaseTimeStamp
        );

//        if (timeLeft <= 0) {
//            timeLeft = purchaseFreeDayCountDown();
//        }
//        if (timeLeft <= 0) {
//            timeLeft = activityPurchaseFreeDayTimeLimitGetCountDown();
//        }
        return timeLeft > 0;
    }


    public boolean showPopupAnimation() {
        return canShowTimeLimitedPopWindow() || needShowPurchaseFreeDayPopWindow();
    }

    public String getItemName() {
        String temp = name;
        if (null != properties && null != properties.simpleName) {
            temp = properties.simpleName;
        }
        return temp;
    }

    public String getNowPrice() {
        return StringUtil.subZeroAndDot(payPrice);
    }

    public String getOriginalPrice() {
        double marketingPrice = null == properties ? -1d : properties.marketingPrice;
        if (0 >= marketingPrice) {
            return "";
        }
        if (0 == BigDecimal.valueOf(payPrice).compareTo(BigDecimal.valueOf(marketingPrice))) {
            return "";
        }
        return StringUtil.subZeroAndDot(marketingPrice);
    }

    public String getLabelText() {
        return null == properties ? "" : (null == properties.labelText ? "" : properties.labelText);
    }

    public String getLabelImage() {
        return null == properties ? null : (null == properties.label ? null : properties.label);
    }

    public String getRightPartButtonText() {
        return "确认协议并开通";
        // return null == properties ? "" : (null == properties.purchaseButtonText ? "" : properties.purchaseButtonText);
    }

    public boolean isAutoRenew() {
        return properties != null && properties.autoRenew;
    }

    public boolean useCoupon() {
        if (null != subProduct) {
            return subProduct.useCoupon;
        }
        return useCoupon;
    }

    public CharSequence getLeftPartButtonText() {
        String nowPrice = getNowPrice();
        String originalPrice = getOriginalPrice();
        if (StringUtil.isEmpty(originalPrice)) {
            originalPrice = "";
        } else {
            originalPrice = originalPrice + "元";
        }
        String string = "¥" + nowPrice + " /" + getItemName() + " " + originalPrice;
        SpannableString sb = new SpannableString(string);
        sb.setSpan(new AbsoluteSizeSpan(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 11f)), 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        sb.setSpan(new StyleSpan(Typeface.BOLD), 1, 1 + nowPrice.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        sb.setSpan(new AbsoluteSizeSpan(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 26f)), 1, 1 + nowPrice.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        sb.setSpan(new StrikethroughSpan(), string.length() - originalPrice.length(), string.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        sb.setSpan(new ForegroundColorSpan(Color.parseColor("#80FADED2")), string.length() - originalPrice.length(), string.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return sb;
    }

    public CharSequence getRuleInfoText(Runnable clickRunnable) {
        String rule1 = "《会员服务协议》";
        String rule2 = "《自动续费服务规则》";
        String rule3 = null;
        String rule3Url = null;

        if (!ToolUtil.isEmptyCollects(presentDetails)) {
            if (presentDetails.size() == 1) {
                VipPresentDetail presentDetail = presentDetails.get(0);
                if (null != presentDetail && !StringUtil.isEmpty(presentDetail.agreementName)) {
                    rule3 = "《" + presentDetail.agreementName + "》";
                    rule3Url = presentDetail.agreementUrl;
                }
            } else if (presentDetails.size() > 1) {
                rule3 = "联合会员服务协议";
                StringBuilder ids = new StringBuilder();
                for (VipPresentDetail detail : presentDetails) {
                    if (detail != null) {
                        if (ids.length() == 0) {
                            ids.append(detail.presentItemId);
                        } else {
                            ids.append(",").append(detail.presentItemId);
                        }
                    }
                }
                rule3Url = UrlConstants.getInstanse().getMultiVipServiceAgreementUrl(ids.toString());
            }
        }


        String string = "开通前请阅读" + rule1;
        if (null != rule3) {
            string = string + " " + rule2 + "及" + rule3;
        } else {
            string = string + "及" + rule2;
        }
        SpannableString sb = new SpannableString(string);
        int index1 = string.indexOf(rule1);
        sb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                Activity activity = BaseApplication.getMainActivity();
                if (activity instanceof MainActivity) {
                    ToolUtil.clickUrlAction((MainActivity) activity, UrlConstants.getInstanse().getVipServiceAgreementUrl(), widget);
                }
                if (null != clickRunnable) {
                    clickRunnable.run();
                }
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(BaseApplication.getMyApplicationContext().getResources().getColor(R.color.host_color_aaaaaa_66666b));
                ds.setUnderlineText(false);
            }
        }, index1, index1 + rule1.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        int index2 = string.indexOf(rule2);
        sb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                Activity activity = BaseApplication.getMainActivity();
                if (activity instanceof MainActivity) {
                    ToolUtil.clickUrlAction((MainActivity) activity, UrlConstants.getInstanse().getAutoRenewRuleAgreementUrl(), widget);
                }
                if (null != clickRunnable) {
                    clickRunnable.run();
                }
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(BaseApplication.getMyApplicationContext().getResources().getColor(R.color.host_color_aaaaaa_66666b));
                ds.setUnderlineText(false);
            }
        }, index2, index2 + rule2.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        if (null != rule3) {
            int index3 = string.indexOf(rule3);
            String finalRule3Url = rule3Url;
            sb.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    Activity activity = BaseApplication.getMainActivity();
                    if (activity instanceof MainActivity) {
                        ToolUtil.clickUrlAction((MainActivity) activity, finalRule3Url, widget);
                    }
                    if (null != clickRunnable) {
                        clickRunnable.run();
                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(BaseApplication.getMyApplicationContext().getResources().getColor(R.color.host_color_aaaaaa_66666b));
                    ds.setUnderlineText(false);
                }
            }, index3, index3 + rule3.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        return sb;
    }

    public CharSequence getChildRuleInfoText(Runnable clickRunnable) {
        String rule1 = "《会员服务协议》";
        String rule2 = "《自动续费服务规则》";

        String string = "开通前请阅读" + rule1;
        if (isAutoRenew()) {
            string = string + "及" + rule2;
        }
        SpannableString sb = new SpannableString(string);
        int index1 = string.indexOf(rule1);
        sb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                Activity activity = BaseApplication.getMainActivity();
                if (activity instanceof MainActivity) {
                    String url = UrlConstants.getInstanse().getChildVipAgreementUrl("xxm-ting-memeber-service");
                    ToolUtil.clickUrlAction((MainActivity) activity, url, widget);
                }
                if (null != clickRunnable) {
                    clickRunnable.run();
                }
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(BaseApplication.getMyApplicationContext().getResources().getColor(R.color.host_color_aaaaaa_66666b));
                ds.setUnderlineText(false);
            }
        }, index1, index1 + rule1.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        if (isAutoRenew()) {
            int index2 = string.indexOf(rule2);
            sb.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    Activity activity = BaseApplication.getMainActivity();
                    if (activity instanceof MainActivity) {
                        String url = UrlConstants.getInstanse().getChildVipAgreementUrl("xxm-ting-auto-renew-android");
                        ToolUtil.clickUrlAction((MainActivity) activity, url, widget);
                    }
                    if (null != clickRunnable) {
                        clickRunnable.run();
                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(BaseApplication.getMyApplicationContext().getResources().getColor(R.color.host_color_aaaaaa_66666b));
                    ds.setUnderlineText(false);
                }
            }, index2, index2 + rule2.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        return sb;
    }

    public String getSalesRemarkText() {
        return null == salesRemark ? "" : salesRemark;
    }

    public String getSalesRemarkTextNew() {
        String prefix = getSalesRemarkText();
        String claimText = getClaimText();
        if (TextUtils.isEmpty(prefix)) {
            if (TextUtils.isEmpty(claimText)) {
                return "";
            }
            return claimText;
        } else {
            if (TextUtils.isEmpty(claimText)) {
                return prefix;
            }
            return prefix + "\n" + claimText;
        }
    }

    public String getClaimText() {
        return localDisclaimer;
    }

    public boolean canShowTimeLimitedPopWindow() {
        return showTimeLimitedPopWindow && !TextUtils.isEmpty(timeLimitedPopupImage);
    }

    @Nullable
    @Override
    public String buildPromotionItemString() {
        boolean isNull = true;
        JSONArray jsonArray = new JSONArray();

        String tempItemId = getItemId();
        String tempCouponId = getCouponId();
        String tempPromotionCode = getPromotionCode();

        if (useCoupon() && !StringUtil.isEmpty(tempItemId)
                && !StringUtil.isEmpty(tempCouponId)
                && !StringUtil.isEmpty(tempPromotionCode)) {
            isNull = false;
            jsonArray = realBuildPromotionItemString(jsonArray, getDiscountTypeId(), getPromotionTypeId(), tempItemId, tempCouponId, tempPromotionCode);
        }

        tempPromotionCode = getTimeLimitedPromotionCode();
        if (applyTimeLimited && !StringUtil.isEmpty(tempPromotionCode)) {
            isNull = false;
            long timeLimitedPromotionId = getTimeLimitedPromotionId();
            if (0 == timeLimitedPromotionId) {
                jsonArray = realBuildPromotionItemString(jsonArray, getDiscountTypeId(), null, null, tempPromotionCode);
            } else {
                jsonArray = realBuildPromotionItemString(jsonArray, getDiscountTypeId(), timeLimitedPromotionId, null, null, tempPromotionCode);
            }
        }

        if (isNull) {
            return null;
        }
        return jsonArray.toString();
    }

    @Nullable
    @Override
    public Boolean getAutoRenew() {
        if (properties == null) {
            return false;
        }
        return properties.autoRenew;
    }

    @Nullable
    @Override
    public String getReturnUrl() {
        return returnUrl;
    }

    public String getCouponId() {
        if (null != subProduct) {
            return subProduct.couponId;
        }
        return couponId;
    }

    public int getDiscountTypeId() {
        if (null != subProduct) {
            return subProduct.discountTypeId;
        }
        return discountTypeId;
    }

    public int getPromotionTypeId() {
        if (null != subProduct) {
            return subProduct.promotionTypeId;
        }
        return promotionTypeId;
    }

    public String getPromotionCode() {
        if (null != subProduct) {
            return subProduct.promotionCode;
        }
        return promotionCode;
    }

    public String getItemId() {
        if (null != subProduct) {
            return subProduct.itemId;
        }
        return itemId;
    }

    @Nullable
    @Override
    public Boolean getPresent() {
        return present;
    }

    public String getTimeLimitedPromotionCode() {
        if (null != subProduct) {
            return subProduct.timeLimitedPromotionCode;
        }
        return timeLimitedPromotionCode;
    }

    public long getTimeLimitedPromotionId() {
        if (null != subProduct) {
            return subProduct.timeLimitedPromotionId;
        }
        return timeLimitedPromotionId;
    }

    private JSONArray realBuildPromotionItemString(JSONArray jsonArray, int discountTypeId, long promotionTypeId, String realItemId, String realCouponId, String realPromotionCode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("discountTypeId", discountTypeId);
            jsonObject.put("promotionTypeId", promotionTypeId);
            if (null != realItemId) {
                JSONArray itemIds = new JSONArray();
                itemIds.put(Long.valueOf(realItemId));
                jsonObject.put("itemIds", itemIds);
            }
            if (null != realPromotionCode) {
                jsonObject.put("promotionCode", Long.valueOf(realPromotionCode));
            }
            if (null != realCouponId) {
                jsonObject.put("promotionId", Long.valueOf(realCouponId));
            }
            jsonArray.put(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return jsonArray;
    }

    private JSONArray realBuildPromotionItemString(JSONArray jsonArray, long discountTypeId, String realItemId, String realCouponId, String realPromotionCode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("discountTypeId", discountTypeId);
            if (null != realItemId) {
                JSONArray itemIds = new JSONArray();
                itemIds.put(Long.valueOf(realItemId));
                jsonObject.put("itemIds", itemIds);
            }
            if (null != realPromotionCode) {
                jsonObject.put("promotionCode", Long.valueOf(realPromotionCode));
            }
            if (null != realCouponId) {
                jsonObject.put("promotionId", Long.valueOf(realCouponId));
            }
            jsonArray.put(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return jsonArray;
    }
    private JSONArray realBuildSubscribePromotionItemString(JSONArray jsonArray, long discountTypeId, String realPromotionCode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("promotionType", discountTypeId);
            if (null != realPromotionCode) {
                jsonObject.put("promotionCode", Long.valueOf(realPromotionCode));
            }
            jsonArray.put(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return jsonArray;
    }

    @Nullable
    @Override
    public String getSkuJumpUrl() {
        if (properties == null) {
            return null;
        }
        return properties.skuJumpUrl;
    }

    @Nullable
    @Override
    public List<Integer> getSigningChannels() {
        if (subProduct != null) {
            return subProduct.signingChannels;
        }
        return signingChannels;
    }

    @Nullable
    @Override
    public String getPurchasePopUpsDescription() {
        if (purchasePopUpsText != null) {
            return purchasePopUpsText.description;
        }
        return null;
    }


    @Nullable
    @Override
    public String getPurchasePopUpsDescriptionV2() {
        if (purchasePopUpsText != null) {
            return purchasePopUpsText.descriptionV2;
        }
        return null;
    }

    @Nullable
    @Override
    public String getPurchasePopUpsTitle() {
        if (purchasePopUpsText != null) {
            return purchasePopUpsText.title;
        }
        return null;
    }

    @Override
    public int getAccessChannel() {
        return accessChannel;
    }

    @Nullable
    @Override
    public List<String> getMobileRechargeType() {
        if (properties != null) {
            return properties.mobileRechargeType;
        }
        return null;
    }

    @Nullable
    @Override
    public String buildSubscribePromotionItemString() {
        boolean isNull = true;
        JSONArray jsonArray = new JSONArray();

        String tempPromotionCode = getTimeLimitedPromotionCode();
        if (applyTimeLimited && !StringUtil.isEmpty(tempPromotionCode)) {
            isNull = false;
            jsonArray = realBuildSubscribePromotionItemString(jsonArray, getDiscountTypeId(), tempPromotionCode);
        }

        if (isNull) {
            return null;
        }
        return jsonArray.toString();
    }

    @Nullable
    @Override
    public String getSubscriptionTips() {
        if(purchasePopUpsText != null){
            return purchasePopUpsText.subscriptionTips;
        }
        return null;
    }

    public static class VipSkuItemProperty implements Serializable {
        @SerializedName("purchaseButtonText")
        public String purchaseButtonText;               // 购买按钮文案
        @SerializedName("labelText")
        public String labelText;                        // 角标文案
        @SerializedName("label")
        public String label;                            // 角标图标
        @SerializedName("simpleName")
        public String simpleName;                           // 产品名称
        @SerializedName("marketingPrice")
        public double marketingPrice;
        @SerializedName("upgradeMonths")
        public int upgradeMonths;                       // 可升级月数
        @SerializedName("autoRenew")
        public boolean autoRenew;
        @SerializedName("skuJumpUrl")
        public String skuJumpUrl;               //sku跳转链接（联合会员跳转链接）
        @SerializedName("mobileRechargeType")
        public List<String> mobileRechargeType;               //支付方式，2 表示移动话费订阅支付
    }

    public static class VipPresentDetail implements Serializable {
        @SerializedName("name")
        public String name;
        @SerializedName("logo")
        public String logo;
        @SerializedName("salePrice")
        public String salePrice;
        @SerializedName("agreementName")
        public String agreementName;
        @SerializedName("agreementUrl")
        public String agreementUrl;
        @SerializedName("presentItemId")
        public String presentItemId;
    }

    public static class VipActivityProperty implements Serializable {
        @SerializedName("countDown")
        public long countDown;

        @SerializedName("bounceScreenImage")
        public String bounceScreenImage;

        @SerializedName("labelText")
        public String labelText;
    }

    public static class ActivityPopupDto implements Serializable {
        @SerializedName("countDown")
        public long countDown;

        @SerializedName("pic")
        public String pic;

        @SerializedName("labelText")
        public String labelText;

        @SerializedName("label")
        public String label;

        @SerializedName("subscriptLogo")
        public String subscriptLogo;

        @SerializedName("subscriptBackgroundColor")
        public String subscriptBackgroundColor;

        @SerializedName("subscriptTextColor")
        public String subscriptTextColor;
    }
    /**
     * Created by 5Greatest on 2021.05.07
     *
     * <AUTHOR>
     * On 2021/5/7
     */
    public static class VipPurchasePopUpsText implements Serializable {
        @SerializedName("title")
        public String title;
        @SerializedName("description")
        public String description;
        @SerializedName("descriptionV2")
        public String descriptionV2;
        @SerializedName("subscriptionTips")
        public String subscriptionTips;
    }
    public static class UpgradeProductModel implements Serializable {
        @SerializedName("itemId")
        public String itemId;
        @SerializedName("text")
        public String text;
        @SerializedName("quantity")
        public int quantity;
        @SerializedName("label")
        public String label;
        @SerializedName("price")
        public String price;
        @SerializedName("months")
        public int months;
        @SerializedName("monthlyPrice")
        public String monthlyPrice;
    }

    public static class SkuDecorates {
        public static final int SKU_DECORATE_TYPE_NORMAL = 0;//兜底
        public static final int SKU_DECORATE_TYPE_SPECIAL_DISCOUNT = 1;//限时折扣
        public static final int SKU_DECORATE_TYPE_PURCHASE_FREE_DAY = 2;//加赠类型
//
        private int decorateType = SKU_DECORATE_TYPE_NORMAL;//当前装饰类型

        private String labelImg;//角标图片
        private String subscriptText;//角标文案
        private String subscriptLogo;//角标前缀icon
        private String subscriptBackgroundColor;//角标背景色
        private String subscriptTextColor;//角标字体颜色
        private String backgroundImage;//sku背景
        private String sellingPointBackgroundColor;//卖点背景色


        public String getSubscriptText() {
            return subscriptText;
        }

        public String getSubscriptLogo() {
            return subscriptLogo;
        }

        public String getSubscriptBackgroundColor() {
            return subscriptBackgroundColor;
        }

        public String getSubscriptTextColor() {
            return subscriptTextColor;
        }

        public String getBackgroundImage() {
            return backgroundImage;
        }

        public String getSellingPointBackgroundColor() {
            return sellingPointBackgroundColor;
        }

        public void setSubscriptText(String subscriptText) {
            this.subscriptText = subscriptText;
        }

        public void setSubscriptLogo(String subscriptLogo) {
            this.subscriptLogo = subscriptLogo;
        }

        public void setSubscriptBackgroundColor(String subscriptBackgroundColor) {
            this.subscriptBackgroundColor = subscriptBackgroundColor;
        }

        public void setSubscriptTextColor(String subscriptTextColor) {
            this.subscriptTextColor = subscriptTextColor;
        }

        public void setBackgroundImage(String backgroundImage) {
            this.backgroundImage = backgroundImage;
        }

        public void setSellingPointBackgroundColor(String sellingPointBackgroundColor) {
            this.sellingPointBackgroundColor = sellingPointBackgroundColor;
        }


        public int getDecorateType() {
            return decorateType;
        }

        public void setDecorateType(int decorateType) {
            this.decorateType = decorateType;
        }

        public String getLabelImg() {
            return labelImg;
        }

        public void setLabelImg(String labelImg) {
            this.labelImg = labelImg;
        }
    }
}
