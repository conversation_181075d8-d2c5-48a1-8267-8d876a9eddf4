package com.ximalaya.ting.android.host.model.ad;

import android.os.Parcel;

import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.VideoCommentInfo;

import java.util.ArrayList;
import java.util.List;

public class AdUnLockVipTrackAdvertis extends Advertis {
    private int videoDuration; // adx视频时长

    private int unlockTime; // 可解锁时长

    private int unlockTimeV2; // 新畅听可解锁时长倒计时
    private int againDuration;// 二次奖励时长，单位s
    private int againPopupType ; // 0 不触发二次奖励 13 二次激励视频奖励 14 二次唤端奖励

    private List<VideoCommentInfo> videoCommentList; // 激励视频弹幕评论
    private boolean commentClickEnable; // 弹幕评论是否可点击

    /**
     * 默认构造方法
     */
    public AdUnLockVipTrackAdvertis() {
        super();
    }

    public AdUnLockVipTrackAdvertis(Advertis advertis) {
        super(advertis);
    }

    public int getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(int videoTime) {
        this.videoDuration = videoTime;
    }

    public int getUnlockTime() {
        return unlockTime;
    }

    public void setUnlockTime(int unlockTime) {
        this.unlockTime = unlockTime;
    }

    public int getUnlockTimeV2() {
        return unlockTimeV2;
    }

    public void setUnlockTimeV2(int unlockTimeV2) {
        this.unlockTimeV2 = unlockTimeV2;
    }

    public int getAgainDuration() {
        return againDuration;
    }

    public void setAgainDuration(int duration) {
        this.againDuration = duration;
    }

    public int getAgainPopupType() {
        return againPopupType;
    }

    public void setAgainPopupType(int againPopupType) {
        this.againPopupType = againPopupType;
    }

    public List<VideoCommentInfo> getVideoCommentList() {
        return videoCommentList;
    }

    public void setVideoCommentList(List<VideoCommentInfo> videoCommentList) {
        this.videoCommentList = videoCommentList;
    }

    public boolean isCommentClickEnable() {
        return commentClickEnable;
    }

    public void setCommentClickEnable(boolean commentClickEnable) {
        this.commentClickEnable = commentClickEnable;
    }

    @Override
    public void readFromParcel(Parcel in) {
        super.readFromParcel(in);
        this.videoDuration = in.readInt();
        this.unlockTime = in.readInt();
        this.unlockTimeV2 = in.readInt();
        this.againDuration = in.readInt();
        this.againPopupType = in.readInt();
        List<VideoCommentInfo> videoCommentList = new ArrayList<>();
        in.readList(videoCommentList, VideoCommentInfo.class.getClassLoader());
        setVideoCommentList(videoCommentList);
        this.commentClickEnable = in.readInt() == 1;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeInt(this.videoDuration);
        dest.writeInt(this.unlockTime);
        dest.writeInt(this.unlockTimeV2);
        dest.writeInt(againDuration);
        dest.writeInt(againPopupType);
        dest.writeList(videoCommentList);
        dest.writeInt(commentClickEnable ? 1 : 0);
    }

    public static final Creator<AdUnLockVipTrackAdvertis> CREATOR =
            new Creator<AdUnLockVipTrackAdvertis>() {
                @Override
                public AdUnLockVipTrackAdvertis createFromParcel(Parcel source) {
                    AdUnLockVipTrackAdvertis adUnLockAdvertisModel = new AdUnLockVipTrackAdvertis();
                    adUnLockAdvertisModel.readFromParcel(source);
                    return adUnLockAdvertisModel;
                }

                @Override
                public AdUnLockVipTrackAdvertis[] newArray(int size) {
                    return new AdUnLockVipTrackAdvertis[size];
                }
            };
}
