package com.ximalaya.ting.android.host.model.play;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * Create by {jian.kang} on 2024/3/22
 *
 * <AUTHOR>
 */

public class PlayPageBusinessInfo {
//    {
//        "visible": true,
//            "title": "问AI",
//            "guideText": "总结概要，归纳亮点，解疑答惑",
//            "link": "iting://open?msg_type=94&bundle=rn_ai_search&agent=xiaoya&scene=PlayPageBookGPT&trackId=1766765&searchWord="
//    }
    @SerializedName("agentAskAi")
    public AgentAskAi agentAskAi;

    public static class AgentAskAi {
        @SerializedName("visible")
        public boolean visible;

        @SerializedName("title")
        public String title;

        @SerializedName("guideText")
        public String guideText;

        @SerializedName("link")
        public String link;

        @SerializedName("noSugLink")
        public String noSugLink;

        @SerializedName("icon")
        public String icon;

        public boolean isValid() {
            return visible && title != null && !title.isEmpty() && link != null && !link.isEmpty();
        }

        public boolean hasGuideText() {
            return guideText != null && !guideText.isEmpty();
        }
    }


    @SerializedName("picTextModel")
    public List<PicTextModel> picTextModel;

    public static class PicTextModel {

        @SerializedName("createdAt")
        public long createdAt;

        @SerializedName("trackId")
        public long trackId;

        @SerializedName("videoType")
        public int videoType;

        @SerializedName("videoContentUrl")
        public String videoContentUrl;

        @SerializedName("updatedAt")
        public long updatedAt;

    }


    @SerializedName("emotionValueShare")
    public EmotionValueShareWrap emotionValueShare;

    public static class EmotionValueShareWrap {
        @SerializedName("guideLayer")
        public EmotionValueShare guideLayer;
    }

    public static class EmotionValueShare implements Serializable {

        @SerializedName("highlightContent")
        public String highlightContent;

        @SerializedName("isVip")
        public boolean isVip;

        @SerializedName("coverUrl")
        public String coverUrl;

        @SerializedName("albumTitle")
        public String albumTitle;

        @SerializedName("vipLevel")
        public String vipLevel;

        @SerializedName("sign")
        public String sign;

        @SerializedName("purchaseUserId")
        public long purchaseUserId;

        @SerializedName("tradeOrderNo")
        public String tradeOrderNo;

        @SerializedName("totalGiftCount")
        public int totalGiftCount;

        @SerializedName("redeemGiftCount")
        public int redeemGiftCount;

        @SerializedName("weekListenDuration")
        public long weekListenDuration;

        @SerializedName("source")
        public String source;
    }
}
