package com.ximalaya.ting.android.host.model.newuser

import android.os.Parcelable
import com.google.gson.Gson
import kotlinx.parcelize.Parcelize

/**
 * 新用户活动相关
 */
object NewUserActiveManager {
    const val SCENETYPE_RECALLUSER = "reCallUser"//场景类型定：reCallUser-召回用户，newUser-新用户,preLossUser-预流失
    const val SCENETYPE_NEWUSER = "newUser"
    const val SCENETYPE_PRELOSSUSER = "preLossUser"
    const val SCENETYPE_RECALL_VIP_USER = "recallVipUser_task"

    private var mNewUserActivityResul: NewUserActivityResul? = null
    private var mNeedShowLotteryDlg = false

    fun getUserTypeBySceneType(sceneType: String?): String {
        return when(sceneType) {
            SCENETYPE_RECALLUSER -> "recall"
            SCENETYPE_PRELOSSUSER -> "preLost"
            SCENETYPE_RECALL_VIP_USER -> "recallVip"
            else -> "new"
        }
    }

    fun clear() {
        mNewUserActivityResul = null
        mNeedShowLotteryDlg = false
    }

    fun parse(data: String) {
        try {
            var newUserActivityResul = Gson().fromJson(data, NewUserActivityResul::class.java)
            newUserActivityResul?.sendResultMap?.newUserLottery?.let { newUserLottery ->
                if (newUserLottery.activityErrorCode == 0) {
                    newUserLottery.backContent?.let {
                        mNeedShowLotteryDlg = true
                    }
                }
            }
            mNewUserActivityResul = newUserActivityResul
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getLotteryTitle(): String? {
        return mNewUserActivityResul?.sendResultMap?.newUserLottery?.backContent?.title
    }

    fun getLotteryCover(): String? {
        return mNewUserActivityResul?.sendResultMap?.newUserLottery?.backContent?.coverPath
    }

    fun getLotteryUrl(): String? {
        return mNewUserActivityResul?.sendResultMap?.newUserLottery?.backContent?.url
    }

    fun getLotteryContent(): BackContent? {
        return mNewUserActivityResul?.sendResultMap?.newUserLottery?.backContent
    }

    interface OnCheckCallBack {
        fun onCheckCallBack(needShow: Boolean)
    }
}

@Parcelize
data class NewUserActivityResul(
    val msg: String,
    val refreshMainPage: Boolean,
    val sendResultMap: SendResultMap?
) : Parcelable

@Parcelize
data class SendResultMap(
    val limitPartFree: LimitPartFree?,
    val newUserLottery: NewUserLottery?,
    val oldLimitPartFree: OldLimitPartFree?
) : Parcelable

@Parcelize
data class LimitPartFree(
    val activityErrorCode: Int,
    val activityErrorMsg: String
) : Parcelable

@Parcelize
data class OldLimitPartFree(
    val activityErrorCode: Int,
    val activityErrorMsg: String
) : Parcelable

@Parcelize
data class NewUserLottery(
    val activityErrorCode: Int,
    val activityErrorMsg: String = "",
    val backContent: BackContent?
) : Parcelable

@Parcelize
data class BackContent(
    val coverPath: String = "",
    val title: String = "",
    val type: Int, //类型0-抽奖弹框，1-查看中奖弹框
    val url: String = "" //活动url
) : Parcelable

