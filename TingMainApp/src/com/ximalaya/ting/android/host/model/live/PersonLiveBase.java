package com.ximalaya.ting.android.host.model.live;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 音视频直播基础信息
 *
 * <AUTHOR>
 */
public interface PersonLiveBase {
    /**
     * 未指定状态
     */
    int LIVE_STATUS_UNSPECIFIED = 0;

    /**
     * 直播结束状态
     */
    int LIVE_STATUS_END = 1;

    /**
     * 直播预告状态
     */
    int LIVE_STATUS_NOTICE = 5;

    /**
     * 正在直播中状态
     */
    int LIVE_STATUS_ING = 9;


    /**
     * 直播状态枚举类型
     */
    @IntDef({LIVE_STATUS_END, LIVE_STATUS_NOTICE, LIVE_STATUS_ING, LIVE_STATUS_UNSPECIFIED})
    @Retention(RetentionPolicy.SOURCE)
    @interface LiveStatus {

    }
}
