package com.ximalaya.ting.android.host.listener

import android.view.View
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem

/**
 * Created by mark on 2025/3/6 17:09
 */
interface IPVipSkuItemActionListener {
    fun onSkuItemSelected(item: VipSkuItem)
    fun onSkuUpgradeMonthClick(view: View,item:VipSkuItem)
    fun onSkuUpgradeMonthViewShow(item:VipSkuItem)
    fun getUpgradeSkuSet():Pair<VipSkuItem, VipSkuItem.UpgradeProductModel?>?
}

