package com.ximalaya.ting.android.host.util.common;

import static com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost.TINGMAIN_KEY_MAC_ADDRESS;
import static com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost.TINGMAIN_KEY_WEBVIEW_USEAGENT;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.media.AudioManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Environment;
import android.os.Looper;
import android.provider.MediaStore;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.Surface;

import androidx.annotation.Keep;
import androidx.core.app.NotificationManagerCompat;
import androidx.fragment.app.Fragment;

import com.igexin.sdk.PushManager;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.encryptservice.DeviceTokenUtil;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileProviderUtil;
import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.framework.util.SerialInfo;
import com.ximalaya.ting.android.host.BuildConfig;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.TouTiaoAdManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.smartdevice.ISmartDeviceFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SmartDeviceActionRouter;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.device.DeviceType;
import com.ximalaya.ting.android.host.util.OAIDUtil;
import com.ximalaya.ting.android.host.util.constant.AppConfigConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.opensdk.httputil.util.BASE64Encoder;
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel;
import com.ximalaya.ting.android.opensdk.player.manager.CrossProcessTransferValueManager;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmutil.BaseDeviceUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

//import android.webkit.WebSettings;
//import android.webkit.WebView;

/**
 * 设备工具类
 * warning:该类部分代码会被com.ximalaya.ting.android.host.util.common.EncryptUtil中的jni代码通过反射调用，修改相关方法请注意函数注释
 *
 * <AUTHOR> 邮箱：<EMAIL>
 */
public class DeviceUtil extends BaseDeviceUtil {

    public static final String PACKAGE_360_WEISHI = "com.qihoo360.mobilesafe";
    public static final String PACKAGE_TENCENT_GUANJIA = "com.tencent.qqpimsecure";
    public static final String PACKAGE_LBE_ANQUAN = "com.lbe.security";
    public static final String PACKAGE_BAIDU_WEISHI = "cn.opda.a.phonoalbumshoushou";
    public static final String PACKAGE_LIEBAO_DASHI = "com.cleanmaster.mguard_cn";
    private static final String TAG = DeviceUtil.class.getSimpleName();
    private static final String KEY_MIUI_VERSION_CODE = "ro.miui.ui.version.code";
    private static final String KEY_MIUI_VERSION_NAME = "ro.miui.ui.version.name";
    private static final String KEY_MIUI_INTERNAL_STORAGE = "ro.miui.internal.storage";
    private static final String CHECK_OP_NO_THROW = "checkOpNoThrow";
    private static final String OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION";


    public static final String OPERATOR_TYPE_MOBILE = "Mobile";
    public static final String OPERATOR_TYPE_UNICOM = "Unicom";
    public static final String OPERATOR_TYPE_TELECOM = "Telecom";


    public static String updateMacAddress(Context context) {
        SharedPreferencesUtil.getInstance(context).removeByKey(TINGMAIN_KEY_MAC_ADDRESS);
        return getLocalMacAddress(context);
    }


    public static String mFormatMacAddress;

    public static Boolean is64ByteVM = null;

    public static String getFormatMacAddress(Context contex) {
        if (!TextUtils.isEmpty(mFormatMacAddress)) {
            return mFormatMacAddress;
        }
        String mac = getLocalMacAddress(contex);
        if (TextUtils.isEmpty(mac)) {
            return mac;
        }
        mac = mac.replace(":", "");
        byte[] bytes = StringUtil.hexStr2ByteArray(mac);
        mFormatMacAddress = BASE64Encoder.encode(bytes);
        return mFormatMacAddress;
    }

    public static String getFormatMacAddress(Context contex, boolean isWifi) {
        if (!TextUtils.isEmpty(mFormatMacAddress)) {
            return mFormatMacAddress;
        }
        String mac = getLocalMacAddress(contex, isWifi);
        if (TextUtils.isEmpty(mac)) {
            return mac;
        }
        mac = mac.replace(":", "");
        byte[] bytes = StringUtil.hexStr2ByteArray(mac);
        mFormatMacAddress = BASE64Encoder.encode(bytes);
        return mFormatMacAddress;
    }

    public static boolean isAppInstalled(Context context, String appPackageName) {
        PackageInfo packageInfo;

        try {
            packageInfo = context.getPackageManager().getPackageInfo(
                    appPackageName, 0);

        } catch (NameNotFoundException e) {
            return false;
        }

        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }

    public static boolean isMIUI() {
        try {
            final BuildProperties prop = BuildProperties.newInstance();
            return prop.getProperty(KEY_MIUI_VERSION_CODE, null) != null
                    || prop.getProperty(KEY_MIUI_VERSION_NAME, null) != null
                    || prop.getProperty(KEY_MIUI_INTERNAL_STORAGE, null) != null;
        } catch (final IOException e) {
            if ("Xiaomi".equals(Build.MANUFACTURER)) {
                return true;
            }
            return false;
        }
    }
    public static String CPU_ABI = "";
    public static void is64CpuAbi(I64CpuAbiCallback callback) {
        if (!TextUtils.isEmpty(CPU_ABI)) {
            if (callback != null) {
                callback.is64CpuAbi(CPU_ABI.contains("arm64-v8a"));
            }
            return;
        }
        MyAsyncTask.execute(() -> {
            // 慢函数 简单更改为异步方式
            if (TextUtils.isEmpty(CPU_ABI)) {
                try {
                    String os_cpuabi = new BufferedReader(
                            new InputStreamReader(
                                    Runtime.getRuntime().exec("getprop ro.product.cpu.abi")
                                            .getInputStream())).readLine();
                    if (os_cpuabi.contains("x86")) {
                        CPU_ABI = "x86";
                    } else if (os_cpuabi.contains("armeabi-v7a")) {
                        CPU_ABI = "armeabi-v7a";
                    } else if (os_cpuabi.contains("arm64-v8a")) {
                        CPU_ABI = "arm64-v8a";
                    } else {
                        CPU_ABI = "armeabi";
                    }
                } catch (Exception e) {
                    CPU_ABI = "armeabi";
                }
            }
            HandlerManager.postOnUIThread(() -> {
                if (callback != null) {
                    callback.is64CpuAbi(CPU_ABI.contains("arm64-v8a"));
                }
            });
        });
    }

    public static boolean isApp64BitAbi(Context context) {
        try {
            String nativeLibraryDir = context.getApplicationInfo().nativeLibraryDir;
            int nextIndexOfLastSlash = nativeLibraryDir.lastIndexOf("/") + 1;
            String instructionSet = nativeLibraryDir.substring(nextIndexOfLastSlash);
            return "arm64".equals(instructionSet) || "x86_64".equals(instructionSet) || "mips64".equals(instructionSet);
        } catch (Exception e) {
        }
        return true;
    }

    public interface I64CpuAbiCallback {
        void is64CpuAbi(boolean is64CpuAbi);
    }

    public static boolean isMeizu() {
        return "Meizu".equalsIgnoreCase(android.os.Build.BRAND);
    }

    public static boolean isHuaWei() {
        String manufacturer = Build.MANUFACTURER;
        return !TextUtils.isEmpty(manufacturer)
                && ("huawei".equalsIgnoreCase(manufacturer) || manufacturer.toLowerCase(Locale.CHINA).contains("huawei"));
    }

    // 检查当前设备是否为三星手机
    public static boolean isSamsungDevice() {
        return Build.MANUFACTURER.equalsIgnoreCase("Samsung");
    }

    // 检查当前是否在安全文件夹中
    public static boolean isSamsungSafeFolder() {
        if (isSamsungDevice()) {
            File appDirectory = BaseApplication.getMyApplicationContext().getFilesDir();
            if (appDirectory != null) {
                String appPath = appDirectory.getAbsolutePath();
                if (appPath != null && !appPath.isEmpty()) {
                    int userIdFromPath = extractUserIdFromPath(appPath);
                    //询问过三星厂商，安全文件夹在150到160之间
                    if (150 <= userIdFromPath && userIdFromPath<= 160) {
                        return true;
                    }
                }
            }

        }
        return false;
    }

    // 提取路径中的用户ID
    private static int extractUserIdFromPath(String path) {
        try {
            // 定义匹配用户ID的正则表达式模式
            Pattern pattern = Pattern.compile("/data/user/(\\d+)/");
            Matcher matcher = pattern.matcher(path);

            // 查找匹配的字符串并提取用户ID
            if (matcher != null && matcher.find()) {
                String userIdString = matcher.group(1);
                if (!userIdString.isEmpty()) {
                    return Integer.parseInt(userIdString);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1; // 默认值
    }

    /**
     * 是否是摩托罗拉机型
     */
    public static boolean isMotorolaDevice() {
        String manufacturer = Build.MANUFACTURER;
        return !TextUtils.isEmpty(manufacturer)
                && ("Motorola".equalsIgnoreCase(manufacturer)
                || manufacturer.toLowerCase(Locale.CHINA).contains("motorola"));
    }

    private static String mActiveChannel;

    /**
     * @return 激活渠道
     */
    public static String getActiveChannel(Context context) {
        if (TextUtils.isEmpty(mActiveChannel)) {
            String channel;
            if (!TextUtils.isEmpty(channel = ToolUtil.getChannelForHuaweiPreinstall())) {
                //            channel += "-prt";
            } else if (!TextUtils.isEmpty(channel = ToolUtil.getChannelForRongyaoPreinstall())) {
                //            channel += "-prt";
            }  else if (ToolUtil.checkPreinstallApp2018("com.ximalaya.ting.android")) {
                channel = "yz-xm2018";
            } else if (ToolUtil.checkPreinstallAppOld("com.ximalaya.ting.android")) {
                channel = "yz-xiaomi";
            } else if (!TextUtils.isEmpty(channel = ToolUtil.getChannelFromEtc())
                    || !TextUtils.isEmpty(channel = ToolUtil.getChannelFromEtcForOppo())
                    || !TextUtils.isEmpty(channel = ToolUtil.getChannelForVivoPreInstall())) {
                //            channel += "-prt";
            } else {
                channel = DeviceUtil.getChannelInApk(context);
            }

            mActiveChannel = channel;
        }

        return mActiveChannel;

    }


    public static boolean IsAirModeOn(Context context) {
        return (Settings.Global.getInt(context.getContentResolver(),
                Settings.Global.AIRPLANE_MODE_ON, 0) == 1);
    }

    private static String mOriginalChannel = null; // 原始渠道

    public static String getOriginalChannel(Context context) {
        if (mOriginalChannel == null) {
            String channel;
            if (!TextUtils.isEmpty(channel = ToolUtil.getChannelForHuaweiPreinstall())) {
                //            channel += "-prt";
            } else if (!TextUtils.isEmpty(channel = ToolUtil.getChannelForRongyaoPreinstall())) {
                //            channel += "-prt";
            } else if (ToolUtil.checkPreinstallApp2018("com.ximalaya.ting.android")) {
                channel = "yz-xm2018";
            } else if (ToolUtil.checkPreinstallAppOld("com.ximalaya.ting.android")) {
                channel = "yz-xiaomi";
            } else if (!TextUtils.isEmpty(channel = ToolUtil.getChannelFromEtc())
                    || !TextUtils.isEmpty(channel = ToolUtil.getChannelFromEtcForOppo())
                    || !TextUtils.isEmpty(channel = ToolUtil.getChannelForVivoPreInstall())) {
                //            channel += "-prt";
            }

            if (TextUtils.isEmpty(channel)) {
                mOriginalChannel = "";
            } else {
                mOriginalChannel = channel;
            }
        }

        return mOriginalChannel;
    }

    public static boolean isGreyVersion() {
        String versionFour = getVersionFour(ToolUtil.getCtx());
        if (TextUtils.isEmpty(versionFour)) {
            return false;
        }
        if (versionFour.endsWith(".3")) {
            return false;
        }
        return true;
    }

    public static String getVersionFour(Context context) {
        if (!TextUtils.isEmpty(mVersionFour)) {
            return mVersionFour;
        }
        mVersionFour = SerialInfo.getVersionName(context);
        if (!TextUtils.isEmpty(mVersionFour)) {
            String[] str = mVersionFour.split("\\.");
            if (str != null && str.length > 3) {
                StringBuilder sb = null;

                for (int i = 0; i < 4; i++) {
                    if (sb == null) {
                        sb = new StringBuilder();
                        sb.append(str[i]);
                    } else {
                        sb.append(".");
                        sb.append(str[i]);
                    }
                }
                if (sb != null)
                    mVersionFour = sb.toString();
            }
        }

        return mVersionFour;
    }

    public static String getVersion(Context context) {
        if (!TextUtils.isEmpty(mVersion)) {
            return mVersion;
        }
        mVersion = SerialInfo.getVersionName(context);
        if (!TextUtils.isEmpty(mVersion)) {
            String[] str = mVersion.split("\\.");
            if (str != null && str.length > 3) {
                StringBuilder sb = null;
                for (int i = 0; i < 3; i++) {
                    if (sb == null) {
                        sb = new StringBuilder();
                        sb.append(str[i]);
                    } else {
                        sb.append(".");
                        sb.append(str[i]);
                    }
                }
                if (sb != null) {
                    mVersion = sb.toString();
                }
            }
        }
        return mVersion;
    }

    public static boolean isHighVersionThree(String versionStrA, String versionStrB) {
        if (TextUtils.isEmpty(versionStrA) || TextUtils.isEmpty(versionStrB)) {
            return false;
        }
        boolean flag = false;
        int[] versionArrA = getVersionArray(versionStrA);
        int[] versionArrB = getVersionArray(versionStrB);
        if (versionArrA == null || versionArrA.length != 3 || versionArrB == null || versionArrB.length != 3) {
            return false;
        }

        if (versionArrA[0] > versionArrB[0]) {
            flag = true;
        } else if (versionArrA[0] == versionArrB[0]) { // 大 版本相同
            if (versionArrA[1] > versionArrB[1]) {
                flag = true;
            } else if (versionArrA[1] == versionArrB[1]) { //   大中 版本相同
                if (versionArrA[2] >= versionArrB[2]) {
                    flag = true;
                }
            }
        }
        return flag;
    }

    private static int[] getVersionArray(String versionStr) {
        if (TextUtils.isEmpty(versionStr)) {
            return null;
        }
        int[] versionArrA = {0, 0, 0};
        String logicVersion = versionStr.trim();
        if (logicVersion.contains(".")) {
            String currentVersionNew = logicVersion.replace(".", "A");
            String[] versionStrArr = currentVersionNew.split("A");
            if (versionStrArr.length >= 3) {
                try {
                    versionArrA[0] = Integer.parseInt(versionStrArr[0]);
                    versionArrA[1] = Integer.parseInt(versionStrArr[1]);
                    versionArrA[2] = Integer.parseInt(versionStrArr[2]);
                } catch (Exception e) {
                    return null;
                }
            } else {
                return null;
            }
            return versionArrA;
        }
        return null;
    }

    public static int getAppVersionCode(Context context) {
        return getVersionCode(context);
    }

    public static String mVersion;
    public static String mVersionFour;

    public static String getPackageName(Context context) {
        Context application = MainApplication.getMyApplicationContext();
        if (application == null) {
            application = context.getApplicationContext();
        }
        return application.getPackageName();
    }

    /**
     * Warning:请求接口中有这个参数的，其他参数符合条件!TextUtils.isEmpty() return true.
     * genSignature:(这里用一句话描述这个方法的作用)
     *
     * @param @param  context
     * @param @param  params
     * @param @return 设定文件
     * @return String DOM对象
     * @throws
     * @since CodingExample　Ver 1.1
     */
    public static String genSignature(Context context,
                                      Map<String, String> params) {
        TreeMap<String, String> map = new TreeMap<>(
                String.CASE_INSENSITIVE_ORDER);
        String channel = getChannelInApk(context);
        if (!TextUtils.isEmpty(channel)) {
            map.put("channel", channel);
        }

        long uid = 0;
        String token = null;
        if (UserInfoMannage.hasLogined()) {
            uid = UserInfoMannage.getInstance().getUser().getUid();
            token = UserInfoMannage.getInstance().getUser().getToken();
            map.put("uid", String.valueOf(uid));
            map.put("token", token);
        }

        String device;
        if (AppConstants.isPad) {
            device = "androidpad";
        } else {
            device = "android";
        }
        map.put("device", device);

        String deviceId = getAndroidId(context);
        map.put("deviceId", deviceId);

        String version = getVersion(context);
        map.put("version", version);

        String impl = context.getPackageName();
        map.put("impl", impl);

        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            map.put(key, value);
        }

        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (!TextUtils.isEmpty(sb)) {
                sb.append("&");
            }
            String key = entry.getKey();
            String value = entry.getValue();
            if (!TextUtils.isEmpty(value)) {
                sb.append(key).append("=").append(value);
            }
        }

        sb.append("&").append(AppConfigConstants.SECURETY_KEY);
        return MD5.md5(sb.toString().toLowerCase());
    }

    public static int getDeviceType(BluetoothDevice btd) {
        if (btd.getAddress().startsWith(DeviceType.SUICHETING_FILTER_ADDR)) {
            return DeviceType.suicheting;
        } else if (btd.getAddress().startsWith(DeviceType.XIMAO_FILTER_ADDR_1)) {
            return DeviceType.ximao;
        } else if (btd.getAddress().startsWith(DeviceType.XIMAO_FILTER_ADDR_2)) {
            return DeviceType.ximao;
        } else if (btd.getAddress().startsWith(DeviceType.QCHETING_FILTER_ADDR)) {
            return DeviceType.Qsuicheting;
        } else if (btd.getAddress().startsWith(DeviceType.SCHETING_FILTER_ADDR)) {
            return DeviceType.Ssuicheting;
        } else if (btd.getAddress().startsWith(
                DeviceType.METAL_CHETING_FILTER_ADDR)) {
            return DeviceType.Msuicheting;
        }
        return DeviceType.otherBluetooth;
    }

    public static void initAfterGetDevice(final Context context,
                                          final BluetoothDevice btd, boolean needPlay) {
        Logger.d("Bluetooth", "DeviceUtil.initAfterGetDevice");
        int deviceType = DeviceUtil.getDeviceType(btd);
        Logger.d("Bluetooth", "deviceType: " + deviceType);
        SmartDeviceUtil.currentPlayingType = deviceType;
        if (deviceType != DeviceType.otherBluetooth) {
            Logger.d("Bluetooth", "该蓝牙设备是喜马拉雅的设备");
            initRecordModel(context, btd);
            boolean needContinuePlay = false;
            try {
                needContinuePlay = ((ISmartDeviceFunctionAction) Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction()).needContinuePlay();
            } catch (Exception e) {
                e.printStackTrace();
            }
            Logger.d("Bluetooth", "needContinuePlay: " + needContinuePlay);
            if (needContinuePlay) {
                XDCSCollectUtil.statErrorToXDCS("SmartDevice_AutoPlay","deviceType:_____"+deviceType);
                PlayTools.needContinuePlay(context, true);
            }
        } else {
            Logger.d("Bluetooth", "该蓝牙设备不是喜马拉雅的设备");
        }

        if (deviceType == DeviceType.suicheting
                || deviceType == DeviceType.Msuicheting
                || deviceType == DeviceType.Qsuicheting
                || deviceType == DeviceType.Ssuicheting) {
            Logger.d("Bluetooth", "该设备是随车听");

            boolean isSuichetingAutoPlaying = false;
            try {
                isSuichetingAutoPlaying = ((ISmartDeviceFunctionAction) Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction()).isSuichetingAutoPlaying();
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (isSuichetingAutoPlaying) {
                Logger.d("Bluetooth", "连接自动播放");
                if (needPlay) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(100);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            int i = 0;
                            while (i < 400) {
                                try {
                                    Thread.sleep(150);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                                if (judgeA2dpConn(btd)) {
                                    Logger.d("Bluetooth", "A2DP Connected");
                                    AudioManager mAudioManager = SystemServiceManager.getAudioManager(context);
                                    // 连接蓝牙后将系统音量设为最大，因为目前喜马拉雅平台的音量过小
                                    mAudioManager
                                            .setStreamVolume(
                                                    AudioManager.STREAM_MUSIC,
                                                    mAudioManager
                                                            .getStreamMaxVolume(AudioManager.STREAM_MUSIC),
                                                    0);
                                    toMainAppPlay(context, true);
                                    break;
                                }
                                i++;
                            }

                        }
                    }, "conn-device").start();
                }
            } else {
                Logger.d("Bluetooth", "连接不自动播放");
            }

            boolean needContinuePlay = false;
            try {
                needContinuePlay = ((ISmartDeviceFunctionAction) Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction()).needContinuePlay();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (needContinuePlay) {
                XDCSCollectUtil.statErrorToXDCS("SmartDevice_AutoPlay","deviceType:_____"+deviceType);
                PlayTools.needContinuePlay(context, true);
            }
        }
    }

    private static void initRecordModel(Context context, BluetoothDevice btd) {
        Logger.d("Bluetooth", "initRecordModel");
        boolean needRecord = false;
        int deviceType = DeviceUtil.getDeviceType(btd);
        RecordModel model = new RecordModel();
        model.setType(1);
        switch (deviceType) {
            case DeviceType.suicheting:
                model.setDevice(1);
                needRecord = true;
                break;
            case DeviceType.ximao:
                model.setDevice(3);
                needRecord = true;
                break;
            case DeviceType.Qsuicheting:
                model.setDevice(6);
                needRecord = true;
                break;
            case DeviceType.Msuicheting:
                model.setDevice(7);
                needRecord = true;
                break;
            default:
                break;
        }
        if (needRecord) {
            Logger.d("Bluetooth", "是需要记录的设备：随车听／喜猫");
            model.setDeviceName(btd.getAddress());
            PlayTools.setRecordModel(context, model);
        }
    }

    private static void toMainAppPlay(Context context, boolean isPlayNow) {
        Logger.d("Bluetooth", "toMainAppPlay");
        boolean needContinuePlay = false;
        try {
            needContinuePlay = ((ISmartDeviceFunctionAction) Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction()).needContinuePlay();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (needContinuePlay) {
            PlayTools.needContinuePlay(context, true);
        }
        String url = "iting://open";
        Intent intent = new Intent(Intent.ACTION_VIEW);
        if (isPlayNow) {
            intent.putExtra("PLAYINGNOW", "PLAYINGNOW");
        }
        intent.setData(Uri.parse(url));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            context.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static String userAgentByWebView;
    private static boolean isAyncUpdateAgent;

    public static String getMemoryUserAgentByWebView() {
        return userAgentByWebView;
    }

    public static String getUserAgentByWebView(final Context context) {
        if (!TextUtils.isEmpty(userAgentByWebView)) {
            return userAgentByWebView;
        }

        if (ToolUtil.isFirstInstallApp(context)) {
            return getSystemUserAgent();
        }

        String userAgent = MmkvCommonUtil.getInstance(context).getStringCompat(TINGMAIN_KEY_WEBVIEW_USEAGENT);

        int osVersion = MmkvCommonUtil.getInstance(context).
                getIntCompat(PreferenceConstantsInHost.TINGMAIN_KEY_WEBVIEW_USEAGENT_SAVE_OS_VERSION);

        // 用户没有升级版本
        if (!TextUtils.isEmpty(userAgent) && Build.VERSION.SDK_INT == osVersion) {
            userAgentByWebView = userAgent;
            return userAgent;
        }

        long curTime = System.currentTimeMillis();
        String uaFromWeb = getUAFromWeb(context);
        Logger.log("DeviceUtil : getUAFromWeb useTime " + (System.currentTimeMillis() - curTime));
        return uaFromWeb;
    }

    private synchronized static String getUAFromWeb(Context context) {
        String userAgent = "";
        try {
            userAgent = getUAByWebSettings(context);

            if (!TextUtils.isEmpty(userAgent)) {
                userAgentByWebView = userAgent;
                return userAgent;
            }

            if (Looper.getMainLooper() == Looper.myLooper()) {
                userAgent = userAgentByWebView = getUAFromWebView(context);
            } else {
                HandlerManager.obtainMainHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (!TextUtils.isEmpty(userAgentByWebView)) {
                            return;
                        }
                        userAgentByWebView = getUAFromWebView(context);
                    }
                });

                userAgent = getSystemUserAgent();
            }

            return userAgent;
        } catch (Exception e) {
            e.printStackTrace();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return getSystemUserAgent();
    }

    public static synchronized String getUAByWebSettings(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            try {
                Thread thread = new Thread(new Runnable() {
                    @Override
                    public void run() {
                        Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
                        String defaultUserAgent = WebSettings.getDefaultUserAgent(context);
                        saveUAToSP(context, defaultUserAgent);
                    }
                });
                thread.setName("DeviceUtil_getDefaultUserAgent_Thread");
                thread.start();
                thread.join(3000);

                return MmkvCommonUtil.getInstance(context).getStringCompat(TINGMAIN_KEY_WEBVIEW_USEAGENT);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    private static void saveUAToSP(Context context, String ua) {
        if (!TextUtils.isEmpty(ua)) {
            MmkvCommonUtil.getInstance(context).saveString(TINGMAIN_KEY_WEBVIEW_USEAGENT, ua);
            MmkvCommonUtil.getInstance(context)
                    .saveInt(PreferenceConstantsInHost.TINGMAIN_KEY_WEBVIEW_USEAGENT_SAVE_OS_VERSION,
                            Build.VERSION.SDK_INT);

            userAgentByWebView = ua;
        }
    }

    public static void getUAByWebSettingsAsync(Context context) {
        if(isAyncUpdateAgent) {
            return;
        }

        isAyncUpdateAgent = true;
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                String uaByWebSettings = getUAByWebSettings(context);
                saveUAToSP(context, uaByWebSettings);
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        TouTiaoAdManager.retryQueryItingAfterSystemUAGot(ToolUtil.getCtx());
                    }
                });
            }
        });
    }


    private static String getUAFromWebView(Context context) {
        String userAgentString = null;
        try {
            WebView webview = new WebView(context.getApplicationContext());
            webview.setWebViewClient(new WebViewClient() {
                @Override
                public boolean onRenderProcessGone(WebView webView, RenderProcessGoneDetail renderProcessGoneDetail) {
                    super.onRenderProcessGone(webView, renderProcessGoneDetail);
                    return true;
                }
            });
            WebSettings settings = webview.getSettings();
            userAgentString = settings.getUserAgentString();
            saveUAToSP(context, userAgentString);
            webview.destroy();

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return userAgentString;
    }

    private static String systemUserAgent = null;

    /**
     * 获取系统自带的UA
     */
    public static String getSystemUserAgent() {
        if (systemUserAgent != null) {
            return systemUserAgent;
        }
        try {
            systemUserAgent = System.getProperty("http.agent");
        } catch (Exception ignored) {
            systemUserAgent = "";
        }
        return systemUserAgent;
    }


    //获取手机号码，有可能获取不到
    public static String getDevicePhone(Context context) {
        if (context == null) {
            return "";
        }
        String phone = "";
        try {
            TelephonyManager telephonyManager = SystemServiceManager.getTelephonyManager(context);
            if (telephonyManager != null) {
                phone = "" + telephonyManager.getLine1Number();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return phone;
    }

    public static boolean isNotificationEnabled(Context context) {
        context = context == null ? BaseApplication.getMyApplicationContext() : context;
        NotificationManagerCompat compat = NotificationManagerCompat.from(context);
        return compat.areNotificationsEnabled();
    }

    /**
     * 打开用用通知开关界面，有的厂商有自己的界面
     * 从小米手机开始尝试，都不是则进入系统默认应用详情界面
     **/
    public static void goToNotifyCationSettingsUi(Activity activity) {
        gotoMiUiNotifySettings(activity);
    }

    /**
     * 跳转到miui的权限管理页面
     */
    private static void gotoMiUiNotifySettings(Activity activity) {
        Intent i = new Intent("miui.intent.action.APP_PERM_EDITOR");
        ComponentName componentName = new ComponentName("com.miui.securitycenter", "com.miui.permcenter.permissions.AppPermissionsEditorActivity");
        i.setComponent(componentName);
        i.putExtra("extra_pkgname", getPackageName(activity));
        try {
            activity.startActivity(i);
        } catch (Exception e) {
            e.printStackTrace();
            gotoFlyMeNotifySettings(activity);
        }
    }

    /**
     * 跳转到魅族的权限管理系统
     */
    private static void gotoFlyMeNotifySettings(Activity activity) {
        Intent intent = new Intent("com.meizu.safe.security.SHOW_APPSEC");
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.putExtra("packageName", BuildConfig.LIBRARY_PACKAGE_NAME);
        try {
            activity.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            gotoHuaWeiNotifySettings(activity);
        }
    }

    /**
     * 华为的权限管理页面
     */
    private static void gotoHuaWeiNotifySettings(Activity activity) {
        try {
            Intent intent = new Intent();
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            ComponentName comp = new ComponentName("com.huawei.systemmanager", "com.huawei.permissionmanager.ui.MainActivity");//华为权限管理
            intent.setComponent(comp);
            activity.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                activity.startActivity(getAppDetailSettingIntent(activity));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }

    }

    /**
     * 获取应用详情页面intent
     *
     * @return
     */
    private static Intent getAppDetailSettingIntent(Activity activity) {
        Intent localIntent = new Intent();
        localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (Build.VERSION.SDK_INT >= 9) {
            localIntent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
            localIntent.setData(Uri.fromParts("package", activity.getPackageName(), null));
        } else if (Build.VERSION.SDK_INT <= 8) {
            localIntent.setAction(Intent.ACTION_VIEW);
            localIntent.setClassName("com.android.settings", "com.android.settings.InstalledAppDetails");
            localIntent.putExtra("com.android.settings.ApplicationPkgName", activity.getPackageName());
        }
        return localIntent;
    }

    /**
     * 调用系统InstalledAppDetails界面显示已安装应用程序的详细信息。 对于Android 2.3（Api Level
     * 9）以上，使用SDK提供的接口； 2.3以下，使用非公开的接口（查看InstalledAppDetails源码）。
     *
     * @param context
     */
    public static void showInstalledAppDetails(Context context) {
        Intent intent = new Intent();
        final int apiLevel = Build.VERSION.SDK_INT;
        if (apiLevel >= 9) { // 2.3（ApiLevel 9）以上，使用SDK提供的接口
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", context.getPackageName(), null);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setData(uri);
        }
        context.startActivity(intent);
    }

    public static void openSystemNotificationPage(Context context) {
        PushManager.getInstance().openNotification(context);
    }

    public static void checkCameraPermissonAndGoCamera(final Fragment fragment, final Uri uri, final int resultCode) {
        String status = Environment.getExternalStorageState();
        if (!Environment.MEDIA_MOUNTED.equals(status)) {
            CustomToast.showFailToast("手机没有SD卡");
            return;
        }

        Activity activity = fragment.getActivity();
        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.CAMERA, R.string.host_deny_perm_camera);
                            }
                        }, new IMainFunctionAction.IPermissionListener() {
                            @Override
                            public void havedPermissionOrUseAgree() {
                                try {
                                    Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                    intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
                                    fragment.startActivityForResult(intent, resultCode);
                                } catch (Exception e) {
                                    CustomToast.showFailToast("此设备没有照相功能");
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void userReject(Map<String, Integer> noRejectPermiss) {

                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void goCamera(final Fragment fragment, final Uri uri, final int resultCode) {
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
        fragment.startActivityForResult(intent, resultCode);
    }

    public static void checkCameraPermissonAndGoCamera(final Activity activity, final Uri uri, final int resultCode) {
        String status = Environment.getExternalStorageState();
        if (!Environment.MEDIA_MOUNTED.equals(status)) {
            CustomToast.showFailToast("手机没有SD卡");
            return;
        }

        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.CAMERA, R.string.host_deny_perm_camera);
                            }
                        }, new IMainFunctionAction.IPermissionListener() {
                            @Override
                            public void havedPermissionOrUseAgree() {
                                try {
                                    Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                    intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
                                    activity.startActivityForResult(intent, resultCode);
                                } catch (Exception e) {
                                    CustomToast.showFailToast("此设备没有照相功能");
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void userReject(Map<String, Integer> noRejectPermiss) {

                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void checkCameraPermissonAndGoCameraVideo(final Activity activity, final Uri uri, final int resultCode) {
        String status = Environment.getExternalStorageState();
        if (!Environment.MEDIA_MOUNTED.equals(status)) {
            CustomToast.showFailToast("手机没有SD卡");
            return;
        }

        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.CAMERA, R.string.host_deny_perm_camera);
                            }
                        }, new IMainFunctionAction.IPermissionListener() {
                            @Override
                            public void havedPermissionOrUseAgree() {
                                try {
                                    Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
                                    intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
                                    activity.startActivityForResult(intent, resultCode);
                                } catch (Exception e) {
                                    CustomToast.showFailToast("此设备没有照相功能");
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void userReject(Map<String, Integer> noRejectPermiss) {

                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void goVideo(final Fragment fragment, final Uri uri, final int resultCode) {
        Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
        // 必须用fragment不然收不到回调
        fragment.startActivityForResult(intent, resultCode);
    }

    public static void checkCameraPermissonAndGoCameraVideo(final Fragment fragment, final Uri uri, final int resultCode) {
        Activity activity = fragment.getActivity();
        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.CAMERA, R.string.host_deny_perm_camera);
                            }
                        }, new IMainFunctionAction.IPermissionListener() {
                            @Override
                            public void havedPermissionOrUseAgree() {
                                try {
                                    Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
                                    intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
                                    // 必须用fragment不然收不到回调
                                    fragment.startActivityForResult(intent, resultCode);
                                } catch (Exception e) {
                                    CustomToast.showFailToast("此设备没有照相功能");
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void userReject(Map<String, Integer> noRejectPermiss) {

                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void checkCameraPermissonAndCallBackOnHavPermisson(final Activity activity, final Uri uri, final int resultCode, final IHandleOk handleOk) {
        String status = Environment.getExternalStorageState();
        if (!Environment.MEDIA_MOUNTED.equals(status)) {
            CustomToast.showFailToast("手机没有SD卡");
            return;
        }
        boolean useRedPermission = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, PreferenceConstantsInHost.KEY_CAMERA_USE_READ_PERMISSION, false);

        final WeakReference<IHandleOk> handleOkWeakReference = new WeakReference<IHandleOk>(handleOk);

        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.CAMERA, R.string.host_deny_perm_camera);
                                if (useRedPermission) {
                                    put(Manifest.permission.READ_EXTERNAL_STORAGE, R.string.host_deny_perm_read_sdcard);
                                }
                            }
                        }, new IMainFunctionAction.IPermissionListener() {
                            @Override
                            public void havedPermissionOrUseAgree() {
                                if (handleOkWeakReference != null && handleOkWeakReference.get() != null) {
                                    handleOkWeakReference.get().onReady();
                                    return;
                                }

                                try {
                                    Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                    intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(uri));
                                    activity.startActivityForResult(intent, resultCode);
                                } catch (Exception e) {
                                    CustomToast.showFailToast("此设备没有照相功能");
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void userReject(Map<String, Integer> noRejectPermiss) {
                                if (useRedPermission) {
                                    CustomToast.showFailToast("无法获取相机权限和存储权限！");
                                } else {
                                    CustomToast.showFailToast("无法获取相机权限！");
                                }
                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void checkStoragePermissonAndCallBackOnHavPermisson(final Activity activity, final IHandleOk handleOk) {
        String status = Environment.getExternalStorageState();
        if (!Environment.MEDIA_MOUNTED.equals(status)) {
            CustomToast.showFailToast("手机没有SD卡");
            return;
        }

        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.READ_EXTERNAL_STORAGE, R.string.host_deny_perm_read_sdcard);
                            }
                        }, new IMainFunctionAction.IPermissionListener() {
                            @Override
                            public void havedPermissionOrUseAgree() {
                                if (handleOk != null) {
                                    handleOk.onReady();
                                    return;
                                }
                            }

                            @Override
                            public void userReject(Map<String, Integer> noRejectPermiss) {
                                CustomToast.showFailToast("无法获取存储权限！");
                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    public static boolean judgeA2dpConn(BluetoothDevice btd) {
        try {
            if (BluetoothAdapter.getDefaultAdapter() == null) {
                return false;
            }

            int currentapiVersion = Build.VERSION.SDK_INT;
            if (currentapiVersion < 11) {
                return true;
            } else {
                int a2dp = BluetoothAdapter.getDefaultAdapter()
                        .getProfileConnectionState(BluetoothProfile.A2DP);
                if (a2dp == BluetoothProfile.STATE_CONNECTED) {
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }

        return false;
    }

    public static String mDeviceRes;

    public static String getDeviceRes(Context context) {
        if (!TextUtils.isEmpty(mDeviceRes)) {
            return mDeviceRes;
        }
        try {
            String deviceRes = URLEncoder.encode(
                    BaseUtil.getScreenWidth(context) + ","
                            + BaseUtil.getScreenHeight(context),
                    "utf-8");
            mDeviceRes = deviceRes;
        } catch (UnsupportedEncodingException e) {
            return mDeviceRes;
        }
        return mDeviceRes;
    }

    public static String mManufacturer;

    public static String getManufacturer() {
        if (!TextUtils.isEmpty(mManufacturer)) {
            return mManufacturer;
        }
        try {
            String manufacturer = android.os.Build.MANUFACTURER;
            if (!TextUtils.isEmpty(manufacturer)) {
                mManufacturer = URLEncoder.encode(manufacturer, "utf-8");
            }
        } catch (Exception e) {
        }
        return mManufacturer;
    }

    /**
     * 检测设备是否支持webP格式的图片
     * 参考文档：https://developer.android.com/studio/write/convert-webp.html
     *
     * @return 是否支持webP格式的图片
     */
    public static boolean isDeviceSupportWebP() {
        return Build.VERSION.SDK_INT >= 18;
    }

    public static void switchDebugWebView(boolean enable) {
        if (Build.VERSION.SDK_INT >= 19) {
            WebView.setWebContentsDebuggingEnabled(enable);
        }
    }

    /***
     * 使用WIFI时，获取本机IP地址
     * @param mContext
     * @return
     */
    public static String getWIFILocalIpAdress(Context mContext) {
        String ip = "";
        try {
            //获取wifi服务
            WifiManager wifiManager = SystemServiceManager.getWifiManager(mContext.getApplicationContext());
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            int ipAddress = wifiInfo.getIpAddress();
            ip = formatIpAddress(ipAddress);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ip;
    }

    private static String formatIpAddress(int ipAdress) {

        return (ipAdress & 0xFF) + "." +
                ((ipAdress >> 8) & 0xFF) + "." +
                ((ipAdress >> 16) & 0xFF) + "." +
                (ipAdress >> 24 & 0xFF);
    }

    /**
     * 使用GPRS时，获取本机IP地址
     *
     * @return
     */
    public static String getGPRSLocalIpAddress() {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface
                    .getNetworkInterfaces(); en.hasMoreElements(); ) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf
                        .getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress()) {
                        return inetAddress.getHostAddress().toString();
                    }
                }
            }
        } catch (SocketException ex) {
        }
        return null;
    }


    /**
     * @return 当前是否处于横屏状态，不区分正向横屏和反向横屏
     */
    public static boolean isLandscape(Activity activity) {
        int orientation = getScreenOrientation(activity);

        return orientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE ||
                orientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
    }

    /**
     * 获取屏幕的朝向
     */
    public static int getScreenOrientation(Activity activity) {
        if (activity == null || activity.getWindowManager() == null) {
            return ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
        }
        Display display = activity.getWindowManager().getDefaultDisplay();
        int rotation = display.getRotation();
        DisplayMetrics dm = new DisplayMetrics();
        display.getMetrics(dm);
        int width = dm.widthPixels;
        int height = dm.heightPixels;
        if (PadAdaptUtil.isPad(activity)) {
            width = PadAdaptUtil.getWidth(activity);
            height = PadAdaptUtil.getHeight(activity);
        }
        int orientation;
        if ((rotation == Surface.ROTATION_0
                || rotation == Surface.ROTATION_180) && height > width || (rotation == Surface.ROTATION_90
                || rotation == Surface.ROTATION_270) && width > height) {
            switch (rotation) {
                case Surface.ROTATION_0:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                    break;
                case Surface.ROTATION_90:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                    break;
                case Surface.ROTATION_180:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
                    break;
                case Surface.ROTATION_270:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
                    break;
                default:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                    break;
            }
        } else {
            switch (rotation) {
                case Surface.ROTATION_0:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                    break;
                case Surface.ROTATION_90:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                    break;
                case Surface.ROTATION_180:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
                    break;
                case Surface.ROTATION_270:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
                    break;
                default:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                    break;
            }
        }

        return orientation;
    }

    /**
     * android p 版本上Build.VERSION.SDK_INT的值为27
     */
    public static int getVersionSdkInt() {
        String release = Build.VERSION.RELEASE;
        int sdkInt;
        if (release.equalsIgnoreCase("p")) {
            sdkInt = 28;
        } else if (release.equalsIgnoreCase("Q")) {
            // Google Pixel上存在bug：sdk_int为28，release为"Q".
            sdkInt = 29;
        } else {
            sdkInt = Build.VERSION.SDK_INT;
        }

        return sdkInt;
    }

    public static String getDeviceToken(Context context) {

        if (SharedPreferencesUtil.getInstance(context).getBoolean(PreferenceConstantsInHost.KEY_IS_NEW_DEVICE_SET, false)) {
            String string = SharedPreferencesUtil.getInstance(context).getString(PreferenceConstantsInHost
                    .KEY_UUID_FOR_TEST);
            if (!TextUtils.isEmpty(string)) {
                return string;
            }
        }

        String deviceId =  DeviceTokenUtil.getDeviceToken(context);
        if (TextUtils.isEmpty(deviceId)) {
            deviceId = DeviceTokenUtil.getOldDeviceToken(context);
            // 公测期间上报空id情况
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null) {
                String xuid = DeviceTokenUtil.getDeviceToken(context);
                String msg = "xuid: " + (xuid == null ? "null" : xuid) + "; device Id: " + (deviceId == null ? "null" : deviceId);
                xdcsPost.statErrorToXDCS("DeviceIdEmpty", msg);
            }
        }
        return deviceId;
    }

    /**
     * 获取网络运营商
     *
     * @param context
     * @return
     */
    @Deprecated
    public static String getCarrierOperator(Context context) {
        String carrierOperator = "";
        TelephonyManager telephonyManager = SystemServiceManager.getTelephonyManager(context);
        try {
            if (telephonyManager != null) {
                String operatorString = telephonyManager.getSimOperator();
                if (TextUtils.isEmpty(operatorString)) {
                    carrierOperator = "None";
                    return carrierOperator;
                }
                if (operatorString.equals("46000") || operatorString.equals("46002")) {
                    //中国移动
                    carrierOperator = "Mobile";
                } else if (operatorString.equals("46001")) {
                    //中国联通
                    carrierOperator = "Unicom";
                } else if (operatorString.equals("46003")) {
                    //中国电信
                    carrierOperator = "Telecom";
                }
            } else {
                carrierOperator = "None";
            }
            return carrierOperator;
        } catch (Exception e) {
            e.printStackTrace();
            return "None";
        }
    }

    /**
     * 获取网络运营商
     *
     * @param context
     * @return
     */
    @Deprecated
    public static String getCarrierOperatorUseCache(Context context) {
        try {
            int operator = NetworkType.getOperator(context);
            if (operator == NetworkType.OPERATOR_CMCC) {
                return OPERATOR_TYPE_MOBILE;
            } else if (operator == NetworkType.OPERATOR_UNICOME) {
                return OPERATOR_TYPE_UNICOM;
            } else if (operator == NetworkType.OPERATOR_TELECOM) {
                return OPERATOR_TYPE_TELECOM;
            } else {
                return "None";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "None";
        }
    }

    /**
     * 获取运营商请使用这个方法，这个方法将对wifi 下的运营商做一个校准：
     * 在移动蜂窝网络下，将获取sim卡的运营商，在wifi 下，将通过ip 库查询用户wifi 运营商
     *
     * @param context
     * @return
     */
    public static String getCarrierOperatorNew(Context context) {
        return getCarrierOperatorNew(context, NetworkType.isConnectToWifi(context));
    }

    public static String getCarrierOperatorNew(Context context, boolean isWifi) {
        if (isWifi) { // 如果是wifi
            if (!TextUtils.isEmpty(WifiOperatorManager.wifiOperator)) {
                return WifiOperatorManager.wifiOperator;
            }
        }
        int type = NetworkType.getOperator(context);
        String carrierOperatorName = "None";
        switch (type) {
            case NetworkType.OPERATOR_CMCC:
                carrierOperatorName = OPERATOR_TYPE_MOBILE; // 移动
                break;
            case NetworkType.OPERATOR_UNICOME:
                carrierOperatorName = OPERATOR_TYPE_UNICOM; // 联通
                break;
            case NetworkType.OPERATOR_TELECOM:
                carrierOperatorName = OPERATOR_TYPE_TELECOM; // 电信
                break;
            default:
                carrierOperatorName = "None";

        }
        return carrierOperatorName;
    }

    //获取手机IMEI
    public static String getIMEI(Context context) {
        return SerialInfo.getIMEI(context);
    }

    //获取网络制式
    public static String getNetworkModeUseCache(Context context) {
        NetworkType.NetWorkType netWorkType = NetworkType.getNetWorkType(context);
        if (!NetworkType.isConnectTONetWork(context)) {
            return "NETWORK_TYPE_UNCONNECTED";
        } else if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_WIFI) {
            return "NETWORK_TYPE_WIFI";
        } else if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_5G) {
            return "NETWORK_TYPE_5G";
        } else if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_4G) {
            return "NETWORK_TYPE_4G";
        } else if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_3G) {
            return "NETWORK_TYPE_3G";
        } else if (netWorkType == NetworkType.NetWorkType.NETWORKTYPE_2G) {
            return "NETWORK_TYPE_2G";
        }
        return "NETWORK_TYPE_UNKNOWN";
    }

    //获取网络制式
    public static String getNetworkMode(Context context) {
        //获取系统的网络服务
        ConnectivityManager connManager = SystemServiceManager.getConnectivityManager(context.getApplicationContext());
        return getNetworkMode(connManager);
    }

    //获取网络制式
    public static String getNetworkMode(ConnectivityManager connManager) {
        try {
            if (null == connManager) {
                return "NETWORK_TYPE_UNKNOWN";
            }

            //获取当前网络类型，如果为空，返回无网络
            NetworkInfo activeNetInfo = connManager.getActiveNetworkInfo();
            if (activeNetInfo == null) {
                return "NETWORK_TYPE_UNCONNECTED";
            }

            if (!activeNetInfo.isAvailable() || !activeNetInfo.isConnected()) {
                return "NETWORK_TYPE_UNCONNECTED";
            }

            // 判断连接的是不是wifi
            if (activeNetInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                return "NETWORK_TYPE_WIFI";
            } else if (activeNetInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                switch (activeNetInfo.getSubtype()) {
                    case TelephonyManager.NETWORK_TYPE_CDMA:// ~ 14-64 kbps
                    case TelephonyManager.NETWORK_TYPE_IDEN:// ~25 kbps
                    case TelephonyManager.NETWORK_TYPE_1xRTT:// ~ 50-100 kbps
                    case TelephonyManager.NETWORK_TYPE_EDGE:// ~ 50-100 kbps
                    case TelephonyManager.NETWORK_TYPE_GPRS:// ~ 100 kbps
                        return "NETWORK_TYPE_2G";

                    case TelephonyManager.NETWORK_TYPE_EVDO_0:// ~ 400-1000 kbps
                    case TelephonyManager.NETWORK_TYPE_UMTS:// ~ 400-7000 kbps
                    case TelephonyManager.NETWORK_TYPE_EVDO_A:// ~ 600-1400 kbps
                    case TelephonyManager.NETWORK_TYPE_HSPA:// ~ 700-1700 kbps
                    case TelephonyManager.NETWORK_TYPE_HSUPA:// ~ 1-23 Mbps
                    case TelephonyManager.NETWORK_TYPE_HSDPA:// ~ 2-14 Mbps
                    case 15: // 对应TelephonyManager.NETWORK_TYPE_HSPAP: 在api level 13下没有此值，但存在此网络类型，下面直接用数值代替
                        return "NETWORK_TYPE_3G";

                    case 13: // 对应TelephonyManager.NETWORK_TYPE_LTE
                        return "NETWORK_TYPE_4G";

                    case TelephonyManager.NETWORK_TYPE_UNKNOWN:
                    default:
                        return "NETWORK_TYPE_UNKNOWN";
                }
            } else {
                return "NETWORK_TYPE_UNKNOWN";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "NETWORK_TYPE_UNCONNECTED";
    }

    private static final String[] HW_DEVICES = new String[]{"PCT-AL10", "PCT-TL10", "PCT-L29", "VCE-AL00", "VCE-TL00", "VCE-L22"};
    private static final String NAVIGATION_BAR_IS_MIN = "navigationbar_is_min";
    private static final int NAVIGATION_BAR_IS_MIN_DEFAULT = 0;

    public static boolean isHwFilletDevice() {
        for (String deviceName : HW_DEVICES) {
            if (deviceName.equals(Build.MODEL)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isOtherFilletDevice() {
        return "V1816A".equals(Build.MODEL) || "PAFM00".equals(Build.MODEL);
    }

    public static boolean checkNavigationBarShowForHuawei(Context context) {
        if (context == null) {
            return false;
        }
        //获取导航栏状态值：0表示显示；1表示隐藏
        int value = Settings.Global.getInt(context.getContentResolver(), NAVIGATION_BAR_IS_MIN, NAVIGATION_BAR_IS_MIN_DEFAULT);
        return value == 0;
    }

    // 二方库会反射调用
    // 不能明文传递OAID了
    @Keep
    @Deprecated
    public static String getOAID() {
        return OAIDUtil.OAID == null
                ? (CrossProcessTransferValueManager.getOAID() == null ? "" : CrossProcessTransferValueManager.getOAID())
                : OAIDUtil.OAID;
    }

    public static String getEncryptOAID() {
        return OAIDUtil.getEncryptOAID() == null
                ? (CrossProcessTransferValueManager.getEncryptOAID() == null ? "" : CrossProcessTransferValueManager.getEncryptOAID())
                : OAIDUtil.getEncryptOAID();
    }

    //0 - have not set, 1 - false, 2 - true
    private static int isMiui12 = 0;

    public static boolean isMiui12() {
        if (isMiui12 != 0) {
            return isMiui12 == 2;
        }

        try {
            String miuiVersionCode =
                    com.ximalaya.ting.android.xmutil.BuildProperties.getSystemProperty(KEY_MIUI_VERSION_CODE, "7");

            if ("10".equals(miuiVersionCode)) {
                isMiui12 = 2;
            } else if ("V12".equals(com.ximalaya.ting.android.xmutil.BuildProperties.getSystemProperty(KEY_MIUI_VERSION_NAME, ""))) {
                isMiui12 = 2;
            } else {
                isMiui12 = 1;
            }

        } catch (Exception e) {
            e.printStackTrace();
            isMiui12 = 1;
        }
        return isMiui12 == 2;
    }

    private static boolean isCollected = false;

    public static void collectLocalInfo() {
        // 如果是miui12不收集应用列表
        if (DeviceUtil.isMiui12()
                // (oppo 设备 || 三星) && 大于等于android 10不收集
                || ((com.ximalaya.ting.android.framework.manager.BuildProperties.isOppoOs()
                || com.ximalaya.ting.android.framework.manager.BuildProperties.isSamsung()) && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)) {
            Logger.log("MainActivity : dont collect");
            return;
        }

        if (isCollected) {
            return;
        }

        isCollected = true;

        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                String fileName = BaseApplication.mAppInstance.getFilesDir() + File.separator + "pushpro_last_collect_time";
                String lastTimeStr = FileUtil.readStrFromFile(fileName);
                long time = 0;
                boolean foreUpload = false;
                if (!TextUtils.isEmpty(lastTimeStr)) {
                    try {
                        time = Long.parseLong(lastTimeStr);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    foreUpload = true;
                }

                if (System.currentTimeMillis() - time > 3 * 24 * 60 * 60 * 1000) {
                    CommonRequestM.collectInfoCDN(BaseApplication.mAppInstance, fileName, foreUpload);
                }
            }
        });
    }

    public static boolean isSamsung() {
        String manufacturer = getManufacturer();

        if (!TextUtils.isEmpty(manufacturer)) {
            return manufacturer.toLowerCase().contains("samsung");
        }

        return false;
    }

    public static boolean is64Byte() {
        //  dalvik.system.VMRuntime.getRuntime().is64Bit();
        try {
            if (is64ByteVM != null) {
                return is64ByteVM;
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                is64ByteVM = false;
                return false;
            }
            Class VMRuntime = Class.forName("dalvik.system.VMRuntime");

            Method vmRuntimeGetRuntime = VMRuntime.getDeclaredMethod("getRuntime", new Class[]{});
            Object VMRuntimeObj = vmRuntimeGetRuntime.invoke(null);

            Method method = VMRuntime.getDeclaredMethod("is64Bit", new Class[]{});
            Object is64Bit = method.invoke(VMRuntimeObj);
            if (is64Bit instanceof Boolean) {
                is64ByteVM = ((Boolean) is64Bit);
                return is64ByteVM;
            }
        } catch (Throwable e) {
            Logger.i(TAG, "is64Bit throw exception " + e);
        }
        is64ByteVM = false;
        return false;
    }

    private static Boolean isHarmonyOs;
    public static boolean isHarmonyOS() {
        if (isHarmonyOs != null) {
            return isHarmonyOs;
        }

        try {
            Class.forName("ohos.aafwk.ability.Ability");
            isHarmonyOs = true;
        } catch (Exception ignored) {
            if ("harmony".equals(getHarmonyOsBandName())) {
                isHarmonyOs = true;
            } else {
                isHarmonyOs  = false;
            }
        }

        return isHarmonyOs;
    }

    private static String harmonyOsName = "";
    /**
     * 获取华为系统版本名称
     * @return String  返回值：harmony
     * */
    public static String getHarmonyOsBandName() {
        try {
            if (TextUtils.isEmpty(harmonyOsName)) {
                Class clz = Class.forName("com.huawei.system.BuildEx");
                harmonyOsName =  (String) clz.getMethod("getOsBrand").invoke(clz);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return harmonyOsName;
    }

    private static String harmonyOsVersion = "";
    /**
     * 获取华为系统版本号
     * @return 版本号
     */
    public static String getHarmonyOsVersion() {
        if (TextUtils.isEmpty(harmonyOsVersion)) {
            harmonyOsVersion = getProp("hw_sc.build.platform.version", "");
        }
        return harmonyOsVersion;
    }

    private static String getProp(String property, String defaultValue) {
        try {
            Class spClz = Class.forName("android.os.SystemProperties");
            Method method = spClz.getDeclaredMethod("get", String.class);
            String value = (String) method.invoke(spClz, property);
            if (TextUtils.isEmpty(value)) {
                return defaultValue;
            }
            return value;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return defaultValue;
    }


    private static Integer harmonyOsPureModeState;
    /**
     * 获取鸿蒙纯净模式状态
     * 0：开启，1：关闭
     *
     * @param context
     * @return
     */
    public static int readHarmonyPureModeState(Context context) {
        try {
            if (!isHarmonyOS()) {
                return 1;
            }
            if (harmonyOsPureModeState == null && context != null) {
                harmonyOsPureModeState = Settings.Secure.getInt(context.getContentResolver(), "pure_mode_state", 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return harmonyOsPureModeState;
    }

    public static boolean isOppoDevice() {
        String model = Build.MANUFACTURER.toLowerCase();
        if ("oppo".equalsIgnoreCase(model) || "oneplus".equalsIgnoreCase(model) || "realme".equalsIgnoreCase(model)) {
            return true;
        }
        return false;
    }

    public static boolean isSonyDevice() {
        String model = Build.MANUFACTURER.toLowerCase();
        if ("sony".equalsIgnoreCase(model)) {
            return true;
        }
        return false;
    }

    private static String sDeviceModel = null;
    /**
     * 获取手机型号
     *
     * @return 手机型号
     */
    public static String getDeviceModel() {
        if (sDeviceModel == null) {
            sDeviceModel = android.os.Build.MODEL;
        }
        return sDeviceModel;
    }
}