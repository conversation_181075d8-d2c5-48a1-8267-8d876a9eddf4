package com.ximalaya.ting.android.host.util.view;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.core.graphics.ColorUtils;
import androidx.palette.graphics.Palette;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.SceneColorUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class DomainColorUtil {
    private static int[] sHightSL = {26, 20};
    private static int[] sMiddleSL = {10, 50};
    private static int[] sLowSL = {10, 50};
    private static List<int[]> sSLList = new ArrayList<>();
    private static SparseArray sDomainColorMap = new SparseArray();
    private static float[] sColorLs21_31_41_56 = {0.26f, 0.31f, 0.41f, 0.56f};
    private static float[] sColorLs21_31_51 = {0.21f, 0.31f, 0.40f};
    private static float[] sColorLs21_31_41_51 = {0.21f, 0.31f, 0.41f, 0.51f};
    private static float[] sColorLs21_27_33 = {0.21f, 0.27f, 0.33f};
    private static float[] sColorLs21_31_41 = {0.21f, 0.31f, 0.41f};
    private static float[] sColorLs21_31_41_48 = {0.21f, 0.31f, 0.41f, 0.48f};
    private static float[] sColorLs31_41_56 = {0.31f, 0.41f, 0.56f};

    static {
        sSLList.add(sHightSL);
        sSLList.add(sMiddleSL);
        sSLList.add(sLowSL);
        sDomainColorMap.put(50, new int[]{1, 2});
        sDomainColorMap.put(130, new int[]{1, 2});
        sDomainColorMap.put(240, new int[]{1, 2});
        sDomainColorMap.put(290, new int[]{1, 2});
        sDomainColorMap.put(300, new int[]{1, 2});
        sDomainColorMap.put(360, new int[]{1, 2});
    }

    private static float findClosetLByH(float originH, float origins,  float originL) {
        float[] colorLs = sColorLs21_31_51;
        if (originH >= 0 && originH <= 10) {
            colorLs = sColorLs21_31_51;
        }
        if (originH >= 11 && originH <= 49) {
            colorLs = sColorLs21_31_41;
        }
        if (originH >= 50 && originH <= 80) {
            colorLs = sColorLs21_31_41;
        }
        if (originH >= 81 && originH <= 170) {
            colorLs = sColorLs21_31_41_48;
        }
        if (originH >= 171 && originH <= 200) {
            colorLs = sColorLs21_31_41_48;
        }
        if (originH >= 201 && originH <= 215) {
            colorLs = sColorLs21_31_41_51;
        }
        if (originH >= 216 && originH <= 235) {
            colorLs = sColorLs21_31_41_56;
        }
        if (originH >= 236 && originH <= 275) {
            colorLs = sColorLs21_31_41_56;
        }
        if (originH >= 276 && originH <= 319) {
            colorLs = sColorLs31_41_56;
        }
        if (originH >= 320 && originH <= 354) {
            colorLs = sColorLs21_31_51;
        }
        return findClosestL(originL, colorLs);
    }

    private static float findClosestL(float originL, float[] colorLs1) {
        if (colorLs1 == null || colorLs1.length == 0) {
            return -1;
        }
        float closestL = colorLs1[0];
        float minDiff = Math.abs(colorLs1[0] - originL);

        for (float colorL : colorLs1) {
            float diff = Math.abs(colorL - originL);

            if (diff < minDiff) {
                minDiff = diff;
                closestL = colorL;
            }
        }
        return closestL;
    }

    private static float getFixS(float originH,float originS) {
        float fixS = originS;
        if (originH >= 0 && originH <= 10) {
            if (originS < 0.40f) {
                fixS = originS;
            } else {
                fixS = 0.4f;
            }
        }
        if (originH > 10 && originH <= 49) {
            if (originS <= 0.40f) {
                fixS = originS;
            } else {
                fixS = 0.40f;
            }
        }
        if (originH > 49 && originH <= 80) {
            if (originS < 0.30f) {
                fixS = originS;
            } else {
                fixS = 0.30f;
            }
        }
        if (originH > 80 && originH <= 170) {
            if (originS < 0.25f) {
                fixS = originS;
            } else {
                fixS = 0.25f;
            }
        }
        if (originH > 170 && originH <= 200) {
            if (originS < 0.25f) {
                fixS = originS;
            } else {
                fixS = 0.25f;
            }
        }
        if (originH > 200 && originH <= 215) {
            if (originS < 0.35f) {
                fixS = originS;
            } else {
                fixS = 0.35f;
            }
        }
        if (originH > 215 && originH <= 235) {
            if (originS < 0.20f) {
                fixS = originS;
            } else {
                fixS = 0.20f;
            }
        }
        if (originH > 235 && originH <= 275) {
            if (originS < 0.20f) {
                fixS = originS;
            } else {
                fixS = 0.20f;
            }
        }
        if (originH > 275 && originH <= 319) {
            if (originS < 0.20f) {
                fixS = originS;
            } else {
                fixS = 0.20f;
            }
        }
        if (originH > 319 && originH <= 354) {
            if (originS < 0.30f) {
                fixS = originS;
            } else {
                fixS = 0.40f;
            }
        }
        if (originH > 354 && originH <= 360) {
            if (originS < 0.40f) {
                fixS = originS;
            } else {
                fixS = 0.4f;
            }
        }
        return fixS;
    }

    public static void getDomainColorForRecommend(Bitmap bitmap, int defColor, LocalImageUtil.Callback callback) {
        if (bitmap != null && !bitmap.isRecycled() && callback != null) {
            new Palette.Builder(bitmap).addFilter(new Palette.Filter() {
                @Override
                public boolean isAllowed(int rgb, @NonNull float[] hsl) {
                    return true;
//                    return !((hsl[0] > 60 && hsl[0] < 200) || (hsl[0] > 260 && hsl[0] < 327));
                }
            }).maximumColorCount(16).generate(palette -> {
                try {
                    int rgb = palette.getDominantColor(defColor);
                    callback.onMainColorGot(getColorFromMainColor(rgb));
                } catch (Exception e) {
                    callback.onMainColorGot(defColor);
                }
            });
        } else {
            if (callback != null) {
                callback.onMainColorGot(defColor);
            }
        }
    }

    public static void getDomainColorForRecommendWithCache(String url, Bitmap bitmap, int defColor, LocalImageUtil.Callback callback) {
        Integer color = PaletteCache.get(url);
        if (color != null && callback != null) {
            callback.onMainColorGot(getColorFromMainColorNew(color));
            return;
        }

        if (bitmap != null && !bitmap.isRecycled() && callback != null) {
            new Palette.Builder(bitmap).addFilter(new Palette.Filter() {
                @Override
                public boolean isAllowed(int rgb, @NonNull float[] hsl) {
                    return true;
//                    return !((hsl[0] > 60 && hsl[0] < 200) || (hsl[0] > 260 && hsl[0] < 327));
                }
            }).maximumColorCount(16).generate(palette -> {
                try {
                    int rgb = defColor;
                    if (palette != null) {
                        rgb = palette.getVibrantColor(defColor);
                        if (rgb == defColor || ColorUtil.shouldUseMainColor(rgb)) {
                            rgb = palette.getDominantColor(defColor);
                        }
                        PaletteCache.put(url, rgb);
                    }
                    callback.onMainColorGot(getColorFromMainColorNew(rgb));
                } catch (Exception e) {
                    callback.onMainColorGot(defColor);
                }
            });
        } else {
            if (callback != null) {
                callback.onMainColorGot(defColor);
            }
        }
    }

    public static void getImageColor(Bitmap bitmap, int defColor, LocalImageUtil.Callback callback) {
        if (bitmap != null && !bitmap.isRecycled() && callback != null) {
            new Palette.Builder(bitmap).addFilter((rgb, hsl) -> true).maximumColorCount(16).generate(palette -> {
                try {
                    int rgb = palette.getDominantColor(defColor);
                    callback.onMainColorGot(rgb);
                } catch (Exception e) {
                    callback.onMainColorGot(defColor);
                }
            });
        } else {
            if (callback != null) {
                callback.onMainColorGot(defColor);
            }
        }
    }

    private static int getColorFromMainColorNew(int rgb) {
        return SceneColorUtils.INSTANCE.adjustColor(rgb);
    }

    private static int getColorFromMainColor(int rgb) {
        float[] hsl = new float[3];
        int r = Color.red(rgb);
        int g = Color.green(rgb);
        int b = Color.blue(rgb);
        ColorUtils.RGBToHSL(r, g, b, hsl);
        hsl[1] = getFixS(hsl[0], hsl[1]);
        hsl[2] = findClosetLByH(hsl[0], hsl[1], hsl[2]);
//        hsl[2] = 0.3f;
        int color = ColorUtils.HSLToColor(hsl);
        if (color == Color.parseColor("#ffffff")) {
            return Color.parseColor("#8f8f8f");
        }
        if (color == Color.parseColor("#000000")) {
            return Color.parseColor("#363636");
        }
        return color;
    }
}
