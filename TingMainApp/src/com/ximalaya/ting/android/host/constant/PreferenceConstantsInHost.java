package com.ximalaya.ting.android.host.constant;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;

import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @Date 16/11/29
 */

public class PreferenceConstantsInHost
        implements PreferenceConstantsInOpenSdk {

    /**
     * SharedPreference file history_listener_data start
     */

    public static final String TINGMAIN_FILENAME_HISTORY_LISTENER_DATA = "history_listener_data";
    /**
     * SharedPreference file history_listener_data end
     */


    /**
     * SharedPreference file play_history_record start
     */

    public static final String TINGMAIN_FILENAME_PLAY_HISTORY_RECORD = "play_history_record";
    /**
     * SharedPreference file play_history_record end
     */

    /**
     * SharedPreference file recently_dynamic start
     */
    public static final String TINGMAIN_FILENAME_RECENTLY_DYNAMIC = "recently_dynamic";

    public static final String TINGMAIN_KEY_DYNAMIC_LIST = "dynamic_list";
    /**
     * SharedPreference file recently_dynamic end
     */

    /**
     * SharedPreference file SkinInfo start
     */

    public static final String TINGMAIN_FILENAME_SKININFO = "SkinInfo";

    public static final String TIMGMAIN_KEY_SKIN = "skin";
    /**
     * SharedPreference file SkinInfo end
     */

    /**
     * SharedPreference file xdcs_data start
     */
    public static final String TINGMAIN_FILENAME_XDCS_DATA = "xdcs_data";

    public static final String TINGMAIN_KEY_SPAND_ID = "spanId";

    public static final String TINGMAIN_KEY_TRACE_ID = "traceId";

    public static final String TINGMAIN_KEY_DOWNLOAD_START_TIME = "downloadstarttime";

    /**
     * SharedPreference file xdcs_data end
     */


    /**
     * SharedPreference file player_share_which_show start
     */
    public static final String TINGMAIN_FILENAME_PLAYER_SHARE_WHICH_SHOW = "player_share_which_show";

    //key 是变量 trackId


    /**
     * SharedPreference file player_share_which_show end
     */

    /**
     * SharedPreference file user_data start
     */

    public static final String TINGMAIN_FILENAME_USER_DATA = "user_data";

    /**
     * Mmkv file user_data start
     */
    public static final String TINGMAIN_FILENAME_USER_DATA_MMKV = "user_data_mmkv";

    /**
     * xuid data file
     */
    public static final String TINGMAIN_FILENAME_XUID_DATA_MMKV = "user_xuid_data_mmkv";

    /**
     * SharedPreference file user_data end
     */


    /**
     * SharedPreference file ting_data start
     */
    public static final String TINGMAIN_KEY_IS_TRACK_QUALITY_SETTING_ACTIVE = "is_track_quality_setting_active";
    public static final String TINGMAIN_KEY_TRACK_QUALITY_LEVEL = "track_quality_level";
    public static final String TINGMAIN_KEY_TRACK_PLAY_QUALITY_LEVEL = "track_play_quality_level";
    public static final String TINGMAIN_KEY_TRACK_PLAY_QUALITY_LEVEL_CELLULAR = "track_play_quality_level_cellular";
    public static final String TINGMAIN_KEY_TRACK_PLAY_QUALITY_LEVEL_WIFI = "track_play_quality_level_wifi";
    public static final String TINGMAIN_KEY_ALARM_RINGTONE_LOCATION = "alarm_ringtone_location";
    public static final String TINGMAIN_KEY_ALARM_RINGTONE_DOWNLOAD_URL = "alarm_ringtone_download_url";
    public static final String TINGMAIN_KEY_ALARM_RINGTONE_TITLE = "alarm_ringtone_title";
    public static final String TINGMAIN_KEY_ALARM_CONTINUE_RING_TIME = "alarm_continue_ring_time";
    public static final String TINGMAIN_KEY_ALARM_LAST_RING_MILLISECOND = "alarm_last_ring_millisecond";
    public static final String TINGMAIN_KEY_ALARM_HOUR = "alarm_hour";
    // FIXME: alarm_min 和 alarm_minute 有区别吗
    public static final String TINGMAIN_KEY_ALARM_MIN = "alarm_min";
    public static final String TINGMAIN_KEY_ALARM_MINUTE = "alarm_minute";
    public static final String TINGMAIN_KEY_ONCE_SET_REPEAT = "alarm_once_set_repeat";
    public static final String TINGMAIN_KEY_IS_REPEAT_MODIFED = "is_repeat_modified";
    public static final String TINGMAIN_KEY_IS_TIME_MODIFED = "is_time_modified";
    //FIXME repeat_week_days 和 repeat_days 有区别吗
    public static final String TINGMAIN_KEY_REPEAT_DAYS_2 = "repeat_week_days";
    public static final String TINGMAIN_KEY_REPEAT_DAYS = "repeat_days"; // 0/1/2/3/4/5/6
    // 0--星期一 6----星期天
    public static final String TINGMAIN_KEY_ALARM_TYPE = "type";
    public static final String TINGMAIN_KEY_GIF_IMGS = "gif_imgs";
    public static final String TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER = "download_album_soundlist_order";
    public static final String TINGMAIN_KEY_HOMEPAGE_RADIO_MODEL = "homePageRadioModel";

    public final static String TINGMAIN_KEY_IS_FIRST_LIKE = "isFirstLike";

    public static final String TINGMAIN_KEY_SEARCH_HISTORY_WORD = "search_history_word";
    public static final String TINGMAIN_KEY_ELDERLY_SEARCH_HISTORY_WORD = "search_elderly_history_word";
    //有追更的搜索历史词
    public static final String TINGMAIN_KEY_SEARCH_HISTORY_UPDATE_WORD = "search_history_update_word";
    public static final String TINGRECORRD_KEY_MATERIAL_SEARCH_HISTORY_WORD = "material_search_history_word";
    public static final String TINGMAIN_KEY_SEARCH_HOT_WORD = "search_hot_word";
    // 视频弹幕开关
    public static final String TINGMAIN_KEY_VIDEO_PAGE_SHOW_DANMAKU = "tingmain_key_video_page_show_danmaku";
    // 视频锁屏旋转开关
    public static final String TINGMAIN_KEY_VIDEO_SCREEN_ROTATION_LOCK = "tingmain_key_video_screen_rotation_lock";
    // 视频手动旋转为横屏时点击状态
    public static final String TINGMAIN_KEY_VIDEO_SCREEN_MANUAL_ROTATION = "tingmain_key_video_screen_manual_rotation";
    // 视频手动旋转为竖屏时点击状态
    public static final String TINGMAIN_KEY_VIDEO_SCREEN_MANUAL_PORTRAIT_ROTATION = "tingmain_key_video_screen_manual_portrait_rotation";
    public static final String TINGMAIN_KEY_SHARESETTING = "share_setting";

    public static final String TINGMAIN_KEY_SAVE_DYNAMIC_MODEL = "save_dynamic_model";

    public static final String TINGMAIN_KEY_P_IS_SURE_NO_3G_DIALOG_NOTIFY = "P_IS_SURE_NO_3G_DIALOG_NOTIFY";

    public static final String TINGMAIN_KEY_PROVINCE_CODE = "province_code";

    public static final String TINGMAIN_KEY_COUNTRY_CODE = "country_code";

    public static final String TINGMAIN_KEY_CITY_CODE = "city_code";

    public static final String TINGMAIN_KEY_COUNTY_CODE = "county_code"; // 县

    public static final String TINGMAIN_KEY_PROVINCE_NAME = "province_name";

    public static final String TINGMAIN_KEY_CITY_NAME = "local_city_name";

    public static final String TINGMAIN_KEY_PUSH_RECEIVE_STAT_RECORD = "push_receive_stat_record";

    public static final String TINGMAIN_KEY_CDN_WIFI_ALERT_RATE = "cdnWifiAlertRate";

    public static final String TINGMAIN_KEY_CDN_NOT_WIFI_ALERT_RATE = "cdnNotWifiAlertRate";

    public static final String TINGMAIN_KEY_CDN_NOT_WIFI_TIME_OUT = "cdnNotWifiConnectTimeout";

    public static final String TINGMAIN_KEY_CDN_TRAFFIC_CBATTERY = "trafficBatteryRecordInterval";

    public static final String TINGMAIN_KEY_ACTIVATED_VERSION_CODE = "activated_version_code";//保存当前激活的版本号
    public static final String TINGMAIN_KEY_GROWTH_ACTIVATED_VERSION_CODE = "growth_activated_version_code";
    public static final String TINGMAIN_KEY_GROWTH_NEED_UPDATE_IMEI = "growth_need_update_imei"; // 是否需要调用增长更新接口去更新imei

    public static final String TINGMAIN_KEY_IS_USE_SYS_PLAYER = "use_sys_player";

    public static final String TINGMAIN_KEY_DOWNLOAD_RECOMMEND_SORT_LIST = "download_recommend_sort_list";

    public static final String TINGMAIN_KEY_WITHOUT_WIFI = "is_download_enabled_in_3g";//是否在2g3g4g下播放下载

    public static final String TINGMAIN_KEY_ENABLE_DOWNLOAD_WITHOUT_WIFI = "is_download_enabled_without_wifi";//是否允许运营商网络下载

    public static final String TINGMAIN_KEY_ENABLE_SPLASH_SHAKE_STYLE = "enable_splash_shake_open"; // 是否允许打开开屏摇一摇样式

    public static final String TINGMAIN_KEY_IS_ON_FOR_WAKE = "isOnForWake";//是否开启特色闹铃

    public static final String TINGMAIN_KEY_PLAY_LAST_RADIO = "play_last_radio";

    public static final String TINGMAIN_KEY_HAS_TRANSLATE = "has_translated";//是否已经搬迁过

    public static final String TINGMAIN_KEY_HAS_HIS_TRANSLATE = "has_his_translated";//Radio，Track历史记录是否转成新版的HistoryModel

    public static final String TINGMAIN_KEY_RECOMMEND_DISCOVERY_UPDATE = "recommend_discovery_update";

    public static final String TINGMAIN_KEY_RECOMMEND_DISCOVERY_ENHANCE = "recommend_discovery_enhance";

    public static final String KEY_SQUARE_CLICKED_TIMES_DATE = "key_square_clicked_times_in_today_date";
    public static final String KEY_SQUARE_CLICKED_TIMES_TODAY = "key_square_clicked_times_in_today";

    public static final String TINGMAIN_KEY_COLDBOOT_USER_DATA = "coldboot_user_data";

    public static final String TINGMAIN_KEY_SUBMITED_ABOUT_ME_INFO = "submited_about_me_info";

    public static final String TINGMAIN_KEY_COLDBOOT_IGNORE_TIMES = "coldboot_ignore_times";

    public static final String TINGMAIN_KEY_COLDBOOT_IGNORE_WHEN = "coldboot_ignore_when";

    public static final String TINGMAIN_KEY_APP_SET_UPDATE_DATE_WHEN_LOADING = "app_set_update_date_when_loading";

    public static final String TINGMAIN_KEY_UPGRADE_IGNORE_VERSION = "upgrade_ignore_version";
    public static final String TINGMAIN_KEY_SHOW_UPDATE_DIALOG_TIME= "show_update_dialog_time";

    public static final String TINGMAIN_KEY_PUSH_START = "start";

    public static final String TINGMAIN_KEY_PUSH_END = "end";

    public static final String TINGMAIN_KEY_DOWNLOAD_RECOMMEND_SORT_LIST_ID = "DOWNLOAD_RECOMMEND_SORT_LIST_ID";

    public static final String TINGMAIN_KEY_SUBSCRIBE_REC_RANK_LIST_ID = "SUBSCRIBE_REC_RANK_LIST_ID";

    public static final String TINGMAIN_KEY_ISONLINE = "isOnline";

    public static final String TINGMAIN_KEY_DEVICE_TOKEN = "DEVICE_TOKEN";

    public final static String TINGMAIN_KEY_CLOUD_HISTORY_SYNCPOINT = "cloud_history_syncpoint";//云历史同步点

    public static final String TINGMAIN_KEY_LOGIN_FROM_XMLY = "login_from_xmly";

    public static final String TINGMAIN_KEY_IS_FIRST_CLICKED = "is_first_clicked";

    public static final String TINGMAIN_KEY_MEMBER_FIRST_CLICK = "member_first_click";

    public static final String TINGMAIN_KEY_SHARE_DATA_PRIVILEGEMODEL = "privilege_list_data";    // 优惠券数据在share里面的字段名

    public static final String TINGMAIN_KEY_SQUARE_TAB_LAST_READ_TIME = "square_tab_last_read_time";//广场tab消息

    public static final String TINGMAIN_KEY_LOCAL_CITY_CODE = "City_Code";//本地听

    public static final String TINGMAIN_KEY_LOCAL_CITY_NAME = "City_Name";//本地听

    public static final String TINGMAIN_KEY_ONE_KEY_SLEEP_MODE_JUMP_TAG = "one_key_listen_sleep_mode_jump_tag"; // 一键听跳转到睡眠模式或者睡眠模式跳转一键听

    public static final String TINGMAIN_KEY_ONE_KEY_CHANNEL_CHANGED_TAG = "one_key_listen_channel_changed_tag"; // 一键听切换频道标记

    public static final String TINGMAIN_KEY_ALBUM_CATEGORY_CACHE = "KEY_CATEGORY_CACHE";

    public static final String TINGMAIN_KEY_CATEGORY_CACHE = "live_category";

    public static final String TINGMAIN_KEY_SHARE_CHECK = "share_live_name";

    public static final String TINGMAIN_KEY_WEIBO_CHECK = "share_live_weibo";

    public static final String TINGMAIN_KEY_FIRST_RUN = "first_run";

    public static final String TINGMAIN_LIVE_CHAT_ROOM_SHOW_GUIDE = "live_room_show_guide";//是否显示直播页面的滑动指示蒙层

    public static final String TINGMAIN_KEY_TANK_DEFAULT_CDN_DOMAIN = "TANK_DEFAULT_CDN_DOMAIN";

    public static final String TINGMAIN_KEY_XM_PLAY_RESOURCE = "xm_play_resource";

    public static final String TINGMAIN_KEY_DNSCONFIG = "dnsconfig";

    public static final String TINGMAIN_KEY_HISTORY_LISTENER = "history_listener";

    public static final String TINGMAIN_KEY_LIVE_HISTORY_LISTENER = "live_history_listener";

    public static final String TINGMAIN_KEY_PLAY_LIST_RECORD = "play_list_record";

    public static final String TINGMAIN_KEY_SUBSCRIBE_REC_RANK_LIST_KEY = "SUBSCRIBE_REC_RANK_LIST_Key";
    public static final String TINGMAIN_KEY_LIVE_HAS_SHOW_GUIDE_PAGE = "live_has_show_guide_page";    //送礼提示页是否展示过
    public static final String TINGMAIN_KEY_LIVE_HAS_DOWNLOAD_GIFT_PIC = "live_has_download_gift_pic"; //直播礼物图片下载
    public static final String TINGMAIN_KEY_NEW_APP_INSTALL_DATE = "new_app_install_date";     // 新用户安装的日期 跟上面的作用基本一样 但是历史原因不做在一起

    public static final String TINGMAIN_KEY_TODAY_FIRST_OPEN_APP_DATE = "TINGMAIN_KEY_TODAY_FIRST_OPEN_APP_DATE";

    public static final String TINGMAIN_KEY_IS_FIRST_ACTIVE = "is_first_active";
    public static final String TINGMAIN_KEY_ACTIVE_IMEI_INVALID = "active_imei_invalid";
    public static final String TINGMAIN_KEY_QUERY_ITING_REQUEST_TIME = "query_iting_request_time"; // 上次请求queryIting接口的时间
    public static final String TINGMAIN_KEY_QUERY_ITING_V3_REQUEST_TIME = "query_iting_v3_request_time"; // 上次请求queryIting v3接口的时间

    public static final String TINGMAIN_KEY_P_XIMALAYA_DEVICE_APP_CONFIG = "p_ximalaya_device_app_config";
    public static final String TINGMAIN_KEY_RESERVED_ACTIVITY_IDS = "reserved_activity_ids";    //已经预约的直播活动id
    public static final String TINGMAIN_KEY_NEW_FEATURE_RINGTONE_USED = "new_feature_ringtone";
    public static final String TINGMAIN_KEY_CALLING_RINGTONE_TRACKID = "calling_ringtone_trackid";
    public static final String TINGMAIN_KEY_GIUID = "giuid";
    public static final String TINGMAIN_KEY_RANK_TITLE = "ranktitle";
    public static final String TINGMAIN_KEY_SHOW_POPWINDOW_TIPS_NUM = "SHOW_POPWINDOW_TIPS_NUM";
    public static final String TINGMAIN_KEY_SHOW_POPWINDOW_TIPS = "SHOW_POPWINDOW_TIPS";
    public static final String TINGMAIN_KEY_CHANGE_CITY = "ChangeCity";
    public static final String TINGMAIN_KEY_DNSLIST_DOMAINS = "dnslist_domains";
    public static final String TINGMAIN_KEY_TRAFFIC_BATTERY_RECORD_INTERVAL = "TRAFFIC_BATTERY_RECORD_INTERVAL";
    public static final String TINGMAIN_KEY_CATEGORY_SPECIAL = "Category_Special";
    public static final String TINGMAIN_KEY_CATEGORY_TITLE = "CategoryTitle";
    public static final String TINGMAIN_KEY_CITY_LIST = "CityList";
    public static final String TINGMAIN_KEY_CITY_LIST_VERSION = "CityListVersion";
    public static final String TINGMAIN_KEY_PLAYINGINFO = "PlayingInfo";
    public static final String TINGMAIN_KEY_IS_CREATED_SHORTCUT = "isCreatedShortcut";
    public static final String TINGMAIN_KEY_INSERT_REFRESH_TIMES = "INSERT_REFRESH_TIMES";
    public static final String TINGMAIN_KEY_RECOMMEND_FLOW_CACHE = "RecommendFlowCache";
    public static final String TINGMAIN_KEY_RECOMMEND_FLOW_CACHE_POSITION = "RecommendFlowCache_Position";
    public static final String TINGMAIN_KEY_RECOMMEND_FLOW_CACHE_INSERT_POSITION = "RecommendFlowCache_Insert_Position";
    public static final String TINGMAIN_KEY_RECORD_UPLOAD_TCP = "isTcpUpload";
    public static final String TINGMAIN_KEY_IS_ALLPEOPLEREAD_SHOW_PLAYPAGE = "isAllPeopleReadShowInPlayPage";
    public static final String TINGMAIN_KEY_IS_ALLPEOPLEREAD_SHOW_READPAGE = "isAllPeopleReadShowInRecordPage";
    public static final String TINGMAIN_KEY_GUIDE_RECORD = "guideRecordPage";

    public static final String TINGMAIN_KEYBOARD_HEIGHT = "default_emotion_height";//emotion的默认高度

    public static final String TINGMAIN_KEYBOARD_HEIGHT_PROTRAIT = "tingmain_keyboard_height_protrait";//竖屏下emotion的默认高度
    public static final String TINGMAIN_KEYBOARD_HEIGHT_HORIZONTAL = "tingmain_keyboard_height_horizontal";//横屏下emotion的默认高度

    //    /*******隐私设置 Start**********/
//    public static final String TINGMAIN_KEY_COMMENTSETTING_INDEX = "CommentSettingIndex";
//    public static final String TINGMAIN_KEY_PRIVATEMSGTSETTING_INDEX = "PrivateMsgtSettingIndex";
//    public static final String TINGMAIN_KEY_ATMESETTING_INDEX = "AtMeSettingIndex";
//    /*******隐私设置 End**********/
    public static final String TINGMAIN_KEY_GROUP_INFO = "group_info";

    public static final String TINGMAIN_KEY_NEED_COMPARE_LOCAL = "needCompareLocal";//是否需要将网上的插件apk和本地的插件apk的版本进行比较

    public static final String TINGMAIN_KEY_GUESSYOULIKE_RECOMMEND_ALBUM = "guessyoulike_recommend_album";//专辑购买页，评价页猜你喜欢推荐专辑

    public static final String TINGMAIN_KEY_RECOMMREND_IS_GUIDED = "recommrend_is_guided"; // 首页使用以及引导使用过

    /*******用户反馈 Start**********/
    public static final String TINGMAIN_KEY_FEEDBACK_CATEGORY = "feedback_category";
    /*******用户反馈 End**********/

    public static final String TINGMAIN_KEY_INTERNATIONAL_CODE = "internationalCode"; // 首页使用以及引导使用过

    public final static String TINGMAIN_KEY_IS_NIGHT_MODE = "is_night_mode"; //夜间模式

    public final static String TINGMAIN_KEY_BLOCK_CANARY_THRESHOLD = "block_canary_threshold";

    public final static String TINGMAIN_KEY_LEAK_CANARY_ENABLE = "TINGMAIN_KEY_LEAK_CANARY_ENABLE";
    public final static String TINGMAIN_KEY_BLOCK_CANARY_ENABLE = "TINGMAIN_KEY_BLOCK_CANARY_ENABLE";
    public final static String TINGMAIN_KEY_ENABLE_DEBUG_WEB_VIEW = "enable_debug_web_view";
    public final static String TINGMAIN_KEY_ENABLE_DEBUG_WOTING_POSITION = "tingmain_key_enable_debug_woting_position";

    public final static String TINGMAIN_KEY_FORCE_UPDATE_BUILD_IN_BUNDLE = "force_update_build_in_bundle";

    public static final String TINGMAIN_KEY_HOMEPAGE_FIRST = "first"; // 首页默认选中的顶tab
    public static final String TINGMAIN_KEY_BOTTOM_FIRST = "bottom_first"; //启动时默认选中的底部tab
    public static final String TINGMAIN_KEY_CHECK_POOL = "check_pool";

    public static final String TINGMAIN_KEY_RECORD_PCM_DATA_SWITCH = "record_pcm_data_switch";
    public static final String TINGMAIN_KEY_SHOW_RAISE_DIALOG_WHEN_SOUND_SWITCH = "show_raise_when_sound_switch";
    public static final String TINGMAIN_KEY_SHOW_RAISE_DIALOG_TRY = "show_raise_dialog_try";
    public static final String TINGMAIN_KEY_RAISE_DIALOG_COMMENTED = "raise_dialog_encouraged";

    /**
     * SharedPreference file ting_data end
     */

    /****我听页面 *****/
    public static final String TINGMAIN_KEY_WOTING_CHECK_TIME = "tingmain_key_woting_check_time";

    // webView 的useagent
    public static final String TINGMAIN_KEY_WEBVIEW_USEAGENT = "TINGMAIN_KEY_WEBVIEW_USEAGENT";
    // 保存webView ua的时候系统的os version,因为系统升级的时候会导致os version发生变化
    public static final String TINGMAIN_KEY_WEBVIEW_USEAGENT_SAVE_OS_VERSION = "TINGMAIN_KEY_WEBVIEW_USEAGENT_SAVE_OS_VERSION";

    // mac 地址
    public static final String TINGMAIN_KEY_MAC_ADDRESS = "TINGMAIN_KEY_MAC_ADDRESS";

    //配置数据
//    public static final String TINGMAIN_KEY_GROUP_SETTINGS = "TINGMAIN_KEY_GROUP_SETTINGS";
//    public static final String TINGMAIN_KEY_BATCH_GROUP_SETTINGS = "TINGMAIN_KEY_BATCH_GROUP_SETTINGS";

    //断点续听
    public static final String TINGMAIN_KEY_CONTINUE_PLAY = "TINGMAIN_KEY_CONTINUE_PLAY";

    public static final String TINGMAIN_KEY_IS_HUAWEI_WATCH_ENABLE = "isHuaweiWatchEnable";

    //保存配置中心abtestId
//    public static final String TINGMAIN_KEY_SAVE_ABTEST_BUCKETIDS = "save_abtest_bucketids";

    public static final String TINGMAIN_KEY_IS_FROM_MENU_TAB = "is_from_menu_tab";

    public static final String TINGMAIN_KEY_CURRENT_VERSION = "TINGMAIN_KEY_CURRENT_VERSION";


    public static final String TINGMAIN_KEY_BINDPHONE_JSON = "TINGMAIN_KEY_BINDPHONE_JSON";
    public static final String TINGMAIN_KEY_BINDPHONE_JSON_NEW = "TINGMAIN_KEY_BINDPHONE_JSON_NEW";

    public static final String TINGMAIN_KEY_LIVE_DISPLAY = "live_display";// 直播入口开关
    public static final String TINGMAIN_KEY_OPEN_LIVE_CALL = "open_live_call";// 连麦入口开关

    public static final String TINGMAIN_KEY_OPEN_OTHER_APP = "open_other_app"; // web页打开其他app的时候出现的弹框

    public static final String KEY_AD_DOWNLOADED_IMG_DATA = "key_ad_downloaded_img_data";

    //
    public static final String TINGMAIN_KEY_TIPS_UPLOAD_IMG = "key_tips_upload_img";

    public static final String TINGMAIN_KEY_TIPS_DYNAMIC_UPLOAD_VIDEO = "key_tips_dynamic_upload_video";

    public static final String TINGMAIN_KEY_IS_SEARCH_CHECK_ALBUM_TAB = "key_is_search_check_album_tab";
    //
    public static final String TINGMAIN_KEY_WELCOME_AD_ADNROIDID = "key_welcome_ad_adnroidid";

    // last tab index
    public static final String TINGHOST_KEY_LAST_TAB_INDEX = "last_tab_index";
    // 用户最后一次离开时的home tab index
    public static final String TINGMAIN_KEY_LAST_HOME_TAB_INDEX = "last_home_tab_index";
    public static final String KEY_LISTEN_COUNT_AND_DATE = "listen_count_and_date";
    public static final String KEY_UUID_FOR_TEST = "uuid_for_test";
    public static final String HOST_CMCC_LISTENER_CARD_INFO = "cmcc_listener_card_info";
    // 已进行过个性化设置
    public static final String KEY_HAS_CUSTOMIZED = "key_has_customized";
    // 是否还要在应用启动时显示兴趣卡片填写页
    public static final String KEY_NEED_SHOW_CUSTOMIZED_PAGE_ON_APP_START = "key_need_show_customized_page_on_app_start";
    // 已进行过标签设置
    public static final String KEY_HAS_CHOSEN_CATEGORY = "key_has_chosen_category";
    // 是否还要在应用启动时显示标签卡片填写页
    public static final String KEY_NEED_SHOW_CHOOSE_LIKE_PAGE_ON_APP_START = "key_need_show_choose_like_page_on_app_start";

    public static final String KEY_SP_FILE_COMMENT_DRAFT = "comment_draft";
    public static final String KEY_COMMENT_DRAFT_TRACK_PREFIX = "comment_draft_track_";
    public static final String KEY_COMMENT_DRAFT_TRACK_BULLET_PREFIX = "comment_draft_track_bullet_";
    // 专辑的排序方式 废弃使用，改用AlbumOrderManager替代
    public static final String KEY_IS_ASC = "key_is_asc";
    // 播放页是否要去请求接口更新下任务状态
    public static final String KEY_MAIN_NEED_UPDATE_LISTEN_TASK = "key_main_need_update_listen_task";
    //软键盘高度，听单使用
    public static final String KEY_TINGLIST_KEYBOARD_HEIGHT = "key_tinglist_keyboard_height";
    // 听单是否首次收藏
    public static final String KEY_TINGLIST_FIRST_COLLECT = "key_tinglist_first_collect";
    public static final String KEY_IS_NEW_DEVICE_SET = "key_is_new_device_set";
    public static final String KEY_IS_NEW_DEVICE_NEW_LOGIN = "key_is_new_device_new_login";

    public static String KEY_IS_CHECK_WEB_URL_HAS_TEST = "is_check_web_url_has_test"; // 是否需要处理url里面有test

    public static final String TINGMAIN_FILENAME_OFFLINE_PLAY_STATISTIC = "off_line_play_statistic";
    public static final String TINGMAIN_KEY_OFFLINE_PLAY_STATISTIC_UPLOAD = "off_line_play_statistic_upload";
    public static final String TINGMAIN_KEY_OFFLINE_POST_TIME = "off_line_post_time";//上传离线播放数据的统计时间

    public static String KEY_SERVICE_DATE = "user_one_day_service_date";  // 服务器时间
    public static String KEY_SERVICE_DATE_FOR_REQUEST = "key_service_date_for_request";  // 服务器时间

    public static final String TINGMAIN_KEY_CHECK_VALID_WEB_URL = "TINGMAIN_KEY_CHECK_VALID_WEB_URL";
    public static final String TINGMAIN_KEY_IS_ANCHORSPACE_INIT_RECORD_MODULE = "is_anchorspace_init_record_module";

    public static final String TINGMAIN_KEY_USE_HARDWARE_DECODE = "use_hardware_decode";

    public static final String TINGMAIN_KEY_VIDEO_DISK_CACHE_ENABLE = "video_disk_cache_enable";

    public static final String KEY_CATEGORY_CONTENT_GENDER = "category_content_gender";

    public static final String TINGMAIN_KEY_RECOMMEND_ALBUMS = "key_recommend_albums";//搜索AB test结果
    public static final String TINGMAIN_KEY_RECOMMEND_ALBUMS_TIME = "key_recommend_albums_time";//搜索AB test结果

    public static final String TINGMAIN_KEY_REQUEST_ENVIRONMENT = "key_request_environment";    // 请求环境

    public static final String TINGMAIN_KEY_HAS_STATE_HOST_PATCH = "has_state_host_patch";

    public static final String KEY_HAS_SHOW_HINT_VIDEO = "has_show_hint_video"; // 是否显示过提示视频

    //表情相关的key
    public static final String HOST_KEY_EMOTION_COLLECTED = "collected_emotion";
    public static final String HOST_KEY_EMOTION_HOT_TAG = "emotion_hot_tag";

    // H5复制的串码打开APP首次上传
    public static final String KEY_H5_STRING_UPLOADED = "key_h5_string_uploaded";

    // 展示过新人礼包弹窗
    public static final String KEY_HAS_SHOWN_GIFT = "key_has_shown_gift";

    public static final String HOME_PAGE_TAB_MODIFY_VERSION = "home_page_tab_modify_version"; // 用户修改首页tab时，对应的后台配置的版本
    public static final String HAS_REQUESTED_HOME_PAGE_TAB_DATA = "has_requested_home_page_tab_data"; // 有请求新的首页tab数据
    public static final String LAST_MAIN_ACTIVITY_PAUSE_TIME = "last_main_activity_pause_time"; // 上次推到后台时间

    // 分享时间
    public static final String KEY_TODAY_SHARE_TIME = "key_today_share_time";

    // 展示过登陆引导
    public static final String KEY_HAS_SHOWN_LOGIN_GUIDE = "key_has_shown_login_guide";


    //VIP放在底部导航栏，我听整合abtest
    public static final String KEY_VIP_ATTACH_BUTTON_TAB_ABTEST_PLAN = "key_vip_attach_button_tab_abtest_plan";

    // 1:通知栏是登录签到    2：通知栏+-15s
    public static final String KEY_NOTIFICATION_TYPE = "key_notification_type";

    public static final String KEY_HAS_SHOWN_STRONG_CONTINUE_PLAY_TIPS = "key_has_shown_strong_continue_play_tips";

    public static final String KEY_PLAY_PAGE_SHOW_DOC_ON_COVER = "key_play_page_show_doc_on_cover";

    public static final String KEY_SHOW_POP_AD_AFTER_SOUND_PATCH = "key_show_pop_ad_after_sound_patch";

    public static final String KEY_DAILY_NEWS_IN_WHITE_LIST = "daily_news_in_white_list";

    // 首页我听提示是否出现过,我听在我的页面的tip提示
    public static final String KEY_IS_WOTING_IN_MINE_TIP_SHOW = "key_is_woting_in_mine_tip_show";
    public static final String KEY_OPEN_CLEAR_DOWNLOAD = "key_open_clear_download";

    //从b用户升级到b+用户的的提示
    public static final String KEY_IS_WOTING_TIP_B_UP_BPLUS = "key_is_woting_tip_b_up_bplus";

    //B+的正常提示
    public static final String KEY_IS_WOTING_TIP_B_PLUS= "key_is_woting_tip_b_plus";

    // 上次展示音量过大提示时间
    public static final String TIME_LAST_SHOW_VOLUME_HINT = "time_last_show_volume_hint";

    public static final String KEY_UPDATE_DIALOG_SHOWN_VERSION = "key_update_dialog_shown_version";

    // 升级弹窗点击过稍后升级
    public static final String KEY_IS_UPDATE_DIALOG_LATER = "key_is_update_dialog_later";

    // 完播推荐开关
    public static final String TINGMAIN_KEY_PLAY_COMPLETE_RECOMMEND = "play_complete_recommend";

    // 推荐播放弹窗是否出现过
    public static final String KEY_IS_PLAY_COMMEND_HINT_SHOWN = "key_is_play_commend_hint_shown";

    // 是否接收过小爱的control
    public static final String KEY_HAS_RECEIVER_XIAOAI_CONTROL = "key_has_receiver_xiaoai_control";

    public static final String KEY_OPEN_LOGGER = "key_open_logger";
    public static final String KEY_OPEN_FLEXBOX = "key_open_flexbox";
    public static final String KEY_ALLOW_USE_X5 = "key_allow_use_x5";
    public static final String KEY_ALLOW_COMPRESS = "key_allow_compress";
//    public static final String KEY_OPEN_LOGGER_TS = "key_open_logger_ts";
    public static final String KEY_OPEN_VPN = "key_open_vpn";
    public static final String KEY_OPEN_NET_LOGGER = "key_open_net_logger"; // 打开网络日志监控
    public static final String KEY_OPEN_PLAYER_LOGGER = "key_open_player_logger";   // 打开播放器内的logger
    public static final String KEY_OPEN_METHOD_TRACE = "key_open_method_trace";   // 打开播放器内的logger
    public static final String KEY_OPEN_APP_HOTFIX = "key_open_app_hot_fix_20230605";   // 打开热修复
    public static final String KEY_OPEN_HTTP2_COOKIE_OPTIMIZE = "key_open_http2_cookie_optimize";
    public static final String KEY_OPEN_COOKIE_CONTROLLER = "key_open_cookie_controller";

    // 首次打开的时间
    public static final String KEY_FIRST_OPEN_TIME = "key_first_open_time";

    // 首次打开的时间是否发送成功
    public static final String KEY_FIRST_OPEN_TIME_SEND_SUCCESS = "key_first_open_time_send_success";

    public static final String SP_FILE_SKIP_HEAD_TAIL = "skip_head_tail";
    // 是否显示过跳过片头片尾的tips
    public static final String KEY_HAS_SHOW_SKIP_HEAD_TAIL_TIPS = "key_has_show_skip_head_tail_tips";
    // 跟跳过片头片尾有关的操作次数
    public static final String KEY_SKIP_HEAD_TAIL_ACTION_COUNT = "KEY_SKIP_HEAD_TAIL_ACTION_COUNT";
    // 通讯录整体Hash
    public static final String KEY_CONTACT_HASH = "key_contact_hash";
    // 上次自动上传通讯录时间
    public static final String KEY_LAST_UPLOAD_CONTACT_TIME = "key_last_upload_contact_time";

    // 是否显示过提示打开快捷方式权限的弹窗
    public static final String KEY_HAS_SHOW_SHORT_CUT_PERMISSION_DIALOG = "KEY_HAS_SHOW_SHORT_CUT_PERMISSION_DIALOG";

    // 本地用户画像
    public static final String KEY_CUSTOMIZED_INTEREST_CARD_MODEL = "key_customized_interest_card_model";
    public static final String KEY_CUSTOMIZED_INTEREST_CARD_MODEL_NEW = "key_customized_interest_card_model_new";

    // 通过微信分享邀请的邀请者uid
    public static final String KEY_SHARE_WEIXIN_UID = "KEY_SHARE_WEIXIN_UID";

    // 好友推荐页隐私设置浮条是否显示
    public static final String KEY_PRIVACY_ENTRANCE_SHOW = "KEY_PRIVACY_ENTRANCE_SHOW";

    public static final String KEY_LOCK_SCREEN_OPEN = "KEY_LOCK_SCREEN_OPEN"; // 是否开启喜马锁屏页面
    public static final String KEY_LOCK_SCREEN_CHECKBOX_CHECKED = "KEY_LOCK_SCREEN_CHECKBOX_CHECKED"; //锁屏显示CheckBox状态

    public static final String KEY_WILL_HANDLE_GUESS_LIKE = "will_handle_guess_like"; // 是否要处理猜你喜欢更新
    public static final String KEY_LAST_SEARCH_WORD = "key_last_search_word"; // 最后一个搜索词
    public static final String KEY_LAST_SEARCH_WORD_WHEN_OPEN_APP = "key_last_search_word_when_open_app"; // 本次打开app后最新的搜索词

    //私密圈首页微信通知view是否显示
    public static final String KEY_PAID_ZONE_WX_NOTICE_HIDE_VIEW = "KEY_PAID_ZONE_WX_NOTICE_HIDE_VIEW";

    //中断弹出view一天最多一次的view
    public static final String KEY_SHOW_INTERRUPT_VIEW_ONCE_TIME_BY_DAY = "KEY_SHOW_INTERRUPT_VIEW_ONCE_TIME_BY_DAY";

    //忽略的提问不再显示
    public static final String KEY_QUESTION_IGNORE = "KEY_QUESTION_IGNORE";

    // Vip弹幕颜色
    public static final String KEY_VIP_BULLET_COLOR_NEW = "KEY_VIP_BULLET_COLOR_NEW";
    public static final String KEY_VIP_BULLET_COLOR_INDEX = "KEY_VIP_BULLET_COLOR_INDEX";

    // 0播放新人引导出现过
    public static final String KEY_NEW_USER_GUIDE_HAS_SHOWN = "key_new_user_guide_has_shown";
    // 新用户是否有播放历史
    public static final String KEY_NEW_USER_HAS_PLAY_HISTORY = "key_new_user_has_play_history";
    public static final String KEY_NEW_USER_NO_PLAY_GUIDE_HAS_SHOWN = "key_new_user_no_play_guide_has_shown";
    // 预装渠道新用户退出引导弹窗
    public static final String KEY_PREINSTALL_NEW_USER_NO_PLAY_GUIDE_HAS_SHOWN = "key_preinstall_new_user_no_play_guide_has_shown";

    //是否开启自动积分解锁
    public static final String KEY_FREE_LISTEN_AUTO_UNLOCK_BY_CREDIT = "key_free_listen_auto_unlock_by_credit";

    // 是否点赞过该声音签名
    public static final String KEY_IS_VOICE_SIG_LIKED = "key_is_voice_sig_liked";

    public static final String KEY_DAILY_SIGN_GUIDE_HAS_SHOWN = "key_daily_sign_guide_has_shown";
    public static final String KEY_DAILY_SIGN_GUIDE_HAS_SHOWN_NEW = "key_daily_sign_guide_has_shown_new";

    public static final String KEY_DAILY_SIGN_SIGN_TIME = "key_daily_sign_sign_time";
    public static final String KEY_DAILY_SIGN_SIGN_FROM_NET= "key_daily_sign_sign_from_net";

    // 记录用户关闭过的应急事故公共
    public static final String KEY_EMERGENCY_ANNOUNCEMENT_HAS_CLOSED = "key_emergency_announcement_has_closed";

    //搜索页语音搜索提示
    public static final String KEY_SEARCH_RECOGINZER_TIP = "key_search_recognizer_tip";
    //搜索页语音动画
    public static final String KEY_SEARCH_VOICE_LOTTIE = "key_search_voice_lottie";
    //搜索历史请求提示更新接口
    public static final String KEY_SEARCH_WORD_UPDATED = "key_search_word_updated";

    public static final String KEY_BIG_SCREEN_REQUEST = "big_screen_request_time";

    //账号页头像上面的气泡是否显示过
    public static final String KEY_LOGIN_BUBBLE_SHOWN = "key_login_bubble_shown";
    //账号页头像上面的气泡显示时的版本号
    public static final String KEY_LOGIN_BUBBLE_SHOWN_VERSION = "key_login_bubble_shown_version";

    // 上一次oaid的值
    public static final String KEY_AD_OAID_VALUE = KEY_AD_OAID_VALUE_FOR_OPEN;
    // 获取oaid的时间
    public static final String KEY_AD_OAID_TIME = "key_ad_oaid_time";
    // 巨幕广告展示成功的日期
    public static final String KEY_BIG_SCREEN_SHOW_SUCCESS_DATE = "big_screen_show_success_date";
    // 巨幕广告展示成功的时间戳
    public static final String KEY_BIG_SCREEN_SHOW_SUCCESS_TIME = "big_screen_show_success_time";
    // 巨幕广告一天内展示的次数
    public static final String KEY_BIG_SCREEN_SHOW_COUNT_ONE_DAY = "big_screen_show_count_one_day";

    // 主线程锁占用检查
    public static final String KEY_OPEN_LOCK_MONITOR = "key_open_lock_monitor";

    // 图片内存占用检查
    public static final String KEY_OPEN_BITMAP_MONITOR = "key_open_lock_monitor";

    // 布局代码相关信息
    public static final String KEY_OPEN_VIEW_CODE_LOCATE = "key_open_view_code_locate";
    public static final String KEY_SETTING_H5_SHARE_LOG_STATUS = "key_setting_h5_share_log_status";

    // 暗黑模式
    public static final String KEY_DARK_MODE = "key_dark_mode";
    // 暗黑模式是否跟随系统变化
    public static final String KEY_FOLLOW_SYSTEM_DARK = "key_follow_system_dark";

    public static final String KEY_SHOOT_MUSIC_GUIDE = "key_shoot_music_guide";

    //今天播放次数
    public static final String KEY_TODYA_PLAY_NUM = "key_today_play_num";

    public static final String KEY_NEED_AGREE_PRIVACY = "key_need_agree_privacy";

    //最近登录的账号
    public static final String KEY_CURRENT_LOGIN_ACCOUNTS = "key_current_login_accounts"; //要删除
    public static final String KEY_CURRENT_LOGIN_ACCOUNTS_NEW = "key_current_login_accounts_new";

    public static final String KEY_HAS_SHOW_CREATE_POST_RED_DOT = "key_has_show_create_post_red_dot";

    public static final String KEY_LAST_PLAYED_SLEEP_SOUND = "key_last_played_sleep_sound";

    public static final String KEY_NEED_SET_EXTRA_TO_STRART = "key_need_set_extra_to_strart";

    public static final String KEY_IS_FROM_PRIVACY_HINT_ACTIVITY = "key_is_from_privacy_hint_activity";

    public static final String KEY_NEED_TO_THIRD_APP_AUTHORIZE = "key_need_to_third_app_authorize";

    public static final String KEY_IS_OPEN_MOCK = "key_is_open_mock";

    public static final String KEY_HAS_SHOW_PPT_PLAY_TIP_2 = "has_show_ppt_play_tip_2";

    //是否是3个月的新用户
    public static final String KEY_IS_NEWER_FROM_3_MONTH = "key_is_newer_from_3_month";

    public static final String KEY_CHILD_PROTECT_SELECT_AGE_RANGE = "key_child_protect_select_age_range";

    public static final String KEY_CHILD_PROTECT_IS_BIND = "key_child_protect_is_bind";

    public static final String KEY_DLNA_TIMER_SET_NOTIFY = "dlna_timer_set_notify";

    //是否满足领取礼包条件
    public static final String KEY_NEW_USER_GIFT_TAG = "key_new_user_gift_tag";

    // deeplink 启动App后，返回到首页是否要出新人礼包弹窗
    public static final String KEY_DEEP_LINK_NEED_SHOW_NEW_GIFT = "key_deep_link_need_show_gift";

    // 上次点击解锁的时间
    public static final String KEY_LAST_UNLOCK_TIME = "key_last_unlock_time";

    // 上传免流状态的时间
    public static final String KEY_LAST_RECORD_FREE_FLOW_TIME = "key_last_record_free_flow_time";

    //驾驶模式电台首次引导动画
    public static final String KEY_DRIVE_MODE_RADIO_SHOW_GUIDE = "key_drive_mode_radio_show_guide";

    //新版驾驶模式电台首次引导动画
    public static final String KEY_DRIVE_MODE_SHOW_GUIDE_V3 = "key_drive_mode_show_guide_v3";

    //记录用户是否手动退出驾驶模式，则下次不用再自动进入驾驶模式
    public static final String KEY_DRIVE_MODE_USER_EXIT_DRIVE_MODE = "key_drive_mode_user_exit_drive_mode";

    //驾驶模式上次收听的电台
    public static final String KEY_DRIVE_MODE_LAST_CHANNEL = "key_drive_mode_last_channel";

    //驾驶模式随心听提示
    public static final String KEY_DRIVE_MODE_FOLLOW_HEART_TIP = "key_drive_mode_follow_heart_tip";

    // 音量增强引导
    public static final String KEY_SHOW_VOLUME_ENHANCE_GUIDE = "key_show_volume_enhance_guide";

    public static final String KEY_OPEN_SCREEN_SHOT_ENABLE = "key_open_screen_shot_enable";   // 打开屏幕截屏开关

    public static final String KEY_HAS_STRONG_SHOW_LOGIN_GUIDE = "key_has_strong_show_login_guide"; // 是否显示过强登录引导页
    public static final String KEY_CLOSE_BUG_COMMIT_BY_SCREEN_SHOT = "key_close_bug_commit_by_screen_shot"; //关闭截屏自动提交缺陷功能
    public static final String KEY_TEST_SPLASH_AD = "key_test_splash_ad"; // 测试开屏sdk广告

    // 是否可以预加载sdk
    public static final String KEY_PREREQUEST_SDK_ENABLE = "KEY_PREREQUEST_SDK_ENABLE";
    public static final String KEY_LAST_PLAY_UNLOCK_PAID_SOUND_PATCH = "key_last_play_unlock_paid_sound_patch"; // 解锁声音

    //手机的mediaplayer是否支持amr-wb的播放，true代表支持，false代表不支持
    public static final String KEY_AMRWB_CAN_SUPPORT = "amrwb_support";
    //新用户冷启动已选一、二级标签
    public static final String KEY_CHOOSE_LIKE_SELECTED_ALL_CODES = "key_choose_like_selected_all_codes";

    //是否显示过老年模式弹窗
    public static String KEY_HAS_SHOW_ELDERLY_OPEN_DIALOG = "key_has_show_elderly_open_dialog";

    //当天第几次启动app 用于青少年模式弹窗需求（日期）
    public static String KEY_START_APP_TIMES_TODAY_DATE = "key_start_app_times_today_date";

    //当天第几次启动app 用于青少年模式弹窗需求（启动次数）
    public static String KEY_START_APP_TIMES_TODAY_COUNT = "key_start_app_times_today_count";

    // 免流是否使用完成
    public static final String KEY_FREE_FLOW_USE_OVER_MOCK = "key_free_flow_use_over_mock";
    //是否已经请求过接口获取到用户的昵称信息
    public static final String KEY_HAS_REQUEST_NICK_NAME_INFO = "key_has_request_nick_name_info";
    //是否需要显示切换昵称弹窗
    public static final String KEY_NEED_SHOW_NICK_NAME_SETTING_DIALOG = "key_need_show_nick_name_setting_dialog";
    //上次显示切换昵称弹窗的日期
    public static final String KEY_NICK_NAME_DIALOG_LAST_SHOW_DATE = "key_nick_name_dialog_last_show_date";
    public static final String KEY_NICK_NAME_DIALOG_LAST_SHOW_DATE_NEW = "key_nick_name_dialog_last_show_date_new_";

    //是否首次安装app并请求框底词接口
    public static final String KEY_FIRST_INSTALL_AND_REQUEST_GUIDE_WORD = "key_first_install_and_request_guide_word";
    //最近的搜索记录（最多5条）
    public static final String KEY_RECENT_SEARCH_WORDS_RECORD = "key_recent_search_words_record";

    // 首页顶tab接口的tabSign
    public static String KEY_HOME_PAGE_TAB_API_TAB_SIGN = "key_home_page_tab_api_tab_sign";

    // 首页顶tab接口的templateDisplay 用于显示新旧全部频道的判断
    public static String KEY_HOME_PAGE_TAB_API_TEMPLATE_DISPLAY = "key_home_page_tab_api_template_display";

    // 首页顶tab接口的templateDisplay 用于显示新旧全部频道的判断
    public static String KEY_HOME_PAGE_TAB_API_CHANGE_MODEL_DISPLAY = "key_home_page_tab_api_change_model_display";

    //首页tab头像红点上次显示的日期
    public static final String KEY_TAB_HEAD_IMAGE_LAST_SHOW_DATE = "key_tab_head_image_last_show_date";

    //每天第一次打开app
    public static final String KEY_OPEN_APP_TIME_ONE_DAY = "key_open_app_time_one_day";

    //是否设置过音量增强
    public static String KEY_HAS_SET_VOLUME_ENHANCE = "key_has_set_volume_enhance";

    // 每日更新引导条是否点击过
    public static final String KEY_IS_EVERYDAY_UPDATE_GUIDE_CLICKED = "key_is_everyday_update_guide_clicked";
    // 每日更新－引导追更是否点击过
    public static final String KEY_HAS_EVERYDAY_UPDATE_SETTING_GUIDE_CLICKED = "key_has_everyday_update_setting_guide_clicked";
    // 每日更新－新人引导是否点击过
    public static final String KEY_HAS_EVERYDAY_UPDATE_FRESH_GUIDE_CLICKED= "key_has_everyday_update_fresh_guide_clicked";
    // 每日更新－微信引导是否点击过
    public static final String KEY_HAS_EVERYDAY_UPDATE_WEIXIN_GUIDE_CLICKED = "key_has_everyday_update_weixin_guide_clicked";
    // 每日更新-推送提示是否显示过
    public static final String KEY_HAS_EVERYDAY_UPDATE_PUSH_GUIDE_SHOWN = "key_has_everyday_update_push_guide_shown";
    // 每日更新引导Tip
    public static final String KEY_SHOWN_EVERYDAY_UPDATE_HINT = "key_show_everyday_update_hint";

    // 一起听退出引导是否显示过
    public static final String KEY_PLANET_ENTER_GUIDE = "key_planet_quit_show_enter_guide";
    // 发现页上次tab定位
    public static final String KEY_FEED_LAST_TAB_VISIT = "key_last_feed_tab_visit_uid_";
    // 发现页权限提示
    public static final String KEY_SHOWN_FEED_PERMISSION_HINT = "key_show_feed_permission_hint_";

    // 首页新用户礼包引导
    public static final String KEY_SHOW_NEW_USER_GIFT_GUIDE = "key_show_new_user_gift_guide";
    // 语音唤醒功能是否打开
    public static final String KEY_VOICE_WAKE_IS_OPENED = "key_voice_wake_is_opened";
    // 驾驶模式是否支持唤醒
    public static final String KEY_VOICE_WAKE_ENABLE = "key_voice_wake_enable";
    // 当天是否显示过语音唤醒提示
    public static final String KEY_HAS_SHOWN_VOICE_WAKE_HINT_TODAY = "key_has_shown_voice_wake_hint_today";
    // 在语音助手功能开启的地方判断是否有处理过录音权限
    public static final String KEY_HAS_HANDLED_RECORD_PERMISSION_FOR_VOICE_WAKE = "key_has_handled_record_permission_for_voice_wake";

    // 免流弹窗现在的状态
//    public static final String KEY_FREE_FLOW_DIALOG_SHOW_CURR_STATE = "key_free_flow_dialog_last_show_curr_state";
    public static final String KEY_FREE_FLOW_DIALOG_SHOW_CURR_STATE_NEW = "key_free_flow_dialog_last_show_curr_state_new";

    // 免流总是允许
    public static final String KEY_FREE_FLOW_DIALOG_ALWAY_ALLOW = "key_free_flow_dialog_alway_allow";

    // 用户不点击转化按钮,连续出现弹窗三次进入30天内不再出现弹窗,3次弹窗的间隔:3个工作日,7个工作日
    public static final String KEY_FREE_FLOW_DIALOG_LAST_SHOW_TIME_NEW = "key_free_flow_dialog_last_show_time_new";

    // 前三天展示免流弹窗的日期
    public static final String KEY_FREE_FLOW_DIALOG_LAST_SHOW_DAY = "key_free_flow_dialog_last_show_day";

     //一起听邀请用户出现时间
    public static final String KEY_SHOW_LIVE_LISTEN_VISIT_TIME = "key_show_live_listen_visit_time";

    public static final String KEY_SHOW_LIVE_LISTEN_VISIT_TIME_NEW = "key_show_live_listen_visit_time_new";

    // 全站畅听:最后一次展示弹窗的时间
    public static final String KEY_LAST_SHOW_DIALOG_TIME = "key_last_dialog_show_time";
    // 开机上次显示的日期
    public static final String KEY_SHOW_INTERACTIVE_SENSOR_DATE = "key_show_interactive_sensor_date";

    // 上次上报录音权限的时间
    public static final String KEY_LAST_REPORT_RECORD_PERMISSION_TIME = "key_last_report_record_permission_time";
    public static final String KEY_LAST_REPORT_RECORD_PERMISSION_TYPE = "key_last_report_record_permission_type";

    // 频道页缓存时间
    public static String KEY_CATEGORY_RECOMMEND_CACHE_TIMESTAMP = "key_category_recommend_cache_timestamp";

    public static String KEY_RECOMMEND_AD_TYPE = "key_recommend_ad_type_for_sdk";

    // 播放错误的时候显示错误弹窗
    public static String KEY_PLAY_ERROR_SHOW_DEBUG_DIALOG = "key_play_error_show_debug_dialog";

    public static String KEY_SPLASH_AD_TYPE = "key_splash_ad_type_for_sdk";

    //每天定位一次成长福利
    public static final String KEY_LOCATION_TO_SPACE_POINT_DATE = "key_location_to_space_point_date";

    // 被禁止且不再询问的权限
    public static final String KEY_PERMISSION_DENY_AND_NO_MORE_ASK_PREFIX = "permission_deny_and_no_more_ask";

    //儿童模式隐私弹框
    public static final String KEY_KIDS_PRIVACY_DIALOG_SHOWN = "kids_privacy_dialog_shown";
    // vip下底导首页引导
    public static final String KEY_VIP_DOWN_GUIDE_HAS_SHOW = "vip_down_guide_has_show";

    // 9.0.9客服引导
    public static final String KEY_MINE_KEFU_GUIDE_NEW = "key_mine_kefu_guide_new";

    // 咔嚓引导
    public static final String KEY_MINE_KACHA_GUIDE_NEW = "key_mine_kacha_guide_new";

    // 9.0.42动态页追更tab引导
    public static final String KEY_DYNAMIC_CHASE_TAB_GUIDE = "key_dynamic_chase_tab_guide";

    // 9.0.42底部栏动态-追更引导
    public static final String KEY_MINE_BOTTOM_TAB_DYNAMIC_CHASE_GUIDE = "key_mine_bottom_tab_dynamic_chase_guide";

    //9.0.21新版收藏引导
    public static final String KEY_MINE_COLLECT_GUIDE_NEW = "key_mine_collect_guide_new";

    //9.0.21新版点赞引导
    public static final String KEY_MINE_LIKE_GUIDE_NEW = "key_mine_like_guide_new";

    public static final String KEY_MINE_GUIDE_LOCAL_BOOK_IMPORT = "key_mine_guide_local_book_import";

    // 个人声音收藏搜索历史
    public static final String KEY_LISTEN_MY_TRACK_SEARCH_HISTORY = "key_listen_my_track_search_history";

    // 账号页tab强引导
    public static final String KEY_MINE_TAB_STRONG_GUIDE_NEW = "key_mine_tab_strong_guide_new";

    // 三方sdk rtb 文件名字
    public static final String THIRD_SDK_RTB_FILE_NAME = "third_sdk_rtb_file_name";

    // 广点通 rtb token
    public static final String KEY_GDT_RTB_TOKEN = "gdt_rtb_token";

    // 广点通 rtb token
    public static final String KEY_GDT_RTB_SDK_INFO = "gdt_rtb_sdk_info";
    // 穿山甲 rtb token
    public static final String KEY_CSJ_RTB_TOKEN = "csj_rtb_token";

    public static final String KEY_CHILD_PROTECT_DIALOG_LAST_SHOW_TIME = "key_child_protect_dialog_last_show_time";
    public static final String KEY_NEW_USER_ACTIVE_TIME_FOR_CHILD_PROTECT_DIALOG = "key_new_user_active_time_for_child_protect_dialog";
    public static final String KEY_HAS_READ_CHILD_PROTECT_DIALOG_SHOW_TIME_FROM_TIME_LIMIT_MANAGER = "key_has_read_child_protect_dialog_show_time_from_time_limit_manager";
    public static final String KEY_OLD_DEVICE_TOKEN_LIST = "key_old_device_token_list";
    public static final String ITEM_USE_NEW_RECOMMEND_FRAGMENT = "item_use_new_recommend_fragment";
    public static final String ITEM_USE_RADIO_AD_FREQUENCY_CONTROL = "item_radio_ad_frequency_control"; // 测试用，广播页贴片频控
    public static final String ITEM_USE_PLAY_COVER_AD_FREQUENCY_CONTROL = "item_play_cover_ad_frequency_control"; // 测试用，播放页贴片频控
    public static final String ITEM_DEEPLINK_USE_XMGROWTH_SDK = "item_deeplink_use_xmgrowth_sdk"; // 测试用
    public static final String ITEM_DEEPLINK_FIRST_REQUEST_NOT_PASS_OAID = "item_deeplink_first_request_not_pass_oaid"; // 测试用
    public static final String ITEM_NEW_SOUND_AD = "item_new_sound_ad"; // 测试用，是否使用新版贴片场景差异化样式

    public static final String ITEM_MOCK_HTTPDNS_SWITCH = "item_mock_http_dns_switch";

    public static final String KEY_FEED_CREATE_OUT_SYNC_CIRCLE_GUIDE = "key_feed_create_out_sync_circle_guide";

    public static final String KEY_DAILY_NEWS3_REFRESH_AFTER_5AM = "key_daily_news3_refresh_after_5am";

    public static final String KEY_DAILY_NEWS4_REFRESH_STATUS = "key_daily_news4_refresh_status";

    public static final String KEY_LIVE_AUDIO_DEVICE_MODE = "key_live_audio_device_mode";

    // 记录上次分享渠道
    public static final String KEY_LAST_SHARE = "key_last_share";

    public static final String KEY_LIKE_PROMPT_STR = "key_like_prompt_str";

    public static final String KEY_LIKE_REPLY_TEXT = "key_like_reply_text";

    // 动态底部tab点击时展示的数据
    public static final String KEY_DYNAMIC_TAB_SHOW_DATA_INFO = "key_dynamic_tab_show_data_info_v2";

    public static final String KEY_DYNAMIC_TAB_CLICK_TIME = "key_dynamic_tab_click_time";

    public static final String KEY_DYNAMIC_TAB_RED_CLICK_TIME = "key_dynamic_tab_red_click_time";

    public static final String KEY_DYNAMIC_TAB_LAST_REQUEST_TIME = "key_dynamic_tab_last_request_time";

    public static final String KEY_DYNAMIC_TAB_DAY_RED_CLICK_COUNT = "key_dyncmic_tab_day_red_click_count";

    public static final String KEY_TEST_SHARE_LOCK_PANEL_INDEX = "key_test_share_lock_panel_index";

    public static final String KEY_CLOSE_DAILY_NEWS_SHORT_CUT = "key_close_daily_news_short_cut";
    public static final String KEY_CLOSE_PUSH_GUARD_PLAYER = "key_close_push_guard_player";

    // 是否使用新的rtb获取方式
    public static final String KEY_USE_NEW_RTB_INFO = "key_use_new_rtb_info";

    public static final String KEY_TING_LIST_COLLECT_DIALOG_SHOWN = "key_ting_list_collect_dialog_shown";

    public static final String KEY_LAST_SHOW_GUIDE_SUBSCRIPTION_TIME = "key_last_show_guide_subscription_time";

    public static final String KEY_LAST_REQUEST_NEED_SHOW_RECALL_USER_RIGHTS_DIALOG_TIME = "key_last_request_need_show_recall_user_rights_dialog_time";

    // 动态底部tab点击时过期时间
    public static final String KEY_DYNAMIC_TAB_CLICK_STRONG_LOTTERY_EXPIRETIME = "key_dynamic_tab_click_strong_lottery_expiretime";

    // 动态底部tab点击抽奖的时间
    public static final String KEY_DYNAMIC_TAB_CLICK_LOTTERY_DATE = "key_dynamic_tab_click_lottery_date";

    // 动态底部tab点击抽奖的ids
    public static final String KEY_DYNAMIC_TAB_CLICK_LOTTERY_IDS = "key_dynamic_tab_click_lottery_ids";

    public static final String KEY_DYNAMIC_TAB_CLICK_LOTTERY_IDS_BY_USER = "key_dynamic_tab_click_lottery_ids_";

    //数盟ID
    public static final String KEY_SMSDK_QUERY_ID = "key_smsdk_query_id";

    // 设置是否有免流历史
    public static final String KEY_DEVICE_HAS_FREE_FLOW_HISTORY = "key_device_has_free_flow_history";

    public static final String KEY_HOMEPAGE_OTHER_TAB_DELAY_CREATE = "key_homepage_other_tab_delay_create";
    public static final String KEY_PLAYPAGE_OTHER_TAB_DELAY_CREATE = "key_playpage_other_tab_delay_create";
    public static final String KEY_XUID_HEADER_VALUE = "key_xuid_header_value";
    public static final String KEY_XUID_HEADER_CHANGED = "key_xuid_header_changed";
    public static final String KEY_XUID_CHANGE_DEVICEID = "key_xuid_change_device_id";
    public static final String KEY_SM_CONTROL_OPEN = "key_sm_control_open";

    public static final String KEY_HAS_NEW_ADD_TO_LISTEN_TRACK = "key_has_new_add_to_listen_track";
    // 兴趣标签引导弹窗上次出现的时间
    public static final String KEY_INTEREST_TAG_GUIDE_DIALOG_LAST_SHOW_TIME = "key_interest_tag_guide_dialog_last_show_time";
    public static final String KEY_USER_INFO_MMKV_FILE_NAME = "key_user_info_mmkv_file_name";
    public static final String KEY_USER_INFO_HAS_MIGRATION = "key_user_info_has_migration";

    public static final String KEY_AVATAR_CROP_UTIL_SWITCH = "avatar_crop_util_switch";
    public static final String  KEY_SAFELY_REMINDER_HAS_CLOSED = "key_safely_reminder_has_closed";
    public static final String KEY_HAS_EVER_USE_ONLINE_ENVIROMENT_PLUGIN = "key_has_ever_use_online_enviroment_plugin";

    public static final String KEY_DIRECTIONAL_USER_DATE = "key_directional_user_date";   // 定向用户时间

    public static final String KEY_AUDIO_PLAY_SCREEN_OFF = "key_audio_play_screen_off";
    public static final String KEY_LOCK_SCREEN_GUIDE_SHOW_TIME = "key_lock_screen_guide_push_show_time";

    public static final String KEY_LOCK_GO_SETTING = "key_click_go_to_lock_setting";

    public static final String TINGMAIN_FILENAME_TALK_QUERY_HISTORY = "talk_query_history";
    public static final String TINGMAIN_KEY_LAST_QUERY_TALK_COMMENTS_TIME = "key_last_query_talk_comments_time_";
    public static final String TINGMAIN_FILENAME_TALK_CARD_SHOW_RECORD = "talk_card_show_record"; // 记录讨论卡片是否展示过
    public static final String KEY_TALK_CARD_HAS_SHOW_PREFIX = "talk_card_has_show_"; // 讨论卡片是否展示过，拼上talkId作为key
    public static final String KEY_KACHA_RED_DOT_NEED_SHOW = "key_kacha_red_dot_need_show"; // 咔嚓红点是否要展示
    public static final String KEY_KACHA_RED_DOT_REMOVED = "key_kacha_red_dot_removed"; // 咔嚓红点是否已经移除了

    public static final String TINGMAIN_FILENAME_LOCKSCREEN_COMMENT_BOX = "lockscreen_comment_box_mmkv_file";

    public static final String KEY_SIGN_GUIDE_TIME = "key_sign_guide_time"; // 签到引导弹窗时间记录
    public static final String KEY_SIGN_EFFECT_TIME = "key_sign_effect_time"; // 签到动效时间记录，7日内只展示一次
    public static final String KEY_SIGN_EFFECT_COUNT = "key_sign_effect_count"; // 签到动效次数记录，最多三次
    public static final String KEY_SIGN_RED_DOT_SHOW_DATE = "key_sign_red_dot_show_date"; // 最近一次签到小红点显示时间
    public static final String KEY_LIVE_RED_DOT_SHOW_DATE = "key_live_red_dot_show_date"; // 直播小红点展示时间

    public static final String KEY_TEXT_NAGATIVE_FEEDBACK_LAST_CLASE_TIME = "key_negative_feedback_last_close_time";


    public static final String KEY_FORBID_AUTO_SIZE = "key_forbid_auto_size";
    public static final String KEY_USE_AUTO_SIZE = "key_use_auto_size_20231221";

    public static final String KEY_AUTO_SIZE_HOOK_RESOURCES = "key_auto_size_hook_resources";
    public static final String KEY_CLOSE_AUTO_SIZE_PLAY_BAR_NEW = "key_close_auto_size_play_bar_new";

    public static final String KEY_USE_AUTO_SIZE_DEBUG = "key_use_auto_size_debug";

    //播放页-互推引导相关
    public static final String FILENAME_AUDIO_PLAY_GUIDE = "FILENAME_AUDIO_PLAY_GUIDE"; //mmkv单独存储文件名
    public static final String KEY_AUDIO_PLAY_HIDE_MC_ID = "key_audio_play_hide_mc_id_"; //当前专辑不展示互推条，value为隐藏的时间
    public static final String KEY_AUDIO_PLAY_HIDE_LIVE_ID = "key_audio_play_hide_live_id_"; //当前专辑不展示互推条，value为隐藏的时间
    public static final String KEY_AUDIO_PLAY_HIDE_GUIDE_SUBSCRIBE_ID = "key_audio_play_hide_guide_subscribe_id_"; //当前专辑不展示引导订阅,后面需要拼接albumid
    public static final String KEY_AUDIO_PLAY_HIDE_GUIDE_FOLLOW_ID = "key_audio_play_hide_guide_follow_id_"; //当前专辑不展示引导关注,后面需要拼接albumid
    // 投稿箱我的页面点击收到的评论的时间
    public static final String KEY_ENTER_MY_MAIL_RECEIVED_COMMENT_TIME  = "key_enter_my_mail_received_comment_time";
    // 投稿箱我的页面点击收到的点赞的时间
    public static final String KEY_ENTER_MY_MAIL_RECEIVED_LIKE_TIME = "key_enter_my_mail_received_like_time";

    // app上次启动时间
    public static final String KEY_LAST_APP_LAUNCH_TIME = "key_last_app_launch_time";
    // app本次启动时间
    public static final String KEY_CURRENT_APP_LAUNCH_TIME = "key_current_app_launch_time";

    //新用户空播放，每天展示一次播放引导
    public static final String KEY_TIME_CHECK_NEWUSER_PLAYTIPSGUIDE = "key_time_check_newuser_playtipsguide";

    // push进程的启动时间（最近一次，包括当前进程存在存活）
    public static final String KEY_APP_LAUNCH_TIME_FOR_PUSH_PROCESS = "key_app_launch_time_for_push_process";

    public static final String KEY_LAST_APP_MAIN_LAUNCH_TIME_FOR_TRACE = "key_last_app_main_launch_time_for_trace";
    public static final String KEY_LAST_APP_PLAYER_LAUNCH_TIME_FOR_TRACE = "key_last_app_player_launch_time_for_trace";

    public static final String KEY_HAS_SHOW_FIRST_UNLOCK_GUIDE_VIDEO = "key_has_show_first_unlock_guide_video_new" ;// 是否已经展示了新手全站畅听解锁引导视频
    public static final String KEY_REWARD_VIDEO_FIREWORK_CLOSE_TIME = "key_reward_video_firework_clsoe_time";// 首页解锁视频弹窗连续关闭次数
    public static final String KEY_REWARD_VIDEO_FIREWORK_NEXT_SHOW_TIME = "key_reward_video_next_show_time"; // 首页解锁视频弹窗连续关闭多次之后，下次可以展示的时间
    public static final String KEY_REWARD_VIDEO_FIREWORK_FIRST_SHOW = "key_reward_video_firework_first_show"; // 是否是首页解锁视频弹第一次展示
    public static final String KEY_REWARD_VIDEO_FIREWORK_HIDE_ANIMATION_FIRST_SHOW = "key_reward_video_firework_hide_animation_first_show" ;// 是否是首页解锁弹屏关闭动画第一次展示

    public static final String  ACTION_MINE_AUTO_SELECT_BOOK_TAB = "com.ximalaya.ting.android.ACTION_MINE_AUTO_SELECT_BOOK_TAB";//自动选中本地 tab 下的书籍 tab

    public static final String  MMKV_KEY_TAB_NAME_CHANGED_GUIDE_SHOWED = "listen_tab_name_changed_guide_showed_v2";
    // 新用户相关：弹窗等逻辑
    public static final String KEY_LIMIT_GIFT_GUIDE_HAS_SHOW = "key_limit_gift_guide_has_show";
    public static final String KEY_LIMIT_GIFT_GUIDE_NEED_DELAY_SHOW = "key_limit_gift_guide_need_delay_show";   //是否有其他弹窗弹出，导致限免专辑引导需要延迟
    public static final String KEY_LIMIT_NEW_USER_COMPLETE_TASK_NEED_REFRESH = "key_limit_new_user_complete_task_need_refresh";
    //召回用户强制显示兴趣弹窗限免弹窗
    public static final String KEY_RECALL_USER_FORCE_SHOW = "key_recall_user_force_show";
    public static final String KEY_RECALL_USER_REQUEST_TIME = "key_recall_user_request_time";
    public static final String KEY_REWARD_VIDEO_TWICE_TIMES = "key_reward_video_twice_times";
    public static final String KEY_REWARD_VIDEO_TWICE_DATE = "key_reward_video_twice_date";
    public static final String KEY_REWARD_VIDEO_TWICE_TIMES_NEW = "key_reward_video_twice_times_new";
    public static final String KEY_REWARD_VIDEO_TWICE_DATE_NEW = "key_reward_video_twice_date_new";
    public static final String KEY_REWARD_VIDEO_LAST_SHOW_TIME = "key_reward_video_last_show_time";
    public static final String KEY_REWARD_VIDEO_HAS_USE = "key_reward_video_has_use";
    public static final String KEY_REWARD_VIDEO_DIALOG_COVERS = "key_reward_video_dialog_covers";

    public static final String KEY_FREE_ICON_TO_VIDEO_DIRECT_TIMES = "key_free_icon_to_video_direct_times";
    public static final String KEY_FREE_ICON_TO_VIDEO_DIRECT_DATE = "key_free_icon_to_video_direct_date";

    public static final String KEY_REWARD_VIDEO_LAST_UNLOCK_DATE = "key_reward_video_last_unlock_date"; // 上次成功解锁的日期
    public static final String KEY_REWARD_VIDEO_CONTINUE_UNLOCK_DAYS = "key_reward_video_continue_unlock_days"; // 连续成功解锁的天数
    public static final String KEY_REWARD_VIDEO_NEXT_SHOW_COUNT_DOWN_DIALOG_TIME = "key_reward_video_next_show_count_down_dialog_time"; // 下次可以展示倒计时弹窗的时间

    // 文稿封面引导记录
    public static final String KEY_MANUSCRIPT_GUIDE_SHOW = "key_manuscript_guide_show";
    public static final String KEY_MANUSCRIPT_COVER_GUIDE_SHOW = "key_manuscript_cover_guide_show";

    public static final String KEY_ANCHOR_TRACK_GUIDE_SHOW_PREFIX = "key_anchor_track_guide_show_prefix";

    public static final String KEY_ANCHOR_TRACK_GUIDE_SHOW_TODAY_COUNT = "key_anchor_track_guide_show_today_count";

    public static final String KEY_ANCHOR_TRACK_GUIDE_SHOW_IS_TODAY = "key_anchor_track_guide_show_is_today";
    public static final String KEY_ANCHOR_ALBUM_GUIDE_SHOW_PREFIX = "key_anchor_album_guide_show_prefix";

    public static final String KEY_ANCHOR_ALBUM_GUIDE_SHOW_DATE_PREFIX = "key_anchor_album_guide_show_date_prefix";

    //v3播放页 ppt的展示消失记录用户习惯
    public static final String KEY_AUDIO_PLAY_PAGE_PPT_SHOW_HIDE_STATE = "key_audio_play_page_ppt_show_hide_state";
    public static final String KEY_ALBUM_GUIDE_SOUNDPATCH_DIALOG_SHOW = "key_album_guide_soundpatch_dialog_show";

    //儿童ai换声角色
    public static final String KEY_PLAY_KID_AI_SOUND_ROLE = "key_play_kid_ai_sound_role";

    // 用户类型
    public static final String KEY_USER_TYPE = "key_user_type_life";

    // 我页多模式切换
    public static final String KEY_MINE_MODE_SWITCH_GUIDE_SHOW = "key_mine_mode_switch_guide_show";

    // 我页当前模式
    public static final String KEY_MINE_MODE_SWITCH_STATUS = "key_mine_mode_switch_status";

    // 我页标题录音直播是否已展示引导
    public static final String KEY_MINE_TITLE_RECORD_LIVE_GUIDE_SHOW = "key_mine_title_record_live_guide_show";

    // 完播推荐自动续播开关

    public static final String KEY_PLAY_COMPLETE_RECOMMEND_AUTO_PLAY_SWITCH = "key_play_complete_recommend_auto_play_switch_new";
    public static final String KEY_PLAY_COMPLETE_RECOMMEND_AUTO_PLAY_SWITCH_CHILD = "key_play_complete_recommend_auto_play_switch_new_child";
    public static final String KEY_PLAY_COMPLETE_RECOMMEND_AUTO_PLAY_SWITCH_AB = "key_play_complete_recommend_auto_play_switch_ab";

    public static final String KEY_AUTO_PLAY_SWITCH_USE_REMOTE = "key_auto_play_switch_use_remote";

    public static final String KEY_PLAY_AD_FRAGMENT_SHOWING = "key_play_ad_fragment_showing" ;// 广告独立播放器是否正在展示

    public static final String KEY_IS_NEW_USESR = "key_is_new_user";

    public static final String KEY_NEW_USER_RIGHTS_DIALOG = "KEY_NEW_USER_RIGHTS_DIALOG_";

    public static final String KEY_ITING_GROWTH_CONTENT_ID_AND_TIME = "key_iting_growth_content_id_and_time";

    public static final String KEY_PLAY_COMPLETE_AUTO_PLAY_TIMES = "key_play_complete_auto_play_times";

    // 最近通过分享链接打开角色页的具体信息，value格式：分享人uid_专辑id_时间戳
    public static final String KEY_LAST_OPEN_SHARED_ROLE_PAGE_INFO = "key_last_open_shared_role_page_info";

    @Nullable
    public static final String KEY_NO_PRIVACY_RANDOM_DEVICE_ID = "key_no_privacy_random_device_id";

    public static final String KEY_SEND_CHILD_PROTECT_CHANGE_ACTION = "key_send_child_protect_change_action";

    //ai唤声入口点击
    public static final String KEY_IS_CLICK_AI_SOUND_ENTRY = "KEY_IS_CLICK_AI_SOUND_ENTRY";

    // 我的页广告相关
    public static final String KEY_MIN_AD_CLOSE_TIMES = "key_mine_ad_close_times";
    public static final String KEY_MIN_AD_CLOSE_DATE = "key_mine_ad_close_date";

    // 下载页广告相关
    public static final String KEY_DOWNLOAD_AD_CLOSE_TIMES = "key_download_ad_close_times";
    public static final String KEY_DOWNLOAD_AD_CLOSE_DATE = "key_download_ad_close_date";

    // 会员购买弹窗协议勾选
    public static final String KEY_HAS_AGREE_VIP_PURCHASE_DIALOG_PROTOCOL = "key_has_agree_vip_purchase_dialog_protocol";
    // 统一购买弹窗协议勾选
    public static final String KEY_HAS_AGREE_UNIVERSAL_PAYMENT_PURCHASE_DIALOG_PROTOCOL = "KEY_HAS_AGREE_VIP_PURCHASE_DIALOG_PROTOCOL";

    // 当天播放总时长
    public static final String KEY_TODAY_PLAY_DURATION = "key_today_play_duration";
    // 实验配置
    public static final String KEY_RECALL_SOUND_1HOUR_NOAD20230811 = "key_recall_sound_1hour_noad20230811";
    // 是否是召回2到28天用户
    public static final String KEY_RECALL_28days = "key_recall_28days";
    public static final String KEY_PRIVACY_PROTOCOL_REMOTE_VERSION = "key_privacy_protocol_remote_version";

    public static final String KEY_ALBUM_HAS_POSTION_EBOOK_TAB = "KEY_ALBUM_HAS_POSTION_EBOOK_TAB";

    public static final String KEY_HYBRID_VIEW_WEB_VIEW_FAST_ENABLED = "KEY_HYBRID_VIEW_WEB_VIEW_FAST_ENABLED";

    public static final String KEY_DEBUG_AITEXT_READER_ENABLE = "key_debug_aitext_reader_enable";
    public static final String KEY_DEBUG_AITEXT_READER_AD_SHOW = "key_debug_aitext_reader_ad_show";
    public static final String KEY_DEBUG_AITEXT_READER_FREE_AD = "key_debug_aitext_reader_free_ad";
    public static final String KEY_USE_NEW_PLAY_BAR_LOCAL = "key_use_new_play_bar_local";

    public static final String KEY_SETTING_VIDEO_PLAY_IMMERSIVE = "KEY_SETTING_VIDEO_PLAY_IMMERSIVE";//视频优先播放

    //9.1.76小版本开关
    public static final String KEY_OPEN_IMMERSIVE_VIDEO_CODE = "KEY_OPEN_IMMERSIVE_VIDEO_CODE";
    public static final String OFFLINE_RESOURCE_MAX_VALID_SIZE_WITH_MB = "offline_resource_max_valid_size_with_mb";
    public static final String OFFLINE_RESOURCE_MAX_ACTIVITY_VALID_SIZE_WITH_MB = "offline_resource_max_activity_valid_size_with_mb";

    //记录用户杀死播放页前在视频还是音频状态的记录
    public static final String KEY_USER_PLAY_LAST_IS_AUDIO = "KEY_USER_PLAY_LAST_IS_AUDIO";
    public static final String KEY_FREE_LISTEN_V2_OPEN = "key_free_listen_v2_open"; // 激励视频时长解锁模式服务端ab开关是否打开
    public static final String KEY_FREE_LISTEN_SCENE_ID = "key_replace_config_scene_id"; // 激励视频时长解锁模式场景id
    public static final String KEY_FREE_LISTEN_EXP_ID = "key_replace_config_exp_id"; // 激励视频时长解锁模式实验分组id
    public static final String KEY_FREE_LISTEN_EXT = "key_replace_config_free_listen_ext"; // 激励视频时长解锁模式透传字段
    public static final String KEY_FREE_LISTEN_REMOTE_SYNC_TIME = "key_free_listen_remote_sync_time"; // 激励视频时长同步服务端时间
    public static final String KEY_FREE_LISTEN_NEED_COUNTDOWN_TIME = "key_free_listen_need_count_down_time" ;// 时长模式是否需要扣减时长，保护期内不扣减时长
    public static final String KEY_FREE_LISTEN_TIME_LIMIT = "key_free_listen_time_limit" ;// 时长模式时长累加上限
    public static final String KEY_FREE_LISTEN_NEED_CONFLICT_SOUND_PATCH = "key_free_listen_need_conflict_sound_patch" ;// 时长模式是否需要互斥贴片请求
    public static final String KEY_FREE_LISTEN_NEED_SPEED_DEC = "key_free_listen_need_speed_dec" ;// 时长模式是否需要倍速扣减

    public static final String KEY_SHORT_PLAY_FREE_LISTEN_REMOTE_SYNC_TIME = "short_play_key_free_listen_remote_sync_time_1226"; // 短剧，激励视频时长同步服务端时间

    public static final String KEY_FREE_LISTEN_GIT_POP_CLICK_DATE = "key_free_listen_gif_pop_click_date"; // 免费赠送时长气泡点击日期
    public static final String KEY_REPLACE_CONFIG_SCENE_INFO = "key_replace_config_scene_info";

    public static final String KEY_REPLACE_CONFIG_NEW_FREE_LISTEN_PARAM_INFO = "key_replace_config_new_free_listen_param_0515";

    public static final String KEY_FREE_LISTEN_V2_SWITCH_CONFIG = "key_free_listen_v2_switch_config";
    public static final String KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN = "key_free_listen_protocol_shown"; // 时长解锁首次协议弹窗是否已展示
    public static final String KEY_FREE_LISTEN_ADD_LISTEN_TIME_SUCCESS = "key_free_listen_add_listen_time_success"; // 每日赠送时长发放状态
    public static final String KEY_FREE_LISTEN_GESTURE_TIPS_HAS_SHOWN = "key_free_listen_gesture_tips_shown";
    public static final String KEY_FREE_LISTEN_HAS_EXPERIENCE = "key_free_listen_has_experience" ; // 是否已经体验过畅听
    public static final String KEY_FREE_LISTEN_ADD_LISTEN_TIME_ONCE_SUCCESS = "key_free_listen_add_listen_time_once_success"; // 保护期一次性赠送时长发放状态
    public static final String KEY_FREE_LISTEN_ADD_LISTEN_TIME_ONCE_SUCCESS_TIME = "key_free_listen_add_listen_time_once_success_time"; // 保护期一次性赠送时长发放状态时间戳
    public static final String KEY_FREE_LISTEN_ADD_LISTEN_TIME_INCREASE_SUCCESS_TIME = "key_free_listen_add_listen_time_increase_success_time"; // 非保护期一次性赠送时长发放状态时间戳
    public static final String KEY_FREE_LISTEN_ADD_LISTEN_TIME_INCREASE_SUCCESS_JSON = "key_free_listen_add_listen_time_increase_success_json"; // 非保护期一次性赠送时长发放reward信息
    public static final String KEY_FREE_LISTEN_SHOW_BOTTON_DIALOG_TIME = "key_free_listen_show_botton_dialog_time"; // 非保护期本地弹屏时间戳
    public static final String KEY_FREE_LISTEN_NEW_USER_DIALOG_SHOWN = "key_free_listen_new_user_dialog_shown"; // 畅听时长新用户已展示过弹窗
    public static final String KEY_FREE_LISTEN_ALL_DAY_DEAD_LINE = "key_free_listen_all_day_dead_line" ; // 全听免费收听截止时间
    public static final String KEY_FREE_LISTEN_TIME_ARRAY_INDEX_VALID_DATE = "key_free_listen_time_array_index_valid_date"; // 时长数组索引有效期的日期
    public static final String KEY_FREE_LISTEN_TIME_ARRAY_INDEX = "key_free_listen_time_array_index"; // 时长数组当前的索引
    public static final String KEY_FREE_LISTEN_TIME_ARRAY = "key_free_listen_time_array"; // 时长数组
    public static final String KEY_FREE_LISTEN_TIME_ARRAY_MAX_SHOW_COUNT = "key_free_listen_time_array_max_show_count"; // 时长数组最大展示数量

    public static final String KEY_FREE_LISTEN_NEW_HOME_DIALOG_ALBUM_INFO_TIME = "KEY_FREE_LISTEN_NEW_HOME_DIALOG_ALBUM_INFO_TIME_2405"; // 新畅听首页弹窗-专辑信息，要展示的时间
    public static final String KEY_FREE_LISTEN_NEW_HOME_DIALOG_ALBUM_INFO_SHOW = "KEY_FREE_LISTEN_NEW_HOME_DIALOG_ALBUM_INFO_SHOW_2405"; // 新畅听首页弹窗-专辑信息，是否要展示

    public static final String KEY_FREE_LISTEN_SHOW_INSPIRE_DIALOG = "KEY_FREE_LISTEN_SHOW_INSPIRE_DIALOG"; // 首页唤端弹窗，是否要展示

    public static final String KEY_FREE_LISTEN_SHOW_INSPIRE_DIALOG_TIME = "KEY_FREE_LISTEN_SHOW_INSPIRE_DIALOG_TIME"; // 首页唤端弹窗，要展示的时间
    public static final String KEY_FREE_LISTEN_DIALOG_GET_VIP_TIME = "KEY_FREE_LISTEN_DIALOG_GET_VIP_TIME_2407"; // 新畅听首页弹窗-领取一天会员，要展示的时间
    public static final String KEY_FREE_LISTEN_DIALOG_GET_VIP_SHOW = "KEY_FREE_LISTEN_DIALOG_GET_VIP_SHOW_2407"; // 新畅听首页弹窗-领取一天会员，是否要展示
    public static final String KEY_FREE_LISTEN_WELFARE_CASH_SHOW_TIME = "KEY_FREE_LISTEN_WELFARE_CASH_SHOW_TIME"; // 新畅听福利页现金弹出动画展示

    public static final String KEY_FREE_LISTEN_SUOND_PATCH_REWARD_TICKET = "KEY_FREE_LISTEN_SUOND_PATCH_REWARD_TICKET"; // 贴片请求使用的风控ticket
    public static final String KEY_FREE_LISTEN_DIALOG_GET_VIP_SHOW_TIME = "KEY_FREE_LISTEN_DIALOG_GET_VIP_SHOW_TIME";
    public static final String KEY_FREE_LISTEN_DIALOG_GET_VIP_SHOW_COUNT = "KEY_FREE_LISTEN_DIALOG_GET_VIP_SHOW_COUNT";

    public static final String KEY_USE_TICKET_INTERCEPTOR = "key_use_ticket_interceptor";// 是否使用ticket拦截器
    public static final String KEY_FREE_LISTEN_OLD_USER_INVITATION = "free_listen_old_user_invitation";// 老用户邀请

    public static final String KEY_USER_BOTTOM_TAB_TAG_HAS_SHOW = "key_user_bottom_tab_tag_has_show_";
    public static final String KEY_SPRING_STYLE_STATUS_AB_STATUS = "key_spring_style_status_ab_status";// 春节新样式 服务端ab
    public static final String KEY_FREE_LISTEN_TAB_STYLE_AB_STATUS = "key_free_listen_tab_style_ab_status";// 免费听新样式 服务端ab
    public static final String KEY_FREE_LISTEN_TAB_VIP_STATUS = "key_free_listen_tab_vip_status";
    public static final String KEY_FREE_LISTEN_TAB_CANFREELISTEN_V2 = "key_free_listen_tab_canfreelisten_v2";

    public static final String KEY_CATEGORY_V2_EXPERIMENT_2_HOMEPAGE = "key_category_v2_experiment_2_homepage"; // 是否是category2实验2,如果是,首页tab编辑tab跳转到新分类页

    public static final String KEY_IS_ACCESSIBILITY_MODE = "key_is_accessibility_mode"; // 无障碍模式
    public static final String KEY_HAS_AUTO_ENTER_ACCESSIBILITY_MODE = "key_has_auto_enter_accessibility_mode"; // 是否自动进入过无障碍模式
    public static final String KEY_ACCESSIBILITY_MODE_HOME_PAGE_TABS_CACHE = "key_accessibility_mode_home_page_tabs_cache"; // 无障碍模式首页tab缓存

    public static final String KEY_AUTO_SHOW_PLAY_FRAGMENT_FROM_ALBUM_UNLOCK = "key_auto_show_play_fragment_from_album_unlock" ;// 时长模式专辑页解锁自动进入播放页

    public static final String KEY_CHAT_XMLY_TIPS = "key_chat_xmly_tips";
    public static final String KEY_CHAT_XMLY_OPEN_VOICE_WAKE_FOR_SDK = "key_chat_xmly_open_voice_wake_for_sdk";
    public static final String KEY_CHAT_XMLY_SETTING_RED_DOT_HAS_SHOW = "key_chat_xmly_setting_red_dot_has_show";

    public static final String KEY_DEBUG_FREE_LISTEN_TOGGLE = "key_debug_free_listen_toggle";
    public static final String KEY_ALBUM_CUSTOM_PAGE_VIDEO_AUTO_PLAY_TIME_PREFIX = "key_album_custom_page_video_auto_play_time_";

    public static final String KEY_LAST_SHOW_DRIVE_MODE_CHAT_XMLY_BUBBLE_TIME = "key_last_show_drive_mode_chat_xmly_bubble_time";
    public static final String KEY_LAST_SHOW_HOME_PAGE_CHAT_XMLY_BUBBLE_TIME = "key_last_show_home_page_chat_xmly_bubble_time";
    public static final String KEY_STREAM_TITLE_STYLE = "streamTitleStyle";
    public static final String KEY_SCENE_ANIMATION_HAS_PLAYED = "key_scene_animation_has_played";

    public static final String KEY_CAMERA_USE_READ_PERMISSION = "key_camera_use_read_permission";

    public static final String KEY_VOICE_SEARCH_RED_DOT_START_TIME = "key_voice_search_red_dot_start_time"; // 首页语音搜索按钮小红点消失时间
    public static final String KEY_VOICE_SEARCH_RED_DOT_END_TIME = "key_voice_search_red_dot_end_time"; // 首页语音搜索按钮小红点消失时间
    public static final String KEY_LOGIN_AUTH_DATA = "key_login_auth_data"; // 登录的免密授权码
    public static final String KEY_FROM_HM_INTENTION_OPEN = "key_from_hm_intention_open"; // 从华为意图打开的时间
    public static final String KEY_HAS_KILL_PROCESS_ONCE = "has_kill_process_once"; // 是否主动杀死了一次进程
    public static final String KEY_PLAY_BAR_COVER = "play_bar_cover"; // 是否主动杀死了一次进程
    public static final String KEY_PLAY_BAR_COVER_FROM_LIVE = "play_bar_cover_from_live"; // 封面是否来自直播
    public static final String KEY_FREE_LISTEN_UPGRADE_TIME = "key_free_listen_upgrde_time"; // 畅听权益升级时间戳

    public static final String KEY_THIRD_UID_UUID_VALYE = "key_third_uid_uuid_valye";

    public static final String KEY_MINE_TOP_CARD_STYLE = "key_mine_top_card_style"; // 畅听权益升级是否展示

    public static final String KEY_MINE_SLIDE_EXPRIENCE = "key_mine_slide_exprience"; // 侧边栏实验

    public static final String ITEM_BOTTOM_ATMOSPHERE_EXP_TIME = "key_main_item_bottom_atmophere_exp";

    // 订阅实验一引导是否已展示
    public static final String KEY_GUIDE_SUBSCRIBE_TO_MINE_1 = "key_guide_subscribe_to_mine_1";
    // 订阅实验二引导是否已展示
    public static final String KEY_GUIDE_SUBSCRIBE_TO_MINE_2 = "key_guide_subscribe_to_mine_2";
    public static final String KEY_REWARD_VIDEO_CONFIG_VERSION = "key_reward_video_config_version";

    public static final String KEY_FREE_LISTEN_DIALOG_OUTSIDE_STATION_SHOW_ENABLE = "key_free_listen_dialog_outside_station_show_enable"; // 畅听站外承接弹窗是否能展示
    public static final String KEY_FREE_LISTEN_DIALOG_OUTSIDE_STATION_TIME = "key_free_listen_dialog_outside_station_time"; // 畅听站外承接弹窗数据缓存时间
    public static final String KEY_FREE_LISTEN_OUTSIDE_STATION_SUCCESS_JSON = "key_free_listen_outside_station_success_json"; // 畅听站外承接弹窗数据缓存

    public static final String KEY_POINT_CENTER_HOME_DIALOG_SHOW_ENABLE = "key_point_center_home_dialog_show_enable"; // 积分中心首页弹窗是否能展示
    public static final String KEY_POINT_CENTER_HOME_DIALOG_TIME = "key_point_center_home_dialog_time"; // 积分中心首页弹窗数据缓存时间
    public static final String KEY_POINT_CENTER_HOME_DIALOG_JSON = "key_point_center_home_dialog_json"; // 积分中心首页弹窗数据缓存
    public static final String KEY_FREE_LISTEN_AD_AUDIO_CODE_SWITCH = "key_free_listen_ad_audio_code_switch"; // 畅听时长用尽音贴开关

    public static final String KEY_DOLBY_ATOMS_SUPPORT = "key_dolby_atoms_support"; // 是否支持dolby音效
    public static final String KEY_FREE_LISTEN_ALL_DAY_DIALOG_SHOW = "KEY_FREE_LISTEN_ALL_DAY_DIALOG_SHOW_ENABLE"; // 新全天弹窗，是否要展示
    public static final String KEY_FREE_LISTEN_ALL_DAY_DIALOG_TIME = "KEY_FREE_LISTEN_ALL_DAY_DIALOG_SHOW_TIME"; // 新全天弹窗，要展示的时间
    public static final String KEY_FREE_LISTEN_ALL_DAY_INSPIRE_DIALOG_SHOW = "KEY_FREE_LISTEN_ALL_DAY_INSPIRE_DIALOG_SHOW_ENABLE"; // 新全天唤端弹窗，是否要展示
    public static final String KEY_FREE_LISTEN_ALL_DAY_INSPIRE_DIALOG_TIME = "KEY_FREE_LISTEN_ALL_DAY_INSPIRE_DIALOG_SHOW_TIME"; // 新全天唤端弹窗，要展示的时间
    public static final String KEY_LISTEN_TASK_FLOAT_SOUND_PATCH_SHOW_TIME = "KEY_LISTEN_TASK_FLOAT_SOUND_PATCH_SHOW_TIME"; // 有收听挂件时贴片展示的时间戳

    public static final String KEY_OPEN_PLAY_FRAGMENT_BEFORE_PLAYING = "open_play_fragment_before_playing";
    public static final String KEY_OPEN_QUICK_LISTEN_DAY = "open_quick_listen_day";
}
