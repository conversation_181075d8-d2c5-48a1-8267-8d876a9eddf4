package com.ximalaya.ting.android.host.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.util.LongSparseArray
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import androidx.annotation.ColorInt
import androidx.core.util.forEach
import androidx.transition.ChangeBounds
import androidx.transition.TransitionManager
import com.ximalaya.ting.android.framework.reflect.FieldUtils
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.other.ForbidableSeekBar
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.Runnable
import kotlin.math.max

/**
 * Created by WolfXu on 2022/3/18.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
open class ScaleableSeekBar : ForbidableSeekBar {
    private val TAG = "ScaleableSeekBar"
    private var mContext: Context? = null
    private val mPointPaint: Paint
    private var mTimelinePointPaint: Paint
    private val mKeyPoints = LongSparseArray<KeyPoint>()
    private val mTimelineCardPoints = LongSparseArray<KeyPoint>()
    private val mScaledTimelineCardPoints = LongSparseArray<KeyPoint>()
    private var mTargetNeedScale = false
    var enableScaleAnimation = false
    private var mNeedScale = false
        set(value) {
            if (supportScale()) {
                if (field != value) {
                    field = value
                    updateScaleState()
                    mOnScaleStateChangeListener?.onScaleStateChanged(field)
                    if(field){
                        mOnUserSeekListener?.onSeekTouchProgress(progress)
                    }
                }
            }
        }
    private var mOriginalMinHeight = 0
    private var mOriginalMaxHeight = 0
    val mKeyPointRadius = 1.dp * 1f
    private val mTimelineCardPointRadius = 1.5f.dp * 1f
    private val mTimelineCardPointScaledRadius = 2.dp * 1f
    private var mProgressScaleHeight = 0
    private var mThumbScaleHeight = 0
    private var mThumbScaleWidth = 0
    private var mOnSeekBarChangeListener: OnSeekBarChangeListener? = null
    protected var mIsDragging = false
    private var mRestoreTimeMS: Long = 0
    private var mOnScaleStateChangeListener: IOnScaleStateChangeListener? = null
    var mIOnHandsUpListener: IOnHandsUpListener? = null

    private var mOnUserSeekListener: IOnUserSeekListener? = null
    private var mThumbScaleDrawable: Drawable ?= null
    private var mOriginThumbScaleDrawable: Drawable ?= null

    private var mBigThumbDrawable: Drawable ?= null
    private var mBigThumbOffset: Int = 0
    private var mCurrentProgress: Int = 0

    private var mThumbOriginDrawable: Drawable ?= null
    private var mProgressScaleDrawable: Drawable ?= null
    private var mProgressOriginDrawable: Drawable ?= null
    var enableTimelinePoints = true
        set(value) {
            if (field != value) {
                field = value
                postInvalidate()
            }
        }

    // 添加overlay相关变量
    private var mOverlayDrawable: Drawable? = null
    private var mIsOverlayAdded = false
    private var mTargetParent: ViewGroup? = null
    private var mCachedSuitableParent: ViewGroup? = null // 缓存找到的合适parent

    constructor(context: Context?): this(context, null) {
    }

    constructor(context: Context?, attrs: AttributeSet?): this(context, attrs, 0) {
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int): super(context, attrs, defStyleAttr) {
        mContext = context
        val attr = context?.obtainStyledAttributes(attrs, R.styleable.ScaleableSeekBar,
            defStyleAttr, 0)

        val keyPointColor = attr?.getColor(R.styleable.ScaleableSeekBar_scaleableSeekBar_KeyPointColor, Color.BLACK)
        mProgressScaleHeight = attr?.getDimensionPixelSize(R.styleable.ScaleableSeekBar_scaleableSeekBar_ProgressScaleHeight, 0) ?: 0
        mThumbScaleHeight = attr?.getDimensionPixelSize(R.styleable.ScaleableSeekBar_scaleableSeekBar_ThumbScaleHeight, 0) ?: 0
        mThumbScaleWidth = attr?.getDimensionPixelSize(R.styleable.ScaleableSeekBar_scaleableSeekBar_ThumbScaleWidth, 0) ?: 0
        mRestoreTimeMS = (attr?.getInt(R.styleable.ScaleableSeekBar_scaleableSeekBar_RestoreTimeMS, 0) ?: 0).toLong()
        val mThumbScaleResource = (attr?.getResourceId(R.styleable.ScaleableSeekBar_scaleableSeekBar_ThumbScaleResource, 0) ?: 0).toInt()
        if (mThumbScaleResource != 0) {
            // 放大时直接更换thumb，不走原来的scale逻辑
            mThumbScaleDrawable = resources.getDrawable(mThumbScaleResource)
            mThumbOriginDrawable = thumb
        }

        val mProgressScaleResource = (attr?.getResourceId(R.styleable.ScaleableSeekBar_scaleableSeekBar_ProgressScaleResource, 0) ?: 0).toInt()
        if (mProgressScaleResource != 0) {
            // 放大时直接更换背景
            mProgressOriginDrawable = progressDrawable;
            mProgressScaleDrawable = resources.getDrawable(mProgressScaleResource)
        }

        attr?.recycle()
        mPointPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        mPointPaint.color = keyPointColor ?: Color.BLACK
        mPointPaint.strokeWidth = 5f
        mPointPaint.style = Paint.Style.FILL

        mTimelinePointPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        mTimelinePointPaint.color = 0xe5ffffff.toInt()
        mTimelinePointPaint.style = Paint.Style.FILL
        mOriginalMinHeight = getMinHeightCompat()
        mOriginalMaxHeight = getMaxHeightCompat()

        super.setOnSeekBarChangeListener(mInterSeekBarChangeListener)
    }

    open fun setThumbOriginDrawable(drawable: Drawable) {
        mThumbOriginDrawable = drawable
        thumb = drawable
        invalidate()
    }


    fun setProgressScaleHeight(height: Int) {
        mProgressScaleHeight = height
    }

    fun setOriginalMinMaxHeight(height: Int) {
        mOriginalMinHeight = height
        mOriginalMaxHeight = height
    }

    fun setProgressOriginDrawable(drawable: Drawable) {
        mProgressOriginDrawable = drawable
        progressDrawable = drawable
        invalidate()
    }

    fun updatePointColor(@ColorInt pointColor: Int) {
        mPointPaint.color = pointColor
        invalidate()
    }

    fun setProgressScaleDrawable(drawable: Drawable) {
        mProgressScaleDrawable = drawable
        invalidate()
    }

    fun setBigThumbDrawable(drawable: Drawable?) {
        mBigThumbDrawable = drawable
        if (drawable != null) {
            mBigThumbOffset = drawable.intrinsicWidth / 2

            mOriginThumbScaleDrawable = mThumbScaleDrawable

            mThumbScaleDrawable = null
            thumb = null

            setupOverlayDrawing()
            invalidate()
        } else {
            clearOverlay()

            mThumbScaleDrawable = mOriginThumbScaleDrawable
            thumb = mThumbOriginDrawable
        }
    }

    fun setProgressScaleDrawable(resId: Int) {
        if (resId != 0) {
            // 放大时直接更换背景
            mProgressOriginDrawable = progressDrawable;
            mProgressScaleDrawable = resources.getDrawable(resId)
        }
    }

    fun setThumbScaleDrawable(resId: Int) {
        if (resId != 0) {
            // 放大时直接更换thumb，不走原来的scale逻辑
            mThumbScaleDrawable = resources.getDrawable(resId)
            mThumbOriginDrawable = thumb
        }
    }




    private fun supportScale(): Boolean {
        return mOriginalMinHeight > 0 && mOriginalMaxHeight > 0 && (mProgressScaleHeight > 0 ||
                thumbNeedScale())
    }

    fun setThumbScaleHeight(height: Int) {
        mThumbScaleHeight = height
    }

    fun setThumbScaleWidth(width: Int) {
        mThumbScaleWidth = width
    }

    private fun thumbNeedScale(): Boolean {
        return mThumbScaleHeight > 0 && mThumbScaleWidth > 0
    }

    private fun getMinHeightCompat(): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            minHeight
        } else {
            var minHeight = -1
            try {
                minHeight = FieldUtils.readField(this, "mMinHeight", true) as? Int ?: -1
            } catch (e: Exception) {
                Logger.e(e)
            }
            minHeight
        }
    }

    private fun getMaxHeightCompat(): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            maxHeight
        } else {
            var maxHeight = -1
            try {
                maxHeight = FieldUtils.readField(this, "mMaxHeight", true) as? Int ?: -1
            } catch (e: Exception) {
                Logger.e(e)
            }
            maxHeight
        }
    }

    private fun setMinHeightCompat(minHeight: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            this.minHeight = minHeight
        } else {
            FieldUtils.writeField(this, "mMinHeight", minHeight)
        }
    }

    private fun setMaxHeightCompat(maxHeight: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            this.maxHeight = maxHeight
        } else {
            FieldUtils.writeField(this, "mMaxHeight", maxHeight)
        }
    }

    override fun setOnSeekBarChangeListener(l: OnSeekBarChangeListener?) {
        mOnSeekBarChangeListener = l
    }

    override fun setProgress(progress: Int) {
        if (mIsDragging) {
            return
        }
        super.setProgress(progress)
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        Logger.d(TAG, "${event?.action}")
        when (event?.action) {
            MotionEvent.ACTION_OUTSIDE ->  {
                Logger.d(TAG, "dispatchTouchEvent ACTION_OUTSIDE")
                mIOnHandsUpListener?.onHandsUp()
                cancelWithDelay()
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                Logger.d(TAG, "dispatchTouchEvent ACTION_UP")
                mIOnHandsUpListener?.onHandsUp()
                cancelWithDelay()
            }
            else -> {}
        }
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val handled = super.onTouchEvent(event)
        var needScale = false
        when (event?.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                if (isCanSeek) {
                    needScale = handled
                }
            }
            MotionEvent.ACTION_OUTSIDE ->  {
                Logger.d(TAG, "onTouchEvent ACTION_OUTSIDE")
                mIOnHandsUpListener?.onHandsUp()
                cancelWithDelay()
                needScale = false
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                Logger.d(TAG, "onTouchEvent ACTION_UP")
                mIOnHandsUpListener?.onHandsUp()
                cancelWithDelay()
                needScale = false
            }
            else -> {}
        }

        if (mTargetNeedScale != needScale) {
            mTargetNeedScale = needScale
            HandlerManager.removeCallbacks(cancelScale)
            if (mTargetNeedScale || mRestoreTimeMS <= 0) {
                mNeedScale = mTargetNeedScale
                Logger.d(TAG, "mTargetNeedScale = TRUE")
            } else {
                Logger.d(TAG, "mTargetNeedScale = FALSE")
                cancelWithDelay()
            }
        }

        Logger.d(TAG, "onTouchEvent handled = $handled")
        return handled
    }

    fun setRestoreTimeMS(restoreTime: Long) {
        mRestoreTimeMS = restoreTime
    }

    private val cancelScale = Runnable {
        mNeedScale = false
    }

    fun cancelWithDelay() {
        HandlerManager.removeCallbacks(cancelScale)
        HandlerManager.postOnUIThreadDelay(cancelScale, mRestoreTimeMS)
    }


    override fun setCanSeek(isCanSeek: Boolean) {
        super.setCanSeek(isCanSeek)
        if (!isCanSeek) {
            mNeedScale = false
        }
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility != View.VISIBLE) {
            Logger.d(TAG, "onVisibilityChanged")
            HandlerManager.removeCallbacks(cancelScale)
            mNeedScale = false
            // 当view不可见时清理overlay
            clearOverlay()
        } else {
            // 当view变为可见时重新设置overlay
            if (mBigThumbDrawable != null) {
                setupOverlayDrawing()
            }
        }
    }

    override fun onDetachedFromWindow() {
//        HandlerManager.onTagDestroy(this)
        Logger.d(TAG, "onDetachedFromWindow")
        HandlerManager.removeCallbacks(cancelScale)
        mNeedScale = false
        // 清理overlay
        clearOverlay()
        super.onDetachedFromWindow()
    }

    public fun cancelScale() {
        mNeedScale = false
    }

    fun cancelScaleAndRefresh() {
        mNeedScale = false
        updateScaleState()
    }

    private fun updateScaleState() {
        if (!supportScale()) {
            return
        }
        (parent as? ViewGroup)?.also {
            if (enableScaleAnimation) TransitionManager.beginDelayedTransition(it, ChangeBounds())
        }

        Log.d(TAG, "updateScaleState, mNeedScale: " + mNeedScale + ", mProgressScaleHeight: " + mProgressScaleHeight + ", mOriginalMaxHeight: " + mOriginalMaxHeight)

        if (mNeedScale && mProgressScaleHeight > 0) {
            setMinHeightCompat(mProgressScaleHeight)
            setMaxHeightCompat(mProgressScaleHeight)
        } else {
            setMinHeightCompat(mOriginalMinHeight)
            setMaxHeightCompat(mOriginalMaxHeight)
        }
        if (mNeedScale && mThumbScaleDrawable != null) {
            thumb = mThumbScaleDrawable
        } else if (!mNeedScale && mThumbScaleDrawable != null) {
            thumb = mThumbOriginDrawable
        } else if (!mNeedScale && thumbNeedScale()) {
            thumb?.let {
                updateThumbSize(it.intrinsicHeight, it.intrinsicWidth)
            }
        }

        if (mNeedScale && mProgressScaleDrawable != null) {
            progressDrawable = mProgressScaleDrawable
        } else if (!mNeedScale && mProgressScaleDrawable != null) {
            progressDrawable = mProgressOriginDrawable
        }

        requestLayout()
    }

    override fun onDraw(canvas: Canvas) {
        // thumb的bounds在AbsSeekBar里面会被改，所以得在每次绘制的时候修改，保证生效
        if (mNeedScale && thumbNeedScale() && mThumbScaleDrawable == null) {
            updateThumbSize(mThumbScaleHeight, mThumbScaleWidth)
        }
        
//        // 如果使用大拇指drawable，可能需要扩展绘制区域
//        if (mBigThumbDrawable != null) {
//            // 保存当前的剪切区域
//            val saveCount = canvas.save()
//            // 扩展绘制区域，允许在padding区域绘制
//            canvas.clipRect(0, 0, width, height)
//            super.onDraw(canvas)
//            canvas.restoreToCount(saveCount)
//        } else {
            super.onDraw(canvas)
//        }
        val contentHeight = (height - paddingTop - paddingBottom).toFloat()
        if (mKeyPoints.size() != 0) {
            for (i in 0 until mKeyPoints.size()) {
                val point = mKeyPoints.valueAt(i)
                if (point != null) {
                    canvas.drawCircle(point.position.toFloat(), paddingTop + contentHeight / 2f, mKeyPointRadius, mPointPaint)
                }
            }
        }
        if (getTimelineCardPoints().size() > 0 && enableTimelinePoints) {
            for (i in 0 until getTimelineCardPoints().size()) {
                val point = getTimelineCardPoints().valueAt(i)
                if (point != null) {
                    canvas.drawCircle(
                        point.position.toFloat(),
                        paddingTop + contentHeight / 2f,
                        if (mNeedScale) mTimelineCardPointScaledRadius else mTimelineCardPointRadius,
                        mTimelinePointPaint
                    )
                }
            }
        }

        // 绘制大拇指(如果设置了的话)
        drawBigThumb(canvas)
    }

    private fun updateThumbSize(targetHeight: Int, targetWidth: Int) {
        if (targetHeight <= 0 || targetWidth <= 0) {
            return
        }
        thumb?.let {
            val originalBounds = it.bounds
            if (originalBounds.height() != targetHeight ||
                originalBounds.width() != targetWidth
            ) {
                val centerX = (originalBounds.left + originalBounds.right) / 2
                val left = centerX - targetWidth / 2
                val right = centerX + targetWidth / 2
                val centerY = (originalBounds.top + originalBounds.bottom) / 2
                val top = centerY - targetHeight / 2
                val bottom = centerY + targetHeight / 2
                it.setBounds(left, top, right, bottom)
            }
        }
    }

    private fun validWidth(): Int {
        return width - paddingStart - paddingEnd
    }

    fun setKeyPoints(points: List<KeyPoint>) {
        val action =  {
            for (p in points) {
                p.position = (validWidth() * p.progress).toInt() + paddingStart + mKeyPointRadius.toInt()
                mKeyPoints.put(p.id, p)
            }
            postInvalidate()
        }
        if (validWidth() <= 0) {
            post { action() }
        } else {
            action()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        if (mKeyPoints != null) {
            val validWidth = validWidth()
            mKeyPoints.forEach { key, value ->
                value.position = (validWidth * value.progress).toInt() + paddingStart + mKeyPointRadius.toInt()
            }
            postInvalidate()
        }
        
        // 尺寸变化时更新overlay位置
        if (mBigThumbDrawable != null) {
            updateOverlayPosition()
        }
    }

    fun getKeyPoints() = mKeyPoints

    private fun getTimelineCardPoints(): LongSparseArray<KeyPoint> {
        return if (mNeedScale) mScaledTimelineCardPoints else mTimelineCardPoints
    }

    fun setPlayTimelineCardPoints(points: List<KeyPoint>) {
        val runnable = Runnable {
            for (p in points) {
                p.position =
                    (validWidth() * p.progress).toInt() + paddingStart + mTimelineCardPointRadius.toInt()
                mTimelineCardPoints.put(p.id, p)
                val p2 = p.copy(progress = p.progress, id = p.id)
                p2.position =
                    (validWidth() * p.progress).toInt() + paddingStart + mTimelineCardPointScaledRadius.toInt()
                mScaledTimelineCardPoints.put(p2.id, p2)
            }
            postInvalidate()
        }
        if (validWidth() > 0) {
            runnable.run()
        } else {
            post(runnable)
        }
    }

    fun clearPlayTimelineCardPoints(){
        mTimelineCardPoints.clear()
        mScaledTimelineCardPoints.clear()
        postInvalidate()
    }

    fun addKeyPoint(point: KeyPoint) {
        point.position = (validWidth() * point.progress).toInt() + paddingStart + mKeyPointRadius.toInt()
        mKeyPoints.put(point.id, point)
        postInvalidate()
    }

    fun clearKeyPoints() {
        mKeyPoints.clear()
        postInvalidate()
    }

    fun removeKeyPoint(id: Long) {
        mKeyPoints.remove(id)
        postInvalidate()
    }

    fun setOnScaleStateChangeListener(listener: IOnScaleStateChangeListener?) {
        mOnScaleStateChangeListener = listener
    }
    fun setOnUserSeekListener(listener: IOnUserSeekListener){
        mOnUserSeekListener = listener
    }

    private val mInterSeekBarChangeListener = object : OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            mCurrentProgress = progress
            mOnSeekBarChangeListener?.onProgressChanged(seekBar, progress, fromUser)
            if (mNeedScale && fromUser) {
                mOnUserSeekListener?.onSeekTouchProgress(progress)
            }
            // 如果使用大拇指drawable，需要重绘或更新overlay
            if (mBigThumbDrawable != null) {
                // 使用overlay方案直接更新位置
                updateOverlayPosition()
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {
            mIsDragging = true
            mOnSeekBarChangeListener?.onStartTrackingTouch(seekBar)
        }

        override fun onStopTrackingTouch(seekBar: SeekBar?) {
            mIsDragging = false
            mOnSeekBarChangeListener?.onStopTrackingTouch(seekBar)
            if (mNeedScale) {
                mOnUserSeekListener?.onSeekTouchRelease(seekBar?.progress ?: 0)
            }
        }
    }

    data class KeyPoint(
        var progress: Float = 0f,
        var id: Long = 0,
        var position: Int = 0
    )

    interface IOnScaleStateChangeListener {
        fun onScaleStateChanged(scaled: Boolean)
    }

    interface IOnHandsUpListener {
        fun onHandsUp()
    }

    interface IOnUserSeekListener {
        fun onSeekTouchRelease(position: Int)
        fun onSeekTouchProgress(position: Int)
    }


    private fun drawBigThumb(canvas: Canvas) {
        // 统一使用overlay方案，这里不需要绘制
        // overlay会自动处理绘制
    }

    // 设置overlay绘制
    private fun setupOverlayDrawing() {
        clearOverlay()
        mBigThumbDrawable?.let { drawable ->
            mOverlayDrawable = drawable
            // 首次查找并缓存合适的parent
            mCachedSuitableParent = findSuitableParent()
            updateOverlayPosition(true)
        }
    }

    // 清除overlay
    private fun clearOverlay() {
        removeOverlay()
    }

    // 更新overlay位置
    private fun updateOverlayPosition(autoAdd: Boolean = false) {
        val targetParent = mCachedSuitableParent ?: return
        
        // 计算新位置
        val bounds = calculateOverlayBounds(targetParent) ?: return
        
        if (mIsOverlayAdded) {
            // 已添加，直接更新bounds
            updateOverlayBounds(bounds)
        } else {
            // 首次添加
            if (autoAdd) addOverlayWithBounds(bounds, targetParent)
        }
    }
    
    // 计算overlay的bounds
    private fun calculateOverlayBounds(targetParent: ViewGroup): android.graphics.Rect? {
        mOverlayDrawable?.let { drawable ->
            val validWidth = validWidth()
            if (validWidth <= 0 || max <= 0) return null
            
            // 计算当前view相对于目标parent的位置
            val relativePosition = calculateRelativePosition(targetParent)
            
            // 根据progress计算位置
            val progressRatio = mCurrentProgress.toFloat() / max.toFloat()
            val thumbCenterX = paddingStart + (validWidth * progressRatio).toInt()
            
            // 计算与progressDrawable对齐的位置
            val progressDrawable = progressDrawable
            val alignedY = if (progressDrawable != null) {
                val progressBounds = progressDrawable.bounds
                val progressCenterY = progressBounds.top + progressBounds.height() / 2
                relativePosition.y + progressCenterY - drawable.intrinsicHeight / 2
            } else {
                relativePosition.y + (height - drawable.intrinsicHeight) / 2
            }
            
            // 计算drawable位置（相对于目标parent）
            val thumbLeft = relativePosition.x + thumbCenterX - mBigThumbOffset
            val thumbTop = alignedY
            val thumbRight = thumbLeft + drawable.intrinsicWidth
            val thumbBottom = thumbTop + drawable.intrinsicHeight
            
            return android.graphics.Rect(thumbLeft, thumbTop, thumbRight, thumbBottom)
        }
        return null
    }
    
    // 计算当前view相对于指定parent的位置
    private fun calculateRelativePosition(targetParent: ViewGroup): android.graphics.Point {
        var currentView: View = this
        var accumulatedX = 0
        var accumulatedY = 0
        
        // 向上遍历到目标parent，累加相对位置
        while (currentView != targetParent && currentView.parent is ViewGroup) {
            accumulatedX += currentView.left
            accumulatedY += currentView.top
            currentView = currentView.parent as View
        }
        
        Logger.d(TAG, "计算相对位置: 累计x=${accumulatedX}px, 累计y=${accumulatedY}px")
        return android.graphics.Point(accumulatedX, accumulatedY)
    }
    
    // 直接更新overlay的bounds（不移除重新添加）
    private fun updateOverlayBounds(bounds: android.graphics.Rect) {
        if (mIsOverlayAdded) {
            mOverlayDrawable?.setBounds(bounds)
            Logger.d(TAG, "直接更新overlay bounds: $bounds")
        }
    }
    
    // 移除overlay
    private fun removeOverlay() {
        if (mIsOverlayAdded) {
            mOverlayDrawable?.let { drawable ->
                mTargetParent?.overlay?.remove(drawable)
            }
            mIsOverlayAdded = false
            mTargetParent = null
        }
    }
    
    // 添加overlay
    private fun addOverlayWithBounds(bounds: android.graphics.Rect, targetParent: ViewGroup) {
        mOverlayDrawable?.let { drawable ->
            // 设置drawable的bounds
            drawable.setBounds(bounds)
            
            // 添加到目标parent的overlay
            targetParent.overlay.add(drawable)
            
            // 记录状态
            mTargetParent = targetParent
            mIsOverlayAdded = true
            Logger.d(TAG, "添加overlay到parent: ${targetParent.javaClass.simpleName}")
        }
    }
    
    // 寻找合适的parent，要求顶部偏移大于100px
    private fun findSuitableParent(): ViewGroup? {
        val minTopOffset = 100 // 最小顶部偏移(dp)
        val minTopOffsetPx = (minTopOffset * context.resources.displayMetrics.density).toInt()
        
        var currentView: View = this
        var currentParent = parent as? ViewGroup
        var accumulatedTop = 0 // 累计的top偏移
        var level = 0
        
        Logger.d(TAG, "开始寻找合适的parent，最小偏移: ${minTopOffsetPx}px")
        
        while (currentParent != null && level < 10) { // 限制最大查找层级
            // 累加当前view相对于其parent的top位置
            accumulatedTop += currentView.top
            
            Logger.d(TAG, "检查parent level=$level, 累计top=${accumulatedTop}px, 当前view.top=${currentView.top}px, className=${currentParent.javaClass.simpleName}")
            
            // 如果累计的top偏移大于阈值，则使用这个parent
            if (accumulatedTop >= minTopOffsetPx) {
                Logger.d(TAG, "找到合适的parent level=$level, 累计top=${accumulatedTop}px")
                return currentParent
            }
            
            // 继续向上查找
            currentView = currentParent
            currentParent = currentParent.parent as? ViewGroup
            level++
        }
        
        Logger.d(TAG, "未找到合适的parent，累计top=${accumulatedTop}px，使用直接parent")
        // 如果没找到合适的，返回直接parent
        return parent as? ViewGroup
    }
}