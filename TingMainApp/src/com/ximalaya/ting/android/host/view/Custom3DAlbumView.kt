package com.ximalaya.ting.android.host.view

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.util.extension.dp

/**
 * 3D专辑封面视图
 * 包含AlbumCoverLayoutView和阴影效果，整体一起旋转
 */
class Custom3DAlbumView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var coverRootView: ViewGroup
    private lateinit var albumCoverLayout: AlbumCoverLayoutView
    private lateinit var ivFgLeftShadow: ImageView
    private lateinit var ivFgBottomShadow: ImageView
    private lateinit var ivBgLeftShadow: ImageView
    private lateinit var ivBgBottomShadow: ImageView

    // 3D旋转角度
    private var rotationY: Float = 7.5f

    // X轴倾斜角度
    private var rotationX: Float = 0f

    private var imageHeight = 93.dp

    init {
        clipChildren = false
        setupLayout()
        initializeViews()
        setAlbumCoverHeight(imageHeight.toFloat())
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 尺寸变化时重新应用3D旋转
        if (w > 0 && h > 0 && (oldw != w || oldh != h)) {
            apply3DRotation()
        }
    }

    /**
     * 设置布局方向
     */
    private fun setupLayout() {
        orientation = VERTICAL
    }

    /**
     * 初始化视图组件
     */
    private fun initializeViews() {
        LayoutInflater.from(context).inflate(R.layout.host_view_custom_3d_album, this, true)
        coverRootView = findViewById(R.id.host_cl_cover_view)
        albumCoverLayout = findViewById(R.id.album_cover_layout)
        ivFgLeftShadow = findViewById(R.id.host_iv_left_fg_shadow)
        ivFgBottomShadow = findViewById(R.id.host_iv_bottom_fg_shadow)
        ivBgLeftShadow = findViewById(R.id.host_iv_left_bg_shadow)
        ivBgBottomShadow = findViewById(R.id.host_iv_bottom_bg_shadow)

        val fgParams = ivFgBottomShadow.layoutParams
        fgParams.width = imageHeight
        ivFgBottomShadow.layoutParams = fgParams

        // 阴影上窄下宽  需要多加一点 根据像素等比换算
        val scale = 0.18f
        val bgParam = ivBgBottomShadow.layoutParams
        bgParam.width = (imageHeight * (1 + scale)).toInt()
        ivBgBottomShadow.layoutParams = bgParam
    }

    /**
     * 应用3D旋转效果到整个容器
     */
    private fun apply3DRotation() {
        coverRootView.pivotX = 0f
        // 对整个容器应用旋转，包括阴影
        coverRootView.rotationY = this.rotationY
        coverRootView.rotationX = this.rotationX

        // 启用硬件加速并设置抗锯齿
        setLayerType(LAYER_TYPE_HARDWARE, Paint().apply {
            isAntiAlias = true
        })
    }

    /**
     * 设置3D旋转角度
     */
    fun setCustomRotationY(angle: Float) {
        this.rotationY = angle
        apply3DRotation()
    }

    /**
     * 设置X轴倾斜角度
     */
    fun setCustomRotationX(angle: Float) {
        this.rotationX = angle
        apply3DRotation()
    }

    /**
     * 设置AlbumCoverLayoutView高度
     */
    private fun setAlbumCoverHeight(height: Float) {
        albumCoverLayout.updateSize(height)
        apply3DRotation()
    }

    /**
     * 设置封面图片
     */
    fun setAlbumCover(coverUrl: String, displayCallback: ImageManager.DisplayCallback? = null) {
        albumCoverLayout.setAlbumCover(
            "$coverUrl!url_rewrite=0",
            imageHeight,
            imageHeight
        ) { lastUrl, bitmap ->
            displayCallback?.onCompleteDisplay(lastUrl, bitmap)
        }
    }

    /**
     * 获取AlbumCoverLayoutView实例
     */
    fun getAlbumCoverLayout(): AlbumCoverLayoutView {
        return albumCoverLayout
    }

    fun getWholeViewWidth(): Float {
        return albumCoverLayout.getWholeViewWidth()
    }

    fun getCoverSize(): Float {
        return albumCoverLayout.getCoverSize()
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            // 清理资源
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}