package com.ximalaya.ting.android.host.view;

import android.animation.Animator;
import android.content.Context;
import android.graphics.Paint;
import android.util.AttributeSet;

import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.view.ViewCompat;

import com.airbnb.lottie.LottieListener;
import com.airbnb.lottie.LottieProperty;
import com.airbnb.lottie.SimpleColorFilter;
import com.airbnb.lottie.model.KeyPath;
import com.airbnb.lottie.value.LottieValueCallback;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.reflect.FieldUtils;
import com.ximalaya.ting.android.framework.view.LottieAnimationViewCompat;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmutil.Logger;


/**
 * Created by le.xin on 2018/9/18.
 * 因为LottieAnimationView 在隐藏状态下也会执行动画,所以尽量使用这个来替换 ,尽量也不要直接使用autoPlay
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17097256298
 */
public class XmLottieAnimationView extends LottieAnimationViewCompat {

    private static final String TAG = XmLottieAnimationView.class.getSimpleName();
    private static boolean IS_DEBUG = ConstantsOpenSdk.isDebug;
    private boolean forceStop = false;

    public XmLottieAnimationView(Context context) {
        super(context);
        init();
    }

    public XmLottieAnimationView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public XmLottieAnimationView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        addAnimatorListener(new XmAnimatorListener());
    }

    @Override
    public void setLayerType(int layerType, @Nullable Paint paint) {
        try {
            super.setLayerType(layerType,paint);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setForegroundColorRes(@ColorRes int color) {
        setForegroundColor(
                AppCompatResources.getColorStateList(getContext(), color).getDefaultColor()
        );
    }

    public void setForegroundColor(@ColorInt int color) {
        addValueCallback(
                new KeyPath("**"), LottieProperty.COLOR_FILTER,
                new LottieValueCallback<>(new SimpleColorFilter(color))
        );
    }

    public void clearForegroundColor() {
        addValueCallback(
                new KeyPath("**"), LottieProperty.COLOR_FILTER,
                new LottieValueCallback<>(null)
        );
    }

    /**
     * 主要是为了解决attach然后很快又detach的情况。
     * 由于需要先加载动画资源，没有立即开始播放，detach的时候还没开始播放，所以没有取消动画，等动画资源加载完之后又开始播放，导致一直在播放。
     *
     */
    private void stopIfDetached() {
        if (!ViewCompat.isAttachedToWindow(this) || !isShown() || forceStop) {
            cancelAnimation();
        }
    }

    // 打印相关信息，方便排查lottie动画没有及时停止的问题。
    private void dumpInfo(String functionName) {
        if (IS_DEBUG) {
            String idName = null;
            try {
                idName = getResources().getResourceName(getId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            String animationName = getAnimationName();
            Logger.i(TAG, functionName + " id: " + idName + ", animationName: " + animationName);
        }
    }

    private String getAnimationName() {
        try {
            return (String) FieldUtils.readField(this, "animationName");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private class XmAnimatorListener implements Animator.AnimatorListener {
        @Override
        public void onAnimationStart(Animator animation) {
            dumpInfo("onAnimationStart");
        }

        @Override
        public void onAnimationEnd(Animator animation) {
        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {
            stopIfDetached();
            dumpInfo("onAnimationRepeat");
        }
    }

    public void setForceStop(boolean forceStop) {
        this.forceStop = forceStop;
    }
}
