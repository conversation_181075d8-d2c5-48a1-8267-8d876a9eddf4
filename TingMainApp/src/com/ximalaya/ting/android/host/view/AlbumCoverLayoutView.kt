package com.ximalaya.ting.android.host.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.extension.px2dp
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew

/**
 * Created by felix.chen on 7/7/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
class AlbumCoverLayoutView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var mCoverSize: Float = 0f
    // 封面圆角视图
    private var mCoverCornerView: CornerRelativeLayout? = null
    // 封面标签圆角视图 在部分手机上设置同一个圆角显示有问题  所以单独设置一个裁剪布局
    private var mTagCornerView: CornerRelativeLayout? = null
    var mCoverImage: ImageView? = null
    private var mAlbumTagImage: ImageView? = null
    private var mIvActivityTag: ImageView? = null
    private var mAdMarkImage: ImageView? = null
    private var mIvRightBg: ImageView? = null

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.AlbumCoverLayout)
        mCoverSize =
            typedArray.getDimensionPixelSize(R.styleable.AlbumCoverLayout_albumCoverSize, 0)
                .toFloat()
        val cdDrawable = typedArray.getResourceId(
            R.styleable.AlbumCoverLayout_albumCdRes,R.drawable.host_album_cover_roght_bg
        )
        typedArray.recycle()
        LayoutInflater.from(context)
            .inflate(R.layout.host_view_album_cover, this, true)

        mIvRightBg = findViewById(R.id.host_iv_cover_right_bg)
        mIvRightBg!!.setImageResource(cdDrawable)
        mCoverImage = findViewById(R.id.host_iv_item_cover)
        mAlbumTagImage = findViewById(R.id.host_iv_album_cover_tag)
        mIvActivityTag = findViewById(R.id.host_iv_activity_tag)
        mAdMarkImage = findViewById(R.id.host_iv_ad_mark)

        mCoverCornerView= findViewById(R.id.host_crl_item_cover)
        mTagCornerView= findViewById(R.id.host_crl_tag_view)

        updateSize(mCoverSize)
    }

    /**
     * 更新圆角大小
     *
     * radius:单位px
     */
    fun updateCornerSize(radius: Float) {
        if (radius < 0) {
            return
        }
        mCoverCornerView?.setCornerRadius(radius)
        var result = radius - 1
        if (result < 0) {
            result = 0f
        }
        mTagCornerView?.setCornerRadius(result)
    }

    fun updateSize(coverSize: Float) {
        updateSizeAndRequestLayout(coverSize)
    }

    /**
     * 因为历史原因 重写了这个方法替代updateSize了
     */
    fun updateSizeAndRequestLayout(coverSize: Float) {
        mCoverSize = coverSize
        val clCoverView = findViewById<CornerRelativeLayout>(R.id.host_crl_item_cover)
        val coverLayoutParams = clCoverView.layoutParams
        coverLayoutParams.width = mCoverSize.toInt()
        coverLayoutParams.height = mCoverSize.toInt()
        clCoverView.layoutParams = coverLayoutParams

        val coverBgLayoutParams = mIvRightBg!!.layoutParams
        coverBgLayoutParams.width = (mCoverSize * 56.0 / 70).toInt()
        coverBgLayoutParams.height = mCoverSize.toInt()
        (coverBgLayoutParams as ConstraintLayout.LayoutParams).marginStart =
            (mCoverSize * 23.0 / 70).toInt()
        mIvRightBg!!.layoutParams = coverBgLayoutParams
        val albumTagLayoutParams = mAlbumTagImage?.layoutParams
        albumTagLayoutParams?.height = (mCoverSize * 16.0 / 70).toInt()

        autoSizeTagView()
    }

    fun getWholeViewWidthInDp(): Int {
        return getWholeViewWidth().px2dp
    }

    fun getWholeViewWidth(): Float {
        return mCoverSize * 79 / 70
    }

    fun getCoverSize(): Float {
        return mCoverSize
    }

    fun calcCoverSizeFromLayoutWidth(width: Int): Float {
        return calcCoverSizeFromLayoutWidth(width.toFloat())
    }

    fun calcCoverSizeFromLayoutWidth(width: Float): Float {
        return width * 70 * 1f / 79
    }

    fun setAlbumCover(coverUrl: String) {
        setAlbumCover(coverUrl, 80, 80)
    }

    fun setAlbumCover(
        coverUrl: String,
        width: Int,
        height: Int,
        displayCallback: ImageManager.DisplayCallback?
    ) {
        ImageManager.from(BaseApplication.getMyApplicationContext())
            .displayImageNotIncludeDownloadCacheSizeInDp(
                mCoverImage,
                coverUrl,
                com.ximalaya.ting.android.host.R.drawable.host_default_album,
                com.ximalaya.ting.android.host.R.drawable.host_default_album,
                width,
                height
            ) { lastUrl, bitmap ->
                displayCallback?.onCompleteDisplay(lastUrl, bitmap)
            }
    }

    fun setAlbumCover(
        coverUrl: String,
        width: Int,
        height: Int,
        resId: Int,
        errId: Int,
        displayCallback: ImageManager.DisplayCallback?
    ) {
        ImageManager.from(BaseApplication.getMyApplicationContext())
            .displayImageNotIncludeDownloadCacheSizeInDp(
                mCoverImage,
                coverUrl,
                resId,
                errId,
                width,
                height
            ) { lastUrl, bitmap ->
                displayCallback?.onCompleteDisplay(lastUrl, bitmap)
            }
    }

    fun setAlbumCover(coverUrl: String, width: Int, height: Int) {
        setAlbumCover(coverUrl, width, height, null)
    }

    fun setImageResource(resId: Int) {
        mCoverImage?.setImageResource(resId)
    }

    fun setImageBitmap(bitmap: Bitmap?) {
        if (bitmap == null) {
            mCoverImage?.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_default_album)
        } else {
            mCoverImage?.setImageBitmap(bitmap)
        }
    }

    fun getDrawable(): Drawable? {
        return mCoverImage?.drawable
    }

    fun setAlbumTag(coverUrl: String?) {
        if (coverUrl.isNullOrEmpty()) {
            mAlbumTagImage?.visibility = GONE
            return
        }
        mAlbumTagImage?.visibility = VISIBLE
        AlbumTagUtilNew.getInstance().loadImageForChannelNew(mAlbumTagImage, coverUrl)
    }

    fun setAdMark(visible: Boolean, adMarkUrl: String?) {
        if (!visible || adMarkUrl.isNullOrEmpty()) {
            mAdMarkImage?.visibility = GONE
            return
        }
        ImageManager.from(BaseApplication.getMyApplicationContext())
            .displayImage(mAdMarkImage, adMarkUrl, -1)
        mAdMarkImage?.visibility = VISIBLE
    }

    fun setActivityTag(activityTagUrl: String?) {
        if (activityTagUrl.isNullOrEmpty()) {
            mIvActivityTag?.visibility = GONE
        } else {
            mIvActivityTag?.visibility = VISIBLE
            ImageManager.from(BaseApplication.getMyApplicationContext())
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    mIvActivityTag,
                    activityTagUrl,
                    -1,
                    -1,
                    80,
                    80
                )
        }
    }


    fun setPodCastTag(activityTagUrl: String?, viewHeightInDp: Int) {
        if (activityTagUrl.isNullOrEmpty()) {
            mIvActivityTag?.visibility = GONE
        } else {
            mIvActivityTag?.layoutParams?.let {
                if (viewHeightInDp > 0) {
                    it.height = viewHeightInDp.dp
                }
            }
            mIvActivityTag?.visibility = VISIBLE
            ImageManager.from(BaseApplication.getMyApplicationContext())
                .displayImageNotIncludeDownloadCache(
                    mIvActivityTag,
                    activityTagUrl,
                    -1
                )
        }
    }

    /**
     * 设置播客标签，自动计算大小  按照封面比例计算大小
     */
    fun setPodCastTagAutoSize(tagUrl: String?, scaleSize: Float = 0.27f, maxSizeInDp: Int = 22.dp) {
        if (mIvActivityTag == null) {
            return
        }
        if (tagUrl.isNullOrEmpty()) {
            mIvActivityTag!!.visibility = GONE
        } else {
            this.scaleSize = scaleSize
            this.maxSizeInDp = maxSizeInDp
            autoSizeTagView()
            mIvActivityTag!!.visibility = VISIBLE
            ImageManager.from(BaseApplication.getMyApplicationContext()).downloadBitmap(tagUrl
            ) { _, bitmap ->
                if (bitmap != null && !bitmap.isRecycled) {
                    val bitmapWidth = bitmap.width
                    val bitmapHeight = bitmap.height
                    val height = mIvActivityTag!!.layoutParams.height
                    val width = height * bitmapWidth.toFloat() / bitmapHeight

                    ImageManager.from(BaseApplication.getMyApplicationContext())
                        .displayImageNotIncludeDownloadCacheSizeInDp(
                            mIvActivityTag,
                            tagUrl,
                            -1, width.dp, height.dp
                        )
                }
            }
        }
    }

    private var scaleSize = 0f
    private var maxSizeInDp = 0

    private fun autoSizeTagView() {
        if (scaleSize <= 0 || maxSizeInDp <= 0 || mCoverSize <= 0) {
            return
        }
        mIvActivityTag?.run {
            val params = layoutParams
            params.height = (mCoverSize * scaleSize).toInt().coerceAtMost(maxSizeInDp)
            layoutParams = params
        }
    }
}