package com.ximalaya.ting.android.ad.manager;

import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_LOADING;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_BAIDU;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_CSJ;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_CSJ_TEMPLATE;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_GDT;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_GDT_WELCOME_SCREEN;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_JAD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.THIRD_AD_TYPE_FEED;
import static com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants.IAdPositionId.AD_PLAY_PAGE_LIVE_MC_AD;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.NoLoadThirdAd;
import com.ximalaya.ting.android.ad.parallelload.ParallelLoadSDKState;
import com.ximalaya.ting.android.ad.parallelload.ThirdSDKPreLoadManager;
import com.ximalaya.ting.android.ad.splashad.ISplashAdProvider;
import com.ximalaya.ting.android.adsdk.aggregationsdk.mediation.IMediationInfo;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.ad.AdAdapterUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.BaseAdSDKManager;
import com.ximalaya.ting.android.host.manager.ad.CSJAdManager;
import com.ximalaya.ting.android.host.manager.ad.CacheDspAdManager;
import com.ximalaya.ting.android.host.manager.ad.GDTSDKManager;
import com.ximalaya.ting.android.host.manager.ad.ILoadNativeAdHandler;
import com.ximalaya.ting.android.host.manager.ad.IThirdNativeAdLoadCallback;
import com.ximalaya.ting.android.host.manager.ad.NullObject;
import com.ximalaya.ting.android.host.manager.ad.ThirdAdLoadParams;
import com.ximalaya.ting.android.host.manager.ad.inventory.AdInventoryCollectManager;
import com.ximalaya.ting.android.host.manager.ad.jad.JadYunSDKManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.trace.SoundPatchAdTrace;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by le.xin on 2020/4/15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class ThirdAdLoadManager {

    // 加载开屏
    public static void loadSplashThirdAd(List<Advertis> advertisList,
                                         @NonNull ThirdAdLoadParams thirdAdLoadParams,
                                         IThirdNativeAdLoadCallback callback,
                                         @Nullable ISplashAdProvider adHandler) {
        baseLoadThirdAd(adHandler, advertisList, thirdAdLoadParams, callback);
    }

    // 加载普通位置的广告
    public static void loadThirdAd(List<Advertis> advertisList,
                                   @NonNull ThirdAdLoadParams thirdAdLoadParams,
                                   IThirdNativeAdLoadCallback callback) {
        baseLoadThirdAd(null, advertisList, thirdAdLoadParams, callback);
    }

    private static void baseLoadThirdAd(@Nullable ISplashAdProvider adHandler,
                                        List<Advertis> advertisList,
                                        ThirdAdLoadParams thirdAdLoadParams,
                                        IThirdNativeAdLoadCallback callbackTemp) {
        if (callbackTemp == null || thirdAdLoadParams == null) {
            return;
        }

        IThirdNativeAdLoadCallback callback;
        if(adHandler == null) {
            callback = new ThirdNativeAdLoadCallbackWrapperForNormal(callbackTemp, thirdAdLoadParams);
        } else {
            callback = new ThirdNativeAdLoadCallbackWrapperForWelcome(callbackTemp);
        }

        if (TextUtils.isEmpty(thirdAdLoadParams.getPositionName()) || ToolUtil.isEmptyCollects(advertisList)) {
            callback.loadThirdNativeAdError(null, false);
            return;
        }

        if (adHandler == null && RtbAdLoadManager.needJDRtb(advertisList)) {
            Logger.d("soundPatchRtb", "触发竞价逻辑");
            RtbAdLoadManager.loadNativeAd(advertisList, callback, thirdAdLoadParams);
            return;
        }

        if (adHandler == null && HostCommonRtbSortUtil.commonRtbAdEnable(advertisList)) {
            Logger.d("----msg_rtb", "触发竞价逻辑");
            HostCommonRtbAdLoadManager.loadAd(advertisList, callback, thirdAdLoadParams);
            return;
        }

        // 设置广告数据请求时间
        setAdvertisRequestTime(advertisList, thirdAdLoadParams.getRequestTime());

        List<Advertis> subAdvertisList = new ArrayList<>();

        // 第一个运营广告
        Advertis mFirstOperationAdvertis = null;
        for (Advertis advertis : advertisList) {
            if (advertis == null) {
                continue;
            }

            if (!AdManager.isThirdAd(advertis)) {
                mFirstOperationAdvertis = advertis;
                break;
            }
        }

        boolean hasCachedAd = false;
        for (Advertis advertis : advertisList) {
            //数据异常，继续遍历
            if (advertis == null) {
                continue;
            }

            //顺延下去，如果出现不是三方广告的就进行终止，后续的广告不在获取
            if (!AdManager.isThirdAd(advertis)) {
                break;
            }

            //当前都是第三方的广告，判断是否存在缓存
            //遇到存在缓存的广告，后续的广告不在发起请求
            AbstractThirdAd cacheThirdAd = CacheDspAdManager.getInstance().
                    returnEffectiveAdNoPop(advertis, AdManager.getDspPositionId(advertis, thirdAdLoadParams.getPositionName()));

            if (!hasCachedAd) {
                subAdvertisList.add(advertis);
            } else {
                // 只要发现有缓存的,后面的sdk不请求
                AdStateReportManager.getInstance().onShowFail(advertis,
                        AdStateReportManager.STATUS_BEFORE_HAS_CACHE, 0, thirdAdLoadParams.getPositionName());
            }

            if (cacheThirdAd != null) {
                hasCachedAd = true;
            }
        }

        //没有三方广告数据
        if (subAdvertisList.size() == 0) {
            callback.loadThirdNativeAdError(mFirstOperationAdvertis, false);
            return;
        }

        Advertis firstAd = subAdvertisList.get(0);
        AbstractThirdAd firstThirdAd = CacheDspAdManager.getInstance().
                returnEffectiveAdNoPop(firstAd, AdManager.getDspPositionId(firstAd, thirdAdLoadParams.getPositionName()));

        if (firstThirdAd != null) {
            Log.d("preRequestSDKAd", " sdk请求之前 匹配上了缓存广告 需要使用  它 id ===  " + firstThirdAd.getDspPositionId());
            //移除缓存
            CacheDspAdManager.getInstance().usedAdAndRemoveFromCache(firstThirdAd);
            //存在缓存，直接进行返回
            firstAd.setCacheType(1);
            callback.loadThirdNativeAdSuccess(firstAd, firstThirdAd);
            checkPreLoadCache();
            return;
        }

        //创建超时和回调成功的记录类
        NativeAdCallBackStatus adStatus = new NativeAdCallBackStatus();
        adStatus.isCallBackFinish = false;
        adStatus.isOverTime = false;
        adStatus.requestTime = System.currentTimeMillis();

        Logger.log("ThirdAdLoadManager : adStatus begin = " + adStatus.hashCode());

        //请求等待队列记录
        Map<Advertis, AbstractThirdAd> returnResult = new HashMap<>();
        Advertis finalMSelfAdvertis = mFirstOperationAdvertis;
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (adStatus.isOverTime && adStatus.isCallBackFinish) {
                    return;
                }
                adStatus.isOverTime = true;

                Logger.log("xinle ThirdAdLoadManager : timeOverRunnable   " + adStatus.isCallBackFinish);

                onTimeOutRecordAllStates(adStatus, returnResult, thirdAdLoadParams.getPositionName());

                if (checkAndShow(subAdvertisList, returnResult, adStatus, thirdAdLoadParams.getRequestTime(), callback,
                        this,
                        thirdAdLoadParams.getPositionName())) {
                    return;
                }

                adStatus.isCallBackFinish = true;
                //超时没有查到有效的广告，直接回调失败，不需要处理缓存操作
                onNotingToShow(returnResult ,thirdAdLoadParams.getRequestTime() ,thirdAdLoadParams.getPositionName());
                callback.loadThirdNativeAdError(finalMSelfAdvertis, true);
            }
        };
        int delayTimeTemp = thirdAdLoadParams.getDspRequestTimeMs() > 0 ?
                thirdAdLoadParams.getDspRequestTimeMs() : AdManager.dspRequestTimeMs();
        if (ConstantsOpenSdk.isDebug) {
            String testTime = ToolUtil.getSystemProperty("debug.main.dsp.timeout", "");
            if (!TextUtils.isEmpty(testTime)) {
                delayTimeTemp = Integer.parseInt(testTime);
            }
        }
        final int delayTime = delayTimeTemp;

        HandlerManager.postOnUIThreadDelay(runnable, delayTime);

        for (Advertis suAd : subAdvertisList) {

            returnResult.put(suAd ,null);
            long startTime = System.currentTimeMillis();
            if (AdManager.isSoundPatchAd(suAd)){
                SoundPatchAdTrace.reportSdkDetailRequestStart(suAd);
            }
            Advertis finalMSelfAdvertis1 = mFirstOperationAdvertis;
            IMultiThirdNativeAdLoadCallback thirdNativeAdLoadCallback = new IMultiThirdNativeAdLoadCallback() {
                @Override
                public void loadThirdNativeAdFinish(AbstractThirdAd thirdAd) {
                    if (System.currentTimeMillis() - adStatus.requestTime > delayTime) {
                        adStatus.isOverTime = true;
                        HandlerManager.removeCallbacks(runnable);
                    }

                    if (AdManager.isSoundPatchAd(suAd)){
                        if (thirdAd != null && !(thirdAd instanceof NoLoadThirdAd)) {
                            SoundPatchAdTrace.reportSdkDetailRequestEnd(suAd,
                                    System.currentTimeMillis() - startTime,
                                    adStatus.isOverTime,
                                    SoundPatchAdTrace.MSG_REQUEST_SUCCESS);
                        } else {
                            SoundPatchAdTrace.reportSdkDetailRequestEnd(suAd,
                                    System.currentTimeMillis() - startTime,
                                    adStatus.isOverTime,
                                    SoundPatchAdTrace.MSG_REQUEST_NO_AD);
                        }
                    }
//                    Logger.log("xinle ThirdAdLoadManager : 加载三方广告结束 " + suAd + "   " +
//                            adStatus + "   " + "   " + thirdAd + "   " + returnResult.get(suAd));

                    //处理超时或者已经展示成功的请求
                    if (adStatus.isOverTime || adStatus.isCallBackFinish) {
                        if (adStatus.isOverTime) {
                            if (adStatus.isCallBackFinish) {
                                onTimeOutRecordAllStates(adStatus, returnResult, thirdAdLoadParams.getPositionName());
                            } else {
                                runnable.run();
                            }
                        } else if (thirdAd instanceof NoLoadThirdAd) {
                            // 返回了,但是没有物料可以填充
                            AdStateReportManager.getInstance().onShowFail(suAd,
                                    AdStateReportManager.STATUS_SDK_NO_BACK,
                                    thirdAd.getCreateObjectTime() - thirdAdLoadParams.getRequestTime(), thirdAdLoadParams.getPositionName());

                        } else if (thirdAd != null) {
                            // 有返回,没展示
                            AdStateReportManager.getInstance().onShowFail(suAd,
                                    AdStateReportManager.STATUS_MATERIALS_NO_SHOW,
                                    thirdAd.getCreateObjectTime() - thirdAdLoadParams.getRequestTime(),
                                    thirdAdLoadParams.getPositionName());
                        }

                        //当前已经超时了，直接进入缓存池，进行数据上报
                        //当前是失败回调，不做处理，上报错误
                        //当前是成功回调，直接放入缓存
                        if (thirdAd != null && !(thirdAd instanceof NoLoadThirdAd) && !thirdAd.isCached()) {
                            CacheDspAdManager.getInstance().push(thirdAd);
                        }
                        return;
                    }

                    //还没有回调数据，临时保存
                    returnResult.put(suAd, thirdAd);

                    //进行一次回调检测，需要等待操作
                    if (checkAndShow(subAdvertisList, returnResult, adStatus, thirdAdLoadParams.getRequestTime(),
                            callback, runnable, thirdAdLoadParams.getPositionName())) {
                        return;
                    }

                    //subAdvertisList中全部都为ErrorLoadThirdAd才会执行到此处
                    // 说明在2秒内，所有的并发全部都失败了，直接进行失败回调，不在进行等待
                    adStatus.isCallBackFinish = true;
                    HandlerManager.removeCallbacks(runnable);
                    onNotingToShow(returnResult, thirdAdLoadParams.getRequestTime(), thirdAdLoadParams.getPositionName());
                    callback.loadThirdNativeAdError(finalMSelfAdvertis1, false);
                    //全部失败，不需要处理缓存
                    Logger.log("xinle ThirdAdLoadManager : 广告全部失败了");
                }
            };

            // 如果发现广告一样则直接替换掉之前的callback,方便复用之前的回调逻辑
            boolean hasPreloadAd = false;
            Map<Advertis, ParallelLoadSDKState> parallelLoadSDKStateMap = thirdAdLoadParams.getParallelLoadSDKStateMap();
            if(parallelLoadSDKStateMap != null) {
                Set<Map.Entry<Advertis, ParallelLoadSDKState>> entries = parallelLoadSDKStateMap.entrySet();

                for (Map.Entry<Advertis, ParallelLoadSDKState> next : entries) {
                    if (next != null && next.getKey() != null) {
                        if (ThirdSDKPreLoadManager.isSameThirdSDK(next.getKey(), suAd) && next.getValue() != null) {
                            MultiThirdNativeAdLoadCallbackWrapper adLoadCallback = next.getValue().getAdLoadCallback();
                            hasPreloadAd = true;
                            // 只有确认sdk没有返回的时候才更新回调
                            if (adLoadCallback != null && adLoadCallback.getAbstractThirdAd() == null) {
                                // 更新回调实现
                                adLoadCallback.updateMultiThirdAd(thirdNativeAdLoadCallback, null);
                                // 移除之前的状态保存
                                parallelLoadSDKStateMap.remove(next.getKey());
                                break;
                            } else {
                                if(adLoadCallback != null) {
                                    thirdNativeAdLoadCallback.loadThirdNativeAdFinish(adLoadCallback.getAbstractThirdAd());
                                } else {
                                    thirdNativeAdLoadCallback.loadThirdNativeAdFinish(CacheDspAdManager.getInstance().returnEffectiveAdNoPop(next.getKey(), AdManager.getDspPositionId(next.getKey(), AD_POSITION_NAME_LOADING)));
                                }
                                break;
                            }
                        }
                    }
                }
            }

            if(!hasPreloadAd) {
                loadOneCountNativeAd(adHandler, thirdAdLoadParams, suAd,
                        thirdNativeAdLoadCallback);
            }
        }
    }

    private static void setAdvertisRequestTime(List<Advertis> advertisList, long requestTime) {
        if(ToolUtil.isEmptyCollects(advertisList)) {
            return;
        }

        for (Advertis advertis : advertisList) {
            //数据异常，继续遍历
            if (advertis == null) {
                continue;
            }
            advertis.setClientRequestTime(requestTime);
        }
    }

    private static void onNotingToShow(Map<Advertis, AbstractThirdAd> returnResult ,
                                       long requestTime , String positionName) {
        if(ToolUtil.isEmptyMap(returnResult)) {
            return;
        }

        Set<Map.Entry<Advertis, AbstractThirdAd>> entries = returnResult.entrySet();

        for (Map.Entry<Advertis, AbstractThirdAd> entry : entries) {
            AbstractThirdAd thirdAd = entry.getValue();

            if (thirdAd instanceof NoLoadThirdAd) {
                // 返回了,但是没有物料可以填充
                AdStateReportManager.getInstance().onShowFail(entry.getKey(),
                        AdStateReportManager.STATUS_SDK_NO_BACK,
                        thirdAd.getCreateObjectTime() - requestTime, positionName);

            } else if(thirdAd != null) {
                // 有返回,没展示
                AdStateReportManager.getInstance().onShowFail(entry.getKey(),
                        AdStateReportManager.STATUS_MATERIALS_NO_SHOW,
                        thirdAd.getCreateObjectTime() - requestTime,
                        positionName);
            }
        }
    }

    // 超时了进行上报没有返回的
    private static void onTimeOutRecordAllStates(NativeAdCallBackStatus adStatus,
                                                 Map<Advertis, AbstractThirdAd> returnResult,
                                                 String positionName) {

        if (adStatus.isRecordTimeout || ToolUtil.isEmptyMap(returnResult)) {
            return;
        }

        Set<Map.Entry<Advertis, AbstractThirdAd>> entries = returnResult.entrySet();

        // 超时了没有返回
        for (Map.Entry<Advertis, AbstractThirdAd> entry : entries) {
            if (entry.getValue() == null) {
                AdStateReportManager.getInstance().onShowFail(entry.getKey(),
                        AdStateReportManager.STATUS_SDK_NO_BACK_IN_TIME, 0, positionName);
            }
        }

        adStatus.isRecordTimeout = true;

    }

    // 超时了进行上报没有返回的 (并行预加载)
    private static void onTimeOutRecordAllStatesForParalle(Advertis advertis,
                                                 String positionName) {

        AdStateReportManager.getInstance().onShowFail(advertis,
                AdStateReportManager.STATUS_SDK_NO_BACK_IN_TIME, 0, positionName);
    }

    private static void onTimeOutRecordPreloadSDK(Advertis advertis,
                                                  String positionName) {

        AdStateReportManager.getInstance().onShowFail(advertis,
                AdStateReportManager.STATUS_SDK_NO_BACK_IN_TIME, 0, positionName);
    }

    private static boolean checkAndShow(List<Advertis> subAdvertisList,
                                        Map<Advertis, AbstractThirdAd> returnResult,
                                        NativeAdCallBackStatus adStatus,
                                        long requestTime,
                                        IThirdNativeAdLoadCallback callback,
                                        Runnable timeOutCheckRunnable, String positionName) {
        if (adStatus.isCallBackFinish) {
            return true;
        }

        // 上报超时还没返回的
        // 上报:规定时间内返回了但是没有数据的
        // 上报返回了,并且有数据,但是有更高优先级的

        //超时检测，按照顺序查找到第一个有效的广告进行返回，不再进行等待操作
        for (Advertis currentAd : subAdvertisList) {
            AbstractThirdAd currentAdResult = returnResult.get(currentAd);

//            Logger.log("xinle ThirdAdLoadManager : checkAndShow " + currentAdResult + "   " + currentAd);

            if (currentAdResult == null) {
                if (adStatus.isOverTime) {
                    //没有获取到值，该广告还没返回，继续遍历
                    continue;
                } else {
                    //获取的为空，说明当前还没回调成功或者失败，需要等待其他广告的返回，终止检测，直接return，后续不执行
                    return true;
                }
            }

            if (currentAdResult instanceof NoLoadThirdAd) {
                //当前广告已经发生错误，继续往后遍历
            } else {
                Logger.log("xinle ThirdAdLoadManager : 三方广告返回了要开始展示了(是否展示成功要看下界面和上报) " + currentAd + "   adStatus.hashCode=" + adStatus.hashCode());

                //当前的广告已经返回了，直接进行回调
                adStatus.isCallBackFinish = true;
                HandlerManager.removeCallbacks(timeOutCheckRunnable);
                if (currentAdResult.isCached()) {
                    currentAd.setCacheType(2);
                    //回调成功，回调的广告来自缓存，需要清除缓存
                    Log.d("preRequestSDKAd", " sdk请求之前 匹配上了缓存广告 需要使用  它 id ===  " + currentAdResult.getDspPositionId());
                    CacheDspAdManager.getInstance().usedAdAndRemoveFromCache(currentAdResult);
                    checkPreLoadCache();
                }
                callback.loadThirdNativeAdSuccess(currentAd, currentAdResult);

                // SDK返回成功回调
                AdStateReportManager.getInstance().onSDKBackSuccess(currentAd, requestTime, positionName);

                returnResult.remove(currentAd);

                Set<Map.Entry<Advertis, AbstractThirdAd>> entries = returnResult.entrySet();

                for (Map.Entry<Advertis, AbstractThirdAd> entry : entries) {
                    AbstractThirdAd value = entry.getValue();
                    if (value != null) {
                        if (value instanceof NoLoadThirdAd) {
                            // 返回了,但是没有物料可以填充
                            AdStateReportManager.getInstance().onShowFail(entry.getKey(),
                                    AdStateReportManager.STATUS_SDK_NO_BACK,
                                    value.getCreateObjectTime() - requestTime,
                                    positionName);
                        } else {
                            // 有返回,没展示
                            AdStateReportManager.getInstance().onShowFail(entry.getKey(),
                                    AdStateReportManager.STATUS_MATERIALS_NO_SHOW,
                                    value.getCreateObjectTime() - requestTime,
                                    positionName);
                        }
                    }


                    if (value != null && !(value instanceof NoLoadThirdAd) && !value.isCached()) {
                        CacheDspAdManager.getInstance().push(value);
                    }
                }

                return true;
            }
        }
        return false;
    }

    /**
     * 加载单条广告
     */
    private static void loadOneCountNativeAd(@Nullable ISplashAdProvider adHandler,
                                            ThirdAdLoadParams thirdAdLoadParams,
                                            Advertis advertis,
                                            IMultiThirdNativeAdLoadCallback callback) {

        if (advertis == null) {
            if (callback != null) {
                callback.loadThirdNativeAdFinish(new NoLoadThirdAd(NullObject.NULL_OBJECT,
                        AdManager.getDspPositionId(advertis, thirdAdLoadParams.getPositionName())));
            }
            return;
        }

        String dspId = AdManager.getDspPositionId(advertis, thirdAdLoadParams.getPositionName());

        //使用缓存数据，并不清理缓存，真正使用的时候才能进行pop
        AbstractThirdAd abstractThirdAd = CacheDspAdManager.getInstance().
                returnEffectiveAdNoPop(advertis, dspId);
        if (abstractThirdAd != null) {
            callback.loadThirdNativeAdFinish(abstractThirdAd);
            return;
        }

        if (advertis.getAdtype() == AD_SOURCE_GDT) {
            GDTSDKManager.getInstance().loadNativeGdt(advertis, thirdAdLoadParams, getDefaultLoadNativeHandler(callback, dspId),
                    dspId);
        } else if (advertis.getAdtype() == AD_SOURCE_CSJ || advertis.getAdtype() == AD_SOURCE_CSJ_TEMPLATE) {
            if (TextUtils.equals(AD_POSITION_NAME_LOADING, thirdAdLoadParams.getPositionName())) {
                if (callback != null) {
                    callback.loadThirdNativeAdFinish(new NoLoadThirdAd(NullObject.NULL_OBJECT, dspId));
                }
                return;
            } else if (advertis.getAdtype() == AD_SOURCE_CSJ_TEMPLATE && AD_PLAY_PAGE_LIVE_MC_AD.equals(String.valueOf(advertis.getPositionId()))) {
                CSJAdManager.getInstance().loadFullScreenVideoAdAd(MainApplication.getMyApplicationContext()
                        , thirdAdLoadParams, dspId, advertis, getDefaultLoadNativeHandler(callback,
                                dspId));
            } else {
                if (advertis.getThirdAdType() == THIRD_AD_TYPE_FEED) {
                    // 普通信息流广告
                    CSJAdManager.getInstance().loadCSJNativeAd(MainApplication.getMyApplicationContext()
                            , thirdAdLoadParams, dspId, advertis, getDefaultLoadNativeHandler(callback,
                                    dspId));
                } else {
                    // draw信息流广告
                    CSJAdManager.getInstance().loadCSJDrawNativeAd(MainApplication.getMyApplicationContext()
                            , thirdAdLoadParams, dspId, advertis, getDefaultLoadNativeHandler(callback,
                                    dspId));
                }
            }
        } else if (advertis.getAdtype() == AD_SOURCE_GDT_WELCOME_SCREEN) {
            GDTSDKManager.getInstance().loadGdtScreen(advertis, dspId, adHandler,
                    getDefaultLoadNativeHandler(callback, dspId));
        } else if (advertis.getAdtype() == AD_SOURCE_JAD) {
            if (TextUtils.equals(AD_POSITION_NAME_LOADING, thirdAdLoadParams.getPositionName())) {
//                //  京东 sdk 开屏广告位主站暂时未实现， 先处理为回调跳过
//                Logger.i("-----msg_jad", " ---- 京东sdk 开屏广告位主站暂时未实现， 先处理为回调跳过");
//                if (callback != null) {
//                    callback.loadThirdNativeAdFinish(
//                            new NoLoadThirdAd(NullObject.NULL_OBJECT, dspId));
//                }
                Logger.v("-----msg_jad", " ---- 开始加载京东开屏");
                JadYunSDKManager.getInstance().loadJadSDKScreen(advertis, dspId, adHandler,
                        getDefaultLoadNativeHandler(callback, dspId));
            } else {
                // 京东如果样式是竖版的，就加载开屏自渲染
                if (advertis != null
                        && (advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE || advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE_SHAKE)) {
                    JadYunSDKManager.getInstance().loadJadSplashNativeAd(MainApplication.getMyApplicationContext(),
                            advertis, dspId, getDefaultLoadNativeHandler(callback, dspId));
                } else {
                    JadYunSDKManager.getInstance().loadJadNativeAd(MainApplication.getMyApplicationContext(),
                            advertis, dspId, getDefaultLoadNativeHandler(callback, dspId));
                }
            }
        } else if (advertis.getAdtype() == AD_SOURCE_BAIDU) {
            AdAdapterUtil.obtainSDKManagerAsync(IMediationInfo.BAIDU, new AdAdapterUtil.OnSDKManagerReadyCallback() {
                @Override
                public void onReady(BaseAdSDKManager sdkManager) {
                    if (sdkManager == null) {
                        return;
                    }
                    sdkManager.loadNativeAd(MainApplication.getMyApplicationContext(), advertis, thirdAdLoadParams, getDefaultLoadNativeHandler(callback, dspId), dspId);
                }
            });
        } else {
            ToolUtil.throwIllegalNoLogicException();

            if (callback != null) {
                callback.loadThirdNativeAdFinish(new NoLoadThirdAd(NullObject.NULL_OBJECT, dspId));
            }
        }
    }

    public static ILoadNativeAdHandler getDefaultLoadNativeHandler(
            IMultiThirdNativeAdLoadCallback callback, String dspId) {
        return new ILoadNativeAdHandler() {
            @Override
            public void loadNextGDT() {
                if (callback != null) {
                    callback.loadThirdNativeAdFinish(
                            new NoLoadThirdAd(NullObject.NULL_OBJECT, dspId));
                }
            }

            @Override
            public void loadNativeAds(AbstractThirdAd adDataRef) {
                Logger.w("-------msg_jad", " -- -- -- --- - -- - - -- -- loadNativeAds = " + adDataRef);
                if (adDataRef != null) {
                    Logger.w("-------msg_jad", " -- -- -- --- - -- - - -- -- price  = " + adDataRef.getRtbPrice());
                }
                if (callback != null) {
                    callback.loadThirdNativeAdFinish(adDataRef);
                }
            }
        };
    }


    /**
     * 广告回调状态记录
     */
    private static class NativeAdCallBackStatus {
        //是否已经回调完成
        boolean isCallBackFinish = false;
        //是否超时
        boolean isOverTime = false;

        long requestTime;
        // 是否已经上报了超时的状态
        boolean isRecordTimeout;

        @NonNull
        @Override
        public String toString() {
            return "NativeAdCallBackStatus{" +
                    "isCallBackFinish=" + isCallBackFinish +
                    ", isOverTime=" + isOverTime +
                    ", requestTime=" + requestTime +
                    ", isRecordTimeout=" + isRecordTimeout +
                    '}';
        }
    }

    public interface IMultiThirdNativeAdLoadCallback {

        //广告加载完成，成功或者失败被回调
        void loadThirdNativeAdFinish(AbstractThirdAd thirdAd);
    }

    public interface ITimeoutCallback {
        void onTimeOut();
    }

    public static class MultiThirdNativeAdLoadCallbackWrapper implements IMultiThirdNativeAdLoadCallback {
        private AbstractThirdAd abstractThirdAd;
        @Nullable
        private IMultiThirdNativeAdLoadCallback multiThirdNativeAdLoadCallback;
        private IMultiThirdNativeAdLoadCallback updateMultiThirdNativeAdLoadCallBack;
        private ITimeoutCallback onTimeOutCallBack;

        public MultiThirdNativeAdLoadCallbackWrapper(@Nullable IMultiThirdNativeAdLoadCallback multiThirdNativeAdLoadCallback) {
            this.multiThirdNativeAdLoadCallback = multiThirdNativeAdLoadCallback;
        }

        public void updateMultiThirdAd(IMultiThirdNativeAdLoadCallback adLoadCallback,
                                       @Nullable ITimeoutCallback onTimeOutCallBack) {
            this.updateMultiThirdNativeAdLoadCallBack = adLoadCallback;
            this.onTimeOutCallBack = onTimeOutCallBack;
        }

        @Override
        public void loadThirdNativeAdFinish(AbstractThirdAd thirdAd) {
            abstractThirdAd = thirdAd;

            if(multiThirdNativeAdLoadCallback != null) {
                multiThirdNativeAdLoadCallback.loadThirdNativeAdFinish(thirdAd);
            }

            if(updateMultiThirdNativeAdLoadCallBack != null) {
                updateMultiThirdNativeAdLoadCallBack.loadThirdNativeAdFinish(thirdAd);
            }
        }

        public AbstractThirdAd getAbstractThirdAd() {
            return abstractThirdAd;
        }

        @Nullable
        public ITimeoutCallback getOnTimeOutCallBack() {
            return onTimeOutCallBack;
        }
    }

    private static class ThirdNativeAdLoadCallbackWrapperForWelcome implements IThirdNativeAdLoadCallback {
        private IThirdNativeAdLoadCallback mCallback;

        public ThirdNativeAdLoadCallbackWrapperForWelcome(IThirdNativeAdLoadCallback callback) {
            mCallback = callback;
        }

        @Override
        public void loadThirdNativeAdSuccess(Advertis backAdvertis,
                                             @NonNull AbstractThirdAd thirdAd) {
            if(mCallback != null) {
                mCallback.loadThirdNativeAdSuccess(backAdvertis, thirdAd);
            }
        }

        @Override
        public void loadThirdNativeAdError(@Nullable Advertis backAdvertis, boolean timeout) {
            // 顺延的是虚拟广告位将虚拟广告位的设置为null,并做上报
            if(AdInventoryCollectManager.isVirtualAd(backAdvertis)) {
                AdInventoryCollectManager.adInventoryCollect(backAdvertis,
                        AdInventoryCollectManager.createAdInventoryInfoByAdvertis(backAdvertis));
                backAdvertis = null;
            }
            if(mCallback != null) {
                mCallback.loadThirdNativeAdError(backAdvertis, timeout);
            }
        }

    }

    // 并行预加载SDK广告
    @Nullable
    public static Map<Advertis, ParallelLoadSDKState> parallelRequestSDKAd(List<Advertis> advertisList,
                                            View skipView,
                                      @NonNull ThirdAdLoadParams thirdAdLoadParams,
                                      @Nullable ISplashAdProvider splashAdProvider) {

        if (ToolUtil.isEmptyCollects(advertisList)) {
            return null;
        }

        List<Advertis> realRequestAdvertisList = new ArrayList<>();
        Map<Advertis, ParallelLoadSDKState> returnResult = new ConcurrentHashMap<>();

        for (Advertis advertis : advertisList) {
            AbstractThirdAd cacheThirdAd = CacheDspAdManager.getInstance().
                    returnEffectiveAdNoPop(advertis, AdManager.getDspPositionId(advertis, thirdAdLoadParams.getPositionName()));

            if(cacheThirdAd == null) {
                realRequestAdvertisList.add(advertis);
            } else {
                ThirdSDKPreLoadManager.log("并行预加载SDK 有缓存 " + advertis);
                returnResult.put(advertis, new ParallelLoadSDKState(new MultiThirdNativeAdLoadCallbackWrapper(null)));
            }
        }

        if(ToolUtil.isEmptyCollects(realRequestAdvertisList)) {
            return null;
        }

        ThirdSDKPreLoadManager.log("并行预加载SDK广告开始 并行加载的广告列表 " + realRequestAdvertisList);

        for (Advertis suAd : realRequestAdvertisList) {
            long requestTime = System.currentTimeMillis();

            RquestStatues requestStatus = new RquestStatues();
            //请求等待队列记录
            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    if(requestStatus.isTimeOut) {
                        return;
                    }
                    requestStatus.isTimeOut = true;

                    ThirdSDKPreLoadManager.log("并行预加载SDK 超时了  " + advertisList);

                    ParallelLoadSDKState parallelLoadSDKState = returnResult.get(suAd);
                    if(parallelLoadSDKState != null
                            && parallelLoadSDKState.getAdLoadCallback() != null
                            && parallelLoadSDKState.getAdLoadCallback().getOnTimeOutCallBack() != null) {
                        parallelLoadSDKState.getAdLoadCallback().getOnTimeOutCallBack().onTimeOut();
                    }

                    onTimeOutRecordAllStatesForParalle(suAd, thirdAdLoadParams.getPositionName());
                }
            };

            final int delayTime = thirdAdLoadParams.getDspRequestTimeMs() > 0 ?
                    thirdAdLoadParams.getDspRequestTimeMs() : AdManager.dspRequestTimeMs();
            HandlerManager.postOnUIThreadDelay(runnable, delayTime);

            MultiThirdNativeAdLoadCallbackWrapper callback =
                    new MultiThirdNativeAdLoadCallbackWrapper(new IMultiThirdNativeAdLoadCallback() {
                @Override
                public void loadThirdNativeAdFinish(AbstractThirdAd thirdAd) {
                    ThirdSDKPreLoadManager.log("并行预加载SDK返回结果了 " +  "  thirdAd=" + (thirdAd instanceof NoLoadThirdAd ? "无数据" : "有数据") + suAd);

                    // 并行预加载的都先放到缓存池中
                    if (thirdAd != null && !(thirdAd instanceof NoLoadThirdAd) && !thirdAd.isCached()) {
                        CacheDspAdManager.getInstance().push(thirdAd);
                    }

                    HandlerManager.removeCallbacks(runnable);

                    // 超时直接返回
                    if (System.currentTimeMillis() - requestTime > delayTime || requestStatus.isTimeOut) {
                        if(!requestStatus.isTimeOut) {
                            runnable.run();
                            requestStatus.isTimeOut = true;
                        }

                        return;
                    }

                    if (thirdAd instanceof NoLoadThirdAd) {
                        // 返回了,但是没有物料可以填充
                        AdStateReportManager.getInstance().onShowFail(suAd,
                                AdStateReportManager.STATUS_SDK_NO_BACK,
                                thirdAd.getCreateObjectTime() - thirdAdLoadParams.getRequestTime(), thirdAdLoadParams.getPositionName());
                    }
                }
            });

            returnResult.put(suAd ,new ParallelLoadSDKState(callback));
            loadOneCountNativeAd(splashAdProvider, thirdAdLoadParams, suAd, callback);
        }

        return returnResult;
    }

    public static synchronized void checkPreLoadCache() {
        if (!CacheDspAdManager.getInstance().getSoundPatchPreloadEnable()) {
            return;
        }
        if (!CacheDspAdManager.getInstance().hasEffectivePreloadAd()) {
            PreloadSDkAdManager.getInstance().requestPreSoundPatch(true);
        }
    }

    @Nullable
    public static synchronized void preRequestSDKAd(List<Advertis> advertisList, int index, DspReportMode mode) {
        if (ToolUtil.isEmptyCollects(advertisList)) {
            Log.d("preRequestSDKAd", "-----准备下载sdk  发现 advertisList === ----------空集合 ");
            return;
        }

        if (CacheDspAdManager.getInstance().hasEffectivePreloadAd()) {
            Log.d("preRequestSDKAd", "-----缓存里面已经存在有效数据---------- ");
            SoundPatchAdTrace.reportPreSoundPatchCutOff();
            return;
        }

        LinkedList<Advertis> realRequestAdvertisList = new LinkedList<>();

        for (Advertis advertis : advertisList) {
            AbstractThirdAd cacheThirdAd = CacheDspAdManager.getInstance().
                    returnEffectiveAdNoPopForPreCache(advertis, AdManager.getDspPositionId(advertis, AppConstants.AD_POSITION_NAME_SOUND_PATCH));

            if (cacheThirdAd == null) {
                realRequestAdvertisList.add(advertis);
            } else {
                Log.d("preRequestSDKAd", " ---pre预加载SDK 发现这个广告在缓存  它的id是" + advertis.getAdid());
            }
        }

        if (ToolUtil.isEmptyCollects(realRequestAdvertisList)) {
            Log.d("preRequestSDKAd", "-------本次没有需要预请求的sdk物料 -------");
            return;
        }

        Log.d("preRequestSDKAd", "----pre预加载SDK广告开始 并行加载的广告列表 " + realRequestAdvertisList);
        //创建超时和回调成功的记录类
        NativeAdCallBackStatus adStatus = new NativeAdCallBackStatus();
        adStatus.isOverTime = false;
        adStatus.requestTime = System.currentTimeMillis();
        Advertis suAd = realRequestAdvertisList.pop();

        ThirdAdLoadParams thirdAdLoadParams =
                new ThirdAdLoadParams(AppConstants.AD_POSITION_NAME_SOUND_PATCH,
                        System.currentTimeMillis());
        // 请求视频
        if (AdManager.isVideoAd(suAd)) {
            thirdAdLoadParams.setVideoParams(true,
                    IAdConstants.IVideoAdConfig.MIN_VIDEO_DURATION,
                    ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                            CConstants.Group_ad.ITEM_SOUND_PATCH_VIDEO_MAX_DURATION_TIME,
                            IAdConstants.IVideoAdConfig.MAX_VIDEO_DURATION));
        }
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (adStatus.isOverTime) {
                    return;
                }
                adStatus.isOverTime = true;

                Log.d("preRequestSDKAd", "pre预加载SDK 超时了  它的id是 " + suAd.getAdid());
                //如果缓存池没有，继续顺延加载
                if (!CacheDspAdManager.getInstance().hasEffectivePreloadAd()) {
                    Log.d("preRequestSDKAd", "ThirdAdLoadManager : 加载三方广告超时，  adid是 " + suAd.getAdid() + " ---------  " +
                            adStatus + " 准备顺延加载");
                    preRequestSDKAd(realRequestAdvertisList, index + 1, mode);
                }

                onTimeOutRecordPreloadSDK(suAd, thirdAdLoadParams.getPositionName());
            }
        };

        final int delayTime = Math.max(AdManager.dspRequestTimeMs(), 5000);
        HandlerManager.postOnUIThreadDelay(runnable, delayTime);

        IMultiThirdNativeAdLoadCallback thirdNativeAdLoadCallback = new IMultiThirdNativeAdLoadCallback() {
            @Override
            public void loadThirdNativeAdFinish(AbstractThirdAd thirdAd) {
                if (System.currentTimeMillis() - adStatus.requestTime > delayTime) {
                    adStatus.isOverTime = true;
                    HandlerManager.removeCallbacks(runnable);
                    Log.d("preRequestSDKAd", "pre预加载SDK 超时了  " + suAd + " --- delay time === " + delayTime);
                    return;
                }

                if (thirdAd != null && !(thirdAd instanceof NoLoadThirdAd) && !thirdAd.isCached()) {
                    Log.d("preRequestSDKAd", "ThirdAdLoadManager : 加载三方广告结束  adid是 " + suAd.getAdid() + " ---------  " +
                            adStatus + " ----thirdAd -----  " + " --------------  " + thirdAd + "放入缓存");
                    if (thirdAd.getAdvertis() != null) {
                        //设置广告标识是预请求
                        thirdAd.getAdvertis().setCacheSource(3);
                    }
                    CacheDspAdManager.getInstance().push(thirdAd);
                    CacheDspAdManager.getInstance().pushPreloadAD(thirdAd);

                    //还没有回调数据，临时保存
//                    returnResult.put(suAd, thirdAd);
                    HandlerManager.removeCallbacks(runnable);
                    mode.success = 1;
                    mode.cacheIdx = index;
                    SoundPatchAdTrace.reportDspPreloadFinish(suAd,mode);
                } else {
                    Log.d("preRequestSDKAd", "ThirdAdLoadManager : 加载三方广告失败，  adid是 " + suAd.getAdid() + " ---------  " +
                            adStatus + " 准备顺延加载");
                    HandlerManager.removeCallbacks(runnable);
                    if (!CacheDspAdManager.getInstance().hasEffectivePreloadAd()) {
                        if (!ToolUtil.isEmptyCollects(realRequestAdvertisList)) {
                            preRequestSDKAd(realRequestAdvertisList, index + 1, mode);
                        } else {
                            mode.cacheIdx = 0;
                            mode.success = 0;
                            SoundPatchAdTrace.reportDspPreloadFinish(suAd,mode);
                        }
                    }
                }
            }
        };

        loadOneCountNativeAd(null, thirdAdLoadParams, suAd,
                thirdNativeAdLoadCallback);
    }

    @Keep
    public static class DspReportMode {
        public int count;
        public String adid;
        public int success;
        public int cacheIdx;
    }

    static class RquestStatues {
        public boolean isTimeOut;
    }

    public static class ThirdNativeAdLoadCallbackWrapperForNormal implements IThirdNativeAdLoadCallback {

        private IThirdNativeAdLoadCallback mCallback;
        private ThirdAdLoadParams mThirdAdLoadParams;

        public ThirdNativeAdLoadCallbackWrapperForNormal(IThirdNativeAdLoadCallback callback,
                                                         ThirdAdLoadParams thirdAdLoadParams) {
            mCallback = callback;
            mThirdAdLoadParams = thirdAdLoadParams;
        }

        @Override
        public void loadThirdNativeAdSuccess(Advertis backAdvertis,
                                             @NonNull AbstractThirdAd thirdAd) {
            if(mCallback != null) {
                mCallback.loadThirdNativeAdSuccess(backAdvertis, thirdAd);
            }
        }

        @Override
        public void loadThirdNativeAdError(@Nullable Advertis backAdvertis, boolean timeout) {
            // 顺延的是虚拟广告位将虚拟广告位的设置为null,并做上报
            if(AdInventoryCollectManager.isVirtualAd(backAdvertis)) {
                AdInventoryCollectManager.adInventoryCollect(backAdvertis,
                        AdInventoryCollectManager.createAdInventoryInfoByAdvertis(backAdvertis));
                if(mThirdAdLoadParams != null && mThirdAdLoadParams.isVirtualAdRemovedOnDefer()) {
                    backAdvertis = null;
                }
            }
            if(mCallback != null) {
                mCallback.loadThirdNativeAdError(backAdvertis, timeout);
            }
        }
    }
}
