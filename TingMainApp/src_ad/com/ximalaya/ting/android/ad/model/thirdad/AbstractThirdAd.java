package com.ximalaya.ting.android.ad.model.thirdad;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.CallSuper;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.adsdk.proxy.DspAdProxy;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IThirdAdStatueCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import java.util.List;
import java.util.Map;

//import com.bytedance.sdk.openadsdk.core.EmptyView;

/**
 * Created by le.xin on 2020-02-25.
 * 第三方sdk 返回的广告抽象
 * <AUTHOR>
 * @email <EMAIL>
 */
public abstract class AbstractThirdAd<T> implements IAbstractAd {
    public final static int THIRD_AD_GDT_NATIVE = 1;    // 广点通自渲染2.0
    public final static int THIRD_AD_CSJ_SPLASH = 2;    // 穿山甲开屏
    public final static int THIRD_AD_CSJ_NATIVE = 3;    // 穿山甲自渲染
    public final static int THIRD_AD_GDT_SPLASH = 4;    // 广点通模板渲染
    public final static int THIRD_AD_CSJ_REWARD = 5;    // 穿山甲激励视频
    public final static int THIRD_AD_GDT_REWARD = 6;    // 广点通激励视频
    public final static int THIRD_AD_GDT_NATIVE_OLD = 7;    // 广点通自渲染1.0
    public final static int THIRD_AD_CSJ_FULL_SCREEN_VIDEO = 8;    // 穿山甲全屏视频
    public final static int THIRD_AD_CSJ_DRAW_VIDEO = 9;    // 穿山甲Draw视频非模板
    public final static int THIRD_AD_CSJ_EXPRESS_DRAW_VIDEO = 10;    // 穿山甲Draw视频模板
    public final static int THIRD_AD_JAD_NATIVE = 11;    // 京东sdk 自渲染信息流
    public final static int THIRD_AD_JAD_SPLASH = 12;    // 京东sdk 模板渲染开屏
    public final static int THIRD_AD_BAIDU_NATIVE = 13;    // 百度sdk自渲染信息流
    public final static int THIRD_AD_BAIDU_REWARD_TEMP = 14;    // 百度sdk自渲染信息流
    public final static int THIRD_AD_CSJ_FULL_SCREEN_VIDEO_TEMP = 15;    // 穿山甲新插屏模版广告
    public final static int THIRD_AD_XM = 100;    // 喜马拉雅自己的广告

    public final static int APP_STATUS_NO_DOWNLOADED = 1;
    public final static int APP_STATUS_INSTALLED = 2;
    public final static int APP_STATUS_UPDATE = 3;
    public final static int APP_STATUS_DOWNLOADING = 4;
    public final static int APP_STATUS_DOWNLOADED = 5;
    public final static int APP_STATUS_DOWNLOAD_FAIL = 6;
    public final static int APP_STATUS_DOWNLOAD_PAUSED = 7;
    public final static int APP_STATUS_DOWNLOAD_REMOVED = 8;

    public static final int RESULT_TYPE_DEFAULT = -1;
    public static final int RESULT_TYPE_LOAD_ERROR = 0;
    public static final int RESULT_TYPE_LOAD_SUCCESS = 1;
    public static final int RESULT_TYPE_LOAD_NO_AD = 2;
    public static final int RESULT_TYPE_LOAD_OVERTIME = 3;
    public static final int RESULT_TYPE_PRICE_LOW_MIN_BASE_PRICE = 4;

    public static final int RESULT_TYPE_CONFIG_MERGE_NO_AD = 5;

    /**
     * SDK 返回的status，自定义
     * 默认                -10000
     * 广告物料为null       -10001
     * sdk未初始化         -10002
     * 有请求未返回物料     -10003
     *
     * 超过开屏最大请求时长   -99999
     *
     * 请求成功             10000
     *
     * 请求成功，但是出价小于adx设置的底价   20000
     *
     */
    public static final int SDK_CALL_BACK_STATUS_SDK_SUCCESS = 10000;
    public static final int SDK_CALL_BACK_STATUS_SDK_PRICE_LOW_MIN_BASE_PRICE = 20000;
    public static final int SDK_CALL_BACK_STATUS_DEFAULT = -10000;
    public static final int SDK_CALL_BACK_STATUS_NULL_AD_MODEL = -10001;
    public static final int SDK_CALL_BACK_STATUS_SDK_NOT_INIT = -10002;
    public static final int SDK_CALL_BACK_STATUS_SDK_SUCCESS_BUT_NO_CALLBACK = -10003;
    public static final int SDK_CALL_BACK_STATUS_SDK_AD_TYPE_MISMATCH = -10004;
    public static final int SDK_CALL_BACK_STATUS_SDK_DSP_POSITION_ID_EMPTY = -10005;
    public static final int SDK_CALL_BACK_STATUS_SDK_TIME_OUT = -99999;

    public static final String SDK_CALL_BACK_MSG_DEFAULT = "xmly-该物料为adx物料";
    public static final String SDK_CALL_BACK_MSG_SDK_SUCCESS = "xmly-SDK请求成功";
    public static final String SDK_CALL_BACK_MSG_SDK_PRICE_LOW_MIN_BASE_PRICE = "xmly-SDK请求成功并出价, 但是出价小于adx设置的底价";
    public static final String SDK_CALL_BACK_MSG_NULL_AD_MODEL = "xmly-服务端返回物料为null";
    public static final String SDK_CALL_BACK_MSG_SDK_NOT_INIT = "xmly-SDKManager == NULL,sdk可能初始化失败";
    public static final String SDK_CALL_BACK_MSG_SDK_SUCCESS_BUT_NO_CALLBACK = "xmly-SDK请求成功，但是没有物料返回";
    public static final String SDK_CALL_BACK_MSG_SDK_TIME_OUT = "xmly-sdk在规定时间内未返回";
    public static final String SDK_CALL_BACK_MSG_SDK_CONFIG_MERGE_NO_AD= "xmly-configmerge物料到loading时，未找到对应物料";
    public static final String SDK_CALL_BACK_MSG_AD_TYPE_MISMATCH = "xmly-dspAdType类型不匹配";
    public static final String SDK_CALL_BACK_MSG_DSP_POSITION_ID_EMPTY = "xmly-dspPositionId为空";

    private long saveTime;
    private boolean isCached;
    private String dspPositionId;

    @Nullable
    private Advertis mAdvertis;
    protected T thirdAd;
    private long createObjectTime;
    @Nullable
    private VideoParamModel videoParamModel;

    private boolean isRecordonTimeOutNoRecord;
    protected DspAdProxy proxy;

    /*
      -1: adx物料, 不用请求dsp ，
        0: 请求dsp出错，
        1: 成功参与出价,
        2: 请求dsp成功但没有返回广告，
        3. 请求dsp超时
        4. sdk 出价，但小于adx的底价
        5. loading merge config, config 中未有与之匹配的物料
       */
    private int dspResult = RESULT_TYPE_DEFAULT;

    private int sdkCallBackStatus = SDK_CALL_BACK_STATUS_DEFAULT; // sdk返回status码，用于查看请求失败原因

    private String sdkCallbackMsg; // sdk返回error信息，用于查看请求失败原因
    private long startRequestTime; // 开始请求时间， load sdk 时
    private long getResponseTime; // 得到请求结果时间  sdk 回调时
    private long startRtbTime; // 开始处理开屏请求时间（时间点） SplashRtbAdLoadManager.loadSplashAd() 方法调用时
    private boolean isCacheAdToSort; // 是否是取的缓存物料参与竞价排序
    private long cacheAdOriginResponseId; // 缓存物料初始的responseId
    private boolean shakeViewDismiss; //摇一摇view是否已经消失

    public Context getContext() {
        return MainApplication.getMyApplicationContext();
    }

    @Nullable
    public T getAdData() {
        return thirdAd;
    }

    private AbstractThirdAd() {

    }

    @Override
    @Nullable
    public Advertis getAdvertis() {
        return mAdvertis;
    }

    // 从缓存池中获取后需要更新下数据
    public void updateAdvertis(Advertis advertis) {
        mAdvertis = advertis;
    }

    public AbstractThirdAd(@Nullable Advertis advertis, T t , String dspPositionId) {
        this.mAdvertis = advertis;
        thirdAd = t;
        this.dspPositionId = dspPositionId;
        createObjectTime = System.currentTimeMillis();
        if (advertis!=null) {
            DspAdProxy.asyncReport(t, advertis.getResponseId() + "", advertis.getAdPositionId(), advertis.getPositionName(), advertis.getDspPositionId(), advertis.getAdid());
        }
    }


    public String getDspPositionId() {
        return dspPositionId;
    }

    public boolean isCached() {
        return isCached;
    }

    public void setCached(boolean cached) {
        isCached = cached;
    }

    public void setSaveTime(long curTime) {
        saveTime = curTime;
    }

    public long getSaveTime() {
        return saveTime;
    }

    public abstract void negativeFeedback();    // 负反馈

    public void setShakeViewDismiss(boolean dismiss) {
        shakeViewDismiss = dismiss;
    }

    public long getCreateObjectTime() {
        return createObjectTime;
    }

    @Override
    public Map<String, Object> getOtherInfo() {
        return null;
    }

    @Override
    public String getDynamicUrl() {
        return null;
    }

    @Override
    public int getImageMode() {
        return ITEM_VIEW_TYPE_NORMAL;
    }

    @Override
    public String getLogoUrl() {
        return "";
    }

    @Override
    public int getPicHeight() {
        return 0;
    }

    @Override
    public int getPicWidth() {
        return 0;
    }

    @CallSuper
    @Override
    public void bindAdToView(Context context, ViewGroup viewGroup,
                             List<View> clickViews, @Nullable FrameLayout.LayoutParams adLogoParams,
                             @Nullable VideoParamModel videoParamModel,
                             IThirdAdStatueCallBack thirdAdStatueCallBack) {
        if (videoParamModel != null) {
            removeAllMediaView(viewGroup, videoParamModel.getVideoLay(), videoParamModel.getVideoCover(), this);
        }

        this.videoParamModel = videoParamModel;
    }

    private static void removeAllMediaView(ViewGroup viewGroup, RelativeLayout relativeLayout, ImageView cover, AbstractThirdAd abstractThirdAd) {
        if(relativeLayout == null || cover == null) {
            return;
        }

        View gdtMediaLay = relativeLayout.findViewById(R.id.host_gdt_ad_media_view);
        if(gdtMediaLay instanceof ViewGroup) {
            gdtMediaLay.setVisibility(View.INVISIBLE);
            ((ViewGroup) gdtMediaLay).removeAllViews();
        }

        FrameLayout csjMediaLay = relativeLayout.findViewById(R.id.host_csj_ad_media_view);
        if(csjMediaLay != null) {
            csjMediaLay.setVisibility(View.INVISIBLE);
            csjMediaLay.removeAllViews();
        }

        View baiduMediaLay = relativeLayout.findViewById(R.id.host_baidu_ad_media_view);
        if(baiduMediaLay != null) {
            baiduMediaLay.setVisibility(View.INVISIBLE);
            if (baiduMediaLay instanceof ViewGroup) {
                ((ViewGroup) baiduMediaLay).removeAllViews();
            }
        }

        View xmMediaLay = relativeLayout.findViewById(R.id.host_xm_ad_media_view);
        if(xmMediaLay instanceof ViewGroup) {
            xmMediaLay.setVisibility(View.INVISIBLE);
            // 喜马自己的视频不进行回收
            if(!(abstractThirdAd instanceof XmNativeAd)) {
                ((ViewGroup) xmMediaLay).removeAllViews();
            }
        }

        if (viewGroup != null) {
            int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View childAt = viewGroup.getChildAt(i);
                if (childAt != null && childAt.getClass().getName().equals("com.bytedance.sdk.openadsdk.core.EmptyView")) {
                    viewGroup.removeViewAt(i);
                    break;
                }
            }
        }

        cover.setVisibility(View.VISIBLE);
    }

    @Nullable
    protected VideoParamModel getVideoParamModel() {
        return videoParamModel;
    }

    public boolean isRecordonTimeOutNoRecord() {
        return isRecordonTimeOutNoRecord;
    }

    public void setRecordonTimeOutNoRecord(boolean recordonTimeOutNoRecord) {
        isRecordonTimeOutNoRecord = recordonTimeOutNoRecord;
    }

    public void setCacheAdOriginResponseId(long responseId) {
        if (cacheAdOriginResponseId != 0) {
            return;
        }
        this.cacheAdOriginResponseId = responseId;
    }

    interface DownloadImageViewListener{
        void onImgCallBack(Bitmap bitmap);
    }

    public void getImageViewFromUrl(String imageUrl, DownloadImageViewListener listener) {
        if (TextUtils.isEmpty(imageUrl)) {
            return;
        }
        Bitmap cacheBitmap = ImageManager.from(getContext()).getFromCacheAndDisk(imageUrl);
        if (cacheBitmap != null) {
            if (listener != null) {
                listener.onImgCallBack(cacheBitmap);
            }
        } else {
            ImageManager.Options options = new ImageManager.Options();
//            options.targetWidth = imageView.getMeasuredWidth();
            options.bitmapConfig =  Bitmap.Config.ARGB_8888;

            ImageManager.from(getContext()).downloadBitmap(imageUrl, options, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (listener != null) {
                        listener.onImgCallBack(bitmap);
                    }
                }
            });
        }
    }

    public int getDspResult() {
        return dspResult;
    }

    public void setDspResult(int dspResult) {
        this.dspResult = dspResult;
    }

    public long getStartRequestTime() {
        return startRequestTime;
    }

    public void setStartRequestTime(long startRequestTime) {
        this.startRequestTime = startRequestTime;
    }

    public long getGetResponseTime() {
        return getResponseTime;
    }

    public void setGetResponseTime(long getResponseTime) {
        this.getResponseTime = getResponseTime;
    }

    public long getSplashTimeCost() {
        return getResponseTime - startRequestTime;
    }

    /**
     * 单个dsp物料发起请求耗时，准备时长 （startRequestTime - startSplashTime）
     * @return
     */
    public long getPreSplashTimeCost() {
        return startRequestTime - startRtbTime;
    }

    public long getStartRtbTime() {
        return startRtbTime;
    }

    public void setStartRtbTime(long startRtbTime) {
        this.startRtbTime = startRtbTime;
    }

    public int getSdkCallBackStatus() {
        return sdkCallBackStatus;
    }

    public void setSdkCallBackStatus(int sdkCallBackStatus) {
        this.sdkCallBackStatus = sdkCallBackStatus;
    }

    public String getSdkCallbackMsg() {
        if (android.text.TextUtils.isEmpty(sdkCallbackMsg)) {
            if (dspResult == RESULT_TYPE_DEFAULT) {
                return SDK_CALL_BACK_MSG_DEFAULT;
            }
        }
        return sdkCallbackMsg;
    }

    public void setSdkCallbackMsg(String sdkCallbackMsg) {
        this.sdkCallbackMsg = sdkCallbackMsg;
    }

    public boolean isCacheAdToSort() {
        return isCacheAdToSort;
    }

    public void setCacheAdToSort(boolean cacheAdToSort) {
        isCacheAdToSort = cacheAdToSort;
    }

    @Override
    public double getRtbPrice() {
        if (getAdvertis() != null && !getAdvertis().isMobileRtb() && getAdvertis().getPrice() > 0) {
            return getAdvertis().getPrice();
        }
        return -1;
    }
}
