package com.ximalaya.ting.android.ad.model.thirdad;

import androidx.annotation.Nullable;

import com.bytedance.sdk.openadsdk.TTFullScreenVideoAd;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

/**
 * Created by zhao.peng.
 * describe
 * Date: 2025/7/25
 */
public class CSJFullScreenVideoAd extends ThirdAdAdapter<TTFullScreenVideoAd> {

    public CSJFullScreenVideoAd(@Nullable Advertis advertis, TTFullScreenVideoAd fullScreenVideoAd, String dspPositionId){
        super(advertis, fullScreenVideoAd, dspPositionId);
    }

    @Override
    public int getType() {
        return THIRD_AD_CSJ_FULL_SCREEN_VIDEO_TEMP;
    }
}
