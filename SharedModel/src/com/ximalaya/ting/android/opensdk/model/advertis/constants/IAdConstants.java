package com.ximalaya.ting.android.opensdk.model.advertis.constants;

/**
 * Created by le.xin on 2020-03-10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface IAdConstants {

    int LIVE_ADID = 50;  // 直播广告adid
    /**
     * 点击跳转类型 1."DIRECT"：直接跳转 2."TIPS"：展示tips跳转 3. "WELFARE" 展示回答结果
     */
    interface IJumpType {
        String DIRECT = "DIRECT";
        String TIPS = "TIPS";
        String WELFARE = "WELFARE";
    }

    interface ICategoryId {
        // 首页categoryId
        int RECOMMEND_CATEGORY_ID = -2;
        // vip categoryId
        int VIP_CATEGORY_ID = -8;
        // 付费 categoryId
        int PAY_CATEGORY_ID = 33;
        // 本地听
        int LOCAL_CATEGORY_ID = 37;
        // 直播
        int LIVE_AUDIO_CATEGORY_ID = -3;
        // 广播
        int RADIO_CONTENT_CATEGORY_ID = -4;
        // 播客
        int PODCAST_CATEGORY_ID = 95;
    }

    /**
     * 主播竞价广告位
     */
    interface IAlbumAdInfoPoisitionName {
        String HOME_GUESS_YOU_LIKE = "home_guess_you_like";
        String HOME_RECOMMEND_FOR_YOU = "home_recommend_for_you";
        String GUESS_YOU_LIKE = "guess_you_like";
        String ALBUM_DETAIL_RELATIVE_RECOMMEND = "album_detail_relative_recommend";
        String TRACK_DETAIL_RELATIVE_RECOMMEND = "track_detail_relative_recommend";
        String SEARCH_RESULT_RELATIVE_RECOMMEND = "search_result_relative_recommend";
        String HOME_CATEGORY_CARD = "home_category_card";
        String CATEGORY_RECOMMEND = "category_recommend";
        String PAYABLE_RECOMMEND = "payable_recommend";
        String CATEGORY_KEYWORD = "category_keyword";
        String PAYABLE_KEYWORD = "payable_keyword";
    }


    interface IAdClickType {
        // 是否可以点击
        int CLICK_TYPE_CAN_CLICK = 1;
        int CLICK_TYPE_CANNOT_CLICK = 2;

        int CLICK_TYPE_OPEN_WX_APPLETS = 17;    // 打开微信小程序
        int CLICK_TYPE_DOWNLOAD = 18;   // 下载
        int CLICK_TYPE_COLLECT = 23; // 订阅专辑
    }

    /**
     * 1：广点通
     * 2：穿山甲
     * 3：喜马拉雅
     * 4：推啊
     * 5: 京东
     */
    interface SDKType {
        int GDT = 1;
        int CSJ = 2;
        int XM = 3;
        int TY = 4;
        int JAD = 5;
    }
    interface IAdPositionId {
        String RADIO_SOUND_PATCH = "-3";    // 广播页声音贴片
        String TRACK_SOUND_PATCH = "0";    // 声音页贴片
        String PURCHASE_MIDDLE_BOTTOM = "-5"; // 付费专辑

        String RECOMMEND_AD = "-2"; //首页广告

        String LOADING = "1";   // 启动页
        String CATE_LIST = "6"; // 二级分类原生广告
        String CATA_BANNER = "17";  // 分类推荐页底部banner
        String CATA_INDEX_BANNER = "18";    // 分类目录页banner
        String NATIVE_PLAY = "21";  // 播放页原生广告
        String FIND_NATIVE = "28";  // 首页大图广告
        String LOCAL_LIST_NATIVE = "30";    // 本地精选原生
        String LOCAL_BANNER = "31";    // 本地首页底部banner
        String ALBUM_NOTICE = "32"; // 专辑通知原生
        String HOME_MIDDLE = "38";  // 首页中部广告
        String HOME_BOTTOM = "39";  // 首页底部广告
        String PLAY_SKIN = "40";    // 播放页皮肤
        String PLAY_READ = "41";    // 播放页诵读广告
        String LIVE = "42";         // 广播页广告
        String PLAY_LARGE = "70";   // 播放页大图广告
        String BROADCASTER_BANNER = "71";   // 播放页大图广告
        String BROADCAST_NATIVE = "72";   // 广播页原生
        String SHARE_FLOAT = "73";   // 分享浮层
        String OPERATE_FLOAT = "74";   // 播放页更多操作浮层
        String PURCHASE_MIDDLE = "75";  // 付费专辑横幅广告
        String PURCHASE_BOTTOM = "76";  // 付费专辑挂件广告
        String XIAOYA_FLOAT = "77";   // 小雅浮层广告
        String FIND_FLOAT = "78";   // 发现信息流浮层广告
        String WALLET_BANNER = "79";   // 钱包充值
        String PLAY_YELLOW_BAR = "80";   // 播放页小黄条广告
        String BRAND_FEATURE = "81";   // 汽车专题广告
        String WAISTBAND = "82";   // 会员页腰封广告
        String HOME_DROP_DOWN = "83";   // 首页下拉广告
        String LIVE_BANNER = "84";   // 直播页banner
        String UNLOCK_VIDEO = "85"; // 付费解锁
        String INCENTIVE_DURATION = "87" ;// 时长解锁模式
        String GIANT_SCREEN = "104";   // 首页巨幕广告
        String POPUP_NEW = "106";    // 弹窗广告
        String ICON = "109";    // icon广告
        String SEARCH_EGGS = "121"; // 搜索彩蛋
        String COLUMN_SPONSORSHIP = "667";  // 频道栏目冠名套餐
        String COMMENT = "123"; // 评论广告
        String FORWARD_VIDEO = "138"; // 前插视频
        String GAME_REWARD_VIDEO = "228"; // 小满激励视频


        // 主播竞价
        String HOME_GUESS_YOU_LIKE = "44";  // 首页猜你喜欢
        String HOME_CATEGORY_CARD = "45";   // 首页分类卡片
        String HOME_RECOMMEND_FOR_YOU = "46";// 首页为你推荐
        String GUESS_YOU_LIKE = "47";       // 猜你喜欢页
        String CATEGORY_RECOMMEND = "48";   // 分类推荐页
        String CATEGORY_KEYWORD = "49";     // 分类热词页
        String PAYABLE_RECOMMEND = "50";    // 精品首页
        String PAYABLE_KEYWORD = "51";      // 精品热词页
        String ALBUM_DETAIL_RELATIVE_RECOMMEND = "52";  // 专辑详情页相关推荐
        String TRACK_DETAIL_RELATIVE_RECOMMEND = "53";  // 声音详情页相关推荐
        String SEARCH_RESULT_RELATIVE_RECOMMEND = "54"; // 搜索结果页相关推荐

        String SEARCH_MAIN_BANNER = "86"; // 搜索页 推荐广告 banner
        String SINGLE_TRACK_VIDEO_AD = "92"; // 单集解锁激励视频广告
        String SKITS_VIDEO_AD = "93"; // 短剧解锁激励视频广告

        String REWARD_VIDEO_MIX_AD = "94"; // 激励视频混投
        String WELFARE_LAYER = "201"; // 用户营销福利， 砸金蛋页面 浮层
        String WELFARE_DIALOG = "202"; // 用户营销福利， 砸金蛋结果页 弹窗
        String WELFARE_INCENTIVE_VIDEO = "203"; // 用户营销福利， 砸金蛋 观看激励视频

        String PAOPAO_ACTIVITY_ENTRY = "214"; // 泡泡条位置的，卡片弹层广告活动入口
        String IMMERSIVE_NEW_SKIN = "217"; // 沉浸式皮肤广告

        String SOUND_PATCH_MORE_AD = "218"; // 声音流+更多广告位
        String GAME_CENTER_REWARD_VIDEO = "229"; // 游戏中心H5激励视频
        String SEARCH_TOP_BRAND_AD = "236"; // 搜索页品牌专区广告
        String LIVE_LITTLE_BANNER_AD = "239"; // 直播间小挂件广告
        String NATIVE_CONTENT_AD = "244"; // 内容推广落地页
        String RECOMMEND_PROMOTION_AD = "246";// 首页大促广告
        String INSERT_SOUND_PATCH = "253";// 中插声音流广告
        String WINDOW_SHOP_AD = "251";// 橱窗追投广告
        String SEARCH_FLOW_AD = "271";  //搜索信息流广告
        String SEARCH_KEY_WORDS_FIRST_AD = "275";  // 搜索结果页广告，出现在精选最佳匹配位置
        String POINTS_CENTER_TASK_BANNER_AD = "278";  // 积分中心拉活广告
        String POINTS_KEY_WORDS_RESULT_AD = "280";  // 搜索结果页广告
        String ALBUM_COMPONENT_1 = "283";  // 小说tab推荐1屏广告
        String ALBUM_COMPONENT_2 = "284";  // 小说tab推荐2屏广告

        String AD_SEARCH_SUGGEST= "285";  // 搜索联想词广告
        String SOUND_AIGC_MIDDLE_INSERT_AD = "286";  // aigc中插声音广告
        String AD_LISTEN_LIST = "287"; // 新首页社会化听单广告，单条
        String AD_WHOLE_LISTEN_LIST = "291"; // 新首页社会化听单广告，整个
        String AD_RANK_LIST = "288"; // 新首页榜单广告

        String AD_SEARCH_BOTTOM_WORDS = "289"; // 搜索框底词广告
        String AD_HOT_SEARCH_LIST = "290"; // 搜索页热搜榜广告词
        String AD_POINT_CASH = "292"; // 新版听书任务积分现金奖励
        String AD_WELFARE_CASH_RECEIVE = "297"; // 福利页领现金奖励
        String AD_WELFARE_CASH_WITHDRAWAL = "298"; // 福利页提取现金奖励

        String SOUND_PATCH_PLAY_BANNER = "14";  //下挂广告
        String SOUND_PATCH_BANNER = "-14";  //贴片-下挂广告
        String AD_INCENTIVE_SPECIES = "88";  // 积分中心看视频任务
        String AD_IINTEGRAL_CENTER_INSPIRE_VIDEO = "254";  // 积分中心看视频任务
        String AD_DOWNLOAD_REWARD_VIDEO = "300";  // 下载页激励视频
        String AD_PLAY_ISOLATE_LARGE = "302";  // 播放页独立大图
        String AD_WELFARE_PLAY_LET_TASK = "305";  // 福利页短剧任务
        String AD_WELFARE_MALL_TASK = "306";  // 福利页商城任务
        String AD_INCENTIVE_WELFARE = "307";  // 福利页看视频任务
        String AD_PLAY_PAGE_LIVE_MC_AD = "311"; // 播放页直播mc入口广告
    }

    interface IAdAnimationType {
        int DEFUALT = 1; // 默认的动画
        int FLIP = 2;   // 翻转动画
        int HIGHT_LIGHT = 100;  // 高光动画
    }

    interface IPlayViewKey {
        int LARGE_COVER = 1;
        int DANMU = 2;
        int HORIZONTAL_COVER = 3;
        int VIDEO_COVER = 4;
        int FLY_FLOWER = 5;
        int POSTER_OVER = 6;
        int ANSWER = 7;
        int FORWARD_VIDEO = 8;
        int INTERACTIVE_DIALOG = 9;
        int ANCHOR_CENTER_DIALOG = 10;
        int LIVE_DANMU = 11;
        int VERTICAL_VIDEO = 12; // 竖版视频贴片
        int VERTICAL_STATIC = 13; //竖版静态贴片
        int LARGE_COVER_REVERT = 14; //会员封面翻转广告
        int DAN_MU_AND_FLOWER = 15; // 会员品牌弹幕广告
        int VERTICAL_LARGE_COVER = 16; // 贴片放大
        int HORIZONTAL_PAUSE_COVER = 17; // 会员暂停图片贴片
        int HORIZONTAL_PAUSE_VIDEO = 18; // 会员暂停视频贴片
        int VERTICAL_STATIC_GRADE_A = 19; // A级竖版静态图
        int VERTICAL_VIDEO_GRADE_A = 20; // A级竖版视频
        int GRADE_S_STATIC_IMAGE = 21; // S级静态贴片
        int GRADE_S_VIDEO = 22; // S级视频贴片
        int VERTICAL_PAUSE_COVER = 23; // 会员竖版暂停图片贴片
        int VERTICAL_PAUSE_VIDEO = 24; // 会员竖版暂停视频贴片
        int VERTICAL_PAUSE_COVER_STYLE_V2 = 25; // 会员竖版暂停图片贴片样式2
        int VERTICAL_PAUSE_VIDEO_STYLE_V2 = 26; // 会员竖版暂停视频贴片样式2
        int XM_SDK_TEMPLATE_STYLE = 27; // sdk模板样式
    }

    interface OtherInfoKey {
        String GROUP_IMAGE_LIST_1 = "groupImageList_1";
        String GROUP_IMAGE_LIST_2 = "groupImageList_2";
        String GROUP_IMAGE_LIST_3 = "groupImageList_3";
    }

    // action按钮动画
    interface IAdActionAnimationStyle {
        int UP_AND_DOWN = 1;    // 上下跳动
        int LEFT_AND_RIGHT = 2; // 左右跳动
        int ROTATION = 3;       // 左右跳动
    }

    // 视频广告的配置
    interface IVideoAdConfig {
        int MIN_VIDEO_DURATION = 5;
        int MAX_VIDEO_DURATION = 60;
    }

    // 新版播放页轮播常驻
    interface IAdCarouselStyle {
        int AD_CAROUSE_DEFAULT = -1;    // 默认的
        int AD_CAROUSE_NO_SKIP = 0; // 不跳转
        int AD_CAROUSE_OPEN_DIALOG = 1; // 跳转到弹层
        int AD_CAROUSE_OPEN_REWARD = 2; // 跳转到激励视频
        int AD_CAROUSE_OPEN_MEMBER = 3; // 会员
        int AD_CAROUSE_OPEN_OTHER = 4;  // 其他链接地址
    }

    // 首页高光touch样式
    interface IHomeTouchAdStyleId {
        int STATIC_IMG_STYLE = 3951;    // 静态图高光touch
        int DYNAMIC_IMG_STYLE = 3950;   // 动态图高光touch
    }

    int IMMERSIVE_SKIN_STYLE_ID = 21701;    // 播放页沉浸式皮肤
    int ADDITIONAL_SKIN_STYLE_ID = 21702;    // 追投皮肤广告

    interface IAdPlayVersion {
        String PLAY_VERSION_OLD = "1";
        String PLAY_VERSION_NEW_NEW_LAY = "2";
    }

    interface IRemoveAdHintBenefit {
        String REMOVE_AD_HINT_BENEFIT_NORMAL = "1"; // 贴片
        String REMOVE_AD_HINT_BENEFIT_PAOPAO = "2"; // 泡泡条
        String REMOVE_AD_HINT_BENEFIT_COMING = "3"; // 即将到期
        String REMOVE_AD_HINT_BENEFIT_MOREPAGE = "4"; // 更多
        String REMOVE_AD_HINT_BENEFIT_STRONG = "5"; // 强提醒
        String REMOVE_AD_HINT_BENEFIT_AUTO_PLAY = "6"; // 自动拉起
    }

    interface IRemoveAdHintClickType {
        String REMOVE_AD_HINT_CLICK_TYPE_FLOAT = "1"; // 弹窗浮层
        String REMOVE_AD_HINT_CLICK_TYPE_VIP = "2"; // 会员
        String REMOVE_AD_HINT_CLICK_TYPE_VIDEO = "3"; // 前插视频
    }

    /**
     *  1.无：NO
     * 	2.左侧炫屏入画：LEFT
     * 	3.右侧炫屏入画：RIGHT
     * 	4.中心散射：CENTER
     * 	5.单元素中心放大：SINGLE_CENTER
     * 	6.多元素中心放大：MULTI_CENTER
     * 	7.超凡大屏：BIG_SCREEN
     */
    interface IDazzlingAnimationType {
        String NO = "NO";
        String LEFT = "LEFT";
        String RIGHT = "RIGHT";
        String CENTER = "CENTER";
        String SINGLE_CENTER = "SINGLE_CENTER";
        String MULTI_CENTER = "MULTI_CENTER";
        String BIG_SCREEN = "BIG_SCREEN";
    }

    interface IUnitSourceType {
        String DAZZLE_COVER_1 = "dazzleCover1";
        String DAZZLE_COVER_2 = "dazzleCover2";
        String DAZZLE_COVER_3 = "dazzleCover3";
        String BUTTON_COVER = "buttonCover";
        String TOUCH_COVER = "touchCover";
        String TOUCH_DYNAMIC_COVER = "touchDynamicCover";
    }
}
