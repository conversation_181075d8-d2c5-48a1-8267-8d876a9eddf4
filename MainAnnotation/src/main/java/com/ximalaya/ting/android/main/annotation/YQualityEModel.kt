package annotation

import android.content.res.ColorStateList
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.updateLayoutParams
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyHolder
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.airbnb.epoxy.VisibilityState
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager
import com.ximalaya.ting.android.host.model.play.TrackQualities
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.annotation.R

@EpoxyModelClass
abstract class YQualityEModel : EpoxyModelWithHolder<YQualityEModel.Holder>() {
    @EpoxyAttribute
    lateinit var quality: TrackQualities

    @EpoxyAttribute
    var playQuality: Int = -1

    @EpoxyAttribute
    var showMasterClass: Boolean = false

    @EpoxyAttribute
    lateinit var viewStyle: Pair<Rect?, Drawable> // viewStyle，first是margin参数，second是 background drawable
    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var clickListener: View.OnClickListener
    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var dolbyMoreClickListener: () -> Unit
    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var traceItemExpose: (TrackQualities) -> Unit
    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var dismissDialog: (() -> Unit)? = null

    override fun getDefaultLayout() =
        R.layout.mainann_item_sound_quality_layout_y

    override fun bind(holder: Holder) {
        holder.itemView?.updateLayoutParams<MarginLayoutParams> {
            topMargin = viewStyle.first?.top ?: 0
            bottomMargin = viewStyle.first?.bottom ?: 0
        }
        holder.itemView?.background = viewStyle.second
        val isAtoms = TrackPlayQualityManager.getInstance().isFullDepth(quality.qualityLevel)
        holder.itemView?.setOnClickListener(clickListener)
        if (isAtoms) {

            holder.titleTextView?.text =
                "${quality.qualityName}"
            val context = ToolUtil.getCtx()
            if (context != null) {
                holder.subTitleTextView?.setTextColor(ContextCompat.getColor(context, R.color.mainann_color_9b6324_dfae76))
                val drawable = ResourcesCompat.getDrawable(
                    context.resources,
                    R.drawable.mainann_ic_rule_info_n_n_line_regular_11,
                    null
                )
                drawable?.setTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.mainann_color_9b6324_dfae76)))
                drawable?.alpha = if(BaseFragmentActivity2.sIsDarkMode) (0.7f * 255).toInt() else 255
                holder.subTitleTextView?.alpha = 0.7f
                SpanUtils.with(holder.subTitleTextView).append("${quality.descText ?: ""}")
                    .appendSpace(6.dp)
                    .appendVerticalCenterImage(drawable!!)
                    .create()
            } else {
                SpanUtils.with(holder.subTitleTextView).append("${quality.descText ?: ""}")
                    .appendSpace(6.dp)
                    .appendVerticalCenterImage(R.drawable.mainann_ic_rule_info_n_n_line_regular_11)
                    .create()
            }
            holder.subTitleTextView?.setOnClickListener {
                MainApplication.getMainActivity()?.let { activity ->
                    if (activity is MainActivity) {
                        ToolUtil.clickUrlAction(
                            activity as MainActivity,
                            quality.linkUrl,
                            null
                        )
                    }
                    dolbyMoreClickListener()
                    dismissDialog?.invoke()
                }
            }
        } else {
            ToolUtil.getCtx()?.let {
                holder.subTitleTextView?.setTextColor(ContextCompat.getColor(it, R.color.host_color_acacaf_66666b))
            }
            holder.subTitleTextView?.alpha = 1f
            holder.titleTextView?.text =
                "${quality.qualityName}（${StringUtil.getFriendlyFileSizeWithOneKeep(quality.fileSize.toDouble())}）"
            holder.subTitleTextView?.text = "${quality.descText ?: ""} ${quality.bitRate ?: ""}"
        }
        if (isAtoms) {
            ViewStatusUtil.setImageRes(
                holder.dolbyTag,
                if (quality.isHuaweiVivid()) R.drawable.mainann_ic_track_quality_audio_vivid else R.drawable.mainann_ic_track_quality_dolby_tag
            )
        }
        ViewStatusUtil.setVisible(if (isAtoms) View.VISIBLE else View.GONE, holder.dolbyTag)

        ViewStatusUtil.setVisible(
            if (quality.needVip || showMasterClass) View.VISIBLE else View.GONE,
            holder.vipIcon
        )

        if (quality.qualityLevel == playQuality) {
            holder.itemView?.isSelected = true
            holder.selectedIcon?.text = "使用中"
            holder.selectedIcon?.setTextColor(
                ContextCompat.getColor(
                    ToolUtil.getCtx(),
                    R.color.host_color_dcdcdc_66666B
                )
            )
        } else {
            holder.itemView?.isSelected = false
            if (!quality.canChoose && quality.hasQuota && !quality.enjoying) {
                holder.selectedIcon?.text = "去试用"
            } else {
                holder.selectedIcon?.text = "使用"
            }
            holder.selectedIcon?.setTextColor(
                ContextCompat.getColor(
                    ToolUtil.getCtx(),
                    R.color.host_color_ff4444
                )
            )
        }
        holder.freeIcon?.visibility = if (quality.isTimeLimitFree) View.VISIBLE else View.GONE
    }

    override fun onVisibilityStateChanged(visibilityState: Int, holder: Holder) {
        super.onVisibilityStateChanged(visibilityState, holder)
        if (visibilityState == VisibilityState.VISIBLE) {
            traceItemExpose(quality)
        }
    }

    class Holder : EpoxyHolder() {
        var itemView: View? = null
        var titleTextView: TextView? = null
        var subTitleTextView: TextView? = null
        var vipIcon: ImageView? = null
        var freeIcon: ImageView? = null
        var dolbyTag: ImageView? = null

        //        var atomsIcon: ImageView? = null
        var selectedIcon: TextView? = null

        override fun bindView(itemView: View) {
            this.itemView = itemView
            titleTextView = itemView.findViewById(R.id.main_tv_title)
            subTitleTextView = itemView.findViewById(R.id.main_tv_subtitle)
//            atomsIcon = itemView.findViewById(R.id.main_iv_atoms)
            vipIcon = itemView.findViewById(R.id.main_iv_vip)
            freeIcon = itemView.findViewById(R.id.main_iv_free)
            dolbyTag = itemView.findViewById(R.id.main_iv_dolby_tag)
            selectedIcon = itemView.findViewById(R.id.main_tv_select)
        }
    }
}