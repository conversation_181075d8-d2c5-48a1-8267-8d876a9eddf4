package com.ximalaya.ting.android.live.video.fragment;

import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_GIFT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_INPUT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_RIGHT_AD;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_SELL_GOOD;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_VIDEO_PLAYER;
import static com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansClubUpdateMessage.IFanClubMessageType.TYPE_JOIN;
import static com.ximalaya.ting.android.live.video.components2.CourseCompConfig.COMPONENT_COURSE_GOODS_LIST;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.exoplayer.view.XmPlayerVideoView;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.live.biz.mode.data.BackRoomManager;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.LivePageAvailabilityConst;
import com.ximalaya.ting.android.live.common.lib.base.dialog_queue.LiveDialogFragmentManager;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.base.util.LivePageAvailabilityUtil;
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansClubStatusModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.NewAudienceAwardInfo;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.AnimPlayQueueManager;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveAbtestUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LivePlayRestoreUtil;
import com.ximalaya.ting.android.live.common.videoplayer.block.VideoPlayerBlock;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.clearScreen.IClearScreenConfig;
import com.ximalaya.ting.android.live.common.view.clearScreen.LiveClearScreenManager;
import com.ximalaya.ting.android.live.common.view.clearScreen.slide.SlideClearScreenManager;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.exitroom.IExitRoomComponent;
import com.ximalaya.ting.android.live.host.components.recommend.IRecommendLiveComponent;
import com.ximalaya.ting.android.live.host.components.roombackground.IRoomBackgroundComponent;
import com.ximalaya.ting.android.live.host.components.roomloading.IRoomLoadingComponent;
import com.ximalaya.ting.android.live.host.components.sellgoods.ISellGoodsComponent;
import com.ximalaya.ting.android.live.host.constant.MicConstants;
import com.ximalaya.ting.android.live.host.fragment.room.BaseRoomFragment;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreLogger;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreManager;
import com.ximalaya.ting.android.live.host.manager.videofloatwindow.LiveVideoFloatWindowManager;
import com.ximalaya.ting.android.live.host.scrollroom.model.RecommendLiveRecord;
import com.ximalaya.ting.android.live.host.scrollroom.request.LiveScrollDataManager;
import com.ximalaya.ting.android.live.host.scrollroom.scrollrecorder.IRoomSchemaGenerator;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.live.host.utils.LiveHostFragmentUtil;
import com.ximalaya.ting.android.live.lib.chatroom.constant.IGoodsInfoChangedConstant;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUserJoinMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatFansIntimacyMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansClubUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNotifyBottomButtonMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonCouponShowViewStatusMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonCourseShopMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonFansGroupMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGetNewCouponMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoShoppingMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsInfoChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsOrderChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonLiveVideoInfoChangeMessage;
import com.ximalaya.ting.android.live.lib.stream.live.data.LivePullUrls;
import com.ximalaya.ting.android.live.lib.stream.mediaplayer.manager.LivePlayBarManager;
import com.ximalaya.ting.android.live.lib.stream.preloadplayer.ILiveMediaSource;
import com.ximalaya.ting.android.live.lib.stream.preloadplayer.LiveHomePagePlayerManager;
import com.ximalaya.ting.android.live.lib.stream.preloadplayer.PreloadPlayerManager;
import com.ximalaya.ting.android.live.lib.stream.publish.XmLiveRoom;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.lifecycle.Component;
import com.ximalaya.ting.android.live.video.R;
import com.ximalaya.ting.android.live.video.components.bottombar.ICourseBottombarComponent;
import com.ximalaya.ting.android.live.video.components.chatlist.chat.ICourseChatListComponent;
import com.ximalaya.ting.android.live.video.components.chatlist.guide.CourseChatListGuideComponent;
import com.ximalaya.ting.android.live.video.components.chatlist.guide.ICourseChatListGuideComponent;
import com.ximalaya.ting.android.live.video.components.countdown.ICourseCountDownComponent;
import com.ximalaya.ting.android.live.video.components.countdown.IRecommendRequestListener;
import com.ximalaya.ting.android.live.video.components.coupons.ICourseCouponComponent;
import com.ximalaya.ting.android.live.video.components.gift.CourseGiftPanelComponent;
import com.ximalaya.ting.android.live.video.components.gift.ICourseGiftPanelComponent;
import com.ximalaya.ting.android.live.video.components.goodslist.ICourseGoodsListComponent;
import com.ximalaya.ting.android.live.video.components.header.ICourseHeaderComponent;
import com.ximalaya.ting.android.live.video.components.input.ICourseInputPanelComponent;
import com.ximalaya.ting.android.live.video.components.liveauth.ICourseAuthComponent;
import com.ximalaya.ting.android.live.video.components.mic.ICourseMicComponent;
import com.ximalaya.ting.android.live.video.components.noticeinput.ICourseNoticeComponent;
import com.ximalaya.ting.android.live.video.components.pop.LiveCourseEnterAndPopComponent;
import com.ximalaya.ting.android.live.video.components.rightarea.ICourseRightAreaComponent;
import com.ximalaya.ting.android.live.video.components.usercard.ICourseUserInfoCardComponent;
import com.ximalaya.ting.android.live.video.components.videoplayer.ICourseVideoPlayerComponent;
import com.ximalaya.ting.android.live.video.components2.CourseCompConfig;
import com.ximalaya.ting.android.live.video.constanst.clearview.CourseClearScreenConfig;
import com.ximalaya.ting.android.live.video.data.model.CourseLiveAuthCheckInfo;
import com.ximalaya.ting.android.live.video.data.model.CourseRoomDetail;
import com.ximalaya.ting.android.live.video.data.model.VideoLiveRecommendInfo;
import com.ximalaya.ting.android.live.video.presenter.LiveCourseRoomPresenter;
import com.ximalaya.ting.android.live.video.roomcore.CourseRoomCore;
import com.ximalaya.ting.android.liveav.lib.constant.SDKInitStatus;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程直播页面
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817870952
 */
public class CourseRoomFragment extends BaseCourseRoomFragment {

    private static final String TAG = "LiveVideoLandscapeFragment";

    private LiveCourseRoomPresenter mLiveCourseRoomPresenter;

    private int mOrientation = Configuration.ORIENTATION_PORTRAIT;

    private LiveVideoFloatWindowManager mLiveVideoFloatWindowManager; // 视频浮窗管理

    private long mAlbumId; //专辑id

    private boolean mPassAuth = false; // 是否通过鉴权

    private int mLivePayType = CourseLiveAuthCheckInfo.TYPE_COMM;//直播业务付费类型 0:普通直播 1:专项直播 2:付费直播

    private String mPullUrl; //拉流地址

    private boolean isTrySeeing = false; //是否试看中

    private PlayerConstants.ResolutionRatio mResolutionRatio = PlayerConstants.ResolutionRatio.LANDSCAPE_16_9;


    public CourseRoomFragment() {
    }

    @SuppressLint("ValidFragment")
    public CourseRoomFragment(boolean isLand) {
    }

    @Override
    public LiveUserInfo getCurrentUserInfo() {
        if (mLiveUserInfo instanceof LiveUserInfo) {
            return (LiveUserInfo) mLiveUserInfo;
        }
        return super.getCurrentUserInfo();
    }

    public static CourseRoomFragment newInstance(long liveId, boolean isLand, long albumId, int playSource) {
        CourseRoomFragment fragment = new CourseRoomFragment(isLand);

        Bundle bundle = new Bundle();
        bundle.putLong(ILiveFunctionAction.KEY_LIVE_ID, liveId);
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, albumId);
        bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, playSource);
        fragment.setArguments(bundle);

        return fragment;
    }

    @Override
    protected void parseBundle() {
        super.parseBundle();
        try {
            mAlbumId = LiveHostFragmentUtil.getFragmentArgument(this, ILiveFunctionAction.KEY_ALBUM_ID);
            mPlaySource = LiveHostFragmentUtil.getFragmentArgument(this, BundleKeyConstants.KEY_PLAY_SOURCE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void initMyUi(Bundle savedInstanceState) {
        super.initMyUi(savedInstanceState);
        mRootView = findViewById(R.id.live_rl_root);

        updateListViewLayoutParamsRule(false);

        AutoTraceHelper.bindPageDataCallback(this, new AutoTraceHelper.IDataProvider() {
            @Override
            public Object getData() {
                Map<String, String> params = new HashMap<>();
                params.put("liveId", mLiveId + "");
                params.put("roomId", mLiveRecordInfo == null ? 0 + "" : mLiveRecordInfo.getRoomId() + "");
                params.put("liveRoomType", mBusinessId + "");
                params.put("videoLiveType", (mLiveRecordInfo == null ? 0 : mLiveRecordInfo.getRoomSubBizType()) + "");
                params.put("liveRoomName", mLiveRecordInfo == null ? "" : mLiveRecordInfo.getRoomTitle());
                params.put("LiveBroadcastState", mLiveRecordInfo == null ? "" : mLiveRecordInfo.getStatus() + "");
                params.put("anchorId", mLiveRecordInfo == null ? "" : mLiveRecordInfo.getHostUid() + "");
                params.put("isLiveAnchor", (mLiveRecordInfo != null && mLiveRecordInfo.getHostUid() == UserInfoMannage.getUid() ? 0 : 1) + "");
                params.put("uid", UserInfoMannage.getUid() + "");
                return params;
            }

            @Override
            public Object getModule() {
                return null;
            }

            @Override
            public String getModuleType() {
                return AutoTraceHelper.MODULE_DEFAULT;
            }
        });
    }

    @Override
    protected IClearScreenConfig getClearScreenConfig() {
        return new CourseClearScreenConfig(mRootView);
    }

    @Override
    protected String getTraceName() {
        return "房间-课程直播间";
    }


    @Override
    public void onMyResume() {
        tabIdInBugly = 163850;
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.finishFloatWindowPlay();
        }
        ILiveMediaSource mCurrentLiveRecord = LiveHomePagePlayerManager.getInstance().getCurrentLiveRecord();
        if (mCurrentLiveRecord != null && mCurrentLiveRecord.getRelativeRoomId() != getRoomId()) {
            LiveHomePagePlayerManager.getInstance().pauseMediaSource();
        }
        super.onMyResume();
    }

    @Override
    protected ILiveCourseRoom.IPresenter createPresenter() {
        mLiveCourseRoomPresenter = new LiveCourseRoomPresenter(this, getConnectionManager());
        return mLiveCourseRoomPresenter;
    }


    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Logger.i(TAG, "onConfigurationChanged:" + newConfig.orientation);
        if (!canUpdateUi()) {
            return;
        }
        if (getCourseComponentHost() != null) {
            getCourseComponentHost().dispatchOrientationChange(newConfig.orientation, newConfig.orientation == mOrientation);
        }
        if (newConfig.orientation == mOrientation) {
            return;
        }
        mOrientation = newConfig.orientation;
        //竖屏切换横屏情况下 更新ui操作
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (getCustomH5Dialog() != null) {
                getCustomH5Dialog().dismiss();
            }
        }

    }


    public PlayerConstants.ResolutionRatio getVideoSizeRatio() {
        return mResolutionRatio;
    }

    @Override
    public boolean onBackPressed() {
        return super.onBackPressed();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.livecourse_fra_room;
    }

    @Override
    public void onRequestPullStreamUrlSuccess(LivePullUrls pullStreamInfo) {
        super.onRequestPullStreamUrlSuccess(pullStreamInfo);
        if (pullStreamInfo.getFlvUrl() != null) {
            onReceivePlayUrl(pullStreamInfo.getFlvUrl());
        }
    }

    @Override
    protected void onReceivePlayUrl(String playUrl) {
        super.onReceivePlayUrl(playUrl);
        mPullUrl = playUrl;

        RecommendLiveRecord liveRecord = LiveScrollDataManager.getInstance().getCurrentRoomRecord();
        if (liveRecord != null && mLiveId == liveRecord.getLiveId() && liveRecord.bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE
                && TextUtils.isEmpty(liveRecord.playUrl)) {
            liveRecord.playUrl = mPullUrl;
            if (liveRecord.roomId == 0) {
                liveRecord.roomId = mRoomId;
            }
        }


        if (mPassAuth || isTrySeeing) {
            startPlay();
        }

    }

    @Override
    public String getLivePullStreamUrl() {
        return mPullUrl;
    }

    public boolean isLiveAbleToPlay() {
        boolean isAbleToPlay = (!TextUtils.isEmpty(mPullUrl) && mPassAuth);
        if (isAbleToPlay) {
            return true;
        }
        ICourseAuthComponent authComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_AUTH);
        if (authComponent != null) {
            return authComponent.isAbleToTrySee();
        }

        return false;
    }

    private void startPlay() {
        if (mIsOpenFlv != null && !mIsOpenFlv) {
            return;
        }
        checkAndInitLiveVideoFloatWindowManager();
        mLiveVideoFloatWindowManager.init(mActivity, LiveMediaType.TYPE_VIDEO, mResolutionRatio);
        //课程直播默认先认为是16：9比例，等拿到视频宽高后再更新
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent != null) {
            videoPlayerComponent.setPlayUrl(mPullUrl, PlayerConstants.PLAYTYPE_LIVE, mResolutionRatio);
        }
    }

    private void checkAndInitLiveVideoFloatWindowManager() {
        if (mLiveVideoFloatWindowManager == null) {
            mLiveVideoFloatWindowManager = new LiveVideoFloatWindowManager();
            mLiveVideoFloatWindowManager.setFloatWindowManagerCallback(new LiveVideoFloatWindowManager.IFloatWindowManagerCallback() {
                @Override
                public boolean isPkModeOrGroupMicMode() {
                    return false;
                }

                @Override
                public BaseRoomFragment getCurrentFragment() {
                    return CourseRoomFragment.this;
                }
            });
        }
    }

    @Override
    public void onRequestPullStreamUrlFail(int errCode, String errMsg) {
        super.onRequestPullStreamUrlFail(errCode, errMsg);
        IRoomLoadingComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_ROOM_LOADING);
        if(component != null){
            component.hideRequestLoading();
            component.showRequestErrorView();
        }
    }


    public boolean isMicViewShow() {
        return isMicViewShow;
    }


    public void hideView(boolean isLockScreen) {
        if (!canUpdateUi()) {
            return;
        }
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.showView(false);
        }
        ICourseRightAreaComponent rightAreaComponent = getComponentSafety(COMPONENT_RIGHT_AD);
        if (rightAreaComponent != null) {
            rightAreaComponent.showView(false);
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.showView(false, false, isLockScreen);
        }
        ICourseCouponComponent couponComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (couponComponent != null) {
            couponComponent.showView(false);
        }
    }

    public void showView() {
        if (!canUpdateUi()) {
            return;
        }

        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.showView(true);
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.showView(true, false, false);
        }
        ICourseCouponComponent couponComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (couponComponent != null) {
            couponComponent.showView(true);
        }

    }

    public void playFinished() {
    }

    public void onVideoPlayerViewScaleChanged(float scale) {
        if (!canUpdateUi()) {
            return;
        }
        try {
            LiveCourseEnterAndPopComponent component = getPresenter().getComponentManager().getComponent(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            component.receivePlayerViewScaleChanged(scale);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        ICourseRightAreaComponent rightAreaComponent = getComponentSafety(COMPONENT_RIGHT_AD);

        if (scale == 1.0f) {
            if (mOrientation == Configuration.ORIENTATION_PORTRAIT) { //竖屏状态
                if (rightAreaComponent != null) {
                    rightAreaComponent.showView(true);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.showView(true, true, false);
                }
                ICourseCouponComponent couponComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
                if (couponComponent != null) {
                    couponComponent.showView(true);
                }

                if (getChatListComponent() != null) {
                    getChatListComponent().setChatListVisible(true);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.judgeShowInputLayout();
                }
            }
        } else { //缩放中
            if (mOrientation == Configuration.ORIENTATION_PORTRAIT) { //竖屏状态
                if (rightAreaComponent != null) {
                    rightAreaComponent.showView(false);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.showView(false, true, false);
                }
                ICourseCouponComponent couponComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
                if (couponComponent != null) {
                    couponComponent.showView(false);
                }
                if (getChatListComponent() != null) {
                    getChatListComponent().setChatListVisible(false);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.judgeShowInputLayout();
                }
            } else {
                if (getChatListComponent() != null && !getChatListComponent().isHideChat()) {
                    hideChat(true);
                    ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
                    if (headerComponent != null) {
                        headerComponent.updateCommentIvResource(true);
                    }
                }
            }
        }
    }

    public void onProgressBarShowInPortraitMode() {
        if (getChatListComponent() != null) {
            getChatListComponent().setChatListVisible(false);
        }
    }

    public void onProgressBarHideInPortraitMode() {
        if (LiveVideoPlayerManager.getInstance().getPlayerViewScaleValue() == 1) {
            ICourseChatListComponent videoChatListComponent = getChatListComponent();
            if (videoChatListComponent != null) {
                videoChatListComponent.setChatListVisible(true);
            }
        }
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.setHeaderVisiable(View.VISIBLE);
        }
    }

    public void onTimeShiftProgressBarShowInPortraitMode() {
        Logger.d(TAG, "竖屏模式下直播时移进度条出现");
        if (getChatListComponent() != null) {
            getChatListComponent().setChatListVisible(false);
        }
        Component comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp instanceof ICourseChatListGuideComponent) {
            ((CourseChatListGuideComponent) comp).showGuideView(false);
        }
    }

    public void onTimeShiftProgressBarHideInPortraitMode() {
        Logger.d(TAG, "竖屏模式下直播时移进度条消失");
        if (LiveVideoPlayerManager.getInstance().getPlayerViewScaleValue() == 1) {
            ICourseChatListComponent videoChatListComponent = getChatListComponent();
            if (videoChatListComponent != null) {
                videoChatListComponent.setChatListVisible(true);
            }
            Component comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
            if (comp instanceof CourseChatListGuideComponent) {
                ((CourseChatListGuideComponent) comp).showGuideView(true);
            }
        }
    }

    public void updatePlayType(int playType) {
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.updatePlayType(playType);
        }
    }

    public void updateResolutionRatioAndSize(PlayerConstants.ResolutionRatio resolutionRatio, int width, int height) {
        checkAndInitLiveVideoFloatWindowManager();
        mLiveVideoFloatWindowManager.init(mActivity, LiveMediaType.TYPE_VIDEO, resolutionRatio);

        mResolutionRatio = resolutionRatio;
        if ((mLiveRecordInfo != null && mLiveRecordInfo.hasPlayBack()
                && !android.text.TextUtils.isEmpty(mLiveRecordInfo.getPlayBackPath()))
                && mLiveRecordInfo.getStatus() != PersonLiveBase.LIVE_STATUS_ING) {
            boolean isLand = VideoPlayerBlock.isLandUIMode(mResolutionRatio);
            if (LiveClearScreenManager.getInstance().isClear() && !isLand) {
                LiveClearScreenManager.getInstance().toggleClearScreen();
            }
            SlideClearScreenManager.getInstance().setRoomSupportClear(isLand);
        }
        if (getCourseComponentHost() != null) {
            getCourseComponentHost().dispatchResolutionRatioAndSize(mResolutionRatio, width, height);
        }
    }

    public boolean canShowScaleGuide() {
        return !isTrySeeProcessing() && !isDoScrollAnim() && !hasDialogShowing();
    }

    /**
     * 课程直播loading还是老组件，如果后期改造，则移除次方法的重载
     **/
    @Override
    protected void showRoomLoadingProgressView() {
        super.showRoomLoadingProgressView();
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.hideHeaderViewExcludeCloseBtn(false,true);
        }
    }

    @Override
    public void onEntryAddFragment(Fragment fragment) {
        super.onEntryAddFragment(fragment);
        ICourseAuthComponent authComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_AUTH);
        if (authComponent != null) {
            authComponent.onEntryAddFragment(fragment);
        }
    }

    private void updatePassAuth(boolean passAuth) {
        mPassAuth = passAuth;
        CourseRoomCore roomCore = (CourseRoomCore) RoomCoreManager.getRoomCore(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE);
        if (roomCore == null) {
            return;
        }
        roomCore.setPassAuth(passAuth);
    }

    // 课程直播鉴权
    @Override
    public void checkCourseLiveAuth() {
        RoomCoreLogger.logFile("checkCourseLiveAuth mBusinessId = " + mBusinessId + ",mLiveId = " + mLiveId + " mAlbumId = " + mAlbumId);
        mLiveCourseRoomPresenter.requestCourseLiveAuthCheck(mBusinessId, mLiveId, mAlbumId, new IDataCallBack<CourseLiveAuthCheckInfo>() {
            @Override
            public void onSuccess(@Nullable CourseLiveAuthCheckInfo res) {
                if (!canUpdateUi()) {
                    return;
                }

                // 此处不会出现，内部逻辑已经排除
                if (res == null) {
                    return;
                }

                mLivePayType = res.type;
                ICourseRightAreaComponent rightAreaComponent = getComponentSafety(COMPONENT_RIGHT_AD);

                if (rightAreaComponent != null) {
                    rightAreaComponent.addDetailAd(res.pendantUrl);
                }

                if (res.code == CourseLiveAuthCheckInfo.HAVE_AUTH) {//拥有观看权限
                    updatePassAuth(true);
                    ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
                    if (videoPlayerComponent != null) {
                        videoPlayerComponent.updateAuthInfo(res);
                    }
                    ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
                    if (bottomBarComponent != null) {
                        bottomBarComponent.setBottomLayoutAndBarVisible(View.VISIBLE);
                    }

                    boolean isGotoPay = false;
                    ICourseAuthComponent authCheckComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_AUTH);
                    if (authCheckComponent != null) {
                        authCheckComponent.updateLiveAuthInfo(res);
                        isGotoPay = authCheckComponent.isGotoPay();
                    }

                    if (getChatListComponent() != null) {
                        getChatListComponent().updateChatListLayout();
                    }

                    loadRoomRightOperations();

                    //付费后自动预约
                    ICourseCountDownComponent countDownComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_COUNT_DOWN);
                    ILiveRoomDetail roomDetail = getLiveRoomDetail();
                    boolean isNoStart = roomDetail != null && roomDetail.getStatus() == PersonLiveBase.LIVE_STATUS_NOTICE;

                    if (isGotoPay && countDownComponent != null && isNoStart) {
                        countDownComponent.subscribeLive();
                    }


                    if (!TextUtils.isEmpty(mPullUrl)) {
                        startPlay();
                    } else {
                        RecommendLiveRecord liveRecord = LiveScrollDataManager.getInstance().getCurrentRoomRecord();
                        if (liveRecord != null && mLiveId == liveRecord.getLiveId() && liveRecord.bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE
                                && !TextUtils.isEmpty(liveRecord.playUrl) && (roomDetail == null || roomDetail.getStatus() == PersonLiveBase.LIVE_STATUS_ING)) {
                            Log.d("duruoochen--preload", "已提前获取到url");
                            mPullUrl = liveRecord.playUrl;
                            startPlay();
                        }
                    }

                } else {
                    updatePassAuth(false);
                    ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
                    if (videoPlayerComponent != null) {
                        videoPlayerComponent.updateAuthInfo(res);
                    }
                    ICourseAuthComponent authCheckComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_AUTH);
                    if (authCheckComponent != null) {
                        authCheckComponent.updateLiveAuthInfo(res);
                    }
                    ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
                    if (bottomBarComponent != null) {
                        bottomBarComponent.setBottomLayoutAndBarVisible(View.GONE);
                    }

                    if (getChatListComponent() != null) {
                        getChatListComponent().updateChatListLayout();
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                updatePassAuth(false);
                ToastManager.showFailToast(message);
                try {
                    IExitRoomComponent component = getComponentSafety(CourseCompConfig.COMPONENT_EXIT_ROOM);
                    component.markClose();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                finishFragment();
            }
        });
    }


    @Override
    public void onRequestLiveRecordDetailSuccess(ILiveRoomDetail recordInfo) {
        super.onRequestLiveRecordDetailSuccess(recordInfo);

        LivePageAvailabilityUtil.postSucceed(LivePageAvailabilityConst.COURSE_ROOM, recordInfo);

        if (null == recordInfo || !canUpdateUi()) {
            notifyTracePageFailed();
            return;
        }
        if (!recordInfo.canPlayLive()) {
            LiveHelper.Log.i("duruochen--preload", "onRequestLiveRecordDetailSuccess--无观看权限");
            PreloadPlayerManager.getInstance().release();
            stopTrySee();
        }
        if (recordInfo instanceof CourseRoomDetail) {
            LiveVideoPlayerManager.getInstance().setRoomDetail(
                    ((CourseRoomDetail) recordInfo).getLivePlaySourceInfo(),
                    PlayableModel.KIND_LIVE_COURSE,
                    BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE
            );
            if (!TextUtils.isEmpty(mPullUrl)) {
                LivePlayBarManager.getInstance().showPlayBar(mPullUrl, ((CourseRoomDetail) recordInfo).getLivePlaySourceInfo(), PlayableModel.KIND_LIVE_COURSE);
            }
        }

        Bundle arguments = getArguments();
        if (arguments != null) {
            if (arguments.containsKey(ILiveFunctionAction.KEY_SHOW_BACK)) {
                //如果有这个key
                boolean showBack = arguments.getBoolean(ILiveFunctionAction.KEY_SHOW_BACK);
                if (!showBack) {
                    //表示不需要展示
                    arguments.putLong(ILiveFunctionAction.KEY_SHOW_BACK_TIME, 0);
                    BackRoomManager.getInstance().setStartTime(0);
                }
            }
        }

        IRoomBackgroundComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_ROOM_BACKGROUND);
        if (null != component) {
            component.showNormalBackground();
        }

        notifyTracePageEnd();
    }

    @Override
    public void onRequestLiveRecordDetailFail(int errCode, String errMsg) {
        super.onRequestLiveRecordDetailFail(errCode, errMsg);

        LivePageAvailabilityUtil.postFail(LivePageAvailabilityConst.COURSE_ROOM, errCode, errMsg);
        notifyTracePageFailed();

        if (!canUpdateUi()) {
            return;
        }
        hideAllDialog();
    }

    @Override
    protected boolean isKickOutErrorCode(int code) {
        return code == 1000;
    }

    public void bottomClickGift() {
        //全屏模式下需要先旋转
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }
        try {
            ICourseGiftPanelComponent videoGiftPanelNewComponent = getComponentSafety(COMPONENT_GIFT_PANEL);
            if (videoGiftPanelNewComponent != null) {
                videoGiftPanelNewComponent.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void bottomClickShop() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getContext(), LoginByConstants.LOGIN_BY_HALF_SCREEN);
            return;
        }
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        ICourseGoodsListComponent goodsListComponent = getComponentSafety(COMPONENT_COURSE_GOODS_LIST);
        if (goodsListComponent != null) {
            goodsListComponent.showGoodsList();
        }
    }

    @Override
    public void isGiftPanelShowing(boolean isShow) {
        super.isGiftPanelShowing(isShow);
    }

    @Override
    public void onPhoneCallStateChanged(boolean isCalling) {
        super.onPhoneCallStateChanged(isCalling);
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (!isCalling) {
            if (videoPlayerComponent != null && !videoPlayerComponent.isError()
                    && mLiveRecordInfo != null && mLiveRecordInfo.getStatus() == PersonLiveBase.LIVE_STATUS_ING
                    && PlayTools.isPlayModelCourseLive(XmPlayerManager.getInstance(getContext()).getCurrSound())) {
                if (mPassAuth || isTrySeeing) {
                    if (videoPlayerComponent.getCurrentPlayType() == PlayerConstants.PLAYTYPE_LIVE) {
                        videoPlayerComponent.resumeLive();
                    } else {
                        videoPlayerComponent.start();
                    }
                } else {
                    LiveHelper.Log.i(TAG, "无观看权限");
                }
            }
        } else {
            if (videoPlayerComponent != null) {
                videoPlayerComponent.pause();
            }
        }
    }

    @Override
    public void showFansClubDialogFragment() {
        if (!isHavePlayAuth()) {
            CustomToast.showFailToast(R.string.live_video_click_no_auth);
            return;
        }
        // 如果开通儿童模式，不能打开粉丝团;此处判断防止入口遗漏能进入直播间
        if (LiveHostCommonUtil.checkChildrenModeOpen(mContext)) {
            return;
        }
        if (!canUpdateUi() || mLiveRecordInfo == null || mLiveRecordInfo.getRoomFansClubVo() == null) {
            return;
        }
        showFansClubDialogFragment(mLiveRecordInfo.getRoomFansClubVo().getFansClubHtmlUrl());
    }

    /**
     * 课程直播 打开粉丝团半屏
     *
     * @param fansClubUrl 粉丝团地址
     */
    @Override
    public void showFansClubDialogFragment(String fansClubUrl) {
        // 全屏时确保退出全屏模式
        requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        super.showFansClubDialogFragment(fansClubUrl);
    }

    public void bottomClickInput() {
        if (!UserInfoMannage.hasLogined()) {
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
            UserInfoMannage.gotoLogin(getActivity(), LoginByConstants.LOGIN_BY_HALF_SCREEN);
            return;
        }

        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            inputComponent.show();
        }

        moveToBottomChatList();
    }

    private void moveToBottomChatList() {
        postOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
                if (canUpdateUi()) {
                    //聊天列表滑动到底部
                    ICourseChatListComponent comp = getChatListComponent();
                    if (comp != null) comp.naturalScrollListToBottom();
                }
            }
        }, 300);
    }

    public void bottomClickNoticeInput() {

        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }
        ICourseNoticeComponent noticeComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_NOTICE);
        if (noticeComponent != null) {
            noticeComponent.show();
        }
    }

    public void showAllMsg(boolean isShowAllMsg) {
        if (!canUpdateUi()) {
            return;
        }
        ICourseChatListComponent comp = getChatListComponent();
        if (comp != null) comp.switchLecture(!isShowAllMsg);
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.judgeShowInputLayout();
        }
    }

    @Override
    public void hideChat(boolean isHideChat) {
        if (getChatListComponent() != null) {
            getChatListComponent().setChatListVisible(!isHideChat);
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.judgeShowInputLayout();
        }
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent != null) {
            videoPlayerComponent.setFullLayoutParams(isHideChat);
        }
    }

    public void retryLogin() {
        if (mLiveRecordInfo != null && mLiveRecordInfo.getRoomId() > 0
                && mPresenter != null) {
            closeRoomImConnection();
            joinRoomImConnection();
        }

    }

    @Override
    public boolean hasDialogShowing() {
        // 当前房间页必须是在栈顶，不能被如 H5 页面覆盖
        boolean isLiveRoomOnTop = isLiveRoomOnTop();
        boolean dialogShowing = LiveHostCommonUtil.hasDialogOrDialogFragmentShowing();

        boolean giftPanelShowing = false;
        try {
            CourseGiftPanelComponent component = getComponentSafety(COMPONENT_GIFT_PANEL);
            if (component != null) {
                giftPanelShowing = component.isGiftPanelShow();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean giftShowing = false;
        try {
            giftShowing = AnimPlayQueueManager.isAnimPlaying();
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean keyBoardShow = false;

        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            keyBoardShow = inputComponent.isKeyboardPanelShowed();
        }

        boolean isUserPopShow = false;
        ICourseUserInfoCardComponent userInfoCardComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_USER_INFO_CARD);
        if (userInfoCardComponent != null) {
            isUserPopShow = userInfoCardComponent.isUserPopShow();
        }

        boolean isShareDialogShow = false;
        try {
            ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
            if (bottomBarComponent != null) {
                isShareDialogShow = bottomBarComponent.isShowShareDialog();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        boolean h5DialogShow = getCustomH5Dialog() != null && getCustomH5Dialog().isShowing();


        boolean isIdle = !isFragmentScrollStateIdle();


        return !isLiveRoomOnTop
                || dialogShowing
                || giftPanelShowing
                || keyBoardShow
                || isUserPopShow
                || isShareDialogShow
                || giftShowing
                || h5DialogShow
                || isIdle;
    }

    @Override
    public void onScrollGuideAnimationFinished() {
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent != null) {
            videoPlayerComponent.onScrollGuideAnimationFinished();
        }
    }

    @Override
    public boolean isDoScrollAnim() {
        return super.isDoScrollAnim();
    }


    public void clickCloseRoomBtn() {
        IExitRoomComponent component = getComponentSafety(CourseCompConfig.COMPONENT_EXIT_ROOM);
        if (component != null) {
            component.requestExitRoom();
        } else {
            finishFragment();
        }
    }

    @Override
    protected BaseVirtualRoom getAndFillRoomCore() {
        CourseRoomCore room = getRoomCore();
        if (room != null) room.setMicWaiting(isWaitMicConnecting());
        return room;
    }

    public void clickMiniRoom() {
        IExitRoomComponent component = getComponentSafety(CourseCompConfig.COMPONENT_EXIT_ROOM);
        if (component == null) {
            return;
        }
        component.requestMiniRoom();

    }

    @Override
    public void requestPlayMode(int mode) {
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent == null) {
            return;
        }
        videoPlayerComponent.requestPlayMode(mode);
    }

    @Override
    public void keyboardShowStateChange(boolean show) {
        super.keyboardShowStateChange(show);

        updateListViewLayoutParamsRule(show);

    }

    private boolean mKeyVisible;

    protected void updateListViewLayoutParamsRule(boolean keyBoardShow) {

        if (mKeyVisible == keyBoardShow) {
            return;
        }

        mKeyVisible = keyBoardShow;

        if (!canUpdateUi()) {
            return;
        }

        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (keyBoardShow) {
            if (headerComponent != null) {
                headerComponent.hideHeaderViewExcludeCloseBtn(false,false);
            }
        } else {
            if (headerComponent != null) {
                headerComponent.hideHeaderViewExcludeCloseBtn(true,false);
            }
        }
    }


    @Override
    public void onChatRoomStatusChanged(long chatId, long roomId, int connectStatus, String msg) {
        super.onChatRoomStatusChanged(chatId, roomId, connectStatus, msg);
        if (chatId > 0) {
            mChatId = chatId;
        }
    }

    public void countdownVisibilityChanged(int visibility) {
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);

        if (videoPlayerComponent == null) {
            return;
        }
        if (View.VISIBLE == visibility) {
            videoPlayerComponent.hidePlayer();
        } else {
            videoPlayerComponent.showPalyer();
        }
        Component comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp instanceof ICourseChatListComponent) {
            ((ICourseChatListComponent) comp).onLiveStatusPanelVisibilityChanged(
                    visibility == View.VISIBLE
            );
        }
    }

    public void trySeeEndPanelVisibilityChanged(int visibility) {
        Component comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp instanceof ICourseChatListComponent) {
            ((ICourseChatListComponent) comp).trySeeEndPanelVisibilityChanged(
                    visibility == View.VISIBLE
            );
        }
    }

    @Override
    public boolean isScreenFull() {
        return mOrientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    /**
     * 获取各个组件的操作事件，影响播放控制布局定时消息
     */
    @Override
    public void onGetClickEvent() {

        if (!canUpdateUi()) {
            return;
        }
        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (playerComponent == null || playerComponent.getCurrentPlayMode() != PlayerConstants.PLAYMODE_FULLSCREEN) {
            return;
        }
        playerComponent.resetAutoHideController();
    }

    /**
     * 此处传过来的是liveId
     **/
    @Override
    public void switchRoom(long roomId, Bundle bundle) {
        setArguments(bundle);
        LivePlayRestoreUtil.markLastEnterRoomId(mRoomId);
        LivePlayRestoreUtil.markClickExitLiveRoomButton(false);
        LivePlayRestoreUtil.markClickMinimumLiveRoom(false);
        this.mRoomId = roomId;
        switchLive(bundle.getLong(ILiveFunctionAction.KEY_LIVE_ID));
        mIsOpenFlv = null;
    }

    /**
     * 切换直播
     *
     * @param liveId 直播场次id
     */
    public void switchLive(long liveId) {
        if (!canUpdateUi() || liveId <= 0) {
            return;
        }

        boolean isSameLive = mLiveId == liveId;
        if (isSameLive) {
            return;
        }

        if (mLiveRecordInfo == null) {
            return;
        }
        requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);

        mLiveId = liveId;

        isFirstQueryHistoryMsg = true;

        updatePassAuth(false);
        isTrySeeing = false;

        if (getCourseComponentHost() != null) {
            getCourseComponentHost().dispatchLiveSwitch(liveId);
        }

        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            micComponent.leaveMic();
        }
        //重新请求数据
        loadMyData();
    }


    @Override
    public void clickAtFunc(String username, long uid) {

        if (mLiveRecordInfo.isRoomForbidden()) {
            return;
        }

        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        if (uid > 0 && !android.text.TextUtils.isEmpty(username)) {

            // @用户
            ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
            if (inputComponent != null) {
                inputComponent.sendATMessage(uid, username);
            }
        }

    }

    @Override
    public void onClickAt(long uid, String username) {

        if (mLiveRecordInfo.isRoomForbidden()) {
            return;
        }

        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        // @用户
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            inputComponent.sendATMessage(uid, username);
        }
    }

    public void onOpenFansGroup(String url) {
        if (NativeHybridFragment.isItingScheme(url)) {
            LiveCommonITingUtil.handleITing(mActivity, url);
        } else {
            showFansClubDialogFragment(url);
        }
    }

    @Override
    public void onReceiveGoodsInfoChangedMessage(CommonGoodsInfoChangedMessage goodsInfoChangedMessage) {
        if (null == goodsInfoChangedMessage || !canUpdateUi()) {
            return;
        }

        super.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        ICourseGoodsListComponent goodsListComponent = getComponentSafety(COMPONENT_COURSE_GOODS_LIST);
        if (goodsListComponent != null) {
            goodsListComponent.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        }
        if (goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.EXPLAIN ||
                goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.UNEXPLAIN ||
                goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.SUPPLEMENT ||
                goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.GONE) {
            ISellGoodsComponent sellGoodsComponent = getComponentSafety(COMPONENT_SELL_GOOD);
            if (sellGoodsComponent != null) {
                sellGoodsComponent.checkSpeakingGoodsInfo();
            }
        }
    }

    @Override
    public void onReceiveGoodsOrderChangedMessage(CommonGoodsOrderChangedMessage goodsOrderChangedMessage) {
        super.onReceiveGoodsOrderChangedMessage(goodsOrderChangedMessage);
        if (!canUpdateUi() || null == goodsOrderChangedMessage) {
            return;
        }

        Logger.i(TAG, "onReceiveGoodsOrderChangedMessage:" + goodsOrderChangedMessage);

        ICourseGoodsListComponent goodsListComponent = getComponentSafety(COMPONENT_COURSE_GOODS_LIST);
        if (goodsListComponent != null) {
            goodsListComponent.onReceiveGoodsOrderChangedMessage(goodsOrderChangedMessage);
        }
    }

    @Override
    public void onReceiveGoShoppingMessage(CommonGoShoppingMessage goShoppingMessage) {
        LiveHelper.Log.i(TAG, "onReceiveGoShoppingMessage:" + goShoppingMessage.toString());
        super.onReceiveGoShoppingMessage(goShoppingMessage);
        CommonChatUserJoinMessage userJoinMessage = new CommonChatUserJoinMessage();
        userJoinMessage.setGoShoppingMessage(goShoppingMessage);
        try {
            LiveCourseEnterAndPopComponent component = getPresenter().getComponentManager().getComponent(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            component.receiveEnterRoomMessage(userJoinMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //关闭优惠券挂件通知
    @Override
    public void onGetCouponViewStatusChangeMsg(CommonCouponShowViewStatusMsg msg) {
        LiveHelper.Log.i(TAG, "onGetCouponViewStatusChangeMsg:" + msg.toString());

        ICourseCouponComponent couponComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (couponComponent != null && msg.state == 1) {
            couponComponent.dismissCouponShowView();
        }
    }

    //主播发券通知
    @Override
    public void onGetNewLiveCouponMsg(CommonGetNewCouponMsg msg) {
        LiveHelper.Log.i(TAG, "onGetNewLiveCouponMsg:" + msg.toString());

        ICourseCouponComponent couponComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (couponComponent == null) {
            return;
        }
        couponComponent.onGetNewLiveCouponMsg(msg);
    }

    /**
     * 清空剪贴板内容
     */
    public static void clearClipboard() {

        ClipboardManager manager = (ClipboardManager) BaseApplication.getMyApplicationContext
                ().getSystemService(Context.CLIPBOARD_SERVICE);
        if (manager != null) {
            try {
                ClipData cd = manager.getPrimaryClip();
                if (null != cd) {
                    manager.setPrimaryClip(cd);
                }
                manager.setText(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showUserInfoCard(long targetId) {
        if (mOrientation == Configuration.ORIENTATION_PORTRAIT) {
            super.showUserInfoCard(targetId);
        }
    }

    @Override
    public boolean isHavePlayAuth() {
        return mPassAuth;
    }

    public int getLivePayType() {
        return mLivePayType;
    }

    public int getPlayType() {
        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);

        if (playerComponent != null) {
            return playerComponent.getCurrentPlayType();
        }
        return PlayerConstants.PLAYTYPE_LIVE;
    }

    // 收到小礼物展示pop条
    @Override
    public void addToSmallGiftPopTask(GiftShowTask task) {
        if (mOrientation == Configuration.ORIENTATION_PORTRAIT && LiveVideoPlayerManager.getInstance().getPlayerViewScaleValue() == 1) {
            try {
                LiveCourseEnterAndPopComponent component = getPresenter().getComponentManager().getComponent(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
                component.receiveGiftPopMessage(task);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onDestroyView() {
        clearClipboard();
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.setFloatWindowManagerCallback(null);
            mLiveVideoFloatWindowManager.release();
        }
        super.onDestroyView();
    }


    public void forbidFloatWindow() {
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.forbidFloatWindow();
        }

    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        super.onLogin(model);
        checkCourseLiveAuth();
    }

    @Override
    public void onBottomOperationViewShow(boolean show) {
        super.onBottomOperationViewShow(show);
        updateChatLayoutRightMargin();
    }

    private void updateChatLayoutRightMargin() {
        if (getChatListComponent() != null) {
            getChatListComponent().updateChatLayoutRightMargin();
        }
    }


    public void bottomClickStartMicButton() {

        //如果处于全屏状态，需要进行旋转
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        //检查登陆状况
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getContext(), LoginByConstants.LOGIN_BY_HALF_SCREEN);
            return;
        }

        if (mLiveRecordInfo != null && mLiveRecordInfo.getHostUid() == UserInfoMannage.getUid()) {
            CustomToast.showFailToast("同一账号无法同时连线");
            return;
        }

        //弹出连麦类型选择对话框
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            micComponent.showSelectMicTypeDialog();
        }
    }

    public void bottomClickMicRequestButton() {

        //如果处于全屏状态，需要进行旋转
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        //弹出结束连麦对话框
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            micComponent.showCancelRequestMicDialog();
        }
    }

    public void bottomClickMicConnectButton() {

        //如果处于全屏状态，需要进行旋转
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {//全屏模式下需要先旋转
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        //弹出结束连麦对话框
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            micComponent.showMicUserDialog();
        }
    }


    @Override
    public void onHostOpenMic(boolean isQueryMicStatus) {
        super.onHostOpenMic(false); //课程直播不会推消息，需要主动展示
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.onRoomMicOpen();
        }
    }

    @Override
    public void onHostCloseMic(boolean isQueryMicStatus) {
        super.onHostCloseMic(isQueryMicStatus);
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent != null) {
            bottomBarComponent.onRoomMicClose();
        }

    }

    public void onMicStatusChanged(int micType, int state) {
        // 连麦状态变化
        if (!canUpdateUi()) {
            return;
        }


        //底部工具栏按钮状态变化
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent == null) {
            return;
        }

        if (state == MicConstants.STATE_IDLE || state == MicConstants.STATE_HUNG_UP) {
            bottomBarComponent.onRoomMicStop();

            //断开连麦后检查一下是否需要加载广告
            loadRoomRightOperations();
        } else if (state == MicConstants.STATE_REQUESTING || state == MicConstants.STATE_CONNECTING) {
            bottomBarComponent.onRoomMicRequest(micType);

        } else if (state == MicConstants.STATE_CONNECTED) {
            bottomBarComponent.onRoomMicConnect(micType);
        }
    }

    public Boolean mIsOpenFlv;

    public void onAudienceGetHostPlayChange(boolean isOpenFlv) {
        if (mIsOpenFlv != null && mIsOpenFlv == isOpenFlv) {
            return;
        }
        mIsOpenFlv = isOpenFlv;

        CourseRoomCore roomCore = (CourseRoomCore) RoomCoreManager.getRoomCore(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE);
        if (roomCore == null) {
            return;
        }
        roomCore.setOpenFlv(mIsOpenFlv);

        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (playerComponent == null) {
            return;
        }
        if (mLiveRecordInfo == null || mLiveRecordInfo.getStatus() != PersonLiveBase.LIVE_STATUS_ING) {
            return;
        }

        if (isOpenFlv) {
            if (TextUtils.isEmpty(getLivePullStreamUrl()) || !isLiveAbleToPlay()) {
                return;
            }
            if (XmLiveRoom.sharedInstance(MainApplication.getMyApplicationContext()).isPublish()) {
                return;
            }
            startPlay();
        } else {
            playerComponent.stop();
            if (playerComponent.getCurrentPlayType() == PlayerConstants.PLAYTYPE_LIVE_SHIFT) {
                CustomToast.showToast("主播接通了您的连麦，已回到直播中");
            }
            if (mOrientation != Configuration.ORIENTATION_PORTRAIT) {//全屏模式下需要先旋转
                requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                CustomToast.showToast("连线中不支持屏幕翻转");
            }
        }

    }

    public void onMicViewShowing(boolean isShow) {
        isMicViewShow = isShow;
        updateChatLayoutRightMargin();

        //挂件的显示
        ISellGoodsComponent sellGoodsComponent = getComponentSafety(COMPONENT_SELL_GOOD);
        if (sellGoodsComponent != null) {
            sellGoodsComponent.setIsMicing(isShow);
        }
        ICourseRightAreaComponent roomRightComponent = getComponentSafety(COMPONENT_RIGHT_AD);
        if (roomRightComponent != null) {
            roomRightComponent.setIsMicing(isShow);
        }
    }

    public XmPlayerVideoView getPlayerView() {
        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);

        if (playerComponent != null) {
            return playerComponent.getPlayerView();
        }
        return null;
    }

    public void leaveMic() {
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            micComponent.leaveMic();
        }

    }

    public boolean isAudienceMicConnected() {
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            return micComponent.isAudienceMicConnected();
        }

        return false;
    }

    public boolean isAudienceMicProcessing() {
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            return micComponent.isAudienceMicProcessing();
        }

        return false;
    }


    public ILiveRoomDetail getLiveRoomDetail() {
        return mLiveRecordInfo;
    }

    public void startVideoTrySee() {
        if (!canUpdateUi()) {
            return;
        }

        isTrySeeing = true;

        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (mPullUrl != null && playerComponent != null) {
            if (XmLiveRoom.sharedInstance(MainApplication.getMyApplicationContext()).isPublish()) {
                return;
            }
            playerComponent.resumeLiveDirectly();
        }
    }

    public void stopTrySee() {

        isTrySeeing = false;

        //结束试看播放
        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (playerComponent != null) {
            playerComponent.stop();
            playerComponent.hidePlayer();

            //如果全屏，需要进行翻转
            //如果是全屏模式，需要进行旋转
            boolean isLand = DeviceUtil.isLandscape(getActivity());
            if (isLand) {
                requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
            }
        }

    }

    public boolean isTrySeeProcessing() {
        return isTrySeeing;
    }

    @Override
    public boolean isMicConnected() {
        return isAudienceMicConnected();
    }

    @Override
    public boolean isWaitMicConnecting() {
        return !isMicConnected() && isAudienceMicProcessing();
    }

    @Override
    public void leaveMicConnection(Runnable action) {
        super.leaveMicConnection(action);
        try {
            if (getMicAvService() != null) {
                IXmMicService micManager = getMicAvService();
                if (getMicAvService().getInitStatus() == SDKInitStatus.INIT_DONE) {
                    micManager.stopLocalPreview();
                    micManager.leaveRoom(false);
                }
                micManager.quitJoinAnchor(null);
            }
            leaveMic();
            action.run();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public IXmMicService getAvService() {
        return LiveClientManager.getInstance().getLiveMicService();
    }

    @Override
    public void onFansClubInfoChange(LiveUserInfo.FansClubVoBean fansClubVoBean) {
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.onFansClubInfoChange(fansClubVoBean);
        }

        Component comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp instanceof ICourseChatListComponent) {
            ((ICourseChatListComponent) comp).updateJoinFansClubMsgStatus(fansClubVoBean.isJoinFansClub());
        }
    }

    @Override
    public boolean isKeyboardPanelShowed() {
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        return inputComponent != null && inputComponent.isKeyboardPanelShowed();
    }

    @Override
    public void onReceiveCourseShopMessage(CommonCourseShopMessage message) {
        super.onReceiveCourseShopMessage(message);
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent == null) {
            return;
        }
        bottomBarComponent.onReceiveCourseShopMessage(message);
    }

    @Override
    public void onReceiveGiftMessage(CommonChatGiftMessage giftMessage) {
        if (LiveVideoPlayerManager.getInstance().getPlayerViewScaleValue() > 1) {
            return;
        }
        super.onReceiveGiftMessage(giftMessage);
    }

    @Override
    public void onReceivedFansClubUpdateMessage(CommonChatRoomFansClubUpdateMessage message) {
        super.onReceivedFansClubUpdateMessage(message);
        if (message == null) {
            return;
        }

        if (getLiveRoomDetail() != null) {
            getLiveRoomDetail().updateFansClubCount(message.cnt);
            if (message.type == TYPE_JOIN) {
                getLiveRoomDetail().updateFansClubJoinStatus(true);
            }
        }

        if (message.type == TYPE_JOIN) {
            ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
            if (headerComponent != null) {
                headerComponent.updateFansJoinSuccessState();
            }

            Component comp = getComponentSafety(COMPONENT_CHAT_LIST);
            if (comp instanceof ICourseChatListComponent) {
                ((ICourseChatListComponent) comp).updateJoinFansClubMsgStatus(true);
            }
        }
    }

    @Override
    public void onReceiveFansGroupMsg(CommonFansGroupMsg msg) {
        super.onReceiveFansGroupMsg(msg);

        if (msg != null && msg.type == CommonFansGroupMsg.FansMsgType.TYPE_OPEN) {
            Component comp = getComponentSafety(COMPONENT_CHAT_LIST);
            if (comp instanceof ICourseChatListComponent) {
                ((ICourseChatListComponent) comp).updateJoinFansClubMsgStatus(true);
            }
        }
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.showFansGroupUpdateDialog(msg);
        }
        if (mLiveCourseRoomPresenter == null || mLiveRecordInfo == null) {
            return;
        }
        if (mPresenter != null && canUpdateUi() && msg != null && msg.type == CommonFansGroupMsg.FansMsgType.TYPE_OPEN) {
            //非活跃用户退团再加团，需要查用户信息更新活跃状态
            mLiveCourseRoomPresenter.requestCourseRoomUserInfo(mLiveRecordInfo.getLiveId());
        }
    }

    @Override
    public void onReceiveFansClubStatusMessage(LiveFansClubStatusModel message) {
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (message == null || headerComponent == null) {
            return;
        }
        headerComponent.updateFansClubActiveStatus(message);

        if (mLiveCourseRoomPresenter == null || mLiveRecordInfo == null) {
            return;
        }
        mLiveCourseRoomPresenter.requestCourseRoomUserInfo(mLiveRecordInfo.getLiveId());
    }

    /**
     * 收到 房间粉丝团亲密度信令
     * @param msg 更新房间亲密度倍数消息
     */
    @Override
    public void onReceiveUpdateFansIntimacyMsg(CommonChatFansIntimacyMsg msg) {
        super.onReceiveUpdateFansIntimacyMsg(msg);
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (msg == null || headerComponent == null) {
            return;
        }
        headerComponent.onReceiveUpdateFansIntimacyMsg(msg);
    }

    @Override
    public void showGiftPanelByGiftId(long giftId, long anchorUid) {
        NewAudienceAwardInfo info = new NewAudienceAwardInfo("");
        info.id = giftId;
        try {
            ICourseGiftPanelComponent videoGiftPanelNewComponent = getComponentSafety(COMPONENT_GIFT_PANEL);
            if (videoGiftPanelNewComponent != null) {
                videoGiftPanelNewComponent.showGiftPanelAndSelectGiftItem(info);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onKickOutChatRoom() {
        super.onKickOutChatRoom();
        // 停止连麦推流
        XmLiveRoom.destroySharedInstance();
        // 停止拉流
        ICourseVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);

        if (playerComponent != null) {
            playerComponent.stop();
        }
        // 停止拉流
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            inputComponent.hide();
        }
        // 隐藏直播间所有弹窗
        hideAllDialog();
    }

    public void hideAllDialog() {
        ICourseMicComponent micComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_MIC);
        if (micComponent != null) {
            micComponent.dismiss();
        }
        try {
            ICourseGiftPanelComponent videoGiftPanelNewComponent = getComponentSafety(COMPONENT_GIFT_PANEL);
            if (videoGiftPanelNewComponent != null) {
                videoGiftPanelNewComponent.hideAll();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (getCustomH5Dialog() != null && getCustomH5Dialog().isShowing()) {
            getCustomH5Dialog().dismiss();
        }
        hideMoreLiveDialog();
        dismissOnlinePageH5Dialog();
        dismissCustomH5Dialog();
        hideRoomSoftInput();

        ICourseUserInfoCardComponent userInfoCardComponent = getExistComponent(CourseCompConfig.COMPONENT_COURSE_USER_INFO_CARD);
        if (userInfoCardComponent != null) {
            userInfoCardComponent.dismiss();
        }

        ICourseCouponComponent couponComponent = getExistComponent(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (couponComponent != null) {
            couponComponent.dismissCouponDialog();
        }
        LiveDialogFragmentManager.INSTANCE.hideAllDialog();
    }

    public void hideRoomSoftInput() {
        try {
            ICourseInputPanelComponent inputPanelComponent = getExistComponent(IBaseRoomCompConfig.COMPONENT_INPUT_PANEL);
            if (inputPanelComponent != null) {
                inputPanelComponent.hide();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onReceiveNotifyBottomButton(CommonChatRoomNotifyBottomButtonMsg msg) {
        Logger.i(TAG, "onReceiveNotifyBottomButton, msg = " + msg);
        super.onReceiveNotifyBottomButton(msg);
        if (msg == null) {
            return;
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (bottomBarComponent == null) {
            return;
        }
        bottomBarComponent.requestBottomButtonList(msg.position, true);
    }

    public void bottomClickEmoji() {
        if (!UserInfoMannage.hasLogined()) {

            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
            UserInfoMannage.gotoLogin(getActivity(), LoginByConstants.LOGIN_BY_HALF_SCREEN);
            return;
        }
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            inputComponent.showAndOpenEmotionPanel();
        }

        moveToBottomChatList();
    }

    @Override
    public void onLiveVideoInfoChangeReceived(CommonLiveVideoInfoChangeMessage message) {
        super.onLiveVideoInfoChangeReceived(message);
        ICourseHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (headerComponent == null) {
            return;
        }
        headerComponent.onLiveVideoInfoChangeReceived(message);
    }

    @Override
    public void showInputPanel() {
        super.showInputPanel();
        if (!canUpdateUi()) {
            return;
        }
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            inputComponent.show();
        }
    }

    @Override
    protected IRoomSchemaGenerator getRoomSchemaGenerator() {
        IRoomSchemaGenerator generator = super.getRoomSchemaGenerator();
        generator.appendParams("live_id", String.valueOf(mLiveId));
        generator.appendParams("album_id", String.valueOf(mAlbumId));
        return generator;
    }

    @Nullable
    private ICourseChatListComponent getChatListComponent() {
        Component comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp instanceof ICourseChatListComponent) {
            return (ICourseChatListComponent) comp;
        }

        return null;
    }

    public boolean isBottomAuthLayoutVisible() {
        ICourseAuthComponent authComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_AUTH);
        return authComponent != null && authComponent.isAuthLayoutVisible();
    }

    protected void showFinishDialogForAudience(int preStatus) {
        try {
            int abTest = LiveAbtestUtil.getAutoEnterLiveRoomConfig();
            if (mLiveRecordInfo != null && (abTest == LiveAbtestUtil.AUTO_ENTER_ROOM_FROM_COVER || abTest == LiveAbtestUtil.AUTO_ENTER_ROOM_FROM_BANNER)) {
                if (!mLiveRecordInfo.hasPlayBack() || com.ximalaya.ting.android.framework.arouter.utils.TextUtils.isEmpty(mLiveRecordInfo.getPlayBackPath())) {
                    ICourseCountDownComponent countDownComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_COUNT_DOWN);
                    if (countDownComponent != null) {
                        countDownComponent.registerRecommendRequestListener(new IRecommendRequestListener() {
                            @Override
                            public void onRequestRecommendLive(List<VideoLiveRecommendInfo> data) {
                                countDownComponent.registerRecommendRequestListener(null);
                                boolean hasRecommendLive = false;
                                IRecommendLiveComponent recommendComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_RECOMMEND);

                                if (data != null && !data.isEmpty()) {
                                    for (int i = 0; i < data.size(); i++) {
                                        if (data.get(i).isPlaybackStatus()) {
                                            hasRecommendLive = true;
                                            break;
                                        }
                                    }
                                    if (hasRecommendLive) {
                                        if (recommendComponent != null) {
                                            hideAllDialog();
                                            recommendComponent.showRecommendLive(getStringSafe(R.string.live_watch_playback), abTest, (BaseRoomDetail)mLiveRecordInfo);
                                        }
                                        return;
                                    }
                                }
                                if (recommendComponent != null) {
                                    hideAllDialog();
                                    if (mLiveRecordInfo instanceof BaseRoomDetail) {
                                        recommendComponent.showRecommendLive(preStatus == PersonLiveBase.LIVE_STATUS_ING ?
                                                getStringSafe(R.string.live_return_room) : getStringSafe(R.string.live_enter_room), abTest, (BaseRoomDetail)mLiveRecordInfo);
                                    }

                                }
                            }
                        });
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}