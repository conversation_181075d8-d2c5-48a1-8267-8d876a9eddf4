package com.ximalaya.ting.android.live.video.adapter.countdown.rv;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.live.LivePlaySourceUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper;
import com.ximalaya.ting.android.live.video.R;
import com.ximalaya.ting.android.live.video.data.model.VideoLiveRecommendInfo;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * Created by zhixin.he on 2022/8/17.
 *
 * @desc 推荐列表条目
 * @email <EMAIL>
 * @phone 15026804470
 */
public class RecommendRVHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private final Context mContext;
    private final RoundImageView mIvRecommend;
    private final TextView mTvStatus;
    private final TextView mTvTitle;
    private final ImageView mIvStatus;
    private OnRecommendListener mOnRecommendListener;
    private VideoLiveRecommendInfo mInfo;
    private int mPosition;


    public RecommendRVHolder(@NonNull View itemView, Context context) {
        super(itemView);
        mContext = context;
        mIvRecommend = itemView.findViewById(R.id.live_video_recommend_iv);
        mTvStatus = itemView.findViewById(R.id.live_video_recommend_status_tv);
        mIvStatus = itemView.findViewById(R.id.live_video_recommend_status_iv);
        mTvTitle = itemView.findViewById(R.id.live_video_recommend_title_tv);
        mIvRecommend.setOnClickListener(this);
    }

    protected void onBindViewHolder(VideoLiveRecommendInfo info, OnRecommendListener onRecommendListener, int position) {
        if (info == null || mContext == null || mContext.getResources() == null
                || mTvTitle == null || mTvStatus == null || mIvRecommend == null) {
            return;
        }
        mInfo = info;
        mPosition = position;
        mTvTitle.setText(info.getName());
        ImageManager.from(mContext).displayImage(mIvRecommend, info.getCoverSmall(), R.drawable.live_video_iv_default);
        switch (info.getStatus()) {
            case PersonLiveBase.LIVE_STATUS_NOTICE:
                mTvStatus.setText("预告");
                ViewStatusUtil.setVisible(View.GONE, mIvStatus);
                break;
            case PersonLiveBase.LIVE_STATUS_ING:
                mTvStatus.setText("直播中");
                ViewStatusUtil.setVisible(View.VISIBLE, mIvStatus);
                break;
            case PersonLiveBase.LIVE_STATUS_END:
                if (info.isPlaybackStatus()) {
                    mTvStatus.setText("回放");
                }
                ViewStatusUtil.setVisible(View.GONE, mIvStatus);
                break;
            default:
                break;
        }
        mOnRecommendListener = onRecommendListener;
        // 课程直播_直播状态面板_直播卡片  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(47637)
                .setServiceId("slipPage")
                .put("currPage", "videoLive")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .put("positionNew", String.valueOf(mPosition + 1)) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("recommendLiveId", String.valueOf(mInfo.getId()))
                .put("itemLiveBroadcastStatus", String.valueOf(mInfo.getStatus())) // 9-正在直播；5-预告；1-结束中
                .put(XmLiveRequestIdHelper.createTracePropsForSlipPage(mInfo.xmRequestId, String.valueOf(mInfo.getId())))
                .createTrace();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.live_video_recommend_iv) {
            if (mOnRecommendListener == null) {
                return;
            }
            if (mInfo == null || TextUtils.isEmpty(mInfo.getItingUrl())) {
                return;
            }
            String itingUrl = LivePlaySourceUtil.appendPlaySourceParamToITing(mInfo.getItingUrl(), ILivePlaySource.SOURCE_LIVE_COURSE_ROOM_FINISH_RECOMMEND);
            mOnRecommendListener.onClickRecommend(itingUrl);
            // 课程直播_直播状态面板_直播卡片  点击事件
            new XMTraceApi.Trace()
                    .click(47636) // 用户点击时上报
                    .put("currPage", "videoLive")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .put("positionNew", String.valueOf(mPosition + 1)) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("recommendLiveId", String.valueOf(mInfo.getId()))
                    .put("itemLiveBroadcastStatus", String.valueOf(mInfo.getStatus())) // 9-正在直播；5-预告；1-结束中
                    .createTrace();
        }
    }

    public interface OnRecommendListener {
        void onClickRecommend(String itingUrl);
    }
}
