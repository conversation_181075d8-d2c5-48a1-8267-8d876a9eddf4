package com.ximalaya.ting.android.live.video.fragment;

import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HEADER;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_INPUT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_RIGHT_AD;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_SELL_GOOD;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_VIDEO_PLAYER;
import static com.ximalaya.ting.android.live.host.view.bottom.LiveBottomInputView.LOG_FAIL;
import static com.ximalaya.ting.android.live.host.view.bottom.LiveBottomInputView.LOG_SUCCESS;
import static com.ximalaya.ting.android.live.video.components2.CourseCompConfig.COMPONENT_COURSE_AUTH;
import static com.ximalaya.ting.android.live.video.components2.CourseCompConfig.COMPONENT_COURSE_GOODS_LIST;
import static com.ximalaya.ting.android.live.video.components2.CourseCompConfig.COMPONENT_COURSE_HALF_SCREEN_HYBRID;
import static com.ximalaya.ting.android.live.video.components2.CourseCompConfig.COMPONENT_COURSE_MIC;
import static com.ximalaya.ting.android.live.video.components2.CourseCompConfig.COMPONENT_COURSE_OPEN_NOTIFI;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileProviderUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.ImageMultiPickFragment;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.IPhotoAction;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.model.image.ImgItem;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.im.xchat.util.UnBoxUtil;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatItemViewType;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatListViewConstant;
import com.ximalaya.ting.android.live.common.component.playbacksetting.manager.LiveTerminateManager;
import com.ximalaya.ting.android.live.common.dialog.web.CommonXmlObjcJsCall;
import com.ximalaya.ting.android.live.common.input.IKeyboardHostFragment;
import com.ximalaya.ting.android.live.common.input.emoji.IEmojiItem;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.icons.LiveIconsManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveCommonDialogManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LivePlayRestoreUtil;
import com.ximalaya.ting.android.live.common.lib.utils.WealthIconCacheUtil;
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.clearScreen.LiveClearScreenManager;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.chatlist.IBaseChatListComponent;
import com.ximalaya.ting.android.live.host.components.exitroom.IExitRoomComponent;
import com.ximalaya.ting.android.live.host.components.sellgoods.ISellGoodsComponent;
import com.ximalaya.ting.android.live.host.fragment.room.BaseRoomFragment;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonCdnStatusMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatAnchorMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatAudienceMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGetRedPacketMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftComboOverMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatHotTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatRedPacketMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatShareLiveRoomMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatTimedRedPacketMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUser;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUserJoinMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonFloatScreenMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonWithdrawChatMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomPublishTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonBusinessMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAnchorVerifyWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNoticeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomRuleInfoUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomSkinUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomStatusChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomTopicUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsInfoChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonPushJsData;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonSimpleTxtMsg;
import com.ximalaya.ting.android.live.lib.livetopic.AnnounceDataCache;
import com.ximalaya.ting.android.live.lib.redenvelope.model.IRedPacketMessage;
import com.ximalaya.ting.android.live.lib.stream.live.data.LivePullUrls;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.video.components.bottombar.ICourseBottombarComponent;
import com.ximalaya.ting.android.live.video.components.chatlist.chat.CourseChatListMsgProducer;
import com.ximalaya.ting.android.live.video.components.chatlist.chat.ICourseChatListComponent;
import com.ximalaya.ting.android.live.video.components.chatlist.guide.ICourseChatListGuideComponent;
import com.ximalaya.ting.android.live.video.components.countdown.ICourseCountDownComponent;
import com.ximalaya.ting.android.live.video.components.gift.CourseGiftPanelComponent;
import com.ximalaya.ting.android.live.video.components.gift.CourseVideoGiftLoader;
import com.ximalaya.ting.android.live.video.components.gift.ICourseGiftPanelComponent;
import com.ximalaya.ting.android.live.video.components.goodslist.ICourseGoodsListComponent;
import com.ximalaya.ting.android.live.video.components.header.ICourseHeaderComponent;
import com.ximalaya.ting.android.live.video.components.hybrid.ICourseHalfScreenHybridComponent;
import com.ximalaya.ting.android.live.video.components.input.ICourseInputPanelComponent;
import com.ximalaya.ting.android.live.video.components.liveauth.ICourseAuthComponent;
import com.ximalaya.ting.android.live.video.components.opennotice.ICourseOpenNotifiComponent;
import com.ximalaya.ting.android.live.video.components.pop.LiveCourseEnterAndPopComponent;
import com.ximalaya.ting.android.live.video.components.rightarea.ICourseRightAreaComponent;
import com.ximalaya.ting.android.live.video.components.usercard.ICourseUserInfoCardComponent;
import com.ximalaya.ting.android.live.video.components.videoplayer.ICourseVideoPlayerComponent;
import com.ximalaya.ting.android.live.video.components2.CourseCompConfig;
import com.ximalaya.ting.android.live.video.components2.CourseComponentHost;
import com.ximalaya.ting.android.live.video.data.model.CourseRoleType;
import com.ximalaya.ting.android.live.video.data.model.CourseRoomDetail;
import com.ximalaya.ting.android.live.video.data.model.ILiveUserInfo;
import com.ximalaya.ting.android.live.video.data.request.CommonRequestForLiveCourse;
import com.ximalaya.ting.android.live.video.data.systemnotice.EnterRoomInfo;
import com.ximalaya.ting.android.live.video.data.systemnotice.OnlineStatusInfo;
import com.ximalaya.ting.android.live.video.data.systemnotice.RoomStatusInfo;
import com.ximalaya.ting.android.live.video.fragment.dialog.LiveCourseWarningDialog;
import com.ximalaya.ting.android.live.video.roomcore.CourseRoomCore;
import com.ximalaya.ting.android.live.video.roomcore.timeshift.IRoomTimeShiftService;
import com.ximalaya.ting.android.live.video.util.LiveCourseShareUtils;
import com.ximalaya.ting.android.live.video.util.LiveCourseUtils;
import com.ximalaya.ting.android.live.video.view.header.VideoLiveRoomStatusView;
import com.ximalaya.ting.android.liveim.chatroom.entity.chat.CustomMessage;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程直播间基类。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817870952
 */
public abstract class BaseCourseRoomFragment extends BaseRoomFragment<ILiveCourseRoom.IPresenter>
        implements ILiveCourseRoom.IView,
        IFragmentFinish,
        IPhotoAction,
        ILoginStatusChangeListener,
        AnchorFollowManage.IFollowAnchorListener {

    private static final String TAG = "BaseLiveVideoFragment";

    private LiveVideoRoomLocalBroadcastReceiver mLiveVideoRoomLocalBroadcastReceiver;

    protected ILiveRoomDetail mLiveRecordInfo;//直播场次信息

    protected ILiveUserInfo mLiveUserInfo; // 房间相关个人信息

    protected boolean isFirstQueryHistoryMsg = true;
    protected boolean isQueryHistoryMsg = false;

    protected boolean isNormalOperationViewShow = false; // 普通底部挂件是否显示
    protected boolean isMicViewShow = false; // 连麦小窗是否显示

    //首次进入一个直播间埋点上报
    private boolean isTrackRoomWhenFirstEnterThisRoom = true;
    private boolean isFirstResume = true;

    /**
     * 主播强提示类对话框 目前只有一个对话框，状态不同
     */
    protected LiveCourseWarningDialog mWarningCacheDialog;

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected ILiveCourseRoom.IPresenter createPresenter() {
        return null;
    }

    @Override
    protected void initBizId() {
        mBusinessId = IBusinessIdConstants.BIZ_ID_VIDEO_COURSE;
    }

    @Override
    public int getRoomBizType() {
        return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE;
    }

    @Override
    protected void initBizManagers() {

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 进入房间时一定是竖屏，先计算好全屏时右侧面板的宽度
        LiveCourseUtils.getFullscreenRightPanelWidth();
        // 监听关注状况变化
        AnchorFollowManage.getSingleton().addFollowListener(this);

    }

    @Override
    protected void initMyUi(Bundle savedInstanceState) {
        try {
            Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction().setAllowUseMobileNetwork(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected void initGiftQueue() {
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 163851;
        super.onMyResume();
        registerLocalReceiver();
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent != null) {
            videoPlayerComponent.resumeLive();
        }

        //注册相机的拍照监听
        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).addPhotoActionListener(this);
        }
        if (mLiveRecordInfo != null && !isFirstResume) {
            trackRoom();
        }
        isFirstResume = false;
    }

    @Override
    public void onPause() {
        unregisterLocalReceiver();
        trackLeaveRoom();
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        if (LivePlayRestoreUtil.isClickMinimumLiveRoom()) {
            LivePlayRestoreUtil.markClickMinimumLiveRoom(false);
        }

        WealthIconCacheUtil.release();

        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).removePhotoActionListener(this);
        }

        //注销关注情况的监听
        AnchorFollowManage.getSingleton().removeFollowListener(this);

        super.onDestroyView();
    }

    @Override
    protected void loadMyData() {
        super.loadMyData();
        checkCourseLiveAuth();
        if (mLiveId > 0) {
            mPresenter.requestCourseRoomUserInfo(mLiveId);
        }

    }

    protected void checkCourseLiveAuth() {
    }

    public IXmMicService getMicAvService() {
        return LiveClientManager.getInstance().getLiveMicService();
    }

    public IRoomTimeShiftService getTimeShiftService() {
        if (getRoomCore() != null && getRoomCore().getRoomCoreService(CourseRoomCore.SERVICE_SHIFT) != null) {
            return (IRoomTimeShiftService) getRoomCore().getRoomCoreService(CourseRoomCore.SERVICE_SHIFT);
        }
        return null;
    }

    @Override
    public ViewGroup getRootView() {
        return (ViewGroup) mContainerView;
    }

    @Override
    public Fragment getFragment() {
        return this;
    }

    public boolean isAnchor() {
        return false;
    }

    public boolean currentUserIsAdmin() {
        if (mLiveUserInfo == null) return false;
        return mLiveUserInfo.getRoleType() == CourseRoleType.ADMIN;
    }

    public String getLivePullStreamUrl() {
        return null;
    }

    @Override
    public void showUserInfoCard(long targetId) {

        int myRole = CourseRoleType.AUDIENCE;

        if (mLiveUserInfo != null && mLiveUserInfo.getRoleType() > 0) {
            myRole = mLiveUserInfo.getRoleType();
        }

        ICourseUserInfoCardComponent userInfoCardComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_USER_INFO_CARD);
        if (userInfoCardComponent != null) {
            userInfoCardComponent.show(mLiveId, mRoomId, myRole, targetId, getHostUid());
        }

    }

    /**
     * 打开举报页面
     *
     * @param isReportLive 是举报直播 false 举报个人
     * @param liveId       直播room_id
     * @param targetUid    举报对象uid
     */
    public void openReportPage(boolean isReportLive, long liveId, long targetUid) {

        if (liveId <= 0 && targetUid < 0) {
            return;
        }

        //如果是全屏模式，需要进行旋转
        boolean isLand = DeviceUtil.isLandscape(getActivity());

        if (isLand) {
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }

        //判断登陆
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_HALF_SCREEN);
            return;
        }

        try {
            BaseFragment reportFragmentLive = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newReportFragmentByCourseLive(liveId, targetUid, UserInfoMannage.getUid(), mRoomId);
            if (reportFragmentLive != null) {
                startFragment(reportFragmentLive);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void registerLocalReceiver() {
        if (mLiveVideoRoomLocalBroadcastReceiver == null) {
            mLiveVideoRoomLocalBroadcastReceiver = new LiveVideoRoomLocalBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE);
            intentFilter.addAction(ChatListViewConstant.ACTION_CHAT_LIST_CLICK_NICKNAME);
            intentFilter.addAction(ChatListViewConstant.ACTION_CHAT_LIST_LONG_CLICK_NICKNAME);
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_GIFT_DIALOG);
            intentFilter.addAction(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_CLOSE_CURRENT_PAGE);
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_SHARE_ROOM);
            LocalBroadcastManager.getInstance(mContext).registerReceiver(mLiveVideoRoomLocalBroadcastReceiver, intentFilter);
        }
    }

    private void unregisterLocalReceiver() {
        if (mLiveVideoRoomLocalBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLiveVideoRoomLocalBroadcastReceiver);
            mLiveVideoRoomLocalBroadcastReceiver = null;
        }
    }

    @Override
    protected void onDisconnectChatRoom() {

    }

    @Override
    protected void onConnectedChatRoom() {

    }

    @Override
    public void onChatRoomJoinResult(boolean success, int code, String msg) {
        super.onChatRoomJoinResult(success, code, msg);
        getComponentSafety(COMPONENT_COURSE_MIC);
        ICourseBottombarComponent bottombarComponent = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (bottombarComponent != null) {
            bottombarComponent.updateInputViewStatus(success ? LOG_SUCCESS : LOG_FAIL);
        }
        if (success) {
            if (mLiveRecordInfo == null) {
                return;
            }
            if (mLiveRecordInfo.getStatus() == 1 && isFirstQueryHistoryMsg && mBusinessId == IBusinessIdConstants.BIZ_ID_VIDEO_COURSE) {
                isQueryHistoryMsg = true;
                if (mPresenter != null) {
                    mPresenter.queryHistoryMsg(getRoomId(), mLiveRecordInfo.getLiveStartAt(), mLiveRecordInfo.getLiveStopAt(), 0, 0, 15, true);
                }
            } else {
                ICourseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
                if (comp != null) comp.onQueryHistoryMessageReceived(new ArrayList<>());
            }
        }
    }

    @Override
    public void showGiftPanel() {
        ICourseGiftPanelComponent videoGiftPanelNewComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_GIFT_PANEL);
        if (videoGiftPanelNewComponent != null) {
            videoGiftPanelNewComponent.show();
        }
    }

    @Override
    public void showGiftPanel(long selectedTargetId) {

    }

    @Override
    public void showGiftPanelByGiftId(long giftId, long anchorUid) {

    }

    @Override
    public void onReceiveChatMessage(CommonChatMessage chatMessage) {
        super.onReceiveChatMessage(chatMessage);
        if (!canUpdateUi()) {
            return;
        }
        if (chatMessage != null) {
            if (chatMessage.mColor == 0) {
                chatMessage.mColor = ChatListViewConstant.COLOR_NORMAL_CONTENT_KTV;
            }
            chatMessage.mUserNickNameColor = ChatListViewConstant.COLOR_NOTICE_VIDEO;

            if (mHostUid > 0 && chatMessage.getSenderUid() == mHostUid) {
                chatMessage.mSender.mIsHost = true;
            }
        }

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.onMessageReceived(chatMessage);
    }

    @Override
    public void onReceiveGiftComboOverMessage(CommonChatGiftComboOverMessage giftComboOverMessage) {
        super.onReceiveGiftComboOverMessage(giftComboOverMessage);

        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_GIFT, giftComboOverMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }


    @Override
    public void addChatAnchorMessage(CommonChatAnchorMessage chatAnchorMessage) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.createAnchorMsg(
                chatAnchorMessage, isAnchor(), currentUserIsAdmin()
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void onReceiveMyInfoUpdateMessage() {
        if (mLiveRecordInfo != null && mPresenter != null) {
            mPresenter.requestCourseRoomUserInfo(mLiveRecordInfo.getLiveId());
        }
    }

    @Override
    public void onReceiveGameRulesUpdateMessage(String ruleInfo) {

    }

    @Override
    public void onReceiveTitleUpdateMessage(String title) {

    }

    @Override
    public void onReceivedTopicUpdateMessage(CommonChatRoomTopicUpdateMessage message) {
        if (message != null && getAbsRoomDetail() instanceof CourseRoomDetail) {
            getAbsRoomDetail().updateTopic(message.topicName);
            getAbsRoomDetail().updateDescription(message.txt);
        }
    }

    @Override
    public void onReceiveRoomSkinUpdateMessage(CommonChatRoomSkinUpdateMessage skinUpdateMessage) {

    }

    @Override
    protected void highEnterRoom(CommonChatUserJoinMessage userJoinMessage) {
        try {
            LiveCourseEnterAndPopComponent component = getPresenter().getComponentManager().getComponent(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            component.receiveEnterRoomMessage(userJoinMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void setEnterRoomMsgIsHostFragment(CommonChatUserJoinMessage userJoinMessage) {
        userJoinMessage.isHostFragment = false;
    }

    @Override
    protected boolean isShowEnterAnim(CommonChatUserJoinMessage userJoinMessage) {
        return false;
    }

    @Override
    protected void normalEnterRoom(ChatItemViewType itemViewType, CommonChatUserJoinMessage userJoinMessage) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                itemViewType, userJoinMessage
        );
        if (msg != null) {
            onReceiveChatMessage(msg);
        }
    }

    @Override
    public void onReceiveRoomCloseMessage(String reason) {
        CustomToast.showToast(reason);
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent != null) {
            videoPlayerComponent.stop();
        }
    }

    @Override
    public void onReceiveCommonBusinessMessage(CommonBusinessMsg message) {
        super.onReceiveCommonBusinessMessage(message);
    }

    @Override
    public void onReceiveShareRoomLiveMessage(CommonChatShareLiveRoomMessage message) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SHARE_ROOM, message
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void onReceiveCacheMessage(List<CommonChatMessage> cacheMessages) {
        if (cacheMessages == null || cacheMessages.isEmpty()) {
            return;
        }

        List<CommonChatMessage> messages = new ArrayList<>();
        for (CommonChatMessage chatMessage : cacheMessages) {
            if (chatMessage != null) {
                if (chatMessage.mColor == 0) {
                    chatMessage.mColor = ChatListViewConstant.COLOR_NORMAL_CONTENT_KTV;
                }
                chatMessage.mUserNickNameColor = ChatListViewConstant.COLOR_NOTICE_VIDEO;

                if (mHostUid > 0 && chatMessage.getSenderUid() == mHostUid) {
                    chatMessage.mSender.mIsHost = true;
                } else {
                    chatMessage.mSender.mIsAdmin = isSenderAdmin(chatMessage);
                }
                messages.add(chatMessage);
            }
        }

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.onCacheMessageReceived(messages);
    }


    private boolean isSenderAdmin(CommonChatMessage chatMessage) {
        if (chatMessage.extendInfo == null) {
            return false;
        }

        try {
            JSONObject jsonObject = new JSONObject((String) chatMessage.extendInfo);

            if (jsonObject.has("role")) {
                return jsonObject.optInt("role") == 2;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;

    }

    @Override
    public void onReceiveRoomStatusChangeMessage(final CommonChatRoomStatusChangeMessage roomStatusChangeMessage) {
        super.onReceiveRoomStatusChangeMessage(roomStatusChangeMessage);
        CustomToast.showDebugFailToast("收到直播间状态变化通知：" + roomStatusChangeMessage.status + " （1为直播结束，9为正在直播）");
        LiveHelper.Log.i(TAG, "onReceiveRoomStatusChangeMessage:" + roomStatusChangeMessage.status);


        if (mLiveRecordInfo.getStatus() == PersonLiveBase.LIVE_STATUS_END && roomStatusChangeMessage.status == PersonLiveBase.LIVE_STATUS_ING) {
            if (!canUpdateUi()) {
                return;
            }
            // 移除关播重新开播后 检查直播间接口，和ios保持一致 （和梓铭确认）
            updateRoomStatusByStatusMsg(roomStatusChangeMessage);

            ICourseChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
            if (comp != null) comp.removeFollowHostGuide();
        } else {

            updateRoomStatusByStatusMsg(roomStatusChangeMessage);

        }

    }

    private void updateRoomStatusByStatusMsg(CommonChatRoomStatusChangeMessage roomStatusChangeMessage) {

        boolean isChangeStatus = roomStatusChangeMessage.status != mLiveRecordInfo.getStatus();

        if (!isChangeStatus) {
            return;
        }

        mLiveRecordInfo.setLiveStatus(roomStatusChangeMessage.status);
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(COMPONENT_BOTTOM_BAR);
        ICourseHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        ISellGoodsComponent sellGoodsComponent = getComponentSafety(COMPONENT_SELL_GOOD);

        switch (roomStatusChangeMessage.status) {
            case PersonLiveBase.LIVE_STATUS_END: // 直播结束
                // 定时退出逻辑判断
                if (LiveTerminateManager.needExitRoomWhenLiveEnd()) {
                    exitRoomUnstoppable();
                    return;
                }
                if (videoPlayerComponent != null) {
                    videoPlayerComponent.hidePlayer();
                    videoPlayerComponent.hideLoading();
                    videoPlayerComponent.setLiveFinish(true);
                }
                if (headerComponent != null) {
                    headerComponent.updateConnectedStatus(VideoLiveRoomStatusView.LIVE_END);
                }
                ICourseGoodsListComponent goodsListComponent = getComponentSafety(COMPONENT_COURSE_GOODS_LIST);
                if (goodsListComponent != null) {
                    goodsListComponent.hideGoodsList();
                }
                if (sellGoodsComponent != null) {
                    sellGoodsComponent.setIsLiving(false);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.setIsLiving(false);
                }

                ICourseAuthComponent authComponent = getComponentSafety(COMPONENT_COURSE_AUTH);
                if (authComponent != null) {
                    authComponent.stopTrySee(true);
                }

                loadDetail();
                if (LiveClearScreenManager.getInstance().isClear()) {
                    LiveClearScreenManager.getInstance().restoreShowScreen();
                }
                break;
            case PersonLiveBase.LIVE_STATUS_NOTICE: // 将要直播
                if (headerComponent != null) {
                    headerComponent.updateConnectedStatus(VideoLiveRoomStatusView.LIVE_PRE_ANNOUNCE);
                }
                if (videoPlayerComponent != null) {
                    videoPlayerComponent.hidePlayer();
                }
                if (sellGoodsComponent != null) {
                    sellGoodsComponent.setIsLiving(false);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.setIsLiving(false);
                }
                break;
            case PersonLiveBase.LIVE_STATUS_ING: // 正在直播中
                if (videoPlayerComponent != null) {
                    videoPlayerComponent.showPalyer();
                    videoPlayerComponent.hideLoading();
                    videoPlayerComponent.setLiveFinish(false);
                }
                if (headerComponent != null) {
                    headerComponent.updateConnectedStatus(VideoLiveRoomStatusView.LIVE_PLAYING);
                }
                if (sellGoodsComponent != null) {
                    sellGoodsComponent.setIsLiving(true);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.setIsLiving(true);
                }
                loadRoomData();
                break;
        }
        ICourseCountDownComponent countDownComponent = getComponentSafety(CourseCompConfig.COMPONENT_COURSE_COUNT_DOWN);
        if (countDownComponent != null) {
            countDownComponent.updateLiveStatus(roomStatusChangeMessage.status);
        }
    }

    protected int mPreRoomStatus = -1;
    protected void showFinishDialogForAudience(int preStatus) {

    }

    @Override
    public void onReceiveHostOnlineListMessage(CommonChatRoomOnlineUserListMsg msg) {
        super.onReceiveHostOnlineListMessage(msg);
        LiveHelper.Log.i(TAG, "onReceiveHostOnlineListMessage:" + msg);
        if (msg != null && getAbsRoomDetail() instanceof CourseRoomDetail) {
            CourseRoomDetail detail = (CourseRoomDetail) getAbsRoomDetail();
            detail.hotScore = msg.hotScore;
            detail.onlineCount = msg.onlineCount;
            detail.playCount = msg.playCnt;
        }
        if (!canUpdateUi()) {
            return;
        }
        ICourseHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.onOnlineStatusChange(msg);
        }
    }

    @Override
    public void onReceiveRoomCustomMessage(CustomMessage message) {
        super.onReceiveRoomCustomMessage(message);
        if (message != null) {

            boolean isAddAdmin = false;

            try {
                LiveHelper.Log.i(TAG, "onReceiveRoomCustomMessage:" + message.type + " " + message.content);
                switch (message.type) {
                    case 1:  // 在线人数
                        OnlineStatusInfo onlineStatusInfo = LiveGsonUtils.sGson.fromJson(message.content, OnlineStatusInfo.class);
                        CommonChatRoomOnlineUserListMsg onlineStatusMessage = new CommonChatRoomOnlineUserListMsg();
                        onlineStatusMessage.onlineCount = onlineStatusInfo.onlineCnt;
                        onlineStatusMessage.playCnt = onlineStatusInfo.playCnt;
                        onlineStatusMessage.hotScore = onlineStatusInfo.hotScore;
                        onReceiveHostOnlineListMessage(onlineStatusMessage);
                        break;
                    case 2: // 直播状态变化
                        RoomStatusInfo roomStatusInfo = LiveGsonUtils.sGson.fromJson(message.content, RoomStatusInfo.class);
                        CommonChatRoomStatusChangeMessage roomStatusChangeMessage = new CommonChatRoomStatusChangeMessage();
                        roomStatusChangeMessage.status = roomStatusInfo.status;
                        roomStatusChangeMessage.reason = roomStatusInfo.reason;
                        onReceiveRoomStatusChangeMessage(roomStatusChangeMessage);
                        break;

                    case 3: // 进场通知
                        EnterRoomInfo enterRoomInfo = LiveGsonUtils.sGson.fromJson(message.content, EnterRoomInfo.class);
                        CommonChatUserJoinMessage userJoinMessage = new CommonChatUserJoinMessage();
                        userJoinMessage.mContent = enterRoomInfo.txt;
                        CommonChatUser user = new CommonChatUser();
                        user.mUid = enterRoomInfo.uid;
                        user.mNickname = enterRoomInfo.nn;
                        userJoinMessage.mUserInfo = user;
                        onReceiveEnterRoomMessage(userJoinMessage);
                        break;

                    case 4: // 房间禁言
                        try {
                            boolean isForbidden = false;
                            JSONObject jsonObject = new JSONObject(message.content);
                            if (jsonObject.has("forbidden")) {
                                isForbidden = jsonObject.optBoolean("forbidden");
                            }
                            String content = isForbidden ? "主播开启了全员禁言" : "主播取消了全员禁言";

                            CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                                    ChatItemViewType.TYPE_COMMON_SYSTEM_HINT,
                                    new CommonSimpleTxtMsg(content)
                            );
                            if (msg != null) onReceiveChatMessage(msg);

                            if (mLiveRecordInfo != null) {
                                mLiveRecordInfo.setRoomForbidden(isForbidden);
                            }

                            if (canUpdateUi()) {
                                ICourseBottombarComponent bottombarComponent = getComponentSafety(COMPONENT_BOTTOM_BAR);
                                if (bottombarComponent != null) {
                                    bottombarComponent.updateInputViewForbid(isForbidden);
                                }
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        break;
                    case 5: // 添加管理员

                        isAddAdmin = true;

                    case 6: // 移除管理员

                        long uid = 0;
                        try {
                            JSONObject jsonObject = new JSONObject(message.content);
                            if (jsonObject.has("touid")) {
                                uid = jsonObject.optLong("touid");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if (mLiveUserInfo != null && mLiveUserInfo.getUid() == uid) {
                            if (isAddAdmin) {
                                mLiveUserInfo.setRoleType(CourseRoleType.ADMIN);
                            } else {
                                mLiveUserInfo.setRoleType(CourseRoleType.AUDIENCE);
                            }

                            if (mPresenter != null) {
                                if (mPresenter.getRoomUserInfo() != null) {
                                    mPresenter.getRoomUserInfo().setRoleType(mLiveUserInfo.getRoleType());
                                }
                            }

                            if (mLiveUserInfo instanceof LiveUserInfo) {
                                // 通知组件更新一下
                                onCurrentLoginUserInfo((LiveUserInfo) mLiveUserInfo);
                            }

                            CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                                    ChatItemViewType.TYPE_COMMON_ADMIN_CHANGE, isAddAdmin
                            );
                            if (msg != null) onReceiveChatMessage(msg);


                            ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
                            if (inputComponent != null) {
                                inputComponent.refreshKeyStatus();
                            }
                            ICourseBottombarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
                            if (component != null) {
                                component.roleTypeChanged(mLiveUserInfo.getRoleType());
                            }
                        }


                        break;

                    case 8: // 分享直播间
                    case 9: // 关注主播
                        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.createCustomMsg(
                                mLiveRecordInfo, message
                        );
                        if (msg != null) onReceiveChatMessage(msg);
                        break;

                    case 10: // 发布话题
                        // 此处不处理，走 super 分发到 onReceivePublishTopicMessage 回调的逻辑
                        break;

                    case 12: {//接收到警告消息

                        if (!canUpdateUi()) {
                            return;
                        }

                        try {

                            CommonChatRoomAnchorVerifyWarningMessage warningMessage = new CommonChatRoomAnchorVerifyWarningMessage();

                            JSONObject jsonObject = new JSONObject(message.content);

                            if (jsonObject.has("title")) {
                                warningMessage.title = jsonObject.optString("title");
                            }
                            if (jsonObject.has("txt")) {
                                warningMessage.txt = jsonObject.optString("txt");
                            }
                            if (jsonObject.has("btnTxt")) {
                                warningMessage.btnTxt = jsonObject.optString("btnTxt");
                            }
                            if (jsonObject.has("type")) {
                                warningMessage.type = jsonObject.optInt("type");
                            }

                            if (mWarningCacheDialog == null) {
                                mWarningCacheDialog = LiveCourseWarningDialog.newInstance(warningMessage);
                            } else {
                                mWarningCacheDialog.setWarningMessage(warningMessage);
                            }

                            mWarningCacheDialog.showNow(getChildFragmentManager(), "LiveVideo_Warning");

                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        break;
                    }

                    default:
                        break;

                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    @Override
    public void onReceivedHotTopicMessage(CommonChatHotTopicMessage message) {
        if (message == null) {
            return;
        }
        super.onReceivedHotTopicMessage(message);
        if (message.getTagId() != null && message.getTagId() > 0 && !TextUtils.isEmpty(message.getTxt())) {
            AnnounceDataCache.update(mHostUid, mRoomId, announceData -> {
                announceData.setContent(message.getTxt());
                announceData.setTopicId(message.getTagId());
                return null;
            });
            CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_TOPIC, message
            );
            if (msg != null) onReceiveChatMessage(msg);
        }
        if (mLiveRecordInfo == null) {
            return;
        }
        mLiveRecordInfo.updateTopicId(UnBoxUtil.unBoxValueSafely(message.getTagId()));
        mLiveRecordInfo.updateTopic(message.getTxt());
        ICourseHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.receiveNewNotice(message.getTxt());
        }
    }

    @Override
    public void onReceivePublishTopicMessage(CustomPublishTopicMessage publishTopicMessage) {
        if (publishTopicMessage == null) {
            return;
        }

        if (mLiveRecordInfo != null) {
            mLiveRecordInfo.updateDescription(publishTopicMessage.txt);
            mLiveRecordInfo.updateTopicId(publishTopicMessage.tagId);
            mLiveRecordInfo.updateTopic(publishTopicMessage.topicName);
        }

        try {
            ICourseHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
            if (headerComponent != null) {
                headerComponent.receiveNewNotice(publishTopicMessage.txt);
            }

            AnnounceDataCache.update(mHostUid, mRoomId, announceData -> {
                announceData.setContent(publishTopicMessage.txt);
                announceData.setTopicId(publishTopicMessage.tagId);
                announceData.setTopicTitle(publishTopicMessage.topicName);
                return null;
            });

            CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_TOPIC, publishTopicMessage
            );
            if (msg != null) onReceiveChatMessage(msg);

            new XMTraceApi.Trace()
                    .setMetaId(16668)
                    .setServiceId("exposure")
                    .put("liveId", mLiveId + "")
                    .put("moduleType", "评论区公告")
                    .put("currPage", "videoLive")
                    .put("currModule", "videoLive")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                    .createTrace();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onReceiveAnchorVerifyWarningMessage(CommonChatRoomAnchorVerifyWarningMessage warningMessage) {
        super.onReceiveAnchorVerifyWarningMessage(warningMessage);

        if (!canUpdateUi()) {
            return;
        }

        if (mWarningCacheDialog == null) {
            mWarningCacheDialog = LiveCourseWarningDialog.newInstance(warningMessage);
        } else {
            mWarningCacheDialog.setWarningMessage(warningMessage);
        }

        mWarningCacheDialog.showNow(getChildFragmentManager(), "LiveVideo_Warning");
    }

    @Override
    public void onReceiveHistoryMessage(List<CommonChatMessage> historyMessages) {
        if (!canUpdateUi()) return;

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null && !historyMessages.isEmpty()) {
            for (CommonChatMessage chatMessage : historyMessages) {
                if (chatMessage != null) {
                    if (chatMessage.mColor == 0) {
                        chatMessage.mColor = ChatListViewConstant.COLOR_NORMAL_CONTENT_KTV;
                    }
                    chatMessage.mUserNickNameColor = ChatListViewConstant.COLOR_NOTICE_VIDEO;

                    if (mHostUid > 0 && chatMessage.getSenderUid() == mHostUid) {
                        chatMessage.mSender.mIsHost = true;
                    }
                }
            }
            if (isFirstQueryHistoryMsg) isFirstQueryHistoryMsg = false;
            comp.onQueryHistoryMessageReceived(historyMessages);
        }
        isQueryHistoryMsg = false;
    }


    @Override
    public void onReceiveGoodsInfoChangedMessage(CommonGoodsInfoChangedMessage goodsInfoChangedMessage) {
        super.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        if (goodsInfoChangedMessage != null && getAbsRoomDetail() instanceof CourseRoomDetail) {
            CourseRoomDetail detail = (CourseRoomDetail) getAbsRoomDetail();
            detail.cartGifUrl = goodsInfoChangedMessage.gifUrl;
        }
    }

    @Override
    public void onSendingMessage(CommonChatMessage chatMessage) {
        super.onSendingMessage(chatMessage);
        if (!canUpdateUi()) {
            return;
        }
        if (chatMessage != null) {
            if (chatMessage.mColor == 0) {
                chatMessage.mColor = ChatListViewConstant.COLOR_NORMAL_CONTENT_KTV;
            }
            chatMessage.mUserNickNameColor = ChatListViewConstant.COLOR_NOTICE_VIDEO;
        }
        ICourseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.onMessageReceived(chatMessage);
        }
    }

    @Override
    public void onSendMessageSuccess(CommonChatMessage chatMessage) {
        super.onSendMessageSuccess(chatMessage);
        ICourseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (canUpdateUi() && comp != null) {
            comp.onHandleMsgSendSucceed(chatMessage);
        }
    }

    @Override
    public void onSendMessageFailed(CommonChatMessage chatMessage) {
        super.onSendMessageFailed(chatMessage);
        ICourseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (canUpdateUi() && comp != null) {
            comp.onHandleMsgSendFailed(chatMessage);
        }
    }

    @Override
    public void onRequestLiveRecordDetailSuccess(ILiveRoomDetail recordInfo) {
        if (recordInfo == null) {
            Logger.i(TAG, "recordInfo = null");
            return;
        }

        LiveRecordInfoManager.getInstance().setLiveRecordInfo(recordInfo, mBusinessId);
        mLiveRecordInfo = recordInfo;
        //预加载礼物列表
        CourseVideoGiftLoader.getInstance(CourseVideoGiftLoader.class).updateGiftList();
        getPresenter().getComponentHost().dispatchHostData(recordInfo);

        if (!mLiveRecordInfo.isFollowed()) {
            // 关注后需要移除
            postOnUiThreadDelayed(mShowFollowDialogRunnable, 30000);
        }
        ICourseBottombarComponent bottomBarComponent = getComponentSafety(COMPONENT_BOTTOM_BAR);
        ICourseHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        ISellGoodsComponent sellGoodsComponent = getComponentSafety(COMPONENT_SELL_GOOD);

        switch (recordInfo.getStatus()) {
            case PersonLiveBase.LIVE_STATUS_END: // 直播结束
                if (!canUpdateUi()) {
                    return;
                }
                if (videoPlayerComponent != null) {
                    videoPlayerComponent.setLiveFinish(true);
                    videoPlayerComponent.hidePlayer();
                }
                removeFollowHostGuide();
                if (headerComponent != null) {
                    headerComponent.updateConnectedStatus(VideoLiveRoomStatusView.LIVE_END);
                }
                if (sellGoodsComponent != null) {
                    sellGoodsComponent.setIsLiving(false);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.setIsLiving(false);
                }
                showFinishDialogForAudience(mPreRoomStatus);
                break;
            case PersonLiveBase.LIVE_STATUS_NOTICE: // 将要直播
                if (headerComponent != null) {
                    headerComponent.updateConnectedStatus(VideoLiveRoomStatusView.LIVE_PRE_ANNOUNCE);
                }
                if (sellGoodsComponent != null) {
                    sellGoodsComponent.setIsLiving(false);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.setIsLiving(false);
                }
                initGiftQueue();
                break;
            case PersonLiveBase.LIVE_STATUS_ING: // 正在直播中

                if (!TextUtils.isEmpty(recordInfo.getPlayUrl())) {
                    onReceivePlayUrl(recordInfo.getPlayUrl());
                } else {
                    boolean playing = false;
                    if (videoPlayerComponent != null) {
                        playing = videoPlayerComponent.isPlaying();
                    }
                    if (!playing && mPresenter != null) {
                        mPresenter.requestPullStreamUrl(mBusinessId, recordInfo.getRoomId(), recordInfo.getLiveId());
                    }
                }
                if (headerComponent != null) {
                    headerComponent.updateConnectedStatus(VideoLiveRoomStatusView.LIVE_PLAYING);
                }
                if (sellGoodsComponent != null) {
                    sellGoodsComponent.setIsLiving(true);
                }
                if (bottomBarComponent != null) {
                    bottomBarComponent.setIsLiving(true);
                }
                initGiftQueue();
                break;
        }

        if (bottomBarComponent != null && recordInfo instanceof CourseRoomDetail) {
            boolean isOpenGift = ((CourseRoomDetail) recordInfo).isOpenGift();
            bottomBarComponent.setIsOpenGift(isOpenGift);
        }
        if (isTrackRoomWhenFirstEnterThisRoom) {
            trackRoom();
            isTrackRoomWhenFirstEnterThisRoom = false;
        }
        mPreRoomStatus = recordInfo.getStatus();
    }

    protected void onReceivePlayUrl(String playUrl) {

    }

    protected void removeFollowHostGuide() {
        removeCallbacks(mShowFollowDialogRunnable);

        ICourseChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.removeFollowHostGuide();
    }

    private final Runnable mShowFollowDialogRunnable = () -> {
        //不可见环境下也不显示
        if (!isVisible()) {
            return;
        }

        ICourseChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showFollowHostGuide();
    };


    @Override
    public void onRequestLiveRecordDetailFail(int errCode, String errMsg) {

    }

    @Override
    public void onRequestRoomUserInfoSuccess(ILiveUserInfo roomUserInfo) {
        mLiveUserInfo = roomUserInfo;
        if (mLiveUserInfo instanceof LiveUserInfo) {
            onCurrentLoginUserInfo((LiveUserInfo) mLiveUserInfo);
        }

        if (roomUserInfo instanceof LiveUserInfo) {
            try {
                CourseGiftPanelComponent giftPanelComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_GIFT_PANEL);
                if (giftPanelComponent != null) {
                    giftPanelComponent.updateCurrentUserInfo((LiveUserInfo) roomUserInfo);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    public void onRequestRoomUserInfoFail(int errCode, String errMsg) {

    }

    @Override
    public void onRequestPullStreamUrlSuccess(LivePullUrls pullStreamInfo) {

    }

    @Override
    public void onRequestPullStreamUrlFail(int errCode, String errMsg) {
        CustomToast.showToast("获取拉流地址失败！");

    }

    public void requestPlayMode(int playMode) {

    }

    public void onClickAt(long uid, String username) {

        if (mLiveRecordInfo.isRoomForbidden()) {
            return;
        }

        // @用户
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null) {
            inputComponent.sendATMessage(uid, username);
        }
    }

    //课程直播没有飘屏 baseRoomfragment总统一实现了 所以空实现不走base分发逻辑
    @Override
    public void onReceiveFloatScreenMessage(CommonFloatScreenMessage msg) {
    }

    public void tryShowOpenNotificationDialog(long delay) {
        ICourseOpenNotifiComponent openNotifiComponent = getComponentSafety(COMPONENT_COURSE_OPEN_NOTIFI);
        if (openNotifiComponent != null) {
            String anchorAvatar = mLiveRecordInfo != null ? mLiveRecordInfo.getAnchorAvatar() : "";
            openNotifiComponent.tryShowWithoutPermission(
                    getHostUid(), anchorAvatar, delay
            );
        }
    }

    public void sendMsg(String data) {
        if (mPresenter != null) {
            mPresenter.sendMessage(data);
        }
    }

    public void queryHistoryMessage(long startTime, long endTime, long msgId, int groupType, int pageCount, boolean isAll) {
        if (mPresenter != null && !isQueryHistoryMsg && mBusinessId == IBusinessIdConstants.BIZ_ID_VIDEO_COURSE) {
            isQueryHistoryMsg = true;
            mPresenter.queryHistoryMsg(getRoomId(), startTime, endTime, msgId, groupType, pageCount, isAll);
        }
    }

    public void showAnnounce() {
        ICourseHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.showAnnounce();
        }
    }

    public void clickAtFunc(String username, long uid) {
        if (mLiveRecordInfo.isRoomForbidden()) {
            return;
        }

        if (uid > 0 && !TextUtils.isEmpty(username)) {
            // @用户
            ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
            if (inputComponent != null) {
                inputComponent.sendATMessage(uid, username);
            }
        }
    }

    @Override
    public void sendImgMsg(String uri) {
        if (getConnectionManager() != null && !getConnectionManager().isConnected()) {
            CustomToast.showSuccessToast("正在连接聊天室");
            return;
        }

        if (mPresenter != null) {
            mPresenter.sendImgMessage(uri);
        }
    }

    @Override
    protected String getPageLogicName() {
        return null;
    }

    @Override
    public int getContainerLayoutId() {
        return 0;
    }

    public void sendMessage(String content, boolean isHotWord) {
        mPresenter.sendTextMsgInVideo(content);
    }

    @Override
    public void sendEmojiMsg(IEmojiItem emojiItem) {
        if (getConnectionManager() != null && !getConnectionManager().isConnected()) {
            CustomToast.showSuccessToast("正在连接聊天室");
            return;
        }

        if (mPresenter != null) {
            mPresenter.sendEmojiMessage(emojiItem);
        }
    }

    public IKeyboardHostFragment getInputHostFragment() {
        return null;
    }

    @Override
    public void keyboardShowStateChange(boolean show) {
        getComponentHost().dispatchInputKeyboardStatusChange(show);
    }

    final class LiveVideoRoomLocalBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || !canUpdateUi() || !isResumed()) {
                return;
            }

            if (ChatListViewConstant.ACTION_CHAT_LIST_CLICK_NICKNAME.equals(intent.getAction())) {
                long uid = intent.getLongExtra(ChatListViewConstant.BUNDLE_KEY_USER_ID, -1);
                if (uid > 0 && isHavePlayAuth()) {
                    BaseCourseRoomFragment.this.showUserInfoCard(uid);
                }
            } else if (ChatListViewConstant.ACTION_CHAT_LIST_LONG_CLICK_NICKNAME.equals(intent.getAction())) {
                String nickName = intent.getStringExtra(ChatListViewConstant.BUNDLE_KEY_USER_NAME);
                long uid = intent.getLongExtra(ChatListViewConstant.BUNDLE_KEY_USER_ID, -1);
                if (!TextUtils.isEmpty(nickName) && uid > 0) {
                    onClickAt(uid, nickName);
                }
            } else if (ChatListViewConstant.ACTION_CHAT_LIST_LONG_CLICK_CONTENT.equals(intent.getAction())) {
                Serializable message = intent.getSerializableExtra(ChatListViewConstant.BUNDLE_KEY_CONTENT);
                if (message instanceof CommonChatMessage) {
                    LiveCommonDialogManager.showMsgOperateDialog(getActivity(), (CommonChatMessage) message);
                }
            } else if (ChatListViewConstant.ACTION_CHAT_LIST_REFRESH_MEDAL.equals(intent.getAction())) {
                LiveIconsManager.getInstance().refreshMedalInfo(mRoomId);
            } else if (CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_CLOSE_CURRENT_PAGE.equals(intent.getAction())) {
                IExitRoomComponent component = getComponentSafety(CourseCompConfig.COMPONENT_EXIT_ROOM);
                if (component != null) {
                    component.requestExitRoom(false);
                }
            } else if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_GIFT_DIALOG.equals(intent.getAction())) {
                if (isRealVisable()) {
                    showGiftPanel();
                }
            } else if (CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE.equals(intent.getAction())) {
                //h5调原生发言
                String content = intent.getStringExtra(CommonXmlObjcJsCall.KEY_SEND_MESSAGE);
                if (!TextUtils.isEmpty(content)) {
                    sendMsg(content);
                }
            } else if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_SHARE_ROOM.equals(intent.getAction())) {
                //课程直播分享
                shareLiveRoom();
            }
        }
    }

    public boolean isHavePlayAuth() {
        return false;
    }

    public void shareLiveRoom() {
        if (mLiveRecordInfo != null) {
            LiveCourseShareUtils.registerShareResultAndUpload(
                    getContext(),
                    getRoomId(),
                    getChatId(),
                    getLiveId(),
                    UserInfoMannage.getUid(),
                    getHostUid()
            );
            if (getActivity() != null) {
                LiveCourseShareUtils.shareLive(
                        getActivity(),
                        getLiveId(),
                        getRoomId(),
                        LiveCourseShareUtils.getLiveShareData(mLiveRecordInfo),
                        ICustomShareContentType.SHARE_TYPE_LIVE_VIDEO,
                        getHostUid()
                );
            }
        }
    }

    public void updateSystemUiVisibility() {
        // 全屏显示，隐藏状态栏和导航栏，拉出状态栏和导航栏显示一会儿后消失。
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
    }

    /**
     * 点击键盘中选择图片的按钮
     */
    public void clickChoosePics() {
        if (!canUpdateUi()) {
            return;
        }
        //进入相册选择页面
        ImageMultiPickFragment fragment = ImageMultiPickFragment.newInstance(1, 1, "发送");
        fragment.setCallbackFinish(this);
        startFragment(fragment);

    }

    //拍照保存的路径
    private String mPhotoCameraSavePath;

    /**
     * 点击键盘中拍摄的按钮
     */
    public void clickCamera() {

        if (!canUpdateUi()) {
            return;
        }

        checkPermission(new HashMap<String, Integer>() {{
            put(Manifest.permission.READ_EXTERNAL_STORAGE, com.ximalaya.ting.android.host.R.string.host_deny_perm_read_sdcard);
        }}, new IMainFunctionAction.IPermissionListener() {
            @Override
            public void havedPermissionOrUseAgree() {

                mPhotoCameraSavePath = takeCameraPhotoSavePath();
                Logger.d("checkPermission", "有权限READ_EXTERNAL_STORAGE");
            }

            @Override
            public void userReject(Map<String, Integer> noRejectPermiss) {
                CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_deny_perm_read_sdcard);
            }
        });

    }


    @Override
    public void onFinishCallback(Class<?> cls, int fid, Object... params) {

        //选择图片对话框的返回
        if (ImageMultiPickFragment.class == cls) {
            if (params != null && params.length > 0) {
                List<ImgItem> items = ((List<ImgItem>) params[0]);
                //进行图片文件消息的发送
                for (ImgItem item : items) {
                    if (item != null && !TextUtils.isEmpty(item.getPath())) {
                        sendImgMsg(item.getPath());
                    }
                }
            }
        }
    }


    /**
     * 进入相机拍摄
     *
     * @return 图片存储路径
     */
    public String takeCameraPhotoSavePath() {
        String fileName = System.currentTimeMillis() + ".jpg";
        File vFile = ToolUtil.getTempImageFile(fileName);
        DeviceUtil.checkCameraPermissonAndGoCamera(mActivity, FileProviderUtil.fromFile(vFile), IMAGE_FROM_CAMERA);
        return vFile != null ? vFile.getPath() : "";
    }


    @Override
    public void catchPhoto(int requestCode, Intent data) {
        if (requestCode == IPhotoAction.IMAGE_FROM_CAMERA) {

            try {

                //保存到系统相册
                LiveCourseUtils.insertImgToMediaStore(this, mContext, mPhotoCameraSavePath, "", "");


            } catch (Exception e) {
                e.printStackTrace();
            }


            //进行图片发送
            sendImgMsg(mPhotoCameraSavePath);

        }
    }

    @Override
    public void cropPhoto() {

    }

    @Override
    public void canceled() {

    }


    @Override
    public boolean isAnchorVisitor() {
        return UserInfoMannage.getUid() > 0 && getHostUid() == UserInfoMannage.getUid();
    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        super.onLogin(model);
        //相同用户不进行重新加载数据
        if (mLiveUserInfo != null && model != null && mLiveUserInfo.getUid() == model.getUid()) {
            return;
        }
        loadMyData();
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
        super.onUserChange(oldModel, newModel);
        loadMyData();
    }


    @Override
    public void onFollow(long uid, boolean follow) {
        if (mLiveRecordInfo != null && mLiveRecordInfo.getHostUid() == uid) {
            mLiveRecordInfo.setFollowed(follow);

            // 如果是关注则取消弹框提示
            if (mLiveRecordInfo.isFollowed()) {
                removeFollowHostGuide();
                // 每回调一次 onFollow 通知关注，该上报接口会触发关注消息下发，多次回调会导致多条消息推送
                CommonRequestForLiveCourse.followRoomOwner(mLiveId, null);
            }

            ICourseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
            if (comp != null) comp.updateFollowHostStatus(true);
        }
    }

    public boolean isScreenFull() {
        return false;
    }

    public void onGetClickEvent() {
    }

    public void hideChat(boolean isHideChat) {

    }

    public void onTouchEmptyPlace() {
        ICourseInputPanelComponent inputComponent = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (inputComponent != null && inputComponent.isKeyboardPanelShowed()) {
            inputComponent.hide();
        }
    }


    public void isGiftPanelShowing(boolean isShow) {
        try {
            LiveCourseEnterAndPopComponent component = getPresenter().getComponentManager().getComponent(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            component.onGiftDialogShowStateChange(isShow);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void handleHalfScreenUrl(String url) {
        ICourseHalfScreenHybridComponent hybridComponent = getComponentSafety(COMPONENT_COURSE_HALF_SCREEN_HYBRID);
        if (hybridComponent != null) {
            hybridComponent.showHybridPage(url);
        }
    }

    public void onBottomOperationViewShow(boolean show) {
        isNormalOperationViewShow = show;
    }

    public void onHostOpenMic(boolean isQueryMicStatus) {
        if (!isQueryMicStatus) {
            CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_SYSTEM_HINT,
                    new CommonSimpleTxtMsg("主播开启观众连线啦，快去和主播连线互动吧~")
            );
            if (msg != null) onReceiveChatMessage(msg);
        }
    }

    public void onHostCloseMic(boolean isQueryMicStatus) {
        if (!isQueryMicStatus) {
            CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_SYSTEM_HINT,
                    new CommonSimpleTxtMsg("主播关闭了观众连线")
            );
            if (msg != null) onReceiveChatMessage(msg);
        }
    }

    @Override
    public BaseGiftLoader createGiftLoader() {
        return CourseVideoGiftLoader.getInstance(CourseVideoGiftLoader.class);
    }

    @Override
    protected void setGiftLoaderParams(BaseGiftLoader giftLoader, long giftId, String animationPath) {
        if (getAbsRoomDetail() != null) {
            giftLoader.setBizType(getRoomBizType())
                    .setLiveId(getLiveId())
                    .setDynamicType(giftId, animationPath);
        }
    }

    @Override
    public void addRedPacketNoticeMessage(CommonChatRedPacketMessage redPacketMessage) {
        CommonLiveLogger.i(TAG, "addRedPacketNoticeMessage " + redPacketMessage);
        // 发红包
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SEND_RED_PACKET, redPacketMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addTimeRedPacketNoticeMessage(CommonChatTimedRedPacketMessage redPacketMessage) {
        CommonLiveLogger.i(TAG, "addTimeRedPacketNoticeMessage " + redPacketMessage);
        // 收到定时红包 发定时红包
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SEND_RED_PACKET, redPacketMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addGetRedPacketNoticeMessage(CommonChatGetRedPacketMessage redPacketMessage) {
        CommonLiveLogger.i(TAG, "addGetRedPacketNoticeMessage " + redPacketMessage);

        CommonChatMessage msg;
        if (redPacketMessage.mRedPacketType == IRedPacketMessage.RED_PACKET_BOX_OR_NEW_YEAR) {
            // 抢到传送门宝箱、新年红包
            msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_CLAIM_BOX_OR_NEW_YEAR_RED_PACKET, redPacketMessage
            );
        } else {
            // 抢到普通红包
            msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_CLAIM_RED_PACKET, redPacketMessage
            );
        }
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addAudienceMessage(CommonChatAudienceMessage audienceMessage) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.createAudienceMsg(
                mLiveRecordInfo, audienceMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addRoomNoticeMessage(CommonChatRoomNoticeMessage chatRoomNoticeMessage) {
        ChatItemViewType itemViewType = ChatItemViewType.TYPE_COMMON_SYSTEM_NOTICE;
        if (!TextUtils.isEmpty(chatRoomNoticeMessage.url) &&
                !TextUtils.isEmpty(chatRoomNoticeMessage.urlText)
        ) {
            itemViewType = ChatItemViewType.TYPE_COMMON_SYSTEM_CALL_TO_ACTION;
        }
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                itemViewType, chatRoomNoticeMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addWarningMessage(CommonChatRoomWarningMessage warningMessage) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SYSTEM_WARNING, warningMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addRoomGameRulesUpdateMessage(CommonChatRoomRuleInfoUpdateMessage gameRuleUpdateMessage) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SYSTEM_CALL_TO_ACTION, gameRuleUpdateMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    protected void insertGiftMsg(CommonChatGiftMessage giftMessage) {
        CommonChatMessage msg = CourseChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_GIFT, giftMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    public void checkPermission(Map<String, Integer> permissions, IMainFunctionAction.IPermissionListener listener) {
        if (null == listener || getActivity() == null) {
            return;
        }

        if (getActivity() instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(getActivity(), (IMainFunctionAction.ISetRequestPermissionCallBack) getActivity(), permissions, listener);
            } catch (Exception e) {
                e.printStackTrace();
                listener.userReject(permissions);
            }
        } else {
            Logger.e("BaseFragment2", "activity没有实现ISetRequestPermissionCallBack 不能检查权限");
        }
    }


    @Override
    public boolean isFollowHost() {
        return getAbsRoomDetail() instanceof CourseRoomDetail && getAbsRoomDetail().isFollowed();
    }

    @Override
    public void doFollowHost(int sceneType) {
        long hostUid = getHostUid();
        if (hostUid <= 0) return;

        // 这里不要注册监听回调，因为该 Fragment 已通过 AnchorFollowManage 注册监听，否则会回调两次 onFollow
        followUser(hostUid, sceneType, null);
    }

    public void onReceivePushJsDataMsg(CommonPushJsData pushJsData) {
        super.onReceivePushJsDataMsg(pushJsData);

        ICourseRightAreaComponent rightAreaComponent = getComponentSafety(COMPONENT_RIGHT_AD);
        if (pushJsData == null || !canUpdateUi() || rightAreaComponent == null) {
            return;
        }
        rightAreaComponent.onReceivePushJsDataMsg(pushJsData);
    }

    @Override
    public void onReceiveWithdrawChatMsg(CommonWithdrawChatMsg withdrawChatMsg) {
        super.onReceiveWithdrawChatMsg(withdrawChatMsg);

        Logger.i(TAG, "onReceiveWithdrawChatMsg, CommonWithdrawChatMsg = " + withdrawChatMsg);

        ICourseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (canUpdateUi() && withdrawChatMsg != null && comp != null) {
            comp.recallMessage(withdrawChatMsg.msgId);
        }
    }

    @Override
    public void onReceiveCdnStatusMessage(CommonCdnStatusMessage message) {
        super.onReceiveCdnStatusMessage(message);
        if (!canUpdateUi()) {
            return;
        }
        ICourseVideoPlayerComponent videoPlayerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (videoPlayerComponent == null) {
            return;
        }
        videoPlayerComponent.updateCdnStatus(message.getStatusType());
    }

    @Override
    public boolean isInterceptMoreLiveGesture() {
        return LiveVideoPlayerManager.getInstance().getPlayerViewScaleValue() > 1;
    }

    @Override
    public void onRequestRoomDetailSuccess(ILiveRoomDetail roomDetail) {
        super.onRequestRoomDetailSuccess(roomDetail);
        onRequestLiveRecordDetailSuccess(roomDetail);
    }

    @Override
    public void onRequestRoomDetailError(long roomId, int code, String message) {
        super.onRequestRoomDetailError(roomId, code, message);
        onRequestLiveRecordDetailFail(code, message);
    }

    @Override
    public int getHeadHeight() {
        LiveViewPositionInfo headInfo = LiveViewPositionManager.getInstance()
                .getInfoForType(LiveViewPositionType.TYPE_FULL_HEADER);
        return headInfo != null ? headInfo.getHeight() : 0;
    }

    protected void trackRoom() {
        // 课程直播  页面展示
        new XMTraceApi.Trace().pageView(16150, "liveRoom").put(LiveRecordInfoManager.getInstance().getBaseProps()).put("currPage", "videoLive").put("videoLiveType", "1").createTrace();
    }

    protected void trackLeaveRoom() {
        // 课程直播  页面离开
        new XMTraceApi.Trace().pageExit2(16151).put(LiveRecordInfoManager.getInstance().getBaseProps()).put("videoLiveType", "1").put("currPage", "videoLive").createTrace();
    }

    @Nullable
    @Override
    public CourseRoomCore getRoomCore() {
        return (CourseRoomCore) super.getRoomCore();
    }


    public CourseComponentHost getCourseComponentHost() {
        if (getComponentHost() instanceof CourseComponentHost) {
            return (CourseComponentHost) getComponentHost();
        }
        return null;
    }
}
