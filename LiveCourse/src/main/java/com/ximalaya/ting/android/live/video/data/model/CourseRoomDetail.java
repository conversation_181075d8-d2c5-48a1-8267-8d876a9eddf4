package com.ximalaya.ting.android.live.video.data.model;


import static com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo.FansGroupStatusCode.TYPE_JOINED;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LivePlayUrlUtil;
import com.ximalaya.ting.android.live.lib.stream.live.ILivePlaySourceInfo;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

/**
 * 课程视频直播场次的详情信息
 *
 * <AUTHOR>
 */
@Keep
public class CourseRoomDetail extends BaseRoomDetail {

    public long id;//场次id
    public String name;//名称
    public String coverPath;//直播封面(原)
    public String coverSmall;//直播封面(小)
    public String coverMiddle;//直播封面(中)
    public String coverLarge;//直播封面(大)
    public long uid;//主播uid
    public String nickname;//主播昵称
    public String avatar;//主播头像
    public long actualStartAt;//实际开始时间(毫秒)
    public long actualStopAt;//实际结束时间(毫秒)
    public long startAt;//预计开始时间(毫秒)
    public long endAt;//预计结束时间(毫秒)
    public String description;//简介描述
    @PersonLiveBase.LiveStatus
    public int status;//直播状态，1:直播结束 5: 直播预告 9: 正在直播中
    public boolean canBooking;//是否可预约，true：可预约
    public int onlineCount;//在线人数
    public int playCount;//播放次数
    public long currentTs;//当前系统时间戳(毫秒)
    public boolean isBooking;//用户是否预约, true: 已预约
    public boolean isFollowed;//用户是否关注，true: 已关注
    public boolean roomForbidden;//房间是否禁言，true:已禁言
    public int categoryId;//直播分类id(第三级id)
    public boolean showPlayback; //是否显示回听，true：显示回看
    public String playbackPath; //回看地址
    public int playbackStatus; // 回看状态，0:无 1:生成中 2:已完成
    public String bgImage; //背景图
    public CategoryInfo categoryInfo; //分类链路信息
    public boolean screenDirection; //横竖屏
    public int pushStreamType; //推流方式 1：电脑直播 2：拉流直播 3：第三方推流直播
    public boolean openGoods; //是否开启带货
    public boolean openGift;  //是否开启送礼
    public boolean forbidSpeak; //直播间禁用评论功能
    private boolean hasFansClub; //是否开通粉丝团
    public String fansClubHtmlUrl; //粉丝团地址
    private LiveUserInfo.FansClubVoBean roomFansClubVo;//当前直播间的粉丝团信息
    public String cartGifUrl; //购物车图标动图url地址
    public boolean hasGoods; //直播间是否有商品上架
    public long hotScore;//热度
    private String topicTagTitle;//话题标题
    private long topicTagId;//话题id
    public String hotScoreIconPath;//热度图标
    public String chatFrameTip;//输入框默认文案

    private long progress;//回放播放进度

    private String playUrl; //播放地址

    private AuthResult authResult; //鉴权结果

    /**
     * 共创主播信息
     */
    private List<AnchorTogetherForms> anchorTogetherForms;

    //剩余可以发起试看的门槛时间
    public static final int THRESHOLD_LEFT_VALUE_TIME_MS = 10 * 1000;

    /**
     * 共创主播
     */
    @Keep
    public static class AnchorTogetherForms {

        /**
         * anchorUid : 1244267
         * startTime : 1732003454000
         * endTime : 1732003454000
         */

        @SerializedName("anchorUid")
        private long anchorUid;
        @SerializedName("startTime")
        private long startTime;
        @SerializedName("endTime")
        private long endTime;
        @SerializedName("anchorAvatar")
        private String anchorAvatar;
        @SerializedName("anchorName")
        private String anchorName;

        public String getAnchorAvatar() {
            return anchorAvatar;
        }

        public void setAnchorAvatar(String anchorAvatar) {
            this.anchorAvatar = anchorAvatar;
        }

        public String getAnchorName() {
            return anchorName;
        }

        public void setAnchorName(String anchorName) {
            this.anchorName = anchorName;
        }

        public long getAnchorUid() {
            return anchorUid;
        }

        public void setAnchorUid(long anchorUid) {
            this.anchorUid = anchorUid;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }
    }

    public static CourseRoomDetail convertJsonToModel(String jsonStr) {

        try {

            CourseRoomDetail res = new CourseRoomDetail();

            JSONObject jsonObject = new JSONObject(jsonStr);

            if (jsonObject.has("id")) {
                res.id = jsonObject.optLong("id");
            }

            if (jsonObject.has("roomId")) {
                res.roomId = jsonObject.optLong("roomId");
            }

            if (jsonObject.has("name")) {
                res.name = jsonObject.optString("name");
            }

            if (jsonObject.has("coverPath")) {
                res.coverPath = jsonObject.optString("coverPath");
            }

            if (jsonObject.has("coverSmall")) {
                res.coverSmall = jsonObject.optString("coverSmall");
            }

            if (jsonObject.has("coverMiddle")) {
                res.coverMiddle = jsonObject.optString("coverMiddle");
            }

            if (jsonObject.has("coverLarge")) {
                res.coverLarge = jsonObject.optString("coverLarge");
            }
            if (jsonObject.has("uid")) {
                res.uid = jsonObject.optLong("uid");
            }

            if (jsonObject.has("nickname")) {
                res.nickname = jsonObject.optString("nickname");
            }

            if (jsonObject.has("avatar")) {
                res.avatar = jsonObject.optString("avatar");
            }

            if (jsonObject.has("actualStartAt")) {
                res.actualStartAt = jsonObject.optLong("actualStartAt");
            }

            if (jsonObject.has("actualStopAt")) {
                res.actualStopAt = jsonObject.optLong("actualStopAt");
            }

            if (jsonObject.has("startAt")) {
                res.startAt = jsonObject.optLong("startAt");
            }

            if (jsonObject.has("endAt")) {
                res.endAt = jsonObject.optLong("endAt");
            }

            if (jsonObject.has("description")) {
                res.description = jsonObject.optString("description");
            }

            if (jsonObject.has("status")) {
                res.status = jsonObject.optInt("status");
            }

            if (jsonObject.has("canBooking")) {
                res.canBooking = jsonObject.optBoolean("canBooking");
            }

            if (jsonObject.has("onlineCount")) {
                res.onlineCount = jsonObject.optInt("onlineCount");
            }

            if (jsonObject.has("playCount")) {
                res.playCount = jsonObject.optInt("playCount");
            }

            if (jsonObject.has("currentTs")) {
                res.currentTs = jsonObject.optLong("currentTs");
            }

            if (jsonObject.has("isBooking")) {
                res.isBooking = jsonObject.optBoolean("isBooking");
            }

            if (jsonObject.has("isFollowed")) {
                res.isFollowed = jsonObject.optBoolean("isFollowed");
            }

            if (jsonObject.has("roomForbidden")) {
                res.roomForbidden = jsonObject.optBoolean("roomForbidden");
            }

            if (jsonObject.has("categoryId")) {
                res.categoryId = jsonObject.optInt("categoryId");
            }

            if (jsonObject.has("showPlayback")) {
                res.showPlayback = jsonObject.optBoolean("showPlayback");
            }

            if (jsonObject.has("playbackPath")) {
                res.playbackPath = jsonObject.optString("playbackPath");
            }

            if (jsonObject.has("playbackStatus")) {
                res.playbackStatus = jsonObject.optInt("playbackStatus");
            }
            if (jsonObject.has("bgImage")) {
                res.bgImage = jsonObject.optString("bgImage");
            }
            if (jsonObject.has("categoryInfo")) {
                String category = jsonObject.getString("categoryInfo");
                res.categoryInfo = LiveGsonUtils.sGson.fromJson(category, CategoryInfo.class);
            }
            if (jsonObject.has("screenDirection")) {
                res.screenDirection = jsonObject.getBoolean("screenDirection");
            }
            if (jsonObject.has("pushStreamType")) {
                res.pushStreamType = jsonObject.getInt("pushStreamType");
            }
            if (jsonObject.has("openGoods")) {
                res.openGoods = jsonObject.getBoolean("openGoods");
            }
            if (jsonObject.has("openGift")) {
                res.openGift = jsonObject.getBoolean("openGift");
            }
            if (jsonObject.has("forbidSpeak")) {
                res.forbidSpeak = jsonObject.getBoolean("forbidSpeak");
            }
            if (jsonObject.has("hasFansClub")) {
                res.hasFansClub = jsonObject.getBoolean("hasFansClub");
            }
            if (jsonObject.has("fansClubHtmlUrl")) {
                res.fansClubHtmlUrl = jsonObject.optString("fansClubHtmlUrl");
            }

            if (jsonObject.has("fansClubVo")) {
                res.roomFansClubVo = new LiveUserInfo.FansClubVoBean(jsonObject.optString("fansClubVo"));
            }

            if (jsonObject.has("cartGifUrl")) {
                res.cartGifUrl = jsonObject.optString("cartGifUrl");
            }

            if (jsonObject.has("hasGoods")) {
                res.hasGoods = jsonObject.optBoolean("hasGoods");
            }
            if (jsonObject.has("hotScore")) {
                res.hotScore = jsonObject.optLong("hotScore");
            }
            if (jsonObject.has("topicTagId")) {
                res.topicTagId = jsonObject.optLong("topicTagId");
            }
            if (jsonObject.has("topicTagTitle")) {
                res.topicTagTitle = jsonObject.optString("topicTagTitle");
            }
            if (jsonObject.has("progress")) {
                res.progress = jsonObject.optLong("progress");
            }

            if (jsonObject.has("hotScoreIconPath")) {
                res.hotScoreIconPath = jsonObject.optString("hotScoreIconPath");
            }

            if (jsonObject.has("chatFrameTip")) {
                res.chatFrameTip = jsonObject.optString("chatFrameTip");
            }

            if (jsonObject.has("livePlayResp")) {
                JSONObject urlJson = jsonObject.optJSONObject("livePlayResp");
                if (urlJson.has("flvUrlsEncrypt")) {
                    JSONArray playUrls = urlJson.optJSONArray("flvUrlsEncrypt");
                    if (playUrls != null && playUrls.length() > 0) {
                        res.playUrl = LivePlayUrlUtil.decrypt(playUrls.optString(0));
                        LiveHelper.Log.i("duruochen--preload", "解密后的url:" + res.playUrl);
                    }
                }
                if ((res.playUrl == null || !res.playUrl.contains(".flv")) && urlJson.has("flvUrls")) {
                    JSONArray playUrls = urlJson.optJSONArray("flvUrls");
                    if (playUrls != null && playUrls.length() > 0) {
                        res.playUrl = playUrls.optString(0);
                    }
                }
            }

            if (jsonObject.has("authResultVo")) {
                res.authResult = GsonUtils.parseJson(jsonObject.getString("authResultVo"), AuthResult.class);
            }
            if (jsonObject.has("anchorTogetherForms")) {
                res.anchorTogetherForms = GsonUtils.parseList(jsonObject.getString("anchorTogetherForms"),
                        AnchorTogetherForms.class);
            }

            return res;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    @Override
    public long getRoomId() {
        return roomId;
    }

    @Override
    public long getChatId() {
        return 0;
    }

    @Override
    public long getHostUid() {
        return uid;
    }

    @Override
    @PersonLiveBase.LiveStatus
    public int getStatus() {
        return status;
    }

    @Override
    public void setRoomForbidden(boolean roomForbidden) {
        this.roomForbidden = roomForbidden;
    }

    @Override
    public boolean isRoomForbidden() {
        return roomForbidden;
    }

    @Override
    public void setLiveStatus(@PersonLiveBase.LiveStatus int status) {
        this.status = status;
    }

    @Override
    public long getLiveStartAt() {
        return actualStartAt;
    }

    @Override
    public long getLivePlanToStartAt() {
        return startAt;
    }

    @Override
    public boolean isCanBooking() {
        return canBooking;
    }

    @Override
    public long getLiveStopAt() {
        return actualStopAt;
    }

    @Override
    public long getLiveId() {
        return id;
    }

    @Override
    public boolean isBooking() {
        return isBooking;
    }

    @Override
    public boolean isBookingThisRecord() {
        return isBooking();
    }

    @Override
    public void updateDescription(String desc) {
        description = desc;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getAnchorAvatar() {
        return avatar;
    }

    @Override
    public String getLargeAvatar() {
        return coverLarge;
    }

    @Override
    public String getAnchorName() {
        return nickname;
    }

    @Override
    public String getRoomTitle() {
        return name;
    }

    @Override
    public int getOnlineCount() {
        return onlineCount;
    }

    @Override
    public int getParticipateCount() {
        return playCount;
    }

    @Override
    public void setFollowed(boolean followed) {
        isFollowed = followed;
    }

    @Override
    public boolean isFollowed() {
        return isFollowed;
    }

    @Override
    public boolean isFollowAnchor() {
        return isFollowed;
    }

    @Override
    public String getRoomName() {
        return getRoomTitle();
    }

    @Override
    public String getSmallCoverPath() {
        return coverSmall;
    }

    @Override
    public String getLargeCoverPath() {
        return coverLarge;
    }

    @Override
    public boolean hasPlayBack() {
        return showPlayback;
    }

    @Override
    public String getPlayBackPath() {
        return playbackPath;
    }

    @Override
    public String getBgImage() {
        return bgImage;
    }

    @Override
    public boolean isOpenGoods() {
        return openGoods;
    }

    public boolean isOpenGift() {
        return openGift;
    }

    @Override
    public long getFMId() {
        return 0;
    }

    public List<AnchorTogetherForms> getAnchorTogetherForms() {
        return anchorTogetherForms;
    }

    @Override
    @LiveMediaType
    public int getMediaType() {
        return LiveMediaType.TYPE_VIDEO;
    }

    public LiveUserInfo.FansClubVoBean getRoomFansClubVo() {
        return roomFansClubVo;
    }

    @Override
    public void updateFansClubCount(int count) {
        if (roomFansClubVo != null) {
            roomFansClubVo.setCount(count);
        }
    }

    @Override
    public void updateFansClubGrade(int grade) {
        if (roomFansClubVo != null) {
            roomFansClubVo.setFansGrade(grade);
        }
    }

    @Override
    public void updateFansClubJoinStatus(boolean isJoined) {
        if (roomFansClubVo != null) {
            roomFansClubVo.setCode(TYPE_JOINED);
        }
    }

    @Override
    public boolean isHasGoods() {
        return hasGoods;
    }

    @Override
    public String getCarGifUrl() {
        return cartGifUrl;
    }

    @Override
    public String getTopic() {
        return topicTagTitle;
    }

    @Override
    public long getTopicId() {
        return topicTagId;
    }

    @Override
    public void updateTopicId(long topicId) {
        topicTagId = topicId;
    }

    @Override
    public void updateTopic(String topic) {
        topicTagTitle = topic;
    }

    public long getHotScore() {
        return hotScore;
    }

    public long getProgress() {
        return progress * 1000;
    }

    private ILivePlaySourceInfo mLivePlaySourceInfo;

    public ILivePlaySourceInfo getLivePlaySourceInfo() {
        if (mLivePlaySourceInfo == null) {
            mLivePlaySourceInfo = new LiveStreamInfo(this);
        }
        return mLivePlaySourceInfo;
    }

    private static class LiveStreamInfo implements ILivePlaySourceInfo {
        private final CourseRoomDetail mDetail;

        private LiveStreamInfo(@NonNull CourseRoomDetail detail) {
            mDetail = detail;
        }

        @Override
        public long getLiveId() {
            return mDetail.getLiveId();
        }

        @Override
        @PersonLiveBase.LiveStatus
        public int getStatus() {
            return mDetail.getStatus();
        }

        @Override
        public long getStreamUid() {
            return mDetail.getHostUid();
        }

        @Override
        public String getHostNickname() {
            return mDetail.getAnchorName();
        }

        @Override
        public String getHostAvatar() {
            return mDetail.getAnchorAvatar();
        }

        @Override
        public String largeCoverUrl() {
            return mDetail.getLargeCoverPath();
        }

        @Override
        public String middleCoverUrl() {
            return mDetail.coverMiddle;
        }

        @Override
        public String smallCoverUrl() {
            return mDetail.getSmallCoverPath();
        }

        @Override
        public String title() {
            return mDetail.getRoomTitle();
        }

        @Override
        public String trackInfo() {
            return "";
        }

        @Override
        public long getRoomId() {
            return mDetail.getRoomId();
        }

        @Override
        @BaseScrollConstant.LiveRoomBizType
        public int getLiveType() {
            return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE;
        }
    }


    public String getHotScoreIconPath() {
        return hotScoreIconPath;
    }

    public String getChatFrameTip() {
        return chatFrameTip;
    }

    @Override
    @BaseScrollConstant.LiveRoomBizType
    public int getRoomBizType() {
        return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE;
    }

    @Override
    public int getRoomSubBizType() {
        return categoryId;
    }

    @Override
    public boolean isAnonymousRoom() {
        return false;
    }

    @Override
    public String getRoomNormalBackgroundUrl() {
        if (!TextUtils.isEmpty(getBgImage())) {
            return getBgImage();
        }
        return super.getRoomNormalBackgroundUrl();
    }

    @Override
    public String getPlayUrl() {
        return playUrl;
    }

    @Override
    public boolean canPlayLive() {
        Logger.i("duruochen--preload", "authResult:" + authResult);
        return authResult == null || authResult.getHasAuth() || authResult.getCode() == CourseLiveAuthCheckInfo.HAVE_AUTH
                || (UserInfoMannage.hasLogined() && authResult.getTrySee() && authResult.getRemainTime() >= THRESHOLD_LEFT_VALUE_TIME_MS);
    }

    @Override
    public int getCategoryId() {
        return categoryId;
    }
}
