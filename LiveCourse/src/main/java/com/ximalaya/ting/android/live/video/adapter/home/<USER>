package com.ximalaya.ting.android.live.video.adapter.home;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.video.R;
import com.ximalaya.ting.android.live.video.data.model.VideoLiveCategoryItemInfo;

import java.util.List;

/**
 * 视频直播 列表adapter
 *
 * <AUTHOR>
 * @date 18/8/16
 */

public class LiveCourseHomePageListAdapter extends BaseAdapter {

    private final Context mContext;
    private final List<VideoLiveCategoryItemInfo> mData;

    private final LayoutInflater mInflater;

    private final int mCoverWidth;
    private final int mAvatarWidth;


    //构造
    public LiveCourseHomePageListAdapter(Context context,
                                         List<VideoLiveCategoryItemInfo> data) {
        mContext = context;
        mData = data;
        mInflater = LayoutInflater.from(context);

        mCoverWidth = BaseUtil.dp2px(context, 100);
        mAvatarWidth = BaseUtil.dp2px(context, 16);
    }


    public List<VideoLiveCategoryItemInfo> getData() {
        return mData;
    }

    @Override
    public int getCount() {
        return mData == null ? 0 : mData.size();
    }

    @Override
    public VideoLiveCategoryItemInfo getItem(int position) {

        return ToolUtil.isEmptyCollects(mData) ? null : mData.get(position);

    }

    @Override
    public long getItemId(int position) {
        return position;
    }




    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {

        final ViewHolder holder;

        if (convertView == null) {
            holder = new ViewHolder();

            convertView = mInflater.inflate(R.layout.livecourse_item_layout_homepage_record_list, parent, false);
            holder.container = convertView.findViewById(R.id.live_container_item);
            holder.ivCover = convertView.findViewById(R.id.live_iv_video_cover);
            holder.ivLiveGoingState = convertView.findViewById(R.id.live_iv_live_status);

            holder.tvTitle = convertView.findViewById(R.id.live_tv_course_name);

            holder.mLlBtnEnter = convertView.findViewById(R.id.live_ll_status_btn);
            holder.mIvBtnEnter = convertView.findViewById(R.id.live_iv_btn_status);
            holder.mTvBtnEnter = convertView.findViewById(R.id.live_tv_status_btn);

            holder.mLlLiveHostInfo = convertView.findViewById(R.id.live_ll_host_info);
            holder.mIvHostAvatar = convertView.findViewById(R.id.live_iv_host_avatar);
            holder.mTvHostName = convertView.findViewById(R.id.live_tv_host_name);

            holder.mLlLiveAudi = convertView.findViewById(R.id.live_ll_going_audi_num);
            holder.mIvLiveAudiIc = convertView.findViewById(R.id.live_iv_ic_live_going);
            holder.mTvLiveAudiNum = convertView.findViewById(R.id.live_tv_going_audi_num);

            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (position >= mData.size()) {
            return convertView;
        }

        final VideoLiveCategoryItemInfo model = mData.get(position);

        if (model != null) {

            //显示课程封面
            showCourseCover(model, holder);

            //直播状态角标
            showLiveStatusCornerImg(model, holder);

            //显示课程标题
            holder.tvTitle.setText(model.name);

            //显示进入直播的按钮布局
            showLiveEnterButton(model, holder);

            //显示主播信息
            showLiveHostInfo(model, holder);

            //展示参与人次  暂时不展示
            if (holder.mLlLiveAudi != null) {
                holder.mLlLiveAudi.setVisibility(View.INVISIBLE);
            }
            //showLiveGoingAudiInfo(model, holder);


            holder.container.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListItemCallback != null) {
                        mListItemCallback.onClickCourseItem(holder, model, position);
                    }
                }
            });

            holder.mLlBtnEnter.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListItemCallback != null) {
                        mListItemCallback.onClickBtnEnter(holder, model, position);
                    }
                }
            });


        }


        return convertView;
    }


    /**
     * 显示课程封面
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showCourseCover(VideoLiveCategoryItemInfo model, ViewHolder holder) {

        ImageManager.from(mContext)
                .displayImage(null, holder.ivCover, model.coverMiddle, com.ximalaya.ting.android.host.R.drawable.host_default_album, 0
                        , mCoverWidth, mCoverWidth
                        , null, null, true);
    }


    /**
     * 显示课程状态角标
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showLiveStatusCornerImg(VideoLiveCategoryItemInfo model, ViewHolder holder) {

        if (holder.ivLiveGoingState == null) {
            return;
        }

        holder.ivLiveGoingState.setVisibility(View.VISIBLE);

        switch (model.status) {

            case PersonLiveBase.LIVE_STATUS_END: {
                if (model.playbackStatus == 2 && !TextUtils.isEmpty(model.playbackPath)) {
                    holder.ivLiveGoingState.setImageResource(R.drawable.live_video_ic_tag_playback);
                } else {
                    holder.ivLiveGoingState.setVisibility(View.INVISIBLE);
                }
                break;
            }

            case PersonLiveBase.LIVE_STATUS_NOTICE: {
                holder.ivLiveGoingState.setImageResource(R.drawable.live_video_ic_tag_pre);
                break;
            }

            case PersonLiveBase.LIVE_STATUS_ING: {
                holder.ivLiveGoingState.setImageResource(R.drawable.live_video_ic_tag_going);
                break;
            }

            default:
                holder.ivLiveGoingState.setVisibility(View.INVISIBLE);
                break;
        }

    }



    /**
     * 显示主播信息
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showLiveEnterButton(VideoLiveCategoryItemInfo model, ViewHolder holder) {

        if (holder.mLlBtnEnter == null) {
            return;
        }

        switch (model.status) {
            case PersonLiveBase.LIVE_STATUS_ING: {
                holder.mLlBtnEnter.setBackgroundResource(R.drawable.live_bg_video_btn_status_going);

                holder.mIvBtnEnter.setVisibility(View.VISIBLE);
                holder.mIvBtnEnter.setImageResource(model.type == 1 ? R.drawable.live_video_ic_btn_status_pre : R.drawable.live_video_ic_btn_status_going);
                UIStateUtil.setTextColor(mContext, com.ximalaya.ting.android.host.R.color.host_color_white_ffffff, holder.mTvBtnEnter);

                holder.mTvBtnEnter.setText(model.type == 1 ? "去看直播" : "去听直播");
                break;
            }

            case PersonLiveBase.LIVE_STATUS_END: {
                holder.mLlBtnEnter.setBackgroundResource(R.drawable.live_bg_video_btn_status_playback);

                UIStateUtil.setTextColor(mContext, com.ximalaya.ting.android.host.R.color.host_color_white_ffffff, holder.mTvBtnEnter);


                if (model.playbackStatus == 2 && !TextUtils.isEmpty(model.playbackPath)) {
                    holder.mIvBtnEnter.setVisibility(View.VISIBLE);
                    holder.mIvBtnEnter.setImageResource(model.type == 1 ? R.drawable.live_video_ic_btn_status_pre : R.drawable.live_video_ic_btn_status_going);

                    holder.mTvBtnEnter.setText(model.type == 1 ? "观看回放" : "收听回放");

                } else {
                    holder.mIvBtnEnter.setVisibility(View.GONE);
                    holder.mTvBtnEnter.setText("已结束");
                }


                break;
            }

            case PersonLiveBase.LIVE_STATUS_NOTICE: {
                holder.mLlBtnEnter.setBackgroundResource(R.drawable.live_bg_video_btn_status_pre);

                holder.mIvBtnEnter.setVisibility(View.GONE);
                UIStateUtil.setTextColor(mContext, R.color.live_color_019e91, holder.mTvBtnEnter);


                long currentTimeMillis = System.currentTimeMillis();

                if (model.startAt <= 0 || currentTimeMillis >= model.startAt) {
                    holder.mTvBtnEnter.setText("即将开播");
                } else {
                    holder.mTvBtnEnter.setText(LiveTimeUtil.convertTimeToPreData(model.startAt));
                }

                break;
            }

            default:
                break;
        }

    }

    /**
     * 显示课程进入按钮
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showLiveHostInfo(VideoLiveCategoryItemInfo model, ViewHolder holder) {

        if (holder.mLlLiveHostInfo == null) {
            return;
        }

        ImageManager.from(mContext)
                .displayImage(null, holder.mIvHostAvatar, model.avatar, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88, 0
                        , mAvatarWidth, mAvatarWidth
                        , null, null, true);

        holder.mTvHostName.setText(model.nickname);

    }

    public void clearData() {
        mData.clear();
        notifyDataSetChanged();
    }

    public void addListData(List<VideoLiveCategoryItemInfo> courseList) {
        mData.addAll(courseList);
        notifyDataSetChanged();
    }

    public void removeItem(VideoLiveCategoryItemInfo model) {
        if (mData != null && !mData.isEmpty()) {
            boolean isDel = mData.remove(model);
            if (isDel) {
                notifyDataSetChanged();
            }
        }
    }

    public static class ViewHolder {

        View container;

        ImageView ivCover;
        ImageView ivLiveGoingState;

        TextView tvTitle;//第1行

        LinearLayout mLlBtnEnter;
        ImageView mIvBtnEnter;
        TextView mTvBtnEnter;

        LinearLayout mLlLiveHostInfo;
        ImageView mIvHostAvatar;
        TextView mTvHostName;

        LinearLayout mLlLiveAudi;
        ImageView mIvLiveAudiIc;
        TextView mTvLiveAudiNum;

    }

    private IOnClickListItemCallback mListItemCallback;

    public void setOnClickListItemCallback(IOnClickListItemCallback callback) {
        mListItemCallback = callback;
    }

    public interface IOnClickListItemCallback {

        void onClickCourseItem(ViewHolder holder, VideoLiveCategoryItemInfo model, int pos);

        void onClickBtnEnter(ViewHolder holder, VideoLiveCategoryItemInfo model, int pos);

    }

}
