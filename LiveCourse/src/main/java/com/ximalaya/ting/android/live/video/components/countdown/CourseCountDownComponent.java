package com.ximalaya.ting.android.live.video.components.countdown;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager2.widget.CompositePageTransformer;
import androidx.viewpager2.widget.ViewPager2;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.live.BookBean;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.gift.panel.view.PageIndicator;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.host.utils.LiveSubscribeUtil;
import com.ximalaya.ting.android.live.video.R;
import com.ximalaya.ting.android.live.video.adapter.countdown.rv.RecommendRVHolder;
import com.ximalaya.ting.android.live.video.adapter.countdown.vp.CountDownVPHolder;
import com.ximalaya.ting.android.live.video.adapter.countdown.vp.RecommendVPAdapter;
import com.ximalaya.ting.android.live.video.components2.BaseCourseComponent;
import com.ximalaya.ting.android.live.video.data.model.CourseRoomDetail;
import com.ximalaya.ting.android.live.video.data.model.VideoLiveRecommendInfo;
import com.ximalaya.ting.android.live.video.data.model.VpInfo;
import com.ximalaya.ting.android.live.video.data.request.CommonRequestForLiveCourse;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.util.ArrayList;
import java.util.List;

/**
 * 倒计时组件。
 *
 * <AUTHOR>
 */
public class CourseCountDownComponent extends BaseCourseComponent
        implements ICourseCountDownComponent, CountDownVPHolder.OnSubscribeListener, RecommendRVHolder.OnRecommendListener, View.OnClickListener {
    private Boolean isSubscribe = false; // 是否已预约

    private RelativeLayout mVpRecommendVideoLayout;
    private ViewPager2 mVpRecommend;
    private List<VpInfo> mDatas;
    private RecommendVPAdapter mAdapter;
    private PageIndicator mIndicator;
    private boolean isMore15;
    private LinearLayout mLlWait;
    private LinearLayout mLlReview;
    private RelativeLayout mRlStatus;
    private IRecommendRequestListener mRecommendRequestListener;


    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        mRlStatus = findViewById(R.id.live_video_rl_live_status);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mRlStatus.getLayoutParams();
        //所有的位置关系调整
        layoutParams.topMargin = (int) (BaseUtil.getStatusBarHeight(getContext()) + getContext().getResources().getDimension(R.dimen.livevideo_course_room_status_panel_margin_top));
        mRlStatus.setLayoutParams(layoutParams);
        mVpRecommend = findViewById(R.id.live_video_vp_recommend);
        mVpRecommendVideoLayout = findViewById(R.id.live_course_video_vp_recommend_layout);
        mLlWait = findViewById(R.id.live_video_ll_wait);
        mLlReview = findViewById(R.id.live_ll_review);
        findViewById(R.id.live_video_review_tv).setOnClickListener(this);
        mIndicator = findViewById(R.id.live_video_indicator);
        mDatas = new ArrayList<>();
        mAdapter = new RecommendVPAdapter(getContext(), mDatas);
        mAdapter.setOnSubscribeListener(this);
        mAdapter.setOnRecommendListener(this);
        mVpRecommend.setAdapter(mAdapter);
        mVpRecommend.setPageTransformer(new CompositePageTransformer());
        mVpRecommend.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                updateIndicator(position);
            }
        });

    }


    /*
     * 更新指示器
     * */
    private void updateIndicator(int position) {
        runAfterViewInflate();

        if (mIndicator == null) {
            return;
        }
        if (mDatas == null || mDatas.size() <= 1) {
            ViewStatusUtil.setVisible(View.GONE, mIndicator);
            return;
        }
        if (mDatas.size() > position) {
            mIndicator.setTabCountAndCurrentPosition(mDatas.size(), position);
            ViewStatusUtil.setVisible(View.VISIBLE, mIndicator);
        }

    }

    @Override
    public void bindData(@NonNull CourseRoomDetail hostData) {
        super.bindData(hostData);
        if (getHostData() == null) {
            return;
        }
        isSubscribe = getHostData().isBooking();
        updateLiveStatus(getHostData().getStatus());
    }

    /*
     * 预约状态更新
     * */
    private void setSubscribe(boolean subscribe) {
        runAfterViewInflate();
        if (canUpdateUi()) {
            isSubscribe = subscribe;
            VpInfo countVpInfo = getVpInfo(RecommendVPAdapter.IRecommendItemType.TYPE_COUNT_DOWN);
            if (countVpInfo == null) {
                return;
            }
            countVpInfo.setCountSubscribe(isSubscribe);
            if (mAdapter == null || mDatas == null || mDatas.size() <= 0) {
                return;
            }
            mAdapter.notifyItemChanged(0);
            if (isSubscribe && isMore15 && mDatas.size() > 1) {
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        mVpRecommend.setCurrentItem(1);
                    }
                }, 500);
            }
        }
    }

    private VpInfo getVpInfo(@RecommendVPAdapter.IRecommendItemType int type) {
        if (mDatas == null || mDatas.size() <= 0) {
            return null;
        }
        for (VpInfo vpInfo : mDatas) {
            if (vpInfo == null) {
                continue;
            }
            if (type == vpInfo.getType()) {
                return vpInfo;
            }
        }
        return null;
    }

    private void showRecommend(@CountDownUiStates int type) {
        if (mDatas == null) {
            return;
        }
        mDatas.clear();
        requestRecommend(type);
    }

    @Override
    public void updateLiveStatus(int status) {
        if (getHostData() == null) {
            return;
        }
        //管理UI状态
        @CountDownUiStates int uiState;
        switch (status) {
            case PersonLiveBase.LIVE_STATUS_NOTICE:
                if (getHostData().isCanBooking()) {
                    uiState = CountDownUiStates.STATE_NOSTART_COUNT_DOWN;
                } else {
                    uiState = CountDownUiStates.STATE_NOSTART;
                }
                break;
            case PersonLiveBase.LIVE_STATUS_END:
                if (getHostData().hasPlayBack() && !TextUtils.isEmpty(getHostData().getPlayBackPath())) {
                    uiState = CountDownUiStates.STATE_END_BACK;
                } else if (getHostData().hasPlayBack() && TextUtils.isEmpty(getHostData().getPlayBackPath())) {
                    uiState = CountDownUiStates.STATE_END_BACKING;
                } else {
                    uiState = CountDownUiStates.STATE_END;
                }
                break;
            case PersonLiveBase.LIVE_STATUS_ING:
            default:
                uiState = CountDownUiStates.STATE_LIVING_HIDE;
                break;
        }
        updateUI(uiState);
    }

    @Override
    public void registerRecommendRequestListener(IRecommendRequestListener listener) {
        mRecommendRequestListener = listener;
    }

    private void updateUI(@CountDownUiStates int state) {
        runAfterViewInflate();
        if (getHostData() == null) {
            return;
        }
        switch (state) {
            case CountDownUiStates.STATE_NOSTART:
                setVisibility(View.GONE);
                break;
            case CountDownUiStates.STATE_NOSTART_COUNT_DOWN:
                setVisibility(View.GONE);
                break;
            case CountDownUiStates.STATE_LIVING_HIDE:
                setVisibility(View.GONE);
                break;
            case CountDownUiStates.STATE_END:
            case CountDownUiStates.STATE_END_BACKING:
                showRecommend(state);
                setVisibility(View.VISIBLE);
                ViewStatusUtil.setVisible(View.VISIBLE, mVpRecommendVideoLayout);
                ViewStatusUtil.setVisible(View.GONE, mLlReview, mLlWait);
                break;
            case CountDownUiStates.STATE_END_BACK:
                setVisibility(View.VISIBLE);
                ViewStatusUtil.setVisible(View.VISIBLE, mLlReview);
                ViewStatusUtil.setVisible(View.GONE, mLlWait, mVpRecommendVideoLayout);
                // 课程直播_直播状态面板_观看回放  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(47639)
                        .setServiceId("slipPage")
                        .put("currPage", "videoLive")
                        .put(LiveRecordInfoManager.getInstance().getBaseProps())
                        .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForSlipPage())
                        .createTrace();
                break;
            default:
                setVisibility(View.GONE);
                break;
        }
    }


    private void setVisibility(int visibility) {
        runAfterViewInflate();
        ViewStatusUtil.setVisible(visibility, mRlStatus);
        ICourseCountDownInteraction interaction = getComponentInteractionSafety(ICourseCountDownInteraction.class);
        if (interaction == null) {
            return;
        }
        interaction.countdownVisibilityChanged(visibility);
    }

    /*
     * 请求推荐数据
     * */
    private void requestRecommend(@CountDownUiStates int type) {

        CommonRequestForLiveCourse.getRecommend(getLiveId(), getHostUid(), new IDataCallBack<List<VideoLiveRecommendInfo>>() {
            @Override
            public void onSuccess(@Nullable List<VideoLiveRecommendInfo> data) {
                if (mRecommendRequestListener != null) {
                    mRecommendRequestListener.onRequestRecommendLive(data);
                }
                runAfterViewInflate();
                if (mDatas == null || mAdapter == null) {
                    return;
                }
                VpInfo recommendVpInfo = getVpInfo(RecommendVPAdapter.IRecommendItemType.TYPE_RECOMMEND);
                if (nonRecommend(data, recommendVpInfo, type)) return;
                if (recommendVpInfo == null) {
                    recommendVpInfo = new VpInfo(RecommendVPAdapter.IRecommendItemType.TYPE_RECOMMEND);
                    mDatas.add(recommendVpInfo);
                }
                recommendVpInfo.setRecommendData(data, type);
                mAdapter.notifyDataSetChanged();
                updateIndicator(0);
            }

            @Override
            public void onError(int code, String message) {
                if (mRecommendRequestListener != null) {
                    mRecommendRequestListener.onRequestRecommendLive(null);
                }
            }
        });
    }

    private boolean nonRecommend(@Nullable List<VideoLiveRecommendInfo> data, VpInfo recommendVpInfo, int type) {
        runAfterViewInflate();
        if (CountDownUiStates.STATE_NOSTART_COUNT_DOWN != type) {
            return false;
        }
        if (data != null && !data.isEmpty()) {
            return false;
        }
        if (recommendVpInfo != null) {
            mDatas.remove(recommendVpInfo);
            mAdapter.notifyDataSetChanged();
        }
        return true;
    }


    @Override
    public void onClickRecommend(String itingUrl) {
        LiveCommonITingUtil.handleITing(getActivity(), itingUrl);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.live_video_review_tv) {
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(getContext(), LoginByConstants.LOGIN_BY_HALF_SCREEN);
                return;
            }
            ICourseCountDownInteraction interaction = getComponentInteractionSafety(ICourseCountDownInteraction.class);

            if (interaction != null && !interaction.isHavePlayAuth()) {
                CustomToast.showFailToast("无法观看本场直播回放，请先获取本场观看权益");
                return;
            }
            if (interaction != null) {
                setVisibility(View.GONE);
                interaction.reviewLive();
            }
            // 课程直播_直播状态面板_观看回放  点击事件
            new XMTraceApi.Trace()
                    .click(47638) // 用户点击时上报
                    .put("currPage", "videoLive")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .createTrace();
        }
    }

    /*
     * 预约接口请求
     * */
    private void requestSubscribe(boolean isSubscribe, boolean isAutoSubscribe) {
        if (getHostData() == null) {
            return;
        }
        CommonRequestForLiveCourse.bookLive(isSubscribe,
                getHostData().getLiveId(),
                BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE,
                new IDataCallBack<BookBean>() {
                    @Override
                    public void onSuccess(@Nullable BookBean bookBean) {
                        if (bookBean == null || bookBean.getRet() != 0) {
                            return;
                        }
                        setSubscribe(isSubscribe);
                        if (isAutoSubscribe) {
                            //付费自动预约 不写日历
                            return;
                        }
                        if (!isSubscribe) {
                            return;
                        }
                        BookBean.RemindBean remindBean = bookBean.getData();
                        if (remindBean != null) {
                            LiveSubscribeUtil.openNotificationAndCalendar((BaseFragment2) getFragment(), remindBean.getTitle(), remindBean.getH5Url(), remindBean.getStartAt());
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });

    }

    /*
     * 点击预约埋点
     * */
    private void clickSubTrace(String item) {
        new XMTraceApi.Trace()
                .setMetaId(16695)
                .setServiceId("dialogClick")
                .put("item", item)
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }


    @Override
    public void onClickSubscribe() {
        //检查登陆
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getContext(), LoginByConstants.LOGIN_BY_HALF_SCREEN);
            return;
        }
        if (getHostData() == null) {
            return;
        }
        if (isSubscribe) {
            clickSubTrace("取消预约");
            requestSubscribe(false, false);
        } else {
            clickSubTrace("预约");
            requestSubscribe(true, false);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mRecommendRequestListener = null;
    }

    @Override
    public void onSwitchLive(long liveId) {
        super.onSwitchLive(liveId);
        hide();
    }

    @Override
    public void onSwitchRoom(long newRoomId, Bundle newArgs) {
        super.onSwitchRoom(newRoomId, newArgs);
        hide();
    }

    @Override
    public void hide() {
        runAfterViewInflate();
        setVisibility(View.GONE);
        mRecommendRequestListener = null;
    }

    @Override
    public void subscribeLive() {
        if (isSubscribe) {
            return;
        }
        requestSubscribe(true, true);
    }

}
