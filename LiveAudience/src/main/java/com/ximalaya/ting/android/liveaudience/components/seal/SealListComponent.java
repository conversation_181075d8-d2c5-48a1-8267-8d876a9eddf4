package com.ximalaya.ting.android.liveaudience.components.seal;

import static com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants.BIZ_ID_AUDIO_PERSONAL;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsInfoChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsOrderChangedMessage;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * 商品列表组件
 *
 * <AUTHOR>
 */
public class SealListComponent extends LamiaComponent implements ISealListComponent {

    private GoodsListDialogFragment mGoodsListDialogFragment;


    @Override
    public void showGoodsList() {
        if (!canUpdateUi()) {
            return;
        }
        mGoodsListDialogFragment = GoodsListDialogFragment.newInstance(getContext(), BIZ_ID_AUDIO_PERSONAL, getHostUid(),getRoomId(), getLiveId());
        mGoodsListDialogFragment.setHandleUrlCallback(new GoodsListDialogFragment.HandleUrlCallback() {
            @Override
            public void clickUrl(String url) {
                if (getInteraction() != null) {
                    getInteraction().handleHalfScreenUrl(url);
                }
            }

            @Override
            public void forbidFloatWindow() {
                if (getInteraction() != null) {
                    getInteraction().forbidFloatWindow();
                }
            }
        });

        if (getActivity() instanceof  MainActivity) {
            mGoodsListDialogFragment.show(getChildFragmentManager(),
                    GoodsListDialogFragment.TAG);
        } else if (MainApplication.getTopActivity() instanceof MainActivity) {
            mGoodsListDialogFragment.show(((MainActivity) MainApplication.getTopActivity()).getSupportFragmentManager(),
                    GoodsListDialogFragment.TAG);
        }

        new XMTraceApi.Trace()
                .setMetaId(21373)
                .setServiceId("exposure")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    @Override
    public void hideGoodsList() {
        if (mGoodsListDialogFragment == null || !canUpdateUi()) {
            return;
        }
        mGoodsListDialogFragment.dismiss();
    }

    @Override
    public void onReceiveGoodsInfoChangedMessage(CommonGoodsInfoChangedMessage goodsInfoChangedMessage) {
        if (mGoodsListDialogFragment != null && mGoodsListDialogFragment.canUpdateUi() && mGoodsListDialogFragment.isVisible()) {
            mGoodsListDialogFragment.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        }
    }

    @Override
    public void onReceiveGoodsOrderChangedMessage(CommonGoodsOrderChangedMessage goodsOrderChangedMessage) {
        if (mGoodsListDialogFragment != null && mGoodsListDialogFragment.canUpdateUi() && mGoodsListDialogFragment.isVisible()) {
            mGoodsListDialogFragment.onReceiveGoodsOrderChangedMessage(goodsOrderChangedMessage);
        }
    }

    public ISealListInteraction getInteraction() {
        try {
            return getComponentHost().getInteractionImpl(ISealListInteraction.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void resetView() {
        super.resetView();

        if (null != mGoodsListDialogFragment) {
            mGoodsListDialogFragment.dismiss();
        }
    }
}
