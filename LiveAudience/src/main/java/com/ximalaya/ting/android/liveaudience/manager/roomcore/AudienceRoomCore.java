package com.ximalaya.ting.android.liveaudience.manager.roomcore;

import static com.ximalaya.ting.android.live.host.manager.roomcore.RoomConstants.RoomCoreServiceName.SERVICE_LOVE;
import static com.ximalaya.ting.android.live.host.manager.roomcore.RoomConstants.RoomCoreServiceName.SERVICE_MIC;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.live.zegoload.ZegoSoLoader;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.LivePremiereMsg;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.LivePermissionUtil;
import com.ximalaya.ting.android.live.host.componentview.header.GiftRankOnlineLiveData;
import com.ximalaya.ting.android.live.host.data.LiveMediaSideInfo;
import com.ximalaya.ting.android.live.host.data.stream.ZegoRoomInfo;
import com.ximalaya.ting.android.live.host.manager.RoomStateDataManager;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreIntent;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreLogger;
import com.ximalaya.ting.android.live.host.manager.roomcore.callback.ILeaveMicCallback;
import com.ximalaya.ting.android.live.host.manager.roomcore.service.mic.IRoomMicService;
import com.ximalaya.ting.android.live.host.utils.LiveSoundWaveSendThread;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.constant.BaseCommonProtoConstant;
import com.ximalaya.ting.android.live.lib.chatroom.entity.BaseCommonChatRsp;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatQueryRoomModeRsp;
import com.ximalaya.ting.android.live.lib.stream.IStreamManager;
import com.ximalaya.ting.android.live.lib.stream.StreamManager;
import com.ximalaya.ting.android.live.lib.stream.publish.LiveRoomUserActionStateManager;
import com.ximalaya.ting.android.live.lib.stream.publish.XmLiveRoom;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.liveaudience.data.model.friends.FriendsMicInfoWrapper;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveJoinRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveMicUser;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveOnlineUserSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveSingleWaitUserNotify;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveUserStatusSyncRsp;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeLogicHelper;
import com.ximalaya.ting.android.liveaudience.manager.LiveMediaSideInfoManager;
import com.ximalaya.ting.android.liveaudience.manager.love.ILoveMessageDispatcherManager;
import com.ximalaya.ting.android.liveaudience.manager.love.ILoveMessageManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeMicStateManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeUIManager;
import com.ximalaya.ting.android.liveaudience.manager.love.impl.LoveMessageDispatcherManagerImpl;
import com.ximalaya.ting.android.liveaudience.manager.love.impl.LoveMessageManagerImpl;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveav.lib.constant.IBizIdConstants;
import com.ximalaya.ting.android.liveav.lib.constant.Role;
import com.ximalaya.ting.android.liveav.lib.constant.RtcProviderType;
import com.ximalaya.ting.android.liveav.lib.data.InitConfig;
import com.ximalaya.ting.android.liveav.lib.data.SoundLevelInfo;
import com.ximalaya.ting.android.liveav.lib.data.StreamInfo;
import com.ximalaya.ting.android.liveav.lib.impl.zego.data.ZegoJoinRoomConfig;
import com.ximalaya.ting.android.liveav.lib.listener.IXmAVEventListener;
import com.ximalaya.ting.android.liveav.lib.listener.IXmLiveDataCallBack;
import com.ximalaya.ting.android.liveim.base.callback.ISendCallback;
import com.ximalaya.ting.android.liveim.base.model.JoinChatRoomConfig;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.liveim.micmessage.constants.UserStatus;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import LOVE.Base.LoveMode;


/**
 * 个播观众端直播间
 *
 * <AUTHOR>
 */
public class AudienceRoomCore extends BaseVirtualRoom {


    private final String TAG = "AudienceRoomCore";

    /**
     * 交友模式信令管理
     */
    protected ILoveMessageManager mLoveMessageManager;

    /**
     * 交友模式消息分发管理
     */
    protected ILoveMessageDispatcherManager mLoveMessageDispatcherManager;

    /**
     * 交友模式，申请连麦时返回的 zego 推流信息，保存等待接通后使用
     */
    public FriendsMicInfoWrapper mFriendsMicInfoWrapper;

    private List<Integer> modeList;

    private IXmMicService mMicService;

    private IRoomMicService mRoomMicService;

    protected IAudienceLoveService mAudienceLoveService;

    private CommonLoveOnlineUserSyncRsp commonLoveOnlineUserSyncRsp;

    protected CommonChatQueryRoomModeRsp currentQueryRoomModeRsp;

    public static int FRIEND_MIC_CONNECT = 1;
    public static int FRIEND_MIC_WAITING = 2;
    public static int FRIEND_MIC_IDLE = 3;

    /**
     * 交友模式连麦状态
     */
    public int mFriendMicStatus = -1;

    private final boolean isAnchor;

    public AudienceRoomCore(boolean isAnchor) {
        this.isAnchor = isAnchor;
        if (isAnchor) {
            mRoomMicService = null;
            getRoomServices().remove(SERVICE_MIC);
        }
    }

    @Override
    protected void clearRoomCoreBeforeEnterRoomOrExitRoom() {
        super.clearRoomCoreBeforeEnterRoomOrExitRoom();
        mFriendMicStatus = -1;
        commonLoveOnlineUserSyncRsp = null;
        modeList = null;
        currentQueryRoomModeRsp = null;
        GiftRankOnlineLiveData.getInstance().clearData();
        GiftRankOnlineLiveData.getInstance().clearOnlineData();
    }

    @Override
    public void enterRoom(int bizType, long roomId, long liveId, int enterType, @Nullable RoomCoreIntent coreIntent) {
        super.enterRoom(bizType, roomId, liveId, enterType, coreIntent);
        mFriendMicStatus = -1;
        commonLoveOnlineUserSyncRsp = null;
        GiftRankOnlineLiveData.getInstance().setRoomId(roomId);
    }

    @Override
    public void reenterRoom(int bizType, long roomId, long liveId, int enterType) {
        super.reenterRoom(bizType, roomId, liveId, enterType);
        loadGiftAndOnlineData();
    }

    @Override
    public void onRoomCoreJoinImSuccess(long roomId, int bizType, JoinChatRoomConfig config) {
        super.onRoomCoreJoinImSuccess(roomId, bizType, config);
        loadGiftAndOnlineData();
    }

    private void loadGiftAndOnlineData() {
        PersonLiveDetail detail = (PersonLiveDetail) getRoomDetail();
        if (!isAnchor && detail != null) {
            GiftRankOnlineLiveData.getInstance().loadGiftRank(detail.getLiveId(), detail.getHostUid());
            GiftRankOnlineLiveData.getInstance().loadOnlineUser(detail.getLiveId(), detail.getRoomBizType());
        }
    }

    public void initService() {
        super.initService();
        if (!isAnchor) {
            mRoomMicService = new RoomMicService(this, getStreamManager());
            getRoomServices().put(SERVICE_MIC, mRoomMicService);
            mAudienceLoveService = new AudienceLoveService(this);
            getRoomServices().put(SERVICE_LOVE, mAudienceLoveService);
        }
    }

    public void setModeList(List<Integer> modeList) {
        this.modeList = modeList;
        Logger.i(TAG, " modeList = " + modeList);
        if (!ToolUtil.isEmptyCollects(modeList)) {
            if (modeList.contains(RoomModeManager.MODE_TYPE_NEW_MIC)) {
                mFriendMicStatus = FRIEND_MIC_IDLE;
            }

            if (modeList.contains(RoomModeManager.MODE_MAKE_FRIENDS) && mRoomMicService != null) {
                mRoomMicService.setIsNewMicWaitingMic(false);
            }
        }
    }


    @Override
    protected void initBizManagers() {
        Logger.i(TAG, " initBizManagers");
        RoomCoreLogger.log(getClass().getSimpleName() + "  initBizManagers");

        RoomCoreLogger.log(getClass().getSimpleName() + " 创建 LoveMessageManagerImpl");
        mLoveMessageManager = new LoveMessageManagerImpl(mConnectionManager);
        addManager(ILoveMessageManager.NAME, mLoveMessageManager);

        RoomCoreLogger.log(getClass().getSimpleName() + " 创建 LoveMessageDispatcherManagerImpl");
        mLoveMessageDispatcherManager = new LoveMessageDispatcherManagerImpl(mConnectionManager);
        addManager(ILoveMessageDispatcherManager.NAME, mLoveMessageDispatcherManager);

        //用于下麦
        mMicService = LiveClientManager.getInstance().getLiveMicService();
    }

    @Override
    public void onReceivePremiereMessage(LivePremiereMsg msg) {
        super.onReceivePremiereMessage(msg);
        Logger.i(TAG, "onReceivePremiereMessage()");
        if (coreUiReceiveAndConsumeMessages()) {
            logFile(getClass().getSimpleName() + "  onReceivePremiereMessage 有其他处理，RoomCore不处理");
            return;
        }
        logFile(getClass().getSimpleName() + "  onReceivePremiereMessage 退出RoomCore ");
        roomGoToDie(true, true);
    }

    @Override
    protected void registerListener() {
        Logger.i(TAG, "registerListener");
        RoomCoreLogger.log(getClass().getSimpleName() + "  registerListener");
        super.registerListener();

        //交友模式下的麦上处理
        if (!isAnchor) {
            mLoveMessageDispatcherManager.addLoveMessageReceivedListener(loveMessageReceivedListener);
        }
    }

    @Override
    protected void unregisterListener() {
        Logger.i(TAG, "unregisterListener");
        super.unregisterListener();
        if (!isAnchor) {
            mLoveMessageDispatcherManager.removeLoveMessageReceivedListener(loveMessageReceivedListener);
        }
        stopSyncUserFriendModeStatus();
    }

    @Override
    public void onRoomModeChange(CommonChatQueryRoomModeRsp queryRoomModeRsp) {
        Logger.i(TAG, "onRoomModeChange ");

        if (isAnchor) {
            return;
        }

        if (queryRoomModeRsp == null) {
            return;
        }

        if (sameMode(queryRoomModeRsp)) {
            Logger.i(TAG, "onRoomModeChange sameMode return ");
            return;
        }

        if (!ToolUtil.isEmptyCollects(queryRoomModeRsp.mModeList)) {
            boolean lastModeNewMic = false;
            if (modeList != null) {
                lastModeNewMic = modeList.contains(RoomModeManager.MODE_TYPE_NEW_MIC);
            }
            boolean newModeNewMic = queryRoomModeRsp.mModeList.contains(RoomModeManager.MODE_TYPE_NEW_MIC);
            if (newModeNewMic) {
                mFriendMicStatus = FRIEND_MIC_IDLE;
            }
            boolean lastModeFriend = false;
            if (modeList != null) {
                lastModeFriend = modeList.contains(RoomModeManager.MODE_MAKE_FRIENDS);
            }
            boolean newModeFriend = queryRoomModeRsp.mModeList.contains(RoomModeManager.MODE_MAKE_FRIENDS);
            Logger.i(TAG, "onRoomModeChange, modeList = " + modeList + " receive modeList = " + queryRoomModeRsp.mModeList);
            if (!newModeFriend) {
                mFriendMicStatus = FRIEND_MIC_IDLE;
            }
            if ((lastModeNewMic && !newModeNewMic)
                    || (lastModeFriend && !newModeFriend)) {
                if (!coreUiReceiveAndConsumeMessages()) {
                    leaveMic();
                    release();
                    startPlayLive();
                }
            }
            updateMicWaitingStatusByModeChange(newModeNewMic, newModeFriend);
            this.modeList = queryRoomModeRsp.mModeList;
            this.currentQueryRoomModeRsp = queryRoomModeRsp;
            return;
        }

        int mode = queryRoomModeRsp.mMode;
        Logger.i(TAG, "onRoomModeChange, 兜底：" + mode);

        List<Integer> temModeList = Collections.singletonList(mode);

        boolean lastModeNewMic = false;
        if (modeList != null) {
            lastModeNewMic = modeList.contains(RoomModeManager.MODE_TYPE_NEW_MIC);
        }
        boolean newModeNewMic = temModeList.contains(RoomModeManager.MODE_TYPE_NEW_MIC);
        if (newModeNewMic) {
            mFriendMicStatus = FRIEND_MIC_IDLE;
        }
        boolean lastModeFriend = false;
        if (modeList != null) {
            lastModeFriend = modeList.contains(RoomModeManager.MODE_MAKE_FRIENDS);
        }
        boolean newModeFriend = temModeList.contains(RoomModeManager.MODE_MAKE_FRIENDS);

        if ((lastModeNewMic && !newModeNewMic)
                || (lastModeFriend && !newModeFriend)) {
            if (!coreUiReceiveAndConsumeMessages()) {
                leaveMic();
                release();
                startPlayLive();
            }
        }
        updateMicWaitingStatusByModeChange(newModeNewMic, newModeFriend);
        this.modeList = Collections.singletonList(mode);
        this.currentQueryRoomModeRsp = queryRoomModeRsp;
    }

    private void updateMicWaitingStatusByModeChange(boolean newModeNewMic, boolean newModeFriend) {
        Logger.i(TAG, "updateMicWaitingStatuByModeChange newModeNewMic = " + newModeNewMic + " newModeFriend= " + newModeFriend);

        if (newModeNewMic) {
            mFriendMicStatus = FRIEND_MIC_IDLE;
        }

        if (newModeFriend && mRoomMicService != null) {
            mRoomMicService.setIsNewMicWaitingMic(false);
        }
    }

    public boolean sameMode(CommonChatQueryRoomModeRsp queryRoomModeRsp) {
        if (queryRoomModeRsp == null) {
            return false;
        }

        List<Integer> lastModeList = modeList;
        List<Integer> modeList = queryRoomModeRsp.mModeList;

        if (!ToolUtil.isEmptyCollects(lastModeList)) {
            if (ToolUtil.isEmptyCollects(modeList)) {
                int mode = queryRoomModeRsp.mMode;
                return lastModeList.size() == 1 && lastModeList.get(0) == mode;
            }

            if (lastModeList.size() != modeList.size()) {
                return false;
            }

            for (int i = 0; i < lastModeList.size(); i++) {
                int lastItemMode = LoveModeLogicHelper.getIntValueCheckBeforeUnBox(lastModeList.get(i), -1);
                int newItemMode = LoveModeLogicHelper.getIntValueCheckBeforeUnBox(modeList.get(i), -1);

                if (lastItemMode != newItemMode) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    private void release() {
        Logger.i(TAG, "release");

        if (XmLiveRoom.sharedInstance(MainApplication.mAppInstance).isPublish()) {
            PlayTools.play(BaseApplication.getMyApplicationContext());
        }
        log(" exitRoom  XmLiveRoom.destroySharedInstance");
        XmLiveRoom.destroySharedInstance();
    }

    private void publishFriendsZegoManager(final FriendsMicInfoWrapper info) {
        logFile("start publishFriendsZegoManager");

        if (mMicService == null) {
            logFile("start publishFriendsZegoManagerm，MicService null");
            return;
        }
        if (mMicService.isPublish()) {
            logFile("mMicService.isPublish() ,  已经在推流，无需再推流");
            return;
        }
        logFile("publishFriendsZegoManager");
        InitConfig initConfig = new InitConfig();
        initConfig.sdkAppId = info.appId;
        initConfig.sdkAppKey = new String(ZegoRoomInfo.decryptSignKey(info.appKey), Charset.forName("ISO-8859-1"));
        initConfig.userId = info.userID;
        initConfig.appId = IBizIdConstants.BIZID_TYPE_LAMIA;
        initConfig.audioDeviceMode = MMKVUtil.getInstance().getInt(PreferenceConstantsInHost.KEY_LIVE_AUDIO_DEVICE_MODE, 0);

        // 声音美化 听众端上麦不必打开
        initConfig.isOpenAudioPreProcess = false;


        mMicService.initAvService(RtcProviderType.Zego, MainApplication.sInstance.realApplication, initConfig, ZegoSoLoader.getInstance(), new IXmLiveDataCallBack<Integer>() {
            @Override
            public void onSuccess(Integer integer) {
                // 初始化成功后开始推流
                ZegoJoinRoomConfig joinRoomConfig = new ZegoJoinRoomConfig();
                joinRoomConfig.setRoomId(info.channelName);
                joinRoomConfig.setStreamId(info.streamId);
                joinRoomConfig.setRole(Role.AUDIENCE);
                joinRoomConfig.setUserId(String.valueOf(info.userID));
                JSONObject object = new JSONObject();
                try {
                    object.put("isOnlyAudio", true);
                    object.put("uid", UserInfoMannage.getUid());
                    object.put("isAnchor", false);
                    joinRoomConfig.setExtraInfo(object.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                logFile("mMicService initAvService and joinRoom");
                mMicService.joinRoom(joinRoomConfig, true);
            }

            @Override
            public void onError(int code, String msg) {
                mMicService.unInit();
            }
        });

        mMicService.addAvEventListener(new IXmAVEventListener() {


            @Override
            public void onError(int code, int stateCode, String msg) {

            }

            @Override
            public void onRecvMediaSideInfo(String s) {

            }

            @Override
            public void onCaptureSoundLevel(final int level) {
                AudienceRoomCore.this.onCaptureSoundLevel(level);
            }

            @Override
            public void onCaptureOtherSoundLevel(List<SoundLevelInfo> infos) {

            }

            @Override
            public void onAudioRecordCallback(int type, byte[] bytes, int sampleRate, int channelCount, int bitDepth) {

            }


            @Override
            public void onPlayNetworkQuality(int rtt, float akbps, int quality) {

            }

            @Override
            public void onDisconnect(int i, String s) {

            }


            @Override
            public void onReconnect() {

            }

            @Override
            public void onMixStreamConfigUpdate(int i) {

            }

            @Override
            public void onPushNetworkQuality(int rtt, float akbps, int quality) {

            }


            @Override
            public void onJoinRoom() {
                LoveModeLogicHelper.log("startPublish zego 连麦结果-------" +
                        "\n stateCode:0 ");
                logFile("zego onJoinRoom");
                LoveModeMicStateManager.getInstance().notifyMicConnected();
            }

            @Override
            public void onLeaveRoom() {
                logFile("zego onLeaveRoom");
            }


            @Override
            public void onKickOut() {
                if (mMicService != null) {
                    mMicService.leaveRoom(false);
                    mMicService.unInit();
                }
            }

            @Override
            public void onStreamExtraInfoUpdate(StreamInfo streamInfo) {

            }

            @Override
            public void onLoginRoomCompletion(List<StreamInfo> streamInfos) {

            }

            @Override
            public void onStreamAdd(List<StreamInfo> streamInfos) {

            }

            @Override
            public void onStreamDelete(List<StreamInfo> streamInfos) {

            }

            @Override
            public void onStreamPlaySuccess(String streamId) {

            }

            @Override
            public void onStreamPlayFailed(String streamId) {

            }

            @Override
            public void onMixNotExitStreams(List<String> streams) {

            }
        });
    }

    public void onCaptureSoundLevel(int level) {
        int mineSeatNo = LoveModeMicStateManager.getInstance().getMyMicNo();
        if (mineSeatNo == -1) return;

        // 构造 SEI 音量信息消息
        LiveMediaSideInfo mediaSideInfo = new LiveMediaSideInfo();
        mediaSideInfo.setType(LiveMediaSideInfo.TYPE_VOLUME);
        mediaSideInfo.setContent(
                new LiveMediaSideInfo.MediaSideInfoContent(level, UserInfoMannage.getUid())
        );
        // SEI 发送我的音量消息
        LiveSoundWaveSendThread.postTask(() ->
                XmLiveRoom.sharedInstance(MainApplication.mAppInstance).sendMediaSideInfo(
                        GsonUtils.toJson(mediaSideInfo)));
        log("onCaptureSoundLevel level = " + level);
    }

    @Override
    public void leaveMic() {
        Logger.i(TAG, "leaveMic");
        super.leaveMic();
        if (!UserInfoMannage.hasLogined()) {
            return;
        }
        leaveMic(new ILeaveMicCallback() {
            @Override
            public void onLeaveResult(boolean success, int errorCode, @NonNull String errorMsg) {

            }
        });
    }

    @Override
    public void leaveMic(@Nullable ILeaveMicCallback callback) {
        super.leaveMic(callback);
        if (!UserInfoMannage.hasLogined()) {
            if (callback != null) {
                callback.onLeaveResult(false, 0, "");
            }
            return;
        }
        if (isLoveMicConnected() || isLoveMicWaiting()) {
            //交友模式信令管理下麦
            if (mLoveMessageManager != null) {
                logFile("mLoveMessageManager reqLeave start");
                mLoveMessageManager.reqLeave(getRoomId(), new ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp>() {
                    @Override
                    public void onSuccess(BaseCommonChatRsp baseCommonChatRsp) {
                        Logger.i("信令测试", "交友模式--下麦成功");
                        logFile("mLoveMessageManager reqLeave onSuccess");
                        if (callback != null) {
                            callback.onLeaveResult(true, 0, "");
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMessage) {
                        Logger.i("信令测试", "交友模式--下麦失败  " + errorCode + "   " + errorMessage);
                        logFile("mLoveMessageManager reqLeave onError " + errorCode + errorMessage);
                        HandlerManager.postOnUIThread(new Runnable() {
                            @Override
                            public void run() {
                                if (callback != null) {
                                    callback.onLeaveResult(false, errorCode, errorMessage);
                                }
                            }
                        });
                    }
                });
            } else {
                if (callback != null) {
                    callback.onLeaveResult(false, 0, "");
                }
            }
        } else if (mRoomMicService != null && (mRoomMicService.isMicConnected() || mRoomMicService.isNewMicWaitingMic())) {
            logFile("mLoveMessageManager quitJoinAnchor start");
            mRoomMicService.quitJoinAnchor(new ISendCallback() {
                @Override
                public void onSendSuccess() {
                    Logger.i("信令测试", "连麦--下麦成功");
                    logFile("mRoomMicService quitJoinAnchor onSuccess");
                    if (callback != null) {
                        callback.onLeaveResult(true, 0, "");
                    }
                }

                @Override
                public void onSendError(int code, String message) {
                    Logger.i("信令测试", "连麦--下麦失败  " + code + "   " + message);
                    logFile("mRoomMicService quitJoinAnchor " + code + message);
                    if (callback != null) {
                        callback.onLeaveResult(false, code, message);
                    }
                }
            });
        } else {
            if (callback != null) {
                callback.onLeaveResult(false, 0, "");
            }
        }
    }

    @Override
    public void exitRoom() {
        Logger.i(TAG, "exit!!");
        log(" exitRoom  leaveMic");
        stopSyncUserFriendModeStatus();
        log(" exitRoom  resetMicWaiting");
        resetMicWaiting();
        LoveModeMicStateManager.getInstance().reset(true);
        commonLoveOnlineUserSyncRsp = null;
        mFriendMicStatus = -1;
        super.exitRoom();
        LiveVideoPlayerManager.getInstance().unInit(getRoomBiz(), getRoomId());
        RoomStateDataManager.getInstance().exitRoom();
    }


    public void resetMicWaiting() {
        mFriendMicStatus = FRIEND_MIC_IDLE;
        if (mRoomMicService != null) {
            mRoomMicService.setIsNewMicWaitingMic(false);
        }
    }


    @Override
    public void releaseMic() {
        Logger.i(TAG, "releaseMic");
        release();
    }

    private void startPlayLive() {
        if (getStreamManager() != null) {
            getStreamManager().startPlayStream();
        }
    }

    private void changeZegoMicState(boolean enable) {
        //关闭、开启麦克风
        Logger.i(TAG, "changeZegoMicState enable = " + enable);
        if (mMicService != null) {
            mMicService.enableMic(enable);
        }
    }

    //交友模式信令监听
    private final IVirtualLoveMessageReceivedListener loveMessageReceivedListener = new IVirtualLoveMessageReceivedListener() {

        @Override
        public void onOnlineUserSyncRspReceived(CommonLoveOnlineUserSyncRsp onlineUserSyncRsp) {
            //用户列表更新
            log("onOnlineUserSyncRspReceived ");
            onReceivedOnlineUserList(onlineUserSyncRsp);
        }

        @Override
        public void onSingleWaitUserNotifyMessageReceived(CommonLoveSingleWaitUserNotify singleWaitUserNotify) {
            super.onSingleWaitUserNotifyMessageReceived(singleWaitUserNotify);
            onSingleWaitUserNotifyReceived(singleWaitUserNotify);
        }

        @Override
        public void onUserStatusSyncRspReceived(CommonLoveUserStatusSyncRsp userStatusSyncRsp) {
            //交友模式是否在麦上,做推流处理
            log("onUserStatusSyncRspReceived ");
            onReceiveUserStatusRsp(userStatusSyncRsp);
        }
    };

    protected void onReceiveUserStatusRsp(CommonLoveUserStatusSyncRsp userStatusSyncRsp) {
        log("onUserStatusSyncRspReceived s1 ");

        if (userStatusSyncRsp.mResultCode != 0) {
            return;
        }
        if (!LoveModeLogicHelper.timeStampCheck("user-status-core", userStatusSyncRsp.mTimeStamp)) {
            mFriendMicStatus = FRIEND_MIC_IDLE;
            return;
        }

        Logger.i(TAG, "onUserStatusSyncRspReceived s2 userStatusSyncRsp.mMuteType = " + userStatusSyncRsp.mMuteType);

        if (userStatusSyncRsp.mUserStatus == BaseCommonProtoConstant.CommonUserStatus.USER_STATUS_MICING) {
            //已接通
            mFriendMicStatus = FRIEND_MIC_CONNECT;
            if (!coreUiReceiveAndConsumeMessages()) {
                checkAudioPermissionAndPublish(userStatusSyncRsp);
                changeZegoMicState(userStatusSyncRsp.mMuteType == BaseCommonProtoConstant.CommonMuteType.MUTE_TYPE_UNMUTE);
            }
            Logger.i("交友模式-", "CONNECT");

        } else if (userStatusSyncRsp.mUserStatus == BaseCommonProtoConstant.CommonUserStatus.USER_STATUS_WAITING) {
            //排队中
            mFriendMicStatus = FRIEND_MIC_WAITING;
            Logger.i("交友模式-", "WAITING");
        } else {
            //断开
            mFriendMicStatus = FRIEND_MIC_IDLE;
            if (!coreUiReceiveAndConsumeMessages()) {
                release();
                startPlayLive();
            }
            Logger.i("交友模式-", "IDLE");
        }
    }

    protected void checkAudioPermissionAndPublish(CommonLoveUserStatusSyncRsp userStatusSyncRsp) {

        if (isAnchor) {
            logFile("checkAudioPermissionAndPublish,主播端没有最小化，不在RoomCore处理，由LiveAnchorRoomBaseFasgment operateAfterSDKInit 推流");
            return;
        }

        if (coreUiReceiveAndConsumeMessages()) {
            logFile("checkAudioPermissionAndPublish,由ui在处理消息，RoomCore不处理");
            return;
        }


        LivePermissionUtil.checkAudioPermission(new IMainFunctionAction.IPermissionListener() {

            @Override
            public void havedPermissionOrUseAgree() {
                if (mFriendsMicInfoWrapper == null) {
                    Logger.i(TAG, "onReceivedOnlineUserList s2 reqJoin");
                    mLoveMessageManager.reqJoin(getRoomId(), 0, new ChatRoomConnectionManager.ISendResultCallback<CommonLoveJoinRsp>() {
                        @Override
                        public void onSuccess(CommonLoveJoinRsp commonLoveJoinRsp) {
                            if (commonLoveJoinRsp == null) {
                                return;
                            }

                            LiveRoomUserActionStateManager.getInstance().setUserMicState(userStatusSyncRsp != null ? userStatusSyncRsp.mUserStatus : BaseCommonProtoConstant.CommonUserStatus.USER_STATUS_OFFLINE);

                            mFriendsMicInfoWrapper = getFriendsMicInfo(commonLoveJoinRsp);
                            publishFriendsZegoManager(mFriendsMicInfoWrapper);
                        }

                        @Override
                        public void onError(int errorCode, String errorMessage) {

                        }
                    });
                } else {
                    publishFriendsZegoManager(mFriendsMicInfoWrapper);
                }
            }

            @Override
            public void userReject(Map<String, Integer> noRejectPermiss) {
                CustomToast.showFailToast("未获取到录音权限，无法连麦");
                if (mLoveMessageManager != null) {
                    mLoveMessageManager.reqLeave(getRoomId(), null);
                }
            }
        });
    }

    /**
     * 用户申请上麦，并不会推这条消息，但是RoomCore这里要存储自己在排麦的消息，UI那边是通过req的返回结果来存储，这边先
     * 通过这个方式存储，后续交友上麦的逻辑，需要在Core中处理
     **/
    void onSingleWaitUserNotifyReceived(CommonLoveSingleWaitUserNotify singleWaitUserNotify) {
        Logger.i(TAG, "onSingleWaitUserNotifyReceived singleWaitUserNotify " + singleWaitUserNotify);
        if (singleWaitUserNotify != null && singleWaitUserNotify.mWaitUser != null && singleWaitUserNotify.mWaitUser.mUid == UserInfoMannage.getUid()) {
            if (singleWaitUserNotify.isJoin) {
                Logger.i(TAG, "onSingleWaitUserNotifyReceived 自己 申请 交友 排麦 ");
                mFriendMicStatus = FRIEND_MIC_WAITING;
            } else {
                Logger.i(TAG, "onSingleWaitUserNotifyReceived 自己 取消 交友 排麦 ");
                mFriendMicStatus = FRIEND_MIC_IDLE;
            }
        }
    }

    protected void onReceivedOnlineUserList(CommonLoveOnlineUserSyncRsp onlineUserSyncRsp) {
        Logger.i(TAG, "onReceivedOnlineUserList s1");
        this.commonLoveOnlineUserSyncRsp = onlineUserSyncRsp;
        if (isRoomCoreExiting()) {
            loge(" onReceivedOnlineUserList isRoomCoreExiting " + isRoomCoreExiting());
            return;
        }
        if (onlineUserSyncRsp == null) {
            return;
        }
        if (isAnchor) {
            logFile("onReceivedOnlineUserList,主播端没有最小化，不在RoomCore处理");
            return;
        }
        // 模式类型（普通、心动、PK）
        int mMode = onlineUserSyncRsp.mLoveMode;
        // 只有到这里才知道正确的模式信息，判断是 Pia 戏模式
        RoomStateDataManager.getInstance().getAudienceRoomStateData().setPiaXiMode(LoveModeUIManager.Mode.isPia(mMode));
        if (coreUiReceiveAndConsumeMessages()) {
            logFile("onReceivedOnlineUserList,由ui在处理消息，RoomCore不处理");
            return;
        }

        if (mLoveMessageManager == null) {
            return;
        }

        List<Long> onlineUserId = new ArrayList<>();

        // rsp 中不包含主播信息，需要自己手动填充
        if (LiveRecordInfoManager.getInstance().getAnchorId() > 0) {
            onlineUserId.add(LiveRecordInfoManager.getInstance().getAnchorId());
        }
        for (CommonLoveMicUser onlineUser : onlineUserSyncRsp.mOnlineUserList) {
            long currentUid = onlineUser.mUid;
            if (currentUid > 0) {
                onlineUserId.add(currentUid);
            }
        }

        XmLiveRoom.sharedInstance(MainApplication.mAppInstance).updateOnlineUsers(onlineUserId);
        startSyncUserFriendModeStatus();
    }

    private boolean isFriendModeOnLine(CommonLoveOnlineUserSyncRsp onlineUserSyncRsp) {
        for (CommonLoveMicUser onlineUser : onlineUserSyncRsp.mOnlineUserList) {
            long currentUid = onlineUser.mUid;
            if (currentUid > 0) {
                if (currentUid == UserInfoMannage.getUid()) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void miniRoom() {
        super.miniRoom();
        if ((commonLoveOnlineUserSyncRsp != null && isFriendModeOnLine(commonLoveOnlineUserSyncRsp)) || mFriendMicStatus == FRIEND_MIC_CONNECT) {
            startSyncUserFriendModeStatus();
        } else {
            stopSyncUserFriendModeStatus();
        }
    }

    private FriendsMicInfoWrapper getFriendsMicInfo(CommonLoveJoinRsp commonLoveJoinRsp) {
        String nickname;
        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
        if (user != null && !TextUtils.isEmpty(user.getNickname())) {
            nickname = user.getNickname();
        } else {
            nickname = String.valueOf(UserInfoMannage.getUid());
        }
        return FriendsMicInfoWrapper.newBuilder()
                .appId(commonLoveJoinRsp.mAppId)
                .appKey(commonLoveJoinRsp.mAppKey)
                .channelName(commonLoveJoinRsp.mChannelName)
                .streamId(commonLoveJoinRsp.mStreamId)
                .userID(String.valueOf(UserInfoMannage.getUid()))
                .userName(nickname)
                .userStatus(commonLoveJoinRsp.mUserStatus)
                .muteType(commonLoveJoinRsp.mMuteType)
                .build();
    }

    private static final int SYNC_USER_STATUS_TIME = 5 * 60 * 1000;

    private final Runnable mSyncUserStatusRunnable = new Runnable() {
        @Override
        public void run() {
            getMyStatus();
            HandlerManager.postOnUIThreadDelay(this, SYNC_USER_STATUS_TIME);
        }
    };

    private void stopSyncUserFriendModeStatus() {
        logFile("stopSyncUserFriendModeStatus");
        HandlerManager.removeCallbacks(mSyncUserStatusRunnable);
    }

    private void startSyncUserFriendModeStatus() {
        logFile("startSyncUserFriendModeStatus");
        HandlerManager.removeCallbacks(mSyncUserStatusRunnable);
        HandlerManager.postOnUIThread(mSyncUserStatusRunnable);
    }

    public void getMyStatus() {
        if (mLoveMessageManager == null) {
            return;
        }
        Logger.i(TAG, "getMyStatus");
        mLoveMessageManager.reqSyncUserStatus(getRoomId(), new ChatRoomConnectionManager.ISendResultCallback<CommonLoveUserStatusSyncRsp>() {
            @Override
            public void onSuccess(CommonLoveUserStatusSyncRsp commonLoveUserStatusSyncRsp) {
                Logger.i(TAG, "getMyStatus onSuccess onReceiveUserStatusRsp");
                onReceiveUserStatusRsp(commonLoveUserStatusSyncRsp);
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                Logger.i(TAG, "reqSyncUserStatus, errorCode = " + errorCode
                        + ", errorMessage =" + errorMessage);
            }
        });
    }

    @Override
    public boolean isWaitMicConnecting() {
        IXmMicService micService = getAvService();
        boolean isRequestingMic = micService.getUserStatus() == UserStatus.USER_STATUS_WAITING;
        //交友模式发出连麦申请
        boolean isMicWaiting = mFriendMicStatus == FRIEND_MIC_WAITING;
        return isMicWaiting || isRequestingMic;
    }

    @Override
    public boolean isMicConnected() {
        IXmMicService micService = getAvService();
        boolean isMicConnectedState = micService.getUserStatus() == UserStatus.USER_STATUS_MICING;
        boolean isPublish = micService.isPublish();

        boolean isMoveMicConnect = mFriendMicStatus == FRIEND_MIC_CONNECT;

        return isPublish || isMicConnectedState || isMoveMicConnect;
    }

    public boolean isLoveMicConnected() {
        return mFriendMicStatus == FRIEND_MIC_CONNECT;
    }

    public boolean isLoveMicWaiting() {
        return mFriendMicStatus == FRIEND_MIC_WAITING;
    }

    @Override
    public boolean isPiaXiMode() {
        return isFriendMode() && commonLoveOnlineUserSyncRsp != null && commonLoveOnlineUserSyncRsp.mLoveMode == LoveMode.LOVE_MODE_PIA.getValue();
    }

    @Override
    public boolean isFriendMode() {
        return modeList != null && modeList.contains(RoomModeManager.MODE_MAKE_FRIENDS);
    }

    @NonNull
    @Override
    public IStreamManager createStreamManager() {
        LiveMediaSideInfoManager mediaSideInfoManager = new LiveMediaSideInfoManager();
        return new StreamManager(mediaSideInfoManager);
    }

    public ILoveMessageManager getLoveMessageManager() {
        return mLoveMessageManager;
    }

    public ILoveMessageDispatcherManager getLoveMessageDispatcherManager() {
        return mLoveMessageDispatcherManager;
    }

    @Override
    public int getBusinessId() {
        return IBusinessIdConstants.BIZ_ID_AUDIO_PERSONAL;
    }

    @Override
    public boolean isAnchorVisitor() {
        return UserInfoMannage.getUid() > 0
                && getHostUid() == UserInfoMannage.getUid();
    }

    @Override
    public boolean isPublishStarted() {
        return mMicService.isPublish();
    }

    @NonNull
    @Override
    public String getCoreName() {
        return TAG;
    }


    public IAudienceLoveService getAudienceLoveService() {
        return mAudienceLoveService;
    }
}
