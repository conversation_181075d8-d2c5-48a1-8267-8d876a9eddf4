package com.ximalaya.ting.android.liveaudience.components.header;

import static android.view.View.GONE;
import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;
import static com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType.TYPE_AUDIO;
import static com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive.LIVE_KEY_ITEM_BARRAGE_SWITCH;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveMoreLiveNotifyMsg.TYPE_LIVING;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.rastermill.FrameSequenceDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.dialog.SimpleDialog;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.biz.mode.util.LiveCommonUtil;
import com.ximalaya.ting.android.live.common.lib.base.bean.UserRelationshipAnimationReq;
import com.ximalaya.ting.android.live.common.lib.base.bean.UserRelationshipAnimationRsp;
import com.ximalaya.ting.android.live.common.lib.base.bean.UserRelationshipReqUnit;
import com.ximalaya.ting.android.live.common.lib.base.bean.UserRelationshipRspUnit;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveCommonConstants;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.entity.HeadAnchorInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansClubStatusModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveMoreLiveNotifyMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.OfficialLiveInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.LivePremiereMsg;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereMsgOnlineStatus;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereStatus;
import com.ximalaya.ting.android.live.common.lib.manager.LiveFollowInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.broadcast.LiveLocalBroadcastManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveAccessibilityUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveDateUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveFormatUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGifHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.lib.utils.ViewExtensionKt;
import com.ximalaya.ting.android.live.common.lib.utils.eventbus.LiveEventCenter;
import com.ximalaya.ting.android.live.common.lib.utils.mainarea.MainAreaAnimConfig;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.more.LiveMoreLiveView;
import com.ximalaya.ting.android.live.common.view.widget.DefendRelativeLayout;
import com.ximalaya.ting.android.live.host.components.IBaseLiveHostInteraction;
import com.ximalaya.ting.android.live.host.components.LiveRoomComponentManager;
import com.ximalaya.ting.android.live.host.components.subscriberoom.RoomSubscribeItem;
import com.ximalaya.ting.android.live.host.fragment.morelive.MoreLiveDialogFragment;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.live.host.view.header.LiveHeaderType;
import com.ximalaya.ting.android.live.host.view.header.LiveRoomStatusView;
import com.ximalaya.ting.android.live.host.view.header.LiveVideoAnchorHeaderView;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatFansIntimacyMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansRankMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonFansGroupMsg;
import com.ximalaya.ting.android.live.lifecycle.ComponentManager;
import com.ximalaya.ting.android.liveaudience.components.base.header.AbsHeaderComponent;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceComponentContext;
import com.ximalaya.ting.android.liveaudience.components.header.child.rank.IAudienceHeaderGiftRankComponent;
import com.ximalaya.ting.android.liveaudience.data.model.liveplay.AnchorLiveData;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.liveaudience.view.LiveHeadlinesView;
import com.ximalaya.ting.android.liveaudience.view.dialog.AVUserInfoCardDialog;
import com.ximalaya.ting.android.liveaudience.view.header.LiveVideoOfficialProgramView;
import com.ximalaya.ting.android.liveaudience.view.headlines.LiveHeadLinesTrace;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * 直播间观众端头部组件
 *
 * <AUTHOR>
 */
public class AudienceHeaderComponent extends AbsHeaderComponent implements
        IAudienceHeaderComponent, View.OnClickListener,
        AVUserInfoCardDialog.IShowUserInfoDialog {

    private static final String TAG = "AudienceHeaderComponent";

    private static final int TIME_SECOND = 1000;
    private static int TIME_FANS_JOIN_ANIM_DURATION = 60 * 1000;
    private static final int TIME_FANS_JOIN_START_ANIM = 5 * 1000;

    private BaseFragment2 mAttachFragment;
    private Context mContext;

    protected TextView mTopicTv;
    protected ImageView mTopicIv;

    /**
     * 优惠券
     */
    protected ImageView mCouponView;

    /**
     * 更多直播入口按钮
     */
    private LiveMoreLiveView mMoreLiveView;

    /**
     * 更多直播内容信息
     */
    @Nullable
    private LiveMoreLiveNotifyMsg mMoreLiveInfo;

    /**
     * 上头条
     */
    private LiveHeadlinesView mTopHeadlinesView;
    private TextView mLiveRoomFmNumber;
    private View mShowTopic;

    protected View mMiniView;

    private PersonLiveDetail.LiveAnchorInfo mLiveUserInfo;
    private PersonLiveDetail.LiveRecordInfo mLiveRecordInfo;
    protected AnchorLiveData mAnchorLiveData;
    private long mLiveId;
    private long mRoomId;
    private long mLastAnchorUid;

    private boolean isMiniSupport = false;

    private AudienceHeaderPresenter mPresenter;

    /**
     * 头部第1、2行View
     */
    protected ViewGroup mHeaderVg_1_2;
    /**
     * 头部第1、2行View高度
     */
    protected int mHeaderHeight_1_2;

    /**
     * 整个头部————向上平移的高度（软件盘弹起）
     */
    protected int mHeaderRl_AllHideMarginTop;

    private FollowClass mFollowClass;
    private final int FANS_GUIDE_TIME = 10000;
    private ValueAnimator mHeightAnim;

    private HeadAnchorInfo mCommonChatRoomTopHeadlinesMsg;

    /**
     * 返回按钮
     */
    private View mHeadBackView;

    /**
     * 头部第一排、第二排、第三排
     */
    private ViewGroup mHeaderFirstLine, mHeaderSecondLine;

    /**
     * 弹幕
     */
    private AppCompatImageView mLiveBarrageSwitchView;

    private boolean mIsBarrageSwitchOn = true;

    /**
     * 官方直播间:头顶
     */
    private ConstraintLayout mOfficialFirstPartLayout;
    /**
     * 官方直播间头像
     */
    private ImageView mOfficialHostIv;
    /**
     * 官方直播间关注按钮
     */
    private TextView mOfficialFollowTv;
    /**
     * 官方直播间节目单
     */
    private ImageView mOfficialProgramIv;

    /**
     * 官方直播间:第二部分，公告，热度，人数
     */
    private View mOfficialSecondPartLayout;
    /**
     * 官方直播间:热度
     */
    private TextView mOfficialHotNumTv;

    private ConstraintLayout mVideoOfficialHeaderParentLayout;
    /**
     * 热舞频道 视频官播间 头部 节目单布局
     * 包括 ： 热舞频道icon + 节目单（关注、对号、节目单）
     */
    private LiveVideoOfficialProgramView mVideoOfficialProgramView;
    /**
     * 视频官播间 主播头部View
     * 包括 ：主播头像 + 主播名称 + 关注粉丝团按钮
     */
    private LiveVideoAnchorHeaderView mVideoOfficialAnchorView;

    /**
     * 首次进入一个直播间埋点上报
     */
    private boolean isTrackRoomWhenFirstEnterThisRoom = true;
    private boolean isFirstResume = true;

    private final HideAndShowSomeViewsSmooth mShowHeaderViews = new HideAndShowSomeViewsSmooth();
    private View mSystemTipsTv;

    /**
     * 用户是否已经展示过守护续购引导
     */
    private boolean isLoadUserInfoToGuard = false;
    private View mFirstGroup;

    private final Set<Long> deleteRoomPreview = new HashSet<>(2);

    private void restoreSomeViewToAlpha1() {
        mShowHeaderViews.startShow();
    }

    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        // 强制关闭无障碍 不然会出现头部无障碍阅读框
        if (view instanceof DefendRelativeLayout) {
            ((DefendRelativeLayout) view).setForceDisableAccessibility(true);
        }
        mAttachFragment = (BaseFragment2) getFragment();
        if (null == mAttachFragment) {
            return;
        }

        mPresenter = new AudienceHeaderPresenter(this, mAttachFragment);
        mContext = getContext();
        initView();
        initHeaderHeight();
        initHeightAnim();
    }

    private void initHeaderHeight() {
        mHeaderVg_1_2.measure(0, 0);
        mHeaderHeight_1_2 = mHeaderVg_1_2.getMeasuredHeight();
        mHeaderRl_All.measure(0, 0);
        mHeaderRl_AllHideMarginTop = -(mHeaderRl_All.getMeasuredHeight() +
                BaseUtil.getStatusBarHeight(getContext()));
    }

    private void initHeightAnim() {
        int start, end;
        start = 0;
        end = mHeaderRl_AllHideMarginTop;
        mHeightAnim = ValueAnimator.ofInt(start, end);
        mHeightAnim.setDuration(100);
        mHeightAnim.removeAllUpdateListeners();
        mHeightAnim.addUpdateListener(animation -> {
            int animatedValue = (int) animation.getAnimatedValue();
            updateAnimHeight(animatedValue, mHeaderRl_All);
        });
    }

    private void updateAnimHeight(int animatedValue, View view) {
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) view.getLayoutParams();
        layoutParams.topMargin = animatedValue;
        view.setLayoutParams(layoutParams);
    }

    @Override
    public void updateTopic(String content) {
        runAfterViewInflate(() -> {
            if (getPremiereStatus() != PremiereStatus.PREMIERE_NO && getPremiereStatus() != PremiereStatus.PREMIERE_LIVE_END) {
                //首映室不更新头部公告图标
                return;
            }
            topicUiChange();
        });
    }

    private final Runnable topicTask = new Runnable() {
        @Override
        public void run() {
            runAfterViewInflate(() -> {
                if (mTopicIv != null) {
                    mTopicIv.setVisibility(VISIBLE);
                }
                if (mTopicTv != null) {
                    if (getLiveRecordInfo() != null && getLiveRecordInfo().status == PersonLiveBase.LIVE_STATUS_NOTICE) {
                        mTopicTv.setText("预告");
                        ViewExtensionKt.show(mShowTopic);
                    } else {
                        mTopicTv.setText("公告");
                        ViewExtensionKt.showOrGone(mShowTopic, isOfficialLive() || isPremiere());
                    }
                }
            });
        }
    };

    private void topicUiChange() {
        runAfterViewInflate(() -> {
            if (null != mTopicTv && "公告".contentEquals(mTopicTv.getText())) {
                return;
            }
            UIStateUtil.setVisibility(mTopicIv, GONE);
            UIStateUtil.safelySetText(mTopicTv, "公告");
        });
        HandlerManager.postOnUIThreadDelay(topicTask, 5000);
    }

    private boolean hasCoupons = false;

    @Override
    public void setCouponViewVisible(boolean hasCoupons) {
        runAfterViewInflate(() -> {
            this.hasCoupons = hasCoupons;
            if (mCouponView.getVisibility() != VISIBLE) {
                //一开始是隐藏的，
                if (hasCoupons) {
                    //曝光埋点
                    makeShowTrack(33376);
                }
            }
            if (mCouponView != null) {
                LiveGifHelper.fromRawResource(getContext().getResources(), com.ximalaya.ting.android.live.common.R.drawable.live_common_coupon, new LiveGifHelper.LoadCallback() {
                    @Override
                    public void onLoaded(@NonNull FrameSequenceDrawable drawable) {
                        if (null == mCouponView) {
                            return;
                        }
                        mCouponView.setVisibility(hasCoupons ? View.VISIBLE : View.GONE);
                        mCouponView.setImageDrawable(drawable);
                        drawable.setLoopBehavior(FrameSequenceDrawable.LOOP_FINITE);
                        drawable.setLoopCount(1);
                        drawable.start();
                        if (isOfficialLive()) {
                            mCouponView.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onError(String errorMsg) {
                        CustomToast.showDebugFailToast("优惠券icon弹出动画解析失败");
                        if (null == mCouponView) {
                            return;
                        }
                        mCouponView.setImageResource(R.drawable.live_common_coupon_default);
                    }
                });
            }
        });
    }

    /**
     * 加入粉丝团成功更新状态
     */
    @Override
    public void updateFansJoinSuccessState() {
        runAfterViewInflate(() -> {
            if (getHostData() == null) {
                return;
            }

            getHostData().roomFansClubVo.setCode(1);
            getHostData().roomFansClubVo.setFansGrade(1);
            switch (liveBizTypeInHeader) {
                case PERSONAL_LIVE_ROOM_TYPE:
                    mAnchorHeaderView.setChildFansGrade(String.valueOf(getHostData().roomFansClubVo.getFansGrade()));
                    mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    break;
                case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.setChildFansGrade(String.valueOf(getHostData().roomFansClubVo.getFansGrade()));
                        mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    }
                    break;
                case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                    if (mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == VISIBLE) {
                        mPremiereAnchorHeaderView.setChildFansGrade(String.valueOf(getHostData().roomFansClubVo.getFansGrade()));
                        premiereFollowViewUpdate(getHostData().isFollowed(), false);
                    }
                    break;
            }
        });
    }

    /**
     * 粉丝百人团任务
     */
    private final Runnable fansPopRunnable = new Runnable() {
        @Override
        public void run() {
            playFansViewProgressAnim(liveFans100PopUrl, liveFansPopNumber);

        }
    };

    /**
     * 播放粉丝百人、千人团动画
     *
     * @param url 动画图片地址
     */
    @Override
    public void playFansViewProgressAnim(String url, int number) {
        runAfterViewInflate(() -> {
            if (canUpdateUi()) {
                //该方法为信令调用，不做一天只展示一次限制
                if (number >= LIVE_FANS_1000) {
                    MmkvCommonUtil.getInstance(getContext())
                            .saveString(PreferenceConstantsInLive.LIVE_KEY_AUDIENCE_FANS_POP_1000_TAG + mRoomId,
                                    LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()));
                } else if (number >= LIVE_FANS_100) {
                    MmkvCommonUtil.getInstance(getContext())
                            .saveString(PreferenceConstantsInLive.LIVE_KEY_AUDIENCE_FANS_POP_100_TAG + mRoomId,
                                    LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()));
                }
                try {
                    IAudienceHeaderGiftRankComponent component = getComponent(HeaderCompConfig.HEADER_GIFT_RANK_COMPONENT);
                    if (null != component) {
                        component.playFansAnim(url);
                    }
                } catch (Exception e) {
                    Logger.e(TAG, e.getMessage());
                }
            }
        });
    }

    @Override
    protected void initView() {
        super.initView();
        liveBizTypeInHeader = PERSONAL_LIVE_ROOM_TYPE;
        //头部主播信息view
        mAnchorHeaderView = findViewById(R.id.live_header_owner_icon_layout);
        mAnchorHeaderView.bindHeaderOnClickListener(this);
        mAnchorHeaderView.updateContentByHeaderType(LiveHeaderType.HEADER_TYPE_AUDI);

        mSystemTipsTv = findViewById(R.id.live_premiere_system_tip);
        mHeaderHostAvatarStokeIv = findViewById(R.id.live_audience_header_iv_stoke_image);
        mHeaderHostRewardAvatarDecorateIv = findViewById(R.id.live_audience_header_reward_avatar_decorate);
        mHeaderRelationshipAnimationView = findViewById(R.id.live_audience_header_relationship_animation);

        //主播FM号
        mLiveRoomFmNumber = findViewById(R.id.live_room_num);

        mPremiereAnchorHeaderView.bindHeaderOnClickListener(this);
        mPremiereAnchorHeaderView.updateContentByHeaderType(LiveHeaderType.HEADER_TYPE_AUDI_PREMIERE);

        if (null != mIvClose) {
            mIvClose.setOnClickListener(this);
        }

        //最小化的按钮
        mMiniView = findViewById(R.id.live_btn_mini_room);
        if (mMiniView != null) {
            mMiniView.setVisibility(View.GONE);
            mMiniView.setOnClickListener(this);
        }

        AutoTraceHelper.bindData(mIvClose, AutoTraceHelper.MODULE_DEFAULT, "");
        mTopicTv = findViewById(R.id.live_header_tv_topic);
        mCouponView = findViewById(R.id.live_iv_coupon);
        mMoreLiveView = findViewById(R.id.live_header_more_live);
        if (mMoreLiveView != null) {
            mMoreLiveView.setEventListener(new LiveMoreLiveView.MoreLiveViewListener() {
                @Override
                public boolean canShowGuide() {
                    if (getRoomCore() == null) {
                        return false;
                    }
                    return getRoomCore().getRoomCoreStayTime() > LiveMoreLiveView.COMMON_DELAY;
                }

                @Override
                public void onPresentInfoChanged(@Nullable LiveMoreLiveNotifyMsg msg) {
                    mMoreLiveInfo = msg;
                }

                @Override
                public void onClick() {
                    if (mMoreLiveInfo == null || TextUtils.isEmpty(mMoreLiveInfo.getIting())) {
                        showMoreLiveDialog(0, false, null);
                    } else {
                        LiveCommonITingUtil.handleITing(getActivity(), mMoreLiveInfo.getIting());
                    }
                }
            });
        }
        if (null != mRanksView) {
            mRanksView.setOnClickListener(this);
        }
        if (null != mCouponView) {
            mCouponView.setOnClickListener(this);
        }
        //直播公告
        mShowTopic = findViewById(R.id.live_header_topic);
        if (null != mShowTopic) {
            mShowTopic.setOnClickListener(this);
        }

        mHeaderVg_1_2 = findViewById(R.id.live_room_header);
        mHeaderVg_1_2.setOnLongClickListener(v -> {
            String playUrl = XmPlayerManager.getInstance(mContext).getCurPlayUrl();
            LiveLamiaUtil.showDebugDialog(playUrl, mAttachFragment.getActivity());
            return true;
        });

        //上头条控件
        mTopHeadlinesView = findViewById(R.id.live_top_view);
        mTopHeadlinesView.setOnClickListener(this);

        AutoTraceHelper.bindData(mTopHeadlinesView, AutoTraceHelper.MODULE_DEFAULT, mCommonChatRoomTopHeadlinesMsg);

        mHeadBackView = findViewById(R.id.liveHeadBackView);
        mHeaderSecondLine = findViewById(R.id.liveaudienceHeaderSecondLine);
        mHeaderFirstLine = findViewById(R.id.liveaudienceHeaderFirstLine);
        mLiveBarrageSwitchView = findViewById(R.id.liveBarrageSwitch);
        mHeadBackView.setOnClickListener(this);
        mLiveBarrageSwitchView.setOnClickListener(this);

        boolean barrageSwitch = MMKVUtil.getInstance().getBoolean(LIVE_KEY_ITEM_BARRAGE_SWITCH, true);
        if (barrageSwitch) {
            mLiveBarrageSwitchView.setBackgroundResource(com.ximalaya.ting.android.live.common.R.drawable.livecomm_danmu_switch_on);
        } else {
            mLiveBarrageSwitchView.setBackgroundResource(com.ximalaya.ting.android.live.common.R.drawable.livecomm_danmu_switch_off);
        }

        mFirstGroup = findViewById(R.id.live_audience_header_layout);
        mOfficialFirstPartLayout = findViewById(R.id.live_cl_official_container);

        // 官方直播间:第二部分
        ViewStub mOfficialSecondHeaderVs = findViewById(R.id.live_vs_official_second_layout);
        mOfficialFollowTv = mOfficialFirstPartLayout.findViewById(R.id.live_iv_official_follow_tv);
        mOfficialHostIv = mOfficialFirstPartLayout.findViewById(R.id.live_iv_official_image);
        mOfficialProgramIv = mOfficialFirstPartLayout.findViewById(R.id.live_iv_program);
        mOfficialSecondPartLayout = mOfficialSecondHeaderVs.inflate();
        mOfficialHotNumTv = mOfficialSecondPartLayout.findViewById(R.id.liveaudience_tv_official_hot_num);
        mVideoOfficialProgramView = findViewById(R.id.liveaudi_video_official_program);
        mVideoOfficialHeaderParentLayout = findViewById(R.id.live_audience_fl_first_live_info);
        mVideoOfficialAnchorView = findViewById(R.id.liveaudi_video_official_anchor_view);

        someViewsToAlpha0();
    }

    @Override
    public void onClick(@NonNull View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        runAfterViewInflate(() -> {
            int i = v.getId();
            if (i == R.id.live_header_owner_icon || i == R.id.live_timing_layout || i == R.id.liveaudi_anchor_name_tv || i == R.id.liveaudi_header_owner_icon) {
                //点击头像，横屏视频模式下，修改为相应时间，不做拦截处理
                if (!isFunctionSwitchOpen()) {
                    if (getAudienceHeaderInteraction() != null) {
                        getAudienceHeaderInteraction().requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                        showUserInfoPop(getHostUid());
                    }
                } else {
                    showUserInfoPop(getHostUid());
                }

                makeClickTrack(33355);
            } else if (i == R.id.live_follow_tv) {
                if (mLiveUserInfo == null) {
                    return;
                }
                followClickTrack();
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mContext);
                    return;
                }
                mPresenter.requestFollow(getHostData());
            } else if (i == R.id.live_header_topic) {
                if (getAudienceHeaderInteraction() != null) {
                    getAudienceHeaderInteraction().showTopicAndNoticeDialog();
                    makeClickTrack(33369);
                }
            } else if (i == R.id.live_btn_close_room) {
                makeClickTrack(33499);
                onCloseRoomClick();
                LiveHelper.Log.i("live event : quit, mLiveId : " + mLiveId);
            } else if (i == R.id.live_btn_mini_room) {

                onMiniRoomClick();

                if (getHostData() != null) {
                    //ABTest下的最小化埋点
                    new XMTraceApi.Trace()
                            .setMetaId(57212)
                            .setServiceId("click")
                            .put("currPage", "liveRoom")
                            .put("liveId", getHostData().getLiveId() + "")
                            .put("roomId", getHostData().getRoomId() + "")
                            .put("LiveBroadcastState", getLiveStatus() + "")
                            .put("liveRoomType", getLiveMediaType() == TYPE_AUDIO ? "1" : "4")
                            .put("anchorId", getHostUid() + "")
                            .createTrace();
                }

            } else if (i == R.id.live_fans_fl || i == R.id.live_video_fans_fl || i == R.id.live_tv_fans_grade || i == R.id.live_rl_fans_grade) {
                //点击粉丝团红心
                makeClickTrack(33358);
                if (!isFunctionSwitchOpen()) {
                    if (getAudienceHeaderInteraction() != null) {
                        getAudienceHeaderInteraction().requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                        showFansH5Dialog();
                    }
                } else {
                    showFansH5Dialog();
                }
            } else if (i == R.id.live_top_view) {
                if (canUpdateUi() && getHostData() != null && getAudienceHeaderInteraction() != null) {
                    Bundle bundle = new Bundle();
                    // 设置 ui 显示参数
                    String url = LiveUrlConstants.getInstance().getHeadlinesH5Url(getHostUid(), getRoomId(), getChatId(), getLiveId());
                    bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
                    bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, getDialogHeightByHeadType());
                    bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, 0);
                    bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, "bottom");
                    // 打开弹窗
                    getAudienceHeaderInteraction().showCustomH5Dialog(bundle);

                    LiveHeadLinesTrace.headLiveClick(mTopHeadlinesView.getTraceStyle());
                    makeClickTrack(33371);
                    new XMTraceApi.Trace()
                            .setMetaId(27756)
                            .setServiceId("click")
                            .put("currPage", "live")
                            .put("roomId", mRoomId + "")
                            .put("anchorId", getHostUid() + "")
                            .put("liveRoomType", "1")
                            .put("liveCategoryId", String.valueOf(RoomModeManager.getInstance().getRoomMode()))
                            .put("liveId", mLiveId + "")
                            .createTrace();
                }
            } else if (i == R.id.live_iv_coupon) {
                //优惠券
                if (isFull()) {
                    IBaseLiveHostInteraction interaction = getComponentInteractionSafety(IBaseLiveHostInteraction.class);
                    if (interaction != null) {
                        interaction.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                    }
                }
                if (getAudienceHeaderInteraction() != null) {
                    getAudienceHeaderInteraction().onClickCouponView();
                    makeClickTrack(33375);
                }
            } else if (i == R.id.liveHeadBackView) {
                onCloseRoomClick();
            } else if (i == R.id.liveBarrageSwitch) {
                if (mIsBarrageSwitchOn) {
                    mLiveBarrageSwitchView.setBackgroundResource(com.ximalaya.ting.android.live.common.R.drawable.livecomm_danmu_switch_off);
                } else {
                    mLiveBarrageSwitchView.setBackgroundResource(com.ximalaya.ting.android.live.common.R.drawable.livecomm_danmu_switch_on);
                }
                mIsBarrageSwitchOn = !mIsBarrageSwitchOn;
                if (getAudienceHeaderInteraction() != null) {
                    getAudienceHeaderInteraction().doBarrageOperation(mIsBarrageSwitchOn);
                }
            } else if (i == R.id.live_container_right_defend_bg) {
                // 守护席入口
                if (getAudienceHeaderInteraction() != null) {
                    if (!isFunctionSwitchOpen()) {
                        getAudienceHeaderInteraction().requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                        getAudienceHeaderInteraction().showGuardListPage();
                    } else {
                        getAudienceHeaderInteraction().showGuardListPage();
                    }
                    // 直播间-守护席入口  点击事件
                    makeClickTrack(43889);
                }
            } else if (i == R.id.live_iv_official_follow_tv) {
                //点击关注官方直播
                followOfficialLive();
            } else if (i == R.id.live_iv_program) {
                //打开官方直播间节目单
                openOfficialProgram();
            } else if (i == R.id.livehost_room_status_tag_premiere) {
                if (mPremiereAnchorHeaderView.getLiveStatusTvSystem()) {
                    ViewStatusUtil.setVisible(VISIBLE, mSystemTipsTv);
                }
            }
        });
    }

    /**
     * 用于区分头部关注按钮点击事件分发
     */
    private void followClickTrack() {
        //如果是官播间，视频关注在这里， 音频关注埋点在 OfficialCardView中
        if (liveBizTypeInHeader == VIDEO_OFFICIAL_LIVE_ROOM_TYPE) {
            new XMTraceApi.Trace()
                    .click(46923)
                    .put("currPage", "liveRoom")
                    .put("isOfficialLive", "true")
                    .put("screenDirection", DeviceUtil.isLandscape(MainApplication.getTopActivity()) ? "1" : "2")
                    .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForSlipPage())
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .createTrace();
        } else {
            makeClickTrack(33356);
        }
    }

    /**
     * 打开官方直播间节目单
     */
    private void openOfficialProgram() {
        if (getHostData() == null) {
            return;
        }
        OfficialLiveInfo officialInfo = getHostData().getOfficialInfo();
        if (officialInfo != null) {
            String playbillITingUrl = officialInfo.getPlaybillItingUrl();
            if (isAnchor()) {
                playbillITingUrl = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(playbillITingUrl, "isAnchor=true");
            }
            LiveCommonITingUtil.handleITing(getActivity(), playbillITingUrl);
        }
    }

    /**
     * 关注音频官方直播频道
     */
    private void followOfficialLive() {
        if (getHostData() == null) {
            return;
        }
        OfficialLiveInfo officialInfo = getHostData().getOfficialInfo();
        if (officialInfo != null) {
            String specificParams = LiveFollowInfoManager.getInstance().getOfficialFollowParams(officialInfo.getUid());
            AnchorFollowManage.followV3(
                    getActivity(), officialInfo.getUid(), false,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM_HEADER, specificParams,
                    new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean aBoolean) {
                            runAfterViewInflate(() -> {
                                if (aBoolean != null && aBoolean && canUpdateUi()) {
                                    if (getHostData() != null && getHostData().getLiveAnchorInfo() != null) {
                                        getHostData().getOfficialInfo().setFollowFlag(true);
                                    }
                                    LiveHelper.Log.i("liveMediaTypeInHeader : " + liveBizTypeInHeader);
                                    if (liveBizTypeInHeader == AUDIO_OFFICIAL_LIVE_ROOM_TYPE) {
                                        CustomToast.showSuccessToast("关注成功");
                                        updateOfficialFollowStatus(true);
                                    } else {
                                        if (mVideoOfficialProgramView != null) {
                                            mVideoOfficialProgramView.setFollowStatus(true, true);
                                        }
                                    }
                                }
                            });
                        }

                        @Override
                        public void onError(int code, String message) {
                            if (!canUpdateUi()) {
                                return;
                            }
                            CustomToast.showFailToast("操作失败，请重试");
                        }
                    }, true, true
            );
        }

    }

    /**
     * 抢头条dialog高度根据头条状态设置初始化
     *
     * @return dialog高度
     */
    private int getDialogHeightByHeadType() {
        switch (mTopHeadlinesView.getTraceStyle()) {
            case LiveHeadLinesTrace.HEAD_LINES_STYLE_NO_TITLE:
                if (UserInfoMannage.getUid() == getHostData().getHostUid()) {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 560 / 812);
                } else {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 604 / 812);
                }
            case LiveHeadLinesTrace.HEAD_LINES_STYLE_HAS_TITLE:
            case LiveHeadLinesTrace.HEAD_LINES_STYLE_DOING_TITLE:
                if (UserInfoMannage.getUid() == getHostData().getHostUid()) {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 350 / 812);
                } else {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 400 / 812);
                }
        }
        return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 560 / 812);
    }

    public void showMoreLiveDialog(
            int pos, boolean fromSlide, @Nullable MoreLiveDialogFragment.RecParam recParam
    ) {
        if (getAudienceHeaderInteraction() != null) {
            getAudienceHeaderInteraction().showMoreLiveDialog(
                    pos, fromSlide, recParam
            );
        }
    }

    private void trackLeaveRoom() {
        new XMTraceApi.Trace()
                .pageExit2(33354)
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    public void showH5GiftRankPage() {
        runAfterViewInflate(() -> {
            if (getHostData() == null) {
                return;
            }

            LiveLamiaUtil.hideSoftInput(mAttachFragment);
            makeClickTrack(33364);
            if (getAudienceHeaderInteraction() != null) {
                getAudienceHeaderInteraction().showGiftRankH5Page();
            }
        });
    }

    private void showFansH5Dialog() {
        //如果开通儿童模式，不能打开粉丝团;此处判断防止入口遗漏能进入直播间
        if (LiveHostCommonUtil.checkChildrenModeOpen(mContext)) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext);
            return;
        }
        showFansClubDialog();
        new UserTracking()
                .setSrcPage("live")
                .setSrcPageId(mLiveId)
                .setSrcModule("topTool")
                .setItem("button")
                .setItemId("粉丝团")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_LIVE_PAGE_CLICK);
    }

    /**
     * 切换直播间，重置页面数据
     **/
    @Override
    public void onSwitchRoom(long newRoomId, Bundle newArgs) {
        trackLeaveRoom();
        isTrackRoomWhenFirstEnterThisRoom = true;
        super.onSwitchRoom(newRoomId, newArgs);
        runAfterViewInflate(() -> {
            ViewStatusUtil.setVisible(View.GONE, mSystemTipsTv, mPremiereLogoIv);
            hideRankAvatarViews();
            HandlerManager.removeCallbacks(rotateRun);
            HandlerManager.removeCallbacks(topicTask);
            HandlerManager.removeCallbacks(fansPopRunnable);
            mAnchorHeaderView.stopFollowAttentionEndAction();

            if (mVideoOfficialAnchorView != null) {
                mVideoOfficialAnchorView.stopFollowAttentionEndAction();
            }
            //只有竖屏能够切换房间，横屏场景不处理
            mPremiereAnchorHeaderView.stopFollowAttentionEndAction();

            isLoadUserInfoToGuard = false;

            mAnchorHeaderView.updateTagViewHeight();
            //音视频官播间切换，需要重置
            if (mVideoOfficialProgramView != null) {
                mVideoOfficialProgramView.reset();
                mVideoOfficialProgramView.setVisibility(GONE);
            }
            if (mOfficialFirstPartLayout != null) {
                mOfficialFirstPartLayout.setVisibility(GONE);
            }
            if (mMoreLiveView != null) {
                mMoreLiveView.clearData();
            }
        });
        deleteRoomPreview.clear();
    }

    @Override
    public void onSwitchSameRoom() {
        super.onSwitchSameRoom();
        runAfterViewInflate(() -> mAnchorHeaderView.updateTagViewHeight());
    }

    private void trackRoom() {
        String cid = !TextUtils.isEmpty(getLiveAdCid()) ? getLiveAdCid() : "";
        new XMTraceApi.Trace()
                .pageView(33353, "liveRoom")
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .put("adCid", cid)
                .createTrace();
    }

    Runnable rotateRun = new Runnable() {
        @Override
        public void run() {
            runAfterViewInflate(() -> {
                if (mAttachFragment != null && mAttachFragment.isRealVisable()) {
                    int joinLiveCount = MmkvCommonUtil.getInstance(mContext).getIntCompat(PreferenceConstantsInLive.LIVE_KEY_JOIN_LIVE_ROOM_COUNT, 0);
                    MmkvCommonUtil.getInstance(mContext).saveInt(PreferenceConstantsInLive.LIVE_KEY_JOIN_LIVE_ROOM_COUNT, ++joinLiveCount);
                }
            });
        }
    };

    @Override
    public void bindData(@NonNull PersonLiveDetail liveDetailData) {
        super.bindData(liveDetailData);
        runAfterViewInflate(() -> {
            if (getHostData() != null) {
                showOrHideHeaderViews(true);
                //退出拦截AB测试中 才会出现最小化按钮
                int playSource = 0;
                if (getAudienceHeaderInteraction() != null) {
                    playSource = getAudienceHeaderInteraction().getPlaySource();
                }
                boolean supportMini = LiveCommonUtil.isSupportMini(playSource, mAttachFragment.getActivity());
                if (mMiniView != null && supportMini && getLiveStatus() == PersonLiveBase.LIVE_STATUS_ING &&
                        !(getLiveMediaType() == LiveMediaType.TYPE_VIDEO && !LiveSettingManager.canShowMiniViewConfig())) {
                    mMiniView.setVisibility(VISIBLE);
                    isMiniSupport = true;
                } else if (mMiniView != null) {
                    mMiniView.setVisibility(GONE);
                    isMiniSupport = false;
                }

                mLiveUserInfo = getHostData().getLiveAnchorInfo();
                mLiveRecordInfo = getHostData().getLiveRecordInfo();
                mLiveId = mLiveRecordInfo != null ? mLiveRecordInfo.id : 0;

                mAnchorLiveData = AnchorLiveData.getInstance();
                mAnchorLiveData.setDetailInfo(liveDetailData);
                mLastAnchorUid = mAnchorLiveData.getUserUid();
                mRoomId = mAnchorLiveData.roomId;

                updateUi();

                Logger.i(TAG, "follow-z: register " + mLastAnchorUid);

                if (!getHostData().isFollowed()) {
                    makeShowTrack(33357);
                } else {
                    if (getHostData().roomFansClubVo == null) {
                        return;
                    }
                    if (getHostData().roomFansClubVo.getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN) {
                        makeShowTrack(33359);
                    }
                }

                if (isTrackRoomWhenFirstEnterThisRoom) {
                    trackRoom();
                    isTrackRoomWhenFirstEnterThisRoom = false;
                }
                setShowBirthdayView();
                setShowRewardAvatarDecorate();

                initTopHeadLineViewVisibility();
                initRankViewVisibility();
                initMoreLiveViewVisibility();
                checkFansCount(liveDetailData);
                if (liveDetailData.getOfficialInfo() != null) {
                    updateOfficialFollowStatus(liveDetailData.getOfficialInfo().getFollowFlag());
                }
            }

            if (getHostData().getTopMsg() != null && initTopHeadLineViewVisibility() && getHostData() != null) {
                if (getHostData().getHeadAnchor() != null) {
                    //有数据就展示
                    Logger.i("上头条测试", "组件bindData-有数据就展示");
                    mTopHeadlinesView.loadData(getHostData().getHeadAnchor().getHasHeadAnchor(),
                            getHostData().getHeadAnchor().getHeadAnchorInfo(),
                            Boolean.TRUE.equals(getHostData().getHeadAnchor().getShowHeadAnchor()));
                    LiveHeadLinesTrace.headLiveShow(mTopHeadlinesView.getTraceStyle());
                } else {
                    if (httpDataIsShowHeadline()) {
                        Logger.i("上头条测试", "组件bindData-接口让展示");
                        mTopHeadlinesView.setVisibility(VISIBLE);
                    } else {
                        Logger.i("上头条测试", "组件bindData-接口不让展示");
                        mTopHeadlinesView.setVisibility(GONE);
                    }
                }
            }
            restoreSomeViewToAlpha1();
        });
    }

    private static final int LIVE_FANS_100 = 100;

    private static final int LIVE_FANS_1000 = 1000;

    /**
     * 粉丝 百千团地址
     */
    private String liveFans100PopUrl;

    /**
     * 粉丝团 百千团人数
     */
    private int liveFansPopNumber;

    /**
     * 检查粉丝团人数
     */
    private void checkFansCount(PersonLiveDetail liveDetailData) {
        if (liveDetailData.roomFansClubVo == null ||
                TextUtils.isEmpty(liveDetailData.roomFansClubVo.getActiveOrGuardIcon())) {
            return;
        }
        if (liveDetailData.roomFansClubVo.getActiveOrGuardCount() < LIVE_FANS_100) {
            return;
        }
        if (liveDetailData.roomFansClubVo.getActiveOrGuardCount() >= LIVE_FANS_1000) {
            //如果当天展示过，则不展示动画
            if (MmkvCommonUtil.getInstance(getContext())
                    .getString(PreferenceConstantsInLive.LIVE_KEY_AUDIENCE_FANS_POP_1000_TAG + mRoomId)
                    .equals(LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()))) {
                return;
            }
        } else if (liveDetailData.roomFansClubVo.getActiveOrGuardCount() >= LIVE_FANS_100) {
            //如果当天展示过，则不展示动画
            if (MmkvCommonUtil.getInstance(getContext())
                    .getString(PreferenceConstantsInLive.LIVE_KEY_AUDIENCE_FANS_POP_100_TAG + mRoomId)
                    .equals(LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis()))) {
                return;
            }
        }
        liveFansPopNumber = liveDetailData.roomFansClubVo.getActiveOrGuardCount();
        liveFans100PopUrl = liveDetailData.roomFansClubVo.getActiveOrGuardIcon();
        //官方直播间不展示粉丝百人团
        if (!isOfficialLive()) {
            HandlerManager.postOnUIThreadDelay(fansPopRunnable, 5000);
        }
    }

    /**
     * 根据粉丝亲密度信息展示相关内容
     */
    private void checkFansIntimacy() {
        runAfterViewInflate(() -> {
            if (getHostData().roomFansClubVo == null ||
                    getHostData().roomFansClubVo.getAdditionTimes() <= 0) {
                return;
            }
            // 粉丝亲密度 大于0 并且当前观众是粉丝状态，调用亲密度动画
            if (getFansCode() == LiveUserInfo.FansGroupStatusCode.TYPE_JOINED
                    && System.currentTimeMillis() < getHostData().roomFansClubVo.getAdditionEndAt()) {
                switch (liveBizTypeInHeader) {
                    case PERSONAL_LIVE_ROOM_TYPE:
                        mAnchorHeaderView.updateFansIntimacy(getHostData().roomFansClubVo.getAdditionTimes(),
                                getHostData().roomFansClubVo.getAdditionEndAt(), getRoomBizType(), false);
                        break;
                    case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                        if (mVideoOfficialAnchorView != null) {
                            mVideoOfficialAnchorView.updateFansIntimacy(getHostData().roomFansClubVo.getAdditionTimes(),
                                    getHostData().roomFansClubVo.getAdditionEndAt(), getRoomBizType(), false);
                        }
                        break;
                    case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                        mPremiereAnchorHeaderView.updateFansIntimacy(getHostData().roomFansClubVo.getAdditionTimes(),
                                getHostData().roomFansClubVo.getAdditionEndAt(), getRoomBizType(), false);
                        break;
                }

                HandlerManager.postOnUIThreadDelay(fansIntimacyRunnable, 5000);
            }
        });
    }

    /**
     * 延迟5s 判断亲密度 是否要发送系统消息
     */
    private final Runnable fansIntimacyRunnable = () -> {
        //判断是否需要发送im消息
        String key = PreferenceConstantsInLive.LIVE_KEY_FANS_INTIMACY_IM_TAG + getRoomId() +
                LiveDateUtils.convertTimestampToDateString(System.currentTimeMillis());
        boolean needSendImMsg = MmkvCommonUtil.getInstance(getContext()).getBoolean(key, false);
        // 如果当天 该直播间 没有发送过im消息，则发送
        if (!needSendImMsg) {
            MmkvCommonUtil.getInstance(getContext()).saveBoolean(key, true);
            try {
                String additionTimes = LiveFormatUtils.INSTANCE.formatDouble(
                        getHostData().roomFansClubVo.getAdditionTimes()
                );
                String additionEndAt = LiveDateUtils.convertTimestampToDateString2(
                        getHostData().roomFansClubVo.getAdditionEndAt()
                );
                if (getAudienceHeaderInteraction() != null) {
                    getAudienceHeaderInteraction().sendFansIntimacyMessage(
                            "粉丝团亲密度已开启"
                                    + additionTimes
                                    + "倍加成，有效期至"
                                    + additionEndAt
                    );
                }
            } catch (Exception e) {
                Logger.e(TAG, e.getMessage());
            }
        }
    };

    /**
     * 首映室更新
     */
    @Override
    public void updatePremiere() {
        runAfterViewInflate(() -> {
            AudienceHeaderComponent.super.updatePremiere();

            if (!isPremiereUI()) {
                mAnchorHeaderView.updateTagViewHeight();
            }

            if (mTopicTv == null) {
                return;
            }
            ViewExtensionKt.show(mShowTopic);
            liveBizTypeInHeader = PERSONAL_PREMIERE_LIVE_ROOM_TYPE;
            if (PremiereStatus.PREMIERE_NO_START == getPremiereStatus()) {
                ViewStatusUtil.setVisible(View.GONE, mTopicIv);
                ViewStatusUtil.setVisible(VISIBLE, mTopicTv);
                mTopicTv.setText("首映预告");
            } else if (PremiereStatus.PREMIERE_ING == getPremiereStatus() ||
                    PremiereStatus.PREMIERE_END_LIVING == getPremiereStatus() || PremiereStatus.PREMIERE_PRE == getPremiereStatus()) {
                ViewStatusUtil.setVisible(View.GONE, mTopicIv);
                ViewStatusUtil.setVisible(VISIBLE, mTopicTv);
                mTopicTv.setText("首映公告");
            } else {
                liveBizTypeInHeader = PERSONAL_LIVE_ROOM_TYPE;
                topicUiChange();
            }
            updateHeaderHeight();
            initHeightAnim();
            if (getPremiereInfo() == null) {
                return;
            }
            mPremiereAnchorHeaderView.setAnchorOnlineStatus(getPremiereInfo().isAnchorOnline());

            //退出ABTest才会使用的逻辑
            int playSource = 0;
            if (getAudienceHeaderInteraction() != null) {
                playSource = getAudienceHeaderInteraction().getPlaySource();
            }
            boolean supportMini = LiveCommonUtil.isSupportMini(playSource, mAttachFragment.getActivity());
            if (mMiniView != null && supportMini && getLiveStatus() == PersonLiveBase.LIVE_STATUS_ING &&
                    !(getLiveMediaType() == LiveMediaType.TYPE_VIDEO && !LiveSettingManager.canShowMiniViewConfig())) {
                mMiniView.setVisibility(VISIBLE);
                isMiniSupport = true;
            } else if (mMiniView != null) {
                mMiniView.setVisibility(GONE);
                isMiniSupport = false;
            }
        });

    }

    /**
     * 房间信息变化，更新关注状态
     */
    private void updateFollowOrFansClubUI() {
        runAfterViewInflate(() -> {
            if (getHostData() != null && getHostData().roomFansClubVo != null) {
                if (getHostData().roomFansClubVo.getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN) {
                    int joinLiveCount = MmkvCommonUtil.getInstance(mContext).getIntCompat(PreferenceConstantsInLive.LIVE_KEY_JOIN_LIVE_ROOM_COUNT, 0);
                    if (joinLiveCount < LiveCommonConstants.FANS_GUIDE_REMINDER_COUNT) {
                        HandlerManager.postOnUIThreadDelay(rotateRun, FANS_GUIDE_TIME);
                    }
                } else if (getHostData().roomFansClubVo.isJoinFansClub()) {
                    switch (liveBizTypeInHeader) {
                        case PERSONAL_LIVE_ROOM_TYPE:
                            mAnchorHeaderView.setChildFansGrade(String.valueOf(getHostData().roomFansClubVo.getFansGrade()));
                            mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                            break;
                        case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                            if (mVideoOfficialAnchorView != null) {
                                mVideoOfficialAnchorView.setChildFansGrade(String.valueOf(getHostData().roomFansClubVo.getFansGrade()));
                                mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                            }
                            break;
                        case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                            mPremiereAnchorHeaderView.setChildFansGrade(String.valueOf(getHostData().roomFansClubVo.getFansGrade()));
                            premiereFollowViewUpdate(getHostData().isFollowed(), false);
                            break;
                    }
                }
                if (getHostData().roomFansClubVo.getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN) {
                    makeShowTrack(33359);
                }
            }
        });
    }

    /**
     * 根据官播间、无障碍，控制榜单入口按钮的显示隐藏
     * 1、官播间、无障碍模式下，不显示主播榜入口按钮
     */
    private void initRankViewVisibility() {
        runAfterViewInflate(() -> {
            if (isOfficialLive() || LiveAccessibilityUtil.isTalkbackMode()) {
                UIStateUtil.hideViews(mRanksView);
            } else {
                UIStateUtil.showViews(mRanksView);
            }
        });
    }

    /**
     * 根据配置中心来、是否官播间、无障碍，控制更多直播按钮的显示隐藏
     */
    private boolean initTopHeadLineViewVisibility() {
        if (getLiveMediaType() == TYPE_AUDIO) {
            if (!isOfficialLive()) {
                if (LiveAccessibilityUtil.isTalkbackMode()) {
                    UIStateUtil.hideViews(mTopHeadlinesView);
                    return false;
                } else {
                    UIStateUtil.showViews(mTopHeadlinesView);
                    return true;
                }
            } else {
                UIStateUtil.hideViews(mTopHeadlinesView);
                return false;
            }
        } else {
            //如果是视频直播，就通过配置中心来获取是否展示该功能的开关
            boolean isOpenTops = LiveSettingManager.getTopHeadLineViewSwitch();
            if (!isOfficialLive()) {
                if (LiveAccessibilityUtil.isTalkbackMode()) {
                    UIStateUtil.hideViews(mTopHeadlinesView);
                    return false;
                } else {
                    UIStateUtil.setVisibility(mTopHeadlinesView, isOpenTops ? View.VISIBLE : View.GONE);
                    return isOpenTops;
                }
            } else {
                UIStateUtil.hideViews(mTopHeadlinesView);
                return false;
            }
        }
    }

    /**
     * 根据配置中心来、是否官播间、无障碍，控制更多直播按钮的显示隐藏
     */
    private void initMoreLiveViewVisibility() {
        runAfterViewInflate(() -> {
            if (getHostUid() > 0) {
                String blackList = LiveSettingManager.getMoreLiveAnchorUidBlackList();
                String hostUid = getHostUid() + "";
                // 黑名单中，不显示更多直播按钮
                if (blackList.contains(hostUid)) {
                    UIStateUtil.hideViews(mMoreLiveView);
                    return;
                }

                if (!isOfficialLive()) {
                    // 非官播间，无障碍模式下，不显示更多直播按钮
                    if (LiveAccessibilityUtil.isTalkbackMode()) {
                        UIStateUtil.hideViews(mMoreLiveView);
                    } else {
                        UIStateUtil.showViews(mMoreLiveView);
                    }
                } else {
                    // 官播间，不显示更多直播按钮
                    UIStateUtil.hideViews(mMoreLiveView);
                }
            }
        });
    }

    /**
     * 更新音频官播间关注状态
     * 调用时机 ：
     * 1. 进入官播间
     * 2. 官播间节目单H5 Call Native
     *
     * @param follow 关注状态
     */
    public void updateOfficialFollowStatus(boolean follow) {
        runAfterViewInflate(() -> {
            //只有官播场景下才会更新相应UI
            if (!isOfficialLive()) {
                return;
            }
            if (LiveRecordInfoManager.getInstance().getLiveRoomType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO) {
                liveBizTypeInHeader = AUDIO_OFFICIAL_LIVE_ROOM_TYPE;
                ViewStatusUtil.setVisible(follow ? INVISIBLE : VISIBLE, mOfficialFollowTv);
                ViewStatusUtil.setVisible(follow ? VISIBLE : INVISIBLE, mOfficialProgramIv);
            } else {
                mVideoOfficialProgramView.setFollowStatus(follow, false);
            }
        });
    }

    private void updateUi() {
        runAfterViewInflate(() -> {
            if (!canUpdateUi() || null == getHostData() || mAttachFragment == null
                    || mLiveUserInfo == null || mLiveRecordInfo == null) {
                return;
            }

            mAnchorHeaderView.updateTagViewIcon(mLiveRecordInfo.hotScoreIconPath);
            mAnchorHeaderView.bindAvatarToHeader(mLiveUserInfo.avatar, mLiveUserInfo.uid);

            mPremiereAnchorHeaderView.updateTagViewIcon(mLiveRecordInfo.hotScoreIconPath);
            checkAccessibilityAndSetDes();

            if (mLiveRecordInfo.fmId > 0) {
                mLiveRoomFmNumber.setText(String.format(Locale.getDefault(), "FM %d", mLiveRecordInfo.fmId));
                mLiveRoomFmNumber.setContentDescription(String.format(Locale.getDefault(), "FM号 %d", mLiveRecordInfo.fmId));
            }

            boolean isMe = UserInfoMannage.hasLogined() && mLiveUserInfo != null && mLiveUserInfo.uid == UserInfoMannage.getUid();

            updateOnlineAndPlayCount(TextUtils.isEmpty(mLiveRecordInfo.hotScoreIconPath) ? mLiveRecordInfo.playCount : mLiveRecordInfo.hotScore);

            int liveStatus = getLiveStatus();

            if (liveStatus == PersonLiveBase.LIVE_STATUS_NOTICE) {
                UIStateUtil.safelySetText(mTopicTv, "预告");
                ViewExtensionKt.gone(mShowTopic);
            } else {
                UIStateUtil.safelySetText(mTopicTv, "公告");
                ViewExtensionKt.showOrGone(mShowTopic, isOfficialLive() || isPremiere());
            }

            switch (liveStatus) {
                case PersonLiveBase.LIVE_STATUS_ING:
                    setCouponViewVisible(hasCoupons);
                    if (getAudienceHeaderInteraction() != null) {
                        getAudienceHeaderInteraction().dismissTopicAndNoticeDialog();
                    }
                    long roomId = getHostData().getRoomId();
                    if (roomId > 0) {
                        updateConnectedStatus(LiveRoomStatusView.LIVE_PLAYING);
                    } else {
                        updateConnectedStatus(LiveRoomStatusView.LIVE_CONNECTING);
                    }
                    break;
                case PersonLiveBase.LIVE_STATUS_NOTICE:
                    LiveLamiaUtil.sendLocalBroadcastForKtv(mContext, getHostData());
                    mCouponView.setVisibility(View.GONE);
                    if (deleteRoomPreview.contains(getLiveId())) {
                        mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
                        mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
                    } else {
                        mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PRE_ANNOUNCE);
                        mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PRE_ANNOUNCE);
                    }

                    if (isMe) {
                        boolean tipShowed = MmkvCommonUtil.getInstance(mContext).getBooleanCompat(
                                PreferenceConstantsInLive.LIVE_KEY_ANCHOR_JOIN_CHAT_ROOM_NOT_START, false
                        );
                        // 新版直播间允许主播在未开播的状态进入直播间
                        if (!tipShowed) {
                            showNoticeDialogForHost();
                        }
                    }
                    break;
                case PersonLiveBase.LIVE_STATUS_END:
                default:
                    mCouponView.setVisibility(View.GONE);
                    XmPlayerManager xm = XmPlayerManager.getInstance(mContext);
                    if (xm.isPlaying()) {
                        xm.pause(PauseReason.Business.LiveAudienceHeader);
                    }
                    mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
                    mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
                    break;
            }
        });
    }

    @Override
    public void onReceiveRoomEndStatusChange() {
        super.onReceiveRoomEndStatusChange();
        runAfterViewInflate(() -> {
            mCouponView.setVisibility(View.GONE);
            XmPlayerManager xm = XmPlayerManager.getInstance(mContext);
            if (xm.isPlaying()) {
                xm.pause(PauseReason.Business.LiveAudienceHeader);
            }
            mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
            mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
        });
    }

    /**
     * 接收到粉丝团 亲密度 信令
     *
     * @param msg 信令
     */
    @Override
    public void onReceiveUpdateFansIntimacyMsg(CommonChatFansIntimacyMsg msg) {
        if (!canUpdateUi()) {
            return;
        }
        // 如果没有加入粉丝团 不触发亲密度
        runAfterViewInflate(() -> {
                    if (getHostData().roomFansClubVo.getCode() != LiveUserInfo.FansGroupStatusCode.TYPE_JOINED) {
                        return;
                    }
                    switch (liveBizTypeInHeader) {
                        case PERSONAL_LIVE_ROOM_TYPE:
                            mAnchorHeaderView.updateFansIntimacy(msg);
                            break;
                        case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                            if (mVideoOfficialAnchorView != null) {
                                mVideoOfficialAnchorView.updateFansIntimacy(msg);
                            }
                            break;
                        case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                            mPremiereAnchorHeaderView.updateFansIntimacy(msg);
                            break;
                    }
                }
        );

    }

    private void onChatGiftRankChanged(CommonChatRoomFansRankMessage info) {
        //not update when data is empty,
        // and not initAfterLoginRoom views by setting  visibility gone
        runAfterViewInflate(() -> {
            if (!canUpdateUi()) {
                return;
            }
            try {
                IAudienceHeaderGiftRankComponent giftRankComponent = getComponent(HeaderCompConfig.HEADER_GIFT_RANK_COMPONENT);
                if (giftRankComponent != null) {
                    giftRankComponent.updateOnChatGiftRankMsgReceived(info);
                }
            } catch (Exception ignored) {
            }
        });
    }

    private void hideRankAvatarViews() {
        try {
            IAudienceHeaderGiftRankComponent giftRankComponent = getComponent(HeaderCompConfig.HEADER_GIFT_RANK_COMPONENT);
            if (giftRankComponent != null) {
                giftRankComponent.hideRankAvatarViews();
            }
        } catch (Exception ignored) {
        }
    }

    /**
     * 更新参与人数
     *
     * @param playCount playCount
     */
    private void updateOnlineAndPlayCount(long playCount) {
        // 结束后就不更新了
        if (getLiveStatus() == PersonLiveBase.LIVE_STATUS_END) {
            return;
        }
        mAnchorHeaderView.setOnlineCount(playCount);
        mPremiereAnchorHeaderView.setOnlineCount(playCount);
        if (mOfficialHotNumTv != null) {
            mOfficialHotNumTv.setText(StringUtil.getFriendlyNumStr(playCount));
        }
    }

    /**
     * 主播进入自己直播间弹窗
     */
    private void showNoticeDialogForHost() {
        MmkvCommonUtil.getInstance(mContext).saveBoolean(PreferenceConstantsInLive.LIVE_KEY_ANCHOR_JOIN_CHAT_ROOM_NOT_START, true);
        @SuppressLint("InflateParams")
        ViewGroup contentView = (ViewGroup) LayoutInflater.from(mContext).inflate(R.layout.liveaudience_layout_host_join_chat_room_as_audience, null);
        final SimpleDialog simpleDialog = new SimpleDialog(getActivity(), contentView, Gravity.CENTER) {
            @Override
            public void onMyCreate() {
                super.onMyCreate();
                Window dialogWindow = getWindow();
                if (dialogWindow != null) {
                    WindowManager.LayoutParams lp = dialogWindow.getAttributes();
                    lp.width = BaseUtil.dp2px(mContext, 280);
                    lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
                    dialogWindow.setGravity(Gravity.CENTER);
                    dialogWindow.setAttributes(lp);
                    dialogWindow.setWindowAnimations(android.R.style.Theme_Holo_Dialog);
                }
            }

            @Override
            public void onClick(View view) {
                int id = view.getId();
                if (id == R.id.live_ok) {
                    dismiss();
                }
            }
        };
        contentView.findViewById(R.id.live_ok).setOnClickListener(simpleDialog);
        simpleDialog.show();
        AutoTraceHelper.bindData(contentView.findViewById(R.id.live_ok), AutoTraceHelper.MODULE_DEFAULT, "");
    }

    /**
     * 显示粉丝团弹窗
     */
    private void showFansClubDialog() {
        runAfterViewInflate(() -> {
            if (canUpdateUi()) {
                if (mAttachFragment == null || mAnchorLiveData == null
                        || mAnchorLiveData.getDetailInfo() == null
                        || TextUtils.isEmpty(mAnchorLiveData.getDetailInfo().getFansClubHtmlUrl())) {
                    return;
                }
                String fansClubUrl = mAnchorLiveData.getDetailInfo().getFansClubHtmlUrl();
                // 弹起粉丝团面板
                if (getAudienceHeaderInteraction() != null) {
                    getAudienceHeaderInteraction().showFansClubDialogFragment(fansClubUrl);
                }
            }
        });
    }

    @Override
    public void onOrientationChange(int orientation, boolean isSameOrientation) {
        super.onOrientationChange(orientation, isSameOrientation);
        runAfterViewInflate(() -> {
            //横屏需要隐藏一些控件
            if (!canUpdateUi()) {
                return;
            }

            if (!isFull()) {
                showVerticalScreenHeader();
                updateHeaderHeight();
            } else {
                showLandscapeHeader();
                updateHeaderHeight2FirstHeight();
            }
            if (mHeaderRl_All != null) {
                mHeaderRl_All.setVisibility(VISIBLE);
            }
        });
    }


    @Override
    public void onFollowSuccess(boolean success) {
        runAfterViewInflate(() -> {
            if (getHostData() == null) {
                return;
            }
            getHostData().setFollowed(success);
            if (!success) {
                return;
            }
            CustomToast.showSuccessToast("关注成功");
            checkAccessibilityAndSetDes();
            //根据不同类型 启动关注成功动画
            switch (liveBizTypeInHeader) {
                case PERSONAL_LIVE_ROOM_TYPE:
                    mAnchorHeaderView.startAttentionOkAnim(() -> {
                        if (checkIsNeedJoinFansAnim()) {
                            saveJoinFansToLocal();
                            mAnchorHeaderView.setFansPopText(getFansBaseText());
                            mAnchorHeaderView.startJoinPopAnim(null);
                            mAnchorHeaderView.setChildFansStatus(true, getFansCode(), true, getFansActiveStatus());
                        } else {
                            if (!notOpenFansClubOrIsAnchorFans()) {
                                //不是粉丝团成员
                                mAnchorHeaderView.startRedHeartAnim();
                            }
                            mAnchorHeaderView.setChildFansStatus(true, getFansCode(), false, getFansActiveStatus());
                        }
                    });
                    break;
                case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.startAttentionOkAnim(() -> {
                            if (mVideoOfficialAnchorView != null) {
                                if (checkIsNeedJoinFansAnim()) {
                                    saveJoinFansToLocal();
                                    mVideoOfficialAnchorView.startJoinPopAnim(() -> mVideoOfficialAnchorView.setChildFansStatus(true, getFansCode(), true, getFansActiveStatus()));
                                } else {
                                    if (!notOpenFansClubOrIsAnchorFans()) {
                                        //不是粉丝团成员
                                        mVideoOfficialAnchorView.startRedHeartAnim();
                                    }
                                    mVideoOfficialAnchorView.setChildFansStatus(true, getFansCode(), false, getFansActiveStatus());
                                }
                            }
                        });
                    }
                    break;
                case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                    mPremiereAnchorHeaderView.startAttentionOkAnim(() -> {
                        if (getHostData() != null) {
                            premiereFollowViewUpdate(getHostData().isFollowed(), false);
                        }
                    });
                    break;
            }
        });
    }

    @Override
    public void updateFollowedStatus() {
        runAfterViewInflate(() -> {
            if (!mAttachFragment.canUpdateUi() || getHostData() == null) {
                return;
            }

            if (!UserInfoMannage.hasLogined()) {
                //没登录 显示按钮，点击去登录
                switch (liveBizTypeInHeader) {
                    case PERSONAL_LIVE_ROOM_TYPE:
                        mAnchorHeaderView.noLoginUi();
                        break;
                    case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                        if (mVideoOfficialAnchorView != null) {
                            mVideoOfficialAnchorView.noLoginUi();
                        }
                        break;
                    case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                        mPremiereAnchorHeaderView.noLoginUi();
                        break;
                }
                return;
            }
            if (mLiveUserInfo != null) {
                if (UserInfoMannage.hasLogined() && UserInfoMannage.getInstance().getUser() != null
                        && UserInfoMannage.getInstance().getUser().getUid() == mLiveUserInfo.uid) {
                    //是我自己，不展示ui
                    switch (liveBizTypeInHeader) {
                        case PERSONAL_LIVE_ROOM_TYPE:
                            mAnchorHeaderView.setFollowVisibility(GONE);
                            break;
                        case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                            if (mVideoOfficialAnchorView != null) {
                                mVideoOfficialAnchorView.setFollowVisibility(GONE);
                            }
                            break;
                        case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                            mPremiereAnchorHeaderView.setFollowVisibility(GONE);
                            break;
                    }
                    return;
                }
                mAnchorHeaderView.setMIsFollow(getHostData().isFollowed());
                checkAccessibilityAndSetDes();
                // 未关注主播，显示关注按钮；已关注主播，隐藏关注按钮
                mAnchorHeaderView.setFollowVisibility(VISIBLE);
                mPremiereAnchorHeaderView.setFollowVisibility(VISIBLE);
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.setFollowVisibility(VISIBLE);
                }

                if (checkIsNeedJoinFansAnim()) {
                    switch (liveBizTypeInHeader) {
                        case PERSONAL_LIVE_ROOM_TYPE:
                            mAnchorHeaderView.setFansPopText(getFansBaseText());
                            mAnchorHeaderView.startJoinPopAnimDelay(3000, this::saveJoinFansToLocal);
                            mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), true, getFansActiveStatus());
                            break;
                        case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                            if (mVideoOfficialAnchorView != null) {
                                mVideoOfficialAnchorView.startJoinPopAnimDelay(3000, this::saveJoinFansToLocal);
                                mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(),
                                        getFansCode(), true, getFansActiveStatus());
                            }
                            break;
                        case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                            saveJoinFansToLocal();
                            premiereFollowViewUpdate(getHostData().isFollowed(), true);
                            break;
                    }
                } else {
                    mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(),
                                getFansCode(), false, getFansActiveStatus());
                    }
                    premiereFollowViewUpdate(getHostData().isFollowed(), false);
                }
                //如果是刚刚关注并且在引导动画中，又立刻取消了关注则把所有动画都关掉，并且重置成当前状态
                if (!(getHostData() != null && getHostData().isFollowed())) {
                    switch (liveBizTypeInHeader) {
                        case PERSONAL_LIVE_ROOM_TYPE:
                            break;
                        case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                            if (mVideoOfficialAnchorView != null) {
                                mVideoOfficialAnchorView.enforceResetStatusNoAnim(getHostData().isFollowed(), getFansCode(), getFansActiveStatus());
                            }
                            break;
                        case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                            if (mPremiereAnchorHeaderView != null) {
                                mPremiereAnchorHeaderView.enforceResetStatusNoAnim(getHostData().isFollowed(), getFansCode(), getFansActiveStatus());
                            }
                            break;
                    }
                }
            } else {
                mAnchorHeaderView.setFollowVisibility(View.GONE);
                mPremiereAnchorHeaderView.setFollowVisibility(View.GONE);
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.setFollowVisibility(GONE);
                }
            }
        });
    }

    /**
     * 判断是否需要展示粉丝团动画
     */
    private boolean checkIsNeedJoinFansAnim() {
        if (isFollowAndNotFans()) {
            //关注了，但不是粉丝团成员，需要动画提醒加入粉丝团
            if (mAnchorLiveData != null && mAnchorLiveData.anchorUid > 0) {
                String json = MmkvCommonUtil.getInstance(mContext).getStringCompat(PreferenceConstantsInLive.LIVE_KEY_SHOW_FANS_JOIN);
                HashMap<String, String> lastMap = LiveGsonUtils.parseMap(json);
                if (lastMap == null) {
                    lastMap = new HashMap<>();
                }
                String lastTime = lastMap.get(String.valueOf(mAnchorLiveData.anchorUid));
                long lastTimeLong = 0L;
                try {
                    if (null != lastTime) {
                        lastTimeLong = Long.parseLong(lastTime);
                    }
                } catch (Exception e) {
                    Logger.e(TAG, e.getMessage());
                }
                if (!LiveDateUtils.isSameDay(lastTimeLong, System.currentTimeMillis())) {
                    //不是同一天 需要显示动画
                    Logger.i(TAG, "checkIsNeedJoinFansAnim    今天还未展示  需要展示动画");
                    return true;
                } else {
                    Logger.i(TAG, "checkIsNeedJoinFansAnim    今天已经展示  不需要再展示动画");
                }
            } else {
                Logger.i(TAG, "checkIsNeedJoinFansAnim   anchorUid不合法   不展示动画");
            }
        } else {
            Logger.i(TAG, "checkIsNeedJoinFansAnim   没有关注/没有开通粉丝团   不需要展示动画");
        }
        return false;
    }

    private boolean isFollowAndNotFans() {
        return getHostData() != null && getHostData().isFollowed() && !notOpenFansClubOrIsAnchorFans();
    }

    /**
     * 本地存储 加入粉丝团标识
     */
    private void saveJoinFansToLocal() {
        if (mAnchorLiveData != null && mAnchorLiveData.anchorUid > 0) {
            String json = MmkvCommonUtil.getInstance(mContext).getStringCompat(PreferenceConstantsInLive.LIVE_KEY_SHOW_FANS_JOIN);
            Map<String, String> lastMaps = LiveGsonUtils.parseMap(json);
            if (lastMaps == null) {
                lastMaps = new HashMap<>();
            }
            lastMaps.put(String.valueOf(mAnchorLiveData.anchorUid), String.valueOf(System.currentTimeMillis()));
            MmkvCommonUtil.getInstance(getContext()).saveString(PreferenceConstantsInLive.LIVE_KEY_SHOW_FANS_JOIN, LiveGsonUtils.toJson(lastMaps));
        }
    }

    /**
     * 1、主播没有开通粉丝团，true
     * 2、加入当前主播的粉丝团成员，true
     */
    private boolean notOpenFansClubOrIsAnchorFans() {
        if (getHostData() == null) {
            return false;
        }
        if (getHostData().roomFansClubVo == null) {
            return false;
        }

        int code = getHostData().roomFansClubVo.getCode();

        if (code == LiveUserInfo.FansGroupStatusCode.TYPE_UNOPEN) {
            return true;
        }

        if (code == LiveUserInfo.FansGroupStatusCode.TYPE_JOINED) {
            Logger.i(TAG, "isAnchorFans：TYPE_JOINED    是粉丝团成员");
            return true;
        } else if (code == LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN) {
            Logger.i(TAG, "isAnchorFans：TYPE_UNJOIN    不是粉丝团成员");
            return false;
        } else {
            Logger.i(TAG, "isAnchorFans：TYPE_UNOPEN    没开通粉丝团");
            return false;
        }
    }

    private int getFansCode() {
        if (getHostData() == null) {
            return LiveUserInfo.FansGroupStatusCode.TYPE_UNOPEN;
        }
        if (getHostData().roomFansClubVo == null) {
            return LiveUserInfo.FansGroupStatusCode.TYPE_UNOPEN;
        }
        return getHostData().roomFansClubVo.getCode();
    }

    /**
     * 获取粉丝团活跃状态
     *
     * @return 粉丝团活跃状态
     */
    private boolean getFansActiveStatus() {
        if (getHostData() == null || getHostData().roomFansClubVo == null) {
            return true;
        }
        return getHostData().roomFansClubVo.isActive();
    }

    /**
     * 粉团伸缩动画
     * 一分钟轮训任务
     */
    private final Runnable mFansJoinTask = new Runnable() {
        @Override
        public void run() {
            runAfterViewInflate(() -> {
                if (!canUpdateUi()) {
                    return;
                }
                if (!isFollowAndNotFans()) {
                    Logger.d("fansJoin", "isFollowAndNotFans() = false");
                    return;
                }
                Logger.d("fansJoin", "startJoinPopAnim");
                switch (liveBizTypeInHeader) {
                    case PERSONAL_LIVE_ROOM_TYPE:
                        mAnchorHeaderView.setFansPopText(getFansBaseText());
                        mAnchorHeaderView.startJoinPopAnim(() -> {
                            saveJoinFansToLocal();
                            HandlerManager.removeCallbacks(mFansJoinTask);
                            HandlerManager.postOnUIThreadDelay(mFansJoinTask, TIME_FANS_JOIN_ANIM_DURATION);
                        });
                        break;
                    case VIDEO_OFFICIAL_LIVE_ROOM_TYPE:
                        if (mVideoOfficialAnchorView != null) {
                            mVideoOfficialAnchorView.startJoinPopAnim(() -> {
                                saveJoinFansToLocal();
                                HandlerManager.removeCallbacks(mFansJoinTask);
                                HandlerManager.postOnUIThreadDelay(mFansJoinTask, TIME_FANS_JOIN_ANIM_DURATION);
                            });
                        }
                        break;
                    case PERSONAL_PREMIERE_LIVE_ROOM_TYPE:
                        saveJoinFansToLocal();
                        break;
                }
            });
        }
    };

    @Override
    public void onFansRankChanged(CommonChatRoomFansRankMessage message) {
        onChatGiftRankChanged(message);
    }

    @Override
    public void onOnlineStatusChange(CommonChatRoomOnlineUserListMsg message) {
        runAfterViewInflate(() -> {
            Logger.i(TAG, "onOnlineStatusChange " + message);
            if (message == null) return;

            if (null != mLiveRecordInfo && canUpdateUi()) {
                mAnchorHeaderView.setOnlineCount(
                        TextUtils.isEmpty(mLiveRecordInfo.hotScoreIconPath) ? message.playCnt : message.hotScore);
                mAnchorHeaderView.onOnlineStatusChange(message.popularityCount);
            }

            if (null != mPremiereAnchorHeaderView && null != mLiveRecordInfo && canUpdateUi()) {
                mPremiereAnchorHeaderView.setOnlineCount(
                        TextUtils.isEmpty(mLiveRecordInfo.hotScoreIconPath) ? message.playCnt : message.hotScore);
                mPremiereAnchorHeaderView.onOnlineStatusChange(message.popularityCount);
            }

            if (mOfficialHotNumTv != null && mLiveRecordInfo != null && canUpdateUi()) {
                int num = TextUtils.isEmpty(mLiveRecordInfo.hotScoreIconPath) ? message.playCnt : message.hotScore;
                mOfficialHotNumTv.setText(StringUtil.getFriendlyNumStr(num));
            }
            try {
                IAudienceHeaderGiftRankComponent giftRankComponent = getComponent(HeaderCompConfig.HEADER_GIFT_RANK_COMPONENT);
                if (null != giftRankComponent) {
                    giftRankComponent.updateOnlineUserCountPushMsg(message.onlineCount);
                }
            } catch (Exception e) {
                Logger.e(TAG, e.getMessage());
            }
        });
    }

    @Override
    public void showUserInfoPop(long targetUid) {
        if (getAudienceHeaderInteraction() != null) {
            getAudienceHeaderInteraction().showUserInfoPop(targetUid);
        }
    }

    @Override
    public void onReceiveFansGroupMsg(CommonFansGroupMsg fansGroupMsg) {
        runAfterViewInflate(() -> {
            if (fansGroupMsg == null || getHostData() == null || !canUpdateUi()) {
                return;
            }
            switch (fansGroupMsg.type) {
                case CommonFansGroupMsg.FansMsgType.TYPE_QUIT:  //退出粉丝团
                    if (getHostData().roomFansClubVo != null) {
                        getHostData().roomFansClubVo.setCode(LiveUserInfo.FansGroupStatusCode.TYPE_UNJOIN);
                        getHostData().roomFansClubVo.setActive(false);
                    }
                    mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    }
                    if (mPremiereAnchorHeaderView != null) {
                        mPremiereAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                        mPremiereAnchorHeaderView.setBackgroundResource(R.drawable.live_dark_radiu20_bg);
                    }
                    break;
                case CommonFansGroupMsg.FansMsgType.TYPE_OPEN:
                    if (getHostData().roomFansClubVo != null) {
                        getHostData().roomFansClubVo.setCode(LiveUserInfo.FansGroupStatusCode.TYPE_JOINED);
                        getHostData().roomFansClubVo.setActive(true);
                    }
                    // 开通粉丝团 发送主视区动画消息
                    LiveEventCenter.postMainAreaAnimEvent(MainAreaAnimConfig.gift());
                    mAnchorHeaderView.openFansUpgradePop(R.string.livecomm_congratulations_on_opening_fan_club, () -> {
                        if (!canUpdateUi() || getHostData() == null) {
                            return;
                        }
                        mAnchorHeaderView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                        mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    });
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.openFansUpgradePop(R.string.livecomm_congratulations_on_opening_fan_club, () -> {
                            if (!canUpdateUi() || getHostData() == null) {
                                return;
                            }
                            mVideoOfficialAnchorView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                            mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                        });
                    }
                    if (mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == View.VISIBLE) {
                        mPremiereAnchorHeaderView.openFansUpgradePop(R.string.livecomm_congratulations_on_opening_fan_club, () -> {
                            if (!canUpdateUi() || getHostData() == null || mPremiereAnchorHeaderView == null) {
                                return;
                            }
                            mPremiereAnchorHeaderView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                            premiereFollowViewUpdate(getHostData().isFollowed(), false);
                        });

                    }

                    break;
                case CommonFansGroupMsg.FansMsgType.TYPE_UPGRADE:
                    if (getHostData().roomFansClubVo != null) {
                        getHostData().roomFansClubVo.setCode(LiveUserInfo.FansGroupStatusCode.TYPE_JOINED);
                    }
                    if (liveBizTypeInHeader == VIDEO_OFFICIAL_LIVE_ROOM_TYPE) {
                        if (mVideoOfficialAnchorView != null) {
                            mVideoOfficialAnchorView.openFansUpgradePop(R.string.livecomm_congratulations_on_your_fan_club_upgrade, () -> {
                                if (!canUpdateUi() || getHostData() == null) {
                                    return;
                                }
                                mVideoOfficialAnchorView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                                mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                            });
                        }
                    } else if (liveBizTypeInHeader == PERSONAL_PREMIERE_LIVE_ROOM_TYPE) {
                        if (mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == View.VISIBLE) {
                            mPremiereAnchorHeaderView.openFansUpgradePop(
                                    R.string.livecomm_congratulations_on_your_fan_club_upgrade,
                                    () -> {
                                        if (!canUpdateUi() || getHostData() == null || mPremiereAnchorHeaderView == null) {
                                            return;
                                        }
                                        mPremiereAnchorHeaderView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                                        premiereFollowViewUpdate(getHostData().isFollowed(), false);
                                    }
                            );
                        }
                    } else {
                        // 升级动画
                        mAnchorHeaderView.openFansUpgradePop(
                                R.string.livecomm_congratulations_on_your_fan_club_upgrade,
                                () -> {
                                    if (!canUpdateUi() || getHostData() == null) {
                                        return;
                                    }
                                    mAnchorHeaderView.setChildFansGrade(String.valueOf(fansGroupMsg.grade));
                                    mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                                }
                        );
                    }
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public void updateFansClubActiveStatus(LiveFansClubStatusModel model) {
        runAfterViewInflate(() -> {
            if (model == null || getHostData() == null) {
                return;
            }
            if (model.getType() == 1) { //粉丝状态变为活跃状态
                if (getHostData().roomFansClubVo != null) {
                    getHostData().roomFansClubVo.setActive(true);
                }
                mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                }
                if (mPremiereAnchorHeaderView != null) {
                    mPremiereAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, true);
                    mPremiereAnchorHeaderView.setBackgroundResource(R.drawable.live_dark_radiu20_bg);
                }
            }
        });
    }


    @Override
    public void receiveTopHeadlinesMsg(HeadAnchorInfo msg) {
        runAfterViewInflate(() -> {
            if (!canUpdateUi()) {
                return;
            }
            mCommonChatRoomTopHeadlinesMsg = msg;
            boolean isShow = false;
            PersonLiveDetail hostData = getHostData();
            if (hostData != null && hostData.getHeadAnchor() != null) {
                isShow = Boolean.TRUE.equals(hostData.getHeadAnchor().getShowHeadAnchor());
            }
            if (initTopHeadLineViewVisibility()) {
                Logger.i("上头条测试", "receiveTopHeadlinesMsg-有数据就展示");
                mTopHeadlinesView.loadData(true, msg, isShow);
            }
        });
    }

    @Override
    public void onRoomSpecialModeChanged() {
        setShowBirthdayView();
        setShowRewardAvatarDecorate();
    }

    @Override
    public void updateRewardAvatarDecorate() {
        setShowRewardAvatarDecorate();
    }

    @Override
    public void onUserInputStatusChange(boolean isInput) {
        if (isInput == isUserInInput()) {
            //这里会重复2次true
            return;
        }
        super.onUserInputStatusChange(isInput);
        // 当发言框弹起时，头部第一部分和第二部分隐藏，飘屏组件显示
        // 当发言框收起时，头部第一部分和第二部分显示，飘屏组件显示

        if (!isFunctionSwitchOpen()) {
            return;
        }
        runAfterViewInflate(() -> {
            if (mHeaderRl_All != null) {
                if (mHeightAnim.isStarted() || mHeightAnim.isRunning()) {
                    mHeightAnim.cancel();
                    mHeightAnim.setIntValues(mHeaderRl_All.getTop(), isInput ? mHeaderRl_AllHideMarginTop : 0);
                } else {
                    mHeightAnim.setIntValues(isInput ? 0 : mHeaderRl_AllHideMarginTop, isInput ? mHeaderRl_AllHideMarginTop : 0);
                }
                mHeightAnim.start();
            }
            ViewStatusUtil.setVisible(isInput ? View.GONE : View.VISIBLE, mIvClose);
        });
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mFollowClass = new FollowClass(this);
        AnchorFollowManage.getSingleton().addFollowListener(mFollowClass);
        LiveLocalBroadcastManager.register(LiveHeadlinesView.SCALE_LOCAL_BROADCAST, headerComponentBroadCastReceiver);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mAnchorLiveData = null;
        if (mTopHeadlinesView != null) {
            mTopHeadlinesView.setOnClickListener(null);
        }
        if (null != mFollowClass) {
            AnchorFollowManage.getSingleton().removeFollowListener(mFollowClass);
        }
        if (null != mAnchorHeaderView) {
            mAnchorHeaderView.stopFollowAttentionEndAction();
        }
        if (mVideoOfficialAnchorView != null) {
            mVideoOfficialAnchorView.stopFollowAttentionEndAction();
        }
        if (null != mMoreLiveView) {
            mMoreLiveView.setEventListener(null);
        }
        //只有竖屏能够切换房间，横屏场景不处理
        if (mPremiereAnchorHeaderView != null) {
            mPremiereAnchorHeaderView.noLoginUi();
        }
        HandlerManager.removeCallbacks(fansIntimacyRunnable);
        HandlerManager.removeCallbacks(rotateRun);
        HandlerManager.removeCallbacks(topicTask);
        HandlerManager.removeCallbacks(fansPopRunnable);
        HandlerManager.removeCallbacks(mFansJoinTask);

        LiveLocalBroadcastManager.unregister(headerComponentBroadCastReceiver);
        if (null != mShowHeaderViews) {
            mShowHeaderViews.reset();
        }
        if (mHeightAnim != null) {
            mHeightAnim.removeAllUpdateListeners();
        }
    }

    @Override
    public void updateConnectedStatus(@LiveRoomStatusView.LiveStatus int status) {
        runAfterViewInflate(() -> {
            if (getHostData() == null || !canUpdateUi()) {
                return;
            }

            // 直播场次状态
            switch (getLiveStatus()) {
                case PersonLiveBase.LIVE_STATUS_NOTICE:
                    mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PRE_ANNOUNCE);
                    if (mPremiereAnchorHeaderView != null) {
                        mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PRE_ANNOUNCE);
                    }
                    break;
                case PersonLiveBase.LIVE_STATUS_ING:
                    mAnchorHeaderView.setLiveStatus(status);
                    if (mPremiereAnchorHeaderView != null) {
                        mPremiereAnchorHeaderView.setLiveStatus(status);
                    }
                    break;
                case PersonLiveBase.LIVE_STATUS_END:
                default:
                    mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
                    if (mPremiereAnchorHeaderView != null) {
                        mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
                    }
                    break;
            }
            Logger.i(TAG, "updateConnectedStatus, playStatus = " + status
                    + ", recordStatus = " + getLiveStatus());
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        Logger.i("测试生命周期:", "onResume");
        if (getHostData() != null && !isFirstResume) {
            trackRoom();
        }
        isFirstResume = false;
    }

    @Override
    public void onPause() {
        super.onPause();
        Logger.i("测试生命周期:", "onPause");
        trackLeaveRoom();
    }

    @Override
    public void onReceiveAnchorOnlineMsg(LivePremiereMsg message) {
        runAfterViewInflate(() -> {
            if (mPremiereAnchorHeaderView != null) {
                mPremiereAnchorHeaderView.setAnchorOnlineStatus(message.getStatus() == PremiereMsgOnlineStatus.PREMIERE_ANCHOR_ONLINE);
            }
        });
    }

    @Override
    public void setSubtitleHeartCountStatus(boolean pauseOnHeartCount) {
        runAfterViewInflate(() -> {
            mAnchorHeaderView.updateScrollTagStatus(pauseOnHeartCount);
            if (mPremiereAnchorHeaderView != null) {
                mPremiereAnchorHeaderView.updateScrollTagStatus(pauseOnHeartCount);
            }
        });
    }

    @Override
    public void onRoomPreviewDelete(RoomSubscribeItem item) {
        deleteRoomPreview.add(getLiveId());
        if (mAnchorHeaderView != null) {
            mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
        }
        if (mPremiereAnchorHeaderView != null) {
            mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_END);
        }
    }

    @Override
    public void loadRelationshipAnimation() {
        runAfterViewInflate(() -> {
            UserRelationshipReqUnit reqUnit = new UserRelationshipReqUnit(
                    UserInfoMannage.getUid(), getHostUid()
            );
            List<UserRelationshipReqUnit> relationParams = new ArrayList<>();
            relationParams.add(reqUnit);
            UserRelationshipAnimationReq reqParams = new UserRelationshipAnimationReq(
                    getRoomBizType(), getRoomId(), relationParams
            );
            CommonRequestForCommon.getUsersRelationAnimation(
                    reqParams,
                    new IDataCallBack<UserRelationshipAnimationRsp>() {
                        @Override
                        public void onSuccess(@Nullable UserRelationshipAnimationRsp data) {
                            if (data == null) {
                                Logger.i("RelationshipAnimation", "reqEmpty()");
                                return;
                            }

                            List<UserRelationshipRspUnit> rspList = data.getUserRelations();
                            int loopCount = data.getPerPlayTimes();
                            if (!rspList.isEmpty() && rspList.get(0).match(reqUnit)) {
                                UserRelationshipRspUnit rsp = rspList.get(0);
                                showRelationshipAnimation(rsp.getTemplateId(), loopCount);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            Logger.i("RelationshipAnimation", "reqError(" + code + "): " + message);
                        }
                    }
            );
        });
    }

    /**
     * 展示竖屏幕下的Header
     */
    private void showVerticalScreenHeader() {
        runAfterViewInflate(() -> {
            if (mHeadBackView != null) {
                mHeadBackView.setVisibility(View.GONE);
            }
            if (mHeaderFirstLine != null) {
                mHeaderFirstLine.setVisibility(VISIBLE);
            }
            ViewStatusUtil.setVisible(View.GONE, mLiveBarrageSwitchView);
            setHeaderViewVisibility(VISIBLE);
            setShowBirthdayView();
            setShowRewardAvatarDecorate();
            int playSource = 0;
            if (getAudienceHeaderInteraction() != null) {
                playSource = getAudienceHeaderInteraction().getPlaySource();
            }
            boolean supportMini = LiveCommonUtil.isSupportMini(playSource, mAttachFragment.getActivity());
            if (mMiniView != null && supportMini && getLiveStatus() == PersonLiveBase.LIVE_STATUS_ING &&
                    !(getLiveMediaType() == LiveMediaType.TYPE_VIDEO && !LiveSettingManager.canShowMiniViewConfig())) {
                mMiniView.setVisibility(VISIBLE);
                isMiniSupport = true;
            } else if (mMiniView != null) {
                mMiniView.setVisibility(GONE);
                isMiniSupport = false;
            }
            if (liveBizTypeInHeader == VIDEO_OFFICIAL_LIVE_ROOM_TYPE && mVideoOfficialHeaderParentLayout != null) {
                // 旋转为竖屏，需要将第一行内的头部信息 移除，第二行的添加头部信息
                if (mVideoOfficialAnchorView.getParent() == mVideoOfficialHeaderParentLayout) {
                    if (mVideoOfficialHeaderParentLayout != null) {
                        ConstraintSet constraintSet = new ConstraintSet();
                        constraintSet.clone(mVideoOfficialHeaderParentLayout);
                        constraintSet.clear(mVideoOfficialAnchorView.getId(), ConstraintSet.LEFT);
                        constraintSet.clear(mVideoOfficialAnchorView.getId(), ConstraintSet.TOP);
                        constraintSet.applyTo(mVideoOfficialHeaderParentLayout);
                        mVideoOfficialHeaderParentLayout.removeView(mVideoOfficialAnchorView);
                    }
                    mHeaderSecondLine.addView(mVideoOfficialAnchorView, 0);
                }

            }
        });
    }

    /**
     * 设置HeaderView中子View是否可见
     *
     * @param visibility true 显示 false 隐藏
     */
    private void setHeaderViewVisibility(int visibility) {
        runAfterViewInflate(() -> {
            ViewStatusUtil.setVisible(
                    visibility,
                    mIvClose,
                    mHeaderSecondLine,
                    mLiveRoomFmNumber, getGiftRankView());
            if (liveBizTypeInHeader == VIDEO_OFFICIAL_LIVE_ROOM_TYPE) {
                ViewStatusUtil.setVisible(visibility, mVideoOfficialAnchorView);
            }

        });
    }

    /**
     * 展示横屏幕下的Header
     */
    private void showLandscapeHeader() {
        runAfterViewInflate(() -> {
            if (mHeadBackView != null) {
                mHeadBackView.setVisibility(VISIBLE);
            }
            ViewStatusUtil.setVisible(VISIBLE, mHeaderFirstLine, mLiveBarrageSwitchView);
            setHeaderViewVisibility(View.GONE);
            ViewStatusUtil.setVisible(INVISIBLE, mHeaderHostAvatarStokeIv, mHeaderHostRewardAvatarDecorateIv, mMiniView);

            // 旋转为横屏，需要添加第一行内的头部信息 ，移除第二行的添加头部信息
            if (liveBizTypeInHeader == VIDEO_OFFICIAL_LIVE_ROOM_TYPE) {
                if (mVideoOfficialAnchorView.getParent() == mHeaderSecondLine) {
                    if (mHeaderSecondLine != null) {
                        mHeaderSecondLine.removeView(mVideoOfficialAnchorView);
                    }
                    mVideoOfficialAnchorView.setVisibility(VISIBLE);

                    mVideoOfficialHeaderParentLayout.addView(mVideoOfficialAnchorView);
                    // 设置布局参数
                    ConstraintSet constraintSet = new ConstraintSet();
                    constraintSet.clone(mVideoOfficialHeaderParentLayout);
                    constraintSet.connect(
                            mVideoOfficialAnchorView.getId(),
                            ConstraintSet.LEFT,
                            mVideoOfficialProgramView.getId(),
                            ConstraintSet.RIGHT,
                            BaseUtil.dp2px(getContext(), 12)
                    );
                    constraintSet.connect(
                            mVideoOfficialAnchorView.getId(),
                            ConstraintSet.TOP,
                            ConstraintSet.PARENT_ID,
                            ConstraintSet.TOP,
                            BaseUtil.dp2px(getContext(), 4)
                    );
                    constraintSet.applyTo(mVideoOfficialHeaderParentLayout);
                }
            }
        });
    }

    @Override
    public void hideHeaderView() {
        runAfterViewInflate(() -> ViewStatusUtil.setVisible(GONE, mHeaderRl_All));
    }

    @Override
    public void showHeaderView() {
        runAfterViewInflate(() -> ViewStatusUtil.setVisible(VISIBLE, mHeaderRl_All));
    }

    private void updateHeaderHeight() {
        runAfterViewInflate(() -> {
            if (mHeaderVg_1_2 != null) {
                mHeaderVg_1_2.measure(0, 0);
                int height = mHeaderVg_1_2.getMeasuredHeight();
                ViewGroup.LayoutParams layoutParams = mHeaderVg_1_2.getLayoutParams();
                layoutParams.height = height;
                mHeaderVg_1_2.setLayoutParams(layoutParams);
            }
        });
    }

    private void updateHeaderHeight2FirstHeight() {
        runAfterViewInflate(() -> {
            if (mHeaderVg_1_2 != null && mHeaderFirstLine != null) {
                mHeaderFirstLine.measure(0, 0);
                int height = mHeaderFirstLine.getMeasuredHeight();
                ViewGroup.LayoutParams layoutParams = mHeaderVg_1_2.getLayoutParams();
                layoutParams.height = height;
                mHeaderVg_1_2.setLayoutParams(layoutParams);
            }
        });
    }

    @Override
    public boolean isRoomMiniSupport() {
        return isMiniSupport;
    }

    @Override
    public void onFansClubInfoChange(LiveUserInfo.FansClubVoBean fansClubVoBean) {
        initFollowStatus();
        updateFollowOrFansClubUI();
    }

    @Override
    public void onGuardInfoChange(LiveUserInfo.GuardGroupVo guardVoBean) {
        updateGuardInfo(guardVoBean);
    }

    /**
     * 更新守护状态
     */
    private void updateGuardInfo(LiveUserInfo.GuardGroupVo guardVoBean) {
        runAfterViewInflate(() -> mAnchorHeaderView.setGuardInfo(guardVoBean.getGuardIconUrl()));
    }

    /**
     * 初始化关注状态
     */
    protected void initFollowStatus() {
        runAfterViewInflate(() -> {
            Logger.i("粉丝团问题", "initFollowStatus");
            TIME_FANS_JOIN_ANIM_DURATION = LiveSettingManager.getFansJoinShowDuration() * TIME_SECOND;
            if (!mAttachFragment.canUpdateUi()) {
                return;
            }

            if (!UserInfoMannage.hasLogined()) {
                //没登录 显示按钮，点击去登录
                mAnchorHeaderView.noLoginUi();
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.noLoginUi();
                }
                if (mPremiereAnchorHeaderView != null) {
                    mPremiereAnchorHeaderView.noLoginUi();
                }
                return;
            }
            if (getHostData() != null) {
                if (UserInfoMannage.hasLogined() && UserInfoMannage.getInstance().getUser() != null
                        && UserInfoMannage.getInstance().getUser().getUid() == getHostUid()) {
                    //是我自己，不展示ui
                    mAnchorHeaderView.setFollowVisibility(View.GONE);
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.setFollowVisibility(View.GONE);
                    }
                    if (mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == View.VISIBLE) {
                        mPremiereAnchorHeaderView.setFollowVisibility(GONE);
                    }
                    return;
                }
                String textStr = getFansBaseText();

                // 未关注主播，显示关注按钮；已关注主播，隐藏关注按钮
                mAnchorHeaderView.setFollowVisibility(VISIBLE);
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.getMAnchorFollowView().getMFansPopTv().setText(textStr);
                    mVideoOfficialAnchorView.setFollowVisibility(VISIBLE);
                }

                checkFansIntimacy();

                if (isFollowAndNotFans()) {
                    //关注且没有加入粉丝团，收听5s后展示引导加入粉丝团动画
                    HandlerManager.removeCallbacks(mFansJoinTask);
                    HandlerManager.postOnUIThreadDelay(mFansJoinTask, TIME_FANS_JOIN_START_ANIM);
                    Logger.i("粉丝团问题", "initFollowStatus1" + "---" + getHostData().isFollowed() + "--" + getFansCode() + "--true--" + getFansActiveStatus());
                    mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), true, getFansActiveStatus());
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), true, getFansActiveStatus());
                    }
                    premiereFollowViewUpdate(getHostData().isFollowed(), true);
                } else {
                    Logger.i("粉丝团问题", "initFollowStatus2" + "---" + getHostData().isFollowed() + "--" + getFansCode() + "--false--" + getFansActiveStatus());
                    mAnchorHeaderView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    if (mVideoOfficialAnchorView != null) {
                        mVideoOfficialAnchorView.setChildFansStatus(getHostData().isFollowed(), getFansCode(), false, getFansActiveStatus());
                    }
                    premiereFollowViewUpdate(getHostData().isFollowed(), false);
                }
            } else {
                mAnchorHeaderView.setFollowVisibility(GONE);
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.setFollowVisibility(GONE);
                }
                if (mPremiereAnchorHeaderView != null) {
                    mPremiereAnchorHeaderView.setFollowVisibility(GONE);
                }
            }
        });
    }

    /**
     * 获取基础粉丝团文案
     *
     * @return 基础粉丝团文案
     */
    @NonNull
    private String getFansBaseText() {
        String textStr = getHostData().roomFansClubVo != null && getHostData().roomFansClubVo.isOnSale()
                ? "粉团" + getHostData().roomFansClubVo.getDiscountNum() + "折特惠" : mAnchorHeaderView.getFansPopText();
        mAnchorHeaderView.setFansPopText(textStr);
        return textStr;
    }

    @Override
    public void showOrHideHeaderViews(boolean show) {
        runAfterViewInflate(() -> {
            Logger.i("官播间测试：", "showOrHideHeaderViews");
            if (mFirstGroup != null) {
                mFirstGroup.setVisibility(show ? VISIBLE : INVISIBLE);
            }
            if (mHeaderSecondLine != null) {
                mHeaderSecondLine.setVisibility(show ? VISIBLE : INVISIBLE);
            }
            if (mHeaderThirdLine != null) {
                mHeaderThirdLine.setVisibility(show ? VISIBLE : INVISIBLE);
            }
            if (LiveAccessibilityUtil.isTalkbackMode()) {
                //如果是无障碍模式,第三排直接隐藏
                mHeaderThirdLine.setVisibility(INVISIBLE);
            }
        });
    }

    private static class FollowClass implements AnchorFollowManage.IFollowAnchorListener {

        private final SoftReference<AudienceHeaderComponent> mRef;

        public FollowClass(AudienceHeaderComponent c) {
            this.mRef = new SoftReference<>(c);
        }

        @Override
        public void onFollow(long uid, boolean follow) {
            AudienceHeaderComponent component = mRef.get();
            if (component == null) {
                return;
            }
            if (component.getHostData() != null && component.getHostData().getLiveAnchorInfo() != null && component.getHostData().getLiveAnchorInfo().uid == uid) {
                component.getHostData().setFollowed(follow);
                if (!follow) {
                    //没关注，取消粉丝团加入引导动画
                    HandlerManager.removeCallbacks(component.mFansJoinTask);
                } else {
                    //关注了，开始引导动画
                    TIME_FANS_JOIN_ANIM_DURATION = LiveSettingManager.getFansJoinShowDuration() * TIME_SECOND;
                    HandlerManager.removeCallbacks(component.mFansJoinTask);
                    HandlerManager.postOnUIThreadDelay(component.mFansJoinTask, TIME_FANS_JOIN_ANIM_DURATION);
                }
                component.updateFollowedStatus();
            }
        }

    }

    private final BroadcastReceiver headerComponentBroadCastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                String scale = intent.getStringExtra(LiveHeadlinesView.SCALE_EXTRA);
                if (TextUtils.isEmpty(scale)) {
                    return;
                }
                CommonLiveLogger.d("headerComponentBroadCastReceiver", "extra:" + scale);
            }
        }
    };

    /**
     * 官播间开始
     */
    @Override
    public void officialLiveStart() {
        super.officialLiveStart();
        Logger.i("官播间测试：", "officialLiveStar");
        //手动设置一遍官播间状态，现在关播不会重新请求房间接口
        PersonLiveDetail detail = getHostData();
        if (detail != null) {
            detail.setOfficialRoomFlag(true);
        }
        runAfterViewInflate(() -> {
            if (LiveRecordInfoManager.getInstance().getLiveRoomType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO) {
                liveBizTypeInHeader = AUDIO_OFFICIAL_LIVE_ROOM_TYPE;
                initAudioOfficialView();
                setShowBirthdayView();
                setShowRewardAvatarDecorate();
                ViewExtensionKt.show(mShowTopic);
                mAnchorHeaderView.setVisibility(GONE);

                if (getHostData() != null) {
                    OfficialLiveInfo officialInfo = getHostData().getOfficialInfo();
                    if (officialInfo != null) {
                        ImageManager.from(mContext).displayImage(mOfficialHostIv, officialInfo.getAvatar(), -1);
                    }
                }
            } else {
                initVideoOfficialView();
                UIStateUtil.setVisibility(mAnchorHeaderView, View.GONE);
            }
            UIStateUtil.setVisibility(mRanksView, View.INVISIBLE);
            UIStateUtil.setVisibility(mTopHeadlinesView, View.INVISIBLE);
            UIStateUtil.setVisibility(mMoreLiveView, View.INVISIBLE);
            UIStateUtil.setVisibility(mCouponView, View.GONE);
        });
    }

    /**
     * 音频官播间
     * 头部view初始化
     */
    private void initAudioOfficialView() {
        liveBizTypeInHeader = AUDIO_OFFICIAL_LIVE_ROOM_TYPE;
        if (mOfficialFirstPartLayout != null) {
            mOfficialFollowTv.setOnClickListener(this);
            mOfficialProgramIv.setOnClickListener(this);
            mOfficialFirstPartLayout.setVisibility(VISIBLE);
        }
        mOfficialSecondPartLayout.setVisibility(VISIBLE);
        Logger.i("官播间测试：", mOfficialFirstPartLayout.hashCode() + "");

    }

    @Override
    public void officialLiveEnd() {
        super.officialLiveEnd();
        runAfterViewInflate(() -> {
            Logger.i("官播间测试：", "officialLiveEnd");
            liveBizTypeInHeader = PERSONAL_LIVE_ROOM_TYPE;
            restoreSomeViewToAlpha1();
            setShowBirthdayView();
            setShowRewardAvatarDecorate();
            mAnchorHeaderView.setVisibility(VISIBLE);
            if (mOfficialFirstPartLayout != null) {
                mOfficialFirstPartLayout.setVisibility(INVISIBLE);
            }
            if (mOfficialSecondPartLayout != null) {
                mOfficialSecondPartLayout.setVisibility(INVISIBLE);
            }
            if (mVideoOfficialProgramView != null) {
                mVideoOfficialProgramView.setVisibility(GONE);
            }
            if (mVideoOfficialAnchorView != null) {
                mVideoOfficialAnchorView.setVisibility(GONE);
            }
            ViewExtensionKt.showOrGone(mShowTopic, isOfficialLive() || isPremiere());
            if (mRanksView != null) {
                mRanksView.setVisibility(VISIBLE);
                updateRank();
            }
            if (mTopHeadlinesView != null) {
                Logger.i("官播间测试：", "officialLiveEnd-mTopHeadlinesView=(VISIBLE);");
                initTopHeadLineViewVisibility();
            }
            if (mMoreLiveView != null) {
                mMoreLiveView.setVisibility(VISIBLE);
            }

            if (mCouponView != null) {
                setCouponViewVisible(hasCoupons);
            }
        });
    }

    /**
     * 视频官播间
     * 头部view初始化
     */
    private void initVideoOfficialView() {
        if (getHostData() == null) {
            return;
        }
        liveBizTypeInHeader = VIDEO_OFFICIAL_LIVE_ROOM_TYPE;
        //节目单View初始化
        if (mVideoOfficialProgramView != null) {
            mVideoOfficialProgramView.setMVideoOfficialProgramListener((isFollow) -> {
                if (isFollow) {
                    if (getAudienceHeaderInteraction() != null) {
                        getAudienceHeaderInteraction().requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                        openOfficialProgram();
                    }
                } else {
                    followOfficialLive();
                }
            });
        }
        if (null != mVideoOfficialProgramView) {
            mVideoOfficialProgramView.setVisibility(VISIBLE);
            mVideoOfficialProgramView.updateOfficialInfo(getHostData().getOfficialInfo());
        }

        //视频官播间 主播头部View初始化
        if (mVideoOfficialAnchorView != null) {
            mVideoOfficialAnchorView.bindHeaderOnClickListener(this);
        }
        if (null != mVideoOfficialAnchorView) {
            mVideoOfficialAnchorView.setVisibility(VISIBLE);
            mVideoOfficialAnchorView.setHostInfo(getHostData().getAnchorName(), getHostData().getAnchorAvatar(), getHostData().getHostUid());
        }

        mOfficialSecondPartLayout.setVisibility(VISIBLE);

        Logger.i(TAG, "音频官播间 initVideoOfficial");
    }


    /**
     * 服务端的数据要不要展示上头条的入口
     */
    private boolean httpDataIsShowHeadline() {
        PersonLiveDetail hostData = getHostData();
        boolean show = false;
        if (hostData != null && hostData.getHeadAnchor() != null && Boolean.TRUE.equals(hostData.getHeadAnchor().getHasHeadAnchor())) {
            //只要有人正在上头条，那从数据层面就一定要展示
            return true;
        }
        if (hostData != null && hostData.getHeadAnchor() != null) {
            //没人正在上头条，入口展不展示就看服务端返回的结果
            show = Boolean.TRUE.equals(hostData.getHeadAnchor().getShowHeadAnchor());
        }
        return show;

    }


    /**
     * 设置首映室View更新
     *
     * @param isFollowed 是否关注
     * @param isWithAnim 是否动画
     */
    private void premiereFollowViewUpdate(boolean isFollowed, boolean isWithAnim) {
        runAfterViewInflate(() -> {
            if (getHostData() == null || mPremiereAnchorHeaderView == null) {
                return;
            }
            mPremiereAnchorHeaderView.setChildFansStatus(isFollowed, getFansCode(), isWithAnim, getFansActiveStatus());
            if (null != getHostData() && getHostData().isFollowed()) {
                mPremiereAnchorHeaderView.setBackgroundResource(R.drawable.live_dark_radiu20_bg);
                mPremiereAnchorHeaderView.setPadding(BaseUtil.dp2px(getContext(), 10), 0, BaseUtil.dp2px(getContext(), 3), 0);
            } else {
                mPremiereAnchorHeaderView.setBackgroundResource(R.drawable.live_dark_radiu20_bg);
            }
        });
    }

    @Override
    protected void onCurrentUserInfoChange(LiveUserInfo currentLoginUserInfo) {
        super.onCurrentUserInfoChange(currentLoginUserInfo);
        runAfterViewInflate(() -> {
            if (canUpdateUi() && !isLoadUserInfoToGuard) {
                isLoadUserInfoToGuard = true;
                if (currentLoginUserInfo == null || currentLoginUserInfo.getGuardGroupVo() == null || currentLoginUserInfo.getGuardGroupVo().getGuardEndTIme() <= 0) {
                    return;
                }
                //如果守护者结束时间在3天内，则开始守护续购动画
                if (LiveTimeUtil.isLastFewDays(currentLoginUserInfo.getGuardGroupVo().getGuardEndTIme(), 3)) {
                    mAnchorHeaderView.startGuardPopAnimDelay(5000);
                }
            }
        });
    }

    @Nullable
    @Override
    public ComponentManager createChildComponentManager() {
        if (null == getFragment()) {
            return null;
        }

        AudienceComponentContext componentContext = new AudienceComponentContext();
        componentContext.setParentComponent(this);
        AudienceHeaderHost host = new AudienceHeaderHost(getFragment(), componentContext);
        return new LiveRoomComponentManager(host, new AudienceHeaderFactory());
    }

    /**
     * 无障碍模式下
     * 设置ContentDescription
     */
    private void checkAccessibilityAndSetDes() {
        runAfterViewInflate(() -> {
            if (getHostData() == null) {
                return;
            }
            if (LiveAccessibilityUtil.isTalkbackMode() && getHostData().isFollowed()) {
                //如果是无障碍模式,并且是已关注
                mAnchorHeaderView.setMIsFollow(true);
                mAnchorHeaderView.setFollowVisibility(GONE);
                String contentDescription = mLiveUserInfo.nickname + " " + "已关注";
                mAnchorHeaderView.setHostNameContentDescription(contentDescription);
                //视频官播间设置无障碍模式
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.getMAnchorFollowView().setMIsFollow(true);
                    mVideoOfficialAnchorView.isAccessibilityUpdateUi();
                }
                return;
            }

            if (mLiveUserInfo != null && !TextUtils.isEmpty(mLiveUserInfo.nickname)) {
                mAnchorHeaderView.setAudiNameOrAnchorTime(mLiveUserInfo.nickname);
                String contentDescription = mLiveUserInfo.nickname + " " + (getHostData().isFollowed() ? "已关注" : "");
                mAnchorHeaderView.setHostNameContentDescription(contentDescription);
                if (mVideoOfficialAnchorView != null) {
                    mVideoOfficialAnchorView.getMAnchorNameView().setContentDescription(contentDescription);
                }
            }
        });
    }

    @Override
    public void resetView() {
        super.resetView();
        someViewsToAlpha0();
    }

    private void someViewsToAlpha0() {
        runAfterViewInflate(() -> {
            mShowHeaderViews.reset();
            ViewStatusUtil.setAlpha(mAnchorHeaderView.getMHeaderIv(), 0);
            mShowHeaderViews
                    .hideAndWaitShow(mAnchorHeaderView, 200, 200)
                    .hideAndWaitShow(getGiftRankView(), 200, 200);

            boolean supportMini = LiveCommonUtil.isSupportMini(-1, mAttachFragment.getActivity());

            if (mMiniView != null && supportMini && getLiveStatus() == PersonLiveBase.LIVE_STATUS_ING &&
                    !(getLiveMediaType() == LiveMediaType.TYPE_VIDEO && !LiveSettingManager.canShowMiniViewConfig())) {
                UIStateUtil.showViews(mMiniView);
            } else if (mMiniView != null) {
                UIStateUtil.hideViews(mMiniView);
            }

            mShowHeaderViews
                    .hideAndWaitShow(mShowTopic, 200, 400)
                    .hideAndWaitShow(mRanksView, 200, 400)
                    .hideAndWaitShow(mTopHeadlinesView, 200, 400)
                    .hideAndWaitShow(mHeaderHostAvatarStokeIv, 200, 400)
                    .hideAndWaitShow(mHeaderHostRewardAvatarDecorateIv, 200, 400)
                    .hideAndWaitShow(mLiveRoomFmNumber, 200, 400)
                    .hideAndWaitShow(mMoreLiveView, 200, 400);
        });
    }

    @Override
    public void onCloseRoomClick() {
        if (getAudienceHeaderInteraction() != null) {
            getAudienceHeaderInteraction().finishLamiaRoomFromCloseClick();
        }
    }

    @Override
    public void onMiniRoomClick() {
        if (getAudienceHeaderInteraction() != null) {
            getAudienceHeaderInteraction().miniLamiaRoomFromMiniClick();
        }
    }

    @Override
    public boolean isMiniRoomSupport() {
        return mMiniView != null && mMiniView.getVisibility() == VISIBLE;
    }

    private View getGiftRankView() {
        try {
            IAudienceHeaderGiftRankComponent giftRankComponent = getComponent(HeaderCompConfig.HEADER_GIFT_RANK_COMPONENT);
            if (null != giftRankComponent) {
                return giftRankComponent.getComponentView();
            }
        } catch (Exception e) {
            Logger.e(TAG, e.getMessage());
        }
        return null;
    }

    @Override
    public void onReceiveMoreLiveMsg(LiveMoreLiveNotifyMsg msg) {
        runAfterViewInflate(() -> {
            if (mMoreLiveView != null) {
                mMoreLiveView.updateContent(msg);
            }
        });
    }

    @Override
    public void slidShowMoreLive() {
        // 侧滑触发的展开更多直播面板
        if (mMoreLiveInfo == null) {
            showMoreLiveDialog(0, true, null);
            return;
        }

        MoreLiveDialogFragment.Tab recTab;
        if (mMoreLiveInfo.getType() == TYPE_LIVING) {
            recTab = MoreLiveDialogFragment.Tab.TAB_ATTENTION;
        } else {
            recTab = MoreLiveDialogFragment.Tab.TAB_RECOMMEND;
        }
        MoreLiveDialogFragment.RecParam param = new MoreLiveDialogFragment.RecParam(
                recTab, mMoreLiveInfo.getRecRoomId(), mMoreLiveInfo.getRecBizType()
        );
        showMoreLiveDialog(0, true, param);
    }

    @Nullable
    private ILamiaHeaderInteraction getAudienceHeaderInteraction() {
        return getComponentInteractionSafety(ILamiaHeaderInteraction.class);
    }
}
