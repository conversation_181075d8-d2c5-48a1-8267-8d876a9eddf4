package com.ximalaya.ting.android.liveaudience.components.premiere.countdown;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.LivePremiereMsg;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereMsgType;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimer;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;

/**
 * Created by zhixin.he on 2024/3/5.
 *
 * @desc 首映室倒计时组件
 * @email <EMAIL>
 * @phone 15026804470
 */
public class PremiereCountDownComponent extends LamiaComponent implements IPremiereCountDownComponent {

    private TextView mCdTv;
    private LiveTimer mLiveTimer;

    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        mCdTv = findViewById(R.id.live_premiere_count_down_dialog_num_tv);
    }

    private void startCountDown(int remainTime) {
        runAfterViewInflate(new Runnable() {
            @Override
            public void run() {
                ViewStatusUtil.setVisible(View.VISIBLE, getView());
                if (mLiveTimer == null) {
                    mLiveTimer = new LiveTimer((remainTime + 1) * 1000L, getClass().getSimpleName()) {
                        @Override
                        public void onChanged(TimerHolder holder) {
                            super.onChanged(holder);
                            if (holder == null) {
                                return;
                            }
                            if (mCdTv == null) {
                                return;
                            }
                            mCdTv.setText(String.valueOf(holder.second));
                            if (holder.second == 0) {
                                ViewStatusUtil.setVisible(View.GONE, getView());
                            }
                        }
                    };
                } else {
                    mLiveTimer.reSetInitTime(remainTime);
                }
                mLiveTimer.startCountDown(false);
            }
        });
    }

    @Override
    public void onReceiveCountDownMsg(LivePremiereMsg msg) {
        if (msg.getRemainTime() <= 0) {
            return;
        }
        if (PremiereMsgType.TYPE_HAS_TEXT == msg.getType()) {
            startCountDown(msg.getRemainTime());
        }
    }

    @Override
    public void resetData() {
        super.resetData();
        if (mLiveTimer != null) {
            mLiveTimer.stop();
            mLiveTimer = null;
        }
    }
}
