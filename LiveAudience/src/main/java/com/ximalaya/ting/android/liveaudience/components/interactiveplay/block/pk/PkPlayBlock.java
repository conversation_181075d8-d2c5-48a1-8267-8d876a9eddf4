package com.ximalaya.ting.android.liveaudience.components.interactiveplay.block.pk;

import static com.ximalaya.ting.android.live.common.view.viewpostion.PlayBizLog.MODULE_PK_PLAY_BLOCK;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Blur;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.drawable.RoundDrawable;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.view.dialog.SimpleDialog;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveRouterUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveViewUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.view.viewpostion.PlayBizLog;
import com.ximalaya.ting.android.live.host.scrollroom.biz.pageritem.RoomBackgroundCache;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomStatusChangeMessage;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.Anchor;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.block.AbsBasePlayBlock;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.mvi.BlockDataIntent;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkAnchorInfo;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkPanelSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkPropPanelNotify;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.enums.CommonPkMatchTypeEnum;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.enums.CommonPkStatusEnum;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.IPkTimer;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkHelper;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkManager;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.liveaudience.view.pk.PkPanelControlView;
import com.ximalaya.ting.android.liveaudience.view.pk.PkPanelView;
import com.ximalaya.ting.android.liveaudience.view.pk.PkStarCraftPanelView;
import com.ximalaya.ting.android.liveaudience.view.pk.host.PkStarCraftPanelControlView;
import com.ximalaya.ting.android.liveaudience.view.pk.video.PKVideoRankView;
import com.ximalaya.ting.android.liveaudience.view.pk.video.PkVideoPanelView;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.util.ArrayList;
import java.util.List;

import PK.Base.MediaType;
import PK.Base.RandomPkType;

/**
 * PK（普通 PK、排位赛 PK、 指定 PK）
 *
 * <AUTHOR>
 */
public class PkPlayBlock extends AbsBasePlayBlock implements IPkPlayBlock {

    private static final String TAG = "PkPlayBlock";

    private LivePkManager mLivePkManager;

    private AnimatorSet mPkExitTransitionAnimSet;
    private Animator.AnimatorListener mPkExitTransitionAnimListener;


    private PkPanelView mPkPanelView;
    private PkPanelControlView mPkPanelControlView;

    private String bgImageUrl;

    private boolean isShowAVPanel;

    @Override
    public void init(LivePkManager livePkManager) {
        //初始化Pk管理类
        mLivePkManager = livePkManager;
    }

    @Override
    public List<Anchor> getAnchorPKInfo() {
        if (mBlockInteraction == null || mBlockInteraction.getRoomModeData() == null) {
            return new ArrayList<>();
        }
        List<Anchor> list = new ArrayList<>();
        if (mLivePkManager != null) {
            CommonPkPanelSyncRsp panelSyncRsp = mLivePkManager.getPanelSyncRsp();
            if (panelSyncRsp != null) {
                if (panelSyncRsp.mHomeAnchorInfo != null && panelSyncRsp.mHomeAnchorInfo.mUid > 0) {
                    if (panelSyncRsp.mHomeAnchorInfo.mRoomId != mBlockInteraction.getRoomModeData().getRoomId()) {
                        //如果根本不不是这个房间的数据，数据有问题
                        return list;
                    }
                    Anchor anchor = new Anchor();
                    anchor.setRoomId(mBlockInteraction.getRoomModeData().getRoomId());
                    anchor.setName(panelSyncRsp.mHomeAnchorInfo.mNickname);
                    anchor.setUid(panelSyncRsp.mHomeAnchorInfo.mUid);
                    list.add(anchor);
                }
                if (panelSyncRsp.mVisitorAnchorInfo != null && panelSyncRsp.mVisitorAnchorInfo.mUid > 0) {
                    Anchor anchor = new Anchor();
                    anchor.setRoomId(mBlockInteraction.getRoomModeData().getRoomId());
                    anchor.setName(panelSyncRsp.mVisitorAnchorInfo.mNickname);
                    anchor.setUid(panelSyncRsp.mVisitorAnchorInfo.mUid);
                    list.add(anchor);
                }
            }
        }
        return list;
    }

    @Override
    public void showPkModeUI() {
        if (mBlockInteraction == null) {
            return;
        }
        if (mBlockInteraction.isAnchor() && !mBlockInteraction.isAnchorVisitor()) {
            LivePkHelper.getInstance().startSyncMicStatus();
        } else {
            LivePkHelper.getInstance().stopSyncMicStatus();
        }
        LivePkHelper.getInstance().syncPanelInfo();
        initPkModeUI();
        //需要在初始化之后就立即调用，保证在展示之前就获取到 pk 状态

        if (!mBlockInteraction.isUserInInput()) {
            //不在输入状态中
            showPkUI();
        }
    }

    @Override
    public void releasePkModeUI() {
        PlayBizLog.INSTANCE.logFile("releasePkModeUI()", MODULE_PK_PLAY_BLOCK);
        if (mBlockInteraction == null) {
            return;
        }
        // todo 这里的setPkStatusTv();需要根据信令处理

        if (mPkPanelView != null && mPkPanelView.getParent() != null) {
            playPkExitTransitionAnim();
        }

        LivePkHelper.getInstance().stopSyncMicStatus();
        if (getInteractiveInteraction() != null) {
            getInteractiveInteraction().releasePkModeUI();
        }
    }

    @Override
    public void handleRoomData(BlockDataIntent data) {
        super.handleRoomData(data);
        if (data instanceof BlockDataIntent.PkBlockData) {
            updateAnonymityFlag();
        }
    }

    private void updateAnonymityFlag() {
        boolean needAnonymity = false;
        try {
            AudienceComponentInteraction interaction = mBlockInteraction.getComponentHost().getInteractionImpl(AudienceComponentInteraction.class);
            needAnonymity = interaction.isNeedAnonymity();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (mPkPanelView != null) {
            mPkPanelView.setAnonymousRoomFlag(needAnonymity);
        }
    }

    private void playPkExitTransitionAnim() {
        PlayBizLog.INSTANCE.logFile("playPkExitTransitionAnim()");
        if (mPkExitTransitionAnimSet == null) {
            mPkExitTransitionAnimSet = new AnimatorSet();
        }
        if (mPkExitTransitionAnimListener == null) {
            mPkExitTransitionAnimListener = new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    PlayBizLog.INSTANCE.logFile("onAnimationEnd()", MODULE_PK_PLAY_BLOCK);

                    if (mBlockInteraction == null || !mBlockInteraction.canUpdateUi()) {
                        PlayBizLog.INSTANCE.logFile("onAnimationEnd() " + (mBlockInteraction == null) + "  canUpdateUi false", MODULE_PK_PLAY_BLOCK);
                        return;
                    }

                    destroyPanelView();
                    destroyPanelControlView();
                }
            };
        }
        if (mPkExitTransitionAnimSet.isStarted()) {
            return;
        }

        PlayBizLog.INSTANCE.log("playPkExitTransitionAnim", MODULE_PK_PLAY_BLOCK);

        ValueAnimator animatorPart1 = ValueAnimator.ofFloat(0f, 1f);
        animatorPart1.addUpdateListener(updateListener);
        animatorPart1.setDuration(200);

        mPkExitTransitionAnimSet.play(animatorPart1);
        mPkExitTransitionAnimSet.addListener(mPkExitTransitionAnimListener);
        mPkExitTransitionAnimSet.start();
    }

    private final ValueAnimator.AnimatorUpdateListener updateListener = new ValueAnimator.AnimatorUpdateListener() {
        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            if (mPkPanelView != null && mBlockInteraction != null && mBlockInteraction.canUpdateUi()) {
                float animatedValue = (float) animation.getAnimatedValue();
                mPkPanelView.setAlpha(1 - animatedValue);
            }
        }
    };

    private void destroyPanelView() {
        PlayBizLog.INSTANCE.logFile("destroyPanelView", MODULE_PK_PLAY_BLOCK);
        if (mPkPanelView != null && mPkPanelView.getParent() != null) {
            PlayBizLog.INSTANCE.logFile("destroyPanelView, post", MODULE_PK_PLAY_BLOCK);
            mPkPanelView.post(new Runnable() {
                @Override
                public void run() {
                    PlayBizLog.INSTANCE.logFile("destroyPanelView, removeView", MODULE_PK_PLAY_BLOCK);
                    if (mPkPanelView != null) {
                        PlayBizLog.INSTANCE.logFile("destroyPanelView() mPkPanelView ", MODULE_PK_PLAY_BLOCK);
                        // Todo Fix https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/54846363?pid=1
                        try {
                            LiveViewUtil.removeViewFromParent(mPkPanelView);
                        } catch (Exception e) {
                            PlayBizLog.INSTANCE.logFile("destroyPanelView() e: " + e.getMessage(), MODULE_PK_PLAY_BLOCK);
                            e.printStackTrace();
                            CrashReport.postCatchedException(e);
                        }
                        mPkPanelView.setOnClickPkPanelViewListener(null);
                        mPkPanelView.setOnEventDispatchListener(null);
                        mPkPanelView = null;
                    }

                }
            });
        } else {
            mPkPanelView = null;
        }
    }

    private void destroyPanelControlView() {
        if (mPkPanelControlView != null && mPkPanelControlView.getParent() != null) {
            mPkPanelControlView.post(new Runnable() {
                @Override
                public void run() {
                    LiveViewUtil.removeViewFromParent(mPkPanelControlView);
                    if (mPkPanelControlView != null) {
                        mPkPanelControlView.setOnClickPanelControlViewListener(null);
                        mPkPanelControlView = null;
                    }

                }
            });
        } else {
            mPkPanelControlView = null;
        }
    }

    public PkPanelView getPkPanelView() {
        return mPkPanelView;
    }


    public PkPanelControlView getPkPanelControlView() {
        return mPkPanelControlView;
    }

    public void initPkModeUIView() {
        if (mPkExitTransitionAnimSet != null && mPkExitTransitionAnimSet.isStarted()) {
            mPkExitTransitionAnimSet.end();
        }
        if (mBlockInteraction.getHostData() != null) {
            setBgUrl(mBlockInteraction.getHostData().getBgImage());
        }
        inflatePanelView(mBlockInteraction.isShowAVPanel());
        inflatePanelControlView();
    }

    private void showUserInfoPop(long hostUid, boolean isFromPk, long roomId, long matchedHostUid) {
        LamiaHelper.Log.i("点击测试-", "hostUid:" + hostUid);
        if (mBlockInteraction != null && mBlockInteraction.getRoomModeHelper() != null && hostUid > 0) {
            mBlockInteraction.getRoomModeHelper().showUserInfoPop(hostUid, isFromPk, roomId, matchedHostUid);
        }
    }

    private void pkBurySamePoint(int metaId) {
        if (mBlockInteraction != null && mBlockInteraction.getRoomModeData() != null) {
            new XMTraceApi.Trace()
                    .click(metaId)
                    .put("pkMode", String.valueOf(RoomModeManager.getInstance().getPkMode()))
                    .put("currPage", "liveRoom")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .createTrace();
        }
    }

    private void inflatePanelView(boolean isShowAVPanel) {
        PlayBizLog.INSTANCE.log(
                "inflatePanelView()  "+isShowAVPanel,
                MODULE_PK_PLAY_BLOCK
        );
        if (mBlockInteraction == null || !mBlockInteraction.canUpdateUi()) {
            return;
        }
        if (mPkPanelView == null) {
            if (RoomModeManager.isStarCraftPkMode()) {
                mPkPanelView = new PkStarCraftPanelView(getContext());
            } else if (RoomModeManager.isRankPkMode()) {
                if (mBlockInteraction.getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
                    mPkPanelView = new PkPanelView(getContext());
                    chooseBgBitmap(null);
                } else {
                    mPkPanelView = new PKVideoRankView(getContext());
                }
            } else {
                if (mBlockInteraction.getLiveMediaType() == LiveMediaType.TYPE_AUDIO && !isShowAVPanel) {
                    mPkPanelView = new PkPanelView(getContext());
                    chooseBgBitmap(null);
                } else {
                    mPkPanelView = new PkVideoPanelView(getContext());
                }
            }
        } else {
            boolean a = mPkPanelView instanceof PkVideoPanelView;
            PlayBizLog.INSTANCE.log(
                    "mPkPanelView != null, mPkPanelView is PkVideoPanelView: " + a,
                    MODULE_PK_PLAY_BLOCK
            );
            if (mBlockInteraction.getLiveMediaType() == LiveMediaType.TYPE_AUDIO && isShowAVPanel &&
                    (!(mPkPanelView.getClass() == PkVideoPanelView.class))) {
                PlayBizLog.INSTANCE.log(
                        "inflatePanelView()   new PkVideoPanelView",
                        MODULE_PK_PLAY_BLOCK
                );
                boolean isMatching = mPkPanelView.getPkStatus() == CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_ING;
                LiveViewUtil.removeViewFromParent(mPkPanelView);
                mPkPanelView.setOnClickPkPanelViewListener(null);
                mPkPanelView.setOnEventDispatchListener(null);
                mPkPanelView = new PkVideoPanelView(getContext());
                mPkPanelView.shouldShowStartPkAnim(isMatching);
            }
            if (mBlockInteraction.getLiveMediaType() == LiveMediaType.TYPE_AUDIO && !isShowAVPanel &&
                    (mPkPanelView.getClass() == PkVideoPanelView.class)) {
                PlayBizLog.INSTANCE.log(
                        "inflatePanelView()   new PkPanelView",
                        MODULE_PK_PLAY_BLOCK
                );
                boolean isPenalty = mPkPanelView.getPkStatus() == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY;
                LiveViewUtil.removeViewFromParent(mPkPanelView);
                mPkPanelView.setOnClickPkPanelViewListener(null);
                mPkPanelView.setOnEventDispatchListener(null);
                mPkPanelView = new PkPanelView(getContext());
                mPkPanelView.shouldNotShowEndPkAnim(isPenalty);
            }
        }
        setAnonymousRoomFlag();
        mPkPanelView.setHostMediaType(mBlockInteraction.getLiveMediaType());
        mPkPanelView.setId(R.id.live_pk_panel_view);
        mPkPanelView.setOwnerActivity(mBlockInteraction.getActivity());
        mPkPanelView.setOwnerFragment(mBlockInteraction.getRoomModeHelper() != null ? mBlockInteraction.getRoomModeHelper().getFragment() : null);
        mPkPanelView.setIsFromHostFragment(mBlockInteraction.isFromHostFragment());

        mPkPanelView.setOnClickPkPanelViewListener(new PkPanelView.IOnClickPkPanelViewListener() {
            @Override
            public void onClickHostAvatar(CommonPkAnchorInfo hostUserInfo) {
                if (hostUserInfo != null) {
                    showUserInfoPop(hostUserInfo.mUid, false, 0, 0);
                }

                if (hostUserInfo != null && mBlockInteraction != null && mBlockInteraction.getRoomModeData() != null) {
                    new XMTraceApi.Trace()
                            .click(33483)
                            .put("uid", String.valueOf(hostUserInfo.mUid))
                            .put("pkMode", String.valueOf(RoomModeManager.getInstance().getPkMode()))
                            .put("currPage", "liveRoom")
                            .put(LiveRecordInfoManager.getInstance().getBaseProps())
                            .createTrace();
                }
            }

            @Override
            public void onClickMatchedUserAvatar(CommonPkAnchorInfo matchedUserInfo) {
                if (matchedUserInfo != null) {
                    showUserInfoPop(matchedUserInfo.mUid, true, matchedUserInfo.mRoomId, matchedUserInfo.mUid);
                }

                if (matchedUserInfo != null && mBlockInteraction != null && mBlockInteraction.getRoomModeData() != null) {
                    new XMTraceApi.Trace()
                            .click(33483)
                            .put("uid", String.valueOf(matchedUserInfo.mUid))
                            .put("pkMode", String.valueOf(RoomModeManager.getInstance().getPkMode()))
                            .put("currPage", "liveRoom")
                            .put(LiveRecordInfoManager.getInstance().getBaseProps())
                            .createTrace();
                }
            }

            @Override
            public void onLongClickHostAvatarForTest() {
                if (mLivePkManager != null) {
                    mLivePkManager.testPkResultAnimation(mBlockInteraction != null &&
                            mBlockInteraction.getRoomModeData() != null ? mBlockInteraction.getRoomModeData().getHostId() : 0);
                }
            }

            @Override
            public void onClickPkRank() {
                if (mBlockInteraction != null && mBlockInteraction.getRoomModeHelper() != null && mBlockInteraction.getRoomModeHelper().getFragment() != null
                        && mBlockInteraction.getRoomModeData() != null) {
                    LiveRouterUtil.goToPkRankFragment(mBlockInteraction.getRoomModeHelper().getFragment());
                }

                pkBurySamePoint(33481);
            }

            @Override
            public void onClickPkRule() {
                pkBurySamePoint(33482);
            }

            @Override
            public void onClickFinishPk() {
                handleOverPk();
            }

            @Override
            public void onClickMuteButton(boolean mute) {
                handleMutePk(mute);
                pkBurySamePoint(33488);
            }

            @Override
            public void onClickGetBox(String path, long templateId) {
                showStarCraftBoxAnimate(path, templateId);
            }

            @Override
            public void onClickPkContributeChairs() {
                pkBurySamePoint(33484);
            }

            @Override
            public void onClickPkContributeBtn() {
                pkBurySamePoint(33485);
            }

            @Override
            public void onClickPkReport() {
                pkBurySamePoint(33487);
            }

            @Override
            public void onClickStarCraftChoose() {
                pkBurySamePoint(33497);
            }

            @Override
            public void onClickStarCraftProp() {
                pkBurySamePoint(33498);
            }

            @Override
            public void onClickPkPredictEntry(long predictId) {
                showPkPredictEntry(predictId);
            }

        });

        mPkPanelView.setOnEventDispatchListener(new IPkTimer.IOnEventDispatchListener() {
            @Override
            public void onPkMatchingTimeOutEvent() {
                // 延迟3s等待服务端推送消息，如果3s后还不到，则主动发送请求
                HandlerManager.postOnBackgroundThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        if (isPkMatching()) {
                            LivePkManager.autoCancelMatch(false);
                            mLivePkManager.cancelPkMatch();
                        }
                    }
                }, 3000);
            }

            @Override
            public void onPkOverEvent() {
                HandlerManager.postOnBackgroundThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        if (mLivePkManager != null && mLivePkManager.getPkStatus() ==
                                CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY) {
                            mLivePkManager.overPk();
                        }
                    }
                }, 3000);
                setPkStatusTv();
            }
        });

        mPkPanelView.setHostRoomInfo(mBlockInteraction.getRoomModeData());

        if (mBlockInteraction.getRoomModeHelper() != null) {
            mPkPanelView.setFragment(mBlockInteraction.getRoomModeHelper().getFragment());
        }

        if (mPkPanelView.getParent() == null) {
            int width;
            if (mBlockInteraction.getLiveMediaType() == MediaType.MediaType_Audio.getValue()) {
                width = BaseUtil.dp2px(getContext(), PkPanelView.PANEL_WIDTH);
            } else {
                width = ViewGroup.LayoutParams.MATCH_PARENT;
            }
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
                    width,
                    ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
            if (mModeContainerLayout != null && mPkPanelView.getParent() == null) {
                mModeContainerLayout.addView(mPkPanelView, layoutParams);
            }
            mPkPanelView.notifyAdded();
        }
    }

    private boolean isAVPKPanel(CommonPkPanelSyncRsp panelSyncRsp) {
        if (!RoomModeManager.isRandomPkMode()) {
            return false;
        }
        if (mBlockInteraction == null || mBlockInteraction.getLiveMediaType() != LiveMediaType.TYPE_AUDIO) {
            return false;
        }
        if (panelSyncRsp == null || panelSyncRsp.mVisitorAnchorInfo == null) {
            return false;
        }
        if (panelSyncRsp.mVisitorAnchorInfo.mediaType != LiveMediaType.TYPE_VIDEO) {
            return false;
        }
        return panelSyncRsp.mPkStatus == CommonPkStatusEnum.PK_STATUS_PK_ING ||
                panelSyncRsp.mPkStatus == CommonPkStatusEnum.PK_STATUS_PK_PENALTY;
    }

    private void showStarCraftBoxAnimate(String path, long templateId) {
        if (mBlockInteraction != null && mBlockInteraction.getRoomModeHelper() != null) {
            mBlockInteraction.getRoomModeHelper().showStarCraftBoxAnimate(path, templateId);
        }
    }

    private void showPkPredictEntry(long predictId) {
        if (mBlockInteraction != null && mBlockInteraction.getRoomModeHelper() != null) {
            mBlockInteraction.getRoomModeHelper().showPkPredictEntry(predictId);
        }
    }

    private boolean isHost() {
        return mBlockInteraction != null && mBlockInteraction.getRoomModeHelper() != null && mBlockInteraction.getRoomModeHelper().isAnchor() && !mBlockInteraction.getRoomModeHelper().isAnchorVisitor();
    }

    public boolean isAudience() {
        return !isHost();
    }


    private void inflatePanelControlView() {
        // 听众和主播以听众身份进入自己直播间，都不显示 PK 控制面板
        if (isAudience() || mBlockInteraction == null || !mBlockInteraction.canUpdateUi()) {
            destroyPanelControlView();
            return;
        }

        if (mPkPanelControlView == null) {
            if (RoomModeManager.isStarCraftPkMode()) {
                mPkPanelControlView = new PkStarCraftPanelControlView(getContext());
            } else {
                mPkPanelControlView = new PkPanelControlView(getContext());
            }
        }
        mPkPanelControlView.setPkPanelView(mPkPanelView);
        mPkPanelControlView.setOnClickPanelControlViewListener(new PkPanelControlView
                .IOnClickPanelControlViewListener() {
            @Override
            public void onClickCancelMatch() {
                // 点击取消匹配按钮，收到 CancelMatchRsp 消息时，更新PkPanelView匹配状态和PkPanelControlView
                if (mLivePkManager != null) {
                    LivePkManager.autoCancelMatch(true);
                    mLivePkManager.cancelPkMatch();
                }
                // 埋点
                if (RoomModeManager.isStarCraftPkMode()) {
                    pkBurySamePoint(33496);
                } else {
                    pkBurySamePoint(33489);
                }
            }

            @Override
            public void onClickRematch(int matchType) {
                // 点击重新匹配按钮，重置匹配计时器，收到 StartMatchRsp 消息时，开始计时，并更新 PkPanelView 匹配状态和
                // PkPanelControlView
                if (mPkPanelView != null) {
                    mPkPanelView.resetTiming();
                }
                if (mLivePkManager != null) {
                    int randomPkType = RandomPkType.RANDOM_PK_ORDINARY.getValue();
                    if (mPkPanelView != null) {
                        randomPkType = mPkPanelView.getRandomPkType();
                    }
                    if (mBlockInteraction != null) {
                        mLivePkManager.startPkMatch(matchType, mBlockInteraction.getLiveMediaType(), randomPkType);
                    }
                }
                if (RoomModeManager.isStarCraftPkMode()) {
                    pkBurySamePoint(33494);
                } else {
                    pkBurySamePoint(33491);
                }
            }

            @Override
            public void onClickFinishPk() {
                // 点击结束PK按钮，收到 OverPkRsp 消息时，停止惩罚时间计时，并更新 PkPanelView 匹配状态和 PkPanelControlView
                handleOverPk();
                // 埋点 结束PK
                statPkControlBtn("PK弹窗", "结束PK");
            }

            @Override
            public void onClickContinuePk(int pkMode) {
                if (mBlockInteraction == null) {
                    return;
                }
                if (pkMode == CommonPkPropPanelNotify.Mode.MODE_MIC_MANUAL) {
                    if (mBlockInteraction.getRoomModeHelper() != null) {
                        mBlockInteraction.getRoomModeHelper().showPkSearchHostView();
                    }
                } else {
                    if (mPkPanelView != null) {
                        mPkPanelView.resetTiming();
                    }
                    if (mLivePkManager != null) {
                        int matchType = pkMode == CommonPkPropPanelNotify.Mode.MODE_STAR_CRAFT ? CommonPkMatchTypeEnum.STAR_CRAFT_FIRE_FIGHT : 0;
                        int randomPkType = RandomPkType.RANDOM_PK_ORDINARY.getValue();
                        if (mPkPanelView != null) {
                            randomPkType = mPkPanelView.getRandomPkType();
                        }
                        mLivePkManager.startPkMatch(matchType, mBlockInteraction.getLiveMediaType(), randomPkType);
                    } else {
                        CustomToast.showDebugFailToast("mLivePkManager == null");
                    }
                }
                // 埋点
                statPkControlBtn("PK结束弹窗", "继续PK");
            }

            @Override
            public void onClickQuitPk() {
                // 点击退出PK按钮，重置匹配计时器，收到 QuitRsp 消息时，重置计时器，并更新 PkPanelView 匹配状态和 PkPanelControlView
                if (mLivePkManager != null) {
                    mLivePkManager.quitPk();
                } else {
                    CustomToast.showDebugFailToast("mLivePkManager == null");
                }
                if (RoomModeManager.isStarCraftPkMode()) {
                    pkBurySamePoint(33495);
                } else {
                    pkBurySamePoint(33490);
                }
            }

            @Override
            public void onClickCancelInvitePk() {
                if (mLivePkManager != null) {
                    mLivePkManager.cancelInvitePk();
                }
            }

            @Override
            public void onClickRevengePk(long pkId) {
                if (mLivePkManager != null) {
                    mLivePkManager.clickRevengePk(pkId);
                }
            }
        });

        int controlViewHeight = BaseUtil.dp2px(getContext(), 70);
        if (mBlockInteraction.getLiveMediaType() == MediaType.MediaType_Video.getValue()) {
            controlViewHeight = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        if (mPkPanelControlView.getParent() == null) {
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, controlViewHeight);
            layoutParams.addRule(RelativeLayout.BELOW, R.id.live_pk_panel_view);
            if (mBlockInteraction.getLiveMediaType() == MediaType.MediaType_Video.getValue()) {
                // 视频pk场景，调整mPkPanelControlView到pk组件的位置
                layoutParams.topMargin = BaseUtil.dp2px(getContext(), 6f);
            }
            if (mModeContainerLayout != null && mPkPanelControlView.getParent() == null) {
                mModeContainerLayout.addView(mPkPanelControlView, layoutParams);
            }
        }
    }

    private void statPkControlBtn(String srcModule, String itemId) {

        if (!TextUtils.isEmpty(itemId) && !TextUtils.isEmpty(srcModule)) {
            UserTracking userTracking = new UserTracking();
            if (mBlockInteraction != null && mBlockInteraction.getRoomModeData() != null) {
                userTracking.setLiveId(mBlockInteraction.getRoomModeData().getLiveId());
            }
            userTracking.setSrcModule(srcModule);
            userTracking.setItem("button");
            userTracking.setItemId(itemId);
            userTracking.statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil
                    .SERVICE_LIVE_PAGE_CLICK);
        }
    }

    public void setPanelBlurBitmap(Bitmap bg) {
        if (mPkPanelView == null) {
            LamiaHelper.Log.i("背景图测试-", " setPanelBlurBitmap mPkPanelView==nul");
            return;
        }
        if (null == bg) {
            LamiaHelper.Log.i("背景图测试-", " setPanelBlurBitmap bg==nul");
            return;
        }
        mPkPanelView.post(new Runnable() {
            @Override
            public void run() {
                if (null == bg) {
                    LamiaHelper.Log.i("背景图测试-", " setPanelBlurBitmap mPkPanelView.post, bg==nul");
                    return;
                }
                if (null != mPkPanelView) {
                    Bitmap blurBitmap = Blur.fastBlur(getContext(), bg, 15, 45);
                    // 由于横屏的封面通过fastBlur会导致显示异常，暂时先不处理横屏封面
                    if (null == blurBitmap || bg.getWidth() > bg.getHeight()) {
                        LamiaHelper.Log.i("背景图测试-", " setPanelBlurBitmap mPkPanelView.post, blurBitmap==nul");
                    } else {
                        RoundDrawable roundDrawable = new RoundDrawable(blurBitmap, BaseUtil.dp2px(getContext(), 16), 0, 0, false);
                        mPkPanelView.setBlurBg(roundDrawable);
                        LamiaHelper.Log.i("背景图测试-", "图片设置成功");
                    }
                }
            }
        });
    }

    /**
     * 先使用缓存的背景
     * 再使用传过来的背景
     */
    public void chooseBgBitmap(Bitmap bg) {
        LamiaHelper.Log.i("背景图测试-", " chooseBgBitmap bgImageUrl:" + bgImageUrl);
        if (mPkPanelView == null) {
            LamiaHelper.Log.i("背景图测试-", " chooseBgBitmap mPkPanelView==nul");
            return;
        }
        Bitmap bitmap = RoomBackgroundCache.getBackgroundBitmap(bgImageUrl);
        if (bitmap != null) {
            LamiaHelper.Log.i("背景图测试-", "bitmapRf != null");
            LamiaHelper.Log.i("背景图测试-", "使用缓存的");
            setPanelBlurBitmap(bitmap);
        } else if (bg != null) {
            LamiaHelper.Log.i("背景图测试-", "使用下载的");
            setPanelBlurBitmap(bg);
        } else {
            //都没有，还是去下载，保底措施，通常发生在刚进房，而房主正在pk，缓存可能还没数据
            LamiaHelper.Log.i("背景图测试-", "都没有，还是去下载");
            loadBlurImg();
        }
    }

    public void destroyView() {
        if (mLivePkManager != null) {
            mLivePkManager.release();
        }

        if (mPkExitTransitionAnimSet != null) {
            if (mPkExitTransitionAnimSet.isStarted()) {
                mPkExitTransitionAnimSet.end();
            }

            if (mPkExitTransitionAnimListener != null) {
                mPkExitTransitionAnimSet.removeListener(mPkExitTransitionAnimListener);
            }
        }
    }

    public void showPkUI() {
        if (mPkPanelView != null && mLivePkManager != null) {
            mPkPanelView.setPkPanelDataForAnimation(mLivePkManager.getPanelSyncRsp());
        }
        UIStateUtil.showViews(mModeContainerLayout, mPkPanelView, mPkPanelControlView);
    }

    public void hidePkSvgaView() {
        if (mLivePkManager != null && mLivePkManager.getPKAnimationHelper() != null) {
            UIStateUtil.hideViews(mLivePkManager.getPKAnimationHelper().mPkAnimView);
        }
    }

    public void setPkStatusTv() {
        if (mPkPanelView != null && mPkPanelView instanceof PkVideoPanelView) {
            ((PkVideoPanelView) mPkPanelView).setPkStatusTv();
        }
    }

    @Override
    public void onReceiveRoomStatusChangeMessage(CommonChatRoomStatusChangeMessage message) {
        if (message.status != PersonLiveBase.LIVE_STATUS_ING) {
            releasePkModeUI();
        }
    }

    @Override
    public void currentUserUpdate(PersonLiveDetail detail) {
        setAnonymousRoomFlag();
    }

    private void setAnonymousRoomFlag() {
        boolean needAnonymity = false;
        try {
            AudienceComponentInteraction interaction = mBlockInteraction.getComponentHost().getInteractionImpl(AudienceComponentInteraction.class);
            needAnonymity = interaction.isNeedAnonymity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (mPkPanelView != null) {
            mPkPanelView.setAnonymousRoomFlag(needAnonymity);
        }
    }

    public void updateMicUserSpeakVolume(long uid, double volume) {
        if (uid <= 0) {
            return;
        }
        if (mPkPanelView != null) {
            mPkPanelView.updateMicUserSpeakVolume(uid, volume);
        }
    }

    private void loadBlurImg() {
        ImageManager.from(MainApplication.getMyApplicationContext()).downloadBitmap(bgImageUrl, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap != null) {
                    LamiaHelper.Log.i("背景图测试-", "loadBlurImg-下载完成");
                    setPanelBlurBitmap(bitmap);
                }
            }
        });
    }

    public void handleOverPk() {
        if (RoomModeManager.isStarCraftPkMode()) {
            if (mLivePkManager != null) {
                mLivePkManager.overStarCraftPk();
            }
        } else {
            LiveLamiaUtil.showPkOverDialog(MainApplication.getTopActivity(), new SimpleDialog.IDialogInterface() {
                @Override
                public void onExecute() {
                    if (mLivePkManager != null) {
                        mLivePkManager.overPk();
                    }
                }
            });
        }
    }

    public void handleMutePk(boolean mute) {
        if (mLivePkManager != null) {
            mLivePkManager.mutePk(mute);
        }
    }

    private void initPkModeUI() {
        if (mBlockInteraction == null) {
            return;
        }
        initPkModeUIView();

        if (getInteractiveInteraction() != null) {
            getInteractiveInteraction().initPkModeUI();
        }
    }

    //pk 模式匹配中
    public boolean isPkMatching() {
        return mLivePkManager != null && mLivePkManager.getPkStatus() == CommonPkPropPanelNotify.PkStatus
                .PK_STATUS_MATCH_ING;
    }

    //pk邀请中
    public boolean isPkInviting() {
        return mLivePkManager != null && mLivePkManager.getPkStatus() == CommonPkPropPanelNotify.PkStatus
                .PK_STATUS_PK_INVITE;
    }

    public boolean isPkIng() {
        return mLivePkManager != null && mLivePkManager.getPkStatus() == CommonPkPropPanelNotify.PkStatus
                .PK_STATUS_PK_ING;
    }

    @Override
    public void updateBgUrl(String bgUrl) {
        //先使用缓存的，再去下载
        setBgUrl(bgUrl);
        Bitmap bitmap = RoomBackgroundCache.getBackgroundBitmap(bgUrl);
        if (bitmap != null) {
            LamiaHelper.Log.i("背景图测试-", "缓存有，直接使用");
            setPanelBlurBitmap(bitmap);
            return;
        }
        ImageManager.from(MainApplication.getMyApplicationContext()).downloadBitmap(bgUrl, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap != null) {
                    LamiaHelper.Log.i("背景图测试-", "有新的背景图下发-下载完成");
                    chooseBgBitmap(bitmap);
                }
            }
        });
    }

    public void setBgUrl(String bgImage) {
        bgImageUrl = bgImage;
    }

    @Override
    public void onResetView() {
        releasePkModeUI();
        hidePkSvgaView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        destroyView();
    }

    @Override
    protected boolean isNeedInputStatusGone() {
        return true;
    }

    @Override
    public void onPkPanelSync(CommonPkPanelSyncRsp panelSyncRsp) {
        if (isAudience()) {
            return;
        }
        PlayBizLog.INSTANCE.log(
                "onPkPanelSync() " + panelSyncRsp.toString(),
                MODULE_PK_PLAY_BLOCK
        );
        if (mBlockInteraction.getLiveMediaType() == LiveMediaType.TYPE_AUDIO && isAVPKPanel(panelSyncRsp) && (mPkPanelView != null && !(mPkPanelView.getClass() == PkVideoPanelView.class))) {
            PlayBizLog.INSTANCE.log(
                    "onPkPanelSync()  !PKVideoRankView",
                    MODULE_PK_PLAY_BLOCK
            );
            LiveViewUtil.removeViewFromParent(mPkPanelView);
            inflatePanelView(true);
            return;
        }
        if (mBlockInteraction.getLiveMediaType() == LiveMediaType.TYPE_AUDIO && !isAVPKPanel(panelSyncRsp) && (mPkPanelView != null && mPkPanelView.getClass() == PkVideoPanelView.class)) {
            PlayBizLog.INSTANCE.log(
                    "onPkPanelSync()  PKVideoRankView",
                    MODULE_PK_PLAY_BLOCK
            );
            boolean isPenalty = mPkPanelView.getPkStatus() == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY;
            LiveViewUtil.removeViewFromParent(mPkPanelView);
            inflatePanelView(false);
            mPkPanelView.shouldNotShowEndPkAnim(isPenalty);
        }
    }

    @Override
    public boolean isQualifierPk() {
        if(mLivePkManager != null && mLivePkManager.getPanelSyncRsp() != null){
            return mLivePkManager.getPanelSyncRsp().mRandomPkType == RandomPkType.RANDOM_PK_QUALIFIER.getValue();
        }
        return false;
    }

    @Override
    public void onAVChangePKPanel(boolean isAudio) {
        isShowAVPanel = !isAudio;
        PlayBizLog.INSTANCE.log(
                "onAVChangePKPanel() " + isAudio,
                MODULE_PK_PLAY_BLOCK
        );
        if (isShowAVPanel) {
            if (mPkPanelView != null && !(mPkPanelView.getClass() == PkVideoPanelView.class)) {
                PlayBizLog.INSTANCE.log(
                        "onAVChangePKPanel()  !PKVideoRankView",
                        MODULE_PK_PLAY_BLOCK
                );
                boolean isMatching = mPkPanelView.getPkStatus() == CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_ING;
                LiveViewUtil.removeViewFromParent(mPkPanelView);
                inflatePanelView(isShowAVPanel);
                mPkPanelView.shouldShowStartPkAnim(isMatching);
                LivePkHelper.getInstance().startSyncPanelInfo();
            }
        } else {
            if (mPkPanelView != null && mPkPanelView.getClass() == PkVideoPanelView.class) {
                PlayBizLog.INSTANCE.log(
                        "onAVChangePKPanel()  PKVideoRankView",
                        MODULE_PK_PLAY_BLOCK
                );
                boolean isPenalty = mPkPanelView.getPkStatus() == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY;
                LiveViewUtil.removeViewFromParent(mPkPanelView);
                inflatePanelView(isShowAVPanel);
                mPkPanelView.shouldNotShowEndPkAnim(isPenalty);
                LivePkHelper.getInstance().startSyncPanelInfo();
            }
        }

    }
}
