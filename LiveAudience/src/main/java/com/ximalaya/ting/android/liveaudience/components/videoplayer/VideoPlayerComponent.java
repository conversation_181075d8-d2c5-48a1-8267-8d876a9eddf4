package com.ximalaya.ting.android.liveaudience.components.videoplayer;


import android.content.pm.ActivityInfo;
import android.graphics.Color;
import android.hardware.SensorManager;
import android.os.Bundle;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;
import android.view.OrientationEventListener;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.base.constants.CdnStatus;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveViewUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.videoplayer.block.VideoPlayerBlock;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.videoplayer.controller.PlayerWindowLandscapeControllerComponent;
import com.ximalaya.ting.android.live.common.videoplayer.controller.PlayerWindowPortraitControllerComponent;
import com.ximalaya.ting.android.live.common.videoplayer.controller.PlayerWindowPortraitProxyClickView;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.live.host.fragment.room.BaseRoomFragment;
import com.ximalaya.ting.android.live.host.manager.videofloatwindow.LiveVideoFloatWindowManager;
import com.ximalaya.ting.android.live.host.view.VideoPlayerView;
import com.ximalaya.ting.android.live.lib.stream.mediaplayer.manager.LiveMediaPlayerManager;
import com.ximalaya.ting.android.live.lib.stream.publish.XmLiveRoom;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.lifecycle.ViewStub2;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveav.lib.util.log.LiveLogUtil;
import com.ximalaya.ting.android.liveim.micmessage.constants.UserStatus;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * 视频播放组件，包括视频播放器、视频播放控制层
 *
 * <AUTHOR>
 */
public class VideoPlayerComponent extends LamiaComponent implements IVideoPlayerComponent, VideoPlayerView.OnLivePlayerViewCallback {

    private static final String TAG = "VideoPlayerComponent";

    private VideoPlayerView mVideoPlayerView;
    private String mPlayUrl;

    private RelativeLayout.LayoutParams mVideoPlayerParams;

    /**
     * 视频直播竖屏模式控制层
     */
    private PlayerWindowPortraitControllerComponent mPlayerPortraitControllerComponent;
    private PlayerWindowPortraitProxyClickView mPlayerPortraitProxyClickView;

    /**
     * 视频浮窗管理
     */
    private LiveVideoFloatWindowManager mLiveVideoFloatWindowManager;
    private PlayerConstants.ResolutionRatio mResolutionRatio = PlayerConstants.ResolutionRatio.LANDSCAPE_16_9;

    private int mVideoWidth;
    private int mVideoHeight;
    private int mCurOrientation;
    private PlayerWindowLandscapeControllerComponent mPlayerLandscapeControllerComponent;

    private @CdnStatus
    int mCdnStatus;

    private int mRoomStatus;
    private int mViewPagerPosition = -1;

    protected PlayerWindowPortraitControllerComponent getPlayerWindowPortraitControllerComponent() {
        if (mPlayerPortraitControllerComponent != null) {
            return mPlayerPortraitControllerComponent;
        }
        View view = findViewById(R.id.live_video_player_window_controller);
        if (view instanceof PlayerWindowPortraitControllerComponent) {
            mPlayerPortraitControllerComponent = (PlayerWindowPortraitControllerComponent) view;
            return mPlayerPortraitControllerComponent;
        }
        if (view instanceof ViewStub2) {
            mPlayerPortraitControllerComponent = new PlayerWindowPortraitControllerComponent(getActivity());
            ((ViewStub2) view).inflate(mPlayerPortraitControllerComponent, true);
        }
        return mPlayerPortraitControllerComponent;
    }

    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        runAfterViewInflate(() -> {
            mVideoPlayerView = findViewById(R.id.live_video_player);
            mVideoPlayerView.setLivePlayerViewCallback(this);
            mVideoPlayerView.setBizType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO);
            if (LiveVideoPlayerManager.getInstance().getVideoWidth() != 0) {
                updateUI(LiveVideoPlayerManager.getInstance().getVideoWidth(), LiveVideoPlayerManager.getInstance().getVideoHeight());
                UIStateUtil.hideViews(mPlayerLandscapeControllerComponent);
            } else {
                updateUI(1280, 720);
                UIStateUtil.hideViews(mPlayerLandscapeControllerComponent);
            }
            LiveViewPositionManager.getInstance().registerLayoutChangeListener(LiveViewPositionType.TYPE_VIDEO_PLAYER, mVideoPlayerView);
        });

        RoomModeManager.getInstance().getLiveModeData().observe(
                getFragment(),
                modeData -> {
                    if ((RoomModeManager.isPkMode() || RoomModeManager.isPkMicMode() || RoomModeManager.isGroupMicMode())
                            && mLiveVideoFloatWindowManager != null && getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
                        mLiveVideoFloatWindowManager.stopPlay();
                    }
                }
        );
    }

    @Override
    public void bindData(@NonNull PersonLiveDetail detail) {
        super.bindData(detail);
        if (getHostData() != null) {
            mRoomStatus = getHostData().getStatus();
        }
        runAfterViewInflate(() -> {
            if (canUpdateUi()) {
                // 音视直播，隐藏视频播放器、视频播放器控制层，停止视频直播拉流
                if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
                    UIStateUtil.hideViews(mVideoPlayerView);
                    UIStateUtil.hideViews(mPlayerPortraitControllerComponent);

                } else {
                    initVideo();
                    if (mVideoPlayerView != null) {
                        mVideoPlayerView.setRoomId(detail != null ? detail.getRoomId() : -1);
                    }
                    if (detail.getStatus() != PersonLiveBase.LIVE_STATUS_ING) {
                        UIStateUtil.hideViews(mPlayerPortraitControllerComponent);
                    }
                }
            }

            checkAndInitLiveVideoFloatWindowManager();
            switchAutoRotate(false);
        });
    }

    @Override
    public void onPhoneCallStateChanged(boolean isCalling) {
        super.onPhoneCallStateChanged(isCalling);

        // 挂断电话，麦下用户需要恢复播放
        if (!isCalling && getLiveMediaType() == LiveMediaType.TYPE_VIDEO && !isFromHostFragment()) {
            if (!isError()) {
                resumeLive();
            }
        }
    }

    /**
     * 视频直播初始化
     */
    private void initVideo() {
        setVideoPlayerWindowController(false);
    }


    @Override
    public void onVideoSizeChanged(int width, int height) {
        LiveHelper.Log.i(TAG, "onVideoSizeChanged:width=" + width + "  height=" + height);
        mVideoWidth = width;
        mVideoHeight = height;
        updateUI(width, height);
    }

    @Override
    public void receiveMicPkModeChanged(boolean usePkLayout, boolean useMicPkLayout, String ratioSize) {
        updateUI(mVideoWidth, mVideoHeight);
    }

    /**
     * 判断当全屏时，竖屏模式要旋转
     */
    private void judgeRotateWhenLand() {
        if (VideoPlayerBlock.isPortMode(mResolutionRatio)) {
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }
    }

    /**
     * 更新播放器布局
     *
     * @param width  宽度
     * @param height 高度
     */
    private void updateVideoPlayerViewLayout(int width, int height) {
        if (mVideoPlayerView == null) {
            return;
        }
        mVideoPlayerParams = VideoPlayerBlock.getLayoutParamsWhenPort(width, height, mResolutionRatio);
        if (mVideoPlayerParams == null) {
            return;
        }
        mVideoPlayerView.setLayoutParams(mVideoPlayerParams);
    }

    /**
     * 更新播放器背景色
     */
    private void updateVideoPlayerViewBackgroundColor() {
        if (mVideoPlayerView == null) {
            return;
        }
        if (LiveViewUtil.isLargeScreen()) {
            mVideoPlayerView.setBackgroundColor(Color.BLACK);
        } else {
            mVideoPlayerView.setBackgroundColor(Color.TRANSPARENT);
        }
    }

    /**
     * 更新播放器裁剪模式
     */
    private void updateVideoPlayerViewAsprctRatio() {
        if (mVideoPlayerView == null) {
            return;
        }
        mVideoPlayerView.setAspectRatio(VideoPlayerBlock.getAspect(mResolutionRatio));
    }


    private void updateUI(int width, int height) {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction == null) {
            return;
        }
        Logger.i(TAG, "updateUI  width:" + width + "  height:" + height);
        if (width == 0 || height == 0) {
            width = LiveVideoPlayerManager.getInstance().getVideoWidth();
            height = LiveVideoPlayerManager.getInstance().getVideoHeight();
        }
        mResolutionRatio = VideoPlayerBlock.matchResolutionRatio(width, height);
        if (mVideoPlayerView != null) {
            mVideoPlayerView.updateResolutionRatio(mResolutionRatio);
        }
        LiveVideoPlayerManager.getInstance().updateResolutionRatio(mResolutionRatio);
        interaction.updateResolutionRatioAndSize(mResolutionRatio, width, height);
        //预加载的时候interaction.getLiveMediaType()拿的是准确的
        if (interaction.getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
            UIStateUtil.showViews(mVideoPlayerView);
        }
        //如果hasRatioSize为true 直接调用changeLayout方法
        if (!LiveVideoPlayerManager.getInstance().getMicRatioSize().isEmpty()) {
            if (mVideoPlayerView != null) {
                LiveHelper.Log.i("ymc", "updateUI-- 命中RatioSize:" + LiveVideoPlayerManager.getInstance().getMicRatioSize());
                changeLayoutParamsByRoomMode(true, 0, true);
            }
            return;
        }

        if (LiveVideoPlayerManager.getInstance().isUsePkLayout()) {
            if (mVideoPlayerView != null) {
                LiveHelper.Log.i("ymc", "updateUI--设置pk的布局:" + "  " + LiveVideoPlayerManager.getInstance().isUsePkLayout() + width + height);
                changeLayoutParamsByRoomMode(true, 0, false);
            }
            return;
        }
        if (LiveVideoPlayerManager.getInstance().isUseMicPkLayout()) {
            LiveHelper.Log.i("ymc", "updateUI--设置多人的布局:" + width + height);
            changeLayoutParamsByRoomMode(false, 3, false);
            return;
        }
        boolean isLand = getActivity() != null && (getActivity().getRequestedOrientation() == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                || getActivity().getRequestedOrientation() == ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE);


        if (isLand) {
            judgeRotateWhenLand();
            return;
        }

        updateVideoPlayerViewLayout(width, height);
        updateVideoPlayerViewBackgroundColor();
        updateVideoPlayerViewAsprctRatio();


        setVideoPlayerWindowController(true);
        checkAndInitLiveVideoFloatWindowManager();
    }

    private void checkAndInitLiveVideoFloatWindowManager() {
        if (mLiveVideoFloatWindowManager == null) {
            mLiveVideoFloatWindowManager = new LiveVideoFloatWindowManager();
            mLiveVideoFloatWindowManager.setFloatWindowManagerCallback(new LiveVideoFloatWindowManager.IFloatWindowManagerCallback() {
                @Override
                public boolean isPkModeOrGroupMicMode() {
                    return RoomModeManager.isPkMode() || RoomModeManager.isPkMicMode() || RoomModeManager.isGroupMicMode();
                }

                @Override
                public BaseRoomFragment getCurrentFragment() {
                    return (BaseRoomFragment) getFragment();
                }
            });
        }
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            mLiveVideoFloatWindowManager.init(getActivity(), getLiveMediaType(), interaction.getVideoSizeRatio());
        }
        mLiveVideoFloatWindowManager.setCurrentResolutionRatio(mResolutionRatio);
    }

    /**
     * 设置视频播放器控制器
     */
    private void setVideoPlayerWindowController(boolean canShowIvFull) {
        if (mVideoPlayerView != null) {
            if (VideoPlayerBlock.isPortMode(mResolutionRatio)) {
                PlayerWindowPortraitControllerComponent playerWindowPortraitController = getPlayerWindowPortraitControllerComponent();
                if (playerWindowPortraitController == null) {
                    mPlayerPortraitControllerComponent = new PlayerWindowPortraitControllerComponent(getActivity());
                    mVideoPlayerView.setVideoPlayerWindowController(mPlayerPortraitControllerComponent, true);
                    if (getHostData() != null && getLiveMediaType() == LiveMediaType.TYPE_VIDEO && getHostData().getStatus() == PersonLiveBase.LIVE_STATUS_ING) {
                        UIStateUtil.showViews(mPlayerPortraitControllerComponent, mPlayerPortraitProxyClickView);
                    }
                    UIStateUtil.hideViews(mPlayerLandscapeControllerComponent);
                    mPlayerPortraitControllerComponent.updateCdnStatus(mCdnStatus);
                } else {
                    mVideoPlayerView.setVideoPlayerWindowController(playerWindowPortraitController, false);
                    if (getHostData() != null && getLiveMediaType() == LiveMediaType.TYPE_VIDEO && getHostData().getStatus() == PersonLiveBase.LIVE_STATUS_ING) {
                        UIStateUtil.showViews(playerWindowPortraitController);
                    }
                    UIStateUtil.hideViews(mPlayerLandscapeControllerComponent);
                    playerWindowPortraitController.updateCdnStatus(mCdnStatus);
                }
                switchAutoRotate(false);

                if (mPlayerPortraitProxyClickView == null) {
                    mPlayerPortraitProxyClickView = new PlayerWindowPortraitProxyClickView(getContext());
                    mVideoPlayerView.addVideoPlayerProxyClickController(mPlayerPortraitProxyClickView);
                }
            } else {
                if (mPlayerLandscapeControllerComponent == null) {
                    mPlayerLandscapeControllerComponent = new PlayerWindowLandscapeControllerComponent(getContext());
                }
                mVideoPlayerView.setVideoPlayerWindowController(mPlayerLandscapeControllerComponent, true);
                showToggleSwitch();
                UIStateUtil.hideViews(mPlayerPortraitControllerComponent);
                UIStateUtil.showViews(mPlayerLandscapeControllerComponent);
                mPlayerLandscapeControllerComponent.updateCdnStatus(mCdnStatus);
                mVideoPlayerView.updateIvFullLayout(LiveViewUtil.isLargeScreen());
                if (canShowIvFull) {
                    mVideoPlayerView.setIvFullScreenVisiable(View.VISIBLE);
                    //只有横屏视频直播才允许自动转屏
                    if (getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
                        switchAutoRotate(true);
                    }
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.finishFloatWindowPlay();
        }
    }

    @Override
    public void initInfoAndShow(String url, int playType) {
        if (!canUpdateUi()) {
            return;
        }
        mPlayUrl = url;
        mVideoPlayerView.initInfoAndShow(url, playType, mResolutionRatio);
        setVideoPlayerWindowController(false);
        if (VideoPlayerBlock.isPortMode(mResolutionRatio)) {
            UIStateUtil.showViews(mPlayerPortraitControllerComponent, mPlayerPortraitProxyClickView);
        }
    }

    @Override
    public void setPlayUrl(String url, int playType, PlayerConstants.ResolutionRatio resolutionRatio) {
        if (!canUpdateUi()) {
            return;
        }

        if (isPlaying() && mPlayUrl != null && mPlayUrl.equals(url)) {
            LiveHelper.Log.i("duruochen--preload", "视频直播已预加载播放中  setPlayUrl直接return");
            if (mVideoWidth != 0 && mVideoHeight != 0) {
                updateUI(mVideoWidth, mVideoHeight);
            }
            if (mVideoPlayerView != null) {
                mVideoPlayerView.hideLoading();
            }
            return;
        }
        boolean isPlayingThisRoom = isPlaying() && url != null && url.equals(LiveMediaPlayerManager.getInstance().getPlayUrl());
        mPlayUrl = url;
        mResolutionRatio = PlayerConstants.ResolutionRatio.LANDSCAPE_16_9;

        mVideoPlayerView.setVideoPath(url, playType, resolutionRatio);
        if (isPlayingThisRoom) {
            LiveHelper.Log.i("duruochen--preload", "当前房间视频直播已预加载播放中");
            if (mVideoWidth != 0 && mVideoHeight != 0) {
                updateUI(mVideoWidth, mVideoHeight);
            }
            if (mVideoPlayerView != null) {
                mVideoPlayerView.hideLoading();
            }
        }
    }


    @Override
    public void requestPlayMode(int requestMode) {
        if (mVideoPlayerView != null) {
            mVideoPlayerView.requestPlayMode(requestMode);
        }
    }

    @Override
    public boolean isPlaying() {
        return mVideoPlayerView.isPlaying();
    }

    @Override
    public void stop() {
        mVideoPlayerView.release();
    }

    @Override
    public void resumeLive() {
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.finishFloatWindowPlay();
        }
        if (TextUtils.isEmpty(mPlayUrl) || XmLiveRoom.sharedInstance(MainApplication.getMyApplicationContext()).isPublish()) {
            return;
        }
        mVideoPlayerView.resumeLive(mPlayUrl);
    }

    @Override
    public void start() {
        mVideoPlayerView.startPlay();
    }

    @Override
    public boolean isError() {
        return mVideoPlayerView.getCurrentPlayState() == PlayerConstants.PLAYSTATE_END ||
                mVideoPlayerView.getCurrentPlayState() == PlayerConstants.PLAYSTATE_ERROR;
    }

    @Override
    public void hidePlayer() {
        mVideoPlayerView.setVisibility(View.INVISIBLE);
    }

    @Override
    public void setLiveFinish(boolean isLiveFinish) {
        if (VideoPlayerBlock.isPortMode(mResolutionRatio) && isLiveFinish) {
            //竖屏单独隐藏控制层，横屏情况下 控制层是在播放器内的
            ViewStatusUtil.setVisible(View.GONE, mPlayerPortraitControllerComponent, mPlayerPortraitProxyClickView);
        }
        mVideoPlayerView.setLiveFinish(isLiveFinish);
    }

    @Override
    public void changeLayoutParamsByRoomMode(boolean isPking, int micAnchorCount, boolean hasRatioSize) {
        LiveLogUtil.log("duruochen--micpk", "changeLayoutParamsByRoomMode:isPking=" + isPking + " micAnchorCount=" + micAnchorCount + " isMianThread:" + (Looper.getMainLooper() == Looper.myLooper()));
        LiveLogUtil.log("duruochen--micpk", "changeLayoutParamsByRoomMode:isPking=" + isPking + " micAnchorCount=" + micAnchorCount + Log.getStackTraceString(new Throwable()));

        //单独处理 hasRatioSize = true逻辑 ，因为getMicRatioSize 可以获取到对应尺寸，不需要判断连麦主播1个数和 pk状态
        if (hasRatioSize) {
            if (mVideoPlayerView != null) {
                mVideoPlayerView.setLayoutParams(VideoPlayerBlock.getPkLayoutParams(true,getLiveMediaType() == LiveMediaType.TYPE_AUDIO));
                mVideoPlayerView.setAspectRatio(PlayerConstants.AR_ASPECT_FILL_PARENT);
                mVideoPlayerView.setIvFullScreenVisiable(View.GONE);
                return;
            }
        }

        //普通pk中或两人连麦pk中 （感觉没有micAnchorCount == 2的情况）
        if (isPking || micAnchorCount == 2) {
            if (mVideoPlayerView != null) {
                LiveHelper.Log.i("ymc", "VideoPlayerComponent changeLayoutParamsByRoomMode=2: " + LiveVideoPlayerManager.getInstance().getMicRatioSize());
                //判断如果有RatioSize 则使用RatioSize  此处应该为 2/3
                mVideoPlayerView.setLayoutParams(VideoPlayerBlock.getPkLayoutParams(false, getLiveMediaType() == LiveMediaType.TYPE_AUDIO));
                mVideoPlayerView.setAspectRatio(PlayerConstants.AR_ASPECT_FILL_PARENT);
                mVideoPlayerView.setIvFullScreenVisiable(View.GONE);
            }
        }
        // 主播间连麦pk中
        else if (micAnchorCount > 2) {
            if (mVideoPlayerView != null) {
                LiveHelper.Log.i("ymc", "VideoPlayerComponent changeLayoutParamsByRoomMode>2: " + LiveVideoPlayerManager.getInstance().getMicRatioSize());
                //判断如果有RatioSize 则使用RatioSize  此处应该为 8/9
                mVideoPlayerView.setLayoutParams(VideoPlayerBlock.getMultiAnchorMicLayoutParams(false));

                mVideoPlayerView.setAspectRatio(PlayerConstants.AR_ASPECT_FILL_PARENT);
                mVideoPlayerView.setIvFullScreenVisiable(View.GONE);
            }
        } else {
            if (mVideoWidth == 0 || mVideoHeight == 0) {
                mVideoWidth = LiveVideoPlayerManager.getInstance().getVideoWidth();
                mVideoHeight = LiveVideoPlayerManager.getInstance().getVideoHeight();
            }
            onVideoSizeChanged(mVideoWidth, mVideoHeight);
        }
    }

    @Override
    public void forbidFloatWindow() {
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.forbidFloatWindow();
        }
    }

    @Override
    public void onOrientationChange(int orientation, boolean isSameOrientation) {
        if (!canUpdateUi()) {
            return;
        }
        if (mCurOrientation != orientation && mVideoPlayerView != null && mVideoPlayerView.getPlayerView() != null) {
            Logger.i(TAG, "onOrientationChange, orientation = " + orientation);
            mVideoPlayerView.getPlayerView().setVideoSize(mVideoWidth, mVideoHeight);
        }
        mCurOrientation = orientation;
        if (mLiveVideoFloatWindowManager != null && getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
            updateUI(mVideoWidth, mVideoHeight);
        }
    }

    @Override
    public void showToggleSwitch() {
        if (mVideoPlayerView != null) {
            mVideoPlayerView.showLandScapeToggleSwitch();
        }
    }


    @Override
    public void setVideoDecorationVisibility(int visibility) {
        if (mVideoPlayerView != null) {
            mVideoPlayerView.setVideoDecorationVisibility(visibility);
        }
    }

    @Override
    public void updateCdnStatus(int cdnStatus) {
        if (!canUpdateUi()) {
            return;
        }
        mCdnStatus = cdnStatus;
        //只有直播中状态才展示"主播正在调试"
        if (mRoomStatus == PersonLiveBase.LIVE_STATUS_ING && mVideoPlayerView != null) {
            mVideoPlayerView.updateCdnStatus(cdnStatus);
        }
        LiveVideoPlayerManager.getInstance().updateCdnStatus(cdnStatus);
    }

    @Override
    public void updateRoomStatus(int roomStatus) {
        mRoomStatus = roomStatus;
    }

    @Override
    public void onPageSelected(int position) {
        if (mVideoPlayerView != null && mVideoPlayerView.getPlayerView() != null) {
            mVideoPlayerView.getPlayerView().setAlpha(0);
            if (mViewPagerPosition == position && getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
                //个播视频直接跳到另一个个播，需要清空掉视频最后一帧画面
                mVideoPlayerView.resetTextureView();
            }
            mViewPagerPosition = position;
        }
    }

    @Override
    public void onStartFullScreenPlay() {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            interaction.onStartFullScreenPlay();
        }
    }

    @Override
    public void onStopFullScreenPlay() {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            interaction.onStopFullScreenPlay();
        }
    }


    @Override
    public void onHideView(boolean isLockScreen) {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            interaction.hideView();
        }
    }

    @Override
    public void onShowView() {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            interaction.showView();
        }
    }

    @Override
    public void playFinished() {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            interaction.playFinished();
        }
    }

    @Override
    public void onDestroy() {
        Logger.i(TAG, "onDestroy");
        if (mVideoPlayerView != null) {
            LiveViewPositionManager.getInstance().unregisterLayoutChangeListener(LiveViewPositionType.TYPE_VIDEO_PLAYER, mVideoPlayerView);
            mVideoPlayerView.setLivePlayerViewCallback(null);
            mVideoPlayerView.unInit();
        }
        if (mLiveVideoFloatWindowManager != null) {
            mLiveVideoFloatWindowManager.setFloatWindowManagerCallback(null);
            mLiveVideoFloatWindowManager.release();
            mLiveVideoFloatWindowManager = null;
        }


        //切换回一般竖屏的模式
        if (mVideoPlayerView != null) {
            switchAutoRotate(false);
            mVideoPlayerView.switchVideoScreenModel(PlayerConstants.PLAYMODE_WINDOW,
                    ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }

        super.onDestroy();
    }

    @Override
    public void onSwitchRoom(long newRoomId, Bundle newArgs) {
        if (getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
            if (mLiveVideoFloatWindowManager != null) {
                mLiveVideoFloatWindowManager.release();
            }
            mPlayUrl = null;
        }
        mRoomStatus = 0;
        super.onSwitchRoom(newRoomId, newArgs);

    }

    @Override
    public void dispatchClickEvent(float x, float y, boolean isLandController) {
        IVideoPlayerComponentInteraction interact = getComponentInteractionSafety(
                IVideoPlayerComponentInteraction.class
        );
        if (interact != null) {
            interact.dispatchVideoControllerClickEvent(x, y,isLandController);
        }
    }

    private int mLastSensorOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
    private OrientationEventListener mOrientationEventListener;
    private final boolean mLockScreen = false;

    /**
     * 是否启动视频的自动屏幕旋转
     *
     * @param enable 是否启动
     */
    private void switchAutoRotate(boolean enable) {

        if (enable) {
            startOrientationEventListener();
        } else {
            stopOrientationEventListener();
        }

    }


    private void startOrientationEventListener() {
        if (getContext() != null) {
            if (mOrientationEventListener == null) {
                mOrientationEventListener = new OrientationEventListener(getContext(),
                        SensorManager.SENSOR_DELAY_UI) {
                    @Override
                    public void onOrientationChanged(int rotation) {
                        Logger.i(TAG, String.valueOf(rotation));
                        if (rotation == OrientationEventListener.ORIENTATION_UNKNOWN) {
                            return;  //手机平放时，检测不到有效的角度
                        }
                        if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
                            return;
                        }
                        int newOrientation;
                        int screenPlayModel;
                        // 旋转到足够的角度才做切换
                        if (rotation > 350 || rotation < 10) { //0度
                            newOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                            screenPlayModel = PlayerConstants.PLAYMODE_WINDOW;
                        } else if (rotation > 80 && rotation < 100) { //90度
                            newOrientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
                            screenPlayModel = PlayerConstants.PLAYMODE_FULLSCREEN;
                        } else if (rotation > 170 && rotation < 190) { //180度
                            newOrientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
                            screenPlayModel = PlayerConstants.PLAYMODE_WINDOW;
                        } else if (rotation > 260 && rotation < 280) { //270度
                            newOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                            screenPlayModel = PlayerConstants.PLAYMODE_FULLSCREEN;
                        } else {
                            return;
                        }
                        // 当用户旋转过方向后才做处理，以解决用户点按钮切换无效的问题
                        if (newOrientation != mLastSensorOrientation) {


                            if (BaseApplication.getTopActivity() != null
                                    && newOrientation != BaseApplication.getTopActivity().getRequestedOrientation()
                                    && isRotateEnable()
                                    && !mLockScreen) {

                                if (mVideoPlayerView != null) {
                                    mVideoPlayerView.switchVideoScreenModel(screenPlayModel, newOrientation);
                                }

                                Logger.d(TAG, "转向：" + newOrientation);
                            }

                            mLastSensorOrientation = newOrientation;
                        }

                    }
                };
            }
        }
        if (mOrientationEventListener != null && mOrientationEventListener.canDetectOrientation()) {
            mOrientationEventListener.enable();
        }
    }

    private void stopOrientationEventListener() {
        if (mOrientationEventListener != null) {
            mOrientationEventListener.disable();
        }
    }

    /**
     * 设备的方向是否可以旋转
     * 判断标准：1 方向没有锁定 2 没有处于pk状态
     *
     * @return 判断值
     */
    private boolean isRotateEnable() {

        //1 方向没有锁定
        try {
            int isRotate = Settings.System.getInt(MainApplication.getMyApplicationContext().getContentResolver(), Settings.System.ACCELEROMETER_ROTATION);
            // 如果用户禁用掉了重力感应就直接return
            if (isRotate == 0) {
                return false;
            }
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction == null) {
            return false;
        }

        boolean isNotMicing = LiveClientManager.getInstance().getLiveMicService().getUserStatus() == UserStatus.USER_STATUS_OFFLINE;

        //2 没有处于pk状态 or 连麦状态
        return !interaction.isPking() && !interaction.isGroupMicOrMicPking() && isNotMicing;
    }

    @Override
    public void setPlayerFailVisible(boolean isVisible) {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (interaction != null) {
            interaction.setPlayerFailVisible(isVisible);
        }
    }


}
