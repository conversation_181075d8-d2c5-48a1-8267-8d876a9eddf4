package com.ximalaya.ting.android.liveaudience.fragment.room;

import static com.ximalaya.ting.android.live.common.chatlist.trace.LiveChatItemEventTraceKt.trackLiveGreetViewShow;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HOST_TASK;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_INPUT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_OFFICIAL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_RECOMMEND;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_VIDEO_PLAYER;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_VOTE;
import static com.ximalaya.ting.android.live.lib.chatroom.entity.CommonRoomSpecialMode.ROOM_MODE_FUNCTION_DEFAULT;
import static com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansClubUpdateMessage.IFanClubMessageType.TYPE_JOIN;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.common.lib.logger.ConnectLogUtilWrapper;
import com.ximalaya.ting.android.common.lib.logger.LamiaBizLog;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.ImageMultiPickFragment;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.image.ImgItem;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.VerticalSlideUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.dialog.SimpleDialog;
import com.ximalaya.ting.android.im.xchat.util.UnBoxUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.biz.mode.data.PrivateChatViewModel;
import com.ximalaya.ting.android.live.biz.pia.entity.CommonPiaStatusRsp;
import com.ximalaya.ting.android.live.biz.pia.panel.manager.XmPiaBgmPlayerManager;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatItemViewType;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatListViewConstant;
import com.ximalaya.ting.android.live.common.component.playbacksetting.manager.LiveTerminateManager;
import com.ximalaya.ting.android.live.common.dialog.base.LiveBaseDialogFragment;
import com.ximalaya.ting.android.live.common.dialog.web.ProvideForH5CustomerDialogFragment;
import com.ximalaya.ting.android.live.common.input.IKeyboardHostFragment;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.common.lib.base.constants.IRoomH5RankAndOnlineTab;
import com.ximalaya.ting.android.live.common.lib.base.constants.IXmAppIdConstants;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveCommonUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.dialog_queue.LiveDialogFragmentManager;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.base.viewmodel.FansClubViewModel;
import com.ximalaya.ting.android.live.common.lib.base.viewmodel.GuardViewModel;
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansProgressBean;
import com.ximalaya.ting.android.live.common.lib.entity.LiveGuardGuideBean;
import com.ximalaya.ting.android.live.common.lib.entity.LiveNewPkAnimMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialNoticeMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialSwitchMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialTimerMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRemindPkMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRoomAvatarDecorate;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType;
import com.ximalaya.ting.android.live.common.lib.entity.OfficialLiveInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.ai.LiveAnchorAiConfigInfo;
import com.ximalaya.ting.android.live.common.lib.entity.ai.LiveAnchorAiHelperInfo;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.LivePremiereMsg;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereInfo;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereMsgAction;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereStatus;
import com.ximalaya.ting.android.live.common.lib.entity.rank.FirstChargeNotify;
import com.ximalaya.ting.android.live.common.lib.gift.anim.SeatGiftManager;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.manager.LiveGlobalDispatcher;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveUserTagsManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveAbtestUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGiftDrawableCache;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.WealthIconCacheUtil;
import com.ximalaya.ting.android.live.common.lib.utils.asr.LiveAsrManager;
import com.ximalaya.ting.android.live.common.lib.utils.display.LiveDisplayUtilKt;
import com.ximalaya.ting.android.live.common.sound.effect.pia.EffectDataHolder;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.clearScreen.LiveClearScreenManager;
import com.ximalaya.ting.android.live.common.view.dialog.LiveCommonAlertDialog;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.birthday.IBirthDayComponent;
import com.ximalaya.ting.android.live.host.components.chatlist.IBaseChatListComponent;
import com.ximalaya.ting.android.live.host.components.hotword.IHotWordComponent;
import com.ximalaya.ting.android.live.host.components.input.IInputPanelComponent;
import com.ximalaya.ting.android.live.host.components.privatechat.CommonPrivateChatComponent;
import com.ximalaya.ting.android.live.host.components.recommend.IRecommendLiveComponent;
import com.ximalaya.ting.android.live.host.components.rightarea.IRoomRightAreaComponent;
import com.ximalaya.ting.android.live.host.components.roombackground.IRoomBackgroundComponent;
import com.ximalaya.ting.android.live.host.components.sellgoods.ISellGoodsComponent;
import com.ximalaya.ting.android.live.host.components.subscriberoom.ISubscribeRoomComponent;
import com.ximalaya.ting.android.live.host.components.tuning.ICommonTuningComponent;
import com.ximalaya.ting.android.live.host.components.vote.ILiveVoteComponent;
import com.ximalaya.ting.android.live.host.constant.LiveBundleKeyConstants;
import com.ximalaya.ting.android.live.host.dialog.LiveDialogUtil;
import com.ximalaya.ting.android.live.host.dialog.manage.LiveManagementFragment;
import com.ximalaya.ting.android.live.host.fragment.room.BaseRoomFragment;
import com.ximalaya.ting.android.live.host.manager.playupload.PremierePlayStatisticsManager;
import com.ximalaya.ting.android.live.host.presenter.listener.OpenGameMessageReveivedListener;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.live.host.utils.checkwindow.filter.ConditionFilter;
import com.ximalaya.ting.android.live.host.utils.checkwindow.filter.recall.BottomBannerConditionFilter;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonBirthInfoMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatAnchorMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatBullet;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGetRedPacketMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftComboOverMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatHotTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatRedPacketMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatTimedRedPacketMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUser;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUserJoinMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonRoomSpecialMode;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonWithdrawChatMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.ReceiverOpenGiftListChatMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonBusinessMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAlbumInfoMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansClubUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNobleClubUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNoticeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomRuleInfoUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomSkinUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomStatusChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomTopicUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonCouponShowViewStatusMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGetNewCouponMsg;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IOpenGameMessageDispatcherManager;
import com.ximalaya.ting.android.live.lib.chatroom.manager.impl.OpenGameMessageDispatcherManagerImpl;
import com.ximalaya.ting.android.live.lib.liveroomalbum.LiveMediaPreviewFragment;
import com.ximalaya.ting.android.live.lib.liveroomalbum.bean.MediaPreviewData;
import com.ximalaya.ting.android.live.lib.liveroomalbum.type.RoomAlbumMediaType;
import com.ximalaya.ting.android.live.lib.livetopic.AnnounceDataCache;
import com.ximalaya.ting.android.live.lib.livetopic.bean.LiveAreaConfigListBean;
import com.ximalaya.ting.android.live.lib.livetopic.bean.MediaInfo;
import com.ximalaya.ting.android.live.lib.redenvelope.model.IRedPacketMessage;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.lifecycle.IComponentStateCallBack;
import com.ximalaya.ting.android.liveaudience.components.base.header.IAbsHeaderComponent;
import com.ximalaya.ting.android.liveaudience.components.bottombar.IBottomBarComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.AVChatListMsgProducer;
import com.ximalaya.ting.android.liveaudience.components.chatlist.guide.IAVChatListGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceComponentHost;
import com.ximalaya.ting.android.liveaudience.components.coupon.ICouponComponent;
import com.ximalaya.ting.android.liveaudience.components.danmu.LiveBarrageComponent;
import com.ximalaya.ting.android.liveaudience.components.enterroom.LiveAVEnterAndGiftPopComponent;
import com.ximalaya.ting.android.liveaudience.components.giftanimation.ICraftBoxAnimateComponent;
import com.ximalaya.ting.android.liveaudience.components.header.IAudienceHeaderComponent;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IInteractivePlayComponent;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IPiaModeComponent;
import com.ximalaya.ting.android.liveaudience.components.mic.IAudienceMicComponent;
import com.ximalaya.ting.android.liveaudience.components.mic.IMicBaseComponent;
import com.ximalaya.ting.android.liveaudience.components.official.IOfficialComponent;
import com.ximalaya.ting.android.liveaudience.components.premiere.countdown.IPremiereCountDownComponent;
import com.ximalaya.ting.android.liveaudience.components.task.ITaskComponent;
import com.ximalaya.ting.android.liveaudience.components.videoplayer.IVideoPlayerComponent;
import com.ximalaya.ting.android.liveaudience.data.model.friends.FriendsMicInfoWrapper;
import com.ximalaya.ting.android.liveaudience.data.model.liveplay.AnchorLiveData;
import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive;
import com.ximalaya.ting.android.liveaudience.dialog.recall.RedPacketAndLuckyBagAndVoteConditionFilter;
import com.ximalaya.ting.android.liveaudience.fragment.finish.LiveAudienceFinishFragment;
import com.ximalaya.ting.android.liveaudience.fragment.topic.LiveRoomPreviewInfoDialogFragment;
import com.ximalaya.ting.android.liveaudience.fragment.topic.LiveTopicInfoFragment;
import com.ximalaya.ting.android.liveaudience.giftModule.loader.LiveGiftLoader;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeMicStateManager;
import com.ximalaya.ting.android.liveaudience.manager.love.util.LoveModeManagerInjectUtil;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.IPkMessageDispatcherManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.IPkMessageManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkHelper;
import com.ximalaya.ting.android.liveaudience.manager.pk.impl.PkMessageDispatcherManagerImpl;
import com.ximalaya.ting.android.liveaudience.manager.pk.impl.PkMessageManagerImpl;
import com.ximalaya.ting.android.liveaudience.manager.pk.util.PkModeManagerInjectUtil;
import com.ximalaya.ting.android.liveaudience.manager.roomcore.AudienceRoomCore;
import com.ximalaya.ting.android.liveaudience.mvp.ILiveBasePresenterView;
import com.ximalaya.ting.android.liveaudience.mvp.LiveAudienceRoomPresenter;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.liveaudience.util.premiere.PremiereRoomUtil;
import com.ximalaya.ting.android.liveaudience.view.dialog.AVUserInfoCardDialog;
import com.ximalaya.ting.android.liveaudience.view.dialog.AnonymityReportDialog;
import com.ximalaya.ting.android.liveaudience.view.dialog.LiveVersionUpdateDialog;
import com.ximalaya.ting.android.liveaudience.view.giftpop.GiftListManager;
import com.ximalaya.ting.android.liveaudience.view.mode.IRoomModeFragment;
import com.ximalaya.ting.android.liveaudience.viewmodel.AIEnableStatusViewModel;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;


/**
 * 音视频直播间基础页面，集成最基础、不易改动的逻辑和模块
 * <p>
 * 1.消息列表，各端基本相同
 * 2.右侧区域
 * 3.礼物展示区域
 *
 * <AUTHOR>
 */
public abstract class LiveRoomBaseFragment extends BaseRoomFragment<LiveAudienceRoomPresenter>
        implements ILiveBasePresenterView
        , IKeyboardHostFragment
        , IFragmentFinish {

    private static final String TAG = "LiveRoomBaseFragment";

    /**
     * 直播间详情实体类
     */
    protected PersonLiveDetail mRoomDetail;


    protected PremiereInfo mPremiereInfo;

    /**
     * 获取的毛玻璃 drawable
     */
    protected Drawable mBlurDrawable;

    /**
     * 话题
     **/
    protected String mTopicContent = "";

    /**
     * 热门话题Id
     */
    protected long mHotTopicId = 0;

    /**
     * 热门话题标题
     */
    protected String mHotTopicTitle = "";


    /**
     * 房间的进入方式，是否非上下滑进入此房间
     */
    protected boolean mCommonStartRoom = false;

    /**
     * Pk模式信令管理
     */
    protected IPkMessageManager mPkMessageManager;

    /**
     * 弹幕互动游戏信令管理
     */
    private IOpenGameMessageDispatcherManager mIOpenGameMessageDispatcherManager;

    /**
     * Pk模式消息分发管理
     */
    protected IPkMessageDispatcherManager mPkMessageDispatcherManager;

    /**
     * 管理员列表弹窗，弱引用
     */
    protected WeakReference<VerticalSlideUtil.VerticalSlideWrapper<LiveManagementFragment>>
            mLiveAdminListFraWrapper;

    /**
     * 禁言用户列表弹窗，弱引用
     */
    protected WeakReference<VerticalSlideUtil.VerticalSlideWrapper<LiveManagementFragment>>
            mLiveBanListFraWrapper;

    private LiveUserInfo mMyUserInfo;
    protected FansClubViewModel mFansClubViewModel;
    protected GuardViewModel mGuardViewModel;
    /**
     * AI助手信息 ViewModel
     */
    protected AIEnableStatusViewModel mAIEnableStatusViewModel;

    protected AVUserInfoCardDialog mUserPop;

    protected ProvideForH5CustomerDialogFragment mGuardDialogFragment;

    protected ProvideForH5CustomerDialogFragment mPkPredictEntryFragment;

    protected ProvideForH5CustomerDialogFragment mPkPredictResultFragment;

    /**
     * 话题或预约半屏弹窗
     */
    private VerticalSlideUtil.VerticalSlideWrapper<Fragment>
            mTopicAndNoticDialogWrapper;

    /**
     * 直播推流媒体类型：音频or视频
     */
    protected int mLiveMediaType = LiveMediaType.TYPE_AUDIO;

    private boolean isAgainConnectRoom;

    private LiveRoomLocalBroadcastReceiver mLiveVideoRoomLocalBroadcastReceiver;

    /**
     * 私信ViewModel
     */
    protected PrivateChatViewModel privateChatViewModel;

    private OpenGameMessageReveivedListener mOpenGameMessageReveivedListener;

    /**
     * 到招呼任务
     */
    private Runnable mGreetRunnable;
    /**
     * true表示首映室直播间 , 没有或者false表示不是
     */
    protected boolean premiereFlag;

    protected long getLiveRecordId() {
        if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return -1;
        }
        return mRoomDetail.getLiveRecordInfo().id;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        registerLocalReceiver();
    }

    @Override
    public void onPause() {
        unregisterLocalReceiver();

        super.onPause();
    }


    @Override
    protected void initBizId() {
        mBusinessId = IBusinessIdConstants.BIZ_ID_AUDIO_PERSONAL;
    }

    @Override
    protected void initBizManagers() {
        super.initBizManagers();

        mPkMessageManager = new PkMessageManagerImpl(mConnectionManager);
        addManager(IPkMessageManager.NAME, mPkMessageManager);
        mPkMessageDispatcherManager = new PkMessageDispatcherManagerImpl(mConnectionManager);
        addManager(IPkMessageDispatcherManager.NAME, mPkMessageDispatcherManager);

        mIOpenGameMessageDispatcherManager = new OpenGameMessageDispatcherManagerImpl(mConnectionManager);
        addManager(IOpenGameMessageDispatcherManager.NAME, mIOpenGameMessageDispatcherManager);

        mOpenGameMessageReveivedListener = new OpenGameMessageReveivedListener(this);
        mIOpenGameMessageDispatcherManager.addOpenGameMessageReceivedListener(mOpenGameMessageReveivedListener);

        LiveLamiaUtil.setMicServiceReference(getAvService());

        initModeManager();
    }

    @Override
    @Nullable
    public AudienceRoomCore getRoomCore() {
        return (AudienceRoomCore) super.getRoomCore();
    }

    protected void initModeManager() {
        if (getRoomCore() != null) {
            RoomModeManager.getInstance().setRmMessageManager(getRoomCore().getRmMessageManager());
            LoveModeManagerInjectUtil.injectLoveMessageManager(getRoomCore().getLoveMessageManager());
            LoveModeManagerInjectUtil.injectLoveMessageDispatcherManager(getRoomCore().getLoveMessageDispatcherManager());
        } else {
            CustomToast.showDebugFailToast("initModeManager getRoomnCore null");
        }

        PkModeManagerInjectUtil.injectPkMessageManager(mPkMessageManager);
        PkModeManagerInjectUtil.injectPkDispatcherManager(mPkMessageDispatcherManager);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Bundle arguments = getArguments();
        if (arguments != null) {
            mCommonStartRoom = arguments.getBoolean(
                    LiveBundleKeyConstants.KEY_START_ROOM_COMMON, false
            );
        }
        mFansClubViewModel = new ViewModelProvider(this).get(FansClubViewModel.class);
        mAIEnableStatusViewModel = new ViewModelProvider(this).get(AIEnableStatusViewModel.class);
        mGuardViewModel = new ViewModelProvider(this).get(GuardViewModel.class);
        mAIEnableStatusViewModel.getMAIEnableStatusData().observe(this, this::onGetEnableAiStatus);
        mFansClubViewModel.getFansClubLiveData().observe(this, this::onFansClubInfoChange);
        mGuardViewModel.getGuardLiveData().observe(this, this::onGuardInfoChange);
    }

    private void onGetEnableAiStatus(LiveAnchorAiHelperInfo aiInfo) {
        mAnchorAiInfo = aiInfo;
        if (mAnchorAiInfo != null && mAnchorAiInfo.isEnableFlag()) {
            CommonRequestForLive.getAiConfigInfo(new IDataCallBack<LiveAnchorAiConfigInfo>() {
                @Override
                public void onSuccess(@Nullable LiveAnchorAiConfigInfo data) {
                    if (data != null && mAnchorAiInfo != null) {
                        mAnchorAiInfo.setAnchorAiConfigInfo(data);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    mAnchorAiInfo.setAnchorAiConfigInfo(null);
                }
            });
        }
    }

    public void onFansClubInfoChange(LiveUserInfo.FansClubVoBean fansClubVoBean) {

    }

    public void onGuardInfoChange(LiveUserInfo.GuardGroupVo guardGroupVo) {

    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRootView = findViewById(R.id.live_lamia_room_container);
        super.initUi(savedInstanceState);
    }

    @Override
    protected void initMyUi(Bundle savedInstanceState) {
        try {
            Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction().setAllowUseMobileNetwork(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void loadData() {
        super.loadData();
        AnchorLiveData.getInstance().setRoomId(mRoomId);
        AnchorLiveData.getInstance().setPlaySource(mPlaySource);
    }

    @Override
    protected void loadMyData() {
        super.loadMyData();
    }

    @Override
    public void switchRoom(long roomId, Bundle bundle) {
        // 上下滑切换相同的房间
        mCommonStartRoom = false;
        bundle.putBoolean(LiveBundleKeyConstants.KEY_START_ROOM_COMMON, false);
        EffectDataHolder.Companion.reset();
        if (premiereFlag) {
            setPremiereRoomFlag(false);
        }
        super.switchRoom(roomId, bundle);
    }

    @Override
    protected void switchToNewRoom(long roomId) {
        HandlerManager.removeCallbacks(mGreetRunnable);
        RoomModeManager.getInstance().setRoomId(roomId);
        LoveModeManagerInjectUtil.injectRoomId(roomId);
        PkModeManagerInjectUtil.injectRoomId(roomId);

        LiveGlobalDispatcher.RoomInfo roomInfo = new LiveGlobalDispatcher.RoomInfo();
        roomInfo.roomId = roomId;
        LiveGlobalDispatcher.getInstance().notifyRoomSwitched(roomInfo);
        super.switchToNewRoom(roomId);
        mRoomDetail = null;
        mPremiereInfo = null;
        premiereFlag = false;
        isAgainConnectRoom = false;
        mTopicContent = "";
        mHotTopicTitle = "";
        mHotTopicId = 0;
        mBlurDrawable = null;
        if (mPresenter != null) {
            mPresenter.mCurrentUserIsAdmin = false;
        }

        Logger.i(TAG, "switchToNewRoom, new roomId = " + roomId);
    }

    @Override
    public void onRequestRoomDetailSuccess(final ILiveRoomDetail roomDetail) {
        super.onRequestRoomDetailSuccess(roomDetail);
        if (!canUpdateUi() || roomDetail == null) {
            if (mRoomDetail != null && mRoomDetail.getRoomId() != mRoomId) {
                mRoomDetail = null;
            }
            return;
        }
        if (roomDetail.getRoomId() != mRoomId) {
            return;
        }

        mRoomDetail = (PersonLiveDetail) roomDetail;

        setOfficialRoomFlag(officialRoomFlag);
        premiereFlag = mRoomDetail.isPremiereFlag();
        if (ConstantsOpenSdk.isDebug) {
            Log.e("LivePremiere", "首映室 flag " + premiereFlag);
        }
        if (premiereFlag) {
            setPremiereRoomFlag(premiereFlag);
        }

        AnchorLiveData.getInstance().setDetailInfo(mRoomDetail);

        RoomModeManager.getInstance().setRoomId(mRoomId);
        RoomModeManager.getInstance().setFromAudience(this instanceof LiveAudienceRoomFragment);

        LoveModeManagerInjectUtil.injectRoomId(mRoomId);
        LoveModeManagerInjectUtil.injectHostNickname(mRoomDetail.getHostNickname());
        PkModeManagerInjectUtil.injectRoomId(mRoomId);
        PkModeManagerInjectUtil.injectHostNickname(mRoomDetail.getHostNickname());

        RoomModeManager.getInstance().setPlaySource(mPlaySource);
        RoomModeManager.getInstance().setItemId(itemId);

        if (mRoomDetail != null && mRoomDetail.getLiveRecordInfo() != null) {
            mTopicContent = mRoomDetail.getLiveRecordInfo().description;
        }
        if (mRoomDetail != null && mRoomDetail.getLiveRecordInfo() != null) {
            mHotTopicTitle = mRoomDetail.getLiveRecordInfo().hotTopicTitle;
            mHotTopicId = mRoomDetail.getLiveRecordInfo().tagId;
        }

        // 记录音视频直播间详情信息，可供埋点使用
        LiveRecordInfoManager.getInstance().setLiveRecordInfo(mRoomDetail, mBusinessId);
        LiveRecordInfoManager.getInstance().setLiveAnchor(isFromHostFragment());
        LiveRecordInfoManager.getInstance().setOfficialRoom(officialRoomFlag);
        LiveRecordInfoManager.getInstance().setPremiereRoom(premiereFlag);
        getWaitComponent(AudienceCompConfig.COMPONENT_SELL_GOOD, new IComponentStateCallBack<ISellGoodsComponent>() {
            @Override
            public void onCreated(@NonNull ISellGoodsComponent component) {
                if (roomDetail == null) {
                    return;
                }
                component.setIsLiving(roomDetail.getStatus() == PersonLiveBase.LIVE_STATUS_ING);
            }
        });

        if (mRoomDetail != null) {
            onCurrentAnchorUserInfo(mRoomDetail.getLiveAnchorInfo());
        }
    }

    @Override
    public void onCurrentLoginUserInfo(LiveUserInfo object) {
        super.onCurrentLoginUserInfo(object);
        mMyUserInfo = object;
        if (mRoomDetail != null) {
            mRoomDetail.handleUserSelfInfo(object);
            if (object.getFansClubInfo() != null) {
                mFansClubViewModel.getFansClubLiveData().setValue(object.getFansClubInfo());
            }
            if (object.getGuardGroupVo() != null) {
                mGuardViewModel.getGuardLiveData().setValue(object.getGuardGroupVo());
            }
            getWaitComponent(AudienceCompConfig.COMPONENT_FRIEND_MODE, new IComponentStateCallBack<IInteractivePlayComponent>() {
                @Override
                public void onCreated(@NonNull IInteractivePlayComponent component) {
                    component.currentUserUpdate(mRoomDetail);
                }
            });
        }
    }

    @Override
    public void dismissBottomBarMoreActionPanel() {

    }

    @Override
    protected void onDisconnectChatRoom() {
        RoomModeManager.getInstance().onDisconnectChatRoom();
        LivePkHelper.getInstance().onDisconnectChatRoom();

        Logger.i(TAG, "onDisconnectChatRoom, roomId = " + mRoomId);
    }

    @Override
    protected void onConnectedChatRoom() {
        AudienceRoomCore room = getRoomCore();
        if (room != null && room.isReenterRoom()) {
            roomCoreLogFile(" onConnectedChatRoom,此房间时最小化后恢复的，需要立即获取当前房间模式");
            RoomModeManager.getInstance().startQueryRoomModeImmediately();
        } else {
            roomCoreLogFile(" onConnectedChatRoom,RoomModeManager onConnectChatRoom");
            RoomModeManager.getInstance().onConnectChatRoom();
        }
        LivePkHelper.getInstance().onConnectChatRoom();
        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.onReconnectRoom();
        }
        Logger.i(TAG, "onReconnectChatRoom, roomId = " + mRoomId);
        if (isAgainConnectRoom) {
            requestPremiereInfo();
        }
        isAgainConnectRoom = true;
    }

    @Override
    protected void onKickOutChatRoom() {
        super.onKickOutChatRoom();
        RoomModeManager.getInstance().onKickOutChatRoom();
        LivePkHelper.getInstance().onKickOutChatRoom();

        Logger.i(TAG, "onKickOutChatRoom, roomId = " + mRoomId);
    }

    @Override
    public void onReceiveChatMessage(CommonChatMessage chatMessage) {
        super.onReceiveChatMessage(chatMessage);
        if (chatMessage == null || !canUpdateUi()) {
            return;
        }

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.onMessageReceived(chatMessage);
        }

        LiveBarrageComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_BARRAGE);
        if (null != component) {
            if (component.isNeedHandleBarrageMessage()) {
                if (chatMessage.itemViewType == ChatItemViewType.TYPE_COMMON_GIFT ||
                        (chatMessage.itemViewType == ChatItemViewType.TYPE_COMMON_DEFAULT_PLAINTEXT &&
                                chatMessage.mMsgType == CommonChatMessage.MESSAGE_TYPE_TXT)
                ) {
                    component.onReceiveChatMessage(chatMessage);
                }
            }
        }

        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.onReceiveChatMessage(chatMessage);
        }
    }

    @Override
    public int getHeadHeight() {
        LiveViewPositionInfo headInfo = LiveViewPositionManager.getInstance()
                .getInfoForType(LiveViewPositionType.TYPE_FULL_HEADER);
        int headHeight = headInfo != null ? headInfo.getHeight() : 0;
        int panelGap = LiveDisplayUtilKt.getDpWithoutAutoSize(10);
        return headHeight + panelGap;
    }

    public FriendsMicInfoWrapper getFriendsMicInfoWrapper() {
        return null;
    }

    @Override
    public void onReceiveBalanceInfoUpdateMessage() {
        super.onReceiveBalanceInfoUpdateMessage();
        // 更新余额(背包)
        GiftListManager.updateLivePackageList();
    }

    @Override
    public void onBrocadeBagUpdateReceived() {
        LiveGiftLoader giftLoader = LiveGiftLoader.getInstance(LiveGiftLoader.class);
        if (giftLoader != null) {
            giftLoader.updateRoomBrocadeBag();
        }
    }

    @Override
    public void onReceiveBulletMessage(CommonChatBullet bullet) {
        super.onReceiveBulletMessage(bullet);
        if (bullet == null) {
            return;
        }
        if (premiereFlag && PremiereRoomUtil.isPremiereStyleRoom(mPremiereInfo)) {
            return;
        }

        LiveBarrageComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_BARRAGE);
        if (null != component && component.isNeedHandleBarrageMessage()) {
            component.onReceiveBulletMessage(bullet);
        }
    }


    public boolean isNowPiaPrepared() {
        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            return piaModeComponent.isPiaPreparedStatus();
        }
        return false;
    }

    public boolean isRoomCreatedFromSlide() {
        return !mCommonStartRoom;
    }

    public void onPiaModeOpen() {
        // do nothing
    }

    public void onPiaModeClose() {
        try {
            IRoomRightAreaComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_RIGHT_AD);
            if (null != component) {
                component.onPiaPanelExpandStateChanged(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        updateUiOnPiaPanelStateChange(false);
    }

    protected void updateUiOnPiaPanelStateChange(boolean expand) {
        View view = findViewById(R.id.live_function_layout);
        ViewStatusUtil.setVisible(expand ? View.GONE : View.VISIBLE, view);
    }

    public void updateChatListTopMargin(int topMargin) {
        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.updateComponentTopMargin(topMargin);
    }

    public void onPiaStatusNotify(@Nullable CommonPiaStatusRsp piaStatusRsp) {
        // do nothing
    }

    public void onPiaPanelStartFold() {
        try {
            IRoomRightAreaComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_RIGHT_AD);
            if (null != component) {
                component.onPiaPanelExpandStateChanged(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        updateUiOnPiaPanelStateChange(false);
    }

    public void onPiaPanelStartExpand() {
        try {
            IRoomRightAreaComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_RIGHT_AD);
            if (null != component) {
                component.onPiaPanelExpandStateChanged(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        updateUiOnPiaPanelStateChange(true);
    }

    public void onPiaStartClicked() {
        // do nothing
    }

    public void onPiaSettingClicked() {
        tracePiaMenuClick("设置");
    }

    public void onPiaEffectClicked() {
        tracePiaMenuClick("屏蔽特效");
    }

    public void onPiaSyncClicked() {
        tracePiaMenuClick("同步");
    }

    public void onPiaTuningClicked() {
        tracePiaMenuClick("调音");
    }

    public void onPiaBGMClicked() {
        tracePiaMenuClick("BGM");
    }

    public void onPiaScrollProgressBroadcast(float progress) {
        // do nothing
    }

    @Override
    public void onReceivedRoomSpecialModeNotify(CommonRoomSpecialMode msg) {
        if (!canUpdateUi() || msg == null) return;

        // 更新详情内字段信息
        if (getRoomDetail() != null) {
            // 咨询过服务端，FunctionFlag 只会传 ROOM_MODE_FUNCTION_CELEBRATION 值，按本地定义默认清空状态
            if (msg.getFunctionFlag() == ROOM_MODE_FUNCTION_DEFAULT) {
                getRoomDetail().setCelebrationFlag(false);
            } else {
                getRoomDetail().setCelebrationFlag(msg.getSwitchFlag());
            }

            getRoomDetail().setCelebrationType(msg.getCelebrationType());
        }

        IAbsHeaderComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (comp != null) comp.onRoomSpecialModeChanged();
    }

    @Override
    public void onReceiveRewardAvatarDecorate(LiveRoomAvatarDecorate msg) {
        if (!canUpdateUi()) return;

        // 更新详情内字段信息
        if (getRoomDetail() != null && getRoomDetail().getLiveAnchorInfo() != null) {
            getRoomDetail().getLiveAnchorInfo().roomAvatarDecorate = msg;
        }

        IAbsHeaderComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (comp != null) comp.updateRewardAvatarDecorate();
    }

    @Override
    public void onReceiveFansProgressMsg(LiveFansProgressBean msg) {
        if (!canUpdateUi()) return;

        // 官方直播间不展示百人千人粉丝团动画  @冬梅
        if (officialRoomFlag) {
            return;
        }
        IAbsHeaderComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
        if (component instanceof IAudienceHeaderComponent) {
            ((IAudienceHeaderComponent) component).playFansViewProgressAnim(msg.getIcon(), msg.getCount());
        }
    }

    @Override
    public void onReceiveGuardGuideMsg(LiveGuardGuideBean msg) {
        super.onReceiveGuardGuideMsg(msg);
        if (!canUpdateUi() || premiereFlag) return;
        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showGuardGuide(msg);
    }

    @Override
    public void onReceiveFirstChargeNotifyMsg(FirstChargeNotify msg) {
        super.onReceiveFirstChargeNotifyMsg(msg);
        if (!canUpdateUi()) return;
        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showSendGiftGuide(msg);
    }

    @Nullable
    public ManageFragment getAppManageFragment() {
        return getManageFragment();
    }

    public void clearTopPage() {
        // do nothing
    }

    public void previewMedia(List<MediaInfo> mediaList, int position) {
        int invalidCount = 0;
        int targetIndex = position;
        ArrayList<MediaPreviewData> previewData = new ArrayList<>();
        for (int index = 0; index < mediaList.size(); index++) {
            MediaInfo mediaInfo = mediaList.get(index);
            switch (UnBoxUtil.unBoxValueSafely(mediaInfo.getType())) {
                case 1: {
                    if (TextUtils.isEmpty(mediaInfo.getUrl())) {
                        if (index == position) {
                            targetIndex = 0;
                        }
                        invalidCount++;
                    } else {
                        if (index == position) {
                            targetIndex = position - invalidCount;
                        }
                        previewData.add(new MediaPreviewData(
                                RoomAlbumMediaType.PHOTO,
                                mediaInfo.getUrl() != null ? mediaInfo.getUrl() : "",
                                ""
                        ));
                    }
                    break;
                }
                case 2: {
                    if (TextUtils.isEmpty(mediaInfo.getUrl()) || TextUtils.isEmpty(mediaInfo.getFirstFramePic())) {
                        if (index == position) {
                            targetIndex = 0;
                        }
                        invalidCount++;
                    } else {
                        if (index == position) {
                            targetIndex = position - invalidCount;
                        }
                        previewData.add(new MediaPreviewData(
                                RoomAlbumMediaType.VIDEO,
                                mediaInfo.getUrl() != null ? mediaInfo.getUrl() : "",
                                mediaInfo.getFirstFramePic() != null ? mediaInfo.getFirstFramePic() : ""
                        ));
                    }
                    break;
                }
                default: {
                    if (index == position) {
                        targetIndex = 0;
                    }
                    invalidCount++;
                    break;
                }
            }
        }
        LiveMediaPreviewFragment.previewMedia(getChildFragmentManager(), previewData, targetIndex);
    }

    public void showPreviewAnnouncementDialog() {
        if (!canUpdateUi() || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }

        // H5 预告
        LiveCommonITingUtil.handleITing(getActivity(), LiveCommonUrlConstants.getLivePreviewInfoUrlNew());
    }

    public void showAnnouncementDialog() {
        if (!canUpdateUi() || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }

        // 是否是首映预告
        boolean premiereForeShow = premiereFlag && PremiereRoomUtil.shouldShowPremiereTopicDialog(mPremiereInfo);
        // 是否是个播间预告
        PersonLiveDetail.LiveRecordInfo mRecordInfo = mRoomDetail.getLiveRecordInfo();
        boolean foreShow = mRecordInfo != null && mRecordInfo.status == PersonLiveBase.LIVE_STATUS_NOTICE;

        if (officialRoomFlag || premiereForeShow) {
            // 官方直播间和首映室展示老版本公告
            showNativeTopic();
        } else if (foreShow) {
            // H5 预告
            LiveCommonITingUtil.handleITing(getActivity(), LiveCommonUrlConstants.getLivePreviewInfoUrlNew());
        } else {
            // H5 公告
            LiveCommonITingUtil.handleITing(getActivity(), LiveCommonUrlConstants.getLiveAnnounceTopicUrl());
        }
    }

    public void showNativeTopic() {
        if (!canUpdateUi() || mRoomDetail == null) {
            return;
        }

        String mAnnouncementStr = null;
        if (officialRoomFlag) {
            if (mRoomDetail.getOfficialInfo() != null) {
                mAnnouncementStr = mRoomDetail.getOfficialInfo().getDescription();
            }
        } else {
            if (mRoomDetail.getLiveRecordInfo() != null) {
                mAnnouncementStr = mRoomDetail.getLiveRecordInfo().description;
            }
        }
        if (mAnnouncementStr == null) {
            mAnnouncementStr = "";
        }

        Fragment fragment;
        if (premiereFlag
                && mRoomDetail != null
                && PremiereRoomUtil.shouldShowPremiereTopicDialog(mPremiereInfo)) {
            fragment = LiveRoomPreviewInfoDialogFragment.newInstance(mRoomDetail, mPremiereInfo);
        } else {
            fragment = LiveTopicInfoFragment.newInstance(mRoomDetail, mAnnouncementStr, false);
        }
        int height = (int) (BaseUtil.getScreenHeight(getContext()) * 0.58);
        mTopicAndNoticDialogWrapper = VerticalSlideUtil.buildSlideWrapper(fragment);
        mTopicAndNoticDialogWrapper
                .setHeight(height)
                .setShowSlideView(false)
                .setBgResource(com.ximalaya.ting.android.live.common.R.drawable.live_common_bg_vertical_slide_layout_white);
        mTopicAndNoticDialogWrapper.show(getChildFragmentManager(), "topic_and_notice");
    }

    public void dismissTopicAndNoticeDialog() {
        if (mTopicAndNoticDialogWrapper != null && mTopicAndNoticDialogWrapper.isShowing()) {
            mTopicAndNoticDialogWrapper.dismiss();
        }
    }

    public void shareLive() {

    }

    @Override
    public void onReceiveMyInfoUpdateMessage() {
        if (mPresenter != null && canUpdateUi()) {
            mPresenter.requestMyUserInfo(getRoomId());
        }
    }

    @Override
    public void onReceiveGameRulesUpdateMessage(String ruleInfo) {

    }

    @Override
    public void onReceiveTitleUpdateMessage(String title) {

    }

    @Override
    public void onReceivedBirthInfoNotify(CommonBirthInfoMessage commonBirthInfoMessage) {
        super.onReceivedBirthInfoNotify(commonBirthInfoMessage);
        try {
            IBirthDayComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_BIRTHDAY);
            if (component != null) {
                component.onReceivedBirthInfoNotify(commonBirthInfoMessage);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onReceiveVersionUpdateMessage(String content) {
        if (TextUtils.isEmpty(content)) {
            return;
        }

        //上次提示的时间，目前频率为用户点击不再提示后， 2 天后再提示
        long lastNotifyTimeInMillis = MmkvCommonUtil.getInstance(getContext())
                .getLongCompat(PreferenceConstantsInLive.LIVE_KEY_LAST_UPDATE_NOTIFY_TIME);
        if (lastNotifyTimeInMillis > 0 && !notifyTimeExpired(lastNotifyTimeInMillis)) {
            LiveHelper.Log.i("ERROR: VersionUpdataTips is not expired !!!!!!!!!!!!!!!!!!!!!");
            return;
        }

        LiveCommonAlertDialog dialog = new LiveVersionUpdateDialog.Builder()
                .setContext(getContext())
                .setFragmentManager(getChildFragmentManager())
                .setDialogContent(content)
                .setLiveId(getLiveRecordId())
                .build();
        dialog.show("app_update");
    }

    private void tracePiaMenuClick(String menuName) {
        boolean isAnchor = UserInfoMannage.getUid() == getHostUid();
        // 直播间-设置及子按钮 (设置|屏蔽特效|调音|BGM|同步)  点击事件
        new XMTraceApi.Trace()
                .click(40085)
                .put("currPage", "liveRoom")
                .put("liveId", getLiveId() + "")
                .put("liveRoomType", getRoomBizType() + "")
                .put("anchorId", getHostUid() + "")
                .put("isLiveAnchor", isAnchor ? "0" : "1")
                .put("roomId", getRoomId() + "")
                .put("ubt_buttonTitle", menuName)
                .createTrace();
    }


    private boolean notifyTimeExpired(long lastNotifyTimeInMillis) {
        long currentTimeMillis = System.currentTimeMillis();

        long TWO_DAYS = 1000 * 60 * 60 * 24 * 2;
        return currentTimeMillis - lastNotifyTimeInMillis > TWO_DAYS;
    }

    @Override
    public void onReceivedFansClubUpdateMessage(CommonChatRoomFansClubUpdateMessage fansClubUpdateMessage) {
        super.onReceivedFansClubUpdateMessage(fansClubUpdateMessage);
        if (fansClubUpdateMessage != null && getRoomDetail() != null) {
            getRoomDetail().updateFansClubCount(fansClubUpdateMessage.cnt);
            if (fansClubUpdateMessage.type == TYPE_JOIN) {
                getRoomDetail().updateFansClubJoinStatus(true);
            }
        }
    }

    @Override
    public void onReceiveRoomSkinUpdateMessage(CommonChatRoomSkinUpdateMessage skinUpdateMessage) {
        if (skinUpdateMessage != null
                && mRoomDetail != null
                && mRoomDetail.getLiveAnchorInfo() != null
                && skinUpdateMessage.roomId == mRoomId) {
            mRoomDetail.getLiveAnchorInfo().bgImagePath = skinUpdateMessage.bgUrl;
            mRoomDetail.getLiveAnchorInfo().dynamicBgUrl = skinUpdateMessage.dynamicBgUrl;
            showNormalBackground();
            IInteractivePlayComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
            if (component != null) {
                component.updateBgUrl(skinUpdateMessage.bgUrl);
            }
        }
    }

    @Override
    public void onReceiveEnterRoomMessage(CommonChatUserJoinMessage userJoinMessage) {
        super.onReceiveEnterRoomMessage(userJoinMessage);

        handleChatUserJoinMessageForGreet(userJoinMessage);
        IPiaModeComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != component) {
            component.onReceiveEnterRoomMessage(userJoinMessage);
        }
    }

    @Override
    public void onRoomNobleClubUpdateMessage(CommonChatRoomNobleClubUpdateMessage message) {
        if (message != null && mRoomDetail != null) {
            mRoomDetail.updateOnlineNobleCount(message.cnt);
        }
    }

    @Override
    protected void setEnterRoomMsgIsHostFragment(CommonChatUserJoinMessage userJoinMessage) {
        userJoinMessage.isHostFragment = false;
    }

    @Override
    protected boolean isShowEnterAnim(CommonChatUserJoinMessage userJoinMessage) {
        // Pia 戏是否需要屏蔽进场特效
        boolean shieldEffect = false;
        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            shieldEffect = piaModeComponent.shieldAnimateEffect();
        }
        boolean isSelf = userJoinMessage.uid() == UserInfoMannage.getUid();
        boolean disableEnterRoomAnima = isPiaMode() && !isSelf && shieldEffect;
        return userJoinMessage.mAnimatedStyleType > 0 && (!isFullScreenLandscapeVideoRoom()) && !disableEnterRoomAnima;
    }

    @Override
    protected void highEnterRoom(CommonChatUserJoinMessage userJoinMessage) {
        try {
            LiveAVEnterAndGiftPopComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            if (null != component) {
                component.receiveEnterRoomMessage(userJoinMessage);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void normalEnterRoom(ChatItemViewType itemViewType, CommonChatUserJoinMessage userJoinMessage) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                itemViewType, userJoinMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    private void handleChatUserJoinMessageForGreet(CommonChatUserJoinMessage userJoinMessage) {
        CommonChatUser mUserInfo = userJoinMessage.mUserInfo;
        long uid = 0L;
        if (mUserInfo != null) {
            uid = mUserInfo.mUid;
        }

        if (uid != 0L && uid == UserInfoMannage.getUid()) {
            if (userJoinMessage.mWelcomeFreshFlag) {
                if (mRoomDetail != null) {
                    IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
                    if (comp != null) comp.showNewbieSayHiGuide();

                    try {
                        IHotWordComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOT_WORD);
                        if (null != component) {
                            component.hideHotWordLayout();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                if (mRoomDetail != null) {
                    mGreetRunnable = new GreetRunnable(userJoinMessage);
                    HandlerManager.postOnUIThreadDelay(mGreetRunnable, 1000);
                }
            }
        }
    }


    /**
     * 显示调音面板
     */
    protected void showMixerDialogFragment() {
        if (!canUpdateUi()) return;

        ICommonTuningComponent component = getComponentSafety(
                IBaseRoomCompConfig.COMPONENT_COMMON_TUNING
        );
        if (component != null) component.showNormalTuningPanel();
    }

    /**
     * 显示 Pia 戏调音面板
     */
    protected void showPiaTuningDialog() {
        if (!canUpdateUi()) return;

        ICommonTuningComponent component = getComponentSafety(
                IBaseRoomCompConfig.COMPONENT_COMMON_TUNING
        );
        if (component != null) component.showPiaTuningPanel();
    }

    private void doGreetAction(CommonChatUserJoinMessage userJoinMessage) {
        if (canUpdateUi() && null != mRoomDetail) {
            CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createGreetMsg(
                    mRoomDetail, userJoinMessage
            );
            if (msg != null) {
                onReceiveChatMessage(msg);
                trackLiveGreetViewShow();
            }
        }
    }

    public int getMainDarkColor() {
        return mainDarkColor;
    }

    public int getMainColor() {
        return mainColor;
    }

    public void onAdPromotionShow(boolean show) {

    }

    private class GreetRunnable implements Runnable {
        private final CommonChatUserJoinMessage mUserJoinMessage;

        public GreetRunnable(CommonChatUserJoinMessage userJoinMessage) {
            mUserJoinMessage = userJoinMessage;
        }

        @Override
        public void run() {
            doGreetAction(mUserJoinMessage);
        }
    }


    @Override
    public void onReceiveRoomCloseMessage(String reason) {
        if (!canUpdateUi()) {
            return;
        }

        // 断开长连接
        closeRoomImConnection();
        // 给出 Toast 提示
        if (TextUtils.isEmpty(reason)) {
            // 本地默认文案需要找产品确定
            reason = "房间已关闭";
        }

        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.onRoomClose();
        }

        CustomToast.showFailToast(reason);
    }


    public synchronized void releaseSDKResourse(boolean stopMix) {
        IXmMicService avService = getAvService();
        if (avService != null) {
            if (avService.isPublish()) {
                avService.leaveRoom(stopMix, getLiveMediaType() == LiveMediaType.TYPE_AUDIO);
            }

            avService.unInit();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mUserPop != null) {
            mUserPop.dismiss();
        }
        HandlerManager.removeCallbacks(mGreetRunnable);
        WealthIconCacheUtil.release();
        LiveGiftDrawableCache.release();
        LiveUserTagsManager.release();
        if (mPkPredictResultFragment != null) {
            mPkPredictResultFragment.dismiss();
            mPkPredictResultFragment = null;
        }
        if (mPkPredictEntryFragment != null) {
            mPkPredictEntryFragment.dismiss();
            mPkPredictEntryFragment = null;
        }
        if (mGuardDialogFragment != null) {
            mGuardDialogFragment.dismiss();
            mGuardDialogFragment = null;
        }
        if (mIOpenGameMessageDispatcherManager != null) {
            mIOpenGameMessageDispatcherManager.removeOpenGameMessageReceivedListener(mOpenGameMessageReveivedListener);
        }
        HandlerManager.removeCallbacks(mDelaySetTopBannerExpand);
    }

    /**
     * 收到发红包消息添加到消息流
     *
     * @param redPacketMessage 收到发红包消息
     */
    @Override
    public void addRedPacketNoticeMessage(CommonChatRedPacketMessage redPacketMessage) {
        CommonLiveLogger.i(TAG, "addRedPacketNoticeMessage " + redPacketMessage);
        // 发红包
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SEND_RED_PACKET, redPacketMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 收到发红包消息添加到消息流
     *
     * @param redPacketMessage 收到发红包消息
     */
    @Override
    public void addTimeRedPacketNoticeMessage(CommonChatTimedRedPacketMessage redPacketMessage) {
        CommonLiveLogger.i(TAG, "addTimeRedPacketNoticeMessage " + redPacketMessage);
        // 收到定时红包 发定时红包
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SEND_RED_PACKET, redPacketMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 收到抢红包消息添加到消息流
     *
     * @param redPacketMessage 收到抢红包消息
     */
    @Override
    public void addGetRedPacketNoticeMessage(CommonChatGetRedPacketMessage redPacketMessage) {
        CommonLiveLogger.i(TAG, "addGetRedPacketNoticeMessage " + redPacketMessage);

        CommonChatMessage msg;
        if (redPacketMessage.mRedPacketType == IRedPacketMessage.RED_PACKET_BOX_OR_NEW_YEAR) {
            // 抢到传送门宝箱、新年红包
            msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_CLAIM_BOX_OR_NEW_YEAR_RED_PACKET, redPacketMessage
            );
        } else {
            // 抢到普通红包
            msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_CLAIM_RED_PACKET, redPacketMessage
            );
        }
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addChatAnchorMessage(CommonChatAnchorMessage chatAnchorMsg) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createAnchorMsg(
                chatAnchorMsg, isAnchor(), currentUserIsAdmin()
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addRoomNoticeMessage(CommonChatRoomNoticeMessage chatRoomNoticeMessage) {
        ChatItemViewType itemViewType = ChatItemViewType.TYPE_COMMON_SYSTEM_NOTICE;
        if (!TextUtils.isEmpty(chatRoomNoticeMessage.url) &&
                !TextUtils.isEmpty(chatRoomNoticeMessage.urlText)
        ) {
            itemViewType = ChatItemViewType.TYPE_COMMON_SYSTEM_CALL_TO_ACTION;
        }
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                itemViewType, chatRoomNoticeMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addOrRemoveAdminMessage(boolean addAdmin) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_ADMIN_CHANGE, addAdmin
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addRoomGameRulesUpdateMessage(CommonChatRoomRuleInfoUpdateMessage gameRuleUpdateMessage) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_OLD_SYSTEM_NOTICE, gameRuleUpdateMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void addToSmallGiftPopTask(GiftShowTask task) {
        super.addToSmallGiftPopTask(task);
        if (task.isFriendsMode) {
            LiveGiftLoader giftLoader = LiveGiftLoader.getInstance(LiveGiftLoader.class);
            SeatGiftManager.getSeatGiftManager().notifyGiftReceived(task, giftLoader);
        }
        try {
            LiveAVEnterAndGiftPopComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            if (null != component) {
                component.receiveGiftPopMessage(task);
            }
        } catch (Exception e) {
            ConnectLogUtilWrapper.Companion.log(getRoomBizType(), "init GiftPopComponent exception." + Log.getStackTraceString(new Throwable()));
            e.printStackTrace();
        }
    }

    @Override
    protected boolean shieldEffectOpen(GiftShowTask task) {
        boolean shieldEffect = false;

        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            shieldEffect = piaModeComponent.shieldAnimateEffect();
        }
        boolean sendFromMe = task.getSenderUid() == UserInfoMannage.getUid();
        // Pia 戏模式下开启屏蔽特效不展示礼物动画
        return isPiaMode() && !sendFromMe && shieldEffect;
    }

    @Override
    protected void setGiftLoaderParams(BaseGiftLoader giftLoader, long giftId, String animationPath) {
        if (mRoomDetail != null) {
            giftLoader.setBizType(mRoomDetail.getRoomBizType())
                    .setLiveId(mRoomDetail.getLiveId())
                    .setDynamicType(giftId, animationPath);
        }
    }

    @Override
    public BaseGiftLoader createGiftLoader() {
        return LiveGiftLoader.getInstance(LiveGiftLoader.class);
    }

    @Override
    public void onReceiveGiftComboOverMessage(CommonChatGiftComboOverMessage giftMessage) {
        super.onReceiveGiftComboOverMessage(giftMessage);
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_GIFT, giftMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    protected void insertGiftMsg(CommonChatGiftMessage giftMessage) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_GIFT, giftMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    protected void insertGiftBoxMessage(ReceiverOpenGiftListChatMessage receiverOpenGiftListChatMessage) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_GIFT, receiverOpenGiftListChatMessage
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 接收到主播发送优惠券通知消息
     *
     * @param msg 优惠券通知消息
     */
    @Override
    public void onGetNewLiveCouponMsg(CommonGetNewCouponMsg msg) {
        ICouponComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (null != component) {
            component.onGetNewCouponMsgById(msg.couponId);
        }
    }

    public void showUserInfoPop(final long targetId) {
        showUserInfoPop(targetId, false, mRoomId, 0);
        if (mGuardDialogFragment != null && mGuardDialogFragment.isShowing()) {
            mGuardDialogFragment.dismiss();
        }
    }

    public void showUserInfoPopOnly(final long targetId) {
        showUserInfoPopOnly(targetId, false, mRoomId, 0);
        if (mGuardDialogFragment != null && mGuardDialogFragment.isShowing()) {
            mGuardDialogFragment.dismiss();
        }
    }

    public void showUserInfoPop(final long targetId, CommonChatMessage crm) {
        showUserInfoPop(crm, false);

        if (null != crm) {
            // 直播间-评论区_头像/昵称  点击事件
            new XMTraceApi.Trace()
                    .click(33386)
                    .put("uid", String.valueOf(targetId))
                    .put("currPage", "liveRoom")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .createTrace();
        }
    }

    public void showAnonymityDialog(long targetId) {
        AnonymityReportDialog reportDialog = AnonymityReportDialog.newInstance();
        reportDialog.setReportAction(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                openReportPage(false, getLiveId(), targetId);
                return null;
            }
        });
        LiveDialogFragmentManager.INSTANCE.addDialog(reportDialog, getChildFragmentManager());
    }

    public void openReportPage(boolean isReportLive, long liveId, long targetUid) {

    }


    public void showUserInfoPop(final CommonChatMessage crm, boolean fromPk) {
        if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        //fromPk代表点击的是PK面板中对方主播的头像
        //如果是匿名直播间，那么不展示资料库，直接弹出举报
        long myUid = UserInfoMannage.getUid();
        long hostUid = mRoomDetail.getLiveAnchorInfo().uid;
        long targetUid = crm.getSenderUid();
        if (isNeedAnonymity() && !fromPk && targetUid != getHostUid() && targetUid != myUid) {
            showAnonymityDialog(targetUid);
        } else {
            initUserInfoDialog(false);
            mUserPop.myShow(hostUid, targetUid, fromPk, crm, isFromHostFragment());
        }
    }

    /**
     * 打开用户资料卡
     *
     * @param targetId        目标id
     * @param isFromGiftPanel 是否来自礼物面板-麦上用户资料卡
     *                        true 资料卡关闭后打开礼物面板
     *                        false 资料卡关闭后不打开礼物面板
     */
    public void showUserInfoPop(final long targetId, final boolean isFromGiftPanel) {
        if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        //fromPk代表点击的是PK面板中对方主播的头像
        //如果是匿名直播间，那么不展示资料库，直接弹出举报
        long myUid = UserInfoMannage.getUid();
        long hostUid = mRoomDetail.getLiveAnchorInfo().uid;
        if (isNeedAnonymity() && targetId != getHostUid() && targetId != myUid) {
            showAnonymityDialog(targetId);
        } else {
            initUserInfoDialog(isFromGiftPanel);
            mUserPop.myShow(hostUid, targetId, false, mRoomId, 0, isFromHostFragment());
        }
    }

    public boolean isNeedAnonymity() {
        if (mRoomDetail == null) {
            return false;
        }
        boolean anonymousRoom = mRoomDetail.isAnonymousRoom();
        boolean isManager = currentUserIsAdmin();
        //房间是匿名的，并且自己不是管理员
        return anonymousRoom && !isManager;
    }

    public void showUserInfoPop(final long targetId, boolean fromPk, long roomId, long matchedHostId) {
        if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        //fromPk代表点击的是PK面板中对方主播的头像
        //如果是匿名直播间，那么不展示资料库，直接弹出举报
        long uid = UserInfoMannage.getUid();
        if (isNeedAnonymity() && !fromPk && targetId != getHostUid() && targetId != uid) {
            showAnonymityDialog(targetId);
        } else {
            initUserInfoDialog(false);
            long hostUid = mRoomDetail.getLiveAnchorInfo().uid;
            mUserPop.myShow(hostUid, targetId, fromPk, roomId, matchedHostId, isFromHostFragment());
        }
    }

    /**
     * 展示资料卡,不考虑匿名直播间的限制
     */
    public void showUserInfoPopOnly(final long targetId, boolean fromPk, long roomId, long matchedHostId) {
        if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        initUserInfoDialog(false);
        long hostUid = mRoomDetail.getLiveAnchorInfo().uid;
        mUserPop.myShow(hostUid, targetId, fromPk, roomId, matchedHostId, isFromHostFragment());
    }

    private void initUserInfoDialog(boolean isFromGiftPanel) {
        long liveRecordId = mRoomDetail.getLiveRecordInfo().id;
        int mode = RoomModeManager.getInstance().getRoomMode();
        int liveType = mRoomDetail.getLiveRecordInfo().bizType;
        int mediaType = mRoomDetail.getMediaType();
        if (mUserPop == null) {
            if (mActivity == null) {
                return;
            }
            mUserPop = new AVUserInfoCardDialog(
                    mActivity, this, liveRecordId, mRoomId, mode,
                    liveType, isFromHostFragment(), mediaType
            );
            mUserPop.setItemClickListener((chatUser, position) -> {
                if (chatUser != null) {
                    IInputPanelComponent panelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
                    if (null != panelComponent) {
                        panelComponent.sendATMessage(chatUser.mUid, chatUser.mNickname);
                    }
                }
            });
            mUserPop.setOwnerActivity(mActivity);
            mUserPop.setActionCallback(new AVUserInfoCardDialog.IActionCallback() {
                @Override
                public void onOpenPrivateChatView(long uid, String nickName) {
                    CommonPrivateChatComponent chatComponent
                            = getComponentSafety(AudienceCompConfig.COMPONENT_PRIVATE_CHAT_SHOW);
                    if (chatComponent != null) {
                        chatComponent.showPrivateChatView(uid, nickName);

                        // 直播间-私信弹框  弹框展示
                        new XMTraceApi.Trace()
                                .setMetaId(35490)
                                .setServiceId("dialogView")
                                .put("currPage", LiveRoomBaseFragment.this.getClass().getSimpleName())
                                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                                .createTrace();
                    }
                }

                @Override
                public void hangUp(long uid) {
                    IMicBaseComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
                    if (component != null) {
                        component.hangUpMic(uid);
                    }
                }

                @Override
                public void inviteMic(long uid, String name) {
                    IMicBaseComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
                    if (component != null) {
                        component.inviteUserJoinMic(uid, name);
                    }
                }

                @Override
                public void onOpenGuardListPage(LiveUserInfo userInfo, boolean isPkAnchor) {
                    showGuardListPage(userInfo, isPkAnchor);
                }
            });
            mUserPop.setJumpPageListener(this);
            if (isFromGiftPanel) {
                mUserPop.setOnCancelListener(dialog -> {
                    showFriendGiftPanel();
                });
            } else {
                mUserPop.setOnCancelListener(null);
            }
        } else {
            if (mUserPop.isShowing()) {
                mUserPop.dismiss();
            }
            mUserPop.setRoomId(mRoomId);
            mUserPop.setLiveId(liveRecordId);
            mUserPop.setMediaType(mediaType);
            mUserPop.setRoomMode(RoomModeManager.getInstance().getRoomMode());
            if (isFromGiftPanel) {
                mUserPop.setOnCancelListener(dialog -> {
                    showFriendGiftPanel();
                });
            } else {
                mUserPop.setOnCancelListener(null);
            }
        }
    }

    @Override
    public void onJumpOtherPageFromUserCardDialog() {
        super.onJumpOtherPageFromUserCardDialog();
        handleUserCardJumpOtherPage();
    }

    protected void handleUserCardJumpOtherPage() {

    }

    @Override
    public void showFansClubDialogFragment() {
        if (mRoomDetail == null) {
            return;
        }
        if (TextUtils.isEmpty(mRoomDetail.getFansClubHtmlUrl())) {
            return;
        }
        showFansClubDialogFragment(mRoomDetail.getFansClubHtmlUrl());
    }

    public void showOpenNoblePage(final int grade) {
        super.showOpenNoblePage(grade, getHostUid());
    }

    /**
     * 守护开通页
     *
     * @param grade 守护等级，用于定位
     */
    public void showOpenGuardPage(final int grade) {
        if (mRoomDetail == null || mRoomDetail.getGuardInfoVO() == null) {
            return;
        }
        String url = mRoomDetail.getGuardInfoVO().getGuardDetailUrl();
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "guardType=" + grade);
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "appId=" + IXmAppIdConstants.XMLY);
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "bizType=" + getRoomBizType());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "anchorUid=" + mRoomDetail.getHostUid());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "roomId=" + mRoomDetail.getRoomId());

        FragmentManager fragmentManager = getChildFragmentManager();

        if (TextUtils.isEmpty(url)) {
            return;
        }

        FragmentTransaction fragTransaction = fragmentManager.beginTransaction();
        Fragment fragment = fragmentManager.findFragmentByTag(ProvideForH5CustomerDialogFragment.TAG);

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, ProvideForH5CustomerDialogFragment.POSITION_BOTTOM);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, 600);
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, ProvideForH5CustomerDialogFragment.SHOWCLOSE_NO);

        mGuardDialogFragment = ProvideForH5CustomerDialogFragment.newInstance(bundle);
        try {
            if (fragment != null) {
                // 防止重复显示对话框，移除正在显示的对话框
                fragTransaction.remove(fragment);
                fragTransaction.commitNowAllowingStateLoss();
            }
            mGuardDialogFragment.showAllowingStateLoss(fragmentManager, ProvideForH5CustomerDialogFragment.TAG);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 从个人资料卡打开守护页面
     *
     * @param userInfo   资料卡用户的信息
     * @param isPkAnchor 是否是 PK 中对面的主播
     */
    private void showGuardListPage(LiveUserInfo userInfo, boolean isPkAnchor) {
        if (mRoomDetail == null || mRoomDetail.getGuardInfoVO() == null) {
            return;
        }

        if (isPkAnchor) {
            // 直接取 iting 跳转对方的守护页面
            if (userInfo == null || userInfo.getGuardGroupVo() == null) {
                return;
            }
            LiveCommonITingUtil.handleITing(getActivity(), userInfo.getGuardGroupVo().getGuardHtmlUrl());
            return;
        }

        int guardType = (userInfo != null && userInfo.getGuardGroupVo() != null) ? userInfo.getGuardGroupVo().getGuardType() : 0;
        int mineGuardType = (mMyUserInfo != null && mMyUserInfo.getGuardGroupVo() != null) ? mMyUserInfo.getGuardGroupVo().getGuardType() : 0;
        String url;
        if (isFromHostFragment()) {
            // 主播端，跳转到榜单页
            url = mRoomDetail.getGuardInfoVO().getWeeklyRankUrl();
        } else if (userInfo != null && userInfo.getUid() == mHostUid) {
            // 点击的是主播资料卡，跳转到守护列表页
            url = mRoomDetail.getGuardInfoVO().getWeeklyRankUrl();
            url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "guardType=" + mineGuardType);
        } else {
            // 点击的是用户资料卡，跳转到守护开通页
            url = mRoomDetail.getGuardInfoVO().getGuardDetailUrl();
            url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "guardType=" + guardType);
        }

        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "appId=" + IXmAppIdConstants.XMLY);
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "bizType=" + getRoomBizType());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "anchorUid=" + mRoomDetail.getHostUid());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "roomId=" + mRoomDetail.getRoomId());


        FragmentManager fragmentManager = getChildFragmentManager();

        if (TextUtils.isEmpty(url)) {
            return;
        }

        FragmentTransaction fragTransaction = fragmentManager.beginTransaction();
        Fragment fragment = fragmentManager.findFragmentByTag(ProvideForH5CustomerDialogFragment.TAG);

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, ProvideForH5CustomerDialogFragment.POSITION_BOTTOM);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, 600);
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, ProvideForH5CustomerDialogFragment.SHOWCLOSE_NO);

        mGuardDialogFragment = ProvideForH5CustomerDialogFragment.newInstance(bundle);
        try {
            if (fragment != null) {
                // 防止重复显示对话框，移除正在显示的对话框
                fragTransaction.remove(fragment);
                fragTransaction.commitNowAllowingStateLoss();
            }

            mGuardDialogFragment.showAllowingStateLoss(fragmentManager, ProvideForH5CustomerDialogFragment.TAG);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 守护列表页
     */
    protected void showGuardListPage() {
        if (mRoomDetail == null || mRoomDetail.getGuardInfoVO() == null) {
            return;
        }
        //检测少儿模式和未登录场景
        if (LiveHostCommonUtil.checkChildrenModeOpen(mContext)) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext);
            return;
        }
        String url = mRoomDetail.getGuardInfoVO().getWeeklyRankUrl();

        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "appId=" + IXmAppIdConstants.XMLY);
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "bizType=" + getRoomBizType());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "anchorUid=" + mRoomDetail.getHostUid());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "roomId=" + mRoomDetail.getRoomId());
        int guardType = 0;
        if (mMyUserInfo != null && mMyUserInfo.getGuardGroupVo() != null) {
            guardType = mMyUserInfo.getGuardGroupVo().getGuardType();
        }
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "guardType=" + guardType);

        if (TextUtils.isEmpty(url)) {
            return;
        }
        final String guardUrl = url;
        if (mCurOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            //判断如果还在横屏到竖屏变换过程中 ，延迟显示避免宽高计算错误
            postOnUiThreadDelayed(new Runnable() {
                @Override
                public void run() {
                    showCustomH5DialogWithSpecificHeight(guardUrl, BaseUtil.getScreenHeight(getContext()) / 4 * 3);
                }
            }, 500);
        } else {
            showCustomH5DialogWithSpecificHeight(guardUrl, BaseUtil.getScreenHeight(getContext()) / 4 * 3);
        }
    }

    public void showPkSearchHostView() {

    }

    public void showStarCraftBoxAnimate(String path, long templateId) {
        ICraftBoxAnimateComponent animateComponent = getComponentSafety(AudienceCompConfig.COMPONENT_CROFT_BOX_ANIMATION);
        if (null != animateComponent) {
            animateComponent.showStarCraftBoxAnimate(path, templateId);
        }
    }

    public void showPkPredictEntry(long predictionId) {
        if (mRoomDetail == null) {
            return;
        }

        if (LiveClearScreenManager.getInstance().isClear()) {
            return;
        }

        String url = LiveUrlConstants.getInstance().getPkPredictPageH5Url();
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "roomId=" + mRoomDetail.getRoomId());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "liveId=" + mRoomDetail.getLiveId());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "anchorUid=" + mRoomDetail.getHostUid());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "predictionId=" + predictionId);

        FragmentManager fragmentManager = getChildFragmentManager();

        FragmentTransaction fragTransaction = fragmentManager.beginTransaction();
        Fragment fragment = fragmentManager.findFragmentByTag(ProvideForH5CustomerDialogFragment.TAG);

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, ProvideForH5CustomerDialogFragment.POSITION_BOTTOM);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, 445);
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, ProvideForH5CustomerDialogFragment.SHOWCLOSE_NO);

        mPkPredictEntryFragment = ProvideForH5CustomerDialogFragment.newInstance(bundle);
        try {
            if (fragment != null) {
                // 防止重复显示对话框，移除正在显示的对话框
                fragTransaction.remove(fragment);
                fragTransaction.commitNowAllowingStateLoss();
            }

            mPkPredictEntryFragment.showNow(fragmentManager, ProvideForH5CustomerDialogFragment.TAG);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showPkPredictResult(long predictionId) {
        if (mRoomDetail == null) {
            return;
        }

        String url = LiveUrlConstants.getInstance().getPkPredictResultH5Url();
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "roomId=" + mRoomDetail.getRoomId());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "liveId=" + mRoomDetail.getLiveId());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "anchorUid=" + mRoomDetail.getHostUid());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "predictionId=" + predictionId);

        FragmentManager fragmentManager = getChildFragmentManager();

        FragmentTransaction fragTransaction = fragmentManager.beginTransaction();
        Fragment fragment = fragmentManager.findFragmentByTag(ProvideForH5CustomerDialogFragment.TAG);

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, ProvideForH5CustomerDialogFragment.POSITION_BOTTOM);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, 445);
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, ProvideForH5CustomerDialogFragment.SHOWCLOSE_NO);

        mPkPredictResultFragment = ProvideForH5CustomerDialogFragment.newInstance(bundle);
        try {
            if (fragment != null) {
                fragTransaction.remove(fragment);
                fragTransaction.commitNowAllowingStateLoss();
            }

            mPkPredictResultFragment.showNow(fragmentManager, ProvideForH5CustomerDialogFragment.TAG);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected int getRoomH5PageOnlineTab() {
        return IRoomH5RankAndOnlineTab.AV_ROOM_ONLINE_H5_ONLINE_TAB;
    }

    @Override
    protected int getRoomH5PageRankTab() {
        return IRoomH5RankAndOnlineTab.AV_ROOM_ONLINE_H5_FANS_RANK_TAB;
    }

    @Override
    protected String getRoomOnlineH5PageUrl() {
        String url = super.getRoomOnlineH5PageUrl();
        if (url == null || url.isEmpty()) {
            url = LiveUrlConstants.getInstance().getAudienceOnlineListH5UrlNew();
        }
        return url;
    }

    /**
     * 切换至普通模式
     **/
    public void showCommonModeUI() {
        if (null != getComponentHost()) {
            getComponentHost().dispatchRoomHalfModeChange(RoomModeManager.halfScreenChatMode());
        }
        updateBannerViewsOnModeChange();
    }

    public void showRadioModeUI() {
        if (null != getComponentHost()) {
            getComponentHost().dispatchRoomHalfModeChange(RoomModeManager.halfScreenChatMode());
        }
        setTopBannerExpandDelay(2000);
    }

    private final Runnable mDelaySetTopBannerExpand = () -> {
        try {
            IRoomRightAreaComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_RIGHT_AD);
            if (null != component) {
                component.setTopBannerExpand(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    };

    public void onShowMainContentAreaUI() {
        hideSubscribeView();
        // 右侧监听主视区方案缺陷，在h5侧设置的时候 无法触发 onlayoutChange 这里还是要发手动设置右侧挂件折叠
        setTopBannerExpandDelay(2000);
    }

    /**
     * 隐藏预告面板
     */
    private void hideSubscribeView() {
        try {
            ISubscribeRoomComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_SUBSCRIBE_ROOM);
            if (null != component) {
                component.hideSubscribeView();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setTopBannerExpandDelay(long delayTime) {
        // 由于可能在直播间内，重新开播的情况，接口来的慢于信令的时间，因此延迟更新
        HandlerManager.removeCallbacks(mDelaySetTopBannerExpand);
        HandlerManager.postOnUIThreadDelay(mDelaySetTopBannerExpand, delayTime);
    }

    /**
     * 切换至交友模式
     */
    public void initFriendModeUI() {
        if (getComponentHost() != null) {
            getComponentHost().dispatchRoomHalfModeChange(true);
        }
        updateBannerViewsOnModeChange();
    }

    public void releaseFriendModeUI() {

    }

    /**
     * 切换至PK模式
     */
    public void initPkModeUI() {
        if (getComponentHost() != null) {
            getComponentHost().dispatchRoomHalfModeChange(true);
        }
        updateBannerViewsOnModeChange();
    }

    public void releasePkModeUI() {
        showNormalBackground();
    }

    /**
     * 模式切换时，更新广告位，延迟加载，避免影响互动区域加载速度
     */
    private void updateBannerViewsOnModeChange() {
        postOnUiThread(new Runnable() {
            @Override
            public void run() {
                IRoomRightAreaComponent component = getExistComponent(AudienceCompConfig.COMPONENT_RIGHT_AD);
                if (component == null) {
                    return;
                }
                component.hideSomeRightViews();
                component.updateBannerViewsOnModeChange();
            }
        });
    }

    public void showLoveMarryBackground() {
        IRoomBackgroundComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_ROOM_BACKGROUND);
        if (null != component) {
            component.showSpecialBackground(R.drawable.live_bg_for_marry_mode);
        }
    }

    @Override
    public void keyboardShowStateChange(boolean show) {
        super.keyboardShowStateChange(show);
        onUserOperateInput(show);
    }

    /**
     * 当观众操作键盘的时候，键盘弹起的时候，需要隐藏的区域，其他区域保持不变
     * 1、直播间头部信息（头像、昵称、赞助榜入口、粉丝团入口、在线人数、主播榜入口、话题入口）隐藏
     * 2、Pk面板隐藏
     * 3、交友模式面板隐藏
     * 4、大挂件和小挂件隐藏
     *
     * @param show true:键盘出现 false:键盘隐藏
     */
    private void onUserOperateInput(boolean show) {
        if (getComponentHost() != null) {
            getComponentHost().dispatchInputKeyboardStatusChange(show);
        }
    }


    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }


    @Override
    public ViewGroup getRootView() {
        return (ViewGroup) mContainerView;
    }

    @Override
    public Fragment getFragment() {
        return this;
    }

    @Override
    public boolean isFriendsMode() {
        return RoomModeManager.isFriendsMode();
    }

    public boolean isPiaMode() {
        return RoomModeManager.isPiaMode();
    }

    @Override
    public void showChargeDialog(long roomId, int sendType, Activity activity, SimpleDialog.IDialogInterface iDialogInterface) {
        LiveDialogUtil.showChargeDialog(roomId, sendType, activity, iDialogInterface);
    }

    @Override
    public boolean isH5DialogShowing() {
        return isCustomH5DialogShowing();
    }

    public RelativeLayout getRoomAnimationContainerView() {
        return findViewById(R.id.live_room_main_content);
    }

    @Override
    public void onFinishCallback(Class<?> cls, int fid, Object... params) {
        if (ImageMultiPickFragment.class == cls) {
            if (params != null && params.length > 0) {
                List<ImgItem> items = ((List<ImgItem>) params[0]);
                if (!items.isEmpty()) {
                    LiveHelper.Log.i("onFinishCallback select image path: " + items.get(0)
                            .getPath());
                    mPresenter.sendImgMessage(items.get(0).getPath());
                }
            }
        }
    }

    @Override
    public void onSendingMessage(CommonChatMessage chatMessage) {
        if (!canUpdateUi()) {
            return;
        }

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.onMessageReceived(chatMessage);
    }

    @Override
    public void onSendMessageSuccess(CommonChatMessage chatMessage) {
        if (!canUpdateUi()) {
            return;
        }

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.onHandleMsgSendSucceed(chatMessage);

        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.onSendMessageSuccess(chatMessage);
        }

        LiveBarrageComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_BARRAGE);
        if (null != component) {
            if (component.isNeedHandleBarrageMessage()) {
                if (chatMessage.itemViewType == ChatItemViewType.TYPE_COMMON_GIFT ||
                        (chatMessage.itemViewType == ChatItemViewType.TYPE_COMMON_DEFAULT_PLAINTEXT &&
                                chatMessage.mMsgType == CommonChatMessage.MESSAGE_TYPE_TXT)
                ) {
                    component.onReceiveChatMessage(chatMessage);
                }
            }
        }
    }

    @Override
    public void onSendMessageFailed(CommonChatMessage chatMessage) {
        if (!canUpdateUi()) {
            return;
        }

        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.onHandleMsgSendFailed(chatMessage);
    }

    @Override
    public LiveUserInfo getCurrentUserInfo() {
        return mMyUserInfo;
    }

    public void showGiftPanel() {

    }

    @Override
    public void showGiftPanel(long selectedTargetId) {

    }

    @Override
    public void showGiftPanelByGiftId(long giftId, long anchorUid) {

    }

    public void showFriendGiftPanel() {

    }

    @Override
    public boolean onBackPressed() {
        if (mTopicAndNoticDialogWrapper != null && mTopicAndNoticDialogWrapper.isShowing()) {
            mTopicAndNoticDialogWrapper.dismiss();
            return true;
        }
        return super.onBackPressed();
    }

    @Override
    public void onReceiveHostOnlineListMessage(CommonChatRoomOnlineUserListMsg msg) {
        super.onReceiveHostOnlineListMessage(msg);
        if (msg != null && mRoomDetail != null) {
            if (mRoomDetail.getLiveRecordInfo() != null) {
                mRoomDetail.getLiveRecordInfo().onlineCount = msg.onlineCount;
                mRoomDetail.getLiveRecordInfo().playCount = msg.playCnt;
                mRoomDetail.getLiveRecordInfo().hotScore = msg.hotScore;
            }
        }
    }

    @CallSuper
    @Override
    public void onReceivedTopicUpdateMessage(CommonChatRoomTopicUpdateMessage message) {
        super.onReceivedTopicUpdateMessage(message);
        if (message == null || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        if (message.isEmpty()) {
            return;
        }

        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_TOPIC, message
        );
        if (msg != null) {
            msg.mTitle = PremiereRoomUtil.showPremiereChatListTopicMessage(mPremiereInfo) ? "首映公告：" : "发布了新公告：";
            onReceiveChatMessage(msg);
        }

        if (mRoomDetail.getLiveRecordInfo() != null) {
            mRoomDetail.getLiveRecordInfo().description = message.txt;
            mRoomDetail.getLiveRecordInfo().hotTopicTitle = message.topicName;
        }
        if (mPremiereInfo != null) {
            mPremiereInfo.setDescription(message.txt);
        }
        mTopicContent = message.txt;

        AnnounceDataCache.update(mHostUid, mRoomId, announceData -> {
            announceData.setContent(message.txt);
            announceData.setMediaList(message.mediaInfos);
            announceData.setTopicTitle(message.topicName);
            return null;
        });
    }

    @Override
    public void onReceivedHotTopicMessage(CommonChatHotTopicMessage message) {
        super.onReceivedHotTopicMessage(message);
        mHotTopicTitle = (message.getTxt() == null) ? "" : message.getTxt();
        mHotTopicId = (message.getTagId() == null) ? 0 : (int) message.getTagId().longValue();

        if (mRoomDetail != null && mRoomDetail.getLiveRecordInfo() != null) {
            mRoomDetail.getLiveRecordInfo().tagId = mHotTopicId;
            mRoomDetail.getLiveRecordInfo().hotTopicTitle = mHotTopicTitle;
        }

        AnnounceDataCache.update(mHostUid, mRoomId, announceData -> {
            announceData.setTopicId(mHotTopicId);
            announceData.setTopicTitle(mHotTopicTitle);
            return null;
        });
    }

    /**
     * 直播间结束消息
     */
    protected void onLiveRoomEndMessageReceived() {
        super.onLiveRoomEndMessageReceived();
    }

    @Override
    public void onReceiveOfficialTimerMessage(LiveOfficialTimerMessage message) {
        LamiaHelper.Log.i("官方直播间：", "定时消息-" + message.toString());
        String text = message.getTxt();
        if (isOfficialSource && !isAnchor()) {
            text = message.getOfficialTxt();
        }
        if (!TextUtils.isEmpty(text)) {
            try {
                IRoomRightAreaComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_RIGHT_AD);
                if (null != component) {
                    component.startTimer(message.getRemainTime(), text);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 官方直播间信令处理
     *
     * @param message 官播间信令
     */
    @Override
    public void onReceiveOfficialSwitchMessage(LiveOfficialSwitchMessage message) {
        //A房间收到END  B房间收到START
        LamiaHelper.Log.i("官方直播间：", "切换房间消息-" + message.toString());

        if (message.getAction() == LiveOfficialSwitchMessage.ACTION_START) {
            setOfficialRoomFlag(true);
            officialRoomFlag = true;
            //查询deatil里的OfficialInfo信息
            loadDetail();
        } else if (message.getAction() == LiveOfficialSwitchMessage.ACTION_END) {
            //观众
            if (!isAnchor()) {
                //从官播来的关注
                if (isOfficialSource) {
                    //还有下一个节目,就跳转
                    if (!TextUtils.isEmpty(message.getNextItingUrl())) {
                        setOfficialRoomFlag(false);
                        LiveCommonITingUtil.handleITing(getActivity(), message.getNextItingUrl());
                    } else {
                        //官方直播结束了
                        OfficialLiveInfo officialInfo = mRoomDetail.getOfficialInfo();
                        if (officialInfo != null) {
                            LiveClearScreenManager.getInstance().restoreShowScreen();
                            PersonLiveDetail.LiveAnchorInfo liveUserInfo = new PersonLiveDetail.LiveAnchorInfo();
                            mRoomDetail.setFollowed(officialInfo.getFollowFlag());
                            liveUserInfo.largeAvatar = officialInfo.getLogoPic();
                            liveUserInfo.uid = officialInfo.getUid();
                            liveUserInfo.nickname = officialInfo.getNickName();

                            // 定时退出逻辑判断
                            if (LiveTerminateManager.needExitRoomWhenLiveEnd()) {
                                exitRoomUnstoppable();
                                return;
                            }

                            int abResult = LiveAbtestUtil.getAutoEnterLiveRoomConfig();
                            if (abResult == LiveAbtestUtil.AUTO_ENTER_ROOM_FROM_COVER || abResult == LiveAbtestUtil.AUTO_ENTER_ROOM_FROM_BANNER) {
                                IRecommendLiveComponent recommendComponent = getComponentSafety(COMPONENT_RECOMMEND);
                                if (recommendComponent != null) {

                                    recommendComponent.showOfficialLiveInfoRecommendLive("返回直播间", abResult, mRoomDetail, officialInfo);
                                }

                            } else {
                                LiveAudienceFinishFragment finishFragment = LiveAudienceFinishFragment.newInstance(getRoomBizType(), this, mRoomDetail,
                                        officialInfo, officialInfo.getUid(), liveUserInfo, mRoomDetail.getLiveRecordInfo().categoryId, new LiveBaseDialogFragment
                                                .IDialogFragmentCallBack() {
                                            @Override
                                            public void callBack(Class tClass, Object... params) {
                                                finishFragment();
                                            }
                                        }, true);
                                finishFragment.show(getChildFragmentManager(), "LiveAudienceFinishFragment");
                                //不走 onLiveRoomEndMessageReceived 统一结束逻辑，单独处理官播间结束逻辑
                            }

                            closeRoomImConnection();
                            PlayTools.pause(getContext(), PauseReason.Business.LiveRoomBaseFragment);
                            //设置播放状态View为结束状态
                            PersonLiveDetail detail = getRoomDetail();
                            detail.getLiveRecordInfo().status = PersonLiveBase.LIVE_STATUS_END;
                            getWaitComponent(AudienceCompConfig.COMPONENT_FRIEND_MODE, new IComponentStateCallBack<IInteractivePlayComponent>() {
                                @Override
                                public void onCreated(@NonNull IInteractivePlayComponent component) {
                                    component.currentRoomStatusUpdate(detail);
                                }
                            });
                            //关闭播放器
                            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
                            if (component != null) {
                                component.setLiveFinish(true);
                                LiveVideoPlayerManager.getInstance().pause();
                            }
                        }
                    }
                    IAudienceMicComponent audienceMicComponent = getComponentSafety(AudienceCompConfig.COMPONENT_AUDIENCE_MIC);
                    if (audienceMicComponent != null) {
                        audienceMicComponent.updateAnchorMicEndUI(true);
                    }
                    IInteractivePlayComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
                    if (component != null) {
                        component.showOfficialCard();
                    }
                } else {
                    //主播自己的观众
                    officialRoomFlag = false;
                    setOfficialRoomFlag(false);
                    //手动设置一遍官播间状态，现在关播不会重新请求房间接口
                    if (mRoomDetail != null) {
                        mRoomDetail.setOfficialRoomFlag(false);
                    }
                }

            } else {
                //主播
                officialRoomFlag = false;
                setOfficialRoomFlag(false);
                //手动设置一遍官播间状态，现在关播不会重新请求房间接口
                if (mRoomDetail != null) {
                    mRoomDetail.setOfficialRoomFlag(false);
                }
            }

        }
    }

    protected void setOfficialRoomFlag(boolean isOfficialRoom) {
        if (mPresenter != null && mPresenter.getComponentHost() instanceof AudienceComponentHost) {
            ((AudienceComponentHost) mPresenter.getComponentHost()).dispatchOfficialRoomFlag(isOfficialRoom);
        }
    }

    protected void setPremiereRoomFlag(boolean isPremiereRoom) {
        premiereFlag = isPremiereRoom;
        if (ConstantsOpenSdk.isDebug) {
            Log.e("LivePremiere", "setPremiereRoomFlag isPremiereRoom " + isPremiereRoom);
        }
        if (mRoomDetail == null) {
            return;
        }
        if (isPremiereRoom && mPresenter != null) {
            mPresenter.requestPremiereInfo(mRoomDetail.getHostUid(), mRoomDetail.getRoomId());
        } else if (!isPremiereRoom) {
            onGetPremiereInfo(null);
        }
        if (mPresenter != null && mPresenter.getComponentHost() instanceof AudienceComponentHost) {
            ((AudienceComponentHost) mPresenter.getComponentHost()).dispatchPremiereRoomFlag(isPremiereRoom);
        }
    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        super.onLogin(model);
        if (premiereFlag && mRoomDetail != null) {
            requestPremiereInfo();
        }
        mAIEnableStatusViewModel.isEnableScreenShot();
    }

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {
        super.onLogout(olderUser);
        if (premiereFlag && mRoomDetail != null) {
            requestPremiereInfo();
        }
        mAIEnableStatusViewModel.getMAIEnableStatusData().postValue(null);
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
        super.onUserChange(oldModel, newModel);
        if (premiereFlag && mRoomDetail != null) {
            requestPremiereInfo();
        }
    }

    @Override
    public void onReceiveOfficialChooseMessage(LiveOfficialNoticeMessage message) {
        LamiaHelper.Log.i("官方直播间：", "通知消息-" + message.toString());
        IOfficialComponent component = getComponentSafety(COMPONENT_OFFICIAL);
        if (component != null) {
            component.receiveOfficialChooseMessage(message);
        }
    }

    /**
     * 禁言列表
     */
    protected void showBanListDialog() {
        if (!canUpdateUi() || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        if (mLiveBanListFraWrapper != null && mLiveBanListFraWrapper.get() != null) {
            mLiveBanListFraWrapper.get().dismiss();
        }
        LiveManagementFragment forbidListFragment = LiveManagementFragment.newInstance(
                getRoomBizType(),
                getRoomId(),
                getLiveId(),
                LiveManagementFragment.TYPE_FORBID
        );
        int height = LiveDialogUtil.getOperationDialogHeight(mContext);
        VerticalSlideUtil.VerticalSlideWrapper<LiveManagementFragment> wrapper
                = VerticalSlideUtil.buildSlideWrapper(forbidListFragment);
        wrapper.setHeight(height);
        wrapper.setShowSlideView(false);
        GradientDrawable drawable = new GradientDrawable();
        drawable.setColor(getResources().getColor(com.ximalaya.ting.android.live.common.R.color.live_color_262626));
        drawable.setCornerRadii(new float[]{
                BaseUtil.dp2px(getContext(), 16),
                BaseUtil.dp2px(getContext(), 16),
                BaseUtil.dp2px(getContext(), 16),
                BaseUtil.dp2px(getContext(), 16),
                0,
                0,
                0,
                0}
        );
        wrapper.setBgDrawable(drawable);
        wrapper.show(getChildFragmentManager(), "forbid-list");
        mLiveBanListFraWrapper = new WeakReference<>(wrapper);
    }

    @Override
    public void onNetWorkChanged(boolean networkAvailable, boolean isWifi) {
        super.onNetWorkChanged(networkAvailable, isWifi);
        if (!canUpdateUi()) return;

        // 网络断开和恢复后需要执行一些操作
        if (networkAvailable) {
            // 重新请求首映室信息
            if (premiereFlag && mRoomDetail != null) {
                requestPremiereInfo();
            }
        }
    }

    /**
     * 管理员列表
     */
    protected void showAdminListDialog() {
        if (!canUpdateUi() || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        if (mLiveAdminListFraWrapper != null && mLiveAdminListFraWrapper.get() != null) {
            mLiveAdminListFraWrapper.get().dismiss();
        }
        LiveManagementFragment adminListFragment = LiveManagementFragment.newInstance(
                getRoomBizType(),
                getRoomId(),
                getLiveId(),
                LiveManagementFragment.TYPE_ADMIN
        );
        int height = LiveDialogUtil.getOperationDialogHeight(mContext);
        VerticalSlideUtil.VerticalSlideWrapper<LiveManagementFragment> wrapper
                = VerticalSlideUtil.buildSlideWrapper(adminListFragment);
        wrapper.setHeight(height);
        wrapper.setShowSlideView(false);
        wrapper.setBgResource(com.ximalaya.ting.android.live.common.R.color.live_color_262626);
        wrapper.show(getChildFragmentManager(), "admin-list");
        mLiveAdminListFraWrapper = new WeakReference<>(wrapper);
    }

    public void onMainDarkColorGot(int color) {
        mainDarkColor = color;
        if (mPresenter != null) {
            mPresenter.getComponentManager().onMainDarkColorGot(color);
        }
    }


    public void onMainColorGot(int color) {
        Log.i("房间背景颜色", "房间内mainColor:" + color);
        mainColor = color;
        if (mPresenter != null) {
            mPresenter.getComponentManager().onMainColorGot(color);
        }
    }

    public PersonLiveDetail getRoomDetail() {
        return mRoomDetail;
    }

    public boolean currentUserIsAdmin() {
        Logger.i(TAG, "mRoomId = " + mRoomId
                + "currentUserIsAdmin = " + (mPresenter != null && mPresenter.mCurrentUserIsAdmin));

        return mPresenter != null && mPresenter.mCurrentUserIsAdmin;
    }

    @Override
    public boolean isFromHostFragment() {
        return false;
    }

    @Override
    public void onReceiveRoomStatusChangeMessage(CommonChatRoomStatusChangeMessage roomStatusChangeMessage) {
        super.onReceiveRoomStatusChangeMessage(roomStatusChangeMessage);
        getWaitComponent(AudienceCompConfig.COMPONENT_SELL_GOOD, new IComponentStateCallBack<ISellGoodsComponent>() {
            @Override
            public void onCreated(ISellGoodsComponent component) {
                component.setIsLiving(roomStatusChangeMessage.status == PersonLiveBase.LIVE_STATUS_ING);
            }
        });

        if (roomStatusChangeMessage.status == PersonLiveBase.LIVE_STATUS_END) {
            EffectDataHolder.Companion.reset();
            XmPiaBgmPlayerManager.Companion.getInstance().destroy();
        }

        boolean isHost = mHostUid > 0 && mHostUid == UserInfoMannage.getUid();
        if (mRoomDetail == null || isHost) return;

        // 非主播端收到房间被关闭状态
        if (roomStatusChangeMessage.status == PersonLiveBase.LIVE_STATUS_END) {
            IPiaModeComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
            if (null != component) {
                component.onRoomClose();
            }
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }


    @Override
    public void addWarningMessage(CommonChatRoomWarningMessage warningMsg) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SYSTEM_WARNING, warningMsg
        );
        if (msg == null) return;

        onReceiveChatMessage(msg);

        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.addWarningMessage(warningMsg);
        }
    }


    @Override
    public void onReceiveRecommendAlbumMessage(CommonChatRoomAlbumInfoMsg albumInfoMsg) {
        super.onReceiveRecommendAlbumMessage(albumInfoMsg);
    }

    /**
     * 注册本地广播，侦听：
     * 1、视频直播点击昵称事件
     * 2、视频直播长按昵称事件
     */
    private void registerLocalReceiver() {
        if (mLiveVideoRoomLocalBroadcastReceiver == null) {
            mLiveVideoRoomLocalBroadcastReceiver = new LiveRoomLocalBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ChatListViewConstant.ACTION_CHAT_LIST_CLICK_NICKNAME);
            intentFilter.addAction(ChatListViewConstant.ACTION_CHAT_LIST_LONG_CLICK_NICKNAME);
            LocalBroadcastManager.getInstance(mContext).registerReceiver(mLiveVideoRoomLocalBroadcastReceiver, intentFilter);
        }
    }

    /**
     * 注销本地广播
     */
    private void unregisterLocalReceiver() {
        if (mLiveVideoRoomLocalBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLiveVideoRoomLocalBroadcastReceiver);
            mLiveVideoRoomLocalBroadcastReceiver = null;
        }
    }

    final class LiveRoomLocalBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || !canUpdateUi() || !isResumed()) {
                return;
            }

            if (ChatListViewConstant.ACTION_CHAT_LIST_CLICK_NICKNAME.equals(intent.getAction())) {
                long uid = intent.getLongExtra(ChatListViewConstant.BUNDLE_KEY_USER_ID, -1);
                if (uid > 0) {
                    showUserInfoPop(uid);
                }
            } else if (ChatListViewConstant.ACTION_CHAT_LIST_LONG_CLICK_NICKNAME.equals(intent.getAction())) {
                String nickName = intent.getStringExtra(ChatListViewConstant.BUNDLE_KEY_USER_NAME);
                long uid = intent.getLongExtra(ChatListViewConstant.BUNDLE_KEY_USER_ID, -1);
                if (!TextUtils.isEmpty(nickName) && uid > 0) {
                    IInputPanelComponent comp = getComponentSafety(COMPONENT_INPUT_PANEL);
                    if (comp != null) comp.sendATMessage(uid, nickName);
                }
            }
        }
    }

    @Override
    public void onReceiveCommonBusinessMessage(CommonBusinessMsg message) {
        super.onReceiveCommonBusinessMessage(message);
        if (message.getType() == null) {
            return;
        }
        if (message.getType() == CommonBusinessMsg.TYPE_VOTE) {
            ILiveVoteComponent component = getComponentSafety(COMPONENT_VOTE);
            if (null != component) {
                component.getVoteIsOpen();
            }
        } else if (message.getType() == CommonBusinessMsg.TYPE_OFFICIAL_VOTE) {
            ILiveVoteComponent component = getComponentSafety(COMPONENT_VOTE);
            if (null != component) {
                component.getOfficialVoteIsOpen();
            }
        }
    }

    public abstract boolean isAnchor();

    public abstract void releaseMicUI();

    public abstract void releasePkMicUI();

    public abstract LoveModeMicStateManager.MicStateObserver getMicStateObserver();

    public abstract IRoomModeFragment getRoomModeFragment();

    public abstract PlayerConstants.ResolutionRatio getVideoSizeRatio();

    public long getLiveAnchorId() {
        if (mRoomDetail != null) {
            return mRoomDetail.getHostUid();
        }
        return 0L;
    }

    protected boolean isLandScapeScreenOrientation() {
        return ViewStatusUtil.isLandScreenOrientation(mActivity);
    }

    protected boolean isVideoRoomType() {
        if (mRoomDetail != null) {
            return mRoomDetail.getMediaType() == LiveMediaType.TYPE_VIDEO;
        }
        return false;
    }

    @Override
    public boolean isFullScreenLandscapeVideoRoom() {
        return isLandScapeScreenOrientation() && isVideoRoomType();
    }

    @Override
    public boolean isFollowHost() {
        if (mRoomDetail != null && mRoomDetail.getLiveAnchorInfo() != null) {
            return mRoomDetail.isFollowed();
        }
        return false;
    }

    @Override
    public void onReceiveWithdrawChatMsg(CommonWithdrawChatMsg withdrawChatMsg) {
        super.onReceiveWithdrawChatMsg(withdrawChatMsg);

        Logger.i(TAG, "onReceiveWithdrawChatMsg, CommonWithdrawChatMsg = " + withdrawChatMsg);

        if (canUpdateUi() && withdrawChatMsg != null) {
            IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
            if (comp != null) comp.recallMessage(withdrawChatMsg.msgId);
        }
    }

    @Override
    public void onReceiveRemindPKMsg(LiveRemindPkMsg msg) {
        super.onReceiveRemindPKMsg(msg);
        IAVChatListGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) {
            comp.showRemindPKMsg(msg);
        }
    }

    @Override
    public void onReceiveAreaConfigUpdateMsg(LiveAreaConfigListBean msg) {
        super.onReceiveAreaConfigUpdateMsg(msg);
        try {
            IInteractivePlayComponent comp = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
            if (comp != null) comp.onReceiveAreaConfigUpdate(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected ConditionFilter[] getRecallGiftGuideConditionFilter() {
        // 「底部运营位请求」、「红包、福袋请求及动画」条件避让
        ConditionFilter[] filters = new ConditionFilter[2];
        filters[0] = new BottomBannerConditionFilter(this);
        filters[1] = new RedPacketAndLuckyBagAndVoteConditionFilter(this);
        return filters;
    }

    public void onGoodsOperationViewShow(boolean isShow) {

    }

    public void onBottomOperationViewShow(boolean isShow) {

    }

    public void onTopOperationViewShow(boolean isShow) {

    }

    /**
     * 检查是否有优惠券
     */
    protected void checkHasCouponOrNot() {
        getWaitComponent(IBaseRoomCompConfig.COMPONENT_COUPON, new IComponentStateCallBack<ICouponComponent>() {
            @Override
            public void onCreated(ICouponComponent component) {
                component.checkHasCouponOrNot();
            }
        });
    }

    @Override
    public void onGetCouponViewStatusChangeMsg(CommonCouponShowViewStatusMsg msg) {
        super.onGetCouponViewStatusChangeMsg(msg);
        LiveHelper.Log.i(TAG, "onGetCouponViewStatusChangeMsg:" + msg.toString());

        if (msg.state != 1) {
            return;
        }

        getWaitComponent(IBaseRoomCompConfig.COMPONENT_COUPON, new IComponentStateCallBack<ICouponComponent>() {
            @Override
            public void onCreated(ICouponComponent component) {
                component.shouldHideCouponView();
            }
        });
    }

    @Override
    public void onGetPremiereInfo(PremiereInfo premiereInfo) {
        LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "  onGetPremiereInfo " + premiereInfo);
        updatePremiereInfo(premiereInfo);
    }

    protected void updatePremiereInfo(PremiereInfo premiereInfo) {
        if (mPremiereInfo == null && premiereInfo == null) {
            return;
        }

        this.mPremiereInfo = premiereInfo;
        getComponentSafety(AudienceCompConfig.COMPONENT_PREMIERE_START_ANIMATION);
        getComponentSafety(AudienceCompConfig.CommonAudioVideoRoomCompConfig.COMPONENT_FRIEND_MODE);

        if (mPresenter != null && mPresenter.getComponentHost() instanceof AudienceComponentHost) {
            ((AudienceComponentHost) mPresenter.getComponentHost()).dispatchPremiereInfo(premiereInfo);
        }
        if (mTopicAndNoticDialogWrapper != null
                && mTopicAndNoticDialogWrapper.isShowing()
                && mTopicAndNoticDialogWrapper.mContentFragment != null
                && mTopicAndNoticDialogWrapper.mContentFragment instanceof LiveRoomPreviewInfoDialogFragment) {
            ((LiveRoomPreviewInfoDialogFragment) mTopicAndNoticDialogWrapper.mContentFragment).setPremiereInfo(premiereInfo);
        }
    }

    @Override
    public void onReceiveWishOrGiftWallProgressMsg(LiveAnchorTaskMessage msg) {
        super.onReceiveWishOrGiftWallProgressMsg(msg);
        ITaskComponent hostTaskComponent = getComponentSafety(COMPONENT_HOST_TASK);
        if (hostTaskComponent != null) {
            hostTaskComponent.onReceiveWishOrGiftWallProgressMsg(msg);
        }
    }

    @Override
    public void onReceivePremiereMsg(LivePremiereMsg msg) {
        super.onReceivePremiereMsg(msg);
        LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "onReceivePremiereMsg " + msg);
        if (ConstantsOpenSdk.isDebug) {
            Log.e("LivePremiere", "onReceivePremiereMsg msg " + msg);
            CustomToast.showDebugFailToast("onReceivePremiereMsg action = " + msg.getAction() + " status = " + msg.getStatus());
        }
        if (msg == null) {
            return;
        }
        switch (msg.getAction()) {
            case PremiereMsgAction.ACTION_CHANGE:
                premiereStatusChange(msg);
                break;
            case PremiereMsgAction.ACTION_COUNT_DOWN:
                IPremiereCountDownComponent premiereCountDownComponent = getComponentSafety(AudienceCompConfig.COMPONENT_PREMIERE_COUNT_DOWN);
                if (premiereCountDownComponent != null) {
                    premiereCountDownComponent.onReceiveCountDownMsg(msg);
                }
                break;
            case PremiereMsgAction.ACTION_CLOSE_VIDEO:
                IInteractivePlayComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
                if (null != component) {
                    component.onReceiveCloseMsg(msg);
                }
                break;
            case PremiereMsgAction.ACTION_SHOW_MESSAGE:
                break;
            case PremiereMsgAction.ACTION_SEPARATE_DISC:
                break;
            case PremiereMsgAction.ACTION_REFRESH_ROOM:
                if (premiereFlag != msg.isPremiereFlag()) {
                    setPremiereRoomFlag(msg.isPremiereFlag());
                }
                break;
            case PremiereMsgAction.ACTION_ANCHOR_ONLINE:
                IAbsHeaderComponent headerComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_HEADER);
                if (headerComponent instanceof IAudienceHeaderComponent) {
                    ((IAudienceHeaderComponent) headerComponent).onReceiveAnchorOnlineMsg(msg);
                }
                break;
            default:
                break;
        }

        IInteractivePlayComponent playComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
        if (null != playComponent) {
            playComponent.onReceivePremiereMsg(msg);
        }
    }

    private void premiereStatusChange(LivePremiereMsg msg) {
        if (mPremiereInfo == null) {
            return;
        }
        //可能信令先到PREMIERE_LIVE_END
        if (PremiereStatus.PREMIERE_END_LIVING == msg.getStatus()
                && PremiereStatus.PREMIERE_LIVE_END == mPremiereInfo.getPremiereStatus()) {
            return;
        }
        mPremiereInfo.setPremiereStatus(msg.getStatus());
        if (PremiereStatus.PREMIERE_LIVE_END == msg.getStatus()) {
            if (!isFromHostFragment()) {
                CustomToast.showToast("首映已结束");
            }
            setPremiereRoomFlag(false);
            return;
        }
        updatePremiereInfo(mPremiereInfo);
    }

    public void requestPremiereInfo() {
        if (!premiereFlag) {
            return;
        }
        if (mPresenter == null) {
            return;
        }
        mPresenter.requestPremiereInfo(getHostUid(), getRoomId());
    }

    protected void hideAllDialog() {
        if (mTopicAndNoticDialogWrapper != null
                && mTopicAndNoticDialogWrapper.isShowing()) {
            mTopicAndNoticDialogWrapper.dismiss();
        }
    }

    protected boolean shouldDisableSomeDialogForPremiereRoom() {
        return premiereFlag && mPremiereInfo != null && (
                mPremiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_NO_START
                        || mPremiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_PRE
                        || mPremiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_ING
                        || mPremiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_END_LIVING);
    }

    @Override
    public void handleButtonAnim(String btnName) {
        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.doButtonAnim(btnName);
        }
    }

    @Override
    public void onDestroy() {
        XmPiaBgmPlayerManager.Companion.getInstance().destroy();
        // asr 引擎注销，如果没有init过 则不会触发 注销操作，暂时考虑个播 观众和主播端
        LiveAsrManager.Companion.destroy();
        super.onDestroy();
        PremierePlayStatisticsManager.getInstance().tryUpload();
    }

    @Override
    public void onReceiveNewPkAnimMsg(LiveNewPkAnimMessage msg) {
        super.onReceiveNewPkAnimMsg(msg);
        if (msg == null) {
            return;
        }
        IInteractivePlayComponent playComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
        if (playComponent != null) {
            playComponent.onReceiveNewPkAnimMsg(msg);
        }
    }
}