package com.ximalaya.ting.android.liveaudience.view.mode;

/**
 * Created by qianmenchao on 2020-01-19.
 *
 * <AUTHOR>
 */
public interface IRoomModeData {
    /**
     * 主播id
     */
    long getHostId();

    /**
     * 直播场次id
     */
    long getLiveId();

    /**
     * 直播间id
     */
    long getRoomId();

    /**
     * PK排位赛段位等级
     */
    long getPkRankGrade();

    /**
     * PK排位赛段位图标
     */
    String getPkRankIconUrl();

    /**
     * 房间类型
     */
    int getLiveRoomType();

    /**
     * 是否关注了主播
     */
    boolean isFollowed();

    /**
     * 主播头像
     * @return 主播头像
     */
    String getAvatarUrl();
}
