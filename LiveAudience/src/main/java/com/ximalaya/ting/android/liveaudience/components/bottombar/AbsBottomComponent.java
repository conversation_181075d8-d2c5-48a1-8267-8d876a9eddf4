package com.ximalaya.ting.android.liveaudience.components.bottombar;

import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * 底部按钮组件基类
 *
 * @email <EMAIL>
 * @phone 15026804470
 */
public abstract class AbsBottomComponent extends LamiaComponent implements LiveViewPositionManager.IPositionListener {

    //底部栏
    protected ViewGroup mBottomBar;
    /**
     * 底部容器ViewGroup(不包含底部栏)——普通进场，弹幕互动游戏按钮，竖屏热词
     */
    protected ViewGroup mBottomIgnoreBarVg;


    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        mBottomIgnoreBarVg = findViewById(R.id.live_chat_room_bottom_layout);
        LiveViewPositionManager.getInstance().registerLayoutChangeListener(LiveViewPositionType.TYPE_BOTTOM, mBottomBar);
        LiveViewPositionManager.getInstance().addListener(this, LiveViewPositionType.TYPE_BOTTOM);
        updateListViewLayoutParamsRule(false);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LiveViewPositionManager.getInstance().unregisterLayoutChangeListener(LiveViewPositionType.TYPE_BOTTOM, mBottomBar);
        LiveViewPositionManager.getInstance().removeListener(this, LiveViewPositionType.TYPE_BOTTOM);
    }

    @Override
    public void onLayoutChange(@Nullable LiveViewPositionInfo info) {
        if (info == null) {
            return;
        }
        if (!mCurrentKeboardShow) {
            updateListViewLayoutParamsRule(false);
        }
    }

    @Override
    public void onUserInputStatusChange(boolean isInput) {
        super.onUserInputStatusChange(isInput);
        updateListViewLayoutParamsRule(isInput);
    }

    protected static int sEmotionViewHeight;

    protected boolean mCurrentKeboardShow = false;

    protected void updateListViewLayoutParamsRule(boolean keyBoardShow) {
        if (!canUpdateUi()) {
            mCurrentKeboardShow = keyBoardShow;
            return;
        }

        View input = findViewById(R.id.livecomm_input_panel);

        int bottomLayoutMarginTop = BaseUtil.dp2px(getContext(), 10);

        if (keyBoardShow) {
            if (input == null) {
                mCurrentKeboardShow = true;
                return;
            }
            input.post(() -> {
                sEmotionViewHeight = input.getMeasuredHeight();
                Logger.i(TAG, "updateListViewLayoutParamsRule sEmotionViewHeight " + sEmotionViewHeight + " " + input.getMeasuredHeight());
                if (!mCurrentKeboardShow) {
                    return;
                }

                if (mBottomIgnoreBarVg != null) {
                    RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mBottomIgnoreBarVg.getLayoutParams();
                    lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                    lp.bottomMargin = sEmotionViewHeight + bottomLayoutMarginTop;
                    mBottomIgnoreBarVg.setLayoutParams(lp);
                }
            });
        } else {
            Logger.i(TAG, "updateListViewLayoutParamsRule keyboardShow = " + keyBoardShow);

            if (mBottomIgnoreBarVg != null) {
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mBottomIgnoreBarVg.getLayoutParams();
                lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                if (LiveViewPositionManager.getInstance().getInfoForType(LiveViewPositionType.TYPE_BOTTOM) != null) {
                    lp.bottomMargin = LiveViewPositionManager.getInstance().getInfoForType(LiveViewPositionType.TYPE_BOTTOM).getHeight()
                            + BaseUtil.dp2px(getContext(), 3);
                } else {
                    lp.bottomMargin = 0;
                }
                mBottomIgnoreBarVg.setLayoutParams(lp);
            }
        }
        mCurrentKeboardShow = keyBoardShow;
    }

}
