package com.ximalaya.ting.android.liveaudience.fragment.room;

import static com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Client.AB_LIVE_ANCHOR_FOLLOW_POP;
import static com.ximalaya.ting.android.live.common.chatlist.trace.LiveChatItemEventTraceKt.trackLiveFollowViewShow;
import static com.ximalaya.ting.android.live.common.chatlist.trace.LiveChatItemEventTraceKt.trackLiveJoinClubViewShow;
import static com.ximalaya.ting.android.live.common.chatlist.trace.LiveChatItemEventTraceKt.trackLiveRewardViewShow;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage.TYPE_ANCHOR_TASK;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_AI_PENALTY;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_EXIT_ROOM;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_GAME_CONTROL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_GIFT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HEADER;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HOST_TASK;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_PLAYER_FAIL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_RED_PACKET_RAIN;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_ROOM_LOADING;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_SPRING_FESTIVAL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_VIDEO_PLAYER;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_AUDIENCE_MIC;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_FRIEND_MODE;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_RECOMMEND;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_TIMED_TASK;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_VIDEO_PK_AUDIENCE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.common.lib.logger.ConnectLogUtilWrapper;
import com.ximalaya.ting.android.common.lib.logger.LamiaBizLog;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.SharePanelDialog;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.biz.mode.data.PrivateChatViewModel;
import com.ximalaya.ting.android.live.biz.pia.drama.fragment.PiaScriptListFragment;
import com.ximalaya.ting.android.live.biz.userverify.XmLiveVerifyManagerKt;
import com.ximalaya.ting.android.live.biz.view.IOnMicEmotionItemClickListener;
import com.ximalaya.ting.android.live.biz.view.LiveMicEmotionDialog;
import com.ximalaya.ting.android.live.common.chatlist.NotifyFollowerManager;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatItemViewType;
import com.ximalaya.ting.android.live.common.component.playbacksetting.LivePlaybackSettingDialog;
import com.ximalaya.ting.android.live.common.component.playbacksetting.manager.LiveTerminateManager;
import com.ximalaya.ting.android.live.common.dialog.web.CommonXmlObjcJsCall;
import com.ximalaya.ting.android.live.common.input.IKeyboardHostFragment;
import com.ximalaya.ting.android.live.common.input.emoji.IEmojiItem;
import com.ximalaya.ting.android.live.common.input.manager.LiveCommonEmojiManager;
import com.ximalaya.ting.android.live.common.lib.ILiveTemplateByIdCallback;
import com.ximalaya.ting.android.live.common.lib.LiveTemplateManager;
import com.ximalaya.ting.android.live.common.lib.avatarcache.ChatUserAvatarCache;
import com.ximalaya.ting.android.live.common.lib.base.constants.CdnStatus;
import com.ximalaya.ting.android.live.common.lib.base.constants.IBusinessIdConstants;
import com.ximalaya.ting.android.live.common.lib.base.constants.IMoreMenuType;
import com.ximalaya.ting.android.live.common.lib.base.constants.IMoreMenuUserType;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveCommonConstants;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.LivePageAvailabilityConst;
import com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.constants.RoomProgressiveLoadingConstants;
import com.ximalaya.ting.android.live.common.lib.base.dialog_queue.LiveDialogFragmentManager;
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.base.util.LivePageAvailabilityUtil;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.entity.HeadAnchorInfo;
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAiSoundPenalty;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAuditNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCarouselRankNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCommonActGiftGuideMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveDanmuCountModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveDanmuFreeModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansClubStatusModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveMenuData;
import com.ximalaya.ting.android.live.common.lib.entity.LiveNewbieGuideMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialWelcomeMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveSpringFestivalMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveSpringFestivalProgressMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveSurprise;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereInfo;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereStatus;
import com.ximalaya.ting.android.live.common.lib.gift.anim.SuperGiftLayout;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.download.AnimationPathSelector;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.RoomRedPointManager;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.NewAudienceAwardInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.PackageInfo;
import com.ximalaya.ting.android.live.common.lib.manager.LiveGlobalDispatcher;
import com.ximalaya.ting.android.live.common.lib.manager.LiveNewbieGuideManager;
import com.ximalaya.ting.android.live.common.lib.manager.LivePageEnterTimeManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.IPlayAnimTask;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.model.LiveTemplateModel;
import com.ximalaya.ting.android.live.common.lib.moremenu.LiveMoreMenuDialog;
import com.ximalaya.ting.android.live.common.lib.utils.LiveAbtestUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LivePlayRestoreUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.memory.LiveMemoryMMKVManager;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.clearScreen.IClearScreenConfig;
import com.ximalaya.ting.android.live.common.view.clearScreen.LiveClearScreenManager;
import com.ximalaya.ting.android.live.common.view.dialog.LivePopAvatarInfoDialog;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.chatlist.IBaseChatListComponent;
import com.ximalaya.ting.android.live.host.components.exitroom.IExitRoomComponent;
import com.ximalaya.ting.android.live.host.components.guide.newbie.INewbieGuideComponent;
import com.ximalaya.ting.android.live.host.components.input.IInputPanelComponent;
import com.ximalaya.ting.android.live.host.components.privatechat.CommonPrivateChatComponent;
import com.ximalaya.ting.android.live.host.components.recommend.IRecommendLiveComponent;
import com.ximalaya.ting.android.live.host.components.redpacketrain.LiveRedPacketRainComponent;
import com.ximalaya.ting.android.live.host.components.rightarea.IRoomRightAreaComponent;
import com.ximalaya.ting.android.live.host.components.roomloading.IRoomLoadingComponent;
import com.ximalaya.ting.android.live.host.components.sellgoods.ISellGoodsComponent;
import com.ximalaya.ting.android.live.host.components.springfestival.LiveSpringFestivalActivityComponent;
import com.ximalaya.ting.android.live.host.constant.Constants;
import com.ximalaya.ting.android.live.host.fragment.debug.RoomDebugConfigure;
import com.ximalaya.ting.android.live.host.fragment.debug.handler.IRoomDebugEventHandler;
import com.ximalaya.ting.android.live.host.liverouter.LiveRouter;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.host.manager.playupload.PremierePlayStatisticsManager;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomConstants;
import com.ximalaya.ting.android.live.host.manager.roomcore.service.IRoomCoreService;
import com.ximalaya.ting.android.live.host.manager.roomcore.service.mic.IRoomMicService;
import com.ximalaya.ting.android.live.host.scrollroom.fragment.LiveScrollFragment;
import com.ximalaya.ting.android.live.host.scrollroom.model.RecommendLiveRecord;
import com.ximalaya.ting.android.live.host.scrollroom.request.LiveScrollDataManager;
import com.ximalaya.ting.android.live.host.scrollroom.scrollrecorder.MiniRoomRecord;
import com.ximalaya.ting.android.live.host.scrollroom.util.AllLiveRoomStarter;
import com.ximalaya.ting.android.live.host.scrollroom.util.startroom.StartRoomUtil;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.live.host.utils.LiveHostFragmentUtil;
import com.ximalaya.ting.android.live.host.utils.ShareUtils;
import com.ximalaya.ting.android.live.host.view.header.LiveRoomStatusView;
import com.ximalaya.ting.android.live.lib.chatroom.constant.BaseCommonProtoConstant;
import com.ximalaya.ting.android.live.lib.chatroom.constant.IGoodsInfoChangedConstant;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonCdnStatusMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatAudienceMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftComboOverMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatHotTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatQueryRoomModeRsp;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatShareLiveRoomMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonFirstCommentAward;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonFloatScreenMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.GameStart;
import com.ximalaya.ting.android.live.lib.chatroom.entity.GameStop;
import com.ximalaya.ting.android.live.lib.chatroom.entity.LiveFansRemindMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.UserStatusNotify;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatFansIntimacyMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansClubUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansRankMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNotifyBottomButtonMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomStatusChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomTopicUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonFansGroupMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoShoppingMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsInfoChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsOrderChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.util.LiveMessageConfigUtilKt;
import com.ximalaya.ting.android.live.lib.livetopic.LiveAnnouncementAlbumDialog;
import com.ximalaya.ting.android.live.lib.livetopic.LiveAnnouncementNewFeatureDialog;
import com.ximalaya.ting.android.live.lib.livetopic.bean.SOURCE;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.Anchor;
import com.ximalaya.ting.android.live.lib.stream.preloadplayer.ILiveMediaSource;
import com.ximalaya.ting.android.live.lib.stream.preloadplayer.PreloadPlayerManager;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.lifecycle.IComponentStateCallBack;
import com.ximalaya.ting.android.liveaudience.components.aiprop.ILiveAiPropComponent;
import com.ximalaya.ting.android.liveaudience.components.bottombar.BottomBarComponent;
import com.ximalaya.ting.android.liveaudience.components.bottombar.IBottomBarComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.AVChatListMsgProducer;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.IAVChatListComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.guide.IAVChatListGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlisttimedtask.AudienceRoomTimedTaskComponent;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceComponentHost;
import com.ximalaya.ting.android.liveaudience.components.coupon.ICouponComponent;
import com.ximalaya.ting.android.liveaudience.components.gamecontrol.ILiveGameControlComponent;
import com.ximalaya.ting.android.liveaudience.components.giftpanel.IAudienceGiftPanelComponent;
import com.ximalaya.ting.android.liveaudience.components.guide.common.IAVCommonGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.header.IAudienceHeaderComponent;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IInteractivePlayComponent;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IPiaModeComponent;
import com.ximalaya.ting.android.liveaudience.components.mic.IAudienceMicComponent;
import com.ximalaya.ting.android.liveaudience.components.pk.IVideoPkAudienceComponent;
import com.ximalaya.ting.android.liveaudience.components.playerfail.IPlayerFailComponent;
import com.ximalaya.ting.android.liveaudience.components.seal.ISealListComponent;
import com.ximalaya.ting.android.liveaudience.components.shoppingfloatscreen.IShoppingFloatComponent;
import com.ximalaya.ting.android.liveaudience.components.task.ITaskComponent;
import com.ximalaya.ting.android.liveaudience.components.videoplayer.IVideoPlayerComponent;
import com.ximalaya.ting.android.liveaudience.constants.clearview.AudienceClearScreenConfig;
import com.ximalaya.ting.android.liveaudience.data.model.friends.FriendsMicInfoWrapper;
import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive;
import com.ximalaya.ting.android.liveaudience.data.request.LiveAudienceRoomViewModel;
import com.ximalaya.ting.android.liveaudience.dialog.comment_award.ISendEmojiCallback;
import com.ximalaya.ting.android.liveaudience.dialog.comment_award.LiveCommentAwardDialogFragment;
import com.ximalaya.ting.android.liveaudience.fragment.love.AudienceActionCallback;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeGuest;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeLogicHelper;
import com.ximalaya.ting.android.liveaudience.friends.base.ILoveModeAudience;
import com.ximalaya.ting.android.liveaudience.giftModule.loader.LiveGiftLoader;
import com.ximalaya.ting.android.liveaudience.manager.endPopup.LiveEndPopupManager;
import com.ximalaya.ting.android.liveaudience.manager.gift.NotifySendGiftGuideManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeMicStateManager;
import com.ximalaya.ting.android.liveaudience.manager.love.util.LoveModeManagerInjectUtil;
import com.ximalaya.ting.android.liveaudience.manager.mode.LiveModeData;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkHelper;
import com.ximalaya.ting.android.liveaudience.manager.pk.util.PkModeManagerInjectUtil;
import com.ximalaya.ting.android.liveaudience.manager.roomcore.AudienceRoomCore;
import com.ximalaya.ting.android.liveaudience.mvp.LiveAudienceRoomPresenter;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.liveaudience.util.premiere.PremiereRoomUtil;
import com.ximalaya.ting.android.liveaudience.view.mode.IRoomModeFragment;
import com.ximalaya.ting.android.liveaudience.view.pk.PkPanelView;
import com.ximalaya.ting.android.liveav.lib.util.log.LiveLogUtil;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.liveim.micmessage.constants.UserStatus;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.player.XMediaplayerImpl;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.PluginAgent;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 直播间基础Fragment，根据直播间所有的业务功能点，对代码逻辑做了一些拆解，每个小功能都在自己的小component里处理
 *
 * <AUTHOR>
 * @since 2020/1/13
 */
public class LiveAudienceRoomFragment extends LiveRoomBaseFragment
        implements IKeyboardHostFragment, AnchorFollowManage.IFollowAnchorListener {

    private static final String TAG = "LiveAudienceRoomFragment";

    /**
     * Fragment 复用时的View
     **/
    protected View mCacheContentView;

    /**
     * Fragment 顶部根布局
     **/
    protected ViewGroup mTopLayout;

    /**
     * Fragment 底部根布局
     **/
    protected ViewGroup mBottomLayout;

    /**
     * 交友模式听终端实现
     **/
    private ILoveModeAudience mMakeFriendsAudience;

    /**
     * 听终端交友模式对外提供的接口回调，如点击按钮时，执行某些操作
     **/
    private AudienceActionCallback mFriendActionCallback;

    /**
     * 某些接口的实现类，为fragment减轻负担
     **/
    protected LiveRoomImplHelper mFragmentImplHelper;

    private boolean mReleased;
    private int mInputMode;
    private ILiveRoomDetail mLamiaRoomDetail;
    private long mLastRoomId;

    private LocalBroadcastReceiver mChatRoomLocalBroadcastReceiver;

    /**
     * 当前房间是否展示过单主播关注弹窗
     */
    private boolean isFollowAnchorDialogShowed;
    private Bundle mBundle;

    private LiveMenuData mMoreMenuModel;

    /**
     * 讲解中挂件是否显示
     */
    private boolean isGoodsOperationViewShow = false;
    /**
     * 顶部大挂件是否显示
     */
    private boolean isTopOperationViewShow = false;
    /**
     * 观众连麦小窗是否显示
     */
    private boolean isAudiMicViewShow = false;
    /**
     * 底部小挂件是否显示
     */
    private boolean isBottomOperationViewShow = false;
    private int mVideoWidth = 16;
    private int mVideoHeight = 9;
    private PlayerConstants.ResolutionRatio mResolutionRatio = PlayerConstants.ResolutionRatio.LANDSCAPE_16_9;

    /**
     * 分享弹框的引用
     */
    private WeakReference<SharePanelDialog> mShareDialogRef;

    /**
     * 管理员公告弹窗
     */
    private WeakReference<LiveAnnouncementAlbumDialog> mAnnounceDialogRef;

    private LiveAudienceRoomViewModel mAudienceRoomViewModel;

    private int mCdnStatus;
    private boolean mIsAdPromotionShow;

    /**
     * 退出房间 推荐弹窗管理器
     */
    private LiveEndPopupManager mLiveEndPopupManager;

    /**
     * 是否已经在展示 直播结束弹窗
     */
    private boolean mStopDialogShowed = false;
    private boolean isLoad;

    private LiveAudienceRoomResourceDebug audienceRoomResourceDebug;

    /**
     * Fragment 直播间实例，用了缓存，
     *
     * @param roomId 直播间id
     * @return Fragment 直播间实例
     **/
    public static LiveAudienceRoomFragment getInstance(long roomId, @ILivePlaySource int playSource) {
        LiveAudienceRoomFragment fragment = new LiveAudienceRoomFragment();

        if (fragment.mBundle == null) {
            fragment.mBundle = new Bundle();
            fragment.setArguments(fragment.mBundle);
        }
        fragment.mBundle.putLong(ILiveFunctionAction.KEY_ROOM_ID, roomId);
        fragment.mBundle.putInt(ILiveFunctionAction.KEY_PLAY_SOURCE, playSource);
        fragment.mBundle.putInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE, LiveMediaType.TYPE_AUDIO);
        fragment.mBundle.putInt(PreferenceConstantsInLive.LIVE_KEY_LIVE_ANCHOR, PreferenceConstantsInLive.LiveAnchorType.TYPE_USER);

        return fragment;
    }

    @Override
    protected LiveAudienceRoomPresenter createPresenter() {
        return new LiveAudienceRoomPresenter(this, mConnectionManager, mRoomId);
    }

    private void registerLocalReceiver() {
        if (mChatRoomLocalBroadcastReceiver == null) {
            mChatRoomLocalBroadcastReceiver = new LocalBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_OPEN_LISTEN_AWARD);
            intentFilter.addAction(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE);
            intentFilter.addAction(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_CLOSE_CURRENT_PAGE);
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_CLICK_FREE_DANMAKU_MSG);
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_OPEN_INPUT_COMPONENT);
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_SHARE_ROOM);
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_GIFT_DIALOG);
            LocalBroadcastManager.getInstance(mContext).registerReceiver(mChatRoomLocalBroadcastReceiver, intentFilter);
        }
    }

    private void unregisterLocalReceiver() {
        if (mChatRoomLocalBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mChatRoomLocalBroadcastReceiver);
            mChatRoomLocalBroadcastReceiver = null;
        }
    }

    public void bottomClickShop() {
        if (!UserInfoMannage.hasLogined()) {
            endFullScreenLandscape();
            UserInfoMannage.gotoLogin(getContext());
            return;
        }

        ISealListComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_SEAL_LIST);
        if (null != component) {
            component.showGoodsList();
        }
    }

    @Override
    public void onGoodsOperationViewShow(boolean isShow) {
        if (!canUpdateUi()) {
            return;
        }

        isGoodsOperationViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
        if (!isShow) {
            return;
        }

        IRoomRightAreaComponent rightAreaComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_RIGHT_AD);
        if (rightAreaComponent != null) {
            rightAreaComponent.hideAdPromotion();
        }
    }

    @Override
    public void onTopOperationViewShow(boolean isShow) {
        if (!canUpdateUi()) {
            return;
        }

        isTopOperationViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    @Override
    public void onBottomOperationViewShow(boolean isShow) {
        if (!canUpdateUi()) {
            return;
        }

        isBottomOperationViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    @Override
    public void onAdPromotionShow(boolean isShow) {
        super.onAdPromotionShow(isShow);
        if (!canUpdateUi()) {
            return;
        }

        mIsAdPromotionShow = isShow;

        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    public void requestPlayMode(int playMode) {
        IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (null != component) {
            component.requestPlayMode(playMode);
        }
    }

    @Override
    public void showGuardListPage() {
        super.showGuardListPage();
    }

    public void showView() {
        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.showHeaderView();
        }

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.showBottomLayout();
        }
    }

    @Override
    public PlayerConstants.ResolutionRatio getVideoSizeRatio() {
        return mResolutionRatio;
    }


    public int getVideoWidth() {
        return mVideoWidth;
    }

    public int getVideoHeight() {
        return mVideoHeight;
    }


    public void updateResolutionRatioAndSize(PlayerConstants.ResolutionRatio resolutionRatio, int width, int height) {
        mResolutionRatio = resolutionRatio;

        if (width == 0 || height == 0) {
            return;
        }
        mVideoWidth = width;
        mVideoHeight = height;

        try {
            AudienceComponentHost componentHost = (AudienceComponentHost) getPresenter().getComponentHost();
            componentHost.dispatchVideoResolutionRatioUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public boolean isPking() {
        IVideoPkAudienceComponent component = getComponentSafety(COMPONENT_VIDEO_PK_AUDIENCE);
        if (component != null) {
            return RoomModeManager.isPkMode() || component.isPking();
        }
        return RoomModeManager.isPkMode();
    }

    @Override
    public void onPiaModeOpen() {
        super.onPiaModeOpen();
        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.updateStreamRoleType(BaseCommonProtoConstant.CommonUserType.USER_TYPE_AUDIENCE);
        }

        try {
            AudienceComponentHost componentHost = (AudienceComponentHost) getPresenter().getComponentHost();
            componentHost.dispatchPiaModeChanged(true);
        } catch (Exception e) {
            e.printStackTrace();
        }

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.showPiaModeUi();
        }
    }

    @Override
    public void onPiaModeClose() {
        super.onPiaModeClose();
        try {
            AudienceComponentHost componentHost = (AudienceComponentHost) getPresenter().getComponentHost();
            componentHost.dispatchPiaModeChanged(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPiaTuningClicked() {
        showPiaTuningDialog();
    }

    @Override
    public void clearTopPage() {
        getManageFragment().clearTopFragment(LiveScrollFragment.class.getName(), true);
    }

    //默认为麦下普通用户拉cdn的flv
    public boolean mIsOpenFlv = true;

    public void onAudienceGetHostPlayChange(boolean isOpenFlv) {
        if (mIsOpenFlv == isOpenFlv) {
            return;
        }
        mIsOpenFlv = isOpenFlv;
        if (mRoomDetail == null || mRoomDetail.getStatus() != PersonLiveBase.LIVE_STATUS_ING) {
            return;
        }
        if (mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_AUDIO) {
            if (isOpenFlv) {
                if (getRoomCore() != null && null != getRoomCore().getStreamManager()) {
                    getRoomCore().getStreamManager().stopPublishAndPlay();
                }
            } else {
                PlayTools.pause(getContext(), PauseReason.Business.LiveAudienceRoomFragment);
            }

        } else {
            if (!canUpdateUi()) {
                return;
            }

            IVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (playerComponent == null) {
                return;
            }

            if (isOpenFlv) {
                playerComponent.resumeLive();
            } else {
                playerComponent.stop();
                if (getCurOrientation() != Configuration.ORIENTATION_PORTRAIT) {//全屏模式下需要先旋转
                    requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                    CustomToast.showToast("连线中不支持屏幕翻转");
                }
            }
        }
    }


    public boolean mIsCalling = false;

    @Override
    public void onPhoneCallStateChanged(boolean isCalling) {
        super.onPhoneCallStateChanged(isCalling);
        if (mIsCalling == isCalling) {
            return;
        }
        mIsCalling = isCalling;

        if (mRoomDetail == null || mRoomDetail.getStatus() != PersonLiveBase.LIVE_STATUS_ING) {
            return;
        }

        if (mRoomDetail.getMediaType() == LiveMediaType.TYPE_AUDIO) {
            if (getRoomCore() != null && null != getRoomCore().getStreamManager()) {
                if (isCalling) {
                    getRoomCore().getStreamManager().stopPlayStream();
                } else {
                    getRoomCore().getStreamManager().startPlayStream();
                }
            }
        } else {
            if (!canUpdateUi()) {
                return;
            }

            IVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (playerComponent == null) {
                return;
            }

            if (isCalling) {
                playerComponent.stop();
            } else {
                playerComponent.resumeLive();
            }
        }

    }

    public void onMicViewShowing(boolean isShow) {
        getWaitComponent(AudienceCompConfig.COMPONENT_SELL_GOOD, new IComponentStateCallBack<ISellGoodsComponent>() {
            @Override
            public void onCreated(ISellGoodsComponent component) {
                component.setIsMicing(isShow);
            }
        });

        getWaitComponent(AudienceCompConfig.COMPONENT_RIGHT_AD, new IComponentStateCallBack<IRoomRightAreaComponent>() {
            @Override
            public void onCreated(IRoomRightAreaComponent component) {
                component.setIsMicing(isShow);
            }
        });

        if (!canUpdateUi()) {
            return;
        }

        isAudiMicViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    /**
     * 聊天列表组件是否需要右侧大间距
     */
    private boolean needChatListBigRightMargin() {
        return isAudiMicViewShow || isGoodsOperationViewShow ||
                isTopOperationViewShow || isBottomOperationViewShow || mIsAdPromotionShow;
    }


    public List<Anchor> getAnchorMicOrPKInfo() {
        IAudienceMicComponent audienceMicComponent = getComponentSafety(COMPONENT_AUDIENCE_MIC);
        if (audienceMicComponent == null) {
            return null;
        }
        List<Anchor> anchorMicInfo = audienceMicComponent.getAnchorMicInfo();
        if (anchorMicInfo != null && !anchorMicInfo.isEmpty()) {
            return anchorMicInfo;
        }

        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            List<Anchor> anchorPKInfo = friendModeComponent.getAnchorPKInfo();
            if (anchorPKInfo != null && !anchorPKInfo.isEmpty()) {
                return anchorPKInfo;
            }
        }

        return null;
    }


    public boolean isFollowAnchorDialogIsShowed() {
        return isFollowAnchorDialogShowed;
    }

    public void handleHalfScreenUrl(String url) {
        showCustomH5Dialog(url);
    }

    public void forbidFloatWindow() {
        IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (null != component) {
            component.forbidFloatWindow();
        }
    }

    protected boolean mIsPkPanelVisible = false;

    /**
     * SEI通知视频pk面板是否展示
     *
     * @param visible true: 展示 false: 隐藏
     */
    public void onPkPanelVisibility(final boolean visible) {
        CommonLiveLogger.d(TAG, "onPkPanelVisibility: " + visible);
        mIsPkPanelVisible = visible;
        if (!canUpdateUi()) {
            return;
        }

        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            friendModeComponent.setPkPanelVisible(visible);
        }
    }

    @Override
    public void onStateChanged(Integer state) {
        if (state == PlayerConstants.PLAYSTATE_ERROR) {
            if (isScrollFragmentHasAnimationPlaying()) {
                return;
            }
            setPlayerFailVisible(true);
        } else {
            setPlayerFailVisible(false);
        }
    }

    final class LocalBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || !canUpdateUi() || !isResumed() || null == mRoomDetail) {
                return;
            }

            if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_OPEN_LISTEN_AWARD.equals(intent.getAction())) {
                String url = intent.getStringExtra(BundleKeyConstants.KEY_EXTRA_URL);

                if (!UserInfoMannage.hasLogined()) {
                    endFullScreenLandscape();
                    UserInfoMannage.gotoLogin(mActivity);
                    return;
                }

                showCustomH5Dialog(url);
            } else if (CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE.equals(intent.getAction())) {
                //h5调原生发言
                String content = intent.getStringExtra(CommonXmlObjcJsCall.KEY_SEND_MESSAGE);
                if (!TextUtils.isEmpty(content)) {
                    sendMessage(content);
                }
            } else if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_CLICK_FREE_DANMAKU_MSG.equals(intent.getAction())) {
                // 点击弹幕体验卡消息，唤起输入面板，并填充文案
                String keyBoardText = intent.getStringExtra(ILiveFragmentAction.KEY_ACTION_CLICK_FREE_DANMAKU_MSG);
                if (keyBoardText == null) keyBoardText = "";
                danmuFreeClick(keyBoardText);
            } else if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_OPEN_INPUT_COMPONENT.equals(intent.getAction())) {
                // 打开弹幕输入面板，收起礼物面板
                CommonLiveLogger.d(TAG, "local broadcast receive inputPanelComponent");
                try {
                    IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
                    component.hide();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                danmuFreeClick("");
            } else if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_SHARE_ROOM.equals(intent.getAction())) {
                showLiveRoom();
            } else if (ILiveFragmentAction.LOCAL_BROADCAST_ACTION_GIFT_DIALOG.equals(intent.getAction())) {
                if (isRealVisable()) {
                    showGiftPanel();
                }
            } else if (CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_CLOSE_CURRENT_PAGE.equals(intent.getAction())) {
                try {
                    IExitRoomComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_EXIT_ROOM);
                    component.requestExitRoom(false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void danmuFreeClick(String keyBoardText) {
        try {
            IInputPanelComponent component = getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.showAndOpenDanMuWithBulletCount(keyBoardText);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, @Nullable Bundle savedInstanceState) {
        registerLocalReceiver();
        if (mCacheContentView != null) {
            return mCacheContentView;
        }
        mCacheContentView = super.onCreateView(inflater, container, savedInstanceState);

        if (privateChatViewModel != null) {
            privateChatViewModel.getPrivateChatModel().observe(getViewLifecycleOwner(), number -> {
                if (mMoreActionDialog != null) {
                    mMoreActionDialog.updatePrivateChatMenuRedPoint(number > 0);
                }
            });
        }
        if (mAudienceRoomViewModel != null) {
            mAudienceRoomViewModel.getPopupAvatarInfoData().observe(getViewLifecycleOwner(), this::showPopupAvatarInfo);
        }

        return mCacheContentView;
    }

    private void showPopupAvatarInfo(LiveSurprise liveSurprise) {
        if (canUpdateUi() && liveSurprise.getPopup()) {
            LivePopAvatarInfoDialog dialog = new LivePopAvatarInfoDialog();
            dialog.setMGiftInfo(liveSurprise);
            LiveDialogFragmentManager.INSTANCE.addDialog(dialog, getChildFragmentManager());
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mFragmentImplHelper == null) {
            mFragmentImplHelper = new LiveRoomImplHelper(this);
        }
        isFollowAnchorDialogShowed = false;
        XmPlayerManager.getInstance(mContext).addPlayerStatusListener(mFragmentImplHelper.getXmPlayerStatusListener());
        originChatRoomFragmentOnCreate();
        mInputMode = getWindow().getAttributes().softInputMode;
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        NotifyFollowerManager.getImpl().updateConfigure();
        UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
        AnchorFollowManage.getSingleton().addFollowListener(this);

        privateChatViewModel = new ViewModelProvider(this).get(PrivateChatViewModel.class);
        mAudienceRoomViewModel = new ViewModelProvider(this).get(LiveAudienceRoomViewModel.class);
        addDebugHandler();
    }

    private IRoomDebugEventHandler debugHiEventHandler;

    private IRoomDebugEventHandler debugFollowEventHandler;

    private IRoomDebugEventHandler debugFansEventHandler;

    private void addDebugHandler() {
        if(ConstantsOpenSdk.isDebug){
            debugHiEventHandler = item -> {
                showChatCHiGuide();
                return true;
            };
            RoomDebugConfigure.addDebugHandler("点击展示C区打招呼引导弹窗", "audie", debugHiEventHandler);
            debugFollowEventHandler = item -> {
                showChatCFollowGuide();
                return true;
            };
            RoomDebugConfigure.addDebugHandler("点击展示C区关注我引导弹窗", "audie", debugFollowEventHandler);
            debugFansEventHandler = item -> {
                showChatCFansGuide();
                return true;
            };
            RoomDebugConfigure.addDebugHandler("点击展示C区加粉团引导弹窗", "audie", debugFansEventHandler);
            audienceRoomResourceDebug = new LiveAudienceRoomResourceDebug();
            audienceRoomResourceDebug.debug(this);
        }
    }

    private void showChatCHiGuide() {
        IAVChatListGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showNewbieSayHiGuide();
    }

    private void showChatCFollowGuide() {
        IAVChatListGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showFollowHostWithSayHiGuide();
    }

    private void showChatCFansGuide() {
        IAVChatListGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showFansClubGuide();
    }

    protected void originChatRoomFragmentOnCreate() {
        if (getRoomId() > 0) {
            new UserTracking().setLiveRoomId(String.valueOf(getRoomId()))
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_LIVE_VIEW);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 163840;
        super.onMyResume();
        if (mOriginalSystemUiVisibility == 0) {
            mOriginalSystemUiVisibility = getWindow().getDecorView().getSystemUiVisibility();
        }
        if (!isFullScreenLandscapeVideoRoom()) {
            restorePortraitSystemUiVisibility();
        } else {
            StatusBarManager.hideStatusBar(getWindow(), true);
            updateSystemUiVisibility();
        }
        if (!isFirstResume) {
            resumePlay();
        }
        isFirstResume = false;
    }

    @Override
    public void onPageSelectedWithoutPlayUrl(long roomId, long liveId) {
        if (mRoomDetail != null && mRoomDetail.getRoomId() == roomId && (liveId == 0 || mRoomDetail.getLiveId() == liveId)) {
            //快速上下滑返回原直播间如果没有预加载音视频则恢复播放
            resumePlay();
        }
    }

    private void beginLoadComponents() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            internalLoadComponents();
        } else {
            postOnUiThread(new Runnable() {
                @Override
                public void run() {
                    internalLoadComponents();
                }
            });
        }
    }

    private void internalLoadComponents() {
        if (isLoad) {
            return;
        }
        isLoad = true;
        mPresenter.getComponentManager().beginComponentsLoader().load();
    }

    @Override
    public void onPageSelected(int position) {
        if (!canUpdateUi()) {
            return;
        }
        try {
            IVideoPlayerComponent playerComponent = mPresenter.getComponentManager().getComponent(COMPONENT_VIDEO_PLAYER);
            if (playerComponent != null) {
                playerComponent.onPageSelected(position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void updateSystemUiVisibility() {
        // 全屏显示，隐藏状态栏和导航栏，拉出状态栏和导航栏显示一会儿后消失。
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
    }

    public boolean isKeyboardDialogShowing() {
        IInputPanelComponent panelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != panelComponent) {
            return panelComponent.isKeyboardPanelShowed();
        }
        return false;
    }

    public boolean isFragmentRealVisible() {
        return isRealVisable();
    }

    @Override
    public void onNetWorkChanged(boolean networkAvailable, boolean isWifi) {
        super.onNetWorkChanged(networkAvailable, isWifi);
        if (!canUpdateUi()) return;

        if (networkAvailable) resumePlay();
    }

    public void resumePlay() {
        if (getStreamManager() != null && mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_AUDIO) {
            if (premiereFlag && mPremiereInfo != null) {
                premierePlay(mPremiereInfo);
            } else {
                getStreamManager().startPlayStream();
            }
        }
        if (mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_VIDEO) {
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.resumeLive();
            }
        }
    }

    @Override
    public void onReceiveRoomCloseMessage(String reason) {
        super.onReceiveRoomCloseMessage(reason);

        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.dismissDialog();
        }

        if (mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_VIDEO) {
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.stop();
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        removeDelayTasks();
        getWindow().setSoftInputMode(mInputMode);
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(mFragmentImplHelper.getXmPlayerStatusListener());
        NotifyFollowerManager.getImpl().destroy();

        AnchorFollowManage.getSingleton().removeFollowListener(this);
        if (getStreamManager() != null) {
            getStreamManager().destroy(!LivePlayRestoreUtil.isClickMinimumLiveRoom());
            getStreamManager().removeStreamPlayStateListener(this);
        }
        unregisterLocalReceiver();
        if (UserInfoMannage.getUid() > 0) {
            ChatUserAvatarCache.self().removeAvatar(UserInfoMannage.getUid());
        }
        exitLiveFullScreen(isFullScreenLandscapeVideoRoom());
        dismissBottomBarMoreActionPanel();
    }

    @Override
    public void onDestroy() {
        releaseInternal();
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
        super.onDestroy();
    }


    private void releaseInternal() {
        if (mReleased) {
            return;
        }
        LiveGlobalDispatcher.getInstance().notifyRoomDestroyed(new LiveGlobalDispatcher.RoomInfo());

        LiveGlobalDispatcher.release();
        LivePkHelper.releaseInstance();

        LiveGiftLoader.release(LiveGiftLoader.class);
        LoveModeMicStateManager.getInstance().removeAllObservers();
        LiveCommonEmojiManager.getInstance().release();

        mReleased = true;
        PremierePlayStatisticsManager.getInstance().release();

    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        getComponentSafety(COMPONENT_VIDEO_PLAYER);

        super.initUi(savedInstanceState);
    }

    @Override
    protected void initMyUi(Bundle savedInstanceState) {
        super.initMyUi(savedInstanceState);
        mTopLayout = findViewById(R.id.live_title_bar);
        mBottomLayout = findViewById(R.id.live_bottom_layout);
    }

    @Override
    protected void startRoomLoading() {
        super.startRoomLoading();
        if (mRoomDetail == null) {
            ViewStatusUtil.setVisible(View.INVISIBLE, mBottomLayout);
        }
    }

    @Override
    protected boolean shouldShowLoadingView() {
        ILiveMediaSource liveRecord = PreloadPlayerManager.getInstance().getCurrentLiveRecord();
        //视频直播不展示中间的loading框
        if (mRoomDetail == null && !(liveRecord != null && mRoomId > 0 && liveRecord.getRelativeRoomId() == mRoomId && liveRecord.getType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO)) {
            return true;
        }
        return false;
    }

    @Override
    protected void loadMyData() {
        super.loadMyData();
        if (mMakeFriendsAudience == null) {
            mMakeFriendsAudience = new LoveModeGuest(getContext());
            getLifecycle().addObserver(mMakeFriendsAudience);
            mMakeFriendsAudience.setRoomFragment(mFragmentImplHelper.getAudienceRoomFragment());
            mFriendActionCallback = (AudienceActionCallback) mMakeFriendsAudience.getActionCallback();
        }
        getWaitComponent(COMPONENT_FRIEND_MODE, new IComponentStateCallBack<IInteractivePlayComponent>() {
            @Override
            public void onCreated(@NonNull IInteractivePlayComponent component) {
                component.setActionCallback(mFriendActionCallback);
            }
        });
        ILiveMediaSource liveRecord = PreloadPlayerManager.getInstance().getCurrentLiveRecord();
        if (liveRecord != null && mRoomId > 0 && liveRecord.getRelativeRoomId() == mRoomId && !TextUtils.isEmpty(liveRecord.getPlayUrl())) {
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                if (liveRecord.getType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO) {
                    mLiveMediaType = LiveMediaType.TYPE_VIDEO;
                    component.setPlayUrl(liveRecord.getPlayUrl(), PlayerConstants.PLAYTYPE_LIVE, getVideoSizeRatio());
                } else if (liveRecord.getType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO && getStreamManager() != null) {
                    mLiveMediaType = LiveMediaType.TYPE_AUDIO;
                    component.hidePlayer();
                }
            }
        }
    }

    @Override
    public void onReceiveHostOnlineListMessage(CommonChatRoomOnlineUserListMsg msg) {
        super.onReceiveHostOnlineListMessage(msg);
        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (headerComponent != null) {
            headerComponent.onOnlineStatusChange(msg);
        }
    }

    @Override
    public void onReceiveMyInfoUpdateMessage() {
        super.onReceiveMyInfoUpdateMessage();
        IInputPanelComponent panelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != panelComponent) {
            panelComponent.onReceiveMyInfoUpdateMessage();
            panelComponent.onReceiveMyInfoUpdateMessage();
        }
        loadMoreMenuInfo();
    }

    @Override
    public void onReceivedHeadLinesMessage(HeadAnchorInfo message) {
        IAudienceHeaderComponent component = getComponentSafety(COMPONENT_HEADER);
        if (component != null) {
            component.receiveTopHeadlinesMsg(message);
        }
    }

    @Override
    public void switchRoom(long roomId, Bundle bundle) {
        mCdnStatus = CdnStatus.CDN_STATUS_START;
        isFollowAnchorDialogShowed = false;
        removeDelayTasks();
        LivePlayRestoreUtil.markLastEnterRoomId(mRoomId);
        LivePlayRestoreUtil.markClickExitLiveRoomButton(false);
        LivePlayRestoreUtil.markClickMinimumLiveRoom(false);
        mIsOpenFlv = true;
        mIsPkPanelVisible = false;
        mStopDialogShowed = false;
        if (mLiveMediaType == LiveMediaType.TYPE_AUDIO) {
            PreloadPlayerManager.getInstance().setRoomFragmentSurfaceWillDestroy(false);
        }
        getNewComponentManager().onSwitchRoom();
        super.switchRoom(roomId, bundle);

        CommonPrivateChatComponent chatComponent = getComponentSafety(AudienceCompConfig.COMPONENT_PRIVATE_CHAT_SHOW);
        if (chatComponent != null) {
            chatComponent.hidePrivateChatView(false);
        }

        PremierePlayStatisticsManager.getInstance().release();
        dismissBottomBarMoreActionPanel();
    }

    @Override
    public void beforeSwitchRoom(@NonNull RecommendLiveRecord newRecord, @Nullable RecommendLiveRecord oldRecord) {
        super.beforeSwitchRoom(newRecord, oldRecord);
        if (oldRecord != null
                && oldRecord.getRelativeRoomId() == getRoomId()
                && oldRecord.getType() == getRoomBizType()
                && newRecord.getRelativeRoomId() != getRoomId()) {
            hideTotalDialog();
        }
    }

    /**
     * 隐藏直播间所有的弹框
     */
    void hideTotalDialog() {
        dismissBottomBarMoreActionPanel();
        hideAllDialog();
        hideMoreLiveDialog();
        hideMakeFriendsDialog();
        dismissTopicAndNoticeDialog();
        dismissCustomH5Dialog();
        hideRoomSoftInput();
        if (mUserPop != null) {
            mUserPop.dismiss();
        }
        ICouponComponent couponComponent = getExistComponent(IBaseRoomCompConfig.COMPONENT_COUPON);
        if (couponComponent != null) {
            couponComponent.dismissCouponDialog();
        }
        LiveDialogFragmentManager.INSTANCE.hideAllDialog();
    }

    @Override
    protected void switchToNewRoom(long roomId) {
        isFollowAnchorDialogShowed = false;
        if (getStreamManager() != null && mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_AUDIO) {
            getStreamManager().destroy(true);
        }
        super.switchToNewRoom(roomId);
        removeDelayTasks();
        dismissCustomH5Dialog();
    }

    @Override
    protected void onDisconnectChatRoom() {
        super.onDisconnectChatRoom();
        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.onDisconnectChatRoom();
        }
    }

    @Override
    protected void onConnectingChatRoom() {
        super.onConnectingChatRoom();
        getWaitComponent(COMPONENT_BOTTOM_BAR, new IComponentStateCallBack<IBottomBarComponent>() {
            @Override
            public void onCreated(IBottomBarComponent component) {
                component.updateInputViewStatus(BottomBarComponent.LOG_ING);
            }
        });
    }

    @Override
    protected void onConnectedChatRoom() {
        super.onConnectedChatRoom();
        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.onConnectChatRoom();
        }

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.updateInputViewStatus(BottomBarComponent.LOG_SUCCESS);
        }
    }

    @Override
    protected void onHitHighMemory() {
        super.onHitHighMemory();
        if (LiveMemoryMMKVManager.INSTANCE.getMemoryABTestKey()) {
            IAVChatListGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE);
            if (comp != null) comp.showSilkyGuide();
        }
    }

    @Override
    protected void onKickOutChatRoom() {
        super.onKickOutChatRoom();
        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.onKickOutChatRoom();
        }
        handleKickOutUser();

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.updateInputViewStatus(BottomBarComponent.LOG_OTHER_DEVICE);
        }
    }

    @Override
    public void onReceiveFansClubStatusMessage(LiveFansClubStatusModel message) {
        if (message == null) {
            return;
        }

        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.updateFansClubActiveStatus(message);
        }
        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            friendModeComponent.updateFansClubActiveStatus(message);
        }
    }

    /**
     * 收到信令 处理粉丝团亲密度
     *
     * @param msg 更新房间亲密度倍数消息
     */
    @Override
    public void onReceiveUpdateFansIntimacyMsg(CommonChatFansIntimacyMsg msg) {
        Logger.i(TAG, "onReceiveNotifyBottomButton, msg = " + msg);
        super.onReceiveUpdateFansIntimacyMsg(msg);
        if (null == msg || null == mRoomDetail) {
            return;
        }

        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.onReceiveUpdateFansIntimacyMsg(msg);
        }

        // 音频官播间 更新亲密度view
        if (mRoomDetail.getRoomFansClubVo().getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_JOINED
                && System.currentTimeMillis() < getRoomDetail().roomFansClubVo.getAdditionEndAt()) {
            IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
            if (null != friendModeComponent) {
                friendModeComponent.setFansIntimacy(getRoomDetail().roomFansClubVo.getAdditionTimes(),
                        getRoomDetail().roomFansClubVo.getAdditionEndAt(), getRoomBizType(), true);
            }
        }
    }




    @Override
    public void onReceiveOfficialWelcomeMessage(final LiveOfficialWelcomeMessage welcomeMessage) {
        if (welcomeMessage.getTemplateId() == 0) {
            return;
        }

        final BaseGiftLoader<?> giftPreLoader = createGiftLoader();
        if (giftPreLoader == null) {
            ConnectLogUtilWrapper.log(getRoomBizType(), "onReceiveGiftMessage giftPreLoader未初始化:");
            return;
        }
        if (mRoomDetail != null) {
            giftPreLoader.setBizType(mRoomDetail.getRoomBizType())
                    .setLiveId(mRoomDetail.getLiveId());
        }

        LiveTemplateManager.getInstance().getTemplateByIdCallback(welcomeMessage.getTemplateId(), new ILiveTemplateByIdCallback() {
            @Override
            public void onSuccess(@Nullable LiveTemplateModel.TemplateDetail templateDetail) {
                Logger.d(TAG, "单个模板资源请求成功, templateDetail = " + templateDetail);
                if (templateDetail == null) {
                    return;
                }
                //资源存在，添加到播放队列
                Pair<Integer, String> animDownloadPathByTemplateId = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(welcomeMessage.getTemplateId());
                giftPreLoader.setDynamicType(0, animDownloadPathByTemplateId.second);
                addToBigGiftTask(new GiftShowTask(welcomeMessage, giftPreLoader));
            }

            @Override
            public void onFailed(@Nullable String message) {
                LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT, "单个模板资源请求失败， giftMessage= " + message);
                GiftShowTask task = new GiftShowTask(welcomeMessage, giftPreLoader);
                task.setResourceDownloadStatus(IPlayAnimTask.ResourceDownloadStatus.STATUS_FAILED);
                addToBigGiftTask(task);
            }
        });
    }

    @Override
    public void onReceiveNewbieGuideMsg(LiveNewbieGuideMsg msg) {
        LiveNewbieGuideManager.onReceiveGuidePushMsg(getRoomBizType(), msg);
        // 收到推送消息，被动触发引导显示
        INewbieGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_NEWBIE_GUIDE);
        if (comp != null) comp.onReceiveGuidePushMsg();
    }

    @Override
    public void onReceiveAiSoundPenaltyMessage(LiveAiSoundPenalty message) {
        ILiveAiPropComponent component = getComponentSafety(COMPONENT_AI_PENALTY);
        if (component != null) {
            component.onReceiveAiPropMessage(message);
        }
    }

    @Override
    public void onReceiveCommonActGiftGuideMsg(LiveCommonActGiftGuideMsg msg) {
        if (shouldDisableSomeDialogForPremiereRoom()) {
            return;
        }
        if (isScrollFragmentHasAnimationPlaying()) {
            return;
        }
        try {
            IAVCommonGuideComponent comp = getComponentHost().getComponentManager().getComponent(
                    IBaseRoomCompConfig.COMPONENT_COMMON_GUIDE
            );
            comp.onReceiveCommonActGiftGuideMsg(msg);
        } catch (Exception e) {
            CustomToast.showDebugFailToast(e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    protected String getTraceName() {
        return "房间-观众端直播间";
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.liveaudience_fra_room;
    }

    @Override
    protected View onCreateView(ViewGroup container) {
        boolean preloadRootView = LiveSettingManager.getBooleanConfig("live_room_av_root_view_preload", true);
        Logger.d(TAG, "onCreateView preloadRootView " + preloadRootView);
        if (!preloadRootView) {
            return super.onCreateView(container);
        }
        try {
            View previewView = LiveRouter.getLamiaAction().getRoomLiveData().getPreLoadRoomRootView();
            Logger.d(TAG, "onCreateView  previewView " + previewView);
            if (previewView != null) {
                return previewView;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return super.onCreateView(container);
    }

    @Override
    public IXmMicService getAvService() {
        return LiveClientManager.getInstance().getLiveMicService();
    }

    @Override
    public void onRequestRoomDetailSuccess(ILiveRoomDetail roomDetail) {
        LivePageAvailabilityUtil.postSucceed(LivePageAvailabilityConst.AUDIENCE_ROOM, roomDetail);

        if (roomDetail == null) {
            notifyTracePageFailed();
            showNormalBackground();
            return;
        }

        // ComponentsManager 先赋值 mediaType ，后续再执行组件 BindData， 才能正确处理
        if (roomDetail instanceof PersonLiveDetail) {
            mLiveMediaType = roomDetail.getMediaType();
        }

        // ComponentsManager 再执行 BindData
        super.onRequestRoomDetailSuccess(roomDetail);

        if (!canUpdateUi()) {
            notifyTracePageFailed();
            return;
        }

        beginLoadComponents();

        if (!(roomDetail instanceof PersonLiveDetail)) {
            notifyTracePageFailed();
            return;
        }
        if (roomDetail.getRoomId() != mRoomId) {
            notifyTracePageFailed();
            showNormalBackground();
            return;
        }

        if (((PersonLiveDetail) roomDetail).getLiveRecordInfo() != null) {
            //更新主播推流状态信息
            onReceiveCdnStatusMessage(new CommonCdnStatusMessage(((PersonLiveDetail) roomDetail).getLiveRecordInfo().alive ?
                    CdnStatus.CDN_STATUS_START : CdnStatus.CDN_STATUS_STOP));
            if (!((PersonLiveDetail) roomDetail).getLiveRecordInfo().alive) {
                LivePageEnterTimeManager.getInstance().setLiveStatus(PersonLiveBase.LIVE_STATUS_NOTICE);
            }
        }


        if (mLiveMediaType == LiveMediaType.TYPE_AUDIO) {
            changeRoomContentMargin(8f);
            if (roomDetail.getStatus() == PersonLiveBase.LIVE_STATUS_ING && !TextUtils.isEmpty(roomDetail.getPlayUrl())) {
                RecommendLiveRecord liveRecord = LiveScrollDataManager.getInstance().getCurrentRoomRecord();
                if (liveRecord != null && liveRecord.roomId == roomDetail.getRoomId() && liveRecord.bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO && !premiereFlag) {
                    liveRecord.playUrl = roomDetail.getPlayUrl();
                    Logger.i("duruochen--preload", "为上下滑model设置音频直播 url");
                }

                //解决快速上下滑动的时序问题
                if (liveRecord != null && liveRecord.roomId != roomDetail.getRoomId()) {
                    LiveHelper.Log.i("duruochen--preload", "解决快速上下滑动的时序问题：详情接口慢了(音频直播)");
                    return;
                }

                if (getStreamManager() != null && mRoomDetail != null && !premiereFlag) {
                    LiveLogUtil.log("drc-playstream", "fragment start");
                    getStreamManager().setMediaSourceAndRoomDetail(mRoomDetail.getLivePlaySourceInfo(), mBusinessId, PlayableModel.KIND_LIVE_FLV, roomDetail.getPlayUrl(), XMediaplayerImpl.TYPE_AUDIO);
                }
            }
        } else {
            changeRoomContentMargin(1f);
            LiveVideoPlayerManager.getInstance().setRoomDetail(
                    mRoomDetail.getLivePlaySourceInfo(),
                    PlayableModel.KIND_LIVE_FLV,
                    BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO
            );

            IVideoPlayerComponent pc = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (pc != null) {
                switch (roomDetail.getStatus()) {
                    case PersonLiveBase.LIVE_STATUS_END:
                        pc.setLiveFinish(true);
                        LiveVideoPlayerManager.getInstance().pause();
                        break;
                    case PersonLiveBase.LIVE_STATUS_NOTICE:
                        pc.setLiveFinish(true);
                        break;
                    case PersonLiveBase.LIVE_STATUS_ING:
                        // 去看看Adapter的ItemLayout里有没有视频预加载的VideoView
                        pc.setLiveFinish(false);
                        RecommendLiveRecord liveRecord = LiveScrollDataManager.getInstance().getCurrentRoomRecord();
                        String url = roomDetail.getPlayUrl();
                        if (TextUtils.isEmpty(url)) {
                            CustomToast.showDebugFailToast("直播间流地址为空");
                            LiveXdcsUtil.doXDCS(TAG, "详情的拉流地址为空");
                        }
                        if (liveRecord != null && liveRecord.roomId == roomDetail.getRoomId() && (liveRecord.bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO
                                || liveRecord.bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO)) {
                            liveRecord.bizType = BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO;
                            liveRecord.playUrl = url;
                            Logger.i("duruochen--preload", "为上下滑model设置视频直播 url");
                        }

                        //解决快速上下滑动的时序问题
                        if (liveRecord != null && liveRecord.roomId != roomDetail.getRoomId()) {
                            LiveHelper.Log.i("duruochen--preload", "解决快速上下滑动的时序问题：详情接口慢了(视频直播)");
                            return;
                        }
                        pc.setPlayUrl(url, PlayerConstants.PLAYTYPE_LIVE, getVideoSizeRatio());
                        break;

                    default:
                        break;

                }
            }
        }

        //关播弹窗 需要再这里补充判断，避免直接进入关播直播间不展示
        if (roomDetail.getStatus() == PersonLiveBase.LIVE_STATUS_END && !TextUtils.isEmpty(roomDetail.getPlayUrl())) {
            showFinishDialogForAudience(mRoomDetail == null ? -1 : mRoomDetail.getStatus());
        }

        if (mRoomDetail != null && mFragmentImplHelper != null) {
            mFragmentImplHelper.setDetail(mRoomDetail);
        }
        if (mRoomDetail == null) {
            hideLoadingViewDelay(mRoomId);
            showRoomLoadErrorView();
            CustomToast.showDebugFailToast("onCurrentRoomDetail detail null");
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, mBottomLayout);
            IRoomLoadingComponent component = getComponentSafety(COMPONENT_ROOM_LOADING);
            if (null != component) {
                component.hideRequestLoading();
                component.hideRequestErrorView();
            }
        }
        if (mLamiaRoomDetail != null && roomDetail.getRoomId() != mLastRoomId) {
            //手动调用，提供左右滑曝光埋点
            PluginAgent.onFragmentResume(this);
        }
        mLamiaRoomDetail = roomDetail;
        mLastRoomId = roomDetail.getRoomId();
        if (((PersonLiveDetail) roomDetail).getLiveRecordInfo() != null) {
            mBundle.putLong(ILiveFunctionAction.KEY_LIVE_TYPE, ((PersonLiveDetail) roomDetail).getLiveRecordInfo().bizType);
        }
        mBundle.putInt(PreferenceConstantsInLive.LIVE_KEY_LIVE_SECOND_TYPE, RoomModeManager.getInstance().getRoomMode());
        mBundle.putLong(ILiveFunctionAction.KEY_LIVE_ID, roomDetail.getLiveId());
        mBundle.putLong(PreferenceConstantsInLive.LIVE_KEY_ANCHOR_UID, roomDetail.getHostUid());
        mBundle.putBoolean(PreferenceConstantsInLive.LIVE_KEY_IS_FOLLOWED, roomDetail.isFollowed());
        mBundle.putInt(PreferenceConstantsInLive.LIVE_KEY_LIVE_STATUS, roomDetail.getStatus());

        notifyTracePageEnd();

        getComponentHost().dispatchRoomHalfModeChange(false);

        doDelayTasksAfterRequestRoomDetailSuccess();

        mPresenter.getComponentHost().dispatchHostData(mRoomDetail);

        if (!LivePlayRestoreUtil.isClickMinimumLiveRoom()) {
            // 非最小化恢复情况下进房，请求关系动画信息
            loadRelationshipAnimation();
        }

        // 加载运营位信息，组件内已做延迟加载
        loadRoomRightOperations();

        showNormalBackground();
    }

    /**
     * 在视频播放模式下，修改顶部margin
     */
    private void changeRoomContentMargin(float margin) {
        if (mBottomLayout != null && getContext() != null) {
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(mBottomLayout.getLayoutParams());
            params.addRule(RelativeLayout.BELOW, R.id.live_room_header_all);
            params.topMargin = BaseUtil.dp2px(getContext(), margin);
            mBottomLayout.setLayoutParams(params);
        }
    }

    @Override
    public void getWaitComponent(String name, IComponentStateCallBack callBack) {
        try {
            getNewComponentManager().getWaitComponent(name, callBack);
        } catch (Exception e) {
            e.printStackTrace();

            Logger.e(TAG, "getWaitComponent error, compName =  " + name + "\n"
                    + Log.getStackTraceString(e));
        }
    }

    /**
     * 请求更多菜单数据
     */
    private void loadMoreMenuInfo() {
        CommonRequestForCommon.getLiveMenuData(getRoomBizType(), mRoomId, mHostUid,
                IMoreMenuUserType.TYPE_USER,
                new IDataCallBack<LiveMenuData>() {
                    @Override
                    public void onSuccess(@Nullable LiveMenuData object) {
                        mMoreMenuModel = object;
                        if (mMoreActionDialog != null) {
                            mMoreActionDialog.notifyMoreMenuDialog(mMoreMenuModel, getRoomBizType());
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        Logger.i(TAG, "requestMoreMenuList error, errorCode = " + code + ", errorMsg = " + message);
                    }
                });
    }

    private void dismissDialog() {
        if (mGuardDialogFragment != null) {
            mGuardDialogFragment.dismiss();
        }
    }

    private void loadGuardExpireNotice() {
        if (!canUpdateUi() || !UserInfoMannage.hasLogined()) {
            return;
        }
        CommonRequestForLive.requestGuardExpireMessage(IBusinessIdConstants.BIZ_ID_AUDIO_PERSONAL, mRoomId, mHostUid, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                //调用接口成功后服务端会发送过期提醒消息
            }

            @Override
            public void onError(int code, String message) {
                Logger.d(TAG, "守护过期接口请求失败");
            }
        });
    }


    protected void hideLoadingViewDelay(final long roomId) {
        postOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
                if (roomId != mRoomId || !canUpdateUi()) {
                    return;
                }
                ViewStatusUtil.setVisible(View.VISIBLE, mBottomLayout);

                IRoomLoadingComponent component = getComponentSafety(COMPONENT_ROOM_LOADING);
                if (null != component) {
                    component.hideRequestLoading();
                }
            }
        }, 500);
    }

    @Override
    public void onRequestRoomDetailError(long roomId, int code, String message) {
        super.onRequestRoomDetailError(roomId, code, message);

        LivePageAvailabilityUtil.postFail(LivePageAvailabilityConst.AUDIENCE_ROOM, code, message);

        if (!canUpdateUi()) {
            return;
        }
        IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (null != component) {
            component.hidePlayer();
        }
        if (roomId != mLastRoomId) {
            //手动调用，提供左右滑曝光埋点
            PluginAgent.onFragmentResume(this);
        }
        mLastRoomId = roomId;
    }

    @Override
    protected boolean isKickOutErrorCode(int code) {
        return code == 1000;
    }

    public void bottomClickLineFunction() {
        //检查登陆状况
        if (!UserInfoMannage.hasLogined()) {
            endFullScreenLandscape();
            UserInfoMannage.gotoLogin(getContext());
            return;
        }
        IAudienceMicComponent micComponent = getComponentSafety(COMPONENT_AUDIENCE_MIC);
        if (micComponent == null) {
            return;
        }
        if (micComponent.getUserMicStatus() == UserStatus.USER_STATUS_MICING) {
            micComponent.showMicUserDialog();
        } else if (micComponent.getUserMicStatus() == UserStatus.USER_STATUS_WAITING) {
            micComponent.showCancelRequestMicDialog();
        } else {
            micComponent.showSelectMicTypeDialog();
        }
    }

    public void onFriendModeOperationClick() {
        if (mFriendActionCallback != null) {
            mFriendActionCallback.onMicOperationClick();
        }
    }

    public void onFriendModeRequestGetMicClick() {
        if (LoveModeManager.getInstance().notInjectManager()) {
            if (getRoomCore() != null) {
                LoveModeManagerInjectUtil.injectLoveMessageManager(getRoomCore().getLoveMessageManager());
                LoveModeManagerInjectUtil.injectLoveMessageDispatcherManager(getRoomCore().getLoveMessageDispatcherManager());
            }
            LoveModeManagerInjectUtil.injectRoomId(mRoomId);

            if (mMakeFriendsAudience != null) {
                mMakeFriendsAudience.initAfterJoinChatRoom();
            }
        }

        if (mMakeFriendsAudience != null && mMakeFriendsAudience.getActionCallback() != null) {
            mMakeFriendsAudience.getActionCallback().onSeatWaitingQueueClick();
        }
    }

    public void dismissMicEmotionDialog() {
        if (mLiveMicEmotionDialog != null) {
            mLiveMicEmotionDialog.dismiss();
        }
    }

    public boolean isSendGiftDialogShow() {
        try {
            IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
            return component.isGiftPanelShowing();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    public void retryLogin() {
        if (mConnectionManager != null && !mConnectionManager.isConnected()) {
            closeRoomImConnection();
            joinRoomImConnection();
        }
    }

    @Override
    protected void parseBundle() {
        super.parseBundle();
        try {
            Bundle arguments = getArguments();

            if (arguments == null) {
                return;
            }
            mLiveMediaType = arguments.getInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void bottomClickSendGift(long giftId) {
        if (giftId > 0) {
            showGiftPanelByGiftId(giftId, 0);
        } else {
            try {
                IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
                component.show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 退出全屏模式
     */
    public void endFullScreenLandscape() {
        if (isFullScreenLandscapeVideoRoom()) {
            //全屏模式退出
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
            }
        }
    }

    public void bottomClickShare() {
        shareLive();
    }

    public void bottomClickMoreAction() {
        showActionMoreDialog();
    }

    public void bottomClickPiaDrama() {
        // 看本
        PiaScriptListFragment fragment = PiaScriptListFragment.newInstance(
                mRoomId, mHostUid, mRoomDetail.getLiveId(), IBusinessIdConstants.BIZ_ID_AUDIO_PERSONAL
        );
        startFragment(fragment);
    }

    /**
     * 底部工具栏-更多操作面板
     */
    private LiveMoreMenuDialog mMoreActionDialog;

    @Override
    public int getLiveMediaType() {
        return mLiveMediaType;
    }

    @Override
    public PrivateChatViewModel getPrivateChatViewModel() {
        return privateChatViewModel;
    }

    @Override
    public boolean isAnchor() {
        return mRoomDetail != null
                && mRoomDetail.getLiveAnchorInfo() != null
                && mRoomDetail.getLiveAnchorInfo().uid == UserInfoMannage.getUid();
    }

    private void showActionMoreDialog() {
        int userType = LiveMoreMenuDialog.USER_TYPE_NORMAL;
        if (isAnchor() && isFromHostFragment()) {
            userType = LiveMoreMenuDialog.USER_TYPE_HOST;
        } else if (currentUserIsAdmin()) {
            userType = LiveMoreMenuDialog.USER_TYPE_ADMIN;
        }
        if (mMoreActionDialog == null) {
            mMoreActionDialog = new LiveMoreMenuDialog(getFragment());
        }

        long privateChatMessageCount = 0;
        if (privateChatViewModel != null && privateChatViewModel.getPrivateChatModel().getValue() != null) {
            privateChatMessageCount = privateChatViewModel.getPrivateChatModel().getValue();
        }

        // Pia 戏模式下，技术层面为交友模式，但业务层面对用户的感知为非交友模式
        boolean friendMode = !isPiaMode() && isFriendsMode();
        mMoreActionDialog.setConfig(
                        new LiveMoreMenuDialog.StateConfig.Builder()
                                .isMute(mIsMute)
                                .isOpenFriendMode(friendMode)
                                .isOpenPiaMode(isPiaMode())
                                .userType(userType)
                                .isPkMode(RoomModeManager.isPkMode())
                                .menuType(IMoreMenuType.TYPE_MORE)
                                .isCanSendPic(mPresenter.mCanSendPic)
                                .isOfficialRoom(officialRoomFlag)
                                .isPrivateChatRedPointShow(privateChatMessageCount > 0)
                                .build())
                .setOnMoreItemOnclickListener(getMoreItemOnclickListener());
        mMoreActionDialog.notifyMoreMenuDialog(mMoreMenuModel, getRoomBizType());
        mMoreActionDialog.show(getChildFragmentManager(), LiveMoreMenuDialog.class.getName());

        new XMTraceApi.Trace()
                .setMetaId(33448)
                .setServiceId("dialogView")
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    public LiveMoreMenuDialog.IOnMoreItemOnclickListener getMoreItemOnclickListener() {
        return mMoreItemOnclickListener;
    }

    private boolean mIsMute;

    /**
     * 主播端/管理员端更多操作面板的点击监听
     */
    private final LiveMoreMenuDialog.IOnMoreItemOnclickListener mMoreItemOnclickListener
            = new LiveMoreMenuDialog.IOnMoreItemOnclickListener() {
        @Override
        public void onClickTopicItem() {
            if (!canUpdateUi() || mRoomDetail == null) {
                return;
            }

            statBottomBarClickButtonEvent("话题");

            boolean isShowed = LiveAnnouncementNewFeatureDialog.showed();
            LiveHelper.Log.i("测试新版提示-", " " + isShowed);
            if (isShowed) {
                //没新版提示，直接打开编辑
                openEditPage();
            } else {
                //有新版提示，打开新版介绍页
                openPhotoHintPage();
            }
        }

        private void openPhotoHintPage() {
            LiveAnnouncementNewFeatureDialog dialog = LiveAnnouncementNewFeatureDialog.newInstance();
            dialog.setOnClickListener(v -> openEditPage());
            dialog.show(getChildFragmentManager(), LiveAnnouncementNewFeatureDialog.TAG);
        }

        private void openEditPage() {
            // 管理员端直播间内底部菜单 - 公告话题编辑入口
            boolean isPremiere = PremiereRoomUtil.isValidPremiereStatus(mPremiereInfo);
            long anchorId = getLiveAnchorId();
            LiveAnnouncementAlbumDialog dialog = LiveAnnouncementAlbumDialog.show(
                    LiveAudienceRoomFragment.this, SOURCE.CHANNEL_2, false, getRoomBizType(),
                    mRoomDetail.getLiveId(), getRoomId(), anchorId, isPremiere
            );
            mAnnounceDialogRef = new WeakReference<>(dialog);
        }

        @Override
        public void onClickManageItem() {
            showAdminListDialog();
            statBottomBarClickButtonEvent("管理");
        }

        @Override
        public void onClickMicItem(boolean isMute) {
            mIsMute = isMute;
            if (mIsMute) {
                statBottomBarClickButtonEvent("关闭麦克风");
            } else {
                statBottomBarClickButtonEvent("打开麦克风");
            }
        }

        @Override
        public void onClickPhotoItem() {
            selectPhotoToSend(false);
            statBottomBarClickButtonEvent("图片");
        }

        @Override
        public void onClickShare() {
            shareLive();
        }

        @Override
        public void onClickBanList() {
            showBanListDialog();
            statBottomBarClickButtonEvent("禁言");
        }

        @Override
        public void onClickHybrid(String url) {
            LiveHostFragmentUtil.appendRoomParamsAndHandleH5UrlOrIting(
                    LiveAudienceRoomFragment.this,
                    url
            );
        }

        @Override
        public void onClickITing(String url) {
            LiveHostFragmentUtil.appendRoomParamsAndHandleH5UrlOrIting(
                    LiveAudienceRoomFragment.this,
                    url
            );
        }

        @Override
        public void onClickPrivateChat() {
            if (!UserInfoMannage.hasLogined()) {
                endFullScreenLandscape();
                UserInfoMannage.gotoLogin(getContext());
                return;
            }

            CommonPrivateChatComponent chatComponent
                    = getComponentSafety(AudienceCompConfig.COMPONENT_PRIVATE_CHAT_SHOW);
            if (chatComponent != null) {
                chatComponent.showMsgCenter();

                // 直播间-私信弹框  弹框展示
                new XMTraceApi.Trace()
                        .setMetaId(35490)
                        .setServiceId("dialogView")
                        .put("currPage", "LiveAudienceRoomFragment")
                        .put(LiveRecordInfoManager.getInstance().getBaseProps())
                        .createTrace();
            }

        }

        @Override
        public void onClickSuggestion() {
            LiveWebUtil.startWebViewFragment((MainActivity) mActivity, LiveUrlConstants.getSuggestH5Url(), false);
        }

        @Override
        public void onClickCostRemind() {
            // 跳转消费提醒iting
            CommonLiveLogger.d(TAG, "onClickCostRemind");

            LiveCommonITingUtil.handleITing(getActivity(),
                    LiveCommonConstants.getCostRemindH5HomeDialog(getRoomId(), getLiveAnchorId()));
        }

        @Override
        public void onClickPackage() {
            if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
                return;
            }
            try {
                IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
                component.show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onClickReport() {
            openReportPage(true, getLiveRecordId(), getHostUid());
        }

        @Override
        public void onClickClearScreen() {
            LiveClearScreenManager.getInstance().toggleClearScreen(true);
        }

        @Override
        public void onClickPlaybackSetting() {
            LivePlaybackSettingDialog.show(getChildFragmentManager());
        }
    };

    private void statBottomBarClickButtonEvent(String itemId) {
        if (TextUtils.isEmpty(itemId)) {
            return;
        }

        new UserTracking().setSrcPage("live")
                .setSrcPageId(getLiveRecordId())
                .setLiveId(getLiveRecordId())
                .setSrcModule("底部功能栏")
                .setItem("button")
                .setItemId(itemId)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_LIVE_PAGE_CLICK);
    }

    public void bottomClickFriendModeEmotion() {
        showMicEmotionDialog();
    }


    public void bottomClickEmotionEntrance() {
        if (!UserInfoMannage.hasLogined()) {
            endFullScreenLandscape();
            UserInfoMannage.gotoLogin(getActivity());
            return;
        }

        IInputPanelComponent panelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != panelComponent) {
            panelComponent.showAndOpenEmotionPanel();
        }

        INewbieGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_NEWBIE_GUIDE);
        if (comp != null) comp.clearEmojiGuideShowTask();

        IAudienceGiftPanelComponent component = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (null != component) {
            component.showRepeatDialog(View.INVISIBLE);
        }
    }

    @Override
    public void keyboardShowStateChange(boolean show) {
        super.keyboardShowStateChange(show);

        if (isFullScreenLandscapeVideoRoom()) {
            if (show) {
                hideLandScapeHeadView();
                setVideoDecorationVisibility(View.GONE);
            } else {
                showView();
                setVideoDecorationVisibility(View.VISIBLE);
            }
            updateSystemUiVisibility();
        }
    }

    private void hideLandScapeHeadView() {
        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.hideHeaderView();
        }
    }

    private void setVideoDecorationVisibility(int visibility) {
        if (!canUpdateUi()) {
            return;
        }

        IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (null != component) {
            component.setVideoDecorationVisibility(visibility);
        }
    }

    @Override
    public ViewGroup getRootView() {
        return mRootView;
    }

    public void openReportPage(boolean isReportLive, long liveId, long targetUid) {

        if (liveId <= 0 && targetUid < 0) {
            return;
        }

        boolean isVideoLive = getLiveMediaType() == LiveMediaType.TYPE_VIDEO;
        if (isVideoLive) {
            //如果是全屏模式，需要进行旋转
            boolean isLand = DeviceUtil.isLandscape(getActivity());
            exitLiveFullScreen(isLand);
        }

        //判断登陆
        if (!UserInfoMannage.hasLogined()) {
            endFullScreenLandscape();
            UserInfoMannage.gotoLogin(mContext);
            return;
        }

        try {
            if (isReportLive) {//举报直播
                BaseFragment reportFragmentLive = null;
                if (isVideoLive) {
                    reportFragmentLive = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                            .newReportFragmentByVideoLive(liveId, targetUid);
                } else if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
                    reportFragmentLive = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                            .newReportFragmentByLiveId(liveId, targetUid);
                }
                if (reportFragmentLive != null) {
                    startFragment(reportFragmentLive);
                }

            } else {//举报个人

                BaseFragment reportFragmentLive = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                        .newReportFragmentByLiveId(liveId, targetUid, "", mLiveMediaType);
                if (reportFragmentLive != null) {
                    startFragment(reportFragmentLive);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void shareLive() {
        if (mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null
                || mRoomDetail.getLiveAnchorInfo() == null) {
            CustomToast.showToast("获取数据中");
            return;
        }
        long roomId = mRoomDetail.getLiveRecordInfo().roomId;
        long liveId = mRoomDetail.getLiveRecordInfo().id;
        long hostUid = mRoomDetail.getLiveAnchorInfo().uid;

        Long chatId = mRoomDetail.getLiveRecordInfo().chatId;
        Long uid = UserInfoMannage.getUid();
        ShareUtils.registerShareResultAndUpload(getContext(), roomId,
                chatId, mRoomDetail.getLiveRecordInfo().id, uid, hostUid);
        try {
            IMainFunctionAction mainAction = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN)
                    .getFunctionAction();
            if (mainAction != null && getActivity() != null) {
                SharePanelDialog sharePanelDialog = ShareUtils.shareLive(getActivity(), liveId,
                        roomId,
                        ShareUtils.getLiveShareData(mRoomDetail),
                        ICustomShareContentType.SHARE_TYPE_LIVEPERSONAL,
                        hostUid);

                mShareDialogRef = new WeakReference<>(sharePanelDialog);

            }
            shareBuryPoint();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void shareBuryPoint() {
        new XMTraceApi.Trace()
                .setMetaId(33464)
                .setServiceId("dialogView")
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    /**
     * 麦上表情选择弹窗
     */
    public LiveMicEmotionDialog mLiveMicEmotionDialog;

    /**
     * 显示麦上表情选择框
     */
    private void showMicEmotionDialog() {
        if (mLiveMicEmotionDialog == null) {
            mLiveMicEmotionDialog = new LiveMicEmotionDialog(mActivity, LiveAudienceRoomFragment.this, LiveMicEmotionDialog.FROM_LAMIA);
            mLiveMicEmotionDialog.setOnMicEmotionItemClickListener(new IOnMicEmotionItemClickListener() {
                @Override
                public void onItemClick(int currentPage, int currentPosition, IEmojiItem emojiBean) {
                    sendEmojiMsg(emojiBean);
                    sendTraceForClickMicEmotion(emojiBean);
                }

                @Override
                public void onSubItemClick(IEmojiItem emojiBean) {
                    sendEmojiMsg(emojiBean);
                    sendTraceForClickMicEmotion(emojiBean);
                }
            });
        }

        mLiveMicEmotionDialog.myShow();
    }

    private void sendTraceForClickMicEmotion(IEmojiItem emojiBean) {
        new XMTraceApi.Trace()
                .put("currPage", "live")
                .put("currPageId", String.valueOf(getLiveRecordId()))
                .put("item", emojiBean.getName() != null ? emojiBean.getName() : "")
                .put("currModule", "gifType")
                .put("roomId", String.valueOf(mRoomId))
                .setMetaId(5802)
                .setServiceId("clickButton")
                .createTrace();
    }

    @Override
    public void onChatRoomJoinResult(boolean success, int code, String msg) {
        super.onChatRoomJoinResult(success, code, msg);
        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.initAfterJoinChatRoom();
        }

        getComponentSafety(COMPONENT_AUDIENCE_MIC);
        getComponentSafety(COMPONENT_VIDEO_PK_AUDIENCE);
        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (success) {
            if (null != component) {
                component.updateInputViewStatus(BottomBarComponent.LOG_SUCCESS);
            }
        } else {
            if (null != component) {
                component.updateInputViewStatus(BottomBarComponent.LOG_FAIL);
            }
        }
    }

    @Override
    public void showFriendGiftPanel() {
        try {
            IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
            component.show(RoomModeManager.MODE_MAKE_FRIENDS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onBackPressed() {
        //全屏播放先返回竖屏播放
        if (isFullScreenLandscapeVideoRoom()) {
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
            }

            if (isKickOutState || isExitRoomUnstoppableState) {
                return doRealBack();
            }
            return true;
        }
        return doRealBack();
    }

    private boolean doRealBack() {
        if (LiveGlobalDispatcher.getInstance().notifyBackPressed()) {
            return true;
        }

        if (isKickOutState() || isExitRoomUnstoppableState()) {
            //被顶掉，不管是否在连麦
            try {
                IExitRoomComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_EXIT_ROOM);
                if (component != null) {
                    component.markClose();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return super.onBackPressed();
    }

    @Override
    public void showCommonModeUI() {
        super.showCommonModeUI();
        getWaitComponent(COMPONENT_BOTTOM_BAR, new IComponentStateCallBack<IBottomBarComponent>() {
            @Override
            public void onCreated(IBottomBarComponent component) {
                component.showCommonModeUi();
                component.updateFriendModeEmotionButtonAndDialog(RoomModeManager.isFriendsMode());
            }
        });
    }

    @Override
    public void initFriendModeUI() {
        super.initFriendModeUI();

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.showFriendModeUi();
            component.updateFriendModeEmotionButtonAndDialog(RoomModeManager.isFriendsMode());
        }
    }

    @Override
    public LoveModeMicStateManager.MicStateObserver getMicStateObserver() {
        return mFragmentImplHelper.getAudienceRoomFragment();
    }

    @Override
    public IRoomModeFragment getRoomModeFragment() {
        return mFragmentImplHelper.getRoomModeFragment();
    }

    @Override
    public void showGiftPanel() {
        try {
            IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
            component.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleKickOutUser() {
        // 处理互踢导致的交友模式资源释放问题，停止连麦推流
        if (!RoomModeManager.isNormalChatMode()) {
            LoveModeManager.getInstance().release();
            releaseSDKResourse(true);
        }
        // 停止拉音视频流
        if (getStreamManager() != null) {
            getStreamManager().destroy(true);
        }
        IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        if (null != component) {
            component.stop();
        }
        // 隐藏直播间所有弹窗
        hideAllDialog();
        // 隐藏输入面板
        IInputPanelComponent inputPanelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != inputPanelComponent) {
            inputPanelComponent.hide();
        }
    }

    @Override
    public void onReceivedQueryRoomModeRsp(CommonChatQueryRoomModeRsp queryRoomModeRsp) {
        super.onReceivedQueryRoomModeRsp(queryRoomModeRsp);

        if (queryRoomModeRsp == null || !canUpdateUi() || !queryRoomModeRsp.isSuccess()) {
            return;
        }

        if (queryRoomModeRsp.mRoomId != mRoomId) {
            return;
        }

        String lastModeString = RoomModeManager.getInstance().getModeString();
        Logger.i(TAG, "current mode: " + lastModeString + ", new mode: " + queryRoomModeRsp.mModeList);

        boolean needTips = RoomModeManager.needModeChangeTips(queryRoomModeRsp.mModeList);

        if (RoomModeManager.sameMode(queryRoomModeRsp)) {
            return;
        }

        if (RoomModeManager.isFriendsMode() && LoveModeManager.getInstance().notInjectManager()) {
            if (getRoomCore() != null) {
                LoveModeManagerInjectUtil.injectLoveMessageManager(getRoomCore().getLoveMessageManager());
                LoveModeManagerInjectUtil.injectLoveMessageDispatcherManager(getRoomCore().getLoveMessageDispatcherManager());
            }
            if (mMakeFriendsAudience != null) {
                mMakeFriendsAudience.initAfterJoinChatRoom();
            }
        } else if (RoomModeManager.isPkMode() && LivePkHelper.getInstance().notInjectManager()) {
            PkModeManagerInjectUtil.injectPkMessageManager(mPkMessageManager);
            PkModeManagerInjectUtil.injectPkDispatcherManager(mPkMessageDispatcherManager);
        }

        RoomModeManager.getInstance().onReceivedQueryRoomModeRspMessage(queryRoomModeRsp, needTips);

        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.onReceivedQueryRoomModeRsp(queryRoomModeRsp);
        }
    }

    @Override
    public void onReceivedTopicUpdateMessage(CommonChatRoomTopicUpdateMessage message) {
        super.onReceivedTopicUpdateMessage(message);
        if (message == null || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null == headerComponent) {
            return;
        }

        if (TextUtils.isEmpty(message.txt)) {
            headerComponent.updateTopic("");
        } else {
            headerComponent.updateTopic(message.txt);
        }
    }

    @Override
    public void onReceiveFansRankMessage(CommonChatRoomFansRankMessage message) {
        super.onReceiveFansRankMessage(message);
        if (message == null || !canUpdateUi()) {
            return;
        }

        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.onFansRankChanged(message);
        }
    }

    @Override
    public void onReceivedFansClubUpdateMessage(CommonChatRoomFansClubUpdateMessage message) {
        super.onReceivedFansClubUpdateMessage(message);
        if (message == null || !canUpdateUi()) {
            return;
        }

        if (message.type == CommonChatRoomFansClubUpdateMessage.IFanClubMessageType.TYPE_JOIN) {
            IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
            if (null != headerComponent) {
                //加入粉丝团 更新相关UI，修改聊天引导状态
                headerComponent.updateFansJoinSuccessState();
            }

            IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
            if (null != friendModeComponent) {
                friendModeComponent.updateFansJoinSuccessState();
            }

            IAVChatListComponent chatListComponent = getComponentSafety(COMPONENT_CHAT_LIST);
            if (chatListComponent != null) {
                chatListComponent.updateJoinFansClubMsgStatus(true);
            }
        }
    }

    @Override
    public void onReceiveAudienceMessageReceived(CommonChatAudienceMessage message) {
        super.onReceiveAudienceMessageReceived(message);

        if (!canUpdateUi()) {
            return;
        }

        if (mPresenter != null) {
            mPresenter.updateMyInfoOnAudienceReceived(mRoomId, message);
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        Logger.i(TAG, "onConfigurationChanged:" + newConfig.orientation);

        hideAllDialog(newConfig.orientation);

        try {
            IAudienceGiftPanelComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_GIFT_PANEL);
            component.onConfigurationChanged(newConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }

        orientationChange(newConfig.orientation);

    }

    private void orientationChange(int orientation) {
        if (getComponentHost() != null) {
            getComponentHost().dispatchOrientationChange(orientation, mCurOrientation == orientation);
        }
        if (mCurOrientation == orientation) {
            return;
        }
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 直播间-视频放大/缩小按钮  点击事件
            new XMTraceApi.Trace()
                    .click(38354)
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .put("Item", "放大")
                    .put("currPage", "liveRoom")
                    .createTrace();
        } else {
            // 直播间-视频放大/缩小按钮  点击事件
            new XMTraceApi.Trace()
                    .click(38354)
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .put("Item", "缩小")
                    .put("currPage", "liveRoom")
                    .createTrace();

            restorePortraitSystemUiVisibility();
        }
        mCurOrientation = orientation;

    }

    private void restorePortraitSystemUiVisibility() {
        StatusBarManager.hideStatusBar(getWindow(), false);
        StatusBarManager.setStatusBarColorDelay(getWindow(), false, this);
        if (mOriginalSystemUiVisibility != 0) {
            getWindow().getDecorView().setSystemUiVisibility(mOriginalSystemUiVisibility);
        }
    }

    @Override
    public void hideAllDialog() {
        super.hideAllDialog();

        IAudienceGiftPanelComponent giftPanelComponent = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (null != giftPanelComponent) {
            giftPanelComponent.hide();
        }
        hideMakeFriendsDialog();
        dismissTopicAndNoticeDialog();

        LiveLamiaUtil.hideSoftInput(this);

        if (mUserPop != null) {
            mUserPop.hide();
        }

        if (mMoreActionDialog != null) {
            mMoreActionDialog.hide();
        }

        if (getCustomH5Dialog() != null) {
            getCustomH5Dialog().dismiss();
        }

        dismissMicEmotionDialog();

        dismissOnlinePageH5Dialog();

        dismissDialog();

        if (mShareDialogRef != null && mShareDialogRef.get() != null) {
            mShareDialogRef.get().dismiss();
        }

        if (mAnnounceDialogRef != null && mAnnounceDialogRef.get() != null) {
            mAnnounceDialogRef.get().dismiss();
        }

        XmLiveVerifyManagerKt.INSTANCE.hideAllDialog();

    }

    /**
     * 仅关闭交友模式相关的dialog
     */
    public void hideMakeFriendsDialog() {
        if (mMakeFriendsAudience != null) {
            mMakeFriendsAudience.dismissDialog();
        }
    }

    public void hideAllDialog(int orientation) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
           hideAllDialog();
        }
    }


    @Override
    protected void showBottomBarGiftRedPoint(@Nullable PackageInfo.RedPoint redPoint) {
        super.showBottomBarGiftRedPoint(redPoint);
        Logger.i(RoomRedPointManager.TAG, "fragment showBottomBarGiftRedPoint redPoint " + redPoint);
        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.showGiftRedPoint();
        }
    }

    @Override
    public void initPkModeUI() {
        super.initPkModeUI();
        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.updateFriendModeEmotionButtonAndDialog(RoomModeManager.isFriendsMode());
        }
    }

    public void releasePkMicUI() {
        IAudienceMicComponent micComponent = getComponentSafety(COMPONENT_AUDIENCE_MIC);
        if (micComponent != null) {
            micComponent.releasePkMicUI();
        }
    }

    public void releaseMicUI() {
        // 用户端等待SEI来处理
    }

    public void adapterFollowMessage(CommonChatMessage message) {
        if (message != null) {
            if (message.extendInfo instanceof NotifyFollowerManager.FollowMessageObj) {
                NotifyFollowerManager.FollowMessageObj followMessageObj = (NotifyFollowerManager
                        .FollowMessageObj) message.extendInfo;

                if (followMessageObj.type == NotifyFollowerManager.TYPE_ACTION_SEND_MESSAGE
                        || followMessageObj.type == NotifyFollowerManager.TYPE_ACTION_SEND_GIFT
                        || followMessageObj.type == NotifyFollowerManager.TYPE_ACTION_STAY_ROOM_60_MINUTES
                        || followMessageObj.type == NotifyFollowerManager.TYPE_ACTION_STAY_ROOM_180_MINUTES) {
                    //关注主播弹窗
                    showFollowAnchorDialog(mRoomId, followMessageObj.type);
                } else if (followMessageObj.type == NotifyFollowerManager.TYPE_ACTION_JOIN_FANS_GROUP) {
                    //引导加入粉丝团消息
                    onReceiveChatMessage(message);
                }

                statFollowMsgShowEvent(followMessageObj.type == NotifyFollowerManager.TYPE_ACTION_JOIN_FANS_GROUP ?
                        " 粉丝团气泡" : "关注气泡");
            }
        }
    }

    public boolean isJoinFansClub() {
        if (mPresenter == null) {
            return false;
        }

        return mPresenter.isJoinFansClub();
    }

    @Override
    protected void addAntherMiniRecordVars(MiniRoomRecord record) {
        super.addAntherMiniRecordVars(record);
        if (null != mRoomDetail) {
            record.setRoomTitle(mRoomDetail.getRoomTitle());
            record.setBgImg(mRoomDetail.getBgImage());
            record.setCover(mRoomDetail.getSmallCoverPath());
        }
    }

    @Override
    public BaseVirtualRoom getAndFillRoomCore() {
        AudienceRoomCore room = getRoomCore();
        room.setRoomBiz(getRoomBizType());
        room.setLiveId(getLiveRecordId());
        room.setRoomId(getRoomId());
        IRoomCoreService roomMicService = room.getRoomCoreService(RoomConstants.RoomCoreServiceName.SERVICE_MIC);
        if (roomMicService != null) {
            ((IRoomMicService) roomMicService).setIsNewMicWaitingMic(getAvService() != null && getAvService().getUserStatus() == UserStatus.USER_STATUS_WAITING);
        }
        int micStatus = AudienceRoomCore.FRIEND_MIC_IDLE;
        if (LoveModeMicStateManager.getInstance().isMicConnected()) {
            micStatus = AudienceRoomCore.FRIEND_MIC_CONNECT;
        } else if (LoveModeMicStateManager.getInstance().isMicWaiting()) {
            micStatus = AudienceRoomCore.FRIEND_MIC_WAITING;
        }
        room.mFriendMicStatus = micStatus;
        LoveModeMicStateManager.getInstance().reset(false);//最小化充值状态，避免检查是否在线弹窗判断影响

        MutableLiveData<LiveModeData> modeData = RoomModeManager.getInstance().getLiveModeData();
        room.setModeList(modeData != null && modeData.getValue() != null ? modeData.getValue().getModeList() : new ArrayList<>());

        if (mMakeFriendsAudience != null) {
            room.mFriendsMicInfoWrapper = mMakeFriendsAudience.getFriendsMicInfoWrapper();
        }
        if (getStreamManager() != null) {
            getStreamManager().removeStreamPlayStateListener(this);
        }
        return room;
    }

    @Override
    public void onFollow(final long uid, final boolean follow) {
        if (mRoomDetail == null || mRoomDetail.getHostUid() != uid) {
            return;
        }
        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            friendModeComponent.onFollow(uid, follow);
        }
        if (!follow) {
            return;
        }
        mRoomDetail.setFollowed(true);

        IAVChatListComponent component = getComponentSafety(
                COMPONENT_CHAT_LIST
        );
        if (component != null) {
            component.updateFollowHostStatus(true);
        }

        // 关注主播，需要隐藏正在展示的关注引导及移除延时任务
        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.removeFollowGuideAndTask();
    }

    @Override
    public boolean hasDialogShowing() {
        // 当前房间页必须是在栈顶，不能被如 H5 页面覆盖
        boolean isLiveRoomOnTop = isLiveRoomOnTop();
        boolean dialogShowing = LiveHostCommonUtil.hasDialogOrDialogFragmentShowing();
        boolean giftShowing = false;
        IAudienceGiftPanelComponent giftPanelComponent = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (null != giftPanelComponent) {
            giftShowing = giftPanelComponent.isGiftPanelShowing();
        }

        boolean keyBoardShow = false;
        IInputPanelComponent inputPanelComp = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != inputPanelComp) {
            keyBoardShow = inputPanelComp.isKeyboardPanelShowed();
        }

        boolean isUserPopShow = mUserPop != null && mUserPop.isShowing();

        boolean isMoreDialogShow = mMoreActionDialog != null && mMoreActionDialog.isShowing();

        boolean h5DialogShow = getCustomH5Dialog() != null && getCustomH5Dialog().isShowing();

        boolean mEmotionDialogShow = mLiveMicEmotionDialog != null && mLiveMicEmotionDialog.isShowing();

        boolean isIdle = !isFragmentScrollStateIdle();

        CommonLiveLogger.i(TAG, "hasDialogShowing "
                + "isLiveRoomOnTop " + isLiveRoomOnTop
                + " dialogShowing " + dialogShowing
                + " giftShowing " + giftShowing
                + " keyBoardShow " + keyBoardShow
                + " isUserPopShow " + isUserPopShow
                + " isMoreDialogShow " + isMoreDialogShow
                + " h5DialogShow " + h5DialogShow
                + " isIdle " + isIdle);
        return !isLiveRoomOnTop
                || dialogShowing
                || giftShowing
                || keyBoardShow
                || isUserPopShow
                || isMoreDialogShow
                || h5DialogShow
                || mEmotionDialogShow
                || isIdle;
    }

    public LiveMenuData getMoreMenuData() {
        return mMoreMenuModel;
    }

    public PkPanelView getPkPanelView() {
        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            return friendModeComponent.getPkPanelView();
        }

        return null;
    }

    private void showFollowAnchorDialog(final long roomId, int action) {
        IAudienceMicComponent micComponent = getComponentSafety(COMPONENT_AUDIENCE_MIC);
        if (micComponent == null) {
            return;
        }
        if (micComponent.isAnchorFollowDialogShowed()) {
            LamiaHelper.Log.i("多人连麦弹窗", "showFollowAnchorDialog-展示过新弹窗，不再展示老弹窗");
            return;
        }
        String abFollowPop = ABTest.getString(AB_LIVE_ANCHOR_FOLLOW_POP, "simple");
        LamiaHelper.Log.i("多人连麦弹窗数据", "showFollowAnchorDialog-action:" + action);
        if (abFollowPop.equals("multiple") && !mRoomDetail.isNewUser()
                && (action != NotifyFollowerManager.TYPE_ACTION_SEND_MESSAGE && action != NotifyFollowerManager.TYPE_ACTION_SEND_GIFT)) {
            //命中多人弹窗+老用户
            List<Anchor> anchorMicOrPKInfo = getAnchorMicOrPKInfo();
            int size = 0;
            if (anchorMicOrPKInfo != null) {
                size = anchorMicOrPKInfo.size();
            }
            if (size > 0) {
                //有多人主播连麦
                LamiaHelper.Log.i("多人连麦弹窗数据", "showFollowAnchorDialog-使用新弹窗");
                return;
            } else {
                //没有多人主播连麦，依然走老弹窗
                LamiaHelper.Log.i("多人连麦弹窗数据", "showFollowAnchorDialog-没有多人主播连麦，依然走老弹窗");
            }
        }
        if (isFullScreenLandscapeVideoRoom() || mRoomDetail.isFollowed()) {
            //已经关注了，就别弹了
            CommonLiveLogger.i(TAG, "已经关注了，就别弹了 " + roomId);
            return;
        }
        CommonLiveLogger.i(TAG, "showFollowAnchorDialog " + roomId);

        if (mRoomDetail.getHostUid() > 0 && mRoomDetail.getHostUid() == UserInfoMannage.getUid()) {
            // 自己的直播间，不展示关注
            return;
        }

        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showFollowHostWithSayHiGuide();
    }


    private void statFollowMsgShowEvent(String type) {
        if (TextUtils.isEmpty(type) || mRoomDetail.getLiveId() == -1) {
            return;
        }

        LamiaHelper.Log.i("live event 泡泡条露出 : " + type + ", mLiveId : " + getLiveRecordId());
        new UserTracking().setSrcPage("live")
                .setLiveId(getLiveRecordId())
                .setModuleType(type)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
    }

    @Override
    public void onCurrentLoginUserInfo(LiveUserInfo object) {
        super.onCurrentLoginUserInfo(object);
        // 更新礼物面板当前用户信息
        getWaitComponent(COMPONENT_GIFT_PANEL, new IComponentStateCallBack<IAudienceGiftPanelComponent>() {
            @Override
            public void onCreated(IAudienceGiftPanelComponent component) {
                component.updateCurrentUserInfo(object);
            }
        });
        getWaitComponent(COMPONENT_AUDIENCE_MIC, new IComponentStateCallBack<IAudienceMicComponent>() {
            @Override
            public void onCreated(IAudienceMicComponent component) {
                component.updateCurrentUserInfo(object);
            }
        });
        // 更新余额(背包)
        LiveGiftLoader.getInstance(LiveGiftLoader.class).updatePackageInfo(true);
    }

    @Override
    public void dismissBottomBarMoreActionPanel() {
        if (mMoreActionDialog != null) {
            mMoreActionDialog.hide();
            mMoreActionDialog = null;
        }
    }

    @Override
    public void sendMessage(String content) {
        super.sendMessage(content);
        try {
            AudienceRoomTimedTaskComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_TIMED_TASK);
            NotifyFollowerManager.getImpl().sendMessage(component.getCurrentRoomRecord());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void sendEmojiMsg(IEmojiItem emojiItem) {
        super.sendEmojiMsg(emojiItem);
        try {
            AudienceRoomTimedTaskComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_TIMED_TASK);
            NotifyFollowerManager.getImpl().sendMessage(component.getCurrentRoomRecord());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onReceiveGiftComboOverMessage(CommonChatGiftComboOverMessage giftMessage) {
        super.onReceiveGiftComboOverMessage(giftMessage);
        if (giftMessage == null || giftMessage.mSender == null) {
            return;
        }
        if (giftMessage.mSender.mUid != UserInfoMannage.getUid()) {
            return;
        }
        try {
            AudienceRoomTimedTaskComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_TIMED_TASK);
            NotifyFollowerManager.getImpl().sendGift(component.getCurrentRoomRecord());
        } catch (Exception e) {
            e.printStackTrace();
        }
        NotifySendGiftGuideManager.sMarkCurrentUserHasSendGift = true;
    }

    @Override
    protected void afterReceiveGiftMessage(CommonChatGiftMessage giftMessage) {
        notifyGiftMessageToNotifyFollowerManager(giftMessage);
    }

    private void notifyGiftMessageToNotifyFollowerManager(CommonChatGiftMessage giftMessage) {
        if (giftMessage == null || giftMessage.mSender == null) {
            return;
        }
        if (giftMessage.mSender.mUid != UserInfoMannage.getUid()) {
            return;
        }
        try {
            AudienceRoomTimedTaskComponent component = getPresenter().getComponentManager().getComponent(COMPONENT_TIMED_TASK);
            NotifyFollowerManager.getImpl().sendGift(component.getCurrentRoomRecord());
        } catch (Exception e) {
            e.printStackTrace();
        }
        NotifySendGiftGuideManager.sMarkCurrentUserHasSendGift = true;
    }

    @Override
    public void onFansClubInfoChange(LiveUserInfo.FansClubVoBean fansClubVoBean) {
        super.onFansClubInfoChange(fansClubVoBean);
        getWaitComponent(COMPONENT_HEADER, new IComponentStateCallBack<IAudienceHeaderComponent>() {
            @Override
            public void onCreated(IAudienceHeaderComponent component) {
                component.onFansClubInfoChange(fansClubVoBean);
                // 调用音频官播间 头部粉丝团组件更新亲密度
                if (null != fansClubVoBean
                        && fansClubVoBean.getCode() == LiveUserInfo.FansGroupStatusCode.TYPE_JOINED
                        && System.currentTimeMillis() < getRoomDetail().roomFansClubVo.getAdditionEndAt()) {
                    IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
                    if (null != friendModeComponent) {
                        friendModeComponent.setFansIntimacy(getRoomDetail().roomFansClubVo.getAdditionTimes(),
                                getRoomDetail().roomFansClubVo.getAdditionEndAt(), getRoomBizType(), false);
                    }
                }
            }
        });
    }

    @Override
    public void onGuardInfoChange(LiveUserInfo.GuardGroupVo guardGroupVo) {
        super.onGuardInfoChange(guardGroupVo);
        getWaitComponent(COMPONENT_HEADER, new IComponentStateCallBack<IAudienceHeaderComponent>() {
            @Override
            public void onCreated(IAudienceHeaderComponent component) {
                component.onGuardInfoChange(guardGroupVo);
            }
        });
    }

    @Override
    public void onReceiveFansGroupMsg(CommonFansGroupMsg msg) {
        super.onReceiveFansGroupMsg(msg);

        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.onReceiveFansGroupMsg(msg);
        }
        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            friendModeComponent.onReceiveFansGroupMsg(msg);
        }
        if (msg != null && msg.type == CommonFansGroupMsg.FansMsgType.TYPE_OPEN) {
            IAVChatListComponent component = getComponentSafety(COMPONENT_CHAT_LIST);
            if (component != null) {
                component.updateJoinFansClubMsgStatus(true);
            }
        }
    }

    @Override
    public void onReceiveNotifyBottomButton(CommonChatRoomNotifyBottomButtonMsg msg) {
        Logger.i(TAG, "onReceiveNotifyBottomButton, msg = " + msg);

        super.onReceiveNotifyBottomButton(msg);
        if (null == msg) {
            return;
        }

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.requestBottomButtonList(msg.position, true);
        }

        loadMoreMenuInfo();
    }

    @Override
    public void onLogin(final LoginInfoModelNew model) {
        super.onLogin(model);
        //存在进入H5页面 立马退出导致泄露的情况 预防一下
        postOnUiThread(new LoginRunnable(this, model));
    }

    /**
     * 静态内部类，使用弱引用持有外部类的引用
     */
    private static class LoginRunnable implements Runnable {
        private final WeakReference<LiveAudienceRoomFragment> fragmentRef;
        private final LoginInfoModelNew mModel;

        LoginRunnable(LiveAudienceRoomFragment fragment, LoginInfoModelNew model) {
            this.fragmentRef = new WeakReference<>(fragment);
            this.mModel = model;
        }

        @Override
        public void run() {
            if (fragmentRef.get() == null || mModel == null) {
                return;
            }
            if (fragmentRef.get().mFragmentImplHelper != null) {
                fragmentRef.get().mFragmentImplHelper.onLogin(mModel);
            }
            if (fragmentRef.get().mPresenter != null) {
                fragmentRef.get().mPresenter.changeUser(mModel);
            }
            fragmentRef.get().loadMoreMenuInfo();
            fragmentRef.get().dismissDialog();
        }
    }

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {
        super.onLogin(olderUser);
        if (mFragmentImplHelper != null) {
            mFragmentImplHelper.onLogout(olderUser);
        }
        if (mPresenter != null) {
            mPresenter.changeUser(null);
        }
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
        super.onUserChange(oldModel, newModel);
        if (mFragmentImplHelper != null) {
            mFragmentImplHelper.onUserChange(oldModel, newModel);
        }
        if (mPresenter != null) {
            mPresenter.changeUser(newModel);
        }
    }

    public boolean isKeyboardPanelShowed() {
        if (!canUpdateUi()) {
            return false;
        }
        IInputPanelComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != component) {
            return component.isKeyboardPanelShowed();
        } else {
            return false;
        }
    }

    @Override
    public void showGiftPanelByGiftId(long giftId, long anchorUid) {
        NewAudienceAwardInfo info = new NewAudienceAwardInfo("");
        info.id = giftId;
        IAudienceGiftPanelComponent component = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (null != component) {
            component.showGiftPanelAndSelectGiftItem(info);
        }
    }

    @Override
    public void showPackageGiftAndLocate(NewAudienceAwardInfo newAudienceAwardInfo) {
        IAudienceGiftPanelComponent component = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (null != component) {
            component.showPackageGiftAndLocate(newAudienceAwardInfo);
        }
    }

    public void showGiftPanelWithLocate(NewAudienceAwardInfo newAudienceAwardInfo) {
        IAudienceGiftPanelComponent component = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (null != component) {
            component.showPackageGiftAndLocate(newAudienceAwardInfo);
        }
    }

    public void showUserInfoCard(long targetUid) {
        showUserInfoPop(targetUid);
    }

    @Override
    public void showUserInfoCard(long targetUid, int forceShowUserCard) {
        if (forceShowUserCard == 1) {
            showUserInfoPopOnly(targetUid);
        } else {
            showUserInfoPop(targetUid);
        }
    }

    public void showLittleGiftDialog(long giftId, int showPopup) {
        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.showLittleGiftDialog(giftId, showPopup);
        }
    }

    public void showInputPanel() {
        IInputPanelComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != component) {
            component.show();
        }
    }

    @Override
    public void showInputPanelAndDanMu(final String text) {
        postOnUiThreadDelayed(() -> {
            IInputPanelComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            if (null != component) {
                component.showAndOpenDanMuWithBulletCount(text);
            }
        }, 500);
    }

    @Override
    public void onReceiveRoomStatusChangeMessage(CommonChatRoomStatusChangeMessage message) {
        int preStatus = getRoomDetail() == null ? -1 : getRoomDetail().getStatus();
        super.onReceiveRoomStatusChangeMessage(message);
        if (message != null) {
            if (null != getComponentHost()) {
                getComponentHost().dispatchRoomStatusChange(message.status);
            }
            if (message.status == PersonLiveBase.LIVE_STATUS_END) {
                // 定时退出逻辑判断
                if (LiveTerminateManager.needExitRoomWhenLiveEnd()) {
                    exitRoomUnstoppable();
                    return;
                }
                onLiveRoomEndMessageReceived();
                showFinishDialogForAudience(preStatus);
            } else if (message.status == PersonLiveBase.LIVE_STATUS_ING) {
                mStopDialogShowed = false;
                if (mLiveEndPopupManager != null) {
                    mLiveEndPopupManager.dismiss();
                }
                onLiveRoomStartMessageReceived();
                // 移除关播重新开播后，检查直播间接口，和ios保持一致 （和梓铭确认）
                // 随机3秒内延迟 重新加载直播间信息，防止高qps
                long randomTime = (new Random().nextInt(3) + 1) * 1000L;
                postOnUiThreadDelayed(new Runnable() {
                    @Override
                    public void run() {
                        loadDetail();
                    }
                }, randomTime);
                if (getCustomH5Dialog() != null && getCustomH5Dialog().isShowing()) {
                    getCustomH5Dialog().dismiss();
                }
            }

            IRoomLoadingComponent roomLoadingComponent = getComponentSafety(COMPONENT_ROOM_LOADING);
            if (null != roomLoadingComponent) {
                roomLoadingComponent.updateRoomStatus(message.status);
            }

            if (getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
                IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
                if (null != component) {
                    component.updateRoomStatus(message.status);
                }
            }

            if (message.status != PersonLiveBase.LIVE_STATUS_ING) {
                IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
                if (null != component) {
                    component.updateConnectedStatus(LiveRoomStatusView.LIVE_END);
                }

                IVideoPkAudienceComponent videoPkAudienceComponent = getComponentSafety(COMPONENT_VIDEO_PK_AUDIENCE);
                if (videoPkAudienceComponent != null) {
                    videoPkAudienceComponent.onLiveEnd();
                }

                IAudienceMicComponent audienceMicComponent = getComponentSafety(COMPONENT_AUDIENCE_MIC);
                if (null != audienceMicComponent) {
                    audienceMicComponent.onLiveEnd();
                    audienceMicComponent.releasePkMicUI();
                    audienceMicComponent.releaseMicUI();
                }
            }
            IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
            if (null != friendModeComponent) {
                friendModeComponent.onReceiveRoomStatusChangeMessage(message);
            }
        }
    }

    protected void onLiveRoomEndMessageReceived() {
        super.onLiveRoomEndMessageReceived();
        if (LiveClearScreenManager.getInstance().isClear()) {
            LiveClearScreenManager.getInstance().restoreShowScreen();
        }
        stopLiveAndCloseDialogs();
        exitLiveFullScreen(isFullScreenLandscapeVideoRoom());

        PersonLiveDetail detail = getRoomDetail();
        detail.getLiveRecordInfo().status = PersonLiveBase.LIVE_STATUS_END;
        if (getPresenter() != null) {
            getPresenter().setLiveStatus(PersonLiveBase.LIVE_STATUS_END);
        }
        if (getAbsRoomDetail() != null) {
            getAbsRoomDetail().setLiveStatus(PersonLiveBase.LIVE_STATUS_END);
        }
        if (getRoomDetail() != null) {
            LiveRecordInfoManager.getInstance().setLiveRecordInfo(getRoomDetail(), mBusinessId);
        }
    }

    /**
     * 展示直播关闭和推荐直播弹窗
     * 跟随关播接口优化一块整理
     * wiki：<a href="https://alidocs.dingtalk.com/i/nodes/AR4GpnMqJzMLERrdt5pRzxy7VKe0xjE3?cid=504816099&corpId=ding51f195092fd77474&iframeQuery=utm_medium=im_card&utm_source=im&utm_medium=im_card&utm_scene=team_space&utm_source=im">直播间关播优化</a>
     */
    protected void showFinishDialogForAudience(int preStatus) {
        if (!canUpdateUi() || getRoomDetail() == null) {
            return;
        }

        boolean isMe = UserInfoMannage.hasLogined() && getRoomDetail().getHostUid() == UserInfoMannage.getUid();
        if (!isMe && shouldPopFinishDialogAndNoticeDialogForAudience()) {
            int abResult = LiveAbtestUtil.getAutoEnterLiveRoomConfig();
            if (abResult == LiveAbtestUtil.AUTO_ENTER_ROOM_FROM_COVER || abResult == LiveAbtestUtil.AUTO_ENTER_ROOM_FROM_BANNER) {
                IRecommendLiveComponent recommendComponent = getComponentSafety(COMPONENT_RECOMMEND);
                if (recommendComponent != null) {
                    hideTotalDialog();
                    recommendComponent.showRecommendLive(preStatus == PersonLiveBase.LIVE_STATUS_ING ?
                            getStringSafe(R.string.live_return_room) : getStringSafe(R.string.live_enter_room), abResult, mRoomDetail);
                }
                return;
            }
            if (!mStopDialogShowed) {
                if (isVisible()) {
                    if (mLiveEndPopupManager == null) {
                        mLiveEndPopupManager = new LiveEndPopupManager(this);
                        mLiveEndPopupManager.setEndPopupCloseClickListener(this::finishFragment);
                    }
                    hideTotalDialog();
                    mLiveEndPopupManager.show(getRoomBizType(), getRoomDetail());
                    mStopDialogShowed = true;
                }
            }
        }
    }

    /**
     * 普通用户是否要展示直播结束的弹框或者预告弹框
     * 如果是一些配置的playSource入口，入口场次如果是结束的，会有2秒自动滚动到下一个直播间，则不展示结束弹框
     **/
    protected boolean shouldPopFinishDialogAndNoticeDialogForAudience() {
        boolean hitPlaySource = StartRoomUtil.playSourceHitConfigAndAutoScrollToNexRoom(getPlaySource());
        RecommendLiveRecord currentRoomRecord = getCurrentRoomRecord();
        // 入口房间，且命中配置的playSource
        boolean keepAliveThisRecord = currentRoomRecord != null && currentRoomRecord.keepAliveThisRecord();

        if (isOnlyRefreshRoomDetail) {
            return true;
        }

        if (keepAliveThisRecord) {
            return false;
        } else {
            return !hitPlaySource;
        }
    }

    private void exitLiveFullScreen(boolean needDisableFeature) {
        if (needDisableFeature) {
            //全屏模式退出
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
            }
        }
    }


    /**
     * 停止直播
     */
    private void stopLiveAndCloseDialogs() {
        if (getStreamManager() != null && mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_AUDIO) {
            getStreamManager().destroy(true);
        } else if (mRoomDetail != null && mRoomDetail.getMediaType() == LiveMediaType.TYPE_VIDEO) {
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.stop();
            }
        }
        setPlayerFailVisible(false);
        hideAllDialog();
        ISealListComponent sealListComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_SEAL_LIST);
        if (null != sealListComponent) {
            sealListComponent.hideGoodsList();
        }
    }

    private void setPlayerFailVisible(boolean isVisible) {
        IPlayerFailComponent playerFailComponent = getComponentSafety(COMPONENT_PLAYER_FAIL);
        if (playerFailComponent != null) {
            playerFailComponent.setVisible(isVisible);
        }
    }

    @Override
    public boolean isAnchorVisitor() {
        return UserInfoMannage.getUid() > 0
                && getHostUid() == UserInfoMannage.getUid();
    }


    @Override
    public FriendsMicInfoWrapper getFriendsMicInfoWrapper() {
        if (mMakeFriendsAudience != null) {
            return mMakeFriendsAudience.getFriendsMicInfoWrapper();
        }
        return null;
    }

    @Override
    public void onReceiveGoodsInfoChangedMessage(CommonGoodsInfoChangedMessage goodsInfoChangedMessage) {
        if (null == goodsInfoChangedMessage || null == getRoomDetail() || !canUpdateUi()) {
            return;
        }

        super.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);

        if (getRoomDetail().getLiveRecordInfo() != null) {
            getRoomDetail().getLiveRecordInfo().cartGifUrl = goodsInfoChangedMessage.gifUrl;
        }

        ISealListComponent sealListComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_SEAL_LIST);
        if (null != sealListComponent) {
            sealListComponent.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        }

        IBottomBarComponent bottomBarComponent = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != bottomBarComponent) {
            bottomBarComponent.onReceiveGoodsInfoChangedMessage(goodsInfoChangedMessage);
        }
        if (goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.EXPLAIN ||
                goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.UNEXPLAIN ||
                goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.SUPPLEMENT ||
                goodsInfoChangedMessage.changeType == IGoodsInfoChangedConstant.GONE) {
            ISellGoodsComponent component = getExistComponent(AudienceCompConfig.COMPONENT_SELL_GOOD);
            if (null != component) {
                component.checkSpeakingGoodsInfo();
            }
        }
    }


    @Override
    public void onReceiveGoodsOrderChangedMessage(CommonGoodsOrderChangedMessage goodsOrderChangedMessage) {
        super.onReceiveGoodsOrderChangedMessage(goodsOrderChangedMessage);

        if (!canUpdateUi() || null == goodsOrderChangedMessage) {
            return;
        }

        Logger.i(TAG, "onReceiveGoodsOrderChangedMessage:" + goodsOrderChangedMessage);

        ISealListComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_SEAL_LIST);
        if (null != component) {
            component.onReceiveGoodsOrderChangedMessage(goodsOrderChangedMessage);
        }
    }

    @Override
    public void onReceiveGoShoppingMessage(CommonGoShoppingMessage goShoppingMessage) {
        super.onReceiveGoShoppingMessage(goShoppingMessage);
        try {
            IShoppingFloatComponent component = getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_SHOPPING_FLOAT);
            component.receiveGoShoppingMessage(goShoppingMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public boolean isMicConnected() {
        return isMicConnectedInternal();
    }

    @Override
    public boolean isWaitMicConnecting() {
        return isMicWaitingInternal();
    }

    protected boolean isMicConnectedInternal() {
        IXmMicService micService = getAvService();
        boolean isMicConnectedState = micService.getUserStatus() == UserStatus.USER_STATUS_MICING;
        boolean isPublish = micService.isPublish();
        return isPublish || isMicConnectedState;
    }

    protected boolean isMicWaitingInternal() {
        IXmMicService micService = getAvService();
        boolean isRequestingMic = micService != null && micService.getUserStatus() == UserStatus.USER_STATUS_WAITING;
        //交友模式发出连麦申请
        boolean isFriendsModeMicRequest = LoveModeMicStateManager.getInstance().isMicWaiting();
        return isFriendsModeMicRequest || isRequestingMic;
    }

    @Override
    public void leaveMicConnection(final Runnable action) {
        super.leaveMicConnection(action);
        if (isMicConnected()) {
            IXmMicService micService = getAvService();
            // 赞助榜单跳转至主播榜，离开房间，释放资源
//            MinimizeRoomManager.getInstance().exitAllVirtualRoom();
            LoveModeLogicHelper.leaveMic();
            if (micService != null) {
                micService.quitJoinAnchor(null);
                micService.leaveRoom(false);
                micService.unInit();
            }
            LoveModeManager.releaseInstance();
        } else if (isWaitMicConnecting()) {
            // 赞助榜单跳转至主播榜，离开房间，释放资源
//            MinimizeRoomManager.getInstance().exitAllVirtualRoom();
            LoveModeLogicHelper.leaveMic();
            IXmMicService micService = getAvService();
            if (micService != null) {
                micService.quitJoinAnchor(null);
            }
        }
        action.run();
    }

    @Override
    public void onReceiveShareRoomLiveMessage(CommonChatShareLiveRoomMessage message) {
        if (message.mUserInfo != null && message.mUserInfo.mUid != UserInfoMannage.getUid()) {
            // 个播用户端，对于别人的分享消息，带「我也分享」引导按钮
            message.withAction = true;
        }

        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SHARE_ROOM, message
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 构建鼓励主播消息：「主播开播 xx 小时以上了，快去鼓励一下 TA 吧~」
     */
    private final Runnable mRewardRunnable = () -> {
        if (!LiveMessageConfigUtilKt.isSendMsgOverNumOneDay()) {
            CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createRewardMsg(mRoomDetail);
            if (msg != null) {
                onReceiveChatMessage(msg);
                trackLiveRewardViewShow();
            }
        }
    };

    /**
     * 守护过期提醒：守护身份即将过期的前五天，每天进直播间后15s会推送给用户过期提醒
     * 由于服务端15s后推实现消息成本较高，故由客户端做
     */
    private final Runnable mGuardExpireRunnable = this::loadGuardExpireNotice;

    /**
     * 通用弹窗以及通用资源加载任务
     */
    private final Runnable mCommonPopRunnable = () -> {
        if (null != mAudienceRoomViewModel) {
            // 来自主站个人页面-头像框获取入口，进入直播间的 playSource
            if (mPlaySource == Constants.PLAY_SOURCE_FROM_AVATAR && itemId > 0) {
                mAudienceRoomViewModel.getPopupAvatarInfo(mPlaySource, itemId);
            }
            // 来自广告
            if (mPlaySource == Constants.PLAY_SOURCE_FROM_ADVERTISE) {
                mAudienceRoomViewModel.getPopupForAdvertise(Constants.PLAY_SOURCE_FROM_ADVERTISE);
            }
        }

        // 预加载礼物列表
        LiveGiftLoader giftLoader = LiveGiftLoader.getInstance(LiveGiftLoader.class);
        if (null != giftLoader) {
            giftLoader.updateGiftList();
        }
    };

    /**
     * 检查是否需要存在需要领取的优惠卷
     * 预加载更多菜单数据
     */
    private final Runnable mRequestCouponRunnable = () -> {
        // 检查是否需要领取的优惠卷
        checkHasCouponOrNot();
        // 预加载更多菜单数据
        loadMoreMenuInfo();
    };

    /**
     * 在直播间详情数据请求完成后做一些延迟任务
     */
    private void doDelayTasksAfterRequestRoomDetailSuccess() {
        postOnUiThreadDelayed(
                mRewardRunnable,
                RoomProgressiveLoadingConstants.LOADING_ROOM_REWARD_ANCHOR_TIPS_DELAY_TIME_MS
        );

        if (UserInfoMannage.hasLogined()) {
            postOnUiThreadDelayed(
                    mGuardExpireRunnable,
                    RoomProgressiveLoadingConstants.LOADING_ROOM_GUARD_EXPIRE_TIPS_DELAY_TIME_MS
            );
        }

        postOnUiThreadDelayed(
                mCommonPopRunnable,
                RoomProgressiveLoadingConstants.LOADING_ROOM_DIALOG_DELAY_TIME_MS
        );

        postOnUiThreadDelayed(
                mRequestCouponRunnable,
                RoomProgressiveLoadingConstants.LOADING_HEADER_AREA_SECTION_THREE_DELAY_TIME_MS
        );
    }

    /**
     * 移除延迟任务
     */
    private void removeDelayTasks() {
        removeCallbacks(mRewardRunnable);
        removeCallbacks(mGuardExpireRunnable);
        removeCallbacks(mRequestCouponRunnable);
        removeCallbacks(mCommonPopRunnable);
    }

    /**
     * 加载用户与主播的关系动画信息
     */
    private void loadRelationshipAnimation() {
        if (!UserInfoMannage.hasLogined()) {
            return;
        }
        getWaitComponent(COMPONENT_HEADER,
                (IComponentStateCallBack<IAudienceHeaderComponent>) component -> {
                    if (component != null) {
                        component.loadRelationshipAnimation();
                    }
                }
        );
    }

    @Override
    public void addAudienceMessage(CommonChatAudienceMessage audienceMessage) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createAudienceMsg(
                mRoomDetail, audienceMessage, false
        );
        if (msg == null) return;

        if (audienceMessage.mType == CommonChatAudienceMessage.AudienceMsgType.MSG_TYPE_COMMON_JOIN_FANS_CLUB) {
            trackLiveJoinClubViewShow();
        } else if (audienceMessage.mType == CommonChatAudienceMessage.AudienceMsgType.MSG_TYPE_PERSONAL_FOLLOW) {
            trackLiveFollowViewShow();
        }
        onReceiveChatMessage(msg);
    }

    /**
     * 首次评论有惊喜
     *
     * @param firstCommentAward 首次评论有惊喜数据
     */
    @Override
    public void onReceiveFirstCommentAwardMessage(CommonFirstCommentAward firstCommentAward) {
        // 展示弹窗
        CommonLiveLogger.d(TAG, "收到首次评论有惊喜消息");
        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.isFirstComment(false);
        }
        LiveDialogFragmentManager.INSTANCE.addDialog(LiveCommentAwardDialogFragment.newInstance(firstCommentAward)
                .setSendEmojiCallback(sendEmojiCallback), getChildFragmentManager());
        IInputPanelComponent panelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != panelComponent) {
            panelComponent.isFirstComment(false);
        }
    }

    @Override
    public void onReceiveCarouselRankMessage(LiveCarouselRankNotify message) {
        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (canUpdateUi() && headerComponent != null) {
            headerComponent.updateCarouselRankMsg(message);
        }
    }

    @Override
    public void onReceiveSpringFestivalMsg(LiveSpringFestivalMessage message) {
        if (!canUpdateUi()) {
            return;
        }

        LiveSpringFestivalActivityComponent sfaComponent = getComponentSafety(COMPONENT_SPRING_FESTIVAL);
        if (sfaComponent != null) {
            sfaComponent.onReceiveSpringFestivalMsg(message);
        }

        LiveRedPacketRainComponent rprComponent = getComponentSafety(COMPONENT_RED_PACKET_RAIN);
        if (rprComponent != null) {
            rprComponent.onReceiveSpringFestivalMsg(message);
        }
    }

    @Override
    public void onReceiveSpringFestivalProgressMsg(LiveSpringFestivalProgressMessage message) {
        if (!canUpdateUi()) {
            return;
        }

        LiveSpringFestivalActivityComponent sfaComponent = getComponentSafety(COMPONENT_SPRING_FESTIVAL);
        if (sfaComponent != null) {
            sfaComponent.onReceiveSpringFestivalProgressMsg(message);
        }
    }

    private final ISendEmojiCallback sendEmojiCallback = item -> {
        sendEmojiMsg(item);
        CommonLiveLogger.d(TAG, "sendEmojiCallback:" + item);
    };

    @Override
    public void onFansRemindMessage(LiveFansRemindMessage liveFansRemindMessage) {
        // no-op，用户收到提醒主播开通粉丝团消息，由于现在主播默认开通粉丝团，已经不存在这个推送场景
    }

    @Override
    public void roomChange(long roomId, Bundle bundle) {
        removeDelayTasks();
        if (isFullScreenLandscapeVideoRoom()) {
            orientationChange(Configuration.ORIENTATION_PORTRAIT);
        }
        exitLiveFullScreen(isFullScreenLandscapeVideoRoom());
        super.roomChange(roomId, bundle);
    }

    @Override
    public boolean isPrivateComponentShowing() {
        CommonPrivateChatComponent chatComponent
                = getComponentSafety(AudienceCompConfig.COMPONENT_PRIVATE_CHAT_SHOW);
        if (chatComponent != null) {
            return chatComponent.isPrivateComponentShowing();
        }

        return super.isPrivateComponentShowing();
    }

    @Override
    public void onReceivedHotTopicMessage(CommonChatHotTopicMessage message) {
        if (message == null || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        super.onReceivedHotTopicMessage(message);

        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createHotTopicMsg(
                mRoomDetail, message
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 普通弹幕体验消息
     *
     * @param message LiveDanmuFreeModel
     */
    @Override
    public void onReceivedDanmuFreeMessage(LiveDanmuFreeModel message) {
        CommonLiveLogger.d(TAG, "收到普通弹幕体验消息: " + message.toString());
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_FREE_DANMU, message
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 普通弹幕剩余次数消息
     *
     * @param message LiveDanmuCountModel
     */
    @Override
    public void onReceivedDanmuCountMessage(LiveDanmuCountModel message) {
        if (null == message || !canUpdateUi()) {
            return;
        }

        CommonLiveLogger.d(TAG, "普通弹幕剩余次数消息: " + message);
        IInputPanelComponent panelComponent = getComponentSafety(AudienceCompConfig.COMPONENT_INPUT_PANEL);
        if (null != panelComponent) {
            panelComponent.updateDanMuCount(
                    message.getAmount() == null ? 0 : message.getAmount(),
                    message.getOrdinaryWillOverTime() == null ? 0 : message.getOrdinaryWillOverTime(),
                    message.getOrdinaryLimit() == null ? 0 : message.getOrdinaryLimit());
        }
    }


    /**
     * 主播侧推流状态变化消息
     *
     * @param message 主播侧推流状态变化消息
     */
    @Override
    public void onReceiveCdnStatusMessage(CommonCdnStatusMessage message) {
        LamiaHelper.Log.i("官方直播间：", "CDN状态-" + message.toString());
        super.onReceiveCdnStatusMessage(message);
        if (!canUpdateUi()) {
            return;
        }
        mCdnStatus = message.getStatusType();
        if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
            if (getStreamManager() != null) {
                getStreamManager().updateCdnStatus(message.getStatusType());
            }
            IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
            if (null != friendModeComponent) {
                friendModeComponent.onReceiveCdnStatusMessage(message);
            }

            IRoomLoadingComponent roomLoadingComponent = getComponentSafety(COMPONENT_ROOM_LOADING);
            if (null != roomLoadingComponent) {
                roomLoadingComponent.updateCdnStatus(mCdnStatus);
            }
        } else {
            IVideoPlayerComponent component = getComponentSafety(COMPONENT_VIDEO_PLAYER);
            if (null != component) {
                component.updateCdnStatus(message.getStatusType());
            }
        }
        if (premiereFlag) {
            premierePlay(mPremiereInfo);
        }
    }

    @Override
    public void onReceiveAuditMsg(LiveAuditNotify message) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createAuditMsg(message);
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void onEntryAddFragment(Fragment fragment) {
        super.onEntryAddFragment(fragment);

        dismissOnlinePageH5Dialog();

        IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.dismissBottomTips();
        }
    }

    @Override
    public void updateOfficialFollowStatus(boolean follow) {
        IAudienceHeaderComponent headerComponent = getComponentSafety(COMPONENT_HEADER);
        if (null != headerComponent) {
            headerComponent.updateOfficialFollowStatus(follow);
        }
    }

    private void showLiveRoom() {
        if (mRoomDetail != null && mRoomDetail.getLiveRecordInfo() != null
                && mRoomDetail.getLiveAnchorInfo() != null) {

            long roomId = mRoomDetail.getLiveRecordInfo().roomId;
            Long chatId = mRoomDetail.getLiveRecordInfo().chatId;
            long liveId = mRoomDetail.getLiveRecordInfo().id;
            long hostUid = mRoomDetail.getLiveAnchorInfo().uid;

            Long uid = UserInfoMannage.getUid();

            ShareUtils.registerShareResultAndUpload(getContext(), roomId, chatId, liveId, uid, hostUid);

            if (getActivity() != null) {
                SharePanelDialog sharePanelDialog = ShareUtils.shareLive(getActivity(), liveId,
                        roomId,
                        ShareUtils.getLiveShareData(mRoomDetail),
                        ICustomShareContentType.SHARE_TYPE_LIVEPERSONAL,
                        hostUid);

                mShareDialogRef = new WeakReference<>(sharePanelDialog);
            }
        }
    }

    @Override
    public boolean canChatListScrollVertically(int direction) {
        IBaseChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        return comp == null || comp.canScrollVertically(direction);
    }

    @Override
    public void doFollowHost(int sceneType) {
        long hostUid = getHostUid();
        if (hostUid <= 0) return;

        followUser(hostUid, sceneType, () -> onFollow(hostUid, true));
    }

    @Override
    public void onReceiveAnchorTaskMsg(LiveAnchorTaskMessage msg) {
        super.onReceiveAnchorTaskMsg(msg);
        ITaskComponent hostTaskComponent = getComponentSafety(COMPONENT_HOST_TASK);
        if (hostTaskComponent != null) {
            List<LiveAnchorTaskMessage.AnchorTaskOrWish> tasks = msg.getAnchorTaskVos();
            if (!tasks.isEmpty() && msg.getType() == TYPE_ANCHOR_TASK && msg.getLevel() > 0) {
                for (int i = 0; i < tasks.size(); i++) {
                    LiveAnchorTaskMessage.AnchorTaskOrWish anchorTask = tasks.get(i);
                    anchorTask.setLevel(msg.getLevel());
                }
            }
            hostTaskComponent.onReceivedAnchorTaskMessage(msg);
        }
    }

    @Override
    protected void updatePremiereInfo(PremiereInfo premiereInfo) {
        super.updatePremiereInfo(premiereInfo);
        if (ConstantsOpenSdk.isDebug) {
            Log.e("LivePremiere", "onGetPremiereInfo premiereInfo " + premiereInfo);
        }
        if (premiereFlag && premiereInfo != null) {
            PremierePlayStatisticsManager.getInstance().setDetail(getRoomDetail());
            PremierePlayStatisticsManager.getInstance().setPremiereInfo(premiereInfo);

            getComponentSafety(AudienceCompConfig.COMPONENT_PREMIERE_SOME_POP);
            getComponentSafety(AudienceCompConfig.COMPONENT_PREMIERE_BUY_ALBUM_GUIDE);
            getComponentSafety(AudienceCompConfig.COMPONENT_COMMON_BUY_ALBUM);

            if (premiereInfo.getAuthInfo() == null || premiereInfo.getTrackInfo() == null) {
                return;
            }
            if (premiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_LIVE_END) {
                LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, " premiere status == PREMIERE_LIVE_END ");
                return;
            }
            if (getStreamManager() == null || mRoomDetail == null) {
                return;
            }

            premierePlay(premiereInfo);

        }
    }

    private void premierePlay(PremiereInfo premiereInfo) {
        // 播放处理 start
        // 首映前 系统开播 无cdn流 不拉流
        if (!premiereFlag || premiereInfo == null) {
            return;
        }
        if (premiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_PRE && mCdnStatus == CdnStatus.CDN_STATUS_STOP) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, " cdn_status_stop ，play ");
            Logger.d("LivePremiere", "0 --- cdn_status_stop ，play  ");
            stopPlayPremiereVoice();
            return;
        }

        // 非首映中，直接播放
        if ((premiereInfo.getPremiereStatus() != PremiereStatus.PREMIERE_PRE
                && premiereInfo.getPremiereStatus() != PremiereStatus.PREMIERE_ING)) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, " is not premiere ing ，play ");
            Logger.d("LivePremiere", "1 --- is not premiere ing ，play  ");
            playPremiereVoice();
            return;
        }
        // 首映中，未登录，不播放
        if (!UserInfoMannage.hasLogined()) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "premiereing ，has not login, stop play ");
            Logger.d("LivePremiere", "2 --- premiereing ，has not login, stop play   ");
            stopPlayPremiereVoice();
            CustomToast.showDebugFailToast("首映中，未登录，停止播放");
            return;
        }
        if (premiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_PRE) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "now is premiere pre ，play ");
            Logger.d("LivePremiere", "now is premiere pre  ，play   ");
            CustomToast.showDebugFailToast("暖场中，，直接播放");
            playPremiereVoice();
            return;
        }
        // 首映中，有权限，直接播放
        if (premiereInfo.getAuthInfo() != null && premiereInfo.getAuthInfo().isHasAuth()) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "premiereing ，is auth，play ");
            Logger.d("LivePremiere", "3 --- premiereing ，is auth，play   ");
            CustomToast.showDebugFailToast("首映中，有权限，直接播放");
            playPremiereVoice();
            return;
        }
        // 首映中，无权限，还有试听时长，直接播放
        if (premiereInfo.getTrackInfo() != null && premiereInfo.getTrackInfo().getRemainTrialDuration() > 0) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "premiereing ，no auth， remain trial duration " + premiereInfo.getTrackInfo().getRemainTrialDuration() + " play");
            Logger.d("LivePremiere", "4 --- premiereing ，no auth， remain trial duration  " + premiereInfo.getTrackInfo().getRemainTrialDuration() + " play");
            playPremiereVoice();
            CustomToast.showDebugFailToast("首映中，无权限，有试听时长" + premiereInfo.getTrackInfo().getRemainTrialDuration() + " s，直接播放");
            return;
        }
        // 首映中，无权限，没有试听时长，停止播放
        LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "premiereing ，no auth，no remain trial duration，stop play duration " + premiereInfo.getTrackInfo().getRemainTrialDuration());
        Logger.d("LivePremiere", "5 --- premiereing ，no auth，no remain trial duration，stop play duration ");
        stopPlayPremiereVoice();
        CustomToast.showDebugFailToast("首映中，无权限，没有试听时长，停止播放");
        // 播放处理
    }

    public void checkHasAuthOrRemainTimeInPremiereIng() {
        PremiereInfo premiereInfo = mPremiereInfo;
        if (!canUpdateUi()) {
            return;
        }
        long roomId = PlayTools.getLiveRoomId(XmPlayerManager.getInstance(mContext).getCurrSound());
        if (getRoomId() != roomId) {
            return;
        }
        if (!premiereFlag || premiereInfo == null) {
            return;
        }
        // 非首映中，不停止
        if ((premiereInfo.getPremiereStatus() != PremiereStatus.PREMIERE_PRE
                && premiereInfo.getPremiereStatus() != PremiereStatus.PREMIERE_ING)) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "checkHasAuthOrRemainTimeInPremiereIng， is not premiere pre or ing ，return ");
            return;
        }
        // 首映中，未登录，不播放
        if (!UserInfoMannage.hasLogined()) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "checkHasAuthOrRemainTimeInPremiereIng，premiereing ，has not login, stop play ");
            stopPlayPremiereVoice();
            CustomToast.showDebugFailToast("test 首映中，未登录，不播放,播放器起播，停了它");
            return;
        }
        // 暖场中，不停止
        if (premiereInfo.getPremiereStatus() == PremiereStatus.PREMIERE_PRE) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "checkHasAuthOrRemainTimeInPremiereIng， now is pre  ，dont stop , return ");
            return;
        }
        // 首映中，有权限，不停止
        if (premiereInfo.getAuthInfo() != null && premiereInfo.getAuthInfo().isHasAuth()) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "checkHasAuthOrRemainTimeInPremiereIng，premiereing ，is auth，not stop ");
            return;
        }
        // 首映中，无权限，还有试听时长，直接播放
        if (premiereInfo.getTrackInfo() != null && premiereInfo.getTrackInfo().getRemainTrialDuration() > 0) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "checkHasAuthOrRemainTimeInPremiereIng，premiereing ，no auth， remain trial duration " + premiereInfo.getTrackInfo().getRemainTrialDuration() + " play");
            return;
        }
        // 首映中，无权限，没有试听时长，停止播放
        LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "checkHasAuthOrRemainTimeInPremiereIng，premiereing ，no auth，no remain trial duration，stop play duration " + premiereInfo.getTrackInfo().getRemainTrialDuration());
        CustomToast.showDebugFailToast("test 首映中，无权限，没有试听时长,播放器起播，停了它");
        stopPlayPremiereVoice();
    }

    private void playPremiereVoice() {
        if (!canUpdateUi()) {
            return;
        }
        if (getStreamManager() == null) {
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "playPremiereVoice ，mStreamManager null ");
            CustomToast.showDebugFailToast("playPremiereVoice but mStreamManager null");
            return;
        }
        getStreamManager().setMediaSourceAndRoomDetail(mRoomDetail.getLivePlaySourceInfo(), mBusinessId, PlayableModel.KIND_LIVE_FLV, mRoomDetail.getPlayUrl(), XMediaplayerImpl.TYPE_AUDIO);
    }

    private void stopPlayPremiereVoice() {
        if (!canUpdateUi()) {
            return;
        }
        if (getStreamManager() != null) {
            getStreamManager().stopPlayStream();
            LamiaBizLog.INSTANCE.writeLogFile(LamiaBizLog.LIVE_AUDIENCE_ROOM, "stopPlayPremiereVoice ");
        }
        AllLiveRoomStarter.stopCurrentAudioPlayer();
    }


    @Override
    protected void setPremiereRoomFlag(boolean isPremiereRoom) {
        super.setPremiereRoomFlag(isPremiereRoom);
        if (!isPremiereRoom) {
            PremierePlayStatisticsManager.getInstance().setDetail(null);
            PremierePlayStatisticsManager.getInstance().setPremiereInfo(null);
        }
    }

    @Override
    public void onReceiveFloatScreenMessage(CommonFloatScreenMessage msg) {
        if (premiereFlag && PremiereRoomUtil.isPremiereStyleRoom(mPremiereInfo)) {
            ConnectLogUtilWrapper.log(getRoomBizType(), "onReceiveFloatScreenMessage, return:" + premiereFlag + ",mPremiereInfo:" + mPremiereInfo);
            return;
        }
        super.onReceiveFloatScreenMessage(msg);
    }

    @Override
    public void onOpenGameStartMessageReceivedMsg(GameStart msg) {
        // 仅仅用户端处理 && 视频直播场景
        if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
            return;
        }
        ILiveGameControlComponent gameControlComponent = getComponentSafety(COMPONENT_GAME_CONTROL);
        if (null != gameControlComponent) {
            gameControlComponent.gameStart();
        }
    }

    @Override
    public void onOpenGameStopMessageReceivedMsg(GameStop msg) {
        // 仅仅用户端处理 && 视频直播场景
        if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
            return;
        }
        ILiveGameControlComponent gameControlComponent = getComponentSafety(COMPONENT_GAME_CONTROL);
        if (null != gameControlComponent) {
            gameControlComponent.gameStop();
        }
    }

    @Override
    public void onStatusNotifyMessageReceivedMsg(UserStatusNotify msg) {
        // 仅仅用户端处理 && 视频直播场景
        if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
            return;
        }
        ILiveGameControlComponent gameControlComponent = getComponentSafety(COMPONENT_GAME_CONTROL);
        if (null != gameControlComponent) {
            gameControlComponent.gameGameStatusNotify(msg);
        }
    }

    @Override
    public boolean interceptAllChatListGuide() {
        ILiveGameControlComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_GAME_CONTROL);
        if (component != null) {
            return component.isGameStart();
        }
        if(getRoomParentFragment().isExchangeGuideAnimationShowing()){
            return true;
        }
        return super.interceptAllChatListGuide();
    }


    @Override
    protected IClearScreenConfig getClearScreenConfig() {
        return new AudienceClearScreenConfig(mRootView);
    }

    @Override
    public void onSlideScreenToClearState(boolean alphaFadeOut, boolean fromUser) {
        super.onSlideScreenToClearState(alphaFadeOut, fromUser);
        IInteractivePlayComponent component = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != component) {
            component.clearScreenStatusChange(true);
        }
    }

    @Override
    public void onSlideScreenToInitState(boolean alphaFadeIn, boolean fromUser) {
        super.onSlideScreenToInitState(alphaFadeIn, fromUser);
        IInteractivePlayComponent component = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (null != component) {
            component.clearScreenStatusChange(false);
        }
    }

    @Override
    public void releasePkModeUI() {
        super.releasePkModeUI();
        IVideoPkAudienceComponent component = getComponentSafety(COMPONENT_VIDEO_PK_AUDIENCE);
        if (null != component) {
            component.releasePkModeUI();
        }
    }
}
