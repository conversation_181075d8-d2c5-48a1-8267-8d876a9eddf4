package com.ximalaya.ting.android.liveaudience.components.component2;

import static com.ximalaya.ting.android.live.common.chatlist.trace.LiveChatGuideEventTraceKt.traceFansClubGuideClicked;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_STUB;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CLICK_COMBO_EFFECT;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_EXIT_ROOM;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_GIFT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HEADER;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_INPUT_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_PIA_PANEL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_PLAYER_FAIL;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_RIGHT_AD;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_VIDEO_PLAYER;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_AUDIENCE_MIC;
import static com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig.COMPONENT_FRIEND_MODE;

import android.content.res.Configuration;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.chatlist.NotifyFollowerManager;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatItemViewType;
import com.ximalaya.ting.android.live.common.input.model.HotWordModel;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCommonActGiftGuideMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveMenuData;
import com.ximalaya.ting.android.live.common.lib.entity.LiveNewbieGuideMsg;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.PackageInfo;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.ViewExtensionKt;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.host.components.BaseLiveComponentInteraction;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.buyalbum.IBuyAlbumComponent;
import com.ximalaya.ting.android.live.host.components.buyalbum.IBuyAlbumInteraction;
import com.ximalaya.ting.android.live.host.components.comboeffect.IClickComboEffectComponent;
import com.ximalaya.ting.android.live.host.components.comboeffect.IClickComboEffectComponentInteraction;
import com.ximalaya.ting.android.live.host.components.enterroom.ILiveEnterAndGiftPopComponent;
import com.ximalaya.ting.android.live.host.components.exitroom.IExitRoomComponent;
import com.ximalaya.ting.android.live.host.components.guide.chatlist.IBaseChatListGuideComponent;
import com.ximalaya.ting.android.live.host.components.guide.newbie.INewbieGuideComponent;
import com.ximalaya.ting.android.live.host.components.guide.newbie.INewbieGuideComponentInteraction;
import com.ximalaya.ting.android.live.host.components.hotword.IHotWordComponent;
import com.ximalaya.ting.android.live.host.components.input.IInputPanelComponent;
import com.ximalaya.ting.android.live.host.components.luckybg.ILuckyBagInteraction;
import com.ximalaya.ting.android.live.host.components.recommend.IRecommendLiveComponentInteraction;
import com.ximalaya.ting.android.live.host.components.redpack.IRedPackComponentInteraction;
import com.ximalaya.ting.android.live.host.components.rightarea.IRoomRightAreaComponent;
import com.ximalaya.ting.android.live.host.components.sellgoods.ISellGoodsComponentInteraction;
import com.ximalaya.ting.android.live.host.components.subscriberoom.RoomSubscribeItem;
import com.ximalaya.ting.android.live.host.components.vote.ILiveVoteComponentInteraction;
import com.ximalaya.ting.android.live.host.fragment.morelive.MoreLiveDialogFragment;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.constant.BaseCommonProtoConstant;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonSimpleTxtMsg;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IManager;
import com.ximalaya.ting.android.live.lib.chatroom.util.TokenUtil;
import com.ximalaya.ting.android.live.lib.livetopic.bean.MediaInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.Anchor;
import com.ximalaya.ting.android.live.lib.stream.publish.LiveRoomUserActionStateManager;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.lifecycle.IComponentHost;
import com.ximalaya.ting.android.live.lifecycle.IComponentStateCallBack;
import com.ximalaya.ting.android.liveaudience.components.bottombar.IBottomBarComponent;
import com.ximalaya.ting.android.liveaudience.components.bottombar.IBottomInteraction;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.AVChatListMsgProducer;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.IAVChatListComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.IAVChatListComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.chatlist.guide.AVChatListGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.guide.IAVChatListGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.guide.IAVChatListGuideComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.chatlisttimedtask.IAudienceRoomTimedTaskInteraction;
import com.ximalaya.ting.android.liveaudience.components.coupon.ICouponComponent;
import com.ximalaya.ting.android.liveaudience.components.coupon.ICouponInteraction;
import com.ximalaya.ting.android.liveaudience.components.danmu.LiveBarrageComponent;
import com.ximalaya.ting.android.liveaudience.components.enterroom.LiveAVEnterAndGiftPopComponent;
import com.ximalaya.ting.android.liveaudience.components.gamecontrol.ILiveGameControlComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.giftpanel.IAudienceGiftPanelComponent;
import com.ximalaya.ting.android.liveaudience.components.giftpanel.IAudienceGiftPanelComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.giftpop.IGiftPopInteraction;
import com.ximalaya.ting.android.liveaudience.components.guide.common.IAVCommonGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.guide.common.IAVCommonGuideComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.header.IAudienceHeaderComponent;
import com.ximalaya.ting.android.liveaudience.components.header.ILamiaHeaderInteraction;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IInteractivePlayComponent;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IPiaModeComponent;
import com.ximalaya.ting.android.liveaudience.components.mic.IAudienceMicComponent;
import com.ximalaya.ting.android.liveaudience.components.mic.IAudienceMicInteraction;
import com.ximalaya.ting.android.liveaudience.components.mic.IMicBaseComponent;
import com.ximalaya.ting.android.liveaudience.components.pk.IVideoPkAudienceInteraction;
import com.ximalaya.ting.android.liveaudience.components.playerfail.IPlayerFailComponent;
import com.ximalaya.ting.android.liveaudience.components.playerfail.IPlayerFailInteraction;
import com.ximalaya.ting.android.liveaudience.components.premiere.IPremiereCommonIInteraction;
import com.ximalaya.ting.android.liveaudience.components.seal.ISealListInteraction;
import com.ximalaya.ting.android.liveaudience.components.shoppingfloatscreen.IShoppingFloatInteraction;
import com.ximalaya.ting.android.liveaudience.components.tuning.IAVTuningComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.videoplayer.IVideoPlayerComponent;
import com.ximalaya.ting.android.liveaudience.components.videoplayer.IVideoPlayerComponentInteraction;
import com.ximalaya.ting.android.liveaudience.constants.MicConstants;
import com.ximalaya.ting.android.liveaudience.data.model.pk.PkBuffAndPropInfo;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonMicPkPanelScore;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonMicPkPanelSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkAnchorInfo;
import com.ximalaya.ting.android.liveaudience.fragment.room.LiveAudienceRoomFragment;
import com.ximalaya.ting.android.liveaudience.fragment.room.LiveRoomBaseFragment;
import com.ximalaya.ting.android.liveaudience.giftModule.loader.LiveGiftLoader;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkHelper;
import com.ximalaya.ting.android.liveaudience.view.pk.PkPanelView;
import com.ximalaya.ting.android.liveim.base.callback.ISendCallback;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.liveim.micmessage.entity.GroupOnlineUserListSyncResult;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;
import java.util.Map;

import PK.Base.Mode;
import PK.Base.RandomPkType;
import RM.Base.ClickType;
import RM.XChat.DoubleClickReq;
import RM.XChat.DoubleClickRsp;

/**
 * 个播观众端组件通信接口。
 *
 * <AUTHOR>
 */
public class AudienceComponentInteraction extends BaseLiveComponentInteraction implements
        IBaseAudienceInteraction,
        IGiftPopInteraction,
        IShoppingFloatInteraction,
        ISellGoodsComponentInteraction,
        IAudienceRoomTimedTaskInteraction,
        ILamiaHeaderInteraction,
        ILuckyBagInteraction,
        IRedPackComponentInteraction,
        ICouponInteraction,
        ISealListInteraction,
        INewbieGuideComponentInteraction,
        IAVTuningComponentInteraction,
        IAVChatListComponentInteraction,
        IAVChatListGuideComponentInteraction,
        IBottomInteraction,
        IVideoPlayerComponentInteraction,
        IAudienceGiftPanelComponentInteraction,
        IAVCommonGuideComponentInteraction,
        IClickComboEffectComponentInteraction,
        IAudienceMicInteraction,
        IVideoPkAudienceInteraction,
        ILiveGameControlComponentInteraction,
        IPremiereCommonIInteraction,
        IBuyAlbumInteraction,
        IPlayerFailInteraction,
        ILiveVoteComponentInteraction,
        IRecommendLiveComponentInteraction {

    LiveRoomBaseFragment liveAVRoomBaseFragment;

    private static final String TAG = "AudienceComponentInteraction";

    @Keep
    public AudienceComponentInteraction() {

    }

    @Override
    public void setComponentHost(IComponentHost<?> componentHost) {
        super.setComponentHost(componentHost);
        if (componentHost.getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            liveAVRoomBaseFragment = (LiveRoomBaseFragment) componentHost.getHostContext().getFragment();
        }
    }

    @Override
    public PlayerConstants.ResolutionRatio getVideoSizeRatio() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            return ((LiveRoomBaseFragment) getComponentHost().getHostContext().getFragment()).getVideoSizeRatio();
        }
        return null;
    }

    @Override
    public int getMainDarkColor() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            return ((LiveRoomBaseFragment) getComponentHost().getHostContext().getFragment()).getMainDarkColor();
        }
        return 0;
    }

    @Override
    public int getMainColor() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            return ((LiveRoomBaseFragment) getComponentHost().getHostContext().getFragment()).getMainColor();
        }
        return 0;
    }

    @Override
    public void showUserInfoPop(long hostUid, boolean isPk, long roomId, long matchedHostId) {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) getComponentHost().getHostContext().getFragment()).showUserInfoPop(hostUid, isPk, roomId, matchedHostId);
        }
    }

    @Override
    public boolean isNeedAnonymity() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            return ((LiveRoomBaseFragment) getComponentHost().getHostContext().getFragment()).isNeedAnonymity();
        }
        return false;
    }

    @Override
    public int getGiftPanelHeight() {
        IAudienceGiftPanelComponent component = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (component != null) return component.getGiftSendDialogHeight();
        return 0;
    }

    @Override
    public boolean isFromHostFragment() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveRoomBaseFragment) {
            return ((LiveRoomBaseFragment) getComponentHost().getHostContext().getFragment()).isFromHostFragment();
        }
        return false;
    }

    @Override
    public int getVideoWidth() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) getComponentHost().getHostContext().getFragment()).getVideoWidth();
        }
        return 0;
    }

    @Override
    public int getVideoHeight() {
        if (getComponentHost().getHostContext().getFragment() instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) getComponentHost().getHostContext().getFragment()).getVideoHeight();
        }
        return 0;
    }

    @Override
    public void showInputPanel() {
        try {
            IInputPanelComponent component = mBaseRoomFragment.getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void dispatchHotWordModel(HotWordModel model) {
        try {
            IInputPanelComponent component = mBaseRoomFragment.getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.setHotWord(model);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void selectHotWord(String hotWord) {
        try {
            IInputPanelComponent component = mBaseRoomFragment.getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.selectHotWord(hotWord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void performHotWordBtnClick() {
        try {
            IInputPanelComponent component = mBaseRoomFragment.getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.performHotWordBtnClick();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onHotWordViewVisibleChange(boolean show) {
        View rightRoot = mBaseRoomFragment.findViewById(R.id.live_id_right_area_root);
        if (rightRoot != null) {
            ViewExtensionKt.setDynamicMarginBottom(rightRoot, show, 100, 60);
        }
        try {
            if (getComponentSafety(COMPONENT_AUDIENCE_MIC) != null) {
                ((IAudienceMicComponent) getComponentSafety(COMPONENT_AUDIENCE_MIC)).setIsShowHotWord(show);
            }
        } catch (Exception e) {
            CustomToast.showDebugFailToast(e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onGoodsShow(boolean goodsShow) {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) mBaseRoomFragment).onGoodsOperationViewShow(goodsShow);
        }
    }

    @Override
    public void onTopOperationViewShow(boolean show) {
        super.onTopOperationViewShow(show);
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) mBaseRoomFragment).onTopOperationViewShow(show);
        }
    }

    @Override
    public void onBottomOperationViewShow(boolean show) {
        super.onBottomOperationViewShow(show);
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) mBaseRoomFragment).onBottomOperationViewShow(show);
        }
    }

    @Override
    public void onBottomOperationViewClicked() {
        try {
            IAVCommonGuideComponent comp = getComponent(IBaseRoomCompConfig.COMPONENT_COMMON_GUIDE);
            comp.dismissCommonActGiftGuide();
        } catch (Exception e) {
            CustomToast.showDebugFailToast(e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onAdPromotionShow(boolean show) {
        super.onAdPromotionShow(show);
        if (liveAVRoomBaseFragment != null) {
            liveAVRoomBaseFragment.onAdPromotionShow(show);
        }
    }

    @Override
    public void onHitButtonVisibilityChanged(int s) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            try {
                IBottomBarComponent component = getComponentHost().getComponentManager().getComponent(COMPONENT_BOTTOM_BAR);
                component.onHitButtonVisibilityChanged(s);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onSendGiftDialogStateChanged(boolean show) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ILiveEnterAndGiftPopComponent enterAndGiftPopComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_ENTER_GIFT_POP);
            if (enterAndGiftPopComponent != null) {
                enterAndGiftPopComponent.onGiftDialogShowStateChange(show);
            }

            IBottomBarComponent bottomBarComponent = getComponentSafety(COMPONENT_BOTTOM_BAR);
            if (bottomBarComponent != null) {
                bottomBarComponent.onSendGiftDialogStateChanged(show);
            }

            INewbieGuideComponent newbieGuideComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_NEWBIE_GUIDE);
            if (newbieGuideComponent != null) {
                newbieGuideComponent.onGiftPanelVisibilityStatusChanged(show);
            }
        }
    }

    @Override
    public void onRepeatBtnStateChanged(boolean show) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            try {
                IBottomBarComponent component = mBaseRoomFragment.getPresenter().getComponentManager().getComponent(COMPONENT_BOTTOM_BAR);
                component.handleAlphaByRepeatBtnState(show);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showFansClubDialogFragment() {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            mBaseRoomFragment.showFansClubDialogFragment();
        }
    }

    @Override
    public void debugShowAllGuide() {
        if (ConstantsOpenSdk.isDebug) {
            IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
            if (comp != null) {
                comp.showFollowHostWithSayHiGuide();
                comp.showNewbieSayHiGuide();
                comp.showFansClubGuide();
                comp.tryShowDailyGiftGuide(LiveCommonActGiftGuideMsg.produceDebugData());
                comp.showTimeRestrictedGiftGuide(LiveNewbieGuideMsg.produceDebugData());
            }
        }
    }

    @Override
    public void goRankPkMatch() {
        if (getLiveMediaType() == LiveMediaType.TYPE_VIDEO && RoomModeManager.isNewMicMode()) {
            IMicBaseComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
            if (component != null) {
                component.checkNeedShowSwitchModeWarningDialog(v -> {
                    LiveClientManager.getInstance().getLiveMicService().stopMic(new ISendCallback() {
                        @Override
                        public void onSendSuccess() {
                            CustomToast.showDebugFailToast("已关闭连麦");
                            startMatchRankPk();
                        }

                        @Override
                        public void onSendError(int code, String message) {
                            CustomToast.showDebugFailToast("关闭连麦失败");
                        }
                    });
                });
            }
        } else {
            startMatchRankPk();
        }
    }

    /**
     * 开始发起匹配新排位PK
     */
    private void startMatchRankPk() {
        LivePkHelper.getInstance().startPkMatch(
                Mode.MODE_SIMPLE_DIVISION.getValue(),
                0,
                getLiveMediaType(),
                RandomPkType.RANDOM_PK_ORDINARY.getValue()
        );
    }

    @Override
    public void handlePackageTabClick() {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            try {
                IBottomBarComponent component = mBaseRoomFragment.getPresenter().getComponentManager().getComponent(COMPONENT_BOTTOM_BAR);
                component.hideGiftButtonRedPoint();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showOpenNoblePage(int grade, long buyForUid) {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) mBaseRoomFragment).showOpenNoblePage(grade);
        }
    }

    @Override
    public void showOpenGuardPage(int grade) {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) mBaseRoomFragment).showOpenGuardPage(grade);
        }
    }

    @Override
    public void adapterFollowMessage(CommonChatMessage message) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).adapterFollowMessage(message);
        }
    }

    @Override
    public boolean isJoinFansClub() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).isJoinFansClub();
        }
        return false;
    }

    @Override
    public void showJoinFansClubGuide() {
        AVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showFansClubGuide();
    }

    @Override
    public void finishLamiaRoomFromCloseClick() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            mBaseRoomFragment.finishFragment();
        }
    }

    @Override
    public void miniLamiaRoomFromMiniClick() {
        IExitRoomComponent component = getComponentSafety(COMPONENT_EXIT_ROOM);
        if (null != component) {
            component.requestMiniRoom();
        }
    }

    @Override
    public void showOnlineH5Page() {
        if (mBaseRoomFragment != null) {
            (mBaseRoomFragment).showOnlineH5Page();
        }
    }

    @Override
    public void showGiftRankH5Page() {
        if (mBaseRoomFragment != null) {
            (mBaseRoomFragment).showGiftRankH5Page();
        }
    }

    @Override
    public void onClickCouponView() {
        try {
            ICouponComponent component = getComponent(AudienceCompConfig.COMPONENT_COUPON);
            component.handleClickCouponView();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showUrlPage(String url) {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.showCustomH5Dialog(url);
        }
    }

    @Override
    public void showMoreLiveDialog(
            int pos, boolean fromSlide, @Nullable MoreLiveDialogFragment.RecParam recParam
    ) {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.showMoreLiveDialog(pos, fromSlide, recParam);
        }
    }

    @Override
    public void requestPlayMode(int playMode) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).requestPlayMode(playMode);
        }
    }

    @Override
    public void doBarrageOperation(boolean isSwitchOn) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            try {
                LiveBarrageComponent component = getComponent(AudienceCompConfig.COMPONENT_BARRAGE);
                if (isSwitchOn) {
                    component.showBarrage();
                } else {
                    component.hideBarrage();
                }
                // 直播间-横屏-弹幕按钮  点击事件
                new XMTraceApi.Trace()
                        .click(39275)
                        .put("currPage", "liveRoom")
                        .put(LiveRecordInfoManager.getInstance().getBaseProps())
                        .createTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showCustomH5Dialog(Bundle dialogBundle) {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.showCustomH5Dialog(dialogBundle);
        }
    }

    @Override
    public void showGuardListPage() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).showGuardListPage();
        }
    }

    @Override
    public void showGiftPop(GiftShowTask task) {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.addToSmallGiftPopTask(task);
        }
    }

    @Override
    public void bottomClickLineFunction() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickLineFunction();
        }
    }

    @Override
    public void bottomClickShop() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickShop();
        }
    }

    @Override
    public void bottomClickSendGift(long giftId) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickSendGift(giftId);
        }
    }

    @Override
    public void bottomClickShare() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickShare();
        }
    }

    @Override
    public void bottomClickMoreAction() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickMoreAction();
        }
    }

    @Override
    public void bottomClickPiaDrama() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickPiaDrama();
        }
    }

    @Override
    public void bottomClickFriendModeEmotion() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickFriendModeEmotion();
        }
    }

    @Override
    public void bottomClickInput() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            LiveAudienceRoomFragment audienceRoomFragment = (LiveAudienceRoomFragment) mBaseRoomFragment;
            if (!UserInfoMannage.hasLogined()) {
                if (audienceRoomFragment.isFullScreenLandscapeVideoRoom()) {
                    //全屏模式退出
                    try {
                        IVideoPlayerComponent component = getComponent(COMPONENT_VIDEO_PLAYER);
                        component.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                UserInfoMannage.gotoLogin(getComponentHost().getHostContext().getActivity());
                return;
            }
            IInputPanelComponent panelComponent;
            try {
                panelComponent = getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
                panelComponent.show();
            } catch (Exception e) {
                e.printStackTrace();
            }

            INewbieGuideComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_NEWBIE_GUIDE);
            if (comp != null) comp.clearEmojiGuideShowTask();

            try {
                IAudienceGiftPanelComponent component = getComponent(COMPONENT_GIFT_PANEL);
                component.showRepeatDialog(View.INVISIBLE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void bottomClickEmotionEntrance() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).bottomClickEmotionEntrance();
        }
    }

    @Override
    public void onFriendModeOperationClick() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).onFriendModeOperationClick();
        }
    }

    @Override
    public void onFriendModeRequestGetMicClick() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).onFriendModeRequestGetMicClick();
        }
    }

    @Override
    public void dismissMicEmotionDialog() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).dismissMicEmotionDialog();
        }
    }

    @Override
    public boolean isSendGiftDialogShow() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).isSendGiftDialogShow();
        }
        return false;
    }

    @Override
    public void retryLoginIfNotConnected() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).retryLogin();
        }
    }

    @Nullable
    @Override
    public LiveMenuData getMoreMenuData() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).getMoreMenuData();
        }
        return null;
    }

    @Nullable
    @Override
    public PkPanelView getPkPanelView() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).getPkPanelView();
        }
        return null;
    }

    @Override
    public void updateSystemUiVisibility() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            mBaseRoomFragment.updateSystemUiVisibility();
        }
    }

    @Override
    public boolean isKeyboardDialogShowing() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).isKeyboardDialogShowing();
        }
        return false;
    }

    @Override
    public boolean isFragmentRealVisible() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).isFragmentRealVisible();
        }
        return false;
    }

    @Override
    public boolean isRealVisable() {
        return mBaseRoomFragment.isRealVisable();
    }

    @Override
    public void onRoomLoadErrorViewShow(boolean show) {
        super.onRoomLoadErrorViewShow(show);
        try {
            IAudienceHeaderComponent component = getComponent(AudienceCompConfig.COMPONENT_HEADER);
            component.showOrHideHeaderViews(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Nullable
    @Override
    public ViewGroup getRoomMp4BackgroundViewParent() {
        return mBaseRoomFragment.findViewById(R.id.live_lamia_room_container);
    }

    @Override
    public void sendGuideItem(@NonNull LiveNewbieGuideMsg.GuideModel giftInfo) {
        switch (giftInfo.getType()) {
            case LiveNewbieGuideMsg.TYPE_GIFT:
                IAudienceGiftPanelComponent giftComp = getComponentSafety(COMPONENT_GIFT_PANEL);
                if (giftComp != null) giftComp.sendGuideGift(giftInfo);
                break;
            case LiveNewbieGuideMsg.TYPE_EMOJI:
                IInputPanelComponent inputComp = getComponentSafety(COMPONENT_INPUT_PANEL);
                if (inputComp != null) inputComp.trySendGuideEmoji(giftInfo);
                break;
        }
    }

    @Nullable
    @Override
    public PackageInfo.Item getPackageItem(int giftId) {
        IAudienceGiftPanelComponent giftComp = getComponentSafety(COMPONENT_GIFT_PANEL);
        if (giftComp != null) {
            return giftComp.getPackageItem(giftId);
        }
        return null;
    }

    @Override
    public void showChatListTimeRestrictedGiftGuide(@NonNull LiveNewbieGuideMsg.GuideModel guideInfo) {
        IBaseChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showTimeRestrictedGiftGuide(guideInfo);
    }

    @Override
    public void removeChatListTimeRestrictedGiftGuide(int guideType) {
        IBaseChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.removeTimeRestrictedGiftGuide(guideType);
    }

    @Override
    public boolean isPiaPanelExpanded() {
        IPiaModeComponent comp = mBaseRoomFragment.getComponentSafety(COMPONENT_PIA_PANEL);
        if (comp != null) {
            return comp.isPiaPanelExpanded();
        }
        return false;
    }

    /**
     * 发现有无优惠券时需要处理展示
     *
     * @param hasCoupons 有优惠券
     */
    @Override
    public void onFindAvailableCouponOrNot(boolean hasCoupons) {
        getWaitComponent(COMPONENT_HEADER, new IComponentStateCallBack<IAudienceHeaderComponent>() {
            @Override
            public void onCreated(IAudienceHeaderComponent component) {
                component.setCouponViewVisible(hasCoupons);
            }
        });
    }

    @Override
    public void handleHalfScreenUrl(String url) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).handleHalfScreenUrl(url);
        }
    }

    @Override
    public void forbidFloatWindow() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).forbidFloatWindow();
        }
    }

    @Nullable
    @Override
    public IXmMicService getAVService() {
        if (mBaseRoomFragment != null) return mBaseRoomFragment.getAvService();

        return null;
    }

    @Nullable
    @Override
    public Integer getRecordMode() {
        if (mBaseRoomFragment != null) return mBaseRoomFragment.getRecordMode();

        return null;
    }

    @Override
    public String getRandomHotWords() {
        IHotWordComponent hotWordComponent;
        try {
            hotWordComponent = liveAVRoomBaseFragment.getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_HOT_WORD);
            return hotWordComponent.getRandomHotWords();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void setHotWordsVisibility(int visibility) {
        IHotWordComponent hotWordComponent;
        try {
            hotWordComponent = liveAVRoomBaseFragment.getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_HOT_WORD);
            hotWordComponent.setHotWordsVisibility(visibility);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public View getRoomBottomLayout() {
        return liveAVRoomBaseFragment.findViewById(R.id.live_bottom_layout);
    }

    @Override
    public BaseGiftLoader<?> getGiftLoader() {
        return LiveGiftLoader.getInstance(LiveGiftLoader.class);
    }

    @Nullable
    @Override
    public Map<Long, Rect> roomSeatPositionCallback() {
        return null;
    }


    @Override
    public int getRoomMode() {
        return RoomModeManager.getInstance().getRoomMode();
    }

    @Nullable
    @Override
    public ViewStub getVoteBigAnimationViewStub() {
        return liveAVRoomBaseFragment.findViewById(R.id.live_vote_stub);
    }

    @Override
    public void onStartFullScreenPlay() {
        try {
            LiveAVEnterAndGiftPopComponent component = getComponentSafety(COMPONENT_ENTER_GIFT_POP);
            if (component != null) {
                component.clearPopMessage();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isCurrentUserHost() {
        if (liveAVRoomBaseFragment != null) {
            return liveAVRoomBaseFragment.isCurrentRoleHost();
        }
        return false;
    }

    @Override
    public void showUserInfoPop(long targetUid, @Nullable CommonChatMessage message) {
        if (liveAVRoomBaseFragment != null) {
            liveAVRoomBaseFragment.showUserInfoPop(targetUid, message);
        }
    }

    @Override
    public void sendAtMsg(@NonNull String nickname, long uid) {
        if (uid <= 0 || TextUtils.isEmpty(nickname)) return;

        // @用户
        IInputPanelComponent comp = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (comp != null) comp.sendATMessage(uid, nickname);
    }

    @Override
    public void onJoinFansClubGuideClick() {
        NotifyFollowerManager.getImpl().userTrack(NotifyFollowerManager.TYPE_ACTION_JOIN_FANS_GROUP);

        showFansClubDialogFragment();

        // 点击上报
        traceFansClubGuideClicked();
    }

    @Override
    public void previewMedia(@NonNull List<MediaInfo> mediaList, int position) {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            ((LiveRoomBaseFragment) mBaseRoomFragment).previewMedia(mediaList, position);
        }
    }

    @Override
    public void onStopFullScreenPlay() {
    }

    @Override
    public void hideView() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            try {
                IAudienceHeaderComponent headerComponent = getComponent(COMPONENT_HEADER);
                headerComponent.hideHeaderView();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                IBottomBarComponent component = getComponent(COMPONENT_BOTTOM_BAR);
                component.hideBottomLayout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showView() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            try {
                IAudienceHeaderComponent headerComponent = getComponent(COMPONENT_HEADER);
                headerComponent.showHeaderView();
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                IBottomBarComponent component = getComponent(COMPONENT_BOTTOM_BAR);
                component.showBottomLayout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showPreviewAnnouncementDialog() {
        if (liveAVRoomBaseFragment != null) liveAVRoomBaseFragment.showPreviewAnnouncementDialog();
    }

    @Override
    public void showAnnouncementDialog() {
        if (liveAVRoomBaseFragment != null) liveAVRoomBaseFragment.showAnnouncementDialog();
    }

    @Override
    public void shareLive() {
        if (liveAVRoomBaseFragment != null) liveAVRoomBaseFragment.shareLive();
    }

    @Override
    public void playFinished() {
    }

    @Override
    public void sendRandomHotWords() {
        IInputPanelComponent comp = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (comp != null) comp.sendRandomHotWords();
    }

    @Override
    public void updateResolutionRatioAndSize(@Nullable PlayerConstants.ResolutionRatio resolutionRatio, int width, int height) {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).updateResolutionRatioAndSize(resolutionRatio, width, height);
        }
    }

    @Override
    public void sendRewardMsg(@NonNull String nickname) {
        if (mBaseRoomFragment == null || mBaseRoomFragment.getContext() == null) return;

        IInputPanelComponent comp = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (comp != null) comp.sendRewardMessage(
                nickname,
                mBaseRoomFragment.getContext().getString(R.string.live_audience_reward_content)
        );
    }

    @Override
    public void fansRemindClick(@NonNull String msgContent) {
        IInputPanelComponent comp = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (comp != null) comp.sendCustomMsg(msgContent);
    }


    @Override
    public boolean isPking() {


        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) mBaseRoomFragment).isPking();
        }
        return false;
    }

    @Override
    public boolean isGroupMicOrMicPking() {
        if (getComponentSafety(COMPONENT_AUDIENCE_MIC) != null) {
            return RoomModeManager.isGroupMicMode() || RoomModeManager.isPkMicMode() || ((IAudienceMicComponent) getComponentSafety(COMPONENT_AUDIENCE_MIC)).getGroupMicAnchorCount() > 1;
        }
        return RoomModeManager.isGroupMicMode() || RoomModeManager.isPkMicMode();
    }

    @Override
    public void dispatchVideoControllerClickEvent(float x, float y, boolean isLandController) {
        IClickComboEffectComponent comp = getComponentSafety(COMPONENT_CLICK_COMBO_EFFECT);
        if (comp != null) comp.dispatchClickEvent(x, y, isLandController);
    }

    public void onClickAcceptMicReq(long targetUid) {
        // no-op
    }

    @Override
    public void activateChatListStubComponent() {
        getComponentSafety(COMPONENT_CHAT_LIST_STUB);
    }

    @Override
    public boolean hasDialogShowing() {
        if (mBaseRoomFragment instanceof LiveRoomBaseFragment) {
            return mBaseRoomFragment.hasDialogShowing();
        }

        return false;
    }

    @Override
    public void addToBigGiftTask(@NonNull GiftShowTask task) {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.addToBigGiftTask(task);
        }
    }

    @Override
    public void muteUserMic(boolean mute) {

    }

    @Override
    public boolean isMicEnable() {
        IXmMicService avService = mBaseRoomFragment.getAvService();
        return avService != null && avService.getMicEnabled();
    }

    @Override
    public boolean isPublish() {
        IXmMicService avService = mBaseRoomFragment.getAvService();
        return avService != null && avService.isPublish();
    }

    @Override
    public void showTopicAndNoticeDialog() {
        if (liveAVRoomBaseFragment != null) {
            liveAVRoomBaseFragment.showAnnouncementDialog();
        }
    }

    @Override
    public void dismissTopicAndNoticeDialog() {
        if (liveAVRoomBaseFragment != null) {
            liveAVRoomBaseFragment.dismissTopicAndNoticeDialog();
        }
    }

    @Override
    public void sendFansIntimacyMessage(String message) {
        CommonSimpleTxtMsg infoMsg = new CommonSimpleTxtMsg(message);
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SYSTEM_HINT, infoMsg
        );
        if (msg != null && liveAVRoomBaseFragment != null)
            liveAVRoomBaseFragment.onReceiveChatMessage(msg);
    }

    @Override
    public void newbieSayHi() {
        IInputPanelComponent comp = getComponentSafety(COMPONENT_INPUT_PANEL);
        if (comp != null) comp.sendRandomHotWords();
    }

    @Nullable
    @Override
    public CommonChatMessage getSayHiMsg() {
        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) return comp.getSayHiMsg();

        return null;
    }

    @Override
    public void sayHi(@NonNull CommonChatMessage sayHiMsg) {
        IAVChatListComponent comp = getComponentSafety(COMPONENT_CHAT_LIST);
        if (comp != null) comp.sayHi(sayHiMsg);
    }

    @Override
    public void tryShowDailyGiftGuide(@NonNull LiveCommonActGiftGuideMsg guideMsg) {
        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.tryShowDailyGiftGuide(guideMsg);
    }

    @Override
    public void dismissShowingDailyGiftGuide() {
        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.dismissShowingDailyGiftGuide();
    }

    @Override
    public void buyTrack(long albumId, long trackId, @NonNull String source) {
        IBuyAlbumComponent component = null;
        try {
            component = getComponent(AudienceCompConfig.COMPONENT_COMMON_BUY_ALBUM);
            component.buyTrack(albumId, trackId, source);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @NonNull
    @Override
    public IManager getManager(@NonNull String name) {
        if (liveAVRoomBaseFragment != null) {
            return liveAVRoomBaseFragment.getManager(name);
        }
        return null;
    }

    @Override
    public void onBuyAlbumCallback() {
        if (liveAVRoomBaseFragment != null) {
            liveAVRoomBaseFragment.requestPremiereInfo();
        }
    }

    @Override
    public void showChatListPremiereAlbumGuide() {
        AVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showPremiereAlbumGuide();
    }

    @Override
    public void dismissChatListPremiereAlbumGuide() {
        AVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.dismissPremiereAlbumGuide();
    }

    @Override
    public void onHostOpenMic(boolean isNeedChatTips) {
        if (isNeedChatTips) {
            // 用户端 - 主播开启连麦
            CommonSimpleTxtMsg infoMsg = new CommonSimpleTxtMsg("主播开启观众连线啦，快去和主播连线互动吧~");
            CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_SYSTEM_HINT, infoMsg
            );
            if (msg != null && liveAVRoomBaseFragment != null)
                liveAVRoomBaseFragment.onReceiveChatMessage(msg);
        }
        IBottomBarComponent bottomBarComp = getComponentSafety(COMPONENT_BOTTOM_BAR);
        if (null != bottomBarComp) {
            bottomBarComp.onRoomLineOpen();
        }
    }

    @Override
    public void onHostCloseMic(boolean isNeedChatTips, boolean isNeedHideLineBtn) {

        if (isNeedChatTips) {
            // 用户端 - 主播关闭连麦
            CommonSimpleTxtMsg infoMsg = new CommonSimpleTxtMsg("主播关闭了观众连线");
            CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                    ChatItemViewType.TYPE_COMMON_SYSTEM_HINT, infoMsg
            );
            if (msg != null && liveAVRoomBaseFragment != null)
                liveAVRoomBaseFragment.onReceiveChatMessage(msg);
        }
        if (isNeedHideLineBtn) {
            try {
                IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
                component.onRoomLineClose();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onMicStatusChanged(int micType, int state) {
        if (state == MicConstants.STATE_CONNECTED) {
            try {
                IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
                component.onRoomLineConnected(micType);
            } catch (Exception e) {
                e.printStackTrace();
            }
            LiveRoomUserActionStateManager.getInstance().setUserMicState(BaseCommonProtoConstant.CommonUserStatus.USER_STATUS_MICING);
        } else if (state == MicConstants.STATE_REQUESTING) {
            try {
                IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
                component.onRoomLineWaiting(micType);
            } catch (Exception e) {
                e.printStackTrace();
            }
            LiveRoomUserActionStateManager.getInstance().setUserMicState(BaseCommonProtoConstant.CommonUserStatus.USER_STATUS_WAITING);
        } else if (state == MicConstants.STATE_IDLE) {
            try {
                IBottomBarComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
                component.onRoomLineDisconnected(micType);
            } catch (Exception e) {
                e.printStackTrace();
            }
            LiveRoomUserActionStateManager.getInstance().setUserMicState(BaseCommonProtoConstant.CommonUserStatus.USER_STATUS_OFFLINE);
        }
    }

    @Override
    public void onAudienceGetHostPlayChange(boolean isOpenFlv) {
        if (liveAVRoomBaseFragment != null && liveAVRoomBaseFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) liveAVRoomBaseFragment).onAudienceGetHostPlayChange(isOpenFlv);
        }
    }


    @Override
    public void onAVChange(boolean isAudio) {
        if (liveAVRoomBaseFragment == null || liveAVRoomBaseFragment.getRoomDetail() == null) {
            return;
        }
        if (getLiveMediaType() != LiveMediaType.TYPE_AUDIO) {
            return;
        }

        IVideoPlayerComponent playerComponent = getComponentSafety(COMPONENT_VIDEO_PLAYER);
        IInteractivePlayComponent friendModeComponent = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (isAudio) {
            if (playerComponent != null) {
                playerComponent.hidePlayer();
            }
            if (friendModeComponent != null) {
                friendModeComponent.showOfficialCard();
            }
        } else {
            LiveVideoPlayerManager.getInstance().setRoomDetail(
                    liveAVRoomBaseFragment.getRoomDetail().getLivePlaySourceInfo(),
                    PlayableModel.KIND_LIVE_FLV,
                    getRoomBizType(),
                    liveAVRoomBaseFragment.getRoomDetail().getPlayUrl()
            );
            if (playerComponent != null) {
                playerComponent.initInfoAndShow(
                        liveAVRoomBaseFragment.getRoomDetail().getPlayUrl(),
                        PlayerConstants.PLAYTYPE_LIVE
                );
            }
            if (friendModeComponent != null) {
                friendModeComponent.hideOfficialCard();
            }
        }
    }

    @Override
    public void onMicViewShowing(boolean isShow) {
        if (liveAVRoomBaseFragment != null && liveAVRoomBaseFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) liveAVRoomBaseFragment).onMicViewShowing(isShow);
        }
    }

    @Nullable
    @Override
    public List<Anchor> getAnchorMicOrPKInfo() {
        if (liveAVRoomBaseFragment != null && liveAVRoomBaseFragment instanceof LiveAudienceRoomFragment) {
            return ((LiveAudienceRoomFragment) liveAVRoomBaseFragment).getAnchorMicOrPKInfo();
        }
        return null;
    }

    @Override
    public boolean isFollowAnchorDialogIsShowed() {
        return liveAVRoomBaseFragment != null && liveAVRoomBaseFragment instanceof LiveAudienceRoomFragment
                && ((LiveAudienceRoomFragment) liveAVRoomBaseFragment).isFollowAnchorDialogIsShowed();
    }

    @Override
    public void onGroupMicStatusChanged(int anchorCount) {
        if (liveAVRoomBaseFragment == null || !liveAVRoomBaseFragment.canUpdateUi()) {
            return;
        }
        boolean isMicing = anchorCount > 1;
        try {
            IRoomRightAreaComponent component = getExistComponent(AudienceCompConfig.COMPONENT_RIGHT_AD);
            component.updateBannerViewsOnGroupMicStatusChanged(isMicing);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (isMicing && liveAVRoomBaseFragment.getCurOrientation() != Configuration.ORIENTATION_PORTRAIT) {
            requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
        }
    }

    @Override
    public void updateMicUserSpeakVolume(long uid, double volume) {
        if (liveAVRoomBaseFragment == null || !liveAVRoomBaseFragment.canUpdateUi()) {
            return;
        }
        getWaitComponent(COMPONENT_FRIEND_MODE, new IComponentStateCallBack<IInteractivePlayComponent>() {
            @Override
            public void onCreated(IInteractivePlayComponent component) {
                component.updateMicUserSpeakVolume(uid, volume);
            }
        });
    }

    @Override
    public void onPkPanelVisibility(boolean visible) {
        if (liveAVRoomBaseFragment != null && liveAVRoomBaseFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) liveAVRoomBaseFragment).onPkPanelVisibility(visible);
        }
    }

    @Override
    public int getCurOrientation() {
        if (liveAVRoomBaseFragment != null) {
            return liveAVRoomBaseFragment.getCurOrientation();
        }
        return 0;
    }

    @Override
    public void sendText(@NonNull String content) {
        try {
            IInputPanelComponent component = getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.sendCustomMsg(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void clearGuideMessage(boolean includeExecuting) {
        try {
            IBaseChatListGuideComponent component = getComponent(COMPONENT_CHAT_LIST_GUIDE);
            component.clearAllGuide(includeExecuting);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void anchorMicViewVisible(boolean visible) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.anchorMicViewVisible(visible);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateMultiPkScore(@NonNull List<CommonPkAnchorInfo> anchorInfos) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.updateMultiPkScore(anchorInfos);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void releaseMicUIAndHideView() {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.releaseMicUIAndHideView();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean liveAnchorMicInfoViewIsVisible() {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            return friendModeComponent.liveAnchorMicInfoViewIsVisiable();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void showPkMicUI(@NonNull List<Anchor> micPkAnchors, long hostUid, int LiveMediaType) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.showPkMicUI(micPkAnchors, hostUid, LiveMediaType);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setPkProgressIconInfo(@NonNull PkBuffAndPropInfo.PkProgressIconModel pkProgressIconModel) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.setPkProgressIconInfo(pkProgressIconModel);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onMicPkPanelScoreResult(@NonNull CommonMicPkPanelScore micPkPanelScore) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.onMicPkPanelScoreResult(micPkPanelScore);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onMicPkPanelSyncRspResult(@NonNull CommonMicPkPanelSyncRsp micPkPanelSyncRsp, CommonMicPkPanelScore micPkPanelScoreInfo) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.onMicPkPanelSyncRspResult(micPkPanelSyncRsp, micPkPanelScoreInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onGroupMicStatusChangedView(@NonNull GroupOnlineUserListSyncResult groupOnlineUserListSyncResult) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.onGroupMicStatusChangedView(groupOnlineUserListSyncResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateAsrResult(@NonNull String anchorId, @Nullable String result, int optType, @Nullable String anchorName, long ts) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.updateAsrResult(anchorId, result, optType, anchorName, ts);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void endFullScreen() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) mBaseRoomFragment).endFullScreenLandscape();
        }
    }

    @Override
    public void setVideoPkBgVisible(boolean isVisible, boolean isAvPk) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.setVideoPkBgVisible(isVisible, isAvPk);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setVideoPkAvatarBg(long visitorUid) {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.setVideoPkAvatarBg(visitorUid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setVideoPkDefaultBg() {
        try {
            IInteractivePlayComponent friendModeComponent = getComponent(COMPONENT_FRIEND_MODE);
            friendModeComponent.setVideoPkDefaultBg();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showAnnouncementEditDialog() {
        if (mBaseRoomFragment instanceof LiveAudienceRoomFragment) {
            LiveAudienceRoomFragment room = (LiveAudienceRoomFragment) mBaseRoomFragment;
            room.getMoreItemOnclickListener().onClickTopicItem();
        }
    }

    @Override
    public void resumePlay() {
        if (liveAVRoomBaseFragment instanceof LiveAudienceRoomFragment) {
            ((LiveAudienceRoomFragment) liveAVRoomBaseFragment).resumePlay();
        }
    }

    @Override
    public void setPlayerFailVisible(boolean isVisible) {
        try {
            IPlayerFailComponent playerFailComponent = getComponent(COMPONENT_PLAYER_FAIL);
            playerFailComponent.setVisible(isVisible);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onAvatarClick(@NonNull GiftShowTask task) {
        showUserInfoPop(task.senderUid);
    }

    @Override
    public boolean isFriendMode() {
        return RoomModeManager.isFriendsMode();
    }

    @Override
    public void sendDoubleClickMsg(@NonNull ClickType clickType) {
        if (mBaseRoomFragment != null) {
            long uniqueId = TokenUtil.generateUniqueId();
            DoubleClickReq req = new DoubleClickReq.Builder()
                    .uniqueId(uniqueId)
                    .clickType(clickType.getValue())
                    .build();
            mBaseRoomFragment.getConnectionManager().sendMessage(
                    uniqueId, req,
                    new ChatRoomConnectionManager.ISendResultCallback<DoubleClickRsp>() {

                        @Override
                        public void onSuccess(DoubleClickRsp doubleClickRsp) {
                            Logger.i("DoubleClick", "succeed rsp -> " + doubleClickRsp);
                        }

                        @Override
                        public void onError(int errorCode, String errorMessage) {
                            Logger.i("DoubleClick", "failed rsp -> (" + errorCode + ") " + errorMessage);
                        }
                    }
            );
        }
    }

    @Override
    public void onClickStatusChanged(boolean isDoubleClicking) {
        // 观众端双击时头部定位在小心心数 tag，结束时恢复轮播
        IAudienceHeaderComponent comp = getComponentSafety(COMPONENT_HEADER);
        if (comp != null) comp.setSubtitleHeartCountStatus(isDoubleClicking);
    }

    @Override
    public void onClickChatListSpace(@NonNull MotionEvent e) {
        IClickComboEffectComponent comp = getComponentSafety(COMPONENT_CLICK_COMBO_EFFECT);
        if (comp != null) comp.dispatchClickEvent(e.getX(), e.getY(), false);
    }

    @Override
    public boolean isShowingRecommendLivePage() {
        return liveAVRoomBaseFragment != null && liveAVRoomBaseFragment.isShowingRecommendLivePage();
    }

    @Override
    public void onRoomPreviewDelete(RoomSubscribeItem item) {
        IAudienceHeaderComponent comp = getComponentSafety(COMPONENT_HEADER);
        if (comp != null) {
            comp.onRoomPreviewDelete(item);
        }
    }

    @Override
    public void onSubscribePanelShow() {
        IRoomRightAreaComponent comp = getComponentSafety(COMPONENT_RIGHT_AD);
        if (comp != null) {
            comp.shrinkTopBanner();
        }
    }

    @Override
    public void onAVChangePKPanel(boolean isAudio) {
        IInteractivePlayComponent comp = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (comp != null) comp.onAVChangePKPanel(isAudio);
    }

    @Override
    public void onRecommendAlbumSubscribeClicked() {
        IInteractivePlayComponent comp = getComponentSafety(COMPONENT_FRIEND_MODE);
        if (comp != null) comp.onRecommendAlbumSubscribeClicked();
    }

    @Override
    public boolean isExchangeGuideShowing() {
        return mBaseRoomFragment != null
                && mBaseRoomFragment.getRoomParentFragment() != null
                && mBaseRoomFragment.getRoomParentFragment().isExchangeGuideAnimationShowing();
    }

    @Override
    public void queryHistoryMessage(long msgId, int pageCount) {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.queryHistoryMessage(msgId, pageCount);
        }
    }
}
