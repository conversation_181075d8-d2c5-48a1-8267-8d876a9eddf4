package com.ximalaya.ting.android.liveaudience.components.interactiveplay.mvi;

import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.liveaudience.view.mode.IRoomModeData;

import java.lang.ref.SoftReference;

/**
 * 封装调用类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phone 15026804470
 */
class RoomModeDataImpl implements IRoomModeData {
    private final SoftReference<InteractivePlayComponent> mRef;

    public RoomModeDataImpl(InteractivePlayComponent component) {
        mRef = new SoftReference<>(component);
    }

    private PersonLiveDetail getDetail() {
        return mRef != null && mRef.get() != null ? mRef.get().getHostData() : null;
    }

    @Override
    public long getHostId() {
        return getDetail() != null ? getDetail().getHostUid() : 0;
    }

    @Override
    public long getLiveId() {
        return getDetail() != null ? getDetail().getLiveId() : 0;
    }

    @Override
    public long getRoomId() {
        return getDetail() != null ? getDetail().getRoomId() : 0;
    }

    @Override
    public long getPkRankGrade() {
        return getDetail() != null && getDetail().getPkRankInfo() != null ? getDetail().getPkRankInfo().grade : 0;
    }

    @Override
    public String getPkRankIconUrl() {
        return getDetail() != null && getDetail().getPkRankInfo() != null ? getDetail().getPkRankInfo().icon : null;
    }

    @Override
    public int getLiveRoomType() {
        return getDetail() != null ? getDetail().getRoomBizType() : 0;
    }

    @Override
    public boolean isFollowed() {
        return getDetail() != null && getDetail().isFollowed();
    }

    @Override
    public String getAvatarUrl() {
        return getDetail() != null ? getDetail().getLargeAvatar() : "";
    }
}
