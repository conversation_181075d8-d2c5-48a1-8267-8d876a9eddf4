package com.ximalaya.ting.android.liveaudience.components.base.header;

import static android.view.View.GONE;
import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.LiveTemplateManager;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.entity.CelebrationType;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCarouselRankNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRoomAvatarDecorate;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereStatus;
import com.ximalaya.ting.android.live.common.lib.gift.anim.ISuperGiftView;
import com.ximalaya.ting.android.live.common.lib.gift.anim.callback.IAnimationCallback;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.anim.svg.SVGAView;
import com.ximalaya.ting.android.live.common.lib.gift.download.AnimationPathSelector;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.model.LiveTemplateModel;
import com.ximalaya.ting.android.live.common.lib.utils.LiveRouterUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.ViewExtensionKt;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.live.host.view.header.LiveAnchorHeaderView;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.liveaudience.components.header.ILamiaHeaderInteraction;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.view.header.LiveRankView;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.Objects;

/**
 * 头部组件抽象类
 * 1.主播端和观众端主播榜功能
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phone 15026804470
 */
public abstract class AbsHeaderComponent extends LamiaComponent {
    /**
     * 头部用于区分直播间模式标识
     * 1：个播音视频普通模式  2：个播首映室  3：音频官方直播间
     * 4：视频官方直播间  5：主播端直播间
     * <p>
     * 未被整合的模式 ：聊天室直播间 ， 课程直播间
     */
    protected int liveBizTypeInHeader = 0;

    /**
     * 个播音视频 普通模式
     */
    protected static final int PERSONAL_LIVE_ROOM_TYPE = 0x001;
    /**
     * 个播首映室
     */
    protected static final int PERSONAL_PREMIERE_LIVE_ROOM_TYPE = 0x002;
    /**
     * 音频官方直播间
     */
    protected static final int AUDIO_OFFICIAL_LIVE_ROOM_TYPE = 0x003;
    /**
     * 视频官方直播间 9.2.84版本 上线
     */
    protected static final int VIDEO_OFFICIAL_LIVE_ROOM_TYPE = 0x004;

    protected LiveRankView mRanksView;

    /**
     * 首映式 头部logo
     */
    protected ImageView mPremiereLogoIv;

    /**
     * 头部 主播信息 View
     * 包含头像、主播名、小心心、关注按钮
     */
    protected LiveAnchorHeaderView mAnchorHeaderView;
    /**
     * 首映室 头部主播信息 View
     * 包含头像、主播名、小心心、关注按钮
     */
    protected LiveAnchorHeaderView mPremiereAnchorHeaderView;

    /**
     * 生日场：主播边框
     */
    protected ImageView mHeaderHostAvatarStokeIv;
    /**
     * 奖励下发头像框装扮，与生日场框互斥，生日场优先级高于该装扮
     */
    protected ImageView mHeaderHostRewardAvatarDecorateIv;

    /**
     * 用户关系动画视图，仅用户端可见
     */
    protected SVGAView mHeaderRelationshipAnimationView;

    protected View mHeaderThirdLine;

    /**
     * 观众端返回键
     */
    protected ImageView mIvClose;

    /**
     * 整个头部————1、2、3行View
     */
    protected View mHeaderRl_All;


    protected void initView() {
        mIvClose = findViewById(R.id.live_btn_close_room);
        mHeaderThirdLine = findViewById(R.id.live_header_third_line);
        mRanksView = findViewById(R.id.live_header_ranks);
        mPremiereLogoIv = findViewById(R.id.live_header_premiere_logo);
        mPremiereAnchorHeaderView = findViewById(R.id.live_header_premiere);

        mRanksView.setOnRankClickListener(this::headerRankClick);
        mHeaderRl_All = findViewById(R.id.live_room_header_all);
        LiveViewPositionManager.getInstance().registerLayoutChangeListener(LiveViewPositionType.TYPE_FULL_HEADER, mHeaderRl_All);
    }

    @Override
    public void bindData(@NonNull PersonLiveDetail detail) {
        super.bindData(detail);
        if (getHostData() == null) {
            return;
        }
        LamiaHelper.Log.i("absHeaderCom bindData");
        runAfterViewInflate(this::updateRank);
    }

    /**
     * 接口更新小时榜
     */
    protected void updateRank() {
        LamiaHelper.Log.i("updateRank isOfficialLive" + isOfficialLive());
        //官播间没有主播榜单
        if (mRanksView == null || getHostData() == null || getHostData().getRankInfos() == null || isOfficialLive()) {
            return;
        }
        mRanksView.updateRankInfo(getHostData().getRankInfos());
        makeShowTrack(40713, "主播榜");
    }


    /**
     * 信令更新主播 轮播榜单
     */
    public void updateCarouselRankMsg(LiveCarouselRankNotify message) {
        runAfterViewInflate(() -> {
            if (mRanksView == null) {
                return;
            }
            mRanksView.updateCarouselRankMsg(message);
        });
    }

    @Override
    public void updatePremiere() {
        super.updatePremiere();
        if (mPremiereLogoIv == null || getHostData() == null) {
            return;
        }

        if (getHostData().isFollowed() || UserInfoMannage.getUid() == getHostUid()) {
            mPremiereAnchorHeaderView.setBackgroundResource(R.drawable.live_dark_radiu20_bg);
            mPremiereAnchorHeaderView.setPadding(BaseUtil.dp2px(getContext(), 10), 0, BaseUtil.dp2px(getContext(), 3), 0);
        } else {
            mPremiereAnchorHeaderView.setPadding(BaseUtil.dp2px(getContext(), 10), 0, BaseUtil.dp2px(getContext(), 3), 0);
        }

        switch (getPremiereStatus()) {
            case PremiereStatus.PREMIERE_PRE:
            case PremiereStatus.PREMIERE_ING:
            case PremiereStatus.PREMIERE_END_LIVING:
                liveBizTypeInHeader = PERSONAL_PREMIERE_LIVE_ROOM_TYPE;
                ImageManager.from(getContext()).displayImage(mPremiereLogoIv, getPremiereInfo().getCoverUrl(), -1);
                ViewStatusUtil.setVisible(VISIBLE, mPremiereLogoIv, mPremiereAnchorHeaderView, mPremiereAnchorHeaderView.getMFollowGuardView());
                mPremiereAnchorHeaderView.updateTagViewHeight();
                if (getHostData() != null && getHostData().getLiveAnchorInfo() != null) {
                    mPremiereAnchorHeaderView.setRoomLiveAnchorInfo(getHostData().getLiveAnchorInfo());
                }
                ViewStatusUtil.setVisible(
                        GONE, mAnchorHeaderView, mHeaderHostAvatarStokeIv, mHeaderHostRewardAvatarDecorateIv
                );
                //命中首映式 如果是左侧返回键 不需要灰色圆形背景
                if (mIvClose != null) {
                    mIvClose.setImageResource(0);
                    mIvClose.setImageResource(R.drawable.live_btn_close_room_abv1);
                }
                break;
            default:
                liveBizTypeInHeader = PERSONAL_LIVE_ROOM_TYPE;
                ViewStatusUtil.setVisible(GONE, mPremiereLogoIv, mPremiereAnchorHeaderView, mPremiereAnchorHeaderView.getMFollowGuardView());
                ViewStatusUtil.setVisible(VISIBLE, mAnchorHeaderView);
                if (mView != null) {
                    // 首映室头部变高，结束后如果不重新布局，会留空隙
                    mView.requestLayout();
                }
                if (mIvClose != null) {
                    mIvClose.setImageResource(0);
                    mIvClose.setImageResource(R.drawable.live_btn_close_room_abv1);
                }
                break;
        }

        setShowBirthdayView();
        setShowRewardAvatarDecorate();
    }

    protected void showRelationshipAnimation(long templateId, int loopCount) {
        if (mHeaderRelationshipAnimationView == null) {
            return;
        }
        if (templateId <= 0 || loopCount <= 0 || isOfficialLive() || isPremiereUI()) {
            mHeaderRelationshipAnimationView.setVisibility(GONE);
            return;
        }
        mHeaderRelationshipAnimationView.setVisibility(VISIBLE);
        String tag = String.valueOf(templateId);
        mHeaderRelationshipAnimationView.setTag(tag);
        mHeaderRelationshipAnimationView.setDisappearAnimation(false);
        mHeaderRelationshipAnimationView.setLoops(loopCount);
        mHeaderRelationshipAnimationView.setAnimationCallback(new IAnimationCallback() {
            @Override
            public void onStart() {}

            @Override
            public void onStop() {
                mHeaderRelationshipAnimationView.setVisibility(GONE);
                mHeaderRelationshipAnimationView.setAnimationCallback(null);
            }

            @Override
            public void onAlphaAnimationStart() {}

            @Override
            public void onDestroy() {}

            @Override
            public void onError(int errorCode, Object message) {
                mHeaderRelationshipAnimationView.setVisibility(GONE);
                mHeaderRelationshipAnimationView.setAnimationCallback(null);
            }
        });
        AnimationPathSelector.getAnimResInfoByTemplateIdAsync(templateId, result -> {
            if (!Objects.equals(mHeaderRelationshipAnimationView.getTag(), tag)) {
                return;
            }
            GiftShowTask task = new GiftShowTask();
            task.animationId = templateId;
            mHeaderRelationshipAnimationView.preparePackAndStart(
                    task, new ISuperGiftView.ProcessCallback() {
                        @Override
                        public void onFail(GiftShowTask task, String errorMsg) {
                            Logger.e("RelationshipAnimation", "播放失败(" + task.animationId + "): " + errorMsg);
                        }

                        @Override
                        public boolean isPause() {
                            return false;
                        }

                        @Override
                        public void destroy() {}

                        @Override
                        public boolean attached() {
                            return isInflated();
                        }
                    }
            );
        });
    }

    /**
     * 加载庆会头像框装扮：生日场、纪念日
     */
    protected void setShowBirthdayView() {
        runAfterViewInflate(() -> {
            if (getHostData() == null || isOfficialLive() || isPremiereUI()) {
                mHeaderHostAvatarStokeIv.setVisibility(INVISIBLE);
                return;
            }

            if (mHeaderHostAvatarStokeIv != null) {
                mHeaderHostAvatarStokeIv.setImageDrawable(CelebrationType.TYPE_CELEBRATION == getHostData().getCelebrationType() ?
                        ContextCompat.getDrawable(getContext(), R.drawable.live_header_common_celebration_stoke)
                        : ContextCompat.getDrawable(getContext(), R.drawable.live_header_common_birthday_stoke));
                mHeaderHostAvatarStokeIv.setVisibility(getHostData().isCelebrationFlag() ? VISIBLE : INVISIBLE);
            }
        });
    }

    /**
     * 设置奖励下发的头像框装扮，与庆会头像框装扮互斥，庆会头像装扮优先级高于该装扮
     */
    protected void setShowRewardAvatarDecorate() {
        runAfterViewInflate(() -> {
            // 下发奖励头像框加载逻辑
            if (mHeaderHostRewardAvatarDecorateIv == null) return;
            if (isPremiereUI() || isOfficialLive() || ViewExtensionKt.isVisible(mHeaderHostAvatarStokeIv)) {
                mHeaderHostRewardAvatarDecorateIv.setVisibility(View.GONE);
                return;
            }

            boolean needShow = false;
            int templateId = -1;
            if (getHostData() != null && getHostData().getLiveAnchorInfo() != null) {
                LiveRoomAvatarDecorate decorate = getHostData().getLiveAnchorInfo().roomAvatarDecorate;
                if (decorate != null) {
                    templateId = decorate.getTemplateId();
                    needShow = templateId > 0;
                }
            }
            ViewExtensionKt.showOrGone(mHeaderHostRewardAvatarDecorateIv, needShow);
            if (!needShow) return;

            mHeaderHostRewardAvatarDecorateIv.setTag(templateId);
            int finalTemplateId = templateId;
            LiveTemplateManager.getInstance().getSingleTemplateById(
                    templateId, new IDataCallBack<LiveTemplateModel>() {
                        @Override
                        public void onSuccess(@Nullable LiveTemplateModel data) {
                            if (!canUpdateUi() || data == null) return;

                            try {
                                int id = (int) mHeaderHostRewardAvatarDecorateIv.getTag();
                                if (id != finalTemplateId) return;

                                String url = data.getTemplateById(
                                        String.valueOf(finalTemplateId)
                                ).getBgImagePath();
                                if (url == null || url.isEmpty()) return;
                                ImageManager.from(getContext()).displayImage(
                                        mHeaderHostRewardAvatarDecorateIv, url, -1
                                );
                            } catch (Exception e) {
                                LiveXdcsUtil.doXDCS("RewardAvatarDecorate", "获取视图 Tag 失败");
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            LiveXdcsUtil.doXDCS("RewardAvatarDecorate", "请求头像框模版资源失败(" + code + "): " + message);
                        }
                    }
            );
        });
    }

    /**
     * 主播榜点击事件
     */
    protected void headerRankClick(int RankType) {
        if (mRanksView == null || getHostData() == null) {
            return;
        }

        boolean isAnchor = isFromHostFragment() && isAnchor();
        String rankName = "主播榜";

        // 拼接业务参数
        HashMap<String, String> appendParams = new HashMap<>();
        appendParams.put("isAnchor", String.valueOf(isAnchor));
        if (RankType == LiveRouterUtil.LiveHostRankJumpTab.TYPE_HOUR) {
            appendParams.put("tab", String.valueOf(LiveRouterUtil.LiveHostRankJumpTab.TYPE_HOUR));
            rankName = "小时榜";
        } else if (RankType == LiveRouterUtil.LiveHostRankJumpTab.TYPE_MOOD) {
            appendParams.put("tab", String.valueOf(LiveRouterUtil.LiveHostRankJumpTab.TYPE_MOOD));
            rankName = "人气榜";
        } else if (RankType == LiveRouterUtil.LiveHostRankJumpTab.TYPE_NEWER) {
            appendParams.put("tab", String.valueOf(LiveRouterUtil.LiveHostRankJumpTab.TYPE_NEWER));
            rankName = "新秀榜";
        } else {
            // 默认打开小时榜
            appendParams.put("tab", String.valueOf(LiveRouterUtil.LiveHostRankJumpTab.TYPE_HOUR));
        }
        // 拼接业务参数
        String url = LiveWebUtil.appendQueryParamsToUriIfKeyNotExist(LiveUrlConstants.getInstance().getH5AnchorRankUrl(), appendParams);

        // 打开主播榜页面
        ILamiaHeaderInteraction interaction = getComponentInteractionSafety(
                ILamiaHeaderInteraction.class
        );
        if (interaction != null) {
            interaction.showUrlPage(url);
        }

        // 直播间-榜单入口（主播榜、小时榜、人气榜、新秀榜等）点击事件，用户点击时上报
        new XMTraceApi.Trace()
                .click(60659)
                .put("currPage", "liveRoom")
                // 主播榜｜小时榜｜人气榜｜新秀榜
                .put("rankName", rankName)
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForClick())
                .createTrace();
    }

    @Override
    public void onSwitchRoom(long newRoomId, Bundle newArgs) {
        super.onSwitchRoom(newRoomId, newArgs);
        runAfterViewInflate(() -> {
            ViewStatusUtil.setVisible(GONE, mPremiereLogoIv, mPremiereAnchorHeaderView, mPremiereAnchorHeaderView.getMFollowGuardView());
            ViewStatusUtil.setVisible(VISIBLE, mAnchorHeaderView);

            if (mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == VISIBLE) {
                mPremiereAnchorHeaderView.updateTagViewHeight();
            }

            if (mHeaderRelationshipAnimationView != null) {
                mHeaderRelationshipAnimationView.stop();
                mHeaderRelationshipAnimationView.setVisibility(GONE);
            }
        });

    }

    @Override
    public void onSwitchSameRoom() {
        super.onSwitchSameRoom();
        runAfterViewInflate(() -> {
            if (mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == VISIBLE) {
                mPremiereAnchorHeaderView.updateTagViewHeight();
            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LiveViewPositionManager.getInstance().unregisterLayoutChangeListener(LiveViewPositionType.TYPE_FULL_HEADER, mHeaderRl_All);
    }

}
