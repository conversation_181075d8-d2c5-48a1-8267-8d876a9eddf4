package com.ximalaya.ting.android.liveaudience.components.playerfail;

import android.view.View;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;

/**
 * 播放失败组件接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phone 15026804470
 */
public class PlayerFailComponent extends LamiaComponent implements IPlayerFailComponent {

    private View mRetryTv;

    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        mRetryTv = findViewById(R.id.live_fail_retry_tv);
        mRetryTv.setOnClickListener(this);
    }

    @Override
    public void setVisible(boolean isVisible) {
        if (isVisible) {
            show();
        } else {
            hide();
        }
    }

    private void show() {
        runAfterViewInflate(new Runnable() {
            @Override
            public void run() {
                ViewStatusUtil.setVisible(View.VISIBLE, getView());
            }
        });
    }

    private void hide() {
        ViewStatusUtil.setVisible(View.GONE, getView());
    }

    @Override
    public void onClick(@NonNull View v) {
        super.onClick(v);
        if (v.getId() == R.id.live_fail_retry_tv) {
            hide();
            IPlayerFailInteraction interactionSafety = getComponentInteractionSafety(IPlayerFailInteraction.class);
            if (interactionSafety != null) {
                interactionSafety.resumePlay();
            }
        }
    }

    @Override
    public void resetView() {
        super.resetView();
        hide();
    }

}
