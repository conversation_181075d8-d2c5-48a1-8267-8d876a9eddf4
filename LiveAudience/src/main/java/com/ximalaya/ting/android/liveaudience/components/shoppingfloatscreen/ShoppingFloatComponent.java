package com.ximalaya.ting.android.liveaudience.components.shoppingfloatscreen;

import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.videoplayer.block.VideoPlayerBlock;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoShoppingMessage;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.liveaudience.view.goods.GoShoppingFloatView;

/**
 * 去购买飘屏组件
 *
 * <AUTHOR>
 * @since 2022/11/9
 */
public class ShoppingFloatComponent extends LamiaComponent implements IShoppingFloatComponent {
    /**
     * 去购买飘屏
     */
    protected GoShoppingFloatView mGoShoppingFloatView;

    @Override
    public View createComponentView(
            @NonNull LayoutInflater inflater,
            @Nullable AttributeSet attrs,
            @Nullable ViewGroup container
    ) {
        mGoShoppingFloatView = new GoShoppingFloatView(getActivity());
        mGoShoppingFloatView.setVisibility(View.VISIBLE);
        return mGoShoppingFloatView;
    }

    /**
     * 设置去购买飘屏的布局参数
     */
    private void setGoShoppingFloatViewLayoutParams() {
        if (null == mGoShoppingFloatView || !canUpdateUi()) {
            return;
        }

        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        if (getHostData() == null || getHostData().getMediaType() == LiveMediaType.TYPE_AUDIO) {
            layoutParams.addRule(RelativeLayout.ALIGN_TOP, R.id.livecomm_chat_list_container);
        } else {
            IShoppingFloatInteraction interaction = null;
            try {
                interaction = getComponentHost().getInteractionImpl(IShoppingFloatInteraction.class);
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (interaction == null) {
                return;
            }

            if (VideoPlayerBlock.isLandUIMode(interaction.getVideoSizeRatio()) && !interaction.isFromHostFragment()) {
                layoutParams.topMargin = BaseUtil.getScreenWidth(getContext()) * interaction.getVideoHeight() / interaction.getVideoWidth() - BaseUtil.dp2px(getContext(), 32);
            } else {
                layoutParams.addRule(RelativeLayout.ABOVE, R.id.livecomm_chat_list_container);
            }
        }
        mGoShoppingFloatView.setLayoutParams(layoutParams);
    }

    @Override
    public void onOrientationChange(int orientation, boolean isSameOrientation) {
        super.onOrientationChange(orientation, isSameOrientation);
        if (!canUpdateUi()) {
            return;
        }
        if (isFull()) {
            if (mGoShoppingFloatView != null) {
                mGoShoppingFloatView.setVisibility(View.GONE);
            }
        } else {
            if (isShouldUpdateResolution()) {
                onVideoResolutionRatioUpdate();
            }
        }
    }


    @Override
    public void receiveGoShoppingMessage(CommonGoShoppingMessage goShoppingMessage) {
        if (!isFunctionSwitchOpen()) {
            return;
        }

        runAfterViewInflate(() -> {
            setGoShoppingFloatViewLayoutParams();
            if (mGoShoppingFloatView != null) {
                mGoShoppingFloatView.setContent(goShoppingMessage);
            }
        });
    }
}
