package com.ximalaya.ting.android.liveaudience.components.mic;

import static com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Client.AB_LIVE_ANCHOR_FOLLOW_POP;
import static com.ximalaya.ting.android.live.common.lib.entity.LiveHostFollowStatusModel.FollowType.TYPE_FOLLOW_UNFOLLOW;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Looper;
import android.text.TextUtils;
import android.view.TextureView;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.live.asr.IAsrCallback;
import com.ximalaya.ting.android.host.live.asr.LiveMediaAsrInfo;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.chatlist.NotifyFollowerManager;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.RoomProgressiveLoadingConstants;
import com.ximalaya.ting.android.live.common.lib.base.dialog_queue.LiveDialogFragmentManager;
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.live.common.lib.entity.LiveHostFollowStatusModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveAccessibilityUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.asr.LiveAsrFactory;
import com.ximalaya.ting.android.live.common.lib.utils.asr.LiveAsrManager;
import com.ximalaya.ting.android.live.common.videoplayer.IVideoPlayerCallback;
import com.ximalaya.ting.android.live.common.videoplayer.block.VideoPlayerBlock;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.dialog.BottomMenuClickDialog;
import com.ximalaya.ting.android.live.host.data.LiveMediaSideInfo;
import com.ximalaya.ting.android.live.host.data.LiveMicAnchor;
import com.ximalaya.ting.android.live.host.data.stream.StreamExtraInfo;
import com.ximalaya.ting.android.live.host.dialog.LiveFollowMicAnchorDialog;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomConstants;
import com.ximalaya.ting.android.live.host.manager.roomcore.service.mic.IRoomMicListener;
import com.ximalaya.ting.android.live.host.manager.roomcore.service.mic.IRoomMicService;
import com.ximalaya.ting.android.live.host.utils.SDKUtils;
import com.ximalaya.ting.android.live.host.view.VideoPlayerView;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.Anchor;
import com.ximalaya.ting.android.live.lib.stream.medainfo.data.PkAndMicInfo;
import com.ximalaya.ting.android.live.lib.stream.videoplayer.LiveVideoPlayerManager;
import com.ximalaya.ting.android.live.livemic.LiveMicView;
import com.ximalaya.ting.android.live.livemic.MicUsersDialog;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceComponentInteraction;
import com.ximalaya.ting.android.liveaudience.components.mic.view.IMicInviteOperateListener;
import com.ximalaya.ting.android.liveaudience.components.mic.view.MicInviteDialog;
import com.ximalaya.ting.android.liveaudience.components.mic.viewmodel.LiveMicViewModel;
import com.ximalaya.ting.android.liveaudience.components.videoplayer.IVideoPlayerComponentInteraction;
import com.ximalaya.ting.android.liveaudience.constants.MicConstants;
import com.ximalaya.ting.android.liveaudience.constants.VideoMixConstants;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonMicPkPanelScore;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonMicPkPanelSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonMicPkScores;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkAnchorInfo;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveav.lib.constant.XmBusinessMode;
import com.ximalaya.ting.android.liveav.lib.data.StreamInfo;
import com.ximalaya.ting.android.liveav.lib.listener.IXmLiveDataCallBack;
import com.ximalaya.ting.android.liveav.lib.util.log.LiveLogUtil;
import com.ximalaya.ting.android.liveim.base.callback.ISendCallback;
import com.ximalaya.ting.android.liveim.micmessage.constants.GroupMicStatus;
import com.ximalaya.ting.android.liveim.micmessage.constants.UserMicType;
import com.ximalaya.ting.android.liveim.micmessage.constants.UserStatus;
import com.ximalaya.ting.android.liveim.micmessage.entity.GroupOnlineUser;
import com.ximalaya.ting.android.liveim.micmessage.entity.GroupOnlineUserListSyncResult;
import com.ximalaya.ting.android.liveim.micmessage.entity.InviteMicNotify;
import com.ximalaya.ting.android.liveim.micmessage.entity.MicStatus;
import com.ximalaya.ting.android.liveim.micmessage.entity.OnlineUser;
import com.ximalaya.ting.android.liveim.micmessage.entity.OnlineUserListSyncResult;
import com.ximalaya.ting.android.liveim.micmessage.entity.UserStatusSyncResult;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 观众侧连麦组件，包括直播间内主播-用户连麦、跨直播间主播-主播连麦
 * <p>
 * Note:
 * 1、在PC助手开启横屏直播且开启屏幕共享时，连麦者需要处理PC摄像头流和桌面共享流
 * 2、目前只有视频直播在 onUserJoinMic 和 onStreamExtraInfoUpdate 才有流附加信息 StreamExtraInfo
 *
 * <AUTHOR>
 */
public class AudienceMicComponent extends LiveCommonMicComponent implements IAudienceMicComponent, IMicInviteOperateListener, IVideoPlayerCallback {

    private static final String TAG = "AudienceMicComponent";


    /**
     * 连麦方式选择对话框
     */
    protected BottomMenuClickDialog mSelectMicTypeDialog;

    /**
     * 取消申请对话框
     */
    protected BottomMenuClickDialog mCancelMicRequestDialog;

    /**
     * 连麦用户弹框
     */
    private MicUsersDialog mMicUserDialog;

    /**
     * 连麦显示控件
     */
    private LiveMicView mMicView;

    /**
     * 播放器view
     */
    private VideoPlayerView mVideoPlayerView;

    private ObjectAnimator mMoveAnimator;

    /**
     * 画面是否在移动
     */
    private boolean isMoving;

    /**
     * 多人主播连麦关注弹窗是否展示过
     */
    private boolean isAnchorFollowDialogShowed;

    protected UserStatus mUserMicStatus = UserStatus.USER_STATUS_OFFLINE;

    /**
     * 当前连麦主播的总人数，包括当前主播
     */
    private int mMicAnchorCount = 1;

    /**
     * 当前连麦pk的分数信息
     */
    private CommonMicPkPanelScore mMicPkPanelScoreInfo;

    /**
     * 是否需要等信令来了更新连麦pk主播信息
     */
    private boolean needUpdateMicPkAnchorInfo = false;

    /**
     * 当前连麦的主播信息
     */
    private List<Anchor> mMicPkAnchors;

    /**
     * 主播间连麦pk状态
     */
    private int mMicPkStatus;

    /**
     * 当前连麦用户信息
     */
    private TextView mMicUserTv;

    private IRoomMicService mRoomMicService;

    /**
     * 是否正在展示热词
     */
    private boolean mIsShowHotWord = false;

    /**
     * 连麦过的主播信息
     */
    private final Map<Long, GroupOnlineUser> mOnlineAnchors = new LinkedHashMap<Long, GroupOnlineUser>(10) {
        @Override
        protected boolean removeEldestEntry(Entry<Long, GroupOnlineUser> eldest) {
            return size() >= 10;
        }
    };

    /**
     * SEI中的连麦主播信息
     */
    private String mMicingAnchorsMediaSideInfo = "";

    /**
     * 服务端给的
     */
    private boolean isOpenMic = false;

    /**
     * 连麦开关是否开启,UI上真实的开关状态，不是服务端给的
     */
    private boolean isSwitchButtonOpen = false;

    private MicStatus mMicStatus;

    /**
     * 当前观众连麦类型
     */
    private boolean isAudioMic = true;

    /**
     * 连麦中共享桌面显示
     */
    private TextureView mMicHostDeskPlayer;

    /**
     * 连麦中主播摄像头显示
     */
    private TextureView mMicHostCameraPlayer;

    /**
     * 初始状态课程共享桌面显示view的布局参数
     */
    private RelativeLayout.LayoutParams mMicHostDeskPlayerParams;

    /**
     * 初始状态课程主播摄像头显示view的布局参数
     */
    private RelativeLayout.LayoutParams mMicHostCameraPlayerParams;

    /**
     * 是否有桌面流
     */
    private boolean hasDesktopStream;
    private MicInviteDialog micInviteDialog;
    private boolean hasShowCurrentMicUserInfo;
    private String mMicNickname;
    private boolean mIsLand;
    private boolean shouldUpdateResolution;
    private LiveMicViewModel mLiveMicViewModel;
    private boolean hasAVHostMic;
    private boolean mIsOfficialEnd;

    private RoomDelayLoadPkBuffAndPropTask mDelayLoadPkBuffAndPropTask;

    @Override
    public void onCreate() {
        super.onCreate();
        setMXmMicService(LiveClientManager.getInstance().getLiveMicService());
    }

    @Override
    public void onCreateView(@Nullable View view) {
        super.onCreateView(view);
        // AudienceMicComponent跟VideoPlayerComponent公用一套UI，
        // 不会走initComponentViewAfterInflated方法，观众端需要主动注册一下SEI监听
        if (getRoomCore() != null
                && getRoomCore().getStreamManager() != null
                && getRoomCore().getStreamManager().getMediaSideInfoManager() != null) {
            getRoomCore().getStreamManager().getMediaSideInfoManager().addMediaSideInfoReceiver(this);
        }

        mVideoPlayerView = findViewById(R.id.live_video_player);
        mMicUserTv = findViewById(R.id.live_tv_mic_nickname);
        mMicView = findViewById(R.id.live_mic_preview);
        mMicView.setClickCallback(() -> {
            if (isFull()) {
                IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                if (interaction != null) {
                    interaction.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW);
                }
            }
            showMicUserDialog();

            new XMTraceApi.Trace()
                    .click(33401)
                    .put("currPage", "liveRoom")
                    .put("connectType", isAudioMic ? "1" : "2")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForClick())
                    .createTrace();

        });
        mMicView.setVisibility(View.GONE);

        mRoomMicService = (IRoomMicService) getRoomCore().getRoomCoreService(RoomConstants.RoomCoreServiceName.SERVICE_MIC);
        if (mRoomMicService != null) {
            mRoomMicService.setRoomMicListener(mRoomMicListener);
        }

        LiveVideoPlayerManager.getInstance().registerVideoPlayerListener(this);

        initPlayerUi();

        if (null != getFragment()) {
            RoomModeManager.getInstance().getPiaModeData().observe(
                    getFragment(),
                    piaMode -> LiveClientManager.getInstance().getLiveMicService().setBusinessMode(
                            piaMode ? XmBusinessMode.PiaXi : XmBusinessMode.Default)
            );

            mLiveMicViewModel = new ViewModelProvider(getFragment()).get(LiveMicViewModel.class);
            mLiveMicViewModel.getPkBuffAndPropInfoData().observe(getFragment(), pkBuffAndPropInfo -> {
                if (pkBuffAndPropInfo.getProgressIconModel() != null) {
                    IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                    if (interaction != null) {
                        interaction.setPkProgressIconInfo(pkBuffAndPropInfo.getProgressIconModel());
                    }
                }
            });
        }
    }

    private void initPlayerUi() {
        if (mMicHostDeskPlayer == null || mMicHostCameraPlayer == null) {
            mMicHostDeskPlayer = findViewById(R.id.live_mic_host_desktop_player);
            mMicHostDeskPlayerParams = (RelativeLayout.LayoutParams) mMicHostDeskPlayer.getLayoutParams();
            mMicHostCameraPlayer = findViewById(R.id.live_mic_host_camera_player);
            mMicHostCameraPlayerParams = (RelativeLayout.LayoutParams) mMicHostCameraPlayer.getLayoutParams();
            mMicHostCameraPlayer.setLayoutParams(mMicHostDeskPlayerParams);
        }
    }

    @Override
    public void bindData(@NonNull PersonLiveDetail detail) {
        super.bindData(detail);
        checkAndInitMicUserDialog();
        mMicUserDialog.updateAnchorInfo(getHostUid(), getHostNickname(), false);
        // 关播后隐藏连麦PK面板
        if (getLiveStatus() == PersonLiveBase.LIVE_STATUS_END) {
            releasePkMicUI();
        }
        if (mSelectMicTypeDialog != null) {
            mSelectMicTypeDialog.dismiss();
            mSelectMicTypeDialog = null;
        }

        HandlerManager.removeCallbacks(mDelayLoadPkBuffAndPropTask);
        mDelayLoadPkBuffAndPropTask = new RoomDelayLoadPkBuffAndPropTask(new WeakReference<>(this));
        HandlerManager.postOnUIThreadDelay(
                mDelayLoadPkBuffAndPropTask,
                RoomProgressiveLoadingConstants.LOADING_INTERACTIVE_PLAY_AREA_PK_DELAY_TIME_MS
        );
    }

    public void loadPkBuffAndPropInfo() {
        mDelayLoadPkBuffAndPropTask = null;

        if (mLiveMicViewModel != null) {
            mLiveMicViewModel.loadPkBuffAndPropInfo();
        }
    }

    @Override
    public void onReceiveRoomEndStatusChange() {
        super.onReceiveRoomEndStatusChange();
        releasePkMicUI();
    }

    private final Runnable mCheckAnchorTask = () -> {
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction == null) {
            return;
        }
        List<Anchor> list = interaction.getAnchorMicOrPKInfo();
        boolean followAnchorDialogIsShowed = interaction.isFollowAnchorDialogIsShowed();
        LamiaHelper.Log.i("多人连麦弹窗", "Runnable-是否展示过老弹窗:" + followAnchorDialogIsShowed);
        if (followAnchorDialogIsShowed) {
            LamiaHelper.Log.i("多人连麦弹窗-http", "展示过老弹窗，不再展示新弹窗");
        }
        if (list != null && !list.isEmpty() && !followAnchorDialogIsShowed) {
            //有数据
            queryFollowStatus(list);
        }
    };

    private void queryFollowStatus(List<Anchor> list) {
        List<Long> queryUidList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Long uid = list.get(i).getUid();
            queryUidList.add(uid);
        }
        CommonRequestForCommon.queryUserFollowStatus(
                queryUidList, new IDataCallBack<List<LiveHostFollowStatusModel>>() {
                    @Override
                    public void onSuccess(@Nullable List<LiveHostFollowStatusModel> data) {
                        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                        if (interaction == null) {
                            return;
                        }
                        boolean followAnchorDialogIsShowed = interaction.isFollowAnchorDialogIsShowed();
                        LamiaHelper.Log.i("多人连麦弹窗-http", "是否展示过老弹窗:" + followAnchorDialogIsShowed);
                        if (followAnchorDialogIsShowed) {
                            LamiaHelper.Log.i("多人连麦弹窗-http", "展示过老弹窗，不再展示新弹窗");
                        }
                        if (data == null || followAnchorDialogIsShowed) {
                            return;
                        }
                        if (!queryUidList.isEmpty()) {
                            if (queryUidList.get(0) != null && queryUidList.get(0) == getHostUid()) {
                                //index=0是当前房间的主播
                                //确保没有切换房间
                                List<LiveMicAnchor> anchors = new ArrayList<>();
                                for (int i = 0; i < list.size(); i++) {
                                    Anchor item = list.get(i);
                                    for (int j = 0; j < data.size(); j++) {
                                        LiveHostFollowStatusModel model = data.get(j);
                                        if (model.relationType == TYPE_FOLLOW_UNFOLLOW && item.getUid() != null && item.getUid() == model.toUid && item.getName() != null) {
                                            LiveMicAnchor liveMicAnchor = new LiveMicAnchor();
                                            liveMicAnchor.setUid(model.toUid);
                                            liveMicAnchor.setRoomOwner(model.toUid == getHostUid());
                                            liveMicAnchor.setNickName(item.getName());
                                            anchors.add(liveMicAnchor);
                                        }
                                    }
                                }
                                if (!anchors.isEmpty()) {
                                    //不为空
                                    if (canUpdateUi() && getChildFragmentManager() != null) {
                                        LamiaHelper.Log.i("多人连麦弹窗数据-http", anchors.toString());
                                        LiveFollowMicAnchorDialog dialog = LiveFollowMicAnchorDialog.newInstance(getHostUid(), "", anchors);
                                        LiveDialogFragmentManager.INSTANCE.
                                                addDialog(dialog, getChildFragmentManager());
                                        //如果弹出了多人弹窗，那么后续的单人弹窗就不要弹了
                                        NotifyFollowerManager.getImpl().updateNotifyMessagePosted(getRoomId());
                                    } else {
                                        LamiaHelper.Log.i("多人连麦弹窗数据-http", "getChildFragmentManager=null");
                                    }
                                    isAnchorFollowDialogShowed = true;
                                } else {
                                    LamiaHelper.Log.i("多人连麦弹窗数据-http", "全都关注过");
                                }

                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                }
        );

    }

    private void checkAnchorMic() {
        HandlerManager.postOnUIThreadDelay(mCheckAnchorTask, 7500);
    }

    @Override
    public void onDestroy() {
        if (mMoveAnimator != null && isMoving) {
            mMoveAnimator.cancel();
        }

        View view = null;
        if (mVideoPlayerView != null && mVideoPlayerView.getPlayerView() != null) {
            view = mVideoPlayerView.getPlayerView().getRenderView();
        }
        if (isMoving && mMoveAnimator != null) {
            mMoveAnimator.cancel();
        }
        if (view != null) {
            view.setTranslationX(0);
        }
        if (mMicUserDialog != null) {
            mMicUserDialog.dismiss();
            mMicUserDialog.setMTrackListener(null);
            mMicUserDialog.setMTrackMuteListener(null);
            mMicUserDialog.setOnAvatarClickListener(null);
            mMicUserDialog = null;
        }

        if (mRoomMicService != null) {
            mRoomMicService.setAsrCallback(null);
        }

        if (mRoomMicService != null) {
            mRoomMicService.setRoomMicListener(null);
        }
        LiveVideoPlayerManager.getInstance().unregisterVideoPlayerListener(this);

        super.onDestroy();
    }

    /**
     * 弹出选择连麦方式对话框，并发起连麦
     */
    @Override
    public void showSelectMicTypeDialog() {
        if (!canUpdateUi() || getActivity() == null) {
            return;
        }

        if (getHostUid() == UserInfoMannage.getUid()) {
            CustomToast.showFailToast("不能与自己进行连麦哦~");
            return;
        }

        //检查SDK
        if (!SDKUtils.isXmAVSDKInited() && mRoomMicService != null) {
            mRoomMicService.initMicRelated(new IXmLiveDataCallBack<Boolean>() {
                @Override
                public void onSuccess(Boolean object) {
                    showSelectMicTypeDialog();
                }

                @Override
                public void onError(int code, String message) {
                    CustomToast.showFailToast("本地推流模块初始化失败！");
                }
            });

            return;
        }

        if (mSelectMicTypeDialog == null) {

            List<BottomMenuClickDialog.MenuItemModel> menuList = new ArrayList<>();
            if (getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
                menuList.add(new BottomMenuClickDialog.MenuItemModel("视频连线", Color.parseColor("#333333"), 16, com.ximalaya.ting.android.live.common.R.drawable.live_ic_mic_video_type, 20));
            }
            menuList.add(new BottomMenuClickDialog.MenuItemModel("语音连线", Color.parseColor("#333333"), 16, com.ximalaya.ting.android.live.common.R.drawable.live_ic_mic_audio_type, 20));

            mSelectMicTypeDialog = new BottomMenuClickDialog.MenuDialogBuilder(getActivity())
                    .setTitle(null)
                    .setIsNeedCloseBtn(true)
                    .setMenuListData(menuList)
                    .setItemClickListener((view, position) -> {
                        int userMicType;
                        if (position == 0 && getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
                            userMicType = MicConstants.TYPE_VIDEO_MIC;
                            makeDialogClickTrack(33397, "视频连线");
                        } else {
                            userMicType = MicConstants.TYPE_AUDIO_MIC;
                            makeDialogClickTrack(33397, "语音连线");
                        }
                        if (mRoomMicService != null) {
                            mRoomMicService.requestJoinAnchor(1, UserMicType.fromValue(userMicType), new ISendCallback() {
                                @Override
                                public void onSendSuccess() {
                                    CustomToast.showToast("申请连线成功，已在申请队列中");
                                }

                                @Override
                                public void onSendError(int code, String message) {
                                    CustomToast.showFailToast(!TextUtils.isEmpty(message) ? message : "申请连线失败");
                                    LiveXdcsUtil.doXDCS(TAG, "requestJoinAudioMic failed:" + message);
                                }
                            });
                        }
                        mSelectMicTypeDialog.dismiss();
                    })
                    .createDialog();
        }

        if (!mSelectMicTypeDialog.isShowing()) {
            mSelectMicTypeDialog.show();
            makeDialogShowTrack(33396);
        }

    }

    @Override
    public List<Anchor> getAnchorMicInfo() {
        List<Anchor> list = new ArrayList<>();
        if (mMicPkAnchors != null) {
            Anchor host = new Anchor();
            host.setUid(getHostUid());
            String anchorName = getHostNickname();
            host.setName(anchorName);
            list.add(host);
            list.addAll(mMicPkAnchors);
        }
        LamiaHelper.Log.i("多人连麦弹窗数据-连麦", list.toString());
        return list;
    }

    @Override
    public void showMicUserDialog() {
        if (!canUpdateUi() || getActivity() == null || mMicStatus == null || null == getFragment()) {
            return;
        }
        checkAndInitMicUserDialog();
        mMicUserDialog.updateUserStatus(mUserMicStatus);
        //本地的真实开关状态
        isSwitchButtonOpen = mMicStatus.isOpen && mMicStatus.isAllowJoin;
        //连麦开关打开，并且自己不处于连麦中
        mMicUserDialog.setMicOpenAndNotLining(isSwitchButtonOpen && mUserMicStatus != UserStatus.USER_STATUS_MICING);
        mMicUserDialog.show(getFragment().getChildFragmentManager(), "MicUserDialog");

        new XMTraceApi.Trace()
                .setMetaId(33398)
                .setServiceId("dialogView")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    @Override
    public void showCancelRequestMicDialog() {
        if (!canUpdateUi() || getActivity() == null) {
            return;
        }

        if (mCancelMicRequestDialog == null) {

            List<BottomMenuClickDialog.MenuItemModel> menuList = new ArrayList<>(1);

            menuList.add(new BottomMenuClickDialog.MenuItemModel("取消申请", Color.parseColor("#FF3B30"), 16, -1, -1));

            mCancelMicRequestDialog = new BottomMenuClickDialog.MenuDialogBuilder(getActivity())
                    .setTitle("已在连线申请队列中，请耐心等待")
                    .setIsNeedCloseBtn(true)
                    .setMenuListData(menuList)
                    .setItemClickListener(new BottomMenuClickDialog.ItemViewClickListener() {
                        @Override
                        public void OnItemViewClick(View view, int position) {
                            // 点击选择连麦方式
                            if (position == 0) {
                                // 连麦者主动取消申请
                                if (mRoomMicService != null) {
                                    mRoomMicService.quitJoinAnchor(new ISendCallback() {
                                        @Override
                                        public void onSendSuccess() {
                                            CustomToast.showFailToast("已成功取消连线申请");
                                        }

                                        @Override
                                        public void onSendError(int code, String message) {
                                        }
                                    });
                                }

                                mCancelMicRequestDialog.dismiss();

                            }
                        }
                    })
                    .createDialog();
        }

        if (!mCancelMicRequestDialog.isShowing()) {
            mCancelMicRequestDialog.show();
        }
    }

    @Override
    public UserStatus getUserMicStatus() {
        return mUserMicStatus;
    }


    @Override
    public void onLiveEnd() {
        //直播结束，直接关闭邀请弹窗
        if (micInviteDialog != null) {
            micInviteDialog.dismissAllowingStateLoss();
        }
        if (mRoomMicService != null) {
            mRoomMicService.onLiveEnd();
        }
    }

    @Override
    public void onMicPkPanelSyncRspResult(CommonMicPkPanelSyncRsp micPkPanelSyncRsp) {
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.onMicPkPanelSyncRspResult(micPkPanelSyncRsp, mMicPkPanelScoreInfo);
        }
    }

    @Override
    public void onMicPkPanelScoreResult(CommonMicPkPanelScore micPkPanelScore) {
        LiveHelper.Log.i("duruochen--micpk", "观众端--onMicPkPanelScoreResult：" + micPkPanelScore);
        mMicPkPanelScoreInfo = micPkPanelScore;
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.onMicPkPanelScoreResult(micPkPanelScore);
        }
    }

    /**
     * 收到模式变更，或者PK状态结束，关闭连麦PK面板UI
     */
    @Override
    public void releasePkMicUI() {
        hasAVHostMic = false;
        mMicPkPanelScoreInfo = null;
    }

    @Override
    public void releaseMicUI() {
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.anchorMicViewVisible(false);
        }
    }

    @Override
    public int getGroupMicAnchorCount() {
        return mMicAnchorCount;
    }

    @Override
    public void updateCurrentUserInfo(LiveUserInfo object) {
        //拿到用户信息后，做判断
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction == null) {
            return;
        }
        String abFollowPop = ABTest.getString(AB_LIVE_ANCHOR_FOLLOW_POP, "simple");
        boolean followAnchorDialogIsShowed = interaction.isFollowAnchorDialogIsShowed();
        if (abFollowPop.equals("multiple") && getHostData() != null && !getHostData().isNewUser() && !followAnchorDialogIsShowed) {
            //命中多人主播弹窗+老用户+有多人主播连麦+单主播弹窗没展示过
            LamiaHelper.Log.i("多人连麦弹窗数据", "checkAnchorMic-使用新弹窗");
            checkAnchorMic();
        }
    }

    @Override
    public boolean isAnchorFollowDialogShowed() {
        return isAnchorFollowDialogShowed;
    }

    private final IAsrCallback mAsrCallback = new IAsrCallback() {
        @Override
        public void onInitSuccess() {
            Logger.i(
                    TAG,
                    "AnchorAsrCallBack onInitSuccess"
            );
        }

        @Override
        public void onInitFailure(@NonNull String error) {
            Logger.i(
                    TAG,
                    "AnchorAsrCallBack onInitFailure, error = $error"
            );
        }

        @Override
        public void onStartSuccess() {
            Logger.i(
                    TAG,
                    "AnchorAsrCallBack onStartSuccess"
            );
        }

        @Override
        public void onStop() {
            Logger.i(
                    TAG,
                    "AnchorAsrCallBack onStop"
            );
        }

        @Override
        public void onResult(@NonNull String result, int optType, long ts) {
            HandlerManager.obtainMainHandler().post(() -> {
                IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                long userId = UserInfoMannage.getUid();
                String userName = "";
                if (UserInfoMannage.getInstance().getUser() != null) {
                    userName = UserInfoMannage.getInstance().getUser().getNickname();
                }
                if (interaction != null) {
                    interaction.updateAsrResult(String.valueOf(userId), result, optType, userName, ts);
                }
                LiveMediaAsrInfo mediaSideInfo = new LiveMediaAsrInfo();
                mediaSideInfo.setType(LiveMediaAsrInfo.TYPE_ASR_MODE);
                LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean userInfo = new LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean();
                userInfo.setNickname(userName);
                userInfo.setUid(userId);
                LiveMediaAsrInfo.MediaAsrInfoContent content = new LiveMediaAsrInfo.MediaAsrInfoContent();
                content.setUserInfo(userInfo);
                content.setText(result);
                content.setOptType(optType);
                content.setTs(ts);
                mediaSideInfo.setContent(content);
                if (getMXmMicService() != null) {
                    getMXmMicService().sendMediaSideInfo(GsonUtils.toJson(mediaSideInfo));
                }
            });
        }

        @Override
        public void onError(int errorCode, @NonNull String errorMessage) {
            HandlerManager.obtainMainHandler().post(() -> {
                IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                long userId = UserInfoMannage.getUid();
                String userName = UserInfoMannage.getNickname();
                if (interaction != null) {
                    interaction.updateAsrResult(String.valueOf(userId), "AI正在识别中...", LiveMediaAsrInfo.OPT_TYPE_REPLACE, userName, 0);
                }
                LiveMediaAsrInfo mediaSideInfo = new LiveMediaAsrInfo();
                mediaSideInfo.setType(LiveMediaAsrInfo.TYPE_ASR_MODE);
                LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean userInfo = new LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean();
                userInfo.setNickname(userName);
                userInfo.setUid(userId);
                LiveMediaAsrInfo.MediaAsrInfoContent content = new LiveMediaAsrInfo.MediaAsrInfoContent();
                content.setUserInfo(userInfo);
                content.setText("AI正在识别中...");
                content.setOptType(LiveMediaAsrInfo.OPT_TYPE_REPLACE);
                mediaSideInfo.setContent(content);
                if (getMXmMicService() != null) {
                    getMXmMicService().sendMediaSideInfo(GsonUtils.toJson(mediaSideInfo));
                }
            });
        }
    };


    private final IRoomMicListener mRoomMicListener = new IRoomMicListener() {

        @Override
        public void onRequestJoinAnchorAcceptSuccess(@Nullable UserMicType userMicType) {
            if (userMicType == UserMicType.USER_MIC_TYPE_VIDEO) {
                mMicView.updateVideoMicUser(null);
            }
            IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
            if (interaction != null) {
                interaction.onMicViewShowing(true);
            }
            trackMicView();
            mMicView.show();
            mRoomMicService.setAsrCallback(mAsrCallback);
        }


        @Override
        public View getLocalPreview() {
            return mMicView == null ? null : mMicView.getVideoPreview();
        }

        @Override
        public void onGroupOnlineUsersChanged(@Nullable GroupOnlineUserListSyncResult groupOnlineUserListSyncResult) {
            if (groupOnlineUserListSyncResult != null && groupOnlineUserListSyncResult.mOnlineUsers != null
                    && !groupOnlineUserListSyncResult.mOnlineUsers.isEmpty()) {
                for (GroupOnlineUser user : groupOnlineUserListSyncResult.mOnlineUsers) {
                    if (user.groupMicStatus == GroupMicStatus.GROUP_MIC_STATUS_NORMAL) {
                        mOnlineAnchors.put(user.userId, user);
                    }
                }
                LiveHelper.Log.i("duruochen--micpk", "onGroupOnlineUsersChanged:anchorCount=" + groupOnlineUserListSyncResult.mOnlineUsers.size() + "  needUpdateMicPkAnchorInfo=" + needUpdateMicPkAnchorInfo
                        + "   mMicPkPanelScoreInfo != null:" + (mMicPkPanelScoreInfo != null));
                if (needUpdateMicPkAnchorInfo || mMicPkPanelScoreInfo != null) {
                    needUpdateMicPkAnchorInfo = false;
                    updateMicPkAnchorInfo();
                }
                IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                if (interaction != null) {
                    interaction.onGroupMicStatusChangedView(groupOnlineUserListSyncResult);
                }
            }
        }

        @Override
        public void onUserStatusChanged(@Nullable UserStatusSyncResult userStatusSyncResult) {
            handleAudienceMicStatusChange(userStatusSyncResult);

            if (mUserMicStatus == UserStatus.USER_STATUS_MICING
                    && null != userStatusSyncResult
                    && userStatusSyncResult.userStatus == UserStatus.USER_STATUS_OFFLINE) {
                CustomToast.showToast("本次连线已结束");
            }

            Logger.i(TAG, "IXmSingleRoomMicEventListener.onUserStatusChanged, userStatusSyncResult = " + userStatusSyncResult);
        }

        @Override
        public void onUserLeaveMic(@Nullable String uid, @Nullable String streamId, @Nullable String extra) {
            initPlayerUi();
            if (!TextUtils.isEmpty(uid) && String.valueOf(getHostUid()).equals(uid)
                    && StreamExtraInfo.isDesktopStream(extra)) {
                hasDesktopStream = false;
                mMicHostCameraPlayer.setLayoutParams(mMicHostDeskPlayerParams);
            }

            Logger.i(TAG, "IXmSingleRoomMicEventListener.onUserLeaveMic, uid = "
                    + uid + ", streamId = " + streamId + ", extra = " + extra);

        }

        @Override
        public void onUserJoinMic(@Nullable String uid, @Nullable String streamId, @Nullable String extraInfo) {
            initPlayerUi();

            Logger.i(TAG, "onUserJoinMic, uid = " + uid
                    + ", streamId = " + streamId
                    + ", extraInfo = " + extraInfo);

            String sid;
            if (null != streamId) {
                sid = streamId;
            } else {
                sid = "";
            }

            StreamExtraInfo se = StreamExtraInfo.parse(extraInfo);
            // se == null 表示纯音频流，新版本PC助手音频和视频直播都有extraInfo，
            // 老版本PC助手音频直播无extraInfo，视频直播有extraInfo
            if (se == null) {
                if (mRoomMicService != null) {
                    mRoomMicService.startPlayAudioStream(sid);
                }
            } else {
                if (se.isScreen) {
                    // 桌面流
                    hasDesktopStream = true;
                    mMicHostCameraPlayer.setLayoutParams(mMicHostCameraPlayerParams);
                    ViewStatusUtil.setVisible(View.VISIBLE, mMicHostDeskPlayer);
                    if (mRoomMicService != null) {
                        mRoomMicService.startPlayVideoStream(sid, mMicHostDeskPlayer);
                    }
                } else {
                    // 摄像头流
                    if (se.isOnlyAudio) {
                        if (mRoomMicService != null) {
                            mRoomMicService.startPlayAudioStream(sid);
                        }
                    } else {
                        ViewStatusUtil.setVisible(View.VISIBLE, mMicHostCameraPlayer);
                        mMicHostCameraPlayer.setLayoutParams(hasDesktopStream ? mMicHostCameraPlayerParams : mMicHostDeskPlayerParams);
                        if (mRoomMicService != null) {
                            mRoomMicService.startPlayVideoStream(sid, mMicHostCameraPlayer);
                        }
                    }
                }
            }
        }

        @Override
        public void onReceiveInviteMsg(@Nullable InviteMicNotify inviteMicNotify) {
            if (!canUpdateUi() || null == getChildFragmentManager() || null == inviteMicNotify) {
                return;
            }
            if (micInviteDialog != null && micInviteDialog.isShowing()) {
                return;
            }
            micInviteDialog = MicInviteDialog.newInstance(inviteMicNotify.nickname, inviteMicNotify.userId);
            micInviteDialog.setMMicOperateListener(AudienceMicComponent.this);
            micInviteDialog.show(getChildFragmentManager(), MicInviteDialog.class.getName());
            LamiaHelper.Log.i("邀请连麦   nickname:", inviteMicNotify.nickname + "  userId:" + inviteMicNotify.userId + "  tip:" + inviteMicNotify.tip + "  mReason:" + inviteMicNotify.mReason);
        }

        @Override
        public void onOnlineUsersInfo(@Nullable OnlineUserListSyncResult onlineUserListSyncResult) {
            if (onlineUserListSyncResult == null || !canUpdateUi()) {
                return;
            }

            checkAndInitMicUserDialog();
            if (onlineUserListSyncResult.mOnlineUsers != null) {
                mMicUserDialog.updateMicUsers(onlineUserListSyncResult.mOnlineUsers);
            }

            if (onlineUserListSyncResult.mOnlineUsers == null || onlineUserListSyncResult.mOnlineUsers.isEmpty()) {

                if (mMicView.containCurrentUser()) {
                    if (canUpdateUi()) {
                        updateUiByOnlineUsersCount(false);
                        mMicUserTv.setVisibility(View.GONE);
                        hasShowCurrentMicUserInfo = false;
                        mMicView.close();
                        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                        if (interaction != null) {
                            interaction.onMicViewShowing(false);
                        }
                    }
                } else {
                    if (canUpdateUi() && mUserMicStatus != UserStatus.USER_STATUS_MICING) {
                        updateUiByOnlineUsersCount(false);
                        mMicUserTv.setVisibility(View.GONE);
                        hasShowCurrentMicUserInfo = false;
                        mMicView.close();
                        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                        if (interaction != null) {
                            interaction.onMicViewShowing(false);
                        }
                    }
                }
                return;
            }
            final OnlineUser user = onlineUserListSyncResult.mOnlineUsers.get(0);
            boolean isMicing = false;
            for (OnlineUser onlineUser : onlineUserListSyncResult.mOnlineUsers) {
                if (onlineUser.userId == UserInfoMannage.getUid()) {
                    isMicing = true;
                    break;
                }
            }
            if (UserInfoMannage.hasLogined() && isMicing) {
                if (user.userMicType == UserMicType.USER_MIC_TYPE_AUDIO) {
                    isAudioMic = true;
                    mMicView.updateAudioMicUsers(onlineUserListSyncResult.mOnlineUsers);
                } else {
                    isAudioMic = false;
                    mMicView.updateVideoMicUser(onlineUserListSyncResult.mOnlineUsers);
                }
                mMicView.show();
                IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                if (interaction != null) {
                    interaction.onMicViewShowing(true);
                }
                trackMicView();
            } else {
                if (canUpdateUi() && !isMicing && isOpenMic) {
                    if (user.userMicType == UserMicType.USER_MIC_TYPE_VIDEO) {
                        isAudioMic = false;
                        updateUiByOnlineUsersCount(true);
                        showCurrentMicUserInfo(user.nickname);
                        mMicView.close();
                    } else {
                        isAudioMic = true;
                        mMicUserTv.setVisibility(View.GONE);
                        hasShowCurrentMicUserInfo = false;
                        mMicView.updateAudioMicUsers(onlineUserListSyncResult.mOnlineUsers);
                        mMicView.show();
                    }
                    IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                    if (interaction != null) {
                        interaction.onMicViewShowing(true);
                    }
                    trackMicView();
                }
            }

            Logger.i(TAG, "IXmSingleRoomMicEventListener.onOnlineUsersInfo, onlineUserListSyncResult = " + onlineUserListSyncResult);


        }

        @Override
        public void onMicStatusChanged(@Nullable MicStatus micStatus) {
            IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
            if (interaction == null) {
                return;
            }
            mMicStatus = micStatus;
            if (micStatus.isOpen) {
                if (isSwitchButtonOpen != micStatus.isAllowJoin) {
                    //开关发生变化
                    if (micStatus.isAllowJoin) {
                        interaction.onHostOpenMic(true);
                    } else {
                        if (mUserMicStatus == UserStatus.USER_STATUS_WAITING) {
                            //如果已经处于申请中，不隐藏按钮，只提示信息流
                            interaction.onHostCloseMic(true, false);
                        } else if (mUserMicStatus == UserStatus.USER_STATUS_MICING) {
                            interaction.onHostCloseMic(true, false);
                        } else {
                            interaction.onHostCloseMic(true, true);
                        }
                    }
                }
            } else {
                if (mUserMicStatus == UserStatus.USER_STATUS_MICING) {
                    CustomToast.showToast("本次连线已结束");
                    if (!micStatus.isAllowJoin) {
                        //挂断了，同时连麦按钮也是关闭的
                        //走到这里，一定是强制开pk模式导致被动挂断连麦，这时候会出现bug：电话虽然被挂断，但是依然显示连麦按钮
                        //这时候处于完全没开启连麦的模式：所以需要全部隐藏掉，但不需要信息流
                        interaction.onHostCloseMic(false, true);
                    }
                }

                interaction.onAudienceGetHostPlayChange(true);
                mUserMicStatus = UserStatus.USER_STATUS_OFFLINE;

                closeMicPreview();

                if (mMicUserDialog != null) {
                    mMicUserDialog.dismiss();
                }

                if (canUpdateUi() && mMicHostCameraPlayer != null) {
                    interaction.onAudienceGetHostPlayChange(true);
                    mMicHostCameraPlayer.setVisibility(View.GONE);
                    mMicHostDeskPlayer.setVisibility(View.GONE);
                }
                hasDesktopStream = false;

                if (isSwitchButtonOpen != micStatus.isAllowJoin) {
                    if (!micStatus.isAllowJoin) {
                        interaction.onHostCloseMic(true, true);
                    }
                }
            }
            //服务器的开关
            isOpenMic = micStatus.isOpen;
            //本地的真实开关状态
            isSwitchButtonOpen = micStatus.isOpen && micStatus.isAllowJoin;
            if (mMicUserDialog != null) {
                //连麦开关打开，并且自己不处于连麦中
                mMicUserDialog.setMicOpenAndNotLining(isSwitchButtonOpen && mUserMicStatus != UserStatus.USER_STATUS_MICING);
            }
        }

        @Override
        public void onStreamExtraInfoUpdate(@NonNull StreamInfo streamInfo) {
            if (streamInfo == null) {
                return;
            }
            initPlayerUi();

            Logger.i(TAG, "onStreamExtraInfoUpdate, extraInfo = " + streamInfo);

            // se == null 表示纯音频流，新版本PC助手音频和视频直播都有extraInfo，
            // 老版本PC助手音频直播无extraInfo，视频直播有extraInfo
            StreamExtraInfo se = StreamExtraInfo.parse(streamInfo.extraInfo);
            if (se == null) {
                mMicHostCameraPlayer.setVisibility(View.GONE);
            } else {
                if (!se.isScreen) {
                    if (se.isOnlyAudio) {
                        mMicHostCameraPlayer.setVisibility(View.GONE);
                    } else {
                        mMicHostCameraPlayer.setVisibility(View.VISIBLE);
                        if (mRoomMicService != null && !se.isScreen) {
                            mRoomMicService.updatePlayView(streamInfo.streamId, mMicHostCameraPlayer);
                        }
                    }
                }
            }

        }

        @Override
        public void onRecvMediaSideInfo(@Nullable LiveMediaSideInfo info) {
            AudienceMicComponent.this.onRecMediaSideInfo(info);
        }

        @Override
        public void onRecvMediaAsrResult(@NonNull LiveMediaAsrInfo.MediaAsrInfoContent result) {
            if (null != result) {
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                        if (interaction != null) {
                            interaction.updateAsrResult(
                                    String.valueOf(result.getUid()),
                                    result.getText(),
                                    result.getOptType(),
                                    result.getNickname(),
                                    result.getTs()
                            );
                        }
                    }
                });
            }
        }
    };


    private void trackMicView() {
        new XMTraceApi.Trace()
                .setMetaId(33402)
                .setServiceId("slipPage")
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .put(LiveRecordInfoManager.getInstance().getXmRequestIdPropsForSlipPage())
                .createTrace();
    }

    private void closeMicPreview() {
        if (!canUpdateUi() || mMicView == null) {
            return;
        }

        mMicView.close();
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.onMicViewShowing(false);
        }
    }

    @Override
    public void onVideoResolutionRatioUpdate() {
        super.onVideoResolutionRatioUpdate();
        if (mIsLand) {
            shouldUpdateResolution = true;
        } else {
            shouldUpdateResolution = false;
            if (hasShowCurrentMicUserInfo) {
                showCurrentMicUserInfo(mMicNickname);
            }
        }
    }

    /**
     * 视频直播观众侧画面计算连麦者昵称信息展示控件的布局
     *
     * @param nickname 用户昵称
     */
    private void showCurrentMicUserInfo(String nickname) {
        IVideoPlayerComponentInteraction interaction = getComponentInteractionSafety(IVideoPlayerComponentInteraction.class);
        if (!canUpdateUi() || interaction == null) {
            return;
        }
        mMicNickname = nickname;
        hasShowCurrentMicUserInfo = true;

        // 适配横竖屏视频直播
        if (VideoPlayerBlock.isLandUIMode(interaction.getVideoSizeRatio())) {
            mMicUserTv.setVisibility(View.GONE);
        } else {
            float videoWidth = VideoMixConstants.MIX_STREAM_WIDTH;
            float videoHeight = VideoMixConstants.MIX_STREAM_HEIGHT;
            float micVideoSize = VideoMixConstants.MIC_VIEW_SIZE;
            float micVideoMarginBottom = VideoMixConstants.MIC_VIEW_MARGIN_BOTTOM;
            float micVideoMarginRight = VideoMixConstants.MIC_VIEW_MARGIN_RIGHT;
            float screenWidth = BaseUtil.getScreenWidth(getContext());
            float screenHeight = BaseUtil.getHasVirtualNavBarScreenHeight(getContext());

            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mMicUserTv.getLayoutParams();
            mMicUserTv.setText(nickname);
            if (screenWidth * videoHeight < videoWidth * screenHeight) {// 宽被裁剪
                params.width = params.height = (int) (micVideoSize / videoHeight * screenHeight);
                params.bottomMargin = (int) (screenHeight * micVideoMarginBottom / videoHeight) - BaseUtil.dp2px(getContext(), mIsShowHotWord ? 95f : 55f);
                params.rightMargin = (int) (screenHeight * micVideoMarginRight / videoHeight);

            } else { // 高被裁剪
                params.width = params.height = (int) (micVideoSize / videoWidth * screenWidth);
                params.rightMargin = (int) (screenWidth * micVideoMarginRight / videoWidth);
                float videoRealHeight = videoWidth * screenHeight / screenWidth; // 实际展示的视频高度
                params.bottomMargin = (int) (((micVideoMarginBottom - (videoHeight - videoRealHeight) / 2) * screenHeight) / videoRealHeight) - BaseUtil.dp2px(getContext(), mIsShowHotWord ? 95f : 55f);
            }
            mMicUserTv.setLayoutParams(params);
            mMicUserTv.setVisibility(View.VISIBLE);
        }
    }


    /**
     * 根据连麦者人数进行页面的更新
     *
     * @param hasOnlines
     */
    @SuppressLint("NewApi")
    private void updateUiByOnlineUsersCount(boolean hasOnlines) {
        View view = null;
        if (mVideoPlayerView != null && mVideoPlayerView.getPlayerView() != null) {
            view = mVideoPlayerView.getPlayerView().getRenderView();
        }
        if (view != null && !isMoving && canUpdateUi()) {
            int distance = (view.getMeasuredWidth() - BaseUtil.getScreenWidth(getContext())) / 2;
            if (distance <= 0) {
                return;
            }
            mMoveAnimator = null;
            if (hasOnlines) {
                if (view.getTranslationX() == -distance) {
                    return;
                }
                mMoveAnimator = ObjectAnimator.ofFloat(view, "translationX", 0, -distance).setDuration(1200);
            } else {
                if (view.getTranslationX() == 0) {
                    return;
                }
                mMoveAnimator = ObjectAnimator.ofFloat(view, "translationX", -distance, 0).setDuration(1200);
            }

            mMoveAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    isMoving = true;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    isMoving = false;
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                }

                @Override
                public void onAnimationRepeat(Animator animation) {
                }
            });
            mMoveAnimator.start();
        }
    }


    /**
     * 观众连麦状态变化
     *
     * @param syncResult 观众连麦状态
     */
    private void handleAudienceMicStatusChange(final UserStatusSyncResult syncResult) {
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction == null) {
            return;
        }

        int micType = -1;

        if (syncResult.userMicType == UserMicType.USER_MIC_TYPE_AUDIO) {
            micType = MicConstants.TYPE_AUDIO_MIC;
        } else if (syncResult.userMicType == UserMicType.USER_MIC_TYPE_VIDEO) {
            micType = MicConstants.TYPE_VIDEO_MIC;
        } else {
            return;
        }

        interaction.onAudienceGetHostPlayChange(syncResult.userStatus != UserStatus.USER_STATUS_MICING);

        if (syncResult.userStatus == UserStatus.USER_STATUS_MICING) {
            initPlayerUi();
        } else {
            if (canUpdateUi() && mMicHostCameraPlayer != null) {
                interaction.onAudienceGetHostPlayChange(true);
                mMicHostCameraPlayer.setVisibility(View.GONE);
                mMicHostDeskPlayer.setVisibility(View.GONE);
            }
            hasDesktopStream = false;
        }

        checkAndInitMicUserDialog();
        mMicUserDialog.updateMuteStatus(syncResult.muteType);


        if (isOpenMic && mUserMicStatus != syncResult.userStatus) {

            mUserMicStatus = syncResult.userStatus;

            if (syncResult.userStatus == UserStatus.USER_STATUS_WAITING) {
                //申请连麦中
                onMicStateChanged(micType, MicConstants.STATE_REQUESTING);

            } else if (syncResult.userStatus == UserStatus.USER_STATUS_MICING) {

                if (canUpdateUi() && mMicHostCameraPlayer != null) {
                    interaction.onAudienceGetHostPlayChange(false);
                }
                initPlayerUi();

                //开始连麦推流 推流操作在sdk内部完成
                //连麦接通
                onMicStateChanged(micType, MicConstants.STATE_CONNECTED);
                if (LiveAccessibilityUtil.isTalkbackMode()) {
                    CustomToast.showToast("连线成功，你已上麦");
                }
            } else if (syncResult.userStatus == UserStatus.USER_STATUS_OFFLINE) {
                //连麦结束
                onMicStateChanged(micType, MicConstants.STATE_IDLE);
                if (!(mMicStatus.isOpen && mMicStatus.isAllowJoin)) {
                    //如果只是通话挂断了，要判断一下，
                    //如果不满足上面的条件，观众端房间连麦开关应该隐藏
                    interaction.onHostCloseMic(false, true);
                }
                if (mUserMicStatus == UserStatus.USER_STATUS_WAITING) {
                    CustomToast.showToast("已成功取消连线申请");
                } else if (LiveAccessibilityUtil.isTalkbackMode()) {
                    CustomToast.showToast("连线断开，你已下麦");
                }

                if (mMicUserDialog != null) {
                    mMicUserDialog.dismiss();
                }
            }
        }

    }

    private void checkAndInitMicUserDialog() {
        if (mMicUserDialog == null) {
            mMicUserDialog = new MicUsersDialog();
            mMicUserDialog.setMTrackListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    new XMTraceApi.Trace()
                            .setMetaId(33400)
                            .setServiceId("dialogClick")
                            .put("item", "结束连麦")
                            .put(LiveRecordInfoManager.getInstance().getBaseProps())
                            .createTrace();
                }
            });
            mMicUserDialog.setMTrackMuteListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    new XMTraceApi.Trace()
                            .setMetaId(33400)
                            .setServiceId("dialogClick")
                            .put("item", "关闭声音")
                            .put(LiveRecordInfoManager.getInstance().getBaseProps())
                            .createTrace();
                }
            });
            mMicUserDialog.setOnAvatarClickListener(new MicUsersDialog.OnFunctionClickListener() {
                @Override
                public void muteSelf(boolean mute, @NonNull ISendCallback callback) {
                    mRoomMicService.muteSelf(mute, callback);
                }

                @Override
                public void quitJoinAnchor(@NonNull ISendCallback callback) {
                    mRoomMicService.quitJoinAnchor(callback);
                    if (liveAsrManager == null) {
                        liveAsrManager = LiveAsrManager.Companion.getInstance(LiveAsrFactory.ASR_TYPE_VOLCANO, MainApplication.getMyApplicationContext());
                    }
                    liveAsrManager.stop();
                }

                @Override
                public void onJoinMic() {
                    showSelectMicTypeDialog();
                }

                @Override
                public void onAvatarClick(long uid) {
                    boolean needAnonymity = false;
                    AudienceComponentInteraction compInteraction = getComponentInteractionSafety(AudienceComponentInteraction.class);
                    IAudienceMicInteraction micInteraction = getComponentInteractionSafety(IAudienceMicInteraction.class);
                    if (compInteraction != null) {
                        needAnonymity = compInteraction.isNeedAnonymity();
                    }
                    if (needAnonymity && UserInfoMannage.getUid() != uid && getHostUid() != uid) {
                        ToastManager.showToast("因主播设置不支持查看他人资料");
                    } else if (micInteraction != null) {
                        micInteraction.showUserInfoPop(uid);
                    }

                    new XMTraceApi.Trace()
                            .setMetaId(33399)
                            .setServiceId("dialogClick")
                            .put(LiveRecordInfoManager.getInstance().getBaseProps())
                            .createTrace();
                }
            });
        }
    }

    private LiveAsrManager liveAsrManager;

    /**
     * 同步状态到容器，更新底部控件的状态
     *
     * @param state 连麦状态
     */
    protected void onMicStateChanged(int micType, int state) {
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.onMicStatusChanged(micType, state);
        }
        // 连麦挂断了
        if (state == MicConstants.STATE_IDLE) {
            if (liveAsrManager == null) {
                liveAsrManager = LiveAsrManager.Companion.getInstance(
                        LiveAsrFactory.ASR_TYPE_VOLCANO,
                        MainApplication.getMyApplicationContext()
                );
            }
            liveAsrManager.setRoomId(getRoomId());
            liveAsrManager.stop();
        } else if (state == MicConstants.STATE_CONNECTED) {
            //连麦接通
            if (liveAsrManager == null) {
                liveAsrManager = LiveAsrManager.Companion.getInstance(
                        LiveAsrFactory.ASR_TYPE_VOLCANO,
                        MainApplication.getMyApplicationContext()
                );
            }
            liveAsrManager.setRoomId(getRoomId());
            liveAsrManager.start();
        }
    }

    @Override
    public void onSwitchRoom(long newRoomId, @Nullable Bundle newArgs) {
        super.onSwitchRoom(newRoomId, newArgs);
        View view = null;
        if (mVideoPlayerView != null && mVideoPlayerView.getPlayerView() != null) {
            view = mVideoPlayerView.getPlayerView().getRenderView();
        }
        if (isMoving && mMoveAnimator != null) {
            mMoveAnimator.cancel();
        }
        if (view != null) {
            view.setTranslationX(0);
        }
        //切换直播间，由于长连接断掉，此时需要手动关闭连麦
        MicStatus status = new MicStatus();
        status.isOpen = false;
        mRoomMicListener.onMicStatusChanged(status);
        mMicAnchorCount = 1;
        needUpdateMicPkAnchorInfo = false;
        mIsOfficialEnd = false;
        mOnlineAnchors.clear();
        mMicPkStatus = 0;
        mMicPkPanelScoreInfo = null;
        releasePkMicUI();
        hasAVHostMic = false;
        releaseMicUI();
        HandlerManager.removeCallbacks(mCheckAnchorTask);
        isAnchorFollowDialogShowed = false;
        mMicPkAnchors = null;
        LiveHelper.Log.i("duruochen--micpk", "switchroom");
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mMicUserDialog != null) {
            mMicUserDialog.dismiss();
        }
    }

    @Override
    public void rejectLine() {
        if (mRoomMicService != null) {
            mRoomMicService.sendResponseInviteMessage(false);
        }
        Map<String, String> props = new HashMap<>();
        props.put("uid", UserInfoMannage.getUid() + "");
        new XMTraceApi.Trace()
                .click(41434)
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getCustomProps(props))
                .put("Item", "暂不接受")
                .createTrace();
    }

    @Override
    public void acceptLine() {
        if (mRoomMicService != null) {
            mRoomMicService.sendResponseInviteMessage(true);
        }

        Map<String, String> props = new HashMap<>();
        props.put("uid", UserInfoMannage.getUid() + "");
        new XMTraceApi.Trace()
                .click(41434)
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getCustomProps(props))
                .put("Item", "接受")
                .createTrace();
    }

    @Override
    public void onWantDirectRefuse(boolean isRefuse) {
        if (mRoomMicService != null) {
            mRoomMicService.setRejectInviteMicLimit(isRefuse, new ISendCallback() {
                @Override
                public void onSendSuccess() {
                    LamiaHelper.Log.i("邀请连麦：", "onWantDirectRefuse-onSendSuccess");
                }

                @Override
                public void onSendError(int code, String message) {
                    ToastManager.showFailToast(message);
                    LamiaHelper.Log.i("邀请连麦：", "onWantDirectRefuse-onSendError " + code);
                }
            });
        }
    }

    @Override
    public void receiveMediaSideInfoJson(String str, int timestamp) {
        try {
            JSONObject jsonObject = new JSONObject(str);
            if (jsonObject.optInt("type") == LiveMediaSideInfo.TYPE_VOLUME) {
                onRecMediaSideInfo(GsonUtils.parseJson(str, LiveMediaSideInfo.class));
            }
        } catch (Exception e) {
            /* OBS推流的话，自身会携带SEI，导致无法解析成json （e:x264 - core 164 r3101 b093bbe - H.264/MPEG-4 AVC codec
             - Copyleft 2003-2022 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0
             analyse=0x3:0x113 me=hex subme=2 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1
             trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=12 lookahead_threads=4
             sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2
             b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=40 keyint_min=4 scenecut=40
             intra_refresh=0 rc_lookahead=10 rc=cbr mbtree=1 bitrate=2000 ratetol=1.0 qcomp=0.60 qpmin=0 qpmax=69
             qpstep=4 vbv_maxrate=2000 vbv_bufsize=2000 nal_hrd=none filler=1 ip_ratio=1.40 aq=1:1.00） */

            Logger.e("SEI", "receive other sei:" + str);
            Logger.e("SEI", e.getMessage());
        }
    }

    @Override
    public void onMediaSideInfo(@Nullable PkAndMicInfo pkAndMicInfo) {
        super.onMediaSideInfo(pkAndMicInfo);
        anchorsMic(pkAndMicInfo);
    }

    @Override
    public void onRecMediaSideInfo(@Nullable LiveMediaSideInfo mediaSideInfo) {
        super.onRecMediaSideInfo(mediaSideInfo);
        //统一处理mediaInfo
        if (mediaSideInfo != null) {
            final LiveMediaSideInfo.MediaSideInfoContent content = mediaSideInfo.getContent();
            if (content == null) {
                return;
            }
            if (Looper.getMainLooper() == Looper.myLooper()) {
                disposeUpdateMicSpeakVolume(content);
            } else {
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        disposeUpdateMicSpeakVolume(content);
                    }
                });
            }
        }
    }

    private void disposeUpdateMicSpeakVolume(LiveMediaSideInfo.MediaSideInfoContent content) {
        if (!canUpdateUi()) {
            return;
        }

        // 将音量信息传递给直播间主播-用户连麦弹窗
        if (mMicUserDialog != null) {
            mMicUserDialog.updateMicUserSpeakVolume(content.uid, content.volume);
        }
        // 将音量信息传递给互动玩法区域
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.updateMicUserSpeakVolume(content.uid, content.volume);
        }

    }


    @Override
    public void onOrientationChange(int orientation, boolean isSameOrientation) {
        super.onOrientationChange(orientation, isSameOrientation);

        if (!canUpdateUi()) {
            return;
        }

        mIsLand = orientation == Configuration.ORIENTATION_LANDSCAPE;
        if (!mIsLand && shouldUpdateResolution) {
            onVideoResolutionRatioUpdate();
        }

        if (mIsLand) {
            if (mSelectMicTypeDialog != null) {
                mSelectMicTypeDialog.dismiss();
            }
            if (mCancelMicRequestDialog != null) {
                mCancelMicRequestDialog.dismiss();
            }
            if (mMicUserDialog != null) {
                mMicUserDialog.dismiss();
            }
            if (micInviteDialog != null) {
                micInviteDialog.dismiss();
            }
        }
    }

    @Override
    public void receivePkAndMicSEI(PkAndMicInfo pkSideInfo) {
        if (pkSideInfo == null || pkSideInfo.getType() == null || pkSideInfo.getType() != 4) {
            return;
        }
        anchorsMic(pkSideInfo);
    }

    private void anchorsMic(PkAndMicInfo pkSideInfo) {
        if (pkSideInfo == null
                || pkSideInfo.getContent() == null
                || pkSideInfo.getContent().getAnchorMic() == null
                || pkSideInfo.getContent().getAnchorMic().getAnchors() == null
                || pkSideInfo.getContent().getAnchorMic().getAnchors().isEmpty()) {
            updateAnchorMicEndUI(false);

        } else {
            if (mIsOfficialEnd) {
                return;
            }


            //正在进行主播间连麦
            List<Anchor> newAnchor = pkSideInfo.getContent().getAnchorMic().getAnchors();
            if (mMicPkAnchors != null && !mMicPkAnchors.isEmpty()) {
                for (int i = 0; i < newAnchor.size(); i++) {
                    for (int j = 0; j < mMicPkAnchors.size(); j++) {
                        if (Objects.equals(newAnchor.get(i).getUid(), mMicPkAnchors.get(j).getUid())) {
                            newAnchor.get(i).setName(mMicPkAnchors.get(j).getName());
                        }
                    }
                }
            }
            mMicPkAnchors = newAnchor;
            LiveLogUtil.log("多人连麦弹窗", "anchorsMic:" + mMicPkAnchors);

            IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
            if (mMicAnchorCount != mMicPkAnchors.size() + 1) {
                mMicAnchorCount = mMicPkAnchors.size() + 1;
                if (interaction != null) {
                    interaction.onGroupMicStatusChanged(mMicAnchorCount);
                }
            }
            if (!TextUtils.isEmpty(mMicPkAnchors.toString()) && !mMicPkAnchors.toString().equals(mMicingAnchorsMediaSideInfo)) {
                LiveLogUtil.log("duruochen--micpk", "receiveMediaSideInfoJson:" + pkSideInfo);
                needUpdateMicPkAnchorInfo = false;
                mMicingAnchorsMediaSideInfo = mMicPkAnchors.toString();
                updateMicPkAnchorInfo();
            }
            boolean visiable = false;
            if (interaction != null) {
                visiable = interaction.liveAnchorMicInfoViewIsVisible();
            }
            if (!visiable) {
                LiveHelper.Log.i("duruochen--micpk", "时序出问题了，被迫设为visible");
                needUpdateMicPkAnchorInfo = true;
                updateMicPkAnchorInfo();
            }
            if (pkSideInfo.getContent().getAnchorMic().getPkStatus() == null) {
                mMicPkStatus = 0;
            } else if (mMicPkStatus != pkSideInfo.getContent().getAnchorMic().getPkStatus()) {
                mMicPkStatus = pkSideInfo.getContent().getAnchorMic().getPkStatus();
            }
            if (!hasAVHostMic && getHostData() != null) {
                if (interaction != null) {
                    interaction.onAVChange(false);
                }
                hasAVHostMic = true;
            }
            if (hasAVHostMic && !ViewStatusUtil.isViewVisible(mVideoPlayerView)) {
                ViewStatusUtil.setVisible(View.VISIBLE, mVideoPlayerView);
            }
            if (interaction != null) {
                interaction.anchorMicViewVisible(true);
            }
        }

    }

    @Override
    public void setOfficialRoomFlag(boolean officialRoomFlag) {
        super.setOfficialRoomFlag(officialRoomFlag);
        if (officialRoomFlag) {
            mIsOfficialEnd = false;
        }
    }

    @Override
    public void updateAnchorMicEndUI(boolean isOfficialEnd) {
        //没有进行主播间连麦
        if (mMicAnchorCount != 1) {
            mIsOfficialEnd = isOfficialEnd;
            mMicingAnchorsMediaSideInfo = "";
            mMicAnchorCount = 1;
            IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
            if (interaction != null) {
                interaction.releaseMicUIAndHideView();
                interaction.onGroupMicStatusChanged(1);
                interaction.onAVChange(true);
            }
            hasAVHostMic = false;
            mMicPkAnchors = null;
            releaseMicUI();
        }
    }

    @Override
    public void setIsShowHotWord(boolean isShow) {
        if (isShow == mIsShowHotWord) {
            return;
        }
        mIsShowHotWord = isShow;
        if (mMicUserTv != null && mMicUserTv.getVisibility() == View.VISIBLE) {
            float videoWidth = VideoMixConstants.MIX_STREAM_WIDTH;
            float videoHeight = VideoMixConstants.MIX_STREAM_HEIGHT;
            float micVideoMarginBottom = VideoMixConstants.MIC_VIEW_MARGIN_BOTTOM;
            float screenWidth = BaseUtil.getScreenWidth(getContext());
            float screenHeight = BaseUtil.getHasVirtualNavBarScreenHeight(getContext());
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mMicUserTv.getLayoutParams();
            if (screenWidth * videoHeight < videoWidth * screenHeight) {// 宽被裁剪
                params.bottomMargin = (int) (screenHeight * micVideoMarginBottom / videoHeight) - BaseUtil.dp2px(getContext(), mIsShowHotWord ? 95f : 55f);
            } else { // 高被裁剪
                float videoRealHeight = videoWidth * screenHeight / screenWidth; // 实际展示的视频高度
                params.bottomMargin = (int) (((micVideoMarginBottom - (videoHeight - videoRealHeight) / 2) * screenHeight) / videoRealHeight) - BaseUtil.dp2px(getContext(), mIsShowHotWord ? 95f : 55f);
            }
        }
    }

    private void updateMicPkAnchorInfo() {
        if (mMicPkAnchors == null || mMicPkAnchors.isEmpty()) {
            return;
        }
        LiveHelper.Log.i("duruochen--micpk", "updateMicPkAnchorInfo");
        for (Anchor anchor : mMicPkAnchors) {
            if (anchor.getUid() != null && mOnlineAnchors != null) {
                GroupOnlineUser user = mOnlineAnchors.get(anchor.getUid());
                if (user == null) {
                    LiveHelper.Log.i("duruochen--micpk", "等信令来了更新主播信息");
                    needUpdateMicPkAnchorInfo = true;
                } else {
                    anchor.setName(user.nickname);
                    anchor.setRoomId(user.roomId);
                    anchor.setUserMicType(user.userMicType);
                }
            }
        }
        IAudienceMicInteraction interaction = getComponentInteractionSafety(IAudienceMicInteraction.class);
        if (interaction != null) {
            interaction.showPkMicUI(mMicPkAnchors, getHostUid(), getLiveMediaType());
        }

        if (mMicPkPanelScoreInfo != null) {
            // 更新分数信息
            List<CommonPkAnchorInfo> anchorInfos = new ArrayList<>();
            for (CommonMicPkScores score : mMicPkPanelScoreInfo.getMicPkScores()) {
                CommonPkAnchorInfo anchorInfo = new CommonPkAnchorInfo();
                anchorInfo.mUid = score.mUid;
                anchorInfo.mScore = score.getScore();
                anchorInfo.rank = score.getRank();
                anchorInfos.add(anchorInfo);
            }
            if (interaction != null) {
                interaction.updateMultiPkScore(anchorInfos);
            }
            mMicPkPanelScoreInfo = null;
        }
    }

    @Override
    public void resetView() {
        super.resetView();

        HandlerManager.removeCallbacks(mDelayLoadPkBuffAndPropTask);
    }
}
