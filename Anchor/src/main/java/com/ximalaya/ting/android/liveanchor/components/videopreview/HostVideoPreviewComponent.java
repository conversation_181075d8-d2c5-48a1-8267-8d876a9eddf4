package com.ximalaya.ting.android.liveanchor.components.videopreview;

import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.live.video_beautify.BeautifySettingConstants;
import com.ximalaya.ting.android.host.live.video_beautify.VideoLiveBeautifyCache;
import com.ximalaya.ting.android.host.live.video_beautify.VideoLiveBeautifySetting;
import com.ximalaya.ting.android.host.model.nvs.MaterialInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType;
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager;
import com.ximalaya.ting.android.live.host.adapter.IOnClickBeautifyItemCallback;
import com.ximalaya.ting.android.live.host.fragment.beautify.VideoHostBeautifyDialogFragment;
import com.ximalaya.ting.android.live.host.fragment.beautify.VideoHostStickerDialogFragment;
import com.ximalaya.ting.android.live.host.manager.beautify.VideoLiveBeautifyToolManager;
import com.ximalaya.ting.android.live.host.utils.LiveHostTraceUtil;
import com.ximalaya.ting.android.live.host.view.FilterTextureView;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveav.lib.impl.zego.constants.XmZegoConstants;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;

import java.util.List;

/**
 * 视频预览区域的组件
 *
 * <AUTHOR>
 */
public class HostVideoPreviewComponent extends LamiaComponent implements IHostVideoPreviewComponent {

    /**
     * 视频直播预览区域
     */
    private FilterTextureView mVideoPreviewPlayView;

    /**
     * 美颜设置弹窗
     */
    private VideoHostBeautifyDialogFragment mBeautifyDialogFragment;

    /**
     * 道具设置弹窗
     */
    private VideoHostStickerDialogFragment mVideoPropsSettingFragment;


    @Override
    protected View createComponentView(@NonNull LayoutInflater inflater, @Nullable AttributeSet attrs, @Nullable ViewGroup container) {
        mVideoPreviewPlayView = new FilterTextureView(getContext());
        return mVideoPreviewPlayView;
    }

    private void inflateVideoPreviewViewStubIfNeeded() {
        runAfterViewInflate();
        LiveViewPositionManager.getInstance().registerLayoutChangeListener(LiveViewPositionType.TYPE_VIDEO_PLAYER, mVideoPreviewPlayView);
        List<MaterialInfo> allFilterList = VideoLiveBeautifyCache.getInstance().getAllFilterList();
        MaterialInfo currentFilter = VideoLiveBeautifyCache.getInstance().getCurrentFilter();
        if (allFilterList != null && !allFilterList.isEmpty()) {
            mVideoPreviewPlayView.setFilters(allFilterList);

            if (currentFilter == null || currentFilter.id == BeautifySettingConstants.ID_NO_FILTE) {
                mVideoPreviewPlayView.setCurrentSelectedFilter(allFilterList.get(0), false);
            } else {
                mVideoPreviewPlayView.setCurrentSelectedFilter(currentFilter, false);
            }

            mVideoPreviewPlayView.setFilterSelectedCallback(filter -> VideoLiveBeautifyToolManager.getInstance().setEffectFilter(filter));
        }
    }

    @Override
    public TextureView getHostPreviewView() {
        runAfterViewInflate();
        return mVideoPreviewPlayView;
    }


    /**
     * 开启预览
     */
    @Override
    public void startVideoPreview() {
        inflateVideoPreviewViewStubIfNeeded();

        if (mVideoPreviewPlayView == null || RoomModeManager.isPkMicMode() || RoomModeManager.isGroupMicMode()) {
            return;
        }

        mVideoPreviewPlayView.setVisibility(View.VISIBLE);

        IXmMicService avService = getAvService();
        if (avService == null) {
            return;
        }
        //加载历史保存的美颜设置
        VideoLiveBeautifySetting beautifySetting = VideoLiveBeautifyCache.getInstance().getBeautifySetting();
        if (beautifySetting != null) {
            avService.enableCameraFront(beautifySetting.isCameraFront);
            avService.enableCamera(true);
        }
        avService.startLocalPreview(mVideoPreviewPlayView);
        if (beautifySetting != null) {
            //只有前置摄像头可以设置镜像，后置默认非镜像
            setVideoMirror(beautifySetting.isCameraFront && beautifySetting.isCameraMirror);
        }

        VideoLiveBeautifyToolManager.getInstance().recoverBeautifyByCache();
    }

    @Override
    public void hideVideoPreview() {
        if (mVideoPreviewPlayView == null) {
            return;
        }

        IXmMicService avService = getAvService();

        if (avService != null) {
            avService.stopLocalPreview();
        }

        mVideoPreviewPlayView.setVisibility(View.GONE);
    }

    /**
     * 切换用户的镜像
     */
    private void setVideoMirror(boolean isMirrorOpen) {

        IXmMicService avService = getAvService();

        if (avService == null) {
            return;
        }

        avService.enablePreviewMirror(isMirrorOpen);

        //设置预览同时需要设置镜像推流模式，保持观众和主播一样的体验
        if (avService.getPreviewMirrorEnabled()) {
            avService.setVideoMirrorMode(XmZegoConstants.VideoMirrorMode.VIDEO_MIRROR_MODE_PREVIEW_PUBLISH_BOTH_MIRROR);
        } else {
            avService.setVideoMirrorMode(XmZegoConstants.VideoMirrorMode.VIDEO_MIRROR_MODE_PREVIEW_PUBLISH_BOTH_NO_MIRROR);
        }
    }


    /**
     * 选择美颜效果按钮按钮
     */
    @Override
    public void openBeautifySetting() {
        if (mBeautifyDialogFragment == null) {
            mBeautifyDialogFragment = VideoHostBeautifyDialogFragment.newInstance(getContext());
            mBeautifyDialogFragment.setOnClickBeautifyItemCallback(new IOnClickBeautifyItemCallback() {
                @Override
                public void onClickSettingItem(int buzType, MaterialInfo info) {
                    if (buzType == IOnClickBeautifyItemCallback.BUZ_FILTER) {
                        syncFilterToFilterTexture(info);
                    }
                }

                @Override
                public void onSwitchChanged(int buzType, boolean isOpen) {

                }
            });
        }
        mBeautifyDialogFragment.setPageSource(VideoHostBeautifyDialogFragment.IPageSource.LIVE_ANCHOR_FRAGMENT);
        mBeautifyDialogFragment.show(getChildFragmentManager(), "beautify");
    }

    private void syncFilterToFilterTexture(MaterialInfo info) {
        if (canUpdateUi() && mVideoPreviewPlayView != null) {

            if (info == null) {
                List<MaterialInfo> allFilterList = VideoLiveBeautifyCache.getInstance().getAllFilterList();
                if (allFilterList != null && !allFilterList.isEmpty()) {
                    mVideoPreviewPlayView.setCurrentSelectedFilter(allFilterList.get(0), true);
                }

            } else {
                mVideoPreviewPlayView.setCurrentSelectedFilter(info, true);
            }

        }
    }


    /**
     * 打开直播道具设置页面
     */
    @Override
    public void openVideoLivePropsPage() {

        if (mVideoPropsSettingFragment == null) {
            mVideoPropsSettingFragment = VideoHostStickerDialogFragment.newInstance(getContext());
            mVideoPropsSettingFragment.setPageSource(VideoHostBeautifyDialogFragment.IPageSource.LIVE_ANCHOR_FRAGMENT);
            mVideoPropsSettingFragment.show(getChildFragmentManager(), "beautify_sticker");
        } else {
            mVideoPropsSettingFragment.show(getChildFragmentManager(), "beautify_sticker");
        }

        LiveHostTraceUtil.makeDialogShowTrack(33573);
    }

    /**
     * 摄像头翻转
     */
    @Override
    public void switchCameraFrontFront() {

        IXmMicService avService = getAvService();

        if (avService == null) {
            return;
        }

        boolean enabled = avService.getCameraFrontEnabled();
        avService.enableCameraFront(!enabled);

        VideoLiveBeautifyCache.getInstance().setIsCameraFrontOrNot(avService.getCameraFrontEnabled());


        //摄像头翻转的同时，需要默认设置镜像
        //前置-开启镜像；后置-关闭镜像
        setVideoMirror(avService.getCameraFrontEnabled());
    }

    private IXmMicService getAvService() {
        try {
            return getComponentInteraction(IHostVideoPreviewInteraction.class).getAvService();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 镜像翻转
     */
    @Override
    public void switchMirrorFront() {

        IXmMicService avService = getAvService();

        if (avService == null) {
            return;
        }

        if (!avService.getCameraFrontEnabled()) {
            //后置摄像头不能开启镜像
            return;
        }

        boolean enabled = avService.getPreviewMirrorEnabled();
        setVideoMirror(avService, !enabled);

        VideoLiveBeautifyCache.getInstance().setIsCameraMirrorOrNot(avService.getPreviewMirrorEnabled());

    }


    /**
     * 切换用户的镜像
     */
    private void setVideoMirror(IXmMicService avService, boolean isMirrorOpen) {
        if (avService == null) {
            return;
        }

        avService.enablePreviewMirror(isMirrorOpen);

        //设置预览同时需要设置镜像推流模式，保持观众和主播一样的体验
        if (avService.getPreviewMirrorEnabled()) {
            avService.setVideoMirrorMode(XmZegoConstants.VideoMirrorMode.VIDEO_MIRROR_MODE_PREVIEW_PUBLISH_BOTH_MIRROR);
        } else {
            avService.setVideoMirrorMode(XmZegoConstants.VideoMirrorMode.VIDEO_MIRROR_MODE_PREVIEW_PUBLISH_BOTH_NO_MIRROR);
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        LiveViewPositionManager.getInstance().unregisterLayoutChangeListener(LiveViewPositionType.TYPE_VIDEO_PLAYER, mVideoPreviewPlayView);
    }
}
