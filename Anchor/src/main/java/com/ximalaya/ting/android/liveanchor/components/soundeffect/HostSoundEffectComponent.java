package com.ximalaya.ting.android.liveanchor.components.soundeffect;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.liveanchor.LiveAnchorSoundEffectDialogFragment;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.lang.ref.SoftReference;

/**
 * 音效组件
 *
 * <AUTHOR>
 */
public class HostSoundEffectComponent extends LamiaComponent implements IHostSoundEffectComponent {

    private static final String TAG = "HostSoundEffectComponent";

    private SoftReference<LiveAnchorSoundEffectDialogFragment> mSoundEffectDialogFragmentRef;

    @Override
    public void show(FragmentManager fragmentManager) {
        Router.getActionByCallback(Configure.BUNDLE_MUSIC, new Router.BundleInstallCallbackWrapper() {

            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (!canUpdateUi()) {
                    return;
                }

                try {
                    // 直播 跳转配乐页面
                    showInternal(fragmentManager);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, true, BundleModel.DOWNLOAD_ASK_USER);
    }

    private void showInternal(FragmentManager fragmentManager) {
        if (fragmentManager == null) {
            return;
        }
        FragmentTransaction mFragTransaction = fragmentManager.beginTransaction();
        LiveAnchorSoundEffectDialogFragment soundEffectDialog =
                (LiveAnchorSoundEffectDialogFragment) fragmentManager.findFragmentByTag(TAG);
        if (soundEffectDialog != null) {
            // 防止重复显示对话框，移除正在显示的对话框
            mFragTransaction.remove(soundEffectDialog);
        }

        soundEffectDialog = new LiveAnchorSoundEffectDialogFragment();
        mSoundEffectDialogFragmentRef = new SoftReference<>(soundEffectDialog);
        soundEffectDialog.show(mFragTransaction, TAG);

        // 直播间-音效功能弹框  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(33577)
                .setServiceId("dialogView")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    @Override
    public void dismiss() {
        if (mSoundEffectDialogFragmentRef != null && mSoundEffectDialogFragmentRef.get() != null) {
            LiveAnchorSoundEffectDialogFragment dialog = mSoundEffectDialogFragmentRef.get();
            dialog.dismiss();
            mSoundEffectDialogFragmentRef = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        dismiss();
    }

    @Override
    public void resetView() {
        super.resetView();
        dismiss();
    }
}
