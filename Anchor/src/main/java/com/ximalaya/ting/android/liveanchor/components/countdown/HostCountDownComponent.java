package com.ximalaya.ting.android.liveanchor.components.countdown;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.host.utils.checkwindow.LiveCommonPopQueueManager;
import com.ximalaya.ting.android.liveanchor.R;
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponent;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.Locale;

/**
 * 主播端倒计时动画组件
 *
 * @email <EMAIL>
 * @phone 15026804470
 */
public class HostCountDownComponent extends LamiaComponent implements IHostCountDownComponent {

    private static final int NUM_COUNTDOWN = 3;
    private boolean countDownFinish;

    protected ViewGroup countDownAniRl;
    protected TextView countdownNumTv;

    @Override
    protected void initComponentViewAfterInflated(@NonNull View view) {
        super.initComponentViewAfterInflated(view);
        countDownAniRl = (ViewGroup) findViewById(getViewStubId());
        countdownNumTv = (TextView) findViewById(R.id.live_countdownNumTv);
        AutoTraceHelper.bindData(countDownAniRl, AutoTraceHelper
                .MODULE_DEFAULT, "");
    }

    @Override
    public void playOneSecondAni() {
        runAfterViewInflate(new Runnable() {
            @Override
            public void run() {
                playOneSecondAni(NUM_COUNTDOWN);
            }
        });
    }

    @Override
    public boolean isCountDownFinish() {
        return countDownFinish;
    }

    /**
     * 显示第num秒的动画，并递归下去
     */
    private void playOneSecondAni(int num) {
        LamiaHelper.Log.i(getClass().getSimpleName() + " playOneSecondAni " + num);
        if (countDownAniRl == null) return;
        final AnimatorSet animatorSet = new AnimatorSet();
        countdownNumTv.setText(String.format(Locale.getDefault(), "%d", num));
        countDownAniRl.setVisibility(View.VISIBLE);
        final int newNum = num - 1;
        ObjectAnimator alpha = ObjectAnimator.ofFloat(countdownNumTv, AnimationUtil
                        .ANIMATOR_PROPERTY_ALPHA,
                0f, 0.5f, 1f, 1f, 1f, 1f, 1f, 1f, 0.5f, 0f);
        alpha.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                if (!canUpdateUi())
                    return;
                animatorSet.cancel();
                //递归显示现已秒的动画
                if (newNum > 0) {
                    playOneSecondAni(newNum);
                } else {
                    hideCountDownWithAni();
                    countDownFinish = true;
                    //step 推流一成功就开始显示计时（并且直播的状态已是开始状态），提示直播开始
                    if (getComponentInteractionSafety(IHostCountDownInteraction.class) == null) {
                        return;
                    }
                    getComponentInteractionSafety(IHostCountDownInteraction.class).tryToShowLiveStart();
                }
                //step 提前一秒开始推流
                if (newNum == 1) {
                    if (getComponentInteractionSafety(IHostCountDownInteraction.class) == null) {
                        return;
                    }
                    getComponentInteractionSafety(IHostCountDownInteraction.class).tryStartPublish();
                }
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        alpha.setDuration(ParamsConstantsInLive.ONE_SECOND);
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(countdownNumTv, AnimationUtil
                        .ANIMATOR_PROPERTY_SCALE_X,
                1f, 1f, 1f, 1f, 1f, 1f, 1f, 1f, 0.6f, 0.2f);
        scaleX.setDuration(ParamsConstantsInLive.ONE_SECOND);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(countdownNumTv, AnimationUtil
                        .ANIMATOR_PROPERTY_SCALE_Y,
                1f, 1f, 1f, 1f, 1f, 1f, 1f, 1f, 0.6f, 0.2f);
        scaleY.setDuration(ParamsConstantsInLive.ONE_SECOND);
        animatorSet.playTogether(alpha, scaleX, scaleY);
        animatorSet.start();
    }

    private void hideCountDownWithAni() {
        if (countDownAniRl == null) return;
        ObjectAnimator alpha = ObjectAnimator.ofFloat(countDownAniRl, AnimationUtil
                        .ANIMATOR_PROPERTY_ALPHA,
                1f, 0f);
        alpha.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                if (canUpdateUi()) {
                    countDownAniRl.setVisibility(View.GONE);
                    countDownAniRl.clearAnimation();
                    ViewGroup parent = (ViewGroup) countDownAniRl.getParent();
                    if (parent != null) {
                        parent.removeView(countDownAniRl);
                        countDownAniRl = null;
                    }
                    LiveCommonPopQueueManager.getInstance().showPop(() -> showShareLive(), LiveCommonPopQueueManager.HOST_SHARE_DIALOG, true);
                }
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        alpha.setDuration(ParamsConstantsInLive.ONE_SECOND);
        alpha.start();
    }

    private void showShareLive() {
        if (getComponentInteractionSafety(IHostCountDownInteraction.class) == null) {
            return;
        }
        getComponentInteractionSafety(IHostCountDownInteraction.class).shareLive();
    }

    @Override
    public void resetView() {
        super.resetView();
        ViewStatusUtil.setVisible(View.GONE, countDownAniRl);
    }
}
