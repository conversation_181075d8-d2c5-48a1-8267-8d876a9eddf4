package com.ximalaya.ting.android.liveanchor.components.header;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.support.rastermill.FrameSequenceDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.entity.HeadAnchorInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveSimpleData;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereStatus;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGifHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTextUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper;
import com.ximalaya.ting.android.live.common.view.widget.LiveOnlineHeaderView;
import com.ximalaya.ting.android.live.host.view.header.LiveHeaderType;
import com.ximalaya.ting.android.live.host.view.header.LiveRoomStatusView;
import com.ximalaya.ting.android.live.host.view.header.TrafficLiveTimerText;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomLoveValueChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.liveanchor.R;
import com.ximalaya.ting.android.liveaudience.components.base.header.AbsHeaderComponent;
import com.ximalaya.ting.android.liveaudience.data.model.FlowCardInfoModel;
import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.liveaudience.view.LiveHeadlinesView;
import com.ximalaya.ting.android.liveaudience.view.headlines.LiveHeadLinesTrace;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Locale;


/**
 * 主播端头部组件.
 *
 * <AUTHOR>
 */
public class HostHeaderComponent extends AbsHeaderComponent implements IHostHeaderComponent, LiveOnlineHeaderView.IFragmentCallBack {
    private static final String TAG = "HostHeaderComponent";
    private static final String KEY_FAVORITE_VALUE_REMIND_POP = "key_favorite_value_remind_pop";
    /**
     * 直播间开始时间初识值
     */
    private static final String ANCHOR_TIME_INIT_VALUE = "00:00:00";

    /**
     * 贡献榜第一 二 三名
     */
    private RoundImageView firstAvatar, secondAvatar, thirdAvatar;
    protected ImageView menuIv;

    /**
     * 头部第一层 除了关闭之外的部分
     */
    private View mFirstFloorExcludeCloseBtn;
    protected TextView mIncomeTv;

    /**
     * 优惠券
     */
    protected ImageView mCouponView;

    private TextView mLiveRoomFmNumber;
    protected ImageView mIvMicOffTip;

    private View mIncomeLayout;
    private boolean hasCoupons = false;
    //上头条
    private LiveHeadlinesView mTopHeadlinesView;
    private LiveOnlineHeaderView mOnlineHeaderView;

    private TextView mTopicTv;

    protected android.animation.ObjectAnimator mMicFadeInFadeOutAnimator;

    protected final Handler mHandler = HandlerManager.obtainMainHandler();

    protected boolean isTiming;

    protected long offsetSecond;

    private HeadAnchorInfo mCommonChatRoomTopHeadlinesMsg;

    //官方直播间:第二部分
    private ViewStub mOfficialSecondHeaderVs;
    //官方直播间:第二部分，公告，热度，人数
    private ConstraintLayout mOfficialSecondPartLayout;
    //官方直播间:人数
    private TextView mOfficialPeopleNumTv;
    //官方直播间:热度
    private TextView mOfficialHotNumTv;
    private LinearLayout mOfficialLikeNumLl;
    //官方直播间:喜爱值
    private TextView mOfficialLikeNumTv;

    private boolean isFirstResume = true;

    private final Runnable mTimingRunnable = new Runnable() {
        @Override
        public void run() {
            if (!canUpdateUi()) {
                return;
            }

            long betweenTime = System.currentTimeMillis() / ParamsConstantsInLive.ONE_SECOND - offsetSecond;
            if (betweenTime >= 0) {
                mAnchorHeaderView.setAudiNameOrAnchorTime(LiveLamiaUtil.formatTimeHMS(betweenTime));
                if (getHostHeaderInteraction() != null) {
                    getHostHeaderInteraction().onTimePlus(betweenTime);
                    if (getHostHeaderInteraction().isLiveHasStart()) {
                        mHandler.postDelayed(this, ParamsConstantsInLive.ONE_SECOND);
                    }
                }
            } else {
                mAnchorHeaderView.setAudiNameOrAnchorTime(ANCHOR_TIME_INIT_VALUE);
            }
        }
    };

    @Override
    public void startTiming() {
        Logger.i(TAG, "startTiming getLiveRecordInfo():" + getLiveRecordInfo()
                + ", isTiming: " + isTiming);

        if (getLiveRecordInfo() == null) {
            return;
        }
        if (isTiming) {
            return;
        }

        isTiming = true;
        long actualStartAt = getLiveRecordInfo().actualStartAt;
        if (actualStartAt > 0) {
            offsetSecond = actualStartAt / ParamsConstantsInLive.ONE_SECOND;
        } else { //刚直播，可能造成actualStartAt 为0，所以计时就从0开始
            offsetSecond = System.currentTimeMillis() / ParamsConstantsInLive.ONE_SECOND;
        }
        mHandler.postDelayed(mTimingRunnable, ParamsConstantsInLive.ONE_SECOND);

    }

    @Override
    public void startMicFadeInFadeOutAnim() {
        if (mIvMicOffTip == null) {
            return;
        }

        mIvMicOffTip.setVisibility(View.VISIBLE);
        if (mMicFadeInFadeOutAnimator == null) {
            mMicFadeInFadeOutAnimator = android.animation.ObjectAnimator.ofFloat(mIvMicOffTip,
                    "alpha", 0, 1f, 0f);
            mMicFadeInFadeOutAnimator.setDuration(3000);
            mMicFadeInFadeOutAnimator.setRepeatCount(android.animation.ValueAnimator.INFINITE);
            mMicFadeInFadeOutAnimator.setRepeatMode(android.animation.ValueAnimator.REVERSE);
        }
        mMicFadeInFadeOutAnimator.start();
    }

    @Override
    public void stopMicFadeInFadeOutAnim() {
        if (mMicFadeInFadeOutAnimator != null) {
            mMicFadeInFadeOutAnimator.end();
        }
    }

    private BaseFragment2 mAttachFragment;
    private HostHeaderPresenter mPresenter;

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public void initOnlineAndParticipateCount(long onLineCount, long participateCount) {
        //初始化在线人数
        if (mOnlineHeaderView != null) {
            mOnlineHeaderView.setOnlineCount(onLineCount);
            mOnlineHeaderView.setTrackListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    makeClickTrack(33367);
                }
            });
        }
        if (mOfficialPeopleNumTv != null) {
            mOfficialPeopleNumTv.setText(LiveTextUtil.formatCount(onLineCount));
        }
    }

    @Override
    public void onRoomSpecialModeChanged() {
        setShowBirthdayView();
        setShowRewardAvatarDecorate();
    }

    @Override
    public void updateRewardAvatarDecorate() {
        setShowRewardAvatarDecorate();
    }

    @Override
    public void updateDotStatus(boolean stop) {
        int drawable = !stop ? com.ximalaya.ting.android.live.common.R.drawable.live_common_green_dot : com.ximalaya.ting.android.live.host.R.drawable.livehost_red_dot;
        mAnchorHeaderView.setCompoundDrawables(drawable);
    }

    @Override
    public void receiveTopHeadlinesMsg(HeadAnchorInfo msg) {
        if (!canUpdateUi()) {
            return;
        }
        mCommonChatRoomTopHeadlinesMsg = msg;
        boolean isShow = false;
        PersonLiveDetail hostData = getHostData();
        if (hostData != null && hostData.getHeadAnchor() != null) {
            isShow = Boolean.TRUE.equals(hostData.getHeadAnchor().getShowHeadAnchor());
        }
        if (initTopHeadLineViewVisibility()) {
            mTopHeadlinesView.loadData(true, msg, isShow);
        }
    }

    @Override
    public void receiveHostOnlineListMessage(CommonChatRoomOnlineUserListMsg msg) {
        if (!canUpdateUi() || null == getLiveRecordInfo() || msg == null) {
            return;
        }

        if (mOnlineHeaderView != null) {
            mOnlineHeaderView.updateOnlineList(msg);
        }
        //个播主播端，更新在线人数
        if(mAnchorHeaderView != null && mAnchorHeaderView.getVisibility() == VISIBLE){
            mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);
            mAnchorHeaderView.setAnchorPlayCount(TextUtils.isEmpty(getLiveRecordInfo().hotScoreIconPath)
                    ? msg.playCnt
                    : msg.hotScore);
            mAnchorHeaderView.onOnlineStatusChange(msg.popularityCount);
        }
        //如果首映室展示 再更新 在线人数逻辑
        //保险起见 没用 if else
        if(mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == VISIBLE){
            mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);
            mPremiereAnchorHeaderView.setAnchorPlayCount(TextUtils.isEmpty(getLiveRecordInfo().hotScoreIconPath)
                    ? msg.playCnt
                    : msg.hotScore);
            mPremiereAnchorHeaderView.onOnlineStatusChange(msg.popularityCount);
        }

        if (mOfficialHotNumTv != null) {
            int num = TextUtils.isEmpty(getLiveRecordInfo().hotScoreIconPath) ? msg.playCnt : msg.hotScore;
            mOfficialHotNumTv.setText(StringUtil.getFriendlyNumStr(num));
        }
        if (mOfficialPeopleNumTv != null) {
            mOfficialPeopleNumTv.setText(LiveTextUtil.formatCount(msg.onlineCount));
        }
    }

    @Override
    public void setCouponViewVisible(boolean hasCoupons) {
        this.hasCoupons = hasCoupons;
        if (mCouponView != null) {
            LiveGifHelper.fromRawResource(getContext().getResources(), com.ximalaya.ting.android.live.common.R.drawable.live_common_coupon, new LiveGifHelper.LoadCallback() {
                @Override
                public void onLoaded(@NonNull FrameSequenceDrawable drawable) {
                    if (null == mCouponView) {
                        return;
                    }
                    mCouponView.setVisibility(hasCoupons ? View.VISIBLE : View.GONE);
                    mCouponView.setImageDrawable(drawable);
                    drawable.setLoopBehavior(FrameSequenceDrawable.LOOP_FINITE);
                    drawable.setLoopCount(1);
                    drawable.start();
                    if (isOfficialLive()) {
                        mCouponView.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onError(String errorMsg) {
                    CustomToast.showDebugFailToast("优惠券icon弹出动画解析失败");
                    if (null == mCouponView) {
                        return;
                    }
                    mCouponView.setImageResource(R.drawable.live_common_coupon_default);
                }
            });
        }
    }

    @Override
    public void updateLiveNetworkQuality(int rtt, float akbps, int quality) {
        LamiaHelper.Log.i("updateLiveNetworkQuality", quality + "");
        if (quality == 3) {
            mAnchorHeaderView.setCompoundDrawables(com.ximalaya.ting.android.live.host.R.drawable.livehost_red_dot);
        } else if (quality == 2) {
            mAnchorHeaderView.setCompoundDrawables(com.ximalaya.ting.android.live.host.R.drawable.live_common_yellow_dot);
        } else if (quality == 0 || quality == 1) {
            mAnchorHeaderView.setCompoundDrawables(com.ximalaya.ting.android.live.host.R.drawable.live_common_green_dot);
        }
    }

    @Override
    public void onAnchorRankChanged(CommonChatRoomLoveValueChangeMessage info) {
        if (info != null) {
            mIncomeTv.setText(StringUtil.getFriendlyNumStr(info.recordAmount));
            if (mOfficialLikeNumTv != null) {
                mOfficialLikeNumTv.setText(StringUtil.getFriendlyNumStr(info.recordAmount));
            }
        }
    }

    @Override
    public void bindData(@NonNull PersonLiveDetail detail) {
        super.bindData(detail);
        runAfterViewInflate(() -> {
            trackRoom();
            LiveHelper.Log.i("mic-debug --timing: bindData, detail: " + detail);
            initTopHeadLineViewVisibility();
            if (getLiveRecordInfo() != null && getLiveRecordInfo().fmId > 0) {
                mLiveRoomFmNumber.setText(String.format(Locale.getDefault(), "FM %d", getLiveRecordInfo().fmId));
                mLiveRoomFmNumber.setContentDescription(String.format(Locale.getDefault(), "FM %d", getLiveRecordInfo().fmId));
            }
            UIStateUtil.showViewsIfTrue(null != getLiveRecordInfo() && getLiveRecordInfo().fmId > 0, mLiveRoomFmNumber);


            if (detail.getTopMsg() != null && initTopHeadLineViewVisibility()) {
                if (detail.getHeadAnchor() != null) {
                    LamiaHelper.Log.i("上头条测试", "组件bindData-有数据就展示");
                    mTopHeadlinesView.loadData(detail.getHeadAnchor().getHasHeadAnchor(),
                            detail.getHeadAnchor().getHeadAnchorInfo(),
                            Boolean.TRUE.equals(detail.getHeadAnchor().getShowHeadAnchor()));
                    LiveHeadLinesTrace.headLiveShow(mTopHeadlinesView.getTraceStyle());
                } else {
                    if (httpDataIsShowHeadline()) {
                        LamiaHelper.Log.i("上头条测试", "组件bindData-接口让展示");
                        mTopHeadlinesView.setVisibility(VISIBLE);
                    } else {
                        LamiaHelper.Log.i("上头条测试", "组件bindData-接口不让展示");
                        mTopHeadlinesView.setVisibility(GONE);
                    }
                }
            }

            if (getAnchorUserInfo() != null) {
                mAnchorHeaderView.bindAvatarToHeader(getAnchorUserInfo().avatar, getHostUid());
            }

            if(mAnchorHeaderView != null && mAnchorHeaderView.getVisibility() == VISIBLE){
                mAnchorHeaderView.updateTagViewIcon(getLiveRecordInfo().hotScoreIconPath);
                mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);
                mAnchorHeaderView.setAudiNameOrAnchorTime(ANCHOR_TIME_INIT_VALUE);
                mAnchorHeaderView.setAnchorPlayCount(TextUtils.isEmpty(getLiveRecordInfo().hotScoreIconPath) ? getLiveRecordInfo().playCount : getLiveRecordInfo().hotScore);
            }

            if(mPremiereAnchorHeaderView != null && mPremiereAnchorHeaderView.getVisibility() == VISIBLE){
                mPremiereAnchorHeaderView.updateTagViewIcon(getLiveRecordInfo().hotScoreIconPath);
                mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);
                mPremiereAnchorHeaderView.setAudiNameOrAnchorTime(ANCHOR_TIME_INIT_VALUE);
                mPremiereAnchorHeaderView.setAnchorPlayCount(TextUtils.isEmpty(getLiveRecordInfo().hotScoreIconPath) ? getLiveRecordInfo().playCount : getLiveRecordInfo().hotScore);
            }
            if (mOfficialHotNumTv != null) {
                long num = TextUtils.isEmpty(getLiveRecordInfo().hotScoreIconPath) ? getLiveRecordInfo().playCount : getLiveRecordInfo().hotScore;
                mOfficialHotNumTv.setText(StringUtil.getFriendlyNumStr(num));
            }
            setShowBirthdayView();
            setShowRewardAvatarDecorate();
            LiveUserInfo.FansClubVoBean roomFansClubVo = null != getHostData() ? getHostData().roomFansClubVo : null;
            if (roomFansClubVo != null) {
                if (roomFansClubVo.isRedPoint()) {
                    mAnchorHeaderView.getFollowDot().setVisibility(View.VISIBLE);
                } else {
                    mAnchorHeaderView.getFollowDot().setVisibility(View.INVISIBLE);
                }
            }
            //守护曝光
            new XMTraceApi.Trace()
                    .setMetaId(43890)
                    .setServiceId("slipPage")
                    .put("currPage", "liveRoom")
                    .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .createTrace();

            AutoTraceHelper.bindData(menuIv, AutoTraceHelper.MODULE_DEFAULT, null != getHostData() ? getHostData() : "");
            AutoTraceHelper.bindData(mIvClose, AutoTraceHelper.MODULE_DEFAULT, "");
            AutoTraceHelper.bindData(mAnchorHeaderView.getMFollowGuardView(), AutoTraceHelper.MODULE_DEFAULT, null != getAnchorUserInfo() ? getAnchorUserInfo() : "");
            AutoTraceHelper.bindData(mIncomeLayout, AutoTraceHelper.MODULE_DEFAULT, getLiveRecordInfo());
            AutoTraceHelper.bindData(mTopHeadlinesView, AutoTraceHelper.MODULE_DEFAULT, mCommonChatRoomTopHeadlinesMsg);
        });
    }


    private boolean initTopHeadLineViewVisibility() {
        if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
            if (!isOfficialLive()) {
                ViewStatusUtil.setVisible(View.VISIBLE, mTopHeadlinesView);
                return true;
            }
        } else {
            boolean isOpenTops = LiveSettingManager.getTopHeadLineViewSwitch();
            if (!isOfficialLive()) {
                mTopHeadlinesView.setVisibility(isOpenTops ? View.VISIBLE : View.GONE);
                return isOpenTops;
            }
        }
        return false;
    }

    /**
     * 服务端的数据要不要展示上头条的入口
     */
    private boolean httpDataIsShowHeadline() {
        PersonLiveDetail hostData = getHostData();
        boolean show = false;
        if (hostData != null && hostData.getHeadAnchor() != null && Boolean.TRUE.equals(hostData.getHeadAnchor().getHasHeadAnchor())) {
            //只要有人正在上头条，那从数据层面就一定要展示
            return true;
        }
        if (hostData != null && hostData.getHeadAnchor() != null) {
            //没人正在上头条，入口展不展示就看服务端返回的结果
            show = Boolean.TRUE.equals(hostData.getHeadAnchor().getShowHeadAnchor());
        }
        return show;

    }

    protected void initComponentViewAfterInflated(@NonNull View view) {
        runAfterViewInflate(() -> {
            super.initComponentViewAfterInflated(view);
            initView();

            mAttachFragment = (BaseFragment2) getFragment();
            if (null == mAttachFragment) {
                return;
            }
            mPresenter = new HostHeaderPresenter(this, mAttachFragment);
            //个播主播端 关注view初始化
            mAnchorHeaderView = findViewById(R.id.liveanchor_header_owner_icon_layout);
            //个播主播端 头部主播信息点击事件绑定
            mAnchorHeaderView.bindHeaderOnClickListener(this);
            //个播主播端 更新头部主播信息类型
            mAnchorHeaderView.updateContentByHeaderType(LiveHeaderType.HEADER_TYPE_ANCHOR);

            mFirstFloorExcludeCloseBtn = findViewById(R.id.live_anchor_header_layout_exclude_close_btn);

            //个播 首映室主播端 关注view初始化
            mPremiereAnchorHeaderView.bindHeaderOnClickListener(this);
            mPremiereAnchorHeaderView.updateContentByHeaderType(LiveHeaderType.HEADER_TYPE_ANCHOR_PREMIERE);

            mLiveRoomFmNumber = findViewById(R.id.live_room_num);

            //直播连接状态
            mAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);
            mPremiereAnchorHeaderView.setLiveStatus(LiveRoomStatusView.LIVE_PLAYING);

            firstAvatar = findViewById(com.ximalaya.ting.android.live.common.R.id.live_firstAvatar);
            secondAvatar = findViewById(com.ximalaya.ting.android.live.common.R.id.live_secondAvatar);
            thirdAvatar = findViewById(com.ximalaya.ting.android.live.common.R.id.live_thirdAvatar);

            mIvClose.setOnClickListener(this);
            mIncomeTv = findViewById(R.id.live_header_tv_income);
            mCouponView = findViewById(R.id.live_iv_coupon);
            mIncomeLayout = findViewById(R.id.live_header_income);
            mHeaderHostAvatarStokeIv = findViewById(R.id.live_anchor_header_iv_stoke_image);
            mHeaderHostRewardAvatarDecorateIv = findViewById(R.id.live_anchor_header_reward_avatar_decorate);
            mIncomeLayout.setOnClickListener(this);
            mCouponView.setOnClickListener(this);

            //上头条控件
            mTopHeadlinesView = findViewById(R.id.live_top_view);
            mTopHeadlinesView.setOnClickListener(this);

            mOnlineHeaderView = findViewById(R.id.live_view_online_header);
            mOnlineHeaderView.setFragmentCallBack(this);
            mOnlineHeaderView.setLiveType(1);

            UIStateUtil.roundIvNotUseCache(firstAvatar, secondAvatar, thirdAvatar);
            mFirstFloorExcludeCloseBtn.setVisibility(View.VISIBLE);
            // 喜爱值引导弹窗
            showFavoriteValueRemindPopWindow();

            mTopicTv = findViewById(com.ximalaya.ting.android.live.R.id.live_header_tv_topic);
            if (mTopicTv != null) {
                mTopicTv.setOnClickListener(this);
            }
        });
    }

    private void showFavoriteValueRemindPopWindow() {
        runAfterViewInflate(() -> {

            if (MMKVUtil.getInstance().getBoolean(KEY_FAVORITE_VALUE_REMIND_POP)) {
                return;
            }
            MMKVUtil.getInstance().saveBoolean(KEY_FAVORITE_VALUE_REMIND_POP, true);
            LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View contentView = inflater.inflate(R.layout.liveanchor_popwindow_favorite_value_remind, null);
            PopupWindow popupWindow = new PopupWindow(contentView, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            popupWindow.setFocusable(true);
            popupWindow.setOutsideTouchable(true);
            popupWindow.showAsDropDown(mIncomeLayout);
            mHandler.postDelayed(() -> {
                if (popupWindow.isShowing()) {
                    popupWindow.dismiss();
                }
            }, 3000);
        });

    }


    @Override
    public void onClick(@NonNull View v) {
        super.onClick(v);
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int i = v.getId();
        if (getHostHeaderInteraction() != null) {
            getHostHeaderInteraction().hideKeyBoard();
        }
        if (i == R.id.live_btn_close_room) {
            if (getHostHeaderInteraction() != null) {
                getHostHeaderInteraction().onHostHeaderExitClick();
                makeClickTrack(33500);
            }
        } else if (i == R.id.live_header_income || i == R.id.liveanchor_ll_official_like_num) {
            if (getHostHeaderInteraction() != null) {
                makeClickTrack(33368);
                getHostHeaderInteraction().onHostHeaderInComeClick();
            }
        } else if (i == R.id.live_fans_fl) {
            //粉丝团
            if (getHostHeaderInteraction() != null) {
                getHostHeaderInteraction().onHostHeaderFansClubClick();
            }
            handleFansDot();
        } else if (i == R.id.live_top_view) {
            makeClickTrack(33371);
            if (canUpdateUi() && getHostData() != null && getHostHeaderInteraction() != null) {
                Bundle bundle = new Bundle();
                String url = LiveUrlConstants.getInstance().getHeadlinesH5Url(getHostData().getHostUid(), getHostData().getRoomId(), getHostData().getChatId(), getLiveId());
                bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
                // 设置 ui 显示参数
                bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, getDialogHeightByHeadType());
                bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, 0);
                bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, "bottom");
                // 打开弹窗
                getHostHeaderInteraction().showCustomH5Dialog(bundle);

                // 埋点
                LiveHeadLinesTrace.headLiveClick(mTopHeadlinesView.getTraceStyle());
            }
        } else if (i == R.id.live_iv_coupon) {
            //优惠券
            if (getHostHeaderInteraction() != null) {
                getHostHeaderInteraction().onClickCouponView();
                makeClickTrack(33375);
            }
        } else if (i == R.id.live_header_owner_icon) {
            //点击主播头像，只有音频官播间 不能点击头像
            if (isOfficialLive() && getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
                return;
            }
            if (getHostHeaderInteraction() != null) {
                getHostHeaderInteraction().clickHostAvatar();
            }
        } else if (i == R.id.live_container_right_defend_bg) {
            //守护
            if (getHostHeaderInteraction() != null) {
                getHostHeaderInteraction().onHostHeaderGuardClick();
                // 直播间-守护席入口  点击事件
                makeClickTrack(43889);
            }
        } else if (i == R.id.live_header_tv_topic) {
            if (getHostHeaderInteraction() != null) {
                getHostHeaderInteraction().showTopicAndNoticeDialog();
            }
        }
    }

    @Override
    public void updatePremiere() {
        runAfterViewInflate(new Runnable() {
            @Override
            public void run() {
                HostHeaderComponent.super.updatePremiere();

                if (!isPremiereUI()) {
                    mAnchorHeaderView.updateTagViewHeight();
                }

                if (mTopicTv == null) {
                    return;
                }
                liveBizTypeInHeader = PERSONAL_PREMIERE_LIVE_ROOM_TYPE;
                if (PremiereStatus.PREMIERE_NO_START == getPremiereStatus() || PremiereStatus.PREMIERE_PRE == getPremiereStatus()) {
                    ViewStatusUtil.setVisible(VISIBLE, mTopicTv);
                    mTopicTv.setText("首映预告");
                } else if (PremiereStatus.PREMIERE_ING == getPremiereStatus() || PremiereStatus.PREMIERE_END_LIVING == getPremiereStatus()) {
                    ViewStatusUtil.setVisible(VISIBLE, mTopicTv);
                    mTopicTv.setText("首映公告");
                } else {
                    liveBizTypeInHeader = PERSONAL_LIVE_ROOM_TYPE;
                    ViewStatusUtil.setVisible(View.GONE, mTopicTv);
                }
            }
        });
    }

    private void handleFansDot() {
        runAfterViewInflate(() -> {
            if (mAnchorHeaderView.getFollowDot().getVisibility() == View.VISIBLE) {
                mAnchorHeaderView.getFollowDot().setVisibility(View.INVISIBLE);
                CommonRequestForLive.setFansDot(new IDataCallBack<LiveSimpleData>() {
                    @Override
                    public void onSuccess(@Nullable LiveSimpleData data) {

                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }
        });
    }

    /**
     * 抢头条dialog高度根据头条状态设置初始化
     *
     * @return dialog高度
     */
    private int getDialogHeightByHeadType() {
        switch (mTopHeadlinesView.getTraceStyle()) {
            case LiveHeadLinesTrace.HEAD_LINES_STYLE_NO_TITLE:
                if (UserInfoMannage.getUid() == getHostUid()) {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 560 / 812);
                } else {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 604 / 812);
                }
            case LiveHeadLinesTrace.HEAD_LINES_STYLE_HAS_TITLE:
            case LiveHeadLinesTrace.HEAD_LINES_STYLE_DOING_TITLE:
                if (UserInfoMannage.getUid() == getHostUid()) {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 350 / 812);
                } else {
                    return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 400 / 812);
                }
        }
        return BaseUtil.px2dip(getContext(), (float) BaseUtil.getScreenHeight(getContext()) * 560 / 812);
    }

    private void trackRoom() {
        new XMTraceApi.Trace()
                .pageView(33353, "liveRoom")
                .put("currPage", "liveRoom")
                .put("liveId", getLiveId() + "")
                .put("roomId", getRoomId() + "")
                .put("LiveBroadcastState", LiveRecordInfoManager.getInstance().getLiveBroadcastState() + "")
                .put("liveRoomName", LiveRecordInfoManager.getInstance().getLiveRoomName())
                .put("liveRoomType", getRoomBizType() + "")
                .put("anchorId", getHostUid() + "")
                .put("isLiveAnchor", isAnchor() ? "0" : "1")
                .put("liveCategoryId", String.valueOf(RoomModeManager.getInstance().getRoomMode()))
                .createTrace();
    }

    private void trackLeaveRoom() {
        new XMTraceApi.Trace()
                .pageExit2(33354)
                .put("liveId", getLiveId() + "")
                .put("roomId", getRoomId() + "")
                .put("LiveBroadcastState", LiveRecordInfoManager.getInstance().getLiveBroadcastState() + "")
                .put("liveRoomName", LiveRecordInfoManager.getInstance().getLiveRoomName())
                .put("liveRoomType", getRoomBizType() + "")
                .put("anchorId", getHostUid() + "")
                .put("isLiveAnchor", isAnchor() ? "0" : "1")
                .put("liveCategoryId", String.valueOf(RoomModeManager.getInstance().getRoomMode()))
                .createTrace();
    }

    @Override
    public void handleTrafficCardInfo() {
        runAfterViewInflate(() -> {
            CommonRequestForLive.getFlowCardInfo(new IDataCallBack<FlowCardInfoModel>() {
                @Override
                public void onSuccess(@Nullable FlowCardInfoModel object) {
                    if (object == null || object.remainSec <= 0) {
                        return;
                    }
                    mAnchorHeaderView.initTrafficCardView();
                    if (object.status == 0) {
                        Logger.d(TAG, "handleTrafficCardInfo, status = " + object.status);
                    } else if (object.status == 1) {
                        long time = object.remainSec * ParamsConstantsInLive.ONE_SECOND;
                        String content = TrafficLiveTimerText.getTimeTextInMinute(time);
                        boolean validContent = !TextUtils.isEmpty(content);
                        if (validContent) {
                            mAnchorHeaderView.addTrafficCard();
                        } else {
                            mAnchorHeaderView.removeTrafficCard();
                        }
                        mAnchorHeaderView.setTextAndStartCountdown(content, time);
                    } else {
                        mAnchorHeaderView.removeTrafficCard();
                    }
                }

                @Override
                public void onError(int code, String message) {
                    Logger.d("error", message);
                }
            });
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getHostData() != null && !isFirstResume) {
            trackRoom();
        }
        isFirstResume = false;
    }

    @Override
    public void onPause() {
        super.onPause();
        trackLeaveRoom();
    }

    @Override
    public void onDestroy() {
        if (mHandler != null && mTimingRunnable != null) {
            mHandler.removeCallbacks(mTimingRunnable);
        }
        if (mMicFadeInFadeOutAnimator != null) {
            mMicFadeInFadeOutAnimator.end();
            mMicFadeInFadeOutAnimator = null;
        }
        super.onDestroy();
    }

    @Override
    public void showOnlineH5Page() {
        if (getHostHeaderInteraction() != null) {
            getHostHeaderInteraction().showOnlineH5Page();
        }
    }


    @Override
    public void officialLiveStart() {
        runAfterViewInflate(() -> {
            super.officialLiveStart();
            initOfficialView();
            setShowBirthdayView();
            setShowRewardAvatarDecorate();
            mAnchorHeaderView.setVisToSubtitleTag(View.GONE);
            // 音频官播间 有卡片不需要展示粉丝团, 视频官播间需要展示
            if (getLiveMediaType() == LiveMediaType.TYPE_AUDIO) {
                mAnchorHeaderView.getMFollowGuardView().setFollowViewVisibility(View.GONE);
            }
//            mAnchorHeaderView.setBackground(null);
            if (mTopHeadlinesView != null) {
                mTopHeadlinesView.setVisibility(View.INVISIBLE);
            }
            if (mIncomeLayout != null) {
                mIncomeLayout.setVisibility(View.INVISIBLE);
            }
            if (mRanksView != null) {
                mRanksView.setVisibility(View.INVISIBLE);
            }
            if (mCouponView != null) {
                mCouponView.setVisibility(View.GONE);
            }
        });
    }

    @Override
    public void officialLiveEnd() {
        runAfterViewInflate(() -> {
            super.officialLiveEnd();
            setShowBirthdayView();
            setShowRewardAvatarDecorate();
            mAnchorHeaderView.setVisToSubtitleTag(VISIBLE);
            mAnchorHeaderView.getMFollowGuardView().setFollowViewVisibility(View.VISIBLE);
            mAnchorHeaderView.setBackgroundResource(R.drawable.live_lamia_room_audience_common_black_bg);
            if (mTopHeadlinesView != null) {
                mTopHeadlinesView.setVisibility(View.VISIBLE);
            }
            if (mIncomeLayout != null) {
                mIncomeLayout.setVisibility(View.VISIBLE);
            }
            if (mRanksView != null) {
                mRanksView.setVisibility(View.VISIBLE);
            }
            if (mOfficialSecondPartLayout != null) {
                mOfficialSecondPartLayout.setVisibility(View.GONE);
            }
            if (mCouponView != null) {
                setCouponViewVisible(hasCoupons);
            }
        });
    }

    private void initOfficialView() {
        runAfterViewInflate(() -> {
            mOfficialSecondHeaderVs = findViewById(R.id.liveanchor_vs_official_second_layout);
            if (mOfficialSecondHeaderVs != null) {
                mOfficialSecondPartLayout = (ConstraintLayout) mOfficialSecondHeaderVs.inflate();
                mOfficialLikeNumLl = mOfficialSecondPartLayout.findViewById(R.id.liveanchor_ll_official_like_num);
                mOfficialPeopleNumTv = mOfficialSecondPartLayout.findViewById(R.id.liveanchor_tv_official_people_num);
                mOfficialHotNumTv = mOfficialSecondPartLayout.findViewById(R.id.liveanchor_tv_official_hot_num);
                mOfficialLikeNumTv = mOfficialSecondPartLayout.findViewById(R.id.liveanchor_tv_official_like_num);
                mOfficialLikeNumLl.setOnClickListener(this);
            }
            mOfficialSecondPartLayout.setVisibility(View.VISIBLE);
        });
    }

    @Nullable
    private IHostHeaderInteraction getHostHeaderInteraction() {
        return getComponentInteractionSafety(IHostHeaderInteraction.class);
    }
}
