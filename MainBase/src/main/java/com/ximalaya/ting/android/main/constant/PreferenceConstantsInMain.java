package com.ximalaya.ting.android.main.constant;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;

/**
 * <AUTHOR>
 * @Date 16/11/29
 * <p>
 */

public class PreferenceConstantsInMain
        implements PreferenceConstantsInOpenSdk {
    public final static String HAS_SHOW_DOCUMENT_TIPS = "hasShowDocumentTips";//播放页文档模式提示
    //    public final static String TINGMAIN_KEY_IS_HUAWEI_WATCH_ENABLE = "isHuaweiWatchEnable";
    public static final String KEY_RECENT_METADATA = "category_recent_metadata";
    public static final String KEY_RECENT = "category_recent";
    public static final String KEY_RECENT_1 = "category_recent_1";
    public static final String KEY_RECENT_2 = "category_recent_2";
    public static final String KEY_RECENT_3 = "category_recent_3";
    public static final String KEY_SHOW_SUBSCRIBE_SETTING = "show_subscribe_setting";
    public static final String KEY_SHOW_ALBUM_BUY_PRESENT_POP_1 = "show_album_buy_present_pop_1";
    public static final String KEY_SHOW_ALBUM_BUY_PRESENT_POP_2 = "show_album_buy_present_pop_2";
    public static final String KEY_SHOW_ALBUM_SUBSCRIBE_POP_2 = "key_show_album_subscribe_pop_2";
    public static final String KEY_SHOW_ALBUM_VIDEO_TAB = "key_show_album_video_tab";
    public static final String KEY_SHOW_ALBUM_STORE_POP = "key_show_album_store_pop";
    public static final String KEY_SHOW_ALBUM_SWITCH_POP = "key_show_album_switch_pop";
    public static final String KEY_SHOW_SHOPING_CART_POP = "key_show_shopping_cart_pop";
    public static final String KEY_SHOW_SHOPING_CART_POP_V3 = "key_show_shopping_cart_pop_v3";

    // 专辑页声音条高亮提示是否出现过
    public static final String KEY_IS_ALBUM_SOUND_TIP_SHOWN = "key_is_album_sound_tip_shown";
    // 专辑页商店是否出现过
    public static final String KEY_IS_ALBUM_STORE_TIP_SHOWN = "key_is_album_store_tip_shown";
    // 专辑页订阅高亮提示是否出现过
    public static final String KEY_IS_ALBUM_SUBSCRIBE_TIP_SHOWN = "key_is_album_subscribe_tip_shown";
    // 播放页倍速提示是否出现过
    public static final String KEY_IS_PLAY_TEMPO_TIP_SHOWN = "key_is_play_tempo_tip_shown";
    // 播放页弹幕提示是否出现过
    public static final String KEY_IS_PLAY_BULLET_TIP_SHOWN = "key_is_play_bullet_tip_shown";
    // 播放页精彩声音标记提示是否出现过
    public static final String KEY_IS_PLAY_MARK_TIP_SHOWN = "key_is_play_mark_tip_shown";
    // 播放页短内容提示是否出现过
    public static final String KEY_IS_SHORT_CONTENT_TIP_SHOWN = "key_is_short_content_tip_shown";
    // 推荐页不感兴趣提示是否出现过
    public static final String KEY_IS_RECOMMEND_DISLIKE_TIP_SHOWN = "key_is_recommend_dislike_tip_shown";
    // 是否是首次打开播放页浮动设置面板
    public static final String KEY_IS_PLAY_FLOAT_PANEL_FIRST_OPEN = "key_is_play_float_panel_first_open";

    // 首次安装、首次订阅
    public static final String KEY_IS_FIRST_SUBSCRIBE = "key_is_first_subscribe";// 首次安装、首次订阅
    // 专辑 视频TAB 的排序方式
    public static final String KEY_VIDEO_IS_ASC = "key_video_is_asc";
    // 专辑 视频TAB 的排序方式
    public static final String KEY_VIDEO_HISTORY = "key_video_history";
    // 专辑 定位正在播放声音的定位提示
    public static final String KEY_HAS_SHOW_LOCATION_TOAST = "key_has_show_location_toast";
    // 听头条 定位正在播放声音的定位提示
    public static final String KEY_HAS_SHOW_LOCATION_TOAST_FOR_TOU_TIAO = "key_has_show_location_toast_for_tou_tiao";

    public static final String KEY_NEED_SYNC_PUSH_SETTING = "need_sync_push_setting";

    public static final String KEY_CATEGORY_FRAGMENT_CACHE = "category_fragment_cache";

    public static final String KEY_CATEGORY_FRAGMENT_HAS_CACHE = "category_fragment_has_cache";

    public static final String KEY_DEFAULT_METADATA = "default_metadata";

    public static final String KEY_FIND_REC_TRACK = "find_rec_track";

    public static final String KEY_FIND_REC_TABS = "find_rec_tabs";

//    public static final String KEY_WILL_HANDLE_GUESS_LIKE = "will_handle_guess_like"; // 是否要处理猜你喜欢更新

    public static final String KEY_CUSTOMISE_CLICKED = "customise_clicked"; //首页的 "点我猜更准" 是否点击过

    public static final String KEY_HAS_SHOW_PPT_PLAY_TIP_1 = "has_show_ppt_play_tip_1";

    public static final String KEY_HAS_FIRST_OPEN_PLAY_FRAGMENT = "has_first_open_play_fragment";


    public static final String KEY_LAST_CLOSE_HINT_LOGIN_TIME = "last_close_hint_login_time";

    public static final String KEY_PLAYFRAGMENT_HAS_SHOWN_XI_MAO_WINDOW = "key_playfragment_has_shown_xi_mao_window";

    public static final String KEY_LISTENOTE_SHOW_RECOMMEND_ALBUM= "key_listenote_show_recommend_album";

    public static final String KEY_LISTENOTE_SHOW_LIST_OR_GRID= "key_listenote_show_list_or_grid";

    public static final String KEY_USER_LISTEN_HOUR_AND_MINU = "key_user_listen_hour_and_minu";

    public static final String KEY_DATE_ALBUM_SHOW_SHARE_GIFT_ICON = "key_date_album_show_share_gift_icon";
    public static final String KEY_DATE_SHOW_SHARE_GIFT_ICON = "key_date_show_share_gift_icon";
    public static final String KEY_DATE_FIRST_SHARE_TRACK = "key_date_share_track";
    public static final String KEY_DATE_FIRST_SHARE_ALBUM = "key_date_share_album";
    public static final String KEY_LAST_DATE_SHOW_ANCHOR_RANK = "key_last_date_show_anchor_rank";
    public static final String KEY_LAST_DATE_SHOW_PROMOTION_INCOME = "key_last_date_show_promotion_income";
    public static final String KEY_LAST_DATE_SHOW_PROMOTION_USER_ID = "key_last_date_show_promotion_userId";

    public static final String KEY_LAST_SAVE_ANCHOR_GRADE = "key_last_save_anchor_grade";

    public static final String KEY_LAST_FIND_RECOMMEND_STREAM="key_last_find_recommend_stream";

    public static final String KEY_LAST_FIND_TAB="key_last_find_tab";

    public static final String KEY_LAST_FIND_CREATE_DYNAMIC_TIP="key_last_find_create_dynamic_tip";

    public static final String KEY_INTEREST_CARD_SELECTED_TAGS = "key_interest_card_selected_tags"; // 未登录账号时保存的兴趣卡片
    public static final String KEY_INTEREST_CARD_SELECTED_TAGS_LOGINED = "key_interest_card_selected_tags_logined"; // 登录账号后保存的兴趣卡片

    // 个性化设置选择的分类
    public static final String KEY_CUSTOMIZED_CATEGORIES_ID = "key_customized_categories_id";
    // 个性化设置，用户信息性别年龄保存
    public static final String KEY_CUSTOMIZED_PERSON_INFO_SAVE = "key_customized_person_info_save";
    // 进行了个性化设置 用以通知发现页刷新
    public static final String KEY_CUSTOMIZED = "key_customized";

    // 收听里程碑标记
    public static final String KEY_FLAG_MILESTONE_24H = "key_flag_milestone_24h";
    public static final String KEY_FLAG_MILESTONE_50H = "key_flag_milestone_50h";
    public static final String KEY_FLAG_MILESTONE_100H = "key_flag_milestone_100h";
    public static final String KEY_FLAG_MILESTONE_200H = "key_flag_milestone_200h";
    public static final String KEY_FLAG_MILESTONE_300H = "key_flag_milestone_300h";

    // 当前播放倍速
    public static final String KEY_PLAY_TEMPO = "key_play_tempo";
    public static final String KEY_IS_DUBBING_FIRST_SHOW = "key_is_dubbing_first_show";

    /**
     * 专辑页vip专享和畅听提示vip优惠金额
     */
    public static final String KEY_HAS_SHOW_VIP_HINT_TOAST_LIST = "key_has_show_vip_hint_toast_list_";

    public static final String KEY_LAST_CLOSE_ALBUM_RECOMMEND = "key_last_close_album_recommend";
    public static final String KEY_USE_NEW_HOME = "use_new_home";
    public static final String KEY_HAS_SHOW_FEED_STREAM_LONG_CLICK_GUIDE = "key_has_show_feed_stream_long_click_guide";
    public static final String KEY_HAS_SHOW_SUBSCRIBE_TOAST_AFTER_GAIN_COUPON_DURING_2018_123_ACTIVITY = "key_has_show_subscribe_toast_after_gain_coupon_during_2018_123_activity"; // 123活动期间领券后自动订阅提示是否已显示过

    public static final String KEY_HAS_SHOW_GUESS_YOU_LIKE_TIPS_DIALOG = "key_has_show_guess_you_like_tips_dialog";
    public static final String KEY_CUSTOMIZE_SELECTED_CATEGORIES_FORMAT = "key_customize_selected_categories_formated";
    public static final String KEY_HAS_CLICKED_CHANGED_INTEREST_ENTRY = "key_has_clicked_selected_interest_entry";
    public static final String KEY_DUBB_IS_DOUBLE_ED = "key_dubb_is_double_ed"; // 是否已经双击过
    public static final String KEY_DUBB_IS_SHOW_DOUBLE_CLICK_HINT = "key_dubb_is_show_double_click_hint";   // 是否显示过双击引导
    public static final String KEY_SEARCH_EBOOK_WANT_LISTEN = "key_search_ebook_want_listen";   // 搜索点击我想听记录 想听人数
    public static final String KEY_HAS_SHOW_NEW_USER_LISTEN_GUIDE = "key_has_show_new_user_listen_guide";

    // 上次播放小雅音箱设备信息
    public static final String KEY_DLNA_XIAOYA_DEVICE_INFO="key_dlna_xiaoya_device_info";
    // 上次播放WIFI音箱设备信息
    public static final String KEY_DLNA_WIFI_DEVICE_INFO="key_dlna_wifi_device_info";

    //专辑切换按钮是否首次出现
    public static final String KEY_IS_ALBUM_SWITCH_SHOWN = "key_is_album_switch_shown";

    // 新人推荐页兴趣偏好提示弹窗是否展示过
    public static final String KEY_HAS_SHOW_NEW_USER_RECOMMEND_INTEREST_DIALOG = "key_has_show_new_user_recommend_interest_dialog";
    public static final String KEY_HAS_SHOW_VOICE_MARK_DIALOG = "key_has_show_voice_mark_dialog";
    // 播放页浮动面板首次打开时的版本号
    public static final String KEY_FLOAT_PANEL_OPEN_VERSION = "key_float_panel_open_version";
    // 是否已经提交过个推所需的imei信息
    public static final String KEY_HAS_POST_IMEI_FOR_GE_TUI = "key_has_post_imei_for_ge_tui";
    // 推荐页新人礼包giftTag字段，取服务端的值存下来，请求时需要传给服务端
    public static final String KEY_RECOMMEND_NEW_USER_GIFT_GIFT_TAG = "key_recommend_new_user_gift_gift_tag";

    //个人中心未认证布局是否关闭过(关闭之后以后不再显示)
    public static final String KEY_ANCHOR_SPACE_UNVERIFY_SHUTDOWN = "key_anchor_space_unverify_shutdown";

    //已经处理过的截屏路径
    public static final String KEY_HANDLE_DUB_VOICE_SCREEN_SHOT = "handle_dub_voice_screen_shot";

    // 任务中心入口tips展示的时间
    public static final String KEY_TASK_CENTER_SHOW_TIME = "key_task_center_show_time";

    // 热评推荐时间
    public static final String KEY_HOT_COMMENT_RECOMMEND_TIME = "key_hot_comment_recommend_time";

    // 引导分享语音播放时间
    public static final String KEY_SHARE_HINT_SOUND_PLAY_TIME = "key_share_hint_sound_play_time";

    // 评论引导
    public static final String KEY_COMMENT_GUIDE_HINT = "key_comment_guide_hint";
    // 评论引导-输入框图片按钮
    public static final String KEY_COMMENT_GUIDE_PIC = "key_comment_guide_pic";
    // 评论引导-输入框底部弹幕按钮
    public static final String KEY_COMMENT_GUIDE_BOTTOM_BULLET = "key_comment_guide_bottom_bullet";
    // 上次踩评论的时间对应的当天0时0分0秒时间戳
    public static final String KEY_LAST_HATE_COMMENT_TIME_BY_DATE = "key_last_hate_comment_time_by_date";

    // 记录当天用户手动关闭弹幕的专辑
    public static final String KEY_PLAY_PAGE_DANMU_ACTION = "key_play_page_danmu_action";
    // 记录用户手动开启弹幕的专辑
    public static final String KEY_PLAY_PAGE_DANMU_OPEN_ACTION = "key_play_page_danmu_open_action";

    public static final String KEY_AUDIO_PLAY_PAGE_SHOW_COMMENT_GUIDE_TIME = "key_audio_play_page_show_comment_guide_time";

    // 记录展示跳过片头片尾提示框的专辑ids
    public static final String KEY_SKIP_HEAD_TAIL_DIALOG_SHOWN_ALBUM_IDS = "key_skip_head_tail_dialog_shown_album_ids";

    //首页日签-日切换动画
    public static final String KEY_DAILY_SIGN_DAY_ANIMATION = "key_daily_sign_day_animation";

    // 音乐听单引导是否展示过
    public static final String KEY_MUSIC_LIST_HINT_SHOWN = "key_music_list_hint_shown";
    // 收藏音乐后引导弹窗是否展示过
    public static final String KEY_MUSIC_LIKED_HINT_SHOWN = "key_music_liked_hint_shown";

    //账号页-头像引导声音签名动画
    public static final String KEY_DAILY_VOICE_SIG_ANIMATION = "key_daily_voice_sig_animation";
    //账号页-头像引导声音签名动画上次显示日期
    public static final String KEY_VOICE_SIG_ANIMATION_SHOWN_DATE = "key_voice_sig_animation_shown_date";

    // 隐私设置-个性化服务
    public static final String KEY_SETTING_USE_PERSONAL_SERVICE_CHANGED = "key_setting_use_personal_service_changed";

    //评论设置-选择接收谁的评论通知
    public static final String KEY_COMMENT_NOTIFY_PEOPLE = "key_comment_notify_people";
    //评论设置-选择评论通知频率
    public static final String KEY_COMMENT_NOTIFY_FREQUENCY = "key_comment_notify_frequency";
    //评论设置-从服务端获取的所有配置数据
    public static final String KEY_COMMENT_SETTING = "key_comment_setting";

    public static final String KEY_XIAOYA_PET_SHOW_IN_MYSPACE = "key_xiaoya_pet_show_in_myspace";

    public static final String KEY_ONEKEY_TIMER_TIP_SHOW = "key_onekey_timer_tip_show";

    public static final String KEY_IS_VIP_TRACK_PLAY_QUALITY_TIP_SHOWN = "key_is_vip_track_play_quality_tip_shown";

    //本地是否可发送底部弹幕
    public static final String KEY_CAN_SEND_BOTTOM_BULLET = "key_can_send_bottom_bullet_";

    // 本地自定义专辑标签
    public static final String KEY_LOCAL_ALBUM_TAG = "key_local_album_tag_";

    // 哄睡模式首次提示是否展示过
    public static final String KEY_HAS_SLEEPY_PLAN_TERMINATE_HINT_SHOWN = "key_has_sleepy_plan_terminate_hint_shown";

    // 儿童哄睡模式是否打开
    public static final String KEY_OPEN_CHILD_SLEEP_MODE = "key_open_child_sleep_mode";

    // 会员协议本地同意
    public static final String KEY_VIP_PROTOCOL_LOCAL_AGREED = "key_vip_protocol_local_agreed_";

    // 主播评论后提示
    public static final String KEY_COMMENT_GUIDE_COMMENTED_HINT = "key_comment_guide_commented_hint";

    //是否已经展示过新用户分享引导
    public static final String KEY_HAS_SHOW_NEWER_SHARE_GUIDE = "key_has_show_newer_share_guide";

    // 保存看广告提示的小黄条的移除时间
    public static final String KEY_TINGLIST_SAVE_REMOVE_LOOK_AD_HINT = "key_tinglist_save_remove_look_ad_hint";

    // 首页新用户挂件手动关闭时间
    public static final String KEY_TIME_CLOSE_USER_GIFT_PENDANT = "key_time_close_user_gift_pendant";

    // 个人页下拉引导
    public static final String KEY_ANCHOR_SPACE_SHOW_YA_GUIDE = "key_anchor_space_show_ya_guide";

    //新用户冷启动页解锁推荐提示是否已展示过
    public static final String KEY_HAS_SHOWN_CHOOSE_CATEGORY_HINT = "key_has_shown_choose_category_hint";

    //推荐播放卡片页新人礼包弹窗是否已自动弹出过
    public static final String KEY_HAS_SHOWN_NEW_USER_GIFT_DIALOG = "key_has_shown_new_user_gift_dialog";

    public static final String KEY_HAS_SHOWN_SLIDE_ANIMATION = "key_has_shown_slide_animation";

    // 进行了听书口味设置，用以通知首页刷新
    public static final String KEY_RECOMMEND_CATEGORY = "key_recommend_category";
    // 进行了兴趣卡片选择，用以通知首页刷新
    public static final String KEY_INTEREST_TAG_SELECTED = "key_interest_tag_selected";

    public static final String KEY_REMOVE_AD_GUDIE = "key_remove_ad_gudie";

    // 关闭最近常听推荐订阅弹窗时间
    public static final String KEY_CLOSE_RECENT_RECOMMEND_SUBSCRIBE_DIALOG_TIME = "key_close_recent_recommend_subscribe_dialog_time";

    // 是否首次点击最近常听推荐订阅弹窗
    public static final String KEY_FIRST_SUBSCRIBE_RECENT_RECOMMEND_SUBSCRIBE_DIALOG = "key_first_subscribe_recent_recommend_subscribe_dialog";

    // 播放页是否首次显示驾驶模式更多入口提示
    public static final String KEY_FIRST_SHOW_DRIVE_MODE_ENTRY_IN_PLAY_PAGE_TIP = "key_first_show_drive_mode_entry_in_play_page_tip";


    // 新用户任务完成，刷新首页
    public static final String KEY_NEW_USER_TASK_COMPLETE = "key_new_user_task_complete";

    // 投放渠道跳转专辑页面
    public static final String KEY_FROM_CAHNNEL_ITING = "key_from_channel_iting";

    // 账号页小雅入口收敛引导
    public static final String KEY_MINE_XIAOYA_ENTRY_GUIDE_NEW = "key_mine_xiaoya_entry_guide_new";

    // 打赏提示
    public static final String KEY_MINE_REWARD_TIP = "key_mine_reward_tip";

    //后台播放设置优化
    public static final String KEY_NOTIFY_USER_CONFIG_BATTERY_OPT = "key_notify_user_config_battery_opt";
    public static final String KEY_NOTIFY_USER_CONFIG_SLEEPING_MODE= "key_notify_user_config_sleeping_mode";
    public static final String KEY_LAST_NOTIFY_USER_CONFIG_BATTERY_OPT_TIME = "key_last_notify_user_config_battery_opt_time";

    public static final String KEY_NOTIFY_USER_WIFI_SLEEP = "key_notify_user_wifi_sleep";

    public static final String KEY_FEED_PLAY_FIRST_GUIDE_HAS_SHOWN = "key_feed_play_first_guide_has_shown";

    // 个人页关注条当前所处日期
    public static final String KEY_ANCHOR_SPACE_FOLLOW_POPUP_DATE = "key_anchor_space_follow_popup_date";
    // 个人页当日已展示关注条主播列表
    public static final String KEY_ANCHOR_SPACE_FOLLOW_POPUP_HAS_SHOW_ANCHOR_LIST = "key_anchor_space_follow_popup_has_show_anchor_list";
    // 个页主播访问记录数据
    public static final String KEY_ANCHOR_SPACE_VISITOR_DATA = "key_anchor_space_visitor_data";

    //上次显示今日热点引导的时间
    public static final String KEY_LAST_SHOW_DAILY_NEWS_HINT_TIME =
            "key_last_show_daily_news_hint_time";

    public static final String KEY_HAS_SHOW_VIDEO_PAGE_SLIDE_GUIDE = "key_has_show_video_page_slide_guide";

    //收藏展示“点赞成功！在【我的-全部服务】里找到全部点赞”的累计次数，5次后展示感谢推荐
    public static final String KEY_HAS_SHOW_LIKE_SUCCESS_COUNT = "key_has_show_like_success_count";

    //是否由强登录引导弹窗进行登录的
    public static final String KEY_IS_LOGIN_FROM_STRONG_LOGIN_GUIDE_DIALOG = "key_is_login_from_strong_login_guide_dialog";

    public static final String KEY_HAS_SHOW_PLAY_MORE_ACTION_AI_DOC_RED_DOAT = "key_has_show_play_more_action_ai_doc_red_doat";

    // 主播已进入自己专辑播放页的记录前缀，后面拼接albumId
    public static final String KEY_ANCHOR_HAS_ENTER_SELF_ALBUM_PLAY_PAGE_PREFIX = "key_anchor_has_enter_self_album_play_page_";
    public static final String KEY_TO_LISTEN_QUESTIONNAIRE_CLICK_COUNT = "key_to_listen_questionnaire_click_count";
    public static final String KEY_TO_LISTEN_QUESTIONNAIRE_GUIDE_SHOWN = "key_to_listen_questionnaire_guide_shown";

    // 播放页AI文稿开关引导
    public static final String KEY_HAS_SHOW_AUDIO_PLAY_AI_DOC_SWITCH_GUIDE = "key_has_show_audio_play_ai_doc_switch_guide";

    public static final String KEY_HAS_DAILYNEWS4_TO_LISTEN_TIP_SHOWN = "key_has_dailynews4_to_listen_tip_shown";

    public static final String KEY_INTEREST_TAG_SELECTED_VALUES = "key_interest_tag_selected_values";

    // 互动开屏播放页引导
    public static final String KEY_HAS_SHOW_INTERACTIVE_AUDIO_PLAY_GUIDE = "key_has_show_interactive_audio_play_guide";

    // 热点是否点击过推送关闭自动播放引导
    public static final String KEY_HAS_DAILYNEWS4_TO_LISTEN_PUSH_PLAY_CLICK = "key_has_dailynews4_to_listen_push_play_click";
    public static final String KEY_HAS_DAILYNEWS4_TO_LISTEN_PUSH_NOT_PLAY = "key_has_dailynews4_to_listen_push_not_play";

    // 投稿箱显示匿名投稿提示的次数
    public static final String KEY_MAIL_BOX_ANONYMOUS_TIPS_SHOW_COUNT = "key_mail_box_anonymous_tips_show_count";
    // 投稿箱上次显示匿名投稿提示的时间戳
    public static final String KEY_LAST_SHOW_MAIL_BOX_ANONYMOUS_TIPS_TIME = "key_last_show_mail_box_anonymous_tips_time";
    // 订阅列表点击统计日期
    public static final String KEY_SUBSCRIBE_DAILY_CLICK_TIME = "key_subscribe_daily_click_time";
    // 订阅列表点击统计次数
    public static final String KEY_SUBSCRIBE_DAILY_CLICK_COUNT = "key_subscribe_daily_click_count";
    // 订阅追更悬浮条上次主动关闭时间
    public static final String KEY_SUBSCRIBE_GUIDE_LAST_CLOSE_TIME = "key_subscribe_guide_last_close_time";
    // 订阅追更悬浮条连续关闭次数
    public static final String KEY_SUBSCRIBE_GUIDE_CONTINUE_CLOSE_COUNT = "key_subscribe_guide_continue_close_count";
    // 个人页商业化主播会员（ximi）首次展示
    public static final String KEY_ANCHOR_BUSINESS_XIMI_HAS_SHOW = "key_anchor_business_ximi_has_show";
    // 推荐卡片页面引导是否展示过
    public static final String KEY_RECOMMEND_ALBUM_CARD_2023_GUIDE_HAS_SHOW = "key_recommend_album_card_2023_guide_has_show";

    public static final String KEY_PLAY_PAGE_POD_SHOWN_TRACK = "key_play_page_pod_shown_track_id";

    public static final String KEY_ALL_SERVICE_GUIDE_LAST_SHOW= "key_all_service_guide_last_show";
    // 是否已打开过新创建评价页
    public static final String KEY_HAS_SHOWN_NEW_CREATE_ALBUM_RATE_GUIDE = "key_has_shown_new_create_album_rate_guide";

    public static final String KEY_SHOW_NEW_USER_VIP_DISCOUNT_DIALOG_TIME = "key_show_new_user_vip_discount_dialog_time";

    // 首页猜你喜欢第一屏广告坑位
    public static final String KEY_HOME_ANCHOR_AD_INDEX_ARRAY = "key_home_anchor_ad_index_array";


    public static final String KEY_MNN_RUN_OVER_TIME = "mnn_run_over_time_v2";

    public static final String KEY_CAN_SHOW_AWARD_DIALOG_IN_PLAY_PAGE = "key_can_show_award_dialog_in_play_page";
    public static final String KEY_LAST_SHOW_AWARD_DIALOG_TIME_IN_PLAY_PAGE = "key_last_show_award_dialog_time_in_play_page";
    public static final String KEY_LAST_IGNORE_AWARD_DIALOG_TS = "key_last_ignore_award_dialog_ts";
    public static final String KEY_AWARD_DIALOG_REST_SHOW_TIMES_THIS_WEEK = "key_award_dialog_rest_show_times_this_week";
    public static final String KEY_CURRENT_PLAYPAGE_SKIN_NAME = "key_current_playpage_skin_name";
    public static final String KEY_CURRENT_SKIN_USER_PRIVIELGE = "key_current_skin_user_privilege";
}
