<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.main.playpage.playy.component.controlbar.view.TouchableViewGroup xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_play_control"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:paddingBottom="18dp"
    android:clipChildren="false"
    tools:background="@color/main_blue_5ba6ff">

    <LinearLayout
        android:id="@+id/main_vg_seek_bar"
        android:clipChildren="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_vg_play_backward_btn"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_gravity="top"
            android:layout_marginLeft="16dp"
            android:contentDescription="@string/main_backward_15_second"
            android:alpha="0.55"
            app:lottie_autoPlay="false"
            app:lottie_fileName="lottie/main_yplay_back_animation_v2.json"
            app:lottie_imageAssetsFolder="lottie/images" />

        <FrameLayout
            android:clipChildren="false"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="26dp">

            <!-- 进度条 -->
            <!-- 当进度条放大高度比普通状态下滑块高度小时，拖动后放大效果有问题，这是由于不会触发onSizeChange，所以通知设置放大后的progressDrawable和滑块drawable，触发onSizeChange会调用到的方法 -->
            <com.ximalaya.ting.android.host.view.ScaleableSeekBar
                android:id="@+id/main_seek_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="17dp"
                android:paddingEnd="17dp"
                android:paddingBottom="7dp"
                android:thumbOffset="0dp"
                android:contentDescription="播放进度条"
                android:max="100"
                android:maxHeight="2dp"
                android:minHeight="2dp"
                android:progress="0"
                android:thumb="@drawable/main_play_page_v2_seekbar_thumb"
                android:progressDrawable="@drawable/host_audio_play_page_v2_seekbar"
                app:scaleableSeekBar_ProgressScaleResource="@drawable/host_audio_play_page_y_seekbar"
                app:scaleableSeekBar_ThumbScaleResource="@drawable/main_play_page_y_seekbar_thumb_scale"
                app:scaleableSeekBar_KeyPointColor="@color/main_color_ccffffff"
                app:scaleableSeekBar_ProgressScaleHeight="6dp"
                app:scaleableSeekBar_RestoreTimeMS="500" />

            <TextView
                android:id="@+id/main_tv_progress"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="17dp"
                android:layout_gravity="bottom|start"
                android:gravity="start|bottom"
                android:includeFontPadding="false"
                android:textSize="10sp"
                android:textColor="@color/main_color_white"
                android:alpha="0.4"
                tools:text="13:20" />

            <TextView
                android:id="@+id/main_tv_sound_quality"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:gravity="bottom"
                android:includeFontPadding="false"
                android:textSize="10sp"
                android:textColor="@color/main_color_white"
                tools:text="音质" />

            <TextView
                android:id="@+id/main_tv_duration"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="17dp"
                android:layout_gravity="bottom|end"
                android:gravity="end|bottom"
                android:includeFontPadding="false"
                android:textSize="10sp"
                android:alpha="0.4"
                android:textColor="@color/main_color_white"
                tools:text="63:20" />

        </FrameLayout>

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_vg_play_forward_btn"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_gravity="top"
            android:layout_marginEnd="16dp"
            android:alpha="0.55"
            app:lottie_autoPlay="false"
            app:lottie_fileName="lottie/main_yplay_forward_animation_v2.json"
            app:lottie_imageAssetsFolder="lottie/images" />
        <!--        </FrameLayout>-->

    </LinearLayout>

    <LinearLayout
        android:id="@+id/main_vg_control_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginTop="20dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/main_iv_play_list_btn_audio"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_marginStart="16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                android:contentDescription="@string/main_play_list"
                android:src="@drawable/main_ic_y_playlist_n_n_line_regular_26" />

            <View
                android:id="@+id/main_iv_play_list_btn_audio_mask_view"
                app:layout_constraintEnd_toEndOf="@+id/main_iv_play_list_btn_audio"
                app:layout_constraintStart_toStartOf="@+id/main_iv_play_list_btn_audio"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="0dp"
                android:layout_height="0dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_iv_play_prev_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginRight="29dp"
            app:lottie_autoPlay="false"
            app:lottie_fileName="lottie/yplay_pre_animation.json"
            app:lottie_imageAssetsFolder="lottie/images" />

        <FrameLayout
            android:id="@+id/main_vg_play_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/main_play_btn_bg"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:src="@drawable/main_bg_round_d8d8d8" />

            <ImageView
                android:id="@+id/main_iv_play_btn_center_icon"
                android:layout_width="66dp"
                android:layout_height="66dp"
                android:layout_gravity="center"
                android:scaleType="centerCrop" />

            <View
                android:id="@+id/main_iv_play_btn_loading"
                android:layout_width="66dp"
                android:layout_height="66dp"
                android:background="@drawable/main_audio_play_page_play_btn_loading_y"
                android:layout_gravity="center"
                android:visibility="invisible"
                tools:visibility="visible" />

        </FrameLayout>
        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_iv_play_next_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginLeft="29dp"
            app:lottie_autoPlay="false"
            app:lottie_fileName="lottie/yplay_next_animation.json"
            app:lottie_imageAssetsFolder="lottie/images" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_vg_timer_off_btn"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:contentDescription="@string/main_timing_shutdown">

            <ImageView
                android:id="@+id/main_iv_timer_off"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_gravity="center_horizontal"
                android:contentDescription="@string/main_timing_shutdown"
                android:scaleType="matrix"
                android:layout_marginEnd="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintBottom_toBottomOf="parent"
                android:src="@drawable/main_ic_y_time_off_line_regular_26" />

            <TextView
                android:id="@+id/main_tv_timer_off"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/main_iv_timer_off"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:maxLines="1"
                app:layout_constraintStart_toStartOf="@id/main_iv_timer_off"
                app:layout_constraintEnd_toEndOf="@id/main_iv_timer_off"
                android:textColor="@color/main_white"
                android:textSize="8sp"
                tools:text="1214:11" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>

</com.ximalaya.ting.android.main.playpage.playy.component.controlbar.view.TouchableViewGroup>
