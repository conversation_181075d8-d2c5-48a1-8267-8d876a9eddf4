<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:showIn="@layout/main_play_page_y">

    <ViewStub
        android:id="@+id/main_track_over_audition_stub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/main_play_page_over_audition_convert_x"
        android:layout_gravity="center"/>

</RelativeLayout>