<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/host_y16"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/main_tv_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="升级1个月" />

    <TextView
        android:id="@+id/main_tv_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:background="@drawable/host_2corner_ff4444"
        android:includeFontPadding="false"
        android:paddingHorizontal="4.1dp"
        android:paddingVertical="3.84dp"
        android:textColor="@color/host_white"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_month"
        app:layout_constraintStart_toEndOf="@+id/main_tv_month"
        app:layout_constraintTop_toTopOf="@+id/main_tv_month"
        tools:text="最划算"
        tools:visibility="invisible" />

    <ImageView
        android:id="@+id/main_iv_month_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/host_default_side_margin"
        android:src="@drawable/host_vip_upgrade_month_checkbox"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_tv_month_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:includeFontPadding="false"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="20dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_iv_month_check"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="￥128" />


</androidx.constraintlayout.widget.ConstraintLayout>