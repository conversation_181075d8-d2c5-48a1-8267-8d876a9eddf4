<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/host_y34"
    tools:ignore="MissingDefaultResource">

    <View
        android:id="@+id/main_view_space"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/main_dialog_real_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_dialog_real_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/host_default_side_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_bg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/host_color_f7f9fc_282828"
            android:src="@drawable/main_bg_decoration_center_dialog"
            android:scaleType="fitXY"
            app:round="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_view_top_space"
             />

        <View
            android:id="@+id/main_view_top_space"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="343:39"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_static_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="56dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="800:660"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_bg"
            app:layout_constraintTop_toTopOf="@+id/main_view_top_space"
            app:round="12dp"
            tools:visibility="visible" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_dynamic_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="56dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="800:660"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_bg"
            app:layout_constraintTop_toTopOf="@+id/main_view_top_space"
            app:round="12dp"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/main_tv_first_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/host_y20"
            android:layout_marginBottom="@dimen/host_y2"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/host_default_side_margin"
            android:text="可以更换个性装扮啦！"
            android:textColor="@color/host_color_131313_ffffff"
            android:textFontWeight="600"
            android:textSize="20sp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_bg"
            app:layout_constraintTop_toBottomOf="@+id/main_iv_dynamic_view" />

        <TextView
            android:id="@+id/main_tv_second_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/host_y6"
            android:alpha="0.5"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/host_default_side_margin"
            android:text="可在「更多」中进入装扮中心"
            android:textColor="@color/host_color_131313_ffffff"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_bg"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_first_title" />

        <androidx.cardview.widget.CardView
            android:id="@+id/main_cv_action_container"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginLeft="@dimen/host_x40"
            android:layout_marginTop="@dimen/host_y18"
            android:layout_marginRight="@dimen/host_x40"
            android:layout_marginBottom="@dimen/host_y40"
            android:background="@null"
            app:cardBackgroundColor="#ff4444"
            app:cardCornerRadius="90dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_bg"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_second_title">

            <TextView
                android:id="@+id/main_tv_action"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="立即体验"
                android:textColor="#ffffff"
                android:textFontWeight="500"
                android:textSize="16sp" />
        </androidx.cardview.widget.CardView>

        <ImageView
            android:id="@+id/main_iv_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_margin="8dp"
            android:src="@drawable/host_ic_close_20_dp"
            android:tint="@color/main_color_999999"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_bg"
            app:layout_constraintTop_toTopOf="@+id/main_iv_bg" />

        <FrameLayout
            android:id="@+id/main_fl_dialog_loading_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_iv_bg"
            tools:visibility="visible">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_loading_view_progress_xmlottieview"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                app:lottie_fileName="lottie/host_loading/loading.json"
                app:lottie_imageAssetsFolder="lottie/host_loading"
                app:lottie_repeatCount="-1"
                app:lottie_repeatMode="restart" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>