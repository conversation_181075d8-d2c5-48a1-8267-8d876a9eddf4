<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="100dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/main_playpage_skin_1"
        android:text="skin_1"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_playpage_skin_2"
        android:background="@color/host_gray"
        android:textColor="@color/host_color_white"
        android:layout_width="80dp"
        android:layout_height="40dp"/>

    <TextView
        android:id="@+id/main_playpage_skin_2"
        android:text="skin_2"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_playpage_skin_1"
        app:layout_constraintEnd_toStartOf="@+id/main_playpage_skin_3"
        android:background="@color/host_gray"
        android:textColor="@color/host_color_white"
        android:layout_width="80dp"
        android:layout_height="40dp"/>

    <TextView
        android:id="@+id/main_playpage_skin_3"
        android:text="skin_3"
        android:gravity="center"
        android:background="@color/host_gray"
        android:textColor="@color/host_color_white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_playpage_skin_2"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="80dp"
        android:layout_height="40dp"/>

    <TextView
        android:id="@+id/main_skin_download_status"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_playpage_skin_1"
        android:layout_marginTop="50dp"
        android:textColor="@color/host_gray"
    />
</androidx.constraintlayout.widget.ConstraintLayout>