<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_play_normal_cover_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_sound_cover"
            android:layout_width="260dp"
            android:layout_height="260dp"
            android:visibility="gone"
            app:corner_radius="12dp"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/main_cover_no_network_tips"
            tools:src="@drawable/host_default_album"
            tools:visibility="visible"/>

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_ai_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:round="12dp"
            app:layout_constraintTop_toTopOf="@+id/main_iv_sound_cover"
            app:layout_constraintStart_toStartOf="@+id/main_iv_sound_cover"
            android:src="@drawable/main_ic_playpage_ai_tag"
        />
        <LinearLayout
            android:id="@+id/main_cover_no_network_tips"
            android:orientation="vertical"
            android:visibility="visible"
            android:layout_marginTop="30dp"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_iv_sound_cover">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/main_color_white_60"
                android:textSize="13sp"
                android:includeFontPadding="false"
                android:contentDescription="当前无网络，请检查网络后重试"
                android:text="当前无网络，请检查网络后重试"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/main_cover_no_network_retry"
                    android:textSize="13sp"
                    android:background="@drawable/main_bg_stroke_99ffffff_corners_200dp"
                    android:textColor="@color/main_color_white_60"
                    android:gravity="center"
                    android:layout_width="100dp"
                    android:layout_height="28dp"
                    android:contentDescription="重试"
                    android:text="重试"/>

                <ImageView
                    android:id="@+id/main_no_network_loading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:src="@drawable/main_ic_ypage_loading"
                    android:gravity="center"
                    android:contentDescription="正在加载中"
                    tools:visibility="visible"/>
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/main_play_skin_cover"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="66dp"
        android:layout_width="80dp"
        android:layout_height="80dp">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_skin_cover"
            android:layout_width="60dp"
            android:layout_height="60dp"
            app:round="60dp"
            android:scaleType="centerCrop"
            android:layout_gravity="center"
            tools:src="@drawable/host_default_album"/>

        <ImageView
            android:id="@+id/main_iv_skin_cover_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/main_playpage_skin_cover_bg"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
