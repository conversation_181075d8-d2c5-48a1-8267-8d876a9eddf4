<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/x_play_ad_banner_layout_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:visibility="visible">

        <RelativeLayout
            android:layout_marginEnd="16dp"
            android:layout_marginLeft="16dp"
            android:id="@+id/x_play_ad_banner_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/main_bg_rect_a0ffffff_radius_6"
            android:visibility="visible">

            <com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
                android:id="@+id/x_play_ad_banner_icon_layout"
                android:layout_width="71dp"
                android:layout_height="40dp"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="13dp"
                android:layout_marginBottom="11dp"
                app:corner_radius="4dp">

                <com.ximalaya.ting.android.host.view.image.RatioImageView
                    android:id="@+id/x_play_ad_banner_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/host_default_cover_album_light"
                    app:corner_radius="4dp"
                    app:needColorFilter="false" />

                <com.ximalaya.ting.android.host.view.ad.AdSourceFromView
                    android:id="@+id/x_play_ad_banner_source"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_alignLeft="@id/x_play_ad_banner_icon"
                    android:layout_alignBottom="@id/x_play_ad_banner_icon" />

                <ImageView
                    android:id="@+id/x_play_ad_banner_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="4dp"
                    android:layout_alignLeft="@+id/x_play_ad_banner_icon"
                    android:layout_alignTop="@+id/x_play_ad_banner_icon"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="2dp"
                    android:layout_marginBottom="2dp"
                    android:scaleType="fitStart"
                    tools:src="@drawable/host_ad_tag_style_2" />

            </com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>

            <TextView
                android:id="@+id/x_play_ad_banner_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="13dp"
                android:layout_marginRight="6dp"
                android:layout_toLeftOf="@+id/x_play_ad_banner_click_btn"
                android:layout_toRightOf="@+id/x_play_ad_banner_icon_layout"
                android:ellipsize="end"
                android:includeFontPadding="true"
                android:maxLines="1"
                android:textColor="@color/main_color_e6ffffff"
                android:textSize="14sp"
                tools:text="免费体验福利 免费体验福利 免费体验福利" />

            <TextView
                android:id="@+id/x_play_ad_banner_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/x_play_ad_banner_title"
                android:layout_alignLeft="@+id/x_play_ad_banner_title"
                android:layout_alignRight="@+id/x_play_ad_banner_title"
                android:layout_marginTop="3dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/main_color_80ffffff"
                android:textSize="12sp"
                android:textStyle="normal"
                tools:text="广告·广告福利 " />

            <TextView
                android:id="@+id/x_play_ad_banner_click_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="28dp"
                android:gravity="center_vertical"
                android:maxWidth="72dp"
                android:maxLines="1"
                android:padding="2dp"
                android:text="查看详情"
                android:textSize="12sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/x_play_ad_banner_close"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="8dp"
                android:layout_marginRight="8dp"
                android:padding="1dp"
                android:src="@drawable/main_ad_ic_x_close_n_line_regular_24" />

            <!--广告关闭的真实点击区域-->
            <View
                android:id="@+id/x_play_ad_banner_close_real"
                android:layout_width="31dp"
                android:layout_height="31dp"
                android:layout_alignParentRight="true"
                android:contentDescription="关闭广告"
                tools:background="@color/host_blue_b34990e2" />

        </RelativeLayout>
    </RelativeLayout>
</com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer>