<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="26dp"
    tools:background="@color/host_color_000000">

    <ImageView
        android:id="@+id/main_iv_speed_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.55"
        android:src="@drawable/main_icon_speed_three"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_tv_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.55"
        android:letterSpacing="-0.05"
        android:text="1.0x"
        android:layout_marginBottom="1dp"
        android:textColor="@color/host_color_ffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_tv_speed_unit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>