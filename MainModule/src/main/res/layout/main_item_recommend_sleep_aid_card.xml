<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:importantForAccessibility="no"
    android:paddingBottom="16dp">

    <TextView
        android:id="@+id/main_tv_module_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="16dp"
        android:layout_toLeftOf="@+id/main_tv_refresh"
        android:ellipsize="end"
        android:fontFamily="sans-serif-light"
        android:gravity="center_vertical"
        android:importantForAccessibility="yes"
        android:lineSpacingExtra="3dp"
        android:maxLines="1"
        android:textColor="@color/main_color_module_title"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="人生松绑建议人生松绑建议人生松绑建议人生松绑建议" />

    <ImageView
        android:id="@+id/main_tv_refresh"
        android:layout_width="40dp"
        android:layout_height="32dp"
        android:layout_alignTop="@+id/main_tv_module_title"
        android:layout_toLeftOf="@+id/main_tv_more"
        android:contentDescription="换一换"
        android:importantForAccessibility="yes"
        android:paddingHorizontal="8dp"
        android:paddingBottom="8dp"
        android:src="@drawable/main_ic_home_item_refresh"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/main_tv_more"
        android:layout_width="40dp"
        android:layout_height="32dp"
        android:layout_alignTop="@+id/main_tv_module_title"
        android:layout_alignParentRight="true"
        android:layout_marginEnd="8dp"
        android:contentDescription="更多"
        android:importantForAccessibility="yes"
        android:paddingHorizontal="8dp"
        android:paddingBottom="8dp"
        android:src="@drawable/main_ic_home_item_more" />

    <RelativeLayout
        android:id="@+id/main_rl_content_root_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_tv_module_title"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/main_bg_sleep_aid_shape">

        <RelativeLayout
            android:id="@+id/main_rl_content_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/main_rl_cover"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="31dp"
                android:clipChildren="false">

                <com.ximalaya.ting.android.host.view.Custom3DAlbumView
                    android:id="@+id/main_3d_album_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:layout_height="93dp"
                    tools:layout_width="100dp" />
            </RelativeLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main_cl_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/main_rl_cover"
                android:layout_marginStart="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="18dp"
                android:layout_toRightOf="@+id/main_rl_cover">

                <TextView
                    android:id="@+id/main_tv_hint_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-light"
                    android:maxLines="1"
                    android:text="助眠专区 · 深夜常听"
                    android:textColor="#3D2727"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/main_iv_hint_flag"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/main_iv_hint_flag"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/main_ic_sleep_aid_hint_flag"
                    android:translationX="-12dp"
                    app:layout_constraintLeft_toRightOf="@+id/main_tv_hint_title"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/main_tv_main_album_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-light"
                    android:maxLines="2"
                    android:text="重生之都市狂仙 | 第重生之都市狂仙"
                    android:textColor="#1D3449"
                    android:textSize="19sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main_tv_hint_title" />

                <LinearLayout
                    android:id="@+id/main_ll_show_tag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main_tv_main_album_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </RelativeLayout>

        <View
            android:id="@+id/main_view_gap_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignTop="@+id/main_rl_content_container"
            android:layout_alignRight="@+id/main_rl_content_container"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="110dp"
            android:layout_marginTop="123dp"
            android:layout_marginEnd="18dp"
            android:background="#1a1D3449" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_view_gap_line"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="24dp">

            <TextView
                android:id="@+id/main_tv_continue_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="sans-serif-light"
                android:maxLines="1"
                android:text="续播"
                android:textColor="#3D2727"
                android:textSize="13sp" />

            <ImageView
                android:id="@+id/main_iv_play_line"
                android:layout_width="8dp"
                android:layout_height="10dp"
                android:layout_marginHorizontal="6dp"
                android:src="@drawable/main_ic_sleep_aid_play_line" />

            <TextView
                android:id="@+id/main_tv_play_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="208. 余凯口述30年史：世界不止余凯口述30年史：世界不止"
                android:textColor="#1D3449"
                android:textSize="13sp" />

            <FrameLayout
                android:id="@+id/main_show_notes_play_layout_wrap"
                android:layout_width="54dp"
                android:layout_height="54dp"
                android:layout_marginEnd="8dp"
                android:contentDescription="播放按钮">

                <FrameLayout
                    android:id="@+id/main_show_notes_play_layout"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:background="@drawable/main_bg_sleep_aid_play_shape">

                    <ImageView
                        android:id="@+id/main_iv_show_notes_play_btn"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        android:src="@drawable/host_btn_play_btn_inside_fill_n_24" />
                </FrameLayout>

            </FrameLayout>

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>