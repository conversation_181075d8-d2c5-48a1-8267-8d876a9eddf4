<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource">

    <View
        android:id="@+id/main_view_space"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/main_dialog_real_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_dialog_real_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/main_bg_rect_ffffff_131313_top_corner_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_iv_top_card_space" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_top_bg_img"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintDimensionRatio="375:247"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:round="16dp" />

        <Space
            android:id="@+id/main_iv_top_card_space"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="375:88"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Space
            android:id="@+id/main_iv_top_card_right_space"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="16:88"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_top_card_space"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_top_card_space"
            app:layout_constraintTop_toTopOf="@+id/main_iv_top_card_space" />

        <ImageView
            android:id="@+id/main_iv_close"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_margin="@dimen/host_x5"
            android:textFontWeight="600"
            android:src="@drawable/host_ic_x_close_n_line_regular_20"
            android:tint="#99999999"
            app:layout_constraintEnd_toStartOf="@+id/main_iv_top_card_right_space"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/main_tv_title_part_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/host_x24"
            android:layout_marginTop="18dp"
            app:layout_constrainedWidth="true"
            android:gravity="center"
            android:textColor="#2c2c3c"
            android:lines="1"
            android:ellipsize="end"
            android:textSize="17dp"
            android:textFontWeight="600"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_goneMarginEnd="@dimen/host_x24"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_title_part_2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_iv_top_card_space"
            tools:text="免除音贴及" />

        <TextView
            android:id="@+id/main_tv_title_part_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#2c2c3c"
            android:textSize="16dp"
            android:layout_marginHorizontal="3dp"
            android:paddingHorizontal="3dp"
            android:paddingVertical="0.3dp"
            android:includeFontPadding="false"
            android:textStyle="bold"
            android:visibility="gone"
            android:textFontWeight="600"
            app:layout_goneMarginStart="@dimen/host_x24"
            app:layout_goneMarginEnd="@dimen/host_x24"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_title_part_3"
            app:layout_constraintStart_toEndOf="@+id/main_tv_title_part_1"
            app:layout_constraintTop_toTopOf="@+id/main_tv_title_part_1"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_title_part_1"
            tools:text="10+" />

        <TextView
            android:id="@+id/main_tv_title_part_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/host_x24"
            android:gravity="center"
            android:ellipsize="end"
            android:lines="1"
            android:visibility="gone"
            android:textColor="#2c2c3c"
            android:textFontWeight="600"
            app:layout_goneMarginStart="@dimen/host_x24"
            android:textSize="17dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_tv_title_part_2"
            app:layout_constraintTop_toTopOf="@+id/main_tv_title_part_1"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_title_part_1"
            tools:text="收听高打扰广告位" />



        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_cl_platinum_coupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_title_part_1">

            <LinearLayout
                android:id="@+id/main_ll_platinum_vip_origin_price"
                android:layout_width="wrap_content"
                android:layout_height="94dp"
                android:background="@drawable/main_bg_platinum_vip_origin_price2"
                android:gravity="bottom|center_horizontal"
                android:minWidth="125dp"
                android:orientation="vertical"
                android:paddingStart="24dp"
                android:paddingRight="36dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/main_tv_platinum_vip_origin_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="28dp"
                    android:alpha="0.8"
                    android:includeFontPadding="false"
                    android:textColor="#ff5E6076"
                    android:textSize="16dp"
                    tools:text="¥15/月" />

            </LinearLayout>

            <TextView
                android:id="@+id/main_tv_platinum_vip_origin_price_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.5"
                android:background="@drawable/main_bg_platinum_origin_price_tag"
                android:gravity="center"
                android:text="原价"
                android:textColor="#5E6076"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/main_ll_platinum_vip_origin_price"
                app:layout_constraintTop_toTopOf="@+id/main_ll_platinum_vip_origin_price" />

            <LinearLayout
                android:id="@+id/main_ll_platinum_vip_discount_price"
                android:layout_width="wrap_content"
                android:layout_height="124dp"
                android:layout_marginStart="9.5dp"
                android:background="@drawable/main_bg_platinum_discount_price2"
                android:gravity="bottom|center_horizontal"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="@+id/main_iv_arrow_down"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:id="@+id/main_tv_platinum_vip_dicount_price_symbol"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:includeFontPadding="false"
                        android:text="¥"
                        android:textColor="#ff461717"
                        android:textFontWeight="500"
                        android:textSize="30dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/main_tv_platinum_vip_dicount_price"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/main_tv_platinum_vip_dicount_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:includeFontPadding="false"
                        android:textColor="#ff461717"
                        android:textFontWeight="500"
                        android:textSize="38dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/main_tv_platinum_vip_dicount_price_symbol"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="165" />

                    <TextView
                        android:id="@+id/main_tv_platinum_vip_dicount_price_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginTop="6dp"
                        android:includeFontPadding="false"
                        android:textColor="#ff461717"
                        android:textSize="16dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/main_tv_platinum_vip_dicount_price"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/main_tv_platinum_vip_dicount_price"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="/首2月" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/main_tv_platinum_vip_dicount_price_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginBottom="14dp"
                    android:alpha="0.4"
                    android:includeFontPadding="false"
                    android:text="每个设备仅可参与1次"
                    android:textColor="#ff8A7C6D"
                    android:textFontWeight="500"
                    android:textSize="10dp" />
            </LinearLayout>
            <TextView
                android:id="@+id/main_tv_platinum_vip_dicount_price_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                app:layout_constraintTop_toTopOf="@+id/main_ll_platinum_vip_discount_price"
                app:layout_constraintEnd_toEndOf="@+id/main_ll_platinum_vip_discount_price"
                android:text="惊喜价"
                android:gravity="center"
                android:textColor="#ffffff"
                android:background="@drawable/main_bg_platinum_discount_price_tag"
                android:textFontWeight="500"
                android:textSize="16dp" />


            <View
                android:id="@+id/main_view_center_2"
                android:layout_width="22.58dp"
                android:layout_height="1dp"
                app:layout_constraintStart_toEndOf="@+id/main_ll_platinum_vip_origin_price"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/main_iv_arrow_down"
                android:layout_width="59.58dp"
                android:layout_height="82.59dp"
                android:scaleType="fitXY"
                android:src="@drawable/main_ic_platinum_down_arrow2"
                app:layout_constraintEnd_toEndOf="@+id/main_view_center_2"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_action_bg"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="@dimen/host_x40"
            android:layout_marginTop="34dp"
            android:layout_marginRight="@dimen/host_x40"
            android:layout_marginBottom="18dp"
            android:background="@drawable/main_bg_rect_ff4444_corner_100"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_cl_platinum_coupon" />

        <TextView
            android:id="@+id/main_tv_action"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="立即开通白金会员"
            android:textColor="@android:color/white"
            android:textFontWeight="500"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_action_bg"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_action_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_action_bg"
            app:layout_constraintTop_toTopOf="@+id/main_iv_action_bg" />


        <FrameLayout
            android:id="@+id/main_fl_dialog_loading_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="invisible">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_loading_view_progress_xmlottieview"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                app:lottie_fileName="lottie/host_loading/loading.json"
                app:lottie_imageAssetsFolder="lottie/host_loading"
                app:lottie_repeatCount="-1"
                app:lottie_repeatMode="restart" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>