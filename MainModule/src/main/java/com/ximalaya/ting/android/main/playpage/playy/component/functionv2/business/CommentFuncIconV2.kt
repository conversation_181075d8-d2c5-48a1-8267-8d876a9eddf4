package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constants.CommentConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.play.CommentModel
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.ConfigAbUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.commentModule.listener.ITrackCommentBehaviorListener
import com.ximalaya.ting.android.main.commentModule.manager.TrackCommentBehaviorFactory
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.view.CommentAnimationViewV2
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.tabs.biz.comment.YPlayCommentIndependentFragment
import com.ximalaya.ting.android.main.playpage.playy.utils.PlayPageKV
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class CommentFuncIconV2(
    private val fragment: BaseFragment2,
    private val playPageContainer: IPlayContainer
) : IBusinessView(), View.OnClickListener {
    private val TAG = "CommentFuncIcon"

    private var curSoundInfo: PlayingSoundInfo? = null

    private var commentIconView: ImageView? = null
    private var commentCountTextView: TextView? = null

    private var commentDescTextView: TextView? = null

    private var mShowingCommentCount = 0

    private var commentView: CommentAnimationViewV2? = null

    private var job: Job? = null

    private var lastCheckId = 0L

    override fun provideIcon(context: Context, soundInfo: PlayingSoundInfo): View? {
        val iconView = commentView ?: CommentAnimationViewV2(context).apply {
            commentView = this

            <EMAIL> = this.commentIconView
            <EMAIL> = this.commentNumberTextView
            <EMAIL> = this.commentTextView

            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                commentIconView.setColorFilter(PSkinManager.getBtnThemeColor())
                commentCountTextView?.setTextColor(PSkinManager.getBtnThemeColor())
                commentDescTextView?.setTextColor(PSkinManager.getBtnThemeColor())
            }

            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )
            setOnClickListener(this@CommentFuncIconV2)
        }

        updateCommentNum(mShowingCommentCount)

        if (lastCheckId != soundInfo.trackInfo?.trackId) {
            lastCheckId = soundInfo.trackInfo?.trackId ?: 0L
            checkAnimation()
        }
        return iconView
    }

    private fun markShowDays() {
        val currentHistory = PlayPageKV.getYPlayCommentGuideHistory()
        val tsArray = currentHistory?.takeIf { it.isNotBlank() }
            ?.split(",");
        val tsArrayStr = if (tsArray != null && tsArray.size > 2) {
            tsArray.subList(0, 2).filter { it.isNotBlank() }.joinToString(",") { it }
        } else {
            tsArray?.filter { it.isNotBlank() }?.joinToString(",") { it } ?: ""
        }

        Logger.d(TAG, "markShowDays: last is $tsArrayStr")
        if (tsArrayStr.isNullOrEmpty()) {
            PlayPageKV.saveYPlayCommentGuideHistory(
                "${System.currentTimeMillis()}"
            )
        } else {
            PlayPageKV.saveYPlayCommentGuideHistory(
                "${System.currentTimeMillis()}, ${tsArrayStr}"
            )
        }
    }

    private fun canShowGuide(): Boolean {
        val currentHistory = PlayPageKV.getYPlayCommentGuideHistory()
        Logger.d(TAG, "getYPlayCommentGuideHistory = $currentHistory")
        val lastShow = currentHistory?.takeIf { it.isNotBlank() }
            ?.split(",")
            ?.mapNotNull { it.trim().toLongOrNull() }
        if (lastShow.isNullOrEmpty()) {
            Logger.d(TAG, "canShowGuide: lastshow is 0 or null; return true")
            return true
        }
        if (ConstantsOpenSdk.isDebug && BaseUtil.getIntSystemProperties("debug.xima.playpage.no_tab_c") > 0) {
            Logger.d(TAG, "canShowGuide: debug; return true")
            return true
        }

        val firstDay = lastShow.first()
        val isToday = YUtils.calculateDaysBetween(firstDay, System.currentTimeMillis()) == 0L
        if (isToday) {
            Logger.d(TAG, "canShowGuide: today has shown")
            return false
        }

        val configureLimitDays = YUtils.commentGuideDaysLimit()
        val showCountInLimit = lastShow.filter {
            YUtils.calculateDaysBetween(it, System.currentTimeMillis()) < configureLimitDays
        }.size

        Logger.d(TAG, "canShowGuide: $configureLimitDays day has been show $showCountInLimit times")
        return showCountInLimit < 3
    }

    private fun checkAnimation() {
        commentView?.cancelAnimation()
        job?.cancel()
        if (mShowingCommentCount <= 20) return

        job = fragment.lifecycleScope.launch {
            delay(300L)
            if (commentView?.canShowGuide == false) {
                Logger.d(TAG, "canShowGuide: layout limit2; ${commentView?.canShowGuide}")
            } else if (playPageContainer.getCurShowTipConfig() == null && canShowGuide()) {
                commentView?.startAnimation()
                markShowDays()
            }
        }
    }

    override fun providerBottomView(context: Context, soundInfo: PlayingSoundInfo): BottomView? {
        return BottomView(
            TextView(context).apply {
                text = "评论"
                alpha = 0.4f
                textSize = 10f
                isSingleLine = true
            }
        )
    }

    override fun getBottomText(): String {
        return "评论"
    }

    override fun shouldDisplay(soundInfo: PlayingSoundInfo, isAudio: Boolean): Boolean {
        this.curSoundInfo = soundInfo
        updateCommentNum(soundInfo.trackInfo?.comments ?: 0)
        return YUtils.isNoTab(soundInfo) && YUtils.needShowComment(soundInfo.albumInfo?.albumId ?: 0)
    }

    override fun onCreate() {
        TrackCommentBehaviorFactory.newInstance().addManager(mCommentChangeListener)
    }

    override fun onDestroy() {
        TrackCommentBehaviorFactory.newInstance().removeManager(mCommentChangeListener)
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }

        val trackId = curSoundInfo?.trackInfo?.trackId ?: return
        (BaseApplication.getTopActivity() as? MainActivity)?.startFragment(
            YPlayCommentIndependentFragment.newInstanceForTrack(
                CommentConstants.FROM_PAGE_AUDIO,
                trackId, playPageContainer.isVideoMode()
            )
        )
    }

    private fun updateCommentNum(count: Int) {
        mShowingCommentCount = count
        if (mShowingCommentCount > 0) {
            val numStr = StringUtil.getFriendlyNumStr(count)
            commentCountTextView?.text = numStr
            ViewStatusUtil.setVisible(View.VISIBLE, commentCountTextView)

            commentIconView?.setImageResource(getDrawableId(true))

            commentDescTextView?.text = "${numStr} 条评论"
        } else {
            ViewStatusUtil.setVisible(View.INVISIBLE, commentCountTextView)
            commentIconView?.setImageResource(getDrawableId(false))
        }
    }

    private fun getDrawableId(hasCount: Boolean): Int {
        return if (hasCount) {
                R.drawable.main_ic_play_page_comment_with_number
            } else {
                R.drawable.main_ic_play_page_comment
            }
    }

    private val mCommentChangeListener = object : ITrackCommentBehaviorListener {
        override fun requestHandleCommentAdded(
            commentType: Int,
            result: CommentModel?,
            rootCommentModel: CommentModel?,
            notifyFromPage: Int
        ) {
            if (result == null) return
            if (result.trackId != curSoundInfo?.trackInfo?.trackId) return
            if (curSoundInfo?.otherInfo?.allowCommentType != PlayingSoundInfo.OtherInfo.ALLOW_COMMENT_TYPE_LEVEL_1 && !ConfigAbUtil.canAddCommentByTopicWhenNoAllow()) return

            if (commentType == CommentConstants.COMMENT_TYPE_SEND_COMMENT || commentType == CommentConstants.COMMENT_TYPE_REPLY_COMMENT) {
                updateCommentNum(mShowingCommentCount + 1)
            }
        }

        override fun requestHandleCommentDeleted(
            commentType: Int, result: CommentModel?, rootCommentModel: CommentModel?
        ) {
            if (result == null) return
            if (result.trackId != curSoundInfo?.trackInfo?.trackId) return
            if (commentType == CommentConstants.COMMENT_TYPE_SEND_COMMENT || commentType == CommentConstants.COMMENT_TYPE_REPLY_COMMENT) {
                updateCommentNum(
                    (mShowingCommentCount - 1 - (if (result.parentId <= 0) result.replyCount else 0)).coerceAtLeast(
                        0
                    )
                )
            }
        }

        override fun requestHandleCommentModelLikeState(model: CommentModel?, like: Boolean) {}

        override fun requestHandleCommentModelHateState(model: CommentModel?, hate: Boolean) {}

        override fun requestHandleCommentRefresh() {}

        override fun notifyAllManagersHasAd(
            hasColumnLargeCoverAdComponent: Boolean,
            trackId: Long
        ) {
        }

        override fun notifyTrackCommentsLoaded(trackId: Long) {}
    }
}