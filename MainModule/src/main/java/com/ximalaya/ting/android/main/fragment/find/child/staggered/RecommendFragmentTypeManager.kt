package com.ximalaya.ting.android.main.fragment.find.child.staggered

import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.main.adapter.find.recommendStaggered.*
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.main.fragment.find.child.rn.util.SceneStyleUtil
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageGaiaXManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageVirtualViewManager
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendModuleItem
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by changle.fang on 2021/10/26.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
object RecommendFragmentTypeManager {
    const val VIEW_TYPE_DEFAULT = -1
    private var VIEW_TYPE_BASE = 0
    val VIEW_TYPE_LOADING = VIEW_TYPE_BASE++ // 加载中显示图片
    val VIEW_TYPE_NETWORK_ERROR = VIEW_TYPE_BASE++ // 网络错误
    val VIEW_TYPE_ALBUM = VIEW_TYPE_BASE++ // 专辑
    val VIEW_TYPE_TRACK = VIEW_TYPE_BASE++ // 声音
    val VIEW_TYPE_FOCUS = VIEW_TYPE_BASE++ // 焦点图
    private val VIEW_TYPE_PROMOTION_OPERATION = VIEW_TYPE_BASE++ // 大促运营位
    val VIEW_TYPE_TITLE_WITH_CYCLE = VIEW_TYPE_BASE++ // 猜你喜欢 换一批刷新栏 为你推荐
    val VIEW_TYPE_AD_BANNER = VIEW_TYPE_BASE++ // 单双混合-单列中插广告样式，单独占一行
    var VIEW_TYPE_AD_BANNER_REALTIME = VIEW_TYPE_BASE++ // 单双混合-实时化信息流广告
    val VIEW_TYPE_AD_MIX_CARD = VIEW_TYPE_BASE++ // 单双混合-双列小卡片广告样式
    val VIEW_TYPE_AD_MIX_ANCHOR = VIEW_TYPE_BASE++ // 单双混合-猜你喜欢声播广告
    val VIEW_TYPE_CHILD_PROTECT_BANNER = VIEW_TYPE_BASE++ // 青少年模式banner运营
    val VIEW_TYPE_RANK_LIST = VIEW_TYPE_BASE++ // 排行榜
    val VIEW_TYPE_RANK_LIST_NEW = VIEW_TYPE_BASE++ // 新排行榜样式
    val VIEW_TYPE_SOCIAL_LISTEN_LIST = VIEW_TYPE_BASE++ // 社会化听单
    val VIEW_TYPE_SOCIAL_LISTEN_LIST_AD = VIEW_TYPE_BASE++ // 社会化听单-广告
    val VIEW_TYPE_SOCIAL_LISTEN_LIST_PLAYLET_AD = VIEW_TYPE_BASE++ // 社会化听单-短剧广告
    val VIEW_TYPE_HOT_LIVE_LIST = VIEW_TYPE_BASE++ // 热门直播
    val VIEW_TYPE_818_KOL_LIVE_LIST = VIEW_TYPE_BASE++ // 818 重磅大咖直播
    val VIEW_TYPE_VIDEO_LIVE_LIST = VIEW_TYPE_BASE++ // 视频直播
    val VIEW_TYPE_SCENE_LISTEN_CARD = VIEW_TYPE_BASE++ // 场景听
    val VIEW_TYPE_SCENE_LISTEN_CARD_STYLE1 = VIEW_TYPE_BASE++ // 场景听 新样式1
    val VIEW_TYPE_USER_RESEARCH_NEW = VIEW_TYPE_BASE++ // 新版用研 用户调研
    val VIEW_TYPE_HOT_TAGS_2024 = VIEW_TYPE_BASE++ // 继续追听专辑列表 常听分类
    val VIEW_TYPE_TAG_FIND_BOOK = VIEW_TYPE_BASE++ // 标签找书卡  常听分类的另一种形态
    val VIEW_TYPE_USER_RESEARCH_NEW_HEADER = VIEW_TYPE_BASE++ // 新版用研header
    val VIEW_TYPE_CHASING_FOR_UPDATE = VIEW_TYPE_BASE++ // 社会化听单 追更新
    val VIEW_TYPE_MOT_ALBUM_UPDATE_LIST = VIEW_TYPE_BASE++ // MOT关注主播专辑上新、同作者专辑上新、有声书
    val VIEW_TYPE_EXPLOSIVE_CONTENT = VIEW_TYPE_BASE++ // 爆款内容
    val VIEW_TYPE_AD_CENTER_BIG_PIC = VIEW_TYPE_BASE++ // 新版中插大图
    val VIEW_TYPE_AD_CENTER_BIG_FEED = VIEW_TYPE_BASE++ // 新版中插信息流广告
    val VIEW_TYPE_CHILD_IP_LIST = VIEW_TYPE_BASE++ // 儿童ip墙
    val VIEW_TYPE_KID_ALL_LIKE = VIEW_TYPE_BASE++ // 儿童大家都爱听组件
    val VIEW_TYPE_IP_NEW_CONTAINER_LIST = VIEW_TYPE_BASE++ // 重磅热推/平台上新容器
    val VIEW_TYPE_IP_1V4_NEW_CONTAINER_LIST = VIEW_TYPE_BASE++ // 跟重磅热推/平台上新容器样式一致  除了第一个 其他的支持声音样式
    val VIEW_TYPE_TAG_LIST = VIEW_TYPE_BASE++ // 标签墙
    val VIEW_TYPE_IP_CONTAINER_LIST = VIEW_TYPE_BASE++ // 重磅热推  开始跟平台上新样式类似  后面改成社会化听单模式(为了能折叠显示出广告)
    val VIEW_TYPE_INTEREST_CARD = VIEW_TYPE_BASE++ // 兴趣卡片

    val VIEW_TYPE_GUESS_YOU_LIKE_LIST = VIEW_TYPE_BASE++ // 猜你喜欢
    val VIEW_TYPE_ANCHOR_LIST = VIEW_TYPE_BASE++ // 主播卡
    val VIEW_TYPE_FREE_LISTENER = VIEW_TYPE_BASE++ // 免费畅听模块

    val VIEW_TYPE_SHORT_VIDEO_CARD_LIST = VIEW_TYPE_BASE++ // 短视频卡列表
    private val VIEW_TYPE_H5_CARD_LIST = VIEW_TYPE_BASE++ // H5卡列表

    private val VIEW_TYPE_HOT_RANK_LIST = VIEW_TYPE_BASE++ // 热点榜

    private val VIEW_TYPE_SEARCH_CUSTOM_CARD_LIST = VIEW_TYPE_BASE++ // 搜索自定义卡片

    val VIEW_TYPE_SLEEP_AID_CARD = VIEW_TYPE_BASE++ // 睡眠卡

    var mFragment: RecommendFragmentStaggered? = null

    fun getItemViewType(data: Any): Int {
        var itemViewType = VIEW_TYPE_DEFAULT
//        var itemViewType = RecommendPageGaiaXManager.getItemViewType(data)
//        if (itemViewType != VIEW_TYPE_DEFAULT) {
//            return itemViewType
//        }
//        itemViewType = RecommendPageVirtualViewManager.getItemViewType(data)
        if (itemViewType == VIEW_TYPE_DEFAULT) {
            if (data is RecommendItemNew) {
                Logger.d("home_h5", "getItemViewType  itemType: ${data.itemType}");

                itemViewType = when (data.itemType) {
                    RecommendItemNew.RECOMMEND_ITEM_ALBUM -> VIEW_TYPE_ALBUM
                    RecommendItemNew.RECOMMEND_ITEM_TRACK -> VIEW_TYPE_TRACK
                    RecommendItemNew.RECOMMEND_ITEM_CHILD_PROTECT_BANNER -> VIEW_TYPE_CHILD_PROTECT_BANNER
                    RecommendItemNew.RECOMMEND_ITEM_USER_RESEARCH_NEW -> VIEW_TYPE_USER_RESEARCH_NEW
                    RecommendItemNew.RECOMMEND_ITEM_USER_RESEARCH_NEW_HEADER -> VIEW_TYPE_USER_RESEARCH_NEW_HEADER
                    RecommendItemNew.RECOMMEND_ITEM_HOT_TAGS_2024 -> getItemViewTypeFromTagList(data)
                    RecommendItemNew.RECOMMEND_ITEM_RANK_LIST -> getItemViewTypeFromRank(data)
                    RecommendItemNew.RECOMMEND_ITEM_SOCIAL_LISTEN_LIST -> VIEW_TYPE_SOCIAL_LISTEN_LIST
                    RecommendItemNew.RECOMMEND_ITEM_KID_ALL_LIKE -> VIEW_TYPE_KID_ALL_LIKE
                    RecommendItemNew.RECOMMEND_ITEM_SOCIAL_LISTEN_LIST_AD -> getItemViewTypeForSocialListenListAd(data)
                    RecommendItemNew.RECOMMEND_ITEM_HOT_LIVE_LIST -> VIEW_TYPE_HOT_LIVE_LIST
                    RecommendItemNew.RECOMMEND_ITEM_818_KOL_LIVE_LIST -> VIEW_TYPE_818_KOL_LIVE_LIST
                    RecommendItemNew.RECOMMEND_ITEM_VIDEO_LIVE_LIST -> VIEW_TYPE_VIDEO_LIVE_LIST
                    RecommendItemNew.RECOMMEND_ITEM_CHASING_FOR_UPDATE -> VIEW_TYPE_CHASING_FOR_UPDATE
                    RecommendItemNew.RECOMMEND_ITEM_MOT_FOLLOW_UPDATE_LIST -> VIEW_TYPE_MOT_ALBUM_UPDATE_LIST
                    RecommendItemNew.RECOMMEND_ITEM_MOT_AUTHOR_UPDATE_LIST -> VIEW_TYPE_MOT_ALBUM_UPDATE_LIST
                    RecommendItemNew.RECOMMEND_ITEM_EXPLOSIVE_CONTENT -> VIEW_TYPE_EXPLOSIVE_CONTENT
                    RecommendItemNew.RECOMMEND_ITEM_CHILD_IP_LIST -> VIEW_TYPE_CHILD_IP_LIST
                    RecommendItemNew.RECOMMEND_ITEM_SCENE_LISTEN_CARD -> getItemViewTypeFromSceneCard()
                    RecommendItemNew.RECOMMEND_ITEM_AD_CENTER_BIG_PIC -> VIEW_TYPE_AD_CENTER_BIG_PIC
                    RecommendItemNew.RECOMMEND_ITEM_AD_CENTER_BIG_FEED -> VIEW_TYPE_AD_CENTER_BIG_FEED
                    RecommendItemNew.RECOMMEND_TYPE_IP_NEW_CONTAINER_LIST -> getItemViewTypeFromIpNew(data)
                    RecommendItemNew.RECOMMEND_TYPE_IP_1V4_NEW_CONTAINER_LIST -> VIEW_TYPE_IP_1V4_NEW_CONTAINER_LIST
                    RecommendItemNew.RECOMMEND_TYPE_TAG_LIST -> VIEW_TYPE_TAG_LIST
                    RecommendItemNew.RECOMMEND_TYPE_INTEREST_CARD -> VIEW_TYPE_INTEREST_CARD
                    RecommendItemNew.RECOMMEND_ITEM_GUESS_YOU_LIKE_LIST -> VIEW_TYPE_GUESS_YOU_LIKE_LIST
                    RecommendItemNew.RECOMMEND_TYPE_ANCHOR_CARD -> VIEW_TYPE_ANCHOR_LIST
                    RecommendItemNew.RECOMMEND_TYPE_FREE_LISTENER -> VIEW_TYPE_FREE_LISTENER
                    RecommendItemNew.RECOMMEND_ITEM_SHORT_VIDEO_LIST -> VIEW_TYPE_SHORT_VIDEO_CARD_LIST
                    RecommendItemNew.RECOMMEND_ITEM_H5_CARD_LIST -> VIEW_TYPE_H5_CARD_LIST
                    RecommendItemNew.RECOMMEND_ITEM_HOT_RANK_LIST -> VIEW_TYPE_HOT_RANK_LIST
                    RecommendItemNew.RECOMMEND_ITEM_SEARCH_CUSTOM_CARD -> VIEW_TYPE_SEARCH_CUSTOM_CARD_LIST
                    RecommendItemNew.RECOMMEND_ITEM_SLEEP_AID_CARD -> VIEW_TYPE_SLEEP_AID_CARD
                    RecommendItemNew.RECOMMEND_ITEM_MODULE -> getItemViewTypeFromModule(data)
                    else -> VIEW_TYPE_DEFAULT
                }
            } else if (data is ItemModel<*>) {
                itemViewType = data.viewType
            }
        }
        return itemViewType
    }

    fun isNewSceneCard(): Boolean {
        return getItemViewTypeFromSceneCard() == VIEW_TYPE_SCENE_LISTEN_CARD_STYLE1
    }

    // 获取习惯听卡片
    private fun getItemViewTypeFromSceneCard(): Int {
        when (SceneStyleUtil.getSceneStyle()) {
            1 -> {
                return VIEW_TYPE_SCENE_LISTEN_CARD_STYLE1
            }
        }

        return VIEW_TYPE_SCENE_LISTEN_CARD
    }

    /**
     * 重磅热推 平台上新 复用的itemType 用contentType做区分
     */
    private fun getItemViewTypeFromIpNew(data: RecommendItemNew): Int {
        val itemData = data.item as? RecommendCommonItem
        // 重磅热推单独适配新样式
        return if (itemData?.contentType == RecommendItemNew.RECOMMEND_TYPE_IP_NEW_CONTAINER_LIST) {
            VIEW_TYPE_IP_CONTAINER_LIST
        } else {
            // 平台上新样式不变  上面一个大的  下面四个小的
            VIEW_TYPE_IP_NEW_CONTAINER_LIST
        }
    }

    private fun getItemViewTypeForSocialListenListAd(data: RecommendItemNew): Int {
        val itemViewType = VIEW_TYPE_SOCIAL_LISTEN_LIST_AD
        val recommendCommonItem = data.item
        if (recommendCommonItem is RecommendCommonItem && recommendCommonItem.subElements != null && recommendCommonItem.subElements!!.isNotEmpty()) {
            val firstSubElement = recommendCommonItem.subElements!![0]
            if ("Playlet" == firstSubElement.bizType) {
                return VIEW_TYPE_SOCIAL_LISTEN_LIST_PLAYLET_AD
            }
        }
        return itemViewType
    }

    private fun getItemViewTypeFromModule(data: RecommendItemNew): Int {
        var itemViewType = VIEW_TYPE_DEFAULT
        if (data.itemType == RecommendItemNew.RECOMMEND_ITEM_MODULE && data.item is RecommendModuleItem) {
            val moduleItem = data.item as? RecommendModuleItem
            itemViewType = when (moduleItem?.moduleType) {
                RecommendModuleItem.RECOMMEND_TYPE_FOCUS -> VIEW_TYPE_FOCUS
                RecommendModuleItem.RECOMMEND_TYPE_PROMOTION_OPERATION -> VIEW_TYPE_PROMOTION_OPERATION
                else -> VIEW_TYPE_DEFAULT
            }
        }
        return itemViewType
    }

    /**
     * 排行榜 复用的itemType 用displayClass做区分
     */
    private fun getItemViewTypeFromRank(data: RecommendItemNew): Int {
        return if (data.displayClass == "fixedSlice") {
            VIEW_TYPE_RANK_LIST_NEW
        } else {
            VIEW_TYPE_RANK_LIST
        }
    }

    /**
     * 畅听分类 标签找书卡 复用的itemType 用contentType做区分
     */
    private fun getItemViewTypeFromTagList(data: RecommendItemNew): Int {
        return if (data.displayClass == "slideLeft") {
            VIEW_TYPE_TAG_FIND_BOOK
        } else {
            VIEW_TYPE_HOT_TAGS_2024
        }
    }

    fun getTypeAdapterProvider(viewType: Int = VIEW_TYPE_DEFAULT): IMulitViewTypeViewAndDataStaggered<out RecyclerView.ViewHolder, out Any>? {
        val fragment = mFragment ?: return null
        if (viewType == VIEW_TYPE_H5_CARD_LIST) {
            val open = EasyConfigure.getBoolean("open_home_rn_hot_card_0317", true)
            Logger.d("home_h5", "getTypeAdapterProvider  VIEW_TYPE_H5_CARD_LIST, open: $open");
            if (open) {
                return RecommendH5CardListAdapterProviderStaggered(
                    fragment,
                    fragment.mAdapterAction
                )
            }
        }
        return when (viewType) {
            VIEW_TYPE_LOADING -> RecommendLoadingAdapterProviderStaggered()
            VIEW_TYPE_ALBUM -> RecommendAlbumNewAdapterProviderStaggered(
                fragment,
                fragment.mAdapterAction,
                fragment.mRealTimeFeedListener
            )
            VIEW_TYPE_TRACK -> RecommendTrackNewAdapterProviderStaggered(
                fragment,
                fragment.mAdapterAction,
                fragment.mRealTimeFeedListener
            )
            VIEW_TYPE_FOCUS -> fragment.createFocueAdapter()
            VIEW_TYPE_PROMOTION_OPERATION -> RecommendPromotionOperationAdapterProviderStaggered(fragment.mAdapterAction)
            VIEW_TYPE_TITLE_WITH_CYCLE -> RecommendTitleWithCycleAdapterProviderStaggered(fragment)
            VIEW_TYPE_AD_MIX_ANCHOR -> RecommendAnchorAdProvider(fragment, fragment.mAdapterAction, fragment.mFeedAnchorAdManager)
            VIEW_TYPE_AD_BANNER -> RecommendBannerAdProvider(fragment, fragment.mAdapterAction, fragment.mFeedAdManager)
            VIEW_TYPE_AD_BANNER_REALTIME -> RecommendBannerAdRealTimeProvider(fragment, fragment.mAdapterAction, fragment.mFeedAdManager)
            VIEW_TYPE_CHILD_PROTECT_BANNER -> RecommendChildProtectBannerProviderStaggered(fragment)
            VIEW_TYPE_USER_RESEARCH_NEW -> RecommendUserResearchNewAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_USER_RESEARCH_NEW_HEADER -> RecommendUserResearchHeaderAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_RANK_LIST -> RecommendRankListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_RANK_LIST_NEW -> NewRecommendRankListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_SOCIAL_LISTEN_LIST -> RecommendSocialListenListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_KID_ALL_LIKE -> RecommendKidAllLikeAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_SOCIAL_LISTEN_LIST_AD -> RecommendSocialListenListAdAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_SOCIAL_LISTEN_LIST_PLAYLET_AD -> RecommendPlayLetAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_HOT_LIVE_LIST -> RecommendHotLiveListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_818_KOL_LIVE_LIST -> Recommend818KolLiveListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_VIDEO_LIVE_LIST -> RecommendVideoLiveListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_CHASING_FOR_UPDATE -> RecommendChasingForUpdateListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_EXPLOSIVE_CONTENT -> RecommendExplosiveContentAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_CHILD_IP_LIST -> RecommendChildIpListAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_SCENE_LISTEN_CARD -> RecommendSceneListenCardAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_SCENE_LISTEN_CARD_STYLE1 -> RecommendSceneListenCardAdapterProviderStaggeredStyle1(fragment, fragment.mAdapterAction)
            VIEW_TYPE_HOT_TAGS_2024 -> RecommendTagList2024AdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_TAG_FIND_BOOK -> RecommendTagFindBookAdapterProviderStaggered(fragment, fragment.mAdapterAction)
            VIEW_TYPE_AD_CENTER_BIG_PIC -> RecommendCenterBigPicAdProvider(fragment, fragment.mAdapterAction, fragment.mCenterBigFeedAdManager, fragment.mCenterBigPicAdManager)
            VIEW_TYPE_AD_CENTER_BIG_FEED -> RecommendCenterBigFeedAdProvider(fragment, fragment.mAdapterAction, fragment.mCenterBigFeedAdManager, fragment.mCenterBigPicAdManager)
            VIEW_TYPE_IP_NEW_CONTAINER_LIST -> RecommendIpNewContainerAdapterProviderStaggered(
                fragment, false, fragment.mAdapterAction
            )

            VIEW_TYPE_IP_1V4_NEW_CONTAINER_LIST -> RecommendIpNewContainerAdapterProviderStaggered(
                fragment, true, fragment.mAdapterAction
            )

            VIEW_TYPE_TAG_LIST -> RecommendTagListAdapterProviderStaggered(
                fragment, fragment.mAdapterAction
            )

            VIEW_TYPE_IP_CONTAINER_LIST -> RecommendIpContainerAdapterProviderStaggered(
                fragment, fragment.mAdapterAction
            )

            VIEW_TYPE_GUESS_YOU_LIKE_LIST -> RecommendGuessYouLikeAdapterProviderStaggered(
                fragment, fragment.mAdapterAction
            )
            VIEW_TYPE_INTEREST_CARD -> RecommendInterestCardAdapterProviderStaggered(
                fragment,
                fragment.mAdapterAction
            )
            VIEW_TYPE_ANCHOR_LIST -> RecommendAnchorListAdapterProviderStaggered(
                fragment,
                fragment.mAdapterAction
            )
            VIEW_TYPE_FREE_LISTENER -> NewRecommendFreeListenerAdapterProviderStaggered(
                fragment,
                fragment.mAdapterAction
            )
            VIEW_TYPE_SHORT_VIDEO_CARD_LIST -> RecommendShortVideoListAdapterProviderStaggered(
                fragment,
                fragment.mAdapterAction
            )
            VIEW_TYPE_HOT_RANK_LIST -> RecommendHotRankAdapterProviderStaggered(fragment)

            VIEW_TYPE_SEARCH_CUSTOM_CARD_LIST -> RecommendSearchCustomCardAdapterProvider(fragment, fragment.mAdapterAction)

            VIEW_TYPE_SLEEP_AID_CARD -> RecommendSleepAidAdapterProvider(fragment, fragment.mAdapterAction)

            else -> {
//                if (RecommendPageVirtualViewManager.isTypeOfVirtualView(viewType)) {
//                    VirtualViewAdapterProvider(viewType, fragment.mAdapterAction)
//                } else if (RecommendPageGaiaXManager.isTypeOfVGaiaxView(viewType)) {
//                    GaiaXViewAdapterProvider(viewType, fragment.mAdapterAction)
//                }
//                 else {
//                }
                null
            }
        }
    }
}