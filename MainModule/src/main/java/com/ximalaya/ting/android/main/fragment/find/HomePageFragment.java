package com.ximalaya.ting.android.main.fragment.find;

import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_ACTIVITY;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_CATEGORY;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_FOLLOW;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_H5;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_LAMIA;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_SINGLE_CATEGORY;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_TEMPLATE_CATEGORY;
import static com.ximalaya.ting.android.host.model.homepage.HomePageTabModel.ITEM_TYPE_VIDEO_TAB;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ArgbEvaluator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.PorterDuff;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager.widget.ViewPagerCanDisableFillNeighbourTab;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieCompositionFactory;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.host.manager.ai.AiAgentTipManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.downloadservice.base.IDownloadTaskCallback;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter.FragmentHolder;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.reflect.FieldUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.chatxmly.ChatXmlyPopupManager;
import com.ximalaya.ting.android.host.chatxmly.IChatXmlyPopup;
import com.ximalaya.ting.android.host.chatxmly.util.ChatXmlyRedDotUtilKt;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.data.model.message.RequestError;
import com.ximalaya.ting.android.host.data.request.HomePageTabRequestTask;
import com.ximalaya.ting.android.host.fragment.BaseHomePageTabFragment;
import com.ximalaya.ting.android.host.fragment.MainBottomTabProvider;
import com.ximalaya.ting.android.host.fragment.ad.SplashUnitAdUtil;
import com.ximalaya.ting.android.host.fragment.play.PlayBarAbManager;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.ISearchHintCallback;
import com.ximalaya.ting.android.host.listener.ISearchHintUpdate;
import com.ximalaya.ting.android.host.listener.IVipStatusChangeListener;
import com.ximalaya.ting.android.host.manager.AgentConfig;
import com.ximalaya.ting.android.host.manager.BottomTabFragmentManager;
import com.ximalaya.ting.android.host.manager.ChildProtectDialogManager;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipChangeListener;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManagerKt;
import com.ximalaya.ting.android.host.manager.DummyUserGuideManager;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.EmergencyPlanManager;
import com.ximalaya.ting.android.host.manager.EventManager;
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager;
import com.ximalaya.ting.android.host.manager.ISceneNewCardChangeListener;
import com.ximalaya.ting.android.host.manager.ISkinSettingChangeListener;
import com.ximalaya.ting.android.host.manager.LivePersonalCenterReminderManager;
import com.ximalaya.ting.android.host.manager.LiveStartReminderManager;
import com.ximalaya.ting.android.host.manager.NewUserRightsDialogManager;
import com.ximalaya.ting.android.host.manager.SimpleMediaPlayer;
import com.ximalaya.ting.android.host.manager.SkinManager;
import com.ximalaya.ting.android.host.manager.SkinSettingChangeWrapListener;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.TopBgForSceneManager;
import com.ximalaya.ting.android.host.manager.UIConsistencyManager;
import com.ximalaya.ting.android.host.manager.account.LoginUtil;
import com.ximalaya.ting.android.host.manager.account.NoReadManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.account.XmLocationManager;
import com.ximalaya.ting.android.host.manager.activity.reddot.IRedDotAction;
import com.ximalaya.ting.android.host.manager.activity.reddot.LiveRedDotPosition;
import com.ximalaya.ting.android.host.manager.activity.reddot.RedDotManage;
import com.ximalaya.ting.android.host.manager.activity.reddot.SignRedDotPosition;
import com.ximalaya.ting.android.host.manager.ad.AdIvRecordManager;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.RewardAgainAdManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.kids.KidsPage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.speechrecognition.ISpeechRecognitionFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.KidActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.kidmode.CategoryRecommendKidEntryManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager;
import com.ximalaya.ting.android.host.model.EmergencyPlan;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.chatxmly.ChatXmlyTips;
import com.ximalaya.ting.android.host.model.homepage.CustomTheme;
import com.ximalaya.ting.android.host.model.homepage.HomePageTabModel;
import com.ximalaya.ting.android.host.model.homepage.HomePageTabTheme;
import com.ximalaya.ting.android.host.model.search.SearchHotWord;
import com.ximalaya.ting.android.host.model.skin.AtmosphereInfo;
import com.ximalaya.ting.android.host.model.skin.DeviceConfigs;
import com.ximalaya.ting.android.host.service.DriveModeBluetoothManager;
import com.ximalaya.ting.android.host.util.CategoryCardUtil;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.RefreshHomeChannelUtils;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.SpeechRecognitionRouterUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.other.PermissionManage;
import com.ximalaya.ting.android.host.util.performance.PageStartOpt;
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor;
import com.ximalaya.ting.android.host.util.startup.StartupOptManager;
import com.ximalaya.ting.android.host.util.startup.ViewPool;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.vip.VipHostUtil;
import com.ximalaya.ting.android.host.view.BadgeView;
import com.ximalaya.ting.android.host.view.BannerView;
import com.ximalaya.ting.android.host.view.BaseBannerView;
import com.ximalaya.ting.android.host.view.CustomTipsView;
import com.ximalaya.ting.android.host.view.ListenScrollStatePagerSlidingTabStrip;
import com.ximalaya.ting.android.host.view.RedDotView;
import com.ximalaya.ting.android.host.view.ad.ShowPairImageView;
import com.ximalaya.ting.android.host.view.ad.ShowReversePairImageView;
import com.ximalaya.ting.android.host.widget.XmWidgetUtil;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.HomePageTabAdapter;
import com.ximalaya.ting.android.main.adapter.find.util.ShortVideoCoverPlayUtil;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryListV2Fragment;
import com.ximalaya.ting.android.main.categoryModule.page.EBookChannelFragment;
import com.ximalaya.ting.android.main.categoryModule.page.tab.HomePageTabAndChannelListNewFragmentDialog;
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.android.main.dialog.HomePageSearchBarMoreActionDialog;
import com.ximalaya.ting.android.main.dialog.VipProtocolDialogFragment;
import com.ximalaya.ting.android.main.fragment.dialog.FreshGiftFragment;
import com.ximalaya.ting.android.main.fragment.find.child.BigScreenAdManager;
import com.ximalaya.ting.android.main.fragment.find.child.PodCastFragmentV2;
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentAdUtil;
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentNew;
import com.ximalaya.ting.android.main.fragment.find.child.SecondFloorFragment;
import com.ximalaya.ting.android.main.fragment.find.child.rn.RecommendRnFragment;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentAdNewUtil;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildPlatformFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildProtectionSettingFragment;
import com.ximalaya.ting.android.main.manager.FreeListenIconManager;
import com.ximalaya.ting.android.main.manager.ITingHandler;
import com.ximalaya.ting.android.main.manager.RecommendAlbumManager;
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
import com.ximalaya.ting.android.main.manager.SearchHotWordSwitchManager;
import com.ximalaya.ting.android.main.manager.homepage.HomeMineMoreTagManager;
import com.ximalaya.ting.android.main.manager.homepage.HomePageFragmentPresenter;
import com.ximalaya.ting.android.main.manager.homepage.HomePageRedDotManager;
import com.ximalaya.ting.android.main.manager.homepage.IHomePageFragmentManagerProvider;
import com.ximalaya.ting.android.main.manager.newUser.AfterListenTaskNotificationManager;
import com.ximalaya.ting.android.main.manager.soundpatch.SoundPatchMainManager;
import com.ximalaya.ting.android.main.model.SignGuideAnimModel;
import com.ximalaya.ting.android.main.model.SignInfoModel;
import com.ximalaya.ting.android.main.model.VipProtocolRsp;
import com.ximalaya.ting.android.main.playModule.dialog.PlayInterrupttedPopFragment;
import com.ximalaya.ting.android.main.playModule.helper.DoShowInterruptViewHelper;
import com.ximalaya.ting.android.main.readerModule.util.ReaderConstants;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.FixedThreadPool;
import com.ximalaya.ting.android.main.util.HomeSendRnEventUtils;
import com.ximalaya.ting.android.main.util.MyListenAbUtil;
import com.ximalaya.ting.android.main.util.MyListenGuideUtil;
import com.ximalaya.ting.android.main.util.mine.ModeSwitchUtil;
import com.ximalaya.ting.android.main.util.other.HomePageTabModelUtil;
import com.ximalaya.ting.android.main.util.other.RecordPermissionReportUtil;
import com.ximalaya.ting.android.main.util.other.ShoppingCartUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.main.view.BottomOvalView;
import com.ximalaya.ting.android.main.view.PullToRefreshStaggeredRecyclerView;
import com.ximalaya.ting.android.main.view.other.EmergencyAnnouncementView;
import com.ximalaya.ting.android.main.view.other.MagneticView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.IOnAppStatusChangedListener;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


/**
 * ClassName:HomeFragment Function: MainActivity下的"首页"tab
 *
 * <AUTHOR>
 * 2015-11-11 下午7:48:28
 * @since Ver 1.1
 */
public class HomePageFragment extends IMainFunctionAction.AbstractHomePageFragment implements OnClickListener,
        Router.IBundleInstallCallback, HomePageTabAdapter.IUserChange, ISearchHintUpdate,
        SearchHotWordSwitchManager.ISearchBarProvider,
        LivePersonalCenterReminderManager.IReminderListener, ILoginStatusChangeListener, IVipStatusChangeListener,
        LiveStartReminderManager.ILiveAnchorStatusListener, IHomePageFragmentManagerProvider, MainBottomTabProvider,
        HomeRecommendPageLoadingOptimizationManager.IAdShowAndDestroyListener{
    private static final String TAG = HomePageFragment.class.getSimpleName();
    public static final int CATEGORY_PAID = 33;
    private static String mDefaultPageType = HomePageTabModel.ITEM_TYPE_RECOMMEND;//默认首页

    private static final long SEARCH_BAR_ANIMATION_DURATION = 200;
    private static final int NOT_DESTROY_TAB_NUM = 5; // 前5个tab不销毁
    private static String mXmRequestId = "";

    private ListenScrollStatePagerSlidingTabStrip mTabs;
    private ViewPagerCanDisableFillNeighbourTab mPager;
    private HomePageTabAdapter mPagerAdapter;
    private List<HomePageTabModel> mTabModelList;
    private boolean isFirstLoaded = true;
    private String mIntentTab = "";
    private ImageView mIvEditTab;
    private View mVEditTabShadow;
    private BottomOvalView mBottomOvalView;
    private ImageView mTopBgView;
    private ImageView mIvAtmosphereBg;
    private ImageView mIvAtmosphereIcon;
    private ImageView mIvTopBgForSceneInDark;
    private int myBgColor = Color.TRANSPARENT;
    private ArgbEvaluator argbEvaluator = new ArgbEvaluator();
    private View mVUnderageModeTop;
    private TextView mVUnderageModeSelectedAge;
    private TextView mVUnderageModeExit;
    private String mSelectTab = "";

    @Nullable
    private BadgeView mVRedDotCountOnRecommendPage;
    @Nullable
    private RedDotView mVPureRedDotOnRecommendPage;
    @Nullable
    private BadgeView mVRedDotCountOnQualityPage;
    @Nullable
    private RedDotView mVPureRedDotOnQualityPage;

    private HomePageTabModel mPendingSwitchToTab; // 等待切换到tab，在tab更新完成之后切换
    private String mPendingSwitchToTabId; // 等待切换到tab，在tab更新完成之后切换
    private boolean mIsFirstLoadTab = true; // 是否是第一次加载tab，第一次加载tab要根据配置跳转到指定的tab
    private int mDefaultTabColor = BannerModel.DEFUALT_COLOR;
    private int mHasFixLiveTab; // 解决第一次安装导致默认选中了直播tab的问题

    // 搜索栏相关
    private ViewGroup mVgSearchBar;
    private ViewGroup mVgSearchBarRightAction;

    private TextView mTvSearchBarRightActionBtn;
    private ViewGroup mVgSearchBarRecommendPageAction;
//    private ImageView mIvSearchBarHistoryBtn;
    private ImageView mIvSearchBarSign;
    private ImageView mIvSearchBarSignEffect;
    private ImageView mIvSignRedDot; // 签到小红点
    private View mVMoreAction;
    private View mSlideEntry;
    private TextView mSlideRedDot;
    private ImageView mIvSearchBarMoreActionBtn;
    private TextView mTvMoreHot;
    private View mViewMoreHotDot;
    private ViewGroup mVgSearchBarLivePageAction;
    private ImageView mIvSearchBarLiveStartBtn;  // 搜索栏右侧搜索tab开始直播按钮
    private ImageView mIvSearchBarLiveMineBtn;   // 搜索栏右侧搜索tab个人中心按钮
    private ViewGroup mVgSearchBarVipPageAction;
    private ImageView mIvSearchBarVipBtn;
    private ImageView mIvMineReminder;
    private ImageView mIvLiveReminder;
    private ImageView mIvSearchRecognizeBtn; // 语音识别按钮
    private ImageView mIvVoiceSearchRedDot; // 语音搜索按钮小红点
    private ViewGroup mVgSearchBarQualityPageAction;
    private ImageView mIvSearchBarQualityCartBtn;

    private ImageView mIvSearchBarIcon;

    private int mCurViewPageScrollState;

    private int lastColor = Color.TRANSPARENT;
    private int lastTopRectColor = Color.TRANSPARENT;
    private boolean mIsDarkStatusBar = true;
    private int mLastUpdateSearchBarPosition = -1;
    private AnimatorSet mSearchBarAnimator;

    private ImageView mDropDownAdImg;
    private ImageView mAdTag;
    @Nullable
    private ShowPairImageView mTwoStyleImgView; // 二楼下拉广告
    private ShowReversePairImageView mReversePairImageView; // 联合霸屏包框部分

    public static final String TITLE_BAR_ALPHA_CHANGE_ACTION = "title_bar_alpha_change_action";
    public static final String TITLE_BAR_ALPHA_CHANGE_DATA = "title_bar_alpha_change_data";
    public static final String TITLE_BAR_ALPHA_CHANGE_DROP_DOWN_LENGTH =
            "title_bar_alpha_change_drop_down_length";
    public static final String TITLE_BAR_ALPHA_CHANGE_RESET = "title_bar_alpha_change_reset";
    public static final String TITLE_BAR_SIGN = "RNCreditCenter_SignInSuccess";//月票需求->签到->rn页面签到成功后发送的广播
    public static final String TITLE_BAR_CREDIT_CHANGE = "RNCreditCenter_CreditChange"; // rn页面积分变化后发送的广播

    public static final String DROP_DOWN_PROPORTION_CHANGE_ACTION =
            "drop_down_proportion_change_action";
    public static final String SPLASH_UNIT_PACK_BG_ACTION =
            "splash_unit_pack_bg_action";   // 开屏联合霸屏包框显示了

    public static final String TITLE_BAR_COLOR_CHANGE_ACTION =
            "title_bar_color_change_action";

    public static boolean hasDownloadedDropDownAd = false;    // 是否已经下载好下拉广告
    public static String mDropDownAdUrl;
    public static Advertis mDropDownAdvertis;
    private int titleBarAndSearchHeight;
    public static boolean isGoingSecondFloor = false;

    public static final int ANIMATION_TIME = 800;
    private LoadTabTask mLoadTabTask;

    private boolean mHasLocalListenTab;
    private EmergencyAnnouncementView mEmergencyAnnouncementView;

    private HomePageFragmentPresenter mPresenter;
    private HomePageRedDotManager mRedDotManager;
    private SearchHotWordSwitchManager mSearchHotWordSwitchManager;
    private boolean mIsInUnderageMode; // 是否处于青少年模式状态
    private boolean isLogin = false;
    private SignInfoModel mSignInfoModel;
    private boolean mNeedRequestSignInfo = true;
    private boolean isVipChangeTriggered = false;
    private boolean mUseSkinSettingBgColor; // 使用皮肤包配置的背景色
    private int mSkinSettingBgColor;
    private boolean mFirstUpdateSearchBar = true; // 启动后首次更新搜索栏位置，不做动画，避免卡顿
    private static final int DEFAULT_COLOR_SEARCH = 0xffff4646; // 其它频道搜索icon颜色
    private static final int DEFAULT_COLOR_SEARCH_RECOMMEND = 0x662c2c3c; // 推荐频道搜索icon颜色
    private static final int DEFAULT_COLOR_RECOGNIZE = 0xff666666; // 其它频道语音识别icon颜色
    private static final int DEFAULT_COLOR_RECOGNIZE_DARK = 0xffcfcfcf; // 其它频道语音识别icon颜色 夜间
    private boolean isGotoEditTab;
    private boolean isWaittingTabScroll;
    private int mEditIconLeftX = BaseUtil.getScreenWidth(mContext) - BaseUtil.dp2px(mContext, 60);
    private boolean isLoadDataAfterBundleInstall;
    private int mBannerHeight = BannerView.getCustomBannerHeight(mContext) + BaseUtil.dp2px(mContext, 16);
    private long mOpenSpeechRecognitionTime = 0L;
    private boolean mSignIconUseColorFilter = true;
    private int mLiveModleIndex = -1;
    private boolean mHasRequestLiveRedDotOnInit = false;
    private SignRedDotPosition mSignRedDotPosition;
    private LiveRedDotPosition mLiveRedDotPosition;
    private boolean mIsColdBoot = true;
    private IChatXmlyPopup mChatXmlyPopupWindow;
    private @Nullable ChatXmlyTips.ChatXmlyTipsBean mTipsBeanBringIntoChatXmlyPopup;
    private LottieAnimationView mLottieChatXmlyBubbleRobot;
    private TextView mTvChatXmlyBubble;
    private ValueAnimator mPoppyEndAnimator;
    private ValueAnimator mPoppyStartAnimator;
    private boolean mHasShowChildXmlyAnimation;
    private Animator mChatXmlyBubbleShowAnimator;
    private Animator mChatXmlyBubbleHideAnimator;
    private RelativeLayout mRlContainer;
    private FreeListenIconManager mFreeListenIconManager;
    private RelativeLayout  mRlSignVg;
    private List<HomePageTabModel> mCacheHomePageTabs = new ArrayList<>();

    private boolean mIsTopBgShown = false;

    private boolean agentStyleChange = false;

    private IOnAppStatusChangedListener mOnAppStatusChangedListener = new IOnAppStatusChangedListener() {
        @Override
        public void onForeground(Intent it) {
            checkLiveTabRedDot();
            if (!mIsColdBoot) {
                RedDotManage.getInstance().uploadUbt();
            }
            mIsColdBoot = false;
        }

        @Override
        public void onBackground(Intent it) {
            RedDotManage.getInstance().setIsNeedUbtSend(true);
        }
    };

    private void findLiveTabIndex() {
        if (mTabModelList != null && mTabModelList.size() > 0) {
            int index = -1;
            for (HomePageTabModel homePageTabModel : mTabModelList) {
                if (homePageTabModel != null && HomePageTabModel.ITEM_TYPE_LAMIA.equals(homePageTabModel.getItemType())) {
                    index = mTabModelList.indexOf(homePageTabModel);
                    break;
                }
            }
            if (index > -1) {
                mLiveModleIndex = index;
            }
        }
    }

    @Override
    public void onSearchBarRecognizeIconClicked(View view, SearchHotWord searchHotWord) {
        new UserTracking().setItem("button").setSrcModule("搜索条").setItemId("searchVoice").setSrcPage("首页").setId("6012").statIting(
                XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        boolean useChatXmly = ChatXmlyPopupManager.INSTANCE.useChatXmlyVoiceAssistant();
        if (useChatXmly) {
            ChatXmlyPopupManager.INSTANCE.checkRecordPermission(getContext(), () -> {
                try {
                    if (getContext() != null && this.isRealVisable() && mVgSearchBar != null) {
                        mChatXmlyPopupWindow = ChatXmlyPopupManager.INSTANCE.newChatXmlyPopup(getActivity(), "首页", mTipsBeanBringIntoChatXmlyPopup, false);
                        mChatXmlyPopupWindow.show(mVgSearchBar, BaseUtil.dp2px(getContext(), 6));
                        hideChatXmlyBubble();
                        ChatXmlyRedDotUtilKt.markRedDotClick(getContext());
                        if (mIvSearchRecognizeBtn != null) {
                            showOrHideSearchRecognizeBtn(false);
                            mChatXmlyPopupWindow.addOnDismissListener(() -> {
                                mChatXmlyPopupWindow = null;
                                if (canUpdateUi() && mIvSearchRecognizeBtn != null) {
                                    showOrHideSearchRecognizeBtn(true);
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } else {
            try {
                final long openTime = SystemClock.elapsedRealtime();
                mOpenSpeechRecognitionTime = openTime;
                SpeechRecognitionRouterUtil.getBundle(bundleModel -> {
                    ISpeechRecognitionFragmentAction fraAction = SpeechRecognitionRouterUtil.getFragAction();
                    if (fraAction != null && canUpdateUi() && openTime == mOpenSpeechRecognitionTime) {
                        String iTing = "iting://open?msg_type=94&bundle=rn_assistant&reuse=true&type=0";
                        new ITingHandler().handleITing(mActivity, Uri.parse(iTing));
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void showOrHideSearchRecognizeBtn(boolean show) {
        if (mIvSearchRecognizeBtn == null) {
            return;
        }
        if (ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
            show = false;
        }
        mIvSearchRecognizeBtn.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
        updateVoiceSearchRedDot();
    }

    private void updateVoiceSearchRedDot() {
        boolean show = mIvSearchRecognizeBtn != null && mIvSearchRecognizeBtn.getVisibility() == View.VISIBLE && ChatXmlyRedDotUtilKt.canShowRedDot(mContext);
        mIvVoiceSearchRedDot.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void onSearchBarClicked(View view, SearchHotWord searchHotWord) {
        new UserTracking("首页", "搜索条")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);
        String strategy = searchHotWord != null ? searchHotWord.getAbInfo() : "";
        String keyWord = searchHotWord != null ? searchHotWord.getSearchWord() : "";
        String busId = searchHotWord != null ? searchHotWord.getBusId() : "";
        String productId = searchHotWord != null ? searchHotWord.getProductId() : "";
        String requestId = searchHotWord != null ? searchHotWord.getRequestId() : "";
        String xmRequestId = searchHotWord != null ? searchHotWord.getXmRequestId() : "";
        String searchWordType = searchHotWord != null ? String.valueOf(searchHotWord.getDisplayType()) : "";
        new XMTraceApi.Trace()
                .click(16776, "search")
                .put("tabName", getCurrentTabName())
                .put("categoryId", String.valueOf(getCurrentTabCategoryId()))
                .put("displayWord", keyWord)
                .put("currPage", "categoryRecommend")
                .put("strategy", strategy)
                .put("busId", busId)
                .put("productId", productId)
                .put("requestId", requestId)
                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                .createTrace();
        new XMTraceApi.Trace()
                .click(40434)
                .put("type", "框底词")
                .put("searchWord", "")
                .put("currPage", "首页")
                .put("strategy", strategy)
                .put("keyWord", keyWord)
                .put("searchWordType", searchWordType)
                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                .createTrace();
        try {
            int categoryId = Integer.MIN_VALUE;
            if (mTabs != null) {
                int index = mTabs.getCurrentItem();
                if (!ToolUtil.isEmptyCollects(mTabModelList) && 0 < index && index < mTabModelList.size()) {
                    HomePageTabModel homePageTabModel = mTabModelList.get(index);
                    categoryId = homePageTabModel == null ? 0 : homePageTabModel.getCategoryId();
                    String categoryName = null == homePageTabModel ? null : homePageTabModel.getId();
                    if ("vip".equals(categoryName)) {
                        VipHostUtil.markPointOnSearchBarClicked();
                    }
                }
            }
            BaseFragment fragment = Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ? Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchFragment(searchHotWord, categoryId) : null;
            if (fragment != null) {
                startFragment(fragment);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public View initSearchBar() {
        return findViewById(R.id.main_tv_search);
    }

    @Override
    public void showReminder(boolean showDialog) {
        if (showDialog) {
            //已经显示过签到弹窗，这里就显示红点
            ViewGroup.LayoutParams layoutParams = mIvMineReminder.getLayoutParams();
            int size = BaseUtil.dp2px(mContext, 6);
            layoutParams.width = layoutParams.height = size;
            mIvMineReminder.setLayoutParams(layoutParams);
            mIvMineReminder.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_ic_red_dot_normal);
        }
        mIvMineReminder.setVisibility(View.VISIBLE);
    }

    @Override
    public void showSigned(boolean signed) {
        if (signed) {
            mIvMineReminder.setVisibility(View.GONE);
        }
    }

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {
        isLogin = false;
        isVipChangeTriggered = false;
        mIvLiveReminder.setVisibility(View.GONE);
        if (mIvSearchBarSign.getVisibility() == View.VISIBLE) {
            mIvSearchBarSign.setImageResource(R.drawable.main_ic_sign_no_n_line_regular_24);
        }
        ChildXmlyTipManager.INSTANCE.getAgentConfigWithOldUserUpdate();
    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        isLogin = true;
        mNeedRequestSignInfo = true;
        ChildXmlyTipManager.INSTANCE.getAgentConfigWithOldUserUpdate();
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
        mNeedRequestSignInfo = true;
    }

    @Override
    public void onVipStatusChanged(boolean isVip) {
        if (isVip && !isVipChangeTriggered) {
            isVipChangeTriggered = true;
            tryShowVipProtocolDialog();
        }
    }

    @Override
    public void showStartLiveRedDot(boolean show) {
        mIvLiveReminder.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onAdClick() {
    }

    private boolean mHasLoadCacheTabs;
    @Override
    public void onAdDestroy() {
        if (!ToolUtil.isEmptyCollects(mCacheHomePageTabs) && !mHasLoadCacheTabs) {
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("__onAdDestroy___loadCacheTabs");
            mHasLoadCacheTabs = true;
            setTabData(mCacheHomePageTabs);
        }
    }

    private enum HEADER_VIEW_COLOR {
        STATUS_BAR(1, 0, 0), // 状态栏不能指定色值，所以直接用数字代替，1表示黑色文字，0表示白色文字
        ACTIVE_TAB(0xff2c2c3c, Color.WHITE, 0xffD8BDA6), // 选中的tab
        DEACTIVATE_TAB(0x8c2c2c3c, Color.WHITE, 0xffDFBB9C), // 未选中的tab
        TAB_INDICATOR(0xffF86442, 0x99ffffff, 0xffD8BDA6),
        SEARCH_BAR_BG(0xfff3f4f5, 0xe6ffffff, 0xffD8BDA6),
        SEARCH_ICON(0xff666666, Color.BLACK, Color.BLACK),
        SEARCH_TEXT(0xff666666, Color.BLACK, Color.BLACK),
        SEARCH_BAR_RIGHT_ACTION_BTN_BG(0xfff3f4f5, 0x33ffffff, 0xffD8BDA6),
        SEARCH_BAR_RIGHT_ACTION_BTN_ICON(0xff666666, Color.WHITE, Color.BLACK),
        SEARCH_BAR_RIGHT_ACTION_BTN_TEXT(0xff666666, Color.WHITE, Color.BLACK),
        SEARCH_BAR_OTHER_BTN(0xff2c2c3c, Color.WHITE, 0xffD8BDA6),
        EDIT_TAB_PAGE_ENTRY(0xff444444, Color.WHITE, 0xffD8BDA6),
        SEARCH_BTN_TEXT(Color.WHITE, Color.WHITE, Color.WHITE),
        SEARCH_BTN_BG(ColorUtil.INVALID_COLOR, ColorUtil.INVALID_COLOR, ColorUtil.INVALID_COLOR);

        // 白天模式  用黑色样式icon
        private final int mBlackThemeColor;
        private int mWhiteThemeColor;
        private final int mGoldenThemeColor;

        HEADER_VIEW_COLOR(int blackThemeColor, int whiteThemeColor, int goldenThemeColor) {
            mBlackThemeColor = blackThemeColor;
            mWhiteThemeColor = whiteThemeColor;
            mGoldenThemeColor = goldenThemeColor;
        }

        void setWhiteThemeColor(int whiteThemeColor) {
            mWhiteThemeColor = whiteThemeColor;
        }

        public int getWhiteThemeColor() {
            return mWhiteThemeColor;
        }

        int getColor(String theme, CustomTheme customTheme) {
            int themeColor = mBlackThemeColor;
            if (!TextUtils.isEmpty(theme)) {
                switch (theme) {
                    case HomePageTabTheme.FOREGROUND_COLOR_BLACK:
                        themeColor = mBlackThemeColor;
                        break;
                    case HomePageTabTheme.FOREGROUND_COLOR_WHITE:
                        themeColor = mWhiteThemeColor;
                        break;
                    case HomePageTabTheme.FOREGROUND_COLOR_GOLDEN:
                        themeColor = mGoldenThemeColor;
                        break;
                    case HomePageTabTheme.FOREGROUND_COLOR_CUSTOM:
                        themeColor = getColorForCustomTheme(customTheme);
                        break;
                    default:
                        break;
                }
            }
            return themeColor;
        }

        int getColorForCustomTheme(CustomTheme customTheme) {
            int color = mWhiteThemeColor;
            if (customTheme != null && customTheme.isValid()) {
                switch (this) {
                    case STATUS_BAR:
                        break;
                    case ACTIVE_TAB:
                    case DEACTIVATE_TAB:
                    case TAB_INDICATOR:
                    case SEARCH_BAR_RIGHT_ACTION_BTN_ICON:
                    case SEARCH_BAR_RIGHT_ACTION_BTN_TEXT:
                    case SEARCH_BAR_OTHER_BTN:
                    case EDIT_TAB_PAGE_ENTRY:
                        color = customTheme.getEffectTabColor();
                        break;
                    case SEARCH_BAR_BG:
                        color = customTheme.getEffectSearchBoxColor();
                        break;
                    case SEARCH_ICON:
                    case SEARCH_TEXT:
                        color = customTheme.getEffectSearchBoxTextIconColor();
                        break;
                    case SEARCH_BAR_RIGHT_ACTION_BTN_BG:
                        color = customTheme.getEffectIconColor();
                        break;
                    case SEARCH_BTN_TEXT:
                        color = customTheme.getEffectSearchButtonTextColor();
                        break;
                    case SEARCH_BTN_BG:
                        color = customTheme.getEffectSearchButtonBGColor();
                        break;
                    default:
                        break;
                }
            }
            return color;
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment onCreate");
        PerformanceMonitor.traceBegin("HomePage_onCreate");
        Bundle bundle = getArguments();
        if (bundle != null && bundle.containsKey(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY)) {
            mIntentTab = bundle.getString(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY);
            bundle.remove(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY);//后面不用了
        }
        if (bundle != null && bundle.containsKey(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME)) {
            mSelectTab = bundle.getString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME);
            bundle.remove(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME);
        }
        if (bundle != null && bundle.containsKey(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID)) {
            mPendingSwitchToTabId = bundle.getString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID);
            bundle.remove(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID);
        }
        try {
            final Activity activity = getActivity();
            if (activity instanceof MainActivity) {
                postOnUiThreadDelayed(() -> {

                    boolean canRequstImei = false;

//                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
//                        canRequstImei = PermissionManage.canRequestPermission(mContext,
//                                Manifest.permission.READ_PHONE_STATE);
//                        // 首次启动时，会在WelComeActivity中请求imei权限，如果已经允许了，这里要把imei权限加进去再判断一次，
//                        // 这样就能走到havedPermissionOrUseAgree里面的逻辑
//                        if (ToolUtil.isFirstInstallApp(getContext()) && !canRequstImei
//                                && ContextCompat.checkSelfPermission(activity
//                                , Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
//                            canRequstImei = true;
//                        }
//                    }

                    //新安装app第一次启动，不请求该权限，和ios逻辑保持一致
                    final boolean canRequstLocal = !ToolUtil.isFirstInstallApp(getContext())
                            && XmLocationManager.configCanRequestLocation
                            && PermissionManage.canRequestPermission(mContext,
                            Manifest.permission.ACCESS_COARSE_LOCATION);

                    if (canRequstImei || canRequstLocal) {
                        RecommendFragmentAdUtil.isGotoCheckPermission = true;
                        RecommendFragmentAdNewUtil.isGotoCheckPermission = true;
                    }
                    final boolean finalCanRequstImei = canRequstImei;
                    if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
                        HandlerManager.postOnUIThreadDelay(new Runnable() {
                            @Override
                            public void run() {
                                PermissionManage.checkPermission(activity, (MainActivity) activity,
                                        new LinkedHashMap<String, Integer>() {
                                            {
                                                if (finalCanRequstImei) {
                                                    put(Manifest.permission.READ_PHONE_STATE,
                                                            com.ximalaya.ting.android.host.R.string.host_deny_perm_read_phone_state);
                                                }

                                                if (canRequstLocal) {
                                                    put(Manifest.permission.ACCESS_COARSE_LOCATION, null);
                                                }
                                            }
                                        }, new IMainFunctionAction.IPermissionListener() {
                                            @Override
                                            public void havedPermissionOrUseAgree() {
                                                LoginUtil.initOneKeyLoginSDK(mContext);
                                                RecommendFragmentAdUtil.isGotoCheckPermission = false;
                                                RecommendFragmentAdNewUtil.isGotoCheckPermission = true;

                                                XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext);
                                            }

                                            @Override
                                            public void userReject(Map<String, Integer> noRejectPermiss) {
                                                RecommendFragmentAdUtil.isGotoCheckPermission = false;
                                                RecommendFragmentAdNewUtil.isGotoCheckPermission = false;
                                                XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext);

                                                LoginUtil.initOneKeyLoginSDK(mContext);
                                            }
                                        });
                            }
                        }, 3000);
                    } else {
                        PermissionManage.checkPermission(activity, (MainActivity) activity,
                                new LinkedHashMap<String, Integer>() {
                                    {
                                        if (finalCanRequstImei) {
                                            put(Manifest.permission.READ_PHONE_STATE,
                                                    com.ximalaya.ting.android.host.R.string.host_deny_perm_read_phone_state);
                                        }

                                        if (canRequstLocal) {
                                            put(Manifest.permission.ACCESS_COARSE_LOCATION, null);
                                        }
                                    }
                                }, new IMainFunctionAction.IPermissionListener() {
                                    @Override
                                    public void havedPermissionOrUseAgree() {
                                        LoginUtil.initOneKeyLoginSDK(mContext);
                                        RecommendFragmentAdUtil.isGotoCheckPermission = false;
                                        RecommendFragmentAdNewUtil.isGotoCheckPermission = true;

                                        XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext);
                                    }

                                    @Override
                                    public void userReject(Map<String, Integer> noRejectPermiss) {
                                        RecommendFragmentAdUtil.isGotoCheckPermission = false;
                                        RecommendFragmentAdNewUtil.isGotoCheckPermission = false;
                                        XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext);

                                        LoginUtil.initOneKeyLoginSDK(mContext);
                                    }
                                });
                    }
                }, 3000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        LivePersonalCenterReminderManager.newInstance().addListener(this);
        LiveStartReminderManager.getInstance().addListener(this);
        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment onCreate finish");

        addDummyUserGuideAction();
        UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
        UserInfoMannage.getInstance().addVipStatusChangeListener(this);
        ChildProtectManager.addChildProtectStatusListener(mChildProtectStatusListener);

        PerformanceMonitor.traceEnd("HomePage_onCreate", 16);
    }

    public void onRecommendDataLoadFinish() {
        if (isNewRecommendTab(getCurTabIndex())) {
            if (getRlContainer().getBackground() == null) {
                getRlContainer().setBackgroundResource(R.color.main_color_F7F9FC_000000);
            }
            updateFreeListenIcon();
            updateMoreHotDot();
        } else {
            if (getRlContainer().getBackground() != null) {
                getRlContainer().setBackground(null);
            }
        }
    }

    public void doFreeListenAnimation() {
        if (mFreeListenIconManager != null) {
            mFreeListenIconManager.doFreeListenAnimation();
        }
    }

    public View getRlContainer() {
        if (mRlContainer == null) {
            PerformanceMonitor.traceBegin("getRlContainer");
            // 目前没用，暂时注释掉
            mRlContainer = findViewById(R.id.main_container_layout);
            PerformanceMonitor.traceEnd("getRlContainer", 401);
        }
        return mRlContainer;
    }

    private ChildProtectManager.IChildProtectStatusLoad mChildProtectStatusListener = () -> {
        if (!canUpdateUi()) {
            return;
        }

        doAfterAnimation(this::updateUnderageModeUiIfNeeded);
    };

    private void updateUnderageModeUiIfNeeded() {
        boolean needResetRecommendPage = false;
        boolean isUnderageModeNow = ChildProtectManager.isChildMode(getContext());
        if (mIsInUnderageMode != isUnderageModeNow) {
            mIsInUnderageMode = isUnderageModeNow;
            Logger.e("RecommendStaggered", "load tab");
            loadTabData();
            try {
                if (mIsInUnderageMode) {
                    initUnderageModeTopIfNeeded();
                    mVgSearchBar.setVisibility(View.GONE);
                    mTabs.setVisibility(View.GONE);
                    mIvEditTab.setVisibility(View.INVISIBLE);
                    mVEditTabShadow.setVisibility(View.INVISIBLE);
                    setBottomOvalViewVisibility(false);
                    unregisterTopColorChangeReceiver();
                    mVUnderageModeTop.setVisibility(View.VISIBLE);
                    ViewGroup.LayoutParams lpViewPager = mPager.getLayoutParams();
                    if (lpViewPager instanceof RelativeLayout.LayoutParams) {
                        ((RelativeLayout.LayoutParams) lpViewPager).addRule(RelativeLayout.BELOW, mVUnderageModeTop.getId());
                    }
                    mIsDarkStatusBar = !BaseFragmentActivity.sIsDarkMode;
                    StatusBarManager.setStatusBarColor(getWindow(), mIsDarkStatusBar);
                } else {
                    mVgSearchBar.setVisibility(View.VISIBLE);
                    mTabs.setVisibility(View.VISIBLE);
                    mIvEditTab.setVisibility(View.VISIBLE);
                    mVEditTabShadow.setVisibility(View.VISIBLE);
                    mVUnderageModeTop.setVisibility(View.GONE);
                    registerTopColorChangeReceiver();
                    ViewGroup.LayoutParams lpViewPager = mPager.getLayoutParams();
                    if (lpViewPager instanceof RelativeLayout.LayoutParams) {
                        ((RelativeLayout.LayoutParams) lpViewPager).addRule(RelativeLayout.BELOW, mTabs.getId());
                    }
                }
                needResetRecommendPage = true;
            } catch (Exception e) {
                e.printStackTrace();
                if (ConstantsOpenSdk.isDebug) {
                    throw e;
                }
            }
        }

        if (mIsInUnderageMode && mVUnderageModeSelectedAge != null) {
            CharSequence tvString = mVUnderageModeSelectedAge.getText();
            String lastSelectedAgeText = null;
            if (!TextUtils.isEmpty(tvString)) {
                lastSelectedAgeText = tvString.toString();
            }
            String selectedAgeRange = ChildProtectManager.getFriendlySelectedAgeRange(getContext());
            // 如果选择的年龄段改变了，刷新推荐页
            if (!TextUtils.isEmpty(lastSelectedAgeText) && !lastSelectedAgeText.equals(selectedAgeRange)) {
                needResetRecommendPage = true;
            }
//            if (!TextUtils.isEmpty(selectedAgeRange)) {
//                mVUnderageModeSelectedAge.setText(selectedAgeRange);
//                mVUnderageModeSelectedAge.setVisibility(View.VISIBLE);
//            } else {
//                mVUnderageModeSelectedAge.setVisibility(View.GONE);
//            }
        }

        if (needResetRecommendPage) {
            resetRecommendPage();
        }
    }

    private void initUnderageModeTopIfNeeded() {
        if (mVUnderageModeTop == null) {
            ViewStub viewStub = findViewById(R.id.main_view_stub_underage_mode_top);
            if (viewStub != null) {
                mVUnderageModeTop = viewStub.inflate();
                mVUnderageModeSelectedAge = mVUnderageModeTop.findViewById(R.id.main_tv_selected_age_range);
                mVUnderageModeExit = mVUnderageModeTop.findViewById(R.id.main_tv_exit_underage_mode);
//                if (BottomTabFragmentManager.INSTANCE.getUseNewTab()) {
                    mVUnderageModeExit.setVisibility(View.VISIBLE);
                    mVUnderageModeSelectedAge.setBackgroundResource(0);
//                } else {
//                    mVUnderageModeExit.setVisibility(View.GONE);
//                    mVUnderageModeSelectedAge.setBackgroundResource(R.drawable.main_bg_rect_stroke_f7f0e8_333231_corner_100);
//                }
//                mVUnderageModeSelectedAge.setOnClickListener(this);
                mVUnderageModeExit.setOnClickListener(this);
                if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
                    int statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
                    ViewGroup.LayoutParams layoutParams = mVUnderageModeTop.getLayoutParams();
                    if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                        ((ViewGroup.MarginLayoutParams) layoutParams).topMargin += statusBarHeight;
                    }
                }
//                View vExitUnderageMode = findViewById(R.id.main_tv_exit_underage_mode);
//                vExitUnderageMode.setOnClickListener(this);
            }
        }
    }

    private void showLiveRedDot() {
        if (!isShowLiveRedDotNetDay()) {
            return;
        }
        if (mLiveModleIndex > -1) {
            mTabs.showRedDot(mLiveModleIndex);
            mLiveRedDotPosition.setRedDotShow(true);
            mLiveRedDotPosition.notifiRedDotShow();
        }
    }

    private void saveLiveRedDotShowDate() {
        if (mLiveRedDotPosition != null && mLiveRedDotPosition.isShowRedDot()) {
            MMKVUtil.getInstance().saveString(PreferenceConstantsInHost.KEY_LIVE_RED_DOT_SHOW_DATE, getCurrDateString());
        }
    }

    private String getCurrDateString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }

    private boolean isShowLiveRedDotNetDay() {
        String lastVisitDateString = MMKVUtil.getInstance().getString(PreferenceConstantsInHost.KEY_LIVE_RED_DOT_SHOW_DATE, "");
        if (!TextUtils.isEmpty(lastVisitDateString)) {
            if (lastVisitDateString.equals(getCurrDateString())) {
                return false;
            } else {
                return true;
            }
        }
        return true;
    }

    private void clearLiveRedDotShowDate() {
        MMKVUtil.getInstance().saveString(PreferenceConstantsInHost.KEY_LIVE_RED_DOT_SHOW_DATE, "");
    }

    private void hideLiveRedDot() {
        if (mLiveModleIndex > -1) {
            mTabs.hideRedDot(mLiveModleIndex);
            mLiveRedDotPosition.setRedDotShow(false);
        }
    }

    private long mLastCheckLiveRedDotTime = 0;
    private long mCheckLiveRedDotInterval = 2 * 1000;
    public void checkLiveTabRedDot() {
        if (!isShowLiveRedDotNetDay()) {
            return;
        }
        long curTime = System.currentTimeMillis();
        if (Math.abs(curTime - mLastCheckLiveRedDotTime) < mCheckLiveRedDotInterval) {
            Logger.i("cf_test", "__not____checkLiveTabRedDot——————" + (curTime - mLastCheckLiveRedDotTime));
            return;
        }
        mLastCheckLiveRedDotTime = curTime;
        Logger.i("cf_test", "____checkLiveTabRedDot");
        MainCommonRequest.checkLiveTabRedDotShow(new IDataCallBack<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean data) {
                if (data != null) {
                    if (data) {
                        if (mLiveModleIndex > -1) {
                            showLiveRedDot();
                        } else {
                            findLiveTabIndex();
                            showLiveRedDot();
                        }
                    } else {
                        hideLiveRedDot();
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
            }
        });
    }

    private void addDummyUserGuideAction() {
        EventManager.Action action = new EventManager.Action();
        // 0播放新人引导弹窗依赖两个条件：1.未产生有效播放；2.在一级页面
        action.dependentEvents.add(new EventManager.Event(EventManager.EVENT_DUMMY_USER_GUIDE));
        action.name = EventManager.ACTION_DUMMY_USER_GUIDE;
        action.uiRunnable = this::showNewUserGuideDialog;
        EventManager.getInstance().addAction(action);
    }

    private void showNewUserGuideDialog() {
        Activity activity = BaseApplication.getTopActivity();
        if (!(activity instanceof MainActivity) || !canUpdateUi()) {
            return;
        }
        boolean hasShowNoPlayGuide = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost
                .KEY_NEW_USER_NO_PLAY_GUIDE_HAS_SHOWN, false);
        String type = hasShowNoPlayGuide ? DummyUserGuideManager.TYPE_SHOW_DIRECTLY
                : DummyUserGuideManager.TYPE_NO_PLAN_MORE_THAN_DUMMY_TIME;
        DummyUserGuideManager.getInstance().showNewUserNoPlayGuideDialogFra((MainActivity) activity, type);
    }

    private void resetRecommendPage() {
        if (mPagerAdapter != null && mPager != null) {
            Fragment f = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            if (f instanceof RecommendFragmentStaggered) {
                ((RecommendFragmentStaggered) f).notifyReset(true);
            } else if (f instanceof RecommendRnFragment) {
                ((RecommendRnFragment) f).notifyReset(true);
            } else {
                findRecommendFragmentAndReset();
            }
        }
    }

    private void findRecommendFragmentAndReset() {
        for (int i = 0; i < fragmentlist.size(); i++) {
            Fragment f = mPagerAdapter.getFragmentAtPosition(i);
            if (f instanceof RecommendFragmentStaggered) {
                ((RecommendFragmentStaggered) f).notifyReset(true);
                return;
            } else if (f instanceof RecommendRnFragment) {
                ((RecommendRnFragment) f).notifyReset(true);
                return;
            }
        }
    }

    private void fontLog(Context context, String tag) {
        if (Logger.isDebug && context != null && context.getResources() != null
                && context.getResources().getConfiguration() != null
                && context.getResources().getDisplayMetrics() != null) {
            com.ximalaya.ting.android.xmutil.Logger.d("fontScaleTest",
                    tag + ": " + this + ", context: " + context + ", res: " + context.getResources()
                            + ", config: " + context.getResources().getConfiguration()
                            + ", displayMe: " + context.getResources().getDisplayMetrics());
            com.ximalaya.ting.android.xmutil.Logger.d("fontScaleTest",
                    tag + ": " + context.getResources().getConfiguration().fontScale
                            + ", scaledDensity: " + context.getResources().getDisplayMetrics().scaledDensity
                            + ", density: " + context.getResources().getDisplayMetrics().density);
        }
    }

    @Override
    public void onMyResume() {
        fontLog(mContext, "homepageFrag context");
        fontLog(mActivity, "homepageFrag activity");
        PerformanceMonitor.traceBegin("HomePage_onMyResume");
        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment onResume");
        tabIdInBugly = 38386;
        super.onMyResume();
        updateUnderageModeUiIfNeeded();
        if (!mIsFirstLoadTab) {
            updateSearchWord();
        }
        updateFreeListenIcon();
        if (mFreeListenIconManager != null) {
            mFreeListenIconManager.onPageResume();
            if (!ViewUtil.isSplashAdShowing()) {
                doFreeListenAnimation();
            }
        }
        RewardAgainAdManager.onPageResume(RewardAgainAdManager.PAGE_SOURCE_HOME);
        checkUserSign();
        updateMoreHotDot();
//        boolean showSearchBtn = ConfigureCenter.getInstance()
//                .getBool(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_SEARCH_DISPLAY, false);
//        if (showSearchBtn) {
//            mVgSearchBtnInSearchBar.setVisibility(View.VISIBLE);
//        } else {
//            mVgSearchBtnInSearchBar.setVisibility(View.GONE);
//        }

//        loadPopAdData();
        refreshMsgCount();
        hasPaused = false;
        MyListenGuideUtil.INSTANCE.setHomePageShow(true);
        updateRedDotInfo(HomePageRedDotManager.TYPE_RECOMMEND_PAGE, 100);
        updateRedDotInfo(HomePageRedDotManager.TYPE_QUALITY_PAGE, 100);
        RouteServiceUtil.getDownloadService().registerDownloadCallback(downloadListener);

        getRedDotManager().setCountsCallBack(goodsCountsCallBack);
        getRedDotManager().doOnResume();

        //TODO by easoll: 临时解决iTing跳转文稿页会出现肚脐眼bug
        if (!PlayBarAbManager.INSTANCE.useNewPlayBar()) {
            Activity activity = getActivity();
            if (activity instanceof MainActivity && ((MainActivity) activity).getManageFragment() != null) {
                List stack = ((MainActivity) activity).getManageFragment().mStacks;
                if (stack != null && stack.size() == 0) {
                    showPlayButton();
                }
            }
        }

        // 有请求过tab数据，则重新加载tab
        boolean hasRequestedTabData = SharedPreferencesUtil.getInstance(
                getActivity()).getBoolean(PreferenceConstantsInHost.HAS_REQUESTED_HOME_PAGE_TAB_DATA);
        if (hasRequestedTabData) {
            loadTabData();
        }

        boolean isFirstLaunch = isFirstLoaded;
        if (isFirstLoaded) {
            UserTrackCookie.getInstance().clearXMLYResource();
            if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_RECOMMEND, mDefaultPageType)) {
                UserTrackCookie.getInstance().setXmContent("homepage", "homepage", null);
            }
            tryToShowChatXmlyBubble();
        } else if (agentStyleChange) {
            agentStyleChange = false;
            mHasShowChildXmlyAnimation = false;
            showChildChatXmlyBubble(true);
        } else if (AiAgentTipManager.INSTANCE.canShowSug()) {
            mHasShowChildXmlyAnimation = false;
            showChildChatXmlyBubble(true);
        } else if (isRealVisable() && canUpdateUi() && ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
            ChildXmlyTipManager.INSTANCE.childChatXmlyExplore("newHomePage");
        }
          // 测试用，勿删
//          mHasShowChildXmlyAnimation = false;
//          showChildChatXmlyBubble(true);
        if (!isFirstLoaded && !isGotoEditTab) {
            traceOnTabShowDelay();
        }
        isFirstLoaded = false;


        // 需要延迟设置一下状态栏文字颜色，因为子fragment会改变状态栏颜色
        if (getView() != null) {
            getView().post(() -> {
                        StatusBarManager.setStatusBarColor(getWindow(), darkStatusBar());
                    }
            );
        }

        updateSkinSetting();
        updateBgAndForegroundColor(false);
        SkinManager.INSTANCE.addSkinSettingChangeListener(mSkinSettingChangeListener);
        TopBgForSceneManager.INSTANCE.addSceneNewCardChangeListener(mSceneNewCardChangeListener);
        //中断弹出提示层
        showPlayInterruptPop();

        if (!mIsInUnderageMode) {
            registerTopColorChangeReceiver();
        }

        showEmergencyAnnouncementIfNeeded();

        showFreshGiftDialogForDeepLinkIfNeed();

        DriveModeBluetoothManager.getInstance().setHomePageShow(true);


        // 检查下是否展示推荐专辑的dialog
        RecommendAlbumManager.INSTANCE.checkShowOnHomePage();

        // 新用户任务提示与登录提示互斥，且新用户任务提示优先级高
//        if (!NewUserManager.getInstance().containsMission(NewUserManager.TYPE_MISSION_HOMEPAGE_SCROLL)) {
//            LoginUtil.showLoginHintView(mContainer);
//        }
        NewUserRightsDialogManager.INSTANCE.showDialog(getContext());
        if (mChatXmlyPopupWindow != null) {
            mChatXmlyPopupWindow.onMyResume();
        }
        LoginUtil.initOneKeyLoginSDK(ToolUtil.getCtx());

        PerformanceMonitor.traceEnd("HomePage_onMyResume", 17);
        if (MyListenAbUtil.INSTANCE.hasSlideBar()) {
            MyListenAbUtil.INSTANCE.exploreSlideBar("newHomePage");
        }
        HandlerManager.postOnUIThreadDelay(() -> {
            if (canUpdateUi()) {
                MyListenGuideUtil.INSTANCE.checkShowSubscribeEx2Guide();
            }
        }, 3000);

        RefreshHomeChannelUtils.onMyResume();
    }

    private boolean mHasRegisterTopColorChangeReceiver = false;

    private void registerTopColorChangeReceiver() {
        if (mHasRegisterTopColorChangeReceiver) {
            return;
        }

        mHasRegisterTopColorChangeReceiver = true;
        IntentFilter filter = new IntentFilter();
        filter.addAction(BannerView.COLOR_CHANGE_ACTION);
        filter.addAction(BannerView.COLOR_ON_SCROLL_ACTION);
        filter.addAction(RefreshLoadMoreListView.SCROLL_CHANGE_LISTENER_ACTION);
        filter.addAction(HomePageFragment.TITLE_BAR_ALPHA_CHANGE_ACTION);
        filter.addAction(HomePageFragment.DROP_DOWN_PROPORTION_CHANGE_ACTION);
        filter.addAction(HomePageFragment.SPLASH_UNIT_PACK_BG_ACTION);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mTopColorChangeReceiver, filter);
        IntentFilter signFilter = new IntentFilter();
        signFilter.addAction(HomePageFragment.TITLE_BAR_SIGN);
        signFilter.addAction(HomePageFragment.TITLE_BAR_CREDIT_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mSignStatusChangeReceiver, signFilter);
    }

    private void updateBgAndForegroundColor(boolean ignoreViewTagColor) {
        if (mPager != null && mPagerAdapter != null) {
            setLastColor(mPager.getCurrentItem(), ignoreViewTagColor, false);
            setTopViewColor(lastColor, lastTopRectColor, 0.0f);

            updateSearchBar(mPager.getCurrentItem());
            updateForegroundColor(mPager.getCurrentItem());
        }
    }

    private void updateSkinSetting() {
        if (SkinManager.INSTANCE.hasValidMainColor()) {
            mUseSkinSettingBgColor = true;
            mSkinSettingBgColor = SkinManager.INSTANCE.getMainColor();
        }
    }

    public int getSkinColor() {
        if (mUseSkinSettingBgColor) {
            return mSkinSettingBgColor;
        }
        return ColorUtil.INVALID_COLOR;
    }

    private void showPlayInterruptPop() {
        if (DoShowInterruptViewHelper.getInstanse().isShowInterruptView(mContext)) {
            Logger.i("PlayFragment", "进入展示Pop逻辑");

            new UserTracking()
                    .setModuleType("中断提示弹窗")
                    .setId(7233)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);

            DoShowInterruptViewHelper.getInstanse().setAudioFocusLoss(mContext);
            DoShowInterruptViewHelper.getInstanse().setTodayHasShow(mContext);
            PlayInterrupttedPopFragment.showPop(this);
        }
    }

    private void showEmergencyAnnouncementIfNeeded() {
        EmergencyPlan.Announcement emergencyAnnouncement =
                EmergencyPlanManager.getInstance().getEmergencyAnnouncement(EmergencyPlanManager.POSITION_HOME_PAGE
                        , () -> showEmergencyAnnouncementIfNeeded(EmergencyPlanManager.getInstance()
                                .getEmergencyAnnouncement(EmergencyPlanManager.POSITION_HOME_PAGE)));
        showEmergencyAnnouncementIfNeeded(emergencyAnnouncement);
    }

    private void showEmergencyAnnouncementIfNeeded(EmergencyPlan.Announcement emergencyAnnouncement) {
        if (emergencyAnnouncement != null && !EmergencyPlanManager.getInstance()
                .hasAnnouncementClosed(EmergencyPlanManager.POSITION_HOME_PAGE,
                        emergencyAnnouncement)) {
            if (mEmergencyAnnouncementView == null) {
                ViewStub viewStub = findViewById(R.id.main_vs_emergency_announcement);
                mEmergencyAnnouncementView = EmergencyAnnouncementView.newInstance(viewStub);
                mEmergencyAnnouncementView.setEventListener(announcement -> EmergencyPlanManager.getInstance()
                        .markAnnouncementClosed(EmergencyPlanManager.POSITION_HOME_PAGE, announcement));
            }
            mEmergencyAnnouncementView.show(emergencyAnnouncement);
        } else {
            if (mEmergencyAnnouncementView != null) {
                mEmergencyAnnouncementView.hide();
            }
        }
    }


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    @Override
    protected boolean darkStatusBar() {
        return mIsDarkStatusBar;
    }

    @Override
    public void onPause() {
        super.onPause();
        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(downloadListener);
        unregisterTopColorChangeReceiver();
        hasPaused = true;
        mIsTopBgShown = false;
        if (mSearchHotWordSwitchManager != null) {
            mSearchHotWordSwitchManager.stopSwitch();
        }
        if (mFreeListenIconManager != null) {
            mFreeListenIconManager.onPagePause();
        }
        showDropAd(0f, true, 0);
        MyListenGuideUtil.INSTANCE.setHomePageShow(false);

        // MainActivity的公用CustomTipsView
        Object object =
                TempDataManager.getInstance().getObjectLayzed(MainActivity.TEMP_DATA_MAIN_TIPS);
        if (object instanceof CustomTipsView) {
            CustomTipsView tipsView = (CustomTipsView) object;
            tipsView.dismissTips();
        }

        FixedThreadPool.execute(() -> SimpleMediaPlayer.getInstance().stop());
        DriveModeBluetoothManager.getInstance().setHomePageShow(false);
        SkinManager.INSTANCE.removeSkinSettingChangeListener(mSkinSettingChangeListener);
        TopBgForSceneManager.INSTANCE.removeSceneNewCardChangeListener(mSceneNewCardChangeListener);
        if (mRedDotManager != null) {
            mRedDotManager.removeCountsCallBack();
        }
        if (mAdTag != null) {
            mAdTag.setVisibility(View.INVISIBLE);
        }
        DummyUserGuideManager.getInstance().dismissNewUserNoPlayGuideDialogFra();

        if (mChatXmlyPopupWindow != null) {
            mChatXmlyPopupWindow.onMyPause();
        }
        Activity activity = getActivity();
        if (activity instanceof BaseFragmentActivity2 && !((BaseFragmentActivity2) activity).isActivityResumed()) {
            hideChatXmlyBubble();
        }
        RewardAgainAdManager.onPagePause(RewardAgainAdManager.PAGE_SOURCE_HOME);
    }

    private void unregisterTopColorChangeReceiver() {
        if (mHasRegisterTopColorChangeReceiver) {
            mHasRegisterTopColorChangeReceiver = false;
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mTopColorChangeReceiver);
        }
    }

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    @Override
    public void onDestroy() {
        UserInfoMannage.getInstance().removeVipStatusChangeListener(this);
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
        MainSearchUtils.clearSearchHints();
        removeActions();
        LivePersonalCenterReminderManager.newInstance().removeListener(this);
        LiveStartReminderManager.getInstance().removeListener(this);
        CategoryRecommendKidEntryManager.getInstance().release();
        ChildProtectManager.removeChildProtectStatusListener(mChildProtectStatusListener);
        mSearchHintCallBack = null;
        getRedDotManager().doOnDestroy();
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mSignStatusChangeReceiver);
        XmAppHelper.unregisterAppStatusChangedListener(mOnAppStatusChangedListener);
        RedDotManage.getInstance().removeRedDotPosition(mLiveRedDotPosition);
        RedDotManage.getInstance().removeRedDotPosition(mSignRedDotPosition);
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.removeAdShowAndDestroyListeners(this);
        if (mFreeListenIconManager != null) {
            mFreeListenIconManager.onPageDestroy();
        }
        if (mPoppyEndAnimator != null) {
            mPoppyEndAnimator.removeAllListeners();
            mPoppyEndAnimator.cancel();
        }
        if (mPoppyStartAnimator != null) {
            mPoppyStartAnimator.removeAllListeners();
            mPoppyStartAnimator.cancel();
        }
        ChildXmlyTipManager.INSTANCE.removeChangeListener(childXmlyChangeListener);
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mChatXmlyChangeRoleReceiver);
//        XmMNNManager.release();
        super.onDestroy();
        ShortVideoCoverPlayUtil.release();
        HomeSendRnEventUtils.destroy();
    }

    private void removeActions() {
        EventManager.getInstance().removeAction("ShowFreshGiftDialog");
        EventManager.getInstance().removeAction("ShowHomePageTips");
        EventManager.getInstance().removeAction("ShowFreshGiftMagnetic");
        EventManager.getInstance().removeAction(EventManager.ACTION_DUMMY_USER_GUIDE);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment initUi begin");
        PerformanceMonitor.traceBegin("HomePage_initUi_");

        PerformanceMonitor.traceBegin("HomePage_initUi_s1");

        mBottomOvalView = findViewById(com.ximalaya.ting.android.framework.R.id.framework_tab_top_bg);

        if (BaseFragmentActivity.sIsDarkMode) {
            mBottomOvalView.setShowShadow(false);
        }

        PerformanceMonitor.traceEnd("HomePage_initUi_s1", 403);

        PerformanceMonitor.traceBegin("HomePage_initUi_s1.2");

        mLiveRedDotPosition = new LiveRedDotPosition(RedDotManage.getInstance(), new IRedDotAction() {
            @Override
            public void hideRedDot() {
                hideLiveRedDot();
                clearLiveRedDotShowDate();
            }

            @Override
            public boolean isViewVisible() {
                return com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(mTabs);
            }
        });

        PerformanceMonitor.traceEnd("HomePage_initUi_s1.2", 302);
        PerformanceMonitor.traceBegin("HomePage_initUi_s1.3");

        mSignRedDotPosition = new SignRedDotPosition(RedDotManage.getInstance(), new IRedDotAction() {
            @Override
            public void hideRedDot() {
                hideSignRedDot();
            }

            @Override
            public boolean isViewVisible() {
                return com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(mTabs);
            }
        });
        PerformanceMonitor.traceEnd("HomePage_initUi_s1.3", 303);

        PerformanceMonitor.traceBegin("HomePage_initUi_s2.1");

        RedDotManage.getInstance().addRedDotPosition(mLiveRedDotPosition);
        RedDotManage.getInstance().addRedDotPosition(mSignRedDotPosition);

        PerformanceMonitor.traceEnd("HomePage_initUi_s2.1", 602);

        PerformanceMonitor.traceBegin("HomePage_initUi_s2.2");
        mTopBgView = findViewById(R.id.main_tab_top_img);
        mIvAtmosphereBg = findViewById(R.id.main_iv_atmosphere_bg);
        mIvAtmosphereIcon = findViewById(R.id.main_iv_atmosphere_icon);
        mIvTopBgForSceneInDark = findViewById(R.id.main_iv_top_bg_for_scene);

        PerformanceMonitor.traceEnd("HomePage_initUi_s2.2", 603);

        PerformanceMonitor.traceBegin("HomePage_initUi_s3");

        // 此控件在mainActivity中
        if (getActivity() instanceof MainActivity) {
            mTwoStyleImgView = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.host_drop_down_two_style_ad);
            mReversePairImageView = getActivity().findViewById(com.ximalaya.ting.android.main.R.id.main_unit_package_box);
            boolean showPreloadPageBox = SplashUnitAdUtil.getInstance().preloadPageBox(mReversePairImageView,
                    (lastUrl, bitmap) -> {
                        if (bitmap != null) {
                            mReversePairImageView.setTag(R.id.main_gaint_cover_blur_success, lastUrl);
                        }
                    });

            if (!showPreloadPageBox) {
                BigScreenAdManager.getInstance().preloadPageBox(mReversePairImageView,
                        (lastUrl, bitmap) -> {
                            if (bitmap != null) {
                                mReversePairImageView.setTag(R.id.main_gaint_cover_blur_success, lastUrl);
                            }
                        });
            }
        }

        PerformanceMonitor.traceEnd("HomePage_initUi_s3", 103);
        PerformanceMonitor.traceBegin("HomePage_initUi_s4");

        mVgSearchBar = findViewById(R.id.main_vg_search_bar);

        mPresenter = new HomePageFragmentPresenter(this);
        mSearchHotWordSwitchManager = new SearchHotWordSwitchManager(this);
        // 初始化HomePageRedDotManager
        getRedDotManager();

        PerformanceMonitor.traceEnd("HomePage_initUi_s4", 104);
        PerformanceMonitor.traceBegin("HomePage_initUi_s5");

        mTvSearchBarRightActionBtn = findViewById(R.id.main_tv_search_bar_action);
        mVgSearchBarRightAction = findViewById(R.id.main_vg_search_bar_action);
        AutoTraceHelper.setLabelForCTWithMultiSameSubView(mVgSearchBarRightAction);
        mVgSearchBarRecommendPageAction =
                findViewById(R.id.main_vg_search_bar_recommend_tab_action);
        if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
            mFreeListenIconManager = new FreeListenIconManager(mVgSearchBarRecommendPageAction, mContext);
        }

        PerformanceMonitor.traceEnd("HomePage_initUi_s5", 105);
        PerformanceMonitor.traceBegin("HomePage_initUi_s6");

        mRlSignVg = findViewById(R.id.main_rl_search_bar_sign);
//        mIvSearchBarHistoryBtn = findViewById(R.id.main_iv_search_bar_history);
        mIvSearchBarSign = findViewById(R.id.main_iv_search_bar_sign);
        mIvSearchBarSignEffect = findViewById(R.id.main_iv_search_bar_sign_effect);
        mIvSignRedDot = findViewById(R.id.main_iv_sign_red_dot);
        mIvSearchBarMoreActionBtn = findViewById(R.id.main_iv_search_bar_more_action);
        mTvMoreHot = findViewById(R.id.main_tv_more_hot);
        mViewMoreHotDot = findViewById(R.id.main_v_more_hot_dot);
        mVMoreAction = findViewById(R.id.main_v_more_action);
        mSlideEntry = findViewById(R.id.main_slide_entry);
        mSlideRedDot = findViewById(R.id.main_slide_entry_red_dot);
        initSlideBar();
        setHistoryAndMoreButtonVisible();
        mVgSearchBarLivePageAction = findViewById(R.id.main_vg_search_bar_live_tab_action);
        mIvSearchBarLiveStartBtn = findViewById(R.id.main_iv_search_bar_live_start);
        mIvSearchBarLiveMineBtn = findViewById(R.id.main_iv_search_bar_live_mine);
        mIvLiveReminder = findViewById(R.id.main_iv_live_start_red_dot);
        mIvMineReminder = findViewById(R.id.main_iv_search_bar_mine_reminder);
        mVgSearchBarQualityPageAction = findViewById(R.id.main_vg_search_bar_quality_tab_action);
        mIvSearchBarQualityCartBtn = findViewById(R.id.main_iv_search_bar_quality_cart_action);
        mIvSearchBarIcon = findViewById(R.id.main_iv_home_search_bar_icon);
        mIvSearchBarIcon.setColorFilter(getNormalSearchIconColor(false), PorterDuff.Mode.SRC_IN);

        mLottieChatXmlyBubbleRobot = findViewById(R.id.main_lottie_chatxmly_bubble_robot);
        mTvChatXmlyBubble = findViewById(R.id.main_tv_chatxmly_bubble);

        mVgSearchBarVipPageAction = findViewById(R.id.main_vg_search_bar_vip_tab_action);
        mIvSearchBarVipBtn = findViewById(R.id.main_vg_search_bar_vip_enter_quanzi);
        mIvSearchRecognizeBtn = findViewById(R.id.main_iv_home_search_bar_recognize);
        mIvSearchRecognizeBtn.setColorFilter(getNormalSearchIconColor(true), PorterDuff.Mode.SRC_IN);
        mIvVoiceSearchRedDot = findViewById(R.id.host_iv_voice_search_red_dot);
        mVEditTabShadow = findViewById(R.id.main_v_category_tab_expand_shadow);
        mIvEditTab = findViewById(R.id.main_iv_edit_tab);
        mIvEditTab.setOnClickListener(this);
        if (MyListenAbUtil.INSTANCE.hasSlideBar()) {
            mIvEditTab.setImageResource(R.drawable.host_ic_standard_tabs_edit_side_bar);
        }

        mTvSearchBarRightActionBtn.setOnClickListener(this);
        mIvSearchBarVipBtn.setOnClickListener(this);
//        mIvSearchBarHistoryBtn.setOnClickListener(this);
        mVMoreAction.setOnClickListener(this);
        mIvSearchBarLiveStartBtn.setOnClickListener(this);
        mIvSearchBarLiveMineBtn.setOnClickListener(this);
        mIvSearchBarQualityCartBtn.setOnClickListener(this);
        mIvSearchBarSign.setOnClickListener(this);
        mIvSearchBarSignEffect.setOnClickListener(this);
        mLottieChatXmlyBubbleRobot.setOnClickListener(this);
        mTvChatXmlyBubble.setOnClickListener(this);


        PerformanceMonitor.traceEnd("HomePage_initUi_s6", 106);
        PerformanceMonitor.traceBegin("HomePage_initUi_s7");

        mTabs = findViewById(R.id.main_tabs);
        UIConsistencyManager.getInstance().setTabUIConsistency(getContext(), mTabs);
        mTabs.setOnScrollListener(new ListenScrollStatePagerSlidingTabStrip.OnScrollListener() {
            @Override
            public void onScrollStateChanged(ListenScrollStatePagerSlidingTabStrip view, int scrollState) {
                if (scrollState == ListenScrollStatePagerSlidingTabStrip.OnScrollListener.SCROLL_STATE_IDLE) {
                    isWaittingTabScroll = false;
                    traceOnTabShowDelay();
                } else if (scrollState == ListenScrollStatePagerSlidingTabStrip.OnScrollListener.SCROLL_STATE_TOUCH_SCROLL) {

                }
            }

            @Override
            public void onScroll(ListenScrollStatePagerSlidingTabStrip view, boolean isTouchScroll, int l, int t, int oldl, int oldt) {

            }
        });
        mPager = findViewById(R.id.main_content);
        // 不要删除此tag
        mPager.setTag(com.ximalaya.ting.android.framework.R.id.framework_home_page_view_pager, true);
        mPager.setOffscreenPageLimit(1);

        mDropDownAdImg = findViewById(R.id.main_drop_down_ad);
        mAdTag = findViewById(R.id.main_home_ad_tag);
        if (RecommendFragmentAbManager.INSTANCE.getUseNewRecommendFragment() && AdManager.isUerNewSmallBanner()) {
            mBannerHeight = BannerView.getNewRecBannerWidthAndHeight(getContext())[1];
        }


        PerformanceMonitor.traceEnd("HomePage_initUi_s7", 107);
        PerformanceMonitor.traceBegin("HomePage_initUi_s8");

        ViewGroup.LayoutParams adTaglayoutParams = mAdTag.getLayoutParams();
        if (adTaglayoutParams instanceof RelativeLayout.LayoutParams) {
            ((RelativeLayout.LayoutParams) adTaglayoutParams).topMargin += BaseUtil.getStatusBarHeight(mContext);
            mAdTag.setLayoutParams(adTaglayoutParams);
        }

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            int statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
            RelativeLayout.LayoutParams tabsLayoutParams =
                    ((RelativeLayout.LayoutParams) mVgSearchBar.getLayoutParams());
            tabsLayoutParams.topMargin += statusBarHeight;
//            RelativeLayout.LayoutParams bottomOvalViewLayoutParams
//                    = (RelativeLayout.LayoutParams) mBottomOvalView.getLayoutParams();
//            bottomOvalViewLayoutParams.height += statusBarHeight;
//            ViewGroup.LayoutParams layoutParams = mIvAtmosphereBg.getLayoutParams();
//            layoutParams.height += statusBarHeight;
//            mIvAtmosphereBg.setLayoutParams(layoutParams);
        }


        PerformanceMonitor.traceEnd("HomePage_initUi_s8", 108);
        PerformanceMonitor.traceBegin("HomePage_initUi_s9");

        updateUnderageModeUiIfNeeded();

        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment initUi finish");

        onPageLoadingCompleted(LoadCompleteType.LOADING);
        loadTabData();
        mTabs.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {

            @Override
            public void onPageSelected(final int i) {
//                boolean isLoadPopAndData = mPagerAdapter != null && (mPagerAdapter
//                .getFragmentClassAtPositon(i) == RecommendFragmentNew.class);
//                if (isLoadPopAndData) {
//                    loadPopAdData();
//                }


                PerformanceMonitor.traceBegin("HomePage_initUi_onPageSelected");

                if (mTabModelList != null) {
                    updateSearchWord();
                    updateTopBgForScene(TopBgForSceneManager.INSTANCE.isHaveNewSceneCard());
                    updateAtmosphere();
                    HomePageTabModel model = mTabModelList.get(i);
                    if (isNewRecommendTab(i)) {
                        getRlContainer().setBackgroundResource(R.color.main_color_F7F9FC_000000);
                    } else {
                        getRlContainer().setBackground(null);
                    }
                    if (model != null) {
                        UserTrackCookie.getInstance().clearXMLYResource();
                        new UserTracking("首页", getTagNameByType(model)).statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);
                        if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_RECOMMEND,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("homepage", "homepage",
                                    null);
                        } else if (TextUtils.equals(ITEM_TYPE_H5,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("html5", "activity", null);
                        } else if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_WOTING_NEW,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("subscribe", "subscribe",
                                    "");
                        } else if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_LOCAL_LISTEN,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("loaclTing", "loaclTing",
                                    "");
                        } else if (TextUtils.equals(ITEM_TYPE_LAMIA,
                                model.getItemType())) {
                            // 首页未出青少年弹窗时，进入直播tab出青少年弹窗
                            showChildProtectDialog();
                            if (mLiveRedDotPosition.isShowRedDot()) {
                                // 全局页-红点频控  点击事件
                                new XMTraceApi.Trace()
                                        .click(59651) // 用户点击时上报
                                        .put("currPage", "forAll")
                                        .put("xmRequestId", "")
                                        .put("guideType", "引导")
                                        .put("tabName", "直播")
                                        .createTrace();
                            }
                            saveLiveRedDotShowDate();
                            mLiveRedDotPosition.setRedDotShow(false);
                            UserTrackCookie.getInstance().setXmContent("live", "live", "");
                        } else if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_LIVE,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("radio", "radio", "");
                        } else if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_VIP,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("vipCategory",
                                    "vipCategory", "");
                        } else if (TextUtils.equals(ITEM_TYPE_SINGLE_CATEGORY,
                                model.getItemType())) {
                            UserTrackCookie.getInstance().setXmContent("category", "category",
                                    "" + model.getCategoryId());
                        }

                        new UserTracking()
                                .setSrcPage("首页")
                                .setSrcModule("TAB")
                                .setItem(model.getItemType())
                                .setItemId(model.getId())
                                .setSrcPosition(i)
                                .setSrcTitle(model.getTitle())
                                .statIting(UserTracking.APP_NAME_EVENT,
                                        XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                    for (int j = 0; j < mTabModelList.size(); j++) {
                        final int index = j;
                        if (i == j) {
                            if (mTabs.needChangePic(i, mTabModelList.get(i).getActiveCoverPath())) {
                                if (!TextUtils.isEmpty(mTabModelList.get(i).getActiveCoverPath())) {
                                    ImageManager.from(mContext).downloadBitmap(mTabModelList.get(i).getActiveCoverPath(),
                                            (lastUrl, bitmap) -> setTabBg(index, bitmap,
                                                    (i >= 0 && i < mTabModelList.size()) ? mTabModelList.get(i).getActiveCoverPath() : "", true), false);
                                } else {
                                    setTabBg(index, null,
                                            mTabModelList.get(i).getActiveCoverPath(), true);
                                }

                            }
                        } else {
                            if (mTabs.needChangePic(j,
                                    mTabModelList.get(j).getUnactiveCoverPath())) {
                                if (!TextUtils.isEmpty(mTabModelList.get(j).getUnactiveCoverPath())) {
                                    ImageManager.from(mContext).downloadBitmap(mTabModelList.get(j).getUnactiveCoverPath(),
                                            (lastUrl, bitmap) -> {
                                                // 在回调中使用index之前，先检查index是否在有效范围内
                                                if (index >= 0 && index < mTabModelList.size()) {
                                                    setTabBg(index, bitmap, mTabModelList.get(index).getUnactiveCoverPath(), false);
                                                } else {
                                                    // 处理数组越界的情况，例如打印日志、抛出异常或者进行其他错误处理
                                                    if (ConstantsOpenSdk.isDebug) {
                                                        throw new RuntimeException("index out of range");
                                                    }
                                                }
                                            },
                                            false
                                    );
                                } else {
                                    setTabBg(index, null,
                                            mTabModelList.get(i).getUnactiveCoverPath(), false);
                                }

                            }
                        }

                    }
                }
                resetBottomViewHeight(i);

                PerformanceMonitor.traceEnd("HomePage_initUi_onPageSelected", 201);
            }

            private int lastPostion = -1;

            private int lastOffsetPixels;
            private int curState = ViewPager.SCROLL_STATE_IDLE;
            int willGoIndex = Integer.MAX_VALUE;
            boolean isGotoLeft = false;
            private int lastState = ViewPager.SCROLL_STATE_IDLE;

            @Override
            public void onPageScrolled(int position, float positionOffset,
                                       int positionOffsetPixels) {
                PerformanceMonitor.traceBegin("HomePage_initUi_onPageScrolled");

                if (mPagerAdapter != null) {
                    if (positionOffsetPixels != 0 && positionOffset != 0f && positionOffset != 1.0f) {
                        willGoIndex = Integer.MAX_VALUE;
                        if (curState == ViewPager.SCROLL_STATE_DRAGGING && lastState == ViewPager.SCROLL_STATE_IDLE) {
                            if (lastOffsetPixels > positionOffsetPixels) {
                                //右滑
                                isGotoLeft = true;
                                willGoIndex = position;
                                if (willGoIndex == mPager.getCurrentItem()) {
                                    willGoIndex = willGoIndex + 1;
                                    isGotoLeft = false;
                                }

                            } else if (lastOffsetPixels < positionOffsetPixels && lastOffsetPixels != 0) {
                                //左滑
                                isGotoLeft = false;
                                willGoIndex = position + 1;
                                if (willGoIndex == mPager.getCurrentItem()) {
                                    willGoIndex = willGoIndex - 1;
                                    isGotoLeft = true;
                                }
                            }
                        } else {
                            if (lastOffsetPixels > positionOffsetPixels) {
                                //右滑
                                isGotoLeft = true;
                                willGoIndex = position;
                            } else if (lastOffsetPixels < positionOffsetPixels && lastOffsetPixels != 0) {
                                //左滑
                                isGotoLeft = false;
                                willGoIndex = position + 1;
                            }
                        }

                        if (willGoIndex != Integer.MAX_VALUE && !mIsInUnderageMode) {
                            if (lastPostion != willGoIndex
                                    || lastColor == Color.TRANSPARENT
                                    || lastColor == BannerModel.DEFUALT_COLOR
                                    || lastTopRectColor == mDefaultTabColor) {
                                lastPostion = willGoIndex;
                                setLastColor(lastPostion, false, true);
                            }

                            int color;
                            int topRectColor;

                            float fraction = isGotoLeft ? (1 - positionOffset) : positionOffset;

                            if (lastColor == Color.TRANSPARENT && lastTopRectColor != Color.TRANSPARENT) {
                                lastColor = lastTopRectColor;
                            }

                            color = (int) (argbEvaluator.evaluate(fraction, myBgColor, lastColor));
                            topRectColor = (int) (argbEvaluator.evaluate(fraction, myBgColor,
                                    lastTopRectColor));
                            if (myBgColor == Color.TRANSPARENT) {
                                if (lastColor == Color.WHITE) {
                                    color = Color.TRANSPARENT;
                                }
                                if (lastTopRectColor == Color.WHITE) {
                                    topRectColor = Color.TRANSPARENT;
                                }
                            } else if (myBgColor == Color.WHITE) {
                                if (lastColor == Color.TRANSPARENT) {
                                    color = Color.TRANSPARENT;
                                }
                                if (lastTopRectColor == Color.TRANSPARENT) {
                                    topRectColor = Color.TRANSPARENT;
                                }
                            } else if (myBgColor == lastTopRectColor) {
                                color = Color.TRANSPARENT;
                            }

                            if (lastColor == Color.TRANSPARENT && lastTopRectColor == Color.TRANSPARENT) {
                                setTopViewColor(color, topRectColor, fraction);
                            } else if (myBgColor == Color.TRANSPARENT) {
                                setTopViewColor(color, topRectColor, 1 - fraction);
                            } else {
                                setTopViewColor(color, topRectColor, 0.0f);
                            }

                        }
                    } else {
                        if (curState == ViewPager.SCROLL_STATE_DRAGGING) {
                            lastPostion = position;
                        } else {
                            lastPostion = mPager.getCurrentItem();
                        }

                        onUserChangeIsTrue(lastPostion);
                    }

                    if (curState != ViewPager.SCROLL_STATE_DRAGGING && curState != ViewPager.SCROLL_STATE_SETTLING) {
                        lastState = curState;
                    }

                    lastOffsetPixels = positionOffsetPixels;
                }
                PerformanceMonitor.traceEnd("HomePage_initUi_onPageScrolled", 202);
            }

            @Override
            public void onPageScrollStateChanged(int i) {
                PerformanceMonitor.traceBegin("HomePage_initUi_onPageScrollStateChanged");

                int lastState = curState;
                curState = i;

                BannerView.canScrollByTime =
                        i != ViewPager.SCROLL_STATE_DRAGGING && i != ViewPager.SCROLL_STATE_SETTLING;
                mCurViewPageScrollState = i;

                // 第一页向左滑，或最后一页向右滑
                if ((i == ViewPager.SCROLL_STATE_IDLE) && (lastState == ViewPager.SCROLL_STATE_DRAGGING)) {
                    if (mPager != null && mPager.getAdapter() != null) {
                        //noinspection StatementWithEmptyBody
                        if (mPager.getCurrentItem() == 0) {
                            // 第一页向左滑
                        } else if (mPager.getCurrentItem() == (mPager.getAdapter().getCount() - 1)) {
                            // 最后一页向右滑
                            startEditTabFragment();
                        }
                    }
                }

                if (i == ViewPager.SCROLL_STATE_IDLE && (lastState == ViewPager.SCROLL_STATE_DRAGGING || lastState == ViewPager.SCROLL_STATE_SETTLING)) {
                    int position = mTabs.getCurrentItem();
                    if (lastPosition == position) {
                        return;
                    }
                    if (mTabModelList != null && position >= 0 && position < mTabModelList.size()) {
                        HomePageTabModel tabModel = mTabModelList.get(position);
                        if (tabModel != null) {
                            XMTraceApi.Trace trace = new XMTraceApi.Trace()
                                    .click(986)
                                    .put("Item", tabModel.getTitle())
                                    .put("categoryId", String.valueOf(tabModel.getCategoryId()))
                                    .put("currPage", "recommend")
                                    .put("url", tabModel.getUrl()) // 活动落地页 url
                                    .put(XmRequestIdManager.XM_REQUEST_ID, tabModel.getXmRequestId());
                            if (tabModel.isLocalCache()) {
                                trace.isLocalCache();
                            }
                            trace.createTrace();
                        }
                    }
                }
                PerformanceMonitor.traceEnd("HomePage_initUi_onPageScrollStateChanged", 203);
            }
        });

        mTabs.setOnTabClickListener(position -> {
            if (mPagerAdapter == null || mPager == null) {
                return;
            }
            PerformanceMonitor.traceBegin("HomePage_initUi_onTabClicked");

            Fragment fragment = mPagerAdapter.getFragmentAtPosition(position);
            if (mPager.getCurrentItem() == position) {
                if (fragment instanceof BaseFragment) {
                    ((BaseFragment) fragment).onRefresh();
                }
            } else {
                if (mTabModelList != null && position >= 0 && position < mTabModelList.size()) {
                    HomePageTabModel tabModel = mTabModelList.get(position);
                    if (tabModel != null) {
                        new XMTraceApi.Trace()
                                .click(986)
                                .put("Item", tabModel.getTitle())
                                .put("categoryId", String.valueOf(tabModel.getCategoryId()))
                                .put("currPage", "recommend")
                                .put("url", tabModel.getUrl()) // 活动落地页 url
                                .put(XmRequestIdManager.XM_REQUEST_ID, tabModel.getXmRequestId())
                                .createTrace();
                    }
                }
            }

            PerformanceMonitor.traceEnd("HomePage_initUi_onTabClicked", 204);

        });

        ChildXmlyTipManager.INSTANCE.addChangeListener(childXmlyChangeListener);

        PerformanceMonitor.traceEnd("HomePage_initUi_s9", 109);
        PerformanceMonitor.traceBegin("HomePage_initUi_s10");

        if (!UserInfoMannage.hasLogined()) {
            new UserTracking()
                    .setModuleType("登录引导")
                    .setSrcPage("首页")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
        }

        if (ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext())) {
            int plan = TempDataManager.getInstance().getInt("FRESH_GUIDE_PLAN");
            if (plan == 1) {
                addShowFreshGiftDialogAction(false);
            } else if (plan == 2) {
                addShowFreshGiftDialogAction(true);
            }
            addShowTipAction();
        }


        PerformanceMonitor.traceEnd("HomePage_initUi_s10", 110);
        PerformanceMonitor.traceBegin("HomePage_initUi_s11");

        HomeRecommendPageLoadingOptimizationManager.INSTANCE.addHomeRecommendPageLoadingListenersH(() -> {
            StartupOptManager.addMainLooperIdleHandler(() -> {
                // 收集下用户是否有录音权限
                HandlerManager.postOnBackgroundThreadDelay(() -> {
                    RecordPermissionReportUtil.reportRecordPermissionStatus(getContext());
                }, 5000);
                return false;
            });
            StartupOptManager.addMainLooperIdleHandler(() -> {
                preloadPlayPageXmlIfNeed();

                // 预加载一下这个类，让里面比较耗时的类初始化操作能在空闲时提前完成
                String tag = SoundPatchMainManager.TAG;
                return false;
            });
        });

        AdIvRecordManager.ivRecord();
        new AfterListenTaskNotificationManager();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ChildXmlyTipManager.ACTION_CHANGE_CHAT_XMLY_TYPE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mChatXmlyChangeRoleReceiver, intentFilter);

        PerformanceMonitor.traceEnd("HomePage_initUi_s11", 111);
        PerformanceMonitor.traceBegin("HomePage_initUi_s12");

        XmAppHelper.registerAppStatusChangedListener(mOnAppStatusChangedListener);
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.addAdShowAndDestroyListeners(this);

        PerformanceMonitor.traceEnd("HomePage_initUi_s12", 112);

        HomeSendRnEventUtils.init();

        PerformanceMonitor.traceEnd("HomePage_initUi_", 15);
    }

    private void initSlideBar() {
        if (MyListenAbUtil.INSTANCE.hasSlideBar()) {
            mSlideEntry.setVisibility(View.VISIBLE);
            mSlideEntry.setOnClickListener(v -> MyListenAbUtil.INSTANCE.openSlideBar("newHomePage"));
            if (findViewById(R.id.main_tv_search).getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) findViewById(R.id.main_tv_search).getLayoutParams();
                layoutParams.leftMargin = BaseUtil.dp2px(mContext, 1);
            }
            if (mIvSearchBarSign.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mIvSearchBarSign.getLayoutParams();
                layoutParams.rightMargin = 0;
            }
        }
    }

    private void preloadPlayPageXmlIfNeed() {
        if (!PageStartOpt.PlayPageOpt.enable()) {
            return;
        }
        List<ViewPool.InflateMany> tasks = Arrays.asList(
                new ViewPool.InflateMany(R.layout.main_play_page_skeleton_loading_view, 1),
                new ViewPool.InflateMany(R.layout.main_play_page_y, 1),
                new ViewPool.InflateMany(R.layout.main_audio_play_component_manuscript_y, 1),
                new ViewPool.InflateMany(R.layout.main_layout_yplay_video, 1),
                new ViewPool.InflateMany(R.layout.main_layout_doc_background_y, 1),
                new ViewPool.InflateMany(R.layout.main_audio_play_component_normal_covers_y, 1)
        );
        PageStartOpt.PlayPageOpt.preloadXmlIfNeed(tasks);
    }

    private SetBottomOvalViewColorRunnable mSetBottomOvalViewColorRunnable;

    private class SetBottomOvalViewColorRunnable implements Runnable {
        private int mColor;
        private int mTopRectColor;
        private float mFraction;

        public SetBottomOvalViewColorRunnable(final int color, final int topRectColor, final float fraction) {
            mColor = color;
            mTopRectColor = topRectColor;
            mFraction = fraction;
        }

        public void setData(final int color, final int topRectColor, final float fraction) {
            mColor = color;
            mTopRectColor = topRectColor;
            mFraction = fraction;
        }

        @Override
        public void run() {
            if (canUpdateUi()) {
                if (mBottomOvalView != null) {
                    mBottomOvalView.setColor(mColor, mTopRectColor, mFraction);
                }
            }
        }
    }

    private void setHistoryAndMoreButtonVisible() {
        boolean showMore = !MyListenAbUtil.INSTANCE.hasSlideBar();
//        mIvSearchBarHistoryBtn.setVisibility(showMore ? View.GONE : View.VISIBLE);
        mVMoreAction.setVisibility(showMore ? View.VISIBLE : View.GONE);
        if (showMore) {
            String tag = HomeMineMoreTagManager.INSTANCE.getTagText(mContext, HomeMineMoreTagManager.TYPE_HOME);
            if (TextUtils.isEmpty(tag)) {
                mTvMoreHot.setVisibility(View.GONE);
            } else {
                mTvMoreHot.setVisibility(View.VISIBLE);
                mTvMoreHot.setText(tag);
            }
            updateMoreHotDot();
        } else {
            mTvMoreHot.setVisibility(View.GONE);
        }
    }

    private void updateMoreHotDot() {
        if (mTvMoreHot.getVisibility() != View.VISIBLE
                // 外部的签到被隐藏了
                && mFreeListenIconManager != null && !mFreeListenIconManager.isOutSignShow()
                && !MmkvCommonUtil.getInstance(getContext()).getBoolean("hasClickSign", false)) {
            mViewMoreHotDot.setVisibility(View.VISIBLE);
        } else {
            if (mViewMoreHotDot.getVisibility() == View.VISIBLE) {
                mViewMoreHotDot.setVisibility(View.GONE);
            }
        }
    }

    private void updateRecognizeIcon(HomePageTabModel model) {
        if (model == null) {
            return;
        }
        if (mLottieChatXmlyBubbleRobot != null && mLottieChatXmlyBubbleRobot.getVisibility() == View.VISIBLE) {
            return;
        }
        boolean show = true;
        if (show == (mIvSearchRecognizeBtn.getVisibility() == View.VISIBLE)) {
            return;
        }
        showOrHideSearchRecognizeBtn(show);
    }

    private void updateFreeListenIcon() {
        if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
            if (mFreeListenIconManager == null) {
                mFreeListenIconManager = new FreeListenIconManager(mVgSearchBarRecommendPageAction, mContext);
            }
        } else {
            if (mFreeListenIconManager != null) {
                mFreeListenIconManager.hideFreeListen();
                mFreeListenIconManager.onPagePause();
            }
            mFreeListenIconManager = null;
            if (mRlSignVg.getVisibility() == View.GONE) {
                mRlSignVg.setVisibility(View.VISIBLE);
            }
        }
    }

    private void showChildProtectDialog() {
        if (getActivity() != null && ViewUtil.haveDialogIsShowing(getActivity())) {
            return;
        }

        if (ChildProtectManager.isChildMode(getContext())) {
            return;
        }

        if (ChildProtectDialogManager.INSTANCE.checkShowDialog(ChildProtectDialogManager.Position.LIVE_TAB)) {
            if (canUpdateUi()) {
                if (getActivity() != null && ViewUtil.haveDialogIsShowing(getActivity())) {
                    return;
                }

                ChildProtectDialogManager.INSTANCE.requestShowDialog(getChildFragmentManager(), "livepage");
            }
        }
    }

    private void tryShowVipProtocolDialog() {
        if (getActivity() != null && ViewUtil.haveDialogIsShowing(getActivity())) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            return;
        }
        boolean localAgreed = SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInMain.KEY_VIP_PROTOCOL_LOCAL_AGREED + UserInfoMannage.getUid(), false);
        if (localAgreed) {
            return;
        }

        // 首次启动不弹窗 但随后的登录会弹窗 用isLogin记录
        if ((ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext()) && isLogin)
                || !ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext())) {
            if (UserInfoMannage.isVipUser()) {
                MainCommonRequest.getShouldShowVipProtocol(new IDataCallBack<VipProtocolRsp>() {
                    @Override
                    public void onSuccess(@Nullable VipProtocolRsp response) {
                        if (response != null && response.getStatus() == VipProtocolRsp.STATUS_NEED_AGREE) {
                            if (canUpdateUi()) {
                                VipProtocolDialogFragment fragment = VipProtocolDialogFragment.newInstance(response);
                                fragment.show(getChildFragmentManager(), "vip_protocol");
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }
        }
    }

    private void addShowFreshGiftDialogAction(boolean condition) {
        EventManager.Action showFreshGiftDialogEvent = new EventManager.Action();
        showFreshGiftDialogEvent.delay = 250;
        showFreshGiftDialogEvent.name = "ShowFreshGiftDialog";
        showFreshGiftDialogEvent.uiRunnable = this::showFreshGiftDialog;
        if (condition) {
            showFreshGiftDialogEvent.addDependentEvent(new EventManager.Event(
                    "customize_page_destroy"));
        }
        EventManager.getInstance().addAction(showFreshGiftDialogEvent);

        EventManager.Action showFreshGiftMagneticEvent = new EventManager.Action();
        showFreshGiftMagneticEvent.delay = 250;
        showFreshGiftMagneticEvent.name = "ShowFreshGiftMagnetic";
        showFreshGiftMagneticEvent.uiRunnable = this::showFreshGiftMagnetic;
        showFreshGiftMagneticEvent.addDisableState("fresh_gift_dialog_clicked");
        showFreshGiftMagneticEvent.addDependentEvent(new EventManager.Event(
                "fresh_gift_dialog_dismiss"));
        EventManager.getInstance().addAction(showFreshGiftMagneticEvent);
    }

    private void addShowTipAction() {
        int plan = TempDataManager.getInstance().getInt("FRESH_GUIDE_PLAN");
        EventManager.Action showTipEvent = new EventManager.Action();
        showTipEvent.name = "ShowHomePageTips";
        showTipEvent.delay = 250;
        showTipEvent.uiRunnable = this::showListenTip;
        if (plan == 0) {
            showTipEvent.addDependentEvent(new EventManager.Event("customize_page_destroy"));
        } else if (plan == 1 || plan == 2) {
            showTipEvent.addDependentEvent(new EventManager.Event("fresh_gift_dialog_dismiss"));
        }
        EventManager.getInstance().addAction(showTipEvent);
    }

    private void showListenTip() {
        if (!canUpdateUi()) {
            return;
        }
        if (!isRealVisable()) {
            return;
        }
    }


    public void updateSearchBarOnActionChange() {
        if (null != mVgSearchBar) {
            mVgSearchBar.post(() -> {
                // 放在主线程刷新ui
                updateSearchBar(mPager.getCurrentItem());
            });
        }
    }

    private void setTabBg(int position, Bitmap bitmap, String coverPath, boolean isActive) {
        // 因为可能是异步调用，tab位置可能发生了改变，要判断一下。如果不同就不要去设置了。
        HomePageTabModel tabModel = null;
        if (position >= 0 && mTabModelList != null && position < mTabModelList.size()) {
            tabModel = mTabModelList.get(position);
        }
        if (bitmap != null && tabModel != null) {
            String tabCover = isActive ? tabModel.getActiveCoverPath() :
                    tabModel.getUnactiveCoverPath();
            if (coverPath != null && !coverPath.equals(tabCover)) {
                return;
            }
        }
        if (bitmap != null) {
            // 目前配置的图片都是按3倍的来配的，为了在不同手机上显示效果基本相同，根据分辨率密度把图片做个缩放
            float density = getResourcesSafe().getDisplayMetrics().density;
            float scale = density / 3;
            Matrix matrix = new Matrix();
            if (isActive) {
                // 对选中的图片做放大处理。
                scale = scale * 1.2f;
            }
            matrix.postScale(scale, scale);
            try {
                bitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(),
                        matrix, true);
            } catch (Exception e) {
                e.printStackTrace();
                if (ConstantsOpenSdk.isDebug) {
                    throw e;
                }
            }
        }
        mTabs.setTvBackgroundByPositionRes(position, bitmap, coverPath);
    }

    private int lastPosition = -1;

    @Override
    public void onUserChange(Fragment fragment, int position) {
        if (lastPosition != position) {
            lastPosition = position;
            onUserChangeIsTrue(position);
        }
    }

    private void onUserChangeIsTrue(int position) {
        if (!mIsInUnderageMode) {
            setLastColor(position, false, false);

            if (lastColor != Color.TRANSPARENT) {
                myBgColor = lastColor;
            } else if (lastTopRectColor != Color.TRANSPARENT) {
                myBgColor = lastTopRectColor;
            } else {
                myBgColor = lastColor;
            }

            setTopViewColor(lastColor, lastTopRectColor, 0.0f);

            if (mLastUpdateSearchBarPosition != position) {
                mLastUpdateSearchBarPosition = position;
                updateSearchBar(position);
                updateForegroundColor(position);
            }
        }
    }


    /**
     * @param lastPostion
     * @param ignoreViewTagColor
     * @param isTabScrolling     是否在tab滚动中
     * @return
     */
    private int setLastColor(int lastPostion, boolean ignoreViewTagColor, boolean isTabScrolling) {
        if (lastPostion < 0) {
            return lastColor;
        }

        lastColor = Color.TRANSPARENT;
        lastTopRectColor = Color.TRANSPARENT;
        boolean hasHeaderBgColor = false;
        if (mPagerAdapter == null) {
            return lastColor;
        }

        FragmentHolder fragmentHolder = mPagerAdapter.getFragmentHolderAtPosition(lastPostion);
        if (fragmentHolder != null) {
            Fragment fragment = null;
            if (fragmentHolder.realFragment != null) {
                fragment = fragmentHolder.realFragment.get();
            }
            if (!ignoreViewTagColor) {
                if (fragment != null && fragment.getView() != null) {
                    Object fraction = fragment.getView().getTag(R.id.main_pager_curr_fraction);
                    if (fraction instanceof Float) {
                        if (((Float) fraction) != 0.0f) {
                            Object tag = fragment.getView().getTag(R.id.main_pager_curr_color);
                            if (tag instanceof Integer) {
                                lastTopRectColor = (int) tag;
                                lastColor = Color.TRANSPARENT;
                                hasHeaderBgColor = true;
                            }
                        }
                    }
                }
            }

            // 取服务端配置
            if (lastColor == Color.TRANSPARENT && lastTopRectColor == Color.TRANSPARENT
                    && isThisTabHasDefualtColor(fragment)
                    && !hasHeaderBgColor) {
                try {
                    lastTopRectColor = getThisTabDefaultColorInt(fragmentHolder);
                    lastColor = Color.TRANSPARENT;
                    hasHeaderBgColor = true;
                } catch (Exception e) {
                    e.printStackTrace();
                    lastTopRectColor = Color.TRANSPARENT;
                    lastColor = Color.TRANSPARENT;
                }
            }

            if (lastColor == Color.TRANSPARENT) {
                BannerView bannerView = getBannerInThisPage(fragmentHolder);
                if (shouldShowHeaderBottomPart(fragmentHolder, bannerView)) {
                    // 有headerColor
                    if (hasHeaderBgColor) {
                        lastColor = lastTopRectColor;
                        lastTopRectColor = Color.TRANSPARENT;
                    } else if (bannerView != null) {
                        lastColor = (bannerView).getCurrMainColor();
                        lastTopRectColor = Color.TRANSPARENT;
                    }
                }
            }

            if (lastColor == Color.TRANSPARENT && lastTopRectColor == Color.TRANSPARENT && !hasHeaderBgColor) {
                // 没有焦点图的页面
                if (getBannerInThisPage(fragmentHolder) == null) {
                    lastColor = Color.TRANSPARENT;
                    lastTopRectColor = Color.TRANSPARENT;
                } else {
                    // 有焦点图但是banner没有显示出来
                    if (isTabScrolling) {
                        lastColor = Color.TRANSPARENT;
                        lastTopRectColor = mDefaultTabColor;
                    } else {
                        lastColor = Color.TRANSPARENT;
                        lastTopRectColor = Color.TRANSPARENT;
                    }
                }
            }

            if (lastColor == Color.WHITE) {
                lastColor = Color.TRANSPARENT;
            }
            if (lastTopRectColor == Color.WHITE) {
                lastTopRectColor = Color.TRANSPARENT;
            }
        }
        return lastColor;
    }

    private boolean noBannerItemType(FragmentHolder fragmentHolder) {
        if (fragmentHolder != null) {
            return ITEM_TYPE_LAMIA.equals(fragmentHolder.itemType)
                    || ITEM_TYPE_ACTIVITY.equals(fragmentHolder.itemType)
                    || ITEM_TYPE_CATEGORY.equals(fragmentHolder.itemType)
                    || ITEM_TYPE_FOLLOW.equals(fragmentHolder.itemType)
                    || ITEM_TYPE_H5.equals(fragmentHolder.itemType)
                    || ITEM_TYPE_VIDEO_TAB.equals(fragmentHolder.itemType)
                    || ITEM_TYPE_TEMPLATE_CATEGORY.equals(fragmentHolder.itemType) // 模板化频道页虽然显示了banner  但是要求顶部不跟随banner变化颜色
                    || (ITEM_TYPE_SINGLE_CATEGORY.equals(fragmentHolder.itemType) && fragmentHolder.args != null && String.valueOf(IAdConstants.ICategoryId.PODCAST_CATEGORY_ID).equals(fragmentHolder.args.getString(BundleKeyConstants.KEY_CATEGORY_ID))) // 新播客频道
                    || (CategoryCardUtil.isLocalListenNewPage(HomePageTabModel.ITEM_TYPE_LOCAL_LISTEN.equals(fragmentHolder.itemType)));
        }
        return true;
    }

    private ArrayList<FragmentHolder> fragmentlist;

    public void onLocalListenCityChanged() {
        if (!ToolUtil.isEmptyCollects(mTabModelList) && mHasLocalListenTab) {
            setTabData(mTabModelList);
        }
    }

    private void removeVipTab(List<HomePageTabModel> tabs) {
        if (ToolUtil.isEmptyCollects(tabs)) {
            return;
        }
        if (!BottomTabFragmentManager.INSTANCE.getUseNewTab()) {
            return;
        }
        for (HomePageTabModel tab : tabs) {
            if (HomePageTabModel.ITEM_TYPE_VIP.equals(tab.getItemType())) {
                tabs.remove(tab);
                return;
            }
        }
    }

    private void checkChangeEditTabButtonState() {
        boolean isShowMoreTab = MmkvCommonUtil.getInstance(mContext)
                .getBoolean(PreferenceConstantsInHost.KEY_HOME_PAGE_TAB_API_CHANGE_MODEL_DISPLAY, true);
        if (ChildProtectManager.isChildMode(mContext)) {
            return;
        }
        if (isShowMoreTab) {
            mIvEditTab.setVisibility(View.VISIBLE);
            mVEditTabShadow.setVisibility(View.VISIBLE);
        } else {
            mIvEditTab.setVisibility(View.GONE);
            mVEditTabShadow.setVisibility(View.GONE);
        }
    }

    private void setTabData(List<HomePageTabModel> tabs) {
        if (!canUpdateUi() || mPager == null || mTabs == null) {
            return;
        }
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("1__setTabData__tabs.size = " + tabs.size());
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            List<HomePageTabModel> customTabs = new ArrayList<>();
            boolean isRecommendDefaultSelect = true;
            for (HomePageTabModel homePageTabModel: tabs) {
                if (HomePageTabModel.ITEM_TYPE_RECOMMEND.equals(homePageTabModel.getItemType())) {
                    customTabs.add(homePageTabModel);
                }
                if (homePageTabModel != null && homePageTabModel.isDefaultSelected()) {
                    if (!HomePageTabModel.ITEM_TYPE_RECOMMEND.equals(homePageTabModel.getItemType())) {
                        isRecommendDefaultSelect = false;
                    }
                }
            }
            if (isRecommendDefaultSelect) {
                mCacheHomePageTabs.clear();
                mCacheHomePageTabs.addAll(tabs);
                tabs = customTabs;
            }
        }
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("2__setTabData__tabs.size = " + tabs.size());
//        removeVipTab(tabs);
        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment setTabData begin");
        checkChangeEditTabButtonState();
        if (!ToolUtil.isEmptyCollects(tabs)) {
            mTabModelList = tabs;
            List<FragmentHolder> lastFragmentList;
            if (fragmentlist == null) {
                fragmentlist = new ArrayList<>();
                lastFragmentList = new ArrayList<>(fragmentlist);
            } else {
                lastFragmentList = new ArrayList<>(fragmentlist);
                fragmentlist.clear();
            }

            List<String> notDestroyTabIds = new ArrayList<>();
            int intentTabIndex = -1;
            Iterator<HomePageTabModel> iterator = mTabModelList.iterator();
            mHasLocalListenTab = false;

            while (iterator.hasNext()) {
                HomePageTabModel model = iterator.next();
                if (model != null && model.getItemType() != null) {
                    if (HomePageTabModel.ITEM_TYPE_WOTING_NEW.equals(model.getItemType())) {
                        // 如果返回我听，则移除掉
                        iterator.remove();
                    } else {
                        Class<? extends Fragment> classByType = null;
                        if (mIsInUnderageMode) {
                            // 青少年模式下，只显示推荐页
                            if (HomePageTabModel.ITEM_TYPE_RECOMMEND.equals(model.getItemType())) {
                                classByType = HomePageTabModelUtil.getClassByType(model.getItemType(), true, model,this);
                            }
                        } else {
                            if (HomePageTabModel.ITEM_TYPE_LOCAL_LISTEN.equals(model.getItemType())) {
                                String cityCode =
                                        SharedPreferencesUtil.getInstance(BaseApplication.getTopActivity())
                                                .getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_CODE);
                                String cityName =
                                        SharedPreferencesUtil.getInstance(BaseApplication.getTopActivity())
                                                .getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_NAME);
                                if (!TextUtils.isEmpty(cityCode) && !TextUtils.isEmpty(cityName)) {
                                    model.setCityCode(cityCode);
                                    model.setTitle(cityName);
                                }
                                mHasLocalListenTab = true;
                            }
                            classByType = HomePageTabModelUtil.getClassByType(model.getItemType(), true, model,this);
                            if (ITEM_TYPE_SINGLE_CATEGORY.equals(model.getItemType())
                                    && model.getCategoryId() == IAdConstants.ICategoryId.PODCAST_CATEGORY_ID) {
                                classByType = PodCastFragmentV2.class;
                            }
                            if (model.getCategoryId() == KidsPage.KID_CHANNEL_CATEGORY_ID) {
                                Class<? extends Fragment> kidFragment = getKidTabFragment();
                                if (kidFragment != null) {
                                    classByType = kidFragment;
                                }
                            }
                            if (ConfigureCenter.getInstance().getBool(ReaderConstants.CONFIG_CENTER_QIJI, ReaderConstants.CONFIG_CENTER_E_BOOK_TAB_BOOKSTORE_DISPLAY, true)) {
                                if (ITEM_TYPE_TEMPLATE_CATEGORY.equals(model.getItemType()) && 130 == model.getCategoryId()) {
                                    classByType = EBookChannelFragment.class;
                                }
                            }
                            if (ITEM_TYPE_TEMPLATE_CATEGORY.equals(model.getItemType()) && 1071 == model.getCategoryId()) {
                                classByType = EBookChannelFragment.class;
                            }
                        }
                        if (classByType != null) {
                            Bundle bundle = HomePageTabModelUtil.getArguments(model, true);
                            // 因为分类推荐页和vip页需要适配在viptab页的情况，这两个页面都增加了根据这个参数判断是否发广播的逻辑。
                            // 所以首页这里也都加一下这个参数。
                            bundle.putBoolean(BundleKeyConstants.KEY_REFRESH_LIST_VIEW_SEND_SCROLL_BROADCAST, true);
                            String headerBgColor = null;
                            //暗黑模式下，不取服务端配置的颜色
                            if (!BaseFragmentActivity.sIsDarkMode) {
                                HomePageTabTheme tabTheme = model.getTabTheme();
                                if (tabTheme != null) {
                                    headerBgColor = tabTheme.getHeaderBGColor();
                                }
                            }
                            fragmentlist.add(new FragmentHolder(classByType, model.getTitle(),
                                    bundle, model.getId(), model.getItemType(), headerBgColor));

                            if (fragmentlist.size() <= NOT_DESTROY_TAB_NUM) {
                                notDestroyTabIds.add(model.getId());
                            }
                            if (intentTabIndex < 0 && !TextUtils.isEmpty(mIntentTab) && model.getItemType().equals
                                    (mIntentTab)) {
                                intentTabIndex = fragmentlist.size() - 1;
                            }
                            if (mHasFixLiveTab == 1 && model.getId() != null && model.getId().equals("lamia")) {
                                mHasFixLiveTab = 2;
                            }
                        } else {  // 对应的fragment 类不存在时需要将model从列表中移除，否则会造成model的index和实际tab的index不一致
                            if (model.getId() != null && model.getId().equals("lamia") && mHasFixLiveTab == 0) {
                                mHasFixLiveTab = 1;
                            }
                            iterator.remove();
                        }
                    }
                } else {
                    iterator.remove();
                }
            }

            mLastUpdateSearchBarPosition = -1;

            int defaultSelectedIndex = 0;
            int currentItem = 0;
            if (mIsFirstLoadTab) {
                for (int i = 0; i < mTabModelList.size(); i++) {
                    HomePageTabModel model = mTabModelList.get(i);
                    if (model != null && model.isDefaultSelected()) {
                        defaultSelectedIndex = i;
                        break;
                    }
                }
                currentItem = intentTabIndex >= 0 &&
                        intentTabIndex < fragmentlist.size() ? intentTabIndex : defaultSelectedIndex;
                if (currentItem != 0) {
                    try {
                        // 用反射设置选中页，避免第一页数据加载。
                        // 没设置Adapter之前是没法通过setCurrentItem设置的，所以如果默认需要跳到第2页，第一页也会加载数据。
                        FieldUtils.writeField(mPager, "mCurItem", currentItem);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (isNewRecommendTab(currentItem)) {
                    getRlContainer().setBackgroundResource(R.color.main_color_F7F9FC_000000);
                }
                if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.needDelayOtherTabCreate()) {
                    // 如果默认页面是推荐页，推荐页加载完成前不加载相邻的tab
                    if (currentItem >= 0 && currentItem < mTabModelList.size()) {
                        HomePageTabModel tabModel = mTabModelList.get(currentItem);
                        if (tabModel != null && HomePageTabModel.ITEM_TYPE_RECOMMEND.equals(tabModel.getItemType())) {
                            mPager.disableFillNeighbourTab(true);
                            HomeRecommendPageLoadingOptimizationManager.INSTANCE.addHomeRecommendPageLoadingListenersH(
                                    () -> {
                                        if (canUpdateUi()) {
                                            mPager.disableFillNeighbourTab(false);
                                        }
                                    });
                            // 确保会恢复
                            HandlerManager.postOnUIThreadDelay(() -> {
                                if (canUpdateUi()) {
                                    mPager.disableFillNeighbourTab(false);
                                }
                            }, 5000);
                        }
                    }
                }
            }

            if (mPagerAdapter == null) {
                mPagerAdapter = new HomePageTabAdapter(getChildFragmentManager(), fragmentlist);
                mPagerAdapter.setUserChange(this);
                mPagerAdapter.setShouldNotDestroyFragmentId(notDestroyTabIds);
                mPager.removeAllViews();
                mPager.setAdapter(mPagerAdapter);
                mTabs.setViewPager(mPager);
                resetBottomViewHeight(0);
            } else {
                Map<WeakReference<Fragment>, Integer> tabsNewPositionMap = new HashMap<>();
                if (!ToolUtil.isEmptyCollects(fragmentlist)) {
                    for (int i = 0; i < fragmentlist.size(); i++) {
                        FragmentHolder fragmentHolder = fragmentlist.get(i);
                        for (int j = 0; j < lastFragmentList.size(); j++) {
                            FragmentHolder oldFragmentHolder = lastFragmentList.get(j);
                            // 存在id相同，但可能是不同类的情况，比如推荐页。所以这里还要判断下是否是同一个类
                            if (!TextUtils.isEmpty(fragmentHolder.id) && fragmentHolder.id.equals(oldFragmentHolder.id)
                                    && fragmentHolder.fragment != null
                                    && fragmentHolder.fragment.equals(oldFragmentHolder.fragment)) {
                                fragmentHolder.realFragment = oldFragmentHolder.realFragment;
                                if (fragmentHolder.realFragment != null) {
                                    tabsNewPositionMap.put(fragmentHolder.realFragment, i);
                                }
                                break;
                            }
                        }

                    }
                }
                mPagerAdapter.setTabsNewPositionMap(tabsNewPositionMap);
                mPagerAdapter.setShouldNotDestroyFragmentId(notDestroyTabIds);
                mPagerAdapter.notifyDataSetChanged();
                mTabs.notifyDataSetChanged();
            }

            if (mHasFixLiveTab == 2 && mPager != null && !mIsFirstLoadTab) {
                mHasFixLiveTab = 3;
                int fixDefaultSelectedIndex = 0;
                for (int i = 0; i < mTabModelList.size(); i++) {
                    HomePageTabModel model = mTabModelList.get(i);
                    if (model != null && model.isDefaultSelected()) {
                        fixDefaultSelectedIndex = i;
                        break;
                    }
                }
                if (mPager.getCurrentItem() != fixDefaultSelectedIndex) {
                    mPager.setCurrentItem(fixDefaultSelectedIndex);
                }
            }
            AutoTraceHelper.bindData(mTabs, fragmentlist, null, AutoTraceHelper.MODULE_DEFAULT);

            if (mIsFirstLoadTab) {
                mIsFirstLoadTab = false;

                mTabs.updateActivateTab(currentItem);
                if (defaultSelectedIndex < mTabModelList.size() && mTabModelList.get(defaultSelectedIndex) != null) {
                    mDefaultPageType = mTabModelList.get(defaultSelectedIndex).getItemType();
                }
                if (mPager.getCurrentItem() >= 0 && mPager.getCurrentItem() < mTabModelList.size()) {
                    HomePageTabModel model = mTabModelList.get(mPager.getCurrentItem());
                    if (model != null) {
                        new UserTracking("首页", getTagNameByType(model)).statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);

                    }
                    if (!TextUtils.equals("adLaunch",
                            UserTrackCookie.getInstance().getXmSource())) {
                        UserTrackCookie.getInstance().clearXMLYResource();
                        if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_RECOMMEND,
                                mDefaultPageType)) {
                            UserTrackCookie.getInstance().setXmContent("homepage", "homepage",
                                    null);
                        }
                    }
                }
                if (!TextUtils.isEmpty(mIntentTab) && mIntentTab.equals(TabFragmentManager.INTENT_CHILD_TAB_ANCHOR_LIVE)) {
                    switchToAnchorLiveFragment();
                }
                mIntentTab = null;
                if (!TextUtils.isEmpty(mSelectTab)) {
                    switchToSelectTab(mSelectTab);
                    mSelectTab = "";
                }
                if (!TextUtils.isEmpty(mPendingSwitchToTabId)) {
                    switchToFragmentById(mPendingSwitchToTabId);
                    mPendingSwitchToTabId = "";
                }
                updateSearchWord();
            }

            mTabs.setmGlobalCallback(() -> {
                for (int i = 0; i < mTabModelList.size(); i++) {
                    final int index = i;
                    HomePageTabModel tabModel = mTabModelList.get(i);
                    boolean isActive = false;
                    if (tabModel != null) {
                        if (!TextUtils.isEmpty(tabModel.getActiveCoverPath())
                                || !TextUtils.isEmpty(tabModel.getUnactiveCoverPath())) {
                            String coverUrl = tabModel.getUnactiveCoverPath();
                            if (mPager.getCurrentItem() == index) {
                                coverUrl = tabModel.getActiveCoverPath();
                                isActive = true;
                            }

                            if (TextUtils.isEmpty(coverUrl)) {
                                return;
                            }

                            final String finalCoverUrl = coverUrl;
                            final boolean finalIsActive = isActive;
                            ImageManager.from(mContext).downloadBitmap(finalCoverUrl,
                                    (lastUrl, bitmap) -> setTabBg(index, bitmap, finalCoverUrl, finalIsActive), false);
                        } else {
                            setTabBg(index, null, null, false);
                        }
                    }
                }
            });
        }

        if (!isLoadDataAfterBundleInstall) {
            boolean checkTabChange = false;
            HomePageTabModel curTab = getCurrentTabModel();
            if (curTab != null && ((mPendingSwitchToTab != null && !curTab.getId().equals(mPendingSwitchToTab.getId()))
                    || (!TextUtils.isEmpty(mPendingSwitchToTabId) && !curTab.getId().equals(mPendingSwitchToTabId)))) {
                checkTabChange = true;
            }
            if (!checkTabChange) {
                traceOnTabShowDelay();
            } else {
                waitForTabScroll();
            }
        }
        isLoadDataAfterBundleInstall = false;

        if (mPendingSwitchToTab != null) {
            switchToFragmentById(mPendingSwitchToTab.getId());
            mPendingSwitchToTab = null;
        } else if (!TextUtils.isEmpty(mPendingSwitchToTabId)) {
            switchToFragmentById(mPendingSwitchToTabId);
            mPendingSwitchToTabId = "";
        }
        findLiveTabIndex();
        if (mLiveModleIndex >= 0 && !mHasRequestLiveRedDotOnInit) {
            mHasRequestLiveRedDotOnInit = true;
            checkLiveTabRedDot();
        }
        Logger.logToFile("HomePageFragment" + "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = HomePageFragment setTabData finish");
    }

    private boolean isNewRecommendTab(int curIndex) {
        return isNewRecommendTab(curIndex, true);
    }

    private boolean isNewRecommendTab(int curIndex, boolean isCheckDarkMode) {
        if (mTabModelList == null || mTabModelList.isEmpty()
                || curIndex < 0 || curIndex >= mTabModelList.size()) {
            return false;
        }

        if (isCheckDarkMode && BaseFragmentActivity2.sIsDarkMode) {
            return false;
        }

        HomePageTabModel homePageTabModel = mTabModelList.get(curIndex);
        if (homePageTabModel != null && TextUtils.equals(HomePageTabModel.ITEM_TYPE_RECOMMEND, homePageTabModel.getItemType())
                && RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
            return true;
        }
        return false;
    }

    private Class<? extends Fragment> getKidTabFragment() {
        try {
            if (Configure.kidBundleModel.needAsync()) {
                Router.getActionByCallback(Configure.BUNDLE_KID, this , true, BundleModel.DOWNLOAD_IN_BACKGROUND);
            } else {
                KidActionRouter kidActionRouter = Router.getActionRouter(Configure.BUNDLE_KID);
                if (kidActionRouter != null) {
                    return kidActionRouter.getFunctionAction().getKidChannelFragment();
                }
            }
        } catch (Throwable thr) {
            thr.printStackTrace();
        }
        return null;
    }

    private void waitForTabScroll() {
        isWaittingTabScroll = true;
        postOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
                if (isWaittingTabScroll) {
                    traceOnTabShowDelay();
                }
            }
        }, 800);
    }

    private void traceOnTabShowDelay() {
        postOnUiThread(this::traceOnTabShow);
    }

    private void traceOnTabShow() {
        if (mTabs.getChildAt(0) instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) mTabs.getChildAt(0);
            int childCount = viewGroup.getChildCount();
            int firstVisibleViewPos = -1;
            for (int i = 0; i < childCount; i++) {
                View view = viewGroup.getChildAt(i);
                if (com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(view)) {
                    if (firstVisibleViewPos < 0) {
                        firstVisibleViewPos = i;
                    }
                    int[] location = new int[2];
                    view.getLocationOnScreen(location);
                    if (!ToolUtil.isEmptyCollects(mTabModelList) && i < mTabModelList.size() && location[0] < mEditIconLeftX) {
                        traceSingleTab(mTabModelList.get(i), i + 1);
                    }
                } else if (firstVisibleViewPos >= 0) {
                    return;
                }
            }
        }
    }

    private void traceSingleTab(HomePageTabModel homePageTabModel, int positionNew) {
        if (homePageTabModel == null) {
            return;
        }
        // 全局-顶TAB  控件曝光
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(18062)
                .setServiceId("slipPage")
                .put("Item", homePageTabModel.getTitle())
                .put("categoryId", String.valueOf(homePageTabModel.getCategoryId()))
                .put("url", homePageTabModel.getUrl()) // 活动落地页 url
                .put(XmRequestIdManager.CONT_ID, homePageTabModel.getId())
                .put(XmRequestIdManager.CONT_TYPE, "homePageTab")
                .put(XmRequestIdManager.XM_REQUEST_ID, homePageTabModel.getXmRequestId())
                .put("positionNew", String.valueOf(positionNew));
        if (homePageTabModel.isLocalCache()) {
            trace.isLocalCache();
        }
        trace.createTrace();
    }

    public void switchToSelectTab(String selectTab) {
        if (TextUtils.isEmpty(selectTab)) {
            return;
        }
        if (mPager != null && mPagerAdapter != null) {
            if (!ToolUtil.isEmptyCollects(mTabModelList)) {
                int index = -1;
                for (int i = 0; i < mTabModelList.size(); i++) {
                    HomePageTabModel tabModel = mTabModelList.get(i);
                    if (tabModel != null && selectTab.equals(tabModel.getTitle())) {
                        index = i;
                        break;
                    }
                }
                if (index == -1) { //没找到跳转推荐
                    index = 0;
                }
                mPager.setCurrentItem(index, false);
            }
        }
    }

    public void switchToSelectTabId(String selectTabId) {
        if (TextUtils.isEmpty(selectTabId)) {
            return;
        }
        if (mPager != null && mPagerAdapter != null) {
            if (!ToolUtil.isEmptyCollects(mTabModelList)) {
                int index = -1;
                for (int i = 0; i < mTabModelList.size(); i++) {
                    HomePageTabModel tabModel = mTabModelList.get(i);
                    if (tabModel != null && selectTabId.equals(tabModel.getId())) {
                        index = i;
                        break;
                    }
                }
                if (index == -1) { //没找到跳转推荐
                    index = 0;
                }
                mPager.setCurrentItem(index, false);
            }
        }
    }

    private int getTabIndexByType(String contentType) {
        if (mTabModelList == null || mTabModelList.isEmpty()) {
            return -1;
        }
        for (int i = 0; i < mTabModelList.size(); i++) {
            HomePageTabModel model = mTabModelList.get(i);
            if (model != null && !TextUtils.isEmpty(model.getItemType()) && model.getItemType().equals(contentType)) {
                return i;
            }
        }
        return -1;
    }

    public boolean switchFindingTabTo(String contentType) {
        int index = getTabIndexByType(contentType);
        if (index >= 0) {
            mPager.setCurrentItem(index);
            return true;
        }
        return false;
    }

    private void startEditTabFragment() {
        HomePageTabAndChannelListNewFragmentDialog fragment = new HomePageTabAndChannelListNewFragmentDialog();
        fragment.setCallbackFinish(mFragmentFinishCallback);
        startFragment(fragment, R.anim.main_slide_in_top, R.anim.main_slide_out_top);
        isGotoEditTab = true;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }

        if (v.getId() == R.id.main_iv_edit_tab) {
            if (MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(PreferenceConstantsInHost.KEY_CATEGORY_V2_EXPERIMENT_2_HOMEPAGE, false)) {
                startFragment(CategoryListV2Fragment.newInstance(false, 0, false, "home_page"));
            } else {
                startEditTabFragment();
            }

            new UserTracking()
                    .setSrcPage("首页")
                    .setSrcModule("TAB")
                    .setItem("page")
                    .setItemId("全部分类页")
                    .setSrcSubModule("分类")
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (v.getId() == R.id.main_tv_search_bar_action) {
            if (mPager != null) {
                int index = mPager.getCurrentItem();
                if (!ToolUtil.isEmptyCollects(mTabModelList) && index >= 0 && index < mTabModelList.size()) {
                    HomePageTabModel tabModel = mTabModelList.get(index);
                    if (tabModel.getSearchBoxRightContent() != null
                            && !TextUtils.isEmpty(tabModel.getSearchBoxRightContent().getIting())
                            && getActivity() instanceof MainActivity) {
                        NativeHybridFragment.start(((MainActivity) getActivity())
                                , tabModel.getSearchBoxRightContent().getIting(), true);
                        String desc = !TextUtils.isEmpty(tabModel.getSearchBoxRightContent().getDescription())
                                ? tabModel.getSearchBoxRightContent().getDescription() : "";
                        new XMTraceApi.Trace()
                                .click(3075)
                                .put("Item", desc)
                                .put("categoryId", String.valueOf(tabModel.getCategoryId()))
                                .put("currPage", "categoryRecommend")
                                .createTrace();

                        new UserTracking()
                                .setSrcPage(tabModel.getItemType())
                                .setSrcPageId(tabModel.getId())
                                .setItem("iting")
                                .setSrcTitle(tabModel.getSearchBoxRightContent().getDescription())
                                .statIting(UserTracking.APP_NAME_EVENT,
                                        XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                }
            }
        } else if (R.id.main_vg_search_bar_vip_enter_quanzi == v.getId()) {
            handleVipQuanziEnterClick();
        } else if (v.getId() == R.id.main_v_more_action) {
            handleMoreClick();
        } else if (v.getId() == R.id.main_iv_search_bar_live_start) {
            try {
                // 正常来讲，能显示这个按钮，是在直播tab，那直播模块肯定安装了，这里不去设监听了。
                Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().handClickEventByLive(ILiveFunctionAction.CLICK_HOME_TAB_START_LIVE, v, fragmentlist.get(mPager.getCurrentItem()).realFragment.get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (v.getId() == R.id.main_iv_search_bar_live_mine) {
            try {
                Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().handClickEventByLive(ILiveFunctionAction.CLICK_HOME_TAB_MINE_LIVE, v, fragmentlist.get(mPager.getCurrentItem()).realFragment.get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } /*else if (v.getId() == R.id.main_tv_exit_underage_mode) {
            try {
                BaseFragment2 fragment;
                if (ChildProtectManager.isChildBind(mContext)) {
                    fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newChildPlatformFragment();
                } else {
                    fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newChildProtectionSettingFragment();
                }
                if (fragment != null) {
                    startFragment(fragment);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }*/ else if (v.getId() == R.id.main_tv_selected_age_range) {
            if (ChildProtectManager.isChildBind(mContext)) {
                startFragment(ChildPlatformFragment.newInstance());
            } else {
                startFragment(ChildProtectionSettingFragment.newInstance());
            }
        } else if (v.getId() == R.id.main_tv_exit_underage_mode) {
            ModeSwitchUtil.clickModeSwitch(false);
        } else if (R.id.main_iv_search_bar_quality_cart_action == v.getId()) {
            ShoppingCartUtil.markCartHasBeenClicked();
            ShoppingCartUtil.goToShoppingCart(this);
            updateRedDotInfo(HomePageRedDotManager.TYPE_QUALITY_PAGE, 0);
            updateRedDotInfo(HomePageRedDotManager.TYPE_RECOMMEND_PAGE, 0);
        } else if (R.id.main_iv_search_bar_sign == v.getId() || R.id.main_iv_search_bar_sign_effect == v.getId()) {
            if (mSignInfoModel != null && mSignInfoModel.isShow()) {
                String signStatus = mSignInfoModel.isSignIn() ? "已签到" : "未签到";
                String iconType = TextUtils.isEmpty(mSignInfoModel.getIcon()) ? "常规" : "红包";
                if (mSignRedDotPosition.isShowRedDot()) {
                    // 全局页-红点频控  点击事件
                    new XMTraceApi.Trace()
                            .click(59651) // 用户点击时上报
                            .put("currPage", "forAll")
                            .put("xmRequestId", mSignInfoModel.getXmRequestId())
                            .put("guideType", "引导")
                            .put("tabName", "签到")
                            .createTrace();
                }
                // 新首页-签到入口  点击事件
                new XMTraceApi.Trace()
                        .click(48416) // 用户点击时上报
                        .put("currPage", "newHomePage")
                        .put("status", signStatus)
                        .put("unReadNum", String.valueOf(isSignRedDotShown() ? 1 : 0))
                        .put("iconType", iconType)
                        .put("style", "页面")
                        .put(XmRequestIdManager.XM_REQUEST_ID, mSignInfoModel.getXmRequestId())
                        .createTrace();
                if (ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME,
                        CConstants.Group_toc.SIGNIN_NEED_LOGIN, false) && !UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mContext);
                    return;
                }
                gotoSign();
                hideSignRedDot();
            }
        } else if (v == mLottieChatXmlyBubbleRobot || v == mTvChatXmlyBubble) {
            if (ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
                boolean isClickSug = mTvChatXmlyBubble.getVisibility() == View.VISIBLE || v == mTvChatXmlyBubble;
                mPoppyEndAnimator.cancel();
                mLottieChatXmlyBubbleRobot.cancelAnimation();
                AgentConfig agentConfig = ChildXmlyTipManager.INSTANCE.getAgentConfig();
                if (isClickSug && agentConfig != null && agentConfig.getAgentContent() != null && agentConfig.isXiaoYaSugValid()) {
                    HashMap<String, String> params = new HashMap<>();
                    params.put("orgSug", agentConfig.getAgentContent().getOrgSug());
                    params.put("sugType", agentConfig.getAgentContent().getSugType());
                    params.put("trackId", agentConfig.getAgentContent().getTrackId() + "");
                    ChildXmlyTipManager.INSTANCE.goChildChatXmlyWithParams("newHomePage", params);
                    ChildXmlyTipManager.INSTANCE.traceSugClick();
                } else if (agentConfig != null && agentConfig.isBoBoStyleVaild()) {
                    HashMap<String, String> params = new HashMap<>();
                    params.put("boboSugValid", "1");
                    ChildXmlyTipManager.INSTANCE.goChildChatXmlyWithParams("newHomePage", params);
                } else {
                    ChildXmlyTipManager.INSTANCE.goChildChatXmly("newHomePage");
                }
            } else {
                if (mTvChatXmlyBubble != null) {
                    String text = mTvChatXmlyBubble.getText().toString();
                    ChatXmlyPopupManager.INSTANCE.traceBubbleClick("首页", text);
                }
                if (mIvSearchRecognizeBtn != null) {
                    mIvSearchRecognizeBtn.performClick();
                }
            }
        }
    }

    private void handleMoreClick() {
//        new HomePageSearchBarMoreActionPopupWindow(getActivity(), this, getRedDotManager()).showAsDropDown(
//                mIvSearchBarMoreActionBtn, BaseUtil.dp2px(getActivity(), 10), -BaseUtil.dp2px(getActivity(), 6));
        if (mVMoreAction != null) {
            int[] positions = new int[2];
            mVMoreAction.getLocationOnScreen(positions);
            HomePageSearchBarMoreActionDialog dialog = HomePageSearchBarMoreActionDialog.getInstance(this,
                    getRedDotManager(), positions[1] + mVMoreAction.getHeight(), mSignInfoModel);
            dialog.show(getChildFragmentManager(), "HomePageSearchBarMoreActionDialog");
        }
        mTvMoreHot.setVisibility(View.GONE);
        HomeMineMoreTagManager.INSTANCE.onTagClick(mContext, HomeMineMoreTagManager.TYPE_HOME);
        HomePageTabModel tabModel = getCurrentTabModel();
        if (tabModel != null) {
            // 新首页-操作  点击事件
            new XMTraceApi.Trace()
                    .click(3196)
                    .put("categoryId", String.valueOf(tabModel.getCategoryId()))
                    .put("currPage", tabModel.getCurrPageForTrace())
                    .put("pageTitle", tabModel.getTitle())
                    .put(XmRequestIdManager.XM_REQUEST_ID, tabModel.getXmRequestId())
                    .createTrace();
        }

        new UserTracking()
                .setSrcPage("首页_推荐")
                .setSrcModule("加号")
                .setItem("button")
                .setItemId("加号")
                .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
    }

    private void handleVipQuanziEnterClick() {
        if (null == mIvSearchBarVipBtn
                || (null == mIvSearchBarVipBtn.getTag())
                || !(mIvSearchBarVipBtn.getTag() instanceof String)) {
            return;
        }
        String url = (String) mIvSearchBarVipBtn.getTag();
        if (StringUtil.isEmpty(url)) {
            return;
        }
        BaseFragment fragment = NativeHybridFragment.newInstance(url, true);
        startFragment(fragment);
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void loadTabData() {
        if (mLoadTabTask != null && mLoadTabTask.getStatus() == AsyncTask.Status.RUNNING) {
            return;
        }
        mLoadTabTask = new LoadTabTask(this);
        mLoadTabTask.myexec();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_page;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {

    }

    protected View inflateViewInner(LayoutInflater inflater, int layoutId, ViewGroup container, boolean attachToRoot) {
        try {
            if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
//                return tryUsePreInflateView(inflater, layoutId, container, attachToRoot);
                return ViewPool.getInstance().getViewForFragment(inflater, layoutId, container, attachToRoot, "HomePageFragment");
            }
            return inflater.inflate(layoutId, container, attachToRoot);
        } catch (Throwable t) {
            throw t;
        }
    }

    private View tryUsePreInflateView(LayoutInflater inflater, int layoutId, ViewGroup container, boolean attachToRoot) {
        try {
            View preloadViewFromPool = ViewPool.getInstance().getPreloadViewFromPool(R.layout.main_fra_home_page);
            Log.d("z_inflate", "inflateViewInner, preloadViewFromPool: " + preloadViewFromPool + ",attachToRoot: " + attachToRoot);

            if (preloadViewFromPool != null) {

                if (attachToRoot) {
                    container.addView(preloadViewFromPool, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                    if (container.getParent() != null) {
                        ((ViewGroup) container.getParent()).removeView(container);
                    }
                } else {
                    preloadViewFromPool.setLayoutParams( new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                    return preloadViewFromPool;
                }
                return container;
            }

        } catch (Throwable t) {
            t.printStackTrace();
        }
        return inflater.inflate(layoutId, container, attachToRoot);
    }

    public void onRefresh() {
        if (mPagerAdapter != null && mPager != null) {
            Fragment f = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            if (f instanceof NativeHybridFragment) {
                ((NativeHybridFragment) f).gotoTop();
            } else if (f instanceof BaseFragment) {
                ((BaseFragment) f).onRefresh();
            }
        }
    }

    private int getCurTabIndex() {
        if (mPager != null) {
            return mPager.getCurrentItem();
        }
        return 0;
    }

    public boolean isCurrTabClass(Class clazz) {
        return mPagerAdapter != null && clazz == mPagerAdapter.getFragmentClassAtPositon(getCurTabIndex());
    }

    private int getDefaultSearchIconColor() {
        // 推荐频道有默认值  非推荐频道使用原先的值 不改动
        if (isNewRecommendTab(getCurTabIndex(), false)) {
            if (BaseFragmentActivity2.sIsDarkMode) {
                return 0x80ffffff;
            } else {
                return DEFAULT_COLOR_SEARCH_RECOMMEND;
            }
        }
        return ColorUtil.INVALID_COLOR;
    }

    private int getNormalSearchIconColor(boolean isRecognize) {
        int defaultColor = getDefaultSearchIconColor();
        // 推荐频道使用特定的颜色 非推荐频道使用原先的值 不改动
        if (defaultColor != ColorUtil.INVALID_COLOR) {
            return defaultColor;
        }
        if (isRecognize) {
            return DEFAULT_COLOR_RECOGNIZE;
        }
        return DEFAULT_COLOR_SEARCH;
    }

    private String getTagNameByType(HomePageTabModel model) {
        String tagName = "";
        if (model != null) {
            tagName = model.getTitle();
            switch (model.getItemType()) {
                case ITEM_TYPE_CATEGORY:
                    tagName = "发现_分类";
                    break;
                case HomePageTabModel.ITEM_TYPE_LIVE:
                    tagName = "发现_广播";
                    break;
                case HomePageTabModel.ITEM_TYPE_ANCHOR:
                    tagName = "发现_主播";
                    break;
                case HomePageTabModel.ITEM_TYPE_LAMIA:
                    tagName = "发现_直播";
                    break;
                case HomePageTabModel.ITEM_TYPE_RECOMMEND:
                    tagName = "发现_推荐";
                    break;
                default:
                    break;

            }
        }
        return tagName;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            Bundle bundle = getArguments();
            if (bundle != null && bundle.containsKey(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY)) {
                String intentTab =
                        bundle.getString(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY);
                bundle.remove(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY);
                if (!TextUtils.isEmpty(intentTab)) {
                    switchToAnchorLiveFragment();
                }
            }
            if (bundle != null && bundle.containsKey(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME)) {
                String selectTab = bundle.getString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME);
                bundle.remove(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME);
                if (!TextUtils.isEmpty(selectTab)) {
                    switchToSelectTab(selectTab);
                }
            }
            if (bundle != null && bundle.containsKey(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID)) {
                String tabId = bundle.getString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID);
                boolean hasModifiedTab = bundle.getBoolean(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_HAS_CHANGE);
                bundle.remove(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID);
                bundle.remove(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_HAS_CHANGE);
                if (hasModifiedTab) { //修改了tab重新获取数据
                    loadTabData();
                    mPendingSwitchToTabId = tabId;
                } else {
                    switchToFragmentById(tabId);
                }
            }
        }
    }

    private void switchToFragmentById(String id) {
        if (TextUtils.isEmpty(id)) {
            return;
        }
        if (mPager != null && mPagerAdapter != null) {
            if (!ToolUtil.isEmptyCollects(fragmentlist)) {
                int index = -1;
                for (int i = 0; i < fragmentlist.size(); i++) {
                    FragmentHolder fragmentHolder = fragmentlist.get(i);
                    if (fragmentHolder != null && id.equals(fragmentHolder.id)) {
                        index = i;
                        break;
                    }
                }
                if (index >= 0) {
                    mPager.setCurrentItem(index, false);
                }
            }
        }
    }

    public void switchToFragmentByType(String type) {
        if (TextUtils.isEmpty(type)) return;
        if (mPager == null || mPagerAdapter == null) {
            mIntentTab = type;
        } else {
            int intentIndex = -1;
            if (mTabModelList != null && !mTabModelList.isEmpty()) {
                for (int i = 0; i < mTabModelList.size(); i++) {
                    HomePageTabModel model = mTabModelList.get(i);
                    if (model != null && type.equals(model.getItemType())) {
                        intentIndex = i;
                    }
                }
            }

            if (intentIndex >= 0) {
                mPager.setCurrentItem(intentIndex, false);
            }
        }
    }

    /**
     * 切换到个人直播页面
     */
    public boolean switchToAnchorLiveFragment() {
        int intentIndex = -1;
        // 打开直播首页成功后，需要跳转到该分类Tab页
        final int intentLiveCategoryId = getArguments() == null ? -1
                :
                getArguments().getInt(BundleKeyConstants.KEY_LIVE_HOME_PAGE_SELECTED_CATEGORY_ID, -1);
        final int playSource = getArguments() == null ? -1
                :
                getArguments().getInt(BundleKeyConstants.KEY_PLAY_SOURCE, -1);

        if (mPager != null && mPagerAdapter != null) {
            if (mTabModelList != null && !mTabModelList.isEmpty()) {
                int index = 0;
                for (HomePageTabModel model : mTabModelList) {
                    if (model != null && (HomePageTabModel.ITEM_TYPE_LAMIA.equals(model.getItemType()))) {
                        intentIndex = index;
                        break;
                    }
                    index++;
                }
            }
            int currentIndex = mPager.getCurrentItem();
            if (intentIndex >= 0 && intentIndex != currentIndex) {
                mPager.setCurrentItem(intentIndex, false);
            }
        }
        if (intentIndex < 0) {
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        try {
                            BaseFragment liveAudioFragment = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE)
                                    .getFragmentAction()
                                    .newLiveCategoryViewPagerFragmentWithPlaySource(
                                            ILivePlaySource.SOURCE_DEFAULT, intentLiveCategoryId);
                            startFragment(liveAudioFragment);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        }
        return intentIndex >= 0;
    }

    @Override
    public void onInstallSuccess(BundleModel bundleModel) {
        Logger.i("bundle_install_", "onInstallSuccess");
        Logger.i("cf_test", "bundle_install_onInstallSuccess" + bundleModel.bundleName);
        if (bundleModel != null && !TextUtils.isEmpty(bundleModel.bundleName)) {
            if (bundleModel.bundleName.equals(Configure.liveBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
            if (bundleModel.bundleName.equals(Configure.radioBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
//            if (bundleModel.bundleName.equals(Configure.weikeBundleModel.bundleName) && canUpdateUi()) {
//                isLoadDataAfterBundleInstall = true;
//                loadTabData();
//            }
            if (bundleModel.bundleName.equals(Configure.rnBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
            if (bundleModel.bundleName.equals(Configure.vipBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
            if (bundleModel.bundleName.equals(Configure.feedBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
            if (bundleModel.bundleName.equals(Configure.kidBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
            if (bundleModel.bundleName.equals(Configure.miniDramaBundleModel.bundleName) && canUpdateUi()) {
                isLoadDataAfterBundleInstall = true;
                loadTabData();
            }
        }
    }

    @Override
    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
        Logger.i("cf_test", "bundle_install_onLocalInstallError" + bundleModel.bundleName);
    }
    private ConcurrentHashMap<String, Boolean> mHasRetryBundle = new ConcurrentHashMap<>();
    @Override
    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
        Logger.i("cf_test", "bundle_install_onRemoteInstallError" + bundleModel.bundleName);
        if (mHasRetryBundle.get(bundleModel.bundleName) != null && mHasRetryBundle.get(bundleModel.bundleName) == true){
            return;
        }
        mHasRetryBundle.put(bundleModel.bundleName, true);
        HandlerManager.postOnBackgroundThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (bundleModel.needAsync()) {
                    Router.getActionByCallback(bundleModel.bundleName, HomePageFragment.this , true, BundleModel.DOWNLOAD_IN_BACKGROUND);
                }
            }
        }, 1500);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mPager != null) {
            updateSearchBar(mPager.getCurrentItem());
        }
    }

    private HomePageRedDotManager.IGotGoodsCountsCallBack goodsCountsCallBack =
            () -> updateRedDotInfo(HomePageRedDotManager.TYPE_RECOMMEND_PAGE, 80);

    private IDownloadTaskCallback downloadListener = new IDownloadTaskCallback() {
        @Override
        public void onDownloadProgress(@Nullable BaseDownloadTask downloadTask) {

        }

        @Override
        public void onCancel(@Nullable BaseDownloadTask downloadTask) {

        }

        @Override
        public void onComplete(@Nullable BaseDownloadTask downloadTask) {
            updateRedDotInfo(HomePageRedDotManager.TYPE_RECOMMEND_PAGE, 0);
        }

        @Override
        public void onUpdateTrack(@Nullable BaseDownloadTask downloadTask) {

        }

        @Override
        public void onStartNewTask(@Nullable BaseDownloadTask downloadTask) {

        }

        @Override
        public void onError(@Nullable BaseDownloadTask downloadTask) {
            updateRedDotInfo(HomePageRedDotManager.TYPE_RECOMMEND_PAGE, 0);
        }

        @Override
        public void onDelete() {
        }
    };

    private void initRedDotViewOnRecommendPage() {
        boolean hasInited = (mVRedDotCountOnRecommendPage != null && mVPureRedDotOnRecommendPage != null);
        if (hasInited) {
            return;
        }

        if (mIvSearchBarMoreActionBtn != null) {
            //推荐页搜索栏右侧 下载数量红点
            mVRedDotCountOnRecommendPage = new BadgeView(getContext());
            mVRedDotCountOnRecommendPage.setTargetView(mIvSearchBarMoreActionBtn);
            mVRedDotCountOnRecommendPage.setBadgeMargin(0, 1, 1, 0);
            mVRedDotCountOnRecommendPage.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
            mVRedDotCountOnRecommendPage.setBackgroundResource(com.ximalaya.ting.android.host.R.drawable.host_title_bar_msg_count_bg);

            //推荐页搜索栏右侧 下载小红点
            mVPureRedDotOnRecommendPage = new RedDotView(getContext());
            mVPureRedDotOnRecommendPage.setTargetView(mIvSearchBarMoreActionBtn);
            FrameLayout.LayoutParams downRightRedLP =
                    (FrameLayout.LayoutParams) mVPureRedDotOnRecommendPage.getLayoutParams();
            downRightRedLP.topMargin = BaseUtil.dp2px(getContext(), 7);
            downRightRedLP.rightMargin = BaseUtil.dp2px(getContext(), 7);
            mVPureRedDotOnRecommendPage.setVisibility(View.INVISIBLE);
        }
    }

    private void initRedDotViewOnQualityPage() {
        boolean hasInited = (null != mVRedDotCountOnQualityPage && null != mVPureRedDotOnQualityPage);
        if (hasInited) {
            return;
        }
        if (null != mIvSearchBarQualityCartBtn) {
            mVRedDotCountOnQualityPage = new BadgeView(getActivity());
            mVRedDotCountOnQualityPage.setTargetView(mIvSearchBarQualityCartBtn);
            if (mVRedDotCountOnRecommendPage != null) {
                mVRedDotCountOnRecommendPage.setBadgeMargin(0, 1, 1, 0);
                mVRedDotCountOnRecommendPage.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
                mVRedDotCountOnRecommendPage.setBackgroundResource(com.ximalaya.ting.android.host.R.drawable.host_title_bar_msg_count_bg);
            }

            mVPureRedDotOnQualityPage = new RedDotView(getActivity());
            mVPureRedDotOnQualityPage.setTargetView(mIvSearchBarQualityCartBtn);
            FrameLayout.LayoutParams downRightRedLP = (FrameLayout.LayoutParams) mVPureRedDotOnQualityPage.getLayoutParams();
            downRightRedLP.topMargin = BaseUtil.dp2px(getContext(), 7);
            downRightRedLP.rightMargin = BaseUtil.dp2px(getContext(), 7);
            mVPureRedDotOnQualityPage.setVisibility(View.INVISIBLE);
        }
    }

    //更新下载声音信息
    private void updateRedDotInfo(int type, long delay) {
        if (HomePageRedDotManager.TYPE_QUALITY_PAGE == type) {
            postOnUiThreadDelayed(() -> {
                if (ShoppingCartUtil.isShowCartRedDot()) {
                    int count = getRedDotManager().getGoodsCount();
                    initRedDotViewOnQualityPage();
                    if (0 < count) {
                        ViewStatusUtil.setVisible(View.INVISIBLE, mVPureRedDotOnQualityPage);
                        ViewStatusUtil.setText(mVRedDotCountOnQualityPage, String.valueOf(count));
                        ViewStatusUtil.setVisible(View.VISIBLE, mVRedDotCountOnQualityPage);
                    } else {
                        ViewStatusUtil.setVisible(View.INVISIBLE, mVRedDotCountOnQualityPage);
                        ViewStatusUtil.setVisible(View.VISIBLE, mVPureRedDotOnQualityPage);
                    }
                } else {
                    ViewStatusUtil.setVisible(View.GONE, mVRedDotCountOnQualityPage, mVPureRedDotOnQualityPage);
                }
            }, delay);
        }
    }

    private void updateSearchBar(int tabIndex) {
        if (!mIsInUnderageMode && !ToolUtil.isEmptyCollects(mTabModelList)
                && tabIndex >= 0 && tabIndex < mTabModelList.size()) {
            HomePageTabModel tabModel = mTabModelList.get(tabIndex);
            if (tabModel != null) {
                boolean showActionBtn = false;
                boolean showRecommendPageAction = true;
                boolean showLivePageAction = false;
                boolean showVipPageAction = false;
                boolean showQualityPageAction = false;


                updateRecognizeIcon(tabModel);

                //等布局完成之后，再做动画，这样才能拿到宽度
                final boolean finalShowActionBtn = showActionBtn;
                final boolean finalShowRecommendPageAction = showRecommendPageAction;
                final boolean finalShowLivePageAction = showLivePageAction;
                final boolean finalShowVipPageAction = showVipPageAction;
                final boolean finalShowQualityPageAction = showQualityPageAction;
                if (finalShowActionBtn) {
                    mVgSearchBarRightAction.measure(0, 0);
                }
                boolean doAnimation = !mFirstUpdateSearchBar;
                mFirstUpdateSearchBar = false;
                postOnUiThread(() -> doAnimationWhileSearchBarChange(doAnimation, finalShowActionBtn,
                        finalShowRecommendPageAction
                        , finalShowLivePageAction
                        , finalShowVipPageAction
                        , finalShowQualityPageAction));
            }
        }
    }

    private void bindSearchBarRightActionData(View view, String from) {
        Map<String, String> data = new HashMap<>();
        data.put("from", from);
        AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, data);
    }

    private void doAnimationWhileSearchBarChange(boolean doAnimation, final boolean actionBtnTargetVisible
            , final boolean recommendPageActionTargetVisible
            , boolean livePageActionTargetVisible
            , boolean vipPageActionTargetVisible
            , final boolean qualityPageActionTargetVisible) {
        int rightAreaTargetWidth = 0;
        if (recommendPageActionTargetVisible) {
            rightAreaTargetWidth = mVgSearchBarRecommendPageAction.getWidth() + BaseUtil.dp2px(getActivity(), 16);
        } else if (livePageActionTargetVisible) {
            rightAreaTargetWidth = mVgSearchBarLivePageAction.getWidth();
        } else if (vipPageActionTargetVisible) {
            rightAreaTargetWidth = mVgSearchBarVipPageAction.getWidth();
        } else if (qualityPageActionTargetVisible) {
            rightAreaTargetWidth = mVgSearchBarQualityPageAction.getWidth();
        } else if (actionBtnTargetVisible) {
            // 右边还有margin
            rightAreaTargetWidth =
                    mVgSearchBarRightAction.getWidth() + +BaseUtil.dp2px(getActivity(), 15);
        }
        int leftAreaTargetWidth = 0;
        if (MyListenAbUtil.INSTANCE.hasSlideBar()) {
            leftAreaTargetWidth = mSlideEntry.getWidth() + BaseUtil.dp2px(getActivity(), 1);
        }

        List<Animator> animatorList = new ArrayList<>();
        if (mSearchBarAnimator != null && mSearchBarAnimator.isRunning()) {
            mSearchBarAnimator.end();
        }
        mSearchBarAnimator = null;


        int searchBarTargetWidth;
        try {
            // 屏幕宽度 - 右边区域宽度 - 搜索条右边距 - 搜索条左边距
            searchBarTargetWidth = BaseUtil.getScreenWidth(getActivity()) - rightAreaTargetWidth - leftAreaTargetWidth
                    - getResourcesSafe().getDimensionPixelSize(R.dimen.main_home_page_search_bar_margin_left)
                    - getResourcesSafe().getDimensionPixelSize(R.dimen.main_edit_home_page_tab_page_rv_tabs_margin_right);
        } catch (Exception e) {
            // 线上有报找不到资源的异常
            searchBarTargetWidth = BaseUtil.getScreenWidth(getActivity()) - rightAreaTargetWidth - leftAreaTargetWidth
                    - BaseUtil.dp2px(getActivity(), 15) - BaseUtil.dp2px(getActivity(), 7);
        }
        if (mSearchHotWordSwitchManager != null && mSearchHotWordSwitchManager.getSearchBarWidth() != searchBarTargetWidth) {
            List<Animator> searchBarAnimators = mSearchHotWordSwitchManager.getWidthObjectAnimator(SEARCH_BAR_ANIMATION_DURATION, searchBarTargetWidth);
            animatorList.addAll(searchBarAnimators);
        }
        addActionBtnAnimatorIfNeeded(recommendPageActionTargetVisible, animatorList,
                mVgSearchBarRecommendPageAction);
        addActionBtnAnimatorIfNeeded(livePageActionTargetVisible, animatorList,
                mVgSearchBarLivePageAction);
        addActionBtnAnimatorIfNeeded(qualityPageActionTargetVisible, animatorList
                , mVgSearchBarQualityPageAction);
        addActionBtnAnimatorIfNeeded(actionBtnTargetVisible, animatorList, mVgSearchBarRightAction);
        addActionBtnAnimatorIfNeeded(vipPageActionTargetVisible, animatorList, mVgSearchBarVipPageAction);

        if (!ToolUtil.isEmptyCollects(animatorList)) {
            if (!BaseUtil.isAppForeground(mContext)) {
                doAnimation = false;
            }
            if (doAnimation) {
                mSearchBarAnimator = new AnimatorSet();
                mSearchBarAnimator.playTogether(animatorList);
                mSearchBarAnimator.start();
            } else {
                for (Animator animator : animatorList) {
                    animator.end();
                }
            }
        }
    }

    private void addActionBtnAnimatorIfNeeded(final boolean actionBtnTargetVisible,
                                              List<Animator> animatorList,
                                              final ViewGroup vgSearchBarRightAction) {
        boolean actionBtnCurVisible = (vgSearchBarRightAction.getVisibility() == View.VISIBLE);
        if (actionBtnCurVisible != actionBtnTargetVisible) {
            float startAlpha = vgSearchBarRightAction.getAlpha();
            float endAlpha = actionBtnTargetVisible ? 1 : 0;
            ObjectAnimator actionBtnAnimator =
                    ObjectAnimator.ofFloat(vgSearchBarRightAction, "alpha", startAlpha, endAlpha);
            actionBtnAnimator.setDuration(SEARCH_BAR_ANIMATION_DURATION);
            if (endAlpha == 0) {
                // 消失的过程，前面快一点，减少和搜索条重叠的效果
                actionBtnAnimator.setInterpolator(new DecelerateInterpolator());
            } else {
                // 出现的过程，前面慢一点，减少和搜索条重叠的效果
                actionBtnAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
            }
            actionBtnAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    vgSearchBarRightAction.setVisibility(actionBtnTargetVisible ? View.VISIBLE :
                            View.INVISIBLE);
                    if (mVgSearchBarQualityPageAction == vgSearchBarRightAction && actionBtnTargetVisible) {
                        if (ShoppingCartUtil.isShowCartRedDot()) {
                            updateRedDotInfo(HomePageRedDotManager.TYPE_QUALITY_PAGE, 0);
                        }
                    }
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animatorList.add(actionBtnAnimator);
        }
    }

    private void updateForegroundColor(int tabIndex) {
        if (mIsInUnderageMode) {
            return;
        }
        String foregroundColorTheme = null;
        CustomTheme customTheme = null;
        boolean bottomOvalViewShowShadow = true;
        if (mPagerAdapter != null) {
            // 如果fragment中存了，按存的来。因为可能由于滑动等动作，修改了颜色
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(tabIndex);
            if (fragment != null) {
                View view = fragment.getView();
                if (view != null) {
                    Object tag = view.getTag(R.id.main_foreground_color);
                    if (tag instanceof String) {
                        foregroundColorTheme = (String) tag;
                    }
                }
            }
        }

        // 后台配置控制是否显示背景上的蒙层
        HomePageTabModel tabModel = getTabModelByIndex(tabIndex);
        if (tabModel != null && tabModel.getCustomTheme() != null && tabModel.getCustomTheme().isValid()) {
            bottomOvalViewShowShadow = tabModel.getCustomTheme().isOpenMaskLayer();
        }

        // 皮肤包背景色只会配深色，所以用白色前景色
        if (!HomePageTabTheme.isValidForegroundColorTheme(foregroundColorTheme)) {
            if (mUseSkinSettingBgColor) {
                foregroundColorTheme = HomePageTabTheme.FOREGROUND_COLOR_WHITE;
                bottomOvalViewShowShadow = false;
            }
        }

        // 取tab在代码中设置的颜色
        if (!HomePageTabTheme.isValidForegroundColorTheme(foregroundColorTheme) && mPager != null && mPagerAdapter != null) {
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            if (fragment instanceof BaseHomePageTabFragment) {
                if (((BaseHomePageTabFragment) fragment).getHomePageCustomTheme() != null) {
                    foregroundColorTheme = HomePageTabTheme.FOREGROUND_COLOR_CUSTOM;
                    customTheme = ((BaseHomePageTabFragment) fragment).getHomePageCustomTheme();
                } else {
                    foregroundColorTheme =
                            ((BaseHomePageTabFragment) fragment).getHomePageHeaderForegroundColorTheme();
                }
            }
        }

        // 如果没存过颜色，取服务端配置的颜色
        if (!HomePageTabTheme.isValidForegroundColorTheme(foregroundColorTheme) && tabModel != null) {
            if (tabModel.getCustomTheme() != null && tabModel.getCustomTheme().isValid()) {
                foregroundColorTheme = HomePageTabTheme.FOREGROUND_COLOR_CUSTOM;
                customTheme = tabModel.getCustomTheme();
            } else if (tabModel.getTabTheme() != null) {
                foregroundColorTheme = tabModel.getTabTheme().getSearchBoxColor();
            }
        }

        if (!HomePageTabTheme.isValidForegroundColorTheme(foregroundColorTheme)) {
            // 如果都没有，当背景为纯白时用黑色。盖上去的那层是透明，所以整体背景是白色。
            if (lastSetColor == Color.TRANSPARENT) {
                foregroundColorTheme = HomePageTabTheme.FOREGROUND_COLOR_BLACK;
            } else {
                foregroundColorTheme = HomePageTabTheme.FOREGROUND_COLOR_WHITE;
            }
        }

        // 暗黑模式，不显示
        if (BaseFragmentActivity.sIsDarkMode) {
            bottomOvalViewShowShadow = false;
        }

        if (mPagerAdapter != null && mPager != null) {
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            if (fragment instanceof BaseHomePageTabFragment) {
                BaseHomePageTabFragment.HeaderShadowType headerShadowType = ((BaseHomePageTabFragment) fragment).getHomePageShadowType();
                switch (headerShadowType) {
                    case SHOW_SHADOW:
                        bottomOvalViewShowShadow = true;
                        break;
                    case NOT_SHOW_SHADOW:
                        bottomOvalViewShowShadow = false;
                        break;
                    default:
                        break;
                }
            }
        }

        updateForegroundColor(foregroundColorTheme, customTheme);
        if (mBottomOvalView != null) {
            mBottomOvalView.setShowShadow(bottomOvalViewShowShadow);
        }
    }

    private boolean isShowPormotionOperationModule() {
        boolean hasPormotionOperationModule = false;
        if (getCurTabFragment() instanceof RecommendFragmentStaggered) {
            RecommendFragmentStaggered recommendFragmentNew = (RecommendFragmentStaggered) getCurTabFragment();
            // 焦点图和大促运营位同时存在时
            if (recommendFragmentNew.hasBanner() && recommendFragmentNew.hasPromotionOperationModule()) {
                hasPormotionOperationModule = true;
            }
        }
        return hasPormotionOperationModule;
    }

    private void updateTopSearchBgColor(boolean shouldShow) {
        AtmosphereInfo atmosphereInfo = SkinManager.INSTANCE.getAtmosphereInfo();
        int color = getResources().getColor(R.color.main_color_ffffff_99000000);
        if (atmosphereInfo != null && atmosphereInfo.getDeviceConfigs() != null && shouldShow) {
            List<DeviceConfigs> deviceConfigs = atmosphereInfo.getDeviceConfigs();
            for (DeviceConfigs info : deviceConfigs) {
                if (info != null && info.getAppType() == 1) {
                    if (BaseFragmentActivity.sIsDarkMode) {
                        if (!TextUtils.isEmpty(info.getDarkSearchBoxColor())) {
                            color = Color.parseColor(info.getDarkSearchBoxColor());
                            if (!TextUtils.isEmpty(info.getDarkTransparency())) {
                                color = ColorUtil.changeColorAlpha01(color, ColorUtil.changePercentToFloat(info.getDarkTransparency()));
                            }
                        }
                    } else {
                        if (!TextUtils.isEmpty(info.getSearchBoxColor())) {
                            color = Color.parseColor(info.getSearchBoxColor());
                            if (!TextUtils.isEmpty(info.getTransparency())) {
                                color = ColorUtil.changeColorAlpha01(color, ColorUtil.changePercentToFloat(info.getTransparency()));
                            }
                        }
                    }
                    break;
                }
            }
        }
        mSearchHotWordSwitchManager.setBackgroundColor(color);
    }

    private void updateForegroundColor(String foregroundColorTheme, CustomTheme customTheme) {
        //暗黑模式下，颜色固定不变
        boolean isDark = BaseFragmentActivity.sIsDarkMode;
        if (isDark) {
            foregroundColorTheme = HomePageTabTheme.FOREGROUND_COLOR_WHITE;
        }
        boolean isShowAtmosphereImage = SkinManager.INSTANCE.isNeedShowAtmosphereImage()
                && isShowRecommendFragment();
        boolean hasPormotionOperationModule = isShowPormotionOperationModule();
        boolean isShowUnitPackBgAd = isShowUnitPackBgAd();
        try {
            for (HEADER_VIEW_COLOR viewColor : HEADER_VIEW_COLOR.values()) {
                int color = viewColor.getColor(foregroundColorTheme, customTheme);
                switch (viewColor) {
                    case STATUS_BAR:
                        // 不可见时不更新状态栏字体颜色
                        if (isRealVisable()) {
                            mIsDarkStatusBar = color == 1;
                            StatusBarManager.setStatusBarColor(getWindow(), mIsDarkStatusBar);
                        }
                        break;
                    case ACTIVE_TAB:
                        mTabs.setActivateTextColor(color);
                        break;
                    case DEACTIVATE_TAB:
                        if (isNewRecommendTab(getCurTabIndex(), false)) {
                            if (isDark) {
                                color = 0xccffffff;
                            } else {
                                color = 0x8c2c2c3c;
                            }
                        }
                        mTabs.setDeactivateTextColor(color);
                        break;
                    case TAB_INDICATOR:
                        if (HomePageTabTheme.FOREGROUND_COLOR_BLACK.equals(foregroundColorTheme) || isDark) {
                            if (isDark && hasPormotionOperationModule) {
                                mTabs.setIndicatorGradientColors(null);
                                mTabs.setIndicatorColor(0x99ffffff);
                            } else {
                                mTabs.setIndicatorGradientColors(null);
                                mTabs.setIndicatorColor(0xffff4444);
                            }
                        } else {
                            mTabs.setIndicatorGradientColors(null);
                            mTabs.setIndicatorColor(color);
                        }
                        break;
                    case SEARCH_BAR_BG:
                        if (mSearchHotWordSwitchManager != null) {
                            if (isShowUnitPackBgAd) {
                                color = 0x1Affffff;
                                mSearchHotWordSwitchManager.setBackgroundColor(color);
                            } else {
                                if (isDark) {
                                    if (isShowAtmosphereImage) {
                                        AtmosphereInfo atmosphereInfo = SkinManager.INSTANCE.getAtmosphereInfo();
                                        if (atmosphereInfo != null && atmosphereInfo.getDeviceConfigs() != null && mFraction < 1.0f) {
                                            List<DeviceConfigs> deviceConfigs = atmosphereInfo.getDeviceConfigs();
                                            for (DeviceConfigs info : deviceConfigs) {
                                                if (info != null && info.getAppType() == 1) {
                                                    if (!TextUtils.isEmpty(info.getDarkSearchBoxColor())) {
                                                        color = Color.parseColor(info.getDarkSearchBoxColor());
                                                        if (!TextUtils.isEmpty(info.getDarkTransparency())) {
                                                            color = ColorUtil.changeColorAlpha01(color, ColorUtil.changePercentToFloat(info.getDarkTransparency()));
                                                        }
                                                    } else {
                                                        color = getResources().getColor(R.color.main_color_99000000);
                                                    }
                                                    break;
                                                }
                                            }
                                        } else {
                                            color = getResources().getColor(R.color.main_color_99000000);
                                        }

                                        mSearchHotWordSwitchManager.setBackgroundColor(color);
                                    } else if (hasPormotionOperationModule) {
                                        color = 0x33ffffff;
                                        mSearchHotWordSwitchManager.setBackgroundColor(color);
                                    } else {
                                        if (isNewRecommendTab(mPager.getCurrentItem(), false)) {
                                            mSearchHotWordSwitchManager.setBackgroundColor(0x99000000);
                                        }else {
                                            mSearchHotWordSwitchManager.setBackgroundColor(0x1affffff);
                                        }
                                    }
                                } else {
                                    if (isNewRecommendTab(mPager.getCurrentItem())) {
                                        if (isShowAtmosphereImage) {
                                            AtmosphereInfo atmosphereInfo = SkinManager.INSTANCE.getAtmosphereInfo();
                                            if (atmosphereInfo != null && atmosphereInfo.getDeviceConfigs() != null && mFraction < 1.0f) {
                                                List<DeviceConfigs> deviceConfigs = atmosphereInfo.getDeviceConfigs();
                                                for (DeviceConfigs info : deviceConfigs) {
                                                    if (info != null && info.getAppType() == 1) {
                                                        if (!TextUtils.isEmpty(info.getSearchBoxColor())) {
                                                            color = Color.parseColor(info.getSearchBoxColor());
                                                        }
                                                        if (!TextUtils.isEmpty(info.getTransparency())) {
                                                            color = ColorUtil.changeColorAlpha01(color, ColorUtil.changePercentToFloat(info.getTransparency()));
                                                        }
                                                        break;
                                                    }
                                                }
                                            } else {
                                                color = getResources().getColor(R.color.main_color_ffffff);
                                            }
                                        } else {
                                            color = getResources().getColor(R.color.main_color_ffffff);
                                        }
                                    } else {
                                        color = getResources().getColor(R.color.main_color_f6f6f6_1affffff);
                                    }
                                    mSearchHotWordSwitchManager.setBackgroundColor(color);
                                }
                            }
                        }
                        break;
                    case SEARCH_ICON:
                        if (mSearchHotWordSwitchManager != null) {
                            if (isNewRecommendTab(getCurTabIndex(), false)) {
                                color = 0xff8d8d91;
                            } else if (isDark) {
                                color = Color.BLACK;
                            }
                            mSearchHotWordSwitchManager.setDrawableColorFilter(color);
                        }
                        break;
                    case SEARCH_TEXT:
                        if (mSearchHotWordSwitchManager != null) {
                            if (isShowUnitPackBgAd) {
                                color = 0x80ffffff;
                            } else {
                                if (isDark) {
                                    if (isShowAtmosphereImage) {
                                        color = 0x80ffffff;
                                    } else if (hasPormotionOperationModule) {
                                        color = 0x99ffffff;
                                    } else if (isNewRecommendTab(getCurTabIndex(), false)) {
                                        color = 0xff8d8d91;
                                    } else {
                                        color = 0xff999999;
                                    }
                                } else if (isNewRecommendTab(getCurTabIndex())) {
                                    color = DEFAULT_COLOR_SEARCH_RECOMMEND;
                                }
                            }
                            mSearchHotWordSwitchManager.setTextColor(color);
                        }
                        break;
                    case SEARCH_BAR_RIGHT_ACTION_BTN_BG:
                        if (isDark) {
                            color = 0x1af3f4f5;
                        }
                        mVgSearchBarRightAction.getBackground().mutate().setColorFilter(color,
                                PorterDuff.Mode.SRC_IN);
                        break;
                    case SEARCH_BAR_RIGHT_ACTION_BTN_ICON:
                        if (mTvSearchBarRightActionBtn.getCompoundDrawables().length >= 1
                                && mTvSearchBarRightActionBtn.getCompoundDrawables()[0] != null) {
                            mTvSearchBarRightActionBtn.getCompoundDrawables()[0].mutate().setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        }
                        break;
                    case SEARCH_BAR_RIGHT_ACTION_BTN_TEXT:
                        mTvSearchBarRightActionBtn.setTextColor(color);
                        break;
                    case SEARCH_BAR_OTHER_BTN:
//                        mIvSearchBarHistoryBtn.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        mIvSearchBarMoreActionBtn.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        if (mSignIconUseColorFilter) {
                            mIvSearchBarSign.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        }
                        if (mFreeListenIconManager != null) {
                            mFreeListenIconManager.setFreeListenIconColor(color);
                        }
                        mIvSearchBarLiveMineBtn.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        mIvSearchBarLiveStartBtn.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        mIvSearchBarQualityCartBtn.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        break;
                    case EDIT_TAB_PAGE_ENTRY:
                        if (isNewRecommendTab(getCurTabIndex(), false)) {
                            if (isDark) {
                                color = 0xccffffff;
                            } else {
                                color = 0xff2c2c3c;
                            }
                        } else if (isDark) {
                            color = 0xffffffff;
                        }
                        if (mIvEditTab != null && mIvEditTab.getDrawable() != null) {
                            mIvEditTab.getDrawable().mutate().setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        }
                        break;
                    case SEARCH_BTN_TEXT:
                        break;
                    case SEARCH_BTN_BG:
                        if (isShowUnitPackBgAd) {
                            mIvSearchBarIcon.setColorFilter(0xffffffff, PorterDuff.Mode.SRC_IN);
                            mIvSearchRecognizeBtn.setColorFilter(0x80ffffff, PorterDuff.Mode.SRC_IN);
                        } else if (isDark) {
                            if (isShowAtmosphereImage) {
                                mIvSearchBarIcon.setColorFilter(0x80ffffff, PorterDuff.Mode.SRC_IN);
                                mIvSearchRecognizeBtn.setColorFilter(0x80ffffff, PorterDuff.Mode.SRC_IN);
                            } else if (hasPormotionOperationModule) {
                                mIvSearchBarIcon.setColorFilter(0xffffffff, PorterDuff.Mode.SRC_IN);
                                mIvSearchRecognizeBtn.setColorFilter(0xffffffff, PorterDuff.Mode.SRC_IN);
                            } else {
                                int defaultColor = getDefaultSearchIconColor();
                                if (defaultColor != ColorUtil.INVALID_COLOR) {
                                    mIvSearchBarIcon.setColorFilter(defaultColor, PorterDuff.Mode.SRC_IN);
                                    mIvSearchRecognizeBtn.setColorFilter(defaultColor, PorterDuff.Mode.SRC_IN);
                                } else {
                                    mIvSearchBarIcon.setColorFilter(DEFAULT_COLOR_SEARCH, PorterDuff.Mode.SRC_IN);
                                    mIvSearchRecognizeBtn.setColorFilter(DEFAULT_COLOR_RECOGNIZE_DARK, PorterDuff.Mode.SRC_IN);
                                }
                            }
                        } else if (color != ColorUtil.INVALID_COLOR) {
                            mIvSearchBarIcon.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                            mIvSearchRecognizeBtn.setColorFilter(color, PorterDuff.Mode.SRC_IN);
                        } else {
                            int defaultColor = getDefaultSearchIconColor();
                            if (defaultColor != ColorUtil.INVALID_COLOR) {
                                mIvSearchBarIcon.setColorFilter(defaultColor, PorterDuff.Mode.SRC_IN);
                                mIvSearchRecognizeBtn.setColorFilter(defaultColor, PorterDuff.Mode.SRC_IN);
                            } else {
                                mIvSearchBarIcon.clearColorFilter();
                                mIvSearchRecognizeBtn.clearColorFilter();
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            Logger.e(TAG, "updateForegroundColor", e);
        }
    }

    private IFragmentFinish mFragmentFinishCallback = new IFragmentFinish() {
        @Override
        public void onFinishCallback(Class<?> cls, int fid, Object... params) {
            if (cls == HomePageTabAndChannelListNewFragmentDialog.class) {
                isGotoEditTab = false;
                if (params != null && params.length > 0) {
                    if (params[0] instanceof Boolean) {
                        Boolean hasModifiedTab = (Boolean) params[0];
                        if (hasModifiedTab) {
                            // 用户编辑并保存了tab，需要更新一下
                            loadTabData();
                        }
                        boolean checkTabChange = false;
                        if (params.length >= 2 && params[1] instanceof HomePageTabModel) {
                            HomePageTabModel homePageTabModel = (HomePageTabModel) params[1];
                            if (getCurrentTabModel() != null && !getCurrentTabModel().getId().equals(homePageTabModel.getId())) {
                                checkTabChange = true;
                            }
                            if (!hasModifiedTab) {
                                if (checkTabChange) {
                                    waitForTabScroll();
                                } else {
                                    traceOnTabShowDelay();
                                }
                            }
                            // 如果tab修改了，需要等tab更新之后再跳转
                            if (!hasModifiedTab) {
                                switchToFragmentById(homePageTabModel.getId());
                            } else {
                                mPendingSwitchToTab = homePageTabModel;
                            }
                        }
                    } else {
                        traceOnTabShowDelay();
                    }
                } else {
                    traceOnTabShowDelay();
                }
            }
        }
    };

    private static class LoadTabTask extends MyAsyncTask<Void, Void, Void> {

        private WeakReference<HomePageFragment> mFragmentReference;
        private List<HomePageTabModel> mTabs;

        private LoadTabTask(HomePageFragment fragment) {
            mFragmentReference = new WeakReference<>(fragment);
        }

        @Override
        protected Void doInBackground(Void... voids) {
            if (mFragmentReference != null && mFragmentReference.get() != null) {
                Fragment fragment = mFragmentReference.get();
                boolean isLocalCache = !SharedPreferencesUtil.getInstance(fragment.getActivity())
                        .getBoolean(PreferenceConstantsInHost.HAS_REQUESTED_HOME_PAGE_TAB_DATA, true);
                if (!isLocalCache) {
                    mXmRequestId = XmRequestIdManager.getInstance(BaseApplication.getMyApplicationContext()).getRequestId();
                }
                SharedPreferencesUtil.getInstance(fragment.getActivity()).saveBoolean(
                        PreferenceConstantsInHost.HAS_REQUESTED_HOME_PAGE_TAB_DATA, false);
                String customTabsSavePath = MainApplication.getMyApplicationContext().getFilesDir()
                        + File.separator + AppConstants.HOMEPAGE_CUSTOM_TABS_SAVE_NAME;
                String customTabsJson = FileUtil.readStrFromFile(customTabsSavePath);
                if (TextUtils.isEmpty(customTabsJson)) {
                    // 最早发布的版本中，是存在缓存目录里的。这里要兼容一下，尝试从缓存目录下拿一下。
                    String oldCustomTabsSavePath =
                            new File(MainApplication.getMyApplicationContext().getCacheDir()
                                    , MD5.md5(AppConstants.HOMEPAGE_CUSTOM_TABS_SAVE_NAME)).getAbsolutePath();
                    customTabsJson = FileUtil.readStrFromFile(oldCustomTabsSavePath);
                }
                if (!TextUtils.isEmpty(customTabsJson)) {
                    try {
                        mTabs = new Gson().fromJson(customTabsJson
                                , new TypeToken<List<HomePageTabModel>>() {
                                }.getType());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (ToolUtil.isEmptyCollects(mTabs)) {
                    MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                            .removeKey(PreferenceConstantsInHost.KEY_HOME_PAGE_TAB_API_TAB_SIGN);
                    String defaultTabsData = FileUtil.readAssetFileData(fragment.getActivity(),
                            "tabs.json");
                    if (!TextUtils.isEmpty(defaultTabsData)) {
                        try {
                            mTabs = new Gson().fromJson(defaultTabsData
                                    , new TypeToken<List<HomePageTabModel>>() {
                                    }.getType());
                            FileUtil.writeStr2File(defaultTabsData, customTabsSavePath);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                if (mTabs != null && !mTabs.isEmpty()) {
                    for (HomePageTabModel tab : mTabs) {
                        if (tab != null) {
                            tab.setLocalCache(isLocalCache);
                        }
                        tab.setXmRequestId(mXmRequestId);
                    }
                }
            }
            return null;
        }

        @Override
        protected void onPostExecute(Void aVoid) {
            if (mFragmentReference != null && mFragmentReference.get() != null) {
                mFragmentReference.get().setTabData(mTabs);
                mFragmentReference.get().onPageLoadingCompleted(LoadCompleteType.OK);
                mFragmentReference.get().mLoadTabTask = null;
            }
        }

    }


    private BroadcastReceiver mTopColorChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                if (BannerView.COLOR_CHANGE_ACTION.equals(intent.getAction())) {
                    if (intent.hasExtra(BannerView.CLASS_NAME) && mPagerAdapter != null && mPager != null &&
                            !(mPagerAdapter.getFragmentClassAtPositon(mPager.getCurrentItem()).
                                    getSimpleName().equals(intent.getStringExtra(BannerView.CLASS_NAME)))) {
                        return;
                    }

                    int myCurColor;
                    int realColor;
                    if (hasCurTabHasDefaultColor()) {
                        myCurColor = getCurTabDefualtColorInt();
                    } else {
                        myCurColor = myBgColor = intent.getIntExtra(BannerView.COLOR_DATA,
                                Color.TRANSPARENT);
                    }
                    realColor = myCurColor;

                    if (mCurViewPageScrollState == ViewPager.SCROLL_STATE_DRAGGING || mCurViewPageScrollState == ViewPager.SCROLL_STATE_SETTLING) {
                        return;
                    }

                    float curFraction = getCurFraction();

                    if (curFraction > 1.0f) {
                        return;
                    }

                    if (curFraction != 0 && curFraction < 1.0f) {
                        realColor = argb((int) (Color.alpha(myCurColor) * curFraction),
                                Color.red(myCurColor), Color.green(myCurColor), Color.blue(myCurColor));
                    }

                    if (curFraction <= 1.0f) {
                        setTopViewColorForBannerView(realColor);
                    }
                    if (mPager != null) {
                        updateForegroundColor(mPager.getCurrentItem());
                    }
                } else if (BannerView.COLOR_ON_SCROLL_ACTION.equals(intent.getAction())) {
                    if (intent.hasExtra(BannerView.CLASS_NAME) && mPagerAdapter != null && mPager != null &&
                            !(mPagerAdapter.getFragmentClassAtPositon(mPager.getCurrentItem()).
                                    getSimpleName().equals(intent.getStringExtra(BannerView.CLASS_NAME)))) {
                        return;
                    }

                    int myCurColor;
                    int realColor;
                    if (hasCurTabHasDefaultColor()) {
                        myCurColor = getCurTabDefualtColorInt();
                        realColor = myCurColor;
                    } else {
                        int color = intent.getIntExtra(BannerView.COLOR_DATA, Color.TRANSPARENT);
                        float offset = intent.getFloatExtra(BannerView.POSITION_OFFSET_DATA, 0f);
                        int evaluate = (int) argbEvaluator.evaluate(offset, myBgColor, color);

                        realColor = myCurColor = evaluate;
                    }

                    if (mCurViewPageScrollState == ViewPager.SCROLL_STATE_DRAGGING || mCurViewPageScrollState == ViewPager.SCROLL_STATE_SETTLING) {
                        return;
                    }

                    float curFraction = getCurFraction();
                    if (curFraction > 1.0f) {
                        return;
                    }
                    if (curFraction != 0 && curFraction < 1.0f) {
                        realColor = argb((int) (Color.alpha(myCurColor) * curFraction),
                                Color.red(myCurColor), Color.green(myCurColor), Color.blue(myCurColor));
                    }

                    if (curFraction <= 1.0f) {
                        setTopViewColorForBannerView(realColor);
                    }
                } else if (RefreshLoadMoreListView.SCROLL_CHANGE_LISTENER_ACTION.equals(intent.getAction())) {
                    int height = intent.getIntExtra(RefreshLoadMoreListView.SCROLL_CHANGE_DATA, 0);
                    int from = intent.getIntExtra(PullToRefreshStaggeredRecyclerView.SCROLL_CHANGE_FROM, 0);
                    int maxSendBroadH = from == PullToRefreshStaggeredRecyclerView.SCROLL_CHANGE_FROM_STAGGERED_RECYCLERVIEW ? PullToRefreshStaggeredRecyclerView.MAX_SEND_BROAD_H : RefreshLoadMoreListView.MAX_SEND_BROAD_H;
                    if (maxSendBroadH != 0) {
                        float fraction = height * 1.0f / maxSendBroadH;
                        if (getCurTabFragment() instanceof RecommendFragmentStaggered) {
                            RecommendFragmentStaggered recommendFragmentNew = (RecommendFragmentStaggered) getCurTabFragment();
                            // 焦点图和大促运营位同时存在时，调整变色的时机
                            if (recommendFragmentNew.hasBanner() && recommendFragmentNew.hasPromotionOperationModule()) {
                                if (height <= mBannerHeight / 3) {
                                    fraction = 0;
                                } else {
                                    fraction = (height - mBannerHeight / 3) * 1.0f / maxSendBroadH;
                                }
                            }
                        }
                        int evaluate = Color.TRANSPARENT;

                        if (fraction < 1.0f) {
                            int color;
                            if (hasCurTabHasDefaultColor()) {
                                color = getCurTabDefualtColorInt();
                            } else {
                                color = setLastColor(mPager.getCurrentItem(), true, false);
                            }

                            if (fraction != 0.0f) {
                                evaluate = (int) argbEvaluator.evaluate(fraction, color,
                                        Color.TRANSPARENT);
                            } else {
                                evaluate = color;
                            }
                        } else {
                            mDropDownAdImg.setVisibility(View.INVISIBLE);
                            mAdTag.setVisibility(View.INVISIBLE);
                            if (!isGoingSecondFloor && mTwoStyleImgView != null) {
                                mTwoStyleImgView.setVisibility(View.INVISIBLE);
                                showDropAd(0, true, 0);
                            }
                        }
                        if (isShowRecommendFragment()) {
                            mFraction = fraction;
                            updateAtmosphereAlpha(fraction);
                            updateTopBgForSceneAlpha(fraction);
                        }
                        if (mPager != null && mPagerAdapter != null && shouldShowHeaderBottomPart(
                                mPagerAdapter.getFragmentHolderAtPosition(mPager.getCurrentItem()))) {
                            if (!ovalIsAddHeight && (mReversePairImageView == null || mReversePairImageView.getVisibility() != View.VISIBLE)) {
                                setTopViewColor(evaluate, Color.TRANSPARENT, fraction);
                            } else {
                                return;
                            }
                        } else {
                            setTopViewColor(Color.TRANSPARENT, evaluate, fraction);
                        }

                        if (fraction == 0.0f) {
                            if (mPager != null) {
                                myBgColor = setLastColor(mPager.getCurrentItem(), true, false);
                            }
                        } else {
                            myBgColor = evaluate;
                        }

                        if (mPager != null && mPagerAdapter != null) {
                            Fragment fragmentAtPosition =
                                    mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
                            if (fragmentAtPosition != null && fragmentAtPosition.getView() != null) {
                                if (fraction == 0.0f) {
                                    fragmentAtPosition.getView().setTag(R.id.main_pager_curr_color, Color.TRANSPARENT);
                                } else {
                                    fragmentAtPosition.getView().setTag(R.id.main_pager_curr_color, evaluate);
                                }
                                fragmentAtPosition.getView().setTag(R.id.main_pager_curr_fraction
                                        , fraction);

                                // 没有联合霸屏包框广告才进行显示
                                if (fragmentAtPosition.getView().getTag(R.id.main_has_unit_pack_bg_ad) == null
                                        && !(isShowRecommendFragment() && SkinManager.INSTANCE.isNeedShowAtmosphereImage())) {
                                    // 由于上拉背景变成白色了，前景色需要切换为黑色，并记录下来
                                    fragmentAtPosition.getView().setTag(R.id.main_foreground_color,
                                            evaluate == Color.TRANSPARENT ? HomePageTabTheme.FOREGROUND_COLOR_BLACK : null);
                                }
                                updateForegroundColor(mPager.getCurrentItem());
                            }
                        }
                    }
                } else if (TITLE_BAR_ALPHA_CHANGE_ACTION.equals(intent.getAction())) {
                    float floatExtra = intent.getFloatExtra(TITLE_BAR_ALPHA_CHANGE_DATA, 0.0f);
                    boolean reset = intent.getBooleanExtra(TITLE_BAR_ALPHA_CHANGE_RESET, false);
                    showDropAd(floatExtra, reset,
                            intent.getIntExtra(HomePageFragment.TITLE_BAR_ALPHA_CHANGE_DROP_DOWN_LENGTH, 0));
                } else if (DROP_DOWN_PROPORTION_CHANGE_ACTION.equals(intent.getAction())) {
                    if (mFirstScrollAd != null && AdManager.isDropDownSecondType(mFirstScrollAd.getShowstyle())) {
                        showTwoStyleDropAd();
                    }
                } else if (SPLASH_UNIT_PACK_BG_ACTION.equals(intent.getAction())) {
                    updateBgAndForegroundColor(true);
                }
            }
        }
    };

    // mSignStatusChangeReceiver不能在onpause的时候解除注册
    private BroadcastReceiver mSignStatusChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                if (TITLE_BAR_SIGN.equals(intent.getAction())) {
                    if (mSignInfoModel != null && mSignInfoModel.isShow()) {
                        mSignInfoModel.setSignIn(true);
                        mIvSearchBarSign.setImageResource(R.drawable.main_ic_sign_yes_n_line_regular_24);
                    }
                    XmWidgetUtil.INSTANCE.sendSignInWidgetBroadCast();
                } else if (TITLE_BAR_CREDIT_CHANGE.equals(intent.getAction())) {
                    XmWidgetUtil.INSTANCE.sendSignInWidgetBroadCast();
                }
            }
        }
    };

    private boolean mFirstScrollHaveAd = false;
    private String mFirstScrollAdImgUrl;
    private Advertis mFirstScrollAd;
    private float mLastScrollValue = 0.0f;

    public static int argb(
            @IntRange(from = 0, to = 255) int alpha,
            @IntRange(from = 0, to = 255) int red,
            @IntRange(from = 0, to = 255) int green,
            @IntRange(from = 0, to = 255) int blue) {
        return (alpha << 24) | (red << 16) | (green << 8) | blue;
    }

    private boolean hasCurTabHasDefaultColor() {
        boolean hasCurTabHasDefaultColor = false;
        if (mPager != null && mPagerAdapter != null) {
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            hasCurTabHasDefaultColor = isThisTabHasDefualtColor(fragment);
        }
        return hasCurTabHasDefaultColor;
    }

    private boolean isThisTabHasDefualtColor(Fragment fragment) {
        if (mUseSkinSettingBgColor) {
            return true;
        }
        if (SkinManager.INSTANCE.isNeedShowAtmosphereImage() && isShowRecommendFragment()) {
            return true;
        }
        if (BaseFragmentActivity.sIsDarkMode) {
            return true;
        }
        boolean hasCurTabHasDefualtColor = false;
        if (fragment instanceof BaseHomePageTabFragment) {
            hasCurTabHasDefualtColor =
                    ((BaseHomePageTabFragment) fragment).getHomePageHeaderBgColor() != BaseHomePageTabFragment.INVALID_COLOR;
        }
        if (!hasCurTabHasDefualtColor) {
            FragmentHolder fragmentHolder =
                    mPagerAdapter.getFragmentHolderAtPosition(mPager.getCurrentItem());
            if (fragmentHolder != null) {
                hasCurTabHasDefualtColor = fragmentHolder.hasEffectiveColor;
            }
        }
        return hasCurTabHasDefualtColor;
    }

    private int getCurTabDefualtColorInt() {
        if (mPager != null && mPagerAdapter != null) {
            FragmentHolder fragmentHolder =
                    mPagerAdapter.getFragmentHolderAtPosition(mPager.getCurrentItem());
            return getThisTabDefaultColorInt(fragmentHolder);
        }
        return Color.TRANSPARENT;
    }

    private int getThisTabDefaultColorInt(FragmentHolder fragmentHolder) {
        if (mUseSkinSettingBgColor) {
            return mSkinSettingBgColor;
        }

        Fragment fragment = null;
        if (fragmentHolder != null && fragmentHolder.realFragment != null) {
            fragment = fragmentHolder.realFragment.get();
        }
        if (BaseFragmentActivity.sIsDarkMode) {
            if (fragment instanceof BaseHomePageTabFragment && ((BaseHomePageTabFragment) fragment).useColorInDarkMode()) {
                return ((BaseHomePageTabFragment) fragment).getHomePageHeaderBgColor();
            }
            return 0xff121212;
        }
        int color = Color.TRANSPARENT;
        if (fragment instanceof BaseHomePageTabFragment
                && ((BaseHomePageTabFragment) fragment).getHomePageHeaderBgColor() != BaseHomePageTabFragment.INVALID_COLOR) {
            color = ((BaseHomePageTabFragment) fragment).getHomePageHeaderBgColor();
        } else if (fragmentHolder != null && fragmentHolder.hasEffectiveColor) {
            color = fragmentHolder.effectColor;
        }
        // 配的白色可以直接改成透明，显示效果一样。白色会导致页面上下滑动的时候透明和白色直接做渐变，出现灰色的情况
        if (color == Color.WHITE) {
            color = Color.TRANSPARENT;
        }
        return color;
    }

    private boolean isEscapeCheckFragmemt(FragmentHolder fragmentHolder) {
        return null != fragmentHolder && "vip".equals(fragmentHolder.id);
    }

    private BannerView getBannerInThisPage(FragmentHolder fragmentHolder) {
        if (fragmentHolder != null) {
            if (noBannerItemType(fragmentHolder)) {
                return null;
            }

            if (fragmentHolder.realFragment != null) {
                Fragment fragment = fragmentHolder.realFragment.get();
                if (fragment != null && fragment.getView() != null) {
                    View view = fragment.getView().findViewWithTag(BannerView.BANNER_TAG);
                    if (view instanceof BannerView && (view.getVisibility() == View.VISIBLE || view.getTag(com.ximalaya.ting.android.host.R.id.main_banner_no_check_visable) != null)) {
                        return (BannerView) view;
                    }
                    return null;
                }
            }
        }
        return null;
    }

    private boolean shouldShowHeaderBottomPart(FragmentHolder fragmentHolder) {
        return shouldShowHeaderBottomPart(fragmentHolder, null);
    }

    // 从外面传入bannerViewOfThisPage，主要是避免重复的findViewWithTag
    private boolean shouldShowHeaderBottomPart(FragmentHolder fragmentHolder,
                                               BannerView bannerViewOfThisPage) {
        if (fragmentHolder == null || fragmentHolder.realFragment == null) {
            return false;
        }
        Fragment fragment = fragmentHolder.realFragment.get();
        BaseHomePageTabFragment.HeaderBgType headerBgType =
                BaseHomePageTabFragment.HeaderBgType.NOT_INSPECTED;
        if (fragment instanceof BaseHomePageTabFragment) {
            headerBgType = ((BaseHomePageTabFragment) fragment).getHomePageHeaderBgType();
        }
        switch (headerBgType) {
            case NOT_INSPECTED:
                if (bannerViewOfThisPage == null) {
                    bannerViewOfThisPage = getBannerInThisPage(fragmentHolder);
                }
                return bannerViewOfThisPage != null;
            case SHOW_BOTTOM_PART:
                return true;
            case NOT_SHOW_BOTTOM_PART:
            default:
                return false;
        }
    }

    private HomePageTabModel getTabModelByIndex(int tabIndex) {
        if (mTabModelList != null && tabIndex >= 0 && tabIndex < mTabModelList.size()) {
            return mTabModelList.get(tabIndex);
        }
        return null;
    }


    private float getCurFraction() {
        float fracton = 0f;
        if (mPager != null && mPagerAdapter != null) {
            FragmentHolder fragmentHolder =
                    mPagerAdapter.getFragmentHolderAtPosition(mPager.getCurrentItem());
            if (fragmentHolder != null) {
                if (fragmentHolder.realFragment != null) {
                    Fragment fragment = fragmentHolder.realFragment.get();
                    if (fragment != null && fragment.getView() != null) {
                        Object tag1 = fragment.getView().getTag(R.id.main_pager_curr_fraction);
                        if (tag1 instanceof Float) {
                            float temp = (float) tag1;
                            if (temp < 1.0f) {
                                fracton = 1 - temp;
                            } else {
                                return temp;
                            }
                        }
                    }
                }
            }
        }
        return fracton;
    }

    private int lastSetColor = Color.TRANSPARENT;
    private ValueAnimator mValueAnimator = null;
    private float lastSetFraction = 1.0f;

    private void setTopViewColorForBannerView(final int color) {
        if (mPager != null && mPagerAdapter != null) {
            FragmentHolder fragmentHolder =
                    mPagerAdapter.getFragmentHolderAtPosition(mPager.getCurrentItem());
            if (fragmentHolder != null && (isEscapeCheckFragmemt(fragmentHolder) || getBannerInThisPage(fragmentHolder) != null)) {
                setTopViewColor(color, Color.TRANSPARENT, lastSetFraction);
            }
        }
    }

    private void setTopViewColor(final int color, final int topRectColor, final float fraction) {
        lastSetFraction = fraction;
        if (color == Color.TRANSPARENT && mDropDownAdImg != null) {
            mDropDownAdImg.setVisibility(View.INVISIBLE);
            if (mTwoStyleImgView != null && !isGoingSecondFloor) {
                mTwoStyleImgView.setVisibility(View.INVISIBLE);
            }
        }

        if (color == Color.TRANSPARENT && topRectColor == Color.TRANSPARENT) {
            if (mBottomOvalView != null) {
                setBottomOvalViewColor(fraction);
                setBottomOvalViewVisibility(false);
            }
            if (mTopBgView != null) {
                mTopBgView.setVisibility(View.INVISIBLE);
            }
            lastSetColor = color;
            return;
        }

        if (mValueAnimator != null) {
            mValueAnimator.cancel();
            mValueAnimator = null;
        }

        if (mBottomOvalView != null) {
            if (lastSetColor == mDefaultTabColor) {
                mValueAnimator = ValueAnimator.ofFloat(0f, 1f);
                mValueAnimator.setDuration(600);
                final int finalLastColor = lastSetColor;
                mValueAnimator.addUpdateListener(animation -> {
                    Object animatedValue = animation.getAnimatedValue();
                    if (animatedValue instanceof Float) {
                        float i = (float) animatedValue;
                        Integer evaluate = (Integer) argbEvaluator.evaluate(i, finalLastColor
                                , color);
                        if (mBottomOvalView != null) {
                            setBottomOvalViewColor(evaluate, topRectColor, fraction);
                        }
                    }
                });
                mValueAnimator.start();
            } else if (color == Color.TRANSPARENT && topRectColor != Color.TRANSPARENT) {

                setBottomOvalViewColor(color, topRectColor, fraction);
            } else {
                setBottomOvalViewColor(color, topRectColor, fraction);
            }
            setBottomOvalViewVisibility(true);
        }

        if (mTopBgView != null && !BaseFragmentActivity.sIsDarkMode) {
            mTopBgView.setVisibility(View.VISIBLE);
        }

        if (color != Color.TRANSPARENT) {
            lastSetColor = color;
        } else if (topRectColor != Color.TRANSPARENT && topRectColor != Color.WHITE) {
            lastSetColor = topRectColor;
        } else {
            lastSetColor = color;
        }
    }


    private void setBottomOvalViewVisibility(boolean visible) {
        if (mBottomOvalView != null) {
            mBottomOvalView.setVisibility(visible && !mIsInUnderageMode && !(SkinManager.INSTANCE.isNeedShowAtmosphereImage() && isCurrTabClass(RecommendFragmentStaggered.class)) &&
                    (!BaseFragmentActivity.sIsDarkMode || isShowPormotionOperationModule()) ? View.VISIBLE : View.INVISIBLE);
        }
    }

    private float mFraction = -1.0f;

    private void setBottomOvalViewColor(int color, int topRectColor, float fraction) {
        if (HomePageTabModel.ITEM_TYPE_RECOMMEND.equals(getCurrentTabItemType()) && BaseFragmentActivity2.sIsDarkMode
                && !isShowPormotionOperationModule()) {
            color = 0xff1e1e1e;
        }
        if (mSetBottomOvalViewColorRunnable != null) {
            removeCallbacks(mSetBottomOvalViewColorRunnable);
            mSetBottomOvalViewColorRunnable.setData(color, topRectColor, fraction);
        } else {
            mSetBottomOvalViewColorRunnable = new SetBottomOvalViewColorRunnable(color, topRectColor, fraction);
        }
        postOnUiThreadDelayed(mSetBottomOvalViewColorRunnable, 30);
        updateSearchBtnColor(color, topRectColor);

    }

    private void updateAtmosphereAlpha(float fraction) {
        if (SkinManager.INSTANCE.isNeedShowAtmosphereImage()
                && isShowRecommendFragment() && !isShowUnitPackBgAd()) {
            mFraction = fraction;
            float alphaScale = 1.0f - fraction;
            if (alphaScale < 0.0f) {
                alphaScale = 0.0f;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (alphaScale == 0.0f) {
                    showOrHideAtmosphereBg(false);
                } else {
                    showOrHideAtmosphereBg(true);
                    if (mIvAtmosphereBg.getDrawable() != null) {
                        mIvAtmosphereBg.getDrawable().setAlpha((int) (255 * alphaScale));
                    }
                    if (mIvAtmosphereIcon.getDrawable() != null) {
                        mIvAtmosphereIcon.getDrawable().setAlpha((int) (255 * alphaScale));
                    }
                }
            }
        }
    }

    private void updateTopBgForSceneAlpha(float fraction) {
        if (TopBgForSceneManager.INSTANCE.isHaveNewSceneCard() && isShowRecommendFragment()) {
            mFraction = fraction;
            float alphaScale = 1.0f - fraction;
            if (alphaScale < 0.0f) {
                alphaScale = 0.0f;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (alphaScale == 0.0f) {
                    updateTopBgForScene(false);
                } else {
                    updateTopBgForScene(true);
                    if (mIvTopBgForSceneInDark.getDrawable() != null) {
                        mIvTopBgForSceneInDark.getDrawable().setAlpha((int) (255 * alphaScale));
                    }
                }
            }
        }
    }

    private boolean isShowUnitPackBgAd() {
        if (mPagerAdapter == null) {
            return false;
        }
        Fragment fragment = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
        if (fragment == null || fragment.getView() == null) {
            return false;
        }
        Object object = fragment.getView().getTag(R.id.main_has_unit_pack_bg_ad);
        if (object instanceof Boolean) {
            return (boolean) object;
        }
        return false;
    }

    public boolean isShowRecommendFragment() {
        int index = mPager.getCurrentItem();
        HomePageTabModel model = null;
        if (mTabModelList != null && index >= 0 && index < mTabModelList.size()) {
            model = mTabModelList.get(index);
        }
        if (model != null) {
            if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_RECOMMEND,
                    model.getItemType())) {
                return true;
            }
        }
        return false;
    }

    private boolean isShowNewZoneStyleRecommendFragment() {
        return isShowRecommendFragment() && BaseBannerView.sIsHasNewZoneStyleBanner;
    }

    private void updateSearchBtnColor(int color, int topRectColor) {
        if (BaseFragmentActivity.sIsDarkMode || mIvSearchBarIcon.getVisibility() != View.VISIBLE) {
            return;
        }
        boolean setSearchBtnColorToBgColor = false;
        if (mBottomOvalView.getVisibility() == View.VISIBLE) {
            if (color != Color.TRANSPARENT && color != Color.WHITE) {
//                Logger.i(TAG, "setBottomOvalViewColor 1");
                HEADER_VIEW_COLOR.SEARCH_BTN_BG.setWhiteThemeColor(color);
                setSearchBtnColorToBgColor = true;
            } else if (topRectColor != Color.TRANSPARENT && topRectColor != Color.WHITE) {
                HEADER_VIEW_COLOR.SEARCH_BTN_BG.setWhiteThemeColor(topRectColor);
                setSearchBtnColorToBgColor = true;
//                Logger.i(TAG, "setBottomOvalViewColor 2");
            }
        }
        if (!setSearchBtnColorToBgColor) {
            HEADER_VIEW_COLOR.SEARCH_BTN_BG.setWhiteThemeColor(ColorUtil.INVALID_COLOR);
//            Logger.i(TAG, "setBottomOvalViewColor 3");
        }
//        Logger.i(TAG, "setBottomOvalViewColor 4");
        int searchBtnColor = HEADER_VIEW_COLOR.SEARCH_BTN_BG.getWhiteThemeColor();
        if (searchBtnColor != ColorUtil.INVALID_COLOR) {
            mIvSearchBarIcon.setColorFilter(HEADER_VIEW_COLOR.SEARCH_BTN_BG.getWhiteThemeColor(), PorterDuff.Mode.SRC_IN);
            mIvSearchRecognizeBtn.setColorFilter(HEADER_VIEW_COLOR.SEARCH_BTN_BG.getWhiteThemeColor(), PorterDuff.Mode.SRC_IN);
        } else {
            int defaultColor = getDefaultSearchIconColor();
            if (defaultColor != ColorUtil.INVALID_COLOR) {
                mIvSearchBarIcon.setColorFilter(defaultColor, PorterDuff.Mode.SRC_IN);
                mIvSearchRecognizeBtn.setColorFilter(defaultColor, PorterDuff.Mode.SRC_IN);
            } else {
                mIvSearchBarIcon.clearColorFilter();
                mIvSearchRecognizeBtn.clearColorFilter();
            }
        }
//        mVSearchBtnMask.setVisibility(setSearchBtnColorToBgColor ? View.VISIBLE : View.INVISIBLE);
        if (!setSearchBtnColorToBgColor) {
            mIvSearchBarIcon.setColorFilter(getNormalSearchIconColor(false), PorterDuff.Mode.SRC_IN);
            mIvSearchRecognizeBtn.setColorFilter(getNormalSearchIconColor(true), PorterDuff.Mode.SRC_IN);
        }
    }


    private void setBottomOvalViewColor(float fraction) {
        setBottomOvalViewColor(Color.TRANSPARENT, Color.TRANSPARENT, fraction);
    }

    private boolean recordDropDown = false;

    private void showDropAd(float floatExtra, boolean reset, int scrollY) {
        if (mDropDownAdImg == null) {
            return;
        }

        if (reset) {
            recordDropDown = false;
        }

        float realFloatExtra = floatExtra;
        floatExtra = 1.0f - floatExtra;
        if (floatExtra <= 0.0f) {
            floatExtra = 0.0f;
        }

        if (mLastScrollValue == 0.0f && floatExtra != 0.0f) {
            mFirstScrollHaveAd = hasDownloadedDropDownAd;
            mFirstScrollAdImgUrl = mDropDownAdUrl;
            mFirstScrollAd = mDropDownAdvertis;
        }

        mLastScrollValue = realFloatExtra;

        if (!reset && mBottomOvalView != null /* && mBottomOvalView.getBgColor() != Color
        .TRANSPARENT*/
                && mFirstScrollHaveAd && HomePageFragment.mDropDownAdvertis != null && mTwoStyleImgView != null) {
            if (mTabs != null) {
                mTabs.setAlpha(floatExtra);
            }
            if (mIvEditTab != null) {
                mIvEditTab.setAlpha(floatExtra);
            }
            if (mVgSearchBar != null) {
                mVgSearchBar.setAlpha(floatExtra);
            }
            if (mBottomOvalView != null) {
                mBottomOvalView.setAlpha(floatExtra);
            }
            if (mTopBgView != null) {
                mTopBgView.setAlpha(floatExtra);
            }

            if (mFirstScrollAd != null &&
                    (AdManager.isDropDownSecondType(mFirstScrollAd.getShowstyle()) || mFirstScrollHaveAd) &&
                    mTwoStyleImgView != null) {
                if (scrollY != 0) {
                    if (titleBarAndSearchHeight == 0) {
                        titleBarAndSearchHeight = getTitleBarAndSearchHeight(mContext);
                    }
                    if (AdManager.isDropDownSecondType(mFirstScrollAd.getShowstyle())) {
                        mTwoStyleImgView.setShowHeight(scrollY + titleBarAndSearchHeight);
                    }

                    if (mReversePairImageView != null && mReversePairImageView.getVisibility() == View.VISIBLE) {
                        mReversePairImageView.setHasDropAd(true);
                        mReversePairImageView.setDropDownHeight(scrollY + titleBarAndSearchHeight);
                    }
                } else {
                    if (mReversePairImageView != null && mReversePairImageView.getVisibility() == View.VISIBLE) {
                        mReversePairImageView.setHasDropAd(true);
                        mReversePairImageView.setDropDownHeight(0);
                    }
                }
            }

            if (floatExtra > 0.0f && floatExtra != 1.0f) {
                if (mFirstScrollAd != null && AdManager.isDropDownSecondType(mFirstScrollAd.getShowstyle())) {
                    if (!mFirstScrollAdImgUrl.equals(mTwoStyleImgView.getTag())) {
                        final String adImgUrl = mFirstScrollAdImgUrl;
                        ImageManager.Options options = new ImageManager.Options();
                        options.targetWidth = BaseUtil.getScreenWidth(getContext());
                        ImageManager.from(mContext).downloadBitmap(mFirstScrollAdImgUrl, options,
                                (lastUrl, bitmap) -> {
                                    if (TextUtils.equals(mFirstScrollAdImgUrl, lastUrl) && bitmap != null) {
                                        mTwoStyleImgView.setImageBitmap(
                                                LocalImageUtil.zoomImg(bitmap,
                                                        BaseUtil.getScreenWidth(getContext())));

                                        mTwoStyleImgView.setTag(adImgUrl);
                                    }
                                });
                    }

                    if (mTwoStyleImgView.getVisibility() != View.VISIBLE) {
                        if (!recordDropDown) {
                            AdManager.adRecord(mContext, mFirstScrollAd,
                                    AdReportModel.newBuilder(AppConstants
                                                            .AD_LOG_TYPE_SITE_SHOW,
                                                    AppConstants.AD_POSITION_NAME_HOME_DROP_DOWN)
                                            .dropDownStage(RecommendFragmentNew.isSecondFlowAdAutoRefreshing
                                                    ? "autoPicShow" : "userPicShow").build());

                            recordDropDown = true;
                        }
                        mTwoStyleImgView.setVisibility(View.VISIBLE);
                    }
                    if (mDropDownAdvertis != null) {
                        ImageManager.from(mContext).displayImage(mAdTag,
                                mDropDownAdvertis.getAdMark(), com.ximalaya.ting.android.host.R.drawable.host_ad_tag_style_2);
                        mAdTag.setVisibility(View.VISIBLE);
                    }
                    mDropDownAdImg.setVisibility(View.INVISIBLE);
                } else if (mFirstScrollAd != null && mFirstScrollAd.getShowstyle() == Advertis.IMG_SHOW_TYPE_DROP_DOWN_TYPE) {
                    if (!mFirstScrollAdImgUrl.equals(mDropDownAdImg.getTag())) {
                        final String adImgUrl = mFirstScrollAdImgUrl;
                        ImageManager.from(mContext).downloadBitmap(mFirstScrollAdImgUrl,
                                (lastUrl, bitmap) -> {
                                    if (TextUtils.equals(mFirstScrollAdImgUrl, lastUrl) && bitmap != null) {
                                        ViewGroup.LayoutParams layoutParams =
                                                mDropDownAdImg.getLayoutParams();
                                        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                                            int height =
                                                    (int) (BaseUtil.getScreenWidth(mContext) * 1.0f / bitmap.getWidth() * bitmap.getHeight());
                                            layoutParams.width = BaseUtil.getScreenWidth(mContext);
                                            layoutParams.height = height;
                                            mDropDownAdImg.setLayoutParams(layoutParams);
                                        }

                                        mDropDownAdImg.setImageBitmap(bitmap);
                                        mDropDownAdImg.setTag(adImgUrl);
                                    }
                                });
                    }
                    if (mDropDownAdImg.getVisibility() != View.VISIBLE) {
                        mDropDownAdImg.setVisibility(View.VISIBLE);
                        AdManager.adRecord(mContext, mFirstScrollAd,
                                AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                AppConstants.AD_POSITION_NAME_HOME_DROP_DOWN);
                    }
                    mTwoStyleImgView.setVisibility(View.INVISIBLE);
                    if (mDropDownAdvertis != null) {
                        ImageManager.from(mContext).displayImage(mAdTag,
                                mDropDownAdvertis.getAdMark(), com.ximalaya.ting.android.host.R.drawable.host_ad_tag_style_2);
                        mAdTag.setVisibility(View.VISIBLE);
                    }
                }
            } else if (Math.abs(realFloatExtra) == 0.0f) {
                mDropDownAdImg.setVisibility(View.INVISIBLE);
                mTwoStyleImgView.setVisibility(View.INVISIBLE);
                mAdTag.setVisibility(View.INVISIBLE);
                recordDropDown = false;
            }
        } else {
            if (mTabs != null) {
                mTabs.setAlpha(1.0f);
            }
            if (mIvEditTab != null) {
                mIvEditTab.setAlpha(1.0f);
            }
            if (mVgSearchBar != null) {
                mVgSearchBar.setAlpha(1.0f);
            }
            if (mBottomOvalView != null) {
                mBottomOvalView.setAlpha(1.0f);
            }
            if (mTopBgView != null) {
                mTopBgView.setAlpha(1.0f);
            }
            if (mDropDownAdImg != null) {
                mDropDownAdImg.setVisibility(View.INVISIBLE);
            }
            if (mTwoStyleImgView != null) {
                mTwoStyleImgView.setVisibility(View.INVISIBLE);
            }

            if (mReversePairImageView != null && mReversePairImageView.getVisibility() == View.VISIBLE) {
                mReversePairImageView.setHasDropAd(false);
                mReversePairImageView.setDropDownHeight(scrollY);
            }

            recordDropDown = false;
        }
    }

    private void showTwoStyleDropAd() {
        if (mPager != null) {
            isGoingSecondFloor = true;

            int showHeight = 0;
            if (mTwoStyleImgView != null) {
                showHeight = mTwoStyleImgView.getShowHeight();
            }
            final ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(mPager, "translationY",
                    mPager.getTranslationY(),
                    mPager.getHeight() - (showHeight - HomePageFragment.getTitleBarAndSearchHeight(mContext)));

            final int finalShowHeight = showHeight;
            objectAnimator.addUpdateListener(animation -> {
                if (animation != null && animation.getAnimatedValue() instanceof Float) {
                    int i = (int) (finalShowHeight + (float) animation.getAnimatedValue());
                    if (mTwoStyleImgView != null) {
                        mTwoStyleImgView.setShowHeight(i);
                    }

                    if (mReversePairImageView != null && mReversePairImageView.getVisibility() == View.VISIBLE) {
                        mReversePairImageView.setHasDropAd(true);
                        mReversePairImageView.setDropDownHeight(i);
                    }
                }
            });

            ObjectAnimator bottomBarAnimator = null;
            ObjectAnimator bottomBgAnimator = null;
            ObjectAnimator playBarAnimator = null;
            ObjectAnimator hotLayAnimator = null;
            if (getActivity() instanceof MainActivity) {
                View bottomBar = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.rg_tabs);
                if (bottomBar != null) {
                    bottomBarAnimator = ObjectAnimator.ofFloat(bottomBar, "translationY", 0,
                            bottomBar.getHeight());
                }

                View containerView = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.fragment_container);
                if (containerView != null) {
                    ViewGroup.LayoutParams layoutParams = containerView.getLayoutParams();
                    if (layoutParams instanceof RelativeLayout.LayoutParams) {
                        ((RelativeLayout.LayoutParams) layoutParams).addRule(RelativeLayout.ABOVE
                                , 0);
                        containerView.setLayoutParams(layoutParams);
                    }
                }

                View bottomBg = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.host_v_tabs_bg);
                if (bottomBg != null) {
                    bottomBgAnimator = ObjectAnimator.ofFloat(bottomBg, "translationY", 0,
                            bottomBg.getHeight());
                    View playBar = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.fragment_playbar);
                    if (playBar != null && !PlayBarAbManager.INSTANCE.useNewPlayBar()) {
                        playBarAnimator = ObjectAnimator.ofFloat(playBar, "translationY", 0,
                                bottomBg.getHeight());
                    } else if (PlayBarAbManager.INSTANCE.useNewPlayBar()) {
                        View playBarNew = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.fragment_playbar_new);
                        playBarNew.scrollTo(0, BaseUtil.getScreenHeight(BaseApplication.getMyApplicationContext()));
                    }

                    View hotLay = getActivity().findViewById(com.ximalaya.ting.android.host.R.id.host_bottom_hot_lay);
                    if (hotLay != null) {
                        hotLayAnimator = ObjectAnimator.ofFloat(hotLay, "translationY", 0,
                                hotLay.getHeight());
                    }
                }
            }

            final AnimatorSet animatorSet = new AnimatorSet();

            final Set<Animator> objectAnimators = new HashSet<>();
            objectAnimators.add(objectAnimator);
            if (bottomBarAnimator != null) {
                objectAnimators.add(bottomBarAnimator);
            }
            if (bottomBgAnimator != null) {
                objectAnimators.add(bottomBgAnimator);
            }
            if (playBarAnimator != null) {
                objectAnimators.add(playBarAnimator);
            }
            if (hotLayAnimator != null) {
                objectAnimators.add(hotLayAnimator);
            }

            animatorSet.playTogether(objectAnimators);
            animatorSet.setDuration(HomePageFragment.ANIMATION_TIME);

            final ObjectAnimator finalPlayBarAnimator = playBarAnimator;
            animatorSet.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);

                    if (mFirstScrollAd != null) {
                        AdManager.adRecord(mContext, mFirstScrollAd,
                                AdReportModel.newBuilder(AppConstants
                                                        .AD_LOG_TYPE_SITE_SHOW,
                                                AppConstants.AD_POSITION_NAME_HOME_DROP_DOWN)
                                        .adIdIsNegative(true)
                                        .dropDownStage("picShow").build());

                        if (mFirstScrollAd.getShowstyle() == Advertis.IMG_SHOW_TYPE_DROP_DOWN_H5) {
                            AdManager.handlerAdClick(mContext, mFirstScrollAd,
                                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                                    AppConstants.AD_POSITION_NAME_HOME_DROP_DOWN)
                                            .ignoreTarget(true).dropDownStage("h5Click").build());

                            BaseFragment fragment =
                                    NativeHybridFragment.newInstance(mFirstScrollAd.getRealLink(), true);
                            startFragment(fragment, -1, -1);

                            // 新首页-下拉二楼  点击事件
//                            new XMTraceApi.Trace()
//                                    .click(40831)
//                                    .put("url", mFirstScrollAd.getRealLink())
//                                    .put("contentType", AdManager.converContentType(mFirstScrollAd.getItingType()))
//                                    .put("contentId", mFirstScrollAd.getAdid() + "")
//                                    .put("currPage", "newHomePage")
//                                    .createTrace();
                        } else {
                            Bitmap bitmap =
                                    RecommendFragmentAdNewUtil.getDropDownAdBitmap() != null ?
                                            RecommendFragmentAdNewUtil.getDropDownAdBitmap() :
                                            RecommendFragmentAdUtil.getDropDownAdBitmap();

                            SecondFloorFragment fra =
                                    SecondFloorFragment.newInstance(mFirstScrollAd, bitmap);
                            startFragment(fra, -1, -1);
                        }
                    }

                    HandlerManager.postOnUIThreadDelay(() -> {
                        Activity activity = getActivity();
                        View containerView = (activity != null) ?
                                activity.findViewById(com.ximalaya.ting.android.host.R.id.fragment_container) : null;
                        if (containerView != null) {
                            ViewGroup.LayoutParams layoutParams =
                                    containerView.getLayoutParams();
                            if (layoutParams instanceof RelativeLayout.LayoutParams) {
                                ((RelativeLayout.LayoutParams) layoutParams).addRule(RelativeLayout.ABOVE, com.ximalaya.ting.android.host.R.id.host_v_tabs_bg);
                                containerView.setLayoutParams(layoutParams);
                            }
                        }

                        for (Animator animator : objectAnimators) {
                            if (animator instanceof ObjectAnimator) {
                                if (animator == finalPlayBarAnimator) {
                                    continue;
                                }

                                Object target = ((ObjectAnimator) animator).getTarget();
                                if (target instanceof View) {
                                    ((View) target).setTranslationY(0);
                                }
                            }
                        }

                        isGoingSecondFloor = false;
                    }, 400);
                }
            });
            animatorSet.setInterpolator(new AccelerateInterpolator());
            animatorSet.start();
        }
    }

    public static int getTitleBarAndSearchHeight(Context context) {
        return BaseUtil.dp2px(context, 32 + 42) + BaseUtil.getStatusBarHeight(context);
    }

    @Override
    public boolean onBackPressed() {
        // 如果正在去下拉二楼则先不处理返回按钮
        if (isGoingSecondFloor) {
            return true;
        }
        return super.onBackPressed();
    }

    // 请求接口，加载tab数据
    @Override
    public void reloadTabData(boolean recommendTab) {
        new HomePageTabRequestTask(() -> {
            if (canUpdateUi()) {
                loadTabData();
            }
        }, null, recommendTab).myexec();
    }

    @Override
    public boolean change2RecommendWhileInVipTab() {
        if (TextUtils.equals(HomePageTabModel.ITEM_TYPE_VIP, getCurrentTabItemType())) {
            switchFindingTabTo(HomePageTabModel.ITEM_TYPE_RECOMMEND);
            return true;
        }
        return false;
    }

    @Override
    public void checkToShowNewTabGuideView() {
        if (mTabs != null && canUpdateUi()) {
            // 旧版HomePageNewTabGuideManager 用户首页tab引导逻辑已下架，这里使用新会员tab引导替换[added at 9.2.12]
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    if (canUpdateUi()) {
//                        boolean enableShowNewVipTabGuidePopupView = HomePageVipTabGuideManager.Companion.isEnableShowNewVipTabGuidePopupView();
                            // 9.2.21版本 会员tab用户教育引导蒙层移除
//                        if (enableShowNewVipTabGuidePopupView) {
//                            //展示会员 tab 引导
//                            Rect rect = new Rect();
//                            View view = mTabs.getChildAt(0);
//                            int targetIndex = -1;
//                            if (view instanceof ViewGroup && mTabModelList != null && !mTabModelList.isEmpty()) {
//                                for (int i = 0; i < mTabModelList.size(); i++) {
//                                    HomePageTabModel tabModel = mTabModelList.get(i);
//                                    if (tabModel != null && HomePageTabModel.ITEM_TYPE_VIP.equals(tabModel.getItemType())) {
//                                        targetIndex = i;
//                                    }
//                                }
//                                if (targetIndex != -1) {
//                                    View targetView = ((ViewGroup) view).getChildAt(targetIndex);
//                                    if (targetView != null) {
//                                        targetView.getGlobalVisibleRect(rect);
//                                        if (!rect.isEmpty()) {
//                                            new HomePageVipTabGuideManager(HomePageFragment.this).show(rect);
//                                        }
//                                    }
//                                }
//                            }
//                            return;
//                        }

                    }
                }
            }, 200);
        }
    }

    @Override
    public void notifyCustomizeDialogFinish() {
        if (mPagerAdapter != null && mPager != null) {
            Fragment f = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
//            if (f instanceof RecommendFragmentNew) {
//                ((RecommendFragmentNew) f).handleOnCustomizeDialogFinish();
//            }
        }
    }

    @Override
    public boolean recommendFragmentIsShowing() {
        if (mPagerAdapter != null && mPager != null) {
            Fragment f = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            return false;
        }
        return false;
    }

    @Override
    public boolean recommendFragmentStaggeredIsShowing() {
        if (mPagerAdapter != null && mPager != null) {
            Fragment f = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());

            if (f instanceof RecommendFragmentStaggered || f instanceof RecommendRnFragment) {
                return !f.isHidden() && ((BaseFragment) f).isRealVisable();
            }
        }
        return false;
    }

    @Override
    public boolean isFragmentOnTop() {
        return isRealVisable();
    }

    @Override
    public Fragment getCurTabFragment() {
        if (mPagerAdapter != null && mPager != null) {
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(mPager.getCurrentItem());
            return fragment;
        }
        return null;
    }

    @Override
    public boolean isRefreshBecauseOfTabClick() {
        Fragment curFrag = getCurTabFragment();
        if (curFrag instanceof RecommendFragmentStaggered) {
            return ((RecommendFragmentStaggered) curFrag).isRefreshBecauseOfTabClick();
        } else if (curFrag instanceof RecommendRnFragment) {
            return ((RecommendRnFragment) curFrag).isRefreshBecauseOfTabClick();
        }
        return false;
    }

    @Override
    public void tryToShowChatXmlyBubble() {
        if (ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
            if (!mHasShowChildXmlyAnimation) {
                if (mLottieChatXmlyBubbleRobot != null) {
                    mLottieChatXmlyBubbleRobot.setVisibility(View.GONE);
                }
                showOrHideSearchRecognizeBtn(false);
            }
            HandlerManager.postOnUIThreadDelay(() -> showChildChatXmlyBubble(false), 3000);
        } else {
            ChatXmlyPopupManager.INSTANCE.tryShowBubble(new ChatXmlyPopupManager.IShowBubbleAction() {
                @Override
                public void showBubble(@NonNull ChatXmlyTips.ChatXmlyTipsBean bubbleInfo) {
                    showChatXmlyBubble(bubbleInfo);
                }

                @Override
                public void updateRedDot() {
                    updateVoiceSearchRedDot();
                }
            }, "homePage", 2000);
        }
    }

    @Override
    public void refreshMsgCount() {
        if (MyListenAbUtil.INSTANCE.hasSlideBar()) {
            int unReadMessageCount = NoReadManage.getInstance(mContext).getNoReadModel().getUnReadMessageCount();
            if (MyListenAbUtil.INSTANCE.hasSlideBarWithMessage() && unReadMessageCount > 0) {
                ViewStatusUtil.setVisible(View.VISIBLE, mSlideRedDot);
                if (unReadMessageCount < 10) {
                    mSlideRedDot.setPadding(BaseUtil.dp2px(mContext, 4), 0, BaseUtil.dp2px(mContext, 4), BaseUtil.dp2px(mContext, 1));
                } else {
                    mSlideRedDot.setPadding(BaseUtil.dp2px(mContext, 3), 0, BaseUtil.dp2px(mContext, 3), BaseUtil.dp2px(mContext, 1));
                }
                mSlideRedDot.setText(unReadMessageCount > 99 ? "99+" : unReadMessageCount + "");
            } else {
                ViewStatusUtil.setVisible(View.INVISIBLE, mSlideRedDot);
            }
        }
    }

    private void showFreshGiftDialogForDeepLinkIfNeed() {
        boolean needShow = SharedPreferencesUtil.getInstance(mContext)
                .getBoolean(PreferenceConstantsInHost.KEY_DEEP_LINK_NEED_SHOW_NEW_GIFT, false);

        if (!needShow) {
            return;
        }

        showFreshGiftDialog();
        SharedPreferencesUtil.getInstance(mContext)
                .saveBoolean(PreferenceConstantsInHost.KEY_DEEP_LINK_NEED_SHOW_NEW_GIFT, false);
    }

    private void showFreshGiftDialog() {
        if (ElderlyModeManager.getInstance().nowFromIting()) {
            return;
        }
        try {
            FreshGiftFragment fragment = new FreshGiftFragment();
            fragment.showFreshGift(getChildFragmentManager(), "fresh_gift");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showFreshGiftMagnetic() {
        // 开关控制是否显示，没取到默认不显示
        if (ConfigureCenter.getInstance().getBool("toc", "New-gift-bag-youxiajiao", false)) {
            try {
                MagneticView view = new MagneticView(mContext,
                        Configure.AnchorSkillEntranceId.FRESH_GIFT);
                view.show(HomePageFragment.this, null);
                userTrackOnGiftShown();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void userTrackOnGiftShown() {
        new UserTracking()
                .setModuleType("新人礼包挂件")
                .setSrcPage("首页_推荐")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
    }

    @Override
    public HomePageTabModel getCurrentTabModel() {
        if (mTabs != null) {
            int index = mTabs.getCurrentItem();
            if (!ToolUtil.isEmptyCollects(mTabModelList) && 0 <= index && index < mTabModelList.size()) {
                return mTabModelList.get(index);
            }
        }
        return null;
    }

    private int getCurrentTabCategoryId() {
        if (mTabs != null) {
            int index = mTabs.getCurrentItem();
            if (!ToolUtil.isEmptyCollects(mTabModelList) && 0 < index && index < mTabModelList.size()) {
                HomePageTabModel homePageTabModel = mTabModelList.get(index);
                return homePageTabModel == null ? -1 : homePageTabModel.getCategoryId();
            }
        }
        return -1;
    }

    private String getCurrentTabItemType() {
        if (mTabs != null) {
            int index = mTabs.getCurrentItem();
            if (!ToolUtil.isEmptyCollects(mTabModelList) && 0 <= index && index < mTabModelList.size()) {
                HomePageTabModel homePageTabModel = mTabModelList.get(index);
                return homePageTabModel == null ? "" : homePageTabModel.getItemType();
            }
        }
        return "";
    }

    private String getCurrentTabName() {
        if (mTabs != null) {
            int index = mTabs.getCurrentItem();
            if (!ToolUtil.isEmptyCollects(mTabModelList) && 0 <= index && index < mTabModelList.size()) {
                HomePageTabModel homePageTabModel = mTabModelList.get(index);
                return homePageTabModel == null ? "" : homePageTabModel.getTitle();
            }
        }
        return "";
    }

    private void updateSearchWord() {
        updateSearchHint(getCurrentTabCategoryId(), getCurrentTabItemType());
    }

    @Override
    public void updateSearchHintWithParentCategoryId() {
        updateSearchHint(-1, getCurrentTabItemType());
    }

    @Override
    public void updateSearchHint(int categoryId) {
        if (ChildProtectManager.isChildMode(getContext())) {
            return;
        }
        try {
            if (Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFunctionAction() != null) {
                Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFunctionAction().loadSearchHint(categoryId, mSearchHintCallBack);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateSearchHint(int categoryId, String tabItemType) {
        if (ChildProtectManager.isChildMode(getContext())) {
            return;
        }
        try {
            if (Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFunctionAction() != null) {
                Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFunctionAction().loadSearchHint(categoryId, tabItemType, mSearchHintCallBack);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ISearchHintCallback<ListModeBase<SearchHotWord>> mSearchHintCallBack = new ISearchHintCallback<ListModeBase<SearchHotWord>>() {
        @Override
        public void onSuccess(ListModeBase<SearchHotWord> listModeBase) {
            //搜索热词加载成功
            if (!canUpdateUi() || !isRealVisable() || listModeBase == null || mSearchHotWordSwitchManager == null) {
                return;
            }
            List<SearchHotWord> hotWords = listModeBase.getList();
            if (ToolUtil.isEmptyCollects(hotWords)) {
                return;
            }
            mSearchHotWordSwitchManager.setSearchHintData(hotWords);
            mSearchHotWordSwitchManager.setSwitchDuration(listModeBase.getDisplayTime());
            mSearchHotWordSwitchManager.setTabNameAndCategoryId(getCurrentTabName(), getCurrentTabCategoryId());
            mSearchHotWordSwitchManager.startSwitch();
        }

        @Override
        public void onFailed(int code, String message) {
            //搜索热词加载失败
            if (!canUpdateUi()) {
                return;
            }
            Logger.d(TAG, getString(R.string.main_search_hint_update_failed));
        }
    };

    public void updateHomePageSearchHintSuc(ListModeBase<SearchHotWord> listModeBase) {
        if (mSearchHintCallBack != null) {
            mSearchHintCallBack.onSuccess(listModeBase);
        }
    }

    public void updateHomePageSearchHintFail(int code, String message) {
        if (mSearchHintCallBack != null) {
            mSearchHintCallBack.onFailed(code, message);
        }
    }

    private ISceneNewCardChangeListener mSceneNewCardChangeListener = new ISceneNewCardChangeListener() {
        @Override
        public void onSceneNewCardChanged(boolean isHaveNewSceneCard) {
            if (canUpdateUi()) {
                updateTopBgForScene(isHaveNewSceneCard);
                updateAtmosphere();
            }
        }
    };

    private ISkinSettingChangeListener mSkinSettingChangeListener = new SkinSettingChangeWrapListener() {
        @Override
        public void onMainColorChanged() {
            if (canUpdateUi()) {
                updateSkinSetting();
                updateBgAndForegroundColor(false);
            }
        }

        @Override
        public void onAtmosphereInfoChanged() {
            if (!canUpdateUi()) {
                return;
            }
            updateAtmosphere();
        }
    };

    public void updateAtmosphere() {
        if (isShowRecommendFragment()) {
            if (mFraction > 1.0f) {
                return;
            }
            if (TopBgForSceneManager.INSTANCE.isHaveNewSceneCard()) {
                return;
            }
            if (isShowUnitPackBgAd()) {
                return;
            }
            AtmosphereInfo atmosphereInfo = SkinManager.INSTANCE.getAtmosphereInfo();
            if (atmosphereInfo != null && atmosphereInfo.isExist()) {
                switch (atmosphereInfo.getConfigType()) {
                    case 2:
                    case 3:
                        showOrHideAtmosphereBg(true);
                        updateBgAndForegroundColor(true);
                        if (BaseFragmentActivity.sIsDarkMode) {
                            ImageManager.from(mContext).displayImage(mIvAtmosphereBg, atmosphereInfo.getDarkTopBackgroundUrl(), -1, null);
                            ImageManager.from(mContext).displayImage(mIvAtmosphereIcon, atmosphereInfo.getDarkMaterialPicUrl(), -1, null);
                        } else {
                            ImageManager.from(mContext).displayImage(mIvAtmosphereBg, atmosphereInfo.getTopBackgroundUrl(), -1, null);
                            ImageManager.from(mContext).displayImage(mIvAtmosphereIcon, atmosphereInfo.getMaterialPicUrl(), -1, null);
                        }
                        break;
                    default:
                        showOrHideAtmosphereBg(false);
                        updateBgAndForegroundColor(false);
                        break;
                }
                return;
            }
        }
        showOrHideAtmosphereBg(false);
        updateBgAndForegroundColor(false);
    }

    public void updateTopBgForScene(boolean isHaveNewSceneCard) {
        if (!isShowRecommendFragment() || !BaseFragmentActivity2.sIsDarkMode || !isHaveNewSceneCard || !TopBgForSceneManager.INSTANCE.isHaveDomainColor()) {
            mIvTopBgForSceneInDark.setVisibility(View.GONE);
        } else {
            mIvTopBgForSceneInDark.setVisibility(View.VISIBLE);
            mIvTopBgForSceneInDark.getDrawable().mutate().setColorFilter(TopBgForSceneManager.INSTANCE.getDomainColor(), PorterDuff.Mode.SRC_IN);
        }
    }

    private void showOrHideAtmosphereBg(boolean isShow) {
        if (isShow && mFraction < 1.0f && !ChildProtectManager.isChildMode(mContext) && !TopBgForSceneManager.INSTANCE.isHaveNewSceneCard()) {
            if (!mIsTopBgShown) {
                mIsTopBgShown = true;
                String xmRequestId = SkinManager.INSTANCE.getAtmosphereInfo().getXmRequestId();
                if (TextUtils.isEmpty(xmRequestId)) {
                    xmRequestId = XmRequestIdManager.getInstance(mContext).getRequestId();
                    SkinManager.INSTANCE.getAtmosphereInfo().setXmRequestId(xmRequestId);
                }
                // 新首页-氛围头图  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(62413)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("xmRequestId", xmRequestId)
                        .put("contentId", "1")
                        .put("contentType", "topBg")
                        .createTrace();
            }
            mIvAtmosphereBg.setVisibility(View.VISIBLE);
            mIvAtmosphereIcon.setVisibility(View.VISIBLE);
            FragmentHolder fragmentHolder = mPagerAdapter.getFragmentHolderAtPosition(mPager.getCurrentItem());
            RelativeLayout.LayoutParams relativeLayoutParams = null;
//            ViewGroup.LayoutParams layoutParams = mIvAtmosphereBg.getLayoutParams();
//            if (layoutParams instanceof ViewGroup.LayoutParams) {
//                relativeLayoutParams = (RelativeLayout.LayoutParams) layoutParams;
//            }
            if (getBannerInThisPage(fragmentHolder) != null && !isShowNewZoneStyleRecommendFragment()) {
//                    relativeLayoutParams.removeRule(RelativeLayout.ALIGN_BOTTOM);
                // 如果是暗黑模式，需要设置推荐页底色为透明，否则氛围图会被遮挡

            } else {
//                    relativeLayoutParams.addRule(RelativeLayout.ALIGN_BOTTOM, R.id.main_tabs);
            }
            if (BaseFragmentActivity.sIsDarkMode &&
                    fragmentHolder != null && fragmentHolder.realFragment != null) {
                Fragment fragment = fragmentHolder.realFragment.get();
                if (fragment != null && fragment.getView() != null) {
                    fragment.getView().setBackgroundColor(Color.TRANSPARENT);
                }
            }
            if (relativeLayoutParams != null) {
//                mIvAtmosphereBg.setLayoutParams(layoutParams);
            }
            if (getCurTabFragment() != null && getCurTabFragment().getView() != null) {
                if (SkinManager.INSTANCE.isTabTitleColorDark()) {
                    getCurTabFragment().getView().setTag(R.id.main_foreground_color
                            , HomePageTabTheme.FOREGROUND_COLOR_BLACK);
                } else {
                    getCurTabFragment().getView().setTag(R.id.main_foreground_color
                            , HomePageTabTheme.FOREGROUND_COLOR_WHITE);
                }
            }
        } else {
            mIsTopBgShown = false;
            mIvAtmosphereBg.setVisibility(View.GONE);
            mIvAtmosphereIcon.setVisibility(View.GONE);
            if (getCurTabFragment() != null && getCurTabFragment().getView() != null) {
                if (isShowRecommendFragment()) {
                    if (mFraction > 1.0f) {
                        getCurTabFragment().getView().setTag(R.id.main_foreground_color
                                , HomePageTabTheme.FOREGROUND_COLOR_BLACK);
                    } else {
                        getCurTabFragment().getView().setTag(R.id.main_foreground_color
                                , null);
                    }
                } else {
                    getCurTabFragment().getView().setTag(R.id.main_foreground_color
                            , null);
                }
            }
        }
    }

    private void onTwoStyleImgViewInVisable() {
        if (mTwoStyleImgView != null) {
            mTwoStyleImgView.setVisibility(View.GONE);
        }
    }

    @Override
    protected View getLoadingView() {
        return null;
    }

    @Override
    protected View getNoContentView() {
        return null;
    }

    @Override
    protected View getNetworkErrorView() {
        return null;
    }

    private boolean ovalIsAddHeight;
    private int mOvalHeight;

    public void resetBottomAddHeightFlag() {
        ovalIsAddHeight = false;

        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                // 隐藏之后重新发送当前的滚动高度
                Fragment curTabFragment = getCurTabFragment();
//                if (curTabFragment instanceof RecommendFragmentNew) {
//                    RecommendFragmentNew recommendFragmentNew = (RecommendFragmentNew) curTabFragment;
//                    recommendFragmentNew.sendScrollHeightListener();
//                }
            }
        }, 10);
    }

    private void resetBottomViewHeight(int index) {
        if (mPagerAdapter == null) {
            return;
        }
        boolean isBannerOverlapStyle = false;
        Fragment f = mPagerAdapter.getFragmentAtPosition(index);
        if (f instanceof RecommendFragmentStaggered && AdManager.isUerNewSmallBanner()) {
            isBannerOverlapStyle = true;
        }
        int statusBarHeight = 0;
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
        }

        int ovalHeight;
        if (isBannerOverlapStyle) {
            ovalHeight = getResources().getDimensionPixelSize(R.dimen.main_bottom_oval_view_height_new);
        } else {
            ovalHeight = getResources().getDimensionPixelSize(R.dimen.main_bottom_oval_view_height);
        }
        ViewGroup.LayoutParams layoutParams = mBottomOvalView.getLayoutParams();
        layoutParams.height = ovalHeight + statusBarHeight;
        mBottomOvalView.setLayoutParams(layoutParams);
        mOvalHeight = ovalHeight + statusBarHeight;

//        layoutParams = mIvAtmosphereBg.getLayoutParams();
//        layoutParams.height = ovalHeight + statusBarHeight;
//        mIvAtmosphereBg.setLayoutParams(layoutParams);
    }

    public void changeBottomOvalViewHeightForPromotion(boolean isPromotionShow) {
        if (mBottomOvalView == null) {
            return;
        }
        if (isPromotionShow) {
            ViewGroup.LayoutParams layoutParams = mBottomOvalView.getLayoutParams();
            layoutParams.height = BaseUtil.dp2px(getContext(), 150);
            mBottomOvalView.setLayoutParams(layoutParams);
            mOvalHeight = layoutParams.height;
        } else {
            resetBottomViewHeight(mPager != null ? mPager.getCurrentItem() : 0);
        }
    }

    public void changeBottomOvalViewHeight(boolean addHeight) {
        if (mBottomOvalView == null) {
            return;
        }

        ovalIsAddHeight = addHeight;

        int statusBarHeight = 0;
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
        }

        if (addHeight) {
            ViewGroup.LayoutParams layoutParams = mBottomOvalView.getLayoutParams();
            int bigScreenWidth = BaseUtil.getScreenWidth(mContext) - BaseUtil.dp2px(mContext, 60);
            int bigScreenHeight = (int) (bigScreenWidth * 9 * 1.0f / 16);
            layoutParams.height =
                    BaseUtil.dp2px(getContext(), 50 + 40 + 8 + 8) + statusBarHeight + bigScreenHeight + BaseUtil.dp2px(getContext(), 30);
            mBottomOvalView.setLayoutParams(layoutParams);
            mOvalHeight = layoutParams.height;
        } else {
            resetBottomViewHeight(mPager != null ? mPager.getCurrentItem() : 0);
        }
    }

    public void listScrollHeight(int scrollHeight) {
        if (mOvalHeight == 0) {
            resetBottomViewHeight(mPager != null ? mPager.getCurrentItem() : 0);
        }

        if (mOvalHeight != 0) {
            ViewGroup.LayoutParams layoutParams = mBottomOvalView.getLayoutParams();
            layoutParams.height = mOvalHeight - scrollHeight;
            mBottomOvalView.setLayoutParams(layoutParams);
        }
    }

    public void setBottomOvalViewTranslate(boolean showShadow) {
        if (!BaseFragmentActivity.sIsDarkMode && mBottomOvalView != null) {
            mBottomOvalView.setViewTranslate(showShadow);
        }
    }

    public ShowReversePairImageView getReversePairImageView() {
        return mReversePairImageView;
    }

    @Override
    public HomePageRedDotManager getRedDotManager() {
        if (null == mRedDotManager) {
            mRedDotManager = new HomePageRedDotManager(this);
        }
        return mRedDotManager;
    }

    /**
     * 检查用户是否已经签到过
     */
    private void checkUserSign() {
        if (!mNeedRequestSignInfo) {
            if (mSignInfoModel != null && mSignInfoModel.isShow()) {
                // 展示小红点
                checkShowSignRedDot();
                trackSignShow(mSignInfoModel.isSignIn(), mSignInfoModel.getXmRequestId());
            }
        } else {
            // 网络请求获取签到状态
            MainCommonRequest.getSignInfo(new IDataCallBack<SignInfoModel>() {
                @Override
                public void onSuccess(@Nullable SignInfoModel signInfoModel) {
                    if (canUpdateUi() && signInfoModel != null) {
                        mNeedRequestSignInfo = false;
                        mSignInfoModel = signInfoModel;
                        mSignInfoModel.setXmRequestId(XmRequestIdManager.getInstance(mContext).getRequestId());
                        mSignIconUseColorFilter = true;
                        if (signInfoModel.isShow()) {
                            if (signInfoModel.isSignIn()) {
                                mIvSearchBarSign.setImageResource(R.drawable.main_ic_sign_yes_n_line_regular_24);
                            } else {
                                @DrawableRes int defaultIcon = R.drawable.main_ic_sign_no_n_line_regular_24;
                                if (!TextUtils.isEmpty(signInfoModel.getIcon())) {
                                    ImageManager.from(getContext()).displayImage(mIvSearchBarSign, signInfoModel.getIcon(),
                                            defaultIcon, defaultIcon);
                                    mIvSearchBarSign.clearColorFilter();
                                    mSignIconUseColorFilter = false;
                                } else {
                                    mIvSearchBarSign.setImageResource(defaultIcon);
                                }
                            }
                            mIvSearchBarSign.setVisibility(View.VISIBLE);
                            mIvSearchBarSign.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    if (ViewUtil.haveDialogIsShowing(getActivity())) {
                                        mIvSearchBarSign.postDelayed(this, 1000);
                                    } else {
                                        playSignIconAnim();
                                    }
                                }
                            }, 1000);

                            // 展示小红点
                            checkShowSignRedDot();
                            trackSignShow(mSignInfoModel.isSignIn(), mSignInfoModel.getXmRequestId());
                            if (mPager != null && mPagerAdapter != null) {
                                updateSearchBar(mPager.getCurrentItem());
                            }
                        } else {
                            mIvSearchBarSign.setVisibility(View.GONE);
                            hideSignRedDot();
                        }
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (code == RequestError.CODE_GOTO_LOGIN) {
                        mNeedRequestSignInfo = false;
                    }
                    CustomToast.showToast("" + message);
                }
            });
        }
    }

    private void checkShowSignRedDot() {
        if (mSignInfoModel != null && mSignInfoModel.isUnSigninRedDot() && mSignInfoModel.isShow() && !mSignInfoModel.isSignIn()) {
            long time = Calendar.getInstance().getTimeInMillis();
            long day = 24 * 3600 * 1000L;
            // x天一次
            JSONObject json = ConfigureCenter.getInstance().getJson(CConstants.Group_toc.GROUP_NAME,
                    CConstants.Group_toc.ITEM_HOME_SIGN_RED_DOT_FREQUENCY);
            if (json == null || json.optInt("intervalDay", -1) == -1) {
                // 无效配置
                hideSignRedDot();
                Logger.w(TAG, "invalid config data of signing red dot");
                return;
            }
            int intervalDay = json.optInt("intervalDay");
            long lastShowTime = getRedDotLastShownTime();
            if (time - lastShowTime < day * intervalDay) {
                // 已显示过
                Logger.d(TAG, "red dot has been shown in last " + intervalDay + " days");
            } else {
                // show
                showSignRedDot();
                saveRedDotShownTime(time);
            }
        } else {
            hideSignRedDot();
            Logger.d(TAG, "hide signing red dot by server");
        }
    }

    private long getRedDotLastShownTime() {
        long userId = UserInfoMannage.getUid();
        String lastShowTimeStr = MMKVUtil.getInstance().getString(PreferenceConstantsInHost.KEY_SIGN_RED_DOT_SHOW_DATE);
        if (StringUtil.isEmpty(lastShowTimeStr)) {
            return 0;
        }
        try {
            JSONObject json = new JSONObject(lastShowTimeStr);
            return json.optLong(String.valueOf(userId), 0);
        } catch (Exception e) {
            Logger.e(TAG, "red dot time data is wrong");
        }
        return 0;
    }

    private void saveRedDotShownTime(long time) {
        long userId = UserInfoMannage.getUid();
        String lastShowTimeStr = MMKVUtil.getInstance().getString(PreferenceConstantsInHost.KEY_SIGN_RED_DOT_SHOW_DATE);
        JSONObject json;
        if (StringUtil.isEmpty(lastShowTimeStr)) {
            json = new JSONObject();
            try {
                json.put(String.valueOf(userId), time);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else {
            try {
                json = new JSONObject(lastShowTimeStr);
            } catch (Exception e) {
                json = new JSONObject();
            }
            try {
                json.put(String.valueOf(userId), time);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        MMKVUtil.getInstance().saveString(PreferenceConstantsInHost.KEY_SIGN_RED_DOT_SHOW_DATE, json.toString());
    }

    private void showSignRedDot() {
        mIvSignRedDot.setVisibility(View.VISIBLE);
        mSignRedDotPosition.setRedDotShow(true);
        mSignRedDotPosition.notifiRedDotShow();
    }

    private void hideSignRedDot() {
        mIvSignRedDot.setVisibility(View.INVISIBLE);
        mSignRedDotPosition.setRedDotShow(false);
    }

    private boolean isSignRedDotShown() {
        return mIvSignRedDot.getVisibility() == View.VISIBLE;
    }

    /**
     * 跳转到积分中心去签到
     */
    private void gotoSign() {
        String urlStr = "iting://open?msg_type=94&bundle=rn_credit_center&reuse=true&srcChannel=home_page";
        if (mSignInfoModel != null && !TextUtils.isEmpty(mSignInfoModel.getUrl())) {
            urlStr = mSignInfoModel.getUrl();
        }
        ToolUtil.clickUrlAction(this, urlStr, null);
    }

    private void trackSignShow(boolean hasSign, String xmRequestId) {
        if (mFreeListenIconManager != null && !mFreeListenIconManager.isOutSignShow()) {
            return;
        }
        // 新首页-签到入口  控件曝光
        String status = hasSign ? "已签到" : "未签到";
        String iconType = "常规";
        if (mSignInfoModel != null && !TextUtils.isEmpty(mSignInfoModel.getIcon())) {
            iconType = "红包";
        }
        new XMTraceApi.Trace()
                .setMetaId(48417)
                .setServiceId("slipPage")
                .put("currPage", "newHomePage")
                .put("status", status)
                .put("unReadNum", String.valueOf(isSignRedDotShown() ? 1 : 0))
                .put("iconType", iconType)
                .put(XmRequestIdManager.CONT_ID, "1")
                .put(XmRequestIdManager.CONT_TYPE, "homePageSign")
                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                .put("style", "页面")
                .createTrace();
    }

    /**
     * 签到按钮动效
     */
    private void playSignIconAnim() {
        String jsonString = ConfigureCenter.getInstance().getJsonString("toc", "home_sign_animation", "");
        //Logger.e("tag21","jsonString = "+jsonString);
        if (TextUtils.isEmpty(jsonString)) {
            return;
        }
        SignGuideAnimModel signGuideAnimModel = GsonUtils.parseJson(jsonString, SignGuideAnimModel.class);
        long lastCount = MMKVUtil.getInstance().getLong(PreferenceConstantsInHost.KEY_SIGN_EFFECT_COUNT, 0);
        if (lastCount >= signGuideAnimModel.getFrequency()) {
            return;
        }
        long lastTime = MMKVUtil.getInstance().getLong(PreferenceConstantsInHost.KEY_SIGN_EFFECT_TIME, -1);
        if (System.currentTimeMillis() - lastTime < signGuideAnimModel.getIntervalDay() * 24 * 3600 * 1000L) {
            return;
        }
        ImageManager.from(mContext).displayImage(mIvSearchBarSignEffect, signGuideAnimModel.getGift(), -1);
        MMKVUtil.getInstance().saveLong(PreferenceConstantsInHost.KEY_SIGN_EFFECT_COUNT, lastCount + 1);
        MMKVUtil.getInstance().saveLong(PreferenceConstantsInHost.KEY_SIGN_EFFECT_TIME, System.currentTimeMillis());
        final int[] animationCount = {0};
        ObjectAnimator singRotationYAnimator = ObjectAnimator.ofFloat(mIvSearchBarSign, "rotationY", 0f, 90f).setDuration(500);
        ObjectAnimator singRotationYBackAnimator = ObjectAnimator.ofFloat(mIvSearchBarSign, "rotationY", -90f, 0f).setDuration(500);
        ObjectAnimator signRotationYAnimatorChange = ObjectAnimator.ofFloat(mIvSearchBarSignEffect, "rotationY", -90f, 0f).setDuration(500);
        ObjectAnimator signRotation = ObjectAnimator.ofFloat(mIvSearchBarSignEffect, "rotation", 0f, -15f, 0f, 15f, 0f).setDuration(500);
        ObjectAnimator effectRotationYAnimator = ObjectAnimator.ofFloat(mIvSearchBarSignEffect, "rotationY", 0f, 90f).setDuration(500);
        singRotationYAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                mIvSearchBarSignEffect.setVisibility(View.VISIBLE);
                signRotationYAnimatorChange.start();
                mIvSearchBarSign.setVisibility(View.INVISIBLE);
                //mIvSearchBarSign.setRotationY(0);
            }

            @Override
            public void onAnimationCancel(Animator animator) {
                resetSignIcon();
            }

            @Override
            public void onAnimationRepeat(Animator animator) {
            }
        });
        signRotationYAnimatorChange.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                signRotation.start();
            }

            @Override
            public void onAnimationCancel(Animator animator) {
                resetSignIcon();
            }

            @Override
            public void onAnimationRepeat(Animator animator) {
            }
        });
        signRotation.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                effectRotationYAnimator.start();
            }

            @Override
            public void onAnimationCancel(Animator animator) {
                resetSignIcon();
            }

            @Override
            public void onAnimationRepeat(Animator animator) {
            }
        });
        effectRotationYAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mIvSearchBarSignEffect.setVisibility(View.GONE);
                mIvSearchBarSignEffect.setRotationY(0);
                mIvSearchBarSign.setVisibility(View.VISIBLE);
                singRotationYBackAnimator.start();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                resetSignIcon();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        singRotationYBackAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                animationCount[0]++;
                mIvSearchBarSign.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (animationCount[0] < 3) {
                            singRotationYAnimator.start();
                        }
                    }
                }, 1500);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                resetSignIcon();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        singRotationYAnimator.setStartDelay(1000);
        singRotationYAnimator.start();
    }

    private void resetSignIcon() {
        mIvSearchBarSign.setRotationY(0);
        mIvSearchBarSign.setVisibility(View.VISIBLE);
        mIvSearchBarSignEffect.setVisibility(View.GONE);
    }

    private final BroadcastReceiver mChatXmlyChangeRoleReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
                mHasShowChildXmlyAnimation = false;
                showChildChatXmlyBubble(false);
            } else {
                showOrHideSearchRecognizeBtn(true);
                mLottieChatXmlyBubbleRobot.setVisibility(View.GONE);
                mTvChatXmlyBubble.setVisibility(View.GONE);
                if (mSearchHotWordSwitchManager != null) {
                    mSearchHotWordSwitchManager.showTextSwitcher();
                }
            }
        }
    };


    private ChildXmlyTipChangeListener childXmlyChangeListener = new ChildXmlyTipChangeListener() {
        @Override
        public void onStyleChange() {
            postOnUiThread(() -> {
                if (isRealVisable()) {
                    mHasShowChildXmlyAnimation = false;
                    showChildChatXmlyBubble(true);
                } else {
                    agentStyleChange = true;
                }
            });
        }

        @Override
        public void onRoleTypeChange() {

        }
    };

    private void showChildChatXmlyBubble(Boolean forceShowStart) {
        if (!canUpdateUi()) {
            return;
        }
        if (ChildXmlyTipManager.INSTANCE.getAgentConfig() == null) {
            setLottieIconDefault();
            return;
        }
        ChildXmlyTipManager.INSTANCE.childChatXmlyExplore("newHomePage");
        if (mLottieChatXmlyBubbleRobot == null || mTvChatXmlyBubble == null) {
            return;
        }
        if (mHasShowChildXmlyAnimation) {
            return;
        }
        mHasShowChildXmlyAnimation = true;
        // 波波独立样式是否生效
        if (mTvChatXmlyBubble.getLayoutParams() instanceof ConstraintLayout.LayoutParams) {
            ConstraintLayout.LayoutParams lottieParams = (ConstraintLayout.LayoutParams) mLottieChatXmlyBubbleRobot.getLayoutParams();
            lottieParams.width = ChildXmlyTipManager.INSTANCE.getIconSize().getFirst();
            lottieParams.height = ChildXmlyTipManager.INSTANCE.getIconSize().getSecond();
            lottieParams.setMarginEnd(BaseUtil.dp2px(mContext, 0));
            lottieParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
            mLottieChatXmlyBubbleRobot.setLayoutParams(lottieParams);

            ConstraintLayout.LayoutParams tvParams = (ConstraintLayout.LayoutParams) mTvChatXmlyBubble.getLayoutParams();
            tvParams.endToEnd = ConstraintLayout.LayoutParams.UNSET;
            tvParams.endToStart = R.id.main_lottie_chatxmly_bubble_robot;
            tvParams.setMarginEnd(0);
            mTvChatXmlyBubble.setLayoutParams(tvParams);
        }
        mTvChatXmlyBubble.setPadding(BaseUtil.dp2px(mContext, 12), 0,
                ChildXmlyTipManager.INSTANCE.isXiaoYaType() ? 0 : BaseUtil.dp2px(mContext, 8), 0);
        if (ChildXmlyTipManager.INSTANCE.isXiaoYaType()) {
            mTvChatXmlyBubble.setTextColor(getColorSafe(R.color.main_color_2c2c3c_ffffff));
        } else {
            mTvChatXmlyBubble.setTextColor(getColorSafe(R.color.main_color_131313_ffffff));
        }
        mTvChatXmlyBubble.setBackground(null);
        mTvChatXmlyBubble.setScaleX(1f);
        mTvChatXmlyBubble.setFadingEdgeLength(BaseUtil.dp2px(mContext, 30));
        mTvChatXmlyBubble.setHorizontalFadingEdgeEnabled(true);
        mTvChatXmlyBubble.setEllipsize(TextUtils.TruncateAt.END);
        mTvChatXmlyBubble.setGravity(Gravity.CENTER);
        mLottieChatXmlyBubbleRobot.setRepeatCount(0);
        mLottieChatXmlyBubbleRobot.removeAllLottieOnCompositionLoadedListener();
        mLottieChatXmlyBubbleRobot.removeAllAnimatorListeners();
        String bubbleTextStr = AiAgentTipManager.INSTANCE.getProcessSug();
        mPoppyStartAnimator = ValueAnimator.ofInt(0, bubbleTextStr.length());
        mPoppyStartAnimator.setInterpolator(new LinearInterpolator());
        mPoppyStartAnimator.setDuration(400);
        mPoppyStartAnimator.addUpdateListener(animation -> {
            int value = (int) animation.getAnimatedValue();
            Logger.d(TAG, "poppyAnimator value = " + value);
            if (mTvChatXmlyBubble != null) {
                mTvChatXmlyBubble.setText(bubbleTextStr.substring(0, value));
            }
        });
        mPoppyEndAnimator = ValueAnimator.ofInt(bubbleTextStr.length(), 0);
        mPoppyEndAnimator.setInterpolator(new LinearInterpolator());
        mPoppyEndAnimator.addUpdateListener(animation -> {
            int value = (int) animation.getAnimatedValue();
            if (mTvChatXmlyBubble != null) {
                mTvChatXmlyBubble.setText(bubbleTextStr.substring(0, value));
            }
        });
        Logger.d("AiAgentTipManager", "poppyAnimator startDelay = " + ChildXmlyTipManager.INSTANCE.getAgentSugTime());
        mPoppyEndAnimator.setStartDelay(ChildXmlyTipManager.INSTANCE.getAgentSugTime() * 1000L + 400);
        mPoppyEndAnimator.setDuration(400);
        showOrHideSearchRecognizeBtn(false);
        if (forceShowStart || AiAgentTipManager.INSTANCE.canShowSug()) {
            // 添加监听并播放引导语动画
            mLottieChatXmlyBubbleRobot.addAnimatorListener(new AnimatorListenerAdapter() {

                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    if (!canUpdateUi()) {
                        return;
                    }
                    AiAgentTipManager.INSTANCE.markSugShow();
                    tryShowLoopAnim();
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    super.onAnimationCancel(animation);
                    if (!canUpdateUi()) {
                        return;
                    }
                    AiAgentTipManager.INSTANCE.markSugShow();
                    setLottieIconDefault();
                }
            });
            String lottieGuideUrl = ChildXmlyTipManager.INSTANCE.getBubbleGuideLottieUrl();
            if (lottieGuideUrl.isEmpty()) {
                setLottieIconDefault();
            } else {
                LottieCompositionFactory.fromUrl(mContext, lottieGuideUrl).addListener(composition -> {
                    if (!canUpdateUi()) {
                        return;
                    }
                    if (composition != null) {
                        mLottieChatXmlyBubbleRobot.setComposition(composition);
                        mLottieChatXmlyBubbleRobot.playAnimation();
                        mLottieChatXmlyBubbleRobot.setVisibility(View.VISIBLE);
                    } else {
                        tryShowLoopAnim();
                    }
                });
            }

            // 添加监听并播放文本动画
            mPoppyEndAnimator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    super.onAnimationStart(animation);
                    if (!canUpdateUi()) {
                        return;
                    }
                    if (mTvChatXmlyBubble.getWidth() > 0) {
                        mTvChatXmlyBubble.setPivotX(mTvChatXmlyBubble.getWidth());
                    }
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    if (!canUpdateUi()) {
                        return;
                    }
                    mTvChatXmlyBubble.setVisibility(View.GONE);
//                    tryShowLoopAnim();
                    if (mSearchHotWordSwitchManager != null) {
                        mSearchHotWordSwitchManager.showTextSwitcher();
                    }
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    super.onAnimationCancel(animation);
                    if (!canUpdateUi()) {
                        return;
                    }
//                    tryShowLoopAnim();
                    mTvChatXmlyBubble.setVisibility(View.GONE);
                    if (mSearchHotWordSwitchManager != null) {
                        mSearchHotWordSwitchManager.showTextSwitcher();
                    }
                }
            });
            mTvChatXmlyBubble.setVisibility(View.VISIBLE);
            ChildXmlyTipManager.INSTANCE.eTraceSugShow();
            mTvChatXmlyBubble.post(() -> {
                if (!canUpdateUi()) {
                    return;
                }
                if (mTvChatXmlyBubble.getWidth() > 0) {
                    mTvChatXmlyBubble.setPivotX(mTvChatXmlyBubble.getWidth());
                }
                mPoppyStartAnimator.start();
                mPoppyEndAnimator.start();
            });
            if (mSearchHotWordSwitchManager != null) {
                mSearchHotWordSwitchManager.hideTextSwitcher();
            }
        } else  {
            tryShowLoopAnim();
        }
    }

    /**
     * 设置默认图/兜底展示
     * */
    private void setLottieIconDefault() {
        int type = ChildXmlyTipManager.INSTANCE.showTipType();
        int resId = type == ChildXmlyTipManagerKt.CHAT_XIAOYA ? R.drawable.host_home_chatxmly_xiaoya_search_icon : R.drawable.host_home_chatxmly_search_icon_new;
        mLottieChatXmlyBubbleRobot.setImageResource(resId);
        mLottieChatXmlyBubbleRobot.setVisibility(View.VISIBLE);
    }

    /**
     * 循环动画逻辑
     * */
    private void tryShowLoopAnim() {
        String lottieLoopUrl = ChildXmlyTipManager.INSTANCE.getBubbleLoopLottieUrl();
        if (lottieLoopUrl.isEmpty()) {
            setLottieIconDefault();
            return;
        }
        mLottieChatXmlyBubbleRobot.removeAllAnimatorListeners();
        LottieCompositionFactory.fromUrl(mContext, lottieLoopUrl).addListener(composition -> {
            if (!canUpdateUi()) {
                return;
            }
            if (composition != null) {
                // 设计要求只做一次动画
                mLottieChatXmlyBubbleRobot.setRepeatCount(0);
                mLottieChatXmlyBubbleRobot.setComposition(composition);
                mLottieChatXmlyBubbleRobot.playAnimation();
                mLottieChatXmlyBubbleRobot.setVisibility(View.VISIBLE);
            } else {
                setLottieIconDefault();
            }
        });
    }

    private void showChatXmlyBubble(ChatXmlyTips.ChatXmlyTipsBean chatXmlyTipsBean) {
        if (!isRealVisable()) {
            return;
        }
        if (!canUpdateUi()) {
            return;
        }
        if (ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
            return;
        }
        String bubbleText = chatXmlyTipsBean.getBubble();
        if (TextUtils.isEmpty(bubbleText)) {
            return;
        }
        if (mLottieChatXmlyBubbleRobot == null) {
            return;
        }
        if (mTvChatXmlyBubble == null) {
            return;
        }
        boolean userNewVersion = ChatXmlyPopupManager.INSTANCE.useChatXmlyVoiceAssistantNew2();
        if (userNewVersion) {
            mTvChatXmlyBubble.setTextColor(getColorSafe(R.color.main_color_2a3e8d_a0d8ff));
            mTvChatXmlyBubble.setBackgroundResource(R.drawable.main_bg_rect_ddefff_d6e5ff_131313_corner_100);
        }
        mTipsBeanBringIntoChatXmlyPopup = chatXmlyTipsBean;
        ChatXmlyPopupManager.INSTANCE.traceBubbleShow("首页", bubbleText);
        mTvChatXmlyBubble.setScaleX(0f);
        // 有专辑标题，在中间显示省略号，否则在结尾显示省略号
        if (!TextUtils.isEmpty(chatXmlyTipsBean.getContentTitle())) {
            mTvChatXmlyBubble.setEllipsize(TextUtils.TruncateAt.MIDDLE);
        } else {
            mTvChatXmlyBubble.setEllipsize(TextUtils.TruncateAt.END);
        }
        mTvChatXmlyBubble.setText(bubbleText);
        String showAnimPath = BaseFragmentActivity2.sIsDarkMode ? "lottie/chatxmly/search_bubble_dark.json" : "lottie/chatxmly/search_bubble.json";
        if (userNewVersion) {
            showAnimPath = BaseFragmentActivity2.sIsDarkMode ? "lottie/chatxmly/search_bubble_dark_new.json" : "lottie/chatxmly/search_bubble_new.json";
        }
        mLottieChatXmlyBubbleRobot.removeAllLottieOnCompositionLoadedListener();
        mLottieChatXmlyBubbleRobot.addLottieOnCompositionLoadedListener(composition -> {
            mLottieChatXmlyBubbleRobot.setVisibility(View.VISIBLE);
            mLottieChatXmlyBubbleRobot.playAnimation();
            Logger.i(TAG, "mLottieChatXmlyBubbleRobot loaded");
        });
        mLottieChatXmlyBubbleRobot.removeAllAnimatorListeners();
        mLottieChatXmlyBubbleRobot.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                showOrHideSearchRecognizeBtn(false);
                mTvChatXmlyBubble.setVisibility(View.VISIBLE);
                ChildXmlyTipManager.INSTANCE.eTraceSugShow();
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                postOnUiThreadDelayed(mHideChatXmlyBubbleWithAnimTask, 5000);
                if (mTvChatXmlyBubble.getWidth() > 0) {
                    mTvChatXmlyBubble.setPivotX(mTvChatXmlyBubble.getWidth());
                }
                if (mChatXmlyBubbleShowAnimator != null && mLottieChatXmlyBubbleRobot.getVisibility() == View.VISIBLE) {
                    mChatXmlyBubbleShowAnimator.start();
                    if (mSearchHotWordSwitchManager != null) {
                        mSearchHotWordSwitchManager.hideTextSwitcher();
                    }
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mLottieChatXmlyBubbleRobot.setAnimation(showAnimPath);
        if (mChatXmlyBubbleShowAnimator != null) {
            mChatXmlyBubbleShowAnimator.cancel();
        } else {
            mChatXmlyBubbleShowAnimator = ObjectAnimator.ofFloat(mTvChatXmlyBubble, "scaleX", 0f, 1f);
            mChatXmlyBubbleShowAnimator.setDuration(300);
        }
    }

    private void hideChatXmlyBubbleWithAnim() {
        if (mLottieChatXmlyBubbleRobot == null) {
            return;
        }
        if (mTvChatXmlyBubble == null) {
            return;
        }
        if (mLottieChatXmlyBubbleRobot.getVisibility() != View.VISIBLE) {
            return;
        }
        if (mTvChatXmlyBubble.getWidth() > 0) {
            mTvChatXmlyBubble.setPivotX(mTvChatXmlyBubble.getWidth());
        }
        boolean userNewVersion = ChatXmlyPopupManager.INSTANCE.useChatXmlyVoiceAssistantNew2();
        String hideAnimPath = BaseFragmentActivity2.sIsDarkMode ? "lottie/chatxmly/search_bubble_revert_dark.json" : "lottie/chatxmly/search_bubble_revert.json";
        if (userNewVersion) {
            hideAnimPath = BaseFragmentActivity2.sIsDarkMode ? "lottie/chatxmly/search_bubble_revert_dark_new.json" : "lottie/chatxmly/search_bubble_revert_new.json";
        }

        if (mChatXmlyBubbleHideAnimator != null) {
            mChatXmlyBubbleHideAnimator.removeAllListeners();
            mChatXmlyBubbleHideAnimator.cancel();
        } else {
            mChatXmlyBubbleHideAnimator = ObjectAnimator.ofFloat(mTvChatXmlyBubble, "scaleX", 1f, 0f);
            mChatXmlyBubbleHideAnimator.setDuration(300);
        }
        mChatXmlyBubbleHideAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
            }
        });
        mLottieChatXmlyBubbleRobot.removeAllLottieOnCompositionLoadedListener();
        mLottieChatXmlyBubbleRobot.addLottieOnCompositionLoadedListener(composition -> {
            // 等lottie动画加载完再整体开始动画
            mLottieChatXmlyBubbleRobot.playAnimation();
            mChatXmlyBubbleHideAnimator.start();
        });
        mLottieChatXmlyBubbleRobot.removeAllAnimatorListeners();
        mLottieChatXmlyBubbleRobot.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                hideChatXmlyBubble();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mLottieChatXmlyBubbleRobot.setAnimation(hideAnimPath);
    }

    private void hideChatXmlyBubble() {
        if (ChildXmlyTipManager.INSTANCE.shouldShowTip()) {
            return;
        }
        if (mLottieChatXmlyBubbleRobot == null) {
            return;
        }
        if (mTvChatXmlyBubble == null) {
            return;
        }
        mLottieChatXmlyBubbleRobot.removeAllLottieOnCompositionLoadedListener();
        if (mLottieChatXmlyBubbleRobot.getVisibility() != View.VISIBLE) {
            return;
        }
        if (mSearchHotWordSwitchManager != null) {
            mSearchHotWordSwitchManager.showTextSwitcher();
        }
        if (mChatXmlyBubbleHideAnimator != null) {
            mChatXmlyBubbleHideAnimator.removeAllListeners();
            mChatXmlyBubbleHideAnimator.cancel();
        }
        if (mChatXmlyBubbleShowAnimator != null) {
            mChatXmlyBubbleShowAnimator.removeAllListeners();
            mChatXmlyBubbleShowAnimator.cancel();
        }
        removeCallbacks(mHideChatXmlyBubbleWithAnimTask);
        mLottieChatXmlyBubbleRobot.removeAllAnimatorListeners();
        mLottieChatXmlyBubbleRobot.setVisibility(View.GONE);
        mLottieChatXmlyBubbleRobot.cancelAnimation();
        if (mChatXmlyPopupWindow == null || !mChatXmlyPopupWindow.isShowingNow()) {
            showOrHideSearchRecognizeBtn(true);
        }
        mTvChatXmlyBubble.setText("");
        mTvChatXmlyBubble.setScaleX(0f);
        mTvChatXmlyBubble.setVisibility(View.GONE);
        mTipsBeanBringIntoChatXmlyPopup = null;
    }

    private final Runnable mHideChatXmlyBubbleWithAnimTask = this::hideChatXmlyBubbleWithAnim;

    @Override
    public int getContentTopHeight() {
        if (mPager != null) {
            int[] location = new int[2];
            mPager.getLocationOnScreen(location);
            return location[1];
        }
        return 0;
    }

}