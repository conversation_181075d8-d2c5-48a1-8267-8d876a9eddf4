package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.kt.ifNullOrBlank
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.IClickExportListener
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew.RECOMMEND_ITEM_818_KOL_LIVE_LIST
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.view.live.LiveLotteryTagView
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 新首页 - 818 重磅大咖直播卡片
 *
 * Created by zoey on 2024/04/09.
 * <AUTHOR>
 * @email <EMAIL>
 */
class Recommend818KolLiveListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<Recommend818KolLiveListAdapterProviderStaggered.LiveListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<Recommend818KolLiveListAdapterProviderStaggered.LiveListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<Recommend818KolLiveListAdapterProviderStaggered.LiveListCardViewHolder, RecommendItemNew> {

    private var oldScrollState = RecyclerView.SCROLL_STATE_IDLE

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_818_live_list_card, parent, false
        )
    }

    override fun createViewHolder(convertView: View?): LiveListCardViewHolder? {
        return convertView?.let { LiveListCardViewHolder(it) }
    }

    override fun bindViewHolder(
        holder: LiveListCardViewHolder?, position: Int, data: RecommendItemNew?, convertView: View?
    ) {
        if (holder == null || data == null) return

        if (data.xmRequestId != null && (data.xmRequestId + position) == holder.uniqueId) {
            return
        } else {
            holder.uniqueId = data.xmRequestId + position
        }

        val recommendCommonItem = data.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) return
        if (recommendCommonItem.subElements.isNullOrEmpty()) return

        if (recommendCommonItem.subElements!!.size < MmkvCommonUtil.getInstance(ToolUtil.getCtx())
                .getInt(CConstants.Group_toc.ITEM_HOME_818_KOL_LIVE_CARD_MIN_COUNT, 3)) {
            RecommendFragmentPageErrorManager.uploadDataError("818直播数量不足", null)
        }
        if (recommendCommonItem.landingPage.isNullOrBlank()) {
            RecommendFragmentPageErrorManager.uploadDataError("818直播iting为空", null)
        }
        if (!TextUtils.isEmpty(recommendCommonItem.subTitle)) {
            holder.tvLiveListTittle.text = recommendCommonItem.title
            holder.tvLiveListTittle.maxLines = 1
            holder.tvLiveListSubTittle.text = recommendCommonItem.subTitle
            ViewStatusUtil.setVisible(View.VISIBLE, holder.tvLiveListSubTittle)
        } else {
            holder.tvLiveListTittle.text = recommendCommonItem.title
            holder.tvLiveListTittle.maxLines = 2
            ViewStatusUtil.setVisible(View.GONE, holder.tvLiveListSubTittle)
        }

        val performMoreClick: (action: String?, targetView: View?) -> Unit = { action, targetView ->
            // 新首页-首页大卡模块  点击事件
            val trace = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem?.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            trace.createTrace()

            // 新首页-直播卡片-更多  点击事件
            val trace1 = XMTraceApi.Trace()
                .click(62467) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId ?: "") // 客户端传，去重用
                .put("action", action)
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem?.ubtV2,
                (position + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            if (data.isLocalCache) {
                trace1.isLocalCache
            }
            trace1.createTrace()

            ToolUtil.clickUrlAction(
                fragment, recommendCommonItem.landingPage ?: "", targetView ?: holder.tvGoForMore
            )
        }
        holder.tvLiveListTittle.setOnClickListener {
            if (recommendCommonItem.landingPage.isNullOrBlank()) return@setOnClickListener
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            performMoreClick("click", it)
        }
        holder.tvLiveListSubTittle.setOnClickListener {
            if (recommendCommonItem.landingPage.isNullOrBlank()) return@setOnClickListener
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            performMoreClick("click", it)
        }

        holder.tvGoForMore.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            val isShowMore = !recommendCommonItem.landingPage.isNullOrBlank()
            var level1DisLikeTitle = recommendCommonItem.extraInfo?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick("click", null)
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(position)
                }
            }
            val moreFuncBuild = MoreFuncBuild.createLiveListMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener
            )

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (position + 1).toString())
                put("xmRequestId", data.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.extraInfo?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }

        val liveListAdapter = LiveListLiveItemAdapter(
            dataAction, fragment, recommendCommonItem, data,
            recommendCommonItem.subElements ?: emptyList(), position,
            holder.rcvLiveList,
            recommendCommonItem.landingPage.isNullOrEmpty().not()
        ).also {
            it.setRelaseJumpActivityListener {
                performMoreClick("slide", holder.rcvLiveList)
            }
        }
        holder.rcvLiveList.adapter = liveListAdapter
        holder.rcvLiveList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == oldScrollState) return
                oldScrollState = newState
                if (oldScrollState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(data, position, holder)
                    (recyclerView.layoutManager as? LinearLayoutManager)?.also {
                        data.firstVisiblePosition = it.findFirstVisibleItemPosition()
                        data.firstVisibleOffset = it.findViewByPosition(
                            data.firstVisiblePosition
                        )?.left ?: 0
                    }
                }
            }
        })
        (holder.rcvLiveList.layoutManager as? LinearLayoutManager)?.also {
            if (data.firstVisiblePosition <= 0 && data.firstVisibleOffset <= 0) {
                return@also
            }
            if (data.firstVisiblePosition != it.findFirstVisibleItemPosition() ||
                data.firstVisibleOffset != (it.findViewByPosition(data.firstVisiblePosition)?.left ?: 0)) {
                holder.rcvLiveList.scrollToPosition(data.firstVisiblePosition)
                holder.rcvLiveList.post {
                    // 二次滚动 offset 需要加上 Recyclerview 的左侧 padding 值
                    holder.rcvLiveList.scrollBy(-data.firstVisibleOffset + 16.dp, 0)
                }
            }
        }
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?, position: Int, holder: LiveListCardViewHolder?
    ) {
        if (data == null || holder == null) return

        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) return

        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }
                for (i in 0 until holder.rcvLiveList.childCount) {
                    val view = holder.rcvLiveList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        traceItemShow(position, data, index, subElement, view, recommendCommonItem)
                    }
                }
            }
        }
    }

    override fun onConfigurationChanged(holder: LiveListCardViewHolder?) {}

    class LiveListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val tvLiveListTittle: TextView = convertView.findViewById(R.id.main_tv_live_list_tittle)
        val tvLiveListSubTittle: TextView = convertView.findViewById(R.id.main_tv_live_list_sub_title)
        val rcvLiveList: RecyclerView = convertView.findViewById(R.id.main_rcv_live_list)
        val tvGoForMore: View = convertView.findViewById(R.id.main_tv_more)

        var uniqueId = ""
    }

    class LiveListLiveItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        private val fragment: BaseFragment2,
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        list: List<CommonSubElement>,
        var parentPosition: Int,
        val recyclerView: RecyclerView,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 直播列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            mEnableMoreItem = enableJumpMore
            commonSubElementList.addAll(list)
        }

        class LiveItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val itemViewRoot: ConstraintLayout? = view.findViewById(R.id.main_csl_item_root_view)
            val itemCoverView: RoundImageView = view.findViewById(R.id.main_iv_item_cover)
            val itemLivingTagView: View = view.findViewById(R.id.main_ll_living_tag)
            val itemPlaying: XmLottieAnimationView = view.findViewById(R.id.main_lottie_play_status)
            val itemLivingTvView: TextView = view.findViewById(R.id.main_tv_living_label)
            val itemNormalTagView: TextView = view.findViewById(R.id.main_tv_normal_tag)
            val itemTitle: TextView = view.findViewById(R.id.main_tv_title)
            val itemLotteryTag: LiveLotteryTagView = view.findViewById(R.id.main_tag_lottery_view)
            val itemNickname: TextView = view.findViewById(R.id.main_tv_nickname)
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): RecyclerView.ViewHolder {
            return LiveItemViewHolder(
                ViewPool.getInstance().getView(
                    HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                    R.layout.main_item_recommend_818_live_list_item,
                    parent,
                    false,
                    RECOMMEND_ITEM_818_KOL_LIVE_LIST
                )
            )
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        override fun getItemCount(): Int {
            return commonSubElementList.size.let {
                if (enableJumpMore) it + 1 else it
            }
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is LiveItemViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                moreRootView?.setPadding(10.dp, 0, 0, 0)
                horizontalView?.updateLayoutParams {
                    width = 37.dp
                    height = 162.dp
                }
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun onBindViewHolderInner(holder: LiveItemViewHolder, position: Int) {
            val element = commonSubElementList.getOrNull(position) ?: return

            holder.itemView.setTag(R.id.main_id_item_data, element)
            holder.itemView.setTag(R.id.main_id_data_index, position)

            val titleHeight = holder.itemTitle.lineHeight * 2
            val nameHeight = holder.itemNickname.lineHeight
            val fixedHeight = 162.dp + 6.dp + titleHeight + 3.dp + nameHeight
            holder.itemViewRoot?.minHeight = fixedHeight

            val onCardClick = object : IClickExportListener {
                override fun onClick(isReport: Boolean, view: View?) {
                    if (element.landingPage.isNullOrBlank()) {
                        return
                    }

                    if (isReport) {
                        // 新首页-首页大卡模块  点击事件
                        val bigBrace = XMTraceApi.Trace()
                            .click(62176) // 用户点击时上报
                            .put("currPage", "newHomePage")
                            .put("modulePosition", (parentPosition + 1).toString()) // 客户端传
                            .put("xmRequestId", recommendItemNew?.xmRequestId ?: "") // 客户端传
                        SpmTraceUtil.addSpmTraceInfo(
                            bigBrace,
                            moduleItem?.ubtV2,
                            (parentPosition + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(bigBrace, (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2)
                        if (recommendItemNew?.isLocalCache == true) {
                            bigBrace.isLocalCache
                        }
                        bigBrace.createTrace()

                        // 新首页-直播间卡片  点击事件
                        val trace = XMTraceApi.Trace()
                            .click(62465) // 用户点击时上报
                            .put("currPage", "newHomePage")
                            .put("modulePosition", (parentPosition + 1).toString()) // 客户端传
                            .put("xmRequestId", recommendItemNew?.xmRequestId ?: "") // 客户端传，去重用
                            .put("positionNew", (position + 1).toString()) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                            .put("rec_src", moduleItem.ubt?.recSrc ?: "") // 服务端传。
                            .put("rec_track", moduleItem.ubt?.recTrack ?: "") // 服务端传。
                            .put("ubtTraceId", moduleItem.ubt?.traceId ?: "") // 服务端传。
                            .put("contentId", (element.id ?: 0).toString()) // 客户端传。去重使用
                            .put("contentType", element.bizType ?: "") // 客户端传。去重使用
                            .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 1-100 曝光面积占比，客户端传

                        LiveLotteryTagView.TagType.convert(element.extraInfo?.lotteryType)?.also {
                            trace.put("rewardName", it.desc) // 奖励名称
                        }
                        SpmTraceUtil.addSpmTraceInfo(
                            trace,
                            moduleItem.ubtV2,
                            (parentPosition + 1).toString(),
                            contentTitle = element.title,
                            contentPosition = (position + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, element.ubtV2)
                        if (recommendItemNew?.isLocalCache == true) {
                            trace.isLocalCache
                        }
                        trace.createTrace()
                    }

                    ToolUtil.clickUrlAction(fragment, element.landingPage!!, view)
                }
            }

            val onCardLongClick = {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (parentPosition + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                if (!moduleItem.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(moduleItem.ubtV2)
                }
                if (!element.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(element.ubtV2)
                }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = element.bizType ?: ""
                traceMap["contentId"] = element.refId?.toString() ?: ""
                // 负反馈接口请求传参
                requestMap[HttpParamsConstants.PARAM_LIVE_ID] = element.id?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] = element.anchor?.uid.toString()
                requestMap["card_contentType"] = moduleItem.contentType ?: ""
                requestMap["card_bizType"] = moduleItem.bizType ?: ""
                requestMap["card_id"] = moduleItem.id?.toString() ?: ""

                val disLikeLeve2Build = DisLikeLeve2Build()
                disLikeLeve2Build.isFromAd = false
                disLikeLeve2Build.anchorName = element.anchor?.nickName
                disLikeLeve2Build.requestMap = requestMap
                disLikeLeve2Build.traceMap = traceMap
                disLikeLeve2Build.onFeedBackListener = object :
                    NewXmFeedBackPopDialog.IOnFeedBackListener() {
                    override fun onDialogShow(showSuccess: Boolean) {
                    }

                    override fun onFeedBack(list: List<XmFeedInnerModel>) {
                        val sourceType = recommendItemNew?.itemType ?: return
                        MainCommonRequest.getSingleLiveListItem(sourceType, moduleItem, object :
                            IDataCallBack<CommonSubElement> {
                            override fun onSuccess(subElement: CommonSubElement?) {
                                if (subElement == null) {
                                    removeItem(position)
                                    return
                                }
                                commonSubElementList[position] = subElement
                                notifyItemChanged(position)
                                traceItemShow(
                                    parentPosition, recommendItemNew, position, subElement, holder.itemView, moduleItem
                                )
                            }

                            override fun onError(code: Int, message: String?) {
                                removeItem(position)
                            }
                        })
                    }

                    private fun removeItem(position: Int) {
                        commonSubElementList.removeAt(position)
                        notifyItemRemoved(position)
                        notifyItemRangeChanged(position, commonSubElementList.size - position)
                    }
                }

                val moreFunLister = object : IMoreFuncListener() {
                    override fun onTopModuleClick() {
                        onCardClick.onClick(false)
                    }
                }

                val build: MoreFuncBuild = MoreFuncBuild.createLiveLongClickModel(
                    fragment, element.refId, element.bizType, moreFunLister, true, disLikeLeve2Build
                )
                build.trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", element.bizType ?: "")
                    put("contentId", element.refId?.toString() ?: "")
                    put("modulePosition",(parentPosition + 1).toString())
                    put("positionNew",(position + 1).toString())
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    element.ubtV2?.let { it1 -> putAll(it1) }
                }

                XmMoreFuncManager.checkShowMorePage(build)
            }

            // 点击跳转
            holder.itemView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
                onCardClick.onClick(true, it)
            }

            // 长按负反馈
            holder.itemView.setOnLongClickListener {
                onCardLongClick.invoke()
                true
            }

            ImageManager.from(
                BaseApplication.getMyApplicationContext()
            ).displayImageNotIncludeDownloadCacheSizeInDp(
                holder.itemCoverView, element.cover, R.drawable.main_placeholder_818_live_cover,
                119, 162
            )

            if (element.extraInfo?.status == 9) {
                // 直播中
                holder.itemLivingTagView.visibility = View.VISIBLE
                holder.itemPlaying.playAnimation()
                holder.itemLivingTvView.text = element.extraInfo.statusLabel.ifNullOrBlank {
                    holder.itemView.context.getString(R.string.main_avatar_living)
                }
                holder.itemNormalTagView.visibility = View.GONE
            } else {
                holder.itemLivingTagView.visibility = View.GONE
                holder.itemNormalTagView.text = element.extraInfo?.statusLabel
                holder.itemNormalTagView.visibility =
                    if (element.extraInfo?.statusLabel.isNullOrBlank()) {
                        View.GONE
                    } else {
                        View.VISIBLE
                    }
            }
            // 标题和昵称
            holder.itemTitle.text = element.title
            val tagType: LiveLotteryTagView.TagType? = LiveLotteryTagView.TagType.convert(
                element.extraInfo?.lotteryType
            )
            if (tagType == null) {
                holder.itemLotteryTag.visibility = View.GONE
            } else {
                holder.itemLotteryTag.visibility = View.VISIBLE
                holder.itemLotteryTag.setTagInfo(tagType)
            }
            holder.itemNickname.updateLayoutParams<MarginLayoutParams> {
                marginStart = if (holder.itemLotteryTag.isVisible) {
                    6.dp
                } else {
                    0
                }
            }
            holder.itemNickname.text = element.anchor?.nickName
        }
    }

    companion object {
        fun traceItemShow(
            position: Int,
            data: RecommendItemNew,
            index: Int,
            subElement: CommonSubElement,
            view: View,
            recommendCommonItem: RecommendCommonItem
        ) {
            // 新首页-直播间卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62466)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId ?: "") // 客户端传，去重用
                .put(
                    "positionNew",
                    (index + 1).toString()
                ) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "") // 服务端传。
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "") // 服务端传。
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "") // 服务端传。
                .put("contentId", (subElement.id ?: 0).toString()) // 客户端传。去重使用
                .put("contentType", subElement.bizType ?: "") // 客户端传。去重使用
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                ) // 1-100 曝光面积占比，客户端传

            LiveLotteryTagView.TagType.convert(subElement.extraInfo?.lotteryType)?.also {
                trace.put("rewardName", it.desc) // 奖励名称
            }
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                contentTitle = subElement.title,
                contentPosition = (index + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            trace.createTrace()

            HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, view, index)
        }
    }
}