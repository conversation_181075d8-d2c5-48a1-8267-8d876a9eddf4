package com.ximalaya.ting.android.main.playpage.playy.component.video

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.PorterDuff
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.manager.TrackCollectManager
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.XmRequestPage.getPageUniqueRequestId
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
import com.ximalaya.ting.android.main.manager.TempoManager
import com.ximalaya.ting.android.main.model.download.DownloadTotalInfoModel
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher.Companion.changePlayMode
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher.Companion.currentPlayPageMode
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher.Companion.validModes
import com.ximalaya.ting.android.main.playpage.playy.view.PlayModeSegmentSwitch
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

class YVideoPlayMoreDlgFragment(
    private val videoId: Long,
    private val videoBaseInfo: AlbumVideoInfoModel.AlbumVideoInfo?,
    private val enableDownload: Boolean = true,
    private val downloadCallback: (DownloadTotalInfoModel?) -> Unit,
    private val showAllDownload: () -> Unit,
    private val report: () -> Unit,
    private val copyRight: () -> Unit,
    private val share: () -> Unit,
    private val mSoundInfo: PlayingSoundInfo,
    private val playContainer: IPlayContainer,
    private val showFav: Boolean = false
) : BottomSheetDialogFragment(), View.OnClickListener {

    private lateinit var mLlRoot: LinearLayout
    private lateinit var mIvDownload: ImageView
    private lateinit var mIvReport: ImageView
    private lateinit var mIvCopyright: ImageView

    private lateinit var modeSwitch: PlayModeSegmentSwitch
    private lateinit var modeSwitchText: TextView


    private var mAnchorIv: ImageView? = null
    private var mAnchorNameTv: TextView? = null
    private var mAnchorFollowTv: TextView? = null
    private var mIvAlbumCover: ImageView? = null
    private var mAlbumNameTv: TextView? = null
    private var mAlbumSubscribeTv: TextView? = null
    private var mAnchorContainer: View? = null
    private var mAlbumContainer: View? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.main_play_more_dlg_style)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return layoutInflater.inflate(R.layout.main_fra_dialog_video_play_more_y, container, false)
    }

    @SuppressLint("ClickableViewAccessibility", "SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initFav(view)
        initAnchorHeader(view)
        updateAlbumInfo(mSoundInfo)
        updateAnchor(mSoundInfo)

        mLlRoot = view.findViewById(R.id.main_ll_root)
        mIvDownload = view.findViewById(R.id.main_iv_download)
        view.findViewById<View>(R.id.main_ll_download)?.let {
            if (!enableDownload) it.visibility = View.GONE
            it.setOnTouchListener { v, event ->
                if (event.action == MotionEvent.ACTION_UP) {
                    if (event.x < v.width / 2) {
                        onDownloadClick()
                    } else {
                        dismiss()
                        showAllDownload()
                    }
                    traceClick("下载")
                }
                true
            }
        }
        var ivShare = view.findViewById<ImageView>(R.id.main_iv_share)
        view.findViewById<View>(R.id.main_ll_share).setOnClickListener {
            dismiss()
            share()
        }

        mIvReport = view.findViewById(R.id.main_iv_report)
        view.findViewById<View>(R.id.main_ll_report).setOnClickListener {
            dismiss()
            report()
            traceClick("举报")
        }

        mIvCopyright = view.findViewById(R.id.main_iv_copyright)
        view.findViewById<View>(R.id.main_ll_copyright).setOnClickListener {
            dismiss()
            copyRight()
            traceClick("版权申诉")
        }

        val isDark = BaseFragmentActivity.sIsDarkMode
        if (isDark) {
            mIvDownload.setColorFilter(Color.parseColor("#dcdcdc"), PorterDuff.Mode.SRC_IN)
            mIvReport.setColorFilter(Color.parseColor("#dcdcdc"), PorterDuff.Mode.SRC_IN)
            mIvCopyright.setColorFilter(Color.parseColor("#dcdcdc"), PorterDuff.Mode.SRC_IN)
            ivShare.setColorFilter(Color.parseColor("#dcdcdc"), PorterDuff.Mode.SRC_IN)
        }
    }
    
    private fun initAnchorHeader(view: View) {
        mAnchorContainer = view.findViewById<View>(R.id.main_play_more_action_ll_header_anchor)
        mAnchorIv = view.findViewById<ImageView>(R.id.main_play_more_action_lv_header_anchor_iv)
        mAnchorNameTv = view.findViewById<TextView>(R.id.main_play_more_action_lv_header_anchor_tv)
        mAnchorFollowTv = view.findViewById<TextView>(R.id.main_play_more_action_lv_header_anchor_follow_tv)
        mAlbumContainer = view.findViewById<View>(R.id.main_play_more_action_ll_header_album)
        mAlbumNameTv = view.findViewById<TextView>(R.id.main_play_more_action_lv_header_track_tv)
        mIvAlbumCover = view.findViewById<ImageView>(R.id.main_iv_album_cover)
        mAlbumSubscribeTv = view.findViewById<TextView>(R.id.main_play_more_action_lv_header_track_subscribe_tv)

        mAnchorFollowTv?.setOnClickListener(View.OnClickListener { v: View? -> doFollow() })
        mAlbumSubscribeTv?.setOnClickListener(View.OnClickListener { v: View? -> doSubscribe() })
        mAnchorContainer?.setOnClickListener(View.OnClickListener { v: View? ->
            toAnchorPage()
        })
        mAlbumContainer?.setOnClickListener(View.OnClickListener { v: View? ->
            toAlbumPage()
        })

    }

    private fun initFav(view: View) {
        val favLayout = view.findViewById<View>(R.id.main_ll_fav)
        if (showFav) {
            favLayout.visibility = View.VISIBLE
        } else {
            favLayout.visibility = View.GONE
            return
        }

        val favIv = view.findViewById<ImageView>(R.id.main_iv_fav)
        val favTv = view.findViewById<TextView>(R.id.main_tv_fav)

        val isCollected = mSoundInfo.otherInfo?.isCollect == true

        if (isCollected) {
            favIv.setImageResource(R.drawable.main_ic_faved_n_n_line_regular)
            favTv.text = "已收藏"
        } else {
            favIv.setImageResource(R.drawable.main_ic_fav_n_n_line_regular)
            favTv.text = "收藏"
        }
        val trackId = mSoundInfo.trackInfo?.trackId?: return
        favLayout.setOnClickListener {
            dismiss()
            TrackCollectManager.getInstance().requestCollectOrUnCollect(
                isCollected,
                trackId,
                object : IDataCallBack<Boolean?> {
                    override fun onSuccess(data: Boolean?) {
                        if (!isCollected) {
//                            CustomToast.showToast("收藏成功")
                            favIv.setImageResource(R.drawable.main_ic_faved_n_n_line_regular)
                            favTv.text = "已收藏"
                        } else {
                            favIv.setImageResource(R.drawable.main_ic_fav_n_n_line_regular)
                            favTv.text = "收藏"
                            CustomToast.showToast("取消收藏")
                        }
                    }

                    override fun onError(code: Int, message: String) {
                        CustomToast.showFailToast(message)
                    }
                })
        }
    }

    private fun updateAnchor(soundInfo: PlayingSoundInfo?) {
        if (soundInfo == null) {
            return
        }
        var logo: String? = null
        val userInfo = soundInfo.userInfo
        if (userInfo != null) {
            logo = userInfo.smallLogo
            mAnchorNameTv!!.text = userInfo.nickname
        }
        ImageManager.from(context)
            .displayImage(mAnchorIv, logo, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88)
        if (userInfo != null && userInfo.uid == UserInfoMannage.getUid()) {
            mAnchorFollowTv!!.visibility = View.INVISIBLE
        } else {
            mAnchorFollowTv!!.visibility = View.VISIBLE
            var isFollow = false
            if (soundInfo.otherInfo != null) {
                isFollow = soundInfo.otherInfo!!.isFollowed
            }
            setAnchorStatus(isFollow)
        }
    }

    private fun setAnchorStatus(isFollow: Boolean) {
        if (isFollow) {
            mAnchorFollowTv?.visibility = View.GONE
        } else {
            mAnchorFollowTv?.text = "关注"
            mAnchorFollowTv?.setSelected(false)
            mAnchorFollowTv?.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.main_album_icon_add, 0, 0, 0
            )
        }
        mAnchorFollowTv?.setContentDescription(if (isFollow) "取消关注" else "关注")
    }

    private fun updateAlbumInfo(soundInfo: PlayingSoundInfo?) {
        if (soundInfo == null) {
            return
        }
        val track = soundInfo.trackInfo2TrackM() ?: return
        if (soundInfo.albumInfo != null && !TextUtils.isEmpty(soundInfo.albumInfo!!.title)) {
            mAlbumNameTv!!.text = soundInfo.albumInfo!!.title
        } else if (track.album != null) {
            mAlbumNameTv!!.text = track.album!!.albumTitle
        }
        val album = soundInfo.albumInfo ?: return
        ImageManager.from(context).displayImage(
            mIvAlbumCover,
            album.getValidCoverUrl(),
            com.ximalaya.ting.android.host.R.drawable.host_default_album
        )
        setAlbumSubscribeStatus(album.isFavorite)
    }

    private fun setAlbumSubscribeStatus(isFavorite: Boolean) {
        if (isFavorite) {
            mAlbumSubscribeTv!!.visibility = View.GONE
        } else {
            mAlbumSubscribeTv!!.setSelected(false)
            mAlbumSubscribeTv!!.text = "订阅"
            mAlbumSubscribeTv!!.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.main_album_icon_add, 0, 0, 0
            )
        }
        mAlbumSubscribeTv!!.setContentDescription(if (isFavorite) "取消订阅" else "订阅")
    }

    private fun doFollow() {
        if (mSoundInfo.otherInfo == null || mSoundInfo.userInfo == null) {
            return
        }
        val isCurrentFollowed: Boolean = mSoundInfo.otherInfo?.isFollowed == true
        AnchorFollowManage.followV3WithLoginCheckDeferredAction(
            activity,
            mSoundInfo.userInfo?.uid?: 0L,
            isCurrentFollowed,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
            5,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(`object`: Boolean?) {
                    if (`object` == null || mSoundInfo == null || mSoundInfo.otherInfo == null) {
                        return
                    }
                    if (`object`) {
                        CustomToast.showSuccessToast("关注成功")
                    }
                    setAnchorStatus(`object`)
                }

                override fun onError(code: Int, message: String) {}
            }, true, true, "newPlay"
        )
        val trace = XMTraceApi.Trace()
            .click(17635)
            .put("currPage", "newPlay")
            .put("currTrackId", getTrackId().toString())
            .put("currAlbumId", getAlbumId().toString())
            .put("anchorId", getUid().toString())
            .put("categoryId", getCategoryId().toString())
            .put("item", if (isCurrentFollowed) "取消关注" else "关注")
            .put("bizType", "" + AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE)
            .put("subBizType", "5")
        val service = PlayPageInternalServiceManager.getInstance().getService(
            IXPlayFragmentService::class.java
        )
        if (service != null) {
            trace.put("trackForm", if (service.isVideoMode()) "video" else "track")
        }
        trace.createTrace()
    }

    private fun doSubscribe() {
        if (mSoundInfo == null || mSoundInfo.toAlbumM() == null) {
            return
        }
        if (parentFragment !is BaseFragment2) {
            return
        }
        val album: AlbumM = mSoundInfo.toAlbumM()
        val param = AlbumCollectParam(
            "newPlay",
            AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_AUDIO_PLAY_50002,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE, 8,
            album, true
        )
        param.showFollowDialog = false
        AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(
            param,
            parentFragment as BaseFragment2?,
            object : ICollectWithFollowStatusCallback {
                override fun getFollowSubBizType(): Int {
                    return 8
                }

                override fun followDialogAction(status: Int) {}
                override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                    album.isFavorite = isCollected
                    setAlbumSubscribeStatus(isCollected)
//                    if (isCollected && AlbumEventManage.getAutoFollow()) {
//                        if (mSoundInfo != null && mSoundInfo.otherInfo != null) {
//                            mSoundInfo.otherInfo?.isFollowed = true
//                            updateLvHeaderView(mSoundInfo)
//                        }
//                    }
                }

                override fun onError() {}
            },
            "newPlay"
        )
        val trace = XMTraceApi.Trace()
            .click(17636)
            .put("currPage", "newPlay")
            .put("currTrackId", getTrackId().toString())
            .put("currAlbumId", getAlbumId().toString())
            .put("anchorId", getUid().toString())
            .put("categoryId", getCategoryId().toString())
            .put("Item", if (album.isFavorite) "取消订阅" else "订阅")
        val service = PlayPageInternalServiceManager.getInstance().getService(
            IXPlayFragmentService::class.java
        )
        if (service != null) {
            trace.put("trackForm", if (service.isVideoMode()) "video" else "track")
        }
        trace.createTrace()
    }

    private fun toAnchorPage() {
        if (ChildProtectManager.isChildProtectOpen(context)) {
            ChildProtectManager.showFeatureCannotUseToast()
            return
        }
        if (mSoundInfo == null || mSoundInfo.trackInfo2TrackM() == null) {
            return
        }
        val track: TrackM = mSoundInfo.trackInfo2TrackM()
        if (parentFragment !is BaseFragment2) {
            return
        }
        dismiss()
        if (track.uid > 0) {
            (parentFragment as BaseFragment2?)!!.startFragment(newAnchorSpaceFragment(track.uid))
        } else if (track.getAnnouncer() != null && track.getAnnouncer().announcerId > 0) {
            (parentFragment as BaseFragment2?)!!.startFragment(newAnchorSpaceFragment(track.getAnnouncer().announcerId))
        }
        UserTracking()
            .setSrcPage("track")
            .setSrcPageId(track.dataId)
            .setSrcModule("主播条")
            .setItemId(track.uid)
            .setItem("user")
            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW)
        XMTraceApi.Trace()
            .click(17634)
            .put("currPage", "newPlay")
            .put("currTrackId", getTrackId().toString())
            .put("currAlbumId", getAlbumId().toString())
            .put("anchorId", getUid().toString())
            .put("categoryId", getCategoryId().toString())
            .put(XmRequestIdManager.XM_REQUEST_ID, getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
            .createTrace()
    }

    private fun toAlbumPage() {
        if (mSoundInfo == null || mSoundInfo.albumInfo == null) {
            return
        }
        dismiss()
        val albumInfo: PlayingSoundInfo.AlbumInfo = mSoundInfo.albumInfo?: return
        if (albumInfo.isPaid && albumInfo.priceTypeId == 2) {
            AlbumEventManage.startMatchAlbumFragment(
                albumInfo.albumId, AlbumEventManage.FROM_ALBUM_BELONG,
                ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM, null, null, -1, activity
            )
        } else {
            if (parentFragment !is BaseFragment2) {
                return
            }
            // 因为售后页可以存在统一商详页的形式，所以不再直接跳转至专辑页
            AlbumEventManage.startMatchAlbumFragment(
                albumInfo.albumId, AlbumEventManage.FROM_ALBUM_BELONG,
                ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM, null, null, -1, BaseApplication.getMainActivity()
            )

            /*((BaseFragment2) getParentFragment()).startFragment(
                    AlbumFragmentNew.newInstance(
                            albumInfo.title,
                            albumInfo.albumId,
                            AlbumEventManage.FROM_ALBUM_BELONG,
                            ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM));*/
        }
        if (mSoundInfo.trackInfo == null) {
            return
        }
        // 新声音播放页-更多面板-专辑条  点击事件
        XMTraceApi.Trace()
            .click(51718) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("currAlbumId", getAlbumId().toString())
            .put("currTrackId", getTrackId().toString())
            .put("anchorId", getUid().toString()) // 当前声音对应的主播id 专辑
            .put(XmRequestIdManager.XM_REQUEST_ID, getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
            .put("categoryId", getCategoryId().toString())
            .createTrace()
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.main_rl_speed -> {
                TempoManager.getInstance()
                    .showConfigDialog(activity, false, true, object : TempoManager.TempoTrace {
                        override fun onShow() {
                            XMTraceApi.Trace()
                                .setMetaId(48802)
                                .setServiceId("dialogView") // 弹窗展示时上报
                                .put("currPage", "newVideoPlay")
                                .put("currVideoId", "" + videoId)
                                .put("currTrackId", "" + videoId)
                                .put("currAlbumId", videoBaseInfo?.albumId?.toString())
                                .put("trackForm", "video") // track 表示音频，video 表示视频
                                .createTrace()
                        }

                        override fun onClick(itemName: String?) {
// 播放页视频tab_倍速弹框-操作  弹框控件点击
                            // 播放页视频tab_倍速弹框-操作  弹框控件点击
                            XMTraceApi.Trace()
                                .setMetaId(48803)
                                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                                .put("currPage", "newVideoPlay")
                                .put("item", itemName) // 打开只应用到本专辑|关闭只应用到本专辑|倍速对应的按钮名称
                                .put("currVideoId", "" + videoId)
                                .put("currTrackId", "" + videoId)
                                .put("currAlbumId", videoBaseInfo?.albumId?.toString())
                                .createTrace()
                        }
                    })
                traceClick("倍速播放")
                dismiss()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 播放页视频tab_更多  弹框展示
        XMTraceApi.Trace()
            .setMetaId(48376)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("currPage", "newVideoPlay")
            .put("currTrackId", videoId.toString())
            .put("currAlbumId", videoBaseInfo?.albumId?.toString())
            .put("trackForm", "video") // track 表示音频，video 表示视频
            .createTrace()
    }

    private fun traceClick(item: String) {
        // 播放页视频tab_更多-操作  弹框控件点击
        XMTraceApi.Trace()
            .setMetaId(48377)
            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
            .put("item", item)
            .put("currPage", "newVideoPlay")
            .put("currTrackId", videoId.toString())
            .put("currAlbumId", videoBaseInfo?.albumId?.toString())
            .put("trackForm", "video") // track 表示音频，video 表示视频
            .createTrace()
    }

    override fun onStart() {
        super.onStart()
        view?.post {
            // 修改默认高度
            val bottomSheet = dialog?.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let {
                val behavior = BottomSheetBehavior.from(it)
                val totalHeight = mLlRoot.height
                val halfExpandedRatio = mLlRoot.height * 1f / BaseUtil.getScreenHeight(context)
                if (halfExpandedRatio < 0f || halfExpandedRatio > 1f) {
                    // 确保比例在合理范围内
                    behavior.halfExpandedRatio = 0.5f
                } else {
                    behavior.halfExpandedRatio = halfExpandedRatio
                }
                behavior.peekHeight = totalHeight
                behavior.expandedOffset = Math.max(0, BaseUtil.getScreenHeight(context) - totalHeight)
            }
        }
    }

    private fun checkDownloadInfoValid(mDownloadInfo: DownloadTotalInfoModel?): Boolean {
        if (mDownloadInfo?.videoDownloadInfo?.downloadPathResultMap == null) {
            CustomToast.showFailToast("网络出错，请稍后重试")
            return false
        }
        return true
    }

    private fun onDownloadClick() {
        dismiss()
        if (!NetworkUtils.isNetworkAvaliable(activity)) {
            CustomToast.showFailToast(R.string.main_no_net)
            return
        }
        MainCommonRequest.getDownloadTotalInfo(
            videoId,
            object : IDataCallBack<DownloadTotalInfoModel?> {
                override fun onSuccess(downloadInfoModel: DownloadTotalInfoModel?) {
                    if (downloadInfoModel == null || !checkDownloadInfoValid(downloadInfoModel)) {
                        CustomToast.showFailToast("网络出错，请稍后重试")
                        return
                    }
                    downloadCallback(downloadInfoModel)
                }

                override fun onError(code: Int, message: String) {
                    CustomToast.showFailToast(message)
                }
            })

    }

    private fun getTrackId(): Long {
        val info = PlayPageDataManager.getInstance().getSoundInfo() ?: return -1
        val track = info.trackInfo2TrackM() ?: return -1
        return track.dataId
    }

    private fun getAlbumId(): Long {
        val info = PlayPageDataManager.getInstance().getSoundInfo() ?: return -1
        val album = info.toAlbumM() ?: return -1
        return album.id
    }

    private fun getCategoryId(): Int {
        val info = PlayPageDataManager.getInstance().getSoundInfo() ?: return -1
        val track = info.trackInfo2TrackM() ?: return -1
        return track.categoryId
    }

    private fun getUid(): Long {
        val info = PlayPageDataManager.getInstance().getSoundInfo() ?: return -1
        val track = info.trackInfo2TrackM() ?: return -1
        return track.uid
    }
}