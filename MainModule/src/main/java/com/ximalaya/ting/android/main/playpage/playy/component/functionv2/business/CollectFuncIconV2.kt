package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.view.HapticFeedbackConstants
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.play.data.PlayPageDynamicState
import com.ximalaya.ting.android.host.manager.AudioInfoTraceUtil
import com.ximalaya.ting.android.host.manager.TrackCollectManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.appcomment.AppCommentManager
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.function.view.YAnimatedCountTextView
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil
import com.ximalaya.ting.android.main.playpage.util.PlayPageInteractiveTraceUtil
import com.ximalaya.ting.android.main.playpage.view.updateProgress
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager


class CollectFuncIconV2(private val fragment2: BaseFragment2?) : IBusinessView(),
    View.OnClickListener {
    private var curSoundInfo: PlayingSoundInfo? = null
    private val iconSize = 26.dp

    private var mCollectCountTv: YAnimatedCountTextView? = null
    private var mLottieCollect: LottieAnimationView? = null
    private var isCollectBtnClick = false

    private var mCollectCount: Long = 0


    override fun provideIcon(context: Context, soundInfo: PlayingSoundInfo): View {
        return mLottieCollect ?: XmLottieAnimationView(context).apply {
            layoutParams = ViewGroup.MarginLayoutParams(iconSize, iconSize)
            imageAlpha = (255 * 0.55).toInt()
            contentDescription = "收藏"
            setOnClickListener(this@CollectFuncIconV2)
            val jsonLottie = "lottie/host_lottie_for_collect_action_y.json"
            setAnimation(jsonLottie)
            mLottieCollect = this


            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                setColorFilter(PSkinManager.getBtnThemeColor())
            }
        }
    }

    override fun providerBadgeView(context: Context, soundInfo: PlayingSoundInfo): BadgeView {
        return BadgeView(
            mCollectCountTv ?: YAnimatedCountTextView(context).apply {
                layoutParams = ViewGroup.MarginLayoutParams(
                    ViewGroup.MarginLayoutParams.WRAP_CONTENT,
                    ViewGroup.MarginLayoutParams.WRAP_CONTENT
                )
                isSingleLine = true
                textSize = 8f
                alpha = 0.55f
                includeFontPadding = false

                if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                    setTextColor(PSkinManager.getBtnThemeColor())
                } else {
                    setTextColor(Color.WHITE)
                }

                mCollectCountTv = this
                typeface = Typeface.DEFAULT_BOLD
                updateCountText()
            }
        )
    }

    override fun getBottomText(): String {
        return "收藏"
    }

    override fun providerBottomView(context: Context, soundInfo: PlayingSoundInfo): BottomView? {
        return BottomView(
            TextView(context).apply {
                text = "收藏"
                alpha = 0.4f
                textSize = 10f
                isSingleLine = true
            }
        )
    }

    override fun shouldDisplay(soundInfo: PlayingSoundInfo, isAudio: Boolean): Boolean {
        this.curSoundInfo = soundInfo
        update()
        return true
    }

    private fun update() {
        val isCollect = PlayCommentUtil.isTrackCollected(curSoundInfo)
        if (mLottieCollect?.isAnimating == true) {
            mLottieCollect?.cancelAnimation()
        }
        mLottieCollect.updateProgress(if (isCollect) 1f else 0.toFloat())
    }

    override fun onCreate() {
    }

    override fun onDestroy() {
    }

    override fun updateDynamicData(dynamic: PlayPageDynamicState) {
        mCollectCount = dynamic.collectCount
        updateCollectState(dynamic.isCollect)
    }

    private fun updateCountText() {
        mCollectCountTv?.setCount(mCollectCount)
        ViewStatusUtil.setVisible(
            if (mCollectCount > 0) View.VISIBLE else View.GONE,
            mCollectCountTv
        )
    }

    private fun updateCollectState(isCollect: Boolean) {
        updateCountText()
        if (isCollect && mLottieCollect?.isAnimating == true) return
        mLottieCollect?.cancelAnimation()
        mLottieCollect.updateProgress(if (isCollect) 1f else 0f)
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        doCollect()
    }

    private fun doCollect() {
        val activity = BaseApplication.getTopActivity() ?: return
        if (mLottieCollect?.isAnimating == true) return
        val isCollected = PlayCommentUtil.isTrackCollected(curSoundInfo)
        traceLikeOrCollect(isCollected, false)
        if (UserInfoMannage.hasLogined()) {
            if (!isCollected) {
                AppCommentManager.showDialog(600, true)
                mLottieCollect?.playAnimation()
                if (mCollectCountTv?.visibility == View.VISIBLE) {
                    mCollectCountTv?.animateChange(mCollectCount)
                }
            } else {
                mLottieCollect.updateProgress(0f)
            }
        } else {
            UserInfoMannage.gotoLogin(activity)
            return
        }

        val track: TrackM? = curSoundInfo?.trackInfo2TrackM()
        track?.dataId?.let {
            isCollectBtnClick = true
            TrackCollectManager.getInstance().requestCollectOrUnCollect(
                isCollected,
                it,
                object : IDataCallBack<Boolean?> {
                    override fun onSuccess(`object`: Boolean?) {
                        if (`object` == null || !`object`) {
                            return
                        }
                        onSoundCollected(curSoundInfo)
                        isCollectBtnClick = false
                    }

                    override fun onError(code: Int, message: String) {
                        // 回调回来 mLottieCollect 可能未空，需要做判断
                        if (mLottieCollect != null) {
                            if (mLottieCollect?.isAnimating == true) {
                                mLottieCollect?.cancelAnimation()
                            }
                            mLottieCollect?.updateProgress(if (isCollected) 1f else 0.toFloat())
                        }
                        isCollectBtnClick = false
                        CustomToast.showFailToast(message)
                    }
                })
        }
    }

    private fun onSoundCollected(info: PlayingSoundInfo?) {
        if (info?.otherInfo == null) {
            return
        }
        val isCollect = info.otherInfo!!.isCollect
        if (isCollect) {
            mLottieCollect?.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
        } else {
            if (mLottieCollect?.isAnimating == true) {
                mLottieCollect?.cancelAnimation()
            }
            mLottieCollect.updateProgress(0f)
        }
    }

    private fun traceLikeOrCollect(isCancel: Boolean, isLike: Boolean) {
        // 声音-点赞/收藏操作（底部）  点击事件
        XMTraceApi.Trace()
            .click(17506) // 用户点击时上报
            .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
            .put("currTrackId", curSoundInfo?.trackInfo?.trackId?.toString() ?: "")
            .put("currAlbumId", curSoundInfo?.albumInfo?.albumId?.toString() ?: "")
            .put("Item", if (isLike) "点赞" else "收藏") // 点赞 / 收藏，记录点击后的状态
            .put("currPage", "newPlay") // newPlay/互动播放页/互动浮层页等
            .put("replyId", "")
            .put("isCancel", isCancel.toString()) // true/false
            .put("commentId", "")
            .put("uid", curSoundInfo?.userInfo?.uid?.toString())
            .put("style", "竖版") // 横版｜竖版
            .apply {
                PlayPageInteractiveTraceUtil.tracePlayInfo(this)
            }
            .put("hasDynamicGuide", "false") // ture/false
            .put("content", "") // 有上报引导点赞文案/无不上报
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE
                )
            )
            .put("fullScreenMode", "half") // full 表示全屏，half 表示半屏
            .put("playViewForm", "1") //  1 表示高唤起 2 表示低唤起
            .apply {
                AudioInfoTraceUtil.interceptTrace(this)
            }
            .createTrace()
    }
}