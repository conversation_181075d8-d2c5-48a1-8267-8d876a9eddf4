package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by felix.chen on 2023/3/13.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18621868330
 */
class RecommendExplosiveContentAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendExplosiveContentAdapterProviderStaggered.CardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendExplosiveContentAdapterProviderStaggered.CardViewHolder, RecommendItemNew> {


    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_explosive_content_card, parent, false)
    }

    class CardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val ivTitleIcon: ImageView = convertView.findViewById(R.id.main_iv_explosive_content_icon)
        val tvTitle: TextView = convertView.findViewById(R.id.main_tv_explosive_content_title)
        val rcvSocialList: RecyclerView = convertView.findViewById(R.id.main_rcv_album_list)
        val tvMore: TextView = convertView.findViewById(R.id.main_iv_more)
        val ivBackGround: ImageView = convertView.findViewById(R.id.main_iv_background)
        val ivBackGroundStatic: ImageView = convertView.findViewById(R.id.main_iv_background_static)
    }

    override fun createViewHolder(convertView: View?): CardViewHolder? {
        if (convertView == null) {
            return null
        }
        return CardViewHolder(convertView)
    }

    override fun bindViewHolder(
        holder: CardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }

        if (!recommendCommonItem.title.isNullOrEmpty()) {
            holder.tvTitle.text = recommendCommonItem.title
        } else {
            holder.tvTitle.text = ""
        }

        ImageManager.from(BaseApplication.getMyApplicationContext()).displayImageNotIncludeDownloadCacheSizeInDp(
            holder.ivTitleIcon,
            if (BaseFragmentActivity.sIsDarkMode) recommendCommonItem.ext?.other?.iconDark else recommendCommonItem.ext?.other?.icon,
            -1,
            22,
            22
        )

        if (!TextUtils.isEmpty(recommendCommonItem.ext?.other?.landingPageText)) {
            holder.tvMore.text = recommendCommonItem.ext?.other?.landingPageText
        }
        holder.ivBackGroundStatic.visibility = View.VISIBLE
        ImageManager.from(BaseApplication.getMyApplicationContext()).displayImageNotIncludeDownloadCacheSizeInDp(
            holder.ivBackGroundStatic,
            if (BaseFragmentActivity.sIsDarkMode) recommendCommonItem.ext?.other?.darkBackdropHolder else recommendCommonItem.ext?.other?.backdropHolder,
            -1,
            300,
            150
        )
        ImageManager.from(BaseApplication.getMyApplicationContext()).displayImageNotIncludeDownloadCacheSizeInDp(
            holder.ivBackGround,
            if (BaseFragmentActivity.sIsDarkMode) recommendCommonItem.ext?.other?.darkBackdrop else recommendCommonItem.ext?.other?.backdrop,
            -1,
            300,
            150
        ) { lastUrl, bitmap ->
            holder.ivBackGroundStatic.visibility = View.GONE
        }

        if (TextUtils.isEmpty(recommendCommonItem.landingPage)) {
            ViewStatusUtil.setVisible(View.INVISIBLE, holder.tvMore)
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.tvMore)
        }

        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnOneClickListener {
            if (TextUtils.isEmpty(recommendCommonItem.landingPage)) {
                return@setOnOneClickListener
            }
            // 新首页-首页大卡模块  点击事件
            var trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem?.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem?.ubtV2)
            trace1.createTrace()

            // 新首页-IP推荐卡-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(63023) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("xmRequestId", recommendItemNew.xmRequestId)
                .put("modulePosition", (position + 1).toString())
                .put("url", recommendCommonItem.landingPage)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
            ToolUtil.clickUrlAction(fragment, recommendCommonItem.landingPage!!, holder.tvMore)
        }
        val view = holder.rootView.findViewById<View>(R.id.main_ll_explosive_title)
        val viewLayoutParams = view.layoutParams as? ViewGroup.MarginLayoutParams
        viewLayoutParams?.topMargin = 9.dp

        val rcvLayoutParams = holder.rcvSocialList.layoutParams as? ViewGroup.MarginLayoutParams
        rcvLayoutParams?.topMargin = 10.dp

        val ivMoreLayoutParams = holder.tvMore.layoutParams as? ViewGroup.MarginLayoutParams
        ivMoreLayoutParams?.setMargins(0, 0, 0, 0)
        holder.tvMore.setPadding(16.dp, 10.dp, 16.dp, 10.dp)

        val cardAlbumListAdapter =
            ExplosiveContentItemAdapter(
                dataAction,
                fragment,
                recommendCommonItem,
                recommendItemNew,
                recommendCommonItem.subElements!!,
                position
            )
        // 听单专辑列表
        holder.rcvSocialList.adapter = cardAlbumListAdapter
        holder.rcvSocialList.layoutManager = LinearLayoutManager(convertView?.context, LinearLayoutManager.VERTICAL, false)
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: CardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }
                val childCount = holder.rcvSocialList.childCount
                for (i in 0 until childCount) {
                    val view = holder.rcvSocialList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val albumId = subElement.refId ?: 0

                        // 新首页-IP推荐卡  控件曝光
                        val trace = XMTraceApi.Trace()
                            .setMetaId(63022)
                            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                            .put("currPage", "newHomePage")
                            .put("xmRequestId", data.xmRequestId)
                            .put("positionNew", (index + 1).toString()) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                            .put("contentId", albumId.toString()) // 客户端传。去重使用
                            .put("contentType", "explosiveContent") // 客户端传。去重使用
                            .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 可见区域占屏幕的比例
                            .put("modulePosition", (position + 1).toString())
                        SpmTraceUtil.addSpmTraceInfo(
                            trace,
                            recommendCommonItem.ubtV2,
                            (position + 1).toString(),
                            subElement.title,
                            (index + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
                        if (data.isLocalCache) {
                            trace.isLocalCache
                        }
                        trace.createTrace()

                        HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, view, index)
                    }
                }
            }
        }
    }

    class ExplosiveContentItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        // 页面
        private val fragment: BaseFragment2,
        // 卡片数据
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        // 专辑列表
        list: List<CommonSubElement>,
        var positionNew: Int
    ) : RecyclerView.Adapter<ExplosiveContentItemAdapter.BaseViewHolder>() {
        val ITEM_TYPE_ALBUM = 0
        val ITEM_TYPE_TRACK = 1

        // 专辑列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            commonSubElementList.addAll(list)
        }

        override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): BaseViewHolder {
            return if (viewType == ITEM_TYPE_ALBUM) {
                AlbumViewHolder(
                    LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(
                        R.layout.main_item_recommend_explosive_content_album_item, parent, false
                    )
                )
            } else {
                TrackViewHolder(
                    LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(
                        R.layout.main_item_recommend_explosive_content_track_item, parent, false
                    )
                )
            }
        }

        override fun getItemViewType(position: Int): Int {
            val commonSubElement = commonSubElementList[position]
            commonSubElement?.bizType?.let {
                if (it == "Album") {
                    return ITEM_TYPE_ALBUM
                } else if (it == "Track") {
                    return ITEM_TYPE_TRACK
                }
            }
            return ITEM_TYPE_ALBUM
        }

        override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
            var commonSubElement = commonSubElementList[position] ?: return
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)

            if (getItemViewType(position) == ITEM_TYPE_ALBUM) {
                bindAlbumViewHolder(holder as AlbumViewHolder, position, commonSubElement)
            } else if (getItemViewType(position) == ITEM_TYPE_TRACK) {
                bindTrackViewHolder(holder as TrackViewHolder, position, commonSubElement)
                holder.ivPlay.setOnClickListener {
                    traceOnItemClick(commonSubElement, position, it, true)
                    if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying &&
                        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId == commonSubElement.refId) {
                        XmPlayerManager.getInstance(ToolUtil.getCtx()).pause(PauseReason.Business.RecommendExplosiveContent)
                    } else {
                        if (commonSubElement.landingPage.isNullOrEmpty()) {
                            return@setOnClickListener
                        }
                        ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, it)
                    }
                }
            }

            holder.itemView.setOnClickListener {
                traceOnItemClick(commonSubElement, position, it, false)
                if (getItemViewType(position) == ITEM_TYPE_ALBUM) {
                    if (commonSubElement.landingPage.isNullOrEmpty()) {
                        return@setOnClickListener
                    }
                    ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, it)
                } else {
                    if (commonSubElement.ext?.other?.albumLandingPage.isNullOrEmpty()) {
                        return@setOnClickListener
                    }
                    ToolUtil.clickUrlAction(fragment, commonSubElement.ext?.other?.albumLandingPage!!, it)
                }
            }
        }

        private fun bindAlbumViewHolder(holder: AlbumViewHolder, position: Int, commonSubElement: CommonSubElement) {
            commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                commonSubElement.ext?.other?.getBRTagUrl()
            )

            holder.itemTitleTv.text = commonSubElement.title
            if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.ext!!.reasonContent
            }
            holder.ivCoverClothesStatic.visibility = View.VISIBLE
            ImageManager.from(BaseApplication.getMyApplicationContext()).displayImageNotIncludeDownloadCacheSizeInDp(
                holder.ivCoverClothesStatic,
                if (BaseFragmentActivity.sIsDarkMode) commonSubElement.ext?.albumDressDarkHolder else commonSubElement.ext?.albumDressHolder,
                -1,
                104,
                94
            )
            ImageManager.from(BaseApplication.getMyApplicationContext()).displayImageNotIncludeDownloadCacheSizeInDp(
                holder.ivCoverClothes,
                if (BaseFragmentActivity.sIsDarkMode) commonSubElement.ext?.albumDressDark else commonSubElement.ext?.albumDress,
                -1,
                104,
                94
            ) { lastUrl, bitmap ->
                holder.ivCoverClothesStatic.visibility = View.GONE
            }
            var textViewContainerWith = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
            val otherWidth = 16 + 79 + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(holder.layoutShowTags, commonSubElement.ext?.showTags,
                (textViewContainerWith - otherWidth.dp), commonSubElement.ext?.subTitle1, commonSubElement.ext?.subTitle2)
        }


        private fun bindTrackViewHolder(holder: TrackViewHolder, position: Int, commonSubElement: CommonSubElement) {
            holder.itemTitleTv.text = commonSubElement.title
            var params: ConstraintLayout.LayoutParams = holder.cslTextArea.layoutParams as ConstraintLayout.LayoutParams
            if (position == 1) {
                params.topMargin = 8.dp
            } else {
                params.topMargin = 12.dp
            }
            if (commonSubElement.subTitle.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.subTitle
            }
            if (commonSubElement.ext?.other?.tag.isNullOrEmpty()) {
                holder.subTagTv.visibility = View.GONE
            } else {
                holder.subTagTv.visibility = View.VISIBLE
                holder.subTagTv.text = commonSubElement.ext?.other?.tag
            }
            if (position == commonSubElementList.size - 1) {
                ViewStatusUtil.setVisible(View.GONE, holder.dividerView)
            } else {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.dividerView)
            }

            if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying &&
                XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId == commonSubElement.refId) {
                holder.ivPlay.setImageResource(R.drawable.host_btn_pause_btn_n_fill_n_24)
            } else {
                holder.ivPlay.setImageResource(R.drawable.host_btn_play_btn_inside_fill_n_24)
            }
        }

        private fun traceOnItemClick(commonSubElement: CommonSubElement, position: Int, view: View, fromPlayIcon: Boolean) {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return
            }
            var albumId = commonSubElement.refId ?: 0
            var trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (positionNew + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                moduleItem?.ubtV2,
                (positionNew + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, moduleItem?.ubtV2)
            trace1.createTrace()

            // 新首页-IP推荐卡  点击事件
            var trace = XMTraceApi.Trace()
                .click(63021) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("contentId", albumId.toString())
                .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 可见区域占屏幕的比例
                .put("modulePosition", (positionNew + 1).toString())
                .put("area", if (fromPlayIcon) "play" else "item")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem?.ubtV2,
                (positionNew + 1).toString(),
                contentTitle = commonSubElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()
        }

        override fun getItemCount(): Int {
            return commonSubElementList.size
        }

        abstract class BaseViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        }

        class AlbumViewHolder(view: View) : BaseViewHolder(view) {
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var albumCoverLayoutView: AlbumCoverLayoutView = view.findViewById(R.id.main_album_cover_layout)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var ivCoverClothes: ImageView = view.findViewById(R.id.main_iv_cover_clothes)
            var ivCoverClothesStatic: ImageView = view.findViewById(R.id.main_iv_cover_clothes_static)
        }

        class TrackViewHolder(view: View) : BaseViewHolder(view) {
            var cslTextArea: ConstraintLayout = view.findViewById(R.id.main_layout_right_txt_area)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var subTagTv: TextView = view.findViewById(R.id.main_tv_sub_tag)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var ivPlay: ImageView = view.findViewById(R.id.main_iv_play_btn)
            var dividerView: View = view.findViewById(R.id.main_divider_line)
        }
    }

    companion object {
        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }
    }
}