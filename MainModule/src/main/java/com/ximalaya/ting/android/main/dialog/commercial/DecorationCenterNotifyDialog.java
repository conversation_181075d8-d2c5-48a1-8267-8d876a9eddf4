package com.ximalaya.ting.android.main.dialog.commercial;


import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.utils.widget.ImageFilterView;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.commercial.ActionButtonInfo;
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageSkinDownloadManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @time 2024/9/6 15:40
 * @description: 免付一体化转化弹窗
 **/
public class DecorationCenterNotifyDialog extends BaseLoadDialogFragment
        implements View.OnClickListener {
    public static final String TAG = DecorationCenterNotifyDialog.class.getSimpleName();
    private static final String KEY_DECORATION_CENTER_NOTICE_DIALOG_HAS_SHOWN = "key_decoration_center_notice_dialog_has_shown";
    private static final String KEY_DECORATION_CENTER_NOTICE_CAN_SHOW = "key_decoration_center_notice_dialog_can_show";

    public static void recordCanShowDecorationCenterNotifyDialog(boolean result) {
        MMKVUtil.getInstance().saveBoolean(KEY_DECORATION_CENTER_NOTICE_CAN_SHOW, result);
    }

    public static void tryShow(BaseFragment fragment) {
        if (!PlayPageSkinDownloadManager.isConfigOn()) {
            Logger.w(TAG, "tryShow: switch on: false");
            return;
        }
        if (!fragment.isRealVisable()) {
            Logger.w(TAG, "tryShow: fragment is not visible:" + fragment);
            return;
        }
        boolean debugMode = false;
        if (ConstantsOpenSdk.isDebug) {
            debugMode = "1".equals(ToolUtil.getDebugSystemProperty("debug.mark.decoration", "0"));
        }
        boolean canShow = MMKVUtil.getInstance().getBoolean(KEY_DECORATION_CENTER_NOTICE_CAN_SHOW, false);
        if (!canShow && !debugMode) {
            Logger.w(TAG, "tryShow: key_decoration_center_notice_dialog_can_show:false");
            return;
        }
        boolean hasShown = MMKVUtil.getInstance().getBoolean(KEY_DECORATION_CENTER_NOTICE_DIALOG_HAS_SHOWN, false);
        if (hasShown && !debugMode) {
            Logger.w(TAG, "tryShow: key_decoration_center_notice_dialog_has_shown");
            return;
        }
        if (ViewUtil.haveDialogIsShowing(fragment.getActivity())) {
            Logger.w(TAG, "tryShow: haveDialogIsShowing");
            return;
        }
        new DecorationCenterNotifyDialog().showDialog(fragment.getChildFragmentManager(), TAG);
    }

    private View vTargetChildView;
    private View vTopClickArea;
    private ImageView vCloseIcon;
    private ImageFilterView vBgView;
    private ImageFilterView vStaticView;
    private ImageFilterView vDynamicView;
    private TextView vFirstTitle;
    private TextView vSecondTitle;
    private TextView vActionButton;
    private FrameLayout vLoadingLayout;
    private XmLottieAnimationView vLoadingView;
    private String xmRequestId;
    private boolean isVideoPlaying;
    private JSONObject mData;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        parentNeedBg = false;
        setStyle(DialogFragment.STYLE_NO_TITLE, com.ximalaya.ting.android.host.R.style.host_share_dialog);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_dialog_decoration_center_notify;
    }

    @Override
    protected void initUi(View view, Bundle savedInstanceState) {
        vTopClickArea = view.findViewById(R.id.main_view_space);
        vTopClickArea.setOnClickListener(this);
        vTargetChildView = view.findViewById(R.id.main_dialog_real_container);
        bindDialogView(vTargetChildView);
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        if (null != window) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;
            params.gravity = Gravity.BOTTOM;
            params.windowAnimations = R.style.host_popup_window_from_bottom_animation;
            window.setAttributes(params);
        }
    }

    @Override
    public void loadData() {
        setDataToView();
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_SHOW_FINISHED);
    }

    private void setDataToView() {
        mData = ConfigureCenter.getInstance().getJson(CConstants.Group_toc.GROUP_NAME, "decoration_center_notify_dialog_material");
        if (mData == null) {
            mData = new JSONObject();
            try {
                mData.put("title", "可以更换个性装扮啦！");
                mData.put("subTitle", "可在「更多」中进入装扮中心");
                mData.put("staticImageUrl", "https://imagev2.xmcdn.com/storages/eb1f-audiofreehighqps/86/1C/GKwRIaIMSlryAABjeAPh19Gn.png");
                mData.put("dynamicImageUrl", "https://imagev2.xmcdn.com/storages/4cd2-audiofreehighqps/A1/41/GAqh_aQMSlqbAA4b5gPh15Iy.webp");
                mData.put("buttonText", "立即体验");
                mData.put("actionUrl", "iting://open?msg_type=94&bundle=rn_dress_up_center");
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        setMovieView();
        setTitle();
        setActionButton();
    }

    private void setMovieView() {
        if (vDynamicView == null) return;
        vDynamicView.setVisibility(View.VISIBLE);
        vStaticView.setVisibility(View.VISIBLE);

        if (!TextUtils.isEmpty(mData.optString("staticImageUrl"))) {
            ImageManager.from(getContext()).displayImage(vStaticView, mData.optString("staticImageUrl"), -1);
        }
        if (!TextUtils.isEmpty(mData.optString("dynamicImageUrl"))) {
            ImageManager.from(getContext()).displayImage(vDynamicView, mData.optString("dynamicImageUrl"), -1, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (bitmap != null) {
                        vStaticView.setVisibility(View.INVISIBLE);
                    }
                }
            });
        }
    }

    private void setTitle() {
        String firstTitle = mData.optString("title", "可以更换个性装扮啦！");
        String secondTitle = mData.optString("subTitle", "可在「更多」中进入装扮中心");
        if (!TextUtils.isEmpty(firstTitle)) {
            vFirstTitle.setText(firstTitle);
            vFirstTitle.setVisibility(View.VISIBLE);
        } else {
            vFirstTitle.setVisibility(View.INVISIBLE);
        }

        if (!TextUtils.isEmpty(secondTitle)) {
            vSecondTitle.setText(secondTitle);
            vSecondTitle.setVisibility(View.VISIBLE);
        } else {
            vSecondTitle.setVisibility(View.INVISIBLE);
        }
    }

    private void setActionButton() {
        String text = mData.optString("buttonText", "立即体验");
        vActionButton.setText(text);
        vActionButton.setOnClickListener(this);
    }

    private void bindDialogView(View view) {
        if (view != null) {
            vBgView = view.findViewById(R.id.main_iv_bg);
            vStaticView = view.findViewById(R.id.main_iv_static_view);
            vDynamicView = view.findViewById(R.id.main_iv_dynamic_view);
            vFirstTitle = view.findViewById(R.id.main_tv_first_title);
            vSecondTitle = view.findViewById(R.id.main_tv_second_title);
            vActionButton = view.findViewById(R.id.main_tv_action);
            vCloseIcon = view.findViewById(R.id.main_iv_close);
            vLoadingLayout = view.findViewById(R.id.main_fl_dialog_loading_container);
            vLoadingView = view.findViewById(R.id.main_loading_view_progress_xmlottieview);
            vCloseIcon.setOnClickListener(this);
        }
    }

    public void showDialog(FragmentManager fragmentManager, String tag) {
        xmRequestId = XmRequestIdManager.getInstance(getContext()).getRequestId();
        traceDialogShow();
        MMKVUtil.getInstance().saveBoolean(KEY_DECORATION_CENTER_NOTICE_DIALOG_HAS_SHOWN, true);
        show(fragmentManager, tag);
    }


    private void traceDialogShow() {
//        if (mData == null) {
//            return;
//        }
//        // 全局-会员MOT弹窗  弹框展示
//        new XMTraceApi.Trace()
//                .setMetaId(63211)
//                .setServiceId("dialogView") // 弹窗展示时上报
//                .put("currPage", "")
//                .put("currTrackId", "" + mData.getTrackId())
//                .put("currAlbumId", "" + mData.getAlbumId())
//                .put("dialogId", mData.getPopupId())
//                .put("strategy", mData.getPolicyGroup())
//                .put(mData.getExtraContextMap("priceSensitiveEmbedPointsInfo"))
//                .put(mData.getExtraContextMap("eventTracking"))
//                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
//                .put(XmRequestIdManager.CONT_TYPE, "commercial_dialog")
//                .put(XmRequestIdManager.CONT_ID, mData.getPopupId())
//                .createTrace();
    }

    private void trackDialogBtnClick(ActionButtonInfo buttonInfo) {
//        // 全局-会员MOT弹窗-立即续费  弹框控件点击
//        new XMTraceApi.Trace()
//                .setMetaId(63212)
//                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
//                .put("currPage", "")
//                .put("currTrackId", "" + mData.getTrackId())
//                .put("currAlbumId", "" + mData.getAlbumId())
//                .put("dialogId", mData.getPopupId())
//                .put("strategy", mData.getPolicyGroup())
//                .put("actionType", "" + buttonInfo.getActionType())
//                .put("Item", item) // 例如：立即领取 | 立即去收听
//                .put(mData.getExtraContextMap("priceSensitiveEmbedPointsInfo"))
//                .put(mData.getExtraContextMap("eventTracking"))
//                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
//                .put(XmRequestIdManager.CONT_TYPE, "commercial_dialog")
//                .put(XmRequestIdManager.CONT_ID, mData.getPopupId())
//                .createTrace();
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_iv_close || id == R.id.main_view_space) {
            dismissAllowingStateLoss();
        } else if (id == R.id.main_tv_action) {
            String itingUrl = mData.optString("actionUrl", "iting://open?msg_type=94&bundle=rn_dress_up_center");
            if (getActivity() instanceof MainActivity) {
                ToolUtil.clickUrlAction((MainActivity) getActivity(), itingUrl, null);
                dismissAllowingStateLoss();
            }
        }
    }

    private void showLoading() {
        if (vTopClickArea != null) {
            vTopClickArea.setOnClickListener(null);
        }
        if (vLoadingLayout != null) {
            vLoadingLayout.setVisibility(View.VISIBLE);
        }
        if (vLoadingView != null) {
            vLoadingView.cancelAnimation();
            vLoadingView.playAnimation();
        }
    }

    private void hideLoading() {
        if (vTopClickArea != null) {
            vTopClickArea.setOnClickListener(this);
        }
        if (vLoadingLayout != null) {
            vLoadingLayout.setVisibility(View.GONE);
        }
        if (vLoadingView != null) {
            vLoadingView.cancelAnimation();
        }
    }


    @Override
    public String getDialogSource() {
        return "commercial";
    }

    @Override
    public String getBusinessId() {
        return super.getBusinessId();
    }
}
