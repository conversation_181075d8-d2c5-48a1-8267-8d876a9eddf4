package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.animation.Animator
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.listener.SimpleAnimatorListener
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.manager.freelisten.FreeListenManager
import com.ximalaya.ting.android.main.mine.extension.removeSelf
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.function.FunctionDownloadWrapperView
import com.ximalaya.ting.android.main.playpage.playy.component.function.FunctionTraceUtil
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.util.PlayPageDownloadUtils
import com.ximalaya.ting.android.main.playpage.util.PlayTtsUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created on 2024/5/30.
 * <AUTHOR>
 * @email <EMAIL>
 */
class DownloadFuncIconV2 : IBusinessView(), OnClickListener {
    private val TAG = "DownloadFuncIconV2"

    private var soundInfo: PlayingSoundInfo? = null

    private var wrapperView: FunctionDownloadWrapperView? = null
    private var badgeAnimationView: XmLottieAnimationView? = null

    override fun provideIcon(context: Context, soundInfo: PlayingSoundInfo): View? {
        val downloadView = wrapperView
        if (downloadView != null) {
            if (PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) {
                downloadView.downloadImage.alpha = 0.15f
            } else {
                downloadView.downloadImage.alpha = 0.55f
            }
            return downloadView
        }

        return FunctionDownloadWrapperView(context).apply {
            onVisibilityChanged = {
                if (badgeAnimationView != null) {
                    Logger.d(TAG, "onVisibilityChanged; badgeAnimationView current is ${badgeAnimationView?.visibility}," +
                            " target is $it; has add = ${badgeAnimationView?.parent != null}; ${badgeAnimationView?.hashCode()} ")

                    if (it == View.VISIBLE) {
                        ViewStatusUtil.setVisible(View.VISIBLE, badgeAnimationView)
                    } else {
                        ViewStatusUtil.setVisible(View.GONE, badgeAnimationView)
                    }

                } else {
                    Logger.d(TAG, "onVisibilityChanged; badgeAnimationView == null ")
                }
            }
            val drawableId = R.drawable.main_ic_download_n_n_line_regular_32
            downloadImage.setImageResource(drawableId)
            val animationJson = "lottie/play_page_doc/main_download_animation_text.json"
            animationView.setAnimation(animationJson)
            animationView.alpha = 0.55f
//            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
//                downloadImage.setColorFilter(PSkinManager.getBtnThemeColor())
//                animationView.setColorFilter(PSkinManager.getBtnThemeColor())
//            }
            animationView.addAnimatorListener(object : SimpleAnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                    manager?.playContainer?.forceShowOrHideTips(false)
                }

                override fun onAnimationEnd(animation: Animator?) {
                    manager?.playContainer?.forceShowOrHideTips(true)
                }

                override fun onAnimationCancel(animation: Animator?) {
                    manager?.playContainer?.forceShowOrHideTips(true)
                }
            })

            ViewStatusUtil.setVisible(View.GONE, animationView)

            if (PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) {
                downloadImage.alpha = 0.15f
            } else {
                downloadImage.alpha = 0.55f
            }

            layoutParams = FrameLayout.LayoutParams(26.dp, 26.dp)
            contentDescription = "下载"

            setOnClickListener(this@DownloadFuncIconV2)

            wrapperView = this
        }
    }

    private fun needHandleChildVip(
        soundInfo: PlayingSoundInfo,
    ): Boolean {
        val track = soundInfo.trackInfo2TrackM() ?: return false
        if (!ChildProtectManager.isChildCategory(track.categoryId)) {
            return false
        }
        val childAlbumSoundDownLoad = ABTest.getString("chidAlbum_soundDownLoad", "A")
        if (childAlbumSoundDownLoad != "B") {
            return false
        }
        val authorizedByUnlock = FreeListenManager.getInstance().isUnlockTrack(track)
        if (track.album?.isChildVipAlbum == true && (!track.isAuthorized || authorizedByUnlock)) {
            return true
        }
        return false
    }

    override fun providerBadgeView(context: Context, soundInfo: PlayingSoundInfo): BadgeView? {
        if (!needHandleChildVip(soundInfo)) {
            if (!PlayPageDownloadUtils.isFreeUnlockTrack()) return null
        }
        val alphaText = if (PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) 0.15f else 0.55f
        return BadgeView(
            TextView(context).apply {
                text = "vip"
                alpha = alphaText
                textSize = 8f
                isSingleLine = true
                typeface = Typeface.DEFAULT_BOLD

//                if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
//                    setTextColor(PSkinManager.getBtnThemeColor())
//                } else {
//                    setTextColor(Color.WHITE)
//                }
            }
        )
    }

    override fun providerBottomView(context: Context, soundInfo: PlayingSoundInfo): BottomView? {
        val alphaText = if (PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) 0.15f else 0.4f
        return BottomView(
            TextView(context).apply {
                text = "下载"
                alpha = alphaText
                textSize = 10f
                isSingleLine = true
            }
        )
    }

    override fun getBottomText(): String {
        return "下载"
    }

    override fun getBottomTextAlpha(): Float {
        return if (PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) 0.15f else 0.4f
    }

    override fun shouldDisplay(soundInfo: PlayingSoundInfo, isAudio: Boolean): Boolean {
        this.soundInfo = soundInfo
        return true
    }

    override fun afterUpdate(soundInfo: PlayingSoundInfo) {
        val wrapperView = wrapperView?: return
        if (wrapperView.animationView.isAnimating) {
            wrapperView.animationView.cancelAnimation()
        }

        removeBadgeAnimation()

        if (wrapperView.isShown == true && YUtils.needShowAnimationInPlayPage(soundInfo)) {
            doAnimation(wrapperView)
        }
    }

    private fun showBaderAnimation(anchor: View) {
        var targetContainer: RelativeLayout? = null
        var parent = anchor.parent

        while (parent != null && parent is ViewGroup) {
            if (parent is RelativeLayout && parent.id == R.id.main_audio_container) {
                targetContainer = parent
                break
            }
            parent = parent.parent
        }

        if (targetContainer != null) {
            removeBadgeAnimation()

            val animationView = object : XmLottieAnimationView(anchor.context) {
                override fun onAttachedToWindow() {
                    super.onAttachedToWindow()
                    playAnimation()
                }

                override fun setVisibility(visibility: Int) {
                    super.setVisibility(visibility)
                    Logger.d(TAG, "setVisibility: $visibility")
                    Logger.e(TAG, Log.getStackTraceString(Throwable()))
                }

                override fun playAnimation() {
                    super.playAnimation()
                    Logger.d(TAG, "playAnimation: ${hashCode()}")
                }
            }.apply {
                id = View.generateViewId()
                imageAssetsFolder = "lottie/images"
                setAnimation("lottie/play_page_doc/main_download_animation_pop.json")

                badgeAnimationView = this
                addAnimatorListener(object : SimpleAnimatorListener {
                    override fun onAnimationEnd(animation: Animator?) {
                        removeBadgeAnimation()
                    }

                    override fun onAnimationCancel(animation: Animator?) {
                        removeBadgeAnimation()
                    }
                })
            }
            Logger.d(TAG, "init bagde: ${animationView.hashCode()}")
            targetContainer.addView(animationView, RelativeLayout.LayoutParams(
                116.dp, 36.dp
            ).apply {
                addRule(RelativeLayout.ABOVE, R.id.main_rv_func_root)
                leftMargin = 16.dp
            })
        }
    }

    private fun removeBadgeAnimation() {
        badgeAnimationView?.removeAllAnimatorListeners()
        badgeAnimationView?.cancelAnimation()
        badgeAnimationView?.removeSelf()
        badgeAnimationView = null
    }

    private fun doAnimation(wrapper: FunctionDownloadWrapperView?) {
        val wrapperView = wrapper?: return

        if (!PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) {
            traceShowAnimation()
            showBaderAnimation(wrapperView)

            ViewStatusUtil.setVisible(View.INVISIBLE, wrapperView.downloadImage)
            ViewStatusUtil.setVisible(View.VISIBLE, wrapperView.animationView)
            wrapperView.animationView.playAnimation()
        }
    }

    private fun traceShowAnimation() {
        // 新声音播放页-下载-引导提示  控件曝光
        XMTraceApi.Trace()
            .setMetaId(65290)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put(XmRequestIdManager.CONT_ID, "${soundInfo?.trackInfo?.trackId}")
            .put(XmRequestIdManager.CONT_TYPE, "newPlayTimeCard")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .put("currTrackId", "${soundInfo?.trackInfo?.trackId}")
            .put("currAlbumId", "${soundInfo?.albumInfo?.albumId}")
            .put("description", "该声音即将下架") // 传引导文案
            .createTrace()
    }

    override fun onCreate() {
    }

    override fun onDestroy() {
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }

        if (PlayTtsUtil.isTtsOrIsTimbre(soundInfo, true)) {
            ToastManager.showToast("该声音不支持下载")
            return
        }

        doDownload()

        FunctionTraceUtil.traceClick49674(
            soundInfo?.trackInfo?.trackId,
            soundInfo?.trackInfo?.albumId,
            "下载",
            "下载",
            true
        )
    }

    private fun doDownload() {
        PlayPageDownloadUtils.doDownload()
        // 新声音播放页-下载  点击事件
        XMTraceApi.Trace()
            .click(43201) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("currAlbumId", soundInfo?.trackInfo?.trackId?.toString() ?: "0")
            .put("currTrackId", soundInfo?.trackInfo?.albumId?.toString() ?: "0")
            .put("fullScreenMode", "full") // full 表示全屏，half 表示半屏
            .put("trackForm", "音频") // track 表示音频，video 表示视频
            .apply {
                if (badgeAnimationView?.isShown == true) {
                    put("description", "该声音即将下架")
                }
            }
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .createTrace()
    }
}