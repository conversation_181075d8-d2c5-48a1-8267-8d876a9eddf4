package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by changle.fang on 2021/3/12.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class RecommendChildIpListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) :
    IMulitViewTypeViewAndDataStaggered<RecommendChildIpListAdapterProviderStaggered.ChasingViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendChildIpListAdapterProviderStaggered.ChasingViewHolder, RecommendItemNew> {

    val mContext: Context = BaseApplication.getMyApplicationContext()
    private var mOldState = RecyclerView.SCROLL_STATE_IDLE
    private var mRecommendItemNew: RecommendItemNew? = null

    override fun getView(
        layoutInflater: LayoutInflater?,
        position: Int,
        parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_child_ip_list, parent, false)
    }

    class ChasingViewHolder(convertView: View) :
        RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val tvTitle: TextView = convertView.findViewById(R.id.main_tv_chasing_title)
        val chasingListRecyclerView: RecyclerView =
            convertView.findViewById(R.id.main_rcl_chasing_list)
        val tvMore: TextView = convertView.findViewById(R.id.main_tv_more)
        var lastScrollX: Int = 0
        var uniqueId: String = ""
        var modulePosition: Int = 0
    }

    override fun createViewHolder(convertView: View?): ChasingViewHolder? {
        if (convertView == null) {
            return null
        }
        return ChasingViewHolder(convertView)
    }

    override fun bindViewHolder(
        holder: ChasingViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        mRecommendItemNew = recommendItemNew
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem !is RecommendCommonItem
            || recommendCommonItem.subElements == null || recommendCommonItem.subElements!!.isEmpty()
        ) {
            return
        }
        holder.modulePosition = position
        if (recommendItemNew.addStreamTitle == 1) {
            holder.tvTitle.visibility = View.GONE
        } else {
            holder.tvTitle.visibility = View.VISIBLE
        }
        if (RecommendFragmentTypeManager.isNewSceneCard()) {
            holder.tvTitle.setTextColor(
                ContextCompat.getColor(
                    mContext,
                    R.color.main_color_2c2c3c_ffffff
                )
            )
        } else {
            holder.tvTitle.setTextColor(
                ContextCompat.getColor(
                    mContext,
                    R.color.main_color_131313_ffffff
                )
            )
        }
        val positionNew = position
        var chasingSubElementListAdapter = ChasingSubElementListAdapter(
            fragment,
            recommendItemNew.xmRequestId,
            recommendCommonItem.subElements!!,
            recommendCommonItem,
            positionNew,
            recommendItemNew,
            dataAction
        )
        if (recommendCommonItem.landingPage.isNullOrEmpty()) {
            holder.tvMore.visibility = View.GONE
        } else {
            holder.tvMore.visibility = View.VISIBLE
        }
        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnClickListener {
            var trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem?.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem?.ubtV2)
            trace1.createTrace()
            // 新首页-儿童IP聚合卡片-更多  点击事件
            var trace = XMTraceApi.Trace()
                .click(62352) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 例如：1000000000
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem?.ubtV2,
                (position + 1).toString(),
                tab1Title = "更多",
                tab1Position = "d01",
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
            ToolUtil.clickUrlAction(fragment, recommendCommonItem.landingPage ?: "", it)
        }
        holder.tvTitle.text = recommendCommonItem.title
        holder.chasingListRecyclerView.adapter = chasingSubElementListAdapter
        holder.chasingListRecyclerView.layoutManager =
            LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
        holder.chasingListRecyclerView.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    holder.lastScrollX = recyclerView.computeHorizontalScrollOffset()
                    traceOnChildIpShow(recommendItemNew, holder)
                }
            }
        })
        if (recommendItemNew.xmRequestId != null && (recommendItemNew.xmRequestId + positionNew).equals(holder.uniqueId)) {
            holder.chasingListRecyclerView.scrollBy(holder.lastScrollX, 0)
        } else {
            holder.uniqueId = recommendItemNew.xmRequestId + positionNew
        }
    }

    private fun traceOnChildIpShow(data: RecommendItemNew,
                                   holder: ChasingViewHolder) {
        val recommendCommonList = data.item
        if (recommendCommonList !is RecommendCommonItem
            || recommendCommonList.subElements == null
            || recommendCommonList.subElements.isNullOrEmpty()
        ) {
            return
        }
        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                val childCount = holder.chasingListRecyclerView.childCount
                for (i in 0 until childCount) {
                    val view = holder.chasingListRecyclerView.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: return@postOnUiThread
                        val position = view.getTag(R.id.main_id_data_index) as? Int ?: return@postOnUiThread
                        // 新首页-儿童IP聚合卡片  控件曝光
                        val trace = XMTraceApi.Trace()
                            .setMetaId(62348)
                            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                            .put("currPage", "newHomePage")
                            .put(
                                "positionNew",
                                (position + 1).toString()
                            ) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                            .put("contentId", subElement.refId.toString()) // 客户端传。去重使用
                            .put("contentType", "childIpWall") // 客户端传。去重使用
                            .put("xmRequestId", data.xmRequestId) // 客户端传。去重使用
                            .put("modulePosition", (holder.modulePosition + 1).toString()) // 客户端传。card 在流里的位置，从上到下，从 1 开始计数
                            .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 可见区域占屏幕的比例
                        SpmTraceUtil.addSpmTraceInfo(
                            trace,
                            recommendCommonList.ubtV2,
                            (holder.modulePosition + 1).toString(),
                            tab1Title = subElement.title,
                            tab1Position = (position + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonList.ubtV2)
                        if (data.isLocalCache) {
                            trace.isLocalCache
                        }
                        trace.createTrace()

                        HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonList, subElement, view, position)
                    }
                }
            }
        }
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ChasingViewHolder?
    ) {
        if (holder == null || data == null) {
            return
        }
        val recommendCommonList = data.item
        if (recommendCommonList !is RecommendCommonItem
            || recommendCommonList.subElements == null
            || recommendCommonList.subElements.isNullOrEmpty()
        ) {
            return
        }
        if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
            // 新首页-首页大卡模块  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62177)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("xmRequestId", data.xmRequestId) // 客户端传
                .put("contentType", data.itemType) // 客户端传
                .put("contentId", recommendCommonList.id.toString()) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonList.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonList.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            trace.createTrace()
        }
        traceOnChildIpShow(data, holder)
    }

    class ChasingSubElementListAdapter(
        private var fragment: BaseFragment2,
        var xmRequestId: String?,
        var subElements: List<CommonSubElement>,
        var recommendCommonItem: RecommendCommonItem,
        var modulePosition: Int,
        var recommendItemNew: RecommendItemNew,
        val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
    ) :
        RecyclerView.Adapter<ChasingSubElementListAdapter.MyViewHolder>() {
        override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): MyViewHolder {
            val layout = R.layout.main_fra_recommend_child_ip_item_new
            return MyViewHolder(
                LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(layout, parent, false)
            )
        }

        override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
            val subElement = subElements[position] ?: return
            holder.itemView.setTag(R.id.main_id_item_data, subElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
//            if (position == itemCount - 1 && itemCount < 8) {
//                holder.itemView.setPadding(0, 0, BaseUtil.dp2px(fragment.context, 16f), 0)
//            }

            holder.tvSubElementTitle.text = subElement.title
//            if (FoldableScreenCompatUtil.lastScreenWidthDp < 375) {
//                holder.containerView.setPadding(0, 0, BaseUtil.dp2px(ToolUtil.getCtx(), 14.0f), 0)
//            } else {
//                holder.containerView.setPadding(0, 0, BaseUtil.dp2px(ToolUtil.getCtx(), 16.0f), 0)
//            }
            if ((subElement.extraInfo?.hasNew ?: 0) > 0) {
                holder.ivNewTag.visibility = View.VISIBLE
            } else {
                holder.ivNewTag.visibility = View.GONE
            }

            RecommendCornerUtils.updateTitleColor(holder.tvSubElementTitle)

            ImageManager.from(BaseApplication.getMyApplicationContext())
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    holder.ivSubElementCover,
                    subElement.cover,
                    com.ximalaya.ting.android.host.R.drawable.host_default_album,
                    R.drawable.main_recommend_item_default_bg,
                    90,
                    90
                )
            holder.itemView.setOnClickListener {
                subElement.extraInfo?.hasNew?.let {
                    subElement.extraInfo.hasNew = 0
                }
                if (subElement.landingPage.isNullOrEmpty()) {
                    return@setOnClickListener
                }
                // 大卡点击事件
                var trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", xmRequestId) // 客户端传
                SpmTraceUtil.addSpmTraceInfo(
                    trace1,
                    recommendCommonItem?.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem?.ubtV2)
                trace1.createTrace()
                // 新首页-儿童IP聚合卡片  点击事件
                var trace = XMTraceApi.Trace()
                    .click(62347) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put(
                        "positionNew",
                        (position + 1).toString()
                    ) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("xmRequestId", xmRequestId) // 客户端传。去重使用
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传。card 在流里的位置，从上到下，从 1 开始计数
                    .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(it).toString()) // 可见区域占屏幕的比例
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendCommonItem?.ubtV2,
                    (modulePosition + 1).toString(),
                    tab1Title = subElement.title,
                    tab1Position = (position + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement?.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem?.ubtV2)
                trace.createTrace()
                ToolUtil.clickUrlAction(fragment, subElement.landingPage!!, it)
            }
        }

        override fun getItemCount(): Int {
            return subElements.size
        }

        class MyViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var tvSubElementTitle: TextView
            var ivSubElementCover: ImageView
            var containerView: ConstraintLayout
            var ivNewTag: ImageView

            init {
                tvSubElementTitle =
                    view.findViewById<View>(R.id.main_tv_sub_element_title) as TextView
                ivSubElementCover = view.findViewById<View>(R.id.main_iv_item_cover) as ImageView
                containerView = view.findViewById(R.id.main_container_layout)
                ivNewTag = view.findViewById(R.id.main_iv_item_new_tag)
            }
        }
    }
}