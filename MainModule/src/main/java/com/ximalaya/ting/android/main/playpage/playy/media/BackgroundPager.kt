package com.ximalaya.ting.android.main.playpage.playy.media

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.async.AsyncBundleLoader
import com.ximalaya.ting.android.host.fragment.play.data.Theme
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment
import com.ximalaya.ting.android.main.playpage.playy.YPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.skin.SkinChangeListener
import com.ximalaya.ting.android.player.video.view.XmVideoView
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import com.ximalaya.ting.android.xmplaysdk.IRenderView
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class BackgroundPager(
    private val yPlayFragment: YPlayFragment,
    private val viewPager: ViewPager2,
    private val topViewPager: ViewPager2
): SkinChangeListener {

    private val TAG = "BackgroundPager"

    private val yPlayViewModel = ViewModelProvider(yPlayFragment.requireActivity()).get(YPlayViewModel::class.java)
    private val xPlayViewModel = ViewModelProvider(yPlayFragment.requireActivity()).get(XPlayViewModel::class.java)

    private var modes: List<PlayModeSwitcher.PlayMode> = emptyList()

    private val videoThemeColor = 0xff131313.toInt()
    private val videoTheme = Theme(videoThemeColor, videoThemeColor, videoThemeColor, videoThemeColor, videoThemeColor)

    private var lastPlayMode: PlayModeSwitcher.PlayMode? = null

    private var skinLoadJob: Job? = null
    private var mSkinVideoView: IMediaPlayerControl? = null
    private var mSkinVideoTopMaskView: View? = null
    private var mSkinVideoBottomMaskView: View? = null
    private var mSkinImg: ImageView = yPlayFragment.findViewById(R.id.main_play_background_skin_img)
    private val skinVideoContainer: FrameLayout = yPlayFragment.findViewById(R.id.main_play_background_skin_video)
    private var shouldBlurSkin = false

    private fun removeSkin() {
        skinVideoContainer.removeAllViews()

        mSkinImg.visibility = View.INVISIBLE
        mSkinImg.setImageDrawable(null)

        if (mSkinVideoView != null) {
            mSkinVideoView?.release(true)
            mSkinVideoView = null
        }
    }

    private suspend fun initSkinView() {
        val context = yPlayFragment.requireContext()
        if (PSkinManager.isEnabled) {
            if (mSkinVideoView == null) {
                kotlin.runCatching { AsyncBundleLoader.load(Configure.BUNDLE_VIDEO) }.getOrNull()?: return
                val videoPlayer = Router.getActionRouter<VideoActionRouter>(Configure.BUNDLE_VIDEO)?.functionAction?.newXmVideoView(context)?: return
                mSkinVideoView = videoPlayer
                skinVideoContainer.addView(videoPlayer.view)
                addVideoMask(skinVideoContainer, PSkinManager.getThemeColor())
            }
            val themeColor = PSkinManager.getThemeColor()
            mSkinVideoTopMaskView?.background = GradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                intArrayOf(
                    themeColor,
                    ColorUtil.changeColorAlpha(themeColor, 169),
                    Color.TRANSPARENT
                )
            )

            mSkinVideoBottomMaskView?.background = GradientDrawable(
                GradientDrawable.Orientation.BOTTOM_TOP,
                intArrayOf(
                    themeColor,
                    ColorUtil.changeColorAlpha(themeColor, 213),
                    Color.TRANSPARENT
                )
            )


            skinVideoContainer.visibility = View.VISIBLE

            mSkinVideoView?.videoPath = PSkinManager.getBackgroundVideoUrl()
            mSkinVideoView?.loop(true)
            (mSkinVideoView as? XmVideoView)?.setHandleAudioFocus(false)
            (mSkinVideoView as? XmVideoView)?.setAspectRatio(IRenderView.AR_ASPECT_FILL_PARENT)

            ImageManager.from(context).displayImage(mSkinImg, PSkinManager.getBackgroundImageUrl(), -1)
            mSkinImg.visibility = View.VISIBLE
            mSkinImg.imageAlpha = 0

            // 设置初始透明度：如果当前是第一页，视频显示，图片隐藏
            val currentPosition = viewPager.currentItem
            updateBlurView(shouldBlurSkin || currentPosition != 0)
        }
    }

    private fun addVideoMask(skinVideoContainer: FrameLayout, color: Int) {
        val topMaskView = View(skinVideoContainer.context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                142.dp
            )
            mSkinVideoTopMaskView = this
        }
        skinVideoContainer.addView(topMaskView)

        val bottomMaskView = View(skinVideoContainer.context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                270.dp,
                Gravity.BOTTOM
            )
            mSkinVideoBottomMaskView = this
        }
        skinVideoContainer.addView(bottomMaskView)
    }

    init {
        viewPager.offscreenPageLimit = 2
        topViewPager.offscreenPageLimit = 2

        yPlayViewModel.coverMainColor.observe(yPlayFragment.viewLifecycleOwner) {
            val coverTheme = yPlayViewModel.coverTheme
            val docTheme = yPlayViewModel.docTheme
            adapter.notifyDataSetChanged()
            topAdapter.notifyDataSetChanged()

            if (lastPlayMode == PlayModeSwitcher.PlayMode.COVER ||
                lastPlayMode == PlayModeSwitcher.PlayMode.SELL ||
                lastPlayMode == PlayModeSwitcher.PlayMode.DOC && docTheme == null) {
                updateTheme(PlayModeSwitcher.PlayMode.COVER)
            }
        }

        yPlayViewModel.aiGMainColor.observe(yPlayFragment.viewLifecycleOwner) {
            if (it != PlayPageDataManager.DEFAULT_BACKGROUND_COLOR) {
                val docTheme = yPlayViewModel.docTheme
                adapter.notifyDataSetChanged()
                topAdapter.notifyDataSetChanged()

                if (lastPlayMode == PlayModeSwitcher.PlayMode.DOC) {
                    updateTheme(PlayModeSwitcher.PlayMode.DOC)
                }
            } else {
                Logger.d(TAG, "aiGMainColor: domainColor DEFAULT_BACKGROUND_COLOR")
                val docTheme = yPlayViewModel.docTheme
                if (docTheme != null) {
                    adapter.notifyDataSetChanged()
                    topAdapter.notifyDataSetChanged()

                    if (lastPlayMode == PlayModeSwitcher.PlayMode.DOC) {
                        updateTheme(PlayModeSwitcher.PlayMode.COVER)
                    }
                }
            }
        }

        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                Logger.d(TAG, "onPageSelected: position = $position")
                lastPlayMode = modes.getOrNull(position)
                updateTheme(lastPlayMode)
                
                // 确保页面切换完成后的透明度状态正确
                if (PSkinManager.isEnabled && mSkinVideoView != null) {
                    updateBlurView(shouldBlurSkin || position != 0)
                }
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                
                // 处理皮肤视频和图片的透明度变化
                if (PSkinManager.isEnabled && mSkinVideoView != null) {
                    when (position) {
                        0 -> {
                            // 从第一页滑动到其他页面
                            val videoAlpha = 1f// - positionOffset
                            val imgAlpha = positionOffset
                            
                            mSkinVideoView?.view?.alpha = videoAlpha
                            mSkinImg.imageAlpha = (imgAlpha * 255).toInt()
                        }
                        else -> {
                            // 从其他页面滑动，如果下一页是第一页
                            if (position + 1 == 0) {
                                val videoAlpha = positionOffset
                                val imgAlpha = 1f - positionOffset
                                
                                mSkinVideoView?.view?.alpha = videoAlpha
                                mSkinImg.imageAlpha = (imgAlpha * 255).toInt()
                            } else {
                                // 不涉及第一页的滑动，保持视频隐藏，图片显示
                                mSkinVideoView?.view?.alpha = 0f
                                mSkinImg.imageAlpha = 255
                            }
                        }
                    }
                }
            }
        })
    }

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        skinLoadJob?.cancel()
        if (newSkinConfig != null) {
            skinLoadJob = yPlayFragment.scope().launch {
                initSkinView()
            }
        } else {
            removeSkin()
        }

        if (lastPlayMode != null) {
            updateTheme(lastPlayMode)
        }
        adapter.notifyDataSetChanged()
        topAdapter.notifyDataSetChanged()
    }

    fun blurSkin(blurSkin: Boolean) {
        shouldBlurSkin = blurSkin
        updateBlurView(blurSkin)
    }

    private fun updateBlurView(blurSkin: Boolean) {
        if (PSkinManager.isEnabled && mSkinVideoView != null) {
            if (blurSkin) {
                mSkinVideoView?.view?.alpha = 0f
                mSkinImg.imageAlpha = 255
                mSkinVideoView?.pause()
            } else {
                val currentPosition = viewPager.currentItem
                if (currentPosition == 0) {
                    mSkinVideoView?.view?.alpha = 1f
                    mSkinImg.imageAlpha = 0
                    mSkinVideoView?.start()
                } else {
                    mSkinVideoView?.view?.alpha = 0f
                    mSkinImg.imageAlpha = 255
                    mSkinVideoView?.pause()
                }
            }
        }
    }

    fun onPause() {
        if (PSkinManager.isEnabled && mSkinVideoView != null) {
            mSkinVideoView?.pause()
        }
    }

    fun onResume() {
        if (PSkinManager.isEnabled && mSkinVideoView != null) {
            val currentPosition = viewPager.currentItem
            if (currentPosition == 0) {
                mSkinVideoView?.start()
            } else {
                mSkinVideoView?.pause()
            }
        }
    }

    private fun updateTheme(mode: PlayModeSwitcher.PlayMode?) {
        if (PSkinManager.isEnabled) {
            if (PlayModeSwitcher.PlayMode.VIDEO == mode) {
                xPlayViewModel.updateMediaTheme(videoTheme, true)
            } else {
                xPlayViewModel.updateSkinTheme(PSkinManager.getThemeColor(), false)
            }
            return
        }

        when (mode) {
            PlayModeSwitcher.PlayMode.VIDEO -> {
                xPlayViewModel.updateMediaTheme(videoTheme, true)
            }
            PlayModeSwitcher.PlayMode.DOC -> {
                val docTheme = yPlayViewModel.docTheme
                if (docTheme != null) {
                    Logger.d(TAG, "updateTheme DOC; ${Integer.toHexString(docTheme!!.backgroundColor)}" )
                    xPlayViewModel.updateMediaTheme(docTheme!!, false)
                } else {
                    val coverTheme = yPlayViewModel.coverTheme
                    if (coverTheme != null) {
                        Logger.d(TAG, "updateTheme DOC with cover; ${Integer.toHexString(coverTheme!!.backgroundColor)}" )
                        xPlayViewModel.updateMediaTheme(coverTheme!!, false)
                    }
                }
            }
            else -> {
                val coverTheme = yPlayViewModel.coverTheme
                if (coverTheme != null) {
                    Logger.d(TAG, "updateTheme COVER; ${Integer.toHexString(coverTheme!!.backgroundColor)}" )
                    xPlayViewModel.updateMediaTheme(coverTheme!!, false)
                }
            }
        }
    }

    private val adapter = object : RecyclerView.Adapter<BackgroundHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BackgroundHolder {
            return BackgroundHolder(View(parent.context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                })
        }

        override fun getItemId(position: Int): Long {
            return modes[position].mode.toLong()
        }

        override fun getItemCount() = modes.size


        override fun onBindViewHolder(holder: BackgroundHolder, position: Int) {
            holder.bind(position)
        }
    }

    private val topAdapter = object : RecyclerView.Adapter<BackgroundHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BackgroundHolder {
            return BackgroundHolder(View(parent.context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            })
        }

        override fun getItemId(position: Int): Long {
            return modes[position].mode.toLong()
        }

        override fun getItemCount() = modes.size


        override fun onBindViewHolder(holder: BackgroundHolder, position: Int) {
            holder.bind(position)
        }
    }

    inner class BackgroundHolder(val view: View): RecyclerView.ViewHolder(view) {
        fun bind(position: Int) {
            when (modes[position]) {
                PlayModeSwitcher.PlayMode.VIDEO -> {
                    view.setBackgroundColor(videoThemeColor)
                }
                PlayModeSwitcher.PlayMode.DOC -> {
                    if (PSkinManager.isEnabled && PSkinManager.getThemeColor() != Color.TRANSPARENT) {
                        view.setBackgroundColor(PSkinManager.getThemeColor())
                    } else {
                        val docTheme = yPlayViewModel.docTheme
                        val coverTheme = yPlayViewModel.coverTheme
                        Logger.d(TAG, "BackgroundHolder bind; position = $position, view.hash = ${view.hashCode()}; bind doc; $docTheme" )
                        val color = docTheme?.backgroundColor?: coverTheme?.backgroundColor?: return
                        view.setBackgroundColor(color)
                    }
                }
                else -> {
                    if (PSkinManager.isEnabled && PSkinManager.getThemeColor() != Color.TRANSPARENT) {
                        view.setBackgroundColor(PSkinManager.getThemeColor())
                    } else {
                        val coverTheme = yPlayViewModel.coverTheme
                        Logger.d(TAG, "BackgroundHolder bind; position = $position, view.hash = ${view.hashCode()}; bind cover; $coverTheme" )
                        val color = coverTheme?.backgroundColor?: return
                        view.setBackgroundColor(color)
                    }
                }
            }
        }
    }

    fun update(modes: List<PlayModeSwitcher.PlayMode>, index: Int) {
        this.modes = modes

        if (viewPager.adapter == null) {
            viewPager.adapter = adapter
            topViewPager.adapter = topAdapter
        } else {
            adapter.notifyDataSetChanged()
            topAdapter.notifyDataSetChanged()

            lastPlayMode = modes.getOrNull(index)
            updateTheme(lastPlayMode)
        }

        if (index >= 0 && index < modes.size) {
            viewPager.setCurrentItem(index, false)
            topViewPager.setCurrentItem(index, false)
        }
    }
}