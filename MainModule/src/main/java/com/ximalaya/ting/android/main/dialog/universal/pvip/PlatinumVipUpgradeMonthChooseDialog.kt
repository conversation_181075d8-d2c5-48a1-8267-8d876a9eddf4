package com.ximalaya.ting.android.main.dialog.universal.pvip

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem.UpgradeProductModel
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger

/**
 * <AUTHOR>
 * @time 2024/9/6 15:40
 * @description: 免付一体化转化弹窗
 */
class PlatinumVipUpgradeMonthChooseDialog(
    private val vipSkuShelfInfo: VipSkuShelfInfo,
    private val source: String,
    private val vipSkuItem: VipSkuItem,
    private val chosenPair: Pair<VipSkuItem, UpgradeProductModel?>?,
    private val maxHeight: Int
) : BaseLoadDialogFragment(), View.OnClickListener {

    companion object {
        val TAG = PlatinumVipUpgradeMonthChooseDialog::class.java.simpleName
        val ACTION = "PLATINUM_VIP_UPGRADE_MONTH_CHOOSE"
        val KEY_QUANTITY = "KEY_QUANTITY"

        @JvmStatic
        fun showDialog(
            vipSkuShelfInfo: VipSkuShelfInfo,
            source: String,
            vipSkuItem: VipSkuItem,
            chosenPair: Pair<VipSkuItem, UpgradeProductModel?>?,
            maxHeight: Int,
            fragmentManager: FragmentManager
        ) {
            val dialog = PlatinumVipUpgradeMonthChooseDialog(
                vipSkuShelfInfo,
                source,
                vipSkuItem,
                chosenPair,
                maxHeight
            )
            dialog.showDialog(fragmentManager, TAG)
        }
    }

    private var vTargetChildView: View? = null
    private var vTopClickArea: View? = null
    private var vCloseIcon: ImageView? = null
    private var vTitle: TextView? = null
    private var vRecyclerView: EpoxyRecyclerView? = null
    private var xmRequestId: String? = null
    private var mData = mutableListOf<UpgradeProductModel>()
    private var mSelectedPosition = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parentNeedBg = false
        setStyle(
            DialogFragment.STYLE_NO_TITLE,
            com.ximalaya.ting.android.host.R.style.host_share_dialog
        )
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_dialog_platinum_vip_upgrade_month_choose
    }

    override fun initUi(view: View, savedInstanceState: Bundle?) {
        if (vipSkuItem.upgradeProductVos.isNullOrEmpty()) {
            dismissAllowingStateLoss()
            return
        }
        mData.addAll(vipSkuItem.upgradeProductVos)
        if (chosenPair?.first == vipSkuItem) {
            mSelectedPosition = mData.indexOfFirst {
                it == chosenPair.second
            }
        }
        if (mSelectedPosition < 0) {
            mSelectedPosition = 0
        }
        Logger.i("mark112231","initUI:p:$mSelectedPosition")
        vTopClickArea = view.findViewById(R.id.main_view_space)
        vTopClickArea?.setOnClickListener(this)
        vTargetChildView =
            view.findViewById(R.id.main_dialog_real_container)
        bindDialogView(vTargetChildView)
        traceDialogShow()
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        if (null != window) {
            val params = window.attributes
            params.width = WindowManager.LayoutParams.MATCH_PARENT
            params.height = WindowManager.LayoutParams.MATCH_PARENT
            params.gravity = Gravity.BOTTOM
            params.windowAnimations =
                com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation
            window.attributes = params
        }
    }

    public override fun loadData() {
    }

    override fun onDestroy() {
        super.onDestroy()
        HighValueFireworkManager.updateState(HighValueFireworkManager.STATE_SHOW_FINISHED)
    }

    private fun setDataToView() {
        vRecyclerView?.withModels {
            mData.forEachIndexed { index, upgradeProduct ->
                add(
                    PlatinumVipUpgradeMonthEModel(
                        upgradeProduct, index, mSelectedPosition
                    ) { chooseMonth ->
                        val position = mData.indexOfFirst { it == chooseMonth }
                        Logger.i("mark112231","click:p:$position")
                        Logger.i("mark112231","click:se:$mSelectedPosition")
                        Logger.i("mark112231","click:upgradeProduct:${upgradeProduct.text} ,chosen:${chooseMonth.text}")
                        if (position != mSelectedPosition) {
                            traceItemClick(chooseMonth.text, position)
                            mSelectedPosition = position
                            notifyChoose(chooseMonth)
                            dismissAllowingStateLoss()
                        }
                    }.id("upgradeMonthItem_$index")
                )
            }
        }
    }

    fun bindDialogView(view: View?) {
        if (view != null) {
            vTitle = view.findViewById(R.id.main_tv_title)
            vRecyclerView = view.findViewById(R.id.main_rv_list)
            vCloseIcon = view.findViewById(R.id.main_iv_close)
            vCloseIcon?.setOnClickListener(this)
            vRecyclerView?.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            val calcHeight = mData.size * 50.dp + 64.dp
            if (maxHeight in 1 until calcHeight) {
                vRecyclerView?.updateLayoutParams<RelativeLayout.LayoutParams> {
                    height = maxHeight - 64.dp
                }
            }
            setDataToView()
            if (mSelectedPosition > 4) {
                vRecyclerView?.smoothScrollToPositionWithOffset(mSelectedPosition, 100.dp)
            }
            view.visibility = View.VISIBLE
        }
    }

    fun showDialog(fragmentManager: FragmentManager, tag: String) {
        xmRequestId = XmRequestIdManager.getInstance(context).requestId
        show(fragmentManager, tag)
    }

    override fun onClick(v: View) {
        val id = v.id
        if (id == R.id.main_iv_close || id == R.id.main_view_space) {
            traceItemClick("关闭")
            dismissAllowingStateLoss()
        }
    }

    private fun traceDialogShow() {
        // 统一付费半浮层-升级浮层  控件曝光
        XMTraceApi.Trace()
            .setMetaId(67819)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "")
            .put("xmRequestId", xmRequestId)
            .put("tabName", "platinumVip")
            .put("trackId", "" + vipSkuShelfInfo.trackId)
            .put("albumId", "" + vipSkuShelfInfo.albumId)
            .put(
                "sceneName",
                source
            ) // 后端透传product 自制页专辑购买 productTrackList 自制页声音列表 productMiddleBar 自制页中插条 albumTrackList 专辑页声音列表 newPlayAfterSample 新播放页试听结束 playAfterSample 老播放页试听结束 afterSample 全局试听结束 playBeforeSample 新老播放页小黄条 adLocking 广告页
            .put(XmRequestIdManager.CONT_TYPE, "upgradeMonth")
            .put(XmRequestIdManager.CONT_ID, "0")
            .createTrace()
    }

    private fun traceItemClick(item: String, position: Int? = null) {
        // 统一付费半浮层-升级浮层  弹框控件点击
        XMTraceApi.Trace()
            .setMetaId(67820)
            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
            .put("currPage", null)
            .put("xmRequestId", xmRequestId)
            .put("tabName", "platinumVip")
            .put("Item", item) // 例如：升级 200 天|升级 1 个月|关闭
            .put("trackId", "" + vipSkuShelfInfo.trackId)
            .put("albumId", "" + vipSkuShelfInfo.albumId)
            .put(
                "sceneName",
                source
            ) // 后端透传product 自制页专辑购买 productTrackList 自制页声音列表 productMiddleBar 自制页中插条 albumTrackList 专辑页声音列表 newPlayAfterSample 新播放页试听结束 playAfterSample 老播放页试听结束 afterSample 全局试听结束 playBeforeSample 新老播放页小黄条 adLocking 广告页
            .put("positionNew", "${position ?: -1}") // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put(XmRequestIdManager.CONT_TYPE, "upgradeMonth")
            .put(XmRequestIdManager.CONT_ID, "${position ?: -1}")
            .createTrace()
    }

    private fun notifyChoose(chosenItem: UpgradeProductModel) {
        val intent = Intent()
        intent.action = ACTION
        intent.putExtra(BundleKeyConstants.KEY_ITEM_ID, chosenItem.itemId)
        intent.putExtra(KEY_QUANTITY, chosenItem.quantity)
        LocalBroadcastManager.getInstance(ToolUtil.getCtx()).sendBroadcast(intent)
    }

    override fun getDialogSource(): String {
        return "commercial"
    }

    override fun getBusinessId(): String {
        return "platinum_vip_upgrade_month_choose"
    }

}