package com.ximalaya.ting.android.main.playpage.playy.component.audio.dialog;

import static com.ximalaya.ting.android.main.playpage.util.PlayTtsUtil.TYPE_A_PLUS_TIMBRE;

import android.content.DialogInterface;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseScrollDialogfragment;
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback;
import com.ximalaya.ting.android.host.manager.PlanTerminateManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteManager;
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.play.LrcManager;
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.listenlist.TingListInfoModel;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.play.TrackQualityInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.read.manager.TingTextToReaderManager;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil;
import com.ximalaya.ting.android.host.util.common.IScreenConfigChangedListener;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.AnchorSpaceUtil;
import com.ximalaya.ting.android.main.dialog.commercial.DecorationCenterNotifyDialog;
import com.ximalaya.ting.android.main.playModule.quality.PlayQualityCountDownTextView;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.IAdSoundPatchMoreService;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.SoundPatchMoreAdComponent;
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageSkinDownloadManager;
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer;
import com.ximalaya.ting.android.main.playpage.playy.YUtils;
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher;
import com.ximalaya.ting.android.main.playpage.playy.utils.DanmakuUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.main.view.LinearItemDecoration;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.HttpParamsConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlanTerminateListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.ToListenUtil;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by ZhuPeipei on 2020-05-25 20:18.
 *
 * @Description: 播放页更多弹框
 */
public class YPlayMoreActionDialogFragment extends BaseScrollDialogfragment {
    private final static String TAG = "PlayMoreActionDialogFragment";

    private LinearLayout mRootLay;
    private RecyclerView mHeaderRv;
    private PlayQualityCountDownTextView mQualityCountDown;
    private YPlayMoreActionHeaderAdapter mHeaderAdapter;
    public PlayingSoundInfo mSoundInfo;
    private RefreshLoadMoreListView mMoreListView;
    private YPlayMoreActionAdapter mMoreActionAdapter;
    private final List<YPlayMoreActionAdapter.MoreActionTag> mListData = new ArrayList<>();
    private List<YPlayMoreActionAdapter.MoreActionTag> mPassedData;
    public boolean mIsShowing = true;

    private IPlayContainer mPlayContainer;

    // 音质信息
    private List<TrackQualityInfo> currentTrackQualityInfo;

    // 主播店铺 区域
    private ImageView mAnchorIv;
    private TextView mAnchorNameTv;
    private TextView mAnchorFollowTv;
    private ImageView mIvAlbumCover;
    private TextView mAlbumNameTv;
    private TextView mAlbumSubscribeTv;
    private ImageView mShoppingIv;
    private TextView mShoppingTv;
    private View mShoppingContainer;
    private boolean mIsChildProtectMode;
    private View mAnchorContainer;
    private View mAlbumContainer;

    private View mMoreAdView;   // 更多广告位 广告

    private ViewStub mMoreAdViewStub;

    private LinearLayout mMoreAdLayout;

    private ClickCallback mClickCallback;

    private IAdSoundPatchMoreService mSoundPatchMoreAdComponentService; // 声音流+更多广告的Component

    private static final String KEY_SHOW_WX_SHARE = "KEY_SHOW_WX_SHARE";
    private static final String KEY_SHOW_ALBUM_FAV = "KEY_SHOW_ALBUM_FAV";


    public static YPlayMoreActionDialogFragment newInstance() {
        Bundle args = new Bundle();
        YPlayMoreActionDialogFragment fragment = new YPlayMoreActionDialogFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable @org.jetbrains.annotations.Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mSoundInfo == null) {
            mSoundInfo = PlayPageDataManager.getInstance().getSoundInfo();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        // 新声音播放页_更多  弹框展示
        boolean isPodcast = ToListenUtil.isPlayPagePodCastPlayListMode(BaseApplication.getMyApplicationContext());
        new XMTraceApi.Trace()
                .setMetaId(38337)
                .setServiceId("dialogView")
                .put("isPodcast", String.valueOf(isPodcast))
                .put("currPage", "新声音播放页")
                .createTrace();
    }

    /**
     * 带入操作项列表，会替代默认操作项
     */
    public static YPlayMoreActionDialogFragment newInstanceWithTags(
            Boolean showWxShare,
            Boolean showAlbumFav,
            List<YPlayMoreActionAdapter.MoreActionTag> tags) {
        Bundle args = new Bundle();
        args.putBoolean(KEY_SHOW_WX_SHARE, showWxShare);
        args.putBoolean(KEY_SHOW_ALBUM_FAV, showAlbumFav);
        YPlayMoreActionDialogFragment fragment = new YPlayMoreActionDialogFragment();
        fragment.setArguments(args);
        fragment.setPassedData(tags);
        return fragment;
    }

    @Override
    public float contentRatio() {
        return 0.8f;
//        int height = BaseUtil.getScreenHeight(getContext());
//        return (height - BaseUtil.dp2px(getContext(), 150)) * 1f / height;
    }

    @Override
    public void initUi() {
        mRootLay = findViewById1(R.id.main_frag_play_more_lay);
        mMoreAdViewStub = findViewById1(R.id.main_operate_float_lay);
        mHeaderRv = findViewById1(R.id.main_play_more_action_header_rv);
        mQualityCountDown = findViewById1(R.id.main_play_quality_count_down);

        boolean showWx = getArguments() != null && getArguments().getBoolean(KEY_SHOW_WX_SHARE, false);
        boolean showFav = getArguments() != null && getArguments().getBoolean(KEY_SHOW_ALBUM_FAV, false);
        mHeaderAdapter = new YPlayMoreActionHeaderAdapter(showWx, showFav, this, mSoundInfo);
        mHeaderRv.setAdapter(mHeaderAdapter);
        mHeaderRv.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        int defaultSpanCount = 3;
        if (mHeaderAdapter.getItemCount() > 1) {
            defaultSpanCount = (mHeaderAdapter.getItemCount() - 1);
        }
        int margin = (BaseUtil.getScreenWidth(getContext()) -
                BaseUtil.dp2px(getContext(), 16 * 2 + 54 * (defaultSpanCount + 1))) / defaultSpanCount;
        decoration.setMargin(BaseUtil.dp2px(getContext(), 16));
        decoration.setHalfSpacing(margin / 2);
        mHeaderRv.addItemDecoration(decoration);
        mMoreListView = findViewById1(R.id.main_play_more_action_lv);
        initHeaderView();
        mMoreActionAdapter = new YPlayMoreActionAdapter(this, mListData, mSoundInfo);
        mMoreListView.setAdapter(mMoreActionAdapter);
        mMoreListView.setMode(PullToRefreshBase.Mode.DISABLED);
        mMoreListView.setHasMoreNoFooterView(false);
        mMoreListView.setAllHeaderViewColor(Color.WHITE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            mMoreListView.getRefreshableView().setBackground(null);
        }

        AnchorFollowManage.getSingleton().addFollowListener(followAnchorListener);
        FoldableScreenCompatUtil.INSTANCE.addListener("YPlayMoreActionDialogFragment", listener);
    }

    private AnchorFollowManage.IFollowAnchorListener followAnchorListener = (uid, follow) -> {
        if (mSoundInfo != null && mSoundInfo.trackInfo != null && mSoundInfo.trackInfo.uid == uid) {
            setAnchorStatus(follow);
        }
    };

    public boolean isDanmakuOpen() {
        return mSoundInfo != null && DanmakuUtil.INSTANCE.isDanmakuOpen(mSoundInfo);
    }

    private void initQualityCountDown(int index, int margin) {
        if (0 > index) {
            ViewStatusUtil.setVisible(View.GONE, mQualityCountDown);
            return;
        }
        int dp50 = BaseUtil.dp2px(getContext(), 50);
        int dp10 = BaseUtil.dp2px(getContext(), 10);
        int maxWidth = dp50 + margin - dp10;
        int leftPosition = BaseUtil.dp2px(getContext(), 24) + (margin + dp50) * index + dp10;
        if (null != mQualityCountDown) {
            mQualityCountDown.setMaxWidth(maxWidth);
            ViewGroup.LayoutParams layoutParams = mQualityCountDown.getLayoutParams();
            if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin = leftPosition;
            }
        }
    }

    private void initHeaderView() {
        View headView = LayoutInflater.from(getContext())
                .inflate(R.layout.main_play_more_action_header_anchor_view,
                        mMoreListView.getRefreshableView(), false);
        headView.setPadding(0, 0, 0, 0);
        mAnchorContainer = headView.findViewById(R.id.main_play_more_action_ll_header_anchor);
        mAnchorIv = headView.findViewById(R.id.main_play_more_action_lv_header_anchor_iv);
        mAnchorNameTv = headView.findViewById(R.id.main_play_more_action_lv_header_anchor_tv);
        mAnchorFollowTv = headView.findViewById(R.id.main_play_more_action_lv_header_anchor_follow_tv);
        mAlbumContainer = headView.findViewById(R.id.main_play_more_action_ll_header_album);
        mAlbumNameTv = headView.findViewById(R.id.main_play_more_action_lv_header_track_tv);
        mIvAlbumCover = headView.findViewById(R.id.main_iv_album_cover);
        mAlbumSubscribeTv = headView.findViewById(R.id.main_play_more_action_lv_header_track_subscribe_tv);
        mShoppingContainer = headView.findViewById(R.id.main_play_more_action_lv_header_shopping_container);
        mShoppingIv = headView.findViewById(R.id.main_play_more_action_lv_header_shopping_iv);
        mShoppingTv = headView.findViewById(R.id.main_play_more_action_lv_header_shopping_tv);

        mMoreListView.getRefreshableView().addHeaderView(headView);

        mAnchorFollowTv.setOnClickListener(v -> doFollow());
        mAlbumSubscribeTv.setOnClickListener(v -> doSubscribe());
        mAnchorContainer.setOnClickListener(v -> {
            if (!OneClickHelper.getInstance().onClick(v)) {
                return;
            }
            toAnchorPage();
        });
        mAlbumContainer.setOnClickListener(v -> {
            if (!OneClickHelper.getInstance().onClick(v)) {
                return;
            }
            toAlbumPage();
        });

        mMoreAdLayout = headView.findViewById(R.id.main_ll_ad_sound_patch_more);

        if (mSoundPatchMoreAdComponentService == null) {
            mSoundPatchMoreAdComponentService = PlayPageInternalServiceManager.getInstance().getService(IAdSoundPatchMoreService.class);
        }

        if (mSoundPatchMoreAdComponentService != null) {
            mMoreAdLayout.setVisibility(View.VISIBLE);
            mSoundPatchMoreAdComponentService.bindViewWithListener(mMoreAdLayout, new AdItemClickListener(this));
        } else {
            mMoreAdLayout.setVisibility(View.GONE);
        }
    }

    private static class AdItemClickListener implements SoundPatchMoreAdComponent.ItemClickListener {

        private WeakReference<YPlayMoreActionDialogFragment> weakReference;

        public AdItemClickListener(YPlayMoreActionDialogFragment fragment) {
            weakReference = new WeakReference<>(fragment);
        }

        @Override
        public void onClick() {
            if (weakReference != null && weakReference.get() != null) {
                weakReference.get().myDismiss();
            }
        }
    }


    @Override
    public void onDestroyView() {
        if (mSoundPatchMoreAdComponentService != null) {
            mSoundPatchMoreAdComponentService.unBindView();
        }
        AnchorFollowManage.getSingleton().removeFollowListener(followAnchorListener);
        FoldableScreenCompatUtil.INSTANCE.removeListener("YPlayMoreActionDialogFragment");
        super.onDestroyView();
    }

    @Override
    public void loadData() {
        if (!canUpdateUi()) {
            return;
        }
        if (PlayPageDataManager.getInstance().getSoundInfo() != null) {
            mSoundInfo = PlayPageDataManager.getInstance().getSoundInfo();
        }

        if (mSoundInfo == null || mSoundInfo.trackInfo == null) {
            return;
        }
        TrackM track = mSoundInfo.trackInfo2TrackM();
        if (track == null) {
            return;
        }

        // 头部刷新
        updateHeader(track, null);

        // 更新专辑条状态
        updateAlbumContainerView();

        // 主播 店铺刷新
        updateLvHeaderView(mSoundInfo);

        // 操作区刷新
        updateAdapterView(track);

        // 广告展示
//        updateAd();

        // 会员音质权益倒计时展示
        //loadAndUpdatePlayQuality(mSoundInfo);
        if (mSoundPatchMoreAdComponentService == null) {
            mSoundPatchMoreAdComponentService = PlayPageInternalServiceManager.getInstance().getService(IAdSoundPatchMoreService.class);
        }
        if (mSoundPatchMoreAdComponentService != null) {
            mMoreAdLayout.setVisibility(View.VISIBLE);
            mSoundPatchMoreAdComponentService.loadSoundPatchMoreData(true);
        } else {
            mMoreAdLayout.setVisibility(View.GONE);
        }

        initTimerOff();
    }

    private void initTimerOff() {
        int leftTime = PlanTerminateManager.getLastLeftTime();
        if (leftTime > 0) {
            if (mMoreActionAdapter != null) {
                mMoreActionAdapter.updateTimerOffUi(leftTime, false);
            }
        } else {
            if (mMoreActionAdapter != null) {
                mMoreActionAdapter.updateTimerOffUi(leftTime, true);
            }
        }
    }

    private void updateAlbumContainerView() {
        boolean isDecoupleTrack = PlayCommentUtil.isDecoupleTrack(mSoundInfo);
        if (mAlbumContainer != null) {
            mAlbumContainer.setVisibility(isDecoupleTrack ? View.GONE : View.VISIBLE);
        }
    }

    private void checkChildProtectMode() {
        boolean mode = ChildProtectManager.isChildProtectOpen(getContext());
        if (mIsChildProtectMode == mode) {
            return;
        }
        mIsChildProtectMode = mode;

        if (mIsChildProtectMode) {
            mShoppingContainer.setVisibility(View.GONE);
        } else {
            mShoppingContainer.setVisibility(View.VISIBLE);
        }
    }

    private void updateHeader(TrackM track, List<TrackQualityInfo> qualityInfos) {
        mHeaderAdapter.updateHeadItem(mSoundInfo);
        mHeaderAdapter.notifyDataSetChanged();

        // TrackPlayQualityManager.getInstance().resetQualityOnUserIsNotVip();

        //MoreActionHeaderAdapter.HeaderTag.HighQuality.setImg(getTrackPlayQualityImg(track, qualityInfos));
    }

    private void updateLvHeaderView(PlayingSoundInfo soundInfo) {
        updateAnchor(soundInfo);
        updateAlbumInfo(soundInfo);
        updateShoppView();
    }

    private void updateAnchor(PlayingSoundInfo soundInfo) {
        if (soundInfo == null) {
            return;
        }
        String logo = null;
        PlayingSoundInfo.UserInfo userInfo = soundInfo.userInfo;
        if (userInfo != null) {
            logo = userInfo.smallLogo;
            mAnchorNameTv.setText(userInfo.nickname);
        }
        ImageManager.from(getContext()).displayImage(mAnchorIv, logo, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);
        if (userInfo != null && userInfo.uid == UserInfoMannage.getUid()) {
            mAnchorFollowTv.setVisibility(View.INVISIBLE);
        } else {
            mAnchorFollowTv.setVisibility(View.VISIBLE);
            boolean isFollow = false;
            if (soundInfo.otherInfo != null) {
                isFollow = soundInfo.otherInfo.isFollowed;
            }
            setAnchorStatus(isFollow);
        }
    }

    private void setAnchorStatus(boolean isFollow) {
        if (isFollow) {
            mAnchorFollowTv.setVisibility(View.GONE);
//            mAnchorFollowTv.setText("已关注");
//            mAnchorFollowTv.setSelected(true);
//            mAnchorFollowTv.setCompoundDrawables(null, null, null, null);
        } else {
            mAnchorFollowTv.setText("关注");
            mAnchorFollowTv.setSelected(false);
            mAnchorFollowTv.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.main_album_icon_add, 0, 0, 0);
        }
        mAnchorFollowTv.setContentDescription(isFollow ? "取消关注" : "关注");
    }

    private void updateAlbumInfo(PlayingSoundInfo soundInfo) {
        if (soundInfo == null) {
            return;
        }
        TrackM track = soundInfo.trackInfo2TrackM();
        if (track == null) {
            return;
        }
        if (soundInfo.albumInfo != null && !TextUtils.isEmpty(soundInfo.albumInfo.title)) {
            mAlbumNameTv.setText(soundInfo.albumInfo.title);
        } else if (track.getAlbum() != null) {
            mAlbumNameTv.setText(track.getAlbum().getAlbumTitle());
        }
        PlayingSoundInfo.AlbumInfo album = soundInfo.albumInfo;
        if (album == null) {
            return;
        }
        ImageManager.from(getContext()).displayImage(mIvAlbumCover, album.getValidCoverUrl(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
        setAlbumSubscribeStatus(album.isFavorite);
    }

    private void setAlbumSubscribeStatus(boolean isFavorite) {
        if (isFavorite) {
            mAlbumSubscribeTv.setVisibility(View.GONE);
//            mAlbumSubscribeTv.setSelected(true);
//            mAlbumSubscribeTv.setText("已订阅");
//            mAlbumSubscribeTv.setCompoundDrawables(null, null, null, null);
        } else {
            mAlbumSubscribeTv.setSelected(false);
            mAlbumSubscribeTv.setText("订阅");
            mAlbumSubscribeTv.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.main_album_icon_add, 0, 0, 0);
        }
        mAlbumSubscribeTv.setContentDescription(isFavorite ? "取消订阅" : "订阅");
    }

    private void updateShoppView() {
        // todo 不做这期

    }

    private void updateAdapterView(TrackM track) {
        mListData.clear();
        if (ToolUtil.isEmptyCollects(mPassedData)) {
            mListData.addAll(Arrays.asList(YPlayMoreActionAdapter.MoreActionTag.values()));
        } else {
            mListData.addAll(mPassedData);
        }

        if (!YUtils.hasValidAnchorShopUrl(mSoundInfo)) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.AnchorShop);
        }

        if (isHideGameEntry()) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.LoteGame);
        }

        if (isHideAdFreeEntry()) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.FreeAd);
        }

        if (mSoundInfo != null && mSoundInfo.otherInfo != null && !mSoundInfo.otherInfo.showContentSurveyEntrance) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.SurveyContent);
        }

        if ((track.getType() == Track.TYPE_TTS_A_PLUS && track.getTimbreType() == TYPE_A_PLUS_TIMBRE)
                || track.getType() == Track.TYPE_TTS || track.getTimbreType() == TYPE_A_PLUS_TIMBRE) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.Download);
        }

        if (!PlayCompleteManager.INSTANCE.useYMode()) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.PlayRecommend);
        }

        if (mSoundInfo != null && mSoundInfo.albumInfo != null && !mSoundInfo.albumInfo.isPodcastAlbum) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.MonthTicket);
        }

        if (!LrcManager.getInstance().hasTranslateLrc()) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.Translate);
        }

        if (!ConstantsOpenSdk.isDebug) {
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.TipsDemo);
        }
        if (PlayPageSkinDownloadManager.isConfigOn()) {
            String decorationCenterABValue = ABTest.getString("vip_decoration_entry", "0");
            if (ConstantsOpenSdk.isDebug) {
                decorationCenterABValue = ToolUtil.getDebugSystemProperty("debug.mark.decoration", decorationCenterABValue);
            }
            if (!"1".equals(decorationCenterABValue)) {
                mListData.remove(YPlayMoreActionAdapter.MoreActionTag.DecorationCenter);
                DecorationCenterNotifyDialog.recordCanShowDecorationCenterNotifyDialog(false);
            } else {
                DecorationCenterNotifyDialog.recordCanShowDecorationCenterNotifyDialog(true);
                JSONObject extraInfo = ConfigureCenter.getInstance().getJson(CConstants.Group_toc.GROUP_NAME, "vip_decoration_entry_extra_info");
                if (extraInfo != null) {
                    YPlayMoreActionAdapter.MoreActionTag.DecorationCenter.setTitle(extraInfo.optString("title", "装扮中心"));
                    Pair<String, String> extraPair = new Pair<>(extraInfo.optString("subTitle", ""), extraInfo.optString("url", "iting://open?msg_type=94&bundle=rn_dress_up_center"));
                    YPlayMoreActionAdapter.MoreActionTag.DecorationCenter.setExtraData(extraPair);
                }
            }
        }
        boolean showFav = getArguments() != null && getArguments().getBoolean(KEY_SHOW_ALBUM_FAV, false);
        //边听边看
        if (!(getParentFragment() instanceof BaseFragment2) || !showFav && !TingTextToReaderManager.isTingTextToReaderEnable(mSoundInfo)
        ) {
            // 因为头部要展示听单，所以这里需要隐藏听单
            mListData.remove(YPlayMoreActionAdapter.MoreActionTag.AddTingList);
        }

        String text = ConfigureCenter.getInstance().getString("tob", "tort_text", "版权申诉");
        YPlayMoreActionAdapter.MoreActionTag.CopyRight.setTitle(text);
        mMoreActionAdapter.notifyDataSetChanged();
    }

    // 展示广告
    private void updateAd() {
        loadOperateFloatAd();
    }

    private void loadAndUpdatePlayQuality(PlayingSoundInfo soundInfo) {
        if (null == mHeaderAdapter || 0 > mHeaderAdapter.getHighQualityIndex()) {
            currentTrackQualityInfo = null;
            if (null != mHeaderAdapter) {
                mHeaderAdapter.setPlayQualityInfo(0, null);
            }
            updatePlayQuality();
            return;
        }
        if (soundInfo.trackInfo != null) {
            long fCurrentTrackId = soundInfo.trackInfo.trackId;
            CommonRequestM.getPlayPageQualityList(soundInfo.trackInfo.trackId, new IDataCallBack<List<TrackQualityInfo>>() {
                private final WeakReference<YPlayMoreActionDialogFragment> fragmentWeakReference = new WeakReference<>(YPlayMoreActionDialogFragment.this);

                @Override
                public void onSuccess(@Nullable List<TrackQualityInfo> data) {
                    YPlayMoreActionDialogFragment dialog = fragmentWeakReference.get();
                    if (null == dialog) {
                        return;
                    }
                    dialog.updateHeader(null, data);
                    if (!ToolUtil.isEmptyCollects(data)) {
                        dialog.currentTrackQualityInfo = data;
                        if (null != dialog.mHeaderAdapter) {
                            dialog.mHeaderAdapter.setPlayQualityInfo(fCurrentTrackId, data);
                        }
                    } else {
                        dialog.currentTrackQualityInfo = null;
                        if (null != dialog.mHeaderAdapter) {
                            dialog.mHeaderAdapter.setPlayQualityInfo(0, null);
                        }
                    }

                    dialog.updatePlayQuality();
                }

                @Override
                public void onError(int code, String message) {
                    Logger.i(TAG, "get quality list failed " + code + " " + message);
                    YPlayMoreActionDialogFragment dialog = fragmentWeakReference.get();
                    if (null == dialog) {
                        return;
                    }
                    dialog.currentTrackQualityInfo = null;
                    if (null != dialog.mHeaderAdapter) {
                        dialog.mHeaderAdapter.setPlayQualityInfo(0, null);
                    }
                    dialog.updatePlayQuality();
                }
            });
        } else {
            currentTrackQualityInfo = null;
            if (null != mHeaderAdapter) {
                mHeaderAdapter.setPlayQualityInfo(0, null);
            }
            updatePlayQuality();
        }
    }

    private void updatePlayQuality() {
        List<TrackQualityInfo> qualityInfos = currentTrackQualityInfo;
        if (null == mQualityCountDown) {
            return;
        }
        if (UserInfoMannage.isVipUser() || null == qualityInfos) {
            ViewStatusUtil.setVisible(View.GONE, mQualityCountDown);
            markPointOnPlayQualityBtnShown(false);
            return;
        }
        long expiredDuration = -1;
        long localTimeStamp = -1;
        for (TrackQualityInfo qualityInfo : qualityInfos) {
            if (null == qualityInfo) {
                continue;
            }
            if (qualityInfo.getEnjoying()) {
                expiredDuration = qualityInfo.getExpireDate();
                localTimeStamp = qualityInfo.getLocalBaseTimeStamp();
                break;
            }
        }
        if (0 >= expiredDuration) {
            ViewStatusUtil.setVisible(View.GONE, mQualityCountDown);
            markPointOnPlayQualityBtnShown(false);
            return;
        }
        mQualityCountDown.prePare(this, expiredDuration * 1000L, localTimeStamp, new PlayQualityCountDownTextView.ICountDownEndCallBack() {
            private final WeakReference<View> toVanishAreaReference = new WeakReference<>(mQualityCountDown);

            @Override
            public void onEnd() {
                ViewStatusUtil.setVisible(View.GONE, toVanishAreaReference.get());
            }
        });
        boolean isSuccess = mQualityCountDown.startCountDown();
        ViewStatusUtil.setVisible(isSuccess ? View.VISIBLE : View.GONE, mQualityCountDown);
        markPointOnPlayQualityBtnShown(true);
    }

    /**
     * @param isEnjoining 是否在体验中
     */
    private void markPointOnPlayQualityBtnShown(boolean isEnjoining) {
        // 新声音播放页-更多面板  控件曝光
        long trackId = null == mSoundInfo ? 0 : (null == mSoundInfo.trackInfo ? 0 : mSoundInfo.trackInfo.trackId);
        long albumId = null == mSoundInfo ? 0 : (null == mSoundInfo.trackInfo ? 0 : mSoundInfo.trackInfo.albumId);
        new XMTraceApi.Trace()
                .setMetaId(44130)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currTrackId", "" + trackId)
                .put("currAlbumId", "" + albumId)
                .put("status", "" + isEnjoining)
                .put(XmRequestIdManager.CONT_ID, "" + trackId)
                .put(XmRequestIdManager.CONT_TYPE, "newPlayMore")
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace();
    }

    /**
     * 更多广告
     */
    private void loadOperateFloatAd() {
        resertMoreAd();
        Map<String, String> map = new HashMap<>();
        map.put("sourceId", PlayTools.getCurTrackId(getContext()) + "");
        map.put("sourcePage", "1");

        Track curTrack = PlayTools.getCurTrack(getContext());

        if (curTrack != null) {
            map.put(Advertis.FIELD_PAGE_MODE,
                    XmAdsManager.getSoundPlayStyle(curTrack) + "");
        }

        map.put(HttpParamsConstantsInOpenSdk.PARAM_AD_VERSION, AdManager.getAdPlayVersion());

        AdRequest.getMoreAd(map, new IDataCallBack<List<Advertis>>() {
            @Override
            public void onSuccess(List<Advertis> object) {
                if (!isAddFix()) {
                    return;
                }

                if (ToolUtil.isEmptyCollects(object)) {
                    resertMoreAd();
                    return;
                }

                final Advertis advertis = object.get(0);
                if (advertis != null) {
                    if (!TextUtils.isEmpty(advertis.getImageUrl())) {
                        ImageManager.from(getContext()).downloadBitmap(advertis.getImageUrl(),
                                new ImageManager.DisplayCallback() {
                                    @Override
                                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                                        setMoreAdView(advertis);
                                    }
                                }, false);
                    } else {
                        resertMoreAd();
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                resertMoreAd();
            }
        });
    }

    private void setMoreAdView(Advertis advertis) {
        if (advertis == null) {
            return;
        }

        if (mMoreAdView == null) {
            mMoreAdView = mMoreAdViewStub.inflate();
        }

        if (mMoreAdView != null) {
            ImageView adCover = (ImageView) mMoreAdView.findViewById(com.ximalaya.ting.android.host.R.id.host_share_ad_cover);
            ImageView subAdCover = (ImageView) mMoreAdView.findViewById(com.ximalaya.ting.android.host.R.id.host_share_ad_sub_cover);
            ImageView adMask = (ImageView) mMoreAdView.findViewById(com.ximalaya.ting.android.host.R.id.host_share_ad_mark);
            ImageManager.from(getContext()).displayImage(adCover, advertis.getImageUrl(), -1);
            ImageManager.from(getContext()).displayImage(subAdCover, advertis.getSubCover(), -1);
            adMask.setVisibility(View.VISIBLE);
            ImageManager.from(getContext()).displayImage(adMask, advertis.getAdMark(),
                    com.ximalaya.ting.android.host.R.drawable.host_ad_tag_bg_4c000000, 0,
                    BaseUtil.dp2px(getContext(), AdManager.AD_MASK_HEIGHT));

            mMoreAdView.setVisibility(View.VISIBLE);

            if (AdManager.canClick(advertis)) {
                mMoreAdView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        myDismiss();

                        AdManager.handlerAdClick(getContext(), advertis,
                                new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        AppConstants.AD_POSITION_NAME_MORE_OPERATE)
                                        .sourcePage(AdManager.SHARE_AD_SOURCE_PAGE_SOUND)
                                        .sourceId(PlayTools.getCurTrackId(getContext()) + "").build());
                    }
                });
            } else {
                mMoreAdView.setOnClickListener(null);
            }

            AdManager.adRecord(getContext(), advertis,
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                            AppConstants.AD_POSITION_NAME_MORE_OPERATE)
                            .sourcePage(AdManager.SHARE_AD_SOURCE_PAGE_SOUND)
                            .sourceId(PlayTools.getCurTrackId(getContext()) + "").build());
        }
    }

    private void resertMoreAd() {
        if (mMoreAdView != null) {
            mMoreAdView.setVisibility(View.GONE);
        }
    }

    private void doFollow() {
        if (mSoundInfo == null
                || mSoundInfo.otherInfo == null
                || mSoundInfo.userInfo == null) {
            return;
        }
        boolean isCurrentFollowed = mSoundInfo.otherInfo.isFollowed;
        AnchorFollowManage.followV3WithLoginCheckDeferredAction(getActivity(),
                mSoundInfo.userInfo.uid,
                isCurrentFollowed,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
                5,
                new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(Boolean object) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (object == null || mSoundInfo == null
                                || mSoundInfo.otherInfo == null) {
                            return;
                        }
                        mSoundInfo.otherInfo.isFollowed = object;
                        if (object) {
                            CustomToast.showSuccessToast("关注成功");
                        }
                        setAnchorStatus(object);
                    }

                    @Override
                    public void onError(int code, String message) {
                    }
                }, true, true, "newPlay");

        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .click(17635)
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(getTrackId()))
                .put("currAlbumId", String.valueOf(getAlbumId()))
                .put("anchorId", String.valueOf(getUid()))
                .put("categoryId", String.valueOf(getCategoryId()))
                .put("item", isCurrentFollowed ? "取消关注" : "关注")
                .put("bizType", "" + AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE)
                .put("subBizType", "5");
        IXPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IXPlayFragmentService.class);
        if (service != null) {
            trace.put("trackForm", service.isVideoMode() ? "video" : "track");
        }
        trace.createTrace();
    }

    private void doSubscribe() {
        if (mSoundInfo == null || mSoundInfo.toAlbumM() == null) {
            return;
        }
        if (!(getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        AlbumM album = mSoundInfo.toAlbumM();
        AlbumCollectParam param = new AlbumCollectParam("newPlay",
                AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_AUDIO_PLAY_50002,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE, 8,
                album, true);
        param.setShowFollowDialog(false);
        AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(param, (BaseFragment2) getParentFragment(), new ICollectWithFollowStatusCallback() {

            @Override
            public int getFollowSubBizType() {
                return 8;
            }

            @Override
            public void followDialogAction(int status) {

            }

            @Override
            public void onCollectSuccess(int code, boolean isCollected) {
                if (!canUpdateUi()) {
                    return;
                }
                if (mSoundInfo != null && mSoundInfo.albumInfo != null) {
                    mSoundInfo.albumInfo.isFavorite = isCollected;
                }
                album.setFavorite(isCollected);
                setAlbumSubscribeStatus(isCollected);

                if (isCollected && AlbumEventManage.getAutoFollow()) {
                    if (mSoundInfo != null && mSoundInfo.otherInfo != null) {
                        mSoundInfo.otherInfo.isFollowed = true;
                        updateLvHeaderView(mSoundInfo);
                    }
                }
            }

            @Override
            public void onError() {

            }
        }, "newPlay");

        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .click(17636)
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(getTrackId()))
                .put("currAlbumId", String.valueOf(getAlbumId()))
                .put("anchorId", String.valueOf(getUid()))
                .put("categoryId", String.valueOf(getCategoryId()))
                .put("Item", album.isFavorite() ? "取消订阅" : "订阅");
        IXPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IXPlayFragmentService.class);
        if (service != null) {
            trace.put("trackForm", service.isVideoMode() ? "video" : "track");
        }
        trace.createTrace();
    }

    private void toAnchorPage() {
        if (ChildProtectManager.isChildProtectOpen(getContext())) {
            ChildProtectManager.showFeatureCannotUseToast();
            return;
        }
        if (mSoundInfo == null || mSoundInfo.trackInfo2TrackM() == null) {
            return;
        }
        TrackM track = mSoundInfo.trackInfo2TrackM();
        if (!(getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        myDismiss();
        if (track.getUid() > 0) {
            ((BaseFragment2) getParentFragment()).startFragment(AnchorSpaceUtil.newAnchorSpaceFragment(track.getUid()));
        } else if (track.getAnnouncer() != null && track.getAnnouncer().getAnnouncerId() > 0) {
            ((BaseFragment2) getParentFragment()).startFragment(AnchorSpaceUtil.newAnchorSpaceFragment(track.getAnnouncer().getAnnouncerId()));
        }
        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .setSrcModule("主播条")
                .setItemId(track.getUid())
                .setItem("user")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);

        new XMTraceApi.Trace()
                .click(17634)
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(getTrackId()))
                .put("currAlbumId", String.valueOf(getAlbumId()))
                .put("anchorId", String.valueOf(getUid()))
                .put("categoryId", String.valueOf(getCategoryId()))
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace();
    }

    private void toAlbumPage() {
        if (mSoundInfo == null || mSoundInfo.albumInfo == null) {
            return;
        }
        myDismiss();
        final PlayingSoundInfo.AlbumInfo albumInfo = mSoundInfo.albumInfo;
        if (albumInfo.isPaid && albumInfo.priceTypeId == 2) {
            AlbumEventManage.startMatchAlbumFragment(albumInfo.albumId, AlbumEventManage
                            .FROM_ALBUM_BELONG,
                    ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM, null, null, -1, getActivity());
        } else {
            if (!(getParentFragment() instanceof BaseFragment2)) {
                return;
            }
            // 因为售后页可以存在统一商详页的形式，所以不再直接跳转至专辑页
            AlbumEventManage.startMatchAlbumFragment(albumInfo.albumId, AlbumEventManage.FROM_ALBUM_BELONG,
                    ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM, null, null, -1, BaseApplication.getMainActivity());

            /*((BaseFragment2) getParentFragment()).startFragment(
                    AlbumFragmentNew.newInstance(
                            albumInfo.title,
                            albumInfo.albumId,
                            AlbumEventManage.FROM_ALBUM_BELONG,
                            ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM));*/
        }
        if (mSoundInfo.trackInfo == null) {
            return;
        }
        // 新声音播放页-更多面板-专辑条  点击事件
        new XMTraceApi.Trace()
                .click(51718) // 用户点击时上报
                .put("currPage", "newPlay")
                .put("currAlbumId", String.valueOf(getAlbumId()))
                .put("currTrackId", String.valueOf(getTrackId()))
                .put("anchorId", String.valueOf(getUid())) // 当前声音对应的主播id 专辑
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .put("categoryId", String.valueOf(getCategoryId()))
                .createTrace();
        new UserTracking().setSrcPage("track").setSrcPageId(mSoundInfo.trackInfo.trackId).
                setSrcModule("专辑条").setItem("album").setItemId(albumInfo.albumId).
                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);
    }

    @Override
    public void onResume() {
        super.onResume();
        long curTrackId = PlayPageDataManager.getInstance().getCurTrackId();
        if (mSoundInfo == null || mSoundInfo.trackInfo == null || curTrackId != mSoundInfo.trackInfo.trackId) {
            Logger.d(TAG, "onResume loaddata " + curTrackId);
            loadData();
        } else if (PlanTerminateManager.isTimerContinuePlaying()) {
            initTimerOff();
        }
        XmPlayerManager.getInstance(getContext()).addPlayerStatusListener(mPlayerStatusListener);
        XmPlayerManager.getInstance(getContext()).addPlanTerminateListener(mPlanTerminateListener);
        checkChildProtectMode();
    }

    @Override
    public void onPause() {
        super.onPause();
        XmPlayerManager.getInstance(getContext()).removePlayerStatusListener(mPlayerStatusListener);
        XmPlayerManager.getInstance(getContext()).removePlanTerminateListener(mPlanTerminateListener);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_dialog_frag_xplay_more_action;
    }

    @Override
    public View getInnerScrollView() {
        return mMoreListView;
    }

    private long getTrackId() {
        PlayingSoundInfo info = mSoundInfo;
        if (info == null) {
            return -1;
        }
        TrackM track = info.trackInfo2TrackM();
        if (track == null) {
            return -1;
        }
        return track.getDataId();
    }

    private long getAlbumId() {
        PlayingSoundInfo info = mSoundInfo;
        if (info == null) {
            return -1;
        }
        AlbumM album = info.toAlbumM();
        if (album == null) {
            return -1;
        }
        return album.getId();
    }

    private int getCategoryId() {
        PlayingSoundInfo info = mSoundInfo;
        if (info == null) {
            return -1;
        }
        TrackM track = info.trackInfo2TrackM();
        if (track == null) {
            return -1;
        }
        return track.getCategoryId();
    }

    private long getUid() {
        PlayingSoundInfo info = mSoundInfo;
        if (info == null) {
            return -1;
        }
        TrackM track = info.trackInfo2TrackM();
        if (track == null) {
            return -1;
        }
        return track.getUid();
    }

    @Override
    protected void statCancel() {
        new XMTraceApi.Trace()
                .setMetaId(38340)
                .setServiceId("dialogClick")
                .put("item", "取消")
                .put("currPage", "新声音播放页")
                .createTrace();
    }

    private void myDismiss() {
        dismiss();

        new XMTraceApi.Trace()
                .click(17638)
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(getTrackId()))
                .put("currAlbumId", String.valueOf(getAlbumId()))
                .put("anchorId", String.valueOf(getUid()))
                .put("categoryId", String.valueOf(getCategoryId()))
                .put("Item", "取消")
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace();
    }

    private @DrawableRes
    int getTrackPlayQualityImg(Track track, List<TrackQualityInfo> qualityInfos) {
        int trackQualityLevel = TrackPlayQualityManager.getInstance().getTrackPlayQualityLevel();
        if (trackQualityLevel != Track.TRACK_QUALITY_SMART_MODE) {
            boolean hasSelectQuality = false;
            List<Integer> qualityList = new ArrayList<>();
            if (mSoundInfo != null && mSoundInfo.trackQualities != null) {
                // 此处感觉不会再起作用
                for (TrackQualityInfo trackQualityInfo : mSoundInfo.trackQualities) {
                    if (trackQualityInfo != null && trackQualityInfo.isValid()) {
                        if (trackQualityInfo.getQualityLevel() == trackQualityLevel && trackQualityInfo.getCanChoose()) {
                            hasSelectQuality = true;
                            break;
                        }
                        if (trackQualityInfo.getCanChoose()) {
                            qualityList.add(trackQualityInfo.getQualityLevel());
                        }
                    }
                }

                // 没有所选择的音质，则做降级，找不到则设为智能选择
                if (!hasSelectQuality) {
                    if (!qualityList.isEmpty()) {
                        Collections.sort(qualityList);
                        for (int i = qualityList.size() - 1; i >= 0; i--) {
                            Integer qualityLevel = qualityList.get(i);
                            if (qualityLevel <= trackQualityLevel) {
                                trackQualityLevel = qualityLevel;
                                hasSelectQuality = true;
                                break;
                            }
                        }
                    }
                }
            } else if (null != qualityInfos) {
                for (TrackQualityInfo trackQualityInfo : qualityInfos) {
                    if (trackQualityInfo != null && trackQualityInfo.isValid()) {
                        if (trackQualityInfo.getQualityLevel() == trackQualityLevel && trackQualityInfo.getCanChoose()) {
                            hasSelectQuality = true;
                            break;
                        }
                        if (trackQualityInfo.getCanChoose()) {
                            qualityList.add(trackQualityInfo.getQualityLevel());
                        }
                    }
                }

                // 没有所选择的音质，则做降级，找不到则设为智能选择
                if (!hasSelectQuality) {
                    if (!qualityList.isEmpty()) {
                        Collections.sort(qualityList);
                        for (int i = qualityList.size() - 1; i >= 0; i--) {
                            Integer qualityLevel = qualityList.get(i);
                            if (qualityLevel <= trackQualityLevel) {
                                trackQualityLevel = qualityLevel;
                                hasSelectQuality = true;
                                break;
                            }
                        }
                    }
                }
            }
            if (!hasSelectQuality) {
                trackQualityLevel = Track.TRACK_QUALITY_SMART_MODE;
            }
        }
        switch (trackQualityLevel) {
            case Track.TRACK_QUALITY_SMART_MODE:
                return R.drawable.main_play_action_quality_auto;
            case Track.TRACK_QUALITY_NORMAL:
                return R.drawable.main_play_action_quality_standard;
            case Track.TRACK_QUALITY_HIGH_PLUS:
                return R.drawable.main_play_action_quality_ultra;
            case Track.TRACK_QUALITY_LOSSLESS:
                return R.drawable.main_play_action_quality_loss_less;
            default:
                return R.drawable.main_play_action_quality_high;
        }
    }

    private IXmPlayerStatusListener mPlayerStatusListener = new IXmPlayerStatusListener() {
        @Override
        public void onPlayStart() {

        }

        @Override
        public void onPlayPause() {

        }

        @Override
        public void onPlayStop() {

        }

        @Override
        public void onSoundPlayComplete() {

        }

        @Override
        public void onSoundPrepared() {

        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            if (canUpdateUi()) {
                loadData();
            }
        }

        @Override
        public void onBufferingStart() {

        }

        @Override
        public void onBufferingStop() {

        }

        @Override
        public void onBufferProgress(int percent) {

        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return false;
        }
    };

    /**
     * 是否隐藏 边听边玩入口， 默认隐藏
     */
    private boolean isHideGameEntry() {

        // 配置中心下发的不展示边听边玩入口专辑频道id
        JSONObject jsonObject = ConfigureCenter.getInstance().getJson(
                CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_AD_GAME_ENTRY_BLACK_CATEGORY);
        Set<String> blackCategorySet = new HashSet<>();
        if (jsonObject != null) {
            JSONArray blackCategoryIds = jsonObject.optJSONArray("blackCategoryIds");
            if (blackCategoryIds != null) {
                for (int i = 0; i < blackCategoryIds.length(); i++) {
                    blackCategorySet.add(blackCategoryIds.optString(i));
                }
            }
        }
        /**
         * 隐藏入口条件：
         *   1.边听边玩开关关闭
         *   2.或者 android < 23(< 23 版本sdk 初始化会crash),
         *   3.或者 是青少年模式
         *   4.当前频道是在配置中心黑名单中
         */
        return (!ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_PLAY_GAME_POPUP, false))
                || Build.VERSION.SDK_INT < Build.VERSION_CODES.M
                || ChildProtectManager.isChildMode(getContext())
                || (blackCategorySet != null && blackCategorySet.contains(String.valueOf(getCategoryId())));
    }

    /**
     * 是否隐藏免广告入口， 默认隐藏
     *
     * @return
     */
    private boolean isHideAdFreeEntry() {
        try {
            // 有前插视频 或者 其他广告数据时 展示免广告入口
            List<Advertis> advertisList = XmPlayerManager.getInstance(getContext()).getForwardAdvertis();
            if (advertisList == null) {
                AdvertisList curAdvertis = XmPlayerManager.getInstance(getContext()).getCurSoundAdList();
                if (advertisList != null) {
                    advertisList = curAdvertis.getAdvertisList();
                }
            }
            Logger.i("-------msg ", " ------- isHideAdFreeEntry advertisList = " + advertisList);
            return ToolUtil.isEmptyCollects(advertisList) || advertisList.get(0) == null;
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 设置更多显示的项目
     */
    public void setPassedData(List<YPlayMoreActionAdapter.MoreActionTag> data) {
        mPassedData = data;
    }

    public void setPlayContainer(IPlayContainer playContainer) {
        mPlayContainer = playContainer;
    }

    public void setClickCallback(ClickCallback callback) {
        mClickCallback = callback;
    }

    public ClickCallback getClickCallback() {
        return mClickCallback;
    }

    public interface ClickCallback {
        void onRewardClicked();

        void onAddTingListSuccess(TingListInfoModel tingListInfoModel);

        void onDanMuClick(boolean open);
    }

    private final IXmPlanTerminateListener mPlanTerminateListener = new IXmPlanTerminateListener() {
        @Override
        public void onTimeout(int type) throws RemoteException {
            if (mMoreActionAdapter != null) {
                mMoreActionAdapter.updateTimerOffUi(0, true);
            }
        }

        @Override
        public void onLeftTimeChanged(int leftTime, int type) throws RemoteException {
            if (mMoreActionAdapter != null) {
                mMoreActionAdapter.updateTimerOffUi(leftTime, false);
            }
        }

        @Override
        public void onLeftSeriesChanged(int series, int type) throws RemoteException {

        }

        @Override
        public void onCancel() throws RemoteException {
            if (mMoreActionAdapter != null) {
                mMoreActionAdapter.updateTimerOffUi(0, true);
            }
        }

        @Override
        public IBinder asBinder() {
            return null;
        }
    };

    public void onDanMuSwitchOpen(boolean open) {
        if (mClickCallback != null) {
            mClickCallback.onDanMuClick(open);
        }
    }

    public void updateComponent() {
        if (mPlayContainer != null) {
            mPlayContainer.updateCoverComponents();
        }
    }

    public void onDocSwitchChange(boolean docShow) {
        if (mPlayContainer != null) {
            mPlayContainer.onDocSwitchChange(docShow);
        }
    }

    public PlayModeSwitcher getPlayModeSwitcher() {
        if (mPlayContainer != null) {
            return mPlayContainer.getPlayModeSwitcher();
        }
        return null;
    }

    public IPlayContainer getPlayContainer() {
        return mPlayContainer;
    }


    @Override
    public void show(FragmentManager manager, String tag) {
        super.show(manager, tag);
        mIsShowing = true;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        mIsShowing = true;
        return super.show(transaction, tag);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mIsShowing = false;
    }

    public void onSoundInfoLoaded() {
        if (canUpdateUi()) {
            loadData();
        }
    }

    private LinearItemDecoration decoration = new LinearItemDecoration();

    private IScreenConfigChangedListener listener = new IScreenConfigChangedListener() {
        @Override
        public void onScreenWidthChanged(@NonNull Configuration config) {
            if (mHeaderRv != null && mHeaderAdapter != null) {

                int defaultSpanCount = 3;
                if (mHeaderAdapter.getItemCount() > 1) {
                    defaultSpanCount = (mHeaderAdapter.getItemCount() - 1);
                }

                int width = BaseUtil.dp2px(getContext(), config.screenWidthDp);
                int margin = (width - BaseUtil.dp2px(getContext(), 16 * 2 + 54 * (defaultSpanCount + 1))) / defaultSpanCount;
                decoration.setHalfSpacing(margin / 2);
                mHeaderAdapter.notifyDataSetChanged();
                mHeaderRv.requestLayout();
            }
        }
    };

    public void recoveryTheme() {
        if (mPlayContainer != null) {
            mPlayContainer.clearPicTheme();
        }
    }
}
