package com.ximalaya.ting.android.main.playpage.playy.skin

data class PSkinConfig(
    val name: String, // 皮肤名称
    val coverVagueImage: String,    // 完整文件路径
    val uiColorInt: Int,            // 颜色值 int
    val coverImage: String,         // 完整文件路径
    val progressSlider: String,     // 完整文件路径
    val moveBackground: String,     // 完整文件路径
    val maskLayerColorInt: Int,     // 颜色值 int
    val playButton: String,          // 完整文件路径
    val cycleCoverBackground: String? = null, // 封面皮肤配置
)
