package com.ximalaya.ting.android.main.playpage.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Shader;
import android.os.Looper;
import android.text.DynamicLayout;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.lrcview.LrcEntry;
import com.ximalaya.ting.android.host.view.lrcview.WordInfoModel;
import com.ximalaya.ting.android.main.manager.TempoManager;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by {jian.kang} on 12/13/22
 *
 * <AUTHOR>
 */
public class AiDocAndTtsHalfScreenLrcViewV2 extends View {
    private String TAG = "AILrcViewV2";
    private static final long TIMER_FREQURENCY = 100L;
    private float mActiveTextSize;
    private float mNormalTextSize;
    private float mOverTextSize;
    private float mPreOverTextSize;
    private int mActiveTextColor;
    private int mNormalTextColor;
    private float mDividerHeight;
    private float mLineSpacingMult;
    private float mLineSpacingAdd;

    private Paint mFadeMaskPaint;
    private int mFadeMaskHeight;
    private int mBottomFadeMaskHeight;
    private Paint mFadeBottomMaskPaint;

    private Context mContext;

    private int mCurrentLine = 0;
    private List<LrcEntry> lrcEntryList;
    private long mCurrentTime = 0;

    private int mShowLineCounts = 5;

    private ValueAnimator xValueAnimator;
    private long mXDuration = 250; // x轴做动画时的时间
    private int mXOffset = 0;
    private int mLastXOffset = 0;
    private int mFirstLineHight = 0;

    private ValueAnimator yValueAnimator;
    private int mYOffset = 0;
    private long mYDuration = 250; // x轴做动画时的时间

    private boolean mIsShow = true;
    private float mTempo = 1f;
    private IOnLayoutListener mLayoutChangeListener;

    private boolean mIsDragging = false;
    private long mLastTime = 0;
    private boolean mDragFromBackToFront;

    public AiDocAndTtsHalfScreenLrcViewV2(@NonNull Context context) {
        super(context);
    }

    public AiDocAndTtsHalfScreenLrcViewV2(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public AiDocAndTtsHalfScreenLrcViewV2(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(@Nullable AttributeSet attrs) {
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        mContext = BaseApplication.getMyApplicationContext().getApplicationContext();
        mActiveTextColor = Color.parseColor("#ffffffff");
        mNormalTextColor = Color.parseColor("#80ffffff");
        mActiveTextSize = BaseUtil.dp2px(getContext(), 20);
        mNormalTextSize = BaseUtil.dp2px(getContext(), 16);
        mDividerHeight = BaseUtil.dp2px(getContext(), 20);
        mLineSpacingMult = 1f;
        mLineSpacingAdd = 0f;
        mOverTextSize = mNormalTextSize;
        mPreOverTextSize = mActiveTextSize;
        mTempo = TempoManager.getInstance().getCurrentTempo();

        mFadeMaskHeight = BaseUtil.dp2px(getContext(), 57);
        mBottomFadeMaskHeight = BaseUtil.dp2px(getContext(), 110);
        mFadeMaskPaint = new Paint();

        mFadeMaskPaint.setShader(new LinearGradient(0, 0, 0, mFadeMaskHeight, 0x00000000, 0xff000000, Shader.TileMode.CLAMP));

        mFadeMaskPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));

        mFadeBottomMaskPaint = new Paint();
        mFadeBottomMaskPaint.setShader(new LinearGradient(0, 0, 0, mBottomFadeMaskHeight, 0xffffffff, 0x00ffffff, Shader.TileMode.CLAMP));
        mFadeBottomMaskPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));
    }

    public float getMaxTextSize() {
        return Math.max(mNormalTextSize, mActiveTextSize);
    }

    public float getLineSpacingMult() {
        return mLineSpacingMult;
    }

    public float getLineSpacingAdd() {
        return mLineSpacingAdd;
    }

    public List<LrcEntry> getLrcEntryList() {
        return lrcEntryList;
    }

    public void setDividerHeight(int dividerHeight) {
        mDividerHeight = dividerHeight;
    }

    private boolean showShortLine;
    private boolean onlyOneLine = false;

    public void showTwoLine(boolean showShortLine) {
        this.showShortLine = showShortLine;
        if (!showShortLine) {
            this.onlyOneLine = false;
        }

        if (yValueAnimator != null && yValueAnimator.isRunning()) {
            yValueAnimator.cancel();
        }
        invalidate();
    }

    public void setOnlyOneLine(boolean onlyOneLine) {
        this.onlyOneLine = onlyOneLine;
        if (yValueAnimator != null && yValueAnimator.isRunning()) {
            yValueAnimator.cancel();
        }
        invalidate();
    }

    public boolean isTwoLine() {
        return showShortLine;
    }


    public int getDynamicLayoutWidth() {
        int margin = 0;
        if (getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            margin = ((ViewGroup.MarginLayoutParams) getLayoutParams()).leftMargin
                    + ((ViewGroup.MarginLayoutParams) getLayoutParams()).rightMargin;
        }
        return BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext()) - margin;
    }

    public void setTimeFrequency(long duration) {
        mXDuration = duration;
        resetXOffset();
    }

    public void setCurrentTextSize(float size) {
        mActiveTextSize = size;
        invalidate();
    }

    public void setNormalTextSize(float size) {
        mNormalTextSize = size;
        invalidate();
    }

    public void setShowLineCounts(int counts) {
        mShowLineCounts = counts;
        invalidate();
    }

    public void setIsDragging(boolean isDragging) {
        mIsDragging = isDragging;
    }

    public void setEntryList(List<LrcEntry> entryList) {
        if (lrcEntryList == null) {
            lrcEntryList = new ArrayList<>();
        }
        lrcEntryList.clear();
        lrcEntryList.addAll(entryList);
        initLrcEntry(entryList);
    }

    private void initLrcEntry(List<LrcEntry> entryList) {
        if (entryList == null || entryList.size() <= 0) {
            return;
        }

        for (LrcEntry entry : entryList) {
            if (entry == null) {
                continue;
            }

            TextPaint textPaint = new TextPaint();
            textPaint.setAntiAlias(true);
            textPaint.setTextAlign(Paint.Align.LEFT);
            textPaint.setColor(mActiveTextColor);
            textPaint.setTextSize(mActiveTextSize);

            entry.initDynamicLayout(textPaint, getDynamicLayoutWidth(), LrcEntry.GRAVITY_CENTER, false, mLineSpacingMult, mLineSpacingAdd);
        }
    }

    /**
     * 在主线程中运行
     */
    private void runOnUi(Runnable r) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            r.run();
        } else {
            post(r);
        }
    }

    private Runnable mDrawLightLineTask = new Runnable() {
        @Override
        public void run() {
            if (!mIsShow || lrcEntryList == null || lrcEntryList.size() <= 0 || mIsDragging) {
                HandlerManager.removeCallbacks(this);
                return;
            }
            updateExactTime(mCurrentTime);
            if (XmPlayerManager.getInstance(mContext).isPlaying()) {
                HandlerManager.postOnUIThreadDelay(mDrawLightLineTask, TIMER_FREQURENCY);
                mCurrentTime += (TIMER_FREQURENCY * mTempo);
            }
        }
    };

    public void cancelUpdateTimeTask() {
        if (mDrawLightLineTask != null) {
            HandlerManager.removeCallbacks(mDrawLightLineTask);
        }
    }

    public void updateTime(long time, boolean isDrag) {
        if (!mIsShow || lrcEntryList == null || lrcEntryList.size() <= 0) {
            return;
        }
        mCurrentTime = time;
        // 记录当前时间，用于判断用户拖动进度条是向前拖动还是向后拖动
        mLastTime = mCurrentTime;
        // 正在拖动进度条时，不滚动
        if (mIsDragging) {
            HandlerManager.removeCallbacks(mDrawLightLineTask);
            return;
        }
        if (isDrag) {
            updateExactTime(mCurrentTime);
        } else {
            HandlerManager.removeCallbacks(mDrawLightLineTask);
            HandlerManager.postOnUIThreadDelay(mDrawLightLineTask, TIMER_FREQURENCY);
        }
    }

    // 更精确的时间，播放器1秒一个回调，有点慢
    private void updateExactTime(long time) {
        runOnUi(new Runnable() {
            @Override
            public void run() {
                int curLine = findShowLine(time);
                // 需要换行的时候，向上做翻滚动画
                if (curLine != mCurrentLine) {
                    doYOffsetAnimation(curLine);
                    resetXOffset();
                } else {
                    int xOffset = findLineXOffset();
                    doXOffsetAnimation(xOffset);
                }
            }
        });
    }

    // 找到当前句中的x偏移量
    private int findLineXOffset() {
        if (lrcEntryList == null || lrcEntryList.size() <= 0 || mCurrentLine >= lrcEntryList.size()) {
            return 0;
        }

        LrcEntry entry = lrcEntryList.get(mCurrentLine);
        if (entry == null || entry.getWordInfoModels() == null || entry.getWordInfoModels().size() <= 0) {
            return 0;
        }

        DynamicLayout dynamicLayout = entry.getDynamicLayout();
        if (dynamicLayout == null) {
            return 0;
        }
        try {
            List<WordInfoModel> models = entry.getWordInfoModels();
            int length = models.size();
            for (int index = length - 1; index >= 0; index--) {
                WordInfoModel word = models.get(index);
                if (word == null) {
                    continue;
                }

                if (index == 0) {
                    if (word.start <= mCurrentTime && word.end >= mCurrentTime) {
                        return getXOffset(word, index, entry.getDynamicLayout(), index == length - 1);
                    } else if (word.start > mCurrentTime) {
                        return 0;
                    }
                } else if (index == length - 1) {
                    if (models.get(index - 1) != null && models.get(index - 1).end < mCurrentTime && mCurrentTime <= word.end) {
                        return getXOffset(word, index, entry.getDynamicLayout(), true);
                    }
                } else {
                    if (models.get(index - 1) != null && models.get(index - 1).end < mCurrentTime && mCurrentTime <= word.end) {
                        return getXOffset(word, index, entry.getDynamicLayout(), false);
                    }
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return getWidth();
    }

    private int getXOffset(WordInfoModel word, int index, DynamicLayout dynamicLayout, boolean isLast) {
        if (word == null || dynamicLayout == null) {
            return mLastXOffset;
        }

        if (index == 0) {
            if (mCurrentTime > word.start) {
                float proportion = ((float) (mCurrentTime - word.start)) / (float) word.duration;
                int wordStartOffset = getWordStartXOffset(index, dynamicLayout);
                int wordEndOffset = getWordEndXOffset(index, dynamicLayout, isLast);
                return wordStartOffset + (int) ((wordEndOffset - wordStartOffset) * proportion);
            } else {
                return 0;
            }
        } else {
            // 改字之前有停顿，也就是两个字之间有时间缝隙
            if (mCurrentTime - word.start < 0) {
                return getWordStartXOffset(index, dynamicLayout);
            } else {
                float proportion = ((float) (mCurrentTime - word.start)) / (float) word.duration;
                int wordStartOffset = getWordStartXOffset(index, dynamicLayout);
                int wordEndOffset = getWordEndXOffset(index, dynamicLayout, isLast);
                return wordStartOffset + (int) ((wordEndOffset - wordStartOffset) * proportion);
            }
        }
    }

    // 获取当前字中距离textView的x偏移，字内的距离计算平均值
    private int getWordStartXOffset(int index, DynamicLayout dynamicLayout) {
        return (int) dynamicLayout.getPrimaryHorizontal(index);
    }

    private int getWordEndXOffset(int index, DynamicLayout dynamicLayout, boolean isLast) {
        if (isLast) {
            return getWidth();
        } else {
            return (int) dynamicLayout.getPrimaryHorizontal(index + 1);
        }
    }

    private void doXOffsetAnimation(int offset) {
        // 防止x轴高亮动画来回抽搐
        if (offset <= mLastXOffset) {
            return;
        }
        if (xValueAnimator == null) {
            xValueAnimator = new ValueAnimator();
        }
        xValueAnimator.cancel();
        xValueAnimator.removeAllUpdateListeners();
        xValueAnimator.setIntValues(mLastXOffset, offset);
        mLastXOffset = offset;
        xValueAnimator.setDuration(mXDuration);
        xValueAnimator.addUpdateListener(animation -> {
            if (animation != null && animation.getAnimatedValue() instanceof Integer) {
                mXOffset = (int) animation.getAnimatedValue();
                invalidate();
            }
        });
        xValueAnimator.start();
    }

    private void doYOffsetAnimation(int currentLine) {
        mOverTextSize = mNormalTextSize;
        mPreOverTextSize = mActiveTextSize;
        if (yValueAnimator == null) {
            yValueAnimator = new ValueAnimator();
        }
        if (yValueAnimator.isRunning()) {
            return;
        }
        if (mIOnLrcShowLineChange != null && getVisibility() == View.VISIBLE) {
            mIOnLrcShowLineChange.onLrcShowLineChange();
        }
        yValueAnimator.setIntValues(0, getYAnimationHeight());
        yValueAnimator.setDuration(mYDuration);
        yValueAnimator.setInterpolator(new AccelerateInterpolator());
        yValueAnimator.addUpdateListener(animation -> {
            if (animation != null && animation.getAnimatedValue() instanceof Integer) {
                mYOffset = (int) animation.getAnimatedValue();
                float prop = (float) mYOffset / (float) getYAnimationHeight();
                mOverTextSize = (mActiveTextSize - mNormalTextSize) * prop + mNormalTextSize;
                mPreOverTextSize = (mActiveTextSize - mNormalTextSize) * (1 - prop) + mNormalTextSize;
                invalidate();
            }
        });
        yValueAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mYOffset = 0;
                mOverTextSize = mNormalTextSize;
                mPreOverTextSize = mActiveTextSize;
                mCurrentLine = currentLine;
                Logger.d(TAG, "doYOffsetAnimation; onAnimationEnd; mCurrentLine= " +mCurrentLine );
                invalidate();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                mYOffset = 0;
                mOverTextSize = mNormalTextSize;
                mPreOverTextSize = mActiveTextSize;
                mCurrentLine = currentLine;
                Logger.d(TAG, "doYOffsetAnimation; onAnimationCancel; mCurrentLine= " +mCurrentLine );
                invalidate();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        yValueAnimator.start();
    }

    // 获取向上滚动的最大距离
    private int getYAnimationHeight() {
        return (int) (mFirstLineHight + mDividerHeight);
    }

    private void resetXOffset() {
        if (xValueAnimator == null) {
            return;
        }
        xValueAnimator.cancel();
        xValueAnimator.removeAllUpdateListeners();
        mLastXOffset = 0;
        mXOffset = 0;
        invalidate();
    }

    // 防止声音切换时，出现闪烁的问题
    public void removeXOffsetWhenSoundSwitch() {
        mLastXOffset = 0;
        mXOffset = 0;
        invalidate();
    }

    public void reset() {
        if (lrcEntryList != null) {
            lrcEntryList.clear();
        }
        mCurrentLine = 0;
        mLastTime = 0;
    }

    private int findShowLine(long time) {
        if (ToolUtil.isEmptyCollects(lrcEntryList)) {
            return 0;
        }
        int left = 0;
        int right = lrcEntryList.size();
        while (left <= right) {
            int middle = (left + right) / 2;
            long middleTime = lrcEntryList.get(middle).getTime();

            if (time < middleTime) {
                right = middle - 1;
            } else {
                if (middle + 1 >= lrcEntryList.size() || time < lrcEntryList.get(middle + 1).getTime()) {
                    return middle;
                }
                left = middle + 1;
            }
        }
        return 0;
    }


    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (showShortLine) {
            drawShort(canvas);
        } else {
            if (mDragFromBackToFront) {
                drawLongByDrag(canvas);
            } else {
                drawLong(canvas);
            }
        }
    }

    // 绘制两行文稿
    private void drawShort(Canvas canvas) {
        if (lrcEntryList == null || lrcEntryList.size() <= 0 || mCurrentLine < 0 || mCurrentLine >= lrcEntryList.size()) {
            return;
        }

        int sc = canvas.saveLayer(0, 0, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);

        float yOffset = -mYOffset;
        LrcEntry entry = lrcEntryList.get(mCurrentLine);
        // 画第一层当前句
        canvas.translate(0, yOffset);
        entry.getCustomPaint().setTextSize(mActiveTextSize);
        entry.getCustomPaint().setColor(mNormalTextColor);
        entry.getDynamicLayout().draw(canvas);
        yOffset = entry.getDynamicLayout().getHeight();

        // 画第二层高亮句
        int lineHeight = entry.getDynamicLayout().getHeight();
        mFirstLineHight = lineHeight;

        int saveLayer = canvas.saveLayer(0f, 0f, mXOffset, lineHeight, null, Canvas.ALL_SAVE_FLAG);
        entry.getCustomPaint().setTextSize(mActiveTextSize);
        entry.getCustomPaint().setColor(mActiveTextColor);
        entry.getDynamicLayout().draw(canvas);
        canvas.restoreToCount(saveLayer);

        if (onlyOneLine) {
            return;
        }

        // 画高亮句下面的句子
        if (mCurrentLine + 1 <= lrcEntryList.size() - 1
                && lrcEntryList.get(mCurrentLine + 1) != null
                && lrcEntryList.get(mCurrentLine + 1).getCustomPaint() != null
                && lrcEntryList.get(mCurrentLine + 1).getDynamicLayout() != null) {
            yOffset = yOffset + mDividerHeight;
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry nextLrc = lrcEntryList.get(mCurrentLine + 1);
            nextLrc.getCustomPaint().setTextSize(mOverTextSize);
            nextLrc.getCustomPaint().setColor(mNormalTextColor);
            yOffset = yOffset + nextLrc.getDynamicLayout().getHeight();
            nextLrc.getDynamicLayout().draw(canvas);
            canvas.restore();
        }

        // 画高亮句下面第二行的句子，因为向上滚动动画时，可能会显示出第三行的句子
        if (mCurrentLine + 2 <= lrcEntryList.size() - 1
                && lrcEntryList.get(mCurrentLine + 2) != null
                && lrcEntryList.get(mCurrentLine + 2).getCustomPaint() != null
                && lrcEntryList.get(mCurrentLine + 2).getDynamicLayout() != null) {
            yOffset = yOffset + mDividerHeight;
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry nextAndNextLrc = lrcEntryList.get(mCurrentLine + 2);
            nextAndNextLrc.getCustomPaint().setTextSize(mNormalTextSize);
            nextAndNextLrc.getCustomPaint().setColor(mNormalTextColor);
            nextAndNextLrc.getDynamicLayout().draw(canvas);
            canvas.restore();
        }

        canvas.restoreToCount(sc);
    }

    // 绘制多行文稿
    private void drawLong(Canvas canvas) {
        if (lrcEntryList == null || lrcEntryList.size() <= 0 || mCurrentLine < 0 || mCurrentLine >= lrcEntryList.size()) {
            Logger.w("DOC_LRC_VIEW", "NOT DATA");
            return;
        }

        int sc = canvas.saveLayer(0, 0, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);

        float yOffset = -mYOffset;
        if (mCurrentLine - 2 >= 0
                && mCurrentLine - 2 <= lrcEntryList.size() - 1
                && lrcEntryList.get(mCurrentLine - 2) != null
                && lrcEntryList.get(mCurrentLine - 2).getCustomPaint() != null
                && lrcEntryList.get(mCurrentLine - 2).getDynamicLayout() != null) {
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry nextAndNextLrc = lrcEntryList.get(mCurrentLine - 2);
            nextAndNextLrc.getCustomPaint().setTextSize(mNormalTextSize);
            nextAndNextLrc.getCustomPaint().setColor(mNormalTextColor);
            yOffset = yOffset + nextAndNextLrc.getDynamicLayout().getHeight();
            nextAndNextLrc.getDynamicLayout().draw(canvas);
            canvas.restore();
        } else {
            // 适配高亮句是前两句的情况
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry entry = lrcEntryList.get(mCurrentLine);
            yOffset = yOffset + entry.getDynamicLayout().getHeight();
            canvas.restore();
        }

        if (mCurrentLine - 1 >= 0
                && mCurrentLine - 1 <= lrcEntryList.size() - 1
                && lrcEntryList.get(mCurrentLine - 1) != null
                && lrcEntryList.get(mCurrentLine - 1).getCustomPaint() != null
                && lrcEntryList.get(mCurrentLine - 1).getDynamicLayout() != null) {
            yOffset = yOffset + mDividerHeight;
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry nextAndNextLrc = lrcEntryList.get(mCurrentLine - 1);
            nextAndNextLrc.getCustomPaint().setTextSize(mNormalTextSize);
            nextAndNextLrc.getCustomPaint().setColor(mNormalTextColor);
            yOffset = yOffset + nextAndNextLrc.getDynamicLayout().getHeight();
            nextAndNextLrc.getDynamicLayout().draw(canvas);
            canvas.restore();
        } else {
            // 适配高亮句是前两句的情况
            yOffset = yOffset + mDividerHeight;
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry entry = lrcEntryList.get(mCurrentLine);
            yOffset = yOffset + entry.getDynamicLayout().getHeight();
            canvas.restore();
        }

        yOffset = yOffset + mDividerHeight;
        float tmp = yOffset;
        LrcEntry entry = lrcEntryList.get(mCurrentLine);
        // 画第一层当前句
        canvas.translate(0, yOffset);
        entry.getCustomPaint().setTextSize(mPreOverTextSize);
        entry.getCustomPaint().setColor(mNormalTextColor);
        entry.getDynamicLayout().draw(canvas);
        yOffset = entry.getDynamicLayout().getHeight();

        // 画第二层高亮句
        int lineHeight = entry.getDynamicLayout().getHeight();
        mFirstLineHight = lineHeight;

        int saveLayer = canvas.saveLayer(0f, 0f, mXOffset, lineHeight, null, Canvas.ALL_SAVE_FLAG);
        entry.getCustomPaint().setTextSize(mPreOverTextSize);
        entry.getCustomPaint().setColor(mActiveTextColor);
        entry.getDynamicLayout().draw(canvas);
        canvas.restoreToCount(saveLayer);

        // 过渡句
        if (mCurrentLine + 1 <= lrcEntryList.size() - 1
                && lrcEntryList.get(mCurrentLine + 1) != null
                && lrcEntryList.get(mCurrentLine + 1).getCustomPaint() != null
                && lrcEntryList.get(mCurrentLine + 1).getDynamicLayout() != null) {
            yOffset = yOffset + mDividerHeight;
            canvas.save();
            canvas.translate(0, yOffset);
            LrcEntry nextAndNextLrc = lrcEntryList.get(mCurrentLine + 1);
            nextAndNextLrc.getCustomPaint().setTextSize(mOverTextSize);
            nextAndNextLrc.getCustomPaint().setColor(mNormalTextColor);
            yOffset = yOffset + nextAndNextLrc.getDynamicLayout().getHeight();
            nextAndNextLrc.getDynamicLayout().draw(canvas);
            canvas.restore();
        }

        for (int i = mCurrentLine + 2; i >= 0 && i < lrcEntryList.size(); i++) {
            if (yOffset < getHeight()) {
                if (lrcEntryList.get(i) != null
                        && lrcEntryList.get(i).getCustomPaint() != null
                        && lrcEntryList.get(i).getDynamicLayout() != null) {
                    yOffset = yOffset + mDividerHeight;
                    canvas.save();
                    canvas.translate(0, yOffset);
                    LrcEntry nextAndNextLrc = lrcEntryList.get(i);
                    nextAndNextLrc.getCustomPaint().setTextSize(mNormalTextSize);
                    nextAndNextLrc.getCustomPaint().setColor(mNormalTextColor);
                    yOffset = yOffset + nextAndNextLrc.getDynamicLayout().getHeight();
                    nextAndNextLrc.getDynamicLayout().draw(canvas);
                    canvas.restore();
                }
            } else {
                break;
            }
        }

        // 顶部蒙层
        canvas.save();
        canvas.translate(0, -tmp);
        canvas.drawRect(0, 0, getWidth(), mFadeMaskHeight, mFadeMaskPaint);
        canvas.restore();

        // 底部蒙层
        canvas.save();
        canvas.translate(0, getHeight() - mBottomFadeMaskHeight - tmp);
        canvas.drawRect(0, 0, getWidth(), mBottomFadeMaskHeight, mFadeBottomMaskPaint);
        canvas.restore();

        canvas.restoreToCount(sc);
    }

    // 从后向前拖动进度条时，文稿需要从上往下滚动，这里做的是假动画，每次都触发固定滚动3行
    private void drawLongByDrag(Canvas canvas) {
        if (lrcEntryList == null || lrcEntryList.size() <= 0) {
            return;
        }

        int sc = canvas.saveLayer(0, 0, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);

        float yOffset = -mYOffset;
        float tmp = yOffset;

        int startIndexOffset = 0;
        if (mCurrentLine == 3) {
            if (lrcEntryList.get(0) != null && lrcEntryList.get(0).getDynamicLayout() != null) {
                yOffset += (lrcEntryList.get(0).getDynamicLayout().getHeight() + mDividerHeight) * 2;
            }
            startIndexOffset = 2;
        } else if (mCurrentLine == 4) {
            if (lrcEntryList.get(0) != null && lrcEntryList.get(0).getDynamicLayout() != null) {
                yOffset += (lrcEntryList.get(0).getDynamicLayout().getHeight() + mDividerHeight);
            }
            startIndexOffset = 1;
        }

        for (int i = mCurrentLine - 5 + startIndexOffset; i >= 0 && i < lrcEntryList.size(); i++) {
            if (yOffset < getHeight()) {
                if (lrcEntryList.get(i) != null
                        && lrcEntryList.get(i).getCustomPaint() != null
                        && lrcEntryList.get(i).getDynamicLayout() != null) {
                    canvas.save();
                    canvas.translate(0, yOffset);
                    LrcEntry nextAndNextLrc = lrcEntryList.get(i);
                    nextAndNextLrc.getCustomPaint().setTextSize(mNormalTextSize);
                    nextAndNextLrc.getCustomPaint().setColor(mNormalTextColor);
                    yOffset = yOffset + nextAndNextLrc.getDynamicLayout().getHeight();
                    nextAndNextLrc.getDynamicLayout().draw(canvas);
                    canvas.restore();
                    yOffset = yOffset + mDividerHeight;
                }
            } else {
                break;
            }
        }

        // 顶部蒙层
        canvas.save();
        canvas.translate(0, -tmp);
        canvas.drawRect(0, 0, getWidth(), mFadeMaskHeight, mFadeMaskPaint);
        canvas.restore();

        // 底部蒙层
        canvas.save();
        canvas.translate(0, getHeight() - mBottomFadeMaskHeight - tmp);
        canvas.drawRect(0, 0, getWidth(), mBottomFadeMaskHeight, mFadeBottomMaskPaint);
        canvas.restore();


        canvas.restoreToCount(sc);
    }

    private void updateViewWithCancelTask() {
        long time = XmPlayerManager.getInstance(mContext).getCurrentPosInner();
        updateExactTime(time);
    }

    public void onMyResume() {
        mIsShow = true;
        // 暂停播放需要刷新进度，防止进度变化
        if (!XmPlayerManager.getInstance(mContext).isPlaying()) {
            cancelUpdateTimeTask();
            updateViewWithCancelTask();
        }
    }

    public void onPause() {
        mIsShow = false;
        cancelUpdateTimeTask();
    }

    public void onDestroy() {
        mIsShow = false;
        cancelUpdateTimeTask();
        mLayoutChangeListener = null;
    }

    public void onPlayStart() {
    }

    public void onPlayPause() {
        cancelUpdateTimeTask();
        updateViewWithCancelTask();
    }

    public void onPlayStop() {
        cancelUpdateTimeTask();
        updateViewWithCancelTask();
    }

    // 倍速变化了
    public void onTempoChanged(float tempo) {
        mTempo = tempo;
        setTimeFrequency((long) (TIMER_FREQURENCY / mTempo));
        HandlerManager.removeCallbacks(mDrawLightLineTask);
        HandlerManager.postOnUIThreadDelay(mDrawLightLineTask, TIMER_FREQURENCY);
    }

    // 拖动进度条的时候无需做翻滚动画
    public void updateCurLine(long pos, boolean needUpdateXOffset) {
        if (yValueAnimator != null && yValueAnimator.isRunning()) {
            yValueAnimator.cancel();
        }
        mCurrentLine = findShowLine(pos);
        Logger.d(TAG, "updateCurLine; mCurrentLine= " +mCurrentLine );
        mCurrentTime = pos;
        mLastTime = mCurrentTime;
        if (needUpdateXOffset) {
            mXOffset = findLineXOffset();
        } else {
            mXOffset = 0;
        }
        invalidate();
    }

    public void updateLineWhenSeek(long pos) {
        mDragFromBackToFront = pos < mLastTime;
        mLastTime = pos;

        int curLine = findShowLine(pos);
        mXOffset = 0;
        if (mDragFromBackToFront) {
            dragFromBackToFront(curLine, pos);
        } else {
            dragFromFrontToBack(curLine, pos);
        }
    }

    // 从前往后拖动，展示时往上滚动
    private void dragFromFrontToBack(int curLine, long pos) {
        if (lrcEntryList == null) return;

        int yOffset = 0;
        for (int i = curLine - 3; i >= 0 && i < lrcEntryList.size() && i <= curLine; i++) {
            LrcEntry entry = lrcEntryList.get(i);
            if (entry != null && entry.getDynamicLayout() != null) {
                yOffset = (int) (yOffset + mDividerHeight + entry.getDynamicLayout().getHeight());
            }
        }

        if (yOffset <= 0) {
            mIsDragging = false;
            return;
        }

        if (yValueAnimator != null && yValueAnimator.isRunning()) {
            yValueAnimator.cancel();
        }

        mIsDragging = true;
        mCurrentLine = curLine - 3;
        Logger.d(TAG, "dragFromFrontToBack; mCurrentLine= " +mCurrentLine );
        ValueAnimator animator = ValueAnimator.ofInt(0, yOffset);
        animator.setDuration(200);
        animator.addUpdateListener(animation -> {
            int value = (Integer) animation.getAnimatedValue();
            mYOffset = value;
            invalidate();
        });
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mYOffset = 0;
                mIsDragging = false;
                mCurrentLine = curLine;
                Logger.d(TAG, "dragFromFrontToBack; onAnimationEnd; mCurrentLine= " +mCurrentLine );

                mXOffset = findLineXOffset();
                mLastXOffset = mXOffset;
                invalidate();
                updateTime(pos, false);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                mYOffset = 0;
                mIsDragging = false;
                mCurrentLine = curLine;
                Logger.d(TAG, "dragFromFrontToBack; onAnimationCancel; mCurrentLine= " +mCurrentLine );
                mXOffset = findLineXOffset();
                mLastXOffset = mXOffset;
                invalidate();
                updateTime(pos, false);
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animator.start();
    }

    // 从后往前拖动，展示时往下滚动
    private void dragFromBackToFront(int curLine, long pos) {
        if(lrcEntryList == null) return;
        int yOffset = 0;
        for (int i = curLine + 2; i >= 0 && i < lrcEntryList.size() && i >= curLine; i--) {
            LrcEntry entry = lrcEntryList.get(i);
            if (entry != null && entry.getDynamicLayout() != null) {
                yOffset = (int) (yOffset + mDividerHeight + entry.getDynamicLayout().getHeight());
            }
        }

        if (yOffset <= 0) {
            mDragFromBackToFront = false;
            mIsDragging = false;
            return;
        }

        if (yValueAnimator != null && yValueAnimator.isRunning()) {
            yValueAnimator.cancel();
        }

        mIsDragging = true;
        mCurrentLine = curLine + 3;
        Logger.d(TAG, "dragFromBackToFront; mCurrentLine= " +mCurrentLine );
        ValueAnimator animator = ValueAnimator.ofInt(yOffset, 0);
        animator.setDuration(200);
        animator.addUpdateListener(animation -> {
            int value = (Integer) animation.getAnimatedValue();
            mYOffset = value;
            invalidate();
        });
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mYOffset = 0;
                mIsDragging = false;
                mCurrentLine = curLine;
                Logger.d(TAG, "dragFromBackToFront;onAnimationEnd; mCurrentLine= " +mCurrentLine );
                mDragFromBackToFront = false;
                mXOffset = findLineXOffset();
                mLastXOffset = mXOffset;
                invalidate();
                updateTime(pos, false);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                mYOffset = 0;
                mIsDragging = false;
                mCurrentLine = curLine;
                Logger.d(TAG, "dragFromBackToFront;onAnimationCancel; mCurrentLine= " +mCurrentLine );
                mDragFromBackToFront = false;
                mXOffset = findLineXOffset();
                mLastXOffset = mXOffset;
                invalidate();
                updateTime(pos, false);
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animator.start();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        if (mLayoutChangeListener != null) {
            mLayoutChangeListener.onLayout(changed, left, top, right, bottom);
        }
    }

    public interface IOnLayoutListener {
        void onLayout(boolean changed, int left, int top, int right, int bottom);
    }

    public void setIOnLayoutListener(IOnLayoutListener listener) {
        mLayoutChangeListener = listener;
    }

    public interface IOnLrcShowLineChange {
        void onLrcShowLineChange();
    }

    private IOnLrcShowLineChange mIOnLrcShowLineChange;

    public void setIOnLrcShowLineChange(IOnLrcShowLineChange onLrcShowLineChange) {
        this.mIOnLrcShowLineChange = onLrcShowLineChange;
    }
}