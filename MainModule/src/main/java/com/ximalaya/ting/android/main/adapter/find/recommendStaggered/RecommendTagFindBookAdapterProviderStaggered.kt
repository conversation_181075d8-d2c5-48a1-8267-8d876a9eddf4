package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.marginStart
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.RpAdaptUtil.rp2PxIn375
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.SkinManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.AlbumRank
import com.ximalaya.ting.android.main.model.rec.RankSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendRankListItem
import com.ximalaya.ting.android.main.playpage.playx.utils.getCurrentView
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 标签找书卡
 */
class RecommendTagFindBookAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendTagFindBookAdapterProviderStaggered.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendTagFindBookAdapterProviderStaggered.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendTagFindBookAdapterProviderStaggered.ViewHolder, RecommendItemNew> {
    private var mOldState = RecyclerView.SCROLL_STATE_IDLE

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_tag_find_book_layout,
            parent,
            false
        )
    }

    class ViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        var contentView: View = convertView.findViewById(R.id.content_view)
        val viewPager: ViewPager = convertView.findViewById(R.id.main_vp_rank_list)
        val tabListRv: RecyclerView = convertView.findViewById(R.id.main_rcv_rank_tab)
        var leftTabShadowView: View = convertView.findViewById(R.id.main_view_left_tab_shadow)
        val rightTabShadowView: View = convertView.findViewById(R.id.main_view_right_tab_shadow)
        val tvMore: TextView = convertView.findViewById(R.id.main_tv_more)
        val tvModuleTitle: TextView = convertView.findViewById(R.id.main_tv_ting_title)
        val scrollNextLayout: View = convertView.findViewById(R.id.main_rank_scroll_next_layout)

        var recommendItemNew: RecommendItemNew? = null
        var mLastScreenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        var isNeedForceUpdate: Boolean = false
        var isFolderScreen: Boolean? = null
        var isDraging: Boolean = false

        init {
            resetSize()
        }

        fun resetSize() {
        }
    }

    override fun onConfigurationChanged(holder: ViewHolder?) {
        holder ?: return
        holder.isNeedForceUpdate = true
    }

    override fun createViewHolder(convertView: View?): ViewHolder? {
        if (convertView == null) {
            return null
        }
        return ViewHolder(convertView)
    }

    override fun bindViewHolder(
        holder: ViewHolder?,
        modulePosition: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }

        isPageChange = false

        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendRankListItem = recommendItemNew.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }

        if (modulePosition == 0 && SkinManager.isNeedShowAtmosphereImage()) {
            holder.leftTabShadowView.visibility = View.GONE
            holder.rightTabShadowView.visibility = View.GONE
        } else {
            holder.leftTabShadowView.visibility = View.VISIBLE
            holder.rightTabShadowView.visibility = View.VISIBLE
        }

        if (!holder.isNeedForceUpdate && holder.isFolderScreen != null
            && isFoldScreenWithExpand() != holder.isFolderScreen
        ) {
            holder.isNeedForceUpdate = true
        }

        if (holder.recommendItemNew == recommendItemNew
            && (holder.recommendItemNew?.item as? RecommendRankListItem)?.innerListSelectedIndex
            == recommendRankListItem.innerListSelectedIndex
            && !holder.isNeedForceUpdate
        ) {
            return
        }

        holder.isFolderScreen = isFoldScreenWithExpand()
        holder.isNeedForceUpdate = false
        holder.recommendItemNew = recommendItemNew
        if (!recommendRankListItem.title.isNullOrEmpty()) {
            holder.tvModuleTitle.text = recommendRankListItem.title
        }

        val cardTabAdapter = TingTabAdapter(
            rankingList as MutableList<AlbumRank>,
            recommendItemNew,
            modulePosition,
            holder.viewPager
        )

        cardTabAdapter.onTabSelectListener = object : OnTabSelectListener {
            var isFromClick = false
            var mPrePosition = 0

            private fun checkTrackAlbum(curPosition: Int) {
                val albumRank = rankingList.getOrNull(curPosition)
                // 列表空  需要请求数据 在bindData中曝光
                if (albumRank?.subElements.isNullOrEmpty()) {
                    isPageChange = true
                    return
                }
                // 非空  在当前曝光
                HandlerManager.postOnUIThreadDelay({
                    traceOnAlbumItemShow(
                        holder.recommendItemNew,
                        modulePosition,
                        holder
                    )
                }, 200)
            }

            override fun onSelect(curPosition: Int) {
                checkTrackAlbum(curPosition)
                val preAlbumRank = rankingList.getOrNull(mPrePosition)
                val curAlbumRank = rankingList.getOrNull(curPosition)
                recommendRankListItem.innerListSelectedIndex = curPosition
                mPrePosition = curPosition

                val action = if (isFromClick) {
                    "click"
                } else {
                    "slipe"
                }
                isFromClick = false

                // 新首页-热门标签-切换  点击事件
                val trace = XMTraceApi.Trace()
                    .click(61859) // 用户点击时上报
                    .put("action", action) // 客户端传。区分点击还是滑动切换
                    .put("currPage", "newHomePage")
                    .put("xmRequestId", recommendItemNew.xmRequestId)
                    .put("tabName", preAlbumRank?.title ?: "") // 根据实际文案，点击前 tabName
                    .put("tagId", curAlbumRank?.id?.toString() ?: "") // 传切换后的
                    .put("modulePosition", (modulePosition + 1).toString())
                    .put("contentType", curAlbumRank?.title ?: "") // 客户端传
                    .put("contentId", curAlbumRank?.id?.toString() ?: "-1") // 客户端传
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString(),
                    curAlbumRank?.title,
                    (curPosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank?.ubtV2)
                trace.createTrace()

                if (!curAlbumRank?.landingPage.isNullOrEmpty()) {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.tvMore)
                } else {
                    ViewStatusUtil.setVisible(View.INVISIBLE, holder.tvMore)
                }

                scrollToPositionTab(holder.tabListRv, curPosition, true)
            }

            override fun clickTitle(prePosition: Int, curPosition: Int) {
                mPrePosition = prePosition
                isFromClick = true
                val trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                    .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
                SpmTraceUtil.addSpmTraceInfo(
                    trace1, recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace1, recommendRankListItem.ubtV2
                )
                trace1.createTrace()
            }
        }
        scrollToPositionTab(
            holder.tabListRv,
            recommendRankListItem.innerListSelectedIndex,
            false
        )
        holder.tabListRv.adapter = cardTabAdapter
        holder.tabListRv.layoutManager = LinearLayoutManager(
            holder.tabListRv.context, LinearLayoutManager.HORIZONTAL, false
        )
        holder.tabListRv.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnTabItemShow(recommendItemNew, modulePosition, holder)
                }
            }
        })

        val tipShowListener = object : IOnListTipShowListener {
            override fun onItemShow(shouldShow: Boolean, forceShow: Boolean) {
                if (!holder.isDraging || forceShow) {
                    ViewStatusUtil.setVisible(
                        if (shouldShow) View.VISIBLE else View.GONE,
                        holder.scrollNextLayout
                    )
                }
            }
        }

        val rankListViewPagerAdapter = RankViewPagerAdapter(
            fragment,
            rankingList,
            recommendItemNew,
            modulePosition,
            dataAction,
            tipShowListener,
            recommendRankListItem,
            holder
        ) {
            moreClickInner(
                rankingList,
                recommendRankListItem,
                recommendItemNew,
                modulePosition,
                null,
                "slide"
            )
        }
        // 听单专辑列表
        holder.viewPager.adapter = rankListViewPagerAdapter
        holder.viewPager.setOnPageChangeListener(
            ViewPageChangeListener(
                recommendRankListItem,
                rankingList,
                this,
                holder,
                tipShowListener
            )
        )
        holder.viewPager.post {
            holder.viewPager.requestLayout()
        }
        if (recommendRankListItem.innerListSelectedIndex < rankingList.size) {
            val curAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
            if (curAlbumRank.landingPage.isNullOrEmpty()) {
                ViewStatusUtil.setVisible(View.INVISIBLE, holder.tvMore)
            } else {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.tvMore)
            }
        }
        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            moreClickInner(rankingList, recommendRankListItem, recommendItemNew, modulePosition, it)
        }

        val indexOfPage = holder.viewPager.currentItem
        if (indexOfPage != recommendRankListItem.innerListSelectedIndex) {
            holder.viewPager.setCurrentItem(recommendRankListItem.innerListSelectedIndex, false)
        }
    }

    private fun moreClickInner(
        rankingList: List<AlbumRank>,
        recommendRankListItem: RecommendRankListItem,
        recommendItemNew: RecommendItemNew,
        modulePosition: Int,
        clickView: View?,
        action: String = "click"
    ) {
        val curAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
        if (curAlbumRank.landingPage.isNullOrEmpty()) {
            return
        }
        val trace1 = XMTraceApi.Trace()
            .click(62176) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
            .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
        SpmTraceUtil.addSpmTraceInfo(
            trace1,
            (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2,
            (modulePosition + 1).toString()
        )
        RecommendNewUbtV2Manager.addUbtV2Data(
            trace1,
            (recommendItemNew.item as? RecommendRankListItem)?.ubtV2
        )
        trace1.createTrace()
        // 新首页-热门标签-更多  点击事件
        val trace = XMTraceApi.Trace()
            .click(61860) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
            .put("modulePosition", (modulePosition + 1).toString())
            .put("action", action)
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            recommendRankListItem.ubtV2,
            (modulePosition + 1).toString(),
            tab2Title = "更多",
            tab2Position = "d01",
            tab1Title = curAlbumRank.title,
            tab1Position = "${recommendRankListItem.innerListSelectedIndex + 1}"
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
        RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank.ubtV2)
        trace.createTrace()
        ScenePlayDataUtil.saveDataForRn(recommendItemNew)
        ToolUtil.clickUrlAction(fragment, curAlbumRank.landingPage, clickView)
    }

    private class ViewPageChangeListener(
        var recommendRankListItem: RecommendRankListItem?,
        var rankingList: List<AlbumRank>?,
        var recommendRankListAdapterProviderStaggered: RecommendTagFindBookAdapterProviderStaggered,
        var holder: ViewHolder?,
        val tipShowListener: IOnListTipShowListener?
    ) : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
        }

        override fun onPageSelected(position: Int) {
            if (recommendRankListItem == null || rankingList == null || holder == null) {
                return
            }
            recommendRankListItem!!.innerListSelectedIndex = position
            (holder?.tabListRv?.adapter as? TingTabAdapter)?.onTabSelectListener?.onSelect(
                position
            )
            holder?.tabListRv?.adapter?.notifyDataSetChanged()
        }

        private var mCurrentPosition = -1

        override fun onPageScrollStateChanged(state: Int) {
            if (state == ViewPager.SCROLL_STATE_IDLE) {
                holder?.isDraging = false
                if (holder != null && mCurrentPosition == holder!!.viewPager.currentItem) {
                    showGuideTipInner(holder!!.viewPager.currentItem, tipShowListener)
                }
            } else if (state == ViewPager.SCROLL_STATE_DRAGGING) {
                if (holder?.isDraging == false) {
                    mCurrentPosition = holder!!.viewPager.currentItem
                }
                holder?.isDraging = true
                tipShowListener?.onItemShow(false, true)
            }
        }

        private fun showGuideTipInner(position: Int, tipShowListener: IOnListTipShowListener?) {
            val albumRank =
                (holder!!.viewPager.adapter as? RankViewPagerAdapter?)?.rankingList?.getOrNull(
                    position
                )
            val isLastTab =
                (holder?.recommendItemNew?.item as? RecommendRankListItem)?.list?.size == (position + 1)
            if (albumRank != null && !albumRank.landingPage.isNullOrEmpty()) {
                val scrollPage = albumRank.scrollPosition / SPAN_COUNT + 1
                val totalPage = (albumRank.subElements?.size ?: 0) / SPAN_COUNT
                val mTipShow = (scrollPage == totalPage) && !isLastTab
                val isAllowShowMore = !TextUtils.isEmpty(albumRank.landingPage)
                if (isAllowShowMore) {
                    tipShowListener?.onItemShow(mTipShow, true)
                }
            }
        }
    }

    // 滚动到指定tab并且居中
    private fun scrollToPositionTab(recyclerView: RecyclerView, position: Int, smooth: Boolean) {
        val itemView = recyclerView.findViewHolderForLayoutPosition(position)?.itemView ?: return
        val itemWidth = itemView.width
        val offset =
            (recyclerView.width - itemWidth) / 2 - itemView.marginStart - 20.dp // 7 + 6 + 14/2为斜杠到文字的距离
        if (smooth) {
            recyclerView.smoothScrollToPositionWithOffset(position, offset)
        } else {
            val manager = recyclerView.layoutManager as? LinearLayoutManager ?: return
            manager.scrollToPositionWithOffset(position, offset)
        }
    }

    private fun traceOnTabItemShow(
        recommendItemNew: RecommendItemNew,
        modulePosition: Int,
        holder: ViewHolder
    ) {
        holder.tabListRv.let {
            val childSize = holder.tabListRv.childCount
            for (i in 0 until childSize) {
                val itemView: View = holder.tabListRv.getChildAt(i) ?: continue
                if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                    val albumRank =
                        itemView.getTag(R.id.main_id_item_data) as? AlbumRank ?: continue

                    // 新首页-榜单  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62213)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage") // 客户端传
                        .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传，去重用
                        .put("contentType", "hotTag2024") // 客户端传，去重用
                        .put("contentId", albumRank.id?.toString() ?: "") // 客户端传，去重用
                        .put(
                            "modulePosition",
                            (modulePosition + 1).toString()
                        ) // 客户端传，card 在流里的位置，从 1 开始计数
                    RecommendNewUbtV2Manager.addUbtV2Data(
                        trace, (recommendItemNew.item as? RecommendRankListItem)?.ubtV2
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank.ubtV2)
                    if (recommendItemNew.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }
            }
        }
    }

    fun checkAdCunt(list: List<AlbumRank>?): Int {
        if (list.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (albumRank in list) {
            val subElements = albumRank.subElements
            if (!subElements.isNullOrEmpty()) {
                for (subElement in subElements) {
                    val adInfo = subElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfo
                    if (!adInfo.isNullOrEmpty()) {
                        adCount++
                    }
                }
            }
        }
        return adCount
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ViewHolder?
    ) {
        if (holder == null || data == null) {
            return
        }
        val recommendRankListItem = data.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }
        if (recommendRankListItem.cardAdCount == -1) {
            recommendRankListItem.cardAdCount = checkAdCunt(rankingList)
        }
        val albumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
        val subElements = albumRank.subElements
        if (subElements.isNullOrEmpty()) {
            return
        }

        fragment.postOnUiThread {
            if (!fragment.canUpdateUi()) {
                return@postOnUiThread
            }

            holder.itemView.postDelayed({
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendRankListItem.id.toString()) // 客户端传
                        .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendRankListItem.ubtV2,
                        (position + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }

                traceOnAlbumItemShow(
                    data,
                    position,
                    holder
                )
                traceOnTabItemShow(data, position, holder)
            }, 200)
        }
    }

    class TingTabAdapter(
        // tab数据
        private val rankingList: MutableList<AlbumRank>,
        private val recommendItemNew: RecommendItemNew?,
        // 位置
        var modulePosition: Int,
        private val viewPager: ViewPager,
    ) : RecyclerView.Adapter<TingTabAdapter.TabViewHolder>() {

        // 选中回掉
        var onTabSelectListener: OnTabSelectListener? = null

        override fun onCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): TabViewHolder {
            return TabViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.main_fra_recommend_tag_find_book_tab_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
            val albumRank = rankingList[position]
            holder.itemView.setTag(R.id.main_id_item_data, albumRank)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            holder.tabTv.setTextIfChanged(albumRank.title)

            if (position == 0) {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
                holder.tabTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
            } else {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                holder.tabTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }

            holder.itemView.setOnOneClickListener {
                val item = recommendItemNew?.item as? RecommendRankListItem
                val innerListSelectedIndex = item?.innerListSelectedIndex ?: -1
                if (innerListSelectedIndex == position) {
                    return@setOnOneClickListener
                }
                item?.innerListSelectedIndex = position
                onTabSelectListener?.clickTitle(innerListSelectedIndex, position)
                viewPager.currentItem = position
            }

            updateTabSelectStyle(holder, position, holder.view.context)
        }

        // 更新选中tab样式
        private fun updateTabSelectStyle(
            holder: TabViewHolder,
            position: Int,
            context: Context
        ) {
            if (viewPager.currentItem == position) {
                holder.tabLl.background =
                    ContextCompat.getDrawable(context, R.drawable.main_bg_shape_1aff4444_r14)
                holder.tabTv.setTextColor(context.resources.getColor(R.color.host_color_ff4444))
            } else {
                holder.tabLl.background =
                    ContextCompat.getDrawable(
                        context,
                        R.drawable.main_bg_tab_select_shape
                    )
                holder.tabTv.setTextColor(context.resources.getColor(R.color.main_color_662c2c3c_e68d8d91))
            }
        }

        override fun getItemCount(): Int {
            return rankingList.size
        }

        class TabViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
            var tabTv: TextView = view.findViewById(R.id.main_tv_ting_tab_title)
            var tabLl: LinearLayout = view.findViewById(R.id.main_ll_tab_container)

            init {
                resetSize()
            }

            private fun resetSize() {
            }
        }
    }

    class RankViewPagerAdapter(
        private val fragment: BaseFragment2,
        val rankingList: List<AlbumRank>?,
        private val recommendItemNew: RecommendItemNew,
        private var modulePosition: Int,
        val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        private val mTipShowListener: IOnListTipShowListener?,
        private val recommendRankListItem: RecommendRankListItem,
        private val parentHolder: ViewHolder?,
        private val mJumpListener: HorizontalMoreBaseAdapter.RelaseJumpActivityListener?
    ) : PagerAdapter() {

        private var mOldState = RecyclerView.SCROLL_STATE_IDLE

        override fun getCount(): Int {
            return rankingList?.size ?: 0
        }

        override fun getItemPosition(albumRank: Any): Int {
            return if (rankingList?.contains(albumRank) == true) {
                rankingList.indexOf(albumRank)
            } else {
                POSITION_NONE
            }
        }

        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view === `object`
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            val view = LayoutInflater.from(container.context)
                .inflate(
                    R.layout.main_fra_recommend_tag_find_book_list_view_page_item,
                    container,
                    false
                )
            val albumRank = rankingList?.get(position)
            if (albumRank == null) {
                bindData(view, null, recommendRankListItem, position)
                return view
            }

            val rankSubElements = albumRank.subElements
            if (!rankSubElements.isNullOrEmpty()) {
                bindData(view, albumRank, recommendRankListItem, position)
            } else {
                val refId = albumRank.refId
                if (refId == null) {
                    bindData(view, null, recommendRankListItem, position)
                } else {
                    MainCommonRequest.getHotTagListItem(
                        albumRank,
                        refId,
                        albumRank.extraInfo,
                        object :
                            IDataCallBack<AlbumRank> {
                            override fun onSuccess(data: AlbumRank?) {
                                if (!fragment.canUpdateUi()) {
                                    return
                                }
                                if (data == null || data.subElements.isNullOrEmpty() || rankingList.isNullOrEmpty()) {
                                    bindData(view, null, recommendRankListItem, position)
                                    return
                                }

                                var find = false
                                for (album in rankingList) {
                                    if (data.equals(album)) {
                                        album.subElements = data.subElements
                                        bindData(view, album, recommendRankListItem, position)
                                        find = true
                                        break
                                    }
                                }

                                if (!find) {
                                    bindData(view, null, recommendRankListItem, position)
                                }
                            }

                            override fun onError(code: Int, message: String?) {
                                if (!fragment.canUpdateUi()) {
                                    return
                                }
                                bindData(view, null, recommendRankListItem, position)
                            }
                        })
                }
            }
            container.addView(view)
            return view
        }

        private fun bindData(
            view: View,
            albumRank: AlbumRank?,
            recommendRankListItem: RecommendRankListItem,
            position: Int
        ) {
            val albumListRv: RecyclerView = view.findViewById(R.id.main_rcv_ting_album_list)

            SPAN_COUNT = if (RecommendFragmentTypeManager.isNewSceneCard() && modulePosition == 1) {
                3
            } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable()
                && modulePosition == 1
            ) {
                2
            } else {
                3
            }
            val rankSubElements = albumRank?.subElements

            val isLastTab = rankingList?.size == (position + 1)
            val isAllowShowMore = !TextUtils.isEmpty(albumRank?.landingPage)
            val enableJumpMore = isLastTab && isAllowShowMore

            val cardAlbumListAdapter = TingCardAlbumItemAdapter(
                fragment,
                rankSubElements,
                modulePosition,
                recommendItemNew,
                albumRank,
                enableJumpMore,
                isAllowShowMore,
                recommendRankListItem,
                albumListRv
            )
            cardAlbumListAdapter.mEnableMoreItem = enableJumpMore
            albumListRv.adapter = cardAlbumListAdapter
            cardAlbumListAdapter.setRelaseJumpActivityListener {
                mJumpListener?.relaseJump()
            }
            val layoutManager =
                GridLayoutManager(view.context, SPAN_COUNT, GridLayoutManager.HORIZONTAL, false)
            layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) {
                        SPAN_COUNT
                    } else {
                        1
                    }
                }
            }
            albumListRv.layoutManager = layoutManager
            val startSnapHelper = StartSnapHelper()
            startSnapHelper.attachToRecyclerView(albumListRv)
            startSnapHelper.setContainerView(albumListRv)

            if (!rankSubElements.isNullOrEmpty()) {
                albumListRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                        super.onScrollStateChanged(recyclerView, newState)
                        if (newState == mOldState) {
                            return
                        }
                        mOldState = newState
                        if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                            var scrollPosition =
                                layoutManager.findFirstCompletelyVisibleItemPosition()
                            if (scrollPosition == -1) {
                                scrollPosition = layoutManager.findFirstVisibleItemPosition()
                            }
                            if (albumRank.scrollPosition < scrollPosition && isAllowShowMore) {
                                val lastCompletePosition =
                                    layoutManager.findLastCompletelyVisibleItemPosition()
                                val columnNum = if (rankSubElements.size % SPAN_COUNT == 0) {
                                    rankSubElements.size / SPAN_COUNT
                                } else {
                                    rankSubElements.size / SPAN_COUNT + 1
                                }
                                val tipShow =
                                    (lastCompletePosition / SPAN_COUNT + 1) >= columnNum && !isLastTab
                                mTipShowListener?.onItemShow(tipShow, false)
                            }
                            albumRank.scrollPosition = scrollPosition
                            traceOnAlbumItemShow(
                                recommendItemNew,
                                modulePosition,
                                parentHolder
                            )
                        } else {
                            if (isAllowShowMore) {
                                mTipShowListener?.onItemShow(false, false)
                            }
                        }
                    }
                })

                albumListRv.scrollToPosition(albumRank.scrollPosition)
                if (isAllowShowMore) {
                    if (parentHolder?.viewPager?.currentItem == position) {
                        val shouldShow =
                            (albumRank.scrollPosition / SPAN_COUNT + 1) >= rankSubElements.size / SPAN_COUNT && !isLastTab
                        mTipShowListener?.onItemShow(shouldShow, false)
                    }
                }

                if (!rankSubElements.isNullOrEmpty() && position == parentHolder?.viewPager?.currentItem && isPageChange) {
                    HandlerManager.postOnUIThreadDelay({
                        isPageChange = false
                        traceOnAlbumItemShow(
                            recommendItemNew,
                            modulePosition,
                            parentHolder
                        )
                    }, 200)
                }
            }
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            if (`object` is View) {
                try {
                    container.removeView(`object`)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    class TingCardAlbumItemAdapter(
        private val fragment: BaseFragment2,
        // 专辑列表
        list: List<RankSubElement>?,
        var modulePosition: Int,
        var recommendItemNew: RecommendItemNew?,
        var albumRank: AlbumRank?,
        private val enableJumpMore: Boolean,
        private val isAllowShowMore: Boolean,
        private val recommendRankListItem: RecommendRankListItem,
        private val albumListRv: RecyclerView,
    ) : HorizontalMoreBaseAdapter() {

        // 专辑列表
        private val albumList = mutableListOf<RankSubElement>()

        init {
            list?.let {
                albumList.addAll(list)
            }
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            } else if (holder is TingAlbumViewHolder) {
                bindAlbumViewHolder(holder, position)
            }
        }

        private fun bindAlbumViewHolder(holder: TingAlbumViewHolder, position: Int) {
            val subElement = albumList.getOrNull(position) ?: return

            holder.itemView.setTag(R.id.main_id_item_data, subElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)

            val isAd = subElement.extraInfo?.subRefInfo?.ad ?: false
            var advertis: Advertis? = null
            if (isAd) {
                advertis = subElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfoObject
            }
            holder.itemView.setTag(R.id.main_id_data_ad_info, advertis)

            val remainder = albumList.size % SPAN_COUNT
            val start: Int = if (remainder == 0) {
                albumList.size - SPAN_COUNT
            } else {
                albumList.size - remainder
            }

            val layoutParams = holder.cslContainerView.layoutParams as MarginLayoutParams
            val textViewContainerWithInPx: Int
            val moreWidth = if (isAllowShowMore) {
                37.dp
            } else {
                0
            }

            // 最后一列
            if (position >= start) {
                // true 有跳转更多 并且是最后一个tab的最后一列  不margin  不然跳转更多控件显示不出来
                // false 两种情况 有跳转更多 非最后一个tab最后一列  margin 37dp  无跳转更多  不margin
                layoutParams.marginEnd = if (enableJumpMore) {
                    0
                } else {
                    moreWidth
                }
                layoutParams.width = rp2PxIn375(375) - moreWidth
                textViewContainerWithInPx = rp2PxIn375(375 - 3) - moreWidth
            } else {
                layoutParams.marginEnd = 0
                layoutParams.width = rp2PxIn375(337 - RpAdaptUtil.getOffset())
                textViewContainerWithInPx = rp2PxIn375(337 - RpAdaptUtil.getOffset() - 3)
            }

            holder.cslContainerView.layoutParams = layoutParams

            onBindAlbumViewInner(subElement, holder, textViewContainerWithInPx)

            holder.itemView.setOnOneClickListener {
                val trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                    .put("card_adTopn", ((recommendItemNew!!.item as RecommendRankListItem).cardAdCount.toString()))
                SpmTraceUtil.addSpmTraceInfo(
                    trace1,
                    (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace1,
                    (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2
                )
                trace1.createTrace()

                // 新首页-热门标签-专辑卡片  点击事件
                val trace = XMTraceApi.Trace()
                    .click(61851) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put(
                        "positionNew",
                        (position + 1).toString()
                    ) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("tabName", albumRank?.title ?: "") // 根据实际文案
                    .put("albumId", subElement.refId?.toString() ?: "")
                    .put("contentType", "album")
                    .put("contentId", subElement.refId?.toString() ?: "")
                    .put("xmRequestId", recommendItemNew?.xmRequestId)
                    .put("tagId", albumRank?.id?.toString() ?: "") // 传对应的社会化标签id，若无则不用传
                    .put("albumTitle", subElement.title ?: "")
                    .put("modulePosition", (modulePosition + 1).toString())
                    .put("rec_src", subElement.ubt?.recSrc ?: "")
                    .put("rec_track", subElement.ubt?.recTrack ?: "")
                    .put("ubtTraceId", subElement.ubt?.traceId ?: "")
                    .put("isAd", if (advertis != null) "true" else "false")
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString(),
                    albumRank?.title,
                    (recommendRankListItem.innerListSelectedIndex + 1).toString(),
                    subElement?.title,
                    (position + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace,
                    (recommendItemNew?.item as RecommendRankListItem).ubtV2
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank?.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
                trace.createTrace()
                if (advertis != null) {
                    // 广告点击跳转
                    AdManager.handlerAdClick(BaseApplication.getMyApplicationContext(), advertis,
                            AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                    AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST)
                                    .positionNew(position + 1)
                                    .modulePosition(modulePosition + 1)
                                    .build())
                } else {
                    jump(fragment, subElement.landingPage)
                }
            }
            holder.itemView.setOnLongClickListener {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (modulePosition + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                albumRank?.ubtV2?.let { it1 -> traceMap.putAll(it1) }
                subElement.ubtV2?.let { it1 -> traceMap.putAll(it1) }
                recommendRankListItem.ubtV2?.let { it1 -> traceMap.putAll(it1) }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = subElement.bizType ?: ""
                traceMap["contentId"] = subElement.refId?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_ALBUM_ID] = subElement.refId.toString()
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] = subElement.anchor?.uid.toString()
                requestMap["card_contentType"] = recommendRankListItem.contentType ?: ""
                requestMap["card_bizType"] = recommendRankListItem.bizType ?: ""
                requestMap["card_id"] = recommendRankListItem.ubtV2?.get("card_id") ?: ""

                val disLikeLeve2Build = DisLikeLeve2Build()
                disLikeLeve2Build.isFromAd = advertis != null
                disLikeLeve2Build.anchorName = subElement.anchor?.nickName
                disLikeLeve2Build.requestMap = requestMap
                disLikeLeve2Build.traceMap = traceMap
                disLikeLeve2Build.onFeedBackListener = object :
                    NewXmFeedBackPopDialog.IOnFeedBackListener() {
                    override fun onDialogShow(showSuccess: Boolean) {
                    }

                    override fun onFeedBack(model: List<XmFeedInnerModel>) {
                        if (advertis != null) {
                            XmAdFeedbackUtil.recordFeedback(advertis, model)
                        }
                        MainCommonRequest.getSingleHotTagListItem(
                            position, albumRank,albumRank?.refId ?: 0, albumRank?.extraInfo,
                            object : IDataCallBack<RankSubElement> {
                                override fun onSuccess(data: RankSubElement?) {
                                    if (!fragment.canUpdateUi() || data == null) {
                                        return
                                    }
                                    if (position >= 0 && position < albumList.size) {
                                        albumList[position] = data
                                        val firstPos = (albumListRv.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                                        notifyItemChanged(position)
                                        albumListRv.scrollToPosition(firstPos)
                                        traceAlbumInner(
                                            data,
                                            albumRank,
                                            recommendRankListItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            holder.itemView,
                                            advertis
                                        )
                                    }
                                }

                                override fun onError(code: Int, message: String?) {

                                }
                            })
                    }
                }

                val build = MoreFuncBuild.createAlbumLongClickModel(
                    fragment, subElement.refId ?: 0, null, true,
                    disLikeLeve2Build
                )
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", recommendItemNew?.itemType)
                    put("contentId", subElement.refId?.toString() ?: "")
                    put("modulePosition", (modulePosition).toString())
                    put("positionNew", (position + 1).toString())

                    albumRank?.ubtV2?.let { it1 -> putAll(it1) }
                    subElement.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap
                XmMoreFuncManager.checkShowMorePage(build)
                true
            }
        }


        override fun getItemCount(): Int {
            if (albumList.isEmpty()) {
                if (enableJumpMore) {
                    return SPAN_COUNT * 2 + 1
                }
                return SPAN_COUNT * 2
            }
            if (enableJumpMore) {
                return albumList.size + 1
            }
            return albumList.size
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            val view = LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(
                R.layout.main_fra_recommend_tag_find_book_item, parent, false
            )
            return TingAlbumViewHolder(view)
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class TingAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutRightTxtArea: View = view.findViewById(R.id.main_layout_right_txt_area)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var showTagsParent: ViewGroup = view.findViewById(R.id.main_layout_show_tag_parent)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)
        }

        private fun onBindAlbumViewInner(
            commonSubElement: RankSubElement,
            holder: TingAlbumViewHolder,
            textViewContainerWith: Int
        ) {
            holder.itemTitleTv.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
            if (commonSubElement.extraInfo?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.extraInfo!!.reasonContent
                holder.itemSubtitle1Tv.maxLines = 1
                RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitle1Tv)
            }
            val adTagShow = commonSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)

            holder.cslContainerView.run {
                val padding = RecommendCornerUtils.getPaddingSize()
                setPadding(paddingLeft, padding, paddingRight, padding)
            }

            val coverSize = RecommendCornerUtils.getSocialCoverSize()
            holder.albumCoverLayoutView.minimumHeight = coverSize.toInt()
            holder.albumCoverLayoutView.updateSize(coverSize)
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)

            holder.showTagsParent.let {
                val params = it.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                it.layoutParams = params
            }

            val adWidth = if (adTagShow) 24 else 0
            // 16 + 70 + 12 + 16
            val otherWidth = 16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(
                holder.layoutShowTags,
                commonSubElement.extraInfo?.showTags,
                textViewContainerWith - otherWidth.dp - adWidth.dp,
                commonSubElement.extraInfo?.subTitle1,
            )
            commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                commonSubElement.extraInfo?.other?.getBRTagUrl()
            )
        }
    }

    companion object {
        private var isPageChange = false
        private var SPAN_COUNT = 3
        private var AD_REPORT_SUB_PERCENT = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_HOME_AD_EXPOSE_PERCENT, 50)

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        private fun traceOnAlbumItemShow(
            data: RecommendItemNew?,
            position: Int,
            holder: ViewHolder?
        ) {
            if (data == null || holder == null) {
                return
            }
            val recommendRankListItem = data.item
            if (recommendRankListItem !is RecommendRankListItem) {
                return
            }
            val rankingList = recommendRankListItem.list
            if (rankingList.isNullOrEmpty()) {
                return
            }

            val albumRank = rankingList.getOrNull(holder.viewPager.currentItem)
            val subElements = albumRank?.subElements
            if (subElements.isNullOrEmpty()) {
                return
            }

            holder.viewPager.getCurrentView()?.let {
                val albumListRv: RecyclerView = it.findViewById(R.id.main_rcv_ting_album_list)
                val childSize = albumListRv.childCount
                for (i in 0 until childSize) {
                    val itemView = albumListRv.getChildAt(i) ?: continue

                    if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                        val rankSubElement =
                            itemView.getTag(R.id.main_id_item_data) as? RankSubElement ?: continue
                        val index = itemView.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val advertis = itemView.getTag(R.id.main_id_data_ad_info)
                        traceAlbumInner(
                            rankSubElement,
                            albumRank,
                            recommendRankListItem,
                            data,
                            index,
                            position,
                            itemView,
                            advertis
                        )
                    }
                }
            }
        }

        fun traceAlbumInner(
            rankSubElement: RankSubElement,
            albumRank: AlbumRank?,
            recommendRankListItem: RecommendRankListItem,
            data: RecommendItemNew?,
            index: Int,
            modulePosition: Int,
            itemView: View,
            advertis: Any?
        ) {
            // 新首页-热门标签-专辑卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(61852)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("positionNew", (index + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("tabName", albumRank?.title ?: "")
                .put("albumId", rankSubElement?.refId?.toString() ?: "")
                .put("contentType", "album")
                .put("contentId", rankSubElement?.refId?.toString() ?: "")
                .put("xmRequestId", data?.xmRequestId)
                .put("tagId", albumRank?.id?.toString() ?: "")
                .put("albumTitle", rankSubElement?.title ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put("rec_src", rankSubElement?.ubt?.recSrc ?: "")
                .put("rec_track", rankSubElement?.ubt?.recTrack ?: "")
                .put("ubtTraceId", rankSubElement?.ubt?.traceId ?: "")
                .put("isAd", if (advertis != null) "true" else "false")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendRankListItem.ubtV2,
                (modulePosition + 1).toString(),
                albumRank?.title,
                (recommendRankListItem.innerListSelectedIndex + 1).toString(),
                rankSubElement?.title,
                (index + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank?.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement?.ubtV2)
            if (data?.isLocalCache == true) {
                trace.isLocalCache
            }
            trace.createTrace()
            // 广告曝光上报
            if (advertis != null && advertis is Advertis && !advertis.isShowedToRecorded && !data?.isLocalCache!!) {
                AdManager.adRecord(BaseApplication.getMyApplicationContext(), advertis,
                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST)
                                .positionNew(index + 1)
                                .modulePosition(modulePosition + 1)
                                .build())
                advertis.isShowedToRecorded = true
            }
            if (advertis != null && advertis is Advertis && !advertis.isRecordedSubPercent && !data?.isLocalCache!!
                    && ViewStatusUtil.getViewVisibleAreaRealPercent(itemView) >= AD_REPORT_SUB_PERCENT) {
                // 做曝光50%的上报
                AdManager.reportSubPercentShow(advertis, data.xmRequestId)
                advertis.isRecordedSubPercent = true
            }

            HomeRealTimeTraceUtils.traceItemShow(data, recommendRankListItem, albumRank, rankSubElement, itemView, index)
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 折叠屏是否展开
        private fun isFoldScreenWithExpand(): Boolean {
            val context = BaseApplication.getTopActivity()
            return BaseUtil.isCollapsibleScreenOnLandscapeMode(context) || BaseUtil.isCollapsibleScreenOnPortraitExpandMode(
                context
            )
        }
    }

    // tab切换事件
    interface OnTabSelectListener {

        fun onSelect(curPosition: Int)

        fun clickTitle(prePosition: Int, curPosition: Int)
    }

    interface IOnListTipShowListener {
        fun onItemShow(shouldShow: Boolean, forceShow: Boolean)
    }
}