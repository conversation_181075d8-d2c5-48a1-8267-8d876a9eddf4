package com.ximalaya.ting.android.main.playpage.playy.media

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.lifecycle.ViewModelProvider
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.YPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.YDocAndGraphicsComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.YDocOnCoverComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.skin.SkinChangeListener
import com.ximalaya.ting.android.main.playpage.playy.view.PlayMediaBackground
import com.ximalaya.ting.android.main.playpage.view.DocBackgroundImageView
import com.ximalaya.ting.android.xmutil.Logger

class DocComponentWrapper(
    private val playContainer: IPlayContainer,
    private val coverManager: YCoverComponentsManager,
    private val bottomRefView: View,
): XBaseCoverComponent(), SkinChangeListener {

    private val TAG = "DocComponentWrapper"

    private var yPlayViewModel: YPlayViewModel? = null

    private val aiDocComponent = YDocOnCoverComponent(::notifyGraph)
    private val graphDocComponent = YDocAndGraphicsComponent(
        bottomRefView = <EMAIL>,
        updateComponents = {
            val soundInfo = curSoundInfo
            if (soundInfo != null) {
                updateComponent(soundInfo)
            }
        },
        isDocShowing = {
            aiDocComponent.isVisible
        }
    )

    private var backgroundView: PlayMediaBackground? = null

    private fun notifyGraph() {
        if (graphDocComponent.isVisible) {
            graphDocComponent.onDocShow(true)
        }
    }

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)

        aiDocComponent.apply {
            coverComponentsManager = coverManager
            onCreate(fragment)
        }
        graphDocComponent.apply {
            coverComponentsManager = coverManager
            onCreate(fragment)
        }

        inflateAndInitUI()

        yPlayViewModel = ViewModelProvider(fragment!!.requireActivity()).get(YPlayViewModel::class.java)
        yPlayViewModel?.coverMainColor?.observe(fragment.viewLifecycleOwner) {
            updateBg()
        }

        yPlayViewModel?.aiGMainColor?.observe(fragment.viewLifecycleOwner) {
            updateBg()
        }

        playContainer.registerSkinChangeListener(this)
    }

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        updateBg()
    }

    private fun updateBg() {
        if (PSkinManager.isEnabled && PSkinManager.getThemeColor() != Color.TRANSPARENT) {
            backgroundView?.setThemeColor(Color.TRANSPARENT)
        } else {
            val docTheme = yPlayViewModel?.docTheme
            val coverTheme = yPlayViewModel?.coverTheme
            val color = if (docTheme != null && docTheme.originalColor != PlayPageDataManager.DEFAULT_BACKGROUND_COLOR) {
                docTheme.originalColor
            } else coverTheme?.originalColor?: return
            backgroundView?.setThemeColor(color)
        }
    }

    override fun inflate(): View {
        val context = mFragment.requireContext()
        return FrameLayout(context).apply {

            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            if (!PSkinManager.isEnabled) {
                val backgroundView = PlayMediaBackground(context)
                addView(backgroundView, FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                ))

                <EMAIL> = backgroundView
            }

            val docBackgroundView = DocBackgroundImageView(context).apply {
                scaleType = ImageView.ScaleType.CENTER_CROP
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                id = R.id.main_iv_doc_background
            }
            addView(docBackgroundView)

            val gDocView = graphDocComponent?.view
            if (gDocView != null) {
                addView(gDocView, ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ))
            }

            val aiDocView = aiDocComponent.view
            if (aiDocView != null) {
                addView(aiDocView, FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ).apply { topMargin = BaseUtil.getStatusBarHeight(context) + 50.dp })
                updateAiBottom(aiDocView)
//                bottomRefView?.afterLayout { updateAiBottom(aiDocView) }
                bottomRefView.addOnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                    if (v.height > 0) {
                        updateAiBottom(aiDocView)
                    }
                }
            }
        }
    }

    private fun updateAiBottom(aiDocView: View) {
        val layoutParams = aiDocView.layoutParams as? FrameLayout.LayoutParams
        val bottomView = bottomRefView
        val bottomViewParent = bottomRefView?.parent as? View
        if (layoutParams != null && bottomView != null && bottomView.top > 0 && bottomViewParent != null && bottomViewParent.height > 0) {
            val target = bottomViewParent.height - bottomView.top
            if (target > layoutParams.bottomMargin) {
                layoutParams.bottomMargin = target
                aiDocView.requestLayout()
            }
        }
    }

    override fun initUi() {
    }

    private fun updateComponent(soundInfo: PlayingSoundInfo) {
        val aiC = aiDocComponent
        val gC = graphDocComponent

        if (aiC.needShowThisComponent(soundInfo)) {
            Logger.d(TAG, "aiC.needShowThisComponent = true")

            if (!aiC.isVisible) {
                aiC.show()
                Logger.d(TAG, "aiC call show()")
                playContainer.addPlayStatusListener(aiDocComponent)
                HandlerManager.postOnUIThread { aiC.onSoundInfoLoaded(soundInfo) }
            } else {
                aiC.onSoundInfoLoaded(soundInfo)
            }

            if (gC.needShowThisComponent(soundInfo)) {
                Logger.d(TAG, "gC needShowThisComponent true")
                if (!gC.isVisible) {
                    gC.show()
                    Logger.d(TAG, "gC call show ")
                    playContainer.addPlayStatusListener(gC)
                    HandlerManager.postOnUIThread { gC.onSoundInfoLoaded(soundInfo) }
                } else {
                    gC.onSoundInfoLoaded(soundInfo)
                }

                aiC.onGAttached(gC)
                gC.onDocShow(true)
            } else {
                gC.hide()
                aiC.onGAttached(null)
                Logger.w(TAG, "gC call hide ")
                playContainer.removePlayStatusListener(gC)
            }
        } else {
            Logger.w(TAG, "aiC call hide ")
            aiDocComponent.hide()
            playContainer.removePlayStatusListener(aiDocComponent)

            if (gC.needShowThisComponentWithLoading(soundInfo)) {
                Logger.d(TAG, "gC needShowThisComponent true")
                if (!gC.isVisible) {
                    gC.show()
                    Logger.d(TAG, "gC call show")
                    playContainer.addPlayStatusListener(gC)
                    HandlerManager.postOnUIThread { gC.onSoundInfoLoaded(soundInfo) }
                } else {
                    gC.onSoundInfoLoaded(soundInfo)
                }
                gC.onDocShow(false)
            } else {
                gC.hide()
                Logger.w(TAG, "gC call hide")
                playContainer.removePlayStatusListener(graphDocComponent)
            }
        }
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        if (soundInfo != null) updateComponent(soundInfo)
    }

    override fun getViewStubId() = 0

    override fun onResume() {
        super.onResume()

        val aic = aiDocComponent
        if (aic?.isVisible == true) {
            aiDocComponent?.onResume()
            playContainer.addPlayStatusListener(aic)
        }

        val gc = graphDocComponent
        if (gc?.isVisible == true) {
            graphDocComponent?.onResume()
            playContainer.addPlayStatusListener(gc)
        }
    }

    override fun onPause() {
        super.onPause()
        val aic = aiDocComponent
        if (aic?.isVisible == true) {
            aic.onPause()
            playContainer.removePlayStatusListener(aic)
        }

        val gc = graphDocComponent
        if (gc?.isVisible == true) {
            gc.onPause()
            playContainer.removePlayStatusListener(gc)
        }
    }

    fun updateDocTranslate(isOpen: Boolean) {
        aiDocComponent.performTranslateClick(isOpen)
    }

    override fun onDestroy() {
        super.onDestroy()
        aiDocComponent.onDestroy()
        graphDocComponent.onDestroy()
        playContainer.unregisterSkinChangeListener(this)
    }
}