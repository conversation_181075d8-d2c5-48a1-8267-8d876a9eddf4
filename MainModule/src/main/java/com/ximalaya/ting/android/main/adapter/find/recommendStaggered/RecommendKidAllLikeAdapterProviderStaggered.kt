package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.marginStart
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.adapter.base.BaseQuickAdapter
import com.ximalaya.ting.android.host.adapter.base.viewholder.BaseViewHolder
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.KidAllLikeTabConfigBean
import com.ximalaya.ting.android.main.model.rec.KidAllLikeTabInfo
import com.ximalaya.ting.android.main.model.rec.KidAllLikeTabList
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendKidAllLikeItem
import com.ximalaya.ting.android.main.playpage.playy.tabs.english.KidHttpApi
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * author : bin.hou
 * time   : 2024/8/05
 * desc   : 大家都爱听组件
 */
class RecommendKidAllLikeAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendKidAllLikeAdapterProviderStaggered.KidAllLikeListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendKidAllLikeAdapterProviderStaggered.KidAllLikeListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendKidAllLikeAdapterProviderStaggered.KidAllLikeListCardViewHolder, RecommendItemNew> {

    private var mHolder: KidAllLikeListCardViewHolder? = null

    class KidAllLikeListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val firstTabList: RecyclerView = convertView.findViewById(R.id.firstTabList)
        val secondTabList: RecyclerView = convertView.findViewById(R.id.secondTabList)
        val rvAlbumList: RecyclerView = itemView.findViewById(R.id.rvAlbumList)
        val tvMore: TextView = itemView.findViewById(R.id.seeMoreText)
        var startSnapHelper: StartSnapHelper? = null

        var recommendItemNew: RecommendItemNew? = null
        var kidTabItem: KidAllLikeTabInfo? = null
        var moduleItem: RecommendKidAllLikeItem? = null

        //专辑adapter
        var albumAdapter: KidAllLikeAlbumItemAdapter? = null

        //第一行adapter
        var firstLineAdapter: TingFirstLineAdapter? = null

        //第二行adapter
        var secondLineAdapter: TingSecondLineAdapter? = null

        //缓存数据
        val cacheData: HashMap<String, HashMap<String, List<CommonSubElement>>> = hashMapOf()

        var mOldState = RecyclerView.SCROLL_STATE_IDLE

        var modulePosition: Int = 0
    }

    override fun createViewHolder(convertView: View?): KidAllLikeListCardViewHolder? {
        convertView ?: return null
        return KidAllLikeListCardViewHolder(convertView)
    }

    override fun bindViewHolder(
        holder: KidAllLikeListCardViewHolder?,
        position: Int,
        recommendItem: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItem == null) {
            return
        }
        mHolder = holder
        holder.modulePosition = position
        if (mHolder?.recommendItemNew == recommendItem
            && (mHolder?.firstTabList?.childCount ?: 0) > 0
            && (mHolder?.secondTabList?.childCount ?: 0) > 0
            && (mHolder?.rvAlbumList?.childCount ?: 0) > 0
        ) {
            return
        }
        mHolder?.cacheData?.clear()
        //标签RecycleView
        val context = holder.itemView.context
        val centerLayoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        holder.firstTabList.itemAnimator = null
        holder.secondTabList.itemAnimator = null
        holder.firstTabList.layoutManager = centerLayoutManager

        mHolder?.recommendItemNew = recommendItem
        mHolder?.moduleItem = mHolder?.recommendItemNew?.item as? RecommendKidAllLikeItem ?: return
        mHolder?.kidTabItem = mHolder?.moduleItem?.ext?.tabInfo ?: return
        val kidTabList = mHolder?.kidTabItem?.tab ?: return

        val firstSelectList = kidTabList[0].selectList ?: return
        mHolder?.firstLineAdapter = TingFirstLineAdapter()
        holder.firstTabList.adapter = mHolder?.firstLineAdapter
        mHolder?.firstLineAdapter?.setList(firstSelectList)
        //设置初始选中位置
        var firstLineDefaultPosition = firstSelectList.indexOfFirst { it.locationed }
        if (firstLineDefaultPosition < 0) {
            firstLineDefaultPosition = 0
        }
        if (mHolder?.moduleItem!!.firstLineSelectPosition >= 0) {
            mHolder?.firstLineAdapter?.updateSelectPosition(mHolder?.moduleItem!!.firstLineSelectPosition, false)
        } else {
            mHolder?.firstLineAdapter?.updateSelectPosition(firstLineDefaultPosition, false)
        }
        mHolder?.firstLineAdapter?.setOnItemClickListener { _, _, p ->
            mHolder?.firstLineAdapter?.updateSelectPosition(p)
            reportAllLikeSelectTab()
            onAnyClickTrace(position)
            loadData()
            trackTabClick(true, position, p)
        }

        //年龄RecycleView
        val secondLayoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        holder.secondTabList.layoutManager = secondLayoutManager
        val secondSelectList = kidTabList[1].selectList ?: return
        mHolder?.secondLineAdapter = TingSecondLineAdapter()
        holder.secondTabList.adapter = mHolder?.secondLineAdapter
        mHolder?.secondLineAdapter?.setList(secondSelectList)
        //设置初始选中位置
        var secondLineDefaultPosition = secondSelectList.indexOfFirst { it.locationed }
        if (secondLineDefaultPosition < 0) {
            secondLineDefaultPosition = 0
        }
        if (mHolder?.moduleItem!!.secondLineSelectPosition >= 0) {
            mHolder?.secondLineAdapter?.updateSelectPosition(mHolder?.moduleItem!!.secondLineSelectPosition, false)
        } else {
            mHolder?.secondLineAdapter?.updateSelectPosition(secondLineDefaultPosition, false)
        }
        mHolder?.secondLineAdapter?.setOnItemClickListener { _, _, p ->
            mHolder?.secondLineAdapter?.updateSelectPosition(p)
            reportAllLikeSelectTab()
            onAnyClickTrace(position)
            loadData()
            trackTabClick(false, position, p)
        }

        val spanCount = if (RecommendFragmentTypeManager.isNewSceneCard() && position == 1) {
            3
        } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() && position == 1) {
            2
        } else {
            3
        }
        //专辑RecycleView
        val layoutManager =
            GridLayoutManager(convertView?.context, spanCount, GridLayoutManager.HORIZONTAL, false)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (mHolder?.albumAdapter?.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) spanCount else 1
            }
        }
        holder.rvAlbumList.layoutManager = layoutManager
        if (holder.startSnapHelper == null) {
            holder.startSnapHelper = StartSnapHelper()
            holder.startSnapHelper?.attachToRecyclerView(holder.rvAlbumList)
            holder.startSnapHelper?.setContainerView(holder.rvAlbumList)
        }
        holder.rvAlbumList.clearOnScrollListeners()
        holder.rvAlbumList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mHolder?.mOldState) {
                    return
                }
                mHolder?.mOldState = newState
                if (mHolder?.mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemAlbumShow(mHolder?.recommendItemNew, position, holder)
                    mHolder?.recommendItemNew?.firstVisiblePosition =
                        (holder.rvAlbumList.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                }
            }
        })
        //mHolder?.firstLineAdapter?.onItemBind = { tab, pos, view ->
        //    traceWrapper {
        //        if (ViewStatusUtil.viewIsRealShowing(view)) {
        //            trackTabShow(true, position, pos, kidTabList[0], tab)
        //        }
        //    }
        //}
        //mHolder?.secondLineAdapter?.onItemBind = { tab, pos, view ->
        //    traceWrapper {
        //        if (ViewStatusUtil.viewIsRealShowing(view)) {
        //            trackTabShow(false, position, pos, kidTabList[1], tab)
        //        }
        //    }
        //}
        val moduleItem = mHolder?.moduleItem ?: return
        mHolder?.albumAdapter = KidAllLikeAlbumItemAdapter(
            dataAction,
            fragment,
            moduleItem,
            mHolder?.recommendItemNew,
            moduleItem.subElements!!,
            position,
            holder.rvAlbumList,
            spanCount,
            holder.rootView,
            !mHolder?.kidTabItem?.moreJumpUrl.isNullOrEmpty()
        )
        mHolder?.albumAdapter?.onItemClick = { view, album, pos ->
            jump(fragment, album.landingPage)
            trackAlbumClick(album, position, pos, ViewStatusUtil.getViewVisibleAreaRealPercent(view))
            onAnyClickTrace(position)
        }
        holder.rvAlbumList.adapter = mHolder?.albumAdapter

        mHolder?.albumAdapter?.setRelaseJumpActivityListener {
            jumpMore(position, "slipe")
        }
        //默认数据放入缓存
        val albumList = moduleItem.subElements
        putCacheData(
            firstKey = firstSelectList[firstLineDefaultPosition].value,
            secondKey = secondSelectList[secondLineDefaultPosition].value,
            albumList
        )
        //加载数据
        loadData(fromClick = false)
        if (mHolder?.kidTabItem?.moreJumpUrl.isNullOrBlank()) {
            holder.tvMore.visibility = View.GONE
        } else {
            holder.tvMore.visibility = View.VISIBLE
        }
        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnOneClickListener {
            jumpMore(position, "click")
        }

        HandlerManager.postOnUIThreadDelay({
            traceOnItemShow(holder.recommendItemNew, position, holder)
        }, 300)
    }

    private fun trackAlbumShow(
        data: RecommendItemNew?,
        recommendCommonItem: RecommendKidAllLikeItem,
        commonSubElement: CommonSubElement,
        modulePosition: Int,
        albumPosition: Int,
        exploreArea: Int,
        itemView: View,
    ) {
        val selectPosition = mHolder?.moduleItem?.firstLineSelectPosition ?: return
        val firstLineData = mHolder?.firstLineAdapter?.getSelectData() ?: return
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData() ?: return
        val kidTabList = mHolder?.kidTabItem?.tab ?: return
        val recommendItemNew = mHolder?.recommendItemNew ?: return
        val firstLineSelectPosition = (mHolder?.moduleItem?.firstLineSelectPosition ?: 0) + 1
        val secondLineSelectPosition = (mHolder?.moduleItem?.secondLineSelectPosition ?: 0) + 1
        // 新首页-亲子大家都爱听模块-卡片  控件曝光
        val trace = XMTraceApi
            .Trace()
            .setMetaId(64683)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newHomePage")
            .put("xmRequestId", recommendItemNew.xmRequestId ?: "") // 客户端传
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("contentType", commonSubElement.contentType) // 客户端传。去重用
            .put("contentId", commonSubElement.refId?.toString()) // 客户端传。去重用
            .put("exploreArea", exploreArea.toString()) // 可见区域占屏幕的比例
            .put("positionNew", (albumPosition + 1).toString())// 客户端传。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("refId", commonSubElement.refId?.toString()) // 服务端传
            .put("bizType", commonSubElement.bizType ?: "") // 服务端传
            .put("tab_refId", firstLineData.value) // 服务端传，传第一排 tab 的 id
            .put("tab_title", firstLineData.showName) // 服务端传，传第一排 tab 的文案
            .put("tab2_title", secondLineData.showName) // 服务端传，第二排的 tab 的 title
            .put("tab2_refId", secondLineData.value) // 服务端传，第二排的 tab 的 refId
            .put("tab_type", kidTabList[0].type) // 服务端传，第一排 tab 的 tab_type
            .put("tab2_type", kidTabList[1].type) // 服务端传，第二排 tab 的 tab_type，当上报的第一排 tab 的曝光点击时不用传该字段
            .put("tabPosition", (selectPosition + 1).toString())
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            mHolder?.moduleItem?.ubtV2,
            (modulePosition + 1).toString(),
            tab1Title = firstLineData.showName,
            tab1Position = firstLineSelectPosition.toString(),
            tab2Title = secondLineData.showName,
            tab2Position = secondLineSelectPosition.toString(),
            contentTitle = commonSubElement.title,
            contentPosition = (albumPosition + 1).toString()
        )
        if (recommendItemNew.isLocalCache) {
            trace.isLocalCache
        }
        RecommendNewUbtV2Manager.addUbtV2Data(trace, mHolder?.moduleItem?.ubtV2)
        trace.createTrace()

        HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, commonSubElement, itemView, albumPosition)
    }

    private fun trackAlbumClick(
        commonSubElement: CommonSubElement,
        modulePosition: Int,
        albumPosition: Int,
        exploreArea: Int,
    ) {
        val selectPosition = mHolder?.moduleItem?.firstLineSelectPosition ?: return
        val firstLineData = mHolder?.firstLineAdapter?.getSelectData() ?: return
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData() ?: return
        val kidTabList = mHolder?.kidTabItem?.tab ?: return

        val firstLineSelectPosition = (mHolder?.moduleItem?.firstLineSelectPosition ?: 0) + 1
        val secondLineSelectPosition = (mHolder?.moduleItem?.secondLineSelectPosition ?: 0) + 1
        // 新首页-亲子大家都爱听模块-卡片  点击事件
        val trace = XMTraceApi.Trace().click(64682) // 用户点击时上报
            .put("currPage", "newHomePage").put("xmRequestId", mHolder?.recommendItemNew?.xmRequestId ?: "") // 客户端传
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("contentType", commonSubElement.contentType) // 客户端传。去重用
            .put("contentId", commonSubElement.id?.toString()) // 客户端传。去重用
            .put("exploreArea", exploreArea.toString()) // 可见区域占屏幕的比例
            .put("positionNew", (albumPosition + 1).toString()) // 客户端传。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("refId", commonSubElement.refId?.toString()) // 服务端传
            .put("bizType", commonSubElement.bizType ?: "") // 服务端传
            .put("tab_refId", firstLineData.value) // 服务端传，传第一排 tab 的 id
            .put("tab_title", firstLineData.showName) // 服务端传，传第一排 tab 的文案
            .put("tab2_title", secondLineData.showName) // 服务端传，第二排的 tab 的 title
            .put("tab2_refId", secondLineData.value) // 服务端传，第二排的 tab 的 refId
            .put("tab_type", kidTabList[0].type) // 服务端传，第一排 tab 的 tab_type
            .put("tab2_type", kidTabList[1].type) // 服务端传，第二排 tab 的 tab_type，当上报的第一排 tab 的曝光点击时不用传该字段
            .put("tabPosition", (selectPosition + 1).toString())
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            mHolder?.moduleItem?.ubtV2,
            (modulePosition + 1).toString(),
            tab1Title = firstLineData.showName,
            tab1Position = firstLineSelectPosition.toString(),
            tab2Title = secondLineData.showName,
            tab2Position = secondLineSelectPosition.toString(),
            contentTitle = commonSubElement.title,
            contentPosition = (albumPosition + 1).toString()
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace, mHolder?.moduleItem?.ubtV2)
        trace.createTrace()
    }

    private fun trackTabShow(
        isFirstLine: Boolean,
        modulePosition: Int,
        tabPosition: Int,
        tabInfoList: KidAllLikeTabList?,
        tabConfigBean: KidAllLikeTabConfigBean?,
    ) {
        val recommendItemNew = mHolder?.recommendItemNew ?: return

        val firstLineData = mHolder?.firstLineAdapter?.getSelectData() ?: return
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData() ?: return
        val firstLineSelectPosition = (mHolder?.moduleItem?.firstLineSelectPosition ?: 0) + 1
        val secondLineSelectPosition = (mHolder?.moduleItem?.secondLineSelectPosition ?: 0) + 1
        // 新首页-亲子大家都爱听模块-tab  控件曝光
        val trace = XMTraceApi.Trace()
        trace.setMetaId(64686)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newHomePage")
            .put("xmRequestId", recommendItemNew.xmRequestId ?: "") // 客户端传
            .put("contentId", tabConfigBean?.value) // 客户端传
            .put("contentType", "KidAllLikeTab") // 客户端传
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("tabPosition", (tabPosition + 1).toString()) // 客户端传。从 1 开始计数，第一排 tab 的位置
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            mHolder?.moduleItem?.ubtV2,
            (modulePosition + 1).toString(),
            tab1Title = firstLineData.showName,
            tab1Position = firstLineSelectPosition.toString(),
            tab2Title = if (isFirstLine) null else secondLineData.showName,
            tab2Position = if (isFirstLine) null else secondLineSelectPosition.toString(),
        )
        if (isFirstLine) {
            trace.put("tab_refId", tabConfigBean?.value) // 服务端传，传第一排 tab 的 id
                .put("tab_title", tabConfigBean?.showName) // 服务端传，传第一排 tab 的文案
                .put("tab_type", tabInfoList?.type) // 服务端传，第一排 tab 的 tab_type
        } else {
            trace.put("tab2_refId", tabConfigBean?.value) // 服务端传，第二排的 tab 的 refId
                .put("tab2_title", tabConfigBean?.showName) // 服务端传，第二排的 tab 的 title
                .put("tab2_type", tabInfoList?.type) // 服务端传，第二排 tab 的 tab_type，当上报的第一排 tab 的曝光点击时不用传该字段
        }
        if (recommendItemNew.isLocalCache) {
            trace.isLocalCache
        }
        RecommendNewUbtV2Manager.addUbtV2Data(trace, mHolder?.moduleItem?.ubtV2)
        trace.createTrace()
    }

    private fun trackTabClick(
        isFirstLine: Boolean,
        modulePosition: Int,
        tabPosition: Int,
    ) {
        val firstLineData = mHolder?.firstLineAdapter?.getSelectData() ?: return
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData() ?: return
        val kidTabList = mHolder?.kidTabItem?.tab ?: return

        val firstLineSelectPosition = (mHolder?.moduleItem?.firstLineSelectPosition ?: 0) + 1
        val secondLineSelectPosition = (mHolder?.moduleItem?.secondLineSelectPosition ?: 0) + 1
        // 新首页-亲子大家都爱听模块-卡片  点击事件
        val trace = XMTraceApi.Trace()
        trace.click(64685) // 用户点击时上报
            .put("currPage", "newHomePage").put("xmRequestId", mHolder?.recommendItemNew?.xmRequestId ?: "") // 客户端传
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("refId", mHolder?.moduleItem?.refId?.toString()) // 服务端传
            .put("bizType", mHolder?.moduleItem?.bizType ?: "") // 服务端传
            .put("tabPosition", (tabPosition + 1).toString()) // 客户端传。从 1 开始计数，第一排 tab 的位置
            .put("action", "click")
        trace.put("tab_refId", firstLineData.value) // 服务端传，传第一排 tab 的 id
            .put("tab_title", firstLineData.showName) // 服务端传，传第一排 tab 的文案
            .put("tab_type", kidTabList[0].type) // 服务端传，第一排 tab 的 tab_type
        if (!isFirstLine) {
            trace.put("tab2_refId", secondLineData.value) // 服务端传，第二排的 tab 的 refId
                .put("tab2_title", secondLineData.showName) // 服务端传，第二排的 tab 的 title
                .put("tab2_type", kidTabList[1].type) // 服务端传，第二排 tab 的 tab_type，当上报的第一排 tab 的曝光点击时不用传该字段
        }
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            mHolder?.moduleItem?.ubtV2,
            (modulePosition + 1).toString(),
            tab1Title = firstLineData.showName,
            tab1Position = firstLineSelectPosition.toString(),
            tab2Title = if (isFirstLine) null else secondLineData.showName,
            tab2Position = if (isFirstLine) null else secondLineSelectPosition.toString(),
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace, mHolder?.moduleItem?.ubtV2)
        trace.createTrace()
    }

    //跳转更多
    private fun jumpMore(modulePosition: Int, action: String) {
        val kidTabList = mHolder?.kidTabItem?.tab ?: return
        if (mHolder?.kidTabItem?.moreJumpUrl.isNullOrBlank()) {
            return
        }
        val uri = Uri.parse(mHolder?.kidTabItem?.moreJumpUrl).buildUpon()
        val firstLineData = mHolder?.firstLineAdapter?.getSelectData()
        if (firstLineData != null) {
            uri.appendQueryParameter(kidTabList[0].key, firstLineData.value)
        }
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData()
        if (secondLineData != null) {
            uri.appendQueryParameter(kidTabList[1].key, secondLineData.value)
        }
        jump(fragment, uri.build().toString())

        // 新首页-亲子大家都爱听模块-更多  点击事件
        val trace1 = XMTraceApi.Trace().click(64684) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("xmRequestId", mHolder?.recommendItemNew?.xmRequestId ?: "") // 客户端传
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("action", action) // 客户端传， 区分是点击触发还是滑动触发
        SpmTraceUtil.addSpmTraceInfo(
            trace1,
            mHolder?.moduleItem?.ubtV2,
            (modulePosition + 1).toString(),
            tab1Title = "更多",
            tab1Position = "d01"
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace1, mHolder?.moduleItem?.ubtV2)
        trace1.createTrace()

        onAnyClickTrace(modulePosition)
    }

    private fun onAnyClickTrace(modulePosition: Int) {
        val trace = XMTraceApi.Trace().click(62176) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
            .put("xmRequestId", mHolder?.recommendItemNew?.xmRequestId) // 客户端传
            .put("card_adTopn", mHolder?.moduleItem?.cardAdCount.toString())
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            mHolder?.moduleItem?.ubtV2,
            (modulePosition + 1).toString()
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace, mHolder?.moduleItem?.ubtV2)
        trace.createTrace()
    }

    //保存缓存数据
    private fun putCacheData(firstKey: String?, secondKey: String?, list: List<CommonSubElement>?) {
        if (firstKey.isNullOrBlank() || secondKey.isNullOrBlank() || list.isNullOrEmpty()) {
            return
        }
        val cacheData = mHolder?.cacheData ?: return
        var firstLineMap = cacheData[firstKey]
        if (firstLineMap == null) {
            firstLineMap = HashMap()
            cacheData[firstKey] = firstLineMap
        }
        firstLineMap[secondKey] = list
    }

    /**
     * 切换Tab请求数据
     */
    private fun loadData(fromClick: Boolean = true) {
        val firstLineData = mHolder?.firstLineAdapter?.getSelectData() ?: return
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData() ?: return
        val kidTabList = mHolder?.kidTabItem?.tab ?: return
        val cacheData = mHolder?.cacheData ?: return
        //优先取缓存
        val list = cacheData[firstLineData.value]?.get(secondLineData.value)
        if (!list.isNullOrEmpty()) {
            setDataList(fromClick, list)
            return
        }
        showPlaceholder()
        KidHttpApi.queryAllLikeListenComponentAlbum(kidTabList[0].type,
            firstLineData.value,
            kidTabList[1].type,
            secondLineData.value,
            object : IDataCallBack<List<CommonSubElement>> {
                override fun onSuccess(data: List<CommonSubElement>?) {
                    setDataList(fromClick, data)
                    putCacheData(firstLineData.value, secondLineData.value, data)
                }

                override fun onError(code: Int, message: String?) {
                }
            })
    }

    //显示占位图
    private fun showPlaceholder() {
        mHolder?.albumAdapter?.updateIsPlaceholder(true)
        mHolder?.albumAdapter?.setList(null)
        val placeholderAlbumSize = 16
        val list = ArrayList<CommonSubElement>(placeholderAlbumSize)
        for (i in 0 until placeholderAlbumSize) {
            list.add(CommonSubElement(id = null, refId = null, ubtV2 = null))
        }
        mHolder?.albumAdapter?.setList(list)
        mHolder?.albumAdapter?.recyclerView?.scrollToPosition(0)
    }

    //设置数据列表
    private fun setDataList(fromClick: Boolean, data: List<CommonSubElement>?) {
        if (data.isNullOrEmpty()) {
            return
        }
        mHolder?.albumAdapter?.updateIsPlaceholder(false)
        mHolder?.albumAdapter?.setList(data)
        mHolder?.albumAdapter?.recyclerView?.scrollToPosition(0)

        if (fromClick) {
            HandlerManager.postOnUIThreadDelay({
                traceOnItemAlbumShow(mHolder?.recommendItemNew, mHolder?.modulePosition ?: 0, mHolder)
            }, 300)
        }
    }

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_kid_all_like_card, parent, false)
    }

    private fun reportAllLikeSelectTab() {
        val firstLineData = mHolder?.firstLineAdapter?.getSelectData() ?: return
        val secondLineData = mHolder?.secondLineAdapter?.getSelectData() ?: return
        val kidTabList = mHolder?.kidTabItem?.tab ?: return
        KidHttpApi.reportAllLikeSelectTab(kidTabList[0].type,
            firstLineData.value,
            kidTabList[1].type,
            secondLineData.value,
            object : IDataCallBack<Boolean> {
                override fun onSuccess(data: Boolean?) {
                }

                override fun onError(code: Int, message: String?) {
                }

            })
    }

    override fun onConfigurationChanged(holder: KidAllLikeListCardViewHolder?) {
    }

    private fun traceOnItemAlbumShow(
        data: RecommendItemNew?, position: Int, holder: KidAllLikeListCardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = mHolder?.moduleItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        if (recommendCommonItem.cardAdCount == -1) {
            recommendCommonItem.cardAdCount = checkAdCunt(recommendCommonItem.subElements)
        }
        fragment.postOnUiThread {
            if (!fragment.canUpdateUi()) {
                return@postOnUiThread
            }
            if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                // 新首页-首页大卡模块  控件曝光
                val trace = XMTraceApi.Trace().setMetaId(62177)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "newHomePage").put("modulePosition", (position + 1).toString())
                    .put("xmRequestId", data.xmRequestId) // 客户端传
                    .put("contentType", data.itemType) // 客户端传
                    .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendCommonItem.ubtV2,
                    (position + 1).toString()
                )
                if (data.isLocalCache) {
                    trace.isLocalCache
                }
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                trace.createTrace()

                val childCount = holder.rvAlbumList.childCount
                for (i in 0 until childCount) {
                    val view = holder.rvAlbumList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val exploreArea = ViewStatusUtil.getViewVisibleAreaRealPercent(view)
                        trackAlbumShow(
                            data,
                            recommendCommonItem,
                            subElement,
                            position,
                            index,
                            exploreArea,
                            view
                        )
                    }
                }
            }
        }
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?, position: Int, holder: KidAllLikeListCardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = mHolder?.moduleItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        if (recommendCommonItem.cardAdCount == -1) {
            recommendCommonItem.cardAdCount = checkAdCunt(recommendCommonItem.subElements)
        }
        fragment.postOnUiThread {
            if (!fragment.canUpdateUi()) {
                return@postOnUiThread
            }
            if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                // 新首页-首页大卡模块  控件曝光
                val trace = XMTraceApi.Trace().setMetaId(62177)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "newHomePage").put("modulePosition", (position + 1).toString())
                    .put("xmRequestId", data.xmRequestId) // 客户端传
                    .put("contentType", data.itemType) // 客户端传
                    .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendCommonItem.ubtV2,
                    (position + 1).toString()
                )
                if (data.isLocalCache) {
                    trace.isLocalCache
                }
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                trace.createTrace()

                val childCount = holder.rvAlbumList.childCount
                for (i in 0 until childCount) {
                    val view = holder.rvAlbumList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val exploreArea = ViewStatusUtil.getViewVisibleAreaRealPercent(view)
                        trackAlbumShow(
                            data,
                            recommendCommonItem,
                            subElement,
                            position,
                            index,
                            exploreArea,
                            view
                        )
                    }
                }

                val kidTabList = mHolder?.kidTabItem?.tab ?: return@postOnUiThread
                for (i in 0 until holder.firstTabList.childCount) {
                    val view = holder.firstTabList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val item = view.getTag(R.id.main_id_item_data) as? KidAllLikeTabConfigBean ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        trackTabShow(true, position, index, kidTabList[0], item)
                    }
                }

                for (i in 0 until holder.secondTabList.childCount) {
                    val view = holder.secondTabList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val item = view.getTag(R.id.main_id_item_data) as? KidAllLikeTabConfigBean ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        trackTabShow(false, position, index, kidTabList[1], item)
                    }
                }
            }
        }
    }


    private fun checkAdCunt(subElements: List<CommonSubElement>?): Int {
        if (subElements.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (subElement in subElements) {
            val adInfo = subElement.ext?.subRefInfo?.businessExtraInfo?.adInfo
            if (!adInfo.isNullOrEmpty()) {
                adCount++
            }
        }
        return adCount
    }

    inner class TingSecondLineAdapter :
        BaseQuickAdapter<KidAllLikeTabConfigBean, BaseViewHolder>(R.layout.main_kid_item_all_like_second_line_title) {

        var onItemBind: (KidAllLikeTabConfigBean, Int, View) -> Unit = { _, _, _ -> }

        fun getSelectData() = data[mHolder?.moduleItem!!.secondLineSelectPosition]

        fun updateSelectPosition(position: Int, smooth: Boolean = true) {
            val origin = mHolder?.moduleItem!!.secondLineSelectPosition
            mHolder?.moduleItem!!.secondLineSelectPosition = position
            notifyItemChanged(position, listOf(1))
            notifyItemChanged(origin, listOf(1))
            scrollToPositionTab(recyclerView, mHolder?.moduleItem!!.secondLineSelectPosition, smooth)
        }

        override fun convert(holder: BaseViewHolder, item: KidAllLikeTabConfigBean) {
            onItemBind(item, holder.layoutPosition, holder.itemView)
            onBindHolderInner(holder, item)
        }

        override fun convert(holder: BaseViewHolder, item: KidAllLikeTabConfigBean, payloads: List<Any>) {
            super.convert(holder, item, payloads)
            onBindHolderInner(holder, item)
        }

        private fun onBindHolderInner(holder: BaseViewHolder, item: KidAllLikeTabConfigBean) {
            val titleTv = holder.getView<TextView>(R.id.tvAgeTitle)
            titleTv.text = item.showName
            if (holder.layoutPosition == 0) {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
                titleTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
            } else {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                titleTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }
            if (mHolder?.moduleItem!!.secondLineSelectPosition == holder.layoutPosition) {
                // 选中Tab
                titleTv.setBackgroundResource(R.drawable.main_kid_bg_shape_all_like_ting_age_selected)
                titleTv.setTextColor(ContextCompat.getColor(context, R.color.main_color_ff4444))
            } else {
                // 未选中Tab
                titleTv.setBackgroundResource(R.drawable.main_kid_bg_shape_all_like_ting_age_unselected_new)
                titleTv.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.main_color_662c2c3c_8d8d91
                    )
                )
            }
            holder.itemView.setTag(R.id.main_id_item_data, item)
            holder.itemView.setTag(R.id.main_id_data_index, holder.layoutPosition)
        }
    }

    inner class TingFirstLineAdapter :
        BaseQuickAdapter<KidAllLikeTabConfigBean, BaseViewHolder>(R.layout.main_kid_item_all_like_ting_first_line_title) {

        var onItemBind: (KidAllLikeTabConfigBean, Int, View) -> Unit = { _, _, _ -> }

        fun getSelectData() = data[mHolder?.moduleItem!!.firstLineSelectPosition]

        fun updateSelectPosition(position: Int, smooth: Boolean = true) {
            val origin = mHolder?.moduleItem!!.firstLineSelectPosition
            mHolder?.moduleItem!!.firstLineSelectPosition = position
            notifyItemChanged(position, listOf(1))
            notifyItemChanged(origin, listOf(1))
            scrollToPositionTab(recyclerView, mHolder?.moduleItem!!.firstLineSelectPosition, smooth)
        }

        override fun convert(holder: BaseViewHolder, item: KidAllLikeTabConfigBean) {
            onItemBind(item, holder.layoutPosition, holder.itemView)
            onBindHolderInner(holder, item)
        }

        private fun onBindHolderInner(holder: BaseViewHolder, item: KidAllLikeTabConfigBean) {
            val titleTv = holder.getView<TextView>(R.id.tvTabTitle)
            if (holder.layoutPosition == data.size - 1) {
                holder.setVisible(R.id.viewLine, false)
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
                titleTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
            } else {
                holder.setVisible(R.id.viewLine, true)
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                titleTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }
            titleTv.text = item.showName
            if (mHolder?.moduleItem!!.firstLineSelectPosition == holder.layoutPosition) {
                // 选中Tab
                titleTv.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
                titleTv.textSize = 16f
                titleTv.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.main_color_2c2c3c_ffffff
                    )
                )
            } else {
                // 未选中Tab
                titleTv.typeface = Typeface.DEFAULT
                titleTv.textSize = 14f
                titleTv.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.main_color_662c2c3c_8d8d91
                    )
                )
            }
            holder.itemView.setTag(R.id.main_id_item_data, item)
            holder.itemView.setTag(R.id.main_id_data_index, holder.layoutPosition)
        }

        override fun convert(holder: BaseViewHolder, item: KidAllLikeTabConfigBean, payloads: List<Any>) {
            super.convert(holder, item, payloads)
            onBindHolderInner(holder, item)
        }
    }

    class KidAllLikeAlbumItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        // 页面
        private val fragment: BaseFragment2,
        // 卡片数据
        private val moduleItem: RecommendKidAllLikeItem,
        private val recommendItemNew: RecommendItemNew?,
        // 专辑列表
        list: List<CommonSubElement>,
        var modulePosition: Int,
        val recyclerView: RecyclerView,
        var spanCount: Int,
        val rootView: View,
        private val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        var onItemClick: (View, CommonSubElement, Int) -> Unit = { _, _, _ -> }
        var onItemBind: (CommonSubElement, Int) -> Unit = { _, _ -> }

        // 专辑列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            commonSubElementList.addAll(list)
        }

        //是否是占位模式
        private var isPlaceholderMode = false

        fun updateIsPlaceholder(isPlaceholder: Boolean) {
            this.isPlaceholderMode = isPlaceholder
        }

        @SuppressLint("SetTextI18n")
        private fun onBindViewHolderInner(holder: KidAllLikeListAlbumViewHolder, position: Int) {
            val remainder: Int = commonSubElementList.size % spanCount
            val start: Int =
                if (remainder == 0) commonSubElementList.size - spanCount else commonSubElementList.size - remainder
            val layoutParams = holder.cslContainerView.layoutParams
            val textViewContainerWithInPx: Int
            if (position >= start) {
                // 最后一列
                layoutParams.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                textViewContainerWithInPx = getRpAdaptSize(375 - 3) - if (enableJumpMore) 37.dp else 0
            } else {
                layoutParams.width = getRpAdaptSize(337 - getOffset())
                textViewContainerWithInPx = getRpAdaptSize(337 - getOffset() - 3)
            }
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)
            holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getSocialCoverSize())
            val padding = RecommendCornerUtils.getPaddingSize()
            holder.cslContainerView.apply {
                setPadding(paddingLeft, padding, paddingRight, padding)
            }
            holder.showTagsLayout.apply {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }

            if (isPlaceholderMode) {
                holder.itemTitleTv.text = ""
                holder.itemSubtitleTv.text = ""
                holder.albumCoverLayoutView.setAlbumCover("")
                holder.albumCoverLayoutView.setAlbumTag("")
                holder.layoutShowTags.visibility = View.GONE
                holder.rightPlaceholderLayout.visibility = View.VISIBLE
                return
            }
            holder.rightPlaceholderLayout.visibility = View.GONE
            val commonSubElement = commonSubElementList[position]
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            holder.itemView.isHapticFeedbackEnabled = true
            holder.itemView.setOnLongClickListener {
                if (commonSubElement.refId == null) {
                    return@setOnLongClickListener true
                }
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (modulePosition + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                if (!moduleItem.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(moduleItem.ubtV2)
                }
                if (!commonSubElement.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(commonSubElement.ubtV2)
                }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = commonSubElement.bizType ?: ""
                traceMap["contentId"] = commonSubElement.refId.toString()
                requestMap[HttpParamsConstants.PARAM_ALBUM_ID] = commonSubElement.refId.toString()
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] = commonSubElement.anchor?.uid.toString()
                requestMap["card_contentType"] = moduleItem.contentType ?: ""
                requestMap["card_bizType"] = moduleItem.bizType ?: ""
                requestMap["card_id"] = moduleItem.id.toString()

                //val disLikeLeve2Build = DisLikeLeve2Build()
                //disLikeLeve2Build.isFromAd = false
                //disLikeLeve2Build.anchorName = commonSubElement.anchor?.nickName
                //disLikeLeve2Build.requestMap = requestMap
                //disLikeLeve2Build.traceMap = traceMap
                //disLikeLeve2Build.onFeedBackListener = object :
                //    NewXmFeedBackPopDialog.IOnFeedBackListener() {
                //    override fun onDialogShow(showSuccess: Boolean) {
                //    }
                //
                //    override fun onFeedBack(feedPosition: Int, model: XmFeedInnerModel) {
                //
                //    }
                //}

                val typeStr = MoreFuncBuild.TYPE_ALBUM
                val refId = commonSubElement.refId
                val build = MoreFuncBuild.createCommonLongClickModel(
                    fragment, typeStr, refId, null, false, null
                )
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", commonSubElement.bizType ?: "")
                    put("contentId", commonSubElement.refId.toString())
                    put("modulePosition", (modulePosition + 1).toString())
                    put("positionNew", (position + 1).toString())
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap
                XmMoreFuncManager.checkShowMorePage(build)

                val albumId = commonSubElement.refId.toString()
                val trace = XMTraceApi.Trace().click(60896) // 用户点击时上报
                    .put("currPage", "newHomePage").put("albumId", albumId)
                    .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("moduleName", moduleItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                    .put("moduleId", moduleItem.id?.toString()) // 例如：100000000
                    .put("rec_src", moduleItem.ubt?.recSrc ?: "")
                    .put("rec_track", moduleItem.ubt?.recTrack ?: "")
                    .put("ubtTraceId", moduleItem.ubt?.traceId ?: "").put("contentId", albumId)
                    .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                    .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    .put("titleId", moduleItem.ext?.recWrap?.id?.toString()) // 传 recWrap 中的 id
                    .put(
                        "tagType", (commonSubElement.ext?.tagType ?: 0).toString()
                    ) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                    .put("socialTagId", commonSubElement.ext?.reasonId ?: "") // 传 reasonId
                    .put("trigger", moduleItem.ext?.extraInfo?.triggerId ?: "")
                    .put("moduleType", moduleItem.contentType ?: "")
                    .put("modulePosition", (modulePosition + 1).toString())
                    .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(it).toString()) // 可见区域占屏幕的比例
                    .put("isAd", "false").put("area", "item")
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    moduleItem.ubtV2,
                    (modulePosition + 1).toString(),
                    contentTitle = commonSubElement?.title,
                    contentPosition = (position + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
                trace.createTrace()

                true
            }
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
            holder.itemSubtitleTv.setTextColor(
                ContextCompat.getColor(
                    holder.itemSubtitleTv.context,
                    R.color.main_color_662c2c3c_8d8d91
                )
            )
            holder.itemTitleTv.text = commonSubElement.title
            if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitleTv.visibility = View.GONE
            } else {
                holder.itemSubtitleTv.visibility = View.VISIBLE
                holder.itemSubtitleTv.text = commonSubElement.ext?.reasonContent
                holder.itemSubtitleTv.maxLines = 1
                RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitleTv)
            }
            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
            val adWidth = if (adTagShow) 24 else 0
            val otherWidth =
                16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + 16 + adWidth //  else 16 + 70 + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(
                holder.layoutShowTags,
                commonSubElement.ext?.showTags,
                textViewContainerWithInPx - otherWidth.dp,
                commonSubElement.ext?.subTitle1,
                commonSubElement.ext?.subTitle2
            )
            holder.albumCoverLayoutView.setAlbumTag(commonSubElement.wrap?.ltSubscriptTag?.tag)
            if (holder.layoutRightTxtArea.layoutParams is ConstraintLayout.LayoutParams) {
                (holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams).startToEnd =
                    R.id.main_album_cover_layout
            }
            holder.albumCoverLayoutView.setAlbumCover(commonSubElement.cover ?: "")
            holder.itemView.setOnOneClickListener {
                if (commonSubElement.refId == null) {
                    return@setOnOneClickListener
                }
                onItemClick(holder.itemView, commonSubElement, position)
            }
            onItemBind(commonSubElement, position)
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is KidAllLikeListAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        override fun getItemCount(): Int {
            if (enableJumpMore) {
                return commonSubElementList.size + 1
            }
            return commonSubElementList.size
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): RecyclerView.ViewHolder {
            val view: View = ViewPool.getInstance().getView(
                HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                R.layout.main_kid_item_all_like_ting_album,
                parent,
                false,
                "KidAllLikeList"
            )
            return KidAllLikeListAlbumViewHolder(view)
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        @SuppressLint("NotifyDataSetChanged")
        fun setList(albumList: List<CommonSubElement>?) {
            commonSubElementList.clear()
            if (!albumList.isNullOrEmpty()) {
                commonSubElementList.addAll(albumList)
            }
            notifyDataSetChanged()
        }

        class KidAllLikeListAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var rightPlaceholderLayout: View = view.findViewById(R.id.right_placeholder_layout)
            var albumCoverLayoutView: AlbumCoverLayoutView = view.findViewById(R.id.main_album_cover_layout)
            var layoutRightTxtArea: View = view.findViewById(R.id.main_layout_right_txt_area)
            var itemSubtitleTv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var showTagsLayout: View = view.findViewById(R.id.main_show_tags_layout)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)

            init {
                resetSize()
            }

            fun resetSize() {
                val layoutParams = cslContainerView.layoutParams
                layoutParams.width = getRpAdaptSize(323)
            }
        }
    }

    companion object {

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        fun scrollToPositionTab(recyclerView: RecyclerView, position: Int, smooth: Boolean) {
            val itemView = recyclerView.findViewHolderForLayoutPosition(position)?.itemView
            if (itemView == null) {
                recyclerView.scrollToPosition(position)
                return
            }
            val itemWidth = itemView.width
            val offset =
                (recyclerView.width - itemWidth) / 2 - itemView.marginStart - 20.dp // 7 + 6 + 14/2为斜杠到文字的距离
            if (smooth) {
                recyclerView.smoothScrollToPositionWithOffset(position, offset)
            } else {
                val manager = recyclerView.layoutManager as? LinearLayoutManager ?: return
                manager.scrollToPositionWithOffset(position, offset)
            }
        }
    }
}