package com.ximalaya.ting.android.main.dialog.universal.pvip

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.airbnb.epoxy.SimpleEpoxyModel
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem.UpgradeProductModel
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.util.setOnOneClickListener

/**
 * Created by mark on 2024/6/12 13:54
 */
class PlatinumVipUpgradeMonthEModel(
    private val upgradeProduct: UpgradeProductModel,
    private val index: Int = 0,
    private val checkedIndex: Int = 0,
    private val onCheckChanged: (UpgradeProductModel) -> Unit
) : SimpleEpoxyModel(R.layout.main_item_platinum_vip_upgrade_month) {

    override fun bind(view: View) {
        super.bind(view)
        val vName: TextView = view.findViewById(R.id.main_tv_month)
        val vPrice: TextView = view.findViewById(R.id.main_tv_month_price)
        val vLabel: TextView = view.findViewById(R.id.main_tv_label)
        val cb: ImageView = view.findViewById(R.id.main_iv_month_check)
        if (upgradeProduct.label.isNullOrEmpty()) {
            vLabel.visibility = View.GONE
        } else {
            vLabel.visibility = View.VISIBLE
            ViewStatusUtil.setText(vLabel, upgradeProduct.label)
        }
        ViewStatusUtil.setText(vName, "升级${upgradeProduct.text}")
        SpanUtils.with(vPrice).append("¥").setFontSize(14, true).append(upgradeProduct.price)
            .create()
        cb.isSelected = index == checkedIndex
        view.setOnOneClickListener {
            onCheckChanged(upgradeProduct)
        }
    }

    override fun hashCode(): Int {
        return super.hashCode() * 31 + upgradeProduct.hashCode() + checkedIndex.hashCode()
    }

    override fun equals(o: Any?): Boolean {
        return if (this === o) {
            true
        } else if (o !is PlatinumVipUpgradeMonthEModel) {
            false
        } else if (!super.equals(o)) {
            false
        } else {
            val that = o as? PlatinumVipUpgradeMonthEModel
            that ?: return false
            val flag1 = that.index == index
            val flag2 = that.checkedIndex == checkedIndex
            val flag3 = that.upgradeProduct == upgradeProduct
            flag1 and flag2 and flag3
        }
    }
}