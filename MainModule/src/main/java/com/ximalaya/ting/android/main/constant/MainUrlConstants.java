package com.ximalaya.ting.android.main.constant;

import android.text.TextUtils;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.fragment.web.IWebFragment;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTrackingUrlMatcher;
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
import com.ximalaya.ting.android.main.request.MainStableUrlConstants;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;

import java.util.Locale;

/**
 * Created by tony.chen on 2017/3/6.
 *
 * <AUTHOR>
 */

public class MainUrlConstants extends MainStableUrlConstants {

    /*线上正式 HOST END*/
    private volatile static MainUrlConstants singleton;
    private String mCategoryCardAd;

    private MainUrlConstants() {
    }

    public static MainUrlConstants getInstanse() {
        if (singleton == null) {
            synchronized (MainUrlConstants.class) {
                if (singleton == null) {
                    singleton = new MainUrlConstants();
                }
            }
        }
        return singleton;
    }

    /*线上正式 DEBUG END*/

    public String getGroupChatHost() {//获取群聊host
        return getServerNetAddressHost() + "trump-web/";  //  http://mobile.test.ximalaya.com/trump-web
    }

    public String getRecommendAlbum() {//获取个性化推荐专辑
        return BaseUtil.chooseEnvironmentUrl(getARHost() + "rec-association/recommend/album/page",
                "http://*************:92/rec-association/recommend/album/page",
                getARHost() + "rec-association/recommend/album/page");

    }

    public String getAlbumsForHotPushUrl(){//今日热点推送落地页推荐专辑
        return getServerNetAddressHost() + "discovery-firstpage/headline/push/trackItems/" + System.currentTimeMillis();
    }

    /**
     * 获得播放页的一些详细信息
     */
    public String getPlayPageInfo() {
        return getServerNetAddressHost() + "mobile/track/v2/playpage";
    }

    public String getVideoRecommendInfosUrl() {
        return BaseUtil.chooseEnvironmentUrl(getARHost() + "rec-association/recommend/video",
                "http://ar.test.ximalaya.com/rec-association/recommend/video",
                getARHost() + "rec-association/recommend/video");
    }

    public String getCommentTags() {
        return getCommentBaseUrl() + "album/comment/taglib/ts-" + System.currentTimeMillis();
    }

    public String getCommentByAlbumCommentIdUrl() {
        return getCommentBaseUrl() + "album";
    }

    public String getRefundWebUrl() {
        return getMNetAddressHost() + "refund/list";
    }

    public String getPayAlbumRelatedGroup() {
        return getGroupChatHost() + "v1/group/common/others/paid/list";
    }

    private String getZoneHost() {

        return getMNetAddressHost() + "community/v2/";
    }

    public String getPayAlbumRelatedGroupOrZone() {

        return getZoneHost() + "communities/entrance-info/albums";
    }

    //    检查提问权限和敏感词
//    说明：此接口校验用户是否具有提问的权限，并且检查提问内容中是否包含敏感词。
//    511	问题 包含不合法词汇，请修改哦
//    515	您使用的是体验账号，仅支持体验付费内容哦~
    public String checkQuoraContent() {
        return getHotLineHost() + "question/native_check";
    }

    public String getQuoraToken() {
        return getHotLineHost() + "token/v1/" + System.currentTimeMillis();
    }

    public String xiPayForQuora() {
        return getHotLineHost() + "xipay/prepare/question/v1";
    }

    //问答  下单
    public String getOrderHotlineUrl() {

        return getHotLineHost() + "/api/v2/payments/xipoint/questions";
    }

    /**
     * 获取评论引导声音贴片
     * 生产环境：http://mobile.ximalaya.com/mobile-track/after/playpage/{ts}?trackId=123
     */
    public String getAudioPatchUrl(long trackId) {
        return getServerNetAddressHost() + "mobile-playpage/after/playpage/ts-" + System.currentTimeMillis() + "?trackId=" + trackId;
    }

    /**
     * 获得声音的评论列表
     */
    public String getTrackCommentList() {
        return getServerNetAddressHost() + "comment-mobile/v1/track/topcomment/" + System.currentTimeMillis();
    }

    public String getVipRightsUrl() {
        return getMNetAddressHost() + "vip/guide/v2/right/";
    }

    public String getRedeemCodeWebUrl(String redeemCode) {
        if (TextUtils.isEmpty(redeemCode)) {
            return getMNetAddressHostS() + "gatekeeper/hybrid-cdkeys-new/exchange";
        } else {
            return getMNetAddressHostS() + "gatekeeper/hybrid-cdkeys-new/exchange?redeemCode=" + redeemCode;
        }
    }

    public String getRedeemCodeWebUrl() {
        return getRedeemCodeWebUrl(null);
    }

    /**
     * 我的钱包-我的拼团url
     *
     * @param timestamp
     * @return
     */
    public String getMyGrouponUrl(long timestamp) {
        return getMNetAddressHostS() + "promotion/groupon/myGroupPage/" + timestamp;
    }

    public String getOpenCreditCardUrl() {
        return "http://m.ximalaya.com/carnival/imgShare/300";
    }

    /**
     * 热线问答主播页
     *
     * @param uid
     * @return
     */
    public String getAnchorHotLine(long uid) {
        return getHotLineHost() + "hotline/answerer/" + uid;
    }

    public String getMicTaskUrl() {
        return getMicTaskHost();
    }

    /**
     * 热线问答主播页
     *
     * @param uid
     * @return
     */
    public String getAnchorHotLine(long uid, long trackId) {
        return getHotLineHost() + "hotline/answerer/" + uid + "?trackId=" + trackId;
    }

    /**
     * 热线问答主页
     *
     * @return
     */
    public String getHotLineMain() {
        return getHotLineHost();
    }

    /**
     * 热线问答消息列表
     *
     * @return
     */
    public String getHotLineMessage() {
        return getHotLineHost() + "message";
    }

    //获取举报原因新接口
    public String getReportReasonNewUrl() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "https://www.ximalaya.com/themis-web/report/report_entrance/";
        } else {
            return "http://ops.test.ximalaya.com/themis-web/report/report_entrance/";
        }
    }

    //获取举报原因新接口
    public String getReportReasonUrlV3() {
        String prefix;
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            prefix = getServerNetAddressHost();
        } else {
            prefix = "http://************/";
        }
        return prefix + "themis-web/report/report_entrance/";
    }

    //上传举报原因新接口
    public String postReportNewUrl() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "https://www.ximalaya.com/themis-web/report/user_report";
        } else {
            return "http://ops.test.ximalaya.com/themis-web/report/user_report";
        }
    }

    //举报
    public String postReportUrlV3() {
        String prefix;
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            prefix = getServerNetAddressHost();
        } else {
            prefix = "http://************/";
        }
        return prefix + "themis-web/report/user_report/";
    }

    public String dislikeForNewLocalListen() {
        return getNewCityRecommendAddressHost() + "culture-citywave-api/dislike";
    }

    public String getNewUserRecommendCardDislikeUrl() {
        return getServerNetAddressHost() + "discovery-feed/addNegativeFeedback";
    }

    public String postCustomizationInfo() {
        return getServerNetAddressHost() + "persona/addInterestCard";
    }

    public String postDislikeInfo() {
        return getServerNetAddressHost() + "persona/traitCollect/addMainPageNoLike2";
    }

    public String postRecommendScore() {
        return getMNetAddressHostS() + "cs-flow-app/api/flow/order/out/submit-common-feedback";
    }

    public String getTrackCommentDetail() {
        return getServerNetAddressHost() + "comment-mobile/v1/comment/detail/" + System.currentTimeMillis();
    }

    public String getTrackCommentDetailUniversal() {
        return getServerNetAddressHost() + "universal-comment-mobile/comment/detailList";
    }

    /**
     * 热门页的直播换一换接口
     */
    public String exchangeHotLive() {
        return getLiveServerMobileHttpHost() + "lamia/v1/hotpage/exchange";
    }

    // 直播预约
    public String getBookLive() {
        return getLiveServerMobileHttpHost() + "diablo-web/v1/live/booking/add";
    }

    // 取消直播预约
    public String getRemoveBookLive() {
        return getLiveServerMobileHttpHost() + "diablo-web/v1/live/booking/remove";
    }

    public String getCategoryListUrl() {
        return getServerNetAddressHost() + "mobile/discovery/v5/categories/" +
                String.valueOf(System.currentTimeMillis());
    }

    public String getAppSwitchSettings() {
        return getServerNetAddressHost() + "mobile/settings/switch/get/ts-" + System.currentTimeMillis();
    }

    public String setAppSwitchSettings() {
        return getServerNetAddressHost() + "mobile/settings/switch/set";
    }

    // 今日热点频道保存接口
    public String saveDailyNewsChannels() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v3/saveCustom/ts-";
    }

    // 今日热点电台接口
    public String getDailyNewsChannels() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v3/queryGroups/ts-";
    }

    public String getOneKeyListenChannels() {
        return getRecommendFlowHost() + "recsys/onekey/rec/load";
    }

    public String getOneKeyListenChannelsNew() {
        //return getRecommendFlowHost() + "recsys/onekey/rec/new/load";
        return getRecommendFlowHost() + "recsys/onekey/rec/more/load";
    }

    public String saveHeadlineFavGroupsUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/saveCustom/ts-" + System.currentTimeMillis();
    }

    public String getHeadlineFavGroupsUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/queryGroups/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取已购专栏专辑退款id
     */
    public String getAlbumRefundId(long albumId) {
        return getMpAddressHost() + "rfd/album/" + albumId + "/refundid/" + System.currentTimeMillis();
    }

    public String getAlbumRefundInfo(long albumId) {
        return getMpAddressHost() + "rfd/albums/" + albumId + "/refund/" + System.currentTimeMillis();
    }

    public String getCampRefundInfo(long albumId) {
        return getServerDakaAddress() + "eduOps/notcheck/trade/album/" + albumId + "/refund/" + System.currentTimeMillis();
    }

    // 首页内容 (!!!!!!! 替换接口之后需要将此地址 再次放到 SafeStartManager这个类中)
    public String getHomeData() {
//        return getServerNetAddressHost() + "mobile/discovery/v4/recommend";
        return getServerNetAddressHost() + "discovery-firstpage/v2/explore";
    }

    // 首页内容 (!!!!!!! 替换接口之后需要在SafeStartManager这个类中修改首页缓存清除相关逻辑。缓存文件是根据接口地址的md5命名的)
    public String getRecommendFeedStreamUrl() {
        return getServerNetAddressHost() + "discovery-feed/v3/mix";
    }

    private String mRecommendFeedStreamUrlV4Path = null;
    public String getRecommendFeedStreamUrlV4() {
        boolean needAddToAdUa = false;
        if (TextUtils.isEmpty(mRecommendFeedStreamUrlV4Path)) {
            String dataRequestUrl = "";
            if (!TextUtils.isEmpty(dataRequestUrl) && RecommendFragmentAbManager.MODE_MIX.equals(RecommendFragmentAbManager.INSTANCE.getMode())) {
                needAddToAdUa = true;
                mRecommendFeedStreamUrlV4Path = dataRequestUrl;
            } else {
                mRecommendFeedStreamUrlV4Path = "discovery-feed/v4/mix";
            }
        }
        String url = getServerNetAddressHost() + mRecommendFeedStreamUrlV4Path;
        if (needAddToAdUa) {
            UserTrackingUrlMatcher.URL_AD_SYSTEM_UA.add(UserTrackingUrlMatcher.subUrlCore(url));
            UserTrackingUrlMatcher.URL_AD_COOKIE_INSTALLED.add(UserTrackingUrlMatcher.subUrlCore(url));
        }
        return url;
    }

    // 新人礼包挂件
    public String getUserGiftPendant() {
        return getServerNetAddressHost() + "discovery-feed/isShowUserGiftPendant";
    }

    public String getCollectTraitInFeedStreamUrl() {
        return getServerNetAddressHost() + "discovery-feed/collectTrait";
    }

    // 首页本地听相关数据刷新
    public String getRecommendCityDataUrl() {
        return getServerNetAddressHost() + "discovery-feed/module/city";
    }

    public String getOneKeyListenQuery() {
        return getRecommendFlowHost() + "recsys/onekey/rec/query";
    }

    public String getOneKeyListenNewQuery() {
        return getRecommendFlowHost() + "recsys/onekey/rec/new/query";
    }

    /**
     * 获取新人礼包
     */
    public String getNewcomerGiftUrl() {
        return getServerNetAddressHost() + "discovery-feed/v1/queryGift";
    }

    /**
     * 获取礼包领取信息
     */
    public String getCollectInfoUrl() {
        return getServerNetAddressHost() + "discovery-feed/v1/collectGift";
    }

    /**
     * 跳转礼包H5 的url
     */
    public String getGiftH5Url() {
        return ConfigureCenter.getInstance().getString("toc", "15days_tingh5",
                getMNetAddressHostS() + "lib/activity_new_user_benefit/last/build/index.html?_fullscreen=1");
    }


    /**
     * 获取一键听中间页封面
     *
     * @return
     */
    public String getOneKeyListenCover() {
        return getRecommendFlowHost() + "recsys/onekey/rec/channel/query";
    }


    //买赠相关接口 start

    public String getBuyPresentUrl() {
        return getMNetAddressHost() + "present/placeorder";
    }

    public String getBuyPresentInfoUrl(long albumId) {
        return getMNetAddressHost() + "present/ordercontext/album/" + albumId + "/" +
                String.valueOf(System.currentTimeMillis());
    }

    public String getQueryBuyPresentStatusUrl(String unifiedOrderNo) {
        return getMNetAddressHost() + "present/orderstatus/" + unifiedOrderNo + "/" +
                String.valueOf(System.currentTimeMillis());
    }


    public String getSharePresentUrl(long presentPackageId, boolean hasSent) {
        if (hasSent) {
            return getMNetAddressHost() + "present/presentpackage/" +
                    String.valueOf(presentPackageId) + "/" +
                    String.valueOf(System.currentTimeMillis());
        } else {
            return getMNetAddressHost() + "present/presentpackage/" +
                    String.valueOf(presentPackageId) + "/share/" +
                    String.valueOf(System.currentTimeMillis());
        }
    }


    public String getSavePresentCardRecordUrl(long presentPackageId) {
        return getMNetAddressHost() + "present/presentpackage/" +
                String.valueOf(presentPackageId) + "/message";
    }

    public String getSendPresentRecordUrl() {
        return getMNetAddressHost() + "present/presentpackages/" + System.currentTimeMillis();
    }

    public String presentGotRecordUrl() {
        return getMNetAddressHost() + "present/presentpackage/myreceive/" + System.currentTimeMillis();
    }

    public String getReceivePresentRecordUrl(long presentPackageId) {
        return getMNetAddressHost() + "present/presentpackage/" + presentPackageId +
                "/records/" + System.currentTimeMillis();
    }

    //买赠相关接口 end


    /**
     * 喜豆和喜点余额查询接口
     *
     * @return
     */
    public String getCoinAndDiamondAccount() {
        long timestamp = System.currentTimeMillis();
        return getMpAddressHost() + "xmacc/mysubaccount/v4/" + timestamp;
    }


    /**
     * 我的收益查询接口
     *
     * @return
     */
    public String getMyIncomeAccount() {
        return getBusinessHostS() + "business-financial-account-mobile-web/api/v1/home/<USER>";
    }

    public String getMyWalletWelfare() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/queryWelfareAwardInfo/ts-" + System.currentTimeMillis();
    }

    /**
     * 我的收益跳转地址
     *
     * @return
     */
    public String getMyIncomeJumpUrl() {
        return getBusinessHostS() + "business-financial-account-mobile-web/home/<USER>/";
    }

    /**
     * 喜币兑喜豆的汇率
     *
     * @return
     */
    public String getExchangeRate() {
        return getMpAddressHost() + "xmacc/exchange/rate";
    }

    public String getWebOfRechargeExchange() {
        return getMpAddressHost() + "business-xi-bean-mobile-web/help/recharge/exchange";
    }

    public String getDealRecord() {
        return getMpAddressHost() + "xmacc/traderecord/v1/";
    }

    public String getAnchorDesk() {
        return getServerNetAddressHost() + "mobile/anchor/desk";
    }

    public String getAlbumTrackListV3() {
//        mobile.test.ximalaya.com/mobile/v1/album/track/v3
        return this.getServerNetAddressHost() + "mobile/v1/album/track/v3/ts-" + System.currentTimeMillis();
    }

    // 推荐页里的猜你喜欢换一批接口
    public String getGuessYouLikeNewRefreshUrl() {
        return getServerNetAddressHost() + "discovery-feed/guessYouLike/cycle";
    }

    // 推荐页里的猜你喜欢模块实时推荐接口
    public String getGuessYouLikeRealTimeRecommendUrl() {
        return getServerNetAddressHost() + "discovery-feed/guessYouLike/realTime";
    }

    // 推荐页中实时推荐数据接口 v4
    public String getRealTimeFeedUrlV4() {
        return getServerNetAddressHost() + "discovery-feed/v4/realTimeFeed";
    }

    // 推荐页中实时推荐数据接口
    public String getRealTimeFeedUrl() {
        return getServerNetAddressHost() + "discovery-feed/realTimeFeed";
    }

    // 推荐页中根据iting 发起更新画像接口
    public String updateItingContentTrait() {
        return getServerNetAddressHost() + "discovery-feed/updateItingContentTrait";
    }

    // 推荐页中根据iting拿到实时推荐的专辑列表
    public String getRealTimeFeedForITing() {
        return getServerNetAddressHost() + "discovery-feed/v4/realTimeFeed";
    }

    // 推荐页中实时推荐数据接口-声音
    public String getRealTimeFeedFromTackUrl() {
        return getServerNetAddressHost() + "discovery-feed/realTimeFeedFromTrack";
    }

    // 混排模式负反馈补充一条数据
    public String getReplenish() {
        return getServerNetAddressHost() + "discovery-feed/replenish";
    }

    // 推荐页信息流直播实时更换接口
    public String getRecommendFeedChangeLiveUrl() {
        return getServerNetAddressHost() + "discovery-feed/changelive";
    }

    public String getRefundRequestUrl(long albumID) {
        return this.getMpAddressHost() + "rfd/order/{merchantOrderNo}/" + albumID + "/v3/refund";
    }

    public String getVipPageUrl() {
        return getMNetAddressHost() + "vip/myvip/ts-" + System.currentTimeMillis();
    }

    /**
     * 直播间列表
     *
     * @return
     */
    public String getLiveRoomList() {
        return getServerNetAddressHost() + "lamia/v7/subscribe/list";
    }

    public String getFollowingLiveList() {
        return getServerNetAddressHost() + "fans/live/v1/following/list";
    }

    public String getAchievementShare() {
        return getHybridHost() + "hybrid/api/layout/grade/user-achievement-share";
    }

    public String getUserGradePage() {
        return getHybridHost() + "hybrid/api/layout/grade/user-grade";
    }

    public String getSubjectListPageUrl() {
        return getMNetAddressHost() + "explore/subject_list";
    }

    public String getSubjectListPageUrl(String categoryId) {
        return getMNetAddressHost() + "explore/category_subject_list?category_id=" + categoryId;
    }

    public String getSubjectDetailPageUrl(String id) {
        return getMNetAddressHost() + SUBJECT_URL + id + "&" + IWebFragment.CAN_SLIDE + "=1";
    }

    public String getCategoryRecommends() {
        //return this.getServerNetAddressHost() + "discovery-category/category/recommend";
        return this.getServerNetAddressHost() + "discovery-category/v5/category/recommend";
        //return this.getServerNetAddressHost() + "discovery-category/keyword/all";
    }

    public String voteCategoryRecommends() {
        return this.getServerNetAddressHost() + "discovery-category/v5/category/recommend/vote";
    }

    public String getPreferredCategoryRecommends() {
        return this.getServerNetAddressHost() + "discovery-category/v4/category/recommend";
    }

    public String getSceneListenRefresh() {
        return this.getServerNetAddressHost() + "discovery-feed/v1/scene/listen/refresh";
    }

    /**
     * 获取虚拟分类推荐
     */
    public String getVirtualCategoryRecommends() {
        return this.getServerNetAddressHost() + "product/v1/category/virtual/recommends";
    }

    public String getStoreBabyInfoUrl() {
        return getServerNetAddressHost() + "discovery-category/storeBabyInfo";
    }

    // 少儿教育，保存年级信息
    public String getStoreChildEducationInfo() {
        return getServerNetAddressHost() + "discovery-category/storeChildEducationInfo";
    }

    public String getCategoryRecommendFeedUrl() {
        return getServerNetAddressHost() + "/discovery-category/categoryFeed";
    }

    /**
     * 获取本地听信息流
     */
    public String getLocalTingFeedUrl() {
        return getServerNetAddressHost() + "discovery-category/city/feed";
    }

    /**
     * 获取新的本地听信息流
     */
    public String getLocalTingFeedUrlNew() {
        return getNewCityRecommendAddressHost() + "culture-citywave-api/feed";
    }

    /**
     * @return 精品推荐页的url
     */
    public String getBoutiqueRecommends() {
//        return getServerNetAddressHost()
//                + "product/v1/category/recommends";
        return getServerNetAddressHost() + "product/v4/category/recommends"; // 兼容虚拟分类 moduleType
    }

    public String getDiscoveryCategoryKeyword() {
        return this.getServerNetAddressHost() + "discovery-category/keyword/albums";
    }

    public String getNewDiscoveryKeywordRefreshForLocalListenUrl(int albumsRecomId) {
        return this.getNewCityRecommendAddressHost() + String.format(Locale.US, "culture-citywave-api/citywave/albumsRecomRefresh/%d", albumsRecomId);
    }

    public String getNewLocalListenClickBubbleUrl(long bigImgId) {
        return getNewCityRecommendAddressHost() + String.format(Locale.US, "culture-citywave-api/citywave/bubble/%d", bigImgId);
    }

    public String getVoucherListPage() {
        return getMNetAddressHostS() + "coupons/voucher/valid_list/" + System.currentTimeMillis();
    }

    public String getVoucherListPageS() {
        return getMNetAddressHostS() + "coupons/voucher/valid_list/" + System.currentTimeMillis();
    }

    public String getWebOfReportIllegal() {
        return "http://m.ximalaya.com/s-activity/hybrid-reportlist/dist/index.html";
    }

    public String getXiMaNoticeList() {
        return getServerNetAddressHost() + "mobile/anchor/announces/" + System.currentTimeMillis();
    }

    /**
     * 获取配音秀播放页的合作模板演员信息
     */
    public String getDubShowCoopInfo() {
        return getHybridHost() + String.format(Locale.US, "dub-web/play/query/cooperateInfo/ts-%d", System.currentTimeMillis());
    }


    /**
     * 获取正在玩配音秀的人列表
     */
    public String getDubbingPeopleList() {
        return getServerNetAddressHost() + "mobile-dub-track/mobile/dubShow/rankpage";
    }

    /**
     * @return 获取热词元数据的地址
     */
    public String getKeywordMetadatas() {
        return getServerNetAddressHost() + "discovery-category/keyword/metadatas";
    }

    /**
     * @return 获取发现页声音推荐流tab列表
     */
    public String getFindRecTrackTabs() {
        return BaseUtil.chooseEnvironmentUrl("http://mobile.ximalaya.com/discovery-stream-mobile/discoveryPage/wholeContent/tabs/ts-" + System.currentTimeMillis());
    }


    /**
     * @return 获取发现页单条声音详情
     */
    public String getFindTrackInfo() {
        return BaseUtil.chooseEnvironmentUrl("http://mobile.ximalaya.com/discovery-stream-mobile/discoveryPage/wholeContent/querySingleItemInfo/ts-" + System.currentTimeMillis());
    }

    /**
     * @return 获取发现页听头条
     */
    public String getFindHeadline() {
        return BaseUtil.chooseEnvironmentUrl("http://mobile.ximalaya.com/discovery-stream-mobile/discoveryPage/wholeContent/fetchHeadlineItems/ts-" + System.currentTimeMillis());
    }

    public String getSkillEntry() {
        return BaseUtil.chooseEnvironmentUrl(getHybridHost() + "anchor-skill/theme/getEntry",
                "http://m.test.ximalaya.com/anchor-skill/theme/getEntry",
                getHybridHost() + "anchor-skill/theme/getEntry");
    }

    public String getSkillOptions() {
        return BaseUtil.chooseEnvironmentUrl(getHybridHost() + "anchor-skill/theme/getOptions",
                "http://zhubo.test.ximalaya.com/anchor-skill/theme/getOptions",
                getHybridHost() + "anchor-skill/theme/getOptions");
    }

    public String getWebOfSkillShare(long uid, String token) {
        return BaseUtil.chooseEnvironmentUrl(getHybridHost() + "anchor-skill/index?uid=" + uid + "&token=" + token,
                "http://zhubo.test.ximalaya.com/anchor-skill/index?uid=" + uid + "&token=" + token,
                getHybridHost() + "anchor-skill/index?uid=" + uid + "&token=" + token);
    }

    public String getWebOfSkillIndex() {
        return BaseUtil.chooseEnvironmentUrl(getHybridHost() + "anchor-skill/index",
                "http://zhubo.test.ximalaya.com/anchor-skill/index",
                getHybridHost() + "anchor-skill/index");
    }

    public String getWebOfSkillManual(int moduleId, long uid, String token) {
        return UrlConstants.getInstanse().getHybridHost()
                + "anchor-skill/index/cargoCard/subject/" + moduleId
                + "?uid=" + uid + "&token=" + token;
    }

    public String getUpdateHead() {
        return getServerNetAddressHost() + "mobile-user/header/update";
    }

    public String getUpLoadPhoto() {
        return getServerNetAddressHost() + "photo-album-web/v1/album/photos/add";
    }

    public String getUpDatePhoto() {
        return getServerNetAddressHost() + "photo-album-web/v1/album/photos/update";
    }

    public String getUpDateHeadNew() {
        return getServerPassportHostS() + "mobile/profile/updateHeader/v1";
    }

    public String getUpdateMyDetailBackgroundUrl() {
        return getServerNetAddressHost() + "mobile-user/background/update";
    }

    public String getUpdateGender() {
        return getServerPassportHostS() + "mobile/profile/updateGender/v1";
    }

    public String getDeletePhoto() {
        return getServerNetAddressHost() + "photo-album-web/v1/album/photos/delete";
    }

    public String getPhotoList() {
        return getServerNetAddressHost() + "photo-album-web/v1/album/photos";
    }

    public String getListenHeadLineTagUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/topBuzz/buzzGroups";
    }

    public String getListenHeadLineTagUrlForFind() {
        return getServerNetAddressHost() + "discovery-stream-mobile/discoveryPage/wholeContent/buzzGroups";
    }

    public String getShowAnchorSkillEntry() {
        return getBusinessHost() + "manage/collectpageurl";
    }

    public String checkQrShareContent() {
        return getServerNetAddressHost() + "thirdparty-share/wordfilter";
    }

    public String getWebOfSign() {
        return getHybridHost() + "hybrid/api/listen/index/ts-" + System.currentTimeMillis();
    }

    public String getSkillThemeIdList() {
        return BaseUtil.chooseEnvironmentUrl(getHybridHost() + "anchor-skill/theme/get?num=-1",
                "http://zhubo.test.ximalaya.com/" + "anchor-skill/theme/get?num=-1",
                getHybridHost() + "anchor-skill/theme/get?num=-1");
    }

    public String getBatchChooseTracksBuyModel() {
        return getMpAddressHost() + "payable/order/trade/prepare/track/quick/v3";
    }

    // 新的单集豆腐块弹窗价格接口
    public String getSingleAlbumPromotionMultiPrice(long albumId, long trackId) {
        return String.format(Locale.getDefault(), "%sproduct/promotion/v1/single/track/%d/%d/price/multi/ts-%d", getServerNetAddressHost(), albumId, trackId, System.currentTimeMillis());
    }

    // 新的单集豆腐块弹窗价格接口
    public String getSingleAlbumPromotionMultiPriceV2(long albumId, long trackId) {
        return String.format(Locale.getDefault(), "%sproduct/promotion/v2/single/track/%d/%d/price/multi/ts-%d", getServerNetAddressHost(), albumId, trackId, System.currentTimeMillis());
    }

    public String getSingleAlbumBatchBuyPromotionPrice(long albumId) {
        return String.format(Locale.getDefault(), "%sproduct/promotion/v2/single/track/%d/price/context/ts-%d", getServerNetAddressHost(), albumId, System.currentTimeMillis());
    }

    public String getSingleAlbumBatchTrackBuyData() {
        return String.format(Locale.getDefault(), "%sproduct/promotion/v3/single/track/purchase/trackList/ts-%d", getServerNetAddressHost(), System.currentTimeMillis());
          }

    public String getCheckBatchTrackPrice(){
        return String.format(Locale.getDefault(), "%sproduct/promotion/v3/single/track/purchase/calculatePrice/ts-%d", getServerNetAddressHost(), System.currentTimeMillis());
    }

    // 新的单集折扣弹窗价格接口
    public String getSingleAlbumPromotionPrice(long albumId) {
        return getServerNetAddressHost() + "product/promotion/v1/single/track/"+albumId+"/price/context/ts-" + System.currentTimeMillis();
    }

    public String getOrderContextForAlbum() {
        return getMpAddressHost() + "payable/order/context/v2/album";
    }

    // 津贴兑换限时免费听
    public String getSubsidyExchange() {
        return getMNetAddressHost() + "subsidyexchange";
    }

    public String getRecommendSubscribeHome() {
        return getServerNetAddressHost() + "subscribe/v2/subscribe/home/<USER>";
    }

    public String getRecommendSubscribeHomeNew() {
        return getServerNetAddressHost() + "subscribe/v1/lately-listen/recommend/ts-" + System.currentTimeMillis();
    }

    public String getRelatedRecommendSubscribe() {
        return getARHost() + "rec-association/recommend/album/sub";
    }

    public String askUpdateSubscribeAlbum() {
        return getServerNetAddressHost() + "mobile/album/request/update/" + System.currentTimeMillis();
    }

    /**
     * 获取用户关注推荐的url
     *
     * @return
     */
    public String getSubscribeRecommendUrl() {
        return getARHost() + "rec-association/recommend/album/sub";
    }

    public String getDownloadTotalInfo() {
        return getServerNetAddressHost() + String.format(Locale.US, "mobile/download/track/size/ts-%d", System.currentTimeMillis());
    }

    /**
     * 一键听换一批
     */
    public String getRefreshOneKeyListener() {
        return getServerNetAddressHost() + "discovery-firstpage/one-key-listen";
    }

    public String albumAutoBuy(long albumId) {
        return getMNetAddressHost() + "payable/autobuy/album/" + albumId;
    }

    public String albumAutoBuyClose() {
        return getMNetAddressHostS() + "payable/autobuy/album/close";
    }

    public String albumAutoBuyOpen() {
        return getMNetAddressHostS() + "payable/autobuy/album/open";
    }

    public String albumAutoBuyList() {
        return getMNetAddressHost() + "payable/myautobuy/albums/" + System.currentTimeMillis();
    }

    public String getWebOfAnchorCredit() {
        return getRedirectUrlS() + "credit/h5/violations/ts_" + System.currentTimeMillis();
    }

    public String getPlayAnchorShopInfoV2(long trackId, long anchorId) {
        return getMNetAddressHost() + String.format("anchor-sell/track/v2/%d/anchorId/%d/cart/", trackId, anchorId);
    }

    // 主播商品
    public String getAnchorShopNew() {
        return getMNetAddressHost() + "anchor-sell/track/";
    }

    // 主播商品
    public String getAnchorShop() {
        return getMNetAddressHost() + "anchor-sell/fans/track/";
    }

    // 主播推广
    public String getAnchorPromotion() {
        return "http://m.ximalaya.com/carnival/imgShare/834";
    }

    // 主播我的数据
    public String getAnchorDataCenter(int datahome) {
        ////centerHome   1:播放  2:订阅   3:分享
        StringBuilder url = new StringBuilder(getHybridHost());
        url.append("data/").append("ts-").append(System.currentTimeMillis());
        if (1 == datahome) {
            url.append("/");
        } else if (2 == datahome) {
            url.append("?type=subscribe");
        } else if (3 == datahome) {
            url.append("?type=share");
        }
        return url.toString();
    }

    /**
     * 发现页 推荐流
     * http://mobile.test.ximalaya.com/nexus/v1/stream/pull?action=down&streamId=2&lastMsgId=1&limit=10
     **/
    public String getFindStream() {
        return getServerNetAddressHost() + "nexus/v1/stream/pull";
    }

    /**
     * 发现页 tab
     * http://mobile.test.ximalaya.com/nexus/v1/stream/pull?action=down&streamId=2&lastMsgId=1&limit=10
     **/
    public String getFindTabs() {
        return getServerNetAddressHost() + "nexus/v1/tabs";
    }

    /**
     * 获取订阅频道列表
     * gitlab http://gitlab.ximalaya.com/x-fm/radio-station/wikis/subscribe_channel
     *
     * @return
     */
    public String subscribedOneKeyChannelList() {
        return getServerNetAddressHost() + "radio-station/v1/subscribe-channel/list";
    }

    public String subscribeOneKeyChannel(boolean isSubscribe) {
        if (isSubscribe) {
            return getServerNetAddressHost() + "radio-station/v1/subscribe/channel";

        } else {
            return getServerNetAddressHost() + "radio-station/v1/unsubscribe/channel";
        }
    }


    public String getDynamicVideoPublishAuthorityUrl() {
        return getServerNetAddressHost() + "chaos" + "/v1/publish/options";
    }

    // 有声书精细化运营保存用户喜欢的男女小说，热词
    public String storeUserNovleInfo() {
        return getServerNetAddressHost() + "discovery-category/storeUserNovelInfo";
    }

    // 有声书精细化运营保存用户喜欢的男女小说，出生日期
    public String storeUserNovleInfoNew() {
        return getServerNetAddressHost() + "persona/baby/addCategoryTrait";
    }

    //vip频道页面数据
    public String getVipFraData() {
        return getServerNetAddressHost() + "vip/v1/recommand/ts-" + System.currentTimeMillis();
    }

    public String getEnvelopeListWeb() {
        return getMNetAddressHost() + "redenvelope/mylist";
    }

    /**
     * 用户兴趣卡片
     */
    public String queryCustomizeCategories() {
        return getServerNetAddressHost() + "persona/query/categories";
    }

    /**
     * 用户兴趣卡片,根据年龄和性别获取用户卡片
     */
    public String queryCustomizeCategoriesNew() {
        return getServerNetAddressHost() + "persona/v2/query/usercategories";
    }

    /**
     * 新用户冷启动卡片，服务端对二级标签卡片的数量做了限制
     */
    public String queryChooseLikeCategories() {
        return getServerNetAddressHost() + "persona/traitCollect/v1/query/usercategories";
    }

    public String queryChooseLikeCategoriesNew() {
        return getServerNetAddressHost() + "persona/traitCollect/v14/query/usercategories";
    }

    public String queryChooseLikeCategoriesNewVersion() {
        return getServerNetAddressHost() + "persona/traitCollect/%s/query/usercategories";
    }

    /**
     * 用户兴趣卡片，点击跳过按钮时通知服务端
     */
    public String queryThird() {
        return getServerNetAddressHost() + "persona/traitCollect/queryThird";
    }

    public String winGetPrize() {
        return getMNetAddressHost() + "x-web-activity/lottery/winGetPrize";
    }

    /**
     * 请求播放列表
     *
     * @return
     */
    public String queryPlayList() {
        return getServerNetAddressHost() + "mobile-dub-track/dubTrack/query/playlist";
    }

    public String followVipNewAlbum(long albumId, boolean followStatus) {
        return getServerNetAddressHost() + "vip/v1/channel/newalbum/"
                + albumId
                + (followStatus ? "/unfollow" : "/follow");
    }

    /**
     * h5页面-红包列表
     *
     * @return
     */
    public String getWebOfRedEnvelopeList() {
        return getMNetAddressHost() + "redenvelope/mylist";
    }

    public String vipChannelRecommendList() {
        return getServerNetAddressHost() + "vip/v1/channel/recommendation/ts-" + System.currentTimeMillis();
    }


    private String getMicroLessonBaseUrlV1() {
        return getServerNetAddressHost() + "microlesson-web/v1/";
    }

    //推荐页面 weike card的刷新 GET /v1/homecard/list
    public String getWeikeRecommCardUrl() {
        return getMicroLessonBaseUrlV1() + "homecard/list";
    }

    public String dubTrackQueryPersonal() {
        return getServerNetAddressHost() + "mobile-dub-track/dubTrack/query/personal";
    }

    public String activateCompensationOfAlbumFreeToPaid() {
        return getMpAddressHost() + "payable/compensation";
    }

    public String getWholeAlbumBuyDialogDetailsUrl() {
        return getServerNetAddressHost() + "product/v1/album/price/ts-" + System.currentTimeMillis();
    }

    public String getVipMonthlyUrl(String userId) {
        return getMNetAddressHost() + "business-user-subscription-mobile-web/userSubscription/findByDomainUserIdGroupId?domain=1&userId=" + userId + "&groupId=VIP_SUBSCRIBE_GROUP";
    }

    public String getAlbumTrackListWholeAlbumNew() {
        return getServerNetAddressHost() + "product/v1/album/tracks/ts-" + System.currentTimeMillis();
    }

    //h5页面-知识大使
    public String getWebOfKnowledgeEnvoy() {
        return getMNetAddressHostS() + "redefine-ambassador-web/router/index";
    }

    //我的钱包-发票
    public String getWebOfInvoice() {
        return getBusinessHostS() + "invoice/v1";
    }

    //更多相关声音
    public String getRecommendTracksUrl() {
        return getServerNetAddressHost() + "mobile-playpage/track/associationTrackPage";
    }

    public String order() {
        return getMNetAddressHostS() + "trade/placeorder/v2";
    }

    public String rechargeCurrencyViaRn() {
        return getMNetAddressHostS() + "common-recharge/rn/recharge";
    }

    public String getLotteryn() {
        return getAdWelfAreHost() + "lotteryn";
    }

    public String getHistoryBanner() {
        return getServerNetAddressHost() + "mobile/playhistory";
    }

    public String getCustomizeRecommendAlbumUrl() {
        return getServerNetAddressHost() + "discovery-feed/guessYouLike";
    }

    public String getVipFraTabs() {
        return getServerNetAddressHost() + "vip/v1/channel/ts-" + +System.currentTimeMillis();
    }

    // 设置为热评
    public String getSetHotCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/hot";
    }

    // 取消热评
    public String getCancelSetHotCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/dishot";
    }

    // 创建评论活动
    public String createCommentTheme() {
        return getServerNetAddressHost() + "comment-mobile/activity/create";
    }

    // 更新评论活动
    public String updateCommentTheme() {
        return getServerNetAddressHost() + "comment-mobile/activity/update";
    }

    // 查询活动获奖用户
    public String getCommentThemeWinners() {
        return getServerNetAddressHost() + "comment-mobile/activity/winners";
    }


    // 获取热评
    public String getHotCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/track/comment";
    }

    // 获取段评列表
    public String getTingReadParaCommentUrl() {
        return getMNetAddressHostS() + "qiji-mobile/xima/listenRead/paragraphComment/comment/list";
    }

    // 通用查询评论列表
    public String getHotCommentUniversalUrl() {
        return getServerNetAddressHost() + "universal-comment-mobile/comment/list";
    }

    // 专辑页声音评论
    public String getAlbumCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/album/comment/" + System.currentTimeMillis();
    }

    // 获取播放页声音精选评论
    public String getTrackWonderfulCommentsUrl(long timeStamp) {
        return getServerNetAddressHost() + "comment-mobile/v1/track/comments/wonderful/" + timeStamp;
    }

    // 获取讨论列表
    public String getTalkCommentsUrl() {
        return getServerNetAddressHost() + "comment-mobile/talk/comments/list";
    }

    // 获取投票评论列表
    public String getVoteCommentsUrl() {
        return getServerNetAddressHost() + "comment-mobile/vote/comments/list";
    }

    // 获取测评评论列表
    public String getSequentialVoteCommentsUrl() {
        return getServerNetAddressHost() + "comment-mobile/sequentialVote/comments/list";
    }

    // 获取讨论卡片详情
    public String getTalkCardDetail() {
        return getMNetAddressHost() + "community-widget/api/v1/communityWidget/talkInfo";
    }

    // 查询招募找搭子详情
    public String getFindPartnerUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/findPartner";
    }

    public String getMcCardDataUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/mc/room/card";
    }

    // 获取投票卡片详情
    public String getVoteCardDetail() {
        return getMNetAddressHost() + "community-widget/api/v1/communityWidget/userVoteInfo";
    }

    // 获取互动卡片时间流列表
    public String getTimelineCardList() {
        return getMNetAddressHost() + "community-widget/api/v1/timeline/list";
    }

    public String getTtsBookInfoUrl() {
        return getServerNetAddressHost() + "mobile-playpage/book/info";
    }

    public String getTtsNextChapterInfoUrl() {
        return getServerNetAddressHost() + "mobile-playpage/book/chapter/next";
    }

    public String getTtsDocInfoUrl() {
        return getServerNetAddressHost() + "mobile-playpage/book/tts/doc";
    }

    public String getTrackTimbresUrl() {
        return getServerNetAddressHost() + "mobile-playpage/book/track/timbres";
    }

    // 获取题目信息
    public String getCommentQuestionInfoUrl() {
        return getServerNetAddressHost() + "comment-mobile/question/info";
    }

    // 提交题目信息
    public String getCommentAnswerUrl() {
        return getServerNetAddressHost() + "comment-mobile/question/answer";
    }

    //售前页推荐专辑接口
    public String getWholeAlbumRecommendList() {
        return getServerNetAddressHost() + "product/presale/v2/album/recommend/ts-" + System.currentTimeMillis();
    }

    public String playPageOverAuditionVipConvertRes() {
        return getServerNetAddressHost() + "mobile/track/afterSample/" + System.currentTimeMillis();
    }

    // 获取单集专辑价格信息
    public String getSingleAlbumPrice(long albumId) {
        return getServerNetAddressHost() + "product/promotion/v1/single/track/" + albumId + "/price/dynamic/ts-" + System.currentTimeMillis();
    }

    // 获取单集专辑价格信息(降级处理)
    public String getSingleAlbumPriceBackUp(long albumId) {
        return getServerNetAddressHost() + "product/promotion/v1/single/track/" + albumId + "/price/detail/ts-" + System.currentTimeMillis();
    }

    // 获取推荐好友
    public String getRecommendFriends() {
        return getPassportAddressHosts() + "friendship-mobile/v1/friends/recommend";
    }

    // 获得通讯录好友
    public String getContactFriend() {
        return getPassportAddressHosts() + "friendship-mobile/v2/friend/contacts";
    }

    // 获得微博好友
    public String getWeiboFriend() {
        return getPassportAddressHosts() + "friendship-mobile/v1/friends/weibo";
    }

    // 订阅页是否显示招好友条幅
    public String getShowFriendBannerUrl() {
        return getPassportAddressHosts() + "friendship-mobile/v1/findFriendsBanner/show";
    }

    // 推荐页进入视频播放页续播接口
    public String getRecommendVideosUrl() {
        return getServerNetAddressHost() + "discovery-feed/query/recvideos";
    }

    // 新人听页面数据
    public String getNewUserListenQueryUrl() {
        return getServerNetAddressHost() + "discovery-feed/squareOperation/queryNew";
    }

    // 新用户我听加礼包
    public String getNewUserGiftAlbumUrl() {
        return getServerNetAddressHost() + "discovery-feed/myListen/newUserScreen/ts-" + System.currentTimeMillis();
    }

    // 通用创建评论
    public String createCommentCommonUrl() {
        return getServerNetAddressHost() + "general-comment-mobile/option/create";
    }

    // 获得通用评论列表
    public String getCommentListCommon() {
        return getServerNetAddressHost() + "general-comment-mobile/query/top";
    }

    // 获得通用评论回复
    public String getCommentReplyListCommon() {
        return getServerNetAddressHost() + "general-comment-mobile/query/detail";
    }

    // 通用删除评论V2
    public String deleteCommentUniversalUrl() {
        return getServerNetAddressHost() + "universal-comment-mobile/comment/delete";
    }

    // 踩声音评论
    public String hateTrackCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/hate";
    }

    // 取消踩声音评论
    public String unhateTrackCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/unhate";
    }

    // 每周热评
    public String getWeeklyHotCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/track/hotcomment";
    }

    // 兑吧免登录URL
    public String getDuibaUrl() {
        return getMNetAddressHost() + "starwar/lottery/duiba/app/login";
    }

    public String getRewardCenterUrl() {
        return getMNetAddressHost() + "starwar/task/listen/layout/home";
    }

    //精品页新版
    public String boutiquePageData2() {
        return getServerNetAddressHost() + "product/v1/payable/channel/recommend/ts-" + System.currentTimeMillis();
    }

    // 新人推荐页页数据
    public String getNewUserRecommendPageDataUrl() {
        return getServerNetAddressHost() + "discovery-feed//query/recommendCard/more";
    }

    public String getUpdateUserTraitByUidUrl() {
        return getServerNetAddressHost() + "discovery-feed/updateUserTraitByUid";
    }

    // 推荐页新人推荐模块换一批接口
    public String getNewUserRecommendModuleRefreshUrl() {
        return getServerNetAddressHost() + "discovery-feed/query/recommendCard/cycle";
    }

    // 获取站外好友收听的信息
    public String getPlayFriendListenedRecordUrl() {
        return getServerNetAddressHost() + "mobile-playpage/friend/listened";
    }

    //会员页-尝鲜模块-查询用户是否领取过新人礼包
    public String vipPageIsGetFreshAward() {
        return getMNetAddressHost() + "vip/vip/fresh/user/isgetaward/ts-" + System.currentTimeMillis();
    }

    public String vipPageGetFreshAward() {
        return getServerNetAddressHost() + "vip/fresh/user/getaward/ts-" + System.currentTimeMillis();
    }

    public String vipPageFreshRecordReportToMail() {
        return getServerNetAddressHost() + "vip/fresh/user/submit/v1/ts-" + System.currentTimeMillis();
    }

    public String vipPageFreshModule() {
        return getMNetAddressHost() + "vip/vip/freshmodule/ts-" + System.currentTimeMillis();
    }

    // 提交个推所需的信息的接口
    public String getPostGeTuiDataUrl() {
        return getServerNetAddressHost() + "persona/getGetuiTags";
    }

    // 专辑/播放页面的版权申诉url
    public String getCopyrightUrl() {
        return getMNetAddressHost() + "anchor-copyright/appealedit";
    }

    public String recordActivation() {
        return getSERVER_XIMALAYA_ACT() + "adrecord/record/activation";
    }

    /**
     * mcn获取专辑分类
     */
    public String getAlbumCategoryUrl() {
        return getServerNetAddressHost() + "mobile-user/artist/albums/" + System.currentTimeMillis();
    }

    /**
     * mcn分类专辑更多
     *
     * @return
     */
    public String getCatAlumsUrl(String categoryId) {
        return getServerNetAddressHost() + "mobile-user/v2/artist/catalbums/" + categoryId + "/" + System.currentTimeMillis();
    }

    /**
     * 获取编辑资料信息
     */
    public String getMyDetailUrl() {
        return getServerNetAddressHost() + "mobile-user/user/profile";
    }

    /**
     * RN支付，喜钻充值获取订单号服务
     *
     * @return RN支付，喜钻充值获取订单号服务
     */
    public String getXiBeanUnifiedOrderNoUrl() {
        return getMpAddressHost() + "business-xi-bean-mobile-web/xibean/recharge/placeorder/cashier";
    }

    /**
     * RN支付，喜钻充值下单服务
     *
     * @return RN支付，喜钻充值下单服务
     */
    public String getXiBeanOrderUrl() {
        return getMNetAddressHostS() + "trade/payorder";
    }

    // 获取服务端的时间
    public String getServiceTime() {
        return getMNetAddressHost() + "starwar/task/listen/serverTime";
    }

    public String getCreateVoiceSignUrl() {
        return getPassportAddressHosts() + "profile-http-app/v1/voice/signature/update";
    }

    public String getDeleteVoiceSigUrl() {
        return getPassportAddressHosts() + "profile-http-app/v1/voice/signature/delete/";
    }

    public String getLikeVoiceSignatureUrl() {
        return getServerNetAddressHost() + "favourite-business/favorite/track";
    }

    public String createVoiceSignatureUrl() {
        return getServerNetSAddressHost() + "mobile-user/soundsignature/report?_fullscreen=1";
    }

    public String universalAlbumCheckInAwardQuery(long checkInAwardId) {
        return getMNetAddressHost() + "business-album-check-in-mobile-web/checkinaward/" + checkInAwardId + "/" + System.currentTimeMillis();
    }

    public String universalAlbumCheckInAwardGet(long checkInAwardId, long contactId, long timestamp) {
        return getMNetAddressHost() + "business-album-check-in-mobile-web/checkinaward/" + checkInAwardId + "/contact/" + contactId + "/" + timestamp;
    }

    public String doUniversalAlbumCheckIn(long albumId, long trackId, long timestamp) {
        return getMNetAddressHost() + "business-album-check-in-mobile-web/usercheckin/" + albumId + "/" + trackId + "/" + timestamp;
    }

    public String getUniversalAlbumCheckInProgress(long albumId) {
        return getMNetAddressHost() + "business-album-check-in-mobile-web/achievement/" + albumId + "/" + System.currentTimeMillis();
    }

    public String getAlbumRateListUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/list/query";
    }

    public String getAlbumLabelCommentListUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/label/comment/list/query";
    }

    public String getAlbumRateDetailUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/list/query";
    }

    public String likeAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/like";
    }

    public String unlikeAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/dislike";
    }

    public String hateAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/hate";
    }

    public String unhateAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/unhate";
    }


    public String deleteAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/delete";
    }

    public String updateAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/update";
    }

    public String getAlbumCommentConfigUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/config";
    }

    public String isAlbumRateAvailableUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/entrance/available";
    }

    public String replyAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/create";
    }

    public String deleteAlbumRateReplyUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/delete";
    }

    public String getSelectedHotCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/daily/hotcomment";
    }

    public String getHotCommentCandidateUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/comment/candidate";
    }

    public String recommendHotCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/comment/recommend";
    }

    public String getRecommendVisitInfoUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/daily/visitinfo";
    }

    /**
     * 新版聚合榜首页
     */
    public String getAggregateRankFirstPageUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/v4/ranking/AggregateRankFirstPage";
    }

    /**
     * 新版聚合榜首页
     */
    public String getAggregateRankFirstPageUrl2() {
        return getServerNetAddressHost() + "discovery-ranking-web/v5/ranking/AggregateRankFirstPage";
    }

    /**
     * 新榜聚合榜分类tab
     */
    public String getAggregateRankTabsUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/ranking/AggregateRankListTabs";
    }

    public String getAggregateRankTabsUrl2() {
        return getServerNetAddressHost() + "discovery-ranking-web/v5/ranking/AggregateRankListTabs";
    }

    public String getAggregateClassifyRankTabsUrl(){
        return getServerNetAddressHost() + "discovery-ranking-web/v1/category/aggregateRankListTabs";
    }

    /**
     * 新版榜单内容列表
     */
    public String getConcreteRankListUrl2() {
        return getServerNetAddressHost() + "discovery-ranking-web/v5/ranking/concreteRankList";
    }

    /**
     * 新版榜单内容列表
     */
    public String getConcreteRankListUrl3() {
        return getServerNetAddressHost() + "discovery-ranking-web/v6/ranking/concreteRankList";
    }

    /**
     * 新版榜单内容列表
     */
    public String getConcreteRankListUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/ranking/concreteRankList";
    }

    /**
     * 全部频道分组数据
     */
    public String getChannelGroupList() {
        return getServerNetAddressHost() + "metadatav2-mobile/channelgroup/list";
    }

    /**
     * 分组下的频道列表
     */
    public String getChannelList() {
        return getServerNetAddressHost() + "metadatav2-mobile/channelgroup/channel/list";
    }

    /**
     * 新品推荐
     */
    public String getNewProduct() {
        return getServerNetAddressHost() + "discovery-category/newProduct/recommend";
    }

    /**
     * 我关注的频道列表
     */
    public String getMyFollowChannel() {
        return getServerNetAddressHost() + "metadatav2-mobile/channel/followList";
    }

    /**
     * 关注频道
     */
    public String getFollowChannel() {
        return getServerNetAddressHost() + "metadatav2-mobile/channel/follow";
    }

    /**
     * 取消关注频道
     */
    public String getUnfollowChannel() {
        return getServerNetAddressHost() + "metadatav2-mobile/channel/unFollow";
    }

    /**
     * 收藏话题
     */
    public String getTopicCollectUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/topicCollect/collect";
    }

    /**
     * 取消收藏话题
     */
    public String getTopicUnCollectUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/topicCollect/unCollect";
    }

    /**
     * 讨论卡片浮层
     */
    public String getTopicTalkCardUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/topicMobile/talkCard";
    }

    /**
     * 获取话题详情页mc和pk信息
     * */
    public String getTopicMcAndVoteUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/topicMobile/detailPageVO";
    }

    /**
     * 选择投票
     * */
    public String getVoteSelectUrl() {
        return getMNetAddressHost() + "anchor-market-platform-web/api/v1/vote/select";
    }

    /**
     * 查询用户投票详情
     */
    public String getUserVoteInfo() {
        return getMNetAddressHost() + "community-widget/api/v1/vote/userVoteInfo";
    }

    /**
     * 批量查询用户投票详情
     */
    public String multiQueryUserVoteInfo() {
        return getMNetAddressHost() + "community-widget/api/v1/vote/multiQueryUserVoteInfo";
    }

    /**
     * 搜索频道
     */
    public String getChannelSearch() {
        return getServerNetAddressHost() + "metadatav2-mobile/channel/search";
    }

    /**
     * 频道落地页顶部信息
     */
    public String getChannelTopInfoUrl() {
        return getServerNetAddressHost() + "discovery-category/channel/queryPageTopInfo";
    }

    /**
     * 频道落地页tab
     */
    public String getChannelTabList() {
        return getServerNetAddressHost() + "discovery-category/tab/list";
    }

    /**
     * 话题频道落地页tab
     */
    public String getTopicChannelTabList() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/topicMobile/tab/list";
    }

    /**
     * 标签频道推荐页数据接口
     */
    public String getTagChannelRecommendPageUrl() {
        return getServerNetAddressHost() + "discovery-category/channel/recommend";
    }

    /**
     * 定制化榜单内容列表
     */
    public String getCustomizeRankListUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/ranking/personaRankList";
    }

    /**
     * 新用户必听榜
     */
    public String getNewUserRankListUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/newUser/AggregateRankPage";
    }

    /**
     * 获取设置
     */
    public String getSettingGetUrl() {
        return getServerNetAddressHost() + "mobile/settings/get";
    }

    /**
     * 修改设置
     */
    public String getSettingSetUrl() {
        return getServerNetAddressHost() + "mobile/settings/set";
    }

    public String getASRResultQueryUrl() {
        return getServerNetAddressHost() + "shortcontent-web/subtitle";
    }


    public String getVipFeedTabUrl(String source) {
        return getServerNetAddressHost() + "vip/feed/v1/categories/ts-" + System.currentTimeMillis() + "?source=" + source;
    }

    public String getVipFeedTabFeedUrl(String source, int categoryId, int offset) {
        return getServerNetAddressHost() + "vip/feed/v1/mix/ts-" + System.currentTimeMillis() + "?source=" + source + "&categoryId=" + categoryId + "&offset=" + offset;
    }

    public String getVipFeedRankListSortUrl(long ruleId) {
        return getMNetAddressHost() + "business-content-sort-mobile-web/sortrule/" + ruleId + "/contents/pagination/ts-" + System.currentTimeMillis();
    }

    public String getMyAllAlbumCommentsUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/podcast/album/list/query/" + System.currentTimeMillis();
    }

    public String getAlbumCommentsDetailUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/podcast/comment/list/query/" + System.currentTimeMillis();
    }

    /**
     * 收听达人分类TAB
     */
    public String getGeekTabListUrl() {
        return getServerNetAddressHost() + "discovery-category/superman/queryTabList";
    }

    /**
     * 收听达人列表
     */
    public String getGeekListUrl() {
        return getServerNetAddressHost() + "discovery-category/superman/queryTabPageList";
    }


    public String universalAlbumCheckInAwardRegisterAddress(long awardId) {
        return getMNetAddressHost() + "business-album-check-in-mobile-web/contact/address?awardId=" + awardId;
    }

    public String getMyTrackCommentListUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/user/comment/detail/";
    }

    public String getMyAlbumRateListUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/user/comment/list/query";
    }

    public String activity19123SubsidyTask() {
        return getMNetAddressHost() + "business-vip-task-web/123/listen/task";
    }

    public String activity19123UpdateSubsidyTask() {
        return getMNetAddressHost() + "business-vip-task-web/usertask/update";
    }

    /**
     * 个人页-我授权的专辑-更多
     */
    public String getCopyRightAlbumMore() {
        return getServerNetAddressHost() + "mobile-user/artist/copyrightAlbums/" + System.currentTimeMillis();
    }

    // 待评价列表
    public String getUncommentedList() {
        return getServerNetAddressHost() + "album-comment-mobile/album/unCommented/list/query/" + System.currentTimeMillis();
    }

    /**
     * 专辑售前页--优惠券
     *
     * @param albumId
     * @return
     */
    public String getWholeAlbumCoupons(long albumId) {
        return getServerNetAddressHost() + "product/v1/album/" + albumId + "/promotion/ts-" + System.currentTimeMillis();
    }

    //发现页播放统计上传
    public String getFeedVideoPlayRecordUrl() {
        return getServerNetAddressHost() + "chaos/v1/video/play/record";
    }

    // 声音页广告查询广告链接
    public String getFansTrackUrl(long trackId) {
        return getMNetAddressHost() + "anchor-sell/fans/track/" + trackId + "/ts/url";
    }

    public String getCommentSettingUrl() {
        return getServerNetAddressHost() + "messenger-web/v1/cmn_message/push_setting/" + System.currentTimeMillis();
    }

    public String getUpdateCommentSettingUrl() {
        return getServerNetAddressHost() + "messenger-web/v1/cmn_message/push_setting/update";
    }

    public String getMyWalletPageAdData() {
        return getMNetAddressHost() + "pay-operation-gateway-web/pay/operation/wallet/getTopBannerConfig";
    }

    public String getBottomBulletStatusUrl() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/query/advanced/right";
    }

    // 是否显示会员协议
    public String getShouldShowVipProtocolUrl() {
        return getMNetAddressHost() + "business-vip-agreement-web/agreement/info";
    }

    // 同意会员协议
    public String postAgreeVipProtocolUrl() {
        return getMNetAddressHost() + "business-vip-agreement-web/agreement/confirm";
    }

    public String wholeAlbumPageMainData(long albumId) {
        return getServerNetAddressHost() + "product/presale/v3/album/"
                + albumId
                + "/detail/ts-" + System.currentTimeMillis();
    }

    public String wholeAlbumPageDynamicData(long albumId) {
        return getServerNetAddressHost() + "product/presale/v3/album/"
                + albumId
                + "/dynamic/ts-" + System.currentTimeMillis();
    }

    public String wholeAlbumTrackList(long albumId) {
        return getServerNetAddressHost() + "product/presale/v1/album/"
                + albumId
                + "/tracks/ts-" + System.currentTimeMillis();
    }

    public String payAlbumSortRuleGroup(int ruleId) {
        return getMNetAddressHost()
                + "business-content-sort-mobile-web/sortrulegroup/"
                + ruleId
                + "/sortrules/ts-"
                + System.currentTimeMillis();
    }

    public String payAlbumSortRuleContent(int ruleContentId) {
        return getMNetAddressHost()
                + "business-content-sort-mobile-web/sortrule/"
                + ruleContentId
                + "/contents/pagination/ts-"
                + System.currentTimeMillis();
    }

    /**
     * 获取登录用户的达人状态
     *
     * @return
     */
    public String getFlagAndStatus() {
        return getServerNetAddressHost() + "talent-web/talent/flagAndStatus/" + System.currentTimeMillis();
    }

    public String setTopCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/track/comment/setTop";
    }

    public String cancelTopCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/track/comment/cancelTop";
    }

    public String getKachaActivityUrl() {
        return getServerNetAddressHost() + "shortcontent-web/activity/dispatch/" + System.currentTimeMillis();
    }

    /**
     * 打包购商品关联关系
     */
    public String vipAndAlbumPackedBuyRelevance() {
        return getMNetAddressHost() + "business-vip-shopping-cart-mobile-web/order-items";
    }

    /**
     * 打包购商品配置
     */
    public String vipAndAlbumPackedBuyConfig() {
        return getMNetAddressHost() + "business-vip-shopping-cart-mobile-web/sign/props";
    }

    public String wholeAlbumPriceInfoDetail(long albumId) {
        return getServerNetAddressHost()
                + "product/promotion/v10/whole/album/"
                + albumId
                + "/price/detail/ts-"
                + System.currentTimeMillis();
    }

    public String wholeAlbumPriceInfoDynamic(long albumId) {
        return getServerNetAddressHost()
                + "product/promotion/v10/whole/album/"
                + albumId
                + "/price/dynamic/ts-"
                + System.currentTimeMillis();
    }

    public String subsidyExchangeAlbumFreeListen(long albumId, String activityId) {
        return getMNetAddressHost() + "subsidyexchange/"
                + activityId + "/100/"
                + albumId + "/exchange";
    }

    public String vipPageRecommendYouRefresh() {
        return getServerNetAddressHost()
                + "vip/v1/channel/recommendation/cycle/ts-"
                + System.currentTimeMillis();
    }

    public String vipRecommendCategoryAlbumList() {
        return getServerNetAddressHost()
                + "vip/v1/channel/interest/items/ts-" + System.currentTimeMillis();
    }

    public String presaleUnLockState(long albumId) {
        return getServerNetAddressHost() + "product/presale/v1/album/" +
                albumId + "/tracks/dynamic/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取配音专辑
     */
    public String getDubbingAlbumsUrl() {
        return getServerNetAddressHost() + "mobile-user/artist/dubbedAlbums/ts-" + System.currentTimeMillis();
    }

    public String getCallTrackListUrl() {
        return getServerNetAddressHost() + "support-anchor-mobile/support/list/ts-" + System.currentTimeMillis();
    }

    public String getCallPageDataUrl() {
        return getServerNetAddressHost() + "support-anchor-mobile/support/page/ts-" + System.currentTimeMillis();
    }

    public String getCallByPointUrl() {
        return getServerNetAddressHost() + "support-anchor-mobile/support/by_point";
    }

    public String getDynamicToken() {
        return getServerNetAddressHost() + "support-anchor-mobile/support/getToken";
    }

    public String rechargeAgreementH5() {
        return getMNetAddressHost() + "virtual-coin-service-protocol#/agreement";
    }

    public String getMyDriveDeviceList() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/list";
    }


    public String settingDriveModeDelete() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/delete";
    }


    // 获取到第一个付费声音的位置
    public String getFirstPaidTrackPosition() {
        return getServerNetAddressHost() +
                BaseUtil.chooseEnvironmentUrl("album-mobile/album/new/firstPaidTrackPosition",
                        "album-mobile-page/new/firstPaidTrackPosition",
                        "album-mobile-page/new/firstPaidTrackPosition");
    }

    public String batchBuyTrackList(long albumId, int pageId) {
        return getCommentHost() + "product/promotion/v1/single/track/"
                + albumId + "/page/" + pageId
                + "/ts-" + System.currentTimeMillis();
    }

    public String getNewAlbumTimeLimitFreeList() {
        return getServerNetAddressHost()
                + "business-sample-album-mobile-web/v1/validitySampleAlbum/"
                + System.currentTimeMillis();
    }

    public String getChildAchievement() {
        return getCommentHost() + "achievement-listen-web/getLabel";
    }

    public String getHasEarnPoint(){
        return getCommentHost() + "achievement-listen-web/hasEarnPoint";
    }

    public String getPlayGuidePortalUrlFormat(){
        return getCommentHost() + "sound-guide-portal/display/";
    }

    public String getInstantScriptColumnModelUrlFormat(){
        return getCommentHost() + "mobile/track/draft/%d/";
    }

    public String getSuperListenerTipUrl(){
        return getCommentHost() + "talent-web/play/tips";
    }

    public String getAnchorCallDeleteUrl(){
        return getCommentHost() + "support-anchor-mobile/support/record/delete";
    }

    public String createAlbumRnGrouponBuy(long albumId) {
        return getCommentHost() + "product/promotion/v1/album/" + albumId + "/groupon/ts-" + System.currentTimeMillis();
    }

    public String getAlbumGrouponBuyShareContent(long albumId) {
        return getCommentHost() + "product/promotion/v1/album/" + albumId + "/groupon/share/ts-" + System.currentTimeMillis();
    }

    public String getAboutCopyrightUrl() {
        if (BaseConstants.ENVIRONMENT_TEST == BaseConstants.environmentId) {
            return "http://static2.test.ximalaya.com/yx/fe-static-resource/last/dist/doc/copyright.html";
        } else {
            return "https://s1.xmcdn.com/yx/fe-static-resource/last/dist/doc/copyright.html";
        }
    }

    public String getUploadListenDurationUrl() {
        return getHybridHost() + "adopt/api/profile/upGrade";
    }

    public String getRecommendAnchorUrl() {
        return getServerNetAddressHost() + "rec-association/recommend/anchor";
    }

    public String getRecommendFriendOrAnchorUrl() {
        return getServerNetAddressHost() + "rec-association/recommend/user/ts-" + System.currentTimeMillis();
    }

    public String getMutualRelationFriendUrl() {
        return getServerNetAddressHost() + "mobile-message-center/friend/list/ts-" + System.currentTimeMillis();
    }

    public String getFocusOnFriendUrl() {
        return getServerNetAddressHost() + "mobile-message-center/following/list/ts-" + System.currentTimeMillis();
    }

    public String getVipCardInfoUrl() {
        return getMNetAddressHostS() + "business-vip-presale-support-web/my/page/vip/access/ts-" + System.currentTimeMillis();
    }

    public String buyXiMiVipGuideDialogContent() {
        return getMNetAddressHost() + "anchor-ximi-web/guide-purchase/track-buying";
    }

    public String getShareCodeUrl() {
        return getMNetAddressHost() + "business-vip-club-mobile-web/club/sharecode/ts-" + System.currentTimeMillis();
    }

    public String getBookChapterForPlayPageUrl() {
        return getServerNetAddressHost() + "mobile-book-reader/play/chapter/query";
    }

    public String getPlayCommunityOrDiscussData(long trackId) {
        return getServerNetAddressHost() + "mobile-playpage/view/" + trackId + "/" + System.currentTimeMillis();
    }

    public String getWalletOperationResources() {
        return getMNetAddressHostS() + "pay-operation-gateway-web/pay/operation/getConfig";
    }

    public String getWalletOrderResources() {
        return getMNetAddressHostS() + "pay-operation-gateway-web/pay/operation/getMultiConfig";
    }

    public String queryVipAutoRenewStatus() {
        return getMNetAddressHost() + "business-user-subscription-mobile-web/userSubscription/findByDomainUserIdGroupId";
    }

    public String h5OfMySubsidy() {
        return getMNetAddressHostS()
                + "marketing/activity2/6417/ts-" + System.currentTimeMillis() +
                "?use_lottie=false";
    }

    public String h5OfManageAutoRenew() {
        return getMNetAddressHost() + "gatekeeper/signature-management-mobile-web";
    }

    /**
     * 领券接口（2020.09.22）
     * 文档：http://gitlab.ximalaya.com/business/business-promotion-coupon/wikis/http#%25E6%2589%25B9%25E9%2587%258F%25E9%25A2%2586%25E5%2588%25B8%25E6%258E%25A5%25E5%258F%25A3
     */
    public String getRequestCouponUrl() {
        return getMNetAddressHost() + "promotion/coupon/user/allocate";
    }

    /**
     * 批量领券接口
     */
    public String getMultiRebateCouponUrl() {
        return getMNetAddressHost() + "promotion/coupon/user/multiAllocate";
    }

    /**
     * 获取购物车商品数量
     */
    public String getGoodsCountInCartUrl() {
        return getMNetAddressHost() + "business-shopping-cart-mobile-web/cart/quantity/ts-" + System.currentTimeMillis();
    }

    /**
     * 开通自动充值页面的url
     */
    public String getGoToSignAutoChargeUrl() {
        return getMpAddressHostS() + "payable/autopay/show/";
    }

    /**
     * 请求支付设置页面的数据
     */
    public String getAutoChargeManageDataUrl() {
        return getMpAddressHost() + "payable/myautobuy/albums/ts-" + System.currentTimeMillis();
    }

    /**
     * 修改自动充值的金额
     */
    public String getModifyAutoRechargeValueUrl() {
        return getMpAddressHost() + "payable/autopay/modify";
    }

    /**
     * 关闭自动充值
     */
    public String getCancelAutoRechargeUrl() {
        return getMpAddressHost() + "payable/autopay/close";
    }

    /**
     * 尝鲜礼包换一批接口
     */
    public String getUpdateVipFreshGiftUrl() {
        return getServerNetAddressHost() + "vip/fresh/user/cycle/v1/ts-" + System.currentTimeMillis();
    }



    /**
     * 是否有存在的一起听房间
     */
    public String queryUserLiveListenRoomLiving() {
        return getServerNetAddressHost() + "seria-web/room/user/living";
    }

    /**
     * 喜点/喜钻充值成功页 的 客服页面链接
     */
    public String getCustomServiceForRecharge() {
        return getMNetAddressHostS() + "cs-bridge-web/page/contact-cs?systemNum=sOvmN-_H0d6grFYmeZ-4Lw";
    }

    /**
     * 专辑已购页 的 客服页面链接
     */
    public String getCustomServiceForBoughtAlbum() {
        return getMNetAddressHostS() + "cs-bridge-web/page/contact-cs?systemNum=xVhfMJLXC-kVokEf-37DQg&_fix_keyboard=1";
    }

    public String getPodcastHomeUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/api/v2/home";
    }

    public String getPodcastTrackFeedUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/api/v2/feed/getTrackFeed";
    }

    public String getPodcastAllCategoryUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/allCate/metadataList";
    }

    public String getPodcastInterestCategoryUrl() {
        return getServerNetSAddressHost() + "podcast-channel-web/api/v2/feed/interestCustom";
    }

    public String getPodcastQueryInterestUrl() {
        return getServerNetSAddressHost() + "podcast-channel-web/api/v2/feed/queryInterest";
    }

    public String getPodcastAllCategoryAlbumUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/allCate/albumList";
    }

    public String getPodcastRecommendedFeedContextUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/api/v2/feed/recommendedContext";
    }

    public String getPodcastListenExposureFilterUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/api/v2/home/<USER>";
    }

    public String getPodcastTopUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/api/v2/top/list";
    }

    public String getTopIdListUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/top/getTopIdList";
    }

    public String getTopListUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/top/getTopList";
    }

    public String getTagTypeListUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/tag/getTagTypeList";
    }

    public String getTagAggregationUrl() {
        return getServerNetAddressHost() + "podcast-channel-web/tag/tagAggregation";
    }

    /**
     * 访问过的星球场馆
     */
    public String uploadPlanetVisited() {
        return getServerNetAddressHost() + "welisten-mobile/theme/visit";
    }

    public String getRecommendFriendUrl() {
        return getServerPassportHostS() + "user-http-app/v1/friend/recommend";
    }

    public String getRecommendAnchorUrlForDialog() {
        return getMNetAddressHostS() + "fans-web/v1/friend/recommend";
    }

    public String getSignInfoUrl() {
        return getHybridHost() + "web-activity/signIn/actionWithRecords";
    }

    public String queryTaskRecordsUrl() {
        return getHybridHost() + "web-activity/task/taskRecords";
    }

    public String getReceiveTaskAwardUrl() {
        return getHybridHost() + "web-activity/task/drawTaskAward";
    }

    public String getGenTaskTokenUrl() {
        return getHybridHost() + "web-activity/task/genTaskToken";
    }

    public String getIncrTaskProgressUrl() {
        return getHybridHost() + "web-activity/task/incrTaskProgress";
    }

    /**
     * 看过勋章
     */
    public String uploadPlayShareMedalClick() {
        return getServerNetAddressHost() + "discovery-category/shareAct/seeShareMedal";
    }

    /**
     * 分享收听成就列表
     */
    public String getListenMedalShareUrl() {
        return getServerNetAddressHost() + "thirdparty-share/listencj/list";
    }

    /**
     * 分享单个成就
     */
    public String getListenMedalShareDetailUrl() {
        return getServerNetAddressHost() + "thirdparty-share/listencj/detail";
    }

    /**
     * 勋章墙
     */
    public String getListenMedalListUrl() {
        return getServerNetAddressHost() + "achievement-listen-web/listMedal";
    }

    /**
     * 单个勋章
     */
    public String getListenMedalDetailUrl() {
        return getServerNetAddressHost() + "achievement-listen-web/getMedal";
    }

    /**
     * 钱包里的买赠记录页面
     */
    public String getBuyAndPresentRecordPageUrl() {
        return getMNetAddressHostS() + "gatekeeper/album-present-web/present/record/list";
    }

    // 人设2.0新个人页接口 start
    public String getAnchorSpaceIntroUrlV3() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/homepage";
    }

    public String getAnchorWorksAlbumFilterUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/album";
    }

    public String getAnchorWorksRelativeAlbumFilterUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/relatedAlbum";
    }

    public String getAnchorWorksSingleTrackFilterUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/track";
    }

    public String getAnchorWorksDubberAlbumFilterUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/dubber/album";
    }

    // 人设2.0新个人页接口 end


    // 个人页-个人简介（tab上方信息）
    public String getAnchorSpaceIntroUrl() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/intro";
    }

    // 个人页-主页（tab主页）
    public String getAnchorSpaceHomeTabDataUrl() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/homepage";
    }

    public String getAnchorDyncGroupUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/chaos";
    }

    // 个人页-作品（tab作品页）
    public String getAnchorSpaceWorksTabDataUrl() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/works";
    }

    // 个人页-分类专辑接口 更多
    public String getAnchorSpaceMCNAlbumsMoreUrl() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/catalbums/all";
    }

    public String getSyncThirdPartyInfoUrl() {
        return getPassportAddressHosts() + "mobile/profile/syncThirdparty/v1";
    }

    public String getCheckAlbumForCustomPosterUrl() {
        return getServerNetAddressHost() + "thirdparty-share/abtest/poster/page";
    }

    public String getVisitAnchorUrl() {
        return getServerNetAddressHost() + "browsing-history-business/visitHistory/personalPage/add";
    }

    public String getRecentVisitorsInfoUrl() {
        return getServerNetAddressHost() + "browsing-history-business/visitHistory/personalPage/query";
    }

    public String getDeleteAnchorUrl() {
        return getServerNetAddressHost() + "browsing-history-business/visitHistory/personalPage/delete";
    }

    public String uploadClickPLCShareUrl() {
        return getServerNetAddressHost() + "discovery-category/shareAct/seePlcBubble";
    }

    public String getContentCluster() {
        return getServerNetAddressHost() + "discovery-feed/module/contentCluster";
    }

    public String getPunchInData() {
        return getServerNetAddressHost() + "mobile-album/album/study";
    }

    public String getPLCShareZhuLiUrl() {
        return getMNetAddressHost() + "anchor-pull-newuser-web/api/v1/help/trackClick";
    }

    public String getRecommendNewUserRank(int newUserOperationAb) {
        if (newUserOperationAb == 1) {
            return getServerNetSAddressHost() + "discovery-ranking-web/newUser/ranking/queryAllRankType";
        } else {
            return getServerNetSAddressHost() + "discovery-ranking-web/newUser/ranking/v2/queryAllRankType";
        }
    }

    public String getRecommendNewUserRankAlbumList(int newUserOperationAb) {
        if (newUserOperationAb == 1) {
            return getServerNetSAddressHost() + "discovery-ranking-web/newUser/ranking/singleRankList";
        } else {
            return getServerNetSAddressHost() + "discovery-ranking-web/newUser/ranking/v2/singleRankList";
        }
    }

    public String getAllServicesUrl() {
        return getServerNetAddressHost() + "mobile-user/homePage/module/list";
    }

    public String getMineAnchorModelUrl() {
        return getMNetAddressHost() + "mobile-anchor-web/api/v1/userPage/getBannerMessageList";
    }

    public String getMineAnchorMenuUrl() {
        return getMNetAddressHost() + "mobile-anchor-web/api/v1/userPage/getMenuList";
    }

    public String getSendMineReadMsgUrl() {
        return getMNetAddressHost() + "mobile-anchor-web/api/message/readMessage";
    }

    /**
     * 支付面板数据 (打赏喜点弹窗)
     */
    public String getRewardDianDataUrl() {
        return getMNetAddressHost() + "anchor-reward-web/api/reward/panelData";
    }

    /**
     * 打赏（喜点）
     */
    public String getRewardDianUrl() {
        return getMNetAddressHost() + "anchor-reward-web/api/reward/submit";
    }

    /**
     * 查询主播的打赏用户榜
     */
    public String getRewardSupportRankUrl() {
        return getMNetAddressHost() + "anchor-reward-web/api/reward/supportRank";
    }

    public String getThirdPartyDataUrl() {
        return getPassportAddressHosts() + "page/transmission_sdk_android";
    }

    public String getPrivacyPolicyUrl() {
        return getPassportAddressHosts() + "page/privacy_policy";
    }

    public String getRefundAlbumCustomerServiceUrl() {
        return getMNetAddressHostS() + "cs-bridge-web/page/contact-cs?systemNum=HYqg4ZckW7D8D0NUcFqgNA&_fix_keyboard=1";
    }

    public String getMineTagListUrl() {
        return getPassportAddressHosts() + "mobile/tag/list";
    }

    public String getMineTagSelectUrl() {
        return getPassportAddressHosts() + "mobile/tag/select";
    }

    /**
     * 取消点赞专辑评价回复
     * @return
     */
    public String albumReplyDislike() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/dislike/" + System.currentTimeMillis();
    }

    /**
     * 点赞专辑评价回复
     * @return
     */
    public String albumReplyLike() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/like/" + System.currentTimeMillis();
    }

    public String getAlbumShareStoneData() {
        return getServerNetAddressHost() + "mobile-album/album/other/page";
    }

    /**
     * 踩专辑回复
     * @return
     */
    public String albumReplyHate() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/hate/" + System.currentTimeMillis();
    }

    /**
     * 取消踩专辑回复
     * @return
     */
    public String albumReplyUnhate() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/reply/unhate/" + System.currentTimeMillis();
    }

    /**
     * 领取会员特权音质链接
     * */
    public String getRequestVipHighPlayQualityUrl() {
        return getMNetAddressHostS() + "business-vip-support-mobile-web/shq/privilege/deliver";
    }


    // 获取白金会员vip悦听兑换券弹窗数据
    public String getPlatinumVipVoucherPopUrl() {
        return getMNetAddressHostS() + "business-svip-coupon-mobile-web/svip/coupon/popup";
    }

    public String getPlatinumVipVoucherUrl(){
        return getMNetAddressHostS() + "business-svip-coupon-mobile-web/svip/coupon/consume";
    }

    public String getTingListTabInfo() {
        return getServerNetAddressHost() + "mobile/listenlist/queryTags";
    }

    /**
     * 从糖葫芦进入，根据不同的标签拉不同的听单
     * @return
     */
    public String getTingListGroupListFromCalabash() {
        return getServerNetAddressHost() + "mobile/listenlist/queryItems";
    }

    /**
     * 非糖葫芦进入，拉取推荐听单列表
     * @return
     */
    public String getTingListGroupList() {
        return getServerNetAddressHost() + "mobile/listenlist/recommendListenlist";
    }

    /**
     * 获取贷款功能跳转链接
     * */
    public String getTLLoanUrl() {
        return getPageNetSHost() + "mkt/act/33edb381f3bffd4b";
    }

    /**
     * 获取新版频道页请求链接
     * */
    public String getNewCategoryUrl() {
        return getServerNetSAddressHost() + "mobile-category/v1/category/page/queryCategoryPageData";
    }

    /**
     * 获取新版频道页换一换数据
     * */
    public String getChannelChange() {
        return getServerNetSAddressHost() + "mobile-category/v1/category/page/queryCardData";
    }

    /**
     * 频道页面投票卡片投票
     * */
    public String getChannelVoteAction() {
        return getMNetAddressHost() + "anchor-market-platform-web/api/v1/vote/select";
    }

    /**
     * 微信公众号预约获取nonce
     */
    public String getNonceInfoUrl() {
        return getMNetAddressHost() + "reach-web/nonce/" + System.currentTimeMillis();
    }

    /**
     * 微信公众号预约获取ticket跳转链接
     */
    public String getTicketInfoUrl() {
        return getMNetAddressHost() + "reach-web/weChat/guide/createTicket";
    }

    /**
     * 查询纠错记录
     */
    public String getDocErrorsRecord(long trackId) {
        return getServerNetSAddressHost() + "mobile-playpage/ai/doc/errors/" + trackId + "/" + System.currentTimeMillis();
    }

    /**
     * 是否支持
     */
    public String getDocCorrectSupportUrl() {
        return getServerNetSAddressHost() + "mobile-playpage/ai/doc/correct/support";
    }

    /**
     * 话题详情页分享链接
     */
    public String getTopicDetailShareUrl(boolean isTob, String tagId, String color) {
        if (!isTob) {
            //https://m.test.ximalaya.com/gatekeeper/anchor-topic-landing/【tagId】?color=
            String address = getMNetAddressHostS() + "gatekeeper/anchor-topic-landing/" + tagId;
            if (!TextUtils.isEmpty(color)) {
                if (color.startsWith("#")){
                    color = color.substring(1);
                }
                address += "?color=" + color;
            }
            return address;
        }
        return getTopicDetailShareUrl(true, tagId);
    }

    public String getTopicDetailShareUrl(boolean isTob, String tagId) {
        if (tagId == null) return "";
        String address = getMNetAddressHostS() + "gatekeeper/topic-share-h5?tagId=" + tagId;
        if (isTob) {
            address += "&userType=anchor";
        }
        return address;
    }

    /**
     * 月票-签到
     */
    public String getSignUrl() {
        return getMNetAddressHost() + "x-web-activity/signIn/getHomePageSignInInfo";
    }

    /**
     * 二级分类频道页
     */
    public String getChannelTreeMeta() {
        return getServerNetSAddressHost() + "mobile-category/v1/category/page/getChannelTreeMeta";
    }

    /**
     * 广告橱窗的链接
     * */
    public String getAdClosetH5ContentUrl(long trackId) {
        return getMNetAddressHostS() + "newretail-springboard-ssr/product/list?_full_with_transparent_bar=1&gid=&trackId=" + trackId;
    }

    public String getMarketGiftUrl() {
        return getServerNetSAddressHost() + "thirdparty-share/shareRight/giftPackage/receive/" + System.currentTimeMillis();
    }

    public String getUpdateFeedbackResultUrl() {
        return getServerNetAddressHost() + "general-relation-service/general/relation/add/" + System.currentTimeMillis();
    }

    public String getOnlyFocusPicUrl(){
        return getServerNetAddressHost() + "focus-mobile/focusPic/info";
    }

    // 专辑评价引导box
    public String getAlbumCommentBox() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/box";
    }

    // 获取我的月票
    public String getMyMonthlyTicker() {
        return getServerNetAddressHost() + "monthly-ticket-mobile/my-ticket/general/" + System.currentTimeMillis();
    }

    public String receiveMonthlyTicketByAlbumListenTask(long albumId) {
        return getServerNetAddressHost() + "comment-mobile/monthTicket/albumListen/" + albumId + "/receive";
    }

    public String queryAlbumListenTasksToGetMonthlyTickets(long albumId) {
        return getServerNetAddressHost() + "comment-mobile/monthTicket/albumListen/" + albumId;
    }

    // 查询专辑的收听福利
    public String queryListenWelfare(long albumId) {
        return getServerNetAddressHost() + "comment-mobile/monthTicket/listenWelfare/" + albumId;
    }

    // 用户获取福利
    public String receiveAwardForListenWelfare(long albumId) {
        return getServerNetAddressHost() + "comment-mobile/monthTicket/listenWelfare/" + albumId + "/receiveAward";
    }

    public String operateAnchorGuideSwitch() {
        return getServerNetAddressHost() + "comment-mobile/v1/guideComments/show";
    }

    public String getPlayBottomBoxes(){
        return getServerNetAddressHost() + "comment-mobile/box/playPage";
    }

    // 话题详情页组件信息(组件信息非全部都有) http://ops.ximalaya.com/api-manager-backend/router-page/projectApiLook/1763/101382
    public String getTopicDetailComponentsUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v2/topicMobile/detailPage/components";
    }

    // 话题浮层页话题信息 http://ops.ximalaya.com/api-manager-backend/router-page/projectApiLook/1763/102921
    public String getTopicDetailInfoUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v1/topicMobile/middlePage/components";
    }

    // 话题下声音列表信息（分页查） http://ops.ximalaya.com/api-manager-backend/router-page/projectApiLook/1763/101383?activeTab=0
    public String getTopicDetailTrackListUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v2/topicMobile/detailPage/trackList";
    }

    // 查询社区组件专辑来源信息，仅投票+讨论
    public String getCardAlbumSource() {
        return getMNetAddressHost() + "community-widget/api/v1/album/source";
    }

    public String getAiDocFeedbackH5() {
        return getMNetAddressHost() + "cs-flow-app/page/common-feedback?appKey=47c19a5d-e930-40b9-8909-284f5cb79926&_fix_keyboard=1";
    }

    public String getTtsFeedbackH5() {
        return getMNetAddressHost() + "cs-flow-app/page/common-feedback?appKey=c36825f9-659d-4c8d-92a5-e07a7d46f1a2&_fix_keyboard=1";
    }

    public String getPlayPageCanPopUrl() {
        return getMNetAddressHostS() + "mobile-playpage/playpage/float/layer/canPop";
    }

    public String getXimiPayPopUrl() {
        return getMNetAddressHostS() + "anchor-ximi-web/api/v1/playPage/payPop";
    }

    // 高能时刻
    public String highEnergyList(long trackId, long albumId) {
        return getMNetAddressHost() + "community-widget/api/v1/track/interaction/render/" + trackId + "?albumId=" + albumId;
    }

    public String getLikeIconUrl(String bizType) {
        return getMNetAddressHostS() + "comment-mobile/likeIcon/" + bizType;
    }

    public String getShareMedalDetailUrl() {
        return getServerNetSAddressHost() + "thirdparty-share/share/poster/medal/detail/query/" + System.currentTimeMillis();
    }

    public String getShareMedalWallUrl() {
        return getServerNetSAddressHost() + "thirdparty-share/share/poster/medal/wall/query/" + System.currentTimeMillis();
    }

    public String getAlbumRecommendUrl() {
        return getServerNetSAddressHost() + "album-comment-mobile/album/vote/evaluation/" + System.currentTimeMillis();
    }


    public String getMyGroupInfoListUrl() {
        return getServerNetSAddressHost() + "imc-group-web/group/query/all_list/" + System.currentTimeMillis();
    }

    public String getBookFreeListenSettingUrl() {
        return getServerNetSAddressHost() + "mobile/free_listen/settings/query/" + System.currentTimeMillis();
    }

    public String updateBookFreeListenSetting(){

        return getServerNetSAddressHost() +"mobile/free_listen/settings/update";
    }

    //解锁短剧
    public String getUnlockShortPlayUrl(){
        return getMNetAddressHostS() +"trade-v3/unlockShortPlay";
    }

    //查询短剧权益余额
    public String getShortPlayBalanceUrl(){
        return getMNetAddressHostS() +"trade-v3/getShortPlayBalance";
    }

    public String checkLiveTabRedDot(){

        return getLiveServerHost() +"diablo-web/v1/home/<USER>/point";
    }

    /**
     * https://mobile.test.ximalaya.com/mobile-user/video/list?pageNum=0&pageSize=20&toUid=12854"
     * @return
     */
    public String getUserVideoList() {
        return getServerNetSAddressHost() + "mobile-user/video/list";
    }

    /**
     * 获取短剧付费面板数据
     * @return
     */
    public String getShortPlayPricePanel(){
        return getServerNetAddressHost() +"mobile-album/shortPlayVideo/price";
    }

    /**
     * 获取排行榜列表tab专辑列表
     * @return
     */
    public String getRankListItemUrl(){
        return getServerNetAddressHost() +"discovery-feed/sceneTab/cardTabChange/index2401";
    }

    public String getHotTagListItemUrl(){
        return getServerNetAddressHost() +"discovery-feed/sceneCard/v2/cardTabChangeData/hot2024";
    }

    public String getSingleSocialListenListItemUrl() {
        return getServerNetAddressHost() +"discovery-feed/v1/social/listen/refresh";
    }

    public String getSingleLiveListItemUrl() {
        return getLiveServerMobileHttpHost() + "lamia/v1/app/feedback/recommend/card";
    }

    public String getVipRecommendYouRefresh() {
        return getServerNetAddressHost() + "vip/v1/channel/recommendation/cycle/ts-" + System.currentTimeMillis();
    }

    public String getAllCategoryTabUrl() {
        return getServerNetAddressHost() + "discovery-feed/sceneCard/v2/cardTabChangeData/tagWallAllCategory";
    }

    public String getAnchorTagUpdateUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/tags/update";
    }

    public String getCategoryTabsUrl() {
        return getServerNetAddressHost() + "discovery-feed/sceneCard/v2/cardTabChangeData/cateAllTabs";
    }

    public String getCategoryV2List() {
        return getServerNetAddressHost() + "mobile-category/sceneCard/v2/queryCardList/all_category";
    }

    public String getCategoryTagDetail() {
        return getServerNetAddressHost() + "discovery-feed/sceneCard/v2/cardTabChangeData";
    }

    public String traitQueryUser() {
        return getServerNetAddressHost() + "mobile-user/trait/query/user";
    }

    public String getPlayPageAnchorLivingInfo() {
        return getLiveServerHost() + "lamia/v1/track/anchor/tip";
    }

    public String getSubscribeUrl(boolean isSubscribe) {
        if(isSubscribe){
            return getServerNetAddressHost() + "mobile/listenlist/collect/add/"+System.currentTimeMillis();
        }
        return getServerNetAddressHost() + "mobile/listenlist/collect/delete/"+System.currentTimeMillis();
    }
    public String getUserHasSkinPrivilegeUrl(){
        return getServerNetAddressHost() + "mobile-user-grade/decoratorV2/userHasPrivilege";
    }
}
