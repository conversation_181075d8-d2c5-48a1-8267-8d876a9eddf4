package com.ximalaya.ting.android.main.playpage.playy.skin

import android.graphics.Color
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager

object PSkinManager {
    private const val TAG = "PSkinManager"
    @JvmStatic
    var currentSkinConfig: PSkinConfig? = null;
    val isEnabled: Boolean get() = currentSkinConfig != null

    fun getBackgroundVideoUrl(): String? {
        return currentSkinConfig?.moveBackground
    }

    fun getBackgroundImageUrl(): String? {
        return currentSkinConfig?.coverVagueImage
    }

    fun getThemeColor(): Int {
        return currentSkinConfig?.maskLayerColorInt ?: PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
    }

    fun getBtnThemeColor(): Int {
        return currentSkinConfig?.uiColorInt ?: Color.TRANSPARENT
    }

    fun getSeekThumbUrl(): String? {
        return currentSkinConfig?.progressSlider
    }

    fun getPlayBtnUrl(): String? {
        return currentSkinConfig?.playButton
    }

    fun getCoverDecorationUrl(): String? {
        return currentSkinConfig?.cycleCoverBackground
    }
}