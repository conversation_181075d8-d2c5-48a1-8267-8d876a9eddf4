package com.ximalaya.ting.android.main.playpage.playy.component.audio.dialog;

import static com.ximalaya.ting.android.host.manager.NewShowNotesManager.SOURCE_FROM_PLAY_PAGE;
import static com.ximalaya.ting.android.host.manager.listentask.ListenTaskAwardManager.FROM_PLAY;
import static com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants.IRemoveAdHintBenefit.REMOVE_AD_HINT_BENEFIT_MOREPAGE;

import android.Manifest;
import android.app.Activity;
import android.content.res.ColorStateList;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Pair;
import android.view.Gravity;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.framework.util.toast.ToastOption;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.TingListConstants;
import com.ximalaya.ting.android.host.data.model.read.TingReadParam;
import com.ximalaya.ting.android.host.drivemode.DriveModeActivityV3;
import com.ximalaya.ting.android.host.e2e.manager.ClientFlowManager;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.play.XPlayPage;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.NewShowNotesManager;
import com.ximalaya.ting.android.host.manager.PlanTerminateManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.ForwardVideoManager;
import com.ximalaya.ting.android.host.manager.ad.gamead.AdGameUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.listentask.ListenTaskAwardManager;
import com.ximalaya.ting.android.host.manager.play.quality.TrackPlayQualityKt;
import com.ximalaya.ting.android.host.manager.play.soundEffect.TrackPlaySoundEffectManager;
import com.ximalaya.ting.android.host.manager.tinglist.ITingListManager;
import com.ximalaya.ting.android.host.manager.tolisten.AutoPlaySwitchManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.play.TrackQualities;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.read.manager.TingTextToReaderManager;
import com.ximalaya.ting.android.host.read.trace.TingReaderCommonTraceManager;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.VividUtil;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.fragment.RemoveAdMorePageDialogFragment;
import com.ximalaya.ting.android.main.dialog.CallingRingtoneDownloadDialog;
import com.ximalaya.ting.android.main.fragment.find.other.recommend.ReportFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.AlarmManagerFragment;
import com.ximalaya.ting.android.main.fragment.planterminate.PlanTerminateFragmentNew;
import com.ximalaya.ting.android.main.manager.PlayPageWifiIntranetManager;
import com.ximalaya.ting.android.main.manager.TempoManager;
import com.ximalaya.ting.android.main.manager.listentask.ListenTaskManager;
import com.ximalaya.ting.android.main.playModule.view.DlnaActionDialog;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.ISkinAdShowCallBack;
import com.ximalaya.ting.android.main.playpage.dialog.PlanTerminalNewDialog;
import com.ximalaya.ting.android.main.playpage.dialog.settings.PlaySettingsDialog;
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioAdComponentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment;
import com.ximalaya.ting.android.main.playpage.playy.YUtils;
import com.ximalaya.ting.android.main.playpage.playy.component.controlbar.YPlaySimpleControlBar;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager;
import com.ximalaya.ting.android.main.playpage.playy.component.survey.SurveyUtil;
import com.ximalaya.ting.android.main.playpage.playy.component.survey.YSurveyDialogFragment;
import com.ximalaya.ting.android.main.playpage.playy.component.tips.TipsPlaceHolder;
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew;
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager;
import com.ximalaya.ting.android.main.playpage.playy.view.PlayModeSegmentSwitch;
import com.ximalaya.ting.android.main.playpage.util.PlayPageDownloadUtils;
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils;
import com.ximalaya.ting.android.main.playpage.vote.dialog.VoteDialogFragment;
import com.ximalaya.ting.android.main.util.other.CopyrightUtil;
import com.ximalaya.ting.android.main.util.ui.RingtoneUtil;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ZhuPeipei on 2020-05-28 13:45.
 *
 * @Description: 更多操作adapter
 */
public class YPlayMoreActionAdapter extends HolderAdapter<YPlayMoreActionAdapter.MoreActionTag> {
    private final BaseDialogFragment mDialogFragment;
    private final PlayingSoundInfo soundInfo;

    // 判断是否是内网wifi环境
    private String mDocUnableReasonStr = "";

    public enum MoreActionTag {
        FreeAd(R.drawable.main_ic_xplay_more_action_adfree_new, "免广告"),
        ListenTask(R.drawable.main_ic_gift_n_n_line_regular_24, "领积分"),
        AnchorShop(R.drawable.main_playpage_anchor_shop, "主播好物"),
//        Switcher(R.drawable.main_ic_switch, "切换展示内容"),
        TipsDemo(R.drawable.main_ic_sound_quality_n_n_line_regular_24, "tips"),
//        SkinSwitch(R.drawable.main_ic_sound_quality_n_n_line_regular_24, "换肤"),

        Translate(R.drawable.main_ic_translate_line_regular_24, "中文翻译"),
//        SoundQuality(R.drawable.main_ic_sound_quality_n_n_line_regular_24, "音质"),
//        SoundEffect(R.drawable.main_ic_sound_effect_n_n_line_regular_24, "音效选择"),
        PlaySettings(R.drawable.main_ic_more_play_settings, "播放设置"),
        SoundEffectQuality(R.drawable.main_ic_more_soundeffect, "音质音效"),
        ShowNotes(R.drawable.main_ic_shownotes_n_n_line_regular_24, "声音详情"),
        TingReaderMode(R.drawable.host_read_icon_playpage_more_ting_reader_mode_icon, "边听边看"),

        //        Read(R.drawable.main_ic_book_amount_n_line_regular_24, "边听边看"),
        AddTingList(R.drawable.main_ic_add_n_n_line_regular_24, "添加到听单"),
        //        PlaySpeed(R.drawable.main_ic_more_action_speed, "播放速度"),
        AlarmStop(R.drawable.main_ic_xplay_more_action_alarm_stop_new, "定时关闭"),
        DecorationCenter(R.drawable.main_xplay_more_decoration_entry, "装扮中心"),
        MonthTicket(R.drawable.main_ic_xplay_more_action_month_ticket_new, "月票"),

        Alarm(R.drawable.main_ic_xplay_more_action_alarm_start_new, "定时启播"),
        DanMu(R.drawable.main_ic_danmu_n_n_line_regular_24, "弹幕"),
        LoteGame(R.drawable.main_play_action_game, "边听边玩"),
        KaCha(R.drawable.main_ic_kacha_action_new, "ka! 剪辑片段"),
        Ring(R.drawable.main_ic_xplay_more_action_alarm_ring, "设为铃声"),
        Download(R.drawable.main_ic_download_more, "下载节目"),
        PlayRecommend(R.drawable.main_ic_wanbo_n_n_line_regular_24, "自动播放推荐内容"),

        Dlna(R.drawable.main_ic_xplay_more_action_alarm_dlna, "连接外设"),
        DriveMode(R.drawable.main_ic_carplay_n_n_line_regular_24, "驾驶模式"),
        SurveyContent(R.drawable.main_ic_write_n_n_line_regular_24, "内容评价"),
        FeedBack(R.drawable.main_ic_feedback_n_n_line_regular_24, "意见反馈"),
        Complain(R.drawable.main_xplay_more_action_complain, "举报"),
        CopyRight(R.drawable.main_xplay_more_action_copyright, "版权申诉");

        MoreActionTag(int img, String title) {
            this.img = img;
            this.title = title;
        }

        MoreActionTag(String tag, String title) {
            this.tag = tag;
            this.title = title;
        }

        public void setImg(int img) {
            this.img = img;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public void setExtraData(Object extraData) {
            this.extraData = extraData;
        }

        @DrawableRes
        private int img;
        private String title;
        private String tag;
        private Object extraData;
    }

    private String adFreeSubtitle;


    public YPlayMoreActionAdapter(BaseDialogFragment fragment, List<MoreActionTag> listData, PlayingSoundInfo soundInfo) {
        super(fragment.getContext(), listData);
        mDialogFragment = fragment;
        this.soundInfo = soundInfo;
        adFreeSubtitle = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_PLAY_MORE_AD_FREE, "");
        initDocDisableTipsConfig();
    }

    private MoreActionViewHolder timerOffHolder;
    private String lastTimerTs;

    public void updateTimerOffUi(int leftTime, boolean clear) {
        if (leftTime > 0) {
            lastTimerTs = TimeHelper.toTime(leftTime, 0) + "后停止播放";
        } else {
            lastTimerTs = "";
        }
        if (PlanTerminateManager.isTimerContinuePlaying()) {
            clear = true;
        }
        if (timerOffHolder != null) {
            if (clear) {
                timerOffHolder.subTitle.setVisibility(View.GONE);
            } else {
//                timerOffHolder.subTitle.setVisibility(View.VISIBLE);
                timerOffHolder.subTitle.setText(lastTimerTs);
            }
        }
    }

    @Override
    public void notifyDataSetChanged() {
        super.notifyDataSetChanged();
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, MoreActionTag moreActionTag, int position) {
        if (!(holder instanceof MoreActionViewHolder) || moreActionTag == null) {
            return;
        }
        MoreActionViewHolder h = (MoreActionViewHolder) holder;
        h.playModeSegmentSwitch.setVisibility(View.GONE);
        if (moreActionTag.img != 0) {
            h.coverIv.setImageResource(moreActionTag.img);
            h.coverIv.setVisibility(View.VISIBLE);
            h.tagTv.setVisibility(View.GONE);
        } else {
            h.tagTv.setText(moreActionTag.tag);
            h.coverIv.setVisibility(View.GONE);
            h.tagTv.setVisibility(View.VISIBLE);
        }
        h.titleTv.setText(moreActionTag.title);
        h.subTitle.setVisibility(View.GONE);
        if (moreActionTag == MoreActionTag.FreeAd) {
            if (!TextUtils.isEmpty(adFreeSubtitle)) {
                h.subTitle.setVisibility(View.VISIBLE);
                h.subTitle.setText(adFreeSubtitle);
            }
        }

        if (moreActionTag == MoreActionTag.AlarmStop) {
            timerOffHolder = h;
            if (!TextUtils.isEmpty(lastTimerTs)) {
                h.subTitle.setVisibility(View.VISIBLE);
                h.subTitle.setText(lastTimerTs);
            }
        }
        if (moreActionTag == MoreActionTag.DecorationCenter) {
            Object extraInfo = moreActionTag.extraData;
            if (extraInfo instanceof Pair) {
                Pair pair = (Pair) extraInfo;
                if (pair.first instanceof String && !TextUtils.isEmpty((String) pair.first)) {
                    h.subTitle.setVisibility(View.VISIBLE);
                    h.subTitle.setText((String) pair.first);
                }
            }
        }
        if (moreActionTag == MoreActionTag.DanMu) {
            h.mSwitch.setVisibility(View.VISIBLE);
            h.mSwitch.setEnabled(true);
            updateDanMuSwitch(h.mSwitch);
            h.mSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    doDanMuSwitchClick(isChecked);
                }
            });
            h.coverIv.setImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.main_color_333333_dcdcdc)));
            h.titleTv.setTextColor(ContextCompat.getColor(context, R.color.main_color_333333_dcdcdc));
        }
//        else if (moreActionTag == MoreActionTag.Switcher) {
//            h.mSwitch.setVisibility(View.GONE);
//            h.playModeSegmentSwitch.setVisibility(View.VISIBLE);
//            updatePlayModeSwitch(h);
//        }
        else if (moreActionTag == MoreActionTag.Translate) {
            h.mSwitch.setVisibility(View.VISIBLE);
            updateTranslate(h);
        }
        else if (moreActionTag == MoreActionTag.PlayRecommend) {
            h.mSwitch.setVisibility(View.VISIBLE);
            h.mSwitch.setEnabled(false);
            h.coverIv.setImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.main_color_333333_dcdcdc)));
            h.titleTv.setTextColor(ContextCompat.getColor(context, R.color.main_color_333333_dcdcdc));
            updatePlayRecommendSwitch(h.mSwitch);
        } else {
            h.mSwitch.setVisibility(View.GONE);
            h.coverIv.setImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.main_color_333333_dcdcdc)));
            h.titleTv.setTextColor(ContextCompat.getColor(context, R.color.main_color_333333_dcdcdc));
        }

        setClickListener(h.itemView, moreActionTag, position, holder);
        if (moreActionTag == MoreActionTag.FreeAd) {
            Logger.i("-----------msg ---- ", " -------- moreActionTag = FreeAd 上报 ");
            adRecord();
        }

        h.dividerTop.setVisibility(View.GONE);
        h.dividerBottom.setVisibility(View.GONE);
//        AnchorShop
        if (moreActionTag == MoreActionTag.AnchorShop) {
            h.dividerBottom.setVisibility(View.VISIBLE);
        }
        if (moreActionTag == MoreActionTag.ListenTask && soundInfo != null && !YUtils.hasValidAnchorShopUrl(soundInfo)) {
            h.dividerBottom.setVisibility(View.VISIBLE);
        }

        if (moreActionTag == MoreActionTag.DriveMode) {
            h.dividerBottom.setVisibility(View.VISIBLE);
        }
        if (moreActionTag == MoreActionTag.Dlna) {
            h.dividerTop.setVisibility(View.VISIBLE);
        }

        if (moreActionTag == MoreActionTag.SoundEffectQuality) {
            long soundEffectId = TrackPlaySoundEffectManager.getInstance().getTrackSoundEffectId();
            String effectName = TrackPlaySoundEffectManager.getInstance().getNameById(soundEffectId);
            String subTitle = "";
            String soundQualityName = getSoundQualityName();
            if (soundQualityName != null && soundQualityName.contains("音质")) {
                soundQualityName = soundQualityName.replace("音质", "");
            }
            h.titleTv.setText("音质音效");
            if (TextUtils.isEmpty(soundQualityName) && TextUtils.isEmpty(effectName)) {
                subTitle = "";
                h.titleTv.setText("音效");
            } else if (TextUtils.isEmpty(soundQualityName)) {
                subTitle = effectName;
                h.titleTv.setText("音效");
            } else if (TextUtils.isEmpty(effectName)) {
                subTitle = soundQualityName;
            } else {
                subTitle = soundQualityName + " / " + effectName;
            }
            h.subTitle.setText(subTitle);
            h.subTitle.setVisibility(View.VISIBLE);
        }
        h.itemView.setEnabled(true);
        traceItemShow(moreActionTag);
    }

    private String getSoundQualityName() {
        if (soundInfo != null && soundInfo.trackInfo != null) {
            List<TrackQualities> trackQualityList = soundInfo.trackInfo.trackQualityList;
            if (trackQualityList == null || trackQualityList.isEmpty()) {
                return "";
            } else {
                TrackQualities quality = TrackPlayQualityKt.getQualityObjWithDowngrading(trackQualityList, YPlaySimpleControlBar.isWifi());
                if (quality != null) {
                    return quality.getQualityName();
                }
            }
        }
        return "";
    }

    private void traceItemShow(MoreActionTag moreActionTag) {
        if (moreActionTag == null) {
            return;
        }
        if (moreActionTag == MoreActionTag.TingReaderMode) {
            //边听边看曝光埋点
            TingReaderCommonTraceManager.tingReaderPlayPage_MoreEnterModeShow(soundInfo);
        }
        if (moreActionTag == MoreActionTag.Dlna && ClientFlowManager.Companion.getInstance().isOpen()) {
            if (soundInfo == null || soundInfo.trackInfo == null) {
                return;
            }
            long albumId = soundInfo.trackInfo.albumId;
            long trackId = soundInfo.trackInfo.trackId;
            long uid = 0;
            if (soundInfo.userInfo != null) {
                uid = soundInfo.userInfo.uid;
            }
            // 新声音播放页-更多面板-功能条  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(67720)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("trackId", String.valueOf(trackId))
                    .put("albumId", String.valueOf(albumId))
                    .put("anchorId", String.valueOf(uid))
                    .put("currPage", "newPlay")
                    .put("isDuplicateView", "2") // 0，1，2 字段文档见：https://alidocs.dingtalk.com/i/nodes/jb9Y4gmKWr7lEp50HZ55dyaZVGXn6lpz?utm_scene=team_space&utm_source=dingdoc_doc&utm_medium=dingdoc_doc_plugin_card
                    .put("currTrackId", String.valueOf(trackId))
                    .put("currAlbumId", String.valueOf(albumId))
                    .put("Item", "播放设备") // 暂时只在【播放设备】功能条上报
                    .createTrace();
        }
    }

    private void adRecord() {
        List<Advertis> advertisList = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).getForwardAdvertis();
        if (advertisList == null) {
            AdvertisList curAdvertis = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).getCurSoundAdList();
            if (advertisList != null) {
                advertisList = curAdvertis.getAdvertisList();
            }
        }

        // 如果有广告数据，则曝光入口
        if (!ToolUtil.isEmptyCollects(advertisList) && advertisList.get(0) != null) {
            Advertis advertis = advertisList.get(0);
            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                    advertis, AdReportModel.newBuilder(
                            AppConstants.AD_LOG_TYPE_SHOW_OB,
                            AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                            .benefitTip(REMOVE_AD_HINT_BENEFIT_MOREPAGE)
                            .promptObType("1")
                            .adPlayVersion(AdManager.getAdPlayVersion())
                            .ignoreTarget(true).build());
        }
    }

    @Override
    public void onClick(View view, MoreActionTag moreActionTag, int position, BaseViewHolder holder) {
        if (!mDialogFragment.canUpdateUi()) {
            return;
        }
        if (moreActionTag != MoreActionTag.PlayRecommend && moreActionTag != MoreActionTag.Translate) {
            mDialogFragment.dismiss();
        }

        switch (moreActionTag) {
            case Dlna:
                openDlnaDialog();
                break;
            case DriveMode:
                Logger.log("f_tag DriveMode ");
                openDriveModePage();
                break;
            case TingReaderMode:
                openTingReaderMode();
                break;
            case Alarm:
                toAlarmPage();
                break;
            case Complain:
                complain();
                break;
            case CopyRight:
                toCopyRightPage();
                break;
            case Ring:
                doSetCallingRingtone();
                break;
            case LoteGame:
                jumpToLetoGameCenter();
                break;
            case FreeAd:
                showFreeAdBottomDialog();
                break;
            case Download:
                PlayPageDownloadUtils.doDownload();
                break;
            case AlarmStop:
                startTimeOffPage();
                break;
            case KaCha:
                startKachaPage();
                break;
            case DanMu:
                doDanMuSwitchClick();
                break;
            case AddTingList:
                addToTingList();
                break;
            case ListenTask:
                handleListenTaskEntryClick();
                break;
            case PlayRecommend:
                doAutoPlaySetting(holder, moreActionTag);
                break;
            case Translate:
                changeTranslate((MoreActionViewHolder)holder);
                break;
            case TipsDemo:
                showTipsDemo((MoreActionViewHolder)holder);
                break;
//            case SkinSwitch:
//                PSkinManager.toggleSkin();
//                XPlayPage xPlayPage = XPlayPageRef.get();
//                if (xPlayPage instanceof YPlayFragment) {
//                    ((YPlayFragment) xPlayPage).checkSkinNotify();
//                }
//                break;
            case FeedBack:
                goFeedBack();
                break;
            case SurveyContent:
                // 内容评价
                toSurveyContent();
                break;
            case MonthTicket:
                obtainMonthTicket();
                break;
            case ShowNotes:
                startShowNotes();
                break;
            case SoundEffectQuality:
                showSoundEffectDialog(ChooseTrackSoundEffectAiDialogXNew.TAB_EFFECT);
                break;
            case PlaySettings:
                showPlaySettings();
                break;
            case AnchorShop:
                goAnchorShop();
                break;
            case DecorationCenter:
                toDecorationCenter(moreActionTag);
                break;
            default:
                break;
        }

        PlayingSoundInfo info = soundInfo;
        if (info == null || info.trackInfo2TrackM() == null || info.toAlbumM() == null) {
            return;
        }
        TrackM track = info.trackInfo2TrackM();
        AlbumM album = info.toAlbumM();

        traceMoreItemClick(moreActionTag);

        new XMTraceApi.Trace()
                .setMetaId(38339)
                .setServiceId("dialogClick")
                .put("item", moreActionTag.title)
                .put("currPage", "新声音播放页")
                .createTrace();
    }

    private void toDecorationCenter(MoreActionTag tag) {
        String itingUrl = "iting://open?msg_type=94&bundle=rn_dress_up_center";
        if (tag.extraData instanceof Pair) {
            Pair pair = (Pair) tag.extraData;
            if (pair.second instanceof String && !TextUtils.isEmpty((String) pair.second)) {
                itingUrl = (String) pair.second;
            }
        }
        mDialogFragment.dismiss();
        Activity activity = BaseApplication.getTopActivity();
        if (activity instanceof MainActivity) {
            ToolUtil.clickUrlAction((MainActivity) activity, itingUrl, null);
        }
    }

    private void toSurveyContent(){
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(ToolUtil.getCtx());
            return;
        }
        mDialogFragment.dismiss();
        Activity activity = BaseApplication.getTopActivity();
        if (activity instanceof MainActivity) {
            Fragment fragment = ((MainActivity)activity).getCurrentTopFragment();
            if (fragment instanceof BaseFragment2) {
                TrackM track = getCurTrack();
                if (track == null || track.getDataId() <= 0 || track.getAlbum() == null) {
                    return;
                }
                YSurveyDialogFragment ySurveyDialogFragment = YSurveyDialogFragment.newInstance(SurveyUtil.INSTANCE.getSURVEY_URL_CONTENT(),
                        track.getAlbum().getAlbumId(), track.getDataId(), "", track.getAnchorUid(), SurveyUtil.SURVEY_TYPE_CONTENT);
                ySurveyDialogFragment.show(fragment.getChildFragmentManager(),
                        "YSurveyDialogFragment");
            }
        }
    }

    private void showSoundEffectDialog(String focusTab) {
        mDialogFragment.dismiss();
        YPlayFragment.showSoundEffectDialog(false, focusTab, soundInfo);
    }


    private void showPlaySettings() {
        Fragment frag = mDialogFragment.getParentFragment();
        mDialogFragment.dismiss();
        if (frag != null) {
            long albumId = soundInfo != null && soundInfo.albumInfo != null ? soundInfo.albumInfo.albumId : 0L;
            long trackId = soundInfo != null && soundInfo.trackInfo != null ? soundInfo.trackInfo.trackId : 0L;

            PlaySettingsDialog.show(frag.getChildFragmentManager(), trackId, albumId);
        }
    }

    private void goAnchorShop() {
        mDialogFragment.dismiss();
        if (soundInfo != null) YUtils.showAnchorShop(soundInfo);
    }

    private void showSoundQualityDialog() {
        mDialogFragment.dismiss();
        Activity activity = BaseApplication.getTopActivity();
        if (activity instanceof MainActivity) {
            Fragment fragment = ((MainActivity)activity).getCurrentTopFragment();
            if (fragment instanceof BaseFragment2) {
                if (!XPlayCommercialRelatedUtils.checkToShowSHQPrivilegeFloatDialog(
                        soundInfo,
                        (BaseFragment2) fragment,
                        false)
                ) {
                    long albumId = soundInfo != null && soundInfo.albumInfo != null ? soundInfo.albumInfo.albumId : 0L;
                    long trackId = soundInfo != null && soundInfo.trackInfo != null ? soundInfo.trackInfo.trackId : 0L;

                    if (albumId > 0 && trackId > 0) {
//                        YSoundQualityDialog dialog = YSoundQualityDialog.newInstance(albumId, trackId);
//                        dialog.show(
//                                fragment.getChildFragmentManager(),
//                                YSoundQualityDialog.class.getSimpleName()
//                        );
                    }

                }
            }
        }

    }

    private void obtainMonthTicket() {
        if (!UserInfoMannage.hasLogined()) {
            Activity activity = BaseApplication.getTopActivity();
            if (activity == null) {
                return;
            }
            UserInfoMannage.gotoLogin(activity);
            return;
        }
        if (soundInfo == null || soundInfo.trackInfo2TrackM() == null) {
            return;
        }

        TrackM trackM = soundInfo.trackInfo2TrackM();
        if (trackM == null || trackM.getAlbumM() == null) {
            return;
        }

        Activity activity = BaseApplication.getTopActivity();
        if (activity instanceof MainActivity && BaseUtil.isForegroundIsMyApplication(activity)) {
            VoteDialogFragment.show(
                    ((MainActivity) activity).getSupportFragmentManager(),
                    "", trackM.getDataId(), trackM.getAlbumM().getId(), trackM.getAnchorUid()
            );
        }
    }

    private void initDocDisableTipsConfig() {
        mDocUnableReasonStr = ConfigureCenter.getInstance().getString("toc",
                "ai_text_cannot_show_toc_toast", "当前声音暂无版权，无法展示文稿");
    }

    // 内网wifi环境下，弹出不能显示文稿的原因
    private void doDocBtnDisableClick() {
        if (PlayPageWifiIntranetManager.INSTANCE.getMHasLoad()) {
            if (PlayPageWifiIntranetManager.INSTANCE.getMIsIntranetWifi()) {
                if (soundInfo == null || soundInfo.docExtInfo == null || TextUtils.isEmpty(soundInfo.docExtInfo.missingReason)) {
                    CustomToast.showToast(mDocUnableReasonStr);
                } else {
                    CustomToast.showToast(soundInfo.docExtInfo.missingReason);
                }
            } else {
                CustomToast.showToast(mDocUnableReasonStr);
            }
        } else {
            CustomToast.showToast(mDocUnableReasonStr);
        }
    }

    private void traceMoreItemClick(YPlayMoreActionAdapter.MoreActionTag moreActionTag){
        PlayingSoundInfo info = soundInfo;
        if (info == null || info.trackInfo2TrackM() == null || info.toAlbumM() == null) {
            return;
        }
        TrackM track = info.trackInfo2TrackM();
        AlbumM album = info.toAlbumM();

        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .click(17637)
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(track.getDataId()))
                .put("currAlbumId", String.valueOf(album.getId()))
                .put("anchorId", String.valueOf(track.getUid()))
                .put("categoryId", String.valueOf(track.getCategoryId()))
                .put("Item", moreActionTag.title)
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
        if (moreActionTag == YPlayMoreActionAdapter.MoreActionTag.PlayRecommend) {
            trace.put("action", AutoPlaySwitchManager.INSTANCE.isAutoPlay() ? "close" : "open");
        }
        trace.createTrace();
    }

    private void doDanMuSwitchClick() {
        if (mDialogFragment instanceof YPlayMoreActionDialogFragment) {
            boolean isOpen = ((YPlayMoreActionDialogFragment) mDialogFragment).isDanmakuOpen();
            ((YPlayMoreActionDialogFragment) mDialogFragment).onDanMuSwitchOpen(!isOpen);
        }
    }

    private void doDanMuSwitchClick(boolean isChecked) {
        if (mDialogFragment instanceof YPlayMoreActionDialogFragment) {
            ((YPlayMoreActionDialogFragment) mDialogFragment).onDanMuSwitchOpen(isChecked);
        }
    }

    private void updateDanMuSwitch(CheckBox danmuBox) {
        if (danmuBox != null && mDialogFragment instanceof YPlayMoreActionDialogFragment) {
            boolean isOpen = ((YPlayMoreActionDialogFragment) mDialogFragment).isDanmakuOpen();
            danmuBox.setChecked(isOpen);
        }
    }

    private void updatePlayRecommendSwitch(CheckBox cb){
        if (cb!= null && mDialogFragment instanceof YPlayMoreActionDialogFragment) {
            boolean isOpen = AutoPlaySwitchManager.INSTANCE.isAutoPlay();
            cb.setChecked(isOpen);
        }
    }

    private void updateTranslate(MoreActionViewHolder h){
        if (h!= null && mDialogFragment instanceof YPlayMoreActionDialogFragment) {
            boolean open = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_LRC_TRANSLATE_SWITCH, true);
            h.mSwitch.setChecked(open);
//            h.mSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
//                @Override
//                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//
//                }
//            });
        }
    }

    private void changeTranslate(MoreActionViewHolder h) {
        XPlayPage xPlayPage = XPlayPageRef.get();
        if (xPlayPage instanceof YPlayFragment) {
            YCoverComponentsManager coverComponentsManager = ((YPlayFragment) xPlayPage).getCoverManager();
            if (coverComponentsManager != null) {
                boolean isOpen = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_LRC_TRANSLATE_SWITCH, true);
                isOpen = !isOpen;
                h.mSwitch.setChecked(isOpen);

                MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_LRC_TRANSLATE_SWITCH, isOpen);
                coverComponentsManager.updateDocTranslate(isOpen);
            }
        }
    }

    private void showTipsDemo(MoreActionViewHolder h) {
        XPlayPage xPlayPage = XPlayPageRef.get();
        if (xPlayPage instanceof YPlayFragment) {
            TipsPlaceHolder.showDemoTip((YPlayFragment) xPlayPage, context);
        }
    }

    private void openTingReaderMode() {
        if (mDialogFragment == null) {
            return;
        }
        Fragment parentFragment = mDialogFragment.getParentFragment();
        if (parentFragment instanceof BaseFragment2) {
            //点击埋点
            TingReaderCommonTraceManager.tingReaderPlayPage_MoreEnterModeClick(soundInfo);
            TingReadParam param = new TingReadParam();
            param.enterFrom = TingTextToReaderManager.TR_ENTER_FROM_PLATPAGE_MORE_ITEM;
            //跳转到边听边看模式
            TingTextToReaderManager.enterTingReader((BaseFragment2) parentFragment.getParentFragment(), param);
        }
    }

    public String getDocType() {
        if (soundInfo == null || soundInfo.docInfo == null) {
            return "ASR";
        }
        int docType = soundInfo.docInfo.docType;
        if (docType == PlayingSoundInfo.DocInfo.TYPE_LRC) {
            return "字幕";
        } else if (docType == PlayingSoundInfo.DocInfo.TYPE_TTS) {
            return "TTS";
        } else if (docType == PlayingSoundInfo.DocInfo.TYPE_AI_DOC) {
            return "ASR";
        } else {
            return "ASR";
        }
    }

    private void startKachaPage() {
        if (mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            AudioPlayUtil.startKachaPage(
                    soundInfo,
                    (BaseFragment2) mDialogFragment.getParentFragment()
            );
        }
    }

    private void showSpeedConfig() {
        long albumId = soundInfo != null && soundInfo.albumInfo != null ? soundInfo.albumInfo.albumId : 0L;
        if (albumId > 0 && VividUtil.isVividAlbumCurPlaying(albumId + "")) {
            ToastOption option = new ToastOption();
            option.textGravity = Gravity.CENTER;
            CustomToast.showToast(
                    "全景声暂不支持倍速功能\n 请切换其它音质后使用本功能",
                    ToastManager.LENGTH_LONG,
                    option
            );
        } else {
            TempoManager.getInstance().showConfigDialog(context, true, true);
        }
    }

    private void doAutoPlaySetting(BaseViewHolder holder, MoreActionTag moreActionTag) {
        if (!(holder instanceof MoreActionViewHolder)) {
            return;
        }
        boolean status = !AutoPlaySwitchManager.INSTANCE.isAutoPlay();
        CheckBox cbSwitch = ((MoreActionViewHolder) holder).mSwitch;
        cbSwitch.setChecked(status);
        AutoPlaySwitchManager.INSTANCE.setAutoPlay(status, new IDataCallBack<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean data) {
                if (data == null || !data) {
                    cbSwitch.setChecked(!status);
                }
            }

            @Override
            public void onError(int code, String message) {
                cbSwitch.setChecked(!status);
            }
        });
    }

    private void goFeedBack() {
        Activity activity = mDialogFragment.getActivity();
        if (activity instanceof MainActivity) {
            String url = "https://m.ximalaya.com/cs-flow-app/page/common-feedback?appKey=9622ec6c-4d25-419d-a4c6-ddee614e28e7&_fix_keyboard=1";
            url = ConfigureCenter.getInstance().getString("toc", "feedback_url", url);
            if (soundInfo != null && soundInfo.albumInfo != null && soundInfo.trackInfo != null && soundInfo.userInfo != null) {
                long albumId = soundInfo.albumInfo.albumId;
                url = url + SurveyUtil.INSTANCE.getSpendUrl(SurveyUtil.SURVEY_TYPE_FUNCTION, UserInfoMannage.getUid(), albumId, soundInfo.trackInfo.trackId, "", soundInfo.userInfo.uid);
            }
            ToolUtil.clickUrlAction((MainActivity) activity, url, null);
        }
    }

    private void startShowNotes() {
        if (soundInfo != null && soundInfo.trackInfo != null) {
            NewShowNotesManager.INSTANCE.startShowNotesDetailFragment(
                    SOURCE_FROM_PLAY_PAGE, null, 0L,
                    soundInfo.trackInfo.trackId,
                    null
            );
        }
    }

    private void startTimeOffPage() {
        boolean isSleep = false;
        if (soundInfo != null && soundInfo.otherInfo != null) {
            isSleep = soundInfo.otherInfo.isSleeping;
        }
        PlanTerminateFragmentNew fragment;
        if (isSleep) {
            String url = "";
            String title = "";
            if (soundInfo.albumInfo != null) {
                url = soundInfo.albumInfo.coverLarge;
            }
            if (soundInfo.trackInfo != null) {
                title = soundInfo.trackInfo.title;
            }
            fragment = PlanTerminateFragmentNew.newInstance(PlanTerminateFragmentNew.TYPE_SLEEP, url, title);
        } else if (soundInfo != null && mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            String alarmTips = "";
            if (soundInfo.otherInfo != null) {
                alarmTips = soundInfo.otherInfo.alarmTip;
            }

            String alarmSubTips = "";
            if (soundInfo.otherInfo != null) {
                alarmSubTips = soundInfo.otherInfo.alarmSubTips;
            }

            String alarmListenAwardUrl = "";
            if (soundInfo.otherInfo != null) {
                alarmListenAwardUrl = soundInfo.otherInfo.alarmListenAwardUrl;
            }
            PlanTerminalNewDialog.Companion.show(
                    mDialogFragment.getParentFragment().getChildFragmentManager(),
                    "newPlay",
                    alarmTips,
                    alarmSubTips,
                    alarmListenAwardUrl
            );
            return;
        } else {
            fragment = PlanTerminateFragmentNew.newInstance(PlanTerminateFragmentNew.TYPE_NORMAL);
        }
        if (mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            ISkinAdShowCallBack service =
                    PlayPageInternalServiceManager.getInstance().getService(ISkinAdShowCallBack.class);
            if (service != null) {
                Advertis advertis = service.getAdvertis();
                fragment.setAd(advertis);
            }

            fragment.show(mDialogFragment.getParentFragment().getChildFragmentManager(), PlanTerminateFragmentNew.TAG);
        }
    }

    private void addToTingList() {
        if (!mDialogFragment.canUpdateUi() || soundInfo == null || soundInfo.trackInfo2TrackM() == null) {
            return;
        }
        TrackM track = soundInfo.trackInfo2TrackM();
        if (!(mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        final BaseFragment2 frag = (BaseFragment2) mDialogFragment.getParentFragment();
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
            if (funAction != null && frag != null && frag.canUpdateUi()) {
                ITingListManager tingListManager = funAction.newTingListManager(frag);
                boolean isMusic = false;
                if (soundInfo.otherInfo != null) {
                    isMusic = soundInfo.otherInfo.musicRelated == PlayingSoundInfo.OtherInfo.IS_MUSIC;
                }
                tingListManager.showTingList(TingListConstants.TYPE_TRACK, track.getDataId(),
                        track, isMusic, TingListConstants.TING_LIST_UBT_PREV_RESOURCE_TYPE_MORE_DIALOG);
                tingListManager.setITingListResultCallback(tingListInfoModel -> {
                    if (mDialogFragment instanceof YPlayMoreActionDialogFragment) {
                        YPlayMoreActionDialogFragment.ClickCallback callback = ((YPlayMoreActionDialogFragment) mDialogFragment).getClickCallback();
                        if (callback != null) {
                            callback.onAddTingListSuccess(tingListInfoModel);
                        }
                    }
                });
            }
        });
        new UserTracking()
                .setSrcPage("track")
                .setTrackId(track.getDataId())
                .setSrcModule("more")
                .setItem("button")
                .setItemId("addToSubject")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
    }

    private void handleListenTaskEntryClick() {

        if (mDialogFragment.getActivity() instanceof MainActivity) {
            if (!ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.SIGNIN_NEED_LOGIN, false) || UserInfoMannage.hasLogined()) {
                ListenTaskAwardManager.INSTANCE.toTaskCenter(mDialogFragment.getActivity(), FROM_PLAY);
                ListenTaskManager.getInstance().setOnListenTaskClick(true);
                MmkvCommonUtil.getInstance(mDialogFragment.getActivity()).saveBoolean(PreferenceConstantsInHost.KEY_MAIN_NEED_UPDATE_LISTEN_TASK, true);
            } else {
                UserInfoMannage.gotoLogin(mDialogFragment.getActivity());
            }
        }
    }

    private void showFreeAdBottomDialog() {

        List<Advertis> advertisList = XmPlayerManager.getInstance(context).getForwardAdvertis();
        if (advertisList == null) {
            AdvertisList curAdvertis = XmPlayerManager.getInstance(context).getCurSoundAdList();
            if (advertisList != null) {
                advertisList = curAdvertis.getAdvertisList();
            }
        }

        if (advertisList == null) {
            return;
        }
        Advertis advertis = advertisList.get(0);
        if (advertis != null) {
            //1、广告、VIP均有返回，拉起弹层，用户可选择看视频或购买VIP免除广告，按原逻辑不做改动
            //2、VIP有返回，广告无返回，点击入口直接跳转至VIP落地页
            //3、广告有返回，VIP无返回，点击入口直接播放激励视频
            if (hasVipLink(advertis)) {
                RemoveAdMorePageDialogFragment removeAdBottomDialogFragment = new RemoveAdMorePageDialogFragment();
                removeAdBottomDialogFragment.show(((FragmentActivity) context).getSupportFragmentManager(), "free_ad_dialog");
            } else {
                Activity optActivity = MainApplication.getOptActivity();
                if (ToolUtil.activityIsValid(optActivity)) {
                    ForwardVideoManager.getInstance().lookVideo(optActivity, 3, REMOVE_AD_HINT_BENEFIT_MOREPAGE, getRewardVideoCallBack(advertis), advertis);
                    AdManager.adRecord(context, advertis
                            , AdReportModel.newBuilder(
                                    AppConstants.AD_LOG_TYPE_CLICK_OB,
                                    AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                    .benefitTip(REMOVE_AD_HINT_BENEFIT_MOREPAGE)
                                    .promptObType("3")
                                    .ignoreTarget(true).build());
                }
            }
            AdManager.adRecord(context, advertis
                    , AdReportModel.newBuilder(
                            AppConstants.AD_LOG_TYPE_CLICK_OB,
                            AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                            .benefitTip(REMOVE_AD_HINT_BENEFIT_MOREPAGE)
                            .promptObType("1")
                            .ignoreTarget(true).build());
        }
    }

    private boolean hasVipLink(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        return !TextUtils.isEmpty(advertis.getCopywriting())
                && !TextUtils.isEmpty(advertis.getVipPaymentLink());
    }

    private ForwardVideoManager.IRewardVideoCallBack getRewardVideoCallBack(Advertis forwardAd) {
        IAudioAdComponentService audioAdComponentService =
                PlayPageInternalServiceManager.getInstance().getService(IAudioAdComponentService.class);
        ForwardVideoManager.IRewardVideoCallBack rewardVideoCallBack = null;
        if (audioAdComponentService != null) {
            rewardVideoCallBack = audioAdComponentService.getRewardVideoCallBack(forwardAd);
        }
        return rewardVideoCallBack;
    }

    private void jumpToLetoGameCenter() {
        if (mDialogFragment == null || mDialogFragment.getActivity() == null) {
            return;
        }
        TrackM track = getCurTrack();
        if (track == null || track.getAlbum() == null) {
            return;
        }

        // 专辑id 和 声音id
        AdGameUtil.jumpToGameBundle(track.getAlbum().getAlbumId(), track.getDataId(), track.getCategoryId());
    }

    private TrackM getCurTrack() {
        if (soundInfo == null) {
            return null;
        }
        return soundInfo.trackInfo2TrackM();
    }

    /**
     * 设置铃声
     */
    private void doSetCallingRingtone() {
        Track track = getCurTrack();
        if (track == null || mDialogFragment == null || mDialogFragment.getActivity() == null) {
            return;
        }

        if (mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            if (CopyrightUtil.showCopyRightTipsDialogIfNeeded((BaseFragment2) mDialogFragment.getParentFragment(),
                    soundInfo)) {
                return;
            } else if (!track.isHasCopyRight()) {
                CustomToast.showFailToast(PlayPageDataManager.getInstance().getNoCopyrightMsg());
                return;
            }
        }

        Activity activity = mDialogFragment.getActivity();
        SharedPreferencesUtil settings = SharedPreferencesUtil
                .getInstance(activity);
        final long ringtoneTrackId = settings
                .getLong(PreferenceConstantsInHost.TINGMAIN_KEY_CALLING_RINGTONE_TRACKID);

        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                    (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                        {
                            put(Manifest.permission.WRITE_EXTERNAL_STORAGE, com.ximalaya.ting.android.host.R.string.host_deny_perm_sdcard);
                        }
                    }, new IMainFunctionAction.IPermissionListener() {
                        @Override
                        public void havedPermissionOrUseAgree() {
                            File file;
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                File vDirPath = new File(activity.getExternalFilesDir(Environment.DIRECTORY_RINGTONES), "media/ringtones");
                                file = new File(vDirPath, String.valueOf(ringtoneTrackId) + ".mp3");
                            } else {
                                file = new File(Environment.getExternalStorageDirectory() + "/media/ringtones",
                                        ringtoneTrackId + ".mp3");
                            }

                            if (ringtoneTrackId == track.getDataId() && file.exists()) {
                                String name = "";
                                if (track.getAnnouncer() != null) {
                                    name = track.getAnnouncer().getNickname();
                                }
                                String path = Uri.fromFile(file).toString();
                                RingtoneUtil.setMyRingtone(activity, path, RingtoneUtil
                                        .buildMediaInfo(track.getTrackTitle(),
                                                activity.getString(R.string.main_xm_ring), name));
                            } else {
                                if (track.isPaid() && !track.isFree()) {
                                    CustomToast.showFailToast("付费声音不支持设置为铃声");
                                    return;
                                }
                                CallingRingtoneDownloadDialog processDlg = new CallingRingtoneDownloadDialog(
                                        activity);
                                processDlg.setDownloadInfo(track);
                                processDlg.show();
                            }
                        }

                        @Override
                        public void userReject(Map<String, Integer> noRejectPermiss) {
                            CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_failed_to_request_storage_permission);
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void openDlnaDialog() {
        if (!(mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        TrackM track = getCurTrack();
        if (track == null) {
            return;
        }
        if (!track.isHasCopyRight()) {
            CustomToast.showFailToast("版权方要求，该资源在该地区无法播放");
            return;
        }
        if (ClientFlowManager.Companion.getInstance().isOpen()) {
            ClientFlowManager.Companion.getInstance().gotoTransformSettingPage();
            trace();
//            ITingHandler itingHandler = new ITingHandler();
//            itingHandler.handleITing(BaseApplication.getMainActivity(),
//                    Uri.parse("iting://open?msg_type=603&bundle=rn_transform_settings&opName=list&opType=3"));
        } else {
            DlnaActionDialog dlnaDialog = new DlnaActionDialog(mDialogFragment.getActivity(),
                    (BaseFragment2) mDialogFragment.getParentFragment(), getCurTrack());
            dlnaDialog.toggle();
        }

        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .setSrcModule("外放设备")
                .setItem("button")
                .setItemId("外放设备")
                .setId("5273")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);

        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .setSrcModule("功能入口")
                .setItem("button")
                .setItemId("投射")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
    }

    private void trace() {
//        PlayingSoundInfo info = soundInfo;
//        if (info == null || info.trackInfo2TrackM() == null || info.toAlbumM() == null) {
//            return;
//        }
//        TrackM track = info.trackInfo2TrackM();
//        AlbumM album = info.toAlbumM();
//        // 新声音播放页-更多面板-功能条  点击事件
//        new XMTraceApi.Trace()
//                .click(17637)
//                .put("currPage", "newPlay")
//                .put("currTrackId", String.valueOf(track.getDataId()))
//                .put("currAlbumId", String.valueOf(album.getId()))
//                .put("Item", "连接外设")
//                .put("subType", "TTS | 字幕 | ASR")
//                .createTrace();
    }

    private void openDriveModePage() {
        //DriveModeActivityV2.startDriveModeActivityV2();
        DriveModeActivityV3.startDriveModeActivityV3(DriveModeActivityV3.FROM_PLAY_PAGE);
        if (getCurTrack() != null) {
            new UserTracking(6666, "track", "button")
                    .setSrcPageId(getCurTrack().getDataId())
                    .setSrcModule("more")
                    .setItemId("驾驶模式")
                    .statIting(XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
        }
    }

    private void toAlarmPage() {
        TrackM track = getCurTrack();
        if (track == null || !track.isHasCopyRight()) {
            String noCopyRightMsg = "版权方要求，该资源在该地区无法播放";
            CustomToast.showFailToast(noCopyRightMsg);
            return;
        }
        if (mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            ((BaseFragment2) mDialogFragment.getParentFragment()).startFragment(AlarmManagerFragment.newInstance());
        }
        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_SET_CLOCK);
    }

    private void complain() {
        Track track = getCurTrack();
        if (UserInfoMannage.hasLogined() && track != null && (mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            ReportFragment reportFragment = ReportFragment.newInstanceByTrack(track.getDataId(), track.getAgeLevel(), track.getUid());
            ((BaseFragment2) mDialogFragment.getParentFragment()).startFragment(reportFragment);
        } else if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mDialogFragment.getContext());
        }
    }

    private void toCopyRightPage() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mDialogFragment.getContext());
            return;
        }
        if (!(mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        TrackM track = getCurTrack();
        if (track == null || track.getDataId() <= 0) {
            return;
        }
        try {
            String url = ConfigureCenter.getInstance().getString("tob", "tort_url") + "?trackId=" + track.getDataId();
            ((BaseFragment2) mDialogFragment.getParentFragment()).startFragment(NativeHybridFragment.newInstance(url, true));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_yplay_more_action;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new MoreActionViewHolder(convertView);
    }

    private static class MoreActionViewHolder extends BaseViewHolder {
        View itemView;
        ImageView coverIv;
        TextView titleTv;
        TextView tagTv;
        TextView subTitle;
        CheckBox mSwitch;
        View dividerTop;
        View dividerBottom;
        PlayModeSegmentSwitch playModeSegmentSwitch;

        public MoreActionViewHolder(View convertView) {
            itemView = convertView;
            coverIv = convertView.findViewById(R.id.main_play_more_action_item_iv);
            titleTv = convertView.findViewById(R.id.main_play_more_action_item_tv);
            tagTv = convertView.findViewById(R.id.main_play_more_action_item_tag_tv);
            subTitle = convertView.findViewById(R.id.main_play_more_action_item_subtitle);
            mSwitch = convertView.findViewById(R.id.main_cb_switch);
            dividerTop = convertView.findViewById(R.id.main_item_divider_top);
            dividerBottom = convertView.findViewById(R.id.main_item_divider_bottom);
            playModeSegmentSwitch = convertView.findViewById(R.id.main_segment_switch);
         }
    }
}
