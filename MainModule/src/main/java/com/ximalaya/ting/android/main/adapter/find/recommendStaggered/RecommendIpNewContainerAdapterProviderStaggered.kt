package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.NewShowNotesManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.ShowTagManager
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.DomainColorUtil
import com.ximalaya.ting.android.host.view.CornerRelativeLayout
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.ShowTagPlayUtil
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataPlayStatusStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 重磅热推/平台上新容器
 */
class RecommendIpNewContainerAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val is1V4Module: Boolean,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendIpNewContainerAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataPlayStatusStaggered<RecommendIpNewContainerAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendIpNewContainerAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendIpNewContainerAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew> {

    private var mOldState = RecyclerView.SCROLL_STATE_IDLE
    private var mListCardViewHolder: SocialListCardViewHolder? = null


    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_ip_new_container_card,
            parent,
            false
        )
    }

    override fun onPlayStart() {
    }

    override fun onPlayPause() {
    }

    override fun onSoundPlayComplete() {
        mListCardViewHolder?.rcvSocialList?.adapter?.notifyDataSetChanged()
    }

    class SocialListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val rlRootView: View = convertView.findViewById(R.id.main_rl_root_view)
        val activeTopHolder: View = convertView.findViewById(R.id.main_view_top_holder)
        val normalBg: ImageView = convertView.findViewById(R.id.main_normal_bg_top)
        val activeBg: ImageView = convertView.findViewById(R.id.main_active_bg_top)
        val ivTitleHotFix: ImageView = convertView.findViewById(R.id.main_iv_title_icon)
        val rcvSocialList: RecyclerView = convertView.findViewById(R.id.main_rcv_album_list)
        var tvModuleTittle: TextView = convertView.findViewById(R.id.main_tv_social_list_tittle)
        val tvMore: TextView = convertView.findViewById(R.id.main_tv_more)
        val ivFeedBack: ImageView = convertView.findViewById(R.id.main_iv_feedback)

        // 运营文案
        val rlBottom: View = convertView.findViewById(R.id.main_rl_bottom)
        val ivActiveBg: View = convertView.findViewById(R.id.main_view_active_text_bg)
        val ivActiveComma: ImageView = convertView.findViewById(R.id.main_iv_comma)
        val tvActiveText: TextView = convertView.findViewById(R.id.main_tv_active_text)

        // 第一个专辑view start
        var cslContainerView: View = convertView.findViewById(R.id.main_csl_item_root_view)
        var itemTitleTv: TextView = convertView.findViewById(R.id.main_tv_album_title)
        var albumCoverLayoutView: AlbumCoverLayoutView =
            convertView.findViewById(R.id.main_album_cover_layout)
        var layoutRightTxtArea: View = convertView.findViewById(R.id.main_layout_right_txt_area)
        var itemSubtitle1Tv: TextView = convertView.findViewById(R.id.main_tv_sub_title)
        var layoutShowTags: LinearLayout = convertView.findViewById(R.id.main_layout_show_tag)
        var ivAdTag: ImageView = convertView.findViewById(R.id.main_iv_ad_tag)
        // 第一个专辑view end

        var startSnapHelper: StartSnapHelper? = null
        var lastScreenWidth: Int = BaseUtil.getScreenWidth(convertView.context)
        var firstVisiblePosition: Int = 0
        var uniqueId: String = ""
    }

    override fun onConfigurationChanged(holder: SocialListCardViewHolder?) {
        holder ?: return
        val screenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        if (holder.lastScreenWidth == screenWidth) {
            return
        }
        holder.lastScreenWidth = screenWidth
    }

    override fun createViewHolder(convertView: View?): SocialListCardViewHolder? {
        PerformanceMonitor.traceBegin("social_createViewHolder")
        if (convertView == null) {
            return null
        }
        val viewHolder = SocialListCardViewHolder(convertView)
        PerformanceMonitor.traceEnd("social_createViewHolder", 7)
        return viewHolder
    }

    override fun bindViewHolder(
        holder: SocialListCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            HandlerManager.postOnUIThread {
                onBindViewHolderInner(holder, position, recommendItemNew, convertView)
            }
        } else {
            onBindViewHolderInner(holder, position, recommendItemNew, convertView)
        }
    }

    private fun onBindViewHolderInner(
        holder: SocialListCardViewHolder?,
        modulePosition: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        mListCardViewHolder = holder
        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        PerformanceMonitor.traceBegin("social_bindViewHolder_" + recommendCommonItem.title)
        holder.tvModuleTittle.text = recommendCommonItem.title

        val exportMore = { action: String ->
            // 新首页-社会化标注-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(61824) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("moduleName", recommendCommonItem.title) // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.moduleId?.toString() ?: "") // 例如：1000000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("action", action)
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
        }

        val performMoreClick = {
            ScenePlayDataUtil.saveDataForRn(recommendItemNew)

            ToolUtil.clickUrlAction(
                fragment,
                recommendCommonItem.landingPage ?: "",
                holder.tvMore
            )
        }

        holder.tvModuleTittle.setOnClickListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            exportMore("click")
            performMoreClick()
        }

        val isActive = recommendCommonItem.ext?.other?.activityStyle ?: false

        var isAllowFeedback = true
        // 活动态不允许负反馈
        if (isActive || is1V4Module) {
            isAllowFeedback = false
        }

        if (isAllowFeedback) {
            holder.ivFeedBack.visibility = View.VISIBLE
            holder.tvMore.visibility = View.GONE
        } else {
            holder.ivFeedBack.visibility = View.GONE

            if (!recommendCommonItem.landingPage.isNullOrEmpty()) {
                holder.tvMore.visibility = View.VISIBLE
                recommendCommonItem.ext?.other?.landingPageText?.apply {
                    holder.tvMore.text = this
                }
            } else {
                holder.tvMore.visibility = View.GONE
            }
        }

        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnClickListener {
            holder.tvModuleTittle.performClick()
        }

        holder.ivFeedBack.setOnClickListener {
            exportMore("click")
            val isShowMore = !recommendCommonItem.landingPage.isNullOrEmpty()
            var level1DisLikeTitle = recommendCommonItem.ext?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick()
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(modulePosition)
                }
            }

            val moreFuncBuild = MoreFuncBuild.createSocialListenMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener
            )

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (modulePosition + 1).toString())
                put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.ext?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }

        val context = convertView?.context ?: BaseApplication.mAppInstance
        val activeText = recommendCommonItem.ext?.other?.activityText ?: ""
        var isActiveText = activeText.isNotEmpty()

        var activeBgUrl = ""
        var normalBgUrl = ""
        val backgroundUrl = if (BaseFragmentActivity.sIsDarkMode) {
            recommendCommonItem.ext?.other?.darkBackground ?: ""
        } else {
            recommendCommonItem.ext?.other?.background ?: ""
        }
        if (isActive) {
            activeBgUrl = backgroundUrl
        } else {
            normalBgUrl = backgroundUrl
        }

        var topPadding = 0

        if (isActive && activeBgUrl.isNotEmpty()) {
            topPadding = 6.dp
            holder.activeTopHolder.visibility = View.VISIBLE
            holder.activeBg.visibility = View.VISIBLE
            holder.normalBg.visibility = View.GONE

            ImageManager.from(context).displayImageSizeInDp(
                holder.activeBg, activeBgUrl, -1, 300, 150
            )
        } else {
            holder.activeTopHolder.visibility = View.GONE
            holder.activeBg.visibility = View.GONE

            if (normalBgUrl.isNotEmpty()) {
                topPadding = 16.dp
                holder.normalBg.visibility = View.VISIBLE
                ImageManager.from(context).displayImageSizeInDp(
                    holder.normalBg, normalBgUrl, -1, 375, 115
                )
            } else {
                holder.normalBg.visibility = View.GONE
            }
        }

        val bottomPadding = if (isActiveText) {
            12.dp
        } else {
            8.dp
        }

        holder.rlRootView.apply {
            setPadding(paddingLeft, topPadding, paddingRight, bottomPadding)
        }

        val iconUrl = recommendCommonItem.ext?.other?.beforeTitleTag ?: ""
        if (iconUrl.isNotEmpty()) {
            holder.ivTitleHotFix.visibility = View.VISIBLE
            ImageManager.from(context).displayImageSizeInDp(
                holder.ivTitleHotFix, iconUrl, R.drawable.main_module_title_red_hot,
                R.drawable.main_module_title_red_hot, 22, 22
            )
        } else {
            holder.ivTitleHotFix.visibility = View.GONE
        }

        val params = holder.rlBottom.layoutParams as MarginLayoutParams
        if (activeText.isEmpty()) {
            // 距离标题高度 + cslContainerView.paddingTop = 16
            params.topMargin = 3.dp

            // 第一个专辑设置左右间距
            holder.cslContainerView.apply {
                setPadding(16.dp, paddingTop, 16.dp, paddingBottom)
            }

            holder.ivActiveBg.visibility = View.GONE
            holder.ivActiveComma.visibility = View.GONE
            holder.tvActiveText.visibility = View.GONE
        } else {
            isActiveText = true
            params.topMargin = 12.dp

            holder.cslContainerView.apply {
                setPadding(28.dp, paddingTop, 28.dp, paddingBottom)
            }

            holder.ivActiveBg.visibility = View.VISIBLE
            holder.ivActiveComma.visibility = View.VISIBLE
            holder.tvActiveText.visibility = View.VISIBLE
            holder.tvActiveText.text = activeText

            val activeTextColorStr = if (BaseFragmentActivity.sIsDarkMode) {
                recommendCommonItem.ext?.other?.activityTextColorDark ?: ""
            } else {
                recommendCommonItem.ext?.other?.activityTextColor ?: ""
            }

            var activeTextColor: Int? = null
            if (activeTextColorStr.isNotEmpty()) {
                try {
                    activeTextColor = Color.parseColor(activeTextColorStr)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            if (activeTextColor == null) {
                activeTextColor = context.resources.getColor(R.color.main_color_F0657C_CC6677)
            }

            holder.ivActiveComma.setColorFilter(activeTextColor)
            holder.tvActiveText.setTextColor(activeTextColor)
        }
        holder.rlBottom.layoutParams = params

        val item: CommonSubElement?
        var list = recommendCommonItem.subElements
        if (!list.isNullOrEmpty()) {
            item = list.firstOrNull()
            if (item != null) {
                val textViewContainerWith = if (isActiveText) {
                    getRpAdaptSize(375) - 24.dp
                } else {
                    getRpAdaptSize(375)
                }
                onBindModuleAlbumView(
                    item,
                    holder,
                    recommendCommonItem,
                    recommendItemNew,
                    modulePosition,
                    textViewContainerWith,
                    isAllowFeedback
                )
                list = list.subList(1, list.size)
            }
        }

        val spanCount = 2
        val cardAlbumListAdapter = SocialListAlbumItemAdapter(
            dataAction,
            fragment,
            recommendCommonItem,
            recommendItemNew,
            list,
            modulePosition,
            holder.rcvSocialList,
            spanCount,
            recommendCommonItem.landingPage.isNullOrEmpty().not(),
            isAllowFeedback,
            isActiveText
        )
        cardAlbumListAdapter.mEnableMoreItem = cardAlbumListAdapter.enableJumpMore
        cardAlbumListAdapter.setRelaseJumpActivityListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            exportMore("slide")
            performMoreClick()
        }
        // 听单专辑列表
        holder.rcvSocialList.adapter = cardAlbumListAdapter
        RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
            this.javaClass.simpleName,
            spanCount,
            modulePosition,
            recommendItemNew
        )
        val layoutManager =
            GridLayoutManager(convertView?.context, spanCount, GridLayoutManager.HORIZONTAL, false)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) spanCount else 1
            }
        }
        holder.rcvSocialList.layoutManager = layoutManager
        if (holder.startSnapHelper == null) {
            holder.startSnapHelper = StartSnapHelper()
            holder.startSnapHelper!!.attachToRecyclerView(holder.rcvSocialList)
            holder.startSnapHelper!!.setContainerView(holder.rcvSocialList)
        }
        holder.rcvSocialList.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    isReportTopData = false
                    traceOnItemShow(recommendItemNew, modulePosition, holder)
                    holder.firstVisiblePosition =
                        (holder.rcvSocialList.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                }
            }
        })
        if (recommendItemNew.xmRequestId != null && (recommendItemNew.xmRequestId + modulePosition) == holder.uniqueId) {
            holder.rcvSocialList.scrollToPosition(holder.firstVisiblePosition)
        } else {
            holder.uniqueId = recommendItemNew.xmRequestId + modulePosition
        }

        // 有声音样式  缓存下
        val trackItem = list?.find { "Track" == it.bizType }
        ShowTagPlayUtil.setRecyclerViewTag(trackItem != null, holder, holder.rcvSocialList)

        PerformanceMonitor.traceEnd("social_bindViewHolder_" + recommendCommonItem.title, 8)
    }

    private fun onBindModuleAlbumView(
        commonSubElement: CommonSubElement,
        holder: SocialListCardViewHolder,
        moduleItem: RecommendCommonItem,
        recommendItemNew: RecommendItemNew?,
        modulePosition: Int,
        textViewContainerWith: Int,
        isAllowFeedback: Boolean
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            HandlerManager.postOnUIThread {
                onBindModuleAlbumViewInner(
                    commonSubElement,
                    holder,
                    moduleItem,
                    recommendItemNew,
                    modulePosition,
                    textViewContainerWith,
                    isAllowFeedback
                )
            }
        } else {
            onBindModuleAlbumViewInner(
                commonSubElement,
                holder,
                moduleItem,
                recommendItemNew,
                modulePosition,
                textViewContainerWith,
                isAllowFeedback
            )
        }
    }

    private fun onBindModuleAlbumViewInner(
        commonSubElement: CommonSubElement,
        holder: SocialListCardViewHolder,
        moduleItem: RecommendCommonItem,
        recommendItemNew: RecommendItemNew?,
        modulePosition: Int,
        textViewContainerWith: Int,
        isAllowFeedback: Boolean
    ) {
        holder.cslContainerView.setTag(R.id.main_id_item_data, commonSubElement)
        holder.cslContainerView.setTag(R.id.main_id_data_index, 0)

        holder.cslContainerView.visibility = View.VISIBLE
        holder.itemTitleTv.text = commonSubElement.title
        RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
        if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
            holder.itemSubtitle1Tv.visibility = View.GONE
        } else {
            holder.itemSubtitle1Tv.visibility = View.VISIBLE
            holder.itemSubtitle1Tv.text = commonSubElement.ext!!.reasonContent
            holder.itemSubtitle1Tv.maxLines = 1
        }

        RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)

        val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
        ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
        val adWidth = if (adTagShow) 24 else 0
        val otherWidth =
            16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 10 + 16 + adWidth //  else 16 + 82 + 10 + 16
        RecommendShowTagsUtilNew.bindTagsView(
            holder.layoutShowTags,
            commonSubElement.ext?.showTags,
            textViewContainerWith - otherWidth.dp,
            commonSubElement.ext?.subTitle1,
            commonSubElement.ext?.subTitle2
        )
        commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
            holder.albumCoverLayoutView.setAlbumTag(
                it
            )
        }
        if (holder.layoutRightTxtArea.layoutParams is ConstraintLayout.LayoutParams) {
            (holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams).startToEnd =
                R.id.main_album_cover_layout
        }
        commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

        holder.albumCoverLayoutView.setPodCastTagAutoSize(
            commonSubElement.ext?.other?.getBRTagUrl()
        )

        holder.cslContainerView.setOnClickListener {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return@setOnClickListener
            }
            val albumId = commonSubElement.refId ?: 0
            val contentId = albumId.toString()
            val tarTypeId = commonSubElement.ext?.tagType ?: 0
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put("card_adTopn", moduleItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            // 新首页-社会化标注-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(60896) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("albumId", albumId.toString())
                .put("positionNew", "1") // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", moduleItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", moduleItem.id.toString()) // 例如：100000000
                .put("rec_src", moduleItem.ubt?.recSrc ?: "")
                .put("rec_track", moduleItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", moduleItem.ubt?.traceId ?: "")
                .put("contentId", contentId.toString())
                .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("trackId", "") // 当是声音时传
                .put("titleId", moduleItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("socialTagId", commonSubElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", moduleItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", moduleItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put(
                    "exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(it).toString()
                ) // 可见区域占屏幕的比例
                .put("isAd", "false")
                .put("area", "item")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = commonSubElement?.title,
                contentPosition = "1"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()
            ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, it)
        }

        if (isAllowFeedback) {
            holder.cslContainerView.setOnLongClickListener {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (modulePosition + 1).toString()
                traceMap["positionNew"] = "1"
                if (!moduleItem.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(moduleItem.ubtV2)
                }
                if (!commonSubElement.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(commonSubElement.ubtV2)
                }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = commonSubElement.bizType ?: ""
                traceMap["contentId"] = commonSubElement.refId?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_ALBUM_ID] = commonSubElement.refId.toString()
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] =
                    commonSubElement.anchor?.uid.toString()
                requestMap["card_contentType"] = moduleItem.contentType ?: ""
                requestMap["card_bizType"] = moduleItem.bizType ?: ""
                requestMap["card_id"] = moduleItem.id.toString()

                val disLikeLeve2Build = DisLikeLeve2Build()
                disLikeLeve2Build.isFromAd = false
                disLikeLeve2Build.anchorName = commonSubElement.anchor?.nickName
                disLikeLeve2Build.requestMap = requestMap
                disLikeLeve2Build.traceMap = traceMap
                disLikeLeve2Build.onFeedBackListener =
                    object : NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onDialogShow(showSuccess: Boolean) {
                        }

                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
                            MainCommonRequest.getSingleSocialListenListItem(
                                0, null, moduleItem,
                                object : IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            return
                                        }

                                        onBindModuleAlbumViewInner(
                                            subElement,
                                            holder,
                                            moduleItem,
                                            recommendItemNew,
                                            modulePosition,
                                            textViewContainerWith,
                                            isAllowFeedback
                                        )

                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            0,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.cslContainerView),
                                            null,
                                            holder.cslContainerView
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                    }
                                })
                        }
                    }

                val typeStr = MoreFuncBuild.TYPE_ALBUM
                val refId = commonSubElement.refId
                val build: MoreFuncBuild = MoreFuncBuild.createCommonLongClickModel(
                    fragment, typeStr, refId, null, true, disLikeLeve2Build
                )
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", commonSubElement.bizType ?: "")
                    put("contentId", commonSubElement.refId?.toString() ?: "")
                    put("modulePosition", (modulePosition + 1).toString())
                    put("positionNew", "1")
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap

                XmMoreFuncManager.checkShowMorePage(build)
                true
            }
        }
    }

    private fun checkAdCunt(subElements: List<CommonSubElement>?): Int {
        if (subElements.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (subElement in subElements) {
            val adInfo = subElement.ext?.subRefInfo?.businessExtraInfo?.adInfo
            if (!adInfo.isNullOrEmpty()) {
                adCount++
            }
        }
        return adCount
    }

    private var isReportTopData = true

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: SocialListCardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        if (recommendCommonItem.cardAdCount == -1) {
            recommendCommonItem.cardAdCount = checkAdCunt(recommendCommonItem.subElements)
        }
        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                        .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    trace.createTrace()
                }

                if (isReportTopData) {
                    if (ViewStatusUtil.viewIsRealShowing(holder.cslContainerView)) {
                        val subElement =
                            holder.cslContainerView.getTag(R.id.main_id_item_data) as? CommonSubElement
                        val index = holder.cslContainerView.getTag(R.id.main_id_data_index) as? Int

                        if (subElement != null && index != null) {
                            traceAlbumInner(
                                subElement,
                                recommendCommonItem,
                                data,
                                index,
                                position,
                                ViewStatusUtil.getViewVisibleAreaRealPercent(holder.cslContainerView),
                                null,
                                holder.cslContainerView
                            )
                        }
                    }
                } else {
                    isReportTopData = true
                }

                val childCount = holder.rcvSocialList.childCount
                for (i in 0 until childCount) {
                    val view = holder.rcvSocialList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement =
                            view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val advertis = view.getTag(R.id.main_id_data_ad_info) as? Advertis
                        traceAlbumInner(
                            subElement, recommendCommonItem, data, index, position,
                            ViewStatusUtil.getViewVisibleAreaRealPercent(view), advertis, view
                        )
                    }
                }
            }
        }
    }

    class SocialListAlbumItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        // 页面
        private val fragment: BaseFragment2,
        // 卡片数据
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        // 专辑列表
        list: List<CommonSubElement>?,
        var modulePosition: Int,
        val recyclerView: RecyclerView,
        var spanCount: Int,
        val enableJumpMore: Boolean,
        private val isAllowFeedback: Boolean,
        private val isActiveText: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 数据列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            list?.apply {
                commonSubElementList.addAll(list)
            }
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            val view: View = ViewPool.getInstance().getView(
                HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                R.layout.main_item_recommend_ip_new_container_item,
                parent,
                false,
                "SocialListenList"
            )
            return SocialListAlbumViewHolder(view)
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is SocialListAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun onBindViewHolderInner(holder: SocialListAlbumViewHolder, position: Int) {
            val commonSubElement = commonSubElementList[position]
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position + 1)
            val remainder: Int = commonSubElementList.size % spanCount
            val start: Int = if (remainder == 0) {
                commonSubElementList.size - spanCount
            } else {
                commonSubElementList.size - remainder
            }
            val isTrackUIType = "Track" == commonSubElement.bizType
            val isAlbumUIType = "Album" == commonSubElement.bizType
                    || "Playlet" == commonSubElement.bizType
            val layoutParams = holder.cslContainerView.layoutParams
            val layoutParamsTrack = holder.cslTrackContainerView.layoutParams as MarginLayoutParams
            val layoutParamsTrackCover = holder.cslTrackCoverView.layoutParams as MarginLayoutParams
            val layoutParamsTxtArea = holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams
            val textViewContainerWithInPx: Int
            if (isActiveText) {
                holder.cslContainerView.apply {
                    setPadding(28.dp, paddingTop, 4.dp, paddingBottom)
                }

                // todo 修改封面大小
                layoutParamsTrackCover.width = RecommendCornerUtils.getCoverSize().toInt()
                layoutParamsTrackCover.height = layoutParamsTrackCover.width
                layoutParamsTrackCover.leftMargin = 28.dp

                layoutParamsTxtArea.goneEndMargin = 4.dp
            } else {
                holder.cslContainerView.apply {
                    setPadding(16.dp, paddingTop, 16.dp, paddingBottom)
                }

                // todo 修改封面大小
                layoutParamsTrackCover.width = RecommendCornerUtils.getCoverSize().toInt()
                layoutParamsTrackCover.height = layoutParamsTrackCover.width
                layoutParamsTrackCover.leftMargin = 16.dp

                layoutParamsTxtArea.goneEndMargin = 16.dp
            }

            val padding = RecommendCornerUtils.getPaddingSize()
            layoutParamsTrackCover.topMargin = padding
            layoutParamsTrackCover.bottomMargin = padding

            holder.cslTrackCoverView.layoutParams = layoutParamsTrackCover
            holder.layoutRightTxtArea.layoutParams = layoutParamsTxtArea

            holder.cslTrackCoverView.setCornerRadius(RecommendCornerUtils.getSocialCorner())

            holder.showTagsParentAlbum?.let {
                val params = it.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                it.layoutParams = params
            }
            holder.showTagsParentTrack?.let {
                val params = it.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                it.layoutParams = params
            }

            if (position >= start) {
                val width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                // 最后一列
                layoutParams.width = width
                layoutParamsTrack.width = width
                textViewContainerWithInPx =
                    getRpAdaptSize(375 - 3) - if (enableJumpMore) 37.dp else 0
            } else {
                val offset = if (isActiveText) {
                    12
                } else {
                    0
                }
                val width = getRpAdaptSize(337 - getOffset() - offset)
                layoutParams.width = width
                layoutParamsTrack.width = width
                textViewContainerWithInPx = getRpAdaptSize(337 - offset - getOffset() - 3)
            }

            if (isAlbumUIType) {
                onBindAlbumView(commonSubElement, holder, position, textViewContainerWithInPx)
            } else if (isTrackUIType) {
                onBindTrackView(
                    commonSubElement,
                    holder,
                    textViewContainerWithInPx,
                    isActiveText
                )
                if (NewShowNotesManager.userNewShowNotes()) {
                    holder.showNotePlayBtnBgWrap.setOnClickListener {
                        onItemClickInner(
                            commonSubElement, false, true,
                            position, it, true
                        )
                    }
                }
            }
            holder.itemView.setOnClickListener {
                onItemClickInner(
                    commonSubElement,
                    isAlbumUIType,
                    isTrackUIType,
                    position,
                    it,
                    false
                )
            }

            if (isAllowFeedback) {
                holder.itemView.setOnLongClickListener {
                    val requestMap = mutableMapOf<String, String>()
                    val traceMap = mutableMapOf<String, String>()
                    traceMap["currPage"] = "newHomePage"
                    traceMap["cardPosition"] = (modulePosition + 1).toString()
                    traceMap["positionNew"] = (position + 2).toString()
                    if (!moduleItem.ubtV2.isNullOrEmpty()) {
                        traceMap.putAll(moduleItem.ubtV2)
                    }
                    if (!commonSubElement.ubtV2.isNullOrEmpty()) {
                        traceMap.putAll(commonSubElement.ubtV2)
                    }
                    traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                    traceMap["contentType"] = commonSubElement.bizType ?: ""
                    traceMap["contentId"] = commonSubElement.refId?.toString() ?: ""
                    if (isAlbumUIType) {
                        requestMap[HttpParamsConstants.PARAM_ALBUM_ID] =
                            commonSubElement.refId.toString()
                    } else {
                        requestMap[HttpParamsConstants.PARAM_TRACK_ID] =
                            commonSubElement.refId.toString()
                    }
                    requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                    requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] =
                        commonSubElement.anchor?.uid.toString()
                    requestMap["card_contentType"] = moduleItem.contentType ?: ""
                    requestMap["card_bizType"] = moduleItem.bizType ?: ""
                    requestMap["card_id"] = moduleItem.id.toString()

                    val disLikeLeve2Build = DisLikeLeve2Build()
                    disLikeLeve2Build.isFromAd = false
                    disLikeLeve2Build.anchorName = commonSubElement.anchor?.nickName
                    disLikeLeve2Build.requestMap = requestMap
                    disLikeLeve2Build.traceMap = traceMap
                    disLikeLeve2Build.onFeedBackListener = object :
                        NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onDialogShow(showSuccess: Boolean) {
                        }

                        override fun onFeedBack(model: List<XmFeedInnerModel>) {
                            val oldId = if (isTrackUIType) commonSubElement.refId else 0L
                            val oldItingUrl =
                                if (isTrackUIType) commonSubElement.landingPage else null
                            MainCommonRequest.getSingleSocialListenListItem(
                                position + 1, oldItingUrl, moduleItem,
                                object : IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            return
                                        }
                                        commonSubElementList[position] = subElement
                                        if (isTrackUIType) {
                                            // 替换其他item的所有当前track 这样子点击某个item播放数据都会是新的
                                            replaceRefreshIting(
                                                commonSubElementList,
                                                oldId,
                                                subElement.refId
                                            )
                                            replaceRefreshIting(
                                                moduleItem.subElements,
                                                oldId,
                                                subElement.refId
                                            )
                                            notifyDataSetChanged()
                                        } else {
                                            val firstPos =
                                                (recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                                            notifyItemChanged(position)  // 使用这个  当前在最后一列时  系统定位不准 会滚动到前一列
                                            recyclerView.scrollToPosition(firstPos)
                                        }
                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            position + 1,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView),
                                            commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject,
                                            holder.itemView
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                    }
                                })
                        }
                    }

                    var typeStr: Int? = null
                    val refId = commonSubElement.refId
                    if (isAlbumUIType) {
                        typeStr = MoreFuncBuild.TYPE_ALBUM
                    } else if (isTrackUIType) {
                        typeStr = MoreFuncBuild.TYPE_TRACK
                    }
                    val build: MoreFuncBuild = MoreFuncBuild.createCommonLongClickModel(
                        fragment, typeStr, refId, null, true, disLikeLeve2Build
                    )
                    val trackMap = mutableMapOf<String, String?>().apply {
                        put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                        put("contentType", commonSubElement.bizType ?: "")
                        put("contentId", commonSubElement.refId?.toString() ?: "")
                        put("modulePosition", (modulePosition + 1).toString())
                        put("positionNew", (position + 2).toString())
                        moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                        commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                    }
                    build.trackMap = trackMap

                    XmMoreFuncManager.checkShowMorePage(build)
                    true
                }
            }

        }

        private fun onBindTrackView(
            commonSubElement: CommonSubElement, holder: SocialListAlbumViewHolder,
            textViewContainerWith: Int, isActiveText: Boolean
        ) {
            holder.cslTrackContainerView.visibility = View.VISIBLE
            ViewStatusUtil.setVisible(View.GONE, holder.cslContainerView)
            val track = Track()
            track.dataId = commonSubElement.refId ?: 0
            val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
            if (isPlaying) {
                holder.ivPlayButton.setImageResource(R.drawable.host_pause_btn_n_fill_n_28)
            } else {
                holder.ivPlayButton.setImageResource(R.drawable.host_play_btn_inside_fill_n_28)
            }
            val adWidth = 0
            var otherWidth = if (NewShowNotesManager.userNewShowNotes()) {
                16 + 12 + 8 + 54 // 16封面左侧间距 + 12 封面右侧 + 8 播放按钮左侧 + 54 播放按钮
            } else {
                16 + 12 + 16   // 16封面左侧间距 + 12 封面右侧 + item最右侧间距
            }

            // 活动态 封面大小 60  非活动态 70  逻辑在上一个方法中
            otherWidth += if (isActiveText) {
                RecommendCornerUtils.getCoverSize().toInt()
            } else {
                RecommendCornerUtils.getSocialCoverSize().toInt()
            }

            val containerWidthInPx = textViewContainerWith - otherWidth.dp - adWidth.dp
            if (commonSubElement.ext?.showTags.isNullOrEmpty()) {
                val newList = mutableListOf<ShowTag>()
                if (!TextUtils.isEmpty(commonSubElement.ext?.albumTitle)) {
                    val tag1 = ShowTag()
                    tag1.tag = commonSubElement.ext?.albumTitle
                    tag1.type = RecommendShowTagsUtilNew.TYPE_ALBUM_TITLE
                    newList.add(tag1)
                }
                if (commonSubElement.ext?.trackPlayNum != null && commonSubElement.ext.trackPlayNum > 0) {
                    val tag2 = ShowTag()
                    tag2.tag = getPlayCountString(commonSubElement.ext.trackPlayNum)
                    tag2.value = commonSubElement.ext.trackPlayNum.toString()
                    tag2.type = RecommendShowTagsUtilNew.TYPE_COUNT_PLAY
                    newList.add(tag2)
                }
                ShowTagManager.bindTagsView(
                    holder.trackShowTagLayout, newList, containerWidthInPx,
                    commonSubElement.refId,
                )
            } else {
                ShowTagManager.bindTagsView(
                    holder.trackShowTagLayout,
                    commonSubElement.ext?.showTags,
                    containerWidthInPx,
                    commonSubElement.refId,
                )
            }

            ShowTagPlayUtil.setHolderTrackItem(
                holder,
                commonSubElement.refId,
                holder.trackShowTagLayout
            )

            RecommendCornerUtils.updateTitleColor(holder.tvTrackTitle)
            holder.tvTrackTitle.text = commonSubElement.title
            if (RecommendShowTagsUtilNew.canShowOneLine(
                    holder.tvTrackTitle,
                    commonSubElement.title,
                    textViewContainerWith - otherWidth.dp
                )
            ) {
                holder.tvTrackSubTitle.text = commonSubElement.summary ?: ""
                ViewStatusUtil.setVisible(
                    if (!TextUtils.isEmpty(commonSubElement.summary)) View.VISIBLE else View.GONE,
                    holder.tvTrackSubTitle
                )
                RecommendCornerUtils.updateSubTitleMargin(holder.tvTrackSubTitle)
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.tvTrackSubTitle)
            }
            var hasValidServerColor = false
            if (commonSubElement.cachedCoverColor != null && commonSubElement.cachedCoverColor != ColorUtil.INVALID_COLOR) {
                hasValidServerColor = true
                setPlayBgColor(commonSubElement.cachedCoverColor!!, holder)
            }
            ImageManager.from(BaseApplication.getMyApplicationContext())
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    holder.ivTrackCover,
                    commonSubElement.cover,
                    com.ximalaya.ting.android.host.R.drawable.host_default_album,
                    70,
                    70
                ) { lastUrl, bitmap ->
                    if (bitmap != null && !hasValidServerColor) {
                        DomainColorUtil.getDomainColorForRecommend(
                            bitmap,
                            Color.BLACK
                        ) { color: Int ->
                            setPlayBgColor(color, holder)
                            commonSubElement.cachedCoverColor = color
                        }
                    }
                }

            if (NewShowNotesManager.userNewShowNotes()) {
                holder.showNotePlayBtnBg.run {
                    val params = this.layoutParams as MarginLayoutParams
                    params.width = RecommendCornerUtils.getShowNoteSize()
                    params.height = params.width
                    this.layoutParams = params
                }

                holder.showNotePlayBtn.run {
                    val params = this.layoutParams
                    params.width = RecommendCornerUtils.getShowNotePlaySize()
                    params.height = params.width
                    this.layoutParams = params
                }

                RecommendCornerUtils.updateShowNoteRoundBg(holder.showNotePlayBtnBg)
            }

            if (NewShowNotesManager.userNewShowNotes()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.GONE, holder.vPlayButtonBg, holder.ivPlayButton)
                if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying &&
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId == commonSubElement.refId
                ) {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, true)
                } else {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, false)
                }
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.vPlayButtonBg, holder.ivPlayButton)
            }
        }

        private fun getPlayCountString(playCount: Long?): String {
            if (playCount == null || playCount <= 0) {
                return "99次播放"
            }
            return if (playCount < 10000) {
                playCount.toString() + "次播放"
            } else {
                val count = playCount / 10000
                "播放量超" + count + "万"
            }
        }

        private fun onBindAlbumView(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder,
            position: Int,
            textViewContainerWith: Int
        ) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindAlbumViewInner(commonSubElement, holder, position, textViewContainerWith)
                }
            } else {
                onBindAlbumViewInner(commonSubElement, holder, position, textViewContainerWith)
            }
        }

        private fun onBindAlbumViewInner(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder,
            position: Int,
            textViewContainerWith: Int
        ) {
            holder.cslTrackContainerView.visibility = View.GONE
            holder.cslContainerView.visibility = View.VISIBLE
            holder.itemTitleTv.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
            if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.ext!!.reasonContent
                holder.itemSubtitle1Tv.maxLines = 1
                RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitle1Tv)
            }

            holder.cslContainerView.run {
                val padding = RecommendCornerUtils.getPaddingSize()
                setPadding(paddingLeft, padding, paddingRight, padding)
            }
            holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getCoverSize())
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)

            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
            val adWidth = if (adTagShow) 24 else 0
            val otherWidth =
                16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + 16 + adWidth //  else 16 + 70 + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(
                holder.layoutShowTags,
                commonSubElement.ext?.showTags,
                textViewContainerWith - otherWidth.dp,
                commonSubElement.ext?.subTitle1,
                commonSubElement.ext?.subTitle2
            )
            commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            if (holder.layoutRightTxtArea.layoutParams is ConstraintLayout.LayoutParams) {
                (holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams).startToEnd =
                    R.id.main_album_cover_layout
            }
            commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }
        }

        private fun onItemClickInner(
            commonSubElement: CommonSubElement,
            isAlbumUIType: Boolean,
            isTrackUIType: Boolean,
            position: Int,
            view: View?,
            isClickPlayBtn: Boolean
        ) {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return
            }
            var albumId = 0L
            var trackId = 0L
            var contentId = ""
            if (isAlbumUIType) {
                albumId = commonSubElement.refId ?: 0
                contentId = albumId.toString()
            } else if (isTrackUIType) {
                trackId = commonSubElement.refId ?: 0
                contentId = trackId.toString()
            }
            val tarTypeId = commonSubElement.ext?.tagType ?: 0
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put("card_adTopn", moduleItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            // 新首页-社会化标注-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(60896) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("albumId", albumId.toString())
                .put("positionNew", (position + 2).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", moduleItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", moduleItem.id.toString()) // 例如：100000000
                .put("rec_src", moduleItem.ubt?.recSrc ?: "")
                .put("rec_track", moduleItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", moduleItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("trackId", trackId.toString()) // 当是声音时传
                .put("titleId", moduleItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("socialTagId", commonSubElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", moduleItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", moduleItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                ) // 可见区域占屏幕的比例
                .put("isAd", "false")
                .put("area", if (isClickPlayBtn) "play" else "item")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = commonSubElement?.title,
                contentPosition = (position + 2).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()
            if (isTrackUIType && !isClickPlayBtn && NewShowNotesManager.userNewShowNotes()) {
                // 打开shownotes二级页
                NewShowNotesManager.startShowNotesDetailFragment(
                    NewShowNotesManager.SOURCE_FROM_HOME,
                    NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                    0,
                    trackId,
                    null
                )
            } else {
                if (isClickPlayBtn) {
                    val track = Track()
                    track.dataId = trackId
                    val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
                    if (isPlaying) {
                        PlayTools.pause(BaseApplication.getMyApplicationContext(), PauseReason.Business.RecommendIpNewContainerAdapter)
                    } else {
                        ToolUtil.clickUrlAction(
                            fragment,
                            NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                            view
                        )
                    }
                } else {
                    if (isTrackUIType) {
                        ToolUtil.clickUrlAction(
                            fragment,
                            NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                            view
                        )
                    } else {
                        ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, view)
                    }
                }
            }
        }

        private fun replaceRefreshIting(
            list: List<CommonSubElement>?,
            oldId: Long?,
            newId: Long?
        ) {
            if (list.isNullOrEmpty() || oldId == 0L || newId == 0L) {
                return
            }
            list.forEach {
                it.landingPage = it.landingPage?.replace(oldId.toString(), newId.toString())
            }
        }

        private fun setPlayBgColor(color: Int, holder: SocialListAlbumViewHolder) {
            val gradientDrawable = GradientDrawable()
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.cornerRadius = 14.dp.toFloat()
            gradientDrawable.setColor(color)
            holder.vPlayButtonBg.background = gradientDrawable
        }

        override fun getItemCount(): Int {
            if (enableJumpMore) {
                return commonSubElementList.size + 1
            }
            return commonSubElementList.size
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class SocialListAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutRightTxtArea: View = view.findViewById(R.id.main_layout_right_txt_area)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var showTagsParentAlbum: ViewGroup? = view.findViewById(R.id.main_layout_show_tag_parent_album)
            var showTagsParentTrack: ViewGroup? = view.findViewById(R.id.main_layout_show_tag_parent_track)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)

            var cslTrackContainerView: View = view.findViewById(R.id.main_csl_item_root_view_track)
            var cslTrackCoverView: CornerRelativeLayout = view.findViewById(R.id.main_crl_item_cover)
            var tvTrackTitle: TextView = view.findViewById(R.id.main_tv_track_title)
            var ivTrackCover: ImageView = view.findViewById(R.id.main_iv_item_cover_track)
            var tvTrackSubTitle: TextView = view.findViewById(R.id.main_tv_track_sub_title)
            var trackShowTagLayout: LinearLayout =
                view.findViewById(R.id.main_tv_track_show_tag_layout)
            var vPlayButtonBg: View = view.findViewById(R.id.main_v_bg_play)
            var ivPlayButton: ImageView = view.findViewById(R.id.main_iv_play_btn)
            var showNotePlayBtnBgWrap: View = view.findViewById(R.id.main_show_notes_play_layout_wrap)
            var showNotePlayBtnBg: View = view.findViewById(R.id.main_show_notes_play_layout)
            var showNotePlayBtn: ImageView = view.findViewById(R.id.main_iv_show_notes_play_btn)

            init {
            }

            fun resetSize() {
            }
        }
    }

    companion object {
        // 基准宽度
        private const val DESIGN_BASE_WITH = 375f

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        // 折叠屏是否展开
        private fun isFoldScreenWithExpand(): Boolean {
            val context = BaseApplication.getTopActivity()
            return BaseUtil.isCollapsibleScreenOnLandscapeMode(context) || BaseUtil.isCollapsibleScreenOnPortraitExpandMode(
                context
            )
        }

        fun traceAlbumInner(
            subElement: CommonSubElement,
            recommendCommonItem: RecommendCommonItem,
            data: RecommendItemNew?,
            position: Int,
            modulePosition: Int,
            exploreArea: Int,
            advertis: Advertis?,
            itemView: View
        ) {
            var albumId = 0L
            var trackId = 0L
            var contentId = ""
            when (subElement.bizType) {
                "Album", "Playlet" -> {
                    albumId = subElement.refId ?: 0
                    contentId = albumId.toString()
                }

                "Track" -> {
                    trackId = subElement.refId ?: 0
                    contentId = trackId.toString()
                }

                "Live" -> {
                    contentId = subElement.refId?.toString() ?: "0"
                }
            }
            val tarTypeId = subElement.ext?.tagType ?: 0
            // 新首页-社会化标注-专辑卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(60897)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("albumId", albumId.toString())
                .put("exploreArea", exploreArea.toString()) // 可见区域占屏幕的比例
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", recommendCommonItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.id.toString()) // 例如：100000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", subElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", data?.xmRequestId)
                .put("trackId", trackId.toString()) // 当是声音时传
                .put("titleId", recommendCommonItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("socialTagId", subElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put("isAd", if (advertis != null) "true" else "false")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = subElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data?.isLocalCache == true) {
                trace.isLocalCache
            }
            trace.createTrace()

            // 广告曝光上报
            if (advertis != null && !advertis.isShowedToRecorded && data != null && !data.isLocalCache) {
                AdManager.adRecord(
                    BaseApplication.getMyApplicationContext(), advertis,
                    AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SITE_SHOW,
                        AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST
                    )
                        .positionNew(position + 2)
                        .modulePosition(modulePosition + 1)
                        .build()
                )
                advertis.isShowedToRecorded = true
            }

            HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, itemView, position)
        }
    }
}