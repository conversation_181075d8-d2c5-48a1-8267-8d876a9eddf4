package com.ximalaya.ting.android.main.util

import android.view.View
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.RecommendTrackItem
import com.ximalaya.ting.android.main.view.RecommendPullToRefreshStaggeredRecyclerView
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.read.utils.checkActivity

object FeedTrackPlayUtil {

    /**
     * 修复信息流列表中  部分视听的声音播完了  但播放按钮状态不变问题
     */
    fun checkPlayStatus(
        lastModel: PlayableModel?, curModel: PlayableModel?,
        refreshView: RecommendPullToRefreshStaggeredRecyclerView?,
        mAdapter: RecommendFragmentStaggeredAdapter?
    ) {
        try {
            realCheckPlayStatus(lastModel, curModel, refreshView, mAdapter)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun realCheckPlayStatus(
        lastModel: PlayableModel?, curModel: PlayableModel?,
        refreshView: RecommendPullToRefreshStaggeredRecyclerView?,
        mAdapter: RecommendFragmentStaggeredAdapter?
    ) {
        if (!refreshView?.context.checkActivity()) {
            return
        }

        val layoutManager = refreshView?.refreshableView?.layoutManager ?: return

        val first = refreshView.findFirstVisiblePosition()
        val last = refreshView.findLastVisiblePosition()

        if (first < 0 || last < 0) {
            return
        }

        for (modulePosition in first..last) {
            val itemView = layoutManager.findViewByPosition(modulePosition) ?: continue
            val track =
                itemView.getTag(R.id.host_id_recommend_feed_track_cache) as? TrackPlayModel
                    ?: continue
            if (track.trackId == curModel?.dataId || track.trackId == lastModel?.dataId) {
                mAdapter?.notifyItemChanged(modulePosition, "playStatusChange")
            }
        }
    }

    @JvmStatic
    fun setTrackTag(itemView: View, track: RecommendTrackItem?) {
        if (track == null) {
            return
        }
        val model = TrackPlayModel(track.dataId)
        setTrackTag(itemView, model)
    }

    @JvmStatic
    fun setTrackTag(itemView: View, trackId: Long?) {
        if (trackId == null) {
            return
        }
        val model = TrackPlayModel(trackId)
        setTrackTag(itemView, model)
    }

    @JvmStatic
    fun setTrackTag(itemView: View, model: TrackPlayModel) {
        itemView.setTag(R.id.host_id_recommend_feed_track_cache, model)
    }

}

data class TrackPlayModel(
    val trackId: Long?
)