package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad;

import static com.ximalaya.ting.android.host.manager.ad.TrackSoundAdHelper.APP_RESTART;
import static com.ximalaya.ting.android.host.manager.ad.TrackSoundAdHelper.APP_STOP;
import static com.ximalaya.ting.android.host.manager.ad.TrackSoundAdHelper.PLAY_PAGE_RESTART;
import static com.ximalaya.ting.android.host.manager.ad.TrackSoundAdHelper.PLAY_PAGE_STOP;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_STATIC_IMAGE_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_STATIC_VERTICAL_IMAGE_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_STATIC_VERTICAL_STYLE2_IMAGE_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_VERTICAL_STYLE2_VIDEO_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_VERTICAL_VIDEO_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_VIDEO_AD;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import com.ximalaya.ting.android.ad.manager.PreloadSDkAdManager;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.ActivityManagerDetacher;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdIvClickManager;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.IAdEngineProviderExtend;
import com.ximalaya.ting.android.host.manager.ad.IPlayAdEngine;
import com.ximalaya.ting.android.host.manager.ad.TrackSoundAdHelper;
import com.ximalaya.ting.android.host.manager.ad.model.AdTypeInfo;
import com.ximalaya.ting.android.host.manager.ad.util.ADABTestUtil;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.fragment.BasePlayPageTabFragment;
import com.ximalaya.ting.android.main.playpage.internalservice.IAdDanMuDataService;
import com.ximalaya.ting.android.main.playpage.internalservice.IDocOnCoverComponentService;
import com.ximalaya.ting.android.main.playpage.internalservice.ILrcAdComponentService;
import com.ximalaya.ting.android.main.playpage.internalservice.ISeekBarComponentService;
import com.ximalaya.ting.android.main.playpage.internalservice.IXAdCoverViewService;
import com.ximalaya.ting.android.main.playpage.internalservice.IXDocSwitchStateChangeService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDemonstrationInfoCenter;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageVipDialogFragmentManager;
import com.ximalaya.ting.android.main.playpage.playy.ScreenChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.XPlayPageStatus;
import com.ximalaya.ting.android.main.playpage.playy.biz.YellowBarSizeChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponentWithPlayStatusListener;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YPlayHeight;
import com.ximalaya.ting.android.main.playpage.playy.component.immersive.XImmersive;
import com.ximalaya.ting.android.main.playpage.playy.component.immersive.XImmersiveType;
import com.ximalaya.ting.android.main.playpage.playy.manager.XPlaySoundAdFloatViewManager;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListenerExpand;
import com.ximalaya.ting.android.opensdk.player.receive.ScreenStatusReceiver;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * Created by le.xin on 2020/6/2.
 * 播放封面区广告
 * <AUTHOR>
 * @email <EMAIL>
 */
public class XPlayAdCoverComponentX extends XBaseCoverComponentWithPlayStatusListener
        implements IXmAdsStatusListenerExpand, ScreenChangeListener, PlayPageDemonstrationInfoCenter.IDemonstrationInfoCallback,
        XImmersive, YellowBarSizeChangeListener {

    private ViewGroup mFragmentRootLay;
    @Nullable
    private RelativeLayout adRootLay;

    @Nullable
    private View mCheckCoverVisView;

    private IPlayAdEngine mPlayAdEngine;

    private Map<Integer, RelativeLayout> rootViewLayoutMap = new HashMap<>();

    private boolean canShowThisComponent;
    private boolean showDanMuComponent;
    private boolean showDanMuFlowerComponent;
    private boolean isHideAnmationIng;
    private boolean isYellowBarShow = false;
    private View curAdComponentView;
    private AdTypeInfo adTypeInfo;

    private boolean hasInvokeHide;

    private int mAdWidth;
    private int screenHeight;

    private AdIvClickManager mAdIvClickManager;
    private final Set<IXAdCoverViewService.IXAdViewChangeService> mAdViewChangeServices
            = new CopyOnWriteArraySet<>();

    private boolean mLastVisible = false;
    private ActivityManagerDetacher.AppStatusListener  mAppStatusListener =  new ActivityManagerDetacher.AppStatusListener() {
        @Override
        public void onAppGoToForeground(Activity startedActivity) {
            if(mFragment !=null && mFragment.canUpdateUi()){
                MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_HAS_APP_GO_TO_FOREGROUND,true);
            }
            TrackSoundAdHelper.report(APP_RESTART);
            PreloadSDkAdManager.getInstance().requestPreSoundPatch(false);
        }

        @Override
        public void onAppGoToBackground(Activity stoppedActivity) {
            MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_HAS_APP_GO_TO_FOREGROUND,false);
            TrackSoundAdHelper.report(APP_STOP);
        }
    };

    private ScreenStatusReceiver.IScreenStatusListener mIScreenStatusListener = screenStatus -> {
        boolean IsScreenOn = screenStatus == ScreenStatusReceiver.IScreenStatusListener.SCREEN_ON;
        TrackSoundAdHelper.report(IsScreenOn ? TrackSoundAdHelper.SCREEN_ON : TrackSoundAdHelper.SCREEN_OFF);
    };

    @Override
    public void onCreate(BaseFragment2 fragment) {
        super.onCreate(fragment);

        mFragmentRootLay = mFragment.findViewById(R.id.main_play_container);
        if(!(mFragmentRootLay instanceof ViewGroup)) {
            throw new RuntimeException("播放页根布局发生变化了");
        }
        adRootLay = mFragment.findViewById(R.id.main_audio_play_root_lay);
        mContentView = adRootLay;
        mCheckCoverVisView = mFragment.findViewById(R.id.main_vg_track_title);
        mPlayAdEngine = getNewPlayAdEngine();
        mAdIvClickManager = new AdIvClickManager();
        rootViewLayoutMap.put(R.id.main_audio_play_root_lay, adRootLay);
        mAdWidth = BaseUtil.getScreenWidth(getContext());
        screenHeight = BaseUtil.getScreenHeight(getContext());
        setContentViewChangeListener(adRootLay);
        saveSoundPatchTicket();
        PlayPageInternalServiceManager.getInstance().registerService(IXDocSwitchStateChangeService.class, new IXDocSwitchStateChangeService(){
            @Override
            public void onDocSwitchChange(boolean open) {
                mPlayAdEngine.removeAd();
            }
        });
        if (getCoverComponentsManager() != null && getCoverComponentsManager().getPlayContainer() != null) {
            getCoverComponentsManager().getPlayContainer().addScreenChangeListener(this);
        }
        if (getCoverComponentsManager() != null) {
            getCoverComponentsManager().registerYellowBarSizeChangeListener(this);
        }
        PlayPageInternalServiceManager.getInstance().registerService(IXAdCoverViewService.class, new IXAdCoverViewService() {
            @Override
            public void registerAdCoverStateChange(IXAdViewChangeService adViewChangeService) {
                mAdViewChangeServices.add(adViewChangeService);
            }

            @Override
            public void unregisterAdCoverStateChange(IXAdViewChangeService adViewChangeService) {
                mAdViewChangeServices.remove(adViewChangeService);
            }

            @Override
            public int getAdCoverViewRealBottom() {
                if (canShowThisComponent && curAdComponentView != null){
                    int[] location = new int[2];
                    curAdComponentView.getLocationOnScreen(location);
                    return location[1] + curAdComponentView.getHeight();
                }
                return 0;
            }

            @Override
            public View getCurAdComponentView() {
                return curAdComponentView;
            }

            @Override
            public boolean curAdIsGradeS() {
                return isGradeSType();
            }

            @Override
            public boolean adComponentIsShowing() {
                return canShowThisComponent;
            }
        });

        if (BaseApplication.sInstance != null) {
            BaseApplication.sInstance.addAppStatusListener(mAppStatusListener);
        }
        PlayPageDemonstrationInfoCenter.registerCallBack(this);

        if (getCoverComponentsManager() != null) {
            getCoverComponentsManager().addCoverChangeListener(currentCoverComponentEnum -> {
//                if (currentCoverComponentEnum == YCoverComponentsEnum.PPT_PLAY_COMPONENT && hasAdShowing() && hasInvokeHide) {
//                    hasInvokeHide = false;
//                    if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_AD_HIDE_REMOVE_AD_ENABLE, true)) {
//                        mPlayAdEngine.removeAd();
//                        canShowThisComponent = false;
//                        showDanMuComponent = false;
//                        showDanMuFlowerComponent = false;
//                    }
//                }
            });
        }

        ScreenStatusReceiver.addScreenStatusListener(mIScreenStatusListener);
        
        // 初始化直播入口广告管理器
        PlayPageLiveEntryAdManager.getInstance().registerBroadcastReceiver();
    }
    private  int mContentViewHeight;
    private void setContentViewChangeListener(RelativeLayout view) {
        view.addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> {
            mContentViewHeight = bottom - top;
            Log.d("pengz","PlayAdCoverComponent : onLayoutChange newHeight=" + mContentViewHeight);
        });
    }

    private void saveSoundPatchTicket() {
        boolean enable = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_ADD_SOUND_PATCH_TICKET_ENABLE, true);
        if (enable) {
            String ticketStr = XuidManager.INSTANCE.getTicket("b=social&s=show_point_task01&u=" + UserInfoMannage.getUid());
            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SUOND_PATCH_REWARD_TICKET, ticketStr);
        } else {
            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SUOND_PATCH_REWARD_TICKET, "");
        }
    }

    private void onAdFullScreenChange(boolean isFull) {
        for (IXAdCoverViewService.IXAdViewChangeService listener : mAdViewChangeServices) {
            listener.onAdFullScreenChange(isFull);
        }
    }

    private void onAdStickyChanged(boolean sticky) {
        for (IXAdCoverViewService.IXAdViewChangeService listener : mAdViewChangeServices) {
            listener.onStickyChanged(sticky);
        }
    }

    private void adapterViewPosition(boolean isFull){
        if (curAdComponentView == null) return;
        Logger.log("PlayAdCoverComponent : adapterViewPosition");
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) curAdComponentView.getLayoutParams();
        if (isFull) {
            boolean isVerticalType = adTypeInfo != null && adTypeInfo.isVerticalType();
            boolean isSoundType = adTypeInfo != null && adTypeInfo.isSoundType();
            boolean isNewVerticalType = adTypeInfo != null && adTypeInfo.isNewVerticalType();
            if (isVerticalType || isSoundType || !isDocShow()){
                if (isNewVerticalType) {
                    layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                    layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                    layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
                    layoutParams.topMargin = BaseUtil.dp2px(getContext(), 42);
                } else {
                    if (isVerticalType) {
                        layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
                        int topMargin = BaseUtil.dp2px(getContext(), 48);
                        if (isSoundPatchSmallStyle()) {
                            topMargin = BaseUtil.dp2px(getContext(), BaseUtil.isSmallScreenDevice(getContext()) ? 52 : 100);
                            if (getCoverComponentsManager().getYellowBarHeight() > 0 && !BaseUtil.isSmallScreenDevice(getContext())) {
                                topMargin = topMargin - BaseUtil.dp2px(getContext(), 30);
                            }
                        }
                        layoutParams.topMargin = topMargin;
                    } else {
                        layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
                        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
                    }
                }
            } else {
                layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                int topMargin = BaseUtil.dp2px(getContext(), 76);
                if (isSoundPatchSmallStyle()) {
                    topMargin = BaseUtil.dp2px(getContext(), 48);
                }
                layoutParams.topMargin = topMargin;
            }
        } else {
            boolean isVerticalType = adTypeInfo != null && adTypeInfo.isVerticalType();
            if (isVerticalType) {
                layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                int topMargin = BaseUtil.dp2px(getContext(), 35);
                if (isVIPPauseStyle()) {
                    topMargin = BaseUtil.dp2px(getContext(), 52);
                    if (BaseUtil.isSmallScreenDevice(getContext()) || (mAdWidth * 2 > screenHeight)) {
                        topMargin = BaseUtil.dp2px(getContext(), 38);
                    }
                    layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
                }
                layoutParams.topMargin = topMargin;
            } else {
                // 折叠屏展开状态走单独样式，折叠状态走旧样式
                // 过滤掉暂停贴
                if (AdManager.isFoldScreenExpand()){
                    adapterNewHorAdFoldViewLayoutParam(layoutParams);
                } else if (!BaseUtil.isFoldScreen(getContext()) &&
                        isYSoundPatchNewStyle() && !isVIPPauseStyle()) {
                    adapterNewHorAdViewLayoutParam(layoutParams);
                } else {
                    adapterHorAdViewLayoutParam(layoutParams);
                }
            }
        }
        curAdComponentView.setLayoutParams(layoutParams);
    }

    public void adapterHorAdViewLayoutParam(RelativeLayout.LayoutParams layoutParams) {
        boolean isAdapterYPlayHorAdView = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "isAdapterYPlayHorAdView", true);
        if (isAdapterYPlayHorAdView && mContentViewHeight > 0) {
            int adBlankHeight =  mContentViewHeight - BaseUtil.dp2px(getContext(), 50) - YPlayHeight.getTIPS_BAR_HEIGHT() - YPlayHeight.getFUNCTION_BAR_HEIGHT();
            //横版贴片最大高度
            int adMaxHeight = BaseUtil.getScreenWidth(getContext()) * 9 / 16;
            Log.e("pengzhao"," 即将适配横版 贴片片 adBlankHeight  ====== " + adBlankHeight+ "======adMaxHeight ===== "+ adMaxHeight);
            //如果空白区域还小于最大高度，则不进行适配（除非极个别宽小屏手机）
            if (adBlankHeight < adMaxHeight) {
                layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
                layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
                int topMargin = 0;
                layoutParams.topMargin = topMargin;
            } else {
                int marginTop = (adBlankHeight - adMaxHeight) / 2 + BaseUtil.dp2px(getContext(), 35);
                Log.e("pengzhao"," 适配过后的横版 贴片片 marginTop  ====== " + marginTop);
                layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                layoutParams.topMargin = marginTop;
            }
        } else {
            layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
            layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
            int topMargin = 0;
            layoutParams.topMargin = topMargin;
        }
    }

    public void adapterNewHorAdViewLayoutParam(RelativeLayout.LayoutParams layoutParams) {
        boolean isAdapterYPlayHorAdView = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "isAdapterYPlayHorAdView", true);
        if (isAdapterYPlayHorAdView && mContentViewHeight > 0) {
            int adBlankHeight =  mContentViewHeight - BaseUtil.dp2px(getContext(), 50) - YPlayHeight.getTIPS_BAR_HEIGHT() - YPlayHeight.getFUNCTION_BAR_HEIGHT();
            //横版贴片最大高度
            int adMaxHeight = BaseUtil.getScreenWidth(getContext()) * 9 / 16 + BaseUtil.dp2px(getContext(), BaseUtil.isSmallScreenDevice(getContext()) ? 60 : 115);
            Log.e("pengzhao1"," 即将适配横版 贴片片 adBlankHeight  ====== " + adBlankHeight+ "======adMaxHeight ===== "+ adMaxHeight);
            //如果空白区域还小于最大高度，则不进行适配（除非极个别宽小屏手机）
            if (adBlankHeight < adMaxHeight) {
                layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                int topMargin = BaseUtil.dp2px(getContext(), 36);
                layoutParams.topMargin = topMargin;
            } else {
                int marginTop = (adBlankHeight - adMaxHeight) / 2 + BaseUtil.dp2px(getContext(), 45);
                Log.e("pengzhao1"," 适配过后的横版 贴片片 marginTop  ====== " + marginTop);
                layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
                layoutParams.topMargin = marginTop;
            }
        } else {
            layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
            layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
            int topMargin = 0;
            layoutParams.topMargin = topMargin;
        }
    }

    public void adapterNewHorAdFoldViewLayoutParam(RelativeLayout.LayoutParams layoutParams) {
        boolean isAdapterYPlayHorAdView = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "isAdapterYPlayHorAdView", true);
        if (isAdapterYPlayHorAdView && mContentViewHeight > 0) {
            int adBlankHeight =  mContentViewHeight - BaseUtil.dp2px(getContext(), 50) - YPlayHeight.getTIPS_BAR_HEIGHT() - YPlayHeight.getFUNCTION_BAR_HEIGHT();
            //横版贴片最大高度
            int adMaxHeight = BaseUtil.getScreenWidth(getContext()) * 9 / 16 + BaseUtil.dp2px(getContext(), BaseUtil.isSmallScreenDevice(getContext()) ? 60 : 115);
            Log.e("pengzhao1"," 即将适配横版 贴片片 adBlankHeight  ====== " + adBlankHeight+ "======adMaxHeight ===== "+ adMaxHeight);
            //如果空白区域还小于最大高度，则不进行适配（除非极个别宽小屏手机）
            int marginTop = (adBlankHeight - adMaxHeight) / 2 + BaseUtil.dp2px(getContext(), 45);
            Log.e("pengzhao1"," 适配过后的横版 贴片片 marginTop  ====== " + marginTop);
            layoutParams.removeRule(RelativeLayout.CENTER_IN_PARENT);
            layoutParams.topMargin = BaseUtil.dp2px(getContext(), 50);
        } else {
            layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
            layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
            int topMargin = 0;
            layoutParams.topMargin = topMargin;
        }
    }

    @Override
    public void onChange(int height) {
        boolean isVerticalType = adTypeInfo != null && adTypeInfo.isVerticalType();
        if (isSoundPatchSmallStyle() && isVerticalType && getCoverComponentsManager().isFullScreen()) {
            adapterViewPosition(true);
        }
    }

    private boolean isDocShow() {
        IDocOnCoverComponentService docService =
                PlayPageInternalServiceManager.getInstance().getService(IDocOnCoverComponentService.class);
        if (docService == null) {
            return true;
        }
        if (getCoverComponentsManager() != null) {
            if (getCoverComponentsManager().getCurrentSoundInfo() == null) {
                return false;
            } else {
                return docService.isSupportShowDoc(getCoverComponentsManager().getCurrentSoundInfo());
            }
        }
        return false;
    }

    private IPlayAdEngine getNewPlayAdEngine() {
        XAudioPlayCoverAdEngine playCoverAdEngine = new XAudioPlayCoverAdEngine(getAdEngineProvider());
        playCoverAdEngine.setCoverComponentsManager(getCoverComponentsManager());
        return playCoverAdEngine;
    }

    private boolean hasAdShowing() {
        return canShowThisComponent || showDanMuFlowerComponent;
    }

    private IAdEngineProviderExtend getAdEngineProvider() {
        return new IAdEngineProviderExtend() {
            @Override
            public Context getContext() {
                return mContext;
            }

            @Override
            public boolean canUpdateUi() {
                return mFragment != null && mFragment.canUpdateUi();
            }

            @Override
            public boolean isRealVis() {
                return mFragment != null && mFragment.isRealVisable();
            }

            @Override
            public BaseFragment2 getBaseFragment2() {
                return mFragment;
            }

            @Override
            public ViewGroup getFragmentRootLayout() {
                return mFragmentRootLay;
            }

            @Override
            public RelativeLayout getAdContentLayout() {
                return adRootLay;
            }

            @Override
            public RelativeLayout getAdContentLayoutById(int resId) {
                RelativeLayout rootLayout = rootViewLayoutMap.get(resId);
                if (rootLayout == null){
                    if (mFragment != null) {
                        View view = mFragment.findViewById(resId);
                        if (view instanceof ViewStub) {
                            try {
                                View inflate = ((ViewStub) view).inflate();
                                if (inflate instanceof RelativeLayout){
                                    rootLayout = (RelativeLayout)inflate;
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        } else if (view instanceof RelativeLayout){
                            rootLayout = (RelativeLayout)view;
                        }
                    }
                    if (rootLayout == null){
                        Logger.logToFile("XPlayAdCoverComponentX getAdContentLayoutById : null 1, resId = " + resId);
                        rootLayout = rootViewLayoutMap.get(R.id.main_audio_play_root_lay);
                    }
                }
                if (rootLayout != null){
                    adRootLay = rootLayout;
                    mContentView = rootLayout;
                    rootViewLayoutMap.put(resId, rootLayout);
                } else {
                    Logger.logToFile("XPlayAdCoverComponentX getAdContentLayoutById : null 2, resId = " + resId);
                }
                return rootLayout;
            }

            @Override
            public int getAlbumCoverHeight() {
                return getCoverComponentsManager().getCoverContainerHeight();
            }

            @Override
            public int getAdContentWidth() {
                return mAdWidth;
            }

            @Override
            public int getAdContentHeight() {
                return mContentViewHeight;
            }

            @Override
            public boolean isLargeDevice() {
                return getCoverComponentsManager().isLargeDevice();
            }

            @Override
            public boolean hasAdShowing() {
                return XPlayAdCoverComponentX.this.hasAdShowing();
            }

            @Override
            public void onAdStateChange(boolean show, boolean hideAnimationIng) {
                canShowThisComponent = show;

                isHideAnmationIng = hideAnimationIng;
                boolean enable = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_FIX_CLOSE_COVER_AD_ENABLE, true);
                if (isHideAnmationIng && enable){
                    HandlerManager.removeCallbacks(onHideAnimationOverRunnable);
                    HandlerManager.postOnUIThreadDelay(onHideAnimationOverRunnable, 500);
                }
                Logger.log("PlayAdCoverComponent : onAdStateChange show " + show);
                if (mPlayAdEngine instanceof XAudioPlayCoverAdEngine && mAdIvClickManager != null) {
                    ((XAudioPlayCoverAdEngine) mPlayAdEngine).setAdClickListener(new XAudioPlayCoverAdEngine.IAdClickListener() {
                        @Override
                        public void onAdClick() {
                            mAdIvClickManager.onAdClick();
                        }
                    });
                    if (show) {
                        mAdIvClickManager.onAdShow(((XAudioPlayCoverAdEngine) mPlayAdEngine).getCurSoundAd(), AppConstants.AD_POSITION_NAME_SOUND_PATCH, mAdWidth, getAlbumCoverHeight());
                    } else {
                        mAdIvClickManager.onAdDestroy();
                    }
                }

                getCoverComponentsManager().updateComponents();
                //如果发现广告的Component没有展示中 则需要关闭掉广告ui
                if (canShowThisComponent && !isVisible() && ADABTestUtil.getAdRemoveSoundPatchEnable()){
                    mPlayAdEngine.removeAd();
                    canShowThisComponent = false;
                    showDanMuComponent = false;
                    showDanMuFlowerComponent = false;
                }
                // s级贴片展示时，如果舞台区是折叠状态，直接隐藏贴片
                if (show && isGradeSType() && getCoverComponentsManager().getPlayContainer() != null &&
                        getCoverComponentsManager().getPlayContainer().getPageStatus().isPinTop()){
                    mPlayAdEngine.removeAd();
                }
            }

            @Override
            public void onHideAnimationOver() {
                HandlerManager.removeCallbacks(onHideAnimationOverRunnable);
                isHideAnmationIng = false;
                getCoverComponentsManager().updateComponents();
            }

            @Override
            public void onShowDanMuAd(IAbstractAd abstractAd) {
                showDanMuComponent = true;

                IAdDanMuDataService danMuDataService =
                        PlayPageInternalServiceManager.getInstance()
                                .getService(IAdDanMuDataService.class);
                if (danMuDataService != null) {
                    danMuDataService.addDanmuData(abstractAd);
                }
            }

            @Override
            public void onHideDanMu() {
                showDanMuComponent = false;

                IAdDanMuDataService danMuDataService =
                        PlayPageInternalServiceManager.getInstance()
                                .getService(IAdDanMuDataService.class);
                if (danMuDataService != null) {
                    danMuDataService.removeDanmuData();
                }
            }

            @Override
            public boolean isDanMuOpen() {
                return getCoverComponentsManager().isDanmakuOpen();
            }

            @Override
            public void onShowDanMuFlowerAd(IAbstractAd advertis) {
                showDanMuFlowerComponent = true;
            }

            @Override
            public void onHideDanMuFlowerAd() {
                showDanMuFlowerComponent = false;
            }

            @Override
            public int getCategoryId() {
                PlayingSoundInfo curSoundInfo = getCurSoundInfo();

                if(curSoundInfo != null
                        && curSoundInfo.trackInfo != null
                        && curSoundInfo.trackInfo.categoryId != 0) {
                    return curSoundInfo.trackInfo.categoryId;
                }

                Track curTrack = PlayTools.getCurTrack(getContext());
                if(curTrack != null) {
                    return curTrack.getCategoryId();
                }

                return 0;
            }

            @Override
            public View getCheckCoverVisView() {
                return mCheckCoverVisView;
            }

            @Override
            public int getControlViewTop() {
                ISeekBarComponentService service =
                        PlayPageInternalServiceManager.getInstance().getService(ISeekBarComponentService.class);
                if (service != null) {
                    return service.getSeekBarTop();
                }

                return 0;
            }

            @Override
            public int getListViewScrollY() {
                if(mFragment instanceof BasePlayPageTabFragment) {
                    return ((BasePlayPageTabFragment) mFragment).getCurrentScrollY();
                }

                return 0;
            }

            @Override
            public void resetAllAd() {
                mPlayAdEngine.removeAd();

                XmPlayerManager.getInstance(getContext()).exitSoundAds();
            }

            @Override
            public int getPageMode() {
                return Advertis.PAGE_MODE_NORMAL;
            }

            @Override
            public void setScrollHeightListenerView(View view) {
            }

            @Override
            public void curAdViewShow(View curAdView, AdTypeInfo adType) {
                curAdComponentView = curAdView;
                adTypeInfo = adType;
                adapterViewPosition(getCoverComponentsManager().isFullScreen());
            }

            @Override
            public boolean isYellowBarShow() {
                return isYellowBarShow;
            }
        };
    }

    private final Runnable onHideAnimationOverRunnable = new Runnable() {
        @Override
        public void run() {
            isHideAnmationIng = false;
            getCoverComponentsManager().updateComponents();
        }
    };

    @Override
    public void onResume() {
        super.onResume();
        PreloadSDkAdManager.getInstance().requestPreSoundPatch(false);
        mPlayAdEngine.doResumeAction();
        TrackSoundAdHelper.report(PLAY_PAGE_RESTART);
        mLastVisible = true;
    }

    public void onPause() {
        mPlayAdEngine.doOnFragmentPauseAction();
        if (mLastVisible) {
            TrackSoundAdHelper.report(PLAY_PAGE_STOP);
        }
        mLastVisible = false;
    }

    public boolean isVerticalType() {
        return adTypeInfo != null && adTypeInfo.isVerticalType();
    }

    public boolean isSoundVideoType() {
        return adTypeInfo != null && adTypeInfo.isSoundType();
    }

    public boolean isGradeSType() {
        return adTypeInfo != null && adTypeInfo.isGradeSType();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        mPlayAdEngine.doOnFragmentDestoryAction();
        if (getCoverComponentsManager() != null && getCoverComponentsManager().getPlayContainer() != null) {
            getCoverComponentsManager().getPlayContainer().removeScreenChangeListener(this);
        }
        if (getCoverComponentsManager() != null) {
            getCoverComponentsManager().unRegisterYellowBarSizeChangeListener(this);
        }
        PlayPageInternalServiceManager.getInstance().unRegisterService(IXAdCoverViewService.class);
        PlayPageInternalServiceManager.getInstance().unRegisterService(IXDocSwitchStateChangeService.class);
        PlayPageInternalServiceManager.getInstance().unRegisterService(IXAdCoverViewService.class);
        PlayPageDemonstrationInfoCenter.removeCallBack(this);
        mAdViewChangeServices.clear();
        rootViewLayoutMap.clear();
        if(BaseApplication.sInstance != null){
            BaseApplication.sInstance.removeAppStatusListener(mAppStatusListener);
        }
        HandlerManager.removeCallbacks(mCheckPlayStateRunnable);
        ScreenStatusReceiver.removeScreenStatusListener(mIScreenStatusListener);
        HandlerManager.removeCallbacks(onHideAnimationOverRunnable);
        
        // 注销直播入口广告管理器
        PlayPageLiveEntryAdManager.getInstance().unregisterBroadcastReceiver();
    }

    public void onBackEvent() {
        if (ConfigureCenter.getInstance().getBool("ad", "backPressCloseVideoAdEnable", false)) {
            Logger.log("onBackEvent backPressCloseVideoAdEnable  ==== true 返回关闭视频贴片");
            if (mPlayAdEngine != null) {
                mPlayAdEngine.doOnFragmentBackPressedAction();
            }
        }
    }

    @Override
    public boolean lifecycleCheckVisible() {
        return false;
    }

    @Override
    public void initUi() {
    }

    @Override
    protected int getViewStubId() {
        return 0;
    }

    @Override
    protected View inflate() {
        return adRootLay;
    }

    @Override
    public boolean needShowThisComponent(PlayingSoundInfo soundInfo) {
        Logger.i("XPlayAdCoverComponentX", "needShowThisComponent ----  canShowThisComponent = " + canShowThisComponent
                + ", showDanMuComponent = " + showDanMuComponent
                + ", showDanMuFlowerComponent = " + showDanMuFlowerComponent
                + ", isHideAnmationIng = " + isHideAnmationIng
        );
        //检查声音是否能弹出
        PlayPageVipDialogFragmentManager.getInstance().requestVipDialog(soundInfo);
        return (canShowThisComponent || showDanMuComponent || showDanMuFlowerComponent || isHideAnmationIng);
    }

    // 返回为false的话表示可以和封面一起显示
    @Override
    public boolean updateCurrComponent() {
        return !(showDanMuComponent || isHideAnmationIng || showDanMuFlowerComponent);
    }

    // 组件隐藏时也可回调,页面隐藏时就不回调了
    @Override
    public boolean needListenPlayStatusEvenHidden() {
        return true;
    }


    /****************************************************
     * 广告状态-- begin
     *****************************************************/
    @Override
    public void onStartGetAdsInfo(int playMethod, boolean duringPlay, boolean isPaused) {
        mPlayAdEngine.onStartGetAdsInfo(playMethod, duringPlay, isPaused);
    }

    @Override
    public void onGetAdsInfo(AdvertisList ads) {
        mPlayAdEngine.onGetAdsInfo(ads);
    }

    @Override
    public void onAdsStartBuffering() {
        mPlayAdEngine.onAdsStartBuffering();
    }

    @Override
    public void onAdsStopBuffering() {
        mPlayAdEngine.onAdsStopBuffering();
    }

    @Override
    public void onStartPlayAds(Advertis ad, int position) {
        mPlayAdEngine.onStartPlayAds(ad, position);
    }

    @Override
    public void onCompletePlayAds() {
        mPlayAdEngine.onCompletePlayAds();
    }

    @Override
    public void onError(int what, int extra) {
        mPlayAdEngine.onError(what, extra);
    }

    // 前插视频广告
    @Override
    public void onGetForwardVideo(List<Advertis> advertis) {
        mPlayAdEngine.onGetForwardVideo(advertis);
    }

    @Override
    public void onDocSwitchChanged(boolean docShow) {
        super.onDocSwitchChanged(docShow);
        XmPlayerManager.getInstance(mFragment.getContext()).
                setPlayerScene(getCoverComponentsManager().isFullScreen(), docShow);
    }

    @Override
    public void onStickyChanged(boolean sticky) {
        onAdStickyChanged(sticky);
    }

    /****************************************************
     * 广告状态-- end
     *****************************************************/

    /****************************************************
     * 播放状态-- begin
     *****************************************************/
    private boolean noNeedRemoveSoundPatch;
    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        super.onSoundSwitch(lastModel, curModel);
        XmPlayerManager.getInstance(mFragment.getContext()).
                setPlayerScene(getCoverComponentsManager().isFullScreen(), isDocShow());
        hasInvokeHide = false;
        isYellowBarShow = false;
        //如果命中贴片展示频控&&当前的贴片又在展示中&&并且开关开启，不消失贴片
        noNeedRemoveSoundPatch = false;
        if (AdRequest.checkAdExposureInterval() && hasAdShowing() && ADABTestUtil.getAdExposureClearedEnable() && !curIsThirdSoundAd()) {
            noNeedRemoveSoundPatch = true;
            //兜底策略:如果是视频贴片命中频控优化 如果3s后声音专辑还没起播，则让贴片消失
            if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_AD_CHECK_PLAY_STATE_ENABLE, true)) {
                checkPlayStatus();
            }
            return;
        }
        canShowThisComponent = false;
        showDanMuComponent = false;
        showDanMuFlowerComponent = false;
        if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_AD_RESET_HIDEANMATION_ENABLE, true)) {
            isHideAnmationIng = false;
        }
        if (PlayPageInternalServiceManager.getInstance().getService(ILrcAdComponentService.class) != null) {
            // 避免前一次贴片展示过程中，切换下一首，会请求两次字幕广告
            PlayPageInternalServiceManager.getInstance().getService(ILrcAdComponentService.class).cancelRequestAd();
        }
        getCoverComponentsManager().updateComponents();

        mPlayAdEngine.doOnSoundSwitchAction(lastModel, curModel);
    }

    private Runnable mCheckPlayStateRunnable;
    private void checkPlayStatus() {
        if (!canUpdateUi()) {
            return;
        }

        if (mCheckPlayStateRunnable == null) {
            mCheckPlayStateRunnable = () -> {
                if (noNeedRemoveSoundPatch && mPlayAdEngine != null) {
                    noNeedRemoveSoundPatch = false;
                    mPlayAdEngine.doCheckPlayState();
                }
            };
        }
        HandlerManager.removeCallbacks(mCheckPlayStateRunnable);
        HandlerManager.postOnUIThreadDelay(mCheckPlayStateRunnable,3000);
    }

    @Override
    public void onSoundInfoLoaded(PlayingSoundInfo soundInfo) {
        super.onSoundInfoLoaded(soundInfo);
    }

    @Override
    public void onPlayPause() {
        super.onPlayPause();

        mPlayAdEngine.doOnPlayPauseAction();
    }

    public void onPlayStart() {
        super.onPlayStart();
        //播放开始时也加上频控的判断，处理播放开始延迟很久而频控时间已结束而导致异常case
        if (noNeedRemoveSoundPatch && AdRequest.checkAdExposureInterval() && ADABTestUtil.getAdExposureClearedEnable()) {
            //声音专辑信息请求完成后，处理播放
            mPlayAdEngine.doAudioPlayFocusAction();
            noNeedRemoveSoundPatch = false;
        }
        mPlayAdEngine.doOnPlayStartAction();
    }

    @Override
    public void hide() {
        super.hide();
        hasInvokeHide = true;
    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
        super.onPlayProgress(currPos, duration);

        mPlayAdEngine.doOnPlayProgress(currPos, duration);
    }

    /****************************************************
     * 播放状态-- end
     *****************************************************/

    @Override
    public void onFullScreenChanged(boolean fullScreen) {
        if (canShowThisComponent) {
            adapterViewPosition(fullScreen);
            onAdFullScreenChange(fullScreen);
        }
    }

    private boolean curIsThirdSoundAd() {
        if (mPlayAdEngine != null && mPlayAdEngine instanceof XAudioPlayCoverAdEngine) {
            Advertis ad = ((XAudioPlayCoverAdEngine) mPlayAdEngine).getCurSoundAd();
            return AdManager.isThirdAd(ad);
        }
        return false;
    }

    private boolean isSoundPatchSmallStyle() {
        if (mPlayAdEngine != null && mPlayAdEngine instanceof XAudioPlayCoverAdEngine) {
            Advertis ad = ((XAudioPlayCoverAdEngine) mPlayAdEngine).getCurSoundAd();
            return ad != null && ad.getSoundPatchClientNewStyle() == 1;
        }
        return false;
    }

    private boolean isYSoundPatchNewStyle() {
        if (mPlayAdEngine != null && mPlayAdEngine instanceof XAudioPlayCoverAdEngine) {
            Advertis ad = ((XAudioPlayCoverAdEngine) mPlayAdEngine).getCurSoundAd();
            return ad != null && ad.isYSoundPatchNewStyle();
        }
        return false;
    }

    private boolean isVIPPauseStyle() {
        if (mPlayAdEngine != null && mPlayAdEngine instanceof XAudioPlayCoverAdEngine) {
            Advertis ad = ((XAudioPlayCoverAdEngine) mPlayAdEngine).getCurSoundAd();
            return ad != null && (ad.getSoundType() == TYPE_VIP_PAUSE_STATIC_IMAGE_AD || ad.getSoundType() == TYPE_VIP_PAUSE_VIDEO_AD
            || ad.getSoundType() == TYPE_VIP_PAUSE_STATIC_VERTICAL_IMAGE_AD || ad.getSoundType() == TYPE_VIP_PAUSE_VERTICAL_VIDEO_AD
            || ad.getSoundType() == TYPE_VIP_PAUSE_STATIC_VERTICAL_STYLE2_IMAGE_AD || ad.getSoundType() == TYPE_VIP_PAUSE_VERTICAL_STYLE2_VIDEO_AD);
        }
        return false;
    }

    private boolean isYSoundPatchVerticalV2() {
        if (mPlayAdEngine != null && mPlayAdEngine instanceof XAudioPlayCoverAdEngine) {
            Advertis ad = ((XAudioPlayCoverAdEngine) mPlayAdEngine).getCurSoundAd();
            return ad != null && ad.getVerticalSoundPatchEnlarge() == 1;
        }
        return false;
    }

    @Override
    public void onScreenStatusChanged(@NotNull XPlayPageStatus xPlayPageStatus) {

    }

    @Override
    public void beforeFullScreenChanged(boolean fullScreen) {
        if (fullScreen) {
            XPlaySoundAdFloatViewManager.getInstance().hideFloatView();
        }
    }

    @Override
    public void afterFullScreenChanged(boolean fullScreen) {
        XmPlayerManager.getInstance(mFragment.getContext()).setPlayerScene(fullScreen, isDocShow());
    }

    @Override
    public void onReceiveInfo(PlayPageDemonstrationInfoCenter.DemonstrationInfo demonstrationInfo) {
        if (demonstrationInfo.getDemonstrationType() == PlayPageDemonstrationInfoCenter.DemonstrationType.YELLOW_BAR) {
            isYellowBarShow = demonstrationInfo.isShown();
        }
    }

    @Override
    public int getBottomColor() {
        View maskView = mContentView;
        if (maskView == null || !maskView.isShown()) {
            return 0;
        }
        try {
            Bitmap bitmap = Bitmap.createBitmap(maskView.getWidth(), 1, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            canvas.translate(0f, 1f - maskView.getHeight());
            maskView.draw(canvas);
            int bottomMaskEdgeColor = bitmap.getPixel(0,0);
            bitmap.recycle();
            return bottomMaskEdgeColor;
        }catch (Throwable t) {
            Logger.log("getBottomColor exception");
        }
        return 0;
    }

    @Nullable
    @Override
    public XImmersiveType getImmersiveContentType() {
        if(isGradeSType()) return XImmersiveType.IMAGE;
        return null;
    }

    public IPlayAdEngine getPlayAdEngine() {
        return mPlayAdEngine;
    }

    @Override
    public boolean needShowNavigationBar(PlayingSoundInfo soundInfo) {
        return isSoundPatchSmallStyle();
    }

    public boolean needShowFunctionBar(@Nullable PlayingSoundInfo soundInfo) {
        return !isYSoundPatchVerticalV2();
    }

    public boolean needShowTips(PlayingSoundInfo soundInfo) {
        return !isVerticalType() || isVIPPauseStyle();
    }
}
