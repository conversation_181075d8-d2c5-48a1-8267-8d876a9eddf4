package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.animation.Animator
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.StateListDrawable
import android.view.HapticFeedbackConstants
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.play.data.PlayPageDynamicState
import com.ximalaya.ting.android.host.manager.AudioInfoTraceUtil
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.appcomment.AppCommentManager
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.RouteServiceUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.manager.comment.IRemoteLikeIconLoadResultListener
import com.ximalaya.ting.android.main.manager.comment.RemoteLikeIcon
import com.ximalaya.ting.android.main.manager.comment.RemoteLikeIconManager
import com.ximalaya.ting.android.main.playpage.playy.component.function.view.YAnimatedCountTextView
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.util.PlayPageInteractiveTraceUtil
import com.ximalaya.ting.android.main.playpage.view.updateProgress
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class CustomLikeFuncIconV2(private val fragment2: BaseFragment2) : IBusinessView(),
    View.OnClickListener {
    private var curSoundInfo: PlayingSoundInfo? = null
    private var curTrackId: Long = 0
        set(value) {
            if (field != value) {
                field = value
                checkBindDynamicLikeIcon(value)
            }
        }

    private var remoteIconInfo: RemoteLikeIcon? = null

    private val iconSize = 26.dp

    private var mLikeCountTv: YAnimatedCountTextView? = null
    private var isLikeBtnClick = false

    private var mLikeCount: Long = 0

    private var lottieLike: XmLottieAnimationView? = null
    private var mIvDynamicLike: ImageView? = null
    private var mLikeFrameLayout: FrameLayout? = null

    override fun onCreate() {
        RemoteLikeIconManager.addLoadResultListener(mRemoteLikeIconLoadResultListener)
    }

    override fun provideIcon(context: Context, soundInfo: PlayingSoundInfo): View {
        if (lottieLike == null) {
            lottieLike = XmLottieAnimationView(context).apply {
                layoutParams = ViewGroup.MarginLayoutParams(iconSize, iconSize)
                imageAlpha = (255 * 0.55).toInt()
                contentDescription = "点赞"
            }
        }

        if (mIvDynamicLike == null) {
            mIvDynamicLike = ImageView(context).apply {
                layoutParams = ViewGroup.MarginLayoutParams(iconSize, iconSize)
                visibility = View.GONE
                imageAlpha = (255 * 0.55).toInt()
            }
        }

        val frameLayout = mLikeFrameLayout ?: FrameLayout(context).apply {
            layoutParams = ViewGroup.MarginLayoutParams(iconSize, iconSize)

            if (mIvDynamicLike?.parent != this) {
                (mIvDynamicLike?.parent as? ViewGroup)?.removeView(mIvDynamicLike)
                addView(mIvDynamicLike)
            }
            if (lottieLike?.parent != this) {
                (lottieLike?.parent as? ViewGroup)?.removeView(lottieLike)
                addView(lottieLike)
            }
            mLikeFrameLayout = this
            setOnClickListener(this@CustomLikeFuncIconV2)

            val remoteIcon = <EMAIL>
            if (remoteIcon != null) {
                setCustomView(remoteIcon)
            }
        }
        return frameLayout
    }

    override fun providerBadgeView(context: Context, soundInfo: PlayingSoundInfo): BadgeView {
        return BadgeView(mLikeCountTv ?: YAnimatedCountTextView(context).apply {
            layoutParams = ViewGroup.MarginLayoutParams(
                ViewGroup.MarginLayoutParams.WRAP_CONTENT,
                ViewGroup.MarginLayoutParams.WRAP_CONTENT
            )
            isSingleLine = true
            textSize = 8f
            alpha = 0.55f
            includeFontPadding = false
            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                setTextColor(PSkinManager.getBtnThemeColor())
            } else {
                setTextColor(Color.WHITE)
            }
            mLikeCountTv = this
            typeface = Typeface.DEFAULT_BOLD
            updateCountText()
        })
    }

    override fun getBottomText(): String {
        return "点赞"
    }

    override fun providerBottomView(context: Context, soundInfo: PlayingSoundInfo): BottomView? {
        return BottomView(
            TextView(context).apply {
                text = "点赞"
                alpha = 0.4f
                textSize = 10f
                isSingleLine = true
            }
        )
    }

    override fun shouldDisplay(soundInfo: PlayingSoundInfo, isAudio: Boolean): Boolean {
        this.curSoundInfo = soundInfo
        curTrackId = curSoundInfo?.trackInfo?.trackId ?: 0L
        return remoteIconInfo != null
    }

    private fun checkBindDynamicLikeIcon(trackId: Long) {
        val remoteIconInfo = RemoteLikeIconManager.getCachedTrackFullScreenIcon(trackId)
        if (remoteIconInfo != null) {
            fragment2.lifecycleScope.launch {
                delay(1000L)
                setCustomView(remoteIconInfo)
                if (<EMAIL> != remoteIconInfo) {
                    <EMAIL> = remoteIconInfo
                    requestRefresh()
                }
            }
        } else {
            if (trackId == curSoundInfo?.trackInfo?.trackId) {
                RemoteLikeIconManager.loadIconForTrackFullScreenType(
                    trackId,
                    curSoundInfo?.albumInfo?.albumId ?: 0L,
                    curSoundInfo?.trackInfo?.categoryId ?: 0
                )
            }
        }
    }

    override fun onDestroy() {
        RemoteLikeIconManager.removeLoadResultListener(mRemoteLikeIconLoadResultListener)
    }

    override fun updateDynamicData(dynamic: PlayPageDynamicState) {
        mLikeCount = dynamic.likeCount
        updateLikeState(dynamic.isLiked)
    }

    private fun updateCountText() {
        mLikeCountTv?.setCount(mLikeCount)
        ViewStatusUtil.setVisible(if (mLikeCount > 0) View.VISIBLE else View.GONE, mLikeCountTv)
    }

    private fun updateLikeState(isLike: Boolean) {
        updateCountText()
        if (isLike && lottieLike?.isAnimating == true) return
        lottieLike?.cancelAnimation()
        lottieLike.updateProgress(if (isLike) 1f else 0f)

        ViewStatusUtil.setVisible(View.VISIBLE, mIvDynamicLike)
        mIvDynamicLike?.isSelected = isLike
    }


    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        doLike()
    }

    private fun doLike() {
        val activity = BaseApplication.getTopActivity() ?: return
        val track: TrackM? = curSoundInfo?.trackInfo2TrackM()
        val isLike: Boolean = track?.isLike == true
        if (UserInfoMannage.hasLogined()) {
            if (!isLike) {
                if (lottieLike?.tag != "" && lottieLike?.composition != null) {
                    ViewStatusUtil.setVisible(View.GONE, mIvDynamicLike)
                    ViewStatusUtil.setVisible(View.VISIBLE, lottieLike)
                    if (lottieLike?.isAnimating == true) {
                        lottieLike?.resumeAnimation()
                    } else {
                        lottieLike?.playAnimation()
                    }
                } else {
                    ViewStatusUtil.setVisible(View.GONE, lottieLike)
                    ViewStatusUtil.setVisible(View.VISIBLE, mIvDynamicLike)
                    mIvDynamicLike?.isSelected = true
                }
                RemoteLikeIconManager.getCachedTrackFullScreenIcon(curTrackId)?.largeLottie?.let {
                    RemoteLikeIconManager.showFullScreenLottie(it)
                }
                if (mLikeCountTv?.visibility == View.VISIBLE) {
                    mLikeCountTv?.animateChange(mLikeCount)
                }
            }
        }
        traceLikeOrCollect(isLike, true)
        isLikeBtnClick = true
        LikeTrackManage.toLikeOrUnLike(track,
            true,
            null,
            activity,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(`object`: Boolean?) {
                    if (`object` == null || !`object`) {
                        return
                    }

                    onSoundLiked(!isLike, track)
                    isLikeBtnClick = false
                }

                override fun onError(code: Int, message: String) {
                    if (lottieLike?.tag != "" && lottieLike?.composition != null) {
                        lottieLike.updateProgress(if (isLike) 1f else 0f)
                    } else {
                        ViewStatusUtil.setVisible(View.GONE, lottieLike)
                        ViewStatusUtil.setVisible(View.VISIBLE, mIvDynamicLike)
                        mIvDynamicLike?.isSelected = isLike
                    }
                    CustomToast.showFailToast(message)
                    isLikeBtnClick = false
                }
            })
    }

    private fun onSoundLiked(isLiked: Boolean, track: Track?) {
        if (track == null) {
            return
        }
        if (isLiked) {
            AppCommentManager.showDialog(600, true)
            lottieLike?.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
        } else {
            lottieLike?.updateProgress(0f)
        }

        RouteServiceUtil.getDownloadService().updateFavorState(track.dataId, isLiked, true)
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
            .updateTrackInPlayList(track)
    }

    private fun setCustomView(remoteIconInfo: RemoteLikeIcon) {
        mIvDynamicLike?.setImageDrawable(StateListDrawable().apply {
            addState(
                intArrayOf(android.R.attr.state_selected),
                BitmapDrawable(fragment2.resources, remoteIconInfo.selectedIcon)
            )
            addState(
                intArrayOf(), BitmapDrawable(fragment2.resources, remoteIconInfo.unselectedIcon)
            )
        })
        if (remoteIconInfo.smallLottie.isNullOrEmpty()) {
            lottieLike?.tag = "" // 标记没有绑定lottie资源
        } else {
            lottieLike?.setAnimationFromUrl(remoteIconInfo.smallLottie)
            lottieLike?.tag = remoteIconInfo.smallLottie
            lottieLike?.removeAllAnimatorListeners()
            lottieLike?.addAnimatorListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    if (lottieLike?.visibility == View.VISIBLE) {
                        mIvDynamicLike?.isSelected = true
                        mIvDynamicLike?.visibility = View.VISIBLE
                        ViewStatusUtil.setVisible(View.GONE, lottieLike)
                    }
                }

                override fun onAnimationCancel(animation: Animator?) {
                    if (lottieLike?.visibility == View.VISIBLE) {
                        mIvDynamicLike?.visibility = View.VISIBLE
                        ViewStatusUtil.setVisible(View.GONE, lottieLike)
                    }
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }
            })
        }
        ViewStatusUtil.setVisible(View.GONE, lottieLike)
        ViewStatusUtil.setVisible(View.VISIBLE, mIvDynamicLike)
    }

    private val mRemoteLikeIconLoadResultListener = object : IRemoteLikeIconLoadResultListener {
        override fun onLoadSuccess(bizType: Int, id: Long, result: RemoteLikeIcon) {
            if (bizType == RemoteLikeIconManager.BIZ_TRACK_FULL_SCREEN && id == curTrackId) {
                setCustomView(result)

                if (<EMAIL> != result) {
                    <EMAIL> = result
                    requestRefresh()
                }
            }
        }
    }

    private fun traceLikeOrCollect(isCancel: Boolean, isLike: Boolean) {
        // 声音-点赞/收藏操作（底部）  点击事件
        XMTraceApi.Trace().click(17506) // 用户点击时上报
            .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
            .put("currTrackId", curSoundInfo?.trackInfo?.trackId?.toString() ?: "")
            .put("currAlbumId", curSoundInfo?.albumInfo?.albumId?.toString() ?: "")
            .put("Item", if (isLike) "点赞" else "收藏") // 点赞 / 收藏，记录点击后的状态
            .put("currPage", "newPlay") // newPlay/互动播放页/互动浮层页等
            .put("replyId", "").put("isCancel", isCancel.toString()) // true/false
            .put("commentId", "").put("uid", curSoundInfo?.userInfo?.uid?.toString())
            .put("style", "竖版") // 横版｜竖版
            .apply {
                PlayPageInteractiveTraceUtil.tracePlayInfo(this)
            }.put("hasDynamicGuide", "false") // ture/false
            .put("content", "") // 有上报引导点赞文案/无不上报
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE
                )
            ).put("fullScreenMode", "half") // full 表示全屏，half 表示半屏
            .put("playViewForm", "1") //  1 表示高唤起 2 表示低唤起
            .apply {
                AudioInfoTraceUtil.interceptTrace(this)
            }
            .createTrace()
    }
}