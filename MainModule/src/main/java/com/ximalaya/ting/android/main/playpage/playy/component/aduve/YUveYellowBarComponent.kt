package com.ximalaya.ting.android.main.playpage.playy.component.aduve

import android.util.Log
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import com.ximalaya.ting.android.ad.uve.impl.IUveBehaviorCallback
import com.ximalaya.ting.android.host.async.AsyncRequest
import com.ximalaya.ting.android.host.commercial.CommercialDialogUnifiedFrequencyManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.dialog.GlobalDialogManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager
import com.ximalaya.ting.android.host.manager.play.soundEffect.TrackPlaySoundEffectManager
import com.ximalaya.ting.android.host.manager.play.soundEffect.sleep.SleepEffectUtils
import com.ximalaya.ting.android.host.manager.vip.UserVipInfoManager
import com.ximalaya.ting.android.host.model.play.CommonGuidanceInfo
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.play.YellowZoneModelV2
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.common.SceneInfoUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.aduve.business.yellowbar.IUveYellowBarComponent
import com.ximalaya.ting.android.main.manager.YPlayPageYellowBarManager
import com.ximalaya.ting.android.main.playpage.dialog.XNoAdRightDialog
import com.ximalaya.ting.android.main.playpage.internalservice.IAdCoverHideService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.playx.XUtils
import com.ximalaya.ting.android.main.playpage.playy.biz.YellowBarPriorityListener
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils.getPlayPageHideBarTypeWrapper
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.soundEffect.SoundEffectPlayerConstant
import com.ximalaya.ting.android.timeutil.TimeService
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*

class YUveYellowBarComponent(
    private val contentContainer: ViewGroup
) : AbstractUveYellowBarComponent(),
    YellowBarPriorityListener, IUveYellowBarComponent<YellowZoneModelV2> {
    companion object {
        private const val TAG = "XUveYellowBarComponent"

        @JvmStatic
        fun markPoint(fragment: BaseFragment2?, albumId: Long, trackId: Long, sceneId: Int) {
            fragment?.lifecycleScope?.launch {
                val url =
                    UrlConstants.getInstanse().serverNetSAddressHost + "business-sale-promotion-guide-mobile-web/exposure/v1";
                val map = mapOf(
                    "albumId" to albumId,
                    "trackId" to trackId,
                    "sceneId" to sceneId
                )

                withContext(Dispatchers.IO) {
                    runCatching {
                        val paraString = GsonUtils.toJson(map)
                        AsyncRequest.postStr<Any>(
                            url, paraString
                        )
                    }
                }
            }
        }
    }

    private var isCanShowBannerWithPriority: Boolean = false // 通过主站回调的 优先级控制

    private var mNoAdRightDialogRecordPair: Pair<Long, XNoAdRightDialog.GlobalDialogRecord?>? = null
    private var mNoAdRightDialogShowTimer: Timer? = null
    private var mTimerTask: TimerTask? = null

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)
        this.vBuyViewContainer = contentContainer
        registerAdCoverStateChangeListener()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterAdCoverStateChangeListener()
        releaseNoAdDialogShowTask()
    }

    var mHasRegisterAdCoverChangeListener = false
    fun registerAdCoverStateChangeListener() {
        if (mHasRegisterAdCoverChangeListener) {
            return
        }
        val adCoverHideService =
            PlayPageInternalServiceManager.getInstance().getService(IAdCoverHideService::class.java)

        if (adCoverHideService != null) {
            adCoverHideService.registerAdCoverStateChange(mAdCoverStateChangeListener)
            mHasRegisterAdCoverChangeListener = true
        }
    }

    private fun unregisterAdCoverStateChangeListener() {
        if (mAdCoverStateChangeListener == null) {
            return
        }
        val adCoverHideService = PlayPageInternalServiceManager.getInstance().getService(
            IAdCoverHideService::class.java
        )
        adCoverHideService?.unregisterAdCoverStateChange(mAdCoverStateChangeListener)
        mHasRegisterAdCoverChangeListener = false
    }

    private val mAdCoverStateChangeListener =
        object : IAdCoverHideService.IAdCoverStateChangeListenerExtern {
            override fun onAdCoverHide() {
            }

            override fun noAdCover() {
            }

            override fun onUserClose() {
                if (UserVipInfoManager.currentUserIsPlatinumVip()) {
                    //白金会员 不执行CommercialDialogUnifiedFrequencyManager.SOURCE_PLAYPAGE_AFTER_CLOSE_AD
                    XPlayCommercialRelatedUtils.checkToShowADPrivilegeFloatDialog(
                        curSoundInfo,
                        fragment,
                        true
                    )
                } else {
                    val abTest = com.ximalaya.ting.android.xmabtest.ABTest.getString(
                        "playpage_window_ad_erase",
                        "0"
                    )
                    var debugMode = false
                    if (ConstantsOpenSdk.isDebug) {
                        debugMode =
                            "1" == ToolUtil.getDebugSystemProperty("debug.mark.platinumAdFree", "0")
                    }
                    if (abTest == "1" || debugMode) {
                        CommercialDialogUnifiedFrequencyManager.requestDialogMaterial(
                            CommercialDialogUnifiedFrequencyManager.SOURCE_PLAYPAGE_AFTER_CLOSE_AD,
                            mapOf<String, String>(
                                "playPageStyle" to "heightScreen"
                            ),
                            curSoundInfo?.trackInfo2TrackM(),
                            mFragment
                        ) {
                            XPlayCommercialRelatedUtils.checkToShowADPrivilegeFloatDialog(
                                curSoundInfo,
                                fragment,
                                true
                            )
                        }
                    } else {
                        XPlayCommercialRelatedUtils.checkToShowADPrivilegeFloatDialog(
                            curSoundInfo,
                            fragment,
                            true
                        )
                    }
                }
            }

            override fun onAdCoverShow() {
            }
        }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        // 数据不再来自 播放信息，而是来自 UVE 的回调，故不调用super.onSoundInfoLoaded(soundInfo)
        curSoundInfo = soundInfo

        var trackId = -1L
        if (soundInfo?.trackInfo != null) {
            trackId = soundInfo.trackInfo!!.trackId
        }
        debugLog("onSoundInfoLoaded >>> trackId: " + trackId)
        CommercialDialogUnifiedFrequencyManager.requestDialogMaterial(
            CommercialDialogUnifiedFrequencyManager.SOURCE_PLAY,
            mapOf<String, String>(
                "sleepAidMode" to "${SleepEffectUtils.isUsing()}",
                "playPageStyle" to "heightScreen"
            ),
            curSoundInfo?.trackInfo2TrackM(),
            mFragment
        ) {
            if (!XPlayCommercialRelatedUtils.checkToShowVipRelatedDialog(
                    soundInfo,
                    fragment,
                )
            ) {
                processNoAdRightDialog(true)
            }
        }
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        super.onSoundSwitch(lastModel, curModel)
        // hide()
    }

    override fun onSoundPlayComplete() {
        // do Nothing
    }

    override fun onAudioAuditionOver(track: Track?) {
        // do Nothing
    }

    override fun onResume() {
        super.onResume()
        processNoAdRightDialog(false)
    }


    private fun processNoAdRightDialog(isSoundInfoLoaded: Boolean) {
        Logger.i(TAG, "processNoAdRightDialog -1")
        if (!XPlayCommercialRelatedUtils.checkNoAdDialogCanShow(context)) {
            // 客户端记录28天内只展示1次
            releaseNoAdDialogShowTask()
            return
        }

        if (XPlayCommercialRelatedUtils.checkToShowNoAdDialog(curSoundInfo, fragment)) {
            // 展示缓存的免广告、音质音效转化弹窗
            releaseNoAdDialogShowTask()
            return
        }

        if (isSoundInfoLoaded) {
            releaseNoAdDialogShowTask()
        }
        val curTime = TimeService.currentTimeMillis()
        CommonGuidanceInfo.findTarget(curSoundInfo, CommonGuidanceInfo.TYPE_VIP_FLOAT_LAYER)
            ?.takeIf {
                it.expireTime > curTime
            }?.let {
                Logger.i(TAG, "processNoAdRightDialog -2")
                XPlayCommercialRelatedUtils.saveNoAdDialogFloatModel(context, it)
                if ((mNoAdRightDialogRecordPair?.second?.mType ?: Int.MIN_VALUE) == it.type) {
                    mNoAdRightDialogRecordPair = Pair(
                        curTrackId,
                        XNoAdRightDialog.createDialogRecord(fragment, it, curSoundInfo!!)
                    )
                    Logger.i(TAG, "processNoAdRightDialog -2.1:$mNoAdRightDialogRecordPair")
                    return@let
                }
                Logger.i(TAG, "processNoAdRightDialog -3")
                releaseNoAdDialogShowTask()
                mNoAdRightDialogRecordPair = Pair(
                    curTrackId, XNoAdRightDialog.createDialogRecord(fragment, it, curSoundInfo!!)
                )
                mTimerTask = object : TimerTask() {
                    override fun run() {
                        Logger.i(TAG, "processNoAdRightDialog -4")
                        HandlerManager.postOnUIThread {
                            if (fragment != null && fragment.canUpdateUi()) {
                                if (!XPlayCommercialRelatedUtils.checkIfCanShowVipRelatedFloat(
                                        fragment
                                    )
                                ) {
                                    releaseNoAdDialogShowTask()
                                    return@postOnUIThread
                                }
                                if (XUtils.isLandScape(fragment.context)) {
                                    XUtils.exitLandScape(fragment.activity)
                                }
                                if (mNoAdRightDialogRecordPair?.first == curTrackId && curTrackId != 0L
                                    && mNoAdRightDialogRecordPair?.second != null
                                ) {
                                    Logger.i(
                                        TAG,
                                        "processNoAdRightDialog -4.1:$mNoAdRightDialogRecordPair"
                                    )
                                    GlobalDialogManager.getInstance()
                                        .checkAbleToShowSimple(mNoAdRightDialogRecordPair?.second)
                                    mNoAdRightDialogRecordPair = null
                                    releaseNoAdDialogShowTask()
                                }
                            }
                        }
                    }
                }
                mNoAdRightDialogShowTimer = Timer()
                mNoAdRightDialogShowTimer!!.schedule(mTimerTask!!, it.expireTime - curTime)
            }
    }

    private fun releaseNoAdDialogShowTask() {
        if (mTimerTask != null) {
            mTimerTask?.cancel()
            mTimerTask = null
        }
        if (mNoAdRightDialogShowTimer != null) {
            mNoAdRightDialogShowTimer?.purge()
            mNoAdRightDialogShowTimer?.cancel()
            mNoAdRightDialogShowTimer = null
        }
        mNoAdRightDialogRecordPair = null
    }

    override fun onLosePriority() {
        isCanShowBannerWithPriority = false
    }

    override fun onGainedPriority() {
        isCanShowBannerWithPriority = true
    }

    override fun getUveAdParams(
        sceneId: Int,
        otherMap: MutableMap<Any?, Any?>?
    ): MutableMap<String, String> {
        val params: MutableMap<String, String> = HashMap();

        val hideBarType = getPlayPageHideBarTypeWrapper()
        if (!StringUtil.isEmpty(hideBarType)) {
            params.put("hideBarType", hideBarType)
        }

        params.put(
            "trackQualityLevel",
            "" + TrackPlayQualityManager.getInstance().trackPlayQualityLevel
        )

        if (TrackPlaySoundEffectManager.getInstance().isSoundEffectFunctional) {
            val currentId = TrackPlaySoundEffectManager.getInstance().trackSoundEffectId
            val currentNormalValid =
                TrackPlaySoundEffectManager.getInstance().normalValidTrackSoundEffectId
            if (currentId == currentNormalValid && SoundEffectPlayerConstant.EFFECT_ID_NONE != currentId) {
                params.put("soundEffectId", "" + currentId)
            }
        }
        params["playPageStyle"] = "heightScreen"

        try {
            SceneInfoUtil.addSceneInfoToParams(params)
            SceneInfoUtil.addNewFreeCrowdToParams(params)
        } catch (e: Throwable) {

        }


        var trackId = -1L
        if (curSoundInfo?.trackInfo != null) {
            trackId = curSoundInfo!!.trackInfo!!.trackId
        }

        // fix trackId 更新不及时
        var currentTrackId: Long = -1
        if (XmPlayerManager.getInstance(ToolUtil.getCtx()) != null && XmPlayerManager.getInstance(
                ToolUtil.getCtx()
            ).currSound != null
        ) {
            val track = XmPlayerManager.getInstance(ToolUtil.getCtx()).currSound;
            if (track is Track) {
                if (PlayableModel.KIND_TRACK == track.getKind() || PlayableModel.KIND_MODE_SLEEP == track.getKind()) {
                    currentTrackId = track.getDataId()
                }
            }
        } else if (curSoundInfo?.trackInfo != null) {
            currentTrackId = curSoundInfo!!.trackInfo!!.trackId
        }
        debugLog("onSoundInfoLoaded >>> trackId: $trackId  currentTrackId: $currentTrackId")
        params["trackId"] = currentTrackId.toString()

        if (curSoundInfo != null && curSoundInfo!!.trackInfo != null) {
            params["albumId"] = curSoundInfo!!.trackInfo!!.albumId.toString()
        }

        debugLog("getUveAdParams >>> " + params)
        return params
    }

    override fun onUveLoadFinished(response: YellowZoneModelV2?, callback: IUveBehaviorCallback?) {
        debugLog("onUveLoadFinish can show? " + isCanShowBannerWithPriority + ", data: " + response)
        YPlayPageYellowBarManager.instance.debugLog("onUveLoadFinish can show? " + isCanShowBannerWithPriority + ", data: " + response)
        if (response == null) {
            hide()
            return
        }
        var currentTrackId: Long = 0
        if (XmPlayerManager.getInstance(ToolUtil.getCtx()) != null && XmPlayerManager.getInstance(
                ToolUtil.getCtx()
            ).currSound != null
        ) {
            val track = XmPlayerManager.getInstance(ToolUtil.getCtx()).currSound;
            if (track is Track) {
                if (PlayableModel.KIND_TRACK == track.getKind() || PlayableModel.KIND_MODE_SLEEP == track.getKind()) {
                    currentTrackId = track.getDataId()
                }
            }
        }
        debugLog("onUveLoadFinis curSoundInfo: " + curSoundInfo + ", " + currentTrackId)
        YPlayPageYellowBarManager.instance.debugLog("onUveLoadFinis curSoundInfo: " + curSoundInfo + ", " + currentTrackId)

        if (curSoundInfo == null) {
            return
        }
        debugLog("onUveLoadFinis curSoundInfo.trackId: " + curSoundInfo?.trackInfo?.trackId + ", " + currentTrackId)
        YPlayPageYellowBarManager.instance.debugLog("onUveLoadFinis curSoundInfo.trackId: " + curSoundInfo?.trackInfo?.trackId + ", " + currentTrackId)
        setView(curSoundInfo, response)

        debugLog("onUveLoadFinis currentBuyViewChild: $currentBuyViewChild")
        YPlayPageYellowBarManager.instance.debugLog(
            "onUveLoadFinis currentBuyViewChild: $currentBuyViewChild \n ${
                GsonUtils.toJson(
                    response
                )
            }"
        )

        if (currentBuyViewChild != null) {
            currentBuyViewChild.setBehaviorCallback(callback)
        }
    }

    override fun animationShow() {
        super.animationShow()
        YPlayPageYellowBarManager.instance.debugLog("animationShow")
        YPlayPageYellowBarManager.instance.notifyHeightChanged()
    }

    override fun hide() {
        super.hide()
        YPlayPageYellowBarManager.instance.debugLog("hide")
        YPlayPageYellowBarManager.instance.notifyHeightChanged()
    }

    override fun isInterceptRequest(sceneId: Int): Boolean {
        // 目前已知：小黄条展示展示时， 二次曝光、暂停均可以不请求uve，根据场景判断是否独占， 如果独占的话则不再请求uve接口

        val showing = currentBuyViewChild?.isShowing ?: false
        debugLog("isInterceptRequest s2 >> isShowing: $showing")
        return showing
    }

    override fun refreshView() {
        debugLog("refreshView skipp refresh, wait uve")
    }

    fun debugLog(log: String) {
        if (ConstantsOpenSdk.isDebug) {
            Log.w("z_uve_yellow", log)
        }
    }
}