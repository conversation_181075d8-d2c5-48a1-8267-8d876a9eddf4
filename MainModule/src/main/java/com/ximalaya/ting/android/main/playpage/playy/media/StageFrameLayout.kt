package com.ximalaya.ting.android.main.playpage.playy.media

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.playpage.playx.utils.afterLayout
import com.ximalaya.ting.android.main.playpage.playy.view.PlayMediaBackground

class StageFrameLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    val backgroundView = PlayMediaBackground(context)

    init {
        addView(backgroundView, LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        ))
    }
    var belowStatusBar = false

    var refBottomView: View? = null
        set(value) {
            field = value
            update()
            registerLayoutListener(value)
        }

    var contentView: View? = null
        set(value) {
            field = value
            (value?.parent as? ViewGroup)?.removeView(value)
            addView(value, LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            ).apply {
                if (belowStatusBar) {
                    topMargin = BaseUtil.getStatusBarHeight(context)
                }
            })
            update()
        }

    private fun registerLayoutListener(view: View?) {
        view?.afterLayout {
            update()
        }
    }

    private fun update() {
        val content = contentView?: return

        val layoutParams = content.layoutParams as? FrameLayout.LayoutParams?: (LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        ).apply {
            if (belowStatusBar) {
                topMargin = BaseUtil.getStatusBarHeight(context) + 50.dp
            }
            content.layoutParams = this
        })

        if (belowStatusBar) {
            layoutParams.topMargin = BaseUtil.getStatusBarHeight(context) + 50.dp
        }

        val bottomView = refBottomView
        val bottomViewParent = refBottomView?.parent as? View
        if (bottomView != null && bottomView.top > 0 && bottomViewParent != null && bottomViewParent.height > 0) {
            layoutParams.bottomMargin = bottomViewParent.height - bottomView.top
        }
        content.requestLayout()
    }

    fun updateBackgroundTheme(color: Int) {
        backgroundView.setThemeColor(color)
    }
}