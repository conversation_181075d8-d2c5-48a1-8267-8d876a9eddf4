package com.ximalaya.ting.android.main.playpage.playx.utils

import android.graphics.Matrix
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ListView
import android.widget.ScrollView
import androidx.core.view.children
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.handmark.pulltorefresh.library.PullToRefreshListView
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView
import com.tencent.smtt.sdk.WebView
import com.ximalaya.ting.android.host.view.XMSmartRefreshLayout
import com.ximalaya.ting.android.robust.utils.Log
import java.lang.reflect.Field


fun View.afterLayout(block: View.() -> Unit) {
    val originalVto = viewTreeObserver
    originalVto.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
        override fun onPreDraw(): Boolean {
            if (originalVto.isAlive) originalVto.removeOnPreDrawListener(this)
            viewTreeObserver.removeOnPreDrawListener(this)
            block(this@afterLayout)
            return true
        }
    })
}

fun ViewPager.getCurrentView(): View? {
    try {
        children.forEachIndexed { _, view ->
            val layoutParams = view.layoutParams as ViewPager.LayoutParams
            val f: Field = layoutParams.javaClass.getDeclaredField("position") //NoSuchFieldException
            f.isAccessible = true
            val position = f.get(layoutParams) as? Int //IllegalAccessException
            if (!layoutParams.isDecor && currentItem == position) {
                return view
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        android.util.Log.i("dqq1","Exception:${e.message}")
    }
    return null
}

private fun inTarget(view: View): Boolean {
    return view is ListView ||
    view is XMSmartRefreshLayout ||
    view is PullToRefreshRecyclerView ||
    view is RecyclerView ||
    view is ScrollView ||
    view is NestedScrollView ||
    view is PullToRefreshListView && view.isPullToRefreshEnabled ||
    view is WebView || view is android.webkit.WebView
}


fun transformMatrixToLocal(view: View, matrix: Matrix) {
    val parent = view.parent
    if (parent is View) {
        val vp = parent as View
        transformMatrixToLocal(vp, matrix)
        matrix.postTranslate(vp.scrollX.toFloat(), vp.scrollY.toFloat())
    }
    matrix.postTranslate(-view.left.toFloat(), -view.top.toFloat())
    val vm = view.getMatrix()
    if (!vm.isIdentity) {
        val inverted = Matrix()
        if (vm.invert(inverted)) {
            matrix.postConcat(inverted)
        }
    }
}

fun isTransformedTouchPointInView(
    x: Float, y: Float, child: View,
): Boolean {
    val point = FloatArray(2)
    point[0] = x
    point[1] = y
    transformPointToViewLocal(point, child)
    return child.inxy(point[0].toInt(), point[1].toInt())
}

fun transformPointToViewLocal(point: FloatArray, child: View) {
    point[0] += (child.scrollX - child.left).toFloat()
    point[1] += (child.scrollY - child.top).toFloat()
    val vm = child.getMatrix()
    if (!vm.isIdentity) {
        val inverted = Matrix()
        if (vm.invert(inverted)) {
            inverted.mapPoints(point)
        }
    }
}
private fun View.inxy(x: Int, y: Int): Boolean {
    val rect = Rect()
    getHitRect(rect)
    return rect.contains(x, y)
}

fun findViewAt(viewGroup: ViewGroup, x: Float, y: Float): View? {
    Log.d("findViewAt", "start: $x:$y; ${viewGroup.toString()}")
    for (i in 0 until viewGroup.childCount) {
        val child = viewGroup.getChildAt(i)
        if (inTarget(child) && child.isShown && isTransformedTouchPointInView(x, y, child)) {
            Log.d("findViewAt", "found $child")
            return child
        } else if (child is ViewPager && isTransformedTouchPointInView(x, y, child)) {
            val currentItemViewInPager = child.getCurrentView()
            if (currentItemViewInPager != null) {
                val point = FloatArray(2)
                point[0] = x
                point[1] = y
                transformPointToViewLocal(point, child)
                Log.d("findViewAt", "$point; continue find in viewpager child: $currentItemViewInPager")
                return findViewAt(child, point[0], point[1])
            }
        }
        if (child is ViewGroup) {
            val point = FloatArray(2)
            point[0] = x
            point[1] = y
            transformPointToViewLocal(point, child)
            val foundView = findViewAt(child, point[0], point[1])
            if (foundView != null && foundView.isShown()) {
                Log.d("findViewAt", "found child: $foundView")
                return foundView
            }
        } else {
            if (child.isShown && isTransformedTouchPointInView(x, y, child)) {
                Log.d("findViewAt", "found child view: $child")
                return child
            }
        }
    }
    Log.d("findViewAt", "found  view: null")
    return null
}