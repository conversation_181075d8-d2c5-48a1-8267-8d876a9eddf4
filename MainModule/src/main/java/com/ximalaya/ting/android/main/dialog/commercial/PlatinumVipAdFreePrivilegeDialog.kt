package com.ximalaya.ting.android.main.dialog.commercial

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.PorterDuff
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.commercial.CommercialDialogData
import com.ximalaya.ting.android.host.commercial.ICommercialDialog
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.ui.TrackItemTagUtil
import com.ximalaya.ting.android.host.util.view.TrackTagSpan
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.util.ui.DrawableBuildUtil.GradientDrawableBuilder
import com.ximalaya.ting.android.main.util.ui.TextUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

/**
 * <AUTHOR>
 * @time 2025/4/3 11:55
 * @description: 白金会员MOT弹窗
**/
class PlatinumVipAdFreePrivilegeDialog : BaseLoadDialogFragment(), ICommercialDialog, View.OnClickListener {

    companion object {
        fun jumpUrl(url: String?): Boolean {
            url ?: return false
            val rightUrl = getRightUrl(url)
            val activity = BaseApplication.getMainActivity()
            if (activity is MainActivity) {
                ToolUtil.clickUrlAction(activity, rightUrl, null)
                return true
            }
            return false
        }

        private fun getRightUrl(url: String): String {
            var tempUrl: String = url
            for (i in 0..3) {
                if (tempUrl.contains("://")) {
                    return tempUrl
                }
                tempUrl = Uri.decode(tempUrl)
            }
            return tempUrl
        }
    }

    private var xmRequestId: String? = null
    private var mData: CommercialDialogData? = null
    private var vTargetChildView: View? = null
    private var vTopClickArea: View? = null
    private var vTopBgImageView:ImageFilterView? = null
    private var mIvClose: ImageView? = null
    private var mTvTitlePart1: TextView? = null
    private var mTvTitlePart2: TextView? = null
    private var mTvTitlePart3: TextView? = null
    private var mTvOriginPriceName: TextView? = null
    private var mTvOriginPrice: TextView? = null
    private var mTvDiscountPriceName: TextView? = null
    private var mTvDiscountPriceSymbol: TextView? = null
    private var mTvDiscountPrice: TextView? = null
    private var mTvDiscountPriceUnit: TextView? = null
    private var vButtonBgImageView: ImageFilterView? = null
    private var mTvAction: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parentNeedBg = false
        setStyle(
            DialogFragment.STYLE_NO_TITLE,
            com.ximalaya.ting.android.host.R.style.host_share_dialog
        )
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        if (null != window) {
            val params = window.attributes
            params.width = WindowManager.LayoutParams.MATCH_PARENT
            params.height = WindowManager.LayoutParams.MATCH_PARENT
            params.gravity = Gravity.BOTTOM
            params.windowAnimations =
                com.ximalaya.ting.android.main.R.style.host_popup_window_from_bottom_animation
            window.attributes = params
        }
    }

    override fun getContainerLayoutId(): Int =
        com.ximalaya.ting.android.main.R.layout.main_dialog_platinum_vip_ad_free_privilege

    override fun initUi(view: View?, savedInstanceState: Bundle?) {
        vTopClickArea = view?.findViewById(com.ximalaya.ting.android.main.R.id.main_view_space)
        vTopClickArea?.setOnClickListener(this)
        vTargetChildView =
            view?.findViewById(com.ximalaya.ting.android.main.R.id.main_dialog_real_container)
        initView()
    }

    private fun initView() {
        vTargetChildView?.apply {
            vTopBgImageView = findViewById(com.ximalaya.ting.android.main.R.id.main_iv_top_bg_img)
            mIvClose = findViewById(com.ximalaya.ting.android.main.R.id.main_iv_close)
            mTvTitlePart1 = findViewById(com.ximalaya.ting.android.main.R.id.main_tv_title_part_1)
            mTvTitlePart2 = findViewById(com.ximalaya.ting.android.main.R.id.main_tv_title_part_2)
            mTvTitlePart3 = findViewById(com.ximalaya.ting.android.main.R.id.main_tv_title_part_3)
            mTvOriginPriceName =
                findViewById(com.ximalaya.ting.android.main.R.id.main_tv_platinum_vip_origin_price_name)
            mTvOriginPrice =
                findViewById(com.ximalaya.ting.android.main.R.id.main_tv_platinum_vip_origin_price)
            mTvDiscountPriceName =
                findViewById(com.ximalaya.ting.android.main.R.id.main_tv_platinum_vip_dicount_price_name)
            mTvDiscountPriceSymbol = findViewById(com.ximalaya.ting.android.main.R.id.main_tv_platinum_vip_dicount_price_symbol)
            mTvDiscountPrice =
                findViewById(com.ximalaya.ting.android.main.R.id.main_tv_platinum_vip_dicount_price)
            mTvDiscountPriceUnit =
                findViewById(com.ximalaya.ting.android.main.R.id.main_tv_platinum_vip_dicount_price_unit)
            mIvClose = findViewById(com.ximalaya.ting.android.main.R.id.main_iv_close)
            mTvAction = findViewById(com.ximalaya.ting.android.main.R.id.main_tv_action)
            vButtonBgImageView = findViewById(com.ximalaya.ting.android.main.R.id.main_iv_action_bg)
        }
        mTvAction?.setOnClickListener(this)
        initDialogLayoutParams()
    }

    private fun setDataToView() {
        ViewStatusUtil.setOnClickListener(
            mIvClose, this
        )
        updateStyle()
        updatePrivilegeText()
        mData?.getExtraContextJSON("productInfo")?.let {
            val stringBuilder = java.lang.StringBuilder()
            val payPrice = it.optString("productOffLinePrice")
            val priceUnit = it.optString("originalUnit")
            if (!payPrice.isNullOrEmpty()) {
                stringBuilder.append("¥").append(payPrice)
            }
            if (!priceUnit.isNullOrEmpty()) {
                stringBuilder.append("/").append(priceUnit)
            }
            ViewStatusUtil.setText(mTvOriginPrice, stringBuilder)
            val discountTitle = it.optString("productFirstTimeField")
            val productPrice = it.optString("productPrice")
            val productPriceUnit = it.optString("discountUnit")
            ViewStatusUtil.setText(mTvDiscountPriceName, discountTitle ?: "惊喜价")
            ViewStatusUtil.setText(mTvDiscountPrice, productPrice)
            context?.let { it1 ->
                TextUtil.setDinTypeFace(it1, mTvDiscountPriceSymbol!!)
                TextUtil.setDinTypeFace(it1, mTvOriginPrice!!)
                TextUtil.setDinTypeFace(it1, mTvDiscountPrice!!)
            }
            if (!productPriceUnit.isNullOrEmpty()) {
                ViewStatusUtil.setText(mTvDiscountPriceUnit, "/$productPriceUnit")
                ViewStatusUtil.setVisible(View.VISIBLE, mTvDiscountPriceUnit)
            } else {
                ViewStatusUtil.setVisible(View.GONE, mTvDiscountPriceUnit)
            }

        }
        mData?.buttons?.firstOrNull()?.let {
            ViewStatusUtil.setText(mTvAction, it.buttonText ?: "立即开通")
        }

    }

    private fun updatePrivilegeText() {
        mData ?: return
        mTvTitlePart1 ?: return
        ViewStatusUtil.setVisible(View.GONE, mTvTitlePart3, mTvTitlePart2)
        if (!mData!!.mainText.isNullOrEmpty()) {
            val text = mData?.mainText!!
            // "恭喜您，获得#高亮#会员专属免广告权益"
            // 文案必须匹配这个格式：文本#高亮文本#文本
            if (text.contains("#")) {
                val textList = text.split("#")
                if (textList.size >= 3) {
                    val drawable =
                        GradientDrawableBuilder().color(Color.parseColor(if (BaseFragmentActivity2.sIsDarkMode) "#FFD393" else "#2C2C3C"))
                            .cornerRadius(3.dpFloat)
                            .build()
                    ViewStatusUtil.setText(mTvTitlePart1, textList[0])
                    ViewStatusUtil.setText(mTvTitlePart2, textList[1])
                    mTvTitlePart2?.background = drawable
                    ViewStatusUtil.setText(mTvTitlePart3, textList[2])
                    ViewStatusUtil.setVisible(View.VISIBLE, mTvTitlePart3, mTvTitlePart2)
                } else {
                    ViewStatusUtil.setText(mTvTitlePart1, mData!!.mainText)
                }
            } else {
                ViewStatusUtil.setText(mTvTitlePart1, mData!!.mainText)
            }
        }
    }

    private fun updateStyle() {
        mData?.getImageCollectionItem(CommercialDialogData.IMAGE_COLLECTION_BACKGROUND)
            ?.getValidImage()?.let {
                ImageManager.from(context).displayImage(vTopBgImageView, it, -1)
            }
        mData?.getImageCollectionItem(CommercialDialogData.IMAGE_COLLECTION_BUTTON)?.getValidImage()
            ?.let {
                ImageManager.from(context)
                    .displayImage(vButtonBgImageView, it, R.drawable.main_bg_rect_ff4444_corner_100)
            }
        if (BaseFragmentActivity2.sIsDarkMode) {
            ViewStatusUtil.setTextColor(mTvTitlePart1, 0xFFFFD393.toInt())
            ViewStatusUtil.setTextColor(mTvTitlePart2, 0xFF131313.toInt())
            ViewStatusUtil.setTextColor(mTvTitlePart3, 0xFFFFD393.toInt())
            ViewStatusUtil.setTextColor(mTvOriginPrice, 0xFF9D9EAF.toInt())
            ViewStatusUtil.setTextColor(mTvOriginPriceName, 0xFF9D9EAF.toInt())
        } else {
            ViewStatusUtil.setTextColor(mTvTitlePart1, 0xFF2C2C3C.toInt())
            ViewStatusUtil.setTextColor(mTvTitlePart2, 0xFFFFFFFF.toInt())
            ViewStatusUtil.setTextColor(mTvTitlePart3, 0xFF2C2C3C.toInt())
            ViewStatusUtil.setTextColor(mTvOriginPriceName, 0xFF5E6076.toInt())
            ViewStatusUtil.setTextColor(mTvOriginPrice, 0xFF5E6076.toInt())
        }
    }

    override fun loadData() {
        setDataToView()
    }

    private fun initDialogLayoutParams() {
        val dialogViewWidth = getCompatScreenWidth(BaseApplication.getMyApplicationContext())
        vTargetChildView?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            width = dialogViewWidth
        }
    }

    override fun callDismiss() {
        dismissAllowingStateLoss()
    }

    override fun isRealShowing(): Boolean {
        return isAddFix
    }

    override fun setData(data: CommercialDialogData?) {
        mData = data
    }

    override fun bindDialogView(view: View?) {
    }

    private fun getCompatScreenWidth(context: Context): Int {
        if (FoldableScreenCompatUtil.isBigWidthScreen()) {
            return 375.dp
        } else {
            return BaseUtil.getScreenWidth(context)
        }
    }

    override fun showDialog(fragmentManager: FragmentManager, tag: String?) {
        xmRequestId = XmRequestIdManager.getInstance(context).requestId
        traceDialogShow()
        show(fragmentManager, tag)
    }

    private fun traceDialogShow() {
        mData?.getExtraContextJSON("productInfo")?.let {
            val payPrice = it.optString("productOffLinePrice")
            val productPrice = it.optString("productPrice")
            val productPriceUnit = it.optString("discountUnit")

            XMTraceApi.Trace()
                .setMetaId(63211)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                .put(XmRequestIdManager.CONT_TYPE, "commercial_dialog")
                .put(XmRequestIdManager.CONT_ID, "${mData!!.popupId}")
                .put("currPage", "")
                .put("currTrackId", "" + mData!!.trackId)
                .put("currAlbumId", "" + mData!!.albumId)
                .put("dialogId", mData!!.popupId)
                .put("strategy", mData!!.policyGroup)
                .put("item", mData?.buttons?.firstOrNull()?.buttonText)
                .put("originPrice", payPrice)
                .put("payAmount", productPrice)
                .put("unit", productPriceUnit)
                .put(mData!!.getExtraContextMap("priceSensitiveEmbedPointsInfo"))
                .put(mData!!.getExtraContextMap("eventTracking"))
                .createTrace()
        }
    }

    private fun trackDialogBtnClick() {
        mData?.getExtraContextJSON("productInfo")?.let {
            val payPrice = it.optString("productOffLinePrice")
            val productPrice = it.optString("productPrice")
            val productPriceUnit = it.optString("discountUnit")
            // 全局-会员MOT弹窗-立即续费  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(63212)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                .put(XmRequestIdManager.CONT_TYPE, "commercial_dialog")
                .put(XmRequestIdManager.CONT_ID, "${mData?.popupId}")
                .put("currPage", "")
                .put("currTrackId", "" + mData?.trackId)
                .put("currAlbumId", "" + mData?.albumId)
                .put("dialogId", mData?.popupId)
                .put("strategy", mData?.policyGroup)
                .put("item", mData?.buttons?.firstOrNull()?.buttonText)
                .put("originPrice", payPrice)
                .put("payAmount", productPrice)
                .put("unit", productPriceUnit)
                .put(mData!!.getExtraContextMap("priceSensitiveEmbedPointsInfo"))
                .put(mData!!.getExtraContextMap("eventTracking"))
                .createTrace()
        }
    }

    override fun canShow(): Boolean {
        mData?.getExtraContextJSON("productInfo") ?: return false
        return UserInfoMannage.hasLogined()
    }

    override fun onClick(v: View?) {
        when (v) {
            mIvClose, vTopClickArea -> {
                callDismiss()
            }
            mTvAction -> {
                var baseFragment2: BaseFragment2? = null
                if (parentFragment is BaseFragment2) {
                    baseFragment2 = parentFragment as BaseFragment2
                } else if (activity is MainActivity) {
                    baseFragment2 = (activity as MainActivity).currentTopBaseFragment
                }
                baseFragment2?.let {
                    trackDialogBtnClick()
                    callDismiss()
                    ToolUtil.clickUrlAction(it, mData?.buttons?.firstOrNull()?.buttonUrl ?: "", v)
                }
            }
        }
    }

    override fun getDialogSource(): String {
        return "commercial"
    }

    override fun getBusinessId(): String? {
        return if (mData != null) {
            mData!!.popupId
        } else super.getBusinessId()
    }

}