package com.ximalaya.ting.android.main.adapter.find.util

import android.graphics.Bitmap
import android.graphics.Color
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.view.DomainColorUtil
import kotlin.math.roundToInt

/**
 * 色值数据模型
 */
data class ColorValueModel(
    val bgColor: Int,
    val textColor: Int,
    val playBtnBgColor: Int,
    val playBtnColor: Int,
    var isDark: Boolean,
)

object SleepAidColorUtils {

    private val defaultModel = ColorValueModel(
        0xFFEDEDED.toInt(),
        0xFF333333.toInt(),
        0xCCFFFFFF.toInt(),
        0xFF333333.toInt(),
        false
    )

    private val darkDefaultModel = ColorValueModel(
        0xFF242424D.toInt(),
        0xFFF2F2F2.toInt(),
        0x0dF2F2F2.toInt(),
        0xFFF2F2F2.toInt(),
        true
    )

    @JvmStatic
    fun getDefaultColor(): ColorValueModel {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            darkDefaultModel
        } else {
            defaultModel
        }
    }

    @JvmStatic
    fun getSleepAidColor(bitmap: Bitmap?, callBack: IRequestCallback) {
        DomainColorUtil.getImageColor(bitmap, ColorUtil.INVALID_COLOR) { mainColor ->
            if (mainColor == ColorUtil.INVALID_COLOR) {
                callBack.onSuccess(getDefaultColor())
                return@getImageColor
            }
            callBack.onSuccess(calculateColorValues(mainColor))
        }
    }

    /**
     * 根据传入的颜色值计算对应的色值模型
     * @param color 输入的颜色值
     * @return ColorValueModel 包含背景色、文字色、按钮背景色和按钮播放色
     */
    @JvmStatic
    private fun calculateColorValues(color: Int): ColorValueModel {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            calculateDarkModeColorValues(color)
        } else {
            calculateLightModeColorValues(color)
        }
    }

    /**
     * 计算白天模式的色值模型
     * @param color 输入的颜色值
     * @return ColorValueModel 包含背景色、文字色、按钮背景色和按钮播放色
     */
    private fun calculateLightModeColorValues(color: Int): ColorValueModel {
        val hsl = FloatArray(3)
        Color.colorToHSV(color, hsl)

        val hue = hsl[0]
        val saturation = hsl[1] * 100 // 转换为百分比

        // 白天模式背景色值：S≤30则S不变，S>30则S=30，L固定为92
        val backgroundSaturation = if (saturation <= 30) saturation else 30f
        val backgroundLightness = 92f
        val backgroundColor = calculateColorFromHSL(hue, backgroundSaturation, backgroundLightness)

        // 白天模式文字色值：S≤30则S不变，S>30则S=30，L固定为20
        val textSaturation = if (saturation <= 30) saturation else 30f
        val textLightness = 20f
        val textColor = calculateColorFromHSL(hue, textSaturation, textLightness)

        // 白天模式按钮背景色：#FFFFFF 80%透明度
        val buttonBackgroundColor = Color.argb(
            (255 * 0.8).roundToInt(), // 80%透明度
            255, 255, 255 // 白色
        )

        // 白天模式按钮播放色（同文字色值）
        val buttonPlayColor = textColor

        return ColorValueModel(
            bgColor = backgroundColor,
            textColor = textColor,
            playBtnBgColor = buttonBackgroundColor,
            playBtnColor = buttonPlayColor,
            false
        )
    }

    /**
     * 计算夜间模式的色值模型
     * @param color 输入的颜色值
     * @return ColorValueModel 包含背景色、文字色、按钮背景色和按钮播放色
     */
    private fun calculateDarkModeColorValues(color: Int): ColorValueModel {
        val hsl = FloatArray(3)
        Color.colorToHSV(color, hsl)

        val hue = hsl[0]
        val saturation = hsl[1] * 100 // 转换为百分比

        // 夜间模式背景色值：S≤8则S不变，S>8则S=8，L固定为14
        val backgroundSaturation = if (saturation <= 8) saturation else 8f
        val backgroundLightness = 14f
        val backgroundColor = calculateColorFromHSL(hue, backgroundSaturation, backgroundLightness)

        // 夜间模式文字色值：S≤30则S不变，S>30则S=30，L固定为92
        val textSaturation = if (saturation <= 30) saturation else 30f
        val textLightness = 92f
        val textColor = calculateColorFromHSL(hue, textSaturation, textLightness)

        // 夜间模式按钮背景色（文字颜色+5%透明度）
        val buttonBackgroundColor = Color.argb(
            (255 * 0.05).roundToInt(), // 95%透明度
            Color.red(textColor),
            Color.green(textColor),
            Color.blue(textColor)
        )

        // 夜间模式按钮播放色（同文字色值）
        val buttonPlayColor = textColor

        return ColorValueModel(
            bgColor = backgroundColor,
            textColor = textColor,
            playBtnBgColor = buttonBackgroundColor,
            playBtnColor = buttonPlayColor,
            true
        )
    }

    /**
     * 根据HSL值计算颜色
     * @param hue 色相 (0-360)
     * @param saturation 饱和度 (0-100)
     * @param lightness 亮度 (0-100)
     * @return 对应的颜色值
     */
    private fun calculateColorFromHSL(hue: Float, saturation: Float, lightness: Float): Int {
        val h = hue / 360f
        val s = saturation / 100f
        val l = lightness / 100f

        val q = if (l < 0.5f) l * (1 + s) else l + s - l * s
        val p = 2 * l - q

        fun hueToRgb(t: Float): Float {
            val t2 = when {
                t < 0f -> t + 1f
                t > 1f -> t - 1f
                else -> t
            }
            return when {
                t2 < 1f / 6f -> p + (q - p) * 6 * t2
                t2 < 1f / 2f -> q
                t2 < 2f / 3f -> p + (q - p) * (2f / 3f - t2) * 6
                else -> p
            }
        }

        val r = (hueToRgb(h + 1f / 3f) * 255).roundToInt().coerceIn(0, 255)
        val g = (hueToRgb(h) * 255).roundToInt().coerceIn(0, 255)
        val b = (hueToRgb(h - 1f / 3f) * 255).roundToInt().coerceIn(0, 255)

        return Color.rgb(r, g, b)
    }

}

interface IRequestCallback {
    fun onSuccess(data: ColorValueModel)
}