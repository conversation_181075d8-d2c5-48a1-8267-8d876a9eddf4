package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.IClickExportListener
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.ShortVideoCoverPlayUtil
import com.ximalaya.ting.android.main.adapter.find.view.ShortVideoCoverView
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew.RECOMMEND_ITEM_SHORT_VIDEO_LIST
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 新首页 - 短视频卡片
 *
 */
class RecommendShortVideoListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendShortVideoListAdapterProviderStaggered.ListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendShortVideoListAdapterProviderStaggered.ListCardViewHolder, RecommendItemNew> {

    private var oldScrollState = RecyclerView.SCROLL_STATE_IDLE

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_short_video_list_card, parent, false
        )
    }

    override fun createViewHolder(convertView: View?): ListCardViewHolder? {
        return convertView?.let { ListCardViewHolder(it) }
    }

    override fun bindViewHolder(
        holder: ListCardViewHolder?, position: Int, data: RecommendItemNew?, convertView: View?
    ) {
        if (holder == null || data == null) return

        if (data.xmRequestId != null && (data.xmRequestId + position) == holder.uniqueId) {
            return
        } else {
            holder.uniqueId = data.xmRequestId + position
        }

        val recommendCommonItem = data.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) return
        if (recommendCommonItem.subElements.isNullOrEmpty()) return

        holder.tvModuleTitle.text = recommendCommonItem.title

        val exportMore = { action: String ->
            // 新首页-社会化标注-更多  点击事件
            val trace1 = XMTraceApi.Trace()
                .click(61824) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId ?: "") // 客户端传，去重用
                .put("action", action)
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            if (data.isLocalCache) {
                trace1.isLocalCache
            }

            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                "更多",
                SpmTraceUtil.TAB_MORE_VALUE
            )

            trace1.createTrace()
        }

        val performMoreClick = {
            // 新首页-首页大卡模块  点击事件
            val trace = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId) // 客户端传
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }

            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )

            trace.createTrace()

            ToolUtil.clickUrlAction(
                fragment, recommendCommonItem.landingPage ?: "", holder.tvMore
            )
        }
        holder.tvModuleTitle.setOnClickListener {
            if (recommendCommonItem.landingPage.isNullOrBlank()) return@setOnClickListener
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            exportMore("click")
            performMoreClick()
        }

        holder.tvMore.setOnClickListener {
            exportMore("click")
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            val isShowMore = !recommendCommonItem.landingPage.isNullOrBlank()
            var level1DisLikeTitle = recommendCommonItem.extraInfo?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick()
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(position)
                }
            }
            val moreFuncBuild = MoreFuncBuild.createSocialListenMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener,
                "查看更多视频"
            )

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (position + 1).toString())
                put("xmRequestId", data.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.extraInfo?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }

        val adapter = ListAdapter(
            dataAction, fragment, recommendCommonItem, data,
            recommendCommonItem.subElements ?: emptyList(), position,
            holder.rvList,
            recommendCommonItem.landingPage.isNullOrEmpty().not()
        ).also {
            it.setRelaseJumpActivityListener {
                exportMore("slide")
                performMoreClick()
            }
        }
        holder.rvList.adapter = adapter

        ShortVideoCoverPlayUtil.bindModule(ShortVideoCoverPlayUtil.FROM_HOME, position)

        holder.rvList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == oldScrollState) return
                oldScrollState = newState
                if (oldScrollState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(data, position, holder)
                    (recyclerView.layoutManager as? LinearLayoutManager)?.also {
                        data.firstVisiblePosition = it.findFirstVisibleItemPosition()
                        data.firstVisibleOffset = it.findViewByPosition(
                            data.firstVisiblePosition
                        )?.left ?: 0
                    }

                    ShortVideoCoverPlayUtil.checkScroll(
                        ShortVideoCoverPlayUtil.FROM_HOME,
                        position,
                        recyclerView
                    )
                }
            }
        })
        (holder.rvList.layoutManager as? LinearLayoutManager)?.also {
            if (data.firstVisiblePosition <= 0 && data.firstVisibleOffset <= 0) {
                return@also
            }
            if (data.firstVisiblePosition != it.findFirstVisibleItemPosition() ||
                data.firstVisibleOffset != (it.findViewByPosition(data.firstVisiblePosition)?.left
                    ?: 0)
            ) {
                holder.rvList.scrollToPosition(data.firstVisiblePosition)
                holder.rvList.post {
                    // 二次滚动 offset 需要加上 Recyclerview 的左侧 padding 值
                    holder.rvList.scrollBy(-data.firstVisibleOffset + 16.dp, 0)
                }
            }
        }
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?, position: Int, holder: ListCardViewHolder?
    ) {
        if (data == null || holder == null) return

        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) return

        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    trace.createTrace()
                }
                for (i in 0 until holder.rvList.childCount) {
                    val view = holder.rvList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement =
                            view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        traceItemShow(position, data, index, subElement, view, recommendCommonItem)
                    }
                }
            }
        }
    }

    class ListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val tvModuleTitle: TextView = convertView.findViewById(R.id.main_tv_title)
        val rvList: RecyclerView = convertView.findViewById(R.id.main_rcv_live_list)
        val tvMore: View = convertView.findViewById(R.id.main_tv_more)

        var uniqueId = ""
    }

    class ListAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        private val fragment: BaseFragment2,
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew,
        list: List<CommonSubElement>,
        var parentPosition: Int,
        val recyclerView: RecyclerView,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            mEnableMoreItem = enableJumpMore
            commonSubElementList.addAll(list)
        }

        class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val mVideoCoverView: ShortVideoCoverView =
                view.findViewById(R.id.main_short_video_cover_view)
            val mTvTitle: TextView = view.findViewById(R.id.main_tv_video_title)
            val mViewMask: View = view.findViewById(R.id.main_view_video_mask)
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): RecyclerView.ViewHolder {
            return ItemViewHolder(
                ViewPool.getInstance().getView(
                    HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                    R.layout.main_item_recommend_short_video_list_item,
                    parent,
                    false,
                    RECOMMEND_ITEM_SHORT_VIDEO_LIST
                )
            )
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        override fun getItemCount(): Int {
            return commonSubElementList.size.let {
                if (enableJumpMore) it + 1 else it
            }
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is ItemViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                moreRootView?.setPadding(10.dp, 0, 0, 0)
                horizontalView?.updateLayoutParams {
                    width = 37.dp
                    height = 202.dp
                }
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun onBindViewHolderInner(holder: ItemViewHolder, position: Int) {
            val element = commonSubElementList.getOrNull(position) ?: return

            holder.itemView.setTag(R.id.main_id_item_data, element)
            holder.itemView.setTag(R.id.main_id_data_index, position)

            val corner = RecommendCornerUtils.getShortVideoCorner()
            holder.mVideoCoverView.setCoverCorner(corner)

            (holder.mViewMask.background as? GradientDrawable)?.let {
                // 左上, 右上, 右下, 左下
                val radii = floatArrayOf(0f, 0f, 0f, 0f, corner, corner, corner, corner)
                it.setCornerRadii(radii)
            }

            val onCardClick = object : IClickExportListener {
                override fun onClick(isReport: Boolean, view: View?) {
                    if (element.landingPage.isNullOrBlank()) {
                        return
                    }

                    if (isReport) {
                        // 新首页-首页大卡模块  点击事件
                        val bigBrace = XMTraceApi.Trace()
                            .click(62176) // 用户点击时上报
                            .put("currPage", "newHomePage")
                            .put("modulePosition", (parentPosition + 1).toString()) // 客户端传
                            .put("xmRequestId", recommendItemNew.xmRequestId ?: "") // 客户端传
                        RecommendNewUbtV2Manager.addUbtV2Data(
                            bigBrace,
                            (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
                        )
                        if (recommendItemNew.isLocalCache) {
                            bigBrace.isLocalCache
                        }
                        SpmTraceUtil.addSpmTraceInfo(
                            bigBrace,
                            (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                            (parentPosition + 1).toString()
                        )
                        bigBrace.createTrace()

                        // 新首页-社会化标注-声音/专辑/直播/视频卡片  点击事件
                        val trace =XMTraceApi.Trace()
                            .click(60896) // 用户点击时上报
                            .put("currPage", "newHomePage")
                            .put("positionNew", (position + 1).toString()) // 客户端传。
                            .put("contentId", (element.id ?: 0).toString()) // 客户端传。去重使用
                            .put("contentType", element.bizType ?: "") // 客户端传。去重使用
                            .put("xmRequestId", recommendItemNew.xmRequestId ?: "") // 客户端传，去重用
                            .put("modulePosition", (parentPosition + 1).toString()) // 客户端传
                            .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString())
                            .put("area", "item | play") // 客户端传。区分是条还是播放按钮

                        RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, element.ubtV2)
                        if (recommendItemNew.isLocalCache) {
                            trace.isLocalCache
                        }
                        SpmTraceUtil.addSpmTraceInfo(
                            trace,
                            moduleItem.ubtV2,
                            (parentPosition + 1).toString(),
                            contentTitle = element.title,
                            contentPosition = (position + 1).toString()
                        )
                        trace.createTrace()
                    }

                    ToolUtil.clickUrlAction(fragment, element.landingPage!!, view)
                }
            }

            val onCardLongClick = {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (parentPosition + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                if (!moduleItem.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(moduleItem.ubtV2)
                }
                if (!element.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(element.ubtV2)
                }
                traceMap["xmRequestId"] = recommendItemNew.xmRequestId ?: ""
                traceMap["contentType"] = element.bizType ?: ""
                traceMap["contentId"] = element.refId?.toString() ?: ""
                // 负反馈接口请求传参
                requestMap[HttpParamsConstants.PARAM_SHORT_VIDEO_ID] = element.id?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] = element.anchor?.uid.toString()
                requestMap["card_contentType"] = moduleItem.contentType ?: ""
                requestMap["card_bizType"] = moduleItem.bizType ?: ""
                requestMap["card_id"] = moduleItem.id?.toString() ?: ""

                val disLikeLeve2Build = DisLikeLeve2Build()
                disLikeLeve2Build.isFromAd = false
                disLikeLeve2Build.anchorName = element.anchor?.nickName
                disLikeLeve2Build.requestMap = requestMap
                disLikeLeve2Build.traceMap = traceMap
                disLikeLeve2Build.onFeedBackListener = object :
                    NewXmFeedBackPopDialog.IOnFeedBackListener() {
                    override fun onDialogShow(showSuccess: Boolean) {
                    }

                    override fun onFeedBack(list: List<XmFeedInnerModel>) {
                        MainCommonRequest.getSingleVideoListItem(
                            position,
                            element.landingPage,
                            moduleItem,
                            object : IDataCallBack<CommonSubElement> {
                                override fun onSuccess(subElement: CommonSubElement?) {
                                    if (subElement == null) {
                                        removeItem(position)
                                        return
                                    }
                                    commonSubElementList[position] = subElement
                                    notifyItemChanged(position)
                                    traceItemShow(
                                        parentPosition,
                                        recommendItemNew,
                                        position,
                                        subElement,
                                        holder.itemView,
                                        moduleItem
                                    )
                                }

                                override fun onError(code: Int, message: String?) {
                                    removeItem(position)
                                }
                            })
                    }

                    private fun removeItem(position: Int) {
                        commonSubElementList.removeAt(position)
                        moduleItem.subElements?.removeAt(position)
                        notifyItemRemoved(position)
                        notifyItemRangeChanged(position, commonSubElementList.size - position)
                    }
                }

                val moreFunLister = object : IMoreFuncListener() {
                    override fun onTopModuleClick() {
                        onCardClick.onClick(false)
                    }
                }

                val build: MoreFuncBuild = MoreFuncBuild.createShortVideoLongClickModel(
                    fragment, element.refId, element.bizType, moreFunLister, true, disLikeLeve2Build
                )
                build.trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", element.bizType ?: "")
                    put("contentId", element.refId?.toString() ?: "")
                    put("modulePosition", (parentPosition + 1).toString())
                    put("positionNew", (position + 1).toString())
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    element.ubtV2?.let { it1 -> putAll(it1) }
                }

                XmMoreFuncManager.checkShowMorePage(build)
            }

            // 点击跳转
            holder.itemView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
                onCardClick.onClick(true, it)
            }

            // 长按负反馈
            holder.itemView.setOnLongClickListener {
                onCardLongClick.invoke()
                true
            }

            val staticCover = element.cover ?: ""
            val giftCover = element.preview ?: ""

            holder.mVideoCoverView.position = position
            holder.mVideoCoverView.setData(staticCover, giftCover, object :
                ShortVideoCoverView.IAllowPlayCallBack {
                override fun isAllowedPlay(): Boolean {
                    return ShortVideoCoverPlayUtil.isAllowPlay(
                        ShortVideoCoverPlayUtil.FROM_HOME,
                        parentPosition,
                        position
                    )
                }
            })

            holder.mTvTitle.text = element.title
        }

    }

    companion object {
        fun traceItemShow(
            position: Int,
            data: RecommendItemNew,
            index: Int,
            subElement: CommonSubElement,
            view: View,
            recommendCommonItem: RecommendCommonItem
        ) {
            // 新首页-社会化标注-声音/专辑/直播/视频卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(60897)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("positionNew",  (index + 1).toString())
                .put("contentId", (subElement.id ?: 0).toString()) // 客户端传。去重使用
                .put("contentType", subElement.bizType ?: "") // 客户端传。去重使用
                .put("xmRequestId", data.xmRequestId ?: "") // 客户端传，去重用
                .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString())
                .put("modulePosition", (position + 1).toString()) // 客户端传

            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                contentTitle = subElement.title,
                contentPosition = (index + 1).toString()
            )
            trace.createTrace()

            HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, view, index)
        }
    }
}