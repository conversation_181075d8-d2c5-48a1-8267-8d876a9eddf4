package com.ximalaya.ting.android.main.aduve.business.ad.component

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.support.rastermill.Helper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.Animation.AnimationListener
import android.view.animation.AnimationSet
import android.view.animation.TranslateAnimation
import android.widget.*
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.ad.manager.YPlayPrivateGoodsAdManager
import com.ximalaya.ting.android.ad.manager.YPlayPrivateGoodsAdManager.STATUS_AD_DEFAULT
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd
import com.ximalaya.ting.android.ad.uve.UveConstants
import com.ximalaya.ting.android.ad.uve.impl.IUveBehaviorCallback
import com.ximalaya.ting.android.adsdk.XmAdSDK
import com.ximalaya.ting.android.adsdk.adapter.XmNativeAd
import com.ximalaya.ting.android.adsdk.adapter.base.record.ShowRecordManager
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel
import com.ximalaya.ting.android.adsdk.constants.IXmAdConstants
import com.ximalaya.ting.android.adsdk.external.INativeAd
import com.ximalaya.ting.android.adsdk.external.INativeAd.IAdInteractionListener
import com.ximalaya.ting.android.adsdk.model.AdModel
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.Blur
import com.ximalaya.ting.android.framework.util.FileUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IThirdAdStatueCallBack
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.PlayAdFrequencyMonitor
import com.ximalaya.ting.android.host.manager.PlayPageDialogManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.*
import com.ximalaya.ting.android.host.manager.ad.inventory.AdInventoryCollectManager
import com.ximalaya.ting.android.host.manager.ad.videoad.CanPauseCountDownTimer
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.view.ad.AdSourceFromView
import com.ximalaya.ting.android.host.view.image.RatioImageView
import com.ximalaya.ting.android.hybrid.intercept.util.AppVersionUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adModule.manager.AdCloseManager
import com.ximalaya.ting.android.main.aduve.business.ad.imply.IUveAdComponentImpl
import com.ximalaya.ting.android.main.manager.YPlayPageYellowBarManager
import com.ximalaya.ting.android.main.playpage.internalservice.IAdCoverHideService
import com.ximalaya.ting.android.main.playpage.internalservice.IXAdCoverViewService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.manager.YDomainColorUtil
import com.ximalaya.ting.android.main.playpage.playy.XPlayPageStatus
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment
import com.ximalaya.ting.android.main.playpage.playy.component.base.BaseComponentWithPlayStatusListener
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.XAudioPlayCoverAdEngine
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.XPlayAdCoverComponentX
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.polymerizaiton.XAdPolymerizationManager
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsEnum
import com.ximalaya.ting.android.main.playpage.playy.manager.XPlaySoundAdFloatViewManager
import com.ximalaya.ting.android.main.playpage.playy.utils.PlayUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.constants.HttpParamsConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl.PlayMode
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.opensdk.util.NewPlayPageUtil
import com.ximalaya.ting.android.xmutil.Logger
import java.io.File

class YUveAdBannerViewComponentNew() : BaseComponentWithPlayStatusListener(),
    View.OnClickListener,
    XAdPolymerizationManager.PolymerizationViewStatusListener,
    IUveAdComponentImpl<INativeAd> {

    companion object {
        private const val TAG: String = "XUveAdBannerViewComponent"
    }

    private var mAdBannerView: ViewGroup? = null
    private var mAdBannerLayoutBg: ViewGroup? = null
    private var mAdBannerLayout: RelativeLayout? = null
    private var mBannerIcon: RatioImageView? = null
    private var mAdTag: ImageView? = null
    private var mBannerTitle: TextView? = null
    private var mBannerDesc: TextView? = null
    private var mBannerBtn: TextView? = null
    private var mBannerClose: View? = null
    private var mBannerAdSourceFromView: AdSourceFromView? = null

    private var mGifViewLayout: ViewGroup? = null
    private var mGifCover: ImageView? = null
    private var mGifClose: LinearLayout? = null
    private var mGifTimeDown: TextView? = null

    private var isSoundPatchCoverShowing: Boolean? = false

    private var mBannerXmNativeAd: INativeAd? = null // 需要展示的uve下挂
    private var mCurrentBannerXmNativeAd: INativeAd? = null // 正在展示的uve下挂

    private var mSoundPatchBannerAd: IAbstractAd? = null
    private var mCurrentSoundPatchBannerAd: IAbstractAd? = null

    private var mHasYellowBarShow: Boolean = false

    private var isLoadPlayBannerData: Boolean? = false

    private var mCountDownTimer: CanPauseCountDownTimer? = null

    private var mIsPaused: Boolean = false
    private var mCurrentDuringPlay: Boolean = false
    private var mCurrentPlayMethod: Int = 0

    private var isFullScreenModel: Boolean = false

    private var isFirstInitView: Boolean = true

    private var showBannerTrackId = 0L
    private var isCanShowBannerWithPriority: Boolean = false // 通过主站回调的 优先级控制

    private var isPolymerizationViewShowing: Boolean = false // 聚合流是否展示， 如果展示则不能展示下挂

    private var isSoundPatchRequestFinished: Boolean = false //

    private var isPrivateGoodsRequestFinished: Boolean = false

    private var mUveBehaviorCallback: IUveBehaviorCallback? = null

    private val mLayoutParams: FrameLayout.LayoutParams by lazy {
        FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.CENTER
        }
    }

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)
        val vBarView = LayoutInflater.from(mContext).inflate(R.layout.main_layout_y_play_banner_ad_view, null)
        mAdBannerView = vBarView as? ViewGroup

        if (mAdBannerView == null) {
            return
        }
        //添加进UVE管理容器
        YPlayPageYellowBarManager.instance.getAdContainer(mContext).addView(mAdBannerView, mLayoutParams)

        mAdBannerLayoutBg = mAdBannerView?.findViewById(R.id.x_play_ad_banner_layout_bg)
        mAdBannerLayout = mAdBannerView?.findViewById(R.id.x_play_ad_banner_layout)
        mBannerIcon = mAdBannerView?.findViewById(R.id.x_play_ad_banner_icon)
        mAdTag = mAdBannerView?.findViewById(R.id.x_play_ad_banner_tag)
        mBannerTitle = mAdBannerView?.findViewById(R.id.x_play_ad_banner_title)
        mBannerDesc = mAdBannerView?.findViewById(R.id.x_play_ad_banner_desc)
        mBannerBtn = mAdBannerView?.findViewById(R.id.x_play_ad_banner_click_btn)
        mBannerClose = mAdBannerView?.findViewById(R.id.x_play_ad_banner_close_real)
//        if (ConstantsOpenSdk.isDebug) {
//            mBannerClose?.setBackgroundColor(Color.parseColor("#40fd5353"))
//        }
        mGifViewLayout = fragment?.findViewById(R.id.main_xplay_banner_ad_gif_view)

        mGifCover = mGifViewLayout?.findViewById(R.id.x_play_ad_banner_gif_cover)
        mGifClose = mGifViewLayout?.findViewById(R.id.x_play_ad_banner_gif_close)
        mGifTimeDown = mGifViewLayout?.findViewById(R.id.x_play_ad_banner_gif_time_down)
        mBannerAdSourceFromView = mAdBannerView?.findViewById(R.id.x_play_ad_banner_source)
        ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerView)
        ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerLayoutBg)
//        mAdBannerLayoutBg?.setBackgroundResource(R.drawable.host_default_banner_no_data_bg)
        ViewStatusUtil.setVisible(View.INVISIBLE, mAdBannerLayout)
        ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
        ViewStatusUtil.setVisible(View.GONE, mAdTag)
        ViewStatusUtil.setOnClickListener(mAdBannerView, this)
        YPlayPrivateGoodsAdManager.getInstance().registerPrivateGoodsListener(privateGoodsStateEvent)
        isFirstInitView = true
    }

    override fun onResume() {
        super.onResume()
        registerAdCoverStateChangeListener()

        if (mSoundPatchBannerAd != null && mCurrentSoundPatchBannerAd == mSoundPatchBannerAd) {
            if (canShowSoundPatchBanner(mSoundPatchBannerAd?.advertis)) {
                showSoundPatchCoverBanner(mSoundPatchBannerAd) // 优先展示联合下挂
            }
        }

        Logger.i(
            "msg_uve_banner",
            " ---- ---- ---- -- onDialogShowChange  注册 浮层监听"
        )
        PlayPageDialogManager.addXPlayPageDialogShowChangeListener(this)
        // 当前声音id和展示banner时的声音id不一样了(可能在后台切换了声音)，需要移除下挂
        if (showBannerTrackId != getPlayingTrackId()) {
            invisibleBannerViewRelative()
        }

        XAdPolymerizationManager.getInstance().addPolymerizationViewStatusListener(this)

        try {
            if (mFragment is YPlayFragment) {
                val playAdCoverComponent = (mFragment as YPlayFragment).getCoverManager()
                    ?.getMutexCoverComponentByEnum(YCoverComponentsEnum.AD_COMPONENT) as? XPlayAdCoverComponentX
                isSoundPatchCoverShowing =
                    ((playAdCoverComponent as XPlayAdCoverComponentX)?.playAdEngine as XAudioPlayCoverAdEngine).adEngineProvider?.hasAdShowing()
            }
            Logger.i(
                "msg_uve_banner",
                " onResume ---- ---- ---- -- 判断一下 贴片是否还在展示 --》 isSoundPatchCoverShowing = " + isSoundPatchCoverShowing
            )
        } catch (e: Throwable) {
            // 默认关闭
            isSoundPatchCoverShowing = false
            Logger.e(
                "msg_uve_banner",
                " onResume ---- ---- ---- -- 判断一下 贴片是否还在展示 --》出错 isSoundPatchCoverShowing = false ---》 e： " + e.message
            )
            e.printStackTrace()
        }
    }

    override fun onPause() {
        super.onPause()
        isLoadPlayBannerData = false

        Logger.d(
            "msg_uve_banner",
            "onPause"
        )
//        unregisterAdCoverStateChangeListener()
        if (mSoundPatchBannerAd == null && mBannerXmNativeAd == null) {
            resetAllViewRelative()
        }
        Logger.d(
            "msg_uve_banner",
            " ---- ---- ---- -- onDialogShowChange  移除 浮层监听"
        )
        PlayPageDialogManager.removeXPlayPageDialogShowChangeListener(this)
        XAdPolymerizationManager.getInstance().removeStatusListener()
    }

    override fun onDestroy() {
        super.onDestroy()
        resetAllViewRelative()
        unregisterAdCoverStateChangeListener()
        isLoadPlayBannerData = false
        isSoundPatchCoverShowing = false
        if (mSoundPatchBannerAd != null) {
            mSoundPatchBannerAd?.onDestroy()
            mSoundPatchBannerAd = null
        }
        if (mCurrentSoundPatchBannerAd != null) {
            mCurrentSoundPatchBannerAd?.onDestroy()
            mCurrentSoundPatchBannerAd = null
        }

        if (mBannerXmNativeAd != null) {
            mBannerXmNativeAd?.destroy()
            mBannerXmNativeAd = null
        }
        if (mCurrentBannerXmNativeAd != null) {
            mCurrentBannerXmNativeAd?.destroy()
            mCurrentBannerXmNativeAd = null
        }
        if (mAdBannerView != null) {
            ViewStatusUtil.setOnClickListener(mAdBannerView, null)
        }
        YPlayPrivateGoodsAdManager.getInstance().onDestroy()
        Logger.d("msg_uve_banner", " 下挂 请求标识 重置 44444 = " + isLoadPlayBannerData)
    }

    fun showXGBanner(xgNativeAd: INativeAd?, msg: String?) {

        if (mSoundPatchBannerAd != null && mCurrentSoundPatchBannerAd == mSoundPatchBannerAd) {
            if (canShowSoundPatchBanner(mSoundPatchBannerAd?.advertis)) {
                Logger.e(
                    "msg_uve_banner",
                    "------ showXGBanner 正在展示 贴片下挂， 不展示 单独下挂 -- --- -- - --- - "
                )
                return
            }
        }

        if (XPlaySoundAdFloatViewManager.getInstance()
                .isInterceptOtherAdRequest(activity, mIsPaused, mCurrentDuringPlay)
        ) {
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner 声音互动浮层广告 拦截， 不展示 uve banner ---- ---- -- ------------ "
            )
            resetAllViewRelative()
            return
        }
        if (mFragment is YPlayFragment && (mFragment as YPlayFragment).isShowingToListenGuide()) {
            Logger.e(
                "-------msg_uve_banner",
                "------ banner 没展示 待播引导"
            )
            resetAllViewRelative()
            return
        }

        Logger.e(
            "msg_uve_banner",
            "------ showXGBanner  0----- ---- 展示时机  = " + msg
        )

        if (PlayPageInternalServiceManager.getInstance()
                .getService(IXAdCoverViewService::class.java)?.adComponentIsShowing() == true
        ) {
            Logger.e(
                "msg_uve_banner",
                "------ banner 没展示 贴片还在展示"
            )
            isSoundPatchCoverShowing = true
            resetAllViewRelative()
            return
        }

        if (!(isCanShowBannerWithPriority)) {
            Logger.e(
                "msg_uve_banner",
                "------ banner 没展示  isCanShowBannerWithPriority = " + isCanShowBannerWithPriority
            )
            resetAllViewRelative()
            return
        }

        if (isPolymerizationViewShowing || XAdPolymerizationManager.getInstance().isPolymerizationViewShowing) {
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner  下挂 没展示 " + isPolymerizationViewShowing + XAdPolymerizationManager.getInstance().isPolymerizationViewShowing
            )
            resetAllViewRelative()
            return
        }

        if (isFullScreenModel) {
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner播放页 全屏展示， 不展示下挂---- isFullScreenModel = " + isFullScreenModel
            )
            resetAllViewRelative()
            return
        }
        if (isSoundPatchCoverShowing == true) {
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner播放页 ， 贴片正在展示啊， 不展示下挂---- isSoundPatchCoverShowing = " + isSoundPatchCoverShowing
            )
            resetAllViewRelative()
            return
        }
        Logger.d(
            "msg_uve_banner",
            " ---- showXGBanner  - ---- -111111111   -- -- - isSoundPatchRequestFinish =" + isSoundPatchRequestFinished
        )

        if (!isSoundPatchRequestFinished) {
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner播放页 ， 贴片还没请求完成，等待贴片数据，再判断是否展示下挂， 不展示下挂---- isSoundPatchRequestFinish = " + isSoundPatchRequestFinished
            )
//            invisibleBannerViewRelative()
            HandlerManager.postOnUIThreadDelay(showXgBannerDelayRunnable, 1500)
            return
        }

        if (!isPrivateGoodsRequestFinished && YPlayPrivateGoodsAdManager.getInstance()
                .getCurTrackGoodsAdStatus(curTrackId) == STATUS_AD_DEFAULT) {
            isPrivateGoodsRequestFinished = true
            Logger.i(
                "PlayPageCarryGoodsAd",
                "banner数据已经到了，但是带货数据还没有回来，在等其1.5s"
            )
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner播放页 ， 贴片还没请求完成，等待带货组件数据数据，再判断是否展示下挂， 不展示下挂---- isPrivateGoodsRequestFinished = $isSoundPatchRequestFinished"
            )
            HandlerManager.postOnUIThreadDelay(showXgBannerDelayRunnableV2, 1500)
            return
        }

        if (YPlayPrivateGoodsAdManager.getInstance().getCurTrackGoodsAdStatus(curTrackId) == YPlayPrivateGoodsAdManager.AD_STATUS_SHOWING) {
            Logger.e(
                "msg_uve_banner",
                "------ showXGBanner播放页 ， 私域带货是广告蜜声组件， 有数据展示中  不展示下挂----  "
            )
            resetAllViewRelative()
            return
        }

        if (xgNativeAd == null) {
            resetAllViewRelative()
            return
        }

        if (xgNativeAd != null && xgNativeAd == mCurrentBannerXmNativeAd) {
            ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerView)
            ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerLayout)
//            mAdBannerLayoutBg?.background = null
            Logger.d(
                "msg_uve_banner",
                "------ showXGBanner 展示下挂---- 下挂数据已经被渲染过， 直接展示， 不需要重新渲染"
            )
            YPlayPageYellowBarManager.instance.notifyHeightChanged()
            return
        }

        ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerLayoutBg)
//        mAdBannerLayoutBg?.setBackgroundResource(R.drawable.host_default_banner_no_data_bg)
        ViewStatusUtil.setVisible(View.INVISIBLE, mAdBannerLayout)

        mCurrentBannerXmNativeAd = xgNativeAd

        Logger.w("msg_uve_banner", " =--- -=-= = 展示 下挂 showXGBanner ， 类型 = " + msg)
        YPlayPrivateGoodsAdManager.getInstance().onAdBannerShow(1,getPlayingTrackId())
        if (xgNativeAd != null) {

            Logger.w(
                "msg_uve_banner",
                " color -----  展示 下挂 showXGBanner 是否是视频模式： " + PlayUtil.isVideoPage())
            try {
                    if (PlayUtil.isVideoPage()) {
                        Logger.w(
                            "msg_uve_banner",
                            " color -----  展示 下挂 showXGBanner 视频模式： -- ffffff "
                        )
                        setBannerViewToVideoPageMode()
                    } else {
                        Logger.w(
                            "msg_uve_banner",
                            " color -----  展示 下挂 showXGBanner 不是视频模式： -- 其他颜色 "
                        )
                        setBannerViewToNormalMode()
                    }
            } catch (e: Throwable) {
                e.printStackTrace()
            }

            val clickView: MutableList<View> = ArrayList()
            clickView.add(mAdBannerLayout!!)

            try {
                (xgNativeAd.adModel as AdModel)!!.isCanRecordToShow = false
            } catch (e: Exception) {
                e.printStackTrace()
            }
            var layoutParams: FrameLayout.LayoutParams? = null
            if (AdSDKManager.isSDKAd(xgNativeAd)) {
                if (AdSDKManager.isGDTAd(xgNativeAd)) {
                    ViewStatusUtil.setVisible(View.VISIBLE, mAdTag)
                    mAdTag?.setImageResource(R.drawable.host_gdt_ad_tag)
                } else if (AdSDKManager.isBaiDuAd(xgNativeAd)){
                    ViewStatusUtil.setVisible(View.VISIBLE, mAdTag)
                    mAdTag?.setImageResource(R.drawable.host_baidu_ad_tag_withe)
                } else {
                    mAdTag?.setImageResource(R.drawable.host_csj_ad_tag_small_new)
                    ViewStatusUtil.setVisible(View.VISIBLE, mAdTag)
                }
                layoutParams = AdManager.createCustomLayoutParamsForGdt()
                layoutParams.height = BaseUtil.dp2px(context, 0f)
                layoutParams.width = BaseUtil.dp2px(context, 0f)
            } else {
                ViewStatusUtil.setVisible(View.GONE, mAdTag)
            }
            AdSDKManager.bindSourceFromAndAdTag(xgNativeAd, null, -1, mBannerAdSourceFromView, AppConstants.AD_POSITION_NAME_SOUND_PATCH_PLAY_BANNER)
            xgNativeAd.bindAdToView(
                mAdBannerView!!,
                clickView,
                layoutParams,
                mBannerClose,
                object : IAdInteractionListener {
                    override fun onAdClicked(view: View?, iNativeAd: INativeAd, b: Boolean) {
                        Logger.d(
                            "msg_uve_banner",
                            " 非混合样式 banner  onAdClicked --== === = " + iNativeAd
                        )
                        if (mUveBehaviorCallback != null) {
                            val params: MutableMap<String, String> = HashMap()
                            try {
                                params["plan"] = (iNativeAd?.adModel as AdModel).planId.toString()
                                params["material"] = (iNativeAd?.adModel as AdModel).adid.toString()
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            mUveBehaviorCallback!!.onClick(params)
                        }
                    }

                    override fun onAdShow(iNativeAd: INativeAd) {
                        Logger.d(
                            "msg_uve_banner",
                            " 非混合样式 banner  onAdShow --== === = " + iNativeAd
                        )
                    }

                    override fun onADStatusChanged(iNativeAd: INativeAd) {}

                    override fun onAdClose(closeEvent: Int) {
                        if (mUveBehaviorCallback != null) {
                            val params: MutableMap<String, String> = HashMap()
                            try {
                                params["plan"] = (xgNativeAd?.adModel as AdModel).planId.toString()
                                params["material"] = (xgNativeAd?.adModel as AdModel).adid.toString()
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            mUveBehaviorCallback!!.onClose(true, params)
                        }
                        ViewStatusUtil.setVisible(View.GONE, mAdBannerView)
                        YPlayPageYellowBarManager.instance.notifyHeightChanged()
                        if (xgNativeAd != null) {
                            try {
                                (xgNativeAd.adModel as AdModel)?.isRecorded = false
                                val builder = SDKAdReportModel.Builder(
                                    AppConstants.AD_LOG_TYPE_SOUND_TINGCLOSE,
                                    (xgNativeAd.adModel as AdModel)?.positionName.toString()
                                ).showStyle((xgNativeAd.adModel as AdModel)?.showstyle.toString())

                                ShowRecordManager.getInstance()
                                    .showRecord((xgNativeAd.adModel as AdModel), builder.build())
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            xgNativeAd.destroy()
                        }
                        Logger.d("msg_uve_banner", " onAdClose " + mBannerXmNativeAd)

                        if (mBannerXmNativeAd != null) {
                            mBannerXmNativeAd?.destroy()
                            mBannerXmNativeAd = null
                        }
                        if (mCurrentBannerXmNativeAd != null) {
                            mCurrentBannerXmNativeAd?.destroy()
                            mCurrentBannerXmNativeAd = null
                        }
                    }
                })
            var titleStr: String = ""
            var descStr: String = ""
            if (AdSDKManager.isSDKAd(xgNativeAd)) {
                try {
                    titleStr = xgNativeAd.desc + ""
                    val adModel: AdModel = (xgNativeAd as XmNativeAd).adModel
//                    if (AdManager.isOperationAd(adModel.adUserType)) {
//                        descStr = "推广 · " + xgNativeAd.title + ""
//                    } else {
//                        descStr = "广告 · " + xgNativeAd.title + ""
//                    }
                    if (adModel != null && !isEmptyOrNull(adModel.adTextMark)) {
                        descStr = adModel.adTextMark + " · " + xgNativeAd.title + ""
                    } else {
                        descStr = xgNativeAd.title + ""
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    titleStr = xgNativeAd.title + ""
                    descStr = "广告 · " + xgNativeAd.desc + ""
                }
            } else {
                try {
                    titleStr = xgNativeAd.title + ""
                    val adModel: AdModel = (xgNativeAd as XmNativeAd).adModel
//                    if (AdManager.isOperationAd(adModel.adUserType)) {
//                        descStr = "推广 · " + xgNativeAd.desc + ""
//                    } else {
//                        descStr = "广告 · " + xgNativeAd.desc + ""
//                    }

                    if (adModel != null && !isEmptyOrNull(adModel.adTextMark)) {
                        descStr = adModel.adTextMark + " · " + xgNativeAd.desc + ""
                    } else {
                        descStr = xgNativeAd.desc + ""
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    titleStr = xgNativeAd.title + ""
                    descStr = xgNativeAd.desc + ""
                }
            }
            var buttonText: String = "查看详情"
            try {
                if (TextUtils.isEmpty(xgNativeAd.buttonText)) {
                    if (xgNativeAd.adSDKAdapterModel != null) {
                        if (TextUtils.isEmpty(xgNativeAd.adSDKAdapterModel.getClickTitle())) {
                            buttonText = xgNativeAd.adSDKAdapterModel.getButtonText()
                        } else {
                            buttonText = xgNativeAd.adSDKAdapterModel.getClickTitle()
                        }
                    }
                } else {
                    buttonText = xgNativeAd.buttonText + ""
                }
            } catch (thr: Exception) {
                thr.printStackTrace()
                buttonText = "查看详情"
            }

            showBannerAdView(
                xgNativeAd.cover + "",
                titleStr,
                descStr,
                buttonText
            )

            try {
                Logger.d(
                    "msg_uve_banner",
                    " 非混合样式 banner  手动000000 onAdShow --== === = " + xgNativeAd
                )
                (xgNativeAd.adModel as AdModel)?.isRecorded = false
                val builder = SDKAdReportModel.Builder(
                    IXmAdConstants.IAdLogType.AD_LOG_TYPE_SITE_SHOW,
                    (xgNativeAd.adModel as AdModel)?.positionName.toString()
                )
                    .showStyle((xgNativeAd.adModel as AdModel)?.showstyle.toString())

                builder.newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                ShowRecordManager.getInstance()
                    .showRecord((xgNativeAd.adModel as AdModel), builder.build())
                (xgNativeAd.adModel as AdModel)?.isRecorded = true

                PlayAdFrequencyMonitor.onPlayBannerShow(xgNativeAd, (xgNativeAd.adModel as AdModel))
            } catch (e: Exception) {
                e.printStackTrace()
            }

            if (mUveBehaviorCallback != null) {
                val params: MutableMap<String, String> = HashMap()
                try {
                    params["plan"] = (xgNativeAd.adModel as AdModel).planId.toString()
                    params["material"] = (xgNativeAd.adModel as AdModel).adid.toString()
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                mUveBehaviorCallback!!.onShow(params)
            }

            if (xgNativeAd is XmNativeAd && xgNativeAd?.adModel != null
                && (xgNativeAd.adModel as AdModel)?.showstyle == Advertis.IMG_SHOW_STYLE_PLAY_BANNER_VIEW_BOTTOM_GIF
            ) {
                showGifFloatView(xgNativeAd)
            }
        }
    }

    private fun isEmptyOrNull(adTextMark: String?): Boolean {
        try {
            return adTextMark == null || adTextMark.isNullOrEmpty() || adTextMark.isNullOrBlank() || adTextMark.isEmpty() || adTextMark.isBlank() || "null".equals(adTextMark, true)
        } catch (thr: Throwable) {
            thr.printStackTrace()
        }
       return true
    }

    private fun setBannerViewToNormalMode() {
        try {
            mAdBannerLayout!!.setBackgroundResource(R.drawable.main_bg_rect_a0ffffff_radius_6)
            mBannerTitle?.setTextColor(ContextCompat.getColor(context, R.color.main_color_e6ffffff))
            mBannerDesc?.setTextColor(ContextCompat.getColor(context, R.color.main_color_80ffffff))
        } catch (thr: Throwable) {
            thr.printStackTrace()
        }
    }

    private fun setBannerViewToVideoPageMode() {
        try {
            mAdBannerLayout?.setBackgroundColor(Color.parseColor("#0AFFFFFF"))
            mBannerTitle?.setTextColor(Color.parseColor("#e6FFFFFF"))
            mBannerDesc?.setTextColor(Color.parseColor("#80FFFFFF"))
        } catch (thr: Throwable) {
            thr.printStackTrace()
        }
    }

    private fun showGifFloatView(xgAd: INativeAd?) {
        if (xgAd == null) {
            return
        }

        if (!(isCanShowBannerWithPriority)) {
            Logger.e(
                "msg_uve_banner",
                "------ banner 没展示 " + isCanShowBannerWithPriority
            )
            ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
            return
        }
        if (PlayPageDialogManager.hasDialogShowIngOnPlayPage()) {
            Logger.e(
                "msg_uve_banner",
                "------ showGifFloatView 播放页有其他 浮层展示， gif 不展示 "
            )
            ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
            return
        }

        if (isPolymerizationViewShowing || XAdPolymerizationManager.getInstance().isPolymerizationViewShowing) {
            Logger.e(
                "msg_uve_banner",
                "------ showGifFloatView  gif 不展示 " + isPolymerizationViewShowing + XAdPolymerizationManager.getInstance().isPolymerizationViewShowing
            )
            ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
            return
        }

        if (isFullScreenModel) {
            Logger.e(
                "msg_uve_banner",
                "------ showGifFloatView 全屏展示， 不展示悬浮---- isFullScreenModel = " + isFullScreenModel
            )
            ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
            return
        }

        if (BaseUtil.isCollapsibleScreenOnPortraitExpandMode(context)) {
            Logger.e(
                "msg_uve_banner",
                "------ 折叠屏， 不展示gif view = "
            )
            ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
            return
        }

        val adModel = (xgAd as XmNativeAd).adModel

        gifBannerFadeIn(mGifViewLayout)

        mGifClose?.setOnClickListener(this)
        mGifCover?.setOnClickListener(this)

        startCountDown(adModel)

        val layoutParams: ViewGroup.LayoutParams = mGifClose?.getLayoutParams()!!
        if (layoutParams is MarginLayoutParams) {
            layoutParams.topMargin = BaseUtil.dp2px(context, 8f)
            layoutParams.bottomMargin = 0
            layoutParams.leftMargin = 0
            layoutParams.rightMargin = BaseUtil.dp2px(context, 16f)
            mGifClose?.setLayoutParams(layoutParams)
        }

        Logger.d(
            "msg_uve_banner",
            " gif 图链接 = " + adModel?.dynamicImage
        )

        ImageManager.from(context).downloadBitmap(
            adModel?.dynamicImage, null,
            ImageManager.DisplayCallback { lastUrl, bitmap ->
                if (!TextUtils.isEmpty(lastUrl) && File(lastUrl).exists()) {
                    Helper.fromPath(lastUrl,
                        Helper.LoadCallback { frameSequenceDrawable ->
                            if (frameSequenceDrawable == null) {
                                FileUtil.deleteDir(lastUrl)
                                ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
                            } else {
                                frameSequenceDrawable.setScaleType(mGifCover?.getScaleType())
                                mGifCover?.setImageDrawable(frameSequenceDrawable)
                            }
                        })
                } else {
                    Logger.d(
                        "msg_uve_banner",
                        " 打底图  - backupCover = " + adModel?.backupCover
                    )
                    ImageManager.from(context)
                        .displayImage(
                            mGifCover,
                            adModel?.backupCover,
                            R.drawable.host_default_focus_img_use9
                        )
                }
            }, false
        )

        try {
            Logger.w(
                "msg_uve_banner",
                " 非混合样式 banner  gif onAdShow --== === = " + xgAd
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("WrongConstant")
    fun showSoundPatchCoverBanner(
        soundPatchAbstract: IAbstractAd?
    ) {
        Logger.d(
            "msg_uve_banner",
            "------ showSoundPatchCoverBanner ------ isCanShowBannerWithPriority = " + isCanShowBannerWithPriority
        )
        if (!(isCanShowBannerWithPriority)) {
            Logger.e(
                "msg_uve_banner",
                "------ banner 没展示  -- isCanShowBannerWithPriority" + isCanShowBannerWithPriority
            )
            resetAllViewRelative()
            return
        }
        if (soundPatchAbstract == null) {
            resetAllViewRelative()
            return
        }

        if (isPolymerizationViewShowing || XAdPolymerizationManager.getInstance().isPolymerizationViewShowing) {
            Logger.e(
                "msg_uve_banner",
                "------ showSoundPatchCoverBanner  banner 没展示 " + isPolymerizationViewShowing + XAdPolymerizationManager.getInstance().isPolymerizationViewShowing
            )
            resetAllViewRelative()
            return
        }

        if (isFullScreenModel) {
            Logger.e(
                "msg_uve_banner",
                "------  showSoundPatchCoverBanner  banner 没展示 ， 因为现在全屏 ---- isFullScreenModel = " + isFullScreenModel
            )
            resetAllViewRelative()
            return
        }

        if (mHasYellowBarShow) {
            Logger.e(
                    "msg_uve_banner",
                    "------  showSoundPatchCoverBanner  banner 没展示 ， 因为现在有商业化小黄条需要展示 ")
            resetAllViewRelative()
            return
        }

        if (mSoundPatchBannerAd != null && mCurrentSoundPatchBannerAd == soundPatchAbstract) {
            ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerView)
            YPlayPageYellowBarManager.instance.notifyHeightChanged()
//            mAdBannerLayoutBg?.background = null
            ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerLayout)
            Logger.d(
                "msg_uve_banner",
                "------ showSoundPatchCoverBanner 展示下挂---- 下挂数据已经被渲染过， 直接展示， 不需要重新渲染"
            )
            return
        }

        if (AdManager.isThirdAd(soundPatchAbstract)) {
            Logger.e(
                "msg_uve_banner",
                "------  showSoundPatchCoverBanner  banner 没展示 ， 这是三方sdk 物料， 不展示贴片下挂"
            )
            resetAllViewRelative()
            return
        }

        Logger.d(
            "msg_uve_banner",
            " ----  贴片和下挂一起出 - , soundPatchAdvertis = " + soundPatchAbstract
        )
        ViewStatusUtil.setVisible(View.GONE, mAdBannerView)
        var soundPatchAdvertis: Advertis? = soundPatchAbstract?.advertis

        mAdBannerLayout?.setOnClickListener(this)
//        mBannerBtn?.setOnClickListener(this)
        mBannerClose?.setOnClickListener(this)
        mSoundPatchBannerAd = soundPatchAbstract
        mCurrentSoundPatchBannerAd = mSoundPatchBannerAd

        var imageUrlStr: String = ""
        var adTitleStr: String = ""
        var adDescStr: String = ""
        var adButtonText: String = ""

        mBannerAdSourceFromView?.setAdvertis(soundPatchAdvertis)
        if (AdManager.isThirdAd(soundPatchAbstract)) {
            Logger.w(
                "msg_uve_banner",
                " showSoundPatchCoverBanner - sdk 物料， 展示 sdk 信息 -------"
            )
            imageUrlStr =
                if (TextUtils.isEmpty(soundPatchAbstract.adIcon + "")) soundPatchAbstract?.imgUrl + "" else soundPatchAbstract.adIcon + ""

            adTitleStr = soundPatchAbstract.desc + ""

            if (AdManager.isCSJAd(soundPatchAdvertis)) {
                adDescStr = "穿山甲"
            } else if (AdManager.isGdtAd(soundPatchAdvertis)) {
                adDescStr = "广点通"
            } else if (AdManager.isJadAd(soundPatchAdvertis)) {
                adDescStr = "京东"
            } else if (AdManager.isBaiduAd(soundPatchAdvertis)) {
                adDescStr = "百度"
            } else {
                adDescStr = soundPatchAbstract?.title + ""
            }

            adDescStr = "广告 · " + adDescStr
            adButtonText = soundPatchAbstract?.buttonText + ""

            ViewStatusUtil.setVisible(View.VISIBLE, mAdTag)

            val layoutParams: FrameLayout.LayoutParams = AdManager.createCustomLayoutParamsForGdt()
            layoutParams.gravity = Gravity.TOP or Gravity.LEFT
            layoutParams.leftMargin = BaseUtil.dp2px(context, 4f)
            layoutParams.topMargin = BaseUtil.dp2px(context, 12f)
            layoutParams.height = BaseUtil.dp2px(context, 4f)

            val clickView: MutableList<View> = ArrayList()
            clickView.add(mAdBannerLayout!!)

            soundPatchAbstract.setAdMark(mAdTag, R.drawable.host_ad_tag_style_2)

            soundPatchAbstract.bindAdToView(
                context, mAdBannerView, clickView, layoutParams, null,
                object : IThirdAdStatueCallBack {
                    override fun onADExposed() {
                    }

                    override fun onADClicked() {
                        Logger.w(
                            "msg_uve_banner",
                            " showSoundPatchCoverBanner  --- onADClicked - 三方sdk物料点击了"
                        )
                        val clickReportModel = AdReportModel.Builder(
                            AppConstants.AD_LOG_TYPE_BANNER_BANNER_CLIKCK,
                            AdPositionIdManager.getPositionNameByPositionId(mSoundPatchBannerAd?.advertis?.adPositionId)
                        ).build()

                        AdManager.handlerAdClick(
                            context, mSoundPatchBannerAd?.advertis,
                            clickReportModel
                        )
                    }

                    override fun onADError(code: Int, msg: String?) {
                    }

                    override fun onADStatusChanged() {
                    }
                }
            )
        } else {
            Logger.w("msg_uve_banner", " showSoundPatchCoverBanner - 喜马物料 ---- -- - --")
            // 喜马物料
            if (soundPatchAdvertis?.playBannerInfo == null) {
                Logger.e(
                    "msg_uve_banner",
                    " showSoundPatchCoverBanner ---- 喜马物料里 没有下挂物料信息， 不展示 贴片下挂"
                )
                resetAllViewRelative()
                return
            }
            ViewStatusUtil.setVisible(View.GONE, mAdTag)
            try {
                imageUrlStr = soundPatchAdvertis?.playBannerInfo?.playBannerImage + ""
                adTitleStr = soundPatchAdvertis?.playBannerInfo?.playBannerTitle + ""

                if (AdManager.isOperationAd(soundPatchAdvertis)) {
                    adDescStr = "推广 · " + soundPatchAdvertis?.playBannerInfo?.playBannerSubTitle
                } else {
                    adDescStr = "广告 · " + soundPatchAdvertis?.playBannerInfo?.playBannerSubTitle
                }
                adButtonText = soundPatchAdvertis?.playBannerInfo?.playBannerButtonText + ""
            } catch (e: Exception) {
                e.printStackTrace()
                adDescStr = "广告 · " + soundPatchAdvertis?.playBannerInfo?.playBannerSubTitle
            }
        }

        AdCloseManager.bindAdClose(null,
            mBannerClose,
            soundPatchAbstract,
            soundPatchAdvertis,
            AppConstants.AD_POSITION_NAME_SOUND_PATCH_BANNER,
            false,
            AdCloseManager.IAdCloseCallBack {
                ViewStatusUtil.setVisible(View.GONE, mAdBannerView)
                YPlayPageYellowBarManager.instance.notifyHeightChanged()
                if (mSoundPatchBannerAd != null) {
                    val reportModel = AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_BANNER_BANNER_CLOSE,
                        AppConstants.AD_POSITION_NAME_SOUND_PATCH
                    ).build()

                    AdManager.adRecord(context, soundPatchAdvertis, reportModel)
                }
                mSoundPatchBannerAd = null
                mCurrentSoundPatchBannerAd = null
            })

        showBannerAdView(
            imageUrlStr,
            adTitleStr,
            adDescStr,
            adButtonText
        )
        Logger.d("msg_uve_banner", " ---- ---- ---- -- 当前物料需要展示banner 广告")
    }

    fun showBannerAdView(imageUrl: String, title: String, desc: String, btnCon: String) {
        Logger.d("msg_uve_banner", "------ 开始展示banner imageUrl ---- " + imageUrl)
        Logger.d("msg_uve_banner", "------ 开始展示banner view ---- " + title + btnCon)
        showBannerTrackId = getPlayingTrackId()
        ImageManager.from(context).displayImage(
            mBannerIcon, imageUrl,
            R.drawable.host_default_cover_album_light
        ) { lastUrl, bitmap ->
            if (bitmap != null) {
                Logger.i("msg_uve_banner", "------ 开始展示banner  bitmap != null ---- ")
                mBannerIcon?.setImageBitmap(bitmap)
                mBannerIcon?.setScaleType(ImageView.ScaleType.FIT_CENTER) // 设置图片样式 居中展示
                mBannerIcon?.setBackgroundDrawable(
                    BitmapDrawable(
                        Blur.fastBlur(
                            context,
                            bitmap,
                            30,
                            15
                        )
                    )
                ) // image  背景设置 渲染图
            } else {
                Logger.i("msg_uve_banner", "------ 开始展示banner  bitmap == null ----  背景清空")
                mBannerIcon?.setBackgroundDrawable(null)
            }
        }

        if (!TextUtils.isEmpty(title)) {
            mBannerTitle?.text = title
        }

        if (!TextUtils.isEmpty(desc)) {
            mBannerDesc?.text = desc
            ViewStatusUtil.setVisible(View.VISIBLE, mBannerDesc)
        } else {
            ViewStatusUtil.setVisible(View.INVISIBLE, mBannerDesc)
        }

        if (!TextUtils.isEmpty(btnCon) && !"null".equals(btnCon)) {
            mBannerBtn?.text = btnCon
        } else {
            mBannerBtn?.text = "查看详情"
        }
        ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerView)
//        mAdBannerLayoutBg?.background = null
        ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerLayout)
        YPlayPageYellowBarManager.instance.notifyHeightChanged()
        Logger.d("msg_uve_banner", " 下挂 请求标识 重置11111 = " + isLoadPlayBannerData)
        isLoadPlayBannerData = false
    }

    fun getPlayingTrackId(): Long {
        if (BaseUtil.isMainProcess(context)) {
            return PlayTools.getCurTrackId(context)
        } else {
            val playerSrvice = XmPlayerService.getPlayerSrvice()
            if (playerSrvice != null) {
                val currPlayModel = playerSrvice.currPlayModel
                if (currPlayModel is Track) {
                    return currPlayModel.getDataId()
                }
            }
            return 0
        }
    }

    private fun startCountDown(adModel: AdModel?) {
        if (mCountDownTimer != null) {
            mCountDownTimer?.cancel()
            mCountDownTimer = null
        }

        var timeCost: Long? = 0
        try {
            Logger.d(
                "msg",
                " ---- startCountDown  timeCost 111111 = " + adModel?.videoDurationTime
            )
            timeCost = adModel?.videoDurationTime?.toLong()
        } catch (e: Exception) {
            e.printStackTrace()
            Logger.e("msg", " ---- startCountDown  = " + e.toString())
        }
        if (timeCost!! <= 0) {
            timeCost = 5L
        }
        Logger.d("msg", " ---- startCountDown  timeCost = " + timeCost)
        mCountDownTimer = object : CanPauseCountDownTimer(timeCost * 1000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                if (mGifTimeDown != null) {
                    var l = fixCountDown(millisUntilFinished).toLong()
                    if (l <= 0) {
                        l = 1
                    }
                    var content = "" + l
                    if (l < 10) {
                        content = "0$content"
                    }
                    if (mGifTimeDown != null) {
                        mGifTimeDown?.setText(content)
                    }
                }
            }

            override fun onFinish() {
                gifBannerFadeOut(mGifViewLayout)
            }
        }
        (mCountDownTimer as CanPauseCountDownTimer).start()
    }

    fun fixCountDown(millisUntilFinished: Long): Int {
        return if (millisUntilFinished % 1000 > 500) {
            (millisUntilFinished / 1000 + 1).toInt()
        } else (millisUntilFinished / 1000).toInt()
    }

    fun canShowSoundPatchBanner(advertis: Advertis?): Boolean {
        if (advertis != null && (advertis?.soundType!! in 35..36 || advertis?.soundType!! in 84..85)) {
            return true
        }
        return false
    }

    // 隐藏非贴片下挂
    private fun invisibleBannerViewRelative() {
        try {
            if (mAdBannerView != null && mAdBannerView?.visibility == View.VISIBLE) {

                if (canShowSoundPatchBanner(mCurrentSoundPatchBannerAd?.advertis)) {
                    Logger.e(
                        "msg_uve_banner",
                        "------ invisibleBannerViewRelative 222222 正在展示 贴片下挂， 不隐藏下挂view -- --- -- - --- - "
                    )
                    if (!isCanShowBannerWithPriority) {
                        Logger.e(
                            "msg_uve_banner",
                            "------ invisibleBannerViewRelative  3333333 正在展示 贴片下挂， 但是失去了 优先级， 隐藏掉下挂banner - "
                        )
                        resetAllViewRelative()
                    }
                    return
                }
                Logger.i("msg_uve_banner", " invisibleBannerViewRelative , visibility = true")
                ViewStatusUtil.setVisible(View.VISIBLE, mAdBannerView)
//                ViewStatusUtil.setVisible(View.INVISIBLE, mAdBannerLayout)
//                if (mAdBannerLayoutBg != null) {
//                    mAdBannerLayoutBg?.setBackgroundResource(R.drawable.host_default_banner_no_data_bg)
//                }
                ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
                YPlayPageYellowBarManager.instance.notifyHeightChanged()
            } else {
                Logger.w("msg_uve_banner_close", " invisibleBannerViewRelative , visibility = false")
                resetAllViewRelative()
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    // 隐藏所有下挂相关的广告
    fun resetAllViewRelative() {
        ViewStatusUtil.setVisible(View.GONE, mAdBannerView)
        ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
        YPlayPageYellowBarManager.instance.notifyHeightChanged()
    }

    /** ------- 贴片状态回调 ------- **/
    var mHasRegisterAdCoverChangeListener = false

    fun registerAdCoverStateChangeListener() {

        if (mHasRegisterAdCoverChangeListener) {
            return
        }
        val adCoverHideService =
            PlayPageInternalServiceManager.getInstance().getService(IAdCoverHideService::class.java)

        if (adCoverHideService != null) {
            adCoverHideService.registerAdCoverStateChange(mAdCoverStateChangeListener)
            mHasRegisterAdCoverChangeListener = true
        }
    }

    private val showXgBannerDelayRunnable: Runnable = Runnable {
        isSoundPatchRequestFinished = true
        Logger.d(
            "msg_uve_banner",
            " ---- showXgBannerDelayRunnable - ---- -111111111  - -- - isSoundPatchRequestFinish =  " + isSoundPatchRequestFinished
        )
        Logger.d("msg_uve_banner", " ------  showXgBannerDelayRunnable run ---- 延时保底展示下挂 ")
        showXGBanner(mBannerXmNativeAd, "--- runnable 延时保底展示下挂 ---- ")
    }

    private val showXgBannerDelayRunnableV2: Runnable = Runnable {
        Logger.i("msg_uve_banner", " ------  showXgBannerDelayRunnableV2 run 带货组件runnable 开始执行---- 延时保底展示下挂 ")
        showXGBanner(mBannerXmNativeAd, "--- runnable 延时保底展示下挂 ---- ")
    }


    private fun unregisterAdCoverStateChangeListener() {
        if (mAdCoverStateChangeListener == null) {
            return
        }
        val adCoverHideService = PlayPageInternalServiceManager.getInstance().getService(
            IAdCoverHideService::class.java
        )
        adCoverHideService?.unregisterAdCoverStateChange(mAdCoverStateChangeListener)
        mHasRegisterAdCoverChangeListener = false
    }

    private val mAdCoverStateChangeListener =
        object : IAdCoverHideService.XIAdCoverStateChangeListenerExtern {
            override fun onAdCoverHide() {
                isSoundPatchRequestFinished = true
                HandlerManager.removeCallbacks(showXgBannerDelayRunnable)
                Logger.d(
                    "msg_uve_banner",
                    " ---- onAdCoverHide - ---- -111111111  - -- - -- -- - isSoundPatchRequestFinish = " + isSoundPatchRequestFinished
                )
                if (isSoundPatchCoverShowing == true) {
                    isSoundPatchCoverShowing = false
                    Logger.d(
                        "msg_uve_banner",
                        " ---- onAdCoverHide - ---- -111111111 " + mBannerXmNativeAd
                    )

                    if (mBannerXmNativeAd != null) {
//                        showXGBanner(mBannerXmNativeAd, " onAdCoverHide 贴片自动关闭了，展示下挂")
                        HandlerManager.postOnUIThreadDelay({
                            showXGBanner(
                                mBannerXmNativeAd,
                                " onAdCoverHide 贴片自动关闭了，展示下挂"
                            )
                        }, 120)
                    }
                }
            }

            override fun noAdCover() {
                isSoundPatchRequestFinished = true
                HandlerManager.removeCallbacks(showXgBannerDelayRunnable)
                Logger.d(
                    "msg_uve_banner",
                    " ---- noAdCover - ---- -111111111  -- - -- -- - isSoundPatchRequestFinish =  " + isSoundPatchRequestFinished
                )
                isSoundPatchCoverShowing = false
//                showBannerAdView("noAdCover", "", "", "")
//                Logger.d("msg_uve_banner", " ---- noAdCover 没有贴片， 下挂 单独出 - ")
                Logger.d("msg_uve_banner", " ---- noAdCover 没有贴片， 下挂 单独出 - ")
                if (mBannerXmNativeAd != null) {
                    showXGBanner(mBannerXmNativeAd, "noAdCover 没有贴片， 下挂 单独出")
                }
            }

            override fun onUserClose() {
                isSoundPatchRequestFinished = true
                HandlerManager.removeCallbacks(showXgBannerDelayRunnable)
                Logger.d(
                    "msg_uve_banner",
                    " ---- onUserClose - ---- -111111111   -- -- - isSoundPatchRequestFinish =  " + isSoundPatchRequestFinished
                )
                isSoundPatchCoverShowing = false
                if (mBannerXmNativeAd != null) {
                    showXGBanner(mBannerXmNativeAd, " onUserClose 用户关闭贴片 ，展示下挂")
                }
            }

            override fun onAdCoverShow() {
                isSoundPatchRequestFinished = true
                HandlerManager.removeCallbacks(showXgBannerDelayRunnable)
                Logger.d(
                    "msg_uve_banner",
                    " ---- onAdCoverShow - ---- -111111111   - -- -- -  isSoundPatchRequestFinish = " + isSoundPatchRequestFinished
                )
                isSoundPatchCoverShowing = true
            }

            override fun onAdCoverShowWithAdInfo(abstractAd: IAbstractAd?) {
                isSoundPatchRequestFinished = true
                HandlerManager.removeCallbacks(showXgBannerDelayRunnable)
                Logger.d(
                    "msg_uve_banner",
                    " ---- onAdCoverShowWithAdInfo - ---- -111111111  -- - -- -- - isSoundPatchRequestFinish =  " + isSoundPatchRequestFinished
                )
                isSoundPatchCoverShowing = true
                Logger.d(
                    "msg_uve_banner",
                    " ---- onAdCoverShowWithAdInfo 贴片信息返回了 "
                )

                if (abstractAd?.advertis != null && abstractAd.advertis != null && mSoundPatchBannerAd != abstractAd.advertis
                    && canShowSoundPatchBanner(abstractAd?.advertis)
                ) {
                    mSoundPatchBannerAd = abstractAd
                    if (isFullScreenModel) {
                        Logger.d(
                            "msg_uve_banner",
                            " ---- showSoundPatchCoverBanner 贴片返回了 下挂 ， 但是现在是全屏 播放页， 暂存数据， 等切换半屏再展示 - "
                        )
                    } else {
                        Logger.d(
                            "msg_uve_banner",
                            " ---- showSoundPatchCoverBanner 贴片返回了 下挂 ，展示 下挂 - "
                        )
                        showSoundPatchCoverBanner(abstractAd)
                    }
                    Logger.d(
                        "msg_uve_banner",
                        " ---- showSoundPatchCoverBanner 贴片返回了 下挂 ， 不请求 单独下挂接口 - "
                    )
                    ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
                } else {
                    resetAllViewRelative()
                    Logger.d(
                        "msg_uve_banner",
                        " ----  贴片信息返回了,贴片不带下挂 ，等贴片关闭后，可以展示单独下挂 "
                    )
                }
            }
        }

    private val privateGoodsStateEvent = YPlayPrivateGoodsAdManager.PrivateGoodsStateEvent { id ->
        Logger.i("PlayPageCarryGoodsAd", "onAdDataSuccess: $curTrackId ----  $id")
        if (mBannerXmNativeAd != null && id == curTrackId) {
            isPrivateGoodsRequestFinished = true
            HandlerManager.postOnUIThread {
                showXGBanner(mBannerXmNativeAd, " onUserClose 用户关闭贴片 ，展示下挂")
            }
        }
        HandlerManager.removeCallbacks(showXgBannerDelayRunnableV2)
    }

    @SuppressLint("WrongConstant")
    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        when (v?.id) {
            R.id.x_play_ad_banner_layout -> {
                if (AdManager.isThirdAd(mSoundPatchBannerAd)) {
                    return
                }
                if (mSoundPatchBannerAd != null && mSoundPatchBannerAd?.advertis != null) {

                    val clickReportModel = AdReportModel.Builder(
                        AppConstants.AD_LOG_TYPE_BANNER_BANNER_CLIKCK,
                        AdPositionIdManager.getPositionNameByPositionId(mSoundPatchBannerAd?.advertis?.adPositionId)
                    ).newPlayPageVersion(NewPlayPageUtil.getNewPageParams()).build()

                    AdManager.handlerAdClick(
                        context, mSoundPatchBannerAd?.advertis,
                        clickReportModel
                    )
                }
            }

            /***
             * gif  点击事件
             */
            R.id.x_play_ad_banner_gif_close -> {

                Logger.d(
                    "msg_uve_banner",
                    "  GIF  关闭 --== === = "
                )
                gifBannerFadeOut(mGifViewLayout)

                try {
                    val builder = SDKAdReportModel.Builder(
                        AppConstants.AD_LOG_TYPE_BANNER_GIF_CLOSE,
                        (mBannerXmNativeAd as XmNativeAd).adData?.positionName.toString()
                    )
                        .showStyle((mBannerXmNativeAd as XmNativeAd).adData?.showstyle.toString())

                    ShowRecordManager.getInstance()
                        .showRecord(
                            (mBannerXmNativeAd as XmNativeAd).adData,
                            builder.build()
                        )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            R.id.x_play_ad_banner_gif_cover -> {

                if (mBannerXmNativeAd != null && mBannerXmNativeAd?.adSDKAdapterModel != null) {
                    val clickReportModel = AdReportModel.Builder(
                        AppConstants.AD_LOG_TYPE_BANNER_GIF_CLICK,
                        AdPositionIdManager.getPositionNameByPositionId(mBannerXmNativeAd?.adSDKAdapterModel?.adPositionId)
                    ).build()

                    AdManager.handlerAdClick(
                        context,
                        AdConversionUtil.translateSDKAdModelToAdvertis(mBannerXmNativeAd?.adSDKAdapterModel),
                        clickReportModel
                    )

                    if (mUveBehaviorCallback != null) {
                        val params: MutableMap<String, String> = HashMap()
                        try {
                            params["plan"] = (mBannerXmNativeAd?.adModel as AdModel).planId.toString()
                            params["material"] = (mBannerXmNativeAd?.adModel as AdModel).adid.toString()
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        mUveBehaviorCallback!!.onClick(params)
                    }
                }

                ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
            }
        }
    }

    override fun beforeFullScreenChanged(fullScreen: Boolean) {
    }

    override fun afterFullScreenChanged(fullScreen: Boolean) {
    }

    override fun onScreenStatusChanged(xPlayPageStatus: XPlayPageStatus) {
    }

    override fun onFullScreenChanged(isFull: Boolean) {
        isFullScreenModel = isFull
        if (isFull) {
            resetAllViewRelative()
        } else {
            if (mSoundPatchBannerAd != null && canShowSoundPatchBanner(mSoundPatchBannerAd?.advertis)) {
                showSoundPatchCoverBanner(mSoundPatchBannerAd)
            }

            Logger.i(
                "msg_uve_banner",
                " ------- 切到半屏播放页 - " + mBannerXmNativeAd + " , is cover showing = " + isSoundPatchCoverShowing
            )
            if (mBannerXmNativeAd != null && isSoundPatchCoverShowing == false) {
                showXGBanner(mBannerXmNativeAd, "全屏切到半屏， 可以展示下挂了")
            }
        }
    }


    /***
     * 播放回调
     */

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        Logger.w("msg_uve_banner", " onSoundSwitch  --- 声音切换， 清除掉未展示的 下挂")
        mSoundPatchBannerAd = null
        mCurrentSoundPatchBannerAd = null
        mBannerXmNativeAd = null
        mCurrentBannerXmNativeAd = null

        invisibleBannerViewRelative()

        Logger.d("msg_uve_banner", " 下挂 请求标识 重置 99999 = " + isLoadPlayBannerData)
        isLoadPlayBannerData = false
        isPrivateGoodsRequestFinished = false
    }

    override fun onPlayPause() {
    }

    override fun onPlayStart() {
    }

    /**  ------ 贴片数据获取 -----------*/
    override fun onStartGetAdsInfo(
        playMethod: Int,
        duringPlay: Boolean,
        isPaused: Boolean
    ) {
        isSoundPatchRequestFinished = false
        Logger.d(
            "msg_uve_banner",
            " ---- onStartGetAdsInfo - ---- -111111111  --- - -- - -- -- - isSoundPatchRequestFinish =  " + isSoundPatchRequestFinished
        )
        mSoundPatchBannerAd = null
        mCurrentSoundPatchBannerAd = null
        mBannerXmNativeAd = null
        mCurrentBannerXmNativeAd = null

        Logger.d(
            "msg_uve_banner",
            " ---- onStartGetAdsInfo - ---- -111111111   开始请求新的贴片， 清掉老数据 "
        )

        mCurrentPlayMethod = playMethod
        mCurrentDuringPlay = duringPlay
        mIsPaused = isPaused
        Logger.d(
            "msg_uve_banner",
            " ---------- 贴片数据获取 -  onStartGetAdsInfo  ----> playMethod = " + playMethod + " ， duringPlay = " + duringPlay + "  ，isPaused = " + isPaused
        )
        Logger.d(
            "msg_uve_banner",
            " 请求uve -- 贴片数据获取 -- playMethod = " + playMethod + " ， duringPlay = " + duringPlay + "  ，isPaused = " + isPaused
        )
        isLoadPlayBannerData = false
        invisibleBannerViewRelative()
    }

    override fun onGetAdsInfo(ads: AdvertisList) {
        try {
            if (ads != null && ads.advertisList != null && ads.advertisList.size > 0) {
                Logger.d(
                    "msg_uve_banner",
                    " ---------- 1111 <USER> <GROUP> 贴片返回了数据 "
                )
                if (!AdManager.isThirdAd(ads.advertisList[0]) && !AdInventoryCollectManager.isVirtualAd(ads.advertisList[0])) {
                    Logger.d(
                        "msg_uve_banner",
                        " ---------- 2222 <USER> <GROUP> 贴片返回了数据 ---  当前数据不是 三方dsp的数据, 并且不是虚拟物料，清除掉 延时请求banner任务  "
                    )
                    HandlerManager.removeCallbacks(showXgBannerDelayRunnable)
                } else {
                    Logger.d(
                        "msg_uve_banner",
                        " ---------- 2222 <USER> <GROUP> 贴片返回了数据 ---  三方dsp的数据，由于加载失败没回调，继续等待/ 或者是虚拟物料 "
                    )
                }
            } else {
                isSoundPatchRequestFinished = true
                Logger.d(
                    "msg_uve_banner",
                    " ---- onGetAdsInfo - list = null ---- -111111111   - -- -- - isSoundPatchRequestFinish =  " + isSoundPatchRequestFinished
                )
                if (mBannerXmNativeAd != null) {
                    showXGBanner(mBannerXmNativeAd, "noAdCover 没有贴片， 下挂 单独出")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onAdsStartBuffering() {
    }

    override fun onAdsStopBuffering() {
    }

    override fun onStartPlayAds(ad: Advertis?, position: Int) {
    }

    override fun onCompletePlayAds() {
    }

    override fun onError(what: Int, extra: Int) {
    }

    override fun onGetForwardVideo(advertis: MutableList<Advertis>?) {
    }

    /**
     * 背景色改变
     */
    override fun onThemeColorChanged(foregroundColor: Int, backgroundColor: Int) {

        var textColor: Int = backgroundColor
        try {
            // 暗黑模式
            if (BaseFragmentActivity.sIsDarkMode) {
                textColor = ColorUtil.covertColorToFixedSaturationAndLightness(backgroundColor, 0.59f, 0.76f)
            } else {
                if (PlayUtil.isVideoPage()) {
                    textColor = ColorUtil.covertColorToFixedSaturationAndLightness(backgroundColor, 0.59f, 0.76f)
                } else {
//                    textColor = ColorUtil.covertColorToFixedSaturationAndLightness(backgroundColor, 0.39f, 0.54f)
                    textColor = YDomainColorUtil.fixHighlightL(backgroundColor);
                }
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
        if (mBannerBtn != null) {
            mBannerBtn?.setTextColor(textColor)
        }
    }

    private fun gifBannerFadeIn(gifView: View?) {
        if (gifView == null || gifView.visibility == View.VISIBLE) {
            return
        }
        try {
            val translateAnimation: Animation =
                TranslateAnimation(0f, 0f, (BaseUtil.dp2px(context, 245f)).toFloat(), 0f)
            val alphaAnimation: Animation = AlphaAnimation(0f, 1f)

            val animationSet: AnimationSet = AnimationSet(true)
            animationSet.addAnimation(translateAnimation)
            animationSet.addAnimation(alphaAnimation)
            animationSet.duration = 400
            animationSet.setAnimationListener(object : AnimationListener {
                override fun onAnimationStart(animation: Animation?) {
                }

                override fun onAnimationEnd(animation: Animation?) {
                }

                override fun onAnimationRepeat(animation: Animation?) {
                }
            })
            gifView.startAnimation(animationSet)
            ViewStatusUtil.setVisible(View.VISIBLE, gifView)
        } catch (e: Exception) {
            ViewStatusUtil.setVisible(View.VISIBLE, gifView)
            e.printStackTrace()
        }
    }

    private fun gifBannerFadeOut(gifView: View?) {
        if (gifView == null || gifView.visibility != View.VISIBLE) {
            return
        }

        try {
            val translateAnimation: Animation = TranslateAnimation(
                0f,
                0f, 0f, (BaseUtil.dp2px(context, 245f)).toFloat()
            )
            val alphaAnimation: Animation = AlphaAnimation(
                1f, 0f
            )

            val animationSet: AnimationSet = AnimationSet(true)
            animationSet.addAnimation(translateAnimation)
            animationSet.addAnimation(alphaAnimation)
            animationSet.duration = 400
            animationSet.setAnimationListener(object : AnimationListener {
                override fun onAnimationStart(animation: Animation?) {
                }

                override fun onAnimationEnd(animation: Animation?) {
                    ViewStatusUtil.setVisible(View.GONE, gifView)
                }

                override fun onAnimationRepeat(animation: Animation?) {
                }
            })
            gifView.startAnimation(animationSet)

//            gifView.startAnimation(animation)
        } catch (e: Exception) {
            ViewStatusUtil.setVisible(View.GONE, gifView)
            e.printStackTrace()
        }
    }

    override fun onDialogShowChange(isShow: Boolean) {
        Logger.d(
            "msg_uve_banner",
            " ---- ---- ---- -- onDialogShowChange 播放页有其他弹窗出来了， 关闭gif 浮层 : isShow --> " + isShow
        )
        if (isShow) {
            ViewStatusUtil.setVisible(View.GONE, mGifViewLayout)
        }
    }

    override fun onLosePriority() {
        isCanShowBannerWithPriority = false
        Logger.d(
            "msg_uve_banner_Priority",
            " ---- ---- ---- -- XYellowBar  onLosePriority 失去优先级， 隐藏banner 和 下挂, isCanShowBannerWithPriority = " + isCanShowBannerWithPriority
        )
        invisibleBannerViewRelative()
    }

    override fun onGainedPriority() {
        isCanShowBannerWithPriority = true
        Logger.i(
            "msg_uve_banner_Priority",
            " ---- ---- ---- -- XYellowBar  onGainedPriority 获取优先级， 可以展示, isCanShowBannerWithPriority = " + isCanShowBannerWithPriority
        )
    }

    override fun onPolymerViewShowStatusChange(isShowing: Boolean) {
        Logger.i(
            "msg_uve_banner_polymer",
            " ---- onPolymerViewShowStatusChange --聚合流 广告浮层 ， isShowing = " + isShowing
        )
        isPolymerizationViewShowing = isShowing
        if (isShowing) {
            Logger.e(
                "msg_uve_banner_polymer",
                " ---- onPolymerViewShowStatusChange -- 聚合流 广告浮层 展示， 隐藏下挂相关信息 "
            )
            resetAllViewRelative()
        } else {
            Logger.e(
                "msg_uve_banner_polymer",
                " ---- onPolymerViewShowStatusChange -- 聚合流 广告浮层 关闭了， 接下来是展示下挂 --> mBannerXmNativeAd = " + mBannerXmNativeAd
                        + " mSoundPatchBannerAd = " + mSoundPatchBannerAd
            )
            if (mSoundPatchBannerAd != null) {
                if (canShowSoundPatchBanner(mSoundPatchBannerAd?.advertis)) {
                    showSoundPatchCoverBanner(mSoundPatchBannerAd)
                    return
                }
            }
            if (mBannerXmNativeAd != null && isSoundPatchCoverShowing == false) {
                showXGBanner(mBannerXmNativeAd, "全屏切到半屏， 可以展示下挂了")
            }
        }
    }

    override fun getUveAdParams(
        sceneId: Int,
        otherMap: MutableMap<Any?, Any?>
    ): MutableMap<String, String> {

        val requestMap: MutableMap<String, String> = HashMap()
        requestMap["appid"] = "0"
        try {
            requestMap["uid"] =
                if (UserInfoMannage.hasLogined()) UserInfoMannage.getUid().toString() + "" else ""
            var currentId: Long = 0

            if (XmPlayerManager.getInstance(ToolUtil.getCtx()) != null) {
                val track = XmPlayerManager.getInstance(ToolUtil.getCtx()).currSound
                if (track != null) {
                    if (track is Track) {
                        if (PlayableModel.KIND_TRACK == track.getKind() || PlayableModel.KIND_MODE_SLEEP == track.getKind()) {
                            currentId = track.getDataId()
                            requestMap["trackId"] = track.getDataId().toString() + ""
                            requestMap["pageMode"] =
                                XmAdsManager.getSoundPlayStyle(track as Track).toString() + ""
                        }
                        requestMap["scheduleId"] = (track as Track).scheduleId.toString() + ""
                        requestMap["radioId"] = (track as Track).radioId.toString() + ""
                        requestMap["track_playsource"] = (track as Track).playSource.toString() + ""
                    }
                    val playMode = XmPlayerManager.getInstance(ToolUtil.getCtx()).playMode
                    var order = 1
                    if (playMode == PlayMode.PLAY_MODEL_LIST) {
                        order = 1
                    } else if (playMode == PlayMode.PLAY_MODEL_SINGLE_LOOP) {
                        order = 2
                    } else if (playMode == PlayMode.PLAY_MODEL_RANDOM) {
                        order = 3
                    } else if (playMode == PlayMode.PLAY_MODEL_LIST_LOOP) {
                        order = 4
                    }
                    requestMap["order"] = order.toString() + ""
                    val tempo = XmPlayerManager.getInstance(ToolUtil.getCtx()).tempo
                    var speed = 1
                    if (java.lang.Float.compare(tempo, 0.8f) <= 0) {
                        speed = 0
                    } else if (java.lang.Float.compare(tempo, 1.4f) >= 0) {
                        speed = 2
                    }
                    requestMap["playSpeed"] = speed.toString() + ""
                }
                requestMap["mode"] =
                    if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isOnlineSource) "0" else "1"
            }
            requestMap["trackId"] = currentId.toString() + ""
            Logger.i("msg_uve_banner", " 请求uve ---- playMethod = " + mCurrentPlayMethod)
            requestMap["playMethod"] = mCurrentPlayMethod.toString() + ""
            Logger.i("msg_uve_banner", " 请求uve ---- pause = " + mIsPaused)
            requestMap["paused"] = mIsPaused.toString() + ""
            val isContainPlay =
                MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext())
                    .getBooleanCompat("gif_banner_containPlay")
            requestMap["continuePlay"] = isContainPlay.toString() + ""
            var duringPlay: Boolean = mCurrentDuringPlay
            if (mIsPaused) {
                duringPlay = false
            }
            Logger.i("msg_uve_banner", " 请求uve ---- duringPlay = " + duringPlay)
            requestMap["duringPlay"] = duringPlay.toString() + ""
            requestMap["slotIds"] =
                UveConstants.Position.BANNER.positionID.toString() + ""

            requestMap["appVersion"] = AppVersionUtil.getVersionName(mContext)
            requestMap["sdkJarVersion"] = XmAdSDK.getInstance().sdkJarVersion.toString()
            requestMap["sdkVersion"] = XmAdSDK.getInstance().sdkVersion
            requestMap["sdks"] = XmAdSDK.getInstance().getSdks()
            requestMap["userAgent"] = DeviceUtil.getUserAgentByWebView(mContext)
            requestMap["newPlayPageVersion"] = NewPlayPageUtil.getNewPageParams() ?: "1"
            requestMap[Advertis.IS_DISPLAYED_IN_SCREEN] =
                (if (XmAdsManager.isPlayFragmentShowing) 1 else 0).toString() + ""
            requestMap[HttpParamsConstantsInOpenSdk.PARAM_AD_VERSION] = AdManager.getAdPlayVersion()
        } catch (e: Exception) {
            e.printStackTrace()
        }

        ForwardVideoManager.addCommonRequestParams(requestMap, ToolUtil.getCtx(), 0)
        return requestMap
    }

    override fun isInterceptRequest(sceneId: Int): Boolean {
        return false
    }

    override fun onUveLoadFinished(xgNativeAd: INativeAd?, callback: IUveBehaviorCallback?, hasYellowBarShow: Boolean) {
        try {
            if (xgNativeAd != null) {
                mHasYellowBarShow = false
                mBannerXmNativeAd = xgNativeAd
                mUveBehaviorCallback = callback
                Logger.e(
                    "msg_uve_banner",
                    "------ uve onUveLoadFinished  0 ----- ---- xgNativeAd  = " + xgNativeAd
                )

                showXGBanner(xgNativeAd, "通过uve 请求的数据，尝试展示")
            } else {
                mHasYellowBarShow = hasYellowBarShow
                mBannerXmNativeAd = null
                if (mHasYellowBarShow) {
                    // 有商业化小黄条展示时，不展示贴片联合下挂
                    resetAllViewRelative()
                } else {
                    if (!canShowSoundPatchBanner(mCurrentSoundPatchBannerAd?.advertis)) {
                        resetAllViewRelative()
                    }
                }
                YPlayPrivateGoodsAdManager.getInstance().onAdBannerShow(0, getPlayingTrackId())
                Logger.e(
                    "msg_uve_banner",
                    "------ uve onUveLoadFinished  没有返回数据， 不需要展示"
                )
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }
}