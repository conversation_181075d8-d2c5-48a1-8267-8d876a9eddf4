package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.NewShowNotesManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.ShowTagManager
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.DomainColorUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.host.view.CornerRelativeLayout
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.GuessYouLikeHorMoreUtil
import com.ximalaya.ting.android.main.adapter.find.util.GuessYouLikeUtil
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.ShowTagPlayUtil
import com.ximalaya.ting.android.main.adapter.find.util.TextWrapUtil
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataPlayStatusStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.view.live.LiveLotteryTagView
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 猜你喜欢
 */
class RecommendGuessYouLikeAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendGuessYouLikeAdapterProviderStaggered.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendGuessYouLikeAdapterProviderStaggered.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataPlayStatusStaggered<RecommendGuessYouLikeAdapterProviderStaggered.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendGuessYouLikeAdapterProviderStaggered.ViewHolder, RecommendItemNew> {

    private var mOldState = RecyclerView.SCROLL_STATE_IDLE
    private var mListCardViewHolder: ViewHolder? = null


    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_guess_you_like_card,
            parent,
            false
        )
    }

    override fun onPlayStart() {
//        mListCardViewHolder?.rcvSocialList?.adapter?.notifyDataSetChanged()
    }

    override fun onPlayPause() {
//        mListCardViewHolder?.rcvSocialList?.adapter?.notifyDataSetChanged()
    }

    override fun onSoundPlayComplete() {
        mListCardViewHolder?.recyclerView?.adapter?.notifyDataSetChanged()
    }

    class ViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val bgTop: ImageView = convertView.findViewById(R.id.main_bg_top)
        val recyclerView: RecyclerView = convertView.findViewById(R.id.main_rcv_album_list)
        var tvTitle: TextView = convertView.findViewById(R.id.main_tv_social_list_tittle)
        val ivRefresh: ImageView = convertView.findViewById(R.id.main_tv_refresh)
        val ivMore: ImageView = convertView.findViewById(R.id.main_tv_more)
        var startSnapHelper: StartSnapHelper? = null
        var lastScreenWidth: Int =
            BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        var firstVisiblePosition: Int = 0
    }

    override fun onConfigurationChanged(holder: ViewHolder?) {
        holder ?: return
        if (holder.lastScreenWidth == BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())) {
            return
        }
        holder.lastScreenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
    }

    override fun createViewHolder(convertView: View?): ViewHolder? {
        PerformanceMonitor.traceBegin("social_createViewHolder")
        if (convertView == null) {
            return null
        }
        val viewHolder = ViewHolder(convertView)
        PerformanceMonitor.traceEnd("social_createViewHolder", 7)
        return viewHolder
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun bindViewHolder(
        holder: ViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            HandlerManager.postOnUIThread {
                onBindViewHolderInner(holder, position, recommendItemNew, convertView)
            }
        } else {
            onBindViewHolderInner(holder, position, recommendItemNew, convertView)
        }
    }

    fun onBindViewHolderInner(
        holder: ViewHolder?,
        modulePosition: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        mListCardViewHolder = holder
        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        PerformanceMonitor.traceBegin("guess_you_like_bindViewHolder_" + recommendCommonItem.title)

        // 命中新的习惯听样式  听单在首屏第二个模块  第三个模块广告显示中  直接压缩标题为1行
        if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() &&
            RecommendFragmentTypeManager.isNewSceneCard() && modulePosition == 1
        ) {
            holder.tvTitle.text = recommendCommonItem.title
            holder.tvTitle.maxLines = 1
        } else {
            holder.tvTitle.maxLines = 2
            TextWrapUtil.checkWrap(holder.tvTitle, recommendCommonItem.title, modulePosition)
        }

        if (recommendCommonItem.other?.showSocialListenBg == true) {
            holder.rootView.apply {
                setPadding(paddingLeft, 16.dp, paddingRight, paddingBottom)
            }

            holder.bgTop.visibility = View.VISIBLE
        } else {
            holder.bgTop.visibility = View.GONE
            holder.rootView.apply {
                setPadding(paddingLeft, 0, paddingRight, paddingBottom)
            }
        }

        val exportMore = { action: String ->
            // 新首页-社会化标注-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(61824) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("moduleName", recommendCommonItem.title) // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.moduleId?.toString() ?: "") // 例如：1000000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("action", action)
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
        }

        val performMoreClick = {
            ScenePlayDataUtil.saveDataForRn(recommendItemNew)

            ToolUtil.clickUrlAction(
                fragment, recommendCommonItem.landingPage ?: "", holder.ivMore
            )
        }

        holder.tvTitle.setOnClickListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())

            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1, (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            exportMore("click")
            performMoreClick()
        }

        holder.ivMore.setOnClickListener {
            exportMore("click")
            val isShowMore = !recommendCommonItem.landingPage.isNullOrEmpty()
            var level1DisLikeTitle = recommendCommonItem.ext?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick()
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(modulePosition)
                }
            }

            val moreFuncBuild = MoreFuncBuild.createSocialListenMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener
            )

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (modulePosition + 1).toString())
                put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.ext?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }

        val spanCount = if (RecommendFragmentTypeManager.isNewSceneCard() && modulePosition == 1) {
            3
        } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() && modulePosition == 1) {
            2
        } else {
            3
        }

        val list = recommendCommonItem.subElements

        val cardAlbumListAdapter = AlbumListItemAdapter(
            dataAction,
            fragment,
            recommendCommonItem,
            recommendItemNew,
            list,
            modulePosition,
            holder.recyclerView,
            spanCount,
            holder.rootView,
            !recommendCommonItem.landingPage.isNullOrEmpty()
        )
        cardAlbumListAdapter.mEnableMoreItem = cardAlbumListAdapter.enableJumpMore
        cardAlbumListAdapter.setRelaseJumpActivityListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1, (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            exportMore("slide")
            performMoreClick()
        }
        // 听单专辑列表
        holder.recyclerView.adapter = cardAlbumListAdapter
        RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
            this.javaClass.simpleName,
            spanCount,
            modulePosition,
            recommendItemNew
        )
        val layoutManager =
            GridLayoutManager(convertView?.context, spanCount, GridLayoutManager.HORIZONTAL, false)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) spanCount else 1
            }
        }
        holder.recyclerView.layoutManager = layoutManager
        if (holder.startSnapHelper == null) {
            holder.startSnapHelper = StartSnapHelper()
            holder.startSnapHelper!!.attachToRecyclerView(holder.recyclerView)
            holder.startSnapHelper!!.setContainerView(holder.recyclerView)
        }
        holder.recyclerView.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(recommendItemNew, modulePosition, holder)
                    recommendItemNew.firstVisiblePosition =
                        (holder.recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                }
            }
        })

        if (recommendItemNew.firstVisiblePosition != (holder.recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()) {
            holder.recyclerView.scrollToPosition(recommendItemNew.firstVisiblePosition)
        }

        holder.ivRefresh.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            if (isRequestRefresh) {
                return@setOnClickListener
            }

            // 新首页-社会化标注-刷新  点击事件
            val trace = XMTraceApi.Trace()
                .click(65534) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("xmRequestId", recommendItemNew.xmRequestId)
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("contentType", recommendItemNew.itemType) // 客户端传
                .put("contentId", recommendCommonItem.id?.toString()) // 客户端传
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()

            // 数据如果更新了 则返回false
            val isAllowRefresh = { recommendCommonItem: RecommendCommonItem ->
                recommendCommonItem == recommendItemNew.item
            }

            checkRefreshData(
                recommendItemNew,
                list,
                spanCount,
                recommendCommonItem,
                isAllowRefresh,
                dataAction,
                modulePosition,
                holder,
                cardAlbumListAdapter
            )
        }

        // 有声音样式  缓存下
        val item = list?.find { "Track" == it.bizType }
        ShowTagPlayUtil.setRecyclerViewTag(item != null, holder, holder.recyclerView)

        PerformanceMonitor.traceEnd(
            "guess_you_like_bindViewHolder_" + recommendCommonItem.title,
            8
        )
    }

    private var isRequestRefresh = false

    private fun checkRefreshData(
        recommendItemNew: RecommendItemNew,
        list: List<CommonSubElement>?,
        spanCount: Int,
        recommendCommonItem: RecommendCommonItem,
        isAllowRefresh: (recommendCommonItem: RecommendCommonItem) -> Boolean,
        dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        modulePosition: Int,
        holder: ViewHolder,
        cardAlbumListAdapter: AlbumListItemAdapter
    ) {
        val isAlbum = "Album" == list?.getOrNull(0)?.bizType
        isRequestRefresh = true
        GuessYouLikeUtil.refreshGuessYouLikeData(recommendCommonItem, isAlbum, spanCount,
            object : IDataCallBack<RecommendCommonItem?> {
                override fun onSuccess(data: RecommendCommonItem?) {
                    isRequestRefresh = false
                    if (data == null) {
                        ToastManager.showToast("暂无可替换内容")
                        return
                    } else if (!fragment.canUpdateUi()) {
                        return
                    }
                    if (!isAllowRefresh(data)) {
                        return
                    }

                    val subElements = data.subElements
                    if ((subElements?.size ?: 0) >= 3) {
                        recommendItemNew.firstVisiblePosition = 0
                        cardAlbumListAdapter.commonSubElementList.clear()
                        cardAlbumListAdapter.commonSubElementList.addAll(subElements!!)
                        cardAlbumListAdapter.notifyDataSetChanged()

                        HandlerManager.postOnUIThreadDelay({
                            traceOnItemShow(recommendItemNew, modulePosition, holder)
                        }, 200)

                        return
                    }
                    ToastManager.showToast("暂无可替换内容")
                }

                override fun onError(code: Int, message: String?) {
                    isRequestRefresh = false
                    ToastManager.showToast("暂无可替换内容")
                }
            })
    }

    private fun checkAdCunt(subElements: List<CommonSubElement>?): Int {
        if (subElements.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (subElement in subElements) {
            val adInfo = subElement.ext?.subRefInfo?.businessExtraInfo?.adInfo
            if (!adInfo.isNullOrEmpty()) {
                adCount++
            }
        }
        return adCount
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        if (recommendCommonItem.cardAdCount == -1) {
            recommendCommonItem.cardAdCount = checkAdCunt(recommendCommonItem.subElements)
        }
        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                        .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    trace.createTrace()
                }
                val childCount = holder.recyclerView.childCount
                for (i in 0 until childCount) {
                    val view = holder.recyclerView.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement =
                            view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val advertis = view.getTag(R.id.main_id_data_ad_info) as? Advertis
                        traceAlbumInner(
                            subElement, recommendCommonItem, data, index, position,
                            ViewStatusUtil.getViewVisibleAreaRealPercent(view), advertis, view
                        )
                    }
                }
            }
        }
    }

    class AlbumListItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        // 页面
        private val fragment: BaseFragment2,
        // 卡片数据
        val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        // 专辑列表
        list: List<CommonSubElement>?,
        var modulePosition: Int,
        val recyclerView: RecyclerView,
        var spanCount: Int,
        val rootView: View,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 专辑列表
        val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            list?.run {
                commonSubElementList.addAll(list)
            }
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            val view: View = ViewPool.getInstance().getView(
                HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                R.layout.main_item_recommend_guess_you_like_list_item,
                parent,
                false,
                "GuessYouLikeList"
            )
            return AlbumViewHolder(view)
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is AlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                horizontalView?.run {
                    val layoutParams = this.layoutParams
                    layoutParams?.width = 37.dp
                    layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                    this.layoutParams = layoutParams
                    visibility = if (enableJumpMore) {
                        View.VISIBLE
                    } else {
                        View.GONE
                    }

                    if (enableJumpMore) {
                        GuessYouLikeHorMoreUtil.fixHorMoreView(
                            recyclerView,
                            moreRootView,
                            position
                        )
                    }
                }
            }
        }

        private fun onBindAlbumView(
            commonSubElement: CommonSubElement,
            holder: AlbumViewHolder,
            textViewContainerWith: Int
        ) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindAlbumViewInner(commonSubElement, holder, textViewContainerWith)
                }
            } else {
                onBindAlbumViewInner(commonSubElement, holder, textViewContainerWith)
            }
        }

        private fun onBindAlbumViewInner(
            commonSubElement: CommonSubElement,
            holder: AlbumViewHolder,
            textViewContainerWith: Int
        ) {
            holder.cslContainerView.visibility = View.VISIBLE
            ViewStatusUtil.setVisible(View.GONE, holder.cslTrackContainerView)
            holder.itemTitleTv.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
            if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.ext!!.reasonContent
                holder.itemSubtitle1Tv.maxLines = 1
                RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitle1Tv)
            }

            holder.cslContainerView.run {
                val padding = RecommendCornerUtils.getPaddingSize()
                setPadding(paddingLeft, padding, paddingRight, padding)
            }
            holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getSocialCoverSize())
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)
            holder.showTagsParentAlbum?.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }

            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
            val adWidth = if (adTagShow) 24 else 0
            val otherWidth =
                16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + 16 + adWidth //  else 16 + 70 + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(
                holder.layoutShowTags,
                commonSubElement.ext?.showTags,
                textViewContainerWith - otherWidth.dp,
                commonSubElement.ext?.subTitle1,
                commonSubElement.ext?.subTitle2
            )

            commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            if (holder.layoutRightTxtArea.layoutParams is ConstraintLayout.LayoutParams) {
                (holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams).startToEnd =
                    R.id.main_album_cover_layout
            }
            commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                commonSubElement.ext?.other?.getBRTagUrl()
            )
        }

        private fun onBindTrackView(
            commonSubElement: CommonSubElement, holder: AlbumViewHolder,
            textViewContainerWith: Int
        ) {
            holder.cslTrackContainerView.visibility = View.VISIBLE
            ViewStatusUtil.setVisible(View.GONE, holder.cslContainerView)
            val track = Track()
            track.dataId = commonSubElement.refId ?: 0
            val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
            if (isPlaying) {
                holder.ivPlayButton.setImageResource(R.drawable.host_pause_btn_n_fill_n_28)
            } else {
                holder.ivPlayButton.setImageResource(R.drawable.host_play_btn_inside_fill_n_28)
            }
            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(
                if (adTagShow) View.VISIBLE else View.GONE,
                holder.ivAdTagTrack
            )
            val adWidth = if (adTagShow) 24 else 0
            val coverSizeDp = RecommendCornerUtils.getSocialCoverSizeDp()
            val otherWidth = if (NewShowNotesManager.userNewShowNotes()) {
                16 + coverSizeDp + 12 + 54
            } else {
                16 + coverSizeDp + 12 + 16
            }

            holder.cslTrackCoverView.run {
                val coverSize = RecommendCornerUtils.getSocialCoverSize().toInt()
                val params = this.layoutParams as MarginLayoutParams
                params.width = coverSize
                params.height = coverSize
                params.topMargin = RecommendCornerUtils.getPaddingSize()
                params.bottomMargin = RecommendCornerUtils.getPaddingSize()
                this.layoutParams = params

                setCornerRadius(RecommendCornerUtils.getSocialCorner())
            }

            holder.showTagsParentTrack?.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }
            val containerWidthInPx = textViewContainerWith - otherWidth.dp - adWidth.dp

            if (commonSubElement.ext?.showTags.isNullOrEmpty()) {
                val newList = mutableListOf<ShowTag>()
                if (!TextUtils.isEmpty(commonSubElement.ext?.albumTitle)) {
                    val tag1 = ShowTag()
                    tag1.tag = commonSubElement.ext?.albumTitle
                    tag1.type = RecommendShowTagsUtilNew.TYPE_ALBUM_TITLE
                    newList.add(tag1)
                }
                if (commonSubElement.ext?.trackPlayNum != null && commonSubElement.ext.trackPlayNum > 0) {
                    val tag2 = ShowTag()
                    tag2.tag = getPlayCountString(commonSubElement.ext.trackPlayNum)
                    tag2.value = commonSubElement.ext.trackPlayNum.toString()
                    tag2.type = RecommendShowTagsUtilNew.TYPE_COUNT_PLAY
                    newList.add(tag2)
                }
                ShowTagManager.bindTagsView(
                    holder.trackShowTagLayout, newList, containerWidthInPx, commonSubElement.refId
                )
            } else {
                ShowTagManager.bindTagsView(
                    holder.trackShowTagLayout, commonSubElement.ext?.showTags,
                    containerWidthInPx, commonSubElement.refId
                )
            }

            ShowTagPlayUtil.setHolderTrackItem(
                holder,
                commonSubElement.refId,
                holder.trackShowTagLayout
            )

            holder.tvTrackTitle.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.tvTrackTitle)
            if (RecommendShowTagsUtilNew.canShowOneLine(
                    holder.tvTrackTitle,
                    commonSubElement.title,
                    textViewContainerWith - otherWidth.dp
                )
            ) {
                holder.tvTrackSubTitle.text = commonSubElement.summary ?: ""
                ViewStatusUtil.setVisible(
                    if (!TextUtils.isEmpty(commonSubElement.summary)) View.VISIBLE else View.GONE,
                    holder.tvTrackSubTitle
                )
                RecommendCornerUtils.updateSubTitleMargin(holder.tvTrackSubTitle)
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.tvTrackSubTitle)
            }
            var hasValidServerColor = false
            if (commonSubElement.cachedCoverColor != null && commonSubElement.cachedCoverColor != ColorUtil.INVALID_COLOR) {
                hasValidServerColor = true
                setPlayBgColor(commonSubElement.cachedCoverColor!!, holder)
            }
            ImageManager.from(BaseApplication.getMyApplicationContext())
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    holder.ivTrackCover,
                    commonSubElement.cover,
                    com.ximalaya.ting.android.host.R.drawable.host_default_album,
                    70,
                    70
                ) { _, bitmap ->
                    if (bitmap != null && !hasValidServerColor) {
                        DomainColorUtil.getDomainColorForRecommend(
                            bitmap,
                            Color.BLACK
                        ) { color: Int ->
                            setPlayBgColor(color, holder)
                            commonSubElement.cachedCoverColor = color
                        }
                    }
                }

            if (NewShowNotesManager.userNewShowNotes()) {
                holder.showNotePlayBtnBg.run {
                    val params = this.layoutParams as MarginLayoutParams
                    params.width = RecommendCornerUtils.getShowNoteSize()
                    params.height = params.width
                    this.layoutParams = params
                }

                holder.showNotePlayBtn.run {
                    val params = this.layoutParams
                    params.width = RecommendCornerUtils.getShowNotePlaySize()
                    params.height = params.width
                    this.layoutParams = params
                }

                RecommendCornerUtils.updateShowNoteRoundBg(holder.showNotePlayBtnBg)
            }

            if (NewShowNotesManager.userNewShowNotes()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.GONE, holder.vPlayButtonBg, holder.ivPlayButton)
                if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying &&
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId == commonSubElement.refId
                ) {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, true)
                } else {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, false)
                }
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.vPlayButtonBg, holder.ivPlayButton)
            }
        }

        private fun getPlayCountString(playCount: Long?): String {
            if (playCount == null || playCount <= 0) {
                return "99次播放"
            }
            return if (playCount < 10000) {
                playCount.toString() + "次播放"
            } else {
                val count = playCount / 10000
                "播放量超" + count + "万"
            }
        }

        fun onBindViewHolderInner(holder: AlbumViewHolder, position: Int) {
            val commonSubElement = commonSubElementList[position]
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            val remainder: Int = commonSubElementList.size % spanCount
            val start: Int =
                if (remainder == 0) commonSubElementList.size - spanCount else commonSubElementList.size - remainder
            val isTrackUIType = "Track" == commonSubElement.bizType
            val isAlbumUIType =
                ("Album" == commonSubElement.bizType || "Playlet" == commonSubElement.bizType)

            val adShow = commonSubElement.ext?.subRefInfo?.ad ?: false
            var advertis: Advertis? = null
            if (adShow) {
                advertis = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject
            }
            holder.itemView.setTag(R.id.main_id_data_ad_info, advertis)
            holder.itemView.setOnLongClickListener {
                if (adShow) {
                    if (!XmAdFeedbackUtil.feedbackEnable(advertis)) {
                        return@setOnLongClickListener true
                    }
                    val build = MoreFuncBuild()
                    build.fragment2 = fragment
                    if (isAlbumUIType) {
                        build.albumId = commonSubElement.refId ?: 0
                    }
                    if (isTrackUIType) {
                        build.trackId = commonSubElement.refId ?: 0
                    }
                    build.vibratorEnable = true
                    build.isShowLevel2Dislike = true
                    build.isShowSubscription = true
                    val disLikeLeve2Build = DisLikeLeve2Build().apply {
                        this.isFromAd = true
                        this.anchorName = commonSubElement.anchor?.nickName
                    }
                    disLikeLeve2Build.onFeedBackListener = object :
                        NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onDialogShow(showSuccess: Boolean) {}
                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
                            XmAdFeedbackUtil.recordFeedback(advertis, list)
                            val oldId = if (isTrackUIType) commonSubElement.refId else 0L
                            val oldItingUrl =
                                if (isTrackUIType) commonSubElement.landingPage else null
                            MainCommonRequest.getSingleSocialListenListItem(
                                position,
                                oldItingUrl,
                                moduleItem,
                                object :
                                    IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            return
                                        }
                                        commonSubElementList[position] = subElement
                                        if (isTrackUIType) {
                                            replaceRefreshIting(
                                                commonSubElementList,
                                                oldId,
                                                subElement.refId
                                            )
                                            replaceRefreshIting(
                                                moduleItem.subElements,
                                                oldId,
                                                subElement.refId
                                            )
                                            notifyDataSetChanged()
                                        } else {
                                            notifyItemChanged(position)
                                        }
                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView),
                                            commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject,
                                            holder.itemView
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                    }
                                })
                        }
                    }
                    build.disLikeLeve2Build = disLikeLeve2Build
                    XmMoreFuncManager.checkShowMorePage(build)

                } else {
                    val requestMap = mutableMapOf<String, String>()
                    val traceMap = mutableMapOf<String, String>()
                    traceMap["currPage"] = "newHomePage"
                    traceMap["cardPosition"] = (modulePosition + 1).toString()
                    traceMap["positionNew"] = (position + 1).toString()
                    if (!moduleItem.ubtV2.isNullOrEmpty()) {
                        traceMap.putAll(moduleItem.ubtV2)
                    }
                    if (!commonSubElement.ubtV2.isNullOrEmpty()) {
                        traceMap.putAll(commonSubElement.ubtV2)
                    }
                    traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                    traceMap["contentType"] = commonSubElement.bizType ?: ""
                    traceMap["contentId"] = commonSubElement.refId?.toString() ?: ""
                    if (isAlbumUIType) {
                        requestMap[HttpParamsConstants.PARAM_ALBUM_ID] =
                            commonSubElement.refId.toString()
                    } else {
                        requestMap[HttpParamsConstants.PARAM_TRACK_ID] =
                            commonSubElement.refId.toString()
                    }
                    requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                    requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] =
                        commonSubElement.anchor?.uid.toString()
                    requestMap["card_contentType"] = moduleItem.contentType ?: ""
                    requestMap["card_bizType"] = moduleItem.bizType ?: ""
                    requestMap["card_id"] = moduleItem.id.toString()

                    val disLikeLeve2Build = DisLikeLeve2Build()
                    disLikeLeve2Build.isFromAd = false
                    disLikeLeve2Build.anchorName = commonSubElement.anchor?.nickName
                    disLikeLeve2Build.requestMap = requestMap
                    disLikeLeve2Build.traceMap = traceMap
                    disLikeLeve2Build.onFeedBackListener = object :
                        NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onDialogShow(showSuccess: Boolean) {
                        }

                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
                            val oldId = if (isTrackUIType) commonSubElement.refId else 0L
                            val oldItingUrl =
                                if (isTrackUIType) commonSubElement.landingPage else null
                            MainCommonRequest.getSingleSocialListenListItem(
                                position,
                                oldItingUrl,
                                moduleItem,
                                object :
                                    IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            return
                                        }
                                        commonSubElementList[position] = subElement
                                        if (isTrackUIType) {
                                            replaceRefreshIting(
                                                commonSubElementList,
                                                oldId,
                                                subElement.refId
                                            )
                                            replaceRefreshIting(
                                                moduleItem.subElements,
                                                oldId,
                                                subElement.refId
                                            )
                                            notifyDataSetChanged()
                                        } else {
                                            val firstPos =
                                                (recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                                            notifyItemChanged(position)  // 使用这个  当前在最后一列时  系统定位不准 会滚动到前一列
                                            recyclerView.scrollToPosition(firstPos)
                                        }
                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView),
                                            commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject,
                                            holder.itemView
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                    }
                                })
                        }
                    }

                    var typeStr: Int? = null
                    val refId = commonSubElement.refId
                    if (isAlbumUIType) {
                        typeStr = MoreFuncBuild.TYPE_ALBUM
                    } else if (isTrackUIType) {
                        typeStr = MoreFuncBuild.TYPE_TRACK
                    }
                    val build: MoreFuncBuild = MoreFuncBuild.createCommonLongClickModel(
                        fragment, typeStr, refId, null, true, disLikeLeve2Build
                    )
                    val trackMap = mutableMapOf<String, String?>().apply {
                        put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                        put("contentType", commonSubElement.bizType ?: "")
                        put("contentId", commonSubElement.refId?.toString() ?: "")
                        put("modulePosition", (modulePosition + 1).toString())
                        put("positionNew", (position + 1).toString())
                        moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                        commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                    }
                    build.trackMap = trackMap

                    XmMoreFuncManager.checkShowMorePage(build)
                }
                true
            }

            val layoutParams = holder.cslContainerView.layoutParams
            val layoutParamsTrack = holder.cslTrackContainerView.layoutParams
            val textViewContainerWithInPx: Int
            if (position >= start) {
                // 最后一列
                layoutParams.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                layoutParamsTrack.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                textViewContainerWithInPx =
                    getRpAdaptSize(375 - 3) - if (enableJumpMore) 37.dp else 0
            } else {
                layoutParams.width = getRpAdaptSize(337 - getOffset())
                layoutParamsTrack.width = getRpAdaptSize(337 - getOffset())
                textViewContainerWithInPx = getRpAdaptSize(337 - getOffset() - 3)
            }
            if (isAlbumUIType) {
                onBindAlbumView(commonSubElement, holder, textViewContainerWithInPx)
            } else {
                onBindTrackView(
                    commonSubElement,
                    holder,
                    textViewContainerWithInPx
                )
                if (NewShowNotesManager.userNewShowNotes()) {
                    holder.showNotePlayBtnBgWrap.setOnClickListener {
                        onItemClickInner(
                            commonSubElement,
                            false,
                            isTrackUIType,
                            false,
                            position,
                            it,
                            advertis,
                            true
                        )
                    }
                }
            }
            holder.itemView.setOnClickListener {
                onItemClickInner(
                    commonSubElement,
                    isAlbumUIType,
                    isTrackUIType,
                    false,
                    position,
                    it,
                    advertis,
                    false
                )
            }
        }

        private fun onItemClickInner(
            commonSubElement: CommonSubElement, isAlbumUIType: Boolean, isTrackUIType: Boolean,
            isLiveUIType: Boolean,
            position: Int, view: View?, advertis: Advertis?, isClickPlayBtn: Boolean
        ) {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return
            }
            var albumId = 0L
            var trackId = 0L
            var contentId = ""
            var rewardName = ""
            var rankName = ""
            if (isAlbumUIType) {
                albumId = commonSubElement.refId ?: 0
                contentId = albumId.toString()
            } else if (isTrackUIType) {
                trackId = commonSubElement.refId ?: 0
                contentId = trackId.toString()
            } else if (isLiveUIType) {
                contentId = commonSubElement.refId?.toString() ?: "0"
                rewardName = LiveLotteryTagView.TagType.convert(
                    commonSubElement.ext?.other?.lotteryType
                )?.desc ?: ""
                rankName = commonSubElement.ext?.other?.rankName ?: ""
            }
            val tarTypeId = commonSubElement.ext?.tagType ?: 0
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put("card_adTopn", moduleItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            // 新首页-社会化标注-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(60896) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("albumId", albumId.toString())
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", moduleItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", moduleItem.id.toString()) // 例如：100000000
                .put("rec_src", moduleItem.ubt?.recSrc ?: "")
                .put("rec_track", moduleItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", moduleItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("trackId", trackId.toString()) // 当是声音时传
                .put("titleId", moduleItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("socialTagId", commonSubElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", moduleItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", moduleItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                ) // 可见区域占屏幕的比例
                .put("isAd", if (advertis != null) "true" else "false")
                .put("area", if (isClickPlayBtn) "play" else "item")
                .put("rewardName", rewardName) // 直播 奖励名称
                .put("rankName", rankName) // 直播 榜单名称
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = commonSubElement?.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()
            if (isTrackUIType && !isClickPlayBtn && NewShowNotesManager.userNewShowNotes()) {
                // 打开shownotes二级页
                NewShowNotesManager.startShowNotesDetailFragment(
                        NewShowNotesManager.SOURCE_FROM_HOME,
                        NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                        0,
                        trackId,
                        null)
                clickAdRecord(advertis, position)
            } else {
                if (isClickPlayBtn) {
                    val track = Track()
                    track.dataId = trackId
                    val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
                    if (isPlaying) {
                        PlayTools.pause(
                                BaseApplication.getMyApplicationContext(),
                                PauseReason.Business.RecommendSocialListenList
                        )
                    } else {
                        ToolUtil.clickUrlAction(
                                fragment,
                                NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                                view
                        )
                        clickAdRecord(advertis, position)
                    }
                } else {
                    if (isTrackUIType) {
                        ToolUtil.clickUrlAction(
                                fragment,
                                NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                                view
                        )
                    } else {
                        ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, view)
                    }
                    clickAdRecord(advertis, position)
                }
            }
        }

        private fun clickAdRecord(advertis: Advertis?, position: Int) {
            if (advertis != null) {
                AdManager.handlerAdClick(BaseApplication.getMyApplicationContext(), advertis,
                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST)
                                .onlyClickRecord(true)
                                .positionNew(position + 1)
                                .modulePosition(modulePosition + 1)
                                .build())
            }
        }

        private fun replaceRefreshIting(list: List<CommonSubElement>?, oldId: Long?, newId: Long?) {
            if (list.isNullOrEmpty() || oldId == 0L || newId == 0L) {
                return
            }
            list.forEach {
                it.landingPage = it.landingPage?.replace(oldId.toString(), newId.toString())
            }
        }

        private fun setPlayBgColor(color: Int, holder: AlbumViewHolder) {
            val gradientDrawable = GradientDrawable()
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.cornerRadius = 14.dp.toFloat()
            gradientDrawable.setColor(color)
            holder.vPlayButtonBg.background = gradientDrawable
        }

        override fun getItemCount(): Int {
            if (enableJumpMore) {
                return commonSubElementList.size + 1
            }
            return commonSubElementList.size
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class AlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)

            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutRightTxtArea: View = view.findViewById(R.id.main_layout_right_txt_area)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var showTagsParentAlbum: ViewGroup? = view.findViewById(R.id.main_layout_show_tag_parent_album)
            var showTagsParentTrack: ViewGroup? = view.findViewById(R.id.main_layout_show_tag_parent_track)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)
            var ivAdTagTrack: ImageView = view.findViewById(R.id.main_iv_ad_tag_track)

            var cslTrackContainerView: View = view.findViewById(R.id.main_csl_item_root_view_track)
            var cslTrackCoverView: CornerRelativeLayout = view.findViewById(R.id.main_crl_item_cover)
            var tvTrackTitle: TextView = view.findViewById(R.id.main_tv_track_title)
            var ivTrackCover: ImageView = view.findViewById(R.id.main_iv_item_cover_track)
            var tvTrackSubTitle: TextView = view.findViewById(R.id.main_tv_track_sub_title)
            var trackShowTagLayout: LinearLayout =
                view.findViewById(R.id.main_tv_track_show_tag_layout)
            var vPlayButtonBg: View = view.findViewById(R.id.main_v_bg_play)
            var ivPlayButton: ImageView = view.findViewById(R.id.main_iv_play_btn)
            var showNotePlayBtnBgWrap: View = view.findViewById(R.id.main_show_notes_play_layout_wrap)
            var showNotePlayBtnBg: View = view.findViewById(R.id.main_show_notes_play_layout)
            var showNotePlayBtn: ImageView = view.findViewById(R.id.main_iv_show_notes_play_btn)
            var bgItemGuideView: View = view.findViewById(R.id.main_bg_item_guide_view)

            init {
                resetSize()
            }

            fun resetSize() {
                val layoutParams = cslContainerView.layoutParams
                layoutParams.width = getRpAdaptSize(323)
            }
        }
    }

    companion object {
        private var AD_REPORT_SUB_PERCENT = ConfigureCenter.getInstance().getInt(
            CConstants.Group_ad.GROUP_NAME,
            CConstants.Group_ad.ITEM_HOME_AD_EXPOSE_PERCENT,
            50
        )

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        fun traceAlbumInner(
            subElement: CommonSubElement,
            recommendCommonItem: RecommendCommonItem,
            data: RecommendItemNew?,
            index: Int,
            modulePosition: Int,
            exploreArea: Int,
            advertis: Advertis?,
            itemView: View
        ) {
            var albumId = 0L
            var trackId = 0L
            var contentId = ""
            var rewardName = ""
            var rankName = ""
            when (subElement.bizType) {
                "Album", "Playlet" -> {
                    albumId = subElement.refId ?: 0
                    contentId = albumId.toString()
                }

                "Track" -> {
                    trackId = subElement.refId ?: 0
                    contentId = trackId.toString()
                }

                "Live" -> {
                    contentId = subElement.refId?.toString() ?: "0"
                    rewardName = LiveLotteryTagView.TagType.convert(
                        subElement.ext?.other?.lotteryType
                    )?.desc ?: ""
                    rankName = subElement.ext?.other?.rankName ?: ""
                }
            }
            val tarTypeId = subElement.ext?.tagType ?: 0
            // 新首页-社会化标注-专辑卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(60897)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put(
                    "tagType",
                    tarTypeId.toString()
                ) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("albumId", albumId.toString())
                .put("exploreArea", exploreArea.toString()) // 可见区域占屏幕的比例
                .put("positionNew", (index + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", recommendCommonItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.id.toString()) // 例如：100000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", subElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", data?.xmRequestId)
                .put("trackId", trackId.toString()) // 当是声音时传
                .put(
                    "titleId",
                    recommendCommonItem.ext?.recWrap?.id.toString()
                ) // 传 recWrap 中的 id
                .put("socialTagId", subElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put("isAd", if (advertis != null) "true" else "false")
                .put("rewardName", rewardName) // 直播 奖励名称
                .put("rankName", rankName) // 直播 榜单名称
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = subElement?.title,
                contentPosition = (index + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data?.isLocalCache == true) {
                trace.isLocalCache
            }
            trace.createTrace()

            // 广告曝光上报
            if (advertis != null && !advertis.isShowedToRecorded && data != null && !data.isLocalCache) {
                AdManager.adRecord(
                    BaseApplication.getMyApplicationContext(), advertis,
                    AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SITE_SHOW,
                        AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST
                    )
                        .positionNew(index + 1)
                        .modulePosition(modulePosition + 1)
                        .build()
                )
                advertis.isShowedToRecorded = true
            }
            if (advertis != null && !advertis.isRecordedSubPercent && data != null && !data.isLocalCache && exploreArea >= AD_REPORT_SUB_PERCENT) {
                // 做广告曝光50%的上报
                AdManager.reportSubPercentShow(advertis, data.xmRequestId)
                advertis.isRecordedSubPercent = true
            }

            HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, itemView, index)

        }
    }
}