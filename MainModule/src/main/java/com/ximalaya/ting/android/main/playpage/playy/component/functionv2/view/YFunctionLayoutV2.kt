package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.view

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business.BadgeOffset
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.view.CustomLayout

class YFunctionLayoutV2 @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : CustomLayout(context, attrs, defStyleAttr) {
    private val mutableRects = mutableListOf<Pair<View, Rect>>()

    private val topPadding = 5.dp
    private val bottomPadding = 28.dp

    private val iconSize = 26.dp
    private val iconAlpha = 0.55f
    private val realTextSize = 10f
    private val textAlpha = 0.4f
    private val topMarginValue = 4.dp

    val firstBizLayout = FrameLayout(context).apply {
        layoutParams = MarginLayoutParams(iconSize, iconSize).apply { leftMargin = 16.dp }
        <EMAIL>(this)
    }

    val speedIconPanel = SpeedIconView(context).apply {
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, iconSize).apply { topMargin = topMarginValue }
        <EMAIL>(this)
    }

    val likeFrameLayout = FrameLayout(context).apply {
        layoutParams = MarginLayoutParams(iconSize, iconSize)
        <EMAIL>(this)
    }

    val biz3Layout = FrameLayout(context).apply {
        layoutParams = MarginLayoutParams(MarginLayoutParams.WRAP_CONTENT, MarginLayoutParams.WRAP_CONTENT)
        <EMAIL>(this)
    }

    val moreIcon = ImageView(context).apply {
        layoutParams = MarginLayoutParams(iconSize, iconSize).apply { rightMargin = 16.dp }
        imageAlpha = (255 * iconAlpha).toInt()
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setColorFilter(PSkinManager.getBtnThemeColor())
        } else {
            setColorFilter(Color.WHITE)
        }
        val drawableId = R.drawable.main_ic_play_page_more_y
        setImageResource(drawableId)
        <EMAIL>(this)
    }

    val moreText = TextView(context).apply {
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT).apply {
            rightMargin = 16.dp
            topMargin = topMarginValue
        }
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            setTextColor(Color.WHITE)
        }
        alpha = textAlpha
        textSize = realTextSize
        text = "更多"
        <EMAIL>(this)
    }

    val biz3Text = TextView(context).apply {
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT).apply {
            topMargin = topMarginValue
        }
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            setTextColor(Color.WHITE)
        }
        alpha = textAlpha
        textSize = realTextSize
        text = "评论"
        <EMAIL>(this)
    }

    val likeText = TextView(context).apply {
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT).apply {
            topMargin = topMarginValue
        }
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            setTextColor(Color.WHITE)
        }
        alpha = textAlpha
        textSize = realTextSize
        text = "点赞"
        <EMAIL>(this)
    }
    val speedText = TextView(context).apply {
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT).apply {
            topMargin = topMarginValue
        }
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            setTextColor(Color.WHITE)
        }

        alpha = textAlpha
        textSize = realTextSize
        text = "倍速"
        <EMAIL>(this)
    }

    val biz1Text = TextView(context).apply {
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            setTextColor(Color.WHITE)
        }
        alpha = textAlpha
        textSize = realTextSize
        text = "音色切换"
        <EMAIL>(this)
    }

    private var badgeView: View? = null
    private var badgeOffset: BadgeOffset? = null

    private var badge2View: View? = null
    private var badge2Offset: BadgeOffset? = null

    private var badge3View: View? = null
    private var badge3Offset: BadgeOffset? = null
    private var bottom1View: View? = null
    private var bottom3View: View? = null


    fun setFirstBadge(view: View?, offset: BadgeOffset?) {
        if (view != null) {
            if (view.parent == this) {
                this.badgeView = view
                return
            }
            (view.parent as? ViewGroup)?.removeView(view)
            addView(view)
        }

        if (badgeView != null) {
            removeView(badgeView)
        }
        this.badgeView = view

        this.badgeOffset = offset
    }

    fun set2Badge(view: View?, offset: BadgeOffset?) {
        if (view != null) {
            if (view.parent == this) {
                this.badge2View = view
                return
            }
            (view.parent as? ViewGroup)?.removeView(view)
            addView(view)
        }

        if (badge2View != null) {
            removeView(badge2View)
        }
        this.badge2View = view

        this.badge2Offset = offset
    }

    fun set3Badge(view: View?, offset: BadgeOffset?) {
        if (view != null) {
            if (view.parent == this) {
                this.badge3View = view
                return
            }
            (view.parent as? ViewGroup)?.removeView(view)
            addView(view)
        }

        if (badge3View != null) {
            removeView(badge3View)
        }
        this.badge3View = view

        this.badge3Offset = offset
    }

    fun set1Text(textStr: String, alpha: Float = textAlpha) {
        biz1Text.text = textStr
        biz1Text.alpha = alpha
    }

    fun set1BottomView(view: View?) {
        if (view != null) {
            if (view.parent == this) {
                this.bottom1View = view
                return
            }
            (view.parent as? ViewGroup)?.removeView(view)
            addView(view)
        }

        if (bottom1View != null) {
            removeView(bottom1View)
        }
        this.bottom1View = view
        bottom1View?.apply {
            layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)?.apply {
                topMargin = topMarginValue
            }
        }
    }

    fun set3BottomView(view: View?) {
        if (view != null) {
            if (view.parent == this) {
                this.bottom3View = view
                return
            }
            (view.parent as? ViewGroup)?.removeView(view)
            addView(view)
        }

        if (bottom3View != null) {
            removeView(bottom3View)
        }
        this.bottom3View = view
        bottom3View?.apply {
            layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)?.apply {
                topMargin = topMarginValue
            }
        }
    }

    fun set3Text(textStr:String) {
        biz3Text.text = textStr
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        var maxHeight = 0

        children.forEach {
            if (it.visibility != View.GONE) {
                it.autoMeasure()
                maxHeight = maxHeight.coerceAtLeast(it.measuredHeight)
            }
        }

        setMeasuredDimension(
            getDefaultSize(suggestedMinimumWidth, widthMeasureSpec),
            maxHeight + topPadding + bottomPadding
        )
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        var useAbleWidth = r - l
        (firstBizLayout.layoutParams as? MarginLayoutParams)?.also {
            firstBizLayout.layout(it.leftMargin, topPadding)
            useAbleWidth = useAbleWidth - firstBizLayout.width - it.leftMargin
        }
//        (biz1Text.layoutParams as? MarginLayoutParams)?.also{
//            biz1Text.layout(
//                firstBizLayout.left + ((firstBizLayout.measuredWidth - biz1Text.measuredWidth) / 2),
//                firstBizLayout.bottom + it.topMargin
//            )
//        }
        bottom1View?.also {
            if (it.parent == this@YFunctionLayoutV2 ) {
                val topMargin = (bottom1View?.layoutParams as? MarginLayoutParams)?.topMargin ?: 0
                it.layout(
                    firstBizLayout.left + ((firstBizLayout.measuredWidth - (bottom1View?.measuredWidth
                        ?: 0)) / 2),
                    firstBizLayout.bottom + topMargin
                )
            }
        }
        badgeView?.also {
            if (it.parent == this@YFunctionLayoutV2 && it.visibility != View.GONE) {
                val offsetX = badgeOffset?.x?: 0
                val offsetY = badgeOffset?.y?: 0
                it.layout(
                    firstBizLayout.left + 20.dp,//offsetX,
                    firstBizLayout.top - 2.dp//it.measuredHeight/2 + offsetY
                )
            }
        }

        (moreIcon.layoutParams as? MarginLayoutParams)?.also {
            moreIcon.layout(r - moreIcon.measuredWidth - it.rightMargin, topPadding)
            useAbleWidth = useAbleWidth - moreIcon.width - it.rightMargin
        }

        (moreText.layoutParams as? MarginLayoutParams)?.also {
            moreText.layout(
                moreIcon.left + ((moreIcon.measuredWidth - moreText.measuredWidth) / 2),
                moreIcon.bottom + it.topMargin
            )
        }
        val availableSpaceLike = moreIcon.left - firstBizLayout.right
        val likeLeft = (availableSpaceLike - likeFrameLayout.measuredWidth) / 2
        likeFrameLayout.layout(firstBizLayout.right + likeLeft, topPadding)
//        if (likeCountText.visibility != View.GONE) {
////            likeCountText.layout(likeFrameLayout.right - likeCountText.measuredWidth/2, likeFrameLayout.top - likeCountText.measuredHeight/2)
//            likeCountText.layout(
//                likeFrameLayout.left + 20.dp,
//                likeFrameLayout.top - 2.dp
//            )
//        }

        (likeText.layoutParams as? MarginLayoutParams)?.also{
            likeText.layout(
                likeFrameLayout.left + ((likeFrameLayout.measuredWidth - likeText.measuredWidth) / 2),
                likeFrameLayout.bottom + it.topMargin
            )
        }
//        useAbleWidth -= likeFrameLayout.measuredWidth * 3
        val availableSpaceSpeed = likeFrameLayout.left - firstBizLayout.right
        val speedLeft = (availableSpaceSpeed - speedIconPanel.measuredWidth) / 2
        speedIconPanel.layout(firstBizLayout.right + speedLeft, topPadding)
        (speedText.layoutParams as? MarginLayoutParams)?.also {
            speedText.layout(
                speedIconPanel.left + ((speedIconPanel.measuredWidth - speedText.measuredWidth) / 2),
                speedIconPanel.bottom + it.topMargin
            )
        }

        badge2View?.also {
            if (it.parent == this@YFunctionLayoutV2 && it.visibility != View.GONE) {
//                val offsetX = badge3Offset?.x?: 0
//                val offsetY = badge3Offset?.y?: 0
//                it.layout(
//                    custom3FrameLayout.right - it.measuredWidth/2 + offsetX,
//                    custom3FrameLayout.top - it.measuredHeight/2 + offsetY
//                )

                it.layout(
                    likeFrameLayout.left + 20.dp,
                    likeFrameLayout.top - 2.dp
                )
            }
        }

        val availableSpace = moreIcon.left - likeFrameLayout.right
        val custom3Left = (availableSpace - biz3Layout.measuredWidth) / 2
        biz3Layout.layout(likeFrameLayout.right + custom3Left, likeFrameLayout.bottom - biz3Layout.measuredHeight)
//        (biz3Text.layoutParams as? MarginLayoutParams)?.also{
//            biz3Text.layout(
//                biz3Layout.left + ((biz3Layout.measuredWidth - biz3Text.measuredWidth) / 2),
//                biz3Layout.bottom + it.topMargin
//            )
//        }
        bottom3View?.also {
            if (it.parent == this@YFunctionLayoutV2 ) {
                val topMargin = (bottom3View?.layoutParams as? MarginLayoutParams)?.topMargin ?: 0
                it.layout(
                    biz3Layout.left + ((biz3Layout.measuredWidth - biz3Text.measuredWidth) / 2),
                    biz3Layout.bottom + topMargin
                )
            }
        }
        badge3View?.also {
            if (it.parent == this@YFunctionLayoutV2 && it.visibility != View.GONE) {
//                val offsetX = badge3Offset?.x?: 0
//                val offsetY = badge3Offset?.y?: 0
//                it.layout(
//                    custom3FrameLayout.right - it.measuredWidth/2 + offsetX,
//                    custom3FrameLayout.top - it.measuredHeight/2 + offsetY
//                )

                it.layout(
                    biz3Layout.left + 20.dp,
                    biz3Layout.top - 2.dp
                )
            }
        }

        //判断点赞覆盖
        biz3Layout.children.forEach {
            if (it is CommentAnimationViewV2) {
                val right = Math.max(badge2View?.right?: 0, likeFrameLayout.right)
                it.canShowGuide = right - biz3Layout.left < 2.dp
            }
        }


        updateRects()
    }


    private fun updateRects() {
        val inset = 15.dp
        mutableRects.clear()

        children.mapNotNullTo(mutableRects) {
            if (it is TextView || it == badgeView) {
                null
            } else {
                val rect = Rect()
                it.getHitRect(rect)
                rect.inset(-inset, -inset)
                Pair(it, rect)
            }
        }
    }

    private var lastTouchDelegateView: View? = null

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val e = event?: return false

        val x = e.x.toInt()
        val y = e.y.toInt()

        var handled = false

        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchDelegateView = mutableRects.firstOrNull {
                    it.second.contains(x, y)
                }?.first
            }

            MotionEvent.ACTION_POINTER_DOWN,
            MotionEvent.ACTION_POINTER_UP,
            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_MOVE -> {
            }

            MotionEvent.ACTION_CANCEL -> {
                lastTouchDelegateView = null
            }
        }

        val delegate = lastTouchDelegateView
        if (delegate != null) {

            // Offset event coordinates to be inside the target view
            event.setLocation((delegate.getWidth() / 2).toFloat(), (delegate.getHeight() / 2).toFloat())
            handled = delegate.dispatchTouchEvent(event)
        }

        return handled
    }
}