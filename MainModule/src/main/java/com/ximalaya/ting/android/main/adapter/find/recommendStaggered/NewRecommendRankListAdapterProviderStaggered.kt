package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.autosize.AutoSize
import com.ximalaya.ting.android.framework.autosize.AutoSizeConfig
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.GuessYouLikeHorMoreUtil
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.AlbumRank
import com.ximalaya.ting.android.main.model.rec.RankExtraData
import com.ximalaya.ting.android.main.model.rec.RankSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendRankListItem
import com.ximalaya.ting.android.main.rankModule.AggregateRankUtil
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 排行榜 平铺样式
 */
class NewRecommendRankListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<NewRecommendRankListAdapterProviderStaggered.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<NewRecommendRankListAdapterProviderStaggered.ViewHolder, RecommendItemNew> {
    private var mModulePosition = -1
    private var mOldState = RecyclerView.SCROLL_STATE_IDLE
    private var mRankListCardViewHolder: ViewHolder? = null

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_rank_list_new, parent, false)
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val recyclerView: RecyclerView = itemView.findViewById(R.id.main_rv_rank_list)
        var startSnapHelper: StartSnapHelper? = null
    }

    private fun getCurTag(position: Int, recommendItemNew: RecommendItemNew): String {
        return "${position}_${recommendItemNew.hashCode()}_${BaseUtil.getScreenWidth(BaseApplication.mAppInstance)}"
    }

    override fun createViewHolder(convertView: View?): ViewHolder? {
        PerformanceMonitor.traceBegin("Rank_New_createViewHolder")
        if (convertView == null) {
            return null
        }
        val rankListCardViewHolder = ViewHolder(convertView)
        PerformanceMonitor.traceEnd("Rank_New_createViewHolder", 5)
        return rankListCardViewHolder
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun bindViewHolder(
        holder: ViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        mRankListCardViewHolder = holder
        val recommendRankListItem = recommendItemNew.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }

        val saveTag = holder.itemView.getTag(R.id.main_rv_rank_list)
        val curTag = getCurTag(position, recommendItemNew)
        if (saveTag == curTag) {
            return
        }
        holder.itemView.setTag(R.id.main_rv_rank_list, curTag)

        // 过滤低于四条的数据  认为是异常数据
        val rankingList = recommendRankListItem.list?.filter {
            (it.subElements?.size ?: 0) >= 4
        }

        if (rankingList.isNullOrEmpty()) {
            return
        }
        mModulePosition = position
        PerformanceMonitor.traceBegin("Rank_New_bindViewHolder_" + recommendRankListItem.title)

        // 习惯听新样式 在习惯听下面固定3行
        SPAN_COUNT = if (RecommendFragmentTypeManager.isNewSceneCard() && position == 1) {
            3
        } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable()
            && position == 1
        ) {
            2
        } else {
            3
        }
        RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
            this.javaClass.simpleName,
            SPAN_COUNT,
            position,
            recommendItemNew
        )


        val landingPage = recommendRankListItem.extraInfo?.landingPage
        val enableMore = !landingPage.isNullOrEmpty()

        val listAdapter = RankListAdapter(
            fragment, this, rankingList, mModulePosition,
            recommendItemNew, recommendRankListItem, holder.recyclerView
        )
        listAdapter.mEnableMoreItem = enableMore
        listAdapter.setRelaseJumpActivityListener {
            val trace = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendRankListItem.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
            trace.createTrace()

            jump(fragment, landingPage)
        }

        // 新习惯听样式  12改成10
        val params = holder.recyclerView.layoutParams as MarginLayoutParams
        if (RecommendFragmentTypeManager.isNewSceneCard()) {
            params.bottomMargin = 10.dp
            params.topMargin = 10.dp
        } else {
            params.bottomMargin = 12.dp
            params.topMargin = 12.dp
        }
        holder.recyclerView.layoutParams = params

        holder.recyclerView.run {
            val linearLayoutManager = LinearLayoutManager(
                holder.recyclerView.context, LinearLayoutManager.HORIZONTAL, false
            )
            layoutManager = linearLayoutManager
            adapter = listAdapter
            clearOnScrollListeners()
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == mOldState) {
                        return
                    }
                    mOldState = newState
                    if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                        traceOnItemShow(recommendItemNew, mModulePosition, holder)

                        val oldIndex = recommendRankListItem.innerListSelectedIndex
                        val newIndex = linearLayoutManager.findFirstCompletelyVisibleItemPosition()
                        recommendRankListItem.innerListSelectedIndex = newIndex

                        changeListIndexStyle(oldIndex, newIndex, holder.recyclerView)
                    }
                }
            })

            if (holder.startSnapHelper == null) {
                holder.startSnapHelper = StartSnapHelper(6.dp)
                holder.startSnapHelper!!.attachToRecyclerView(this)
                holder.startSnapHelper!!.setContainerView(this)
            }

            if (recommendRankListItem.innerListSelectedIndex > 0) {
                scrollToPositionTab(
                    this,
                    recommendRankListItem.innerListSelectedIndex,
                    false
                )
            }
        }

        PerformanceMonitor.traceEnd("Rank_New_bindViewHolder_" + recommendRankListItem.title, 6)
    }

    private fun scrollToPositionTab(recyclerView: RecyclerView, position: Int, smooth: Boolean) {
        val offset = 6.dp
        if (smooth) {
            recyclerView.smoothScrollToPositionWithOffset(position, offset)
        } else {
            val manager = recyclerView.layoutManager as? LinearLayoutManager ?: return
            manager.scrollToPositionWithOffset(position, offset)
        }
    }

    class RankListAdapter(
        private val fragment: BaseFragment2,
        private val rankStaggered: NewRecommendRankListAdapterProviderStaggered,
        list: List<AlbumRank>?,
        private val modulePosition: Int,
        private val recommendItemNew: RecommendItemNew?,
        private val recommendRankListItem: RecommendRankListItem,
        private val recyclerView: RecyclerView
    ) : HorizontalMoreBaseAdapter() {

        // 专辑列表
        private val rankSubElementList = mutableListOf<AlbumRank>()

        init {
            list?.let {
                rankSubElementList.addAll(list)
            }
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {

            fixAutoSize()

            return ListItemViewHolder(
                ViewPool.getInstance().getView(
                    HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                    R.layout.main_fra_recommend_new_rank_list_layout,
                    parent,
                    false,
                    "RankList"
                )
            )
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrap(holder, position)
                }
            } else {
                onBindViewHolderWrap(holder, position)
            }
        }

        private fun onBindViewHolderWrap(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is ListItemViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                moreRootView?.run {
                    if (layoutParams == null) {
                        layoutParams =
                            LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT)
                    } else {
                        val rootLayoutParams = layoutParams
                        rootLayoutParams.height = LayoutParams.MATCH_PARENT
                        layoutParams = rootLayoutParams
                    }
                }
                horizontalView?.run {
                    val horLayoutParams = layoutParams as? MarginLayoutParams
                    horLayoutParams?.width = 37.dp
                    horLayoutParams?.height = LayoutParams.MATCH_PARENT
                    horLayoutParams?.marginStart = 19.dp
                    layoutParams = horLayoutParams
                }

                GuessYouLikeHorMoreUtil.fixHorMoreView(recyclerView, moreRootView, position)
            }
        }

        fun onBindViewHolderInner(holder: ListItemViewHolder, position: Int) {
            val curAlbumRank = rankSubElementList.getOrNull(position) ?: return

            (holder.llContainer.background as? GradientDrawable)?.run {
                cornerRadius = RecommendCornerUtils.getNewRankBgCorner().toFloat()
            }

            holder.llTitleContainer.minimumHeight = RecommendCornerUtils.getNewRankTitleHeight()

            // 限制文字最大宽度
            holder.llTitle.post {
                val maxWidth = holder.llTitle.measuredWidth - 22.dp
                if (maxWidth > 100.dp) {
                    holder.tvTitle.maxWidth = maxWidth
                } else {
                    holder.tvTitle.maxWidth = Int.MAX_VALUE
                }
            }

            holder.tvTitle.text = curAlbumRank.title
            val landingPageName = curAlbumRank.extraInfo?.get("landingPageName")
            if (landingPageName.isNullOrEmpty()) {
                holder.tvMore.text = "全部"
            } else {
                holder.tvMore.text = landingPageName
            }

            holder.tvTitle.setOnClickListener {
                // 新首页-榜单-标题  点击事件
                val trace = XMTraceApi.Trace()
                    .click(65926) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("xmRequestId", recommendItemNew?.xmRequestId)
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传。从 1 开始计数
                    .put("tabPosition", (position + 1).toString()) // 客户端传。从 1 开始计数
                    .put("rankId", curAlbumRank.id?.toString())

                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank.ubtV2)
                trace.createTrace()

                val trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                    .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
                SpmTraceUtil.addSpmTraceInfo(
                    trace1,
                    recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendRankListItem.ubtV2)
                trace1.createTrace()

                if (curAlbumRank.landingPage.isNullOrEmpty()) {
                    RecommendFragmentPageErrorManager.uploadDataError("排行榜iTing为空", null)
                    return@setOnClickListener
                }
                ToolUtil.clickUrlAction(fragment, curAlbumRank.landingPage, null)
            }

            HomeMoreColorUtil.filterColor(holder.tvMore)
            holder.tvMore.setOnClickListener {
                rankStaggered.itemMoreClickInner(
                    curAlbumRank,
                    recommendRankListItem.list,
                    recommendRankListItem,
                    recommendItemNew,
                    position
                )
            }

            val layoutParams = holder.flRootView.layoutParams as MarginLayoutParams
            layoutParams.width = RpAdaptUtil.rp2PxIn375(299 - getOffset())

            if (rankSubElementList.size - 1 == position) {
                layoutParams.width += if (mEnableMoreItem) {
                    0
                } else {
                    16.dp // 无加载更多时  右边间距16
                }
            }

            // 左边间距16  padding 10
            layoutParams.marginStart = if (position == 0) {
                6.dp
            } else {
                0
            }

            holder.flRootView.layoutParams = layoutParams

            val left = 10.dp
            // 无加载更多时  右边间距16
            val right = if (position == rankSubElementList.size - 1 && !mEnableMoreItem) {
                16.dp
            } else {
                0
            }
            holder.flRootView.setPadding(left, 0, right, 0)

            var list: List<RankSubElement>? = null
            curAlbumRank.subElements?.run {
                list = subList(0, SPAN_COUNT.coerceAtMost(size))
            }
            if (!list.isNullOrEmpty()) {
                holder.recyclerView.run {
                    layoutManager = LinearLayoutManager(
                        context, LinearLayoutManager.VERTICAL, false
                    )
                    adapter = AlbumItemAdapter(
                        fragment,
                        list,
                        modulePosition,
                        curAlbumRank,
                        recommendItemNew,
                        recommendRankListItem,
                        position
                    )
                }
            }
        }


        override fun getItemCount(): Int {
            return rankSubElementList.size + if (mEnableMoreItem) {
                1
            } else {
                0
            }
        }

        class ListItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var flRootView: ViewGroup = view.findViewById(R.id.main_fl_root_view)
            var llContainer: ViewGroup = view.findViewById(R.id.main_ll_content)
            var llTitleContainer: ViewGroup = view.findViewById(R.id.main_ll_title_container)
            var llTitle: ViewGroup = view.findViewById(R.id.ll_title)
            var tvTitle: TextView = view.findViewById(R.id.main_tv_module_title)
            var tvMore: TextView = view.findViewById(R.id.main_tv_more)
            var recyclerView: RecyclerView = view.findViewById(R.id.main_rv_item_list)
        }
    }

    fun itemMoreClickInner(
        curAlbumRank: AlbumRank,
        rankingList: List<AlbumRank>?,
        recommendRankListItem: RecommendRankListItem,
        recommendItemNew: RecommendItemNew?,
        position: Int,
        action: String = "click"
    ) {
        if (curAlbumRank.landingPage.isNullOrEmpty()) {
            RecommendFragmentPageErrorManager.uploadDataError("排行榜iTing为空", null)
            return
        }
        val trace1 = XMTraceApi.Trace()
            .click(62176) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("modulePosition", (mModulePosition + 1).toString()) // 客户端传
            .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
        SpmTraceUtil.addSpmTraceInfo(
            trace1,
            recommendRankListItem.ubtV2,
            (mModulePosition + 1).toString()
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendRankListItem.ubtV2)
        trace1.createTrace()
        // 新首页-榜单-查看完整榜单  点击事件
        val trace = XMTraceApi.Trace()
            .click(61036) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("tabName", curAlbumRank.title ?: "") // 根据实际文案
            .put("xmRequestId", recommendItemNew?.xmRequestId)
            .put("rankId", curAlbumRank.id?.toString() ?: "")
            .put("modulePosition", (mModulePosition + 1).toString())
            .put("action", action)
            .put("positionNew", (position + 1).toString()) // 双端统一从1开始，从左到右
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            recommendRankListItem.ubtV2,
            (mModulePosition + 1).toString(),
            tab1Title = curAlbumRank.title,
            tab1Position = (recommendRankListItem.innerListSelectedIndex + 1).toString(),
            tab2Title = "更多",
            tab2Position = "d01"
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
        RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank.ubtV2)
        trace.createTrace()

        // 保存推荐榜单数据  排行榜界面要用
        AggregateRankUtil.clearRankMapData()
        if (!rankingList.isNullOrEmpty()) {
            rankingList.forEach { albumRank ->
                if (albumRank.extraInfo != null && albumRank.extraInfo!!["rankType"] == "recRank") {
                    albumRank.subElements?.forEach { subElement ->
                        val businessExtraInfo =
                            if (subElement.extraInfo?.subRefInfo?.ad == true) subElement.extraInfo.subRefInfo!!.businessExtraInfo else null
                        val extraData = RankExtraData(subElement.ubt, businessExtraInfo)
                        AggregateRankUtil.addRankMapData(subElement.refId ?: 0L, extraData)
                    }
                    return@forEach
                }
            }
        }
        ToolUtil.clickUrlAction(fragment, curAlbumRank.landingPage, null)
    }

    private fun traceOnAlbumItemShow(
        recyclerView: RecyclerView,
        albumRank: AlbumRank?,
        data: RecommendItemNew?,
        recommendRankListItem: RecommendRankListItem
    ) {
        if (data == null || albumRank == null || recommendRankListItem.list.isNullOrEmpty()
            || albumRank.subElements.isNullOrEmpty()
        ) {
            return
        }

        for (i in 0 until recyclerView.childCount) {
            val itemView = recyclerView.getChildAt(i) ?: continue
            if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                val rankSubElement =
                    itemView.getTag(R.id.main_id_item_data) as? RankSubElement ?: continue
                val index = itemView.getTag(R.id.main_id_data_index) as? Int ?: continue
                val advertis = itemView.getTag(R.id.main_id_data_ad_info)

                // 新首页-榜单-专辑卡片  曝光
                val trace = XMTraceApi.Trace()
                    .setMetaId(61032)
                    .setServiceId("slipPage") // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("tabName", albumRank.title ?: "") // 根据实际文案
                    .put(
                        "exploreArea",
                        ViewStatusUtil.getViewVisibleAreaRealPercent(itemView).toString()
                    ) // 可见区域占屏幕的比例
                    .put("albumId", rankSubElement.refId?.toString() ?: "")
                    .put("contentType", "album")
                    .put("contentId", rankSubElement.refId?.toString() ?: "")
                    .put("positionNew", (index + 1).toString()) // 双端统一从1开始
                    .put("xmRequestId", data.xmRequestId)
                    .put("rec_track", rankSubElement.ubt?.recTrack ?: "")
                    .put("rec_src", rankSubElement.ubt?.recSrc ?: "")
                    .put("ubtTraceId", rankSubElement.ubt?.traceId ?: "")
                    .put("rankId", albumRank.id?.toString() ?: "")
                    .put("modulePosition", (mModulePosition + 1).toString())
                    .put("socialTagId", rankSubElement.getSocialTagId()?.toString())
                    .put("isAd", if (advertis != null) "true" else "false")
                    .put("tabPosition", (recommendRankListItem.innerListSelectedIndex + 1).toString()) // 客户端传。从 1 开始计数
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendRankListItem.ubtV2,
                    (mModulePosition + 1).toString(),
                    albumRank.title,
                    (recommendRankListItem.innerListSelectedIndex + 1).toString(),
                    contentTitle = rankSubElement.title,
                    contentPosition = (index + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement.ubtV2)
                if (data.isLocalCache) {
                    trace.isLocalCache
                }
                trace.createTrace()
                if (ConstantsOpenSdk.isDebug && !data.isLocalCache && TextUtils.isEmpty(data.xmRequestId)) {
                    CustomToast.showToast("排行榜xmRequestId异常，请保存现场联系首页开发")
                }
                // 广告曝光上报
                if (advertis != null && advertis is Advertis && !advertis.isShowedToRecorded && !data.isLocalCache) {
                    AdManager.adRecord(BaseApplication.getMyApplicationContext(), advertis,
                            AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                    AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST)
                                    .positionNew(index + 1)
                                    .modulePosition(mModulePosition + 1)
                                    .build())
                    advertis.isShowedToRecorded = true
                }
                if (advertis != null && advertis is Advertis && !advertis.isRecordedSubPercent && !data.isLocalCache
                        && ViewStatusUtil.getViewVisibleAreaRealPercent(itemView) >= AD_REPORT_SUB_PERCENT) {
                    // 做曝光50%的上报
                    AdManager.reportSubPercentShow(advertis, data.xmRequestId)
                    advertis.isRecordedSubPercent = true
                }

                HomeRealTimeTraceUtils.traceItemShow(data, recommendRankListItem, albumRank, rankSubElement, itemView, index)
            }
        }
    }

    fun checkAdCunt(list: List<AlbumRank>?): Int {
        if (list.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (albumRank in list) {
            val subElements = albumRank.subElements
            if (!subElements.isNullOrEmpty()) {
                for (subElement in subElements) {
                    val adInfo = subElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfo
                    if (!adInfo.isNullOrEmpty()) {
                        adCount++
                    }
                }
            }
        }
        return adCount
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ViewHolder?
    ) {
        if (holder == null || data == null) {
            return
        }

        val recommendRankListItem = data.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }

        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }
        if (recommendRankListItem.cardAdCount == -1) {
            recommendRankListItem.cardAdCount = checkAdCunt(rankingList)
        }
        if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
            // 新首页-首页大卡模块  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62177)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("xmRequestId", data.xmRequestId) // 客户端传
                .put("contentType", data.itemType) // 客户端传
                .put("contentId", recommendRankListItem.id.toString()) // 客户端传
                .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendRankListItem.ubtV2,
                (position + 1).toString()
            )
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
            trace.createTrace()
        }

        (holder.recyclerView.layoutManager as? LinearLayoutManager)?.run {
            val firstVisibleItemPosition = findFirstVisibleItemPosition()
            val lastVisibleItemPosition = findLastVisibleItemPosition()

            for (i in firstVisibleItemPosition..lastVisibleItemPosition) {
                val itemView = findViewByPosition(i) ?: continue
                val recyclerView: RecyclerView =
                    itemView.findViewById(R.id.main_rv_item_list) ?: continue
                val albumRank = rankingList.getOrNull(i)
                traceOnAlbumItemShow(recyclerView, albumRank, data, recommendRankListItem)
            }
        }

    }

    private fun changeListIndexStyle(
        oldIndex: Int,
        newIndex: Int,
        recyclerView: RecyclerView?
    ) {
        if (recyclerView == null) {
            return
        }

        (recyclerView.layoutManager)?.run {
            val oldItemView = findViewByPosition(oldIndex)
            val newItemView = findViewByPosition(newIndex)
            val oldRecyclerView: RecyclerView? =
                oldItemView?.findViewById(R.id.main_rv_item_list)
            val newRecyclerView: RecyclerView? =
                newItemView?.findViewById(R.id.main_rv_item_list)

            val payload = "changeListIndexStyle"
            val itemCount = 3.coerceAtMost(SPAN_COUNT)
            oldRecyclerView?.adapter?.notifyItemRangeChanged(0, itemCount, payload)
            newRecyclerView?.adapter?.notifyItemRangeChanged(0, itemCount, payload)
        }
    }

    @Suppress("DEPRECATION")
    class AlbumItemAdapter(
        private val fragment: BaseFragment2,
        private val list: List<RankSubElement>?,
        private val modulePosition: Int,
        private val albumRank: AlbumRank?,
        private val recommendItemNew: RecommendItemNew?,
        private val recommendRankListItem: RecommendRankListItem?,
        private val parentPosition: Int
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        // 专辑列表
        private val rankSubElementList = mutableListOf<RankSubElement>()
        private var mTypeface: Typeface? = null

        init {
            list?.let {
                rankSubElementList.addAll(list)
            }
            mTypeface =
                Typeface.createFromAsset(fragment.resources.assets, "fonts/XmlyNumberV1.0-Regular.otf")
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return TingAlbumViewHolder(
                ViewPool.getInstance().getView(
                    HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                    R.layout.main_fra_recommend_new_rank_list_album_item,
                    parent,
                    false,
                    "RankList"
                )
            )
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrap(holder, position)
                }
            } else {
                onBindViewHolderWrap(holder, position)
            }
        }

        private fun onBindViewHolderWrap(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is TingAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            }
        }

        private fun updateSize(holder: TingAlbumViewHolder) {
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)
            holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getCoverSize())
            val padding = RecommendCornerUtils.getPaddingSize()

            val coverParams = holder.albumCoverLayoutView.layoutParams as? MarginLayoutParams
            if (coverParams != null) {
                coverParams.topMargin = padding
                coverParams.bottomMargin = padding
                holder.albumCoverLayoutView.layoutParams = coverParams
            }

            val params = holder.showTagParentView.layoutParams as? MarginLayoutParams
            if (params != null) {
                params.topMargin = RecommendCornerUtils.getShowTagGapSize()
                holder.showTagParentView.layoutParams = params
            }
        }

        private fun onBindViewHolderInner(holder: TingAlbumViewHolder, position: Int) {
            val rankSubElement = rankSubElementList.getOrNull(position)
            updateSize(holder)
            if (rankSubElement == null) {
                holder.tvAlbumIndex.setTextIfChanged((position + 1).toString())
                if (mTypeface != null) {
                    holder.tvAlbumIndex.typeface = mTypeface
                }

                if (parentPosition == recommendRankListItem?.innerListSelectedIndex && position < 3
                ) {
                    holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.host_color_xmRed))
                } else {
                    holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.main_color_662c2c3c_444444))
                }

                holder.albumTitleTv.setTextIfChanged("")
                holder.albumCoverLayoutView.setAlbumCover("")

                holder.itemView.setOnClickListener(null)
                holder.itemView.setOnLongClickListener(null)
                return
            }
            holder.albumTitleTv.setBackgroundColor(Color.TRANSPARENT)
            RecommendCornerUtils.updateTitleColor(holder.albumTitleTv)

            holder.itemView.setTag(R.id.main_id_item_data, rankSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            val isAd = rankSubElement.extraInfo?.subRefInfo?.ad ?: false
            val adTagShow = rankSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            var advertis: Advertis? = null
            if (isAd) {
                advertis = rankSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfoObject
            }
            holder.itemView.setTag(R.id.main_id_data_ad_info, advertis)

            holder.itemView.setOnOneClickListener {
                onItemClickInner(rankSubElement, position, holder.itemView, advertis)
            }

            holder.tvAlbumIndex.setTextIfChanged((position + 1).toString())
            holder.albumTitleTv.setTextIfChanged(rankSubElement.title)
            fixTextSize(holder.albumTitleTv)
            if (mTypeface != null) {
                holder.tvAlbumIndex.typeface = mTypeface
            }

            if (parentPosition == recommendRankListItem?.innerListSelectedIndex
                && position < 3) {
                holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.host_color_xmRed))
            } else {
                holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.main_color_662c2c3c_444444))
            }

            rankSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            rankSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            holder.albumCoverLayoutView.updateSize(60.dp.toFloat())

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                rankSubElement.extraInfo?.other?.getBRTagUrl()
            )

            holder.llTextContainer.post {
                val textViewContainerWithInPx = holder.llTextContainer.measuredWidth
                ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
                val adWidth = if (adTagShow) 24 else 0
                RecommendShowTagsUtilNew.bindTagsView(
                    holder.layoutShowTags, rankSubElement.extraInfo?.showTags,
                    textViewContainerWithInPx - adWidth.dp, "", ""
                )

                val albumTitleOneLine = RecommendShowTagsUtilNew.canShowOneLine(
                    holder.albumTitleTv, rankSubElement.title, textViewContainerWithInPx
                )

                if (!rankSubElement.extraInfo?.showTags2.isNullOrEmpty() && albumTitleOneLine) {
                    RecommendShowTagsUtilNew.bindTagsView(
                        holder.layoutShowTags2,
                        rankSubElement.extraInfo?.showTags2,
                        textViewContainerWithInPx,
                        "",
                        "",
                        RecommendShowTagsUtilNew.THEME_SHOWTAG2
                    )
                    RecommendCornerUtils.updateSubTitleMargin(holder.layoutShowTags2)
                    RecommendCornerUtils.updateSubTitleMargin(holder.layoutShowTags2)
                } else {
                    holder.layoutShowTags2.visibility = View.GONE
                }
            }

            holder.itemView.setOnLongClickListener {
                val build = MoreFuncBuild.createAlbumLongClickModel(
                    fragment, rankSubElement.refId ?: 0,
                    object : IMoreFuncListener() {
                        override fun onSubscriptionClick(
                            isSubscription: Boolean,
                            btnText: String?
                        ) {

                        }
                    })
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", recommendItemNew?.itemType)
                    put("contentId", rankSubElement.refId?.toString() ?: "")
                    put("modulePosition", (modulePosition + 1).toString())
                    put("positionNew", (position + 1).toString())

                    albumRank?.ubtV2?.let { it1 -> putAll(it1) }
                    rankSubElement.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap
                XmMoreFuncManager.checkShowMorePage(build)
                true
            }
        }

        private var curScale = 0f
        private val maxScale = 1.22f

        private fun fixTextSize(albumTitleTv: TextView) {
            val displayMetrics = albumTitleTv.context.resources.displayMetrics
            if (curScale == 0f) {
                displayMetrics?.run {
                    curScale = scaledDensity / density
                }
            }
            if (curScale >= maxScale) {
                albumTitleTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f * maxScale)
            } else {
                albumTitleTv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
            }

//            Log.i(
//                "dqq1", "curScale:${curScale} maxScale:${maxScale} " +
//                        "scaledDensity:${displayMetrics.scaledDensity} density:${displayMetrics.density}"
//            )
        }

        fun onItemClickInner(
            rankSubElement: RankSubElement,
            position: Int,
            view: View?,
            advertis: Advertis?
        ) {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put(
                    "card_adTopn",
                    ((recommendItemNew!!.item as RecommendRankListItem).cardAdCount.toString())
                )
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew.item as RecommendRankListItem).ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1, (recommendItemNew.item as RecommendRankListItem).ubtV2
            )
            trace1.createTrace()
            // 新首页-榜单-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(61031) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("tabName", albumRank?.title ?: "") // 根据实际文案
                .put("albumId", rankSubElement.refId?.toString() ?: "")
                .put("contentType", "album")
                .put("contentId", rankSubElement.refId?.toString() ?: "")
                .put("positionNew", (position + 1).toString())
                .put("xmRequestId", recommendItemNew.xmRequestId)
                .put("rec_track", rankSubElement.ubt?.recTrack ?: "")
                .put("rec_src", rankSubElement.ubt?.recSrc ?: "")
                .put("ubtTraceId", rankSubElement.ubt?.traceId ?: "")
                .put("rankId", albumRank?.id?.toString() ?: "")
                .put("tabPosition", ((recommendItemNew.item as RecommendRankListItem).innerListSelectedIndex + 1).toString())
                .put("modulePosition", (modulePosition + 1).toString())
                .put(
                    "exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                )
                .put(
                    "socialTagId", rankSubElement.getSocialTagId().toString()
                )
                .put("isAd", if (advertis != null) "true" else "false")
                .put("area", "item")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                (recommendItemNew.item as RecommendRankListItem).ubtV2,
                (modulePosition + 1).toString(),
                albumRank?.title,
                ((recommendItemNew.item as RecommendRankListItem).innerListSelectedIndex + 1).toString(),
                contentTitle = rankSubElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace, (recommendItemNew.item as RecommendRankListItem).ubtV2
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank?.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement.ubtV2)
            trace.createTrace()
            if (advertis != null) {
                // 广告点击上报
                AdManager.handlerAdClick(BaseApplication.getMyApplicationContext(), advertis,
                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST)
                                .positionNew(position + 1)
                                .modulePosition(modulePosition + 1)
                                .build())
                return
            }
            jump(fragment, rankSubElement.landingPage)
        }

        override fun getItemCount(): Int {
            return rankSubElementList.size
        }

        class TingAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var albumTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var tvAlbumIndex: TextView = view.findViewById(R.id.main_tv_album_index)
            var layoutShowTags2: LinearLayout = view.findViewById(R.id.main_layout_show_tag2)
            var cslContainerView: ConstraintLayout =
                view.findViewById(R.id.main_container_layout_album)
            var llTextContainer: LinearLayout = view.findViewById(R.id.main_ll_text_container)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)
            var showTagParentView: ViewGroup = view.findViewById(R.id.main_show_tags_and_ad_layout)
        }
    }

    companion object {
        private var SPAN_COUNT = 4
        private var AD_REPORT_SUB_PERCENT = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_HOME_AD_EXPOSE_PERCENT, 50)

        private fun fixAutoSize() {
            BaseApplication.getMainActivity()?.let {
                if (AutoSizeConfig.getInstance().application != null && !AutoSizeConfig.getInstance().isStop) {
                    AutoSize.autoConvertDensityOfGlobal(it)
                }
            }
        }
        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }
    }

    // tab切换事件
    interface OnTabSelectListener {

        fun onSelect(position: Int, isFromClick: Boolean)
    }
}