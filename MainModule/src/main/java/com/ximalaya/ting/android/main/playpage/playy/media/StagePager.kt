package com.ximalaya.ting.android.main.playpage.playy.media

import android.content.res.Configuration
import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.children
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import androidx.viewpager2.widget.ViewPager2
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.play.PlayPageAbUtil
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.playpage.audiointerface.IAudioPlayPageLifecycle
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.YNormalCoversComponentY
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverPriorityConfig
import com.ximalaya.ting.android.main.playpage.playy.component.danmu.XDanmakuComponent
import com.ximalaya.ting.android.main.playpage.playy.component.video.YVideoComponent
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher
import com.ximalaya.ting.android.main.playpage.playy.media.tab.derivative.DerivativeComponent
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.skin.SkinChangeListener
import com.ximalaya.ting.android.main.playpage.playy.view.PlayTypeIndicator
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger


class StagePager(
    private val playContainer: IPlayContainer,
    private val pager: ViewPager2,
    private val bViewPager: ViewPager2,
    private val topViewPager: ViewPager2,
    private val playTypeIndicator: PlayTypeIndicator,
    private val bottomRefView: View?,
    private val coverComponent: YNormalCoversComponentY,
    private val docComponent: DocComponentWrapper,
    private val videoComponent: YVideoComponent
): IAudioPlayPageLifecycle, SkinChangeListener {
    private val TAG = "StagePager"

    private val defaultBgColor = 0x12000000.toInt()
    private val defaultMaskColor = 0x19ffffff.toInt()

    private val picMaskColor = 0x26ffffff.toInt()
    private val picBgColor = 0x33000000.toInt()

    private val componentsMap = mapOf(
        PlayModeSwitcher.PlayMode.COVER to coverComponent,
        PlayModeSwitcher.PlayMode.DOC to docComponent,
        PlayModeSwitcher.PlayMode.VIDEO to videoComponent
    )

    private val derivativeComponent = DerivativeComponent(
        playContainer.getContext()!!,
        playContainer,
        playContainer.scope()
    )

    private val docView: View
    private val coverView: View
    private val videoView: View
    private val derivativeView: View

    private var lastPostion = -1
    private var lastPlayMode: PlayModeSwitcher.PlayMode? = null

    private var currAlbumId: Long = 0L
    private var currTrackId: Long = 0L

    private var needShowThisComponent = true
    private var isInVideoImmersive = false
    private var needIndicatorWithOrientation = true
    private var isDragging = false

    private var danmakuComponent: XDanmakuComponent? = null

    private val inTopOffset = 180.dp

    private var enableDrag = true

    private var playPageOffset = 0

    private var firstSelect = true

    private val backgroundViewPager: BackgroundPager =
        BackgroundPager(playContainer as YPlayFragment, bViewPager, topViewPager)

    init {
        bViewPager.isUserInputEnabled = false
        topViewPager.isUserInputEnabled = false

        // init component view
        coverComponent.onCreate(playContainer as BaseFragment2)
        coverView = coverComponent.view

        docComponent.onCreate(playContainer as BaseFragment2)
        docView = docComponent.view

        derivativeView = derivativeComponent.onCreateView()

        videoView = videoComponent.view

        val bgRecyclerView = (bViewPager.children.first() as RecyclerView)
        val topRecyclerView = (topViewPager.children.first() as RecyclerView)

        (pager.children.first() as RecyclerView).addOnScrollListener(object : OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                Logger.d(TAG, "topViewPager start onScrolled; = $dx")

                if(dx > 0 || dx < 0) {
                    bgRecyclerView.scrollBy(dx,0)
                    topRecyclerView.scrollBy(dx,0)
                }
            }
        })

        pager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                Logger.d(TAG, "onPageSelected; position = $position")
                updateIndex(position)

                val currentMode = modes.getOrNull(position)?: return

                if (!firstSelect) {
                    XMTraceApi.Trace()
                        .click(64997) // 用户点击时上报
                        .put("currPage", "newPlay")
                        .put(XmRequestIdManager.XM_REQUEST_ID,
                            XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                        )
                        .put("currAlbumId", "$currAlbumId")
                        .put("currTrackId", "$currTrackId")
                        .put("description", modes.map { mode -> mode.label }.joinToString(separator = "-")) // 传当前声音 tab 的所有文案并用-拼接，例如：封面-文稿
                        .put("Item", currentMode.label) // 传点击的 tab 文案，例如：文稿
                        .createTrace()
                }
                firstSelect = false

                updateDanmakuVisibility()
            }

            override fun onPageScrollStateChanged(state: Int) {
                if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                    isDragging = true
                } else if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    isDragging = false

                    bViewPager.currentItem = pager.currentItem
                    topViewPager.currentItem = pager.currentItem
                }

                if (state != ViewPager2.SCROLL_STATE_IDLE) {
                    (playContainer as YPlayFragment).forceHideBottomBar()
                    (playContainer as YPlayFragment).hideFloatingView()
                }

                updateDanmakuVisibility()
            }
        })

        playContainer.addLifecycleListener(this)

        playTypeIndicator.onClick = {
            val currentMode = modes.getOrNull(it)
            if (currentMode != null) {
                isDragging = true
                PlayModeSwitcher.savePlayModeForAlbum(currAlbumId, currentMode)
                pager.currentItem = it
            }
        }

        videoComponent.onImmersiveChange = {
            isInVideoImmersive = it
            updateIndicatorVisibility()
        }

        playContainer.registerSkinChangeListener(this)
    }


    private var modes: List<PlayModeSwitcher.PlayMode> = emptyList()

    fun updateSkin(blurSkin: Boolean) {
        backgroundViewPager.blurSkin(blurSkin)
    }

    private fun updateIndex(position: Int) {
        val currentMode = modes.getOrNull(position)?: return
        val tempLast = lastPlayMode

        var isLastVideoPlaying = false
        if (currentMode != lastPlayMode) {
            Logger.d(TAG, "updateIndex; $currentMode")
            if (lastPlayMode == PlayModeSwitcher.PlayMode.VIDEO) {
                isLastVideoPlaying = videoComponent.isPlaying()
            }

            lastPlayMode = currentMode

            componentsMap.forEach {
                dispatchLifeCycle(currentMode == it.key, it.key, it.value)
            }
            lastPostion = position


            if (currentMode == PlayModeSwitcher.PlayMode.VIDEO) {
                playContainer.switchToVideo(!isDragging)
            } else if (tempLast == PlayModeSwitcher.PlayMode.VIDEO) {
                playContainer.switchToAudio(isLastVideoPlaying || !isDragging)
            }

            if (isDragging) {
                PlayModeSwitcher.savePlayModeForAlbum(currAlbumId, currentMode)
            }

            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).setIsScreenDoc(currentMode == PlayModeSwitcher.PlayMode.DOC)
        }
    }

    private val adapter = object : RecyclerView.Adapter<ViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            return when (viewType) {
                PlayModeSwitcher.PlayMode.VIDEO.mode -> VideoHolder(FrameLayout(coverView.context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT)

                    (videoView.parent as? ViewGroup)?.removeView(videoView)
                    addView(videoView, ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT)
                    )
                })
                PlayModeSwitcher.PlayMode.DOC.mode -> DocHolder(docView)
                PlayModeSwitcher.PlayMode.SELL.mode -> SellHolder(StageFrameLayout(coverView.context).apply {
                    belowStatusBar = true
                    refBottomView = bottomRefView
                    contentView = derivativeView
                }.apply { layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ) })

                else -> CoverHolder(StageFrameLayout(coverView.context).apply {
                    belowStatusBar = true
                    refBottomView = bottomRefView
                    contentView = coverView

                    coverComponent.setBackgroundView(this.backgroundView)
                }.apply { layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ) })
            }
        }

        override fun getItemId(position: Int): Long {
            return modes[position].mode.toLong()
        }

        override fun getItemCount() = modes.size

        override fun getItemViewType(position: Int): Int {
            return modes[position].mode
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        }
    }

    private class CoverHolder(view: View): ViewHolder(view)
    private class DocHolder(view: View): ViewHolder(view)
    private class VideoHolder(view: View): ViewHolder(view)
    private class SellHolder(view: View): ViewHolder(view)

    fun setStageDanmaku(danmakuComponent: XDanmakuComponent) {
        this.danmakuComponent = danmakuComponent
        updateDanmakuVisibility()
    }

    private fun updateDanmakuVisibility() {
        val danmakuMode = lastPlayMode == PlayModeSwitcher.PlayMode.COVER ||
                lastPlayMode == PlayModeSwitcher.PlayMode.DOC && PlayPageAbUtil.hasDanmakuOndoc()
        danmakuComponent?.setDanmakuVisibility(danmakuMode)

        if (pager.visibility == View.VISIBLE && !isDragging && danmakuMode) {
            danmakuComponent?.showHideForStage(true, lastPlayMode)
            lastPlayMode?.also {
                danmakuComponent?.updateDanMuMaxLine(it)
            }
            return
        }
        danmakuComponent?.showHideForStage(false, lastPlayMode)
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        soundInfo?: return
        currTrackId = soundInfo.trackInfo?.trackId?: 0L
        currAlbumId = soundInfo.albumInfo?.albumId?: 0L

        modes = PlayModeSwitcher.validModes(soundInfo)
        modes.forEach {
            componentsMap[it]?.onSoundInfoLoaded(soundInfo)
        }
        derivativeComponent.onSoundInfoChanged(soundInfo)

        if (pager.adapter == null) {
            pager.adapter = adapter
            playTypeIndicator.attachViewPager(pager)
        } else {
            adapter.notifyDataSetChanged()
        }

        updateIndicators()

        if (!updateIndicatorVisibility() && playTypeIndicator.visibility == View.VISIBLE) {
            traceShow()
        }

        var mode = PlayModeSwitcher.targetPlayPageMode(currAlbumId, soundInfo)
        if (mode != PlayModeSwitcher.PlayMode.SELL && !modes.contains(PlayModeSwitcher.PlayMode.VIDEO)) {
            val derivativeGoodIndex = modes.indexOf(PlayModeSwitcher.PlayMode.SELL)
            if (derivativeGoodIndex >= 0 && PlayModeSwitcher.shouldAutoSwitchDerivativeTab(currAlbumId)) {
                mode = PlayModeSwitcher.PlayMode.SELL
                PlayModeSwitcher.markAutoSwitchDerivativeTab(currAlbumId)
            }
        }

        val index = modes.indexOf(mode)
        if (index == pager.currentItem) {
            updateIndex(pager.currentItem)
        }
        if (index >= 0) {
            pager.setCurrentItem(index, false)
        }
        backgroundViewPager.update(modes, index)
    }

    private fun updateIndicators() {
        val isSkinEnabled = PSkinManager.isEnabled
        playTypeIndicator.indicators = modes.map {
            val button = if (isSkinEnabled || it == PlayModeSwitcher.PlayMode.DOC && it.label != "文稿") {
                picMaskColor
            } else defaultMaskColor

            val bg = if (isSkinEnabled || it == PlayModeSwitcher.PlayMode.DOC && it.label != "文稿") {
                picBgColor
            } else defaultBgColor

            PlayTypeIndicator.IndicatorItem(
                text = it.label,
                buttonColor = button,
                backgroundColor = bg,
                border = if (it == PlayModeSwitcher.PlayMode.VIDEO) defaultMaskColor else Color.TRANSPARENT
            )
        }
    }

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        backgroundViewPager.onSkinChanged(newSkinConfig)
        updateIndicators()
    }

    private fun dispatchLifeCycle(show: Boolean, position: Int) {
        val mode = modes.getOrNull(position)?: return
        val component = componentsMap.get(mode)?: return
        dispatchLifeCycle(show, mode, component)
    }

    private fun dispatchLifeCycle(
        show: Boolean,
        mode: PlayModeSwitcher.PlayMode,
        component: XBaseCoverComponent
    ) {

        if (show) {
            if (!component.isResumed) {
                component.onResume()
            }
        } else {
            if (component.isResumed) {
                component.onPause()
            }
        }
    }

    private fun traceShow() {
        Logger.d(TAG, "traceShow 64998")
        XMTraceApi.Trace()
            .setMetaId(64998)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put(XmRequestIdManager.CONT_ID, "$currTrackId")
            .put(XmRequestIdManager.CONT_TYPE, "playTypeIndicator")
            .put(XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .put("currAlbumId", "$currAlbumId")
            .put("currTrackId", "$currTrackId")
            .put("description", modes.map { mode -> mode.label }.joinToString(separator = "-")) // 传当前声音 tab 的所有文案并用-拼接，例如：封面-文稿
            .createTrace()
    }

    private fun updateIndicatorVisibility(): Boolean {
        var changed = false
        if (needShowThisComponent && needIndicatorWithOrientation && !isInVideoImmersive && modes.size > 1) {
            if (playTypeIndicator.visibility != View.VISIBLE) {
                playTypeIndicator.visibility = View.VISIBLE
                traceShow()
                changed = true
            }

        } else {
            ViewStatusUtil.setVisible(View.GONE, playTypeIndicator)
            changed = true
        }

//        val forbiddenScroll = !needIndicatorWithOrientation || isInVideoImmersive
//        pager.isUserInputEnabled = !forbiddenScroll
//        bViewPager.isUserInputEnabled = !forbiddenScroll
        updateEnableDrag()

        return changed
    }

    private fun updateEnableDrag() {
        val forbiddenScroll = !needIndicatorWithOrientation || isInVideoImmersive
        val atTop = Math.abs(playPageOffset) < inTopOffset

        pager.isUserInputEnabled = atTop && !forbiddenScroll && modes.size > 1
    }

    fun onStageConfigUpdate(config: YCoverPriorityConfig.CoverMatchConfig?) {
        needShowThisComponent = config == null
        updateIndicatorVisibility()
        Logger.d(TAG, "needShowThisComponent = $needShowThisComponent")

        if (config == null) {
            if (pager.visibility != View.VISIBLE) {
                Logger.d(TAG, Log.getStackTraceString(Throwable()))
                ViewStatusUtil.setVisible(View.VISIBLE, pager)
                ViewStatusUtil.setVisible(View.VISIBLE, bViewPager, topViewPager)
                topViewPager.tag = ""
                dispatchLifeCycle(show = true, pager.currentItem)
            }
        } else {
            if (pager.visibility != View.GONE) {
                Logger.d(TAG, Log.getStackTraceString(Throwable()))
                ViewStatusUtil.setVisible(View.GONE, pager)
                ViewStatusUtil.setVisible(View.GONE, bViewPager, topViewPager)
                topViewPager.tag = "hide"

                dispatchLifeCycle(show = false, pager.currentItem)
            }
        }

        updateDanmakuVisibility()
    }

    fun onConfigurationChanged(newConfig: Configuration) {
        val needIndicator = YUtils.isFoldScreenWithExpand((playContainer as YPlayFragment).requireContext())
                || newConfig.orientation == Configuration.ORIENTATION_PORTRAIT
        needIndicatorWithOrientation = needIndicator

        updateIndicatorVisibility()
    }

    override fun onResume() {
        backgroundViewPager.onResume()
        dispatchLifeCycle(show = true, pager.currentItem)
        derivativeComponent.onResume()
        if (playTypeIndicator.visibility == View.VISIBLE) {
            traceShow()
        }
    }

    override fun onPause() {
        backgroundViewPager.onPause()
        derivativeComponent.onPause()
        dispatchLifeCycle(show = false, pager.currentItem)
    }

    override fun onDestroy() {
        derivativeComponent.onDestroy()
        componentsMap.values.forEach {
            it.onDestroy()
        }
    }

    override fun onThemeColorChanged(foregroundColor: Int, backgroundColor: Int) {
    }

    fun isVideoMode(): Boolean {
        return pager.currentItem == modes.indexOf(PlayModeSwitcher.PlayMode.VIDEO)
    }

    fun onPlayContainerScroll(offset: Int) {
        Logger.d(TAG, "onPlayContainerScroll: $offset")
        playPageOffset = offset
        updateEnableDrag()
    }

    fun updateDocTranslate(isOpen: Boolean) {
        docComponent.updateDocTranslate(isOpen)
    }
}
