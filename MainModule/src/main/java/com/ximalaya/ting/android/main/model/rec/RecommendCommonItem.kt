package com.ximalaya.ting.android.main.model.rec

import androidx.annotation.Keep
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.main.adapter.find.util.ColorValueModel
import com.ximalaya.ting.android.main.model.recommend.DislikeReasonModelV1
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import java.util.Random
import kotlin.collections.ArrayList

/**
 * 通用数据结构
 */
@Keep
data class RecommendCommonItem(
    // 内容库id
    val id: Long?,
    var moduleId: Long? = 0,
    val contentType: String? = "",
    val bizType: String? = "",
    // 听单标题
    var title: String? = "",
    val subTitle: String? = "",
    val cover: String? = null,
    val landingPage: String? = null,
    var subElements: MutableList<CommonSubElement>? = null,
    val ubt: Ubt? = null,
    val ubtV2: Map<String, String>?,
    var other: CommonOther? = null,
    val ext: Ext? = null,
    val extraInfo: Ext? = null,
    var cachedCoverColor: Int? = ColorUtil.INVALID_COLOR,
    var dislikeReasonNewV1: List<DislikeReasonModelV1>? = null,
    var cardAdCount: Int = -1,
    var historyIds: StringBuilder? = null,
    val anchor: Anchor? = null,
    val count: CollectedCount? = null,
    val interact: InteractModel? = null,
    var cacheColorModel: ColorValueModel? = null,
){
    fun validCacheColor(): Boolean {
        return cacheColorModel != null && cacheColorModel?.isDark == BaseFragmentActivity2.sIsDarkMode
    }
}

@Keep
data class RecommendElementBox(
    val index: Int = 0,
    val text: String? = null,
    val isSelected: Boolean = false
)

@Keep
data class RecommendElementCoverContent(
    val text: String? = null, // 中间图文案（价格）
    val subText: String? = null, // 中间图文案（划线价格）
    val subContent: String? = null, // 中间图文案（首两月）
    val status: Int = 0 // 权益状态，0｜待领取，1｜已领取，2｜已过期
)

@Keep
data class RecommendOtherDark(
    val buttonColor: String? = "", // 按钮文字颜色
    val gifCover: String? = "", // 猫头动效
    val staticCover: String? = "", // 猫头静图
    val buttonWordColor: String? = "", // 按钮文字颜色
    var cover: String? = null, // 封面
)

@Keep
data class CommonSubElement(
    // 内容库id
    val id: Long?,
    // 听单组合id
    val refId: Long?,
    // 听单标题
    var title: String? = "",
    var subTitle: String? = "",
    val summary: String? = "",
    val tag: String? = "",
    val bizType: String? = "",
    val contentType: String? = "",
    var cover: String? = null,
    var landingPage: String? = "",
    val ubt: Ubt? = null,
    val ubtV2: Map<String, String>?,
    val ext: Ext? = null,
    val wrap: CommonWrap? = null,
    val count: CommonCount? = null,
    val other: CommonOther? = null,
    val surElement: CommonSubElement? = null,
    val anchor: Anchor? = null,
    var isHasScroll: Boolean? = false,
    var metaDataTags: List<MetaDataTag>? = null,
    val extraInfo: ExtraInfo? = null,
    var cachedCoverColor: Int? = ColorUtil.INVALID_COLOR,
    var preview: String? = null  //视频动图封面
) {
    fun getValidCover(isCoverList: Boolean): String? {
        if (isCoverList) {
            return other?.coverList?.getOrNull(0) ?: cover
        }
        return cover ?: other?.coverList?.getOrNull(0)
    }

    /**
     * 根据 [bizType] 判断是否为直播卡片
     *
     * 1. 社会化听单、习惯听模块大卡中插入的直播小卡，bizType 会被服务端统一处理成 Live
     * 2. 直播模块大卡中的直播小卡，bizType 为直播侧服务端透传的类型，有三种类型：LamiaLive/PgcLive/DiabloLive
     */
    fun isLiveBizType(): Boolean {
        return bizType?.let {
            it.contentEquals("Live", true)
                    || it.contentEquals("LamiaLive", true)
                    || it.contentEquals("PgcLive", true)
                    || it.contentEquals("DiabloLive", true)
        } ?: false
    }

    // 视频类型
    fun isVideoType(): Boolean {
        return bizType == "IndexFeedVideoNew"
    }

    // 专辑类型  (追更卡  主播上新卡)
    fun isAlbumType(): Boolean {
        return bizType == "Chasing" || bizType == "AlbumCard"
    }

    // 肚脐眼类型
    fun isNavelType(): Boolean {
        return bizType == "Navel"
    }

    // 一键听类型
    fun isOneClickPlay(): Boolean {
        return bizType == "OneClickPlay"
    }

    // myClub类型
    fun isMyClubRoom(): Boolean {
        return bizType == "MyClubRoom"
    }

    // 活动类型
    fun isActivity(): Boolean {
        return bizType == "activity"
    }

    // 快听类型
    fun isQuickListen(): Boolean {
        return (bizType == "Navel" && contentType == "Navel/ShortContent")
                || (bizType == "OneClickPlay" && contentType == "OneClickPlay/ShortContent")
    }

    fun getButtonWordColor(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            other?.dark?.buttonWordColor
        } else {
            other?.buttonWordColor
        }
    }

    fun getButtonColor(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            other?.dark?.buttonColor
        } else {
            other?.buttonColor
        }
    }

    fun getGifCover(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            other?.dark?.gifCover
        } else {
            other?.gifCover
        }
    }

    fun getStaticCover(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            other?.dark?.staticCover
        } else {
            other?.staticCover
        }
    }

    fun getCardCover(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            other?.dark?.cover
        } else {
            cover
        }
    }
}

@Keep
data class InteractModel(
    val subscribed: Int? = 0,
    var itemRelated: Int? = 0, // 0 听单未订阅  1 听单已订阅
)

@Keep
data class CollectedCount(
    var itemCollectedCount: Int? = 0, // 听单订阅数
)

@Keep
data class Anchor(
    val logo: String? = "",
    val nickName: String? = "",
    val uid: Long? = 0,
    val summary: String? = "",
)

@Keep
data class MetaDataTag(
    val tag: String? = "",
    val trackingId: Long? = 0,
)

@Keep
data class Ext(
    val albumDress: String? = "",
    val albumDressDark: String? = "",
    val albumDressHolder: String? = "",
    val albumDressDarkHolder: String? = "",
    val albumDressOld: String? = "",
    val albumDressDarkOld: String? = "",
    val albumDressHolderOld: String? = "",
    val albumDressDarkHolderOld: String? = "",
    val albumTitle: String? = "",
    val trackPlayNum: Long? = 0L,
    val reasonContent: String? = "",
    val reasonType: String? = "",
    val reasonId: String? = "",
    val recWrap: RecWrap? = null,
    val subTitle1: String? = "",
    val subTitle2: String? = "",
    val tagType: Int? = 0,
    val extraInfo: ExtraInfo? = null,
    val source: String? = "",
    val userCustomPath: String? = "",
    val showTags: List<ShowTag>? = null,
    val showTags2: List<ShowTag>? = null,
    val bar: Bar? = null,
    var other: CommonOther? = null,
    var subRefInfo: SubRefInfo? = null,
    var showAdMark: Boolean = true,
    var useAdClick: Boolean = true,
    val aimAlbumId: Long?,
    var adInfo: String = "",
    var adInfoObject: Advertis?,
    var tracks: String? = "",
    val disLikeTip: String? = "",
    var jiaobiao: ShowTag? = null,
    var freeListenPresent: FreeListenPresent? = null,
    var cardManagementStatus: Int? = null,
    var userLogo: String? = null,
    var mcRoomInfo: McRoomInfo? = null,
    val buttonTag: String? = "", // 按钮文案
    val buttonCover: String? = "", // 按钮图片
    var activityStatus: Int = 0,  // 组队听活动状态，0-未参与，1-已参与
    var bgColor: String? = null,  // 背景颜色
    var subCoverIcon: String? = null,  // 活动态右侧标记图
    var leftTitle: CommonLeftTitle? = null,  // 习惯听标题
    val beads: CommonDragonBall? = null, // 龙珠列表
    var cardStyle: String? = null, // 卡片样式，circle｜中间图为圆图，square｜中间图为方形
    var h5Url: String? = null,
    var pos: Int = 0, // 卡片位置 1表示卡1，-1表示-1位。
    var box: List<RecommendElementBox>? = null,
    var pictures: List<String?>? = null,
    var coverContent: RecommendElementCoverContent? = null,
    var hasButtonEffect: Boolean = false, // 是否有按钮动效
    val lazyTitle: Boolean? = false,
    val nextTrackId: Long? = 0,
    val nextTrackTitle: String? = "",
    val nextTrackLandingPage: String? = "",
) {
    fun isCircleStyle(): Boolean {
        return cardStyle == "circle"
    }
}

@Keep
data class CommonDragonBall(
    val style: String? = "",
    val bizType: String? = null,
    val contentType: String? = null,
    val subElements: MutableList<CommonDragonBallItem>? = null,
    val ubtV2: Map<String, String>?
) {
    fun isShowDragonBall(): Boolean {
        return !subElements.isNullOrEmpty()
    }

    fun isShowTopDragonBall(): Boolean {
        return false
    }
}

@Keep
data class CommonDragonBallItem(
    val id: Long? = 0,
    val title: String? = "",
    val landingPage: String? = null,
    val bizType: String? = null,
    val contentType: String? = null,
    val ubtV2: Map<String, String>?,
    val extraInfo: CommonDragonBallExt? = null,
) {
    fun getIconUrl(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            extraInfo?.darkIcon
        } else {
            extraInfo?.icon
        }
    }

    fun getTextColor(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            extraInfo?.darkWordColor
        } else {
            extraInfo?.wordColor
        }
    }
}

@Keep
data class CommonDragonBallExt(
    val icon: String? = "",
    val darkIcon: String? = null,
    val wordColor: String? = null,
    val darkWordColor: String? = null,
    val titleMaxLength: Int? = 0,
)

@Keep
data class CommonLeftTitle(
    val title: String? = "",
    val landingPage: String? = null,
    val status: CommonLeftTitleStatus? = null,
    val ubtV2: Map<String, String>?,
    var isShowingGuide: Boolean = false, // 本地使用
    var showingStartTime: Long = 0, // 本地使用
) {
    fun isAllowShowGuide(): Boolean {
        return status?.isFinished == 2
    }
}

@Keep
data class CommonLeftTitleStatus(
    val isFinished: Int? = 0,
)

@Keep
data class McRoomInfo(
    val actionText: String?, //"进入房间",
    val topic: String?,//": "我呜呜呜呜",
    val roomId: Int?,//": 440311,
    val tips: String?,//": "正在热聊",
    val users: List<McRoomInfoUser?>?
)

@Keep
data class McRoomInfoUser(
    val nickname: String?, //": "听友326289",
    val avatar: String?, //": "storages/cf73-audiotest/7A/7C/CAoVOVcDt4g4AABedwAAK0Db.png",
    val userId: Int?, //": 326289
)

@Keep
data class FreeListenPresent(
    val buttonText: String?, // 做任务再领一天
    val tag: String?,       // 新人福利测试
    val text: String?,      // 畅听天数
    val subText: String?,   //您已领取VIP畅听权益  或者 中间图文案（划线价格）
    val subContent: String?,   // 中间图文案（首两月）
    val buttonUrl: String?, // iting://open?msg_type=13&album_id=2912127
    val status: Int?, // 权益状态，0｜待领取，1｜已领取，2｜已过期
)

@Keep
data class Bar(
    val title: String? = "",
    val landingPage: String? = "",
    val cover: String? = "",
    val extraInfo: BarExtraInfo?,
)

data class BarExtraInfo(
    val widthHeightRate: Float?,//0.6 宽高比
    val darkCover: String?,      //暗黑图
    val displayStyle: String?, // 1 老样式 2增强样式
)

@Keep
data class ExtraInfo(
    val triggerId: String? = "",
    val motName: String? = "",
    val trackUpdateAt: Long = 0L,
    val subTitle1: String? = "",
    val albumId: Long = 0L,
    var showTags: List<ShowTag>? = null,
    var showTags2: List<ShowTag>? = null,
    var subRefInfo: SubRefInfo? = null,
    val recSrc: String? = "",
    val hotScore: String? = "",
    val parentCategoryName: String? = "",
    val style: String? = "",
    var hasNew: Int? = 0,
    var jiaobiao: ShowTag? = null,
    val status: Int? = 0,
    val statusLabel: String? = "",
    val reasonContent: String? = "",
    val lotteryType: Int? = -1,
    val rankName: String? = "",
    val darkCover: String? = "",
    var other: CommonOther? = null,
    val landingPage: String? = "",
    val permissionExpireTime: String? = "",
)

@Keep
data class SubRefInfo(
    val ad: Boolean = false,
    val businessExtraInfo: RecommendBusinessExtraInfo? = null
)

@Keep
data class RecommendBusinessExtraInfo(
    var showAdMark: Boolean = true,
    var adInfo: String = "",
    var adInfoObject: Advertis?
)

@Keep
data class RecWrap(
    val id: Long? = 0,
    val title: String? = "",
)

@Keep
data class CommonWrap(
    val ltSubscriptTag: LtSubscriptTag? = null,
)

@Keep
data class CommonOther(
    val darkCover: String? = "",
    val tag: String? = "",
    val buttonTag: String? = "",
    val buttonProcess: Int? = -1,
    val cardStyle: String? = "",
    var coverList: List<String>? = null,
    var coverListA: List<String>? = null, // 习惯听缩短高度背景
    val albumLandingPage: String? = "",
    val landingPageText: String? = "",
    val backdrop: String? = "",
    val backdropOld: String? = "",
    var backdropHolderOld: String? = "",
    val darkBackdrop: String? = "",
    val backdropHolder: String? = "",
    val darkBackdropHolder: String? = "",
    val title: String? = "",
    val character: String? = "",
    val hotScore: String? = "",
    val icon: String? = "",
    val iconDark: String? = "",
    val trackId: Long? = 0,
    val albumAsc: Boolean? = false,
    val showSocialListenBg: Boolean? = false,
    val coverB: String? = "", // 我的ai一键听 无顶部样式图片
    val background: String?, // 背景图url
    val darkBackground: String?, // 夜间模式背景图url
    val beforeTitleTag: String?, // 标题前的🔥图标
    val activityStyle: Boolean?, //是否是活动态
    val activityText: String?, // 活动文案
    val activityTextColor: String?, // 活动文案颜色
    val activityTextColorDark: String?, // 活动文案颜色 夜间模式
    val subCover: String?, // 内容推荐卡片封面图
    val lotteryType: Int? = -1, // 直播 抽奖标签类型
    val rankName: String? = "", // 直播 榜单
    val bottomRightMark: String? = "", // 专辑封面右下角图标 目前是播客角标
    val bottomRightDarkMark: String? = "", // 专辑封面右下角图标 目前是播客角标 夜间模式
    val shareCover: String? = "", // 负反馈显示封面图
    val wordColor: String? = "", // 文字颜色
    val buttonColor: String? = "", // 按钮文字颜色
    val recUid: String? = "", // 推荐人UID
    val recName: String? = "", // 推荐人名字
    val recReview: String? = "", // 推荐语
    val totalCount: String? = "", // 专辑数
    val latestTag: String? = "", // 1表示新版本听单
    val gifCover: String? = "", // 猫头动效
    val staticCover: String? = "", // 猫头静图
    val buttonWordColor: String? = "", // 按钮文字颜色
    val dark: RecommendOtherDark? = null,
    val subWordColor: String? = "", // 中间图右侧字体颜色
    val showHotScore: Int = 0, // 热榜值
    var showTags: List<ShowTag>? = null,
)

@Keep
data class CommonCount(
    val comments: Long? = 0,
    val subscribe: Long? = 0,
    val play7Day: Long? = 0,
    var randomTag: String? = ""
) {
    // 随机标签
    fun getRandomNumTag(): String? {
        if (randomTag != null && randomTag!!.isNotEmpty()) {
            return randomTag
        }
        var tagList = ArrayList<String>()
        if (comments != null && comments >= 100) {
            tagList.add("${StringUtil.getFriendlyNumStr(comments)}人评论")
        }
        if (subscribe != null && subscribe >= 1000) {
            tagList.add("${StringUtil.getFriendlyNumStr(subscribe)}人订阅")
        }
        if (play7Day != null && play7Day >= 10000) {
            tagList.add("近7日${StringUtil.getFriendlyNumStr(play7Day)}次播放")
        }
        if (tagList.size > 0) {
            var random = Random()
            var index = random.nextInt(tagList.size)
            return tagList[index]
        }
        return ""
    }
}