package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewStub
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.NewShowNotesManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.ShowTagManager
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.DomainColorUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.host.view.CornerRelativeLayout
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.ShowTagPlayUtil
import com.ximalaya.ting.android.main.adapter.find.util.SocialRequestUtil
import com.ximalaya.ting.android.main.adapter.find.util.TextWrapUtil
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataPlayStatusStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.satisfy.SatisfactionHomeModuleManager
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.view.live.LiveHotValueTagView
import com.ximalaya.ting.android.main.view.live.LiveLotteryTagView
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by felix.chen on 2023/3/13.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18621868330
 */
class RecommendSocialListenListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendSocialListenListAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendSocialListenListAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataPlayStatusStaggered<RecommendSocialListenListAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendSocialListenListAdapterProviderStaggered.SocialListCardViewHolder, RecommendItemNew> {

    private var mOldState = RecyclerView.SCROLL_STATE_IDLE
    private var mListCardViewHolder: SocialListCardViewHolder? = null


    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_social_list_card, parent, false)
    }

    override fun onPlayStart() {
//        mListCardViewHolder?.rcvSocialList?.adapter?.notifyDataSetChanged()
    }

    override fun onPlayPause() {
//        mListCardViewHolder?.rcvSocialList?.adapter?.notifyDataSetChanged()
    }

    override fun onSoundPlayComplete() {
        mListCardViewHolder?.rcvSocialList?.adapter?.notifyDataSetChanged()
    }

    class SocialListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val bgTop: ImageView = convertView.findViewById(R.id.main_bg_top)
        val rcvSocialList: RecyclerView = convertView.findViewById(R.id.main_rcv_album_list)
        var tvSocialListTittle: TextView = convertView.findViewById(R.id.main_tv_social_list_tittle)
        var tvSocialListSubTittle: TextView =
            convertView.findViewById(R.id.main_tv_social_list_sub_title)
        val tvGoneForMore: ImageView = convertView.findViewById(R.id.main_tv_more)

        val subTitleRootView: ViewGroup =
            convertView.findViewById(R.id.main_ll_new_sub_title_root_view)
        val tvLeftTitle: TextView = convertView.findViewById(R.id.main_tv_left_title)
        val ivDivide: ImageView = convertView.findViewById(R.id.main_iv_divide)
        val tvRightTitle: TextView = convertView.findViewById(R.id.main_tv_right_title)

        var strartSnapHelper: StartSnapHelper? = null
        var lastScreenWidth: Int =
            BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        var firstVisiblePosition: Int = 0
        var uniqueId: String = ""
    }

    override fun onConfigurationChanged(holder: SocialListCardViewHolder?) {
        holder ?: return
        if (holder.lastScreenWidth == BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())) {
            return
        }
        holder.lastScreenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
    }

    override fun createViewHolder(convertView: View?): SocialListCardViewHolder? {
        PerformanceMonitor.traceBegin("social_createViewHolder")
        if (convertView == null) {
            return null
        }
        val viewHolder = SocialListCardViewHolder(convertView)
        PerformanceMonitor.traceEnd("social_createViewHolder", 7)
        return viewHolder
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun bindViewHolder(
        holder: SocialListCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            HandlerManager.postOnUIThread {
                onBindViewHolderInner(holder, position, recommendItemNew, convertView)
            }
        } else {
            onBindViewHolderInner(holder, position, recommendItemNew, convertView)
        }
    }

    private fun checkShowSubTitle(
        holder: SocialListCardViewHolder,
        recommendCommonItem: RecommendCommonItem,
        position: Int
    ) {
        val context = holder.itemView.context
        // 隐藏老的副标题
        ViewStatusUtil.setVisible(View.GONE, holder.tvSocialListSubTittle)

        // 听单在首屏第二个模块  第三个模块广告显示中  直接压缩标题为1行
        if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() && position == 1) {
            holder.tvSocialListTittle.text = recommendCommonItem.title
            holder.tvSocialListTittle.maxLines = 1
        } else {
            holder.tvSocialListTittle.maxLines = 2
            TextWrapUtil.checkWrap(holder.tvSocialListTittle, recommendCommonItem.title, position)
        }

        var personText = ""
        recommendCommonItem.ext?.other?.recName?.let {
            if (it.isNotEmpty()) {
                personText = "${it}推荐"
            }
        }

        if (personText.isEmpty()) {
            recommendCommonItem.anchor?.nickName?.let {
                if (it.isNotEmpty()) {
                    personText = "${it}创建"
                }
            }
        }

        var isShowArrow = false
        var countText = ""
        recommendCommonItem.count?.itemCollectedCount?.let { // 订阅数
            val showCount = ConfigureCenter.getInstance()
                .getInt("toc", "listen_list_show_subscribe_count_threshold", 1000)
            if (it > showCount) {
                countText = "${StringUtil.getPlayCountStrFromShowTag(it.toLong())}订阅"
            }
        }

        if (countText.isEmpty()) { // 专辑数
            recommendCommonItem.ext?.other?.totalCount?.toIntOrNull()?.let {
                if (it > 0) {
                    isShowArrow = true
                    countText = "共${StringUtil.getPlayCountStrFromShowTag(it.toLong())}部专辑"
                }
            }
        }

        if (recommendCommonItem.interact?.itemRelated == 1) {
            holder.tvLeftTitle.visibility = View.VISIBLE
            holder.tvLeftTitle.text = "已订阅"

            if (personText.isNotEmpty()) {
                holder.ivDivide.visibility = View.VISIBLE
                holder.tvRightTitle.visibility = View.VISIBLE
                holder.tvRightTitle.text = personText
                holder.tvRightTitle.setCompoundDrawables(null, null, null, null)
            } else if (countText.isNotEmpty()) {
                holder.ivDivide.visibility = View.VISIBLE
                holder.tvRightTitle.visibility = View.VISIBLE
                holder.tvRightTitle.text = countText
                if (isShowArrow) {
                    val drawable = ContextCompat.getDrawable(
                        context,
                        R.drawable.main_ic_social_sub_title_arrow
                    )
                    holder.tvRightTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        null,
                        null,
                        drawable,
                        null
                    )
                } else {
                    holder.tvRightTitle.setCompoundDrawables(null, null, null, null)
                }
            } else {
                holder.ivDivide.visibility = View.GONE
                holder.tvRightTitle.visibility = View.GONE
            }
        } else {
            if (personText.isNotEmpty()) {
                holder.tvLeftTitle.visibility = View.VISIBLE
                holder.tvLeftTitle.text = personText
            } else {
                holder.tvLeftTitle.visibility = View.GONE
            }

            if (countText.isNotEmpty()) {
                if (personText.isNotEmpty()) {
                    holder.ivDivide.visibility = View.VISIBLE
                } else {
                    holder.ivDivide.visibility = View.GONE
                }
                holder.tvRightTitle.visibility = View.VISIBLE
                holder.tvRightTitle.text = countText
                if (isShowArrow) {
                    val drawable = ContextCompat.getDrawable(
                        context,
                        R.drawable.main_ic_social_sub_title_arrow
                    )
                    holder.tvRightTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        null,
                        null,
                        drawable,
                        null
                    )
                } else {
                    holder.tvRightTitle.setCompoundDrawables(null, null, null, null)
                }
            } else {
                holder.ivDivide.visibility = View.GONE
                holder.tvRightTitle.visibility = View.GONE
            }
        }

        holder.subTitleRootView.setOnClickListener {
            holder.tvSocialListTittle.performClick()
        }

        if (holder.tvLeftTitle.visibility == View.VISIBLE || holder.tvRightTitle.visibility == View.VISIBLE) {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.subTitleRootView)
        } else {
            ViewStatusUtil.setVisible(View.GONE, holder.subTitleRootView)
        }
    }

    private fun performSubscribe(
        holder: SocialListCardViewHolder,
        recommendCommonItem: RecommendCommonItem,
        position: Int
    ) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(holder.itemView.context)
            return
        }

        val listenId = recommendCommonItem.id
        if (listenId == null || listenId == 0L) {
            ToastManager.showToast("听单id空")
            return
        }

        val curSubscribe = recommendCommonItem.interact?.itemRelated == 1
        val toSubscribe = !curSubscribe

        val toast = { isSuccess: Boolean, errMsg: String? ->
            if (isSuccess) {
                val msg = if (toSubscribe) {
                    "订阅成功"
                } else {
                    "取消订阅成功"
                }
                ToastManager.showToast(msg)
            } else {
                var msg = errMsg
                if (msg.isNullOrEmpty()) {
                    msg = if (toSubscribe) {
                        "订阅失败，请重试"
                    } else {
                        "取消订阅失败，请重试"
                    }
                }
                ToastManager.showToast(msg)
            }
        }

        SocialRequestUtil.requestSubscribe(toSubscribe, listenId.toString(), object :
            IDataCallBack<Boolean> {
            override fun onSuccess(data: Boolean?) {

                if (data == true) {
                    if (toSubscribe) {
                        recommendCommonItem.interact?.itemRelated = 1
                        val collectedCount = recommendCommonItem.count?.itemCollectedCount ?: 0
                        recommendCommonItem.count?.itemCollectedCount = collectedCount + 1
                    } else {
                        recommendCommonItem.interact?.itemRelated = 0
                        val collectedCount = recommendCommonItem.count?.itemCollectedCount ?: 0
                        recommendCommonItem.count?.itemCollectedCount =
                            (collectedCount - 1).coerceAtLeast(0)
                    }
                }

                if (holder.itemView.context.checkActivity()) {
                    toast(data == true, "")
                    checkShowSubTitle(holder, recommendCommonItem, position)
                }
            }

            override fun onError(code: Int, message: String?) {
                if (holder.itemView.context.checkActivity()) {
                    toast(false, "")
                }
            }
        })
    }

    fun onBindViewHolderInner(
        holder: SocialListCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        SatisfactionHomeModuleManager.checkShowSatisfyView(
            fragment, holder.itemView, recommendItemNew,
            SatisfactionHomeModuleManager.TYPE_LISTEN, position
        )

        mListCardViewHolder = holder
        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        PerformanceMonitor.traceBegin("social_bindViewHolder_" + recommendCommonItem.title)

        val isShowSubscribe = !recommendCommonItem.ext?.other?.latestTag.isNullOrEmpty()

        if (!isShowSubscribe) {
            // 隐藏新的副标题布局
            ViewStatusUtil.setVisible(View.GONE, holder.subTitleRootView)

            // 命中新的习惯听样式  听单在首屏第二个模块  第三个模块广告显示中  直接压缩标题为1行
            if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() &&
                RecommendFragmentTypeManager.isNewSceneCard() && position == 1
            ) {
                holder.tvSocialListTittle.text = recommendCommonItem.title
                holder.tvSocialListTittle.maxLines = 1
                ViewStatusUtil.setVisible(View.GONE, holder.tvSocialListSubTittle)
            } else if (!TextUtils.isEmpty(recommendCommonItem.subTitle)) {
                holder.tvSocialListTittle.text = recommendCommonItem.title
                holder.tvSocialListTittle.maxLines = 1
                holder.tvSocialListSubTittle.text = recommendCommonItem.subTitle
                ViewStatusUtil.setVisible(View.VISIBLE, holder.tvSocialListSubTittle)
            } else {
                holder.tvSocialListTittle.maxLines = 2
                ViewStatusUtil.setVisible(View.GONE, holder.tvSocialListSubTittle)
                TextWrapUtil.checkWrap(
                    holder.tvSocialListTittle,
                    recommendCommonItem.title,
                    position
                )
                RecommendFragmentPageErrorManager.uploadDataError("听单无标题", null)
            }
        } else {
            checkShowSubTitle(holder, recommendCommonItem, position)
        }

        if (recommendCommonItem.other?.showSocialListenBg == true) {
            holder.rootView.apply {
                setPadding(paddingLeft, 16.dp, paddingRight, paddingBottom)
            }

            holder.bgTop.visibility = View.VISIBLE
        } else {
            holder.bgTop.visibility = View.GONE
            holder.rootView.apply {
                setPadding(paddingLeft, 0, paddingRight, paddingBottom)
            }
        }

        val exportMore = { action: String ->
            // 新首页-社会化标注-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(61824) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("moduleName", recommendCommonItem.title) // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.moduleId?.toString() ?: "") // 例如：1000000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("action", action)
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
        }

        val performMoreClick = {
            ScenePlayDataUtil.saveDataForRn(recommendItemNew)

            ToolUtil.clickUrlAction(
                fragment,
                recommendCommonItem.landingPage ?: "",
                holder.tvGoneForMore
            )
        }

        holder.tvSocialListTittle.setOnClickListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            trace1.createTrace()
            exportMore("click")
            performMoreClick()
        }

        holder.tvGoneForMore.setOnClickListener {
            exportMore("click")
            val isShowMore = !recommendCommonItem.landingPage.isNullOrEmpty()
            var level1DisLikeTitle = recommendCommonItem.ext?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick()
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(position)
                    SatisfactionHomeModuleManager.clickModuleDislike(
                        fragment,
                        SatisfactionHomeModuleManager.TYPE_LISTEN,
                        recommendItemNew,
                        position
                    )
                }

                override fun onCommonItemClick(commonItemText: String) {
                    performSubscribe(holder, recommendCommonItem, position)
                }
            }

            val moreFuncBuild = MoreFuncBuild.createSocialListenMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener
            )
            moreFuncBuild.isShowLevel1DisLikeToast =
                !SatisfactionHomeModuleManager.isAllowModuleDislike(SatisfactionHomeModuleManager.TYPE_LISTEN)

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (position + 1).toString())
                put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.ext?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            if (isShowSubscribe) {
                if (recommendCommonItem.interact?.itemRelated == 1) {
                    moreFuncBuild.commonItemIcon = R.drawable.host_ic_more_func_subscribed
                    moreFuncBuild.commonItemText = "取消订阅"
                } else {
                    moreFuncBuild.commonItemIcon = R.drawable.host_ic_more_func_subscription
                    moreFuncBuild.commonItemText = "订阅听单"
                }
            }

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }
        val spanCount = if (RecommendFragmentTypeManager.isNewSceneCard() && position == 1) {
            3
        } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() && position == 1) {
            2
        } else {
            3
        }
        val cardAlbumListAdapter =
            SocialListAlbumItemAdapter(
                dataAction,
                fragment,
                recommendCommonItem,
                recommendItemNew,
                recommendCommonItem.subElements!!,
                position,
                holder.rcvSocialList,
                spanCount,
                holder.rootView,
                recommendCommonItem.landingPage.isNullOrEmpty().not()
            )
        cardAlbumListAdapter.mEnableMoreItem = cardAlbumListAdapter.enableJumpMore
        cardAlbumListAdapter.setRelaseJumpActivityListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            trace1.createTrace()
            exportMore("slide")
            performMoreClick()
        }
        // 听单专辑列表
        holder.rcvSocialList.adapter = cardAlbumListAdapter
        RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
            this.javaClass.simpleName,
            spanCount,
            position,
            recommendItemNew
        )
        val layoutManager =
            GridLayoutManager(convertView?.context, spanCount, GridLayoutManager.HORIZONTAL, false)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) spanCount else 1
            }
        }
        holder.rcvSocialList.layoutManager = layoutManager
        if (holder.strartSnapHelper == null) {
            holder.strartSnapHelper = StartSnapHelper()
            holder.strartSnapHelper!!.attachToRecyclerView(holder.rcvSocialList)
            holder.strartSnapHelper!!.setContainerView(holder.rcvSocialList)
        }
        holder.rcvSocialList.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(recommendItemNew, position, holder)
                    recommendItemNew.firstVisiblePosition =
                        (holder.rcvSocialList.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                }
            }
        })
        if (recommendItemNew.firstVisiblePosition != (holder.rcvSocialList.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()) {
            holder.rcvSocialList.scrollToPosition(recommendItemNew.firstVisiblePosition)
        }

        // 有声音样式  缓存下
        val item = recommendCommonItem.subElements?.find { "Track" == it.bizType }
        ShowTagPlayUtil.setRecyclerViewTag(item != null, holder, holder.rcvSocialList)

        PerformanceMonitor.traceEnd("social_bindViewHolder_" + recommendCommonItem.title, 8)
    }

    fun checkAdCunt(subElements: List<CommonSubElement>?): Int {
        if (subElements.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (subElement in subElements) {
            val adInfo = subElement.ext?.subRefInfo?.businessExtraInfo?.adInfo
            if (!adInfo.isNullOrEmpty()) {
                adCount++
            }
        }
        return adCount
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: SocialListCardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        if (recommendCommonItem.cardAdCount == -1) {
            recommendCommonItem.cardAdCount = checkAdCunt(recommendCommonItem.subElements)
        }
        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                        .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    trace.createTrace()
                }
                val childCount = holder.rcvSocialList.childCount
                for (i in 0 until childCount) {
                    val view = holder.rcvSocialList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement =
                            view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val advertis = view.getTag(R.id.main_id_data_ad_info) as? Advertis
                        traceAlbumInner(
                            subElement, recommendCommonItem, data, index, position,
                            ViewStatusUtil.getViewVisibleAreaRealPercent(view), advertis, view
                        )
                    }
                }
            }
        }
    }

    class SocialListAlbumItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        // 页面
        private val fragment: BaseFragment2,
        // 卡片数据
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        // 专辑列表
        list: List<CommonSubElement>,
        var modulePosition: Int,
        val recyclerView: RecyclerView,
        var spanCount: Int,
        val rootView: View,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 专辑列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            commonSubElementList.addAll(list)
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            val view: View = ViewPool.getInstance().getView(
                HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                R.layout.main_item_recommend_social_list_item,
                parent,
                false,
                "SocialListenList"
            )
            return SocialListAlbumViewHolder(view)
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is SocialListAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                if (layoutParams != null) {
                    horizontalView?.layoutParams = layoutParams
                }
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun onBindAlbumView(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder,
            textViewContainerWith: Int
        ) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindAlbumViewInner(commonSubElement, holder, textViewContainerWith)
                }
            } else {
                onBindAlbumViewInner(commonSubElement, holder, textViewContainerWith)
            }
        }

        private fun onBindAlbumViewInner(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder,
            textViewContainerWith: Int
        ) {
            holder.cslContainerView.visibility = View.VISIBLE
            ViewStatusUtil.setVisible(View.GONE, holder.cslTrackContainerView, holder.liveView)
            holder.itemTitleTv.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
            if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.ext!!.reasonContent
                holder.itemSubtitle1Tv.maxLines = 1
                RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitle1Tv)
            }
            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
            val adWidth = if (adTagShow) 24 else 0
            val otherWidth =
                16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + 16 + adWidth //  else 16 + 70 + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(
                holder.layoutShowTags,
                commonSubElement.ext?.showTags,
                textViewContainerWith - otherWidth.dp,
                commonSubElement.ext?.subTitle1,
                commonSubElement.ext?.subTitle2
            )
            commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            if (holder.layoutRightTxtArea.layoutParams is ConstraintLayout.LayoutParams) {
                (holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams).startToEnd =
                    R.id.main_album_cover_layout
            }
            commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                commonSubElement.ext?.other?.getBRTagUrl()
            )
        }

        private fun onBindTrackView(
            commonSubElement: CommonSubElement, holder: SocialListAlbumViewHolder,
            textViewContainerWith: Int
        ) {
            holder.cslTrackContainerView.visibility = View.VISIBLE
            ViewStatusUtil.setVisible(View.GONE, holder.cslContainerView, holder.liveView)
            val track = Track()
            track.dataId = commonSubElement.refId ?: 0
            val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
            if (isPlaying) {
                holder.ivPlayButton.setImageResource(R.drawable.host_pause_btn_n_fill_n_28)
            } else {
                holder.ivPlayButton.setImageResource(R.drawable.host_play_btn_inside_fill_n_28)
            }
            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(
                if (adTagShow) View.VISIBLE else View.GONE,
                holder.ivAdTagTrack
            )
            val adWidth = if (adTagShow) 24 else 0
            val coverSize = RecommendCornerUtils.getSocialCoverSizeDp()
            val otherWidth = if (NewShowNotesManager.userNewShowNotes()) {
                16 + coverSize + 12 + 54
            } else {
                16 + coverSize + 12 + 16
            }
            val containerWidthInPx = textViewContainerWith - otherWidth.dp - adWidth.dp
            if (commonSubElement.ext?.showTags.isNullOrEmpty()) {
                val newList = mutableListOf<ShowTag>()
                if (!TextUtils.isEmpty(commonSubElement.ext?.albumTitle)) {
                    val tag1 = ShowTag()
                    tag1.tag = commonSubElement.ext?.albumTitle
                    tag1.type = RecommendShowTagsUtilNew.TYPE_ALBUM_TITLE
                    newList.add(tag1)
                }
                if (commonSubElement.ext?.trackPlayNum != null && commonSubElement.ext.trackPlayNum > 0) {
                    val tag2 = ShowTag()
                    tag2.tag = getPlayCountString(commonSubElement.ext.trackPlayNum)
                    tag2.value = commonSubElement.ext.trackPlayNum.toString()
                    tag2.type = RecommendShowTagsUtilNew.TYPE_COUNT_PLAY
                    newList.add(tag2)
                }
                ShowTagManager.bindTagsView(
                    holder.trackShowTagLayout, newList,
                    containerWidthInPx, commonSubElement.refId
                )
            } else {
                ShowTagManager.bindTagsView(
                    holder.trackShowTagLayout, commonSubElement.ext?.showTags,
                    containerWidthInPx, commonSubElement.refId
                )
            }

            ShowTagPlayUtil.setHolderTrackItem(
                holder,
                commonSubElement.refId,
                holder.trackShowTagLayout
            )

            RecommendCornerUtils.updateTitleColor(holder.tvTrackTitle)
            holder.tvTrackTitle.text = commonSubElement.title
            if (RecommendShowTagsUtilNew.canShowOneLine(
                    holder.tvTrackTitle,
                    commonSubElement.title,
                    textViewContainerWith - otherWidth.dp
                )
            ) {
                holder.tvTrackSubTitle.text = commonSubElement.summary ?: ""
                ViewStatusUtil.setVisible(
                    if (!TextUtils.isEmpty(commonSubElement.summary)) View.VISIBLE else View.GONE,
                    holder.tvTrackSubTitle
                )
                RecommendCornerUtils.updateSubTitleMargin(holder.tvTrackSubTitle)
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.tvTrackSubTitle)
            }
            var hasValidServerColor = false
            if (commonSubElement.cachedCoverColor != null && commonSubElement.cachedCoverColor != ColorUtil.INVALID_COLOR) {
                hasValidServerColor = true
                setPlayBgColor(commonSubElement.cachedCoverColor!!, holder)
            }
            ImageManager.from(BaseApplication.getMyApplicationContext())
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    holder.ivTrackCover,
                    commonSubElement.cover,
                    com.ximalaya.ting.android.host.R.drawable.host_default_album,
                    70,
                    70
                ) { _, bitmap ->
                    if (bitmap != null && !hasValidServerColor) {
                        DomainColorUtil.getDomainColorForRecommend(
                            bitmap,
                            Color.BLACK
                        ) { color: Int ->
                            setPlayBgColor(color, holder)
                            commonSubElement.cachedCoverColor = color
                        }
                    }
                }

            if (NewShowNotesManager.userNewShowNotes()) {
                holder.showNotePlayBtnBg.run {
                    val params = layoutParams as MarginLayoutParams
                    params.width = RecommendCornerUtils.getShowNoteSize()
                    params.height = params.width
                    layoutParams = params
                }

                holder.showNotePlayBtn.run {
                    val params = layoutParams
                    params.width = RecommendCornerUtils.getShowNotePlaySize()
                    params.height = params.width
                    layoutParams = params
                }

                RecommendCornerUtils.updateShowNoteRoundBg(holder.showNotePlayBtnBg)
            }

            if (NewShowNotesManager.userNewShowNotes()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.GONE, holder.vPlayButtonBg, holder.ivPlayButton)
                if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying &&
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId == commonSubElement.refId
                ) {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, true)
                } else {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, false)
                }
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.vPlayButtonBg, holder.ivPlayButton)
            }
        }

        private fun onBindLiveView(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder
        ) {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.liveView)
            ViewStatusUtil.setVisible(
                View.GONE,
                holder.cslContainerView,
                holder.cslTrackContainerView
            )
            ImageManager.from(
                BaseApplication.getMyApplicationContext()
            ).displayImageNotIncludeDownloadCacheSizeInDp(
                holder.liveItemCoverView,
                commonSubElement.cover,
                R.drawable.host_default_album,
                70,
                70
            )
            holder.liveItemPlaying?.playAnimation()
            holder.liveItemTitleView?.text = commonSubElement.title
            val subTitle = if (commonSubElement.ext?.reasonContent.isNullOrBlank()
                    .not()
            ) commonSubElement.ext?.reasonContent else commonSubElement.subTitle
            holder.liveItemSubNickname?.text = subTitle

            // 标签优先级：抽奖>榜单>分区，热度常驻
            var visibleCount = 0
            val hotScoreVisible = commonSubElement.ext?.other?.hotScore.isNullOrBlank().not()
            holder.liveItemHotValue?.setContent(commonSubElement.ext?.other?.hotScore)
            if (hotScoreVisible) {
                holder.liveItemHotValue?.visibility = View.VISIBLE
                visibleCount++
            } else {
                holder.liveItemHotValue?.visibility = View.GONE
            }

            val lotteryTagType = LiveLotteryTagView.TagType.convert(
                commonSubElement.ext?.other?.lotteryType
            )
            val lotteryTagVisible = lotteryTagType != null
            if (lotteryTagVisible) {
                holder.liveItemLotteryTag?.visibility = View.VISIBLE
                holder.liveItemLotteryTag?.setTagInfo(lotteryTagType!!)
                visibleCount++
            } else {
                holder.liveItemLotteryTag?.visibility = View.GONE
            }

            val rankDesc = commonSubElement.ext?.other?.rankName
            val rankTagVisible = rankDesc.isNullOrBlank().not()
            if (rankTagVisible) {
                holder.liveItemRankTag?.visibility = View.VISIBLE
                holder.liveItemRankTag?.text = rankDesc
                holder.liveItemRankTag?.updateLayoutParams<MarginLayoutParams> {
                    marginStart = if (lotteryTagVisible) 6.dp else 0
                }
                visibleCount++
            } else {
                holder.liveItemRankTag?.visibility = View.GONE
            }

            val categoryDesc = commonSubElement.ext?.other?.character
            val categoryVisible = categoryDesc.isNullOrBlank().not() && (visibleCount < 3)
            if (categoryVisible) {
                holder.liveItemCategory?.visibility = View.VISIBLE
                holder.liveItemCategory?.text = categoryDesc
                holder.liveItemCategory?.updateLayoutParams<MarginLayoutParams> {
                    marginStart = if (lotteryTagVisible || rankTagVisible) 6.dp else 0
                }
            } else {
                holder.liveItemCategory?.visibility = View.GONE
            }

            if (hotScoreVisible) {
                holder.liveItemHotValue?.updateLayoutParams<MarginLayoutParams> {
                    val leftVisible = lotteryTagVisible || rankTagVisible || categoryVisible
                    marginStart = if (leftVisible) 6.dp else 0
                }
            }
        }

        private fun getPlayCountString(playCount: Long?): String {
            if (playCount == null || playCount <= 0) {
                return "99次播放"
            }
            return if (playCount < 10000) {
                playCount.toString() + "次播放"
            } else {
                val count = playCount / 10000
                "播放量超" + count + "万"
            }
        }

        private fun updateSize(holder: SocialListAlbumViewHolder) {
            val padding = RecommendCornerUtils.getPaddingSize()
            holder.cslContainerView.run {
                setPadding(paddingLeft, padding, paddingRight, padding)
            }
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)
            holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getSocialCoverSize())

            holder.showTagParentAlbum?.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }

            holder.showTagParentTrack?.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }

            val coverSize = RecommendCornerUtils.getSocialCoverSize().toInt()
            holder.crlTrackCover?.let {
                val params = it.layoutParams as MarginLayoutParams
                params.width = coverSize
                params.height = coverSize
                params.topMargin = padding
                params.bottomMargin = padding
                it.layoutParams = params

                it.setCornerRadius(RecommendCornerUtils.getSocialCorner())
            }

            holder.liveRootItemView?.run {
                setPadding(
                    paddingLeft, RecommendCornerUtils.getSocialLivePaddingSize(true),
                    paddingRight, RecommendCornerUtils.getSocialLivePaddingSize(false)
                )
            }
            holder.liveItemCoverView?.run {
                val params = layoutParams
                params.width = coverSize
                params.height = coverSize
                layoutParams = params

                setCornerRadius(
                    RecommendCornerUtils.getSocialCorner().toInt()
                )
            }
            holder.liveShowTag?.let {
                val params = it.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialLiveShowTagGapSize()
                it.layoutParams = params
            }
        }

        fun onBindViewHolderInner(holder: SocialListAlbumViewHolder, position: Int) {
            val commonSubElement = commonSubElementList[position]
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            val remainder: Int = commonSubElementList.size % spanCount
            val start: Int =
                if (remainder == 0) commonSubElementList.size - spanCount else commonSubElementList.size - remainder
            val isTrackUIType = "Track" == commonSubElement.bizType
            val isAlbumUIType =
                ("Album" == commonSubElement.bizType || "Playlet" == commonSubElement.bizType)
            val isLiveUIType = commonSubElement.isLiveBizType()
            if (isLiveUIType) {
                holder.initLiveView()
            }
            updateSize(holder)

            val adShow = commonSubElement.ext?.subRefInfo?.ad ?: false
            var advertis: Advertis? = null
            if (adShow) {
                advertis = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject
            }
            holder.itemView.setTag(R.id.main_id_data_ad_info, advertis)
            holder.itemView.isHapticFeedbackEnabled = !isLiveUIType
            holder.itemView.setOnLongClickListener {
                if (isLiveUIType) {
                    // do nothing
                    // todo(zoeywoohoo): 2024/11/6 要接入直播卡片负反馈需要解决 bizType 透传问题，目前社会化听单中的直播卡 bizType 被统一修改为 Live，导致负反馈接口取数据会匹配不到内容
                } else if (adShow) {
                    if (!XmAdFeedbackUtil.feedbackEnable(advertis)) {
                        return@setOnLongClickListener true
                    }
                    val build = MoreFuncBuild()
                    build.fragment2 = fragment
                    if (isAlbumUIType) {
                        build.albumId = commonSubElement.refId ?: 0
                    }
                    if (isTrackUIType) {
                        build.trackId = commonSubElement.refId ?: 0
                    }
                    build.vibratorEnable = true
                    build.isShowLevel2Dislike = true
                    build.isShowSubscription = true
                    val disLikeLeve2Build = DisLikeLeve2Build().apply {
                        this.isFromAd = true
                        this.anchorName = commonSubElement.anchor?.nickName
                    }
                    disLikeLeve2Build.onFeedBackListener = object :
                        NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
                            XmAdFeedbackUtil.recordFeedback(advertis, list)
                            val oldId = if (isTrackUIType) commonSubElement.refId else 0L
                            val oldItingUrl =
                                if (isTrackUIType) commonSubElement.landingPage else null
                            MainCommonRequest.getSingleSocialListenListItem(
                                position,
                                oldItingUrl,
                                moduleItem,
                                object :
                                    IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            removeElement(
                                                commonSubElementList,
                                                moduleItem.subElements,
                                                oldId,
                                                position,
                                                modulePosition
                                            )
                                            return
                                        }
                                        commonSubElementList[position] = subElement
                                        if (isTrackUIType) {
                                            replaceRefreshIting(
                                                commonSubElementList,
                                                oldId,
                                                subElement.refId
                                            )
                                            replaceRefreshIting(
                                                moduleItem.subElements,
                                                oldId,
                                                subElement.refId
                                            )
                                            notifyDataSetChanged()
                                        } else {
                                            notifyItemChanged(position)
                                        }
                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView),
                                            commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject,
                                            holder.itemView
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                        removeElement(
                                            commonSubElementList,
                                            moduleItem.subElements,
                                            oldId,
                                            position,
                                            modulePosition
                                        )
                                    }
                                })
                        }
                    }
                    build.disLikeLeve2Build = disLikeLeve2Build
                    XmMoreFuncManager.checkShowMorePage(build)
                } else {
                    val requestMap = mutableMapOf<String, String>()
                    val traceMap = mutableMapOf<String, String>()
                    traceMap["currPage"] = "newHomePage"
                    traceMap["cardPosition"] = (modulePosition + 1).toString()
                    traceMap["positionNew"] = (position + 1).toString()
                    if (!moduleItem.ubtV2.isNullOrEmpty()) {
                        traceMap.putAll(moduleItem.ubtV2)
                    }
                    if (!commonSubElement.ubtV2.isNullOrEmpty()) {
                        traceMap.putAll(commonSubElement.ubtV2)
                    }
                    traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                    traceMap["contentType"] = commonSubElement.bizType ?: ""
                    traceMap["contentId"] = commonSubElement.refId?.toString() ?: ""
                    if (isAlbumUIType) {
                        requestMap[HttpParamsConstants.PARAM_ALBUM_ID] =
                            commonSubElement.refId.toString()
                    } else {
                        requestMap[HttpParamsConstants.PARAM_TRACK_ID] =
                            commonSubElement.refId.toString()
                    }
                    requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                    requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] =
                        commonSubElement.anchor?.uid.toString()
                    requestMap["card_contentType"] = moduleItem.contentType ?: ""
                    requestMap["card_bizType"] = moduleItem.bizType ?: ""
                    requestMap["card_id"] = moduleItem.id.toString()

                    val disLikeLeve2Build = DisLikeLeve2Build()
                    disLikeLeve2Build.isFromAd = false
                    disLikeLeve2Build.anchorName = commonSubElement.anchor?.nickName
                    disLikeLeve2Build.requestMap = requestMap
                    disLikeLeve2Build.traceMap = traceMap
                    disLikeLeve2Build.onFeedBackListener = object :
                        NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onDialogShow(showSuccess: Boolean) {
                        }

                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
                            val oldId = if (isTrackUIType) commonSubElement.refId else 0L
                            val oldItingUrl =
                                if (isTrackUIType) commonSubElement.landingPage else null
                            MainCommonRequest.getSingleSocialListenListItem(
                                position,
                                oldItingUrl,
                                moduleItem,
                                object :
                                    IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            removeElement(
                                                commonSubElementList,
                                                moduleItem.subElements,
                                                oldId,
                                                position,
                                                modulePosition
                                            )
                                            return
                                        }
                                        commonSubElementList[position] = subElement
                                        if (isTrackUIType) {
                                            replaceRefreshIting(
                                                commonSubElementList,
                                                oldId,
                                                subElement.refId
                                            )
                                            replaceRefreshIting(
                                                moduleItem.subElements,
                                                oldId,
                                                subElement.refId
                                            )
                                            notifyDataSetChanged()
                                        } else {
                                            var firstPos =
                                                (recyclerView.layoutManager as GridLayoutManager).findFirstCompletelyVisibleItemPosition()
                                            if (firstPos < 0) {
                                                firstPos =
                                                    (recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                                            }
                                            notifyItemChanged(position)  // 使用这个  当前在最后一列时  系统定位不准 会滚动到前一列
                                            recyclerView.scrollToPosition(firstPos)
                                        }
                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView),
                                            commonSubElement.ext?.subRefInfo?.businessExtraInfo?.adInfoObject,
                                            holder.itemView
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                        removeElement(
                                            commonSubElementList,
                                            moduleItem.subElements,
                                            oldId,
                                            position,
                                            modulePosition
                                        )
                                    }
                                })
                        }
                    }

                    var typeStr: Int? = null
                    val refId = commonSubElement.refId
                    if (isAlbumUIType) {
                        typeStr = MoreFuncBuild.TYPE_ALBUM
                    } else if (isTrackUIType) {
                        typeStr = MoreFuncBuild.TYPE_TRACK
                    }
                    val build: MoreFuncBuild = MoreFuncBuild.createCommonLongClickModel(
                        fragment, typeStr, refId, null, true, disLikeLeve2Build
                    )
                    val trackMap = mutableMapOf<String, String?>().apply {
                        put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                        put("contentType", commonSubElement.bizType ?: "")
                        put("contentId", commonSubElement.refId?.toString() ?: "")
                        put("modulePosition", (modulePosition + 1).toString())
                        put("positionNew", (position + 1).toString())
                        moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                        commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                    }
                    build.trackMap = trackMap

                    XmMoreFuncManager.checkShowMorePage(build)
                }
                true
            }

            val layoutParams = holder.cslContainerView.layoutParams
            val liveLayoutParams = holder.liveRootItemView?.layoutParams
            val layoutParamsTrack = holder.cslTrackContainerView.layoutParams
            val textViewContainerWithInPx: Int
            if (position >= start) {
                // 最后一列
                layoutParams.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                liveLayoutParams?.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                layoutParamsTrack.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                textViewContainerWithInPx =
                    getRpAdaptSize(375 - 3) - if (enableJumpMore) 37.dp else 0
            } else {
                layoutParams.width = getRpAdaptSize(337 - getOffset())
                layoutParamsTrack.width = getRpAdaptSize(337 - getOffset())
                liveLayoutParams?.width = getRpAdaptSize(337 - getOffset())
                textViewContainerWithInPx = getRpAdaptSize(337 - getOffset() - 3)
            }
            if (isAlbumUIType) {
                onBindAlbumView(commonSubElement, holder, textViewContainerWithInPx)
            } else if (isLiveUIType) {
                onBindLiveView(commonSubElement, holder)
            } else {
                onBindTrackView(commonSubElement, holder, textViewContainerWithInPx)
                if (NewShowNotesManager.userNewShowNotes()) {
                    holder.showNotePlayBtnBgWrap.setOnClickListener {
                        onItemClickInner(
                            commonSubElement,
                            false,
                            isTrackUIType,
                            false,
                            position,
                            it,
                            advertis,
                            true
                        )
                    }
                }
            }
            holder.itemView.setOnClickListener {
                onItemClickInner(
                    commonSubElement,
                    isAlbumUIType,
                    isTrackUIType,
                    isLiveUIType,
                    position,
                    it,
                    advertis,
                    false
                )
            }
        }

        private fun removeElement(
            commonSubElementList: MutableList<CommonSubElement>,
            subElements: MutableList<CommonSubElement>?,
            oldId: Long?,
            position: Int,
            modulePosition: Int
        ) {
            val size = commonSubElementList.size
            if (size <= 3) { // 删到只有两个item时 直接移除整个模块
                dataAction?.remove(modulePosition)
                return
            }

            var firstPos =
                (recyclerView.layoutManager as GridLayoutManager).findFirstCompletelyVisibleItemPosition()
            if (firstPos < 0) {
                firstPos =
                    (recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
            }

            commonSubElementList.removeAt(position)
            subElements?.removeAt(position)

            // 代表专辑听单
            if (oldId == null || oldId == 0L) {
                val newSize = commonSubElementList.size
                notifyItemRemoved(position)
                if (position != newSize) {
                    notifyItemRangeChanged(position, newSize - position)
                }
            } else {
                commonSubElementList.forEach {
                    it.landingPage =
                        it.landingPage?.replace("${oldId},", "")?.replace(oldId.toString(), "")
                }
                subElements?.forEach {
                    it.landingPage =
                        it.landingPage?.replace("${oldId},", "")?.replace(oldId.toString(), "")
                }
                notifyDataSetChanged()
            }

            recyclerView.scrollToPosition(firstPos)
        }

        private fun onItemClickInner(
            commonSubElement: CommonSubElement, isAlbumUIType: Boolean, isTrackUIType: Boolean,
            isLiveUIType: Boolean,
            position: Int, view: View?, advertis: Advertis?, isClickPlayBtn: Boolean
        ) {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return
            }
            var albumId = 0L
            var trackId = 0L
            var contentId = ""
            var rewardName = ""
            var rankName = ""
            if (isAlbumUIType) {
                albumId = commonSubElement.refId ?: 0
                contentId = albumId.toString()
            } else if (isTrackUIType) {
                trackId = commonSubElement.refId ?: 0
                contentId = trackId.toString()
            } else if (isLiveUIType) {
                contentId = commonSubElement.refId?.toString() ?: "0"
                rewardName = LiveLotteryTagView.TagType.convert(
                    commonSubElement.ext?.other?.lotteryType
                )?.desc ?: ""
                rankName = commonSubElement.ext?.other?.rankName ?: ""
            }
            val tarTypeId = commonSubElement.ext?.tagType ?: 0
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put("card_adTopn", moduleItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            // 新首页-社会化标注-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(60896) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("albumId", albumId.toString())
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", moduleItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", moduleItem.id.toString()) // 例如：100000000
                .put("rec_src", moduleItem.ubt?.recSrc ?: "")
                .put("rec_track", moduleItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", moduleItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("trackId", trackId.toString()) // 当是声音时传
                .put("titleId", moduleItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("socialTagId", commonSubElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", moduleItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", moduleItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                ) // 可见区域占屏幕的比例
                .put("isAd", if (advertis != null) "true" else "false")
                .put("area", if (isClickPlayBtn) "play" else "item")
                .put("rewardName", rewardName) // 直播 奖励名称
                .put("rankName", rankName) // 直播 榜单名称
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = commonSubElement?.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()
            if (isTrackUIType && !isClickPlayBtn && NewShowNotesManager.userNewShowNotes()) {
                // 打开shownotes二级页
                NewShowNotesManager.startShowNotesDetailFragment(
                    NewShowNotesManager.SOURCE_FROM_HOME,
                    NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                    0,
                    trackId,
                    null
                )
                clickAdRecord(advertis, position)
            } else {
                if (isClickPlayBtn) {
                    val track = Track()
                    track.dataId = trackId
                    val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
                    if (isPlaying) {
                        PlayTools.pause(
                            BaseApplication.getMyApplicationContext(),
                            PauseReason.Business.RecommendSocialListenList
                        )
                    } else {
                        ToolUtil.clickUrlAction(
                            fragment,
                            NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                            view
                        )
                        clickAdRecord(advertis, position)
                    }
                } else {
                    if (isTrackUIType) {
                        ToolUtil.clickUrlAction(
                            fragment,
                            NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage!!),
                            view
                        )
                    } else {
                        ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, view)
                    }
                    clickAdRecord(advertis, position)
                }
            }
        }

        private fun clickAdRecord(advertis: Advertis?, position: Int) {
            if (advertis != null) {
                AdManager.handlerAdClick(
                    BaseApplication.getMyApplicationContext(), advertis,
                    AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SITE_CLICK,
                        AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST
                    )
                        .onlyClickRecord(true)
                        .positionNew(position + 1)
                        .modulePosition(modulePosition + 1)
                        .build()
                )
            }
        }

        private fun replaceRefreshIting(list: List<CommonSubElement>?, oldId: Long?, newId: Long?) {
            if (list.isNullOrEmpty() || oldId == 0L || newId == 0L) {
                return
            }
            list.forEach {
                it.landingPage = it.landingPage?.replace(oldId.toString(), newId.toString())
            }
        }

        private fun setPlayBgColor(color: Int, holder: SocialListAlbumViewHolder) {
            val gradientDrawable = GradientDrawable()
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.cornerRadius = 14.dp.toFloat()
            gradientDrawable.setColor(color)
            holder.vPlayButtonBg.background = gradientDrawable
        }

        override fun getItemCount(): Int {
            if (enableJumpMore) {
                return commonSubElementList.size + 1
            }
            return commonSubElementList.size
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class SocialListAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)

            //            var itemCoverIv: AlbumCoverImage = view.findViewById(R.id.main_iv_item_cover)
//            var itemAlbumTagIv: ImageView = view.findViewById(R.id.main_iv_album_pay_cover_tag)
            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutRightTxtArea: View = view.findViewById(R.id.main_layout_right_txt_area)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)
            var ivAdTagTrack: ImageView = view.findViewById(R.id.main_iv_ad_tag_track)
            var showTagParentAlbum: ViewGroup? = view.findViewById(R.id.main_layout_show_tag_parent_album)
            var showTagParentTrack: ViewGroup? = view.findViewById(R.id.main_layout_show_tag_parent_track)

            var cslTrackContainerView: View = view.findViewById(R.id.main_csl_item_root_view_track)
            var tvTrackTitle: TextView = view.findViewById(R.id.main_tv_track_title)
            var crlTrackCover: CornerRelativeLayout? = view.findViewById(R.id.main_crl_item_cover)
            var ivTrackCover: ImageView = view.findViewById(R.id.main_iv_item_cover_track)
            var tvTrackSubTitle: TextView = view.findViewById(R.id.main_tv_track_sub_title)
            var trackShowTagLayout: LinearLayout =
                view.findViewById(R.id.main_tv_track_show_tag_layout)
            var vPlayButtonBg: View = view.findViewById(R.id.main_v_bg_play)
            var ivPlayButton: ImageView = view.findViewById(R.id.main_iv_play_btn)
            var showNotePlayBtnBgWrap: View = view.findViewById(R.id.main_show_notes_play_layout_wrap)
            var showNotePlayBtnBg: View = view.findViewById(R.id.main_show_notes_play_layout)
            var showNotePlayBtn: ImageView = view.findViewById(R.id.main_iv_show_notes_play_btn)
            var bgItemGuideView: View = view.findViewById(R.id.main_bg_item_guide_view)

            // live
            private val liveViewStub: ViewStub = view.findViewById(R.id.main_item_live_vs)
            var liveView: View? = null
            var liveRootItemView: View? = null
            var liveItemCoverView: RoundImageView? = null
            var liveItemPlaying: XmLottieAnimationView? = null
            var liveItemTitleView: TextView? = null
            var liveItemSubNickname: TextView? = null
            var liveItemLotteryTag: LiveLotteryTagView? = null
            var liveItemRankTag: TextView? = null
            var liveItemCategory: TextView? = null
            var liveItemHotValue: LiveHotValueTagView? = null
            var liveShowTag: ViewGroup? = null

            init {
                resetSize()
            }

            fun initLiveView() {
                if (liveView != null) {
                    return
                }
                liveView = liveViewStub.inflate()
                liveRootItemView = liveView!!.findViewById(R.id.main_csl_item_root_view_live)
                liveItemCoverView = liveView!!.findViewById(R.id.main_iv_item_live_cover)
                liveItemPlaying = liveView!!.findViewById(R.id.main_live_lottie_play_status)
                liveItemTitleView = liveView!!.findViewById(R.id.main_live_tv_title)
                liveItemSubNickname = liveView!!.findViewById(R.id.main_tv_sub_nickname_and_badge)
                liveItemLotteryTag = liveView?.findViewById(R.id.main_live_tag_lottery_view)
                liveItemRankTag = liveView?.findViewById(R.id.main_live_tag_rank_view)
                liveItemCategory = liveView!!.findViewById(R.id.main_live_tv_category)
                liveItemHotValue = liveView!!.findViewById(R.id.main_live_tv_hot_value)
                liveShowTag = liveView!!.findViewById(R.id.main_ll_bottom_tag)
            }

            fun resetSize() {
                val layoutParams = cslContainerView.layoutParams
                layoutParams.width = getRpAdaptSize(323)
            }
        }
    }

    companion object {
        private var AD_REPORT_SUB_PERCENT = ConfigureCenter.getInstance().getInt(
            CConstants.Group_ad.GROUP_NAME,
            CConstants.Group_ad.ITEM_HOME_AD_EXPOSE_PERCENT,
            50
        )

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        fun traceAlbumInner(
            subElement: CommonSubElement,
            recommendCommonItem: RecommendCommonItem,
            data: RecommendItemNew?,
            index: Int,
            modulePosition: Int,
            exploreArea: Int,
            advertis: Advertis?,
            itemView: View,
        ) {
            var albumId = 0L
            var trackId = 0L
            var contentId = ""
            var rewardName = ""
            var rankName = ""
            if (subElement.bizType == "Album" || subElement.bizType == "Playlet") {
                albumId = subElement.refId ?: 0
                contentId = albumId.toString()
            } else if (subElement.bizType == "Track") {
                trackId = subElement.refId ?: 0
                contentId = trackId.toString()
            } else if (subElement.isLiveBizType()) {
                contentId = subElement.refId?.toString() ?: "0"
                rewardName = LiveLotteryTagView.TagType.convert(
                    subElement.ext?.other?.lotteryType
                )?.desc ?: ""
                rankName = subElement.ext?.other?.rankName ?: ""
            }
            val tarTypeId = subElement.ext?.tagType ?: 0
            // 新首页-社会化标注-专辑卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(60897)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put(
                    "tagType",
                    tarTypeId.toString()
                ) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("albumId", albumId.toString())
                .put("exploreArea", exploreArea.toString()) // 可见区域占屏幕的比例
                .put("positionNew", (index + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", recommendCommonItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.id.toString()) // 例如：100000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", subElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", data?.xmRequestId)
                .put("trackId", trackId.toString()) // 当是声音时传
                .put("titleId", recommendCommonItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("socialTagId", subElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put("isAd", if (advertis != null) "true" else "false")
                .put("rewardName", rewardName) // 直播 奖励名称
                .put("rankName", rankName) // 直播 榜单名称
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = subElement?.title,
                contentPosition = (index + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data?.isLocalCache == true) {
                trace.isLocalCache
            }
            trace.createTrace()

            // 广告曝光上报
            if (advertis != null && !advertis.isShowedToRecorded && data != null && !data.isLocalCache) {
                AdManager.adRecord(
                    BaseApplication.getMyApplicationContext(), advertis,
                    AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SITE_SHOW,
                        AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST
                    )
                        .positionNew(index + 1)
                        .modulePosition(modulePosition + 1)
                        .build()
                )
                advertis.isShowedToRecorded = true
            }
            if (advertis != null && !advertis.isRecordedSubPercent && data != null && !data.isLocalCache && exploreArea >= AD_REPORT_SUB_PERCENT) {
                // 做广告曝光50%的上报
                AdManager.reportSubPercentShow(advertis, data.xmRequestId)
                advertis.isRecordedSubPercent = true
            }

            HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, itemView, index)
        }
    }
}