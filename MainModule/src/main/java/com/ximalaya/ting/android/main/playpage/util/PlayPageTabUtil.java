package com.ximalaya.ting.android.main.playpage.util;

import android.os.Bundle;
import android.text.TextUtils;
import androidx.fragment.app.Fragment;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.exception.NonException;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.play.XPlayPage;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.model.play.PlayPageTab;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.AccessibilityUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.android.main.playpage.fragment.*;
import com.ximalaya.ting.android.main.playpage.internalservice.IPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayFragmentAbManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.NewPlayPageUtil;

/**
 * Created by WolfXu on 2020-04-28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class PlayPageTabUtil {

    public static Class<? extends Fragment> getClassByType(int type, boolean showPlayFragmentReq, PlayingSoundInfo soundInfo) {
       if (type == PlayPageTab.TYPE_VIDEO) {
           if (isPaid(soundInfo) && forceUseOldWhenPaidItem()) {
               return VideoPlayTabFragment.class;
           } else if (isVerticalVideo(soundInfo, showPlayFragmentReq)) {
               return VideoPlayTabFragmentNew.class;
           } else {
               return VideoPlayTabFragment.class;
           }
       } else {
           return getClassByType(type, soundInfo);
       }
    }

    private static boolean forceUseOldWhenPaidItem() {
        // 与ios同步，当ab值为true时使用新的视频页，false时使用旧的视频页，默认使用新的
        return !ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME, "paidForNewVideo", true);
    }

    private static boolean isPaid(PlayingSoundInfo soundInfo) {
        if (soundInfo == null || soundInfo.trackInfo == null || soundInfo.albumInfo == null) {
            return true;
        }
        if (soundInfo.trackInfo.isPaid) {
            return true;
        }
        // 会员抢先听
        if (soundInfo.trackInfo.vipPriorListenStatus == 1 || soundInfo.albumInfo.isVipFirst)  {
            return true;
        }
        // 免费专辑付费声音
        if (soundInfo.albumInfo.trackPaidType == 1) {
            return true;
        }
        if (soundInfo.authorizeInfo != null && (soundInfo.authorizeInfo.isXimiTrack || soundInfo.authorizeInfo.ximiFirstStatus == 1)) {
            return true;
        }
        return false;
    }

    public static Class<? extends Fragment> getClassByType(int type, PlayingSoundInfo soundInfo) {
        Class<? extends Fragment> clazz = null;
        switch (type) {
            case PlayPageTab.TYPE_SOUND:
                clazz = getPlayPage(soundInfo);
                NewPlayPageUtil.saveYPageStyle(clazz == YPlayFragment.class);
                break;
            case PlayPageTab.TYPE_COMMENT:
                clazz = PlayCommentsTabFragment.class;
                break;
            case PlayPageTab.TYPE_NOVEL:
                clazz = PlayNovelTabFragment.class;
                break;
            case PlayPageTab.TYPE_DOC:
                clazz = PlayManuscriptTabFragment.class;
                break;
            case PlayPageTab.TYPE_VIDEO:
                if (isVerticalVideo(soundInfo, false)) {
                    clazz = VideoPlayTabFragmentNew.class;
                } else {
                    clazz = VideoPlayTabFragment.class;
                }
                break;
            case PlayPageTab.TYPE_MUSIC:
                break;
            case PlayPageTab.TYPE_H5:
                break;
            case PlayPageTab.TYPE_INSTANT_SCRIP:
                break;
            case PlayPageTab.TYPE_TRACK_DETAIL:
                clazz = PlayTrackIntroDetailTabFragment.class;
            default:
                break;
        }
        return clazz;
    }

    public static void addArguments(Bundle arguments, PlayPageTab tab) {
        if (arguments == null || tab == null) {
            return;
        }
        switch (tab.getType()) {
            case PlayPageTab.TYPE_NOVEL:
            case PlayPageTab.TYPE_TTS:
                if (tab.getTabExtra() != null) {
                    arguments.putLong(BundleKeyConstantsInMain.KEY_BOOK_ID, tab.getTabExtra().getBookId());
                    arguments.putLong(BundleKeyConstantsInMain.KEY_CHAPTER_ID, tab.getTabExtra().getChapterId());
                    arguments.putInt(BundleKeyConstantsInMain.KEY_TYPE, tab.getTabExtra().getType());
                }
                break;
            default:
                break;
        }
    }

    public static boolean isFragmentOfType(@PlayPageTab.TabType int type, Fragment fragment, PlayingSoundInfo soundInfo) {
        if (type == PlayPageTab.TYPE_VIDEO) {
            if (fragment instanceof VideoPlayTabFragment || fragment instanceof VideoPlayTabFragmentNew) {
                return true;
            }
        }
        Class<? extends Fragment> fragmentClass = PlayPageTabUtil.getClassByType(type, soundInfo);
        return fragmentClass != null && fragmentClass.isInstance(fragment);
    }

    public static boolean isClassOfType(@PlayPageTab.TabType int type, Class clazz, PlayingSoundInfo soundInfo) {
        if (type == PlayPageTab.TYPE_VIDEO) {
            if (clazz == VideoPlayTabFragment.class || clazz == VideoPlayTabFragmentNew.class) {
                return true;
            }
        }
        return clazz != null && clazz == PlayPageTabUtil.getClassByType(type, soundInfo);
    }

    public static int checkPlayPageTabType(int focusTabType) {
        for (int i : PlayPageTab.PLAY_TAB_TYPE_ARR) {
            if (i == focusTabType) {
                return focusTabType;
            }
        }
        return PlayPageTab.TYPE_NONE;
    }

    private static int verticalVideo = 0;

    public static boolean isVerticalVideo(PlayingSoundInfo soundInfo, Boolean showPlayFragmentReq) {
        if (ConstantsOpenSdk.isDebug) {
            int debugInt = BaseUtil.getIntSystemProperties("debug.xima.video");
            if (debugInt > 0) return true;
            if (debugInt < 0) return false;
        }

        if ((verticalVideo == 0 || showPlayFragmentReq) && soundInfo != null) {
            if (showPlayFragmentReq) PlayTools.showPlayFragmentReq = false;
            if ("vertical".equalsIgnoreCase(soundInfo.videoPlayType)) {
                verticalVideo = 1;
            } else {
                verticalVideo = -1;
            }
        }

        return verticalVideo == 1 && PlayFragmentAbManager.getInstance().useNewVideoTab();
    }

    public static boolean isInteractiveAudioPlayFragment(PlayingSoundInfo soundInfo) {
        return false;
//        return soundInfo != null && soundInfo.otherInfo != null && soundInfo.otherInfo.displayInteractive &&
//                !ChildProtectManager.isChildMode(BaseApplication.getMyApplicationContext());
    }

    /**
     * 配置中心，
     * 是否打开v3播放页
     */
    private static boolean isShowPlayPageV3ByConfigCenter(){
        try {
            boolean enable = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.OPEN_ANDROID_PLAY_PAGE_V3);
            return enable;
        } catch (NonException e) {
            e.printStackTrace();
        }
        return true;
    }

    public static Class<? extends Fragment> getPlayPage(PlayingSoundInfo soundInfo) {
//        return YPlayFragment.class;
        int localStyle = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(
                PreferenceConstantsInOpenSdk.KEY_USE_PLAY_FRAGMENT_STYLE, -1);
        if (localStyle > 0) {
            if (localStyle == 3) {
                return YPlayFragment.class;
            }
            return AudioPlayFragment.class;
        }

        if (isPlayY(soundInfo)) {
            return YPlayFragment.class;
        }
        return AudioPlayFragment.class;
    }

    private static boolean screenReaderEnable = false;
    private static long lastCheckTs = 0;

    private static boolean isScreenReaderEnableNew() {
        if (System.currentTimeMillis() - lastCheckTs < 1500) {
            return screenReaderEnable;
        }
        screenReaderEnable = AccessibilityUtil.INSTANCE.isScreenReaderEnableNew();
        lastCheckTs = System.currentTimeMillis();
        return screenReaderEnable;
    }

    /**
     * 2023 年 V3 半屏播放页根据本地设置和播放页接口数据判断
     * 本地debug设置优先
     */
    public static boolean isPlayX(PlayingSoundInfo soundInfo) {
        return false;
    }

    public static boolean isPlayY(PlayingSoundInfo soundInfo) {
        if (isV4ByLocalConfig()) {
            return true;
        }

        if (isScreenReaderEnableNew() ||
                !isShowPlayPageV3ByConfigCenter() ||
                !PlayFragmentAbManager.getInstance().useNewVideoTab()) {
            return false;
        }

        //有视频的声音不进
//        if (soundInfo != null && soundInfo.trackInfo != null) {
//            if (soundInfo.trackInfo.isVideo) {
//                return false;
//            }
//        }

        //无网络时, soundInfo 来自播放器，保持住 xplayfragment 不变
        if (soundInfo != null && soundInfo.fromPlayer && XPlayPageRef.get() != null) {
            return true;
        }

        String playPageType = "";
        if (soundInfo != null && soundInfo.otherInfo != null) {
            playPageType = soundInfo.otherInfo.pageStyle;
        }
        return "heightScreen".equals(playPageType);
    }

    public static boolean isV4ByLocalConfig() {
        return BaseUtil.getIntSystemProperties("debug.xima.playpage.v4") > 0 ||
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(
                        PreferenceConstantsInOpenSdk.KEY_USE_PLAY_FRAGMENT_STYLE, -1)  == 3;
    }



    /**
     * 2023 年 V3 半屏播放页debug环境本地设置判断
     */
    public static boolean isV3ByLocalConfig() {
        return MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(
                PreferenceConstantsInOpenSdk.KEY_USE_NEW_PLAY_FRAGMENT, false)
                || "halfScreen".equals(BaseUtil.getStrSystemProperties("debug.xima.playpage"))
                || MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(
                PreferenceConstantsInOpenSdk.KEY_USE_PLAY_FRAGMENT_STYLE, -1)  == 2;
    }


    /**
     * 2022年 V2 播放页判断，已废弃，请勿使用
     * @param soundInfo
     * @return
     */
    @Deprecated
    public static boolean isNewAudioPage(PlayingSoundInfo soundInfo) {
       if (ConstantsOpenSdk.isDebug) {
           int debugInt = BaseUtil.getIntSystemProperties("debug.xima.newplay");
           if (debugInt > 0) return true;
           if (debugInt < 0) return false;
       }
        if (MmkvCommonUtil.getInstance(ToolUtil.getCtx()).containsKey(PreferenceConstantsInOpenSdk.KEY_USE_NEW_PLAY_FRAGMENT)) {
            return MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_NEW_PLAY_FRAGMENT, false);
        }
       return soundInfo != null && soundInfo.otherInfo!= null && "new".equals(soundInfo.otherInfo.pageStyle);
    }

    // 强制进入老播放页，当前离线模式使用
    public static void enforceOldPlayPage(PlayingSoundInfo soundInfo) {
        if (soundInfo != null && soundInfo.otherInfo != null) {
            soundInfo.otherInfo.pageStyle = "old";
        }
    }

    public static boolean hasTab(@PlayPageTab.TabType int tabType) {
        IPlayFragmentService playFragmentService =
                PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
        if (playFragmentService != null) {
            return playFragmentService.hasTab(tabType);
        }
        return false;
    }
}
