package com.ximalaya.ting.android.main.model.rec

import androidx.annotation.Keep
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.main.model.recommend.DislikeReasonModelV1
import org.json.JSONArray
import org.json.JSONObject

/**
 * 儿童听单卡片数据
 */
@Keep
data class RecommendRankListItem(
    val moduleId: Long?,
    val moduleType: String? = "",
    // 听单标题
    val title: String? = "",
    val tagSelected: String? = "",
    val list: List<AlbumRank>? = null,
    val ubtV2: Map<String, String>?,
    val bizType: String? = "",
    val contentType: String? = "",
    val id: Long?,
    var innerListSelectedIndex: Int = 0,
    var dislikeReasonNewV1: List<DislikeReasonModelV1>? = null,
    var cardAdCount: Int = -1,
    val style: String? = "",
    var extraInfo: ExtraInfo?,
    var hasSortByMNN: Boolean? = false,
    var landingPage: String? = "",
)

// 对应单个榜单
data class AlbumRank(
    val refId: Long?,
    val uid: Long?,
    val id: Long?,
    val title: String? = "",
    val darkTitleIconUrl: String? = "",
    val titleIconUrl: String? = "",
    val ubtV2: Map<String, String>?,
    val landingPage: String? = "",
    var subElements: List<RankSubElement>? = null,
    var scrollPosition: Int = 0,
    var extraInfo: Map<String, String>?,
    var other: Map<String, String>?,
) {
    public fun equals(other: AlbumRank?): Boolean {
        if (this === other) {
            return true
        }
        if (other == null) {
            return false
        }
        if (this.refId != other.refId) {
            return false
        }
        return if (extraInfo == null) {
            other.extraInfo == null
        } else {
            extraInfo!!.equals(other.extraInfo)
        }
    }

    fun getRankTitleUrl(): String? {
        return if (BaseFragmentActivity.sIsDarkMode) {
            other?.get("titleBlackStyleUrl")
        } else {
            other?.get("titleWhiteStyleUrl")
        }
    }

}

data class RankSubCountData(val play: Long? = 0, val comments: Long? = 0, val subscribe: Long? = 0)

data class RankSubElement(
    // 内容库id
    val id: Long?,
    // 听单组合id
    val refId: Long?,
    val anchor: Anchor? = null,
    // 听单标题
    var title: String? = "",
    val summary: String? = "",
    val bizType: String? = "",
    var contentType: String? = "",
    var cover: String? = null,
    var landingPage: String? = "",
    val ubt: Ubt? = null,
    val ubtV2: Map<String, String>?,
    val extraInfo: ExtraInfo? = null,
    var customTitle: String? = "",
    var socialTag: String? = "",
    var wrap: RankWrap? = null,
    var metaDataTags: List<MetaDataTag>? = null,
    var cachedCoverColor: Int? = ColorUtil.INVALID_COLOR,
    var categoryId: Int? = 0,
    var count:RankSubCountData? = null,
    var localRankScore: Float? = 0f, // 本地生成
    val other: CommonOther? = null,
) {

    fun isAlbum(): Boolean {
        return contentType == "Album"
    }

    fun isTrack(): Boolean {
        return contentType == "Track"
    }

    public fun getSubTittle(): String? {
        if (!this.customTitle.isNullOrEmpty()) {
            return this.customTitle
        } else if (!this.summary.isNullOrEmpty()) {
            return this.summary
        } else {
            if (metaDataTags.isNullOrEmpty()) {
                return ""
            }
            return metaDataTags!![0].tag
        }
    }

    public fun getSocialTagId(): Long? {
        if (metaDataTags.isNullOrEmpty()) {
            return 0
        }
        return metaDataTags!![0].trackingId
    }

    public fun getCustomTag(): String? {
        if (!this.socialTag.isNullOrEmpty()) {
            return this.socialTag
        } else if (!this.customTitle.isNullOrEmpty()) {
            return this.customTitle
        } else if (!this.summary.isNullOrEmpty()) {
            return this.summary
        } else {
            if (metaDataTags.isNullOrEmpty()) {
                return ""
            }
            return metaDataTags!![0].tag
        }
    }
}

data class LtSubscriptTag(var tag: String? = "",
                          var trackingId: Long = 0L) {

}

data class RankWrap(
    var other: String? = "",
    var customTitle: String? = "",
    var socialTag: String? = "",
    var ltSubscriptTag: LtSubscriptTag? = null
) {
    public fun init() {
        if (other.isNullOrEmpty()) {
            return
        }
        var jsonObject = try {
            JSONObject(other)
        } catch (e: Exception) {
            null
        }
        if (jsonObject == null) {
            return
        }
        if (jsonObject.has("customTitle")) {
            customTitle = jsonObject.getString("customTitle")
        }
        if (jsonObject.has("socialTag")) {
            var socialTagString = jsonObject.getString("socialTag")
            var socialTagJSONObject: JSONObject? = try {
                JSONObject(socialTagString)
            } catch (e: Exception) {
                null
            }
            if (socialTagJSONObject == null || !socialTagJSONObject.has("tag")) {
                return
            }
            socialTag = socialTagJSONObject.getString("tag")
        }
    }
}