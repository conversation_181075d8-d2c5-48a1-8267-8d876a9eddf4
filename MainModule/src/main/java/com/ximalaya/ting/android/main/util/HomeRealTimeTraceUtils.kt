package com.ximalaya.ting.android.main.util

import android.view.View
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.model.rec.AlbumRank
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RankSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendKidAllLikeItem
import com.ximalaya.ting.android.read.utils.LogUtils
import java.util.Objects
import com.google.gson.Gson
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.base.BaseModel
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.model.rec.RecommendRankListItem
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.read.utils.GsonUtil

/**
 * 首页实时上报工具类
 */
object HomeRealTimeTraceUtils {

    private const val TAG = "HomeRealTimeTraceUtils"
    private val totalTraceItem = HashSet<HomeTraceModel>()

    // 等待上报的数据
    private val pendingUploadTraceItem = HashSet<HomeTraceModel>()

    // 正在上报中的数据
    private val uploadingTraceItem = HashSet<HomeTraceModel>()

    private var uploadRun: Runnable? = null
    private const val UPLOAD_DELAY_MS = 300L

    @JvmStatic
    private fun isAllowUploadData(): Boolean {
        return EasyConfigure.getBoolean("allow_home_real_time_trace_data", true)
    }

    @JvmStatic
    fun traceItemShow(
        data: RecommendItemNew?,
        rankListItem: RecommendRankListItem?,
        albumRank: AlbumRank?,
        subElement: RankSubElement,
        itemView: View,
        index: Int
    ) {
        if (!isAllowUploadData()) {
            return
        }
        val title = subElement.title
        val cardId = subElement.id
        val bizType = subElement.bizType
        val refId = subElement.refId

        addTraceItemData(data, title, cardId, bizType, refId, index, itemView)
    }

    @JvmStatic
    fun traceItemShow(
        data: RecommendItemNew?,
        recommendCommonItem: RecommendCommonItem,
        subElement: CommonSubElement,
        itemView: View,
        index: Int
    ) {
        if (!isAllowUploadData()) {
            return
        }
        val title = subElement.title
        val cardId = subElement.id
        val bizType = subElement.bizType
        val refId = subElement.refId

        addTraceItemData(data, title, cardId, bizType, refId, index, itemView)
    }

    @JvmStatic
    fun traceItemShow(
        data: RecommendItemNew?,
        recommendCommonItem: RecommendKidAllLikeItem,
        subElement: CommonSubElement,
        itemView: View,
        index: Int
    ) {
        if (!isAllowUploadData()) {
            return
        }
        val title = subElement.title
        val cardId = subElement.id
        val bizType = subElement.bizType
        val refId = subElement.refId

        addTraceItemData(data, title, cardId, bizType, refId, index, itemView)
    }

    private fun addTraceItemData(
        data: RecommendItemNew?,
        title: String?,
        cardId: Long?,
        bizType: String?,
        refId: Long?,
        index: Int,
        itemView: View,
    ) {
        if (data == null || cardId == null || bizType.isNullOrEmpty() || refId == null) {
            return
        }

        if (data.isLocalCache) {
            // printLog("$title bizType:$bizType refId:$refId cardId:$cardId 本地数据")
            return
        }

        val percent = ViewStatusUtil.getViewVisibleAreaRealPercent(itemView)
        if (percent < 50) {
            // printLog("$title bizType:$bizType refId:$refId cardId:$cardId percent:${percent} 不可见")
            return
        }

        val traceItem = HomeTraceModel(
            title,
            cardId,
            bizType,
            refId,
            index
        )
        if (totalTraceItem.contains(traceItem)
            || pendingUploadTraceItem.contains(traceItem)
            || uploadingTraceItem.contains(traceItem)
        ) {
            // printLog("$title bizType:$bizType refId:$refId cardId:$cardId percent:${percent} 已曝光")
            return
        }

        // printLog("$title bizType:$bizType refId:$refId cardId:$cardId 上报数据", false)

        pendingUploadTraceItem.add(traceItem)
        scheduleUploadTraceData()
    }

    private fun scheduleUploadTraceData() {
        if (pendingUploadTraceItem.isEmpty()) {
            return
        }

        if (uploadRun != null) {
            HandlerManager.removeCallbacks(uploadRun)
        }
        uploadRun = Runnable {
            realUploadTraceData(HashSet(pendingUploadTraceItem))
            pendingUploadTraceItem.clear()
        }
        HandlerManager.postOnUIThreadDelay(uploadRun, UPLOAD_DELAY_MS)
    }

    private fun realUploadTraceData(uploadData: HashSet<HomeTraceModel>) {
        if (uploadData.isEmpty()) {
            return
        }

        uploadingTraceItem.addAll(uploadData)

        val uploadList = uploadingTraceItem.map {
            mapOf(
                "id" to it.cardId,
                "refId" to it.refId,
                "bizType" to it.bizType
            )
        }
        val params = GsonUtil.getInstance().getJson(uploadList)

        CommonRequestM.basePostRequestJsonStr(
            UrlConstants.getInstanse().reportRealTimeTraceUrl(), params,
            object : IDataCallBack<BaseModel> {
                override fun onSuccess(data: BaseModel?) {
                    if (data?.ret == 0) {
                        totalTraceItem.addAll(uploadingTraceItem)
                        uploadingTraceItem.clear()
                    } else {
                        // printLog("上传实时曝光数据失败 data:$data")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    // printLog("上传实时曝光数据失败 code:$code message:$message")
                }
            }
        ) { content: String? ->
            if (content.isNullOrEmpty()) {
                return@basePostRequestJsonStr null
            }
            return@basePostRequestJsonStr Gson().fromJson(content, BaseModel::class.java)
        }

    }

    // 下拉刷新清空
    fun resetData() {
        // printLog("清空缓存数据")
        totalTraceItem.clear()
        pendingUploadTraceItem.clear()
        uploadingTraceItem.clear()
    }

    private fun printLog(msg: String, isError: Boolean = true) {
        if (isError) {
            LogUtils.e("$TAG: $msg")
        } else {
            LogUtils.d("$TAG: $msg")
        }
    }
}

data class HomeTraceModel(
    val title: String?,
    val cardId: Long,
    val bizType: String,
    val refId: Long,
    val index: Int,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as HomeTraceModel

        if (bizType != other.bizType) return false
        if (refId != other.refId) return false
        if (index != other.index) return false
        if (cardId != other.cardId) return false

        return true
    }

    override fun hashCode(): Int {
        return Objects.hash(bizType, refId, index, cardId)
    }
}