package com.ximalaya.ting.android.main.util

import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendModelNew
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.read.utils.GsonUtil
import com.ximalaya.ting.android.read.utils.checkActivity
import org.json.JSONObject

object HomeModuleTitleUtils {

    private var cacheTitle = hashMapOf<Long?, String>()

    // 客户端传。0 | 1
    @JvmStatic
    fun isAiTitle(cardId: Long?): String {
        if (cacheTitle[cardId].isNullOrEmpty()) {
            return "0"
        }
        return "1"
    }

    @JvmStatic
    fun checkRequestAiTitle(
        model: RecommendModelNew?,
        isLoadMore: Boolean,
        fragment: RecommendFragmentStaggered
    ) {
        if (!isLoadMore) {
            cacheTitle.clear()
        }

        val list = model?.header
        if (list.isNullOrEmpty()) {
            return
        }

        list.forEach {
            if (it.itemType == RecommendItemNew.RECOMMEND_ITEM_SOCIAL_LISTEN_LIST
                || it.itemType == RecommendItemNew.RECOMMEND_ITEM_SLEEP_AID_CARD
            ) {
                if (it.item is RecommendCommonItem) {
                    checkRequestTitle((it.item as RecommendCommonItem), fragment)
                }
            }
        }
    }

    private fun checkRequestTitle(
        recommendCommonItem: RecommendCommonItem,
        fragment: RecommendFragmentStaggered
    ) {
        if (recommendCommonItem.id == null
            || recommendCommonItem.ext?.lazyTitle != true
        ) {
            return
        }

        if (!cacheTitle[recommendCommonItem.id].isNullOrEmpty()) {
            return
        }

        requestTitle(recommendCommonItem, object : IRequestCalBack {
            override fun onSuccess() {
                checkRefreshUI(recommendCommonItem, fragment)
            }
        })
    }

    private fun checkRefreshUI(
        recommendCommonItem: RecommendCommonItem,
        fragment: RecommendFragmentStaggered
    ) {
        val refreshView = fragment.mRecyclerView
        val mAdapter = fragment.mAdapter

        if (!refreshView?.context.checkActivity()) {
            return
        }

        val first = refreshView?.findFirstVisiblePosition() ?: -1
        val last = refreshView?.findLastVisiblePosition() ?: -1

        if (first < 0 || last < 0) {
            return
        }

        for (modulePosition in first..last) {
            val itemModuleData = mAdapter.mListData.getOrNull(modulePosition)
            if (itemModuleData !is RecommendItemNew) {
                continue
            }
            val itemData = itemModuleData.item as? RecommendCommonItem ?: return

            if (itemData.id == recommendCommonItem.id) {
                mAdapter.notifyItemChanged(modulePosition, "titleChange")
            }
        }
    }

    @JvmStatic
    fun requestTitle(recommendCommonItem: RecommendCommonItem, callBack: IRequestCalBack?) {
        val map = hashMapOf<String, Any?>()

        map["id"] = recommendCommonItem.id
        map["bizType"] = recommendCommonItem.bizType
        map["contentType"] = recommendCommonItem.contentType

        val list = mutableListOf<MutableMap<String, Any?>>()
        recommendCommonItem.subElements?.forEach {
            val map = mutableMapOf<String, Any?>()
            map["id"] = it.id
            list.add(map)
        }
        map["subElements"] = list

        val params = GsonUtil.getInstance().getJson(map)

        CommonRequestM.basePostRequestJsonStr(UrlConstants.getInstanse().aiTitleUrl, params,
            object : IDataCallBack<String> {
                override fun onSuccess(data: String?) {
                    if (!data.isNullOrEmpty()) {
                        cacheTitle[recommendCommonItem.id] = data
                        recommendCommonItem.title = data
                        callBack?.onSuccess()
                    }
                }

                override fun onError(code: Int, message: String?) {
                }
            }
        ) { content: String? ->
            if (content.isNullOrEmpty()) {
                return@basePostRequestJsonStr null
            }

            val jsonObject = JSONObject(content)
            if (jsonObject.optInt("ret") == 0) {
                return@basePostRequestJsonStr jsonObject.optString("data")
            }

            return@basePostRequestJsonStr ""
        }

    }

    interface IRequestCalBack {
        fun onSuccess()
    }
}