package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.marginStart
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.SkinManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.NonScrollableGridLayoutManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.AlbumRank
import com.ximalaya.ting.android.main.model.rec.RankSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendRankListItem
import com.ximalaya.ting.android.main.playpage.playx.utils.getCurrentView
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.satisfy.SatisfactionHomeModuleManager
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by felix.chen on 2023/3/13.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17316357791
 */
class RecommendTagList2024AdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendTagList2024AdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendTagList2024AdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendTagList2024AdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew> {
    private var mOldState = RecyclerView.SCROLL_STATE_IDLE

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_hot_tags_2024, parent, false)
    }

    class RankListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        var contentView: View = convertView.findViewById(R.id.content_view)
        val viewPager: ViewPager = convertView.findViewById(R.id.main_vp_rank_list)
        val tabListRv: RecyclerView = convertView.findViewById(R.id.main_rcv_rank_tab)
        var leftTabShadowView: View = convertView.findViewById(R.id.main_view_left_tab_shadow)
        val rightTabShadowView: View = convertView.findViewById(R.id.main_view_right_tab_shadow)
        val tvMore: TextView = convertView.findViewById(R.id.main_tv_more)
        val tvModuleTitle: TextView = convertView.findViewById(R.id.main_tv_ting_title)

        var recommendItemNew: RecommendItemNew? = null
        var mLastScreenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        var isNeedForceUpdate: Boolean = false
        var isFolderScreen: Boolean? = null

        init {
            resetSize()
        }

        fun resetSize() {
        }
    }

    override fun onConfigurationChanged(holder: RankListCardViewHolder?) {
        holder ?: return
        holder.isNeedForceUpdate = true
    }

    override fun createViewHolder(convertView: View?): RankListCardViewHolder? {
        if (convertView == null) {
            return null
        }
        return RankListCardViewHolder(convertView)
    }

    override fun bindViewHolder(
        holder: RankListCardViewHolder?,
        modulePosition: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }

        isPageChange = false

        SatisfactionHomeModuleManager.checkShowSatisfyView(
            fragment, holder.itemView, recommendItemNew,
            SatisfactionHomeModuleManager.TYPE_CLASSIFICATION, modulePosition
        )

        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendRankListItem = recommendItemNew.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }

        if (modulePosition == 0 && SkinManager.isNeedShowAtmosphereImage()) {
            holder.leftTabShadowView.visibility = View.GONE
            holder.rightTabShadowView.visibility = View.GONE
        } else {
            holder.leftTabShadowView.visibility = View.VISIBLE
            holder.rightTabShadowView.visibility = View.VISIBLE
        }

        if (!holder.isNeedForceUpdate && holder.isFolderScreen != null
            && isFoldScreenWithExpand() != holder.isFolderScreen
        ) {
            holder.isNeedForceUpdate = true
        }

        if (holder.recommendItemNew == recommendItemNew
            && (holder.recommendItemNew?.item as? RecommendRankListItem)?.innerListSelectedIndex
            == recommendRankListItem.innerListSelectedIndex
            && !holder.isNeedForceUpdate
        ) {
            return
        }

        holder.isFolderScreen = isFoldScreenWithExpand()
        holder.isNeedForceUpdate = false
        holder.recommendItemNew = recommendItemNew
        if (!recommendRankListItem.title.isNullOrEmpty()) {
            holder.tvModuleTitle.text = recommendRankListItem.title
        }

        val cardTabAdapter = TingTabAdapter(
            rankingList as MutableList<AlbumRank>,
            recommendItemNew,
            modulePosition,
            holder.viewPager
        )

        cardTabAdapter.onTabSelectListener = object : OnTabSelectListener {
            var isFromClick = false
            var mPrePosition = 0

            private fun checkTrackAlbum(curPosition: Int) {
                val albumRank = rankingList.getOrNull(curPosition)
                // 列表空  需要请求数据 在bindData中曝光
                if (albumRank?.subElements.isNullOrEmpty()) {
                    isPageChange = true
                    return
                }
                // 非空  在当前曝光
                HandlerManager.postOnUIThreadDelay({
                    traceOnAlbumItemShow(
                        holder.recommendItemNew,
                        modulePosition,
                        holder
                    )
                }, 200)
            }

            override fun onSelect(curPosition: Int) {
                checkTrackAlbum(curPosition)
                val preAlbumRank = rankingList.getOrNull(mPrePosition)
                val curAlbumRank = rankingList.getOrNull(curPosition)
                recommendRankListItem.innerListSelectedIndex = curPosition
                mPrePosition = curPosition

                val action = if (isFromClick) {
                    "click"
                } else {
                    "slipe"
                }
                isFromClick = false

                // 新首页-热门标签-切换  点击事件
                val trace = XMTraceApi.Trace()
                    .click(61859) // 用户点击时上报
                    .put("action", action) // 客户端传。区分点击还是滑动切换
                    .put("currPage", "newHomePage")
                    .put("xmRequestId", recommendItemNew.xmRequestId)
                    .put("tabName", preAlbumRank?.title ?: "") // 根据实际文案，点击前 tabName
                    .put("tagId", curAlbumRank?.id?.toString() ?: "") // 传切换后的
                    .put("modulePosition", (modulePosition + 1).toString())
                    .put("contentType", curAlbumRank?.title ?: "") // 客户端传
                    .put("contentId", curAlbumRank?.id?.toString() ?: "-1") // 客户端传
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString(),
                    curAlbumRank?.title,
                    (curPosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank?.ubtV2)
                trace.createTrace()

                if (!curAlbumRank?.landingPage.isNullOrEmpty()) {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.tvMore)
                } else {
                    ViewStatusUtil.setVisible(View.INVISIBLE, holder.tvMore)
                }

                scrollToPositionTab(holder.tabListRv, curPosition, true)
            }

            override fun clickTitle(prePosition: Int, curPosition: Int) {
                mPrePosition = prePosition
                isFromClick = true
                val trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                    .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
                SpmTraceUtil.addSpmTraceInfo(
                    trace1, recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace1, recommendRankListItem.ubtV2
                )
                trace1.createTrace()
            }
        }
        scrollToPositionTab(
            holder.tabListRv,
            recommendRankListItem.innerListSelectedIndex,
            false
        )
        holder.tabListRv.adapter = cardTabAdapter
        holder.tabListRv.layoutManager = LinearLayoutManager(
            holder.tabListRv.context, LinearLayoutManager.HORIZONTAL, false
        )
        holder.tabListRv.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnTabItemShow(recommendItemNew, modulePosition, holder)
                }
            }
        })
        val rankListViewPagerAdapter = RankViewPagerAdapter(
            fragment,
            rankingList,
            recommendItemNew,
            modulePosition,
            dataAction,
            recommendRankListItem,
            holder
        )
        holder.viewPager.adapter = rankListViewPagerAdapter
        holder.viewPager.setOnPageChangeListener(
            ViewPageChangeListener(
                recommendRankListItem,
                rankingList,
                holder
            )
        )
        holder.viewPager.post {
            holder.viewPager.requestLayout()
        }
        if (recommendRankListItem.innerListSelectedIndex < rankingList.size) {
            val curAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
            if (curAlbumRank.landingPage.isNullOrEmpty()) {
                ViewStatusUtil.setVisible(View.INVISIBLE, holder.tvMore)
            } else {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.tvMore)
            }
        }

        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            val curAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
            if (curAlbumRank.landingPage.isNullOrEmpty()) {
                return@setOnClickListener
            }
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendRankListItem.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendRankListItem.ubtV2)
            trace1.createTrace()
            // 新首页-热门标签-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(61860) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendRankListItem.ubtV2,
                (modulePosition + 1).toString(),
                tab2Title = "更多",
                tab2Position = "d01",
                tab1Title = curAlbumRank.title,
                tab1Position = "${recommendRankListItem.innerListSelectedIndex + 1}"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank.ubtV2)
            trace.createTrace()
            ScenePlayDataUtil.saveDataForRn(recommendItemNew)
            ToolUtil.clickUrlAction(fragment, curAlbumRank.landingPage, holder.tvMore)
        }

        val indexOfPage = holder.viewPager.currentItem
        if (indexOfPage != recommendRankListItem.innerListSelectedIndex) {
            holder.viewPager.setCurrentItem(recommendRankListItem.innerListSelectedIndex, false)
        }
    }

    private class ViewPageChangeListener(
        var recommendRankListItem: RecommendRankListItem?,
        var rankingList: List<AlbumRank>?,
        var holder: RankListCardViewHolder?
    ) : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
        }

        override fun onPageSelected(position: Int) {
            if (recommendRankListItem == null || rankingList == null || holder == null) {
                return
            }
            recommendRankListItem!!.innerListSelectedIndex = position
            (holder?.tabListRv?.adapter as? TingTabAdapter)?.onTabSelectListener?.onSelect(
                position
            )
            holder?.tabListRv?.adapter?.notifyDataSetChanged()
        }

        override fun onPageScrollStateChanged(state: Int) {
        }
    }

    // 滚动到指定tab并且居中
    private fun scrollToPositionTab(recyclerView: RecyclerView, position: Int, smooth: Boolean) {
        val itemView = recyclerView.findViewHolderForLayoutPosition(position)?.itemView ?: return
        val itemWidth = itemView.width
        val offset =
            (recyclerView.width - itemWidth) / 2 - itemView.marginStart - 20.dp // 7 + 6 + 14/2为斜杠到文字的距离
        if (smooth) {
            recyclerView.smoothScrollToPositionWithOffset(position, offset)
        } else {
            val manager = recyclerView.layoutManager as? LinearLayoutManager ?: return
            manager.scrollToPositionWithOffset(position, offset)
        }
    }

    private fun traceOnTabItemShow(
        recommendItemNew: RecommendItemNew,
        modulePosition: Int,
        holder: RankListCardViewHolder
    ) {
        holder.tabListRv.let {
            val childSize = holder.tabListRv.childCount
            for (i in 0 until childSize) {
                val itemView: View = holder.tabListRv.getChildAt(i) ?: continue
                if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                    val albumRank =
                        itemView.getTag(R.id.main_id_item_data) as? AlbumRank ?: continue
                    // 新首页-榜单  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62213)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage") // 客户端传
                        .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传，去重用
                        .put("contentType", "hotTag2024") // 客户端传，去重用
                        .put("contentId", albumRank.id?.toString() ?: "") // 客户端传，去重用
                        .put(
                            "modulePosition",
                            (modulePosition + 1).toString()
                        ) // 客户端传，card 在流里的位置，从 1 开始计数
                    RecommendNewUbtV2Manager.addUbtV2Data(
                        trace, (recommendItemNew.item as? RecommendRankListItem)?.ubtV2
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank.ubtV2)
                    if (recommendItemNew.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }
            }
        }
    }

    fun checkAdCunt(list: List<AlbumRank>?): Int {
        if (list.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (albumRank in list) {
            val subElements = albumRank.subElements
            if (!subElements.isNullOrEmpty()) {
                for (subElement in subElements) {
                    val adInfo = subElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfo
                    if (!adInfo.isNullOrEmpty()) {
                        adCount++
                    }
                }
            }
        }
        return adCount
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: RankListCardViewHolder?
    ) {
        if (holder == null || data == null) {
            return
        }
        val recommendRankListItem = data.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }
        if (recommendRankListItem.cardAdCount == -1) {
            recommendRankListItem.cardAdCount = checkAdCunt(rankingList)
        }
        val albumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
        val subElements = albumRank.subElements
        if (subElements.isNullOrEmpty()) {
            return
        }

        fragment.postOnUiThread {
            if (!fragment.canUpdateUi()) {
                return@postOnUiThread
            }

            holder.itemView.postDelayed({
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendRankListItem.id.toString()) // 客户端传
                        .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendRankListItem.ubtV2,
                        (position + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()

                    traceOnAlbumItemShow(data, position, holder)
                    traceOnTabItemShow(data, position, holder)
                }
            }, 200)
        }
    }

    class TingTabAdapter(
        // tab数据
        private val rankingList: MutableList<AlbumRank>,
        private val recommendItemNew: RecommendItemNew?,
        // 位置
        var positionNew: Int,
        private val viewPager: ViewPager,
    ) : RecyclerView.Adapter<TingTabAdapter.TabViewHolder>() {

        // 选中回掉
        var onTabSelectListener: OnTabSelectListener? = null

        override fun onCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): TabViewHolder {
            return TabViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.main_fra_recommend_hot_tag_2024_tab_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
            val albumRank = rankingList[position]
            holder.itemView.setTag(R.id.main_id_item_data, albumRank)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            holder.tabTv.setTextIfChanged(albumRank.title)

            if (position == 0) {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
                holder.tabTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
            } else {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                holder.tabTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }

            holder.itemView.setOnOneClickListener {
                val item = recommendItemNew?.item as? RecommendRankListItem
                val innerListSelectedIndex = item?.innerListSelectedIndex ?: -1
                if (innerListSelectedIndex == position) {
                    return@setOnOneClickListener
                }
                item?.innerListSelectedIndex = position
                onTabSelectListener?.clickTitle(innerListSelectedIndex, position)
                viewPager.currentItem = position
            }

            updateTabSelectStyle(holder, position, holder.view.context)
        }

        // 更新选中tab样式
        private fun updateTabSelectStyle(
            holder: TabViewHolder,
            position: Int,
            context: Context
        ) {
            if (viewPager.currentItem == position) {
                holder.tabLl.background =
                    ContextCompat.getDrawable(context, R.drawable.main_bg_shape_1aff4444_r14)
                holder.tabTv.setTextColor(context.resources.getColor(R.color.host_color_ff4444))
            } else {
                holder.tabLl.background =
                    ContextCompat.getDrawable(
                        context,
                        R.drawable.main_bg_tab_select_shape
                    )
                holder.tabTv.setTextColor(context.resources.getColor(R.color.main_color_662c2c3c_e68d8d91))
            }
        }

        override fun getItemCount(): Int {
            return rankingList.size
        }

        class TabViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
            var tabTv: TextView = view.findViewById(R.id.main_tv_ting_tab_title)
            var tabLl: LinearLayout = view.findViewById(R.id.main_ll_tab_container)

            init {
                resetSize()
            }

            private fun resetSize() {
            }
        }
    }

    class RankViewPagerAdapter(
        private val fragment: BaseFragment2,
        private val rankingList: List<AlbumRank>?,
        private val recommendItemNew: RecommendItemNew,
        private var parentPosition: Int,
        val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        private val recommendRankListItem: RecommendRankListItem,
        private val parentHolder: RankListCardViewHolder
    ) : PagerAdapter() {

        override fun getCount(): Int {
            return rankingList?.size ?: 0
        }

        override fun getItemPosition(albumRank: Any): Int {
            return if (rankingList?.contains(albumRank) == true) {
                rankingList.indexOf(albumRank)
            } else {
                POSITION_NONE
            }
        }

        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view === `object`
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            val view = LayoutInflater.from(container.context)
                .inflate(R.layout.main_fra_recommend_hot_2024_list_view_page_item, container, false)
            val albumRank = rankingList?.get(position)
            if (albumRank == null) {
                bindData(view, null, recommendRankListItem, position)
                return view
            }

            val rankSubElements = albumRank.subElements
            if (!rankSubElements.isNullOrEmpty()) {
                bindData(view, albumRank, recommendRankListItem, position)
            } else {
                val refId = albumRank.refId
                if (refId == null) {
                    bindData(view, null, recommendRankListItem, position)
                } else {
                    MainCommonRequest.getHotTagListItem(
                        albumRank,
                        refId,
                        albumRank.extraInfo,
                        object :
                            IDataCallBack<AlbumRank> {
                            override fun onSuccess(data: AlbumRank?) {
                                if (!fragment.canUpdateUi()) {
                                    return
                                }
                                if (data == null || data.subElements.isNullOrEmpty() || rankingList.isNullOrEmpty()) {
                                    bindData(view, null, recommendRankListItem, position)
                                    return
                                }

                                var find = false
                                for (album in rankingList) {
                                    if (data.equals(album)) {
                                        album.subElements = data.subElements
                                        bindData(view, album, recommendRankListItem, position)
                                        find = true
                                        break
                                    }
                                }

                                if (!find) {
                                    bindData(view, null, recommendRankListItem, position)
                                }
                            }

                            override fun onError(code: Int, message: String?) {
                                if (!fragment.canUpdateUi()) {
                                    return
                                }
                                bindData(view, null, recommendRankListItem, position)
                            }
                        })
                }
            }
            container.addView(view)
            return view
        }

        private fun bindData(
            view: View,
            albumRank: AlbumRank?,
            recommendRankListItem: RecommendRankListItem,
            position: Int
        ) {
            val albumListRv: RecyclerView = view.findViewById(R.id.main_rcv_ting_album_list)

            val spanCount = if (FoldableScreenCompatUtil.isBigWidthScreen()) {
                6
            } else {
                3
            }

            val screenWidth = BaseUtil.getScreenWidth(view.context)
            // 左右间距
            val contentWith = screenWidth - 20.dp
            // 每个item宽度
            val itemWidth = contentWith * 1f / spanCount

            if (FoldableScreenCompatUtil.isBigWidthScreen()) {
                albumListRv.layoutManager = NonScrollableGridLayoutManager(
                    view.context,
                    spanCount,
                    GridLayoutManager.VERTICAL,
                    false
                )
            } else {
                albumListRv.layoutManager = NonScrollableGridLayoutManager(
                    view.context,
                    spanCount,
                    GridLayoutManager.VERTICAL,
                    false
                )
            }

            var rankSubElements = albumRank?.subElements
            if ((rankSubElements?.size ?: 0) > 6) {
                rankSubElements = rankSubElements?.subList(0, 6)
            }
            val cardAlbumListAdapter = TingCardAlbumItemAdapter(
                fragment,
                rankSubElements,
                parentPosition,
                recommendItemNew,
                albumRank,
                spanCount,
                itemWidth,
                recommendRankListItem
            )
            // 听单专辑列表
            albumListRv.adapter = cardAlbumListAdapter

            if (albumRank == null) {
                return
            }

            if (!rankSubElements.isNullOrEmpty() && position == parentHolder.viewPager.currentItem && isPageChange) {
                HandlerManager.postOnUIThreadDelay({
                    isPageChange = false
                    traceOnAlbumItemShow(
                        recommendItemNew,
                        parentPosition,
                        parentHolder
                    )
                }, 200)
            }
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            if (`object` is View) {
                try {
                    container.removeView(`object`)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    class TingCardAlbumItemAdapter(
        private val fragment: BaseFragment2,
        // 专辑列表
        list: List<RankSubElement>?,
        var modulePosition: Int,
        var recommendItemNew: RecommendItemNew?,
        var albumRank: AlbumRank?,
        var spanCount: Int,
        val itemWidth: Float,
        private val recommendRankListItem: RecommendRankListItem,
    ) : RecyclerView.Adapter<TingCardAlbumItemAdapter.TingAlbumViewHolder>() {

        // 专辑列表
        private val rankSubElementList = mutableListOf<RankSubElement>()

        init {
            list?.let {
                rankSubElementList.addAll(list)
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TingAlbumViewHolder {
            return TingAlbumViewHolder(
                LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(
                    R.layout.main_fra_recommend_hot_tags_2024_album_item, parent, false
                )
            )
        }

        override fun onBindViewHolder(
            holder: TingAlbumViewHolder,
            @SuppressLint("RecyclerView") position: Int
        ) {
            val rankSubElement = rankSubElementList.getOrNull(position)

            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)

            if (rankSubElement == null) {
                holder.albumTitleTv.visibility = View.INVISIBLE
                holder.albumTitleTvBg.visibility = View.VISIBLE
                holder.albumCoverLayoutView.setAlbumCover("")
                return
            }
            holder.albumTitleTv.visibility = View.VISIBLE
            holder.albumTitleTvBg.visibility = View.GONE

            holder.itemView.setTag(R.id.main_id_item_data, rankSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)

            val isAd = rankSubElement.extraInfo?.subRefInfo?.ad ?: false
            var advertis: Advertis? = null
            if (isAd) {
                advertis = rankSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfoObject
            }
            holder.itemView.setTag(R.id.main_id_data_ad_info, advertis)

            val adTagShow =
                rankSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
            val adWidth = if (adTagShow) 24 else 0

            val showTagFilter: MutableList<ShowTag>? =
                rankSubElement.extraInfo?.showTags?.toMutableList()
            if (adTagShow && !showTagFilter.isNullOrEmpty() && showTagFilter.size > 1) {
                showTagFilter.removeAt(0)
            }
            holder.llShowTag.post {
                RecommendShowTagsUtilNew.bindTagsView(
                    holder.llShowTag,
                    showTagFilter,
                    holder.llShowTag.measuredWidth - adWidth.dp
                )
            }

            rankSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            rankSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            // item左右间距
            val cardWidth = itemWidth - 12.dp
            val coverWidth = holder.albumCoverLayoutView.calcCoverSizeFromLayoutWidth(cardWidth)
            holder.albumCoverLayoutView.updateSize(coverWidth)

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                rankSubElement.extraInfo?.other?.getBRTagUrl()
            )

            val title = rankSubElement.title ?: ""
            holder.albumTitleTv.setTextIfChanged(title)

            val tvTitleParams = holder.albumTitleTv.layoutParams as MarginLayoutParams
            tvTitleParams.marginEnd = (cardWidth - coverWidth).toInt()
            holder.albumTitleTv.layoutParams = tvTitleParams

            val itemParams = holder.itemView.layoutParams
            itemParams.width = itemWidth.toInt()
            holder.itemView.layoutParams = itemParams

            holder.albumTitleTv.post {
                val maxWidth = holder.albumTitleTv.measuredWidth
                val canShowOneLine = RecommendShowTagsUtilNew.canShowOneLine(
                    holder.albumTitleTv, title, maxWidth
                )

                if (!rankSubElement.extraInfo?.showTags2.isNullOrEmpty() && canShowOneLine) {
                    RecommendShowTagsUtilNew.bindTagsView(
                        holder.llShowTag2, rankSubElement.extraInfo?.showTags2,
                        maxWidth, "", "", RecommendShowTagsUtilNew.THEME_SHOWTAG2
                    )
                } else {
                    holder.llShowTag2.visibility = View.GONE
                }
            }

            holder.itemView.setOnOneClickListener {
                val trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                    .put(
                        "card_adTopn",
                        ((recommendItemNew!!.item as RecommendRankListItem).cardAdCount.toString())
                    )
                SpmTraceUtil.addSpmTraceInfo(
                    trace1,
                    recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace1, recommendRankListItem.ubtV2
                )
                trace1.createTrace()

                // 新首页-热门标签-专辑卡片  点击事件
                val trace = XMTraceApi.Trace()
                    .click(61851) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("positionNew", (position + 1).toString()) // 双端统一从1开始
                    .put("tabName", albumRank?.title ?: "") // 根据实际文案
                    .put("albumId", rankSubElement.refId?.toString() ?: "")
                    .put("contentType", "album")
                    .put("contentId", rankSubElement.refId?.toString() ?: "")
                    .put("xmRequestId", recommendItemNew?.xmRequestId)
                    .put("tagId", albumRank?.id?.toString() ?: "") // 传对应的社会化标签id，若无则不用传
                    .put("albumTitle", rankSubElement.title ?: "")
                    .put("modulePosition", (modulePosition + 1).toString())
                    .put("rec_src", rankSubElement.ubt?.recSrc ?: "")
                    .put("rec_track", rankSubElement.ubt?.recTrack ?: "")
                    .put("ubtTraceId", rankSubElement.ubt?.traceId ?: "")
                    .put("isAd", if (advertis != null) "true" else "false")
                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendRankListItem.ubtV2,
                    (modulePosition + 1).toString(),
                    albumRank?.title,
                    (recommendRankListItem.innerListSelectedIndex + 1).toString(),
                    rankSubElement.title,
                    (position + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank?.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement.ubtV2)
                trace.createTrace()
                if (advertis != null) {
                    // 广告点击跳转
                    AdManager.handlerAdClick(
                        BaseApplication.getMyApplicationContext(), advertis,
                        AdReportModel.newBuilder(
                            AppConstants.AD_LOG_TYPE_SITE_CLICK,
                            AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST
                        )
                            .positionNew(position + 1)
                            .modulePosition(modulePosition + 1)
                            .build()
                    )
                } else {
                    jump(fragment, rankSubElement.landingPage)
                }
            }
            holder.itemView.setOnLongClickListener {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (modulePosition + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                albumRank?.ubtV2?.let { it1 -> traceMap.putAll(it1) }
                rankSubElement.ubtV2?.let { it1 -> traceMap.putAll(it1) }
                recommendRankListItem.ubtV2?.let { it1 -> traceMap.putAll(it1) }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = rankSubElement.bizType ?: ""
                traceMap["contentId"] = rankSubElement.refId?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_ALBUM_ID] = rankSubElement.refId.toString()
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] =
                    rankSubElement.anchor?.uid.toString()
                requestMap["card_contentType"] = recommendRankListItem.contentType ?: ""
                requestMap["card_bizType"] = recommendRankListItem.bizType ?: ""
                requestMap["card_id"] = recommendRankListItem.ubtV2?.get("card_id") ?: ""

                val disLikeLeve2Build = DisLikeLeve2Build()
                disLikeLeve2Build.isFromAd = advertis != null
                disLikeLeve2Build.anchorName = rankSubElement.anchor?.nickName
                disLikeLeve2Build.requestMap = requestMap
                disLikeLeve2Build.traceMap = traceMap
                disLikeLeve2Build.onFeedBackListener = object :
                    NewXmFeedBackPopDialog.IOnFeedBackListener() {
                    override fun onDialogShow(showSuccess: Boolean) {
                    }

                    override fun onFeedBack(list: List<XmFeedInnerModel>) {
                        if (advertis != null) {
                            XmAdFeedbackUtil.recordFeedback(advertis, list)
                        }
                        MainCommonRequest.getSingleHotTagListItem(
                            position, albumRank, albumRank?.refId ?: 0, albumRank?.extraInfo,
                            object : IDataCallBack<RankSubElement> {
                                override fun onSuccess(data: RankSubElement?) {
                                    if (!fragment.canUpdateUi() || data == null) {
                                        return
                                    }
                                    if (position >= 0 && position < rankSubElementList.size) {
                                        rankSubElementList[position] = data
                                        notifyItemChanged(position)
                                        traceAlbumInner(
                                            data,
                                            albumRank,
                                            recommendRankListItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            holder.itemView,
                                            advertis
                                        )
                                    }
                                }

                                override fun onError(code: Int, message: String?) {

                                }
                            })
                    }
                }

                val build = MoreFuncBuild.createAlbumLongClickModel(
                    fragment, rankSubElement.refId ?: 0, null, true,
                    disLikeLeve2Build
                )
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", recommendItemNew?.itemType)
                    put("contentId", rankSubElement.refId?.toString() ?: "")
                    put("modulePosition", (modulePosition).toString())
                    put("positionNew", (position + 1).toString())

                    albumRank?.ubtV2?.let { it1 -> putAll(it1) }
                    rankSubElement.ubtV2?.let { it1 -> putAll(it1) }
                    recommendRankListItem.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap
                XmMoreFuncManager.checkShowMorePage(build)
                true
            }
        }

        override fun getItemCount(): Int {
            if (rankSubElementList.isEmpty()) {
                return spanCount * 2
            }
            return rankSubElementList.size
        }

        class TingAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var albumTitleTv: TextView = view.findViewById(R.id.main_tv_title)
            var albumTitleTvBg: TextView = view.findViewById(R.id.main_tv_title_bg)
            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var llShowTag: LinearLayout = view.findViewById(R.id.main_ll_show_tags)
            var llShowTag2: LinearLayout = view.findViewById(R.id.main_ll_show_tag2)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)

            // 图片封面宽度
            var coverWidth: Int = 0
        }
    }

    companion object {
        private var isPageChange = false

        private var AD_REPORT_SUB_PERCENT = ConfigureCenter.getInstance().getInt(
            CConstants.Group_ad.GROUP_NAME,
            CConstants.Group_ad.ITEM_HOME_AD_EXPOSE_PERCENT,
            50
        )

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        private fun traceOnAlbumItemShow(
            data: RecommendItemNew?,
            position: Int,
            holder: RankListCardViewHolder?
        ) {
            if (data == null || holder == null) {
                return
            }
            val recommendRankListItem = data.item
            if (recommendRankListItem !is RecommendRankListItem) {
                return
            }
            val rankingList = recommendRankListItem.list
            if (rankingList.isNullOrEmpty()) {
                return
            }

            val albumRank = rankingList.getOrNull(holder.viewPager.currentItem)
            val subElements = albumRank?.subElements
            if (subElements.isNullOrEmpty()) {
                return
            }

            holder.viewPager.getCurrentView()?.let {
                val albumListRv: RecyclerView = it.findViewById(R.id.main_rcv_ting_album_list)
                val childSize = albumListRv.childCount
                for (i in 0 until childSize) {
                    val itemView = albumListRv.getChildAt(i) ?: continue
                    if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                        val rankSubElement =
                            itemView.getTag(R.id.main_id_item_data) as? RankSubElement ?: continue
                        val index = itemView.getTag(R.id.main_id_data_index) as? Int ?: continue
                        val advertis = itemView.getTag(R.id.main_id_data_ad_info)
                        traceAlbumInner(
                            rankSubElement,
                            albumRank,
                            recommendRankListItem,
                            data,
                            index,
                            position,
                            itemView,
                            advertis
                        )
                    }
                }
            }
        }

        fun traceAlbumInner(
            rankSubElement: RankSubElement,
            albumRank: AlbumRank?,
            recommendRankListItem: RecommendRankListItem,
            data: RecommendItemNew?,
            index: Int,
            modulePosition: Int,
            itemView: View,
            advertis: Any?
        ) {
            // 新首页-热门标签-专辑卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(61852)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("positionNew", (index + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("tabName", albumRank?.title ?: "")
                .put("albumId", rankSubElement?.refId?.toString() ?: "")
                .put("contentType", "album")
                .put("contentId", rankSubElement?.refId?.toString() ?: "")
                .put("xmRequestId", data?.xmRequestId)
                .put("tagId", albumRank?.id?.toString() ?: "")
                .put("albumTitle", rankSubElement?.title ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put("rec_src", rankSubElement?.ubt?.recSrc ?: "")
                .put("rec_track", rankSubElement?.ubt?.recTrack ?: "")
                .put("ubtTraceId", rankSubElement?.ubt?.traceId ?: "")
                .put("isAd", if (advertis != null) "true" else "false")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendRankListItem.ubtV2,
                (modulePosition + 1).toString(),
                albumRank?.title,
                (recommendRankListItem.innerListSelectedIndex + 1).toString(),
                rankSubElement?.title,
                (index + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank?.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement?.ubtV2)
            if (data?.isLocalCache == true) {
                trace.isLocalCache
            }
            trace.createTrace()
            // 广告曝光上报
            if (advertis != null && advertis is Advertis && !advertis.isShowedToRecorded && !data?.isLocalCache!!) {
                AdManager.adRecord(
                    BaseApplication.getMyApplicationContext(), advertis,
                    AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SITE_SHOW,
                        AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST
                    )
                        .positionNew(index + 1)
                        .modulePosition(modulePosition + 1)
                        .build()
                )
                advertis.isShowedToRecorded = true
            }
            if (advertis != null && advertis is Advertis && !advertis.isRecordedSubPercent && !data?.isLocalCache!!
                && ViewStatusUtil.getViewVisibleAreaRealPercent(itemView) >= AD_REPORT_SUB_PERCENT
            ) {
                // 做曝光50%的上报
                AdManager.reportSubPercentShow(advertis, data.xmRequestId)
                advertis.isRecordedSubPercent = true
            }

            HomeRealTimeTraceUtils.traceItemShow(data, recommendRankListItem, albumRank, rankSubElement, itemView, index)
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 折叠屏是否展开
        private fun isFoldScreenWithExpand(): Boolean {
            val context = BaseApplication.getTopActivity()
            return BaseUtil.isCollapsibleScreenOnLandscapeMode(context) || BaseUtil.isCollapsibleScreenOnPortraitExpandMode(
                context
            )
        }
    }

    // tab切换事件
    interface OnTabSelectListener {

        fun onSelect(curPosition: Int)

        fun clickTitle(prePosition: Int, curPosition: Int)
    }
}