package com.ximalaya.ting.android.main.playpage.playy.skin

import com.ximalaya.ting.android.main.playpage.manager.PlayPageSkinDownloadManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

interface PSkinNotifier {
    /**
     * Registers a listener to be notified of skin changes.
     *
     * @param listener The listener to register.
     */
    fun registerSkinChangeListener(listener: Skin<PERSON>hangeListener)

    /**
     * Unregisters a previously registered listener.
     *
     * @param listener The listener to unregister.
     */
    fun unregisterSkinChangeListener(listener: SkinChangeListener)

    /**
     * Notifies all registered listeners of a skin change.
     *
     * @param newSkinConfig The new skin configuration.
     */
    suspend fun checkSkinNotify()
}

class PSkinNotifierImpl : PSkinNotifier {
    private val TAG = "PSkinNotifier"

    private val listeners = mutableSetOf<SkinChangeListener>()
    private var currentSkinConfig: PSkinConfig? = null

    override fun registerSkinChangeListener(listener: <PERSON><PERSON>hang<PERSON>L<PERSON><PERSON>) {
        if (listener !in listeners) {
            listeners.add(listener)
        }
    }

    override fun unregisterSkinChangeListener(listener: <PERSON><PERSON><PERSON><PERSON>List<PERSON>) {
        listeners.remove(listener)
    }

    override suspend fun checkSkinNotify() {
        val config = withContext(Dispatchers.Default) {
            PlayPageSkinDownloadManager.getPSkinConfig()
        }

        if (currentSkinConfig != config) {
            currentSkinConfig = config
            PSkinManager.currentSkinConfig = config

            withContext(Dispatchers.Main) {
                notifySkinChanged(PSkinManager.currentSkinConfig)
            }
        }
    }

    private fun notifySkinChanged(newSkinConfig: PSkinConfig?) {
        for (listener in listeners) {
            listener.onSkinChanged(newSkinConfig)
        }
    }
}