package com.ximalaya.ting.android.main.playpage.playy.ai

import android.graphics.Outline
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.*
import android.widget.ImageView
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef.get
import com.ximalaya.ting.android.host.model.play.PlayPageBusinessInfo.AgentAskAi
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.ui.DrawableUtil.GradientDrawableBuilder
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi


class PlayAiGuideDialog(
    private val agentAskAi: AgentAskAi,
    private val soundInfo: PlayingSoundInfo
) : BaseDialogFragment<PlayAiGuideDialog>() {
    companion object {
        private val KEY = "PlayAiGuideDialog"

        fun tryShow(playFragment: YPlayFragment, agentAskAi: AgentAskAi, soundInfo: PlayingSoundInfo) {
            if (!playFragment.isRealVisable) return
            val hasShow = MMKVUtil.getInstance().getBoolean(KEY, false)
            if (hasShow) return
            MMKVUtil.getInstance().saveBoolean(KEY, true)

            val playSettingGuideDialog = PlayAiGuideDialog(agentAskAi, soundInfo)
            playSettingGuideDialog.show(playFragment.childFragmentManager, "PlayAiGuideDialog")
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        configureDialogStyle()
        return inflater.inflate(R.layout.main_dialog_play_ai_guide, container, false).apply {
            setOutlineProvider(object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, 16.dpFloat)
                }
            })
            setClipToOutline(true)
        }
    }

    private fun configureDialogStyle() {
        dialog?.apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.apply {
                decorView.setPadding(0, 0, 0, 0)
                val lp: WindowManager.LayoutParams = attributes
                lp.width = WindowManager.LayoutParams.MATCH_PARENT
                lp.height = WindowManager.LayoutParams.WRAP_CONTENT
                attributes = lp
                setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation)
                setBackgroundDrawableResource(R.color.main_transparent)
                setGravity(Gravity.BOTTOM)
                setDimAmount(0.7f)
            }
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initView()


        // 问 AI 引导弹窗  弹框展示
        XMTraceApi.Trace()
            .setMetaId(68824)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("currPage", "newPlay")
            .put("currAlbumId", "${soundInfo.albumInfo?.albumId}")
            .put("currTrackId", "${soundInfo.trackInfo?.trackId}")
            .createTrace()
    }

    private fun initView() {
        view?.also {
            val colors =
                if (BaseFragmentActivity.sIsDarkMode) {
                    intArrayOf(0xff1b1b1d.toInt(), 0xff141414.toInt())
                } else {
                    intArrayOf(0xffe2e6ec.toInt(),0xfff7f9fc.toInt())
                }
            val radius = 16.dpFloat
            it.background = GradientDrawableBuilder()
                .orientation(GradientDrawable.Orientation.TOP_BOTTOM)
                .color(colors)
                .cornerRadius(radius)
                .build()

            (it.layoutParams as ViewGroup.MarginLayoutParams).apply {
                leftMargin = 16.dp
                rightMargin = 16.dp
                bottomMargin = 34.dp
            }
        }
        view?.findViewById<View>(R.id.main_iv_close)?.setOnClickListener { dismiss() }
        view?.findViewById<ImageView>(R.id.main_play_setting_guide_lottie)?.also {
            if (BaseFragmentActivity.sIsDarkMode) {
                ImageManager.from(context).displayImage(it, "https://imagev2.xmcdn.com/storages/a9d7-audiofreehighqps/6D/49/GKwRIJEL_KOuAACpswOwUjFy.png", -1)
            } else {
                ImageManager.from(context).displayImage(it, "https://imagev2.xmcdn.com/storages/a868-audiofreehighqps/9F/05/GKwRIaIL_KOzAAC5-AOwUjOB.png", -1)
            }
        }
        view?.findViewById<View>(R.id.main_play_setting_guide_use)?.setOnClickListener {
            dismiss()

            // 问 AI 引导弹窗-按钮  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(68825)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "newPlay")
                .put("currAlbumId", "${soundInfo.albumInfo?.albumId}")
                .put("currTrackId", "${soundInfo.trackInfo?.trackId}")
                .createTrace()

            val xPlayPage = get()
            if (xPlayPage is YPlayFragment) {
                xPlayPage.openUri(agentAskAi.link)
            }
        }
    }
}