package com.ximalaya.ting.android.main.playpage.playy.ai

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.updatePadding
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.transition.TransitionManager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.manager.ai.AiAgentTipManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.model.play.PlayPageBusinessInfo.AgentAskAi
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.mine.extension.removeSelf
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseComponent
import com.ximalaya.ting.android.main.playpage.playy.PlayTypeChangeListener
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.tips.v2.TipPriority
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject


class AiGuideTipsComponent(
    private val yPlayFragment: YPlayFragment
) : BaseComponent(), PlayTypeChangeListener {

    companion object {
        private val TAG = "AiGuideTipsComponent"
    }


    private var guideView: View? = null
    private var job: Job? = null
    private var dialogJob: Job? = null

    private var textView: TextView? = null
    private var nieLottieView: XmLottieAnimationView? = null

    private var agentAskAiInfo: AgentAskAi? = null
    private val liveFinishReceiver: BroadcastReceiver

    init {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent?) {
                if (intent?.action == "local_live_animation_finished") {
                    if (nieLottieView?.isShown == true) {
                        guideView?.also {
                            if (!textView?.text.isNullOrBlank()) {
                                doAnimation(it, textView)
                            }
                        }
                    }
                }
            }
        }

        liveFinishReceiver = receiver
        LocalBroadcastManager.getInstance(mContext).registerReceiver(receiver,
            IntentFilter("local_live_animation_finished")
        )
    }

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)

        yPlayFragment.addPlayTypeChangeListener(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(liveFinishReceiver)
        yPlayFragment.removePlayTypeChangeListener(this)
    }

    override fun onPlayTypeChanged(isAudio: Boolean) {
        if (!isAudio) {
            // 如果不是音频页面，清除提示
            clearTAnimation()
            return
        }
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        clearTAnimation()

        val agentInfo = soundInfo?.businessInfoMap?.agentAskAi
        if (agentInfo != null && agentInfo.isValid && yPlayFragment.isAudioPage()) {
            // 新声音播放页-问AI入口  控件曝光
            XMTraceApi.Trace()
                .setMetaId(68462)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newPlay")
                .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .put("contentType", "ai_summary") // 去重使用
                .put("contentId", curTrackId.toString()) // 去重使用
                .put("currAlbumId", curAlbumId.toString())
                .put("currTrackId", curTrackId.toString())
                .createTrace()

            showTips(agentInfo)

            dialogJob = yPlayFragment.scope().launch {
                delay(200)
                PlayAiGuideDialog.tryShow(yPlayFragment, agentInfo, soundInfo)
            }
        } else {
            guideView.removeSelf()
        }
    }

    private fun getTips(): View? {
        if (guideView != null) return guideView

        guideView = LayoutInflater.from(yPlayFragment.context)?.inflate(R.layout.main_playpage_ai_guide_tips, yPlayFragment.view as ViewGroup, false)
        guideView?.setOnClickListener {
            XMTraceApi.Trace()
                .click(68461) // 用户点击时上报
                .put("currPage", "newPlay")
                .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
//                .put("action", "click") // click 表示点击触发，gesture表示捏和手势触发
//                .put("contentType", "ai_summary") // 去重使用
//                .put("contentId", curTrackId.toString()) // 去重使用
                .put("currAlbumId", curAlbumId.toString())
                .put("currTrackId", curTrackId.toString())
                .createTrace()

            val url = if (textView?.visibility == View.VISIBLE) {
                agentAskAiInfo?.link
            } else {
                agentAskAiInfo?.noSugLink
            }
            if (!url.isNullOrEmpty()) {
                yPlayFragment.openUri(AiAgentTipManager.getPlayAgentIconDebugUri(url))
            }
        }
        return guideView?.apply {
            textView = findViewById<TextView>(R.id.main_ai_guide_txt)
            nieLottieView = findViewById<XmLottieAnimationView>(R.id.main_ai_guide_ic)
            nieLottieView?.setAnimationFromUrl(
                "https://aod.cos.tx.xmcdn.com/storages/b3e1-audiofreehighqps/B5/E3/GAqhF9kL-LcEAADbbgOuAc4t.json",
                "play_ai_lottie")
            addOnAttachStateChangeListener(object: View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
//                    doAnimation(v, textView)
                }
                override fun onViewDetachedFromWindow(v: View?) {
                    kotlin.runCatching {
                        clearTAnimation()
                    }
                }
            })
        }
    }

    private fun showTips(agentAskAi: AgentAskAi) {
        agentAskAiInfo = agentAskAi
        getTips()?.also {
            textView?.text = agentAskAi.guideText?: ""
            yPlayFragment.addAiGuideView(it)
        }
    }

    private fun clearTAnimation() {
        kotlin.runCatching {
            job?.cancel()
            dialogJob?.cancel()
            nieLottieView?.cancelAnimation()

            (guideView as? ViewGroup)?.also {
                TransitionManager.endTransitions(it)
                it.updatePadding(left = 0)
            }

            textView?.visibility = View.GONE
            yPlayFragment.dismissTips(TipPriority.AI_GUIDE.id)
        }

    }

    private fun doAnimation(view: View, textView: TextView?) {
        job?.cancel()
        nieLottieView?.playAnimation()

//        if (reachLimit()) return
//        markshow()
        job = yPlayFragment.scope().launch {
            delay(3000)
            yPlayFragment.showTips(TipPriority.AI_GUIDE.id, view)
            TransitionManager.beginDelayedTransition(view as ViewGroup)
            view.updatePadding(left = 4.dp)
            textView?.visibility = View.VISIBLE
            delay(4500)
            TransitionManager.beginDelayedTransition(view as ViewGroup)
            textView?.visibility = View.GONE
            view.updatePadding(left = 0)
            delay(500)
            nieLottieView?.cancelAnimation()
            yPlayFragment.dismissTips(TipPriority.AI_GUIDE.id)
        }
    }
}