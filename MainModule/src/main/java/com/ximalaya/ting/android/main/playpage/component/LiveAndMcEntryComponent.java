package com.ximalaya.ting.android.main.playpage.component;

import static com.ximalaya.ting.android.host.manager.handler.HandlerManager.removeCallbacks;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.graphics.PorterDuff;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;

import com.airbnb.lottie.LottieAnimationView;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.LastAudioPlayListCache;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData;
import com.ximalaya.ting.android.host.model.play.PlayPageTab;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playModule.presenter.LiveEntryConfigManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAnimationManager;
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimateBiz;
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimationManagerV2;
import com.ximalaya.ting.android.main.playpage.fragment.PlayFragmentNew;
import com.ximalaya.ting.android.main.playpage.internalservice.ILiveEntryComponentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageLiveEntryManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager;
import com.ximalaya.ting.android.main.playpage.util.PlayPageTabUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by WolfXu on 2020-06-05.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class LiveAndMcEntryComponent implements PlayPageDataManager.IOnThemeColorChangedListener {
    private static final String TAG = LiveAndMcEntryComponent.class.getSimpleName();
    private static final String DELAY_TASK_TAG = "PlayPageLiveEntryDelayTask";
    private static final long WAVE_ANIMATION_DURATION = 15000;
    private FragmentActivity mActivity;
    private BaseFragment2 mFragment;
    private boolean mHasInit;
    private PlayingSoundInfo mSoundInfo;
    private long mLastTrackId;
    private View mRootView;
    private ImageView mIvCloseReason;
    private View mLayoutReason;
    private PlayPageMinorData mPlayPageMinorData;
    //    public PlayingSoundInfo.PlayLiveInfo mPlayLiveInfo;
    private PlayPageMinorData.RightRecommendInfo mRightRecommendInfo;
    private TextView mTvLiveEntryRecReason;
    private View mVLiveEntryAvatarContainer;
    private RoundImageView mIvLiveEntryAvatar;
    private RoundImageView mIvAvatar2;
    private LottieAnimationView mLottieAnimationView;
    private Runnable mLiveEntryReasonHideTask;
    private Runnable mLiveEntryAvatarPartHideTask;
    private ILoadLiveDataCallback mLoadLiveDataCallback;
    private boolean mIsLiveEntryAnimating;
    private String mCurLottieFileName = null;
    private TextView mTvTag = null;
    private ImageView mIvTriangle;
    private LottieAnimationView mLottieLabel;
    private boolean mLastVisibleWhileTabChanged;
    private boolean mWaveAnimationIsRunningWhilePaused;
    private boolean mWaveAnimationIsRunningWhileHideByTabChanged;

    private boolean mIsTipsShow;
    private long mMcLastExposeTime;
    private long mMcExposeCount;

    private String mXmRequestId = "-1";

    public LiveAndMcEntryComponent(BaseFragment2 fragment) {
        mFragment = fragment;
        if (mFragment != null) {
            mActivity = mFragment.getActivity();
        }
    }

    private void initIfNeeded() {
        if (!mHasInit) {
            if (mFragment != null) {
                mHasInit = true;
                ViewStub viewStub = mFragment.findViewById(R.id.main_vs_live_entry);
                if (viewStub != null) {
                    mRootView = viewStub.inflate();
                    if (mRootView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                        ((ViewGroup.MarginLayoutParams) mRootView.getLayoutParams()).topMargin = BaseUtil.getStatusBarHeight(mActivity);
                    }
                    mTvLiveEntryRecReason = mRootView.findViewById(R.id.main_tv_live_rec_reason);
                    //第一次加载跑马灯无效
                    mTvLiveEntryRecReason.post(() -> {
                        mTvLiveEntryRecReason.setSelected(false);
                        mTvLiveEntryRecReason.setSelected(true);
                    });
                    mIvCloseReason = mRootView.findViewById(R.id.main_iv_close_reason);
                    mLayoutReason = mRootView.findViewById(R.id.main_g_reason);
                    mIvTriangle = mRootView.findViewById(R.id.main_iv_triangle);
                    mVLiveEntryAvatarContainer = mRootView.findViewById(R.id.main_vg_live_avatar);
                    mIvLiveEntryAvatar = mRootView.findViewById(R.id.main_iv_live_avatar);
                    mIvAvatar2 = mRootView.findViewById(R.id.main_iv_live_avatar2);
                    if (mRightRecommendInfo != null && !TextUtils.isEmpty(mRightRecommendInfo.title)) {
                        mVLiveEntryAvatarContainer.setContentDescription(mRightRecommendInfo.title);
                    }
                    mLottieAnimationView = mRootView.findViewById(R.id.main_lottie_wave);
                    mTvTag = mRootView.findViewById(R.id.main_tv_tag);
                    mLottieLabel = mRootView.findViewById(R.id.main_lottie_label);
                    setClickListener(mVLiveEntryAvatarContainer);
                    setClickListener(mTvLiveEntryRecReason);
                    mIvCloseReason.setOnClickListener(v -> {
                        if (OneClickHelper.getInstance().onClick(v)) {
                            hide();
                            LiveEntryConfigManager.getInstance().recordTipsClose();
                        }
                    });
                    AutoTraceHelper.bindDataCallback(mIvCloseReason, new AutoTraceHelper.IDataProvider() {
                        @Override
                        public Object getData() {
                            return mSoundInfo;
                        }

                        @Override
                        public Object getModule() {
                            return null;
                        }

                        @Override
                        public String getModuleType() {
                            return null;
                        }
                    });
                }
                PlayPageInternalServiceManager.getInstance().registerService(ILiveEntryComponentService.class, mLiveEntryComponentService);
                PlayPageDataManager.getInstance().addThemeColorChangeListener(this);
            }
        }
    }

    private void setClickListener(View view) {
        view.setOnClickListener(v -> {
            if (OneClickHelper.getInstance().onClick(v)) {
                handleLiveEntryClick(v == mTvLiveEntryRecReason);
            }
        });
        AutoTraceHelper.bindDataCallback(view, new AutoTraceHelper.IDataProvider() {
            @Override
            public Object getData() {
                return mSoundInfo;
            }

            @Override
            public Object getModule() {
                return null;
            }

            @Override
            public String getModuleType() {
                return null;
            }
        });
    }

    private void handleLiveEntryClick(boolean isClickTips) {
        if (mRightRecommendInfo != null) {
            // 先处理点击事件埋点，再执行直播间跳转逻辑，保证埋点数据客户端时序正确
            PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
            if (mRightRecommendInfo.isMc()) {
                // 新声音播放页-顶部mc入口  点击事件
                new XMTraceApi.Trace()
                        .click(45376)
                        .put("currPage", "newPlay")
                        .put("roomId", String.valueOf(mRightRecommendInfo.roomId))
                        .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                        .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .createTrace();
            } else {
                if(mRightRecommendInfo.roomType == 4){
                    String des = "";
                    if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) { //点击都上报description
                        des = mRightRecommendInfo.recReason;
                    }
                    PlayPageLiveEntryManager.playPageLiveEntryClick(mRightRecommendInfo,
                            mXmRequestId, String.valueOf(trackInfo != null ? trackInfo.trackId : 0),
                            String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0),
                            des,
                            ""
                    );
                    return;
                }
                long liveAnchorId = 0L;
                if (!ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos)) {
                    PlayPageMinorData.UserLogo userLogo = mRightRecommendInfo.userLogos.get(0);
                    if (userLogo != null) {
                        liveAnchorId = userLogo.uid;
                    }
                }
                XMTraceApi.Trace traceApi = new XMTraceApi.Trace()
                        .setMetaId(17492)
                        .setServiceId("click")
                        .put("currPage", "newPlay")
                        .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                        .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0))
                        .put("categoryId", String.valueOf(trackInfo != null ? trackInfo.categoryId : 0))
                        .put("anchorId", String.valueOf(liveAnchorId))
                        .put("targetType", isClickTips ? "tips" : "button")
                        .put("liveRoomType", String.valueOf(mRightRecommendInfo.bizType))
                        .put("roomId", String.valueOf(mRightRecommendInfo.roomId))
                        .put("liveId", String.valueOf(mRightRecommendInfo.recordId))
                        .put("strategy_track", TextUtils.isEmpty(mRightRecommendInfo.strategyTrack) ? "" : mRightRecommendInfo.strategyTrack)
                        .put("strategy_src", TextUtils.isEmpty(mRightRecommendInfo.strategySrc) ? "" : mRightRecommendInfo.strategySrc)
                        .put("rec_track", TextUtils.isEmpty(mRightRecommendInfo.recTrack) ? "" : mRightRecommendInfo.recTrack) // 推荐算法数据时上报
                        .put("rec_src", TextUtils.isEmpty(mRightRecommendInfo.recSrc) ? "" : mRightRecommendInfo.recSrc) // 推荐算法数据时上报
                        .put("ubtTraceId", TextUtils.isEmpty(mRightRecommendInfo.ubtTraceId) ? "" : mRightRecommendInfo.ubtTraceId) // 推荐算法数据时上报
                        .put("style", mRightRecommendInfo.getAnimationLabelStyleForStat())
                        .put("titleType", String.valueOf(mRightRecommendInfo.titleSource));

                if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) { //点击都上报description
                    traceApi.put("description", mRightRecommendInfo.recReason);
                }
                traceApi.createTrace();
            }

            LastAudioPlayListCache.INSTANCE.saveAudioPlayListCache(mActivity);
            if (!TextUtils.isEmpty(mRightRecommendInfo.landingUrl)) {
                ToolUtil.clickUrlAction(mFragment, mRightRecommendInfo.landingUrl, null);
            } else if (mRightRecommendInfo.isLive() && mRightRecommendInfo.roomId > 0) {
                PlayTools.playLiveAudioByRoomIdWithPlaySource(mActivity, mRightRecommendInfo.roomId,
                        ILivePlaySource.SOURCE_MAIN_PLAY_PAGE_LIVE_ENTRY);
            }
        }
    }

    public void onFragmentResume() {
        if (canUpdateUi()) {
            if (mRootView != null && mRootView.getVisibility() == View.VISIBLE) {
                // if (mWaveAnimationIsRunningWhilePaused) {
                //     mWaveAnimationIsRunningWhilePaused = false;
                //     toggleWave(true);
                //     postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, WAVE_ANIMATION_DURATION);
                // }
                if (mSoundInfo != null) {
                    if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                        // 新声音播放页-顶部mc入口  控件曝光
                        traceMcViewed(mSoundInfo.trackInfo);
                    } else {
                        // trackLiveShow(mSoundInfo.trackInfo, mRightRecommendInfo);
                    }
                }
            }
        }
    }

    public void onFragmentPause() {
        if (canUpdateUi()) {
            cancelLiveEntryAllAnimation();
            if (mLottieAnimationView != null && mLottieAnimationView.isAnimating()) {
                mWaveAnimationIsRunningWhilePaused = true;
            }
            toggleWave(false);
            if (mLayoutReason != null) {
                mLayoutReason.setVisibility(View.GONE);
            }
        }
    }

    public void onFragmentDestroy() {
        PlayPageInternalServiceManager.getInstance().unRegisterService(ILiveEntryComponentService.class);
        PlayPageDataManager.getInstance().removeThemeColorChangeListener(this);
    }

    public void showLive(PlayingSoundInfo soundInfo, ILoadLiveDataCallback loadLiveDataCallback) {
        mSoundInfo = soundInfo;
        mLoadLiveDataCallback = loadLiveDataCallback;
        if (!ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME,
                CConstants.Group_toc.ITEM_PLAYPAGE_LIVE_SWITCH, false)
                || soundInfo == null || soundInfo.trackInfo == null) {
            dealLiveData(false);
            return;
        }
        long trackId = soundInfo.trackInfo.trackId;
        if (trackId != 0 && trackId == mLastTrackId) {
            if (mRootView == null || mRootView.getVisibility() != View.VISIBLE) {
                dealLiveData(false);
            }
            return;
        }
        if (trackId < 0) {
            dealLiveData(false);
            return;
        }
        mLastTrackId = trackId;
        if (mPlayPageMinorData == null || trackId != mPlayPageMinorData.trackId) {
            loadLiveData(trackId);
        } else {
            if (PlayPageTabUtil.isNewAudioPage(soundInfo)) {
                showV2();
            } else {
                show();
            }
        }
    }

    private void dealLiveData(boolean showLive) {
        if (mLoadLiveDataCallback != null) {
            mLoadLiveDataCallback.showLiveEntry(showLive);
        }
        if (!showLive) {
            AudioPlayPageAnimationManager.INSTANCE.noNeedShow(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
        }
    }

    public static boolean needShowLiveAndMc(PlayPageMinorData.RightRecommendInfo mRightRecommendInfo) {
        return mRightRecommendInfo != null
                && LiveEntryConfigManager.getInstance().shouldLiveEntry()
                && !TextUtils.isEmpty(mRightRecommendInfo.landingUrl)
                && !ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos);
    }

    public void loadLiveData(long trackId) {
        PlayPageMinorDataManager.getInstance().getData(trackId, new IDataCallBack<PlayPageMinorData>() {
            @Override
            public void onSuccess(@Nullable PlayPageMinorData object) {
                mXmRequestId = XmRequestIdManager.getInstance(MainApplication.getMyApplicationContext()).getRequestId();
                mPlayPageMinorData = object;
                if (object != null) {
                    mRightRecommendInfo = object.rightRecommendInfo;
                    if (mRightRecommendInfo == null) {
                        dealLiveData(false);
                        return;
                    }
                    if (needShowLiveAndMc(mRightRecommendInfo)) {
                        if (PlayPageTabUtil.isNewAudioPage(mSoundInfo)) {
                            showV2();
                        } else {
                            show();
                        }
                    } else {
                        dealLiveData(false);
                    }
                } else {
                    dealLiveData(false);
                }
            }

            @Override
            public void onError(int code, String message) {
                dealLiveData(false);
            }
        });
    }

    private void showV2() {
        if (mSoundInfo == null || mRightRecommendInfo == null || !canUpdateUi()) {
            return;
        }
        if (mFragment instanceof PlayFragmentNew && !((PlayFragmentNew) mFragment).isCurrentTabOfType(PlayPageTab.TYPE_SOUND)) {
            dealLiveData(false);
            return;
        }
        mLastVisibleWhileTabChanged = false;

        initIfNeeded();
        boolean shouldShowDynamicPart = LiveEntryConfigManager.getInstance().shouldLiveEntry();
        cancelLiveEntryAllAnimation();

        if (shouldShowDynamicPart) {
            // 兼容自动切换切换分享展示 "分享" 文字，避免挤压
            if (ShareComponent.mUseNewSwitchShare && mFragment instanceof PlayFragmentNew &&
                    mRootView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                int tabSize = ((PlayFragmentNew) mFragment).getTabSize();
                if (tabSize > 0 && tabSize < 3) {
                    ((ViewGroup.MarginLayoutParams) mRootView.getLayoutParams()).rightMargin = BaseUtil.dp2px(mActivity, 22);
                } else {
                    ((ViewGroup.MarginLayoutParams) mRootView.getLayoutParams()).rightMargin = 0;
                }
            }
            dealLiveData(true);
            if (mRootView != null && mRootView.getVisibility() != View.VISIBLE) {
                mRootView.setVisibility(View.VISIBLE);
            }
            if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                // mc没有推荐理由时，用title来显示
                if (TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
                    mRightRecommendInfo.recReason = mRightRecommendInfo.title;
                }
            }
            PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
            mLiveEntryAvatarPartHideTask = () -> {
                if (canUpdateUi()) {
                    toggleWave(false);
                }
            };
            if (mVLiveEntryAvatarContainer != null) {
                mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
                mVLiveEntryAvatarContainer.setAlpha(1);
            }
            LiveEntryConfigManager.getInstance().recordDynamicPartShow();
            if (mTvLiveEntryRecReason != null) {
                mTvLiveEntryRecReason.setText(mRightRecommendInfo.recReason);
            }

            mTvTag.setText(mRightRecommendInfo.isMc() ? "热聊中" : "直播");
            mTvTag.setBackgroundResource(mRightRecommendInfo.isMc() ? R.drawable.main_bg_rect_8f88e7_corner_100
                    : R.drawable.main_bg_rect_ff4477_radius_100);

            if (!ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos)) {
                PlayPageMinorData.UserLogo userLogo = mRightRecommendInfo.userLogos.get(0);
                if (userLogo != null) {
                    int avatarSize = (mRightRecommendInfo.userLogos.size() >= 2) ? BaseUtil.dp2px(mActivity, 20)
                            : BaseUtil.dp2px(mActivity, 24);
                    ViewGroup.LayoutParams layoutParams = mIvLiveEntryAvatar.getLayoutParams();
                    if (layoutParams != null && layoutParams.width != avatarSize || layoutParams.height != avatarSize) {
                        layoutParams.width = avatarSize;
                        layoutParams.height = avatarSize;
                        mIvLiveEntryAvatar.setLayoutParams(layoutParams);
                    }
                    ImageManager.from(mActivity).displayImage(mIvLiveEntryAvatar, userLogo.logo
                            , com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default);
                    if (mRightRecommendInfo.isMc()) {
                        mIvLiveEntryAvatar.setBorderWidth(1);
                    } else {
                        mIvLiveEntryAvatar.setBorderWidth(0);
                    }
                }
                int avatar2Visibility = View.GONE;
                if (mRightRecommendInfo.userLogos.size() >= 2) {
                    PlayPageMinorData.UserLogo userLogo2 = mRightRecommendInfo.userLogos.get(1);
                    if (userLogo2 != null) {
                        ImageManager.from(mActivity).displayImage(mIvAvatar2, userLogo2.logo
                                , com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default);
                        avatar2Visibility = View.VISIBLE;
                        if (mRightRecommendInfo.isMc()) {
                            mIvAvatar2.setBorderWidth(1);
                        } else {
                            mIvAvatar2.setBorderWidth(0);
                        }
                    }
                }
                mIvAvatar2.setVisibility(avatar2Visibility);
            }

            AnimationManagerV2.start(AnimateBiz.LIVE_PUBLIC, () -> {
                if (mRightRecommendInfo.hasAnimationLabel() && mLottieLabel != null) {
                    mTvTag.setVisibility(View.VISIBLE);
                    mTvTag.setAlpha(1);
                    // 动画流程：
                    // 直播头像（带标签）到福袋/红包出现 3s ，3s时直播头像开始消失 红包开始出现 3.5s完全展示，4s红包/福袋开始运动。
                    // 存在到7.5s时开始消失 出现头像和气泡 8s时完全出现 后续存在的动画及时长与现在线上的保持一直就可以
                    HandlerManager.postOnUiThreadDelayed(DELAY_TASK_TAG, () -> {
                        showOrHideViewAnimation(mVLiveEntryAvatarContainer, false, new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationEnd(Animator animation) {
                                super.onAnimationEnd(animation);
                                toggleWave(false);
                                mLottieAnimationView.setVisibility(View.INVISIBLE);
                            }
                        });
                        showOrHideViewAnimation(mLottieLabel, true);
                        mLottieLabel.setVisibility(View.VISIBLE);
                        startLabelAnimation();
                    }, 3000);
                } else {
                    postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, WAVE_ANIMATION_DURATION);
                }
                showTips();
                if (mLottieAnimationView != null) {
                    String lottieFileName = mRightRecommendInfo.isMc() ? "lottie/play_cover_living_lottie/myclub_wave.json"
                            : "lottie/main_audio_play_top_right_live_wave.json";
                    if (!lottieFileName.equals(mCurLottieFileName)) {
                        mCurLottieFileName = lottieFileName;
                        mLottieAnimationView.setAnimation(lottieFileName);
                    }
                    mLottieAnimationView.playAnimation();
                    mLottieAnimationView.setVisibility(View.VISIBLE);
                }

                showOrHideViewAnimation(mLottieAnimationView, true);
            });

            if (mRightRecommendInfo.isMc()) {
                // 新声音播放页-顶部mc入口  控件曝光
                traceMcViewed(trackInfo);
            } else {
                postOnUiThreadDelayed(new Runnable() {
                    @Override
                    public void run() {
                        trackLiveShow(trackInfo, mRightRecommendInfo);
                    }
                }, 1000L);
            }

        } else {
            if (mRootView != null) {
                mRootView.setVisibility(View.INVISIBLE);
            }
            dealLiveData(false);
        }
    }

    private void show() {
        if (mSoundInfo == null || mRightRecommendInfo == null || !canUpdateUi()) {
            return;
        }
        if (mFragment instanceof PlayFragmentNew && !((PlayFragmentNew) mFragment).isCurrentTabOfType(PlayPageTab.TYPE_SOUND)) {
            dealLiveData(false);
            return;
        }
        mLastVisibleWhileTabChanged = false;

        initIfNeeded();
        boolean shouldShowDynamicPart = LiveEntryConfigManager.getInstance().shouldLiveEntry();
        cancelLiveEntryAllAnimation();

        if (shouldShowDynamicPart) {
            // 兼容自动切换切换分享展示 "分享" 文字，避免挤压
            if (ShareComponent.mUseNewSwitchShare && mFragment instanceof PlayFragmentNew &&
                    mRootView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                int tabSize = ((PlayFragmentNew) mFragment).getTabSize();
                if (tabSize > 0 && tabSize < 3) {
                    ((ViewGroup.MarginLayoutParams) mRootView.getLayoutParams()).rightMargin = BaseUtil.dp2px(mActivity, 22);
                } else {
                    ((ViewGroup.MarginLayoutParams) mRootView.getLayoutParams()).rightMargin = 0;
                }
            }
            dealLiveData(true);
            AudioPlayPageAnimationManager.INSTANCE.readyToStart(AudioPlayPageAnimationManager.EAnimation.LIVE_MC, () -> {
                if (mRootView != null && mRootView.getVisibility() != View.VISIBLE) {
                    mRootView.setVisibility(View.VISIBLE);
                }
                if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                    // mc没有推荐理由时，用title来显示
                    if (TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
                        mRightRecommendInfo.recReason = mRightRecommendInfo.title;
                    }
                }
                PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
                mLiveEntryAvatarPartHideTask = () -> {
                    if (canUpdateUi()) {
                        toggleWave(false);
                        AudioPlayPageAnimationManager.INSTANCE.end(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
                    }
                };
                if (mRightRecommendInfo != null && mRightRecommendInfo.hasAnimationLabel() && mLottieLabel != null) {
                    mTvTag.setVisibility(View.VISIBLE);
                    mTvTag.setAlpha(1);
                    // 动画流程：
                    // 直播头像（带标签）到福袋/红包出现 3s ，3s时直播头像开始消失 红包开始出现 3.5s完全展示，4s红包/福袋开始运动。
                    // 存在到7.5s时开始消失 出现头像和气泡 8s时完全出现 后续存在的动画及时长与现在线上的保持一直就可以
                    HandlerManager.postOnUiThreadDelayed(DELAY_TASK_TAG, () -> {
                        showOrHideViewAnimation(mVLiveEntryAvatarContainer, false, new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationEnd(Animator animation) {
                                super.onAnimationEnd(animation);
                                toggleWave(false);
                                mLottieAnimationView.setVisibility(View.INVISIBLE);
                            }
                        });
                        showOrHideViewAnimation(mLottieLabel, true);
                        mLottieLabel.setVisibility(View.VISIBLE);
                        startLabelAnimation();
                    }, 3000);
                } else {
                    postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, WAVE_ANIMATION_DURATION);
                }
                showTips();
                if (mVLiveEntryAvatarContainer != null) {
                    mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
                    mVLiveEntryAvatarContainer.setAlpha(1);
                }
                LiveEntryConfigManager.getInstance().recordDynamicPartShow();
                if (mTvLiveEntryRecReason != null && mRightRecommendInfo != null) {
                    mTvLiveEntryRecReason.setText(mRightRecommendInfo.recReason);
                }

                if (mRightRecommendInfo != null) {
                    mTvTag.setText(mRightRecommendInfo.isMc() ? "热聊中" : "直播");
                    mTvTag.setBackgroundResource(mRightRecommendInfo.isMc() ? R.drawable.main_bg_rect_8f88e7_corner_100
                            : R.drawable.main_bg_rect_ff4477_radius_100);

                }

                if (mRightRecommendInfo != null && !ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos)) {
                    PlayPageMinorData.UserLogo userLogo = mRightRecommendInfo.userLogos.get(0);
                    if (userLogo != null) {
                        int avatarSize = (mRightRecommendInfo.userLogos.size() >= 2) ? BaseUtil.dp2px(mActivity, 20)
                                : BaseUtil.dp2px(mActivity, 24);
                        ViewGroup.LayoutParams layoutParams = mIvLiveEntryAvatar.getLayoutParams();
                        if (layoutParams != null && layoutParams.width != avatarSize || layoutParams.height != avatarSize) {
                            layoutParams.width = avatarSize;
                            layoutParams.height = avatarSize;
                            mIvLiveEntryAvatar.setLayoutParams(layoutParams);
                        }
                        ImageManager.from(mActivity).displayImage(mIvLiveEntryAvatar, userLogo.logo
                                , com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default);
                        if (mRightRecommendInfo.isMc()) {
                            mIvLiveEntryAvatar.setBorderWidth(1);
                        } else {
                            mIvLiveEntryAvatar.setBorderWidth(0);
                        }
                    }
                    int avatar2Visibility = View.GONE;
                    if (mRightRecommendInfo.userLogos.size() >= 2) {
                        PlayPageMinorData.UserLogo userLogo2 = mRightRecommendInfo.userLogos.get(1);
                        if (userLogo2 != null) {
                            ImageManager.from(mActivity).displayImage(mIvAvatar2, userLogo2.logo
                                    , com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default);
                            avatar2Visibility = View.VISIBLE;
                            if (mRightRecommendInfo.isMc()) {
                                mIvAvatar2.setBorderWidth(1);
                            } else {
                                mIvAvatar2.setBorderWidth(0);
                            }
                        }
                    }
                    mIvAvatar2.setVisibility(avatar2Visibility);
                }

                if (mLottieAnimationView != null && mRightRecommendInfo != null) {
                    String lottieFileName = mRightRecommendInfo.isMc() ? "lottie/play_cover_living_lottie/myclub_wave.json"
                            : "lottie/main_audio_play_top_right_live_wave.json";
                    if (!lottieFileName.equals(mCurLottieFileName)) {
                        mCurLottieFileName = lottieFileName;
                        mLottieAnimationView.setAnimation(lottieFileName);
                    }
                    mLottieAnimationView.playAnimation();
                    mLottieAnimationView.setVisibility(View.VISIBLE);
                }

                showOrHideViewAnimation(mLottieAnimationView, true);

                if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                    // 新声音播放页-顶部mc入口  控件曝光
                    traceMcViewed(trackInfo);
                } else {
                    postOnUiThreadDelayed(new Runnable() {
                        @Override
                        public void run() {
                            trackLiveShow(trackInfo, mRightRecommendInfo);
                        }
                    }, 1000L);
                }
            });
        } else {
            if (mRootView != null) {
                mRootView.setVisibility(View.INVISIBLE);
            }
            dealLiveData(false);
        }
    }

    private void trackLiveShow(PlayingSoundInfo.TrackInfo trackInfo, PlayPageMinorData.RightRecommendInfo rightRecommendInfo) {
        if (rightRecommendInfo == null) return;
        if(rightRecommendInfo.roomType == 4){
            String des = "";
            if (mTvLiveEntryRecReason != null
                    && mTvLiveEntryRecReason.getVisibility() == View.VISIBLE
                    && mTvLiveEntryRecReason.getAlpha() > 0.5f
                    && !TextUtils.isEmpty(rightRecommendInfo.recReason)) {
                des =  rightRecommendInfo.recReason;
            }
            PlayPageLiveEntryManager.playPageLiveEntryShow(rightRecommendInfo,
                    mXmRequestId,
                    String.valueOf(trackInfo != null ? trackInfo.trackId : 0),
                    String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0),
                    des,
                    ""
            );
            return;
        }

        long liveAnchorId = 0L;
        if (!ToolUtil.isEmptyCollects(rightRecommendInfo.userLogos)) {
            PlayPageMinorData.UserLogo userLogo = rightRecommendInfo.userLogos.get(0);
            if (userLogo != null) {
                liveAnchorId = userLogo.uid;
            }
        }
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(17493)
                .setServiceId("slipPage")
                .put("exploreType", "1")
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0))
                .put("categoryId", String.valueOf(trackInfo != null ? trackInfo.categoryId : 0))
                .put("anchorId", String.valueOf(liveAnchorId))
                .put("liveRoomType", String.valueOf(rightRecommendInfo.bizType))
                .put("roomId", String.valueOf(rightRecommendInfo.roomId))
                .put("liveId", String.valueOf(rightRecommendInfo.recordId))
                .put("strategy_track", TextUtils.isEmpty(rightRecommendInfo.strategyTrack) ? "" : rightRecommendInfo.strategyTrack)
                .put("strategy_src", TextUtils.isEmpty(rightRecommendInfo.strategySrc) ? "" : rightRecommendInfo.strategySrc)
                .put("rec_track", TextUtils.isEmpty(rightRecommendInfo.recTrack) ? "" : rightRecommendInfo.recTrack) // 推荐算法数据时上报
                .put("rec_src", TextUtils.isEmpty(rightRecommendInfo.recSrc) ? "" : rightRecommendInfo.recSrc) // 推荐算法数据时上报
                .put("ubtTraceId", TextUtils.isEmpty(rightRecommendInfo.ubtTraceId) ? "" : rightRecommendInfo.ubtTraceId) // 推荐算法数据时上报
                .put(XmRequestIdManager.CONT_ID, String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_TYPE, "newPlayLive")
                .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
                .put("style", rightRecommendInfo.getAnimationLabelStyleForStat())
                .put("titleType", String.valueOf(rightRecommendInfo.titleSource));

        //                    .put("liveCategoryId", mRightRecommendInfo.subBizType)
        if (mTvLiveEntryRecReason != null
                && mTvLiveEntryRecReason.getVisibility() == View.VISIBLE
                && mTvLiveEntryRecReason.getAlpha() > 0.5f
                && !TextUtils.isEmpty(rightRecommendInfo.recReason)) {
            trace.put("description", rightRecommendInfo.recReason);
        }
        trace.createTrace();
    }

    private void showTips() {
        mIsTipsShow = false;
        if (mLoadLiveDataCallback != null && !mLoadLiveDataCallback.shouldShowTips()) {
            return;
        }
        PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
        long trackId = (trackInfo != null) ? trackInfo.trackId : 0;
        if (LiveEntryConfigManager.getInstance().shouldShowTips(trackId)) {
            Logger.i(TAG, "show tips");
            if (mRightRecommendInfo == null) {
                return;
            }
            if (trackInfo != null) {
                LiveEntryConfigManager.getInstance().recordTipsShow(trackInfo.trackId);
            }
            if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
                if (mLayoutReason != null) {
                    mIsTipsShow = true;
                    setBackGroundColor();
                    mLayoutReason.setVisibility(View.VISIBLE);
                }
            }
            mTvLiveEntryRecReason.post(() -> {
                ViewGroup.LayoutParams layoutParams = mTvLiveEntryRecReason.getLayoutParams();
                if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                    if (mTvLiveEntryRecReason.getWidth() > BaseUtil.dp2px(mActivity, 120)) {
                        ((ConstraintLayout.LayoutParams) layoutParams).startToStart = ConstraintLayout.LayoutParams.UNSET;
                        ((ConstraintLayout.LayoutParams) layoutParams).endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                    } else {
                        ((ConstraintLayout.LayoutParams) layoutParams).startToStart = mVLiveEntryAvatarContainer.getId();
                        ((ConstraintLayout.LayoutParams) layoutParams).endToEnd = mVLiveEntryAvatarContainer.getId();
                    }
                }
                mTvLiveEntryRecReason.setLayoutParams(layoutParams);
            });
            if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
                showOrHideViewAnimation(mTvLiveEntryRecReason, true);
                showOrHideViewAnimation(mIvCloseReason, true);
                showOrHideViewAnimation(mIvTriangle, true);
            }
            showOrHideViewAnimation(mTvTag, true);
            mTvTag.setVisibility(View.VISIBLE);
            mLiveEntryReasonHideTask = () -> {
                AudioPlayPageAnimationManager.INSTANCE.end(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
                if (canUpdateUi() && mRightRecommendInfo != null) {
                    if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
                        showOrHideViewAnimation(mTvLiveEntryRecReason, false);
                        showOrHideViewAnimation(mIvCloseReason, false);
                        showOrHideViewAnimation(mIvTriangle, false);
                    }
                    // if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                    showOrHideViewAnimation(mTvTag, false);
                    // }
                }
            };
            postOnUiThreadDelayed(mLiveEntryReasonHideTask, LiveEntryConfigManager.getInstance().getTipsShowDuration());
        } else {
            Logger.i(TAG, "no need show tips");
            // if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
            mTvTag.setVisibility(View.INVISIBLE);
            // }
            mLayoutReason.setVisibility(View.GONE);
            AudioPlayPageAnimationManager.INSTANCE.end(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
        }
    }

    private void startLabelAnimation() {
        if (mRightRecommendInfo == null || !mRightRecommendInfo.isLive() || mLottieLabel == null) {
            return;
        }
        String lottiePath = null;
        switch (mRightRecommendInfo.showLabelType) {
            case PlayPageMinorData.RightRecommendInfo.SHOW_RED_PACKET:
                lottiePath = "lottie/play_page_live/red_packet.json";
                break;
            case PlayPageMinorData.RightRecommendInfo.SHOW_LUCKY_BG:
                lottiePath = "lottie/play_page_live/lucky_bag.json";
                break;
            default:
                break;
        }
        if (TextUtils.isEmpty(lottiePath)) {
            return;
        }
        mLottieLabel.setAnimation(lottiePath);
        mLottieLabel.playAnimation();
        HandlerManager.postOnUiThreadDelayed(DELAY_TASK_TAG, () -> {
            mLottieLabel.cancelAnimation();
            showOrHideViewAnimation(mLottieLabel, false, new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    mLottieLabel.setVisibility(View.INVISIBLE);
                }
            });
            showOrHideViewAnimation(mVLiveEntryAvatarContainer, true);
            mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
            mLottieAnimationView.setVisibility(View.VISIBLE);
            toggleWave(true);
            postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, 12000);
        }, 4500);
    }

    private void hideLabelAnimation() {
        if (mLottieLabel != null && mLottieLabel.getVisibility() == View.VISIBLE) {
            mLottieLabel.cancelAnimation();
            mLottieLabel.setVisibility(View.INVISIBLE);
            if (mVLiveEntryAvatarContainer != null) {
                mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
                mVLiveEntryAvatarContainer.setAlpha(1);
            }
        }
    }

    private void traceMcViewed(PlayingSoundInfo.TrackInfo trackInfo) {
        if (mRightRecommendInfo == null || !mRightRecommendInfo.isMc()) {
            return;
        }
        new XMTraceApi.Trace()
                .setMetaId(45377)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("roomId", String.valueOf(mRightRecommendInfo.roomId))
                .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_ID, String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_TYPE, "newPlayMc")
                .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
                .createTrace();
        Logger.i(TAG, "traceMcViewed 45377 trackId: " + (trackInfo != null ? trackInfo.trackId : 0) + " roomId: " + mRightRecommendInfo.roomId);
        reportIfTraceTooFrequently();
    }

    private void reportIfTraceTooFrequently() {
        // 如果曝光埋点过于频繁，上报一下
        long curTime = System.currentTimeMillis();
        if (curTime - mMcLastExposeTime < 300 * 1000) {
            mMcExposeCount++;
        } else {
            mMcExposeCount = 0;
        }
        mMcLastExposeTime = curTime;
        if (mMcExposeCount > 50 && mSoundInfo != null && mSoundInfo.trackInfo != null) {
            String stackTrace = Log.getStackTraceString(new Throwable());
//            XDCSCollectUtil.statErrorToXDCS("playpage", "mc expose too frequently " + mSoundInfo.trackInfo.trackId + " " + stackTrace);
            Logger.i(TAG, "mc expose too frequently " + mSoundInfo.trackInfo.trackId + " " + stackTrace);
        }
    }

    public void hide() {
        if (mRootView != null && mRootView.getVisibility() == View.VISIBLE) {
            mRootView.setVisibility(View.INVISIBLE);
            cancelLiveEntryAllAnimation();
        }
    }

    public void setHideByTagChange() {
        if (mRootView != null && mRootView.getVisibility() == View.VISIBLE) {
            mLastVisibleWhileTabChanged = true;
            if (mLottieAnimationView != null && mLottieAnimationView.isAnimating()) {
                mWaveAnimationIsRunningWhileHideByTabChanged = true;
            }
        }
    }

    public void resumeShowIfNeeded() {
        if (mLastVisibleWhileTabChanged && mRootView != null && mRootView.getVisibility() != View.VISIBLE) {
            mLastVisibleWhileTabChanged = false;
            if (mLastTrackId == PlayPageDataManager.getInstance().getCurTrackId()) {
                mRootView.setVisibility(View.VISIBLE);
                if (mSoundInfo != null) {
                    traceMcViewed(mSoundInfo.trackInfo);
                }
                // if (mWaveAnimationIsRunningWhileHideByTabChanged) {
                //     toggleWave(true);
                //     postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, WAVE_ANIMATION_DURATION);
                // }
            }
        }
    }

    public void clearData() {
        mLastTrackId = 0;
        mPlayPageMinorData = null;
        mIsLiveEntryAnimating = false;
    }

    private void cancelLiveEntryAllAnimation() {
        if (mLiveEntryReasonHideTask != null) {
            removeCallbacks(mLiveEntryReasonHideTask);
            mLiveEntryReasonHideTask = null;
            if (mLayoutReason != null) {
                mLayoutReason.setVisibility(View.GONE);
            }
            if (mTvTag != null) {// && mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                mTvTag.setVisibility(View.INVISIBLE);
            }
        }
        if (mLiveEntryAvatarPartHideTask != null) {
            removeCallbacks(mLiveEntryAvatarPartHideTask);
            mLiveEntryAvatarPartHideTask.run();
            mLiveEntryAvatarPartHideTask = null;
            hideLabelAnimation();
            AudioPlayPageAnimationManager.INSTANCE.end(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
        }
        HandlerManager.onTagDestroy(DELAY_TASK_TAG);
    }

    private void toggleWave(boolean isStart) {
        if (mLottieAnimationView != null) {
            if (isStart) {
                mLottieAnimationView.setVisibility(View.VISIBLE);
                mLottieAnimationView.playAnimation();
            } else {
                mLottieAnimationView.cancelAnimation();
                mLottieAnimationView.setVisibility(View.INVISIBLE);
            }
        }
        mIsLiveEntryAnimating = isStart;
    }

    private void showOrHideViewAnimation(View view, boolean isShow) {
        showOrHideViewAnimation(view, isShow, null);
    }

    private void showOrHideViewAnimation(View view, boolean isShow, @Nullable Animator.AnimatorListener listener) {
        float startFraction = isShow ? 0 : 1;
        float endFraction = isShow ? 1 : 0;
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(view, "alpha", startFraction, endFraction);
        objectAnimator.setDuration(500);
        if (listener != null) {
            objectAnimator.addListener(listener);
        }
        objectAnimator.start();
        mIsLiveEntryAnimating = isShow;
    }

    public void release() {
        mFragment = null;
    }

    private void postOnUiThreadDelayed(Runnable runnable, long delay) {
        if (mFragment != null) {
            mFragment.postOnUiThreadDelayed(runnable, delay);
        }
    }

    private boolean canUpdateUi() {
        return mFragment != null && mFragment.canUpdateUi();
    }

    @Override
    public void onThemeColorChanged(int foregroundColor, int backgroundColor) {
        setBackGroundColor();
    }

    public interface ILoadLiveDataCallback {
        void showLiveEntry(boolean show);

        boolean shouldShowTips();
    }

    private ILiveEntryComponentService mLiveEntryComponentService = new ILiveEntryComponentService() {
        @Override
        public void onLiveEntryAnimate(boolean isAnimate) {

        }

        @Override
        public boolean isLiveEntryAnimating() {
            return mIsLiveEntryAnimating;
        }
    };

    private void setBackGroundColor() {
        if (!canUpdateUi() || !mIsTipsShow) {
            return;
        }

        int backgroundColor = PlayPageDataManager.getInstance().getBackgroundColor();
        int newColor = ColorUtil.covertColorToFixedSaturationAndLightnessWithAlpha(
                backgroundColor, 0xfa, 0.35f, 0.26f);
        if (mTvLiveEntryRecReason != null && mTvLiveEntryRecReason.getBackground() != null) {
            mTvLiveEntryRecReason.getBackground().mutate().setColorFilter(newColor, PorterDuff.Mode.SRC_IN);
        }
        if (mIvTriangle != null && mIvTriangle.getDrawable() != null) {
            mIvTriangle.getDrawable().mutate().setColorFilter(newColor, PorterDuff.Mode.SRC_IN);
        }
    }
}
