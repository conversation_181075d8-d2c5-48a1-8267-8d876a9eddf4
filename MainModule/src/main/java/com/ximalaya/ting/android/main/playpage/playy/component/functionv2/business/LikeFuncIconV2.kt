package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.view.HapticFeedbackConstants
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.play.data.PlayPageDynamicState
import com.ximalaya.ting.android.host.manager.AudioInfoTraceUtil
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.appcomment.AppCommentManager
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.RouteServiceUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.function.view.YAnimatedCountTextView
import com.ximalaya.ting.android.main.playpage.playy.component.rate.EvaluateAlbumManager
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.util.PlayPageInteractiveTraceUtil
import com.ximalaya.ting.android.main.playpage.view.updateProgress
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager


class LikeFuncIconV2(private val fragment2: BaseFragment2?) : IBusinessView(),
    View.OnClickListener {
    private var curSoundInfo: PlayingSoundInfo? = null
    private val iconSize = 26.dp

    private var mLikeCountTv: YAnimatedCountTextView? = null
    private var mLottieLike: LottieAnimationView? = null
    private var isLikeBtnClick = false

    private var mLikeCount: Long = 0

    override fun provideIcon(context: Context, soundInfo: PlayingSoundInfo): View {
        return mLottieLike ?: XmLottieAnimationView(context).apply {
            layoutParams = ViewGroup.MarginLayoutParams(iconSize, iconSize)
//            imageAlpha = (255 * 0.55).toInt()
            contentDescription = "点赞"
            setOnClickListener(this@LikeFuncIconV2)
            imageAlpha = (255 * 0.55).toInt()
            val json = "lottie/host_lottie_for_like_action_y.json"
            setAnimation(json)
            mLottieLike = this

            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                setForegroundColor(PSkinManager.getBtnThemeColor())
            }
        }
    }

    override fun providerBadgeView(context: Context, soundInfo: PlayingSoundInfo): BadgeView {
        return BadgeView(mLikeCountTv ?: YAnimatedCountTextView(context).apply {
            layoutParams = ViewGroup.MarginLayoutParams(
                ViewGroup.MarginLayoutParams.WRAP_CONTENT,
                ViewGroup.MarginLayoutParams.WRAP_CONTENT
            )
            isSingleLine = true
            textSize = 8f
            alpha = 0.55f
            includeFontPadding = false
            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                setTextColor(PSkinManager.getBtnThemeColor())
            } else {
                setTextColor(Color.WHITE)
            }
            typeface = Typeface.DEFAULT_BOLD
            mLikeCountTv = this

            updateCountText()
        })
    }

    override fun getBottomText(): String {
        return "点赞"
    }

    override fun providerBottomView(context: Context, soundInfo: PlayingSoundInfo): BottomView? {
        return BottomView(
            TextView(context).apply {
                text = "点赞"
                alpha = 0.4f
                textSize = 10f
                isSingleLine = true
            }
        )
    }

    override fun afterUpdate(soundInfo: PlayingSoundInfo) {
        update()
    }

    override fun shouldDisplay(soundInfo: PlayingSoundInfo, isAudio: Boolean): Boolean {
        this.curSoundInfo = soundInfo
        update()
        return true
    }

    private fun update() {
        val isLike = curSoundInfo?.otherInfo?.isLike == true
        if (mLottieLike?.isAnimating == true) {
            mLottieLike?.cancelAnimation()
        }
        mLottieLike.updateProgress(if (isLike) 1f else 0.toFloat())
    }

    override fun onCreate() {
    }

    override fun onDestroy() {
    }

    override fun updateDynamicData(dynamic: PlayPageDynamicState) {
        mLikeCount = dynamic.likeCount
        updateLikeState(dynamic.isLiked)
    }

    private fun updateCountText() {
        mLikeCountTv?.setCount(mLikeCount)
        ViewStatusUtil.setVisible(if (mLikeCount > 0) View.VISIBLE else View.GONE, mLikeCountTv)
    }

    private fun updateLikeState(isCollect: Boolean) {
        updateCountText()
        if (isCollect && mLottieLike?.isAnimating == true) return
        mLottieLike?.cancelAnimation()
        mLottieLike.updateProgress(if (isCollect) 1f else 0f)
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        doCollect()
    }

    private fun doCollect() {
        val activity = BaseApplication.getTopActivity() ?: return
        val isLike = curSoundInfo?.otherInfo?.isLike == true
        if (UserInfoMannage.hasLogined()) {
            if (!isLike) {
                if (mLottieLike!!.isAnimating) {
                    mLottieLike!!.resumeAnimation()
                } else {
                    mLottieLike!!.playAnimation()
                }

                if (mLikeCountTv?.visibility == View.VISIBLE) {
                    mLikeCountTv?.animateChange(mLikeCount)
                }
            }
        }
        traceLikeOrCollect(isLike, true)
        isLikeBtnClick = true
        val track: TrackM? = curSoundInfo?.trackInfo2TrackM()
        LikeTrackManage.toLikeOrUnLike(track,
            true,
            null,
            activity,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(`object`: Boolean?) {
                    if (`object` == null || !`object`) {
                        return
                    }

                    onSoundLiked(!isLike, track)
                    isLikeBtnClick = false
                }

                override fun onError(code: Int, message: String) {
                    mLottieLike.updateProgress(if (isLike) 1f else 0f)
                    CustomToast.showFailToast(message)
                    isLikeBtnClick = false
                }
            })
    }

    private fun onSoundLiked(isLiked: Boolean, track: Track?) {
        if (track == null) {
            return
        }
        if (isLiked) {
            if (EvaluateAlbumManager.shouldShowEvaluateDialogFootball()) {
                EvaluateAlbumManager.showBottomEvaluateDialog(
                    track.album?.albumId ?: 0,
                    track.dataId
                )
            } else {
                AppCommentManager.showDialog(600, true)
            }
            mLottieLike?.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
        } else {
            mLottieLike.updateProgress(0f)
        }

        RouteServiceUtil.getDownloadService().updateFavorState(track.dataId, isLiked, true)
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
            .updateTrackInPlayList(track)
    }

    private fun traceLikeOrCollect(isCancel: Boolean, isLike: Boolean) {
        // 声音-点赞/收藏操作（底部）  点击事件
        XMTraceApi.Trace().click(17506) // 用户点击时上报
            .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
            .put("currTrackId", curSoundInfo?.trackInfo?.trackId?.toString() ?: "")
            .put("currAlbumId", curSoundInfo?.albumInfo?.albumId?.toString() ?: "")
            .put("Item", if (isLike) "点赞" else "收藏") // 点赞 / 收藏，记录点击后的状态
            .put("currPage", "newPlay") // newPlay/互动播放页/互动浮层页等
            .put("replyId", "").put("isCancel", isCancel.toString()) // true/false
            .put("commentId", "").put("uid", curSoundInfo?.userInfo?.uid?.toString())
            .put("style", "竖版") // 横版｜竖版
            .apply {
                PlayPageInteractiveTraceUtil.tracePlayInfo(this)
            }.put("hasDynamicGuide", "false") // ture/false
            .put("content", "") // 有上报引导点赞文案/无不上报
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE
                )
            ).put("fullScreenMode", "half") // full 表示全屏，half 表示半屏
            .put("playViewForm", "1") //  1 表示高唤起 2 表示低唤起
            .apply {
                AudioInfoTraceUtil.interceptTrace(this)
            }
            .createTrace()
    }
}