package com.ximalaya.ting.android.main.playpage.playy

import android.content.res.Configuration
import android.graphics.Bitmap
import android.view.View
import com.ximalaya.ting.android.host.fragment.play.XPlayPage
import com.ximalaya.ting.android.main.playpage.audiointerface.IAudioPlayPageLifecycle
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.remote.IScrollListenerCallBack
import com.ximalaya.ting.android.main.playpage.playy.biz.BizViewResource
import com.ximalaya.ting.android.main.playpage.playy.biz.IBizArbiter
import com.ximalaya.ting.android.main.playpage.playy.biz.XConfigRegister
import com.ximalaya.ting.android.main.playpage.playy.biz.XYellowBarStatusListener
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.YFunctionEntriesComponentV2
import com.ximalaya.ting.android.main.playpage.playy.component.immersive.XImmersive
import com.ximalaya.ting.android.main.playpage.playy.component.tips.ITipsChangeListener
import com.ximalaya.ting.android.main.playpage.playy.component.tips.ITipsShowHideChangeListener
import com.ximalaya.ting.android.main.playpage.playy.component.tips.v2.TipPriority
import com.ximalaya.ting.android.main.playpage.playy.component.video.IVideoContainer
import com.ximalaya.ting.android.main.playpage.playy.dialog.YPlayListAndHistoryFragment
import com.ximalaya.ting.android.main.playpage.playy.listener.ISkipHeadTailShowListener
import com.ximalaya.ting.android.main.playpage.playy.listener.UnableToPlayStatusAggregator
import com.ximalaya.ting.android.main.playpage.playy.tabs.XPlayPageTab
import com.ximalaya.ting.android.main.playpage.playy.listener.IPptModeSwitchListener
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinNotifier
import com.ximalaya.ting.android.main.playpage.playy.tabs.provider.faces.XBottomBarProvider
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import kotlinx.coroutines.CoroutineScope

interface IPlayContainer: XPlayPage, PSkinNotifier {

    fun scope(): CoroutineScope
    fun videoComponent(): IVideoContainer?
    fun switchToVideo(startPlay: Boolean = false)
    fun switchToAudio(startPlay: Boolean = true)
    fun toggleControlVisibility(force: Boolean? = null)

    fun getPlayModeSwitcher(): PlayModeSwitcher?

    fun gotoTop(expand: Boolean = true, animate: Boolean = false, once: Boolean = false)

    fun hideAllXTips()
    fun isShowingToListenGuide(): Boolean
    fun showCommentPanel(needInputTimeMark: Boolean = true, enableAutoShowKeyboard: Boolean = true)
    fun showMasterClassNotePanel()
    fun startInputComment(title: String? = null, fromSource: Int = -1)

    fun toggleFullscreen(trackItem: String)

    fun addScreenChangeListener(listener: ScreenChangeListener)
    fun removeScreenChangeListener(listener: ScreenChangeListener)

    fun addLifecycleListener(lifecycleListener: IAudioPlayPageLifecycle)
    fun removeLifecycleListener(lifecycleListener: IAudioPlayPageLifecycle)

    fun addPlayStatusListener(listener: IXmPlayerStatusListener)
    fun removePlayStatusListener(listener: IXmPlayerStatusListener)

    fun addVideoPlayStatusListener(listener: IVideoPlayStatueListener)
    fun removeVideoPlayStatusListener(listener: IVideoPlayStatueListener)

    fun addStickyChangeListener(listener: IStickyChangeListener)
    fun removeStickyChangeListener(listener: IStickyChangeListener)

    fun isVideoMode(): Boolean
    fun addPlayTypeChangeListener(listener: PlayTypeChangeListener)
    fun removePlayTypeChangeListener(listener: PlayTypeChangeListener)

    fun updateCoverComponents()
    fun getTitleBarPositionY(): Int
    fun getOriginalBookLocationX(): Float
    fun registerScrollListener(scrollListener: IScrollListenerCallBack)
    fun removeScrollListener(scrollListener: IScrollListenerCallBack)
    fun addShowSkipHeadTailListener(skipHeadListener: ISkipHeadTailShowListener)
    fun removeShowSkipHeadTailListener(skipHeadListener: ISkipHeadTailShowListener)
    fun notifySkipShowListener(isShow: Boolean, isAudioTips: Boolean)
    fun addUnableToPlayListener(listener: UnableToPlayListener)
    fun removeUnableToPlayListener(listener: UnableToPlayListener)
    fun getCoverManager(): YCoverComponentsManager?
    fun updateNextAndPreBtnStatus(hasNext: Boolean, hasPre: Boolean)
    fun getTopContainerHeightNormal(): Int
    fun addCoverChangeListener(listener: CoverChangeListener)
    fun removeCoverChangeListener(listener: CoverChangeListener)
    fun notifyCoverChangeListener(offset:Float)
    fun onCoverLocationChange() //封面移动会触发此回调

    fun getImmersiveComponent(): XImmersive?
    fun onDocSwitchChange(docShow: Boolean)
    fun getCurTrackId(): Long

    fun playNext()
    fun playPause()
    fun isPlaying(): Boolean
    fun showPlayList(playListFragment: YPlayListAndHistoryFragment)

    fun getPageStatus(): XPlayPageStatus

    fun resizeScreenLayout(height: Int)

    fun addTopLayoutBgColorChangeListener(listener: ITopLayoutBgColorChangeListener)
    fun removeTopLayoutBgColorChangeListener(listener: ITopLayoutBgColorChangeListener)

    fun addXYellowBarStatusListener(listener: XYellowBarStatusListener)
    fun removeXYellowBarStatusListener(listener: XYellowBarStatusListener)

    //弹幕入口点击
    fun onDanmakuEntryBtClick()
    fun addXDanmakuStatusChangeListener(listener: XDanmakuStatusChangeListener)
    fun removeXDanmakuStatusChangeListener(listener: XDanmakuStatusChangeListener)

    //获取FunctionComponent全屏时在屏幕中的位置
    fun getFunctionBarPositionY():Int

    fun enterLandScape()
    fun exitLandScape()

    fun onVideoStart(videoSourceUrl: String?)
    fun onVideoPause(videoSourceUrl: String?, playedTime: Long, duration: Long)
    fun onVideoStop(videoSourceUrl: String?, playedTime: Long, duration: Long)
    fun onVideoProgress(videoSourceUrl: String?, curPosition: Long, duration: Long)
    fun setVideoPlayView(view:IMediaPlayerControl?)
    fun addIConfigurationChangedListener(listener: IConfigurationChangedListener)
    fun removeIConfigurationChangedListener(listener: IConfigurationChangedListener)

    fun hideControlBarWhenLandscape()
    fun getBizArbiters(): IBizArbiter?
    fun getXConfigRegister(): XConfigRegister
    fun cancelCoverAreaScreenOff()

    //tips显示
    fun showTips(id: Int, view: View): Boolean?
    fun dismissTips(id: Int)
    fun addTipsChangeListener(listener: ITipsChangeListener)
    fun removeTipsChangeListener(listener: ITipsChangeListener)
    //tips显示隐藏callback
    fun addTipsShowHideStatusChangeListener(listener:ITipsShowHideChangeListener)
    fun removeTipsShowHideStatusChangeListener(listener:ITipsShowHideChangeListener)
    fun onTipsShowHideChange(id: Int, isShow: Boolean)

    //直播 mc入口动画展开/收缩
    fun onLiveMcEntryExpandAnimatorChange(isStart:Boolean)


    fun currentTab(): XPlayPageTab?

    fun isShowVideoEntry() : Boolean

    //强制显示seekbar
    fun showSeekbarAndFunctionForce()

    fun isVideoUserInputEnabled() :Boolean

    fun updateBottomBar(bottomBarProvider: XBottomBarProvider)

    fun setCustomBottomBar(tabId: Long, bottomBar: BizViewResource?)

    fun canShowStageAreaCard(): Boolean

    fun showReadBookComponent(enterFrom: Int, selectModel: Boolean = false)

    fun closeReadBookComponent()

    fun canShowDanMuEntry(): Boolean

    fun getPlayPageScreenShot(): Bitmap?

    fun forbiddenBizArbiters(forbidden: Boolean)

    fun dismissAllBizArbiters()

    fun addPptModeSwitch(listener: IPptModeSwitchListener)

    fun removePptModeSwitch(listener: IPptModeSwitchListener)

    fun notifyPptModeSwitch(pptMode: Boolean)

    fun showShareDialog()

    fun clearPicTheme()

    fun getTitleBarPlaceHolder(): View?

    fun getTipsContaineriView(): View?

    fun forceShowOrHideTips(show: Boolean)
    fun getCurShowTipConfig(): TipPriority?
    fun getBottomCommentTipView(): View?

    fun openSurveyDialog(type: Int)

    fun getFunctionEntriesComponent(): YFunctionEntriesComponentV2?

    fun openUri(uri: String)

    fun notifyPlayRnLoadFinish(isSuccess: Boolean)
}

interface ScreenChangeListener {
    fun beforeFullScreenChanged(fullScreen: Boolean)
    fun onFullScreenChanged(fullScreen: Boolean)
    fun afterFullScreenChanged(fullScreen: Boolean)

    fun onScreenStatusChanged(xPlayPageStatus: XPlayPageStatus)
}

interface XTabChangeCallback {
    fun onTabSelect(tab: XPlayPageTab)
    fun onTabUnSelect(tab: XPlayPageTab) {}
}

interface ITopLayoutBgColorChangeListener{
    fun onChangeTopLayoutBgColor(mainColorId : Int,subColorId:Int)
}

interface IVideoPlayStatueListener{
    fun onVideoStart(videoSourceUrl: String?)
    fun onVideoPause(videoSourceUrl: String?, playedTime: Long, duration: Long)
    fun onVideoStop(videoSourceUrl: String?, playedTime: Long, duration: Long)
    fun onVideoProgress(videoSourceUrl: String?, curPosition: Long, duration: Long)
    fun setVideoPlayView(view:IMediaPlayerControl?)
}

interface IScreenOrientationChange {
    fun onScreenOrientationChange(isLandScape: Boolean)
}

/**
 *  用于监听播放类型变化事件（音视频切换）
 */
interface PlayTypeChangeListener {

    /**
     * @param isAudio, True 音频 False 视频
     */
    fun onPlayTypeChanged(isAudio: Boolean)
}

interface UnableToPlayListener {
    /**
     * This method is called when the ability to play changes.
     *
     * @param ableToPlay A boolean representing the ability to play.
     * @param reason An object of type UnablePlayReason representing the reason for the change in ability to play.
     */
    fun onStatusChange(ableToPlay: Boolean, reason: Set<UnableToPlayStatusAggregator.UnablePlayReason>)
}

interface CoverChangeListener {
    /**
     * 封面上下移动
     */
    fun changeCoverTranslationY(offsetY : Float)
}

/**
 * 沉浸式背景图状态回调
 */
interface XImmersiveStatusChangeListener {
    fun onshow(isShow: Boolean)
}

interface XDanmakuStatusChangeListener {
    fun addDanmaku(content : String, bulletColorType : Int, bottom : Boolean)

    fun onDanmakuEntryOpenStatusChange(opened:Boolean)
}

interface IConfigurationChangedListener {
   fun onConfigurationChanged(newConfig: Configuration)
   fun onConfigurationChangedByUser(isLandScape: Boolean)
}

interface IStickyChangeListener {
    fun onStickyChange(sticky: Boolean)
}

data class XPlayPageStatus(
    val isFullScreen: Boolean,
    val isAudioMode: Boolean,
    val isSticky: Boolean = false,
    val isLandScape: Boolean = false,
    val isRnLoadSuccess: Boolean = false,
) {
    val canScroll: Boolean get() = isRnLoadSuccess && (isAudioMode || !isLandScape)
    val isPinTop: Boolean get() = false
}