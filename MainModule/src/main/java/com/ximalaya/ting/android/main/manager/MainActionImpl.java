package com.ximalaya.ting.android.main.manager;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.PopupWindow;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.download.DownloadLiteManager;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.service.DownloadAdvertisParams;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FragmentUtil;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.IPromiseCallback;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.adapter.mulitviewtype.IMulitViewTypeAdapter;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.CommentConstants;
import com.ximalaya.ting.android.host.constants.TingListConstants;
import com.ximalaya.ting.android.host.data.model.RnConfirmParam;
import com.ximalaya.ting.android.host.data.model.comment.TingReadComParamModel;
import com.ximalaya.ting.android.host.download.ActionCallBack;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.play.XPlayPage;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.listener.IBaseMySpaceView;
import com.ximalaya.ting.android.host.listener.IBaseTempoListener;
import com.ximalaya.ting.android.host.listener.IColumnLargeAdProvider;
import com.ximalaya.ting.android.host.listener.IDataChangeCallback;
import com.ximalaya.ting.android.host.listener.IFeedBubbleItemClickListener;
import com.ximalaya.ting.android.host.listener.IFloatingPlayControlComponent;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.listener.IHomeRnNetAction;
import com.ximalaya.ting.android.host.listener.ILoadMineDataCallBack;
import com.ximalaya.ting.android.host.listener.IParaComListener;
import com.ximalaya.ting.android.host.listener.IPlayPageLargeAdProvider;
import com.ximalaya.ting.android.host.listener.ITingReadFloatingPlayControlComponent;
import com.ximalaya.ting.android.host.listener.ITomatoesClickCallback;
import com.ximalaya.ting.android.host.live.callback.IMiniDramaLockCallBack;
import com.ximalaya.ting.android.host.live.callback.ISoundTabsCallBack;
import com.ximalaya.ting.android.host.manager.ArriveTraceManager;
import com.ximalaya.ting.android.host.manager.NewShowNotesManager;
import com.ximalaya.ting.android.host.manager.NickNameSettingManager;
import com.ximalaya.ting.android.host.manager.OnlyUseMainProcessSharePreUtil;
import com.ximalaya.ting.android.host.manager.OuterItingManager;
import com.ximalaya.ting.android.host.manager.PhoneContactsManager;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.AdStateManage;
import com.ximalaya.ting.android.host.manager.ad.IAdEngineProviderExtend;
import com.ximalaya.ting.android.host.manager.ad.IMiddleAdViewShowCallback;
import com.ximalaya.ting.android.host.manager.ad.IPlayAdEngine;
import com.ximalaya.ting.android.host.manager.appcomment.AppCommentManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IReadBookInterceptBackListener;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.radio.IRadioFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadAdRecordManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareConstants;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.tinglist.ITingListManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.SavePlayletPlayEpisodeEntity;
import com.ximalaya.ting.android.host.model.VideoFeedBackTraceData;
import com.ximalaya.ting.android.host.model.account.HomePageModel;
import com.ximalaya.ting.android.host.model.account.HomePageModelNew;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.alarm.AlarmRecord;
import com.ximalaya.ting.android.host.model.album.AlbumCommentModel;
import com.ximalaya.ting.android.host.model.album.AlbumListenNote;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AlbumSimpleInfo;
import com.ximalaya.ting.android.host.model.anchor.Anchor;
import com.ximalaya.ting.android.host.model.anchor.AnchorFollowParam;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.model.feed.DownloadKaChaBean;
import com.ximalaya.ting.android.host.model.homepage.HomePageTabModel;
import com.ximalaya.ting.android.host.model.kacha.KachaAIDocModel;
import com.ximalaya.ting.android.host.model.myspace.MineModuleItemInfo;
import com.ximalaya.ting.android.host.model.payment.UniversalPayment;
import com.ximalaya.ting.android.host.model.play.AnchorRewardInfo;
import com.ximalaya.ting.android.host.model.play.CommentModel;
import com.ximalaya.ting.android.host.model.play.DubDownloadInfo;
import com.ximalaya.ting.android.host.model.play.ImageUrl;
import com.ximalaya.ting.android.host.model.play.PlayPageBridgeResult;
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.model.user.ThirdPartyUserInfo;
import com.ximalaya.ting.android.host.read.bean.TRMainPlayTheme;
import com.ximalaya.ting.android.host.read.ting.callback.ITRCreateMainPlayPageThemeCallBack;
import com.ximalaya.ting.android.host.recommend.IRecommendRnView;
import com.ximalaya.ting.android.host.service.XiaoaiControllService;
import com.ximalaya.ting.android.host.socialModule.IFloatingFragmentDismissListener;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.MainBundleUtil;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.PlayletPlayEpisodeUtil;
import com.ximalaya.ting.android.host.util.QRImageUtil;
import com.ximalaya.ting.android.host.util.ShareUtils;
import com.ximalaya.ting.android.host.util.common.CommonBottomDialogUtilConstants;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.other.PermissionManage;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor;
import com.ximalaya.ting.android.host.util.startup.ViewPool;
import com.ximalaya.ting.android.host.view.lrcview.LrcEntry;
import com.ximalaya.ting.android.host.view.other.EmotionSelector;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.fragment.AdAppDownloadRemindDialog;
import com.ximalaya.ting.android.main.adModule.fragment.AdAppDownloadStyle2Dialog;
import com.ximalaya.ting.android.main.adModule.fragment.AdAppDownloadStyle3Dialog;
import com.ximalaya.ting.android.main.adModule.fragment.AdAppDownloadStyle4Dialog;
import com.ximalaya.ting.android.main.adModule.fragment.impl.DownloadDialogStyle4AdRecordListener;
import com.ximalaya.ting.android.main.adModule.manager.RadioAdManager;
import com.ximalaya.ting.android.main.adModule.manager.RadioAdManagerNew;
import com.ximalaya.ting.android.main.adModule.manager.YaoyiYaoAdManage;
import com.ximalaya.ting.android.main.adapter.CalabashLineAdapter;
import com.ximalaya.ting.android.main.adapter.RadioListAdapter;
import com.ximalaya.ting.android.main.adapter.album.AlbumAdapter;
import com.ximalaya.ting.android.main.adapter.album.IAlbumCallback;
import com.ximalaya.ting.android.main.adapter.album.item.AbsWoTingAdapter;
import com.ximalaya.ting.android.main.adapter.album.item.BoughtAlbumAdapter;
import com.ximalaya.ting.android.main.adapter.album.item.WoTingRecommendAdapter;
import com.ximalaya.ting.android.main.adapter.find.recommend.CalabashAdapterProvider;
import com.ximalaya.ting.android.main.adapter.find.util.SearchCustomCardDataUtils;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.MulitViewTypeAdapter;
import com.ximalaya.ting.android.main.adapter.myspace.AttentionMemberAdapter;
import com.ximalaya.ting.android.main.adapter.myspace.ToolsAdapterProvider;
import com.ximalaya.ting.android.main.adapter.myspace.UserListAdapter;
import com.ximalaya.ting.android.main.adapter.track.PaidTrackAdapter;
import com.ximalaya.ting.android.main.aduve.business.yellowbar.UveYellowBarBusiness;
import com.ximalaya.ting.android.main.albumModule.album.ShareTipDailogFragment;
import com.ximalaya.ting.android.main.albumModule.album.album2.AlbumFragmentNew2;
import com.ximalaya.ting.android.main.albumModule.album.album3.AlbumFragmentNew3;
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.pagePart.middleBar.buyAction.BuyActionUtil;
import com.ximalaya.ting.android.main.albumModule.album.wholeAlbum.WholeAlbumRnBuyParamsUtil;
import com.ximalaya.ting.android.main.albumModule.other.SimilarRecommendFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryContentFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryRecommendAdUtil;
import com.ximalaya.ting.android.main.categoryModule.manager.TangramManager;
import com.ximalaya.ting.android.main.categoryModule.page.tab.HomePageTabAndChannelListNewFragmentDialog;
import com.ximalaya.ting.android.main.commentModule.SearchDirectCommentFragment;
import com.ximalaya.ting.android.main.commentModule.fragment.FloatingTrackCommentFragment;
import com.ximalaya.ting.android.main.commentModule.fragment.TRParagraphCommentFragment;
import com.ximalaya.ting.android.main.constant.HttpParamsConstantsInMain;
import com.ximalaya.ting.android.main.dialog.AdDislikeBottomDialogX;
import com.ximalaya.ting.android.main.dialog.BuyAlbumDialog;
import com.ximalaya.ting.android.main.dialog.CommonInspireDialogFragment;
import com.ximalaya.ting.android.main.dialog.CommonRewardAgainDialogFragment;
import com.ximalaya.ting.android.main.dialog.VideoNotLikeFeedBackUtil;
import com.ximalaya.ting.android.main.dialog.albumFragment.AlbumOperationPanelDialog;
import com.ximalaya.ting.android.main.dialog.universal.RequestMaterial;
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentActionsDialog;
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentActionsDialogHelper;
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentUtil;
import com.ximalaya.ting.android.main.downloadModule.quality.ChooseTrackQualityDialog;
import com.ximalaya.ting.android.main.dubbingModule.DubbingPlayFragmentNew;
import com.ximalaya.ting.android.main.dubbingModule.dubdownload.DubVideoDownloadTaskController;
import com.ximalaya.ting.android.main.fragment.dialog.FreshGiftFragment;
import com.ximalaya.ting.android.main.fragment.dialog.h5.H5DialogManager;
import com.ximalaya.ting.android.main.fragment.dialog.h5.VipFloatPurchaseDialog;
import com.ximalaya.ting.android.main.fragment.find.FreeListenFreeRewardDialogFragment;
import com.ximalaya.ting.android.main.fragment.find.FreeListenWatchVideoDialogFragment;
import com.ximalaya.ting.android.main.fragment.find.HomePageFragment;
import com.ximalaya.ting.android.main.fragment.find.child.BigScreenAdManager;
import com.ximalaya.ting.android.main.fragment.find.child.rn.manager.RecommendRnNetManager;
import com.ximalaya.ting.android.main.fragment.find.child.rn.view.RecommendRnViewAdapter;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager;
import com.ximalaya.ting.android.main.fragment.find.other.anchor.FindFriendFragmentNew;
import com.ximalaya.ting.android.main.fragment.mylisten.NotificationOpenGuideDialog;
import com.ximalaya.ting.android.main.fragment.mylisten.SubscribeSearchFragment;
import com.ximalaya.ting.android.main.fragment.myspace.QRCodeScanFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildProtectionAgeRangeSelectFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildProtectionForgetPwdFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildProtectionPassWordFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildProtectionRemindFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildProtectionSettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.MyFootPrintFragmentNew;
import com.ximalaya.ting.android.main.fragment.myspace.child.MyFootPrintFragmentV2;
import com.ximalaya.ting.android.main.fragment.myspace.child.SettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.AddOrEditAlarmFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.PushSettingFragment;
import com.ximalaya.ting.android.main.fragment.offsale.OffSaleRecommendDialog;
import com.ximalaya.ting.android.main.fragment.planterminate.PlanTerminateFragmentNewX;
import com.ximalaya.ting.android.main.fragment.share.album.ShareAlbumPosterUtil;
import com.ximalaya.ting.android.main.fragment.share.track.ShareTrackPosterUtil;
import com.ximalaya.ting.android.main.fragment.smartdevice.WiFiConnectFragment;
import com.ximalaya.ting.android.main.fragment.subscribeRecommend.SubscribeRecommendFragment;
import com.ximalaya.ting.android.main.kachamodule.dialog.KachaSaveLocalDialogFragment;
import com.ximalaya.ting.android.main.kachamodule.manager.ShortContentDirManager;
import com.ximalaya.ting.android.main.manager.albumList.customAlbum.UniversalCustomAlbumListMiddleAdManager;
import com.ximalaya.ting.android.main.manager.comment.LikeIconModelForRN;
import com.ximalaya.ting.android.main.manager.comment.RemoteLikeIconManager;
import com.ximalaya.ting.android.main.manager.freelisten.CommonUtil;
import com.ximalaya.ting.android.main.manager.freelisten.FreeListenManager;
import com.ximalaya.ting.android.main.manager.freelisten.manager.CheckFreeListenDialogManager;
import com.ximalaya.ting.android.main.manager.listentask.ListenTaskManager;
import com.ximalaya.ting.android.main.manager.myspace.MainEntranceApiManage;
import com.ximalaya.ting.android.main.manager.newUser.NewUserDlgManager;
import com.ximalaya.ting.android.main.manager.newUser.NewUserManager;
import com.ximalaya.ting.android.main.manager.playPage.PlayListAndHistoryDialogManager;
import com.ximalaya.ting.android.main.mine.fragment.MineFragmentV9;
import com.ximalaya.ting.android.main.mine.fragment.NickNameSettingDialogFragment;
import com.ximalaya.ting.android.main.mine.util.MineTopUtil;
import com.ximalaya.ting.android.main.model.OneKeyTrack;
import com.ximalaya.ting.android.main.model.SpeedScaleModel;
import com.ximalaya.ting.android.main.model.find.FindHomePageModel;
import com.ximalaya.ting.android.main.model.recommend.DislikeReasonModel;
import com.ximalaya.ting.android.main.model.recommend.DislikeReasonNew;
import com.ximalaya.ting.android.main.model.recommend.RecommendDiscoveryM;
import com.ximalaya.ting.android.main.model.recommend.SpecialColumnM;
import com.ximalaya.ting.android.main.model.recommend.SubscribeRecommendAlbumMListWithDescription;
import com.ximalaya.ting.android.main.payModule.BuyAlbumFragment;
import com.ximalaya.ting.android.main.playModule.freeListen.FreeListenEndGuideToNextAdDialog;
import com.ximalaya.ting.android.main.playModule.view.MoreActionDialogV2;
import com.ximalaya.ting.android.main.playModule.view.SkipHeadTailDialog;
import com.ximalaya.ting.android.main.playlet.fragment.PlayletDetailFragment;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAdaptationUtilKt;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.interactivebox.InteractiveBoxConfigUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.interactivebox.InteractiveFeedbackBar;
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimationManagerV2;
import com.ximalaya.ting.android.main.playpage.component.FloatingControlBarComponent;
import com.ximalaya.ting.android.main.playpage.component.TingReadFloatingConComponent;
import com.ximalaya.ting.android.main.playpage.dialog.AnchorShoppingDialogFragment;
import com.ximalaya.ting.android.main.playpage.dialog.ListenTimeDialogManager;
import com.ximalaya.ting.android.main.playpage.dialog.PlanTerminalNewDialog;
import com.ximalaya.ting.android.main.playpage.fragment.BasePlayPageTabFragment;
import com.ximalaya.ting.android.main.playpage.fragment.NewShowNotesDetailFragment;
import com.ximalaya.ting.android.main.playpage.fragment.PlayFragmentNew;
import com.ximalaya.ting.android.main.playpage.fragment.ToBePlayedFragmentV2;
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayControlComponentService;
import com.ximalaya.ting.android.main.playpage.internalservice.IPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayFragmentAbManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageSkinDownloadManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayShareDialogAbManager;
import com.ximalaya.ting.android.main.playpage.manager.TRObtainPlayPageThemeMainManager;
import com.ximalaya.ting.android.main.playpage.manager.shortdrama.ShortDramaManager;
import com.ximalaya.ting.android.main.playpage.manager.shortdrama.ShortDramaState;
import com.ximalaya.ting.android.main.playpage.playx.dialog.RnCommonDialog;
import com.ximalaya.ting.android.main.playpage.playx.utils.SubscribeFollowPriority;
import com.ximalaya.ting.android.main.playpage.playx.utils.TingDataUtil;
import com.ximalaya.ting.android.main.playpage.playy.PlayColumnLargeAdProvider;
import com.ximalaya.ting.android.main.playpage.playy.PlayPageLargeAdProvider;
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.XAudioPlayCoverAdEngine;
import com.ximalaya.ting.android.main.playpage.playy.component.rn.YRnComponent;
import com.ximalaya.ting.android.main.playpage.playy.component.speed.ISpeedSelectCallback;
import com.ximalaya.ting.android.main.playpage.playy.component.speed.SpeedDialogForQuickListen;
import com.ximalaya.ting.android.main.playpage.playy.skin.PlaypageSkinDebugFragment;
import com.ximalaya.ting.android.main.playpage.playy.utils.YTingDataUtil;
import com.ximalaya.ting.android.main.playpage.util.AudioPlayPageAlbumBuyManager;
import com.ximalaya.ting.android.main.playpage.util.CommentAbFilter;
import com.ximalaya.ting.android.main.playpage.util.InteractiveCardUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayBuyViewUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayPageDownloadUtils;
import com.ximalaya.ting.android.main.playpage.util.PlayPageFollowUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayPageTabUtil;
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils;
import com.ximalaya.ting.android.main.playpage.view.BottomViewNew;
import com.ximalaya.ting.android.main.playpage.vote.dialog.VoteDialogFragment;
import com.ximalaya.ting.android.main.rankModule.AggregateRankUtil;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.share.manager.PLCShareManager;
import com.ximalaya.ting.android.main.share.util.VideoShareUtil;
import com.ximalaya.ting.android.main.util.AlbumMiddleBarUtil;
import com.ximalaya.ting.android.main.util.CommonBottomDialogUtil;
import com.ximalaya.ting.android.main.util.ITingHandlerUtil;
import com.ximalaya.ting.android.main.util.SubscribeUnlockUtil;
import com.ximalaya.ting.android.main.util.TingReadMoreMenuUtils;
import com.ximalaya.ting.android.main.util.WeChatUtil;
import com.ximalaya.ting.android.main.util.other.AlbumActionUtil;
import com.ximalaya.ting.android.main.util.other.ArtistUtil;
import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain;
import com.ximalaya.ting.android.main.util.other.TrackActionUtil;
import com.ximalaya.ting.android.main.util.other.XimiUtil;
import com.ximalaya.ting.android.main.view.dialog.PlayNoCopyRightDialog;
import com.ximalaya.ting.android.main.view.feed.FeedBubblePopupWindow;
import com.ximalaya.ting.android.main.view.other.CommentQuoraInputLayout;
import com.ximalaya.ting.android.main.view.other.MagneticView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.manager.QuickListenForPlayProcessUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.read.bean.TingParaBean;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import okhttp3.internal.huc.OkHttpURLConnection;


/**
 * 主app提供给别的module的操作
 *
 * <AUTHOR> on 2017/2/24.
 */

public class MainActionImpl implements IMainFunctionAction {

    @Override
    public int getSpecialColumnMTypeColumnSubjectConstants() {
        return SpecialColumnM.TYPE_COLUMN_SUBJECT;
    }

    @Override
    public int getCategoryContentFragmentFlagCityDataConstants() {
        return CategoryContentFragment.FLAG_CITY_DATA;
    }

    @Override
    public void gotoArtistPage(String url, Activity activity) {
        ArtistUtil.gotoArtistPage(url, activity);
    }

    @Override
    public void cancelPay(AbstractTrackAdapter adapter) {
        if (adapter instanceof PaidTrackAdapter) {
            adapter.cancelPay();
        }
    }

    @Override
    public AbstractTrackAdapter newPaidTrackAdapter(Context context, List<Track> tracks, int type, int playFrom) {
        return newPaidTrackAdapter(context, tracks, type, playFrom, "");
    }

    @Override
    public AbstractTrackAdapter newPaidTrackAdapter(Context context, List<Track> tracks, int type, int playFrom, String pageFrom) {
        PaidTrackAdapter paidTrackAdapter = new PaidTrackAdapter(context, tracks);
        paidTrackAdapter.setPageFrom(pageFrom);
        paidTrackAdapter.setTrackType(type);
        paidTrackAdapter.setPlaySource(playFrom);
        return paidTrackAdapter;
    }

    @Override
    public HolderAdapter<Radio> newRadioListAdapter(Context context, List<Radio> radios, BaseFragment fragment, boolean fromSearch) {
        RadioListAdapter radioListAdapter = new RadioListAdapter(context, radios);
        radioListAdapter.setFragment(fragment);
        if (fromSearch) {
            radioListAdapter.setType(RadioListAdapter.TYPE_SEARCH);
        }
        return radioListAdapter;
    }

    @Override
    public HolderAdapter<Anchor> newUserListAdapter(Context context, List<Anchor> list, BaseFragment fragment, int type) {
        UserListAdapter userListAdapter = new UserListAdapter(context, list);
        userListAdapter.setFragment((BaseFragment2) fragment);
        userListAdapter.setType(type);
        return userListAdapter;
    }

    @Override
    public BaseAlbumAdapter newAlbumAdapter(Context context, List<Album> albums, int type, boolean chooseType, String keyword, String searchId) {
        AlbumAdapter albumAdapter = new AlbumAdapter(context, albums);
        albumAdapter.setSearchWord(keyword);
        albumAdapter.setSearchId(searchId);
        albumAdapter.setTypeFrom(type);
        albumAdapter.setChooseType(chooseType);
        return albumAdapter;
    }

    @Override
    public HolderAdapter<Anchor> createSearchAttentionMeberAdapter(BaseFragment2 fragment2, List<Anchor> list, int type) {
        if (fragment2 != null) {
            AttentionMemberAdapter memberAdapter = new AttentionMemberAdapter(fragment2.getActivity(), list);
            memberAdapter.setBizType(type);
            memberAdapter.setFragment(fragment2);
            memberAdapter.setType(AttentionMemberAdapter.TYPE_FANS_SEARCH);
            return memberAdapter;
        }
        return null;
    }


    @Override
    public BaseAdapter createWoTingRecommendAdapter(Context context, Activity activity, boolean isShowNoSubscribeType, List<Album> list, BaseFragment fragment, final AdapterView.OnItemClickListener onItemClickListener) {
        WoTingRecommendAdapter woTingRecommendAdapter = new WoTingRecommendAdapter(context, activity, isShowNoSubscribeType);
        woTingRecommendAdapter.setFragment(fragment);
        woTingRecommendAdapter.setData(list);
        if (onItemClickListener != null)
            woTingRecommendAdapter.setRecommendAction(new AbsWoTingAdapter.IRecommendAction() {
                @Override
                public void onSubscribeClick(int position, AlbumM album) {
                    onItemClickListener.onItemClick(null, null, position, position);
                }
            });
        return woTingRecommendAdapter;
    }

    @Override
    public void shareTrack(FragmentActivity activity, Track trackM, int shareTypeTrack) {
        if (activity != null && trackM != null) {
            ShareUtilsInMain.shareTrackV2(activity, trackM, shareTypeTrack, "", null);
        }
    }

    @Override
    public void shareAlbumForPlc(FragmentActivity activity, AlbumM data, int shareTypeAlbum) {
        if (activity != null && data != null) {
            ShareUtilsInMain.shareAlbum(activity, data, shareTypeAlbum, 1107);
        }
    }

    @Override
    public void shareAlbumListenNote(FragmentActivity activity, AlbumM album, AlbumListenNote albumListenNote, int shareType) {
        ShareUtilsInMain.shareAlbumListenNote(activity, album, albumListenNote, shareType);
    }

    @Override
    public void shareVideo(FragmentActivity context, Track trackM) {
        if (context != null && trackM != null) {
            ShareUtilsInMain.shareVideo(context, trackM);
        }
    }

    @Override
    public void shareDub(FragmentActivity context, Track trackM) {
        if (context != null && trackM != null) {
            ShareUtilsInMain.shareDub(context, trackM);
        }
    }

    @Override
    public void showPlayerPageShopDialog(long trackId, String title, String url) {
        String buildLinkUrl = AnchorShoppingDialogFragment.buildLinkUrl(trackId, url);
        AnchorShoppingDialogFragment fragment = AnchorShoppingDialogFragment.newInstance(title, buildLinkUrl);

        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) activity;

            Fragment currentFragment = mainActivity.getCurrentFragmentInManage();
            if (currentFragment == null) {
                currentFragment = mainActivity.getCurrentTopFragment();
            }
            if (currentFragment != null && currentFragment.getChildFragmentManager() != null) {
                FragmentManager childFragmentManager = currentFragment.getChildFragmentManager();
                fragment.show(childFragmentManager, "AnchorShoppingDialogFragment");
            }
        }
    }

    @Override
    public void preloadCommericalDialogResource(@Nullable String popupId, String imageUrl,
                                                @Nullable BaseFragment2 fragment, @NonNull Function0<Unit> function) {
        CommercialDialogConfigMaterialManager.INSTANCE.downloadVideoMaterialAndImage(popupId, imageUrl, function);
    }

    @Override
    public void preloadCommercialDialogVideoResource(@Nullable String popupId, String videoUrl, @NotNull Function0<Unit> function) {
        CommercialDialogConfigMaterialManager.downloadVideoFilePathByUrl(popupId, videoUrl, function);
    }

    @Override
    public void getAlbumSimpleInfoById(long albumId, final IDataCallBack<AlbumSimpleInfo> callback) {
        Map<String, String> simpleParams = new HashMap<>();
        simpleParams.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
        CommonRequestM.getAlbumSimpleInfo(simpleParams, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(AlbumM data) {
                AlbumSimpleInfo albumSimpleInfo = new AlbumSimpleInfo();
                if (data != null) {
                    albumSimpleInfo.setPriceTypeEnum(data.getPriceTypeEnum());
                    albumSimpleInfo.setAuthorized(data.isAuthorized());
                } else {
                    // 默认免费已购买
                    albumSimpleInfo.setPriceTypeEnum(0);
                    albumSimpleInfo.setAuthorized(true);
                }
                if (callback != null)
                    callback.onSuccess(albumSimpleInfo);
            }

            @Override
            public void onError(int code, String message) {
                if (callback != null)
                    callback.onError(code, message);
            }
        });
    }

    @Override
    public IBuyAlbumTip getBuyAlbumDialog(Activity activity, long albumId, String title, boolean isFromAlbumIntro) {
        return new BuyAlbumDialog(activity, albumId, title, isFromAlbumIntro);
    }

    /**
     * 首页tab切换
     */
    @Override
    public boolean switchHomeTab(BaseFragment fragment, int fid) {
        if (fragment != null && fragment instanceof HomePageFragment) {
            String contentType = HomePageTabModel.ITEM_TYPE_RECOMMEND;
            return ((HomePageFragment) fragment).switchFindingTabTo(contentType);
        } else {
            return false;
        }
    }

    @Override
    public void getHomePage(Map<String, String> homeParams, IDataCallBack<HomePageModel> iDataCallBack) {
        MainCommonRequest.getHomePage(homeParams, iDataCallBack);
    }

    @Override
    public void getHomePageNew(Map<String, String> homeParams, IDataCallBack<HomePageModelNew> iDataCallBack) {
        MainCommonRequest.getHomePageModelV9(homeParams, iDataCallBack);
    }

    @Override
    public void getPlayPageInfo(long trackId, HashMap<String, String> params, IDataCallBack<PlayingSoundInfo> iDataCallBack) {
        MainCommonRequest.getPlayPageInfoNew(trackId, params, iDataCallBack);
    }

    private void buryingPointForHandleIting(String url, PushModel model) {
        CommonRequestM.collectItingForGrowth(url);
        new UserTracking().setItemUrl(url).statIting(XDCSCollectUtil.SERVICE_OPEN_ITING);
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(8816)
                .setServiceId("openIting")
                .put("itingUrl", url)
                .immediatelyUpload();
        // 区分来自鸿蒙的跳转
        if (model != null && "harmony".equals(model.fromPage)) {
            trace.put("from", "harmony");
        } else {
            trace.put("from", "other");
        }
        boolean outerIting = OuterItingManager.INSTANCE.isOuterIting();
        if (model != null) {
            model.isOuterIting = outerIting;
        }
        trace.put("sourceType", outerIting ? "outsite" : "insite");

        if (outerIting && model != null && !model.isFromNewIntent) {
            ArriveTraceManager.onAppItingArrive();
        }

        if (model != null && model.isPush) {
            trace.put("itingUrl", model.pushUrl);
        }
        trace.createTrace();
        if (TextUtils.isEmpty(url)) {
            XDCSCollectUtil.statErrorToXDCS("iting", "has model: " + (model != null) + " " + Log.getStackTraceString(new Throwable()));
        }

        if (model != null && !TextUtils.isEmpty(model.speechXmChannel)) {
            XiaoaiControllService.trace(model.speechXmChannel, model.messageType + "", url);
        }
    }

    @Override
    public boolean handleITing(Activity activity, Uri parse) {
        buryingPointForHandleIting(parse.toString(), null);
        ITingHandler mHandler = new ITingHandler();
        return mHandler.handleITing(activity, parse);
    }

    @Override
    public boolean handleITing(Activity activity, Uri parse, String resPosition) {
        buryingPointForHandleIting(parse.toString(), null);
        ITingHandler mHandler = new ITingHandler();
        return mHandler.handleITing(activity, parse, resPosition);
    }

    @Override
    public boolean handleITing(Activity activity, PushModel model) {
        buryingPointForHandleIting(model.schema, model);
        ITingHandler mHandler = new ITingHandler();
        return mHandler.handleITing(activity, model);
    }

    @Override
    public String getPageNameByMsgType(int msgType) {
        return ITingHandler.getPageNameByMsgType(msgType);
    }

    @Override
    public IYaoyiYaoManager getYaoyiYaoManagerInstance(Context context) {
        return YaoyiYaoAdManage.getInstance(context);
    }

    @Override
    public void adRecord(Context context, Advertis thirdAd, String logType, String positonName) {
        AdManager.adRecord(context, thirdAd, logType, positonName);
    }

    @Override
    public void handlerAdClick(Context context, Advertis thirdAd, String logType, String positonName) {
        AdManager.handlerAdClick(context, thirdAd, positonName);
    }

    @Override
    public void handlerAdClick(Context context, Advertis thirdAd, String logType, String positonName, int categoryId,
                               int index) {
        AdManager.handlerAdClick(context, thirdAd, AdReportModel.newBuilder(logType, positonName).categoryId(categoryId).position(index).build());
    }

    @Override
    public void batchAdRecord(Context context, List<? extends Advertis> thirdAds, String logType, String positonName) {
        AdManager.batchAdRecord(context, thirdAds, logType, positonName);
    }

    @Override
    public void batchAdRecord(Context context, List<? extends Advertis> thirdAds, String logType, String positonName,
                              int categoryId) {
        AdManager.batchAdRecord(context, thirdAds, AdReportModel.newBuilder(logType, positonName).categoryId(categoryId).build());
    }

    @Override
    public BaseAlbumAdapter newAlbumAdapter(MainActivity activity, List<Album> listData) {
        return new AlbumAdapter(activity, listData);
    }

    @Override
    public void sendComment(String trackId, String content, String parentId, String second, int contentType, final IDataCallBack<CommentModel> callBack) {
        if (callBack == null) {
            return;
        }

        Map<String, String> paramMaps = new HashMap<>();
        paramMaps.put("trackId", trackId);

        if (!"".equals(content)) {
            paramMaps.put("content", content);
        }

        if (StringUtil.isNotBlank(parentId)) {
            paramMaps.put("parentId", parentId);
        }

        if (StringUtil.isNotBlank(second)) {
            paramMaps.put("second", second);
        }

        MainCommonRequest.sendComment(paramMaps, new IDataCallBack<CommentModel>() {
            @Override
            public void onSuccess(CommentModel object) {
                callBack.onSuccess(object);

//                if (object != null && object.ret == 0) {
//                    Activity topActivity = MainApplication.getTopActivity();
//                    if (topActivity instanceof FragmentActivity) {
//                        CommendSuccessHintPush.checkAndShowDialog((FragmentActivity) topActivity);
//                    }
//                }
            }

            @Override
            public void onError(int code, String message) {
                callBack.onError(code, message);
            }
        }, contentType);
    }

    @Override
    public boolean handleIting(Activity activity, Uri uri) {
        buryingPointForHandleIting(uri.toString(), null);
        ITingHandler handler = new ITingHandler();
        return handler.handleITing(activity, uri);
    }

    @Override
    public void putAdStateManagerAlearDownloadMap(String url, String filePath) {
        if (AdStateManage.alearDownloadMap == null) {
            AdStateManage.alearDownloadMap = new HashMap<>();
        }
        AdStateManage.alearDownloadMap.put(url, filePath);
    }

    @Override
    public void enterOtherUnlock(Activity activity, boolean autoClick) {
        CommonUtil.enterOtherUnlock(activity, autoClick);
    }

    @Override
    public void getUserFollower(int pageId, int pageSize, Integer type, Integer from,
                                final IDataCallBack<ListModeBase<Anchor>> dataCallBack) {
        Map<String, String> params = new HashMap<>();
        params.put("pageId", "" + pageId);
        params.put("pageSize", "" + pageSize);
        params.put("device", "android");

        MainCommonRequest.getUserFollower(params, new IDataCallBack<ListModeBase<Anchor>>() {
            @Override
            public void onSuccess(ListModeBase<Anchor> object) {
                dataCallBack.onSuccess(object);
            }

            @Override
            public void onError(int code, String message) {
                dataCallBack.onError(code, message);
            }
        }, type, from);
    }

    @Override
    public void getFocusAd(Context appContext, long categoryId, final IDataCallBack<List<BannerModel>> callBack) {
        if (appContext == null) {
            return;
        }

        final HashMap<String, String> maps = new HashMap<>();
        maps.put("xt", "" + System.currentTimeMillis());
        maps.put("scale", "2");
        maps.put("categoryId", "" + categoryId);
        maps.put("version", DeviceUtil.getVersion(appContext));
        maps.put("device", "android");
        maps.put("network", CommonRequestM.getInstanse().getNetWorkType());
        maps.put("operator", NetworkType.getOperator(appContext) + "");
        maps.put("deviceId", DeviceUtil.getDeviceToken(appContext));
        maps.put("appid", "0");

        AdRequest.getFocusAd(maps, new IDataCallBack<List<BannerModel>>() {
            @Override
            public void onSuccess(List<BannerModel> object) {
                callBack.onSuccess(object);
            }

            @Override
            public void onError(int code, String message) {
                callBack.onError(code, message);
            }
        });
    }

    @Override
    public void resetPlanTime(Context context) {
//        PlanTerminateFragment.resetPlanTime(context);
    }

    @Override
    public int getUnreadOfMySpace(Context context) {
        int result = 0;
        boolean isSettingRed = SettingFragment.hasRedIcon(context);
        if (isSettingRed) {
            result++;
        }
        return result;
    }

    @Override
    public void interceptAdAppDownload(Activity activity, String title, Advertis advertis, IHandleOk handleOk) {
        //
        if (advertis != null && advertis.isEnableDownloadPopUp()) {
            showDownloadDialogFromStyle(activity, advertis, handleOk);
        } else {
            if (advertis != null && !TextUtils.isEmpty(advertis.getRealLink()) && NetworkType.isConnectMOBILE(ToolUtil.getCtx())) {
                MyProgressDialog progressDialog = new MyProgressDialog(MainApplication.getOptActivity());
                progressDialog.delayShow();
                MyAsyncTask.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            URL url = new URL(advertis.getRealLink());
                            HttpURLConnection connection = new OkHttpURLConnection(url,
                                    BaseCall.getInstanse().getOkHttpClient());
                            connection.setRequestMethod("GET");
                            connection.setRequestProperty("User-Agent", DeviceUtil.getUserAgentByWebView(ToolUtil.getCtx()));
                            connection.setRequestProperty("Accept",
                                    "image/gif, image/jpeg, image/pjpeg, image/pjpeg, application/x-shockwave-flash, application/xaml+xml, application/vnd.ms-xpsdocument, application/x-ms-xbap, application/x-ms-application, application/vnd.ms-excel, application/vnd.ms-powerpoint, application/msword, */*");
                            connection.setRequestProperty("Accept-Language", "zh-CN");
                            connection.setRequestProperty("Charset", "UTF-8");

                            connection.setInstanceFollowRedirects(true);// 设置重定向问题
                            connection.setConnectTimeout(5000);

                            int status = connection.getResponseCode();
                            if (status == HttpURLConnection.HTTP_OK || status == HttpURLConnection.HTTP_PARTIAL) {
                                String filelen = connection.getHeaderField("Content-length");
                                String filerange = connection.getHeaderField("Content-Range");
                                if (!TextUtils.isEmpty(filerange)) {
                                    int index = filerange.lastIndexOf('/');
                                    filerange = filerange.substring(index + 1);
                                    filelen = filerange;
                                }

                                long file_len = 0;
                                try {
                                    file_len = Long.valueOf(filelen);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                                int maxApkSize =
                                        ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                                                CConstants.Group_ad.ITEM_MAX_APK_SIZE_TO_SHOW_DIALOG, 100);
                                Logger.log("MainActionImpl : maxApkSize " + maxApkSize + "   fileLen = " + filelen);
                                long finalFile_len = file_len;
                                HandlerManager.postOnUIThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        progressDialog.cancel();
                                        if (finalFile_len / 1024 / 1024 > maxApkSize) {
//                                            AdAppDownloadRemindDialog adAppDownloadRemindDialog = new AdAppDownloadRemindDialog(activity, title);
//                                            adAppDownloadRemindDialog.setOkHandle(handleOk);
//                                            adAppDownloadRemindDialog.setDialogAdRecordListener(getDialogRecordListener(advertis));
//                                            adAppDownloadRemindDialog.show();
                                            showDownloadDialogFromStyle(activity, advertis, handleOk);
                                        } else {
                                            handleGotoInstall(handleOk);
                                        }
                                    }
                                });
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            HandlerManager.postOnUIThread(new Runnable() {
                                @Override
                                public void run() {
                                    progressDialog.cancel();
                                    CustomToast.showFailToast("下载失败,请重新点击下载" + (ConstantsOpenSdk.isDebug ? e.getMessage() : ""));
                                }
                            });
                        }
                    }
                });
            } else {
                handleGotoInstall(handleOk);
            }
        }
    }

    private void showDownloadDialogFromStyle(Activity activity, Advertis advertis, IHandleOk handleOk) {
        if (advertis == null) {
            return;
        }
        if (advertis.getDownloadPopupStyle() == 1) {
            AdAppDownloadRemindDialog adAppDownloadRemindDialog = new AdAppDownloadRemindDialog(activity, advertis.getDownloadAppName());
            adAppDownloadRemindDialog.setOkHandle(handleOk);
            adAppDownloadRemindDialog.setDialogAdRecordListener(getDialogRecordListener(advertis));
            adAppDownloadRemindDialog.show();
        } else if (advertis.getDownloadPopupStyle() == 2) {
            AdAppDownloadStyle2Dialog style2Dialog = new AdAppDownloadStyle2Dialog(activity, advertis);
            style2Dialog.setOkHandle(handleOk);
            style2Dialog.setDialogAdRecordListener(getDialogRecordListener(advertis));
            style2Dialog.show();
        } else if (advertis.getDownloadPopupStyle() == 3) {
            AdAppDownloadStyle3Dialog style3Dialog = new AdAppDownloadStyle3Dialog(activity, advertis);
            style3Dialog.setOkHandle(handleOk);
            style3Dialog.setDialogAdRecordListener(getDialogRecordListener(advertis));
            style3Dialog.show();
        } else if (advertis.getDownloadPopupStyle() == 4) {
            AdAppDownloadStyle4Dialog style4Dialog = new AdAppDownloadStyle4Dialog(activity, advertis);
            style4Dialog.setOkHandle(handleOk);
            style4Dialog.setDialogAdRecordListener(getDialogRecordListener(advertis));
            style4Dialog.show();
        } else {
            // 样式4全量
            AdAppDownloadStyle4Dialog style4Dialog = new AdAppDownloadStyle4Dialog(activity, advertis);
            style4Dialog.setOkHandle(handleOk);
            style4Dialog.setDialogAdRecordListener(getDialogRecordListener(advertis));
            style4Dialog.show();
        }
    }

    /**
     * 下载弹窗各种状态的回调
     *
     * @param advertis
     * @return
     */
    private DownloadDialogStyle4AdRecordListener getDialogRecordListener(Advertis advertis) {

        return new DownloadDialogStyle4AdRecordListener() {

            @Override
            public void onDialogShow() {
                downloadRecord(advertis);
            }

            @Override
            public void onDialogClickOk() {
                if (advertis == null) {
                    return;
                }
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                // 其他情况都下载
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_DOWNLOAD_DIALOG_CLICK_OK, params);
            }

            @Override
            public void onDialogClickCancel() {
                if (advertis == null) {
                    return;
                }
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                // 其他情况都下载
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_DOWNLOAD_DIALOG_CLICK_CANCEL, params);
            }

            @Override
            public void onDialogBackPressed() {
                if (advertis == null) {
                    return;
                }
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                // 其他情况都下载
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_DOWNLOAD_DIALOG_BACK_PRESSED, params);
            }

            @Override
            public void onPrivacyClickListener() {
                if (advertis == null) {
                    return;
                }
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PRIVACY, params);
            }

            @Override
            public void onPermissionClickListener() {
                if (advertis == null) {
                    return;
                }
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PERMISSION, params);
            }
        };
    }


    private void handleGotoInstall(IHandleOk handleOk) {
        if (handleOk != null) {
//            CustomToast.showSuccessToast("开始为您下载应用,可在通知栏中查看下载进度");
            handleOk.onReady();
        }
    }

    // 亮屏重新安装埋点上报
    private void downloadRecord(Advertis advertis) {
        try {
            DownloadServiceManage.getInstance().recordDownloadState(advertis.getRealLink(), DownloadAdRecordManager.DOWNLOAD_EVENT_DOWNLOAD_DIALOG_SHOW,
                    new DownloadAdvertisParams(advertis, AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId())));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Dialog newAdAppDownloadRemindDialog(Activity activity, String title,
                                               IHandleOk handleOk, IHandleOk handleCancle) {
        AdAppDownloadRemindDialog adAppDownloadRemindDialog = new AdAppDownloadRemindDialog(activity, null);
        adAppDownloadRemindDialog.setTitle(title);
        adAppDownloadRemindDialog.setOkHandle(handleOk);
        adAppDownloadRemindDialog.setCancleHandle(handleCancle);
        return adAppDownloadRemindDialog;
    }

    @Override
    public AbstractHomePageFragment newHomePageFragment() {
        HomePageFragment homePageFragment = new HomePageFragment();
        homePageFragment.fid = Configure.MainFragmentFid.HOME_PAGE_FRAGMENT;
        return homePageFragment;
    }

    @Override
    public AbstractFindingFragment newFindingFragment() {
        return null;
    }

    @Override
    public BaseFragment2 newMyspaceFragment(final boolean isOpenOnSinglePage) {
        return new MineFragmentV9();
    }

    @Override
    public BaseFragment2 newMySpaceAndListenFragment() {
        return new MineFragmentV9();
    }

    /*@Override
    public BaseFragment2 newVipTabsFragment() {
        return new VipTabsFragment();
    }*/

    @Override
    public void getNextTrackInChannel(final Track track, final Context context) {
        //切换频道导致的切歌不算
        if (track == null || track.getChannelId() <= 0)
            return;

        final long channelId = track.getChannelId();
        final String channelName = track.getChannelName();

        List<Track> curTrackList = XmPlayerManager.getInstance(context).getPlayList();
        if (curTrackList != null && curTrackList.get(curTrackList.size() - 1) != null
                && curTrackList.get(curTrackList.size() - 1).getChannelId() != channelId)
            return;//非同一个频道不参与切歌请求
        if (curTrackList == null) {
            return;
        }
        int index = curTrackList.indexOf(track);
        Map<String, String> params = new HashMap<>();
        params.put("isFirst", false + "");
        params.put("unfinished", false + "");
        params.put("channelId", channelId + "");
        if (track.getAlbum() != null) {
            params.put("albumId", track.getAlbum().getAlbumId() + "");
        }
        params.put("ratio", track.getLastPlayedMills() * 0.001f / track.getDuration() + "");
        params.put("duration", track.getLastPlayedMills() * 0.001f + "");
        params.put("trackId", track.getDataId() + "");
        params.put("index", index + "");
        params.put("length", XmPlayerManager.getInstance(context).getPlayList().size() + "");

        MainCommonRequest.getOneKeyListenQuery(params, new IDataCallBack<List<OneKeyTrack>>() {
            @Override
            public void onSuccess(List<OneKeyTrack> data) {
                if (data == null) {
                    return;
                }

                Track curTrack = PlayTools.getCurTrack(context);
                List<Track> curTrackList = XmPlayerManager.getInstance(context).getPlayList();
                if (curTrackList == null) {
                    return;
                }
                int index = curTrackList.indexOf(curTrack);
                List<Track> tracks = new ArrayList<>();
                Logger.log("Hovi__new___________");
                for (OneKeyTrack oneKeyTrack : data) {
                    if (oneKeyTrack == null) {
                        continue;
                    }
                    Track track = oneKeyTrack.trackResult;
                    if (track == null) {
                        continue;
                    }
                    track.setChannelId(channelId);
                    track.setChannelName(channelName);
                    track.setPlaySource(ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY);
                    SubordinatedAlbum album = track.getAlbum();
                    if (album == null) {
                        album = new SubordinatedAlbum();
                    }
                    album.setRecSrc(oneKeyTrack.recSrc);
                    album.setRecTrack(oneKeyTrack.recTrack);
                    tracks.add(track);
                    Logger.log("Hovi_" + track.getTrackTitle());
                }
                Logger.log("Hovi__new___________");
                // 最多最近播放的6条声音(历史3条+正在播1条+即将播2条)
                // TODO: 2017/12/22 列表最多30条
                int start = (curTrackList.size() + data.size()) <= 30 ? 0 : curTrackList.size() + data.size() - 30;
                tracks.addAll(0, curTrackList.subList(start, curTrackList.size()));
                PlayTools.playList(context, tracks, tracks.indexOf(curTrack), false, null);
                processBuryingPoint(context);
            }

            @Override
            public void onError(int i, String s) {

            }
        });
    }

    private void processBuryingPoint(Context context) {
        // 埋点当前声音的播放
        XmPlayerManager playerManager = XmPlayerManager.getInstance(context);
        PlayableModel currSound = playerManager.getCurrSound();
        if (null != currSound && currSound instanceof Track) {
            Track curTrack = (Track) currSound;
            SubordinatedAlbum album = curTrack.getAlbum();
            if (null != album) {
                String recSrc = album.getRecSrc();
                String recTrack = album.getRecTrack();
                if (!TextUtils.isEmpty(recSrc) && !TextUtils.isEmpty(recTrack))
                    UserTrackCookie.getInstance().setXmRecContent(recTrack, recSrc);
            }
        }
    }

    /**
     * 跳转不知道类型的专辑页面
     *
     * @param activity          MainActivity 实例
     * @param albumId           专辑id
     * @param from              专辑打开来源 (默认 AlbumEventManage.FROM_OTHER)
     * @param playSource        专辑播放来源 (默认 ConstantOpenSDK.PLAY_FROM_OTHER)
     * @param recSrc            推荐字段
     * @param recTrack          推荐字段
     * @param unreadTrackNumber 专辑新增声音数（标new）默认-1
     */
    @Override
    public void startUnknownTypeAlbumFragment(MainActivity activity, long albumId, int from, int playSource, String recSrc, String recTrack, int unreadTrackNumber) {
        AlbumEventManage.startMatchAlbumFragment(albumId, from, playSource, recSrc, recTrack, unreadTrackNumber, activity);
    }

    @Override
    public boolean hasPermissionAndRequest(@NonNull Activity activity, @NonNull ISetRequestPermissionCallBack
            requestPermissionCallBack, @NonNull Map<String, Integer> permissions) {
        return PermissionManage.checkPermission(activity, requestPermissionCallBack, permissions, null);
    }

    @Override
    public void checkPermission(@NonNull Activity activity, @NonNull ISetRequestPermissionCallBack
            requestPermissionCallBack, @NonNull Map<String, Integer> permissions, IPermissionListener listener) {
        PermissionManage.checkPermission(activity, requestPermissionCallBack, permissions, listener);
    }

    @Override
    public void getSpecialTingList(Context appContext, long mSubjectId, IDataCallBack<ListModeBase<Track>> callBack) {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ID, mSubjectId + "");
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        MainCommonRequest.getSubjectTrackList(params, callBack);
    }

    @Override
    public void getShareAd(Map<String, String> params, IDataCallBack<List<Advertis>> callBackM) {
        AdRequest.getShareAd(params, callBackM);
    }

    @Override
    public void showAnchorSkillEntrance(@NonNull BaseFragment2 fra, @NonNull ViewGroup parentView,
                                        int entrance, @Nullable View.OnClickListener extraClickListener) {
        if (fra.getContext() != null) {
            new MagneticView(fra.getContext(), entrance).show(fra, extraClickListener);
        }
    }

    @Override
    public void showNotificationDialogForEveryDayUpdate(@NonNull BaseFragment2 frag) {
        NotificationOpenGuideDialog.showDialogForEveryDayUpdate(frag);
    }

    @Override
    public void showCommonBottomDialog(TrackM track, int options, final CommonBottomDialogUtilConstants.Listener listener) {
        CommonBottomDialogUtil.showTrackDialog(track, options, listener);
    }

    @Override
    public void dismisssMagneticView(@NonNull BaseFragment2 fra) {
        if (fra != null && fra.getView() != null) {
            ViewGroup parentView = (ViewGroup) fra.getView();
            for (int i = 0; i < parentView.getChildCount(); i++) {
                View child = parentView.getChildAt(i);
                if (child instanceof MagneticView) {
                    parentView.removeView(child);
                    return;
                }
            }
        }
    }

    @Override
    public void showChooseTrackQualityDialog(Context context, Track track, final IDataCallBack<Object> callBack) {
        final ActionCallBack internalCallback = new ActionCallBack() {
            @Override
            public void onConfirm() {
                if (callBack != null) {
                    callBack.onSuccess(null);
                }
            }

            @Override
            public void onCancel() {
                if (callBack != null) {
                    callBack.onError(-1, "");
                }
            }
        };
        if (context == null) {
            MainActivity mainActivity = (MainActivity) BaseApplication.getMainActivity();
            if (mainActivity != null) {
                context = mainActivity.getContext();
            }
        }
        ChooseTrackQualityDialog.newInstance(context, track, internalCallback).show();
    }

    @Override
    public boolean createQRImage(String content, int widthPix, int heightPix, int margin, Bitmap logoBm, String filePath) {
        return QRImageUtil.createQRImage(content, widthPix, heightPix, margin, logoBm, filePath);
    }

    @Override
    public boolean isAlbumAsc(Context context, long albumId) {
        return MainBundleUtil.isAlbumAsc(context, albumId);
    }

    @Override
    public boolean playListCompleteHint(@NonNull Context context, ICallback<Void> hintCompleteCallback) {
//        BaseHintPlayer baseHintPlayer = new ListCompleteHintPlayer(context, hintCompleteCallback);
        return false;
    }

    @Override
    public void communityShareDialog(Activity activity, int type, long communityId, long communityArticleId, ShareManager.Callback clickCallback) {
        ShareUtils.shareCommunity(activity, type, communityId, communityArticleId, clickCallback);
    }

    @Override
    public View starCommunityShareDialog(Activity activity, int type, long communityId, long communityArticleId, ShareManager.Callback clickCallback) {
        return ShareUtils.getShareCommunityView(activity, type, communityId, communityArticleId, clickCallback);
    }

    @Override
    public void shareDubbing(Activity activity, Track track, boolean isCallForAct) {
        shareDubbing(activity, track, isCallForAct, null);
    }

    @Override
    public void shareDubbing(Activity activity, Track track, boolean isCallForAct, ShareManager.Callback callback) {
        List<String> channelList = new ArrayList<>();
        channelList.add(IShareDstType.SHARE_TYPE_WX_CIRCLE);
        channelList.add(IShareDstType.SHARE_TYPE_WX_FRIEND);
        if (track.getTrackStatus() == 1) {
            channelList.add(IShareDstType.SHARE_TYPE_QQ);
            channelList.add(IShareDstType.SHARE_TYPE_QZONE);
            channelList.add(IShareDstType.SHARE_TYPE_SINA_WB);
        }
        String[] shareChannels = new String[channelList.size()];
        channelList.toArray(shareChannels);
        if (isCallForAct) {
            ShareUtilsInMain.shareTrack(activity, track,
                    ICustomShareContentType.SHARE_TYPE_TRACK_DUB_CALL_ACT, shareChannels, callback);
        } else {
            ShareUtilsInMain.shareTrack(activity, track,
                    ICustomShareContentType.SHARE_TYPE_TRACK_DUB, shareChannels, callback);
        }
    }

    @Override
    public void shareDubbing(Activity activity, ShareWrapContentModel model, boolean isCallForAct, ShareManager.Callback callback) {
        List<String> channelList = new ArrayList<>();
        boolean needSortItem = true;
        if ("DualDub".equals(model.fromPage)) {
            needSortItem = false;
            channelList.add(ShareConstants.SHARE_TYPE_TING_CIRCLE);
        }
        channelList.add(IShareDstType.SHARE_TYPE_WX_CIRCLE);
        channelList.add(IShareDstType.SHARE_TYPE_WX_FRIEND);
        if (model.isAddDownload) {
            //分享是否支持保存到相册功能
            channelList.add(ShareConstants.SHARE_TYPE_DOWNLOAD);
        }
        if ("dunInfo".equals(model.fromPage) || "VideoDubMixFinish".equals(model.fromPage) || "VideoDubAudition".equals(model.fromPage)) {
            //添加海报类型
            channelList.add(ShareConstants.SHARE_TYPE_CREATE_QR_CODE);
        }
        Track track = model.soundInfo;
        if (track.getTrackStatus() == 1) {
            channelList.add(IShareDstType.SHARE_TYPE_QQ);
            channelList.add(IShareDstType.SHARE_TYPE_QZONE);
            channelList.add(IShareDstType.SHARE_TYPE_SINA_WB);
        }
        String[] shareChannels = new String[channelList.size()];
        channelList.toArray(shareChannels);
        model.shareDstNames = shareChannels;
        if (isCallForAct) {
            model.customShareType = ICustomShareContentType.SHARE_TYPE_TRACK_DUB_CALL_ACT;
            ShareUtilsInMain.shareTrack(activity, track, needSortItem, model, callback);
        } else {
            model.customShareType = ICustomShareContentType.SHARE_TYPE_TRACK_DUB;
            if (callback == null) {
                ShareUtilsInMain.showShareDubbingDialog(activity, track, true, model, callback);
            } else {
                ShareUtilsInMain.shareTrack(activity, track, model, callback);
            }
        }
    }

    @Override
    public Class getDubShowPPTPlayFragmentClass() {
        return DubbingPlayFragmentNew.class;
    }

    @Override
    public boolean isSameTrackId(BaseFragment dubShowFragment, long toPlayTrackId) {
        if (dubShowFragment == null || !(dubShowFragment instanceof DubbingPlayFragmentNew)) {
            return false;
        }
        DubbingPlayFragmentNew fragment = (DubbingPlayFragmentNew) dubShowFragment;
        return fragment.getCurTrackId() == toPlayTrackId;
    }

    @Override
    public void playDubShow(Context context, View view, Bundle bundle, boolean isVideo) {
        PlayTools.checkToDubShowPPTPlayFragment(context, bundle, true, view);
    }


    @Override
    public void requestFindHomePageDataAndReturnCalabashView(final ViewGroup parentView, final ITomatoesClickCallback callback, final BaseFragment2 fragment2, final IDataCallBack<View> viewIDataCallBack, final IDataCallBack<Pair<Object, String>> dataCallBack, final String oldJson) {
        if (fragment2 == null || !fragment2.canUpdateUi() || fragment2.getActivity() == null) {
            if (viewIDataCallBack != null) {
                viewIDataCallBack.onError(-1, "");
            }
            return;
        }
        final Activity activity = fragment2.getActivity();
        Map<String, String> params = new ArrayMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_DEVICE, "android");
        params.put(HttpParamsConstantsInMain.PARAM_VERSION, DeviceUtil.getVersion(activity.getApplicationContext()));
        params.put(HttpParamsConstantsInMain.PARAM_CHANNEL, DeviceUtil.getChannelInApk(activity.getApplicationContext()));
        params.put(HttpParamsConstantsInMain.PARAM_CODE, SharedPreferencesUtil.getInstance(activity.getApplicationContext()).getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_CODE));
        MainCommonRequest.getFindRecTrackTabs(params, new IDataCallBack<FindHomePageModel>() {
            @Override
            public void onSuccess(@Nullable FindHomePageModel object) {
                if (!fragment2.canUpdateUi()) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(-1, "");
                    }
                    return;
                }
                if (object == null) {
                    if (!TextUtils.isEmpty(oldJson)) {
                        JSONObject jsonObject = null;
                        try {
                            jsonObject = new JSONObject(oldJson);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } finally {
                            if (jsonObject != null) {
                                object = new FindHomePageModel(jsonObject);
                            }
                        }
                    }
                }
                if (object == null || ToolUtil.isEmptyCollects(object.square)) {
                    if (viewIDataCallBack != null) {
                        viewIDataCallBack.onError(-1, "FindHomePageData null");
                    }
                    return;
                }
                CalabashAdapterProvider calabashAdapterProvider = new CalabashAdapterProvider(fragment2, false);
                calabashAdapterProvider.setTomatoesClickCallbackListener(callback);
                calabashAdapterProvider.setIsSingleLineStyle(object.square);
                calabashAdapterProvider.setFrom(CalabashLineAdapter.FROM_FIND_2);

                View convertView = calabashAdapterProvider.getView(LayoutInflater.from(activity), -1, parentView);
                HolderAdapter.BaseViewHolder holder = calabashAdapterProvider.buildHolder(convertView);

                ItemModel<List<RecommendDiscoveryM>> model = new ItemModel<>(object.square, -1);
                calabashAdapterProvider.bindViewDatas(holder, model, null, -1);
                if (viewIDataCallBack != null) {
                    viewIDataCallBack.onSuccess(convertView);
                }
                if (dataCallBack != null) {
                    dataCallBack.onSuccess(new Pair<Object, String>(object, object.json));
                }
            }

            @Override
            public void onError(int code, String message) {
                if (fragment2.canUpdateUi()) {
                    FindHomePageModel object = null;
                    if (!TextUtils.isEmpty(oldJson)) {
                        JSONObject jsonObject = null;
                        try {
                            jsonObject = new JSONObject(oldJson);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } finally {
                            if (jsonObject != null) {
                                object = new FindHomePageModel(jsonObject);
                            }
                        }
                    }
                    if (object != null) {
                        CalabashAdapterProvider calabashAdapterProvider = new CalabashAdapterProvider(fragment2, false);
                        calabashAdapterProvider.setIsSingleLineStyle(object.square);
                        calabashAdapterProvider.setFrom(CalabashLineAdapter.FROM_FIND_2);

                        View convertView = calabashAdapterProvider.getView(LayoutInflater.from(activity), -1, parentView);
                        HolderAdapter.BaseViewHolder holder = calabashAdapterProvider.buildHolder(convertView);

                        ItemModel<List<RecommendDiscoveryM>> model = new ItemModel<>(object.square, -1);
                        calabashAdapterProvider.bindViewDatas(holder, model, null, -1);
                        if (viewIDataCallBack != null) {
                            viewIDataCallBack.onSuccess(convertView);
                        }
                        if (dataCallBack != null) {
                            dataCallBack.onSuccess(new Pair<Object, String>(object, object.json));
                        }
                    } else {
                        if (viewIDataCallBack != null) {
                            viewIDataCallBack.onError(code, message);
                        }
                    }
                } else {
                    if (dataCallBack != null) {
                        dataCallBack.onError(-1, "");
                    }
                }
            }
        });
    }

    public void requestFindHomePageData(final BaseFragment2 fragment2, final IDataCallBack<String> dataCallBack, final String oldJson) {
        if (fragment2 == null || !fragment2.canUpdateUi() || fragment2.getActivity() == null) {
            if (dataCallBack != null) {
                dataCallBack.onError(-1, "");
            }
            return;
        }
        final Activity activity = fragment2.getActivity();
        Map<String, String> params = new ArrayMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_DEVICE, "android");
        params.put(HttpParamsConstantsInMain.PARAM_VERSION, DeviceUtil.getVersion(activity.getApplicationContext()));
        params.put(HttpParamsConstantsInMain.PARAM_CHANNEL, DeviceUtil.getChannelInApk(activity.getApplicationContext()));
        params.put(HttpParamsConstantsInMain.PARAM_CODE, SharedPreferencesUtil.getInstance(activity.getApplicationContext()).getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_CODE));
        MainCommonRequest.getFindRecTrackTabs(params, new IDataCallBack<FindHomePageModel>() {
            @Override
            public void onSuccess(@Nullable FindHomePageModel object) {
                if (!fragment2.canUpdateUi()) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(-1, "");
                    }
                    return;
                }
                if (object == null) {
                    if (!TextUtils.isEmpty(oldJson)) {
                        JSONObject jsonObject = null;
                        try {
                            jsonObject = new JSONObject(oldJson);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } finally {
                            if (jsonObject != null) {
                                object = new FindHomePageModel(jsonObject);
                            }
                        }
                    }
                }
                if (object == null || ToolUtil.isEmptyCollects(object.square)) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(-1, "FindHomePageData null");
                    }
                    return;
                }
                if (dataCallBack != null) {
                    dataCallBack.onSuccess(object.json);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (fragment2.canUpdateUi()) {
                    if (!TextUtils.isEmpty(oldJson)) {
                        dataCallBack.onSuccess(oldJson);
                    }
                } else {
                    if (dataCallBack != null) {
                        dataCallBack.onError(-1, "");
                    }
                }
            }
        });
    }

    public void dealFindHomePageDataClick(final BaseFragment2 fragment2, View view, String data, int position) {
        try {
            RecommendDiscoveryM recommendDiscoveryM = new RecommendDiscoveryM(new JSONObject(data));
            ArrayList<RecommendDiscoveryM> list = new ArrayList<>();
            list.add(recommendDiscoveryM);

            CalabashLineAdapter calabashLineAdapter = new CalabashLineAdapter(fragment2.getContext(), fragment2, list, CalabashLineAdapter.FROM_FIND_2);
            calabashLineAdapter.onClick(view, recommendDiscoveryM, position);

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void disLike(Map<String, String> params, final IDataCallBack<JSONObject> callBack) {
        MainCommonRequest.dislike(params, new IDataCallBack<JSONObject>() {
            @Override
            public void onSuccess(@Nullable JSONObject object) {
                if (callBack != null) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (callBack != null) {
                    callBack.onError(code, message);
                }
            }
        });
    }

    @Override
    public boolean isResumingRaidoContentFragment(BaseFragment fragment) {
        return fragment != null && fragment.getParentFragment() != null && fragment.getParentFragment() instanceof HomePageFragment &&
                ((HomePageFragment) fragment.getParentFragment()).isCurrTabClass(fragment.getClass());
    }

    @Override
    public boolean isRecommendFragmentVisible(Fragment fragment) {
        if (fragment instanceof HomePageFragment) {
            return ((HomePageFragment) fragment).isCurrTabClass(RecommendFragmentStaggered.class);
        }
        return false;
    }

    @Override
    public IRadioAdManager getRadioAdManager(IRadioFragmentAction.IRadioFragment radioFragment) {
        return new RadioAdManager(radioFragment);
    }

    @Override
    public IRadioAdManagerNew getRadioAdManagerNew(IRadioFragmentAction.IRadioFragmentNew radioFragment) {
        return new RadioAdManagerNew(radioFragment);
    }

    @Override
    @Deprecated
    public void requestAndShowUniversalPaymentCombineActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension) {
        requestAndShowUniversalPaymentCombineActionsDialog(fragment, source, albumId, paymentMaterial, orderContext, itemExtension, false);
    }

    @Override
    public void requestAndShowUniversalPaymentCombineActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension,boolean showAdFreeListen) {
        RequestMaterial requestMaterial = new RequestMaterial(albumId, source)
                .setOrderContext(orderContext).setItemExtension(itemExtension);
        if(albumId == 0){
            requestMaterial.setIndependent(false);
        }
        requestMaterial.setShowAdFreeListen(showAdFreeListen);
        requestMaterial.shutAutoPerform();
        if (null != paymentMaterial) {
            requestMaterial.setTrack(paymentMaterial.track).setActionOuterImp(paymentMaterial.getActionOuterImp());
        } else {
            requestMaterial.setActionOuterImp(UniversalPaymentActionsDialogHelper.getInstance());
        }
        UniversalPaymentActionsDialog.requestAndShowDialog(fragment, requestMaterial);
    }

    @Override
    public void requestAndShowUniversalPaymentCombineActionsDialogForITing(BaseFragment2 fragment, String source, long albumId, HashMap<String, String> transmissionParams, boolean showAdFreeListen, String locatedTab) {
        RequestMaterial requestMaterial = new RequestMaterial(albumId, source)
                .setIndependent(false)
                .setShowAdFreeListen(showAdFreeListen)
                .shutAutoPerform()
                .setTransmissionParams(transmissionParams)
                .setLocatedTab(locatedTab)
                .setActionOuterImp(UniversalPaymentActionsDialogHelper.getInstance());
        UniversalPaymentActionsDialog.requestAndShowDialog(fragment, requestMaterial);
    }

    @Override
    @Deprecated
    public void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension) {
        requestAndShowUniversalPaymentActionsDialog(fragment, source, albumId, paymentMaterial, orderContext, itemExtension, false);
    }

    @Override
    public void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, String source, long albumId, UniversalPayment.PaymentMaterial paymentMaterial, String orderContext, String itemExtension, boolean showAdFreeListen) {
        RequestMaterial requestMaterial = new RequestMaterial(albumId, source)
                .setOrderContext(orderContext).setItemExtension(itemExtension).setShowAdFreeListen(showAdFreeListen);
        requestMaterial.shutAutoPerform();
        if (null != paymentMaterial) {
            requestMaterial.setTrack(paymentMaterial.track).setActionOuterImp(paymentMaterial.getActionOuterImp());
        }
        UniversalPaymentActionsDialog.requestAndShowDialog(fragment, requestMaterial);
    }

    @Override
    public void requestAndShowUniversalPaymentActionsDialog(Track track, String source) {
        if (track == null) {
            return;
        }
        boolean sameTrackDialogShowing = UniversalPaymentActionsDialog.isSameTrackDialogShowing(track.getDataId());
        if (ConstantsOpenSdk.isDebug) {
            Log.d("PAUT_", "requestAndShowDialog, sameTrackDialogShowing: " + sameTrackDialogShowing);
        }
        if (!sameTrackDialogShowing) {
            UniversalPaymentActionsDialog.requestAndShowDialog(track, source);
        }
    }

    @Override
    public void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, Track track, String source, boolean isIndependent, boolean isManual) {
        if (track == null || fragment == null) {
            return;
        }
        boolean sameTrackDialogShowing = UniversalPaymentActionsDialog.isSameTrackDialogShowing(track.getDataId());

        if (!sameTrackDialogShowing) {
            if (TextUtils.isEmpty(source)) {
                source = UniversalPayment.SOURCE_AFTER_SAMPLE;
            }
            RequestMaterial requestMaterial;
            if (null == track.getAlbum()) {
                requestMaterial = new RequestMaterial(UniversalPayment.SOURCE_LOCAL_DEFINE_TYPE);
            } else {
                long albumId = track.getAlbum().getAlbumId();
                requestMaterial = new RequestMaterial(albumId, source)
                        .setTrack(track).setActionOuterImp(UniversalPaymentActionsDialogHelper.getInstance());
            }
            requestMaterial.setManual(isManual).setIndependent(isIndependent).shutAutoPerform();
            UniversalPaymentActionsDialog.requestAndShowDialog(fragment, requestMaterial);
        }
    }


    @Override
    public void requestAndShowUniversalPaymentActionsNonePurchaseDialog(BaseFragment2 fragment, String source, @Nullable String orderContext, @Nullable String itemExtension) {
        RequestMaterial requestMaterial = new RequestMaterial(source)
                .setOrderContext(orderContext).setItemExtension(itemExtension);
        requestMaterial.shutAutoPerform();
        UniversalPaymentActionsDialog.requestAndShowDialog(fragment, requestMaterial);
    }

    @Override
    public void requestAndShowUniversalPaymentActionsDialog(BaseFragment2 fragment, String source, long albumId, Track track, boolean showAdFreeListen) {
        if (albumId <= 0 && track != null) {
            if (track.getAlbum() != null) {
                albumId = track.getAlbum().getAlbumId();
            }
        }
        RequestMaterial requestMaterial = new RequestMaterial(albumId, source)
                .setTrack(track)
                .setActionOuterImp(UniversalPaymentActionsDialogHelper.getInstance())
                .setShowAdFreeListen(showAdFreeListen);
        requestMaterial.shutAutoPerform();
        UniversalPaymentActionsDialog.requestAndShowDialog(fragment, requestMaterial);
    }

    @Override
    public void showSingleAlbumBuyDialogWithForceReload(BaseFragment2 fragment, Album album) {
        UniversalPaymentUtil.buySingleAlbumForceReload(fragment, album);
    }

    @Override
    public boolean isAlbumFragmentNew(Fragment fragment) {
        //是否是专辑详情页面
        if (fragment == null) {
            return false;
        }
        return fragment instanceof AlbumFragmentNew2 || fragment instanceof AlbumFragmentNew3;
    }

    @Override
    public long getAlbumIdFromAlbumFragmentNew(Fragment fragment) {
        //判断是否是专辑详情页面，并从中获取专辑id
        if (fragment == null) {
            return -1;
        }
        if (fragment instanceof AlbumFragmentNew2) {
            return ((AlbumFragmentNew2) fragment).getAlbumId();
        } else if (fragment instanceof AlbumFragmentNew3) {
            return ((AlbumFragmentNew3) fragment).getAlbumId();
        }
        return -1;
    }

    @Override
    public void getVideoInfoById(long trackId, IDataCallBack<String[]> callBack, Track track) {
        MainCommonRequest.getVideoInfo(trackId, callBack, track);
    }

    @Override
    public boolean isInChildProtectFragmentFlow(Fragment fragment) {
        if (fragment != null) {
            //增加未成年人保护相关页面的话，需要在此处增加判断
            return fragment instanceof ChildProtectionPassWordFragment ||
                    fragment instanceof ChildProtectionSettingFragment ||
                    fragment instanceof ChildProtectionRemindFragment ||
                    fragment instanceof ChildProtectionForgetPwdFragment ||
                    fragment instanceof ChildProtectionAgeRangeSelectFragment;
        }
        return false;
    }

    @Override
    public boolean isInChildProtectTipFragment(Fragment fragment) {
        if (fragment != null) {
            //增加未成年人保护相关页面的话，需要在此处增加判断
            return fragment instanceof ChildProtectionPassWordFragment;
        }
        return false;
    }

    @Override
    public void downloadDubVideo(DubDownloadInfo info) {
        DubVideoDownloadTaskController.getInstance().startDownload(info);
    }

    @Override
    public void toOneKeyListen(Activity activity, boolean resetHeadlineTracks) {
        ITingHandlerUtil.toOneKeyListen(activity, resetHeadlineTracks);
    }

    @Override
    public void toOneKeyListen(Activity activity, long toChannelId, long toTrackId, boolean resetHeadlineTracks) {
        ITingHandlerUtil.toOneKeyListen(activity, toChannelId, toTrackId, resetHeadlineTracks);
    }

    @Override
    public void toOneKeyListen(Activity activity, long channelGroupId, long toChannelId, long toTrackId) {
        ITingHandlerUtil.toOneKeyListen(activity, channelGroupId, toChannelId, toTrackId);
    }

    @Override
    public void hideSearchDirectComment(BaseFragment fragment) {
        if (fragment instanceof SearchDirectCommentFragment) {
            ((SearchDirectCommentFragment) fragment).hideCommentView();
        }
    }

    @Override
    public boolean useNewAggregateRankPage() {
        return true;
    }

    @Override
    public void startRankDetailPage(int clusterType, int categoryId, long rankListId) {
        AggregateRankUtil.startRankDetailPage(clusterType, categoryId, rankListId);
    }

    @Override
    public void startRankHomePage() {
        AggregateRankUtil.gotoNewRankPage2Home();
    }

    @Override
    public boolean showMonthlyTicketVoteDialog(String source, long albumId, long anchorId, int initialTab) {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            VoteDialogFragment.Companion.show(((MainActivity) activity).getSupportFragmentManager(), source, 0L, albumId, anchorId, initialTab);
            return true;
        }
        return false;
    }

    @Override
    public IInputBar getInputBar(Activity activity) {
        return new CommentQuoraInputLayout(activity);
    }

    @Override
    public void getTingListDetailForPost(long albumId, IDataCallBack<AlbumListenNote> callback) {
        MainCommonRequest.getTingListDetailForPost(albumId, callback);
    }

    @Override
    public void requestTrackCommentLikeOrUnLike(long trackId, long commentId, boolean isLike, IDataCallBack<Boolean> callBack) {
        Map<String, String> params = new HashMap<>();
        long uid = UserInfoMannage.getUid();
        params.put("uid", uid + "");
        params.put("trackId", trackId + "");
        params.put("commentId", commentId + "");
        params.put("device", "android");
        params.put("islike", isLike + "");
        MainCommonRequest.commentLikeOrUnLike(params, callBack);
    }

    @Override
    public void requestAlbumCommentLikeOrUnLike(boolean isLike, long albumId, long commentId, long commentUid, IDataCallBack<Boolean> callback) {
        if (isLike) {
            MainCommonRequest.likeAlbumRate(albumId, commentId, commentUid, callback);
        } else {
            MainCommonRequest.unlikeAlbumRate(albumId, commentId, commentUid, callback);
        }
    }

    @Override
    public void requestTingdanCommentLikeOrUnLick(boolean isLike, long recordId, long commentId, IDataCallBack<Boolean> callback) {
        if (isLike) {
            MainCommonRequest.likeCommentCommon(recordId, commentId, callback);
        } else {
            MainCommonRequest.unlikeCommentCommon(recordId, commentId, callback);
        }
    }

    @Override
    public void requestDeleteFreeAlbumReply(long albumId, long replyId, IDataCallBack<Boolean> callback) {
        MainCommonRequest.deleteAlbumRateReply(albumId, replyId, callback);
    }

    @Override
    public void requestDeleteFreeAlbumComment(long albumId, long commentId, IDataCallBack<Boolean> callback) {
        MainCommonRequest.deleteAlbumRate(albumId, commentId, callback);
    }

    @Override
    public void requestDeleteAlbumReply(long albumId, long commentId, IDataCallBack<JSONObject> callback) {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
        params.put(HttpParamsConstants.PARAM_REPLY_ID, commentId + "");
        MainCommonRequest.deleteCommentReply(params, callback);
    }

    @Override
    public void requestDeleteTrackComment(long trackId, long commentID, IDataCallBack<Boolean> callback) {
        Map<String, String> specificParams = new HashMap<>();
        specificParams.put(HttpParamsConstants.PARAM_TRACK_ID, "" + trackId);
        specificParams.put("commentId", "" + commentID);
        specificParams.put(HttpParamsConstants.PARAM_DEVICE, "android");
        MainCommonRequest.commentDel(specificParams, callback);
    }

    @Override
    public void replyAlbumRate(long albumId, long commentId, String content, long commentUid, boolean syncType, IDataCallBack<AlbumCommentModel> callback) {
        MainCommonRequest.replyAlbumRate(albumId, commentId, content, commentUid, syncType, callback);
    }

    @Override
    public void sendTrackComment(long trackId, String content, boolean isSyncTing, EmotionSelector.InputInfo inputInfo, long parentId, IDataCallBack<CommentModel> callback) {
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put("uid", UserInfoMannage.getUid() + "");
        requestParams.put("token", UserInfoMannage.getToken());
        requestParams.put("trackId", "" + trackId);
        requestParams.put("synchaos", isSyncTing ? "1" : "0");
        if (parentId > 0) {
            requestParams.put("parentId", parentId + "");
        }
        if (inputInfo != null && inputInfo.imageInfo != null && inputInfo.imageInfo.picUrls != null && !inputInfo.imageInfo.picUrls.isEmpty()) {
            ImageUrl imageUrl = inputInfo.imageInfo.picUrls.get(0);
            String url = imageUrl.getOriginUrl();
            requestParams.put("pictureUrl", url);
        }
        requestParams.put("content", content);
        MainCommonRequest.sendComment(requestParams, callback, CommentConstants.COMMENT_TYPE_SEND_COMMENT);
    }

    @Override
    public void replyTingDanComment(long trackId, String content, long parentId, IDataCallBack<CommentModel> callback) {
        MainCommonRequest.createCommentCommon(trackId, CommentConstants.BUSINESS_TING_LIST, content, parentId, "", callback);
    }

    @Override
    public void isCommentPushSettingOpen(IDataCallBack<Boolean> callBack) {
        PushSettingFragment.isPushSettingOpen(PreferenceConstantsInHost.TINGMAIN_KEY_PUSH_COMMENT, callBack);
    }

    @Override
    public void setCommentPushSetting(boolean isPush) {
        PushSettingFragment.setPushSettingOpen(PreferenceConstantsInHost.TINGMAIN_KEY_PUSH_COMMENT, isPush);
    }

    @Override
    public void setReservePushSettingOpen(boolean onlyOpenReserve, boolean hasNoPermissionOriginal) {
        PushSettingFragment.setReservePushSettingOpen(onlyOpenReserve, hasNoPermissionOriginal);
    }

    @Override
    public void isLivePushSettingOpen(IDataCallBack<Boolean> callBack) {
        PushSettingFragment.isPushSettingOpen(PreferenceConstantsInHost.TINGMAIN_KEY_PUSH_LIVE, callBack);
    }


    @Override
    public void isPushSettingOpen(String pushKey, IDataCallBack<Boolean> callBack) {
        PushSettingFragment.isPushSettingOpen(pushKey, callBack);
    }

    @Override
    public void isPushSettingOpenIncludeLogoutState(String pushKey, IDataCallBack<Boolean> callBack) {
        PushSettingFragment.isPushSettingOpenIncludeLogoutState(pushKey, callBack);
    }

    @Override
    public void setPushSettingOpenIncludeLogoutState(String pushKey, boolean isPush) {
        PushSettingFragment.setPushSettingOpenIncludeLogoutState(pushKey, isPush);
    }


    @Override
    public void setLivePushSetting(boolean isPush) {
        PushSettingFragment.setPushSettingOpen(PreferenceConstantsInHost.TINGMAIN_KEY_PUSH_LIVE, isPush);
    }


    @Override
    public void showSpringEventReminder(boolean showDialog) {

    }

    @Override
    @WorkerThread
    public void deleteShortContentCacheFile() {
        ShortContentDirManager.cleanBasePathFiles();
    }

    public void showDownloadKaChaFragment(FragmentManager fragmentManager, DownloadKaChaBean bean) {
        KachaSaveLocalDialogFragment.showPop(fragmentManager, bean);
    }

    @Override
    public SharedPreferencesUtil getOnlyUseMainProcessSharePre(Context context) {
        return OnlyUseMainProcessSharePreUtil.getInstance(context);
    }

    @Override
    public void openWeChatLiteApp(Context context, String appId, String path, int type,
                                  IDataCallBack callBack) {
        WeChatUtil.openWeChatLiteApp(context, appId, path, type, callBack);
    }

    public void collectTingList(long id, int type, IDataCallBack<Boolean> callback) {
        MainCommonRequest.collectTingList(id, type, callback);
    }

    @Override
    public void cancelCollectTingList(long id, IDataCallBack<Boolean> callback) {
        MainCommonRequest.cancelCollectTingList(id, callback);
    }

    public void showFeedDislikeBubbleDialog(final Activity activity, List<BaseDialogModel> data, IFeedBubbleItemClickListener listener, View view) {
        FeedBubblePopupWindow popWindow = new FeedBubblePopupWindow(activity, data, listener);
        if (activity != null && activity.getWindow() != null) {
            Window window = activity.getWindow();
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.alpha = 0.6f; //设置透明度
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.setAttributes(lp);
        }
        popWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (activity != null && activity.getWindow() != null) {
                    Window window = activity.getWindow();
                    WindowManager.LayoutParams lp = window.getAttributes();
                    lp.alpha = 1f;
                    window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
                    window.setAttributes(lp);
                }
            }
        });
        popWindow.showAtLocation(view);

    }

    @Override
    public IFloatingPlayControlComponent newFloatingPlayControlComponent(BaseFragment2 fragment
            , IFloatingPlayControlComponent.IFloatingControlBarActionListener listener) {
        return new FloatingControlBarComponent(fragment, listener, false);
    }

    @Override
    public boolean isNewPlayFragment() {
        return PlayFragmentAbManager.getInstance().isNewPlayFragment();
    }

    @Override
    public boolean isPlayPagePlayingVideoWhilePaused() {
        return isNewPlayFragment() && PlayFragmentNew.sIsPlayingVideoWhilePaused;
    }

    @Override
    public void tryToGetUserMission() {
        NewUserManager.getInstance().tryToGetNewUserMission(false);
    }

    @Override
    public BaseFragment2 goScan() {
        QRCodeScanFragment qrCodeScanFragment = new QRCodeScanFragment();
        return qrCodeScanFragment;
    }

    @Override
    public void operateH5Dialog(String operation, String target) {
        H5DialogManager.getInstance().handleOperation(operation, target);
    }

    /**
     * 老年模式获取账号页功能入口adapter
     */
    @Override
    public IMulitViewTypeAdapter getMultiTypeAdapter(int type, IBaseMySpaceView baseMySpaceView) {
        Map<Integer, IMulitViewTypeViewAndData> map = new HashMap<Integer, IMulitViewTypeViewAndData>() {
            {
                put(type, new ToolsAdapterProvider(baseMySpaceView));
            }
        };
        IMulitViewTypeAdapter baseAdapter = new MulitViewTypeAdapter(baseMySpaceView.getActivity(), map);
        return baseAdapter;
    }

    @Override
    public void showXimiGuideDialog(String ximiUrl) {
        XimiUtil.showXimiGuideDialog(BaseApplication.getMainActivity(), ximiUrl, null);
    }

    @Override
    public void addOrRemoveTempoListener(boolean add, IBaseTempoListener iBaseTempoListener) {
        TempoManager.getInstance().setIBaseTempoListener(add ? iBaseTempoListener : null);
    }

    @Override
    public boolean showVipGuideView(BaseFragment2 frag, ViewGroup parent, AlbumM album) {
        return AlbumMiddleBarUtil.showVipGuideView(frag, parent, album);
    }

    @Override
    public Pair<Float, String> updateTempo() {
        return new Pair<>(TempoManager.getInstance().getCurrentTempo(),
                TempoManager.getInstance().getCurrentTempoStr());
    }

    @Override
    public void showTempoDialog(Context context) {
        showTempoDialog(context, -1);
    }

    @Override
    public void showTempoDialog(Context context, int animalStyle) {
        TempoManager.getInstance().showConfigDialog(context, false, true, null, animalStyle);
    }

    @Override
    public float getTempo() {
        return TempoManager.getInstance().getCurrentTempo();
    }

    @Override
    public void setTempoByNum(float tempoNum) {
        TempoManager.getInstance().setTempoByNum(tempoNum);
    }

    @Override
    public void setTempoIndexYPage(float tempo) {
        TempoManager.getInstance().setTempoIndexYPage(tempo, false);
    }

    @Override
    public void showVipFloatPurchaseDialog(BaseFragment2 fragment, String h5Url) {
        VipFloatPurchaseDialog.show(fragment, h5Url);
    }

    @Override
    public void mineEntranceSyncHomePageEntrance() {
        MainEntranceApiManage.getInstance().syncHomePageEntrance();
    }

    @Override
    public void loadLocalModelItemAndHomePageModel(ILoadMineDataCallBack.LoadMinePageDataCallback loadMinePageDataCallback) {
        MainEntranceApiManage.getInstance().loadLocalModelItemAndHomePageModel(loadMinePageDataCallback);
    }

    @Override
    public void loadModuleItemInfoList(ILoadMineDataCallBack.LoadDataFinishCallback<List<MineModuleItemInfo>> callback) {
        MainEntranceApiManage.getInstance().loadModuleItemInfoList(callback);
    }

    @Override
    public String getSpKeyMySpaceFragmentHomeModel() {
        return MainEntranceApiManage.MySpaceFragment_HomeModel;
    }

    @Override
    public void showFreshGiftFragment(FragmentManager fragmentManager) {
        try {
            FreshGiftFragment fragment = new FreshGiftFragment();
            fragment.showFreshGift(fragmentManager, "fresh_gift");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean audioPlayPageIsLargeDevice() {
        return AudioPlayPageAdaptationUtilKt.isLargeDevice();
    }

    @Override
    public IPlayAdEngine getPlayAdEngine(IAdEngineProviderExtend adEngineProvider) {
        return new XAudioPlayCoverAdEngine(adEngineProvider);
    }

    @Override
    public Class getBoughtAlbumAdapter() {
        return BoughtAlbumAdapter.class;
    }

    @Override
    public void setBoughtAdapterListener(HolderAdapter adapter, IAlbumCallback callback) {
        if (adapter instanceof BoughtAlbumAdapter) {
            ((BoughtAlbumAdapter) adapter).setAlbumCallback(callback);
        }
    }


    @Override
    public void startBuyFragment(BaseFragment2 fra2, AlbumM albumM, IFragmentFinish callbackFinish) {
        BuyAlbumFragment.buyAlbum(fra2, albumM, null, WholeAlbumRnBuyParamsUtil.FROM_PAGE_PRESALE, callbackFinish);
    }

    @Override
    public BaseDialogFragment newPlayNoCopyRightDialog() {
        return new PlayNoCopyRightDialog();
    }

    @Override
    public BaseDialogFragment newPlayNoCopyRightDialog(long trackId, String recSrc, String recTrack) {
        return new PlayNoCopyRightDialog();
    }

    @Override
    public void setListenTaskManagerTaskClick(boolean clicked) {
        ListenTaskManager.getInstance().setOnListenTaskClick(clicked);
    }

    @Override
    public void preloadBigScreenAd(boolean isColdLoad) {
        BigScreenAdManager.getInstance().loadBigScreenAd(isColdLoad);
    }

    @Override
    public void shareMyTrack(Activity activity, Track track, int shareType, int subType) {
        String plcBubble = "";
        // 有PLC气泡时，点击上报，并去掉气泡
        if (track != null && !TextUtils.isEmpty(track.getPlcBubbleTip())) {
            plcBubble = track.getPlcBubbleTip();
            track.setPlcBubbleTip("");
            PLCShareManager.INSTANCE.uploadClickPLCShare(track.getDataId(), PLCShareManager.SRC_PAGE_MY_WORKS);
        }
        ShareUtilsInMain.shareMyTrack(activity, track, shareType, subType);
        PLCShareManager.INSTANCE.traceOnMyWorksPLCShareClick(track == null ? null : track.getDataId(), plcBubble, subType);
    }

    @Override
    public boolean checkAnchorShareDialogAB() {
        return PlayShareDialogAbManager.INSTANCE.usePLCShareDialog();
    }

    @Override
    public void showNickNameSettingDialog(BaseFragment2 fragment, int source, NickNameSettingManager.INickNameDialogListener listener) {
        if (fragment == null) {
            return;
        }

        NickNameSettingDialogFragment dialog = NickNameSettingDialogFragment.Companion.newInstance(source);
        if (listener != null) {
            dialog.setNickNameDialogListener(listener);
        }
        if (!dialog.isVisible()) {
            dialog.show(fragment.getChildFragmentManager(), "NickNameSettingDialogFragment");
        }
    }

    @Override
    public void tryToCommitMission(int type, Map<String, Object> params) {
        NewUserManager.getInstance().tryToCommitMission(type, params);
    }

    @Override
    public void showVipPageDislikeMenu(BaseFragment2 fragment, Track track, Map<String, String> defaultReasons, Map<String, String> traitsReasons, IDataCallBack<Object> callBack, boolean isFromVip) {
        DislikeReasonModel dislikeReasonModel = DislikeReasonModel.parse(defaultReasons, traitsReasons);
        TrackActionUtil.showBottomMenu(fragment, track, dislikeReasonModel, new TrackActionUtil.ITrackBottomMenuActionListener() {
            @Override
            public void onAddToTingList() {

            }

            @Override
            public void onViewAlbum() {

            }
        }, "VipFeed", new IDataCallBack<DislikeReasonNew>() {
            @Override
            public void onSuccess(@Nullable DislikeReasonNew object) {
                if (null != callBack) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (null != callBack) {
                    callBack.onError(code, message);
                }
            }
        }, isFromVip);
    }

    @Override
    public void showVipPageDislikeMenu(BaseFragment2 fragment, AlbumM album, Map<String, String> defaultReasons, Map<String, String> traitsReasons, IDataCallBack<Object> callBack, boolean isFromVip) {
        DislikeReasonModel dislikeReasonModel = DislikeReasonModel.parse(defaultReasons, traitsReasons);
        AlbumActionUtil.showBottomMenu(fragment, album, "VipFeed", dislikeReasonModel, new IDataCallBack<DislikeReasonNew>() {
            @Override
            public void onSuccess(@Nullable DislikeReasonNew object) {
                if (null != callBack) {
                    callBack.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (null != callBack) {
                    callBack.onError(code, message);
                }
            }
        }, isFromVip);
    }

    @Override
    public Class newMyPrintFragment() {
        return MyFootPrintFragmentNew.class;
    }

    @Override
    public Class newMyPrintFragmentV2() {
        return MyFootPrintFragmentV2.class;
    }

    @Override
    public void startVideoPlayPage(@NonNull BaseFragment2 fromFragment, Bundle args, View fromView) {
        boolean jumpByPlaypageService = false;
        IPlayFragmentService playFragmentService =
                PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
        if (playFragmentService != null) {
            jumpByPlaypageService = playFragmentService.jumpToVideo(args);
        }
        if (!jumpByPlaypageService) {
            fromFragment.showPlayFragment(fromFragment.getView(), args, PlayerManager.PLAY_TAG);
        }
    }

    @Override
    public void startVideoPlayPage(@NonNull MainActivity mainActivity, Bundle args, View fromView) {
        Logger.e("z_short_play", "videoPlay  startVideoPlayPage >>>");

        boolean jumpByPlaypageService = false;
        IPlayFragmentService playFragmentService =
                PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
        if (playFragmentService != null) {
            jumpByPlaypageService = playFragmentService.jumpToVideo(args);
        }
        if (!jumpByPlaypageService) {
            mainActivity.showPlayFragment(fromView, args, PlayerManager.PLAY_TAG);
        }
    }

    @Override
    public void startPlayletPlayPage(@NonNull BaseFragment2 baseFragment2, Context context, long albumId) {
        SavePlayletPlayEpisodeEntity savePlayletPlayEpisodeEntity = new PlayletPlayEpisodeUtil(context).playEpisodeNum(albumId);
        PlayletDetailFragment detailFragment = PlayletDetailFragment.newInstance(true, savePlayletPlayEpisodeEntity != null ? savePlayletPlayEpisodeEntity.trackId : -1, albumId, 1, savePlayletPlayEpisodeEntity == null || savePlayletPlayEpisodeEntity.orderNum == 1 ? true : false);
        baseFragment2.startFragment(detailFragment);
    }

    @Override
    public void showShareTipDialogFragment(BaseFragment2 fragment) {
        if (null == fragment) {
            return;
        }
        ShareTipDailogFragment.newInstance().show(fragment.getChildFragmentManager(), ShareTipDailogFragment.TAG);
    }

    @Override
    public void showAlbumOperationPanel(BaseFragment2 fragment, AlbumM album) {
        if (null == album) {
            return;
        }
        AlbumOperationPanelDialog.showDialog(fragment, album);
    }

    @Override
    public void loadHomeTouchAd(int categoryId, BaseFragment2 fragment) {
        CategoryRecommendAdUtil adUtil = new CategoryRecommendAdUtil();
        adUtil.removeAllBroadsideAd(fragment);
        adUtil.loadAdData(categoryId, fragment);
    }

    @Override
    public void engageBuyAction(BaseFragment2 fragment, AlbumM album, IFragmentFinish finishCallBack, IAction.ICallback<Boolean> actionCallBack) {
        if (null == album) {
            return;
        }
        BuyActionUtil.engageBuy(fragment, album, finishCallBack, actionCallBack);
    }

    @Override
    public void requireSubscribeRecommendFragmentData(long albumId, final IDataCallBack<Object> dataCallBack) {
        SubscribeRecommendFragment.doSubscribeRecommendRequestWithoutLocalSubscribes(
                albumId, BaseApplication.getMyApplicationContext(),
                new IDataCallBack<SubscribeRecommendAlbumMListWithDescription>() {
                    @Override
                    public void onSuccess(@Nullable SubscribeRecommendAlbumMListWithDescription data) {
                        if (null != dataCallBack) {
                            dataCallBack.onSuccess(data);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (null != dataCallBack) {
                            dataCallBack.onError(code, message);
                        }
                    }
                });
    }

    @Override
    public boolean isMineSubscribePageLoading() {
        return MineSubscribePageLoadingOptimizationManager.INSTANCE.isMineSubscribePageLoading();
    }

    @Override
    public void clearTangramBitmapCache() {
        TangramManager.INSTANCE.clearCache();
    }

    @Override
    public void startFragmentOnPlayPage(BaseFragment2 fragment) {
        IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
        if (service != null) {
            service.startFragmentOnPlayPage(fragment);
        }
    }

    @Override
    public void removeFragmentOnPlayPage(BaseFragment2 fragment) {
        IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
        if (service != null) {
            service.removeFragment(fragment);
        }
    }

    @Override
    public void startTimelineCardFragmentOnPlayPage(boolean needAnim, int targetBizType, long targetBizId) {
        Activity activity = BaseApplication.getTopActivity();
        if (!(activity instanceof MainActivity)) {
            return;
        }
        if (((MainActivity) activity).isPlayFragmentVisible()) {
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
                PlayingSoundInfo soundInfo = PlayPageDataManager.getInstance().getSoundInfo();
                if (soundInfo == null || soundInfo.trackInfo == null || soundInfo.albumInfo == null || soundInfo.userInfo == null) {
                    return;
                }
            }
        }
    }

    @Override
    public void startVotePanelFragment(long trackId, long voteId, int srcPage, boolean showKeyboard,
                                       long anchorCommentId, @Nullable Runnable actionOnDismiss) {
        Activity activity = BaseApplication.getTopActivity();
        if (!(activity instanceof MainActivity)) {
            return;
        }
        if (((MainActivity) activity).isPlayFragmentVisible()) {
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
//                FloatingTrackCommentFragment fragment = service.getChildFragment(FloatingTrackCommentFragment.class);
//                if (fragment == null || fragment.getListType() != CommentListView.TYPE_VOTE_COMMENT
//                        || fragment.getVoteId() != voteId) {
//                    fragment = FloatingTrackCommentFragment.newInstanceForVote(trackId, voteId, srcPage, showKeyboard, anchorCommentId);
//                }
//                fragment.setOnDismissListener(new IFloatingFragmentDismissListener() {
//                    @Override
//                    public void onDismiss(@NonNull BaseFragment2 floatingFragment) {
//                        service.hideFragment(floatingFragment);
//                        if (actionOnDismiss != null) {
//                            actionOnDismiss.run();
//                        }
//                    }
//                });
//                service.showFragmentOnPlayPage(fragment);
                FloatingTrackCommentFragment floatingTrackCommentFragment =
                        FloatingTrackCommentFragment.newInstanceForVote(trackId, voteId, srcPage,
                                showKeyboard, anchorCommentId);
                floatingTrackCommentFragment.setOnDismissListener(new IFloatingFragmentDismissListener() {
                    @Override
                    public void onDismiss(@NonNull BaseFragment2 floatingFragment) {
                        service.removeFragment(floatingFragment);
                        if (actionOnDismiss != null) {
                            actionOnDismiss.run();
                        }
                    }
                });
                service.startFragmentOnPlayPage(floatingTrackCommentFragment);
            }
        } else {
            ((MainActivity) activity).startFragment(FloatingTrackCommentFragment.newInstanceForVote(
                    trackId, voteId, srcPage, showKeyboard, anchorCommentId),
                    com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom, com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
        }
    }

    public void startSequentialVotePanelFragment(long trackId, long sequentialVoteId, int srcPage, @Nullable Runnable finishCallback) {
        InteractiveCardUtil.startSequentialVotePanelFragment(trackId, sequentialVoteId, srcPage, finishCallback);
    }

    @Override
    public void startVideoCommentPanel(long videoId, @Nullable IFragmentFinish finishCallback) {
        Activity activity = BaseApplication.getTopActivity();
        if (!(activity instanceof MainActivity)) {
            return;
        }
        FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForVideo(videoId);
        fragment.setCallbackFinish(finishCallback);
        ((MainActivity) activity).startFragment(
                fragment,
                com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom,
                com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
    }

    @Override
    public boolean isAlbumIdInLocalWhiteList(long albumId) {
        return CommentAbFilter.isAlbumIdInWhiteList(albumId);
    }

    @Override
    public void showMorePanelDialog(BaseFragment2 fragment, int source, TrackM trackM) {
        MoreActionDialogV2 dialogV2 = new MoreActionDialogV2(fragment, source);
        if (trackM.isTts()) {
            dialogV2.setHideTrackDownloadEntrance(true);
        }
        dialogV2.setTrack(trackM);
        dialogV2.toggleMoreOperationPanel();
    }

    @Override
    public void notifyUserGiveUpAdUnlock(boolean manual) {
        UniversalPaymentActionsDialogHelper.getInstance().notifyUserGiveUpAdUnlock(manual);
    }

    @Override
    public boolean hasPlayPermission(long trackId) {
        return PlayPageDataManager.getInstance().checkFriendVisible(trackId);
    }

    @Override
    public void showNewUserDialogWithCheck(AlbumM album, boolean isOldAdUnlock) {
        FreeListenManager.getInstance().showNewUserDialogWithCheck(album, isOldAdUnlock);
    }

    @Override
    public void showVoteDialog(long trackId, long albumId, long anchorId) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                Activity activity = BaseApplication.getTopActivity();
                if (activity instanceof MainActivity && BaseUtil.isForegroundIsMyApplication(activity)) {
                    VoteDialogFragment.show(
                            ((MainActivity) activity).getSupportFragmentManager(),
                            "", trackId, albumId, anchorId
                    );
                }
            }
        });
    }

    @Override
    public void showRnCommonDialog(RnConfirmParam rnConfirmParam, IPromiseCallback callback) {
        if (rnConfirmParam != null) {
            Activity activity = BaseApplication.getTopActivity();
            if (activity instanceof MainActivity && BaseUtil.isForegroundIsMyApplication(activity)) {
                RnCommonDialog rnCommonDialog = new RnCommonDialog(activity, rnConfirmParam, callback);
                rnCommonDialog.show();
            }
        }
    }

    @Override
    public void showVoteDialogWithInitialTab(String sourcePage, long trackId, long albumId, long anchorId, int initialTabIndex) {
        Activity activity = BaseApplication.getTopActivity();
        if (activity instanceof MainActivity && BaseUtil.isForegroundIsMyApplication(activity)) {
            VoteDialogFragment.show(
                    ((MainActivity) activity).getSupportFragmentManager(),
                    sourcePage, trackId, albumId, anchorId, initialTabIndex
            );
        }
    }

    @Override
    public Object getPlayPageData() {
        PlayingSoundInfo soundInfo = PlayPageDataManager.getInstance().getSoundInfo();
        if (soundInfo != null) {
            return PlayPageDataManager.getInstance().getJsonObject(soundInfo);
        }
        return null;
    }

    @Override
    public PlayingSoundInfo getPlayPageSoundInfoData() {
        return PlayPageDataManager.getInstance().getSoundInfo();
    }

    @Override
    public int getPlayType() {
        XPlayPage playPage = XPlayPageRef.get();
        if (playPage != null) {
            return playPage.isAudioMode() ? 0 : 1;
        }
        return 0;
    }

    @Override
    public int getPlayType(IRNFunctionRouter.IRNEmbeddedView embeddedView) {
        if (embeddedView != null && embeddedView.getParentContainer() != null) {
            Fragment rnFragment = embeddedView.getParentContainer();
            Fragment playFragment = null;
            if (rnFragment != null) playFragment = rnFragment.getParentFragment();
            if (playFragment instanceof YPlayFragment) {
                return ((YPlayFragment)playFragment).isAudioMode() ? 0 : 1;
            }
        }
        return 0;
    }

    @Override
    public boolean isXPlayFragmentRealVisible() {
        XPlayPage playPage = XPlayPageRef.get();
        if (playPage != null && playPage instanceof BasePlayPageTabFragment) {
            return ((BasePlayPageTabFragment) playPage).isRealVisible();
        }
        return false;
    }

    @Override
    public int isFreeUnlockTrack() {
        return PlayPageDownloadUtils.INSTANCE.isFreeUnlockTrack() ? 1 : 0;
    }

    @Override
    public String getPlaypageColor() {
        int color = PlayPageDataManager.DEFAULT_BACKGROUND_COLOR;

        XPlayPage playPage = XPlayPageRef.get();
        if (playPage != null) {
            color = playPage.getRightColor();
        }

        //获取对应的三色

        int red = (color & 0xff0000) >> 16;
        int green = (color & 0x00ff00) >> 8;
        int blue = (color & 0x0000ff);
        return "rgb(" + red + "," + green + "," + blue + ")";
    }

    @Override
    public BaseDialogFragment tryToShowFreeListenEndGuideToNextAdDialog(Track track) {
        return FreeListenEndGuideToNextAdDialog.showDialog(track);
    }

    @Override
    public String getPlayPageViewData() {
        PlayPageMinorData minorData = PlayPageMinorDataManager.getInstance().getPlayPageMinorData();
        if (minorData != null) {
            return GsonUtils.toJson(minorData);
        }
        return null;
    }

    @Override
    public String getXPlayPageCurrentTabInfo(IRNFunctionRouter.IRNEmbeddedView embeddedView) {
        if (embeddedView != null && embeddedView.getParentContainer() != null) {
            Fragment rnFragment = embeddedView.getParentContainer();
            if (rnFragment instanceof YRnComponent) {
                com.ximalaya.ting.android.main.playpage.playy.tabs.XPlayPageTab tabInfo = ((YRnComponent) rnFragment).getTabInfo();
                if (tabInfo != null && tabInfo.getRawData() != null) {
                    return GsonUtils.toJson(tabInfo.getRawData());
                }
            }
        }
        return null;
    }

    @Override
    public PlayPageBridgeResult showFollowSnackBarIfNeed(long trackId, int anchorRewardStatus, String anchorRewardUrl) {
        if (XPlayPageRef.get() == null || XPlayPageRef.get().isFullScreen()) {
            return PlayPageBridgeResult.RESULT_INVALID_PAGE;
        }
        boolean disableAnchorReward = anchorRewardStatus != AnchorRewardInfo.REWARD_OPEN || TextUtils.isEmpty(anchorRewardUrl);
        InteractiveFeedbackBar.FeedbackBarType barType = disableAnchorReward ? InteractiveFeedbackBar.FeedbackBarType.BAR_TYPE_FOLLOW_NO_REWARD : InteractiveFeedbackBar.FeedbackBarType.BAR_TYPE_FOLLOW;
        return InteractiveFeedbackBar.showForRNPlayPage(barType, trackId, BottomViewNew.BOTTOM_VIEW_FROM_PAGE_AUDIO, (BaseFragment2) PlayerManager.getInstanse().getCurrentFragment(), popWindow -> {
            if (PlayerManager.getInstanse().getCurrentFragment() != null && PlayerManager.getInstanse().getCurrentFragment().canUpdateUi()) {
                if (anchorRewardStatus == AnchorRewardInfo.REWARD_OPEN && anchorRewardUrl != null && anchorRewardUrl.startsWith("iting://")) {
                    if (ChildProtectManager.isChildProtectOpen(ToolUtil.getCtx())) {
                        //未成年模式不支持打赏
                        return;
                    }
                    try {
                        Uri uri = Uri.parse(anchorRewardUrl);
                        if (uri.getQueryParameterNames() != null && !uri.getQueryParameterNames().contains("targetId")) {
                            String newRewardIting = anchorRewardUrl + (anchorRewardUrl.contains("?") ? "&targetId=" + trackId : "?targetId=" + trackId);
                            ((MainActionRouter) Router.getActionRouter(Configure.BUNDLE_MAIN)).getFunctionAction()
                                    .handleIting(PlayerManager.getInstanse().getCurrentFragment().getActivity(), Uri.parse(newRewardIting));
                        } else {
                            ((MainActionRouter) Router.getActionRouter(Configure.BUNDLE_MAIN)).getFunctionAction()
                                    .handleIting(PlayerManager.getInstanse().getCurrentFragment().getActivity(), uri);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }) ? PlayPageBridgeResult.RESULT_SUCCESS : PlayPageBridgeResult.RESULT_INVALID_TRACK;
    }

    @Override
    public PlayPageBridgeResult showToListenGuideIfNeed(View startView) {
        XPlayPage xPlayFragment = XPlayPageRef.get();
        if (xPlayFragment == null) {
            return PlayPageBridgeResult.RESULT_INVALID_PAGE;
        }
//        xPlayFragment.startShowToListenGuideFromRN(startView);
        return PlayPageBridgeResult.RESULT_SUCCESS;
    }

    @Override
    public void showH5ToListenDialog(long trackId, String clickPlayIting, String source) {
        HandlerManager.postOnUIThread(() -> NewShowNotesManager.INSTANCE.startShowNotesDetailFragment(source, clickPlayIting, 0L, trackId, null));
    }

    @Override
    public PlayPageBridgeResult showCollectSnackBarIfNeed(long trackId) {
        if (XPlayPageRef.get() == null || XPlayPageRef.get().isFullScreen()) {
            return PlayPageBridgeResult.RESULT_INVALID_PAGE;
        }
        AppCommentManager.INSTANCE.showDialog(InteractiveBoxConfigUtil.INSTANCE.feedbackSnackbarShow(), true);
        return InteractiveFeedbackBar.showForRNPlayPage(InteractiveFeedbackBar.FeedbackBarType.BAR_TYPE_COLLECT, trackId, BottomViewNew.BOTTOM_VIEW_FROM_PAGE_AUDIO, (BaseFragment2) PlayerManager.getInstanse().getCurrentFragment(), popWindow -> {
            if (PlayerManager.getInstanse().getCurrentFragment() != null) {
                AppCommentManager.INSTANCE.showDialog(0, false);
                IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
                if (funAction != null && PlayerManager.getInstanse().getCurrentFragment().canUpdateUi()) {
                    ITingListManager tingListManager = funAction.newTingListManager((BaseFragment2) PlayerManager.getInstanse().getCurrentFragment());
                    boolean isMusic = false;
                    if (PlayPageDataManager.getInstance().getSoundInfo() != null) {
                        if (PlayPageDataManager.getInstance().getSoundInfo().otherInfo != null) {
                            isMusic = PlayPageDataManager.getInstance().getSoundInfo().otherInfo.musicRelated == PlayingSoundInfo.OtherInfo.IS_MUSIC;
                        }
                        tingListManager.showTingList(TingListConstants.TYPE_TRACK, PlayCommentUtil.getCurTrackId(PlayPageDataManager.getInstance().getSoundInfo()), PlayPageDataManager.getInstance().getSoundInfo().trackInfo2TrackM(), isMusic, TingListConstants.TING_LIST_UBT_PREV_RESOURCE_TYPE_FLOATING_POPUP_WINDOW);
                        tingListManager.setITingListResultCallback(tingListInfoModel -> {
                            InteractiveFeedbackBar.showForTingList(InteractiveFeedbackBar.FeedbackBarType.BAR_TYPE_ADDED, PlayPageDataManager.getInstance().getSoundInfo(), tingListInfoModel, BottomViewNew.BOTTOM_VIEW_FROM_PAGE_AUDIO, (BaseFragment2) PlayerManager.getInstanse().getCurrentFragment(), popWindow1 -> {
                                IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                                if (fragAction != null) {
                                    BaseFragment2 frag = fragAction.newTingListDetailFragment(tingListInfoModel);
                                    ((BaseFragment2) PlayerManager.getInstanse().getCurrentFragment()).startFragment(frag);
                                }
                            });
                        });
                    }

                }
            }

        }) ? PlayPageBridgeResult.RESULT_SUCCESS : PlayPageBridgeResult.RESULT_INVALID_TRACK;
    }

    @Override
    public void showAdDislikeBottomDialog(Context context, String positionId, Advertis advertis, String materialUrl, IDataCallBack dislikeSuccessCallBack) {
        new AdDislikeBottomDialogX(BaseApplication.getTopActivity(), null, advertis.getPositionId() + "", advertis,
                materialUrl,
                new AdDislikeBottomDialogX.IDislikeSuccessCallBack() {
                    @Override
                    public void onDislikeSuccess(boolean isAdCloseByFeedBack) {
                        dislikeSuccessCallBack.onSuccess(isAdCloseByFeedBack);
                    }
                }, null).showDialog();
    }

    @Override
    public IColumnLargeAdProvider getColumnLargeAdProvider() {
        return new PlayColumnLargeAdProvider();
    }
    @Override
    public IPlayPageLargeAdProvider getPlayPageLargeAdProvider() {
        return new PlayPageLargeAdProvider();
    }

    @Override
    public void showPraiseSnackBarIfNeed(long trackId, long albumId, long uid, boolean monthlyTicketVote, long monthlyTicketCount, IDataCallBack<Integer> callBack) {
        if (XPlayPageRef.get() == null || XPlayPageRef.get().isFullScreen()) {
            callBack.onError(PlayPageBridgeResult.RESULT_INVALID_PAGE.ret, PlayPageBridgeResult.RESULT_INVALID_PAGE.msg);
            return;
        }
        if (PlayPageDataManager.getInstance().getSoundInfo() != null
                && monthlyTicketVote) {
            long requestTime = System.currentTimeMillis();
            MainCommonRequest.getMyMonthlyTicker(new IDataCallBack<Integer>() {
                @Override
                public void onSuccess(@Nullable Integer data) {
                    // 同一专辑点赞两次 && 用户有月票才出snackBar
                    if (PlayerManager.getInstanse().getCurrentFragment() != null && PlayerManager.getInstanse().getCurrentFragment().canUpdateUi()) {
                        if (System.currentTimeMillis() - requestTime < 1000 && data != null && data > 0) {
                            if (InteractiveFeedbackBar.showForRNPlayPage(InteractiveFeedbackBar.FeedbackBarType.BAR_TYPE_LIKE, trackId, BottomViewNew.BOTTOM_VIEW_FROM_PAGE_AUDIO, (BaseFragment2) PlayerManager.getInstanse().getCurrentFragment(), (InteractiveFeedbackBar.IPopClick) popWindow ->
                            {
                                showVoteDialog(trackId, albumId, uid);
                                AppCommentManager.INSTANCE.showDialog(0, false);
                            })) {
                                AppCommentManager.INSTANCE.showDialog(InteractiveBoxConfigUtil.INSTANCE.feedbackSnackbarShow(), true);
                                callBack.onSuccess(data);
                            } else {
                                AppCommentManager.INSTANCE.showDialog(0, true);
                                callBack.onError(PlayPageBridgeResult.RESULT_INVALID_TRACK.ret, PlayPageBridgeResult.RESULT_INVALID_TRACK.msg);
                            }
                        } else {
                            AppCommentManager.INSTANCE.showDialog(0, true);
                            callBack.onError(PlayPageBridgeResult.RESULT_MONTHLY_TICKET_EMPTY.ret, PlayPageBridgeResult.RESULT_MONTHLY_TICKET_EMPTY.msg);
                        }
                    }
                }

                @Override
                public void onError(int code, String message) {
                    AppCommentManager.INSTANCE.showDialog(0, true);
                    if (PlayerManager.getInstanse().getCurrentFragment() != null && PlayerManager.getInstanse().getCurrentFragment().canUpdateUi()) {
                        callBack.onError(PlayPageBridgeResult.RESULT_UNKNOWN.ret, PlayPageBridgeResult.RESULT_UNKNOWN.msg);
                    }
                }
            });
        } else {
            AppCommentManager.INSTANCE.showDialog(0, true);
            callBack.onError(PlayPageBridgeResult.RESULT_MONTHLY_TICKET_DISABLE.ret, PlayPageBridgeResult.RESULT_MONTHLY_TICKET_DISABLE.msg);
        }
    }

    public void download() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                PlayPageDownloadUtils.doDownload();
            }
        });
    }

    @Override
    public void refreshPlayPage() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
                if (service != null) {
                    service.refresh();
                }
            }
        });
    }

    @Override
    public void startPlayPageAnimate(String biz, Runnable runnable, boolean raisePriority) {
        AnimationManagerV2.start(biz, runnable, raisePriority);
    }

    @Override
    @Nullable
    public Map<String, String> getVideoPlayInfo() {
        IXPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IXPlayFragmentService.class);
        if (service != null && service.isVideoMode()) {
            Map<String, String> info = new ArrayMap<>();
            info.put("playStatus", service.isVideoPlaying() ? "播放" : "暂停");
            info.put("playDuration", String.valueOf(service.getVideoProgress() / 1000));
            info.put("trackDuration", String.valueOf(service.getVideoDuration() / 1000));
            return info;
        }
        return null;
    }

    @Nullable
    @Override
    public String getPraiseConfigForRN() {
        long currTrackId = PlayTools.getCurTrackId(ToolUtil.getCtx());
        LikeIconModelForRN info = RemoteLikeIconManager.INSTANCE.getCachedIconForRN(RemoteLikeIconManager.BIZ_TRACK, currTrackId);
        if (info != null) {
            return GsonUtils.toJson(info);
        }
        return null;
    }

    @Override
    public void showFullScreenLottie(String url) {
        RemoteLikeIconManager.INSTANCE.showFullScreenLottie(url);
    }

    @Override
    public int getRealPlayProgress() {
        IXPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IXPlayFragmentService.class);
        if (service != null && service.isVideoMode()) {
            return service.getVideoProgress();
        } else {
            return XmPlayerManager.getInstance(ToolUtil.getCtx()).getPlayCurrPositon();
        }
    }

    public boolean shouldShowSubscribe() {
        return !SubscribeFollowPriority.showFollowFirst(
                PlayPageDataManager.getInstance().getSoundInfo()
        );
    }

    @Override
    public boolean isHomePageTabAndChannelListNewFragmentDialog(Fragment fragment) {
        return fragment instanceof HomePageTabAndChannelListNewFragmentDialog;
    }

    @Override
    public void doAlbumFavoriteFromRn(long albumId, String albumTitle, boolean isFavorite, int bizType, int followBizType, int followSubBizType) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                PlayPageFollowUtil.INSTANCE.albumFavorite(albumId, albumTitle, isFavorite, bizType, followBizType, followSubBizType, null);
            }
        });
    }

    @Override
    public void doAnchorFollowFromRn(long anchorId, boolean isFollow, int bizType, int subBizType) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                PlayPageFollowUtil.INSTANCE.followAnchor(anchorId, 0L, isFollow, bizType, subBizType, null);
            }
        });
    }

    @Override
    public void doAnchorFollowFromRn(long anchorId, long albumId, boolean isFollow, int bizType, int subBizType, IDataCallBack<Boolean> callBack) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                PlayPageFollowUtil.INSTANCE.followAnchor(anchorId, albumId, isFollow, bizType, subBizType, callBack);
            }
        });
    }

    @Override
    public void doAnchorFollowFromRnCommon(long anchorId, long albumId, boolean isFollow, int bizType, int subBizType, String srcPage, IDataCallBack<Boolean> callBack) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                AnchorFollowParam param = new AnchorFollowParam(
                        anchorId, albumId, isFollow, bizType, subBizType, true, srcPage
                );
                AnchorFollowManage.followV3WithLoginAndRecommend(
                        BaseApplication.getMainActivity(),
                        param,
                        new IDataCallBack<Boolean>() {
                            @Override
                            public void onSuccess(@Nullable Boolean data) {
                                if (callBack != null) {
                                    callBack.onSuccess(data);
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                if (callBack != null) {
                                    callBack.onError(code, message);
                                }
                            }
                        });
            }
        });
    }

    @Override
    public View getPlayShareView() {
        return null;
    }

    @Override
    public boolean needShowExitPop() {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            return NewUserDlgManager.INSTANCE.needShowExitPop(((MainActivity) activity).getSupportFragmentManager());
        }
        return false;
    }

    @Override
    public void refreshHomepageRecommendFragment() {
        NewUserDlgManager.INSTANCE.setMTodayTaskFinish(true);
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            Fragment topFragment = ((MainActivity) activity).getCurrentTopFragment();
            if (topFragment instanceof HomePageFragment && ((HomePageFragment) topFragment).isShowRecommendFragment()) {
                ((HomePageFragment) ((MainActivity) activity).getCurrentTopFragment()).onRefresh();
            } else {
                MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_LIMIT_NEW_USER_COMPLETE_TASK_NEED_REFRESH, true);
            }
        }
    }

    @Override
    public void refreshHomepageRecommendFragmentOnly() {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            Fragment topFragment = ((MainActivity) activity).getCurrentTopFragment();
            if (topFragment instanceof HomePageFragment && ((HomePageFragment) topFragment).isShowRecommendFragment()) {
                ((HomePageFragment) ((MainActivity) activity).getCurrentTopFragment()).onRefresh();
            } else {
                MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_LIMIT_NEW_USER_COMPLETE_TASK_NEED_REFRESH, true);
            }
        }
    }

    public void refreshHomepageRecommendFragmentForCustomHome() {
        SearchCustomCardDataUtils.refreshHomePage();
    }

    @Override
    public void setAutoOpenStatus(boolean show) {
        PlayListAndHistoryDialogManager.Companion.setAutoOpenStatus(show);
    }

    @Override
    public boolean isNeedShowPlayList() {
        return PlayListAndHistoryDialogManager.Companion.isNeedOpen();
    }

    @Override
    public void setAutoOpenToListenStatus(boolean show) {
        PlayListAndHistoryDialogManager.Companion.setAutoOpenToListenStatus(show);
    }

    @Override
    public boolean getAutoOpenToListenStatus() {
        return PlayListAndHistoryDialogManager.Companion.getAutoOpenToListenStatus();
    }

    @Override
    public void showPlayListWhileInXPlayPage() {
        XPlayPage playPage = XPlayPageRef.get();
        if (playPage != null) {
            playPage.showPlayList();
        }
    }

    @Override
    public void setAutoOpenToListenDeleteMode(boolean deleteMode) {
        PlayListAndHistoryDialogManager.Companion.setAutoOpenToListenStatusDeleteMode(deleteMode);
    }

    @Override
    public void deleteAlbumDownloadTask(long albumId) {
        VipDownloadManager.INSTANCE.delDownloadDataByAlbum(albumId);
    }

    @Override
    public void deleteDownloadTask() {
        VipDownloadManager.INSTANCE.clear();
    }

    @Override
    public void showSubscribeUnlockSnackBar(long albumId) {
        SubscribeUnlockUtil.INSTANCE.showGuideSnackBar(albumId);
    }

    @Override
    public void buySinglePayAlbumTrack(Track track) {
        MainActivity mainActivity = (MainActivity) MainApplication.getMainActivity();
        AudioPlayPageAlbumBuyManager.Companion.create((BaseFragment2) mainActivity.getCurrentTopFragment()).buyTrack(track, false, false);
    }

    @Override
    public void getTrainingRefundStatus(long albumId, int statusId, IDataCallBack<Integer> callBack) {
        MainCommonRequest.getTrainingRefundStatus(albumId, statusId, callBack);
    }

    @Override
    public void showMasterVipAgreementConfirmDialog(IDataChangeCallback<String> callback, boolean needReShow) {
//        MasterVipAgreementManager.Companion.getInstance().showMasterVipAgreementDialog(callback, needReShow);
    }

    @Override
    public void refresshPlayPageNextBtnStatus() {
        IAudioPlayControlComponentService audioPlayControlComponentService =
                PlayPageInternalServiceManager.getInstance().getService(IAudioPlayControlComponentService.class);
        if (audioPlayControlComponentService != null) {
            audioPlayControlComponentService.updateNextAndPreBtnStatus();
        }
    }

    @Override
    public String getTempo2() {
        return TempoManager.getInstance().getCurrentAlbumTempo() + "";
    }

    @Override
    public void addTempoListener(IBaseTempoListener iBaseTempoListener) {
        TempoManager.getInstance().addBaseTempoListener(iBaseTempoListener);
    }

    @Override
    public void removeTempoListener(IBaseTempoListener iBaseTempoListener) {
        TempoManager.getInstance().removeBaseTempoListener(iBaseTempoListener);
    }

    @Override
    public Fragment getCommentFragment(int srcPage, Track track) {
        return FloatingTrackCommentFragment.newInstanceForTrack(srcPage, track, false, 0);
    }

    @Override
    public Fragment getTingReadParaCommentFragment(TingReadComParamModel model, boolean isOnlyShowSendCommentDialog, IParaComListener listener, IFloatingFragmentDismissListener dismissListener) {
        TRParagraphCommentFragment fragment = TRParagraphCommentFragment.newInstance(model, isOnlyShowSendCommentDialog);
        fragment.setOnDismissListener(dismissListener);
        fragment.setParaComListener(listener);
        return fragment;
    }

    @Override
    public ITingReadFloatingPlayControlComponent newTingReadFloatingPlayControlComponent(BaseFragment2 fragment, ITingReadFloatingPlayControlComponent.IFloatingControlBarActionListener listener) {
        return new TingReadFloatingConComponent(fragment, listener, false);
    }

    @Override
    public void onVideoNoLike(BaseFragment2 baseFragment2, VideoFeedBackTraceData traceData) {
        VideoNotLikeFeedBackUtil.onVideoNotLikeClick(baseFragment2, traceData);
    }

    @Override
    public void showListenTimeFragment(String source) {
        ListenTimeDialogManager.showListenTimeFragment(source);
    }

    @Override
    public BaseDialogFragment getFreeListenTimeRewardFragment(FreeListenConfigManager.PopUpPicture popUpPicture, int time, View.OnClickListener clickListener,
                                                View.OnClickListener closeListener) {
        return FreeListenFreeRewardDialogFragment.getInstance(popUpPicture, time, clickListener, closeListener);
    }

    @Override
    public BaseDialogFragment  getFreeListenTimeWatchVideoFragment(List<String> covers, View.OnClickListener clickListener, View.OnClickListener closeListener) {
        return FreeListenWatchVideoDialogFragment.getInstance(covers, clickListener, closeListener);
    }

    @Override
    public View getShareCDPosterView(ShareWrapContentModel contentModel) {
        if (contentModel.isShowPosterHead && contentModel.soundInfo != null) {
            return ShareTrackPosterUtil.INSTANCE.getCDPosterView(contentModel);
        }
        if (contentModel.isShowPosterHead && contentModel.getAlbumModel() != null) {
            return ShareAlbumPosterUtil.INSTANCE.getCDPosterView(contentModel);
        }
        return null;
    }

    @Override
    public View getShareStarPosterView(ShareWrapContentModel contentModel) {
        if (contentModel.isShowPosterHead && contentModel.soundInfo != null) {
            return ShareTrackPosterUtil.INSTANCE.getStarPosterView(contentModel);
        }
        if (contentModel.isShowPosterHead && contentModel.getAlbumModel() != null) {
            return ShareAlbumPosterUtil.INSTANCE.getShareStarPosterView(contentModel);
        }
        return null;
    }

    @Override
    public void startVideoShare(ShareWrapContentModel contentModel) {
        VideoShareUtil.INSTANCE.startVideoShare(contentModel);
    }

    @Override
    public void startVideoClip(ShareWrapContentModel contentModel) {
        VideoShareUtil.INSTANCE.startVideoClip(contentModel);
    }

    @Override
    public void addRequestPlayPageParamData(Map<String, String> params) {
        if (params == null) {
            return;
        }

        if (!PlayFragmentAbManager.getInstance().shouldShowDocOnCover()) {
            params.put("showDocTabs", "1");
        }
        String hideBarType = XPlayCommercialRelatedUtils.getPlayPageHideBarTypeWrapper();
        if (!StringUtil.isEmpty(hideBarType)) {
            params.put("hideBarType", hideBarType);
        }

        int extCustomMode = XPlayCommercialRelatedUtils.isAdClosedBeyondLimit() ? 1 : 0;
        if (UveYellowBarBusiness.getYellowBarUseUVESwitch()) {
            extCustomMode = extCustomMode + (1 << 1);
        }
        if (extCustomMode > 0) {
            params.put("extCustomMode", String.valueOf(extCustomMode));
        }
    }

    @Override
    public List<TingParaBean> getTingParaBeanData(PlayingSoundInfo soundInfo, List<LrcEntry> lrcList) {
        return TingDataUtil.convertData(soundInfo, null, lrcList);
    }

    @Override
    public boolean showNotePanelInXPlayFragment() {
        XPlayPage playPage = XPlayPageRef.get();
        if (playPage == null) {
            return false;
        }
        if (playPage instanceof BasePlayPageTabFragment && ((BasePlayPageTabFragment) playPage).isRealVisible()) {
            playPage.openMasterClassNoteInputPanel();
            return true;
        }
        return false;
    }

    @Override
    public void showSkipHeadTailDialog(Activity activity, long albumId) {
        SkipHeadTailDialog dialog = new SkipHeadTailDialog(activity, albumId);
        dialog.toggle();
    }

    @Override
    public void requestAlbumListMiddleAd(AlbumM album) {
        UniversalCustomAlbumListMiddleAdManager.requestMiddleAd(album);
    }

    @Override
    public void showAlbumListMiddleAdView(ViewGroup viewGroup, AlbumM album, BaseFragment2 fragment2, IMiddleAdViewShowCallback callback) {
        UniversalCustomAlbumListMiddleAdManager.showMiddleAdView(viewGroup, album, fragment2, callback);
    }

    @Override
    public void notifyAlbumListMiddleAdDestroy() {
        UniversalCustomAlbumListMiddleAdManager.onAdDestroy();
    }

    @Override
    public void refreshAlbumListMiddleAdView(ViewGroup viewGroup, AlbumM album, BaseFragment2 fragment2, IMiddleAdViewShowCallback callback) {
        UniversalCustomAlbumListMiddleAdManager.refreshMiddleAd(viewGroup, album, fragment2, callback);
    }

    public BaseDialogFragment getPlanTerminateFragmentNew() {
        return PlanTerminateFragmentNewX.newInstance(PlanTerminateFragmentNewX.TYPE_NORMAL);
    }

    @Override
    public void showPlanTerminateDialogNew(FragmentManager fragmentManager) {
        PlanTerminalNewDialog.Companion.show(fragmentManager);
    }

    @Override
    public void createMainPlayPageThemeForTR(PlayingSoundInfo soundInfo, ITRCreateMainPlayPageThemeCallBack callback) {
        TRObtainPlayPageThemeMainManager.createMainPlayPageTheme(soundInfo, callback);
    }

    @Override
    public TRMainPlayTheme createDefPlayPageThemeForTR() {
        return TRObtainPlayPageThemeMainManager.createDefPlayPageTheme();
    }

    @Override
    public void seekTo(Context context, int progress) {
        AudioPlayUtil.seekTo(context, progress);
    }

    @Override
    public void forward15Second(Context context) {
        AudioPlayUtil.forward15Second(context);
    }

    @Override
    public void backward15Second(Context context) {
        AudioPlayUtil.backward15Second(context);
    }

    @Override
    public void showMoreActionDialog(BaseFragment2 baseFragment, PlayingSoundInfo soundInfo) {
        TingReadMoreMenuUtils.showMoreActionDialog(baseFragment, soundInfo);
    }

    @Override
    public void addReadBookInterceptBackListener(IReadBookInterceptBackListener listener) {
        PlayPageInternalServiceManager.getInstance()
                .registerService(com.ximalaya.ting.android.main.playpage.playx.listener.IReadBookInterceptBackService.class,
                        () -> {
                            if (listener == null) {
                                return false;
                            }
                            return listener.onBackPressed();
                        });
    }

    @Override
    public void removeReadBookInterceptBackListener() {
        PlayPageInternalServiceManager.getInstance().unRegisterService(com.ximalaya.ting.android.main.playpage.playx.listener.IReadBookInterceptBackService.class);
    }

    @Override
    public boolean isPlayPagePlayY(PlayingSoundInfo soundInfo) {
        return PlayPageTabUtil.isPlayY(soundInfo);
    }

    @Override
    public int checkPlayPageTabType(int focusTabType) {
        return PlayPageTabUtil.checkPlayPageTabType(focusTabType);
    }

    @Override
    public void interceptTopSlideExit(Fragment fragment, boolean intercept) {
        if (fragment instanceof PlayFragmentNew) {
            PlayFragmentNew playFragmentNew = (PlayFragmentNew) fragment;
            playFragmentNew.interceptTopSlideExit(intercept);
        }
    }

    /**
     * 提前解析首页布局和推荐页的一些 item 布局，避免在广告展示时进行 inflate
     * @param callback
     */
    @Override
    public void preloadHomePageLayout(Runnable callback) {
        Log.d("z_inflate", "preloadHomePageLayout");

        if (callback == null) {
            return;
        }

        HandlerManager.postOnBackgroundThread(new Runnable() {
            @Override
            public void run() {
                try {

                    preloadRClassAndInflateLayout(callback);

                } catch (Exception e) {
                    e.printStackTrace();
                    HandlerManager.postOnUIThread(new Runnable() {
                        @Override
                        public void run() {
                            callback.run();
                        }
                    });
                }
            }
        });
    }

    private void preloadRClassAndInflateLayout(Runnable callback) {

        try {
            catchPreload("com.ximalaya.ting.android.main.R");
            catchPreload("com.ximalaya.ting.android.main.R$id");
            catchPreload("com.ximalaya.ting.android.main.R$layout");
            catchPreload("com.ximalaya.ting.android.main.R$color");
            catchPreload("com.ximalaya.ting.android.main.R$anim");
            catchPreload("com.ximalaya.ting.android.main.R$dimen");

            catchPreload("com.ximalaya.ting.android.framework.R");
            catchPreload("com.ximalaya.ting.android.framework.R$id");

            catchPreload("com.ximalaya.ting.android.host.R");
            catchPreload("com.ximalaya.ting.android.host.R$id");
            catchPreload("com.ximalaya.ting.android.host.R$drawable");

        } catch (Exception e) {
            e.printStackTrace();
        }

        List<ViewPool.InflateMany> tasks = Arrays.asList(
                new ViewPool.InflateMany(R.layout.main_fra_home_page, 1),   //首页
                new ViewPool.InflateMany(R.layout.main_fra_recommend_new_two_column, 1),    //推荐页
                new ViewPool.InflateMany(R.layout.main_fra_recommend_scene_listen_item, 6), //习惯听
                new ViewPool.InflateMany(R.layout.main_fra_recommend_rank_list_album_item, 12),  //排行榜 item
                new ViewPool.InflateMany(R.layout.main_item_recommend_social_list_item, 12)      //社会化听单 item
        );

        ViewPool.getInstance().preInflateMany(tasks, new Runnable() {
            @Override
            public void run() {
                Log.d("z_inflate", "preloadHomePageLayout finished >>> ");

                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        callback.run();
                    }
                });
            }
        });
    }

    private void catchPreload(String className) {
        try {
            long begin = System.currentTimeMillis();
            Class.forName(className);

            if (PerformanceMonitor.isSwitchOpen()) {
                Log.e("PerformanceMonitor", "preload class [" + className + " ] cost: " + (System.currentTimeMillis() - begin));
            }
        } catch (Exception e) {
            e.printStackTrace();

            if (PerformanceMonitor.isSwitchOpen()) {
                Log.e("PerformanceMonitor", "preload class [" + className + " ] failed: " + e.getMessage());
            }
        }
    }

    @Override
    public void showAIDocShareDialog(PlayingSoundInfo soundInfo, String selectionContent, KachaAIDocModel kachaAIDocModel, FragmentManager manager) {
        AIDocShareManager.showAIDocShareDialog(soundInfo, selectionContent, kachaAIDocModel, manager);
    }

    @Override
    public void startShowNotesDetailFragment(String sourceFrom, String clickPlayIting, long albumId, long trackId, long timeLine,
                                             boolean autoPlay, boolean openComment, Track playListTrack,
                                             CommonTrackList<Track> commonTrackList, int commonTrackListIndex,
                                             NewShowNotesManager.IShowNotesOnPlayClickHandler clickPlayHandler, long previewId, long showStartTime) {
        NewShowNotesDetailFragment.Companion.startShowNotesDetailFragment(sourceFrom, clickPlayIting, albumId,
                trackId, timeLine, autoPlay, openComment, playListTrack, commonTrackList, commonTrackListIndex, clickPlayHandler, previewId, showStartTime);
    }

    @Override
    public void showFreeListenRewardDialogWithoutTrack(String sourceName, long albumId, BaseFragment2 baseFragment2, int layerType) {
        ListenTimeDialogManager.showRewardDialogNew(sourceName, null, baseFragment2, null, false, -1, albumId, null, layerType);
    }

    @Override
    public void showFreeListenRewardDialogWithTrack(String sourceName, Track track, long trackId, long albumId, BaseFragment2 baseFragment2, int layerType) {
        if (trackId <= 0) {
            trackId = track != null ? track.getDataId() : -1;
        }
        ListenTimeDialogManager.showRewardDialogNew(sourceName, track, baseFragment2, null, false, trackId, albumId, null, layerType);
    }

    public Class toBePlayedFragmentClass() {
        return ToBePlayedFragmentV2.class;
    }

    @Override
    public List<TingParaBean> convertLrcData(PlayingSoundInfo soundInfo, List<LrcEntry> lrcList) {
        return YTingDataUtil.convertLrcData(soundInfo, lrcList);
    }

    @Override
    public boolean isYPlayPage(PlayingSoundInfo soundInfo) {
        return PlayPageTabUtil.isPlayY(soundInfo);
    }

    @Override
    public void checkAlbumPageShowFreeListenDialog(long albumId) {
        CheckFreeListenDialogManager.checkAlbumPageShowFreeListenDialog(albumId);
    }

    @Override
    public void showOffSaleDialog(String currPage, long albumId, long trackId) {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) activity;

            Fragment currentFragment = mainActivity.getCurrentFragmentInManage();
            if (currentFragment == null) {
                currentFragment = mainActivity.getCurrentTopFragment();
            }
            if (currentFragment != null && currentFragment.getChildFragmentManager() != null) {
                if (OffSaleRecommendDialog.hasLast() == null) {
                    FragmentManager childFragmentManager = currentFragment.getChildFragmentManager();
                    OffSaleRecommendDialog.newInstance(currPage, albumId, trackId).show(
                            childFragmentManager, "OffSaleRecommendDialog"
                    );
                }
            }
        }
    }

    @Override
    public BaseDialogFragment getCommonInspireDialogFragment(int rewardedTime, int nextRewardTime, INativeAd nativeAd, boolean needReportShow, View.OnClickListener clickAdListener, View.OnClickListener closeAdListener) {
        return CommonInspireDialogFragment.getInstance(rewardedTime, nextRewardTime, nativeAd, needReportShow, clickAdListener, closeAdListener);
    }

    public BaseDialogFragment getCommonRewardAgainDialogFragment(int rewardedTime, int nextRewardTime, View.OnClickListener clickListener) {
        return CommonRewardAgainDialogFragment.getInstance(rewardedTime, nextRewardTime, clickListener);
    }

    @Override
    public void toSimilarPage(Long albumId) {
        Activity activity = BaseApplication.getTopActivity();
        if (!(activity instanceof MainActivity)) {
            return;
        }
        ((MainActivity) activity).startFragment(SimilarRecommendFragment.newInstanceByAlbumId(albumId, "相似推荐"));
    }

    @Override
    public void startSubscribeSearchFragment() {
        Activity activity = BaseApplication.getTopActivity();
        if (!(activity instanceof MainActivity)) {
            return;
        }
        ((MainActivity) activity).startFragment(SubscribeSearchFragment.Companion.newInstanceForNewSubscribe());
    }

    @Override
    public boolean shortDramaIsNewBuyOpening(long albumId) {
        return ShortDramaManager.isNewBuyOpening(albumId);
    }

    @Override
    public void loadCurrentUserRemainShortDramaRightCount(long albumId, IDataCallBack<Long> callback) {
        ShortDramaManager.loadCurrentUserRemainShortDramaRightCount(albumId, callback);
    }

    @Override
    public boolean isShortDrama(Track track) {
        return ShortDramaManager.isShortDrama(track);
    }

    @Override
    public void dismissBuyDialog() {
        ShortDramaManager.dismissBuyDialog();
    }

    @Override
    public void autoUnlock(Track track, IMiniDramaLockCallBack callBack) {
        ShortDramaManager.autoUnlock(track, new ShortDramaManager.IStateListener() {
            @Override
            public void onStateChanged(ShortDramaState state) {
                if (state == ShortDramaState.UNLOCK_FAILED) {
                    callBack.autoUnlockUnlockFailed();
                }
            }
        });
    }

    @Override
    public void getPlayPageTabAndInfoUseParamsMap(long trackId, ISoundTabsCallBack callBack) {
        PlayPageDataManager.getInstance().loadData(trackId, callBack);
    }

    @Override
    public void showMiniDramaBuyDialog(Track track) {
        if (ShortDramaManager.isNewBuyOpening(track)) {
            //不支持短剧，弹出购买弹窗
            ShortDramaManager.showBuyDialog(track);
        }
    }

    @Override
    public boolean topFragmentIsRecommend() {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            Fragment topFragment = ((MainActivity) activity).getCurrentTopFragment();
            return topFragment instanceof HomePageFragment && ((HomePageFragment) topFragment).isShowRecommendFragment();
        }
        return false;
    }

    @Override
    public boolean isMineTopVipNewStyle() {
        return MineTopUtil.INSTANCE.isVipNewStyle();
    }


    @Override
    public void inviteWechatFriends() {
        FindFriendFragmentNew.Companion.doShare(IShareDstType.SHARE_TYPE_WX_FRIEND);
    }

    @Override
    public void getMobileAddressBookList(IDataCallBack<JSONObject> callBack) {
        JSONObject jsonObject = new JSONObject();
        Map<String, ThirdPartyUserInfo> map = PhoneContactsManager.getInstance().getPhoneMap();
        if (map == null || map.isEmpty()) {
            // 有权限时读取通讯录，并更新通讯录好友
            if (ContextCompat.checkSelfPermission(BaseApplication.getMyApplicationContext(), Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED) {
                PhoneContactsManager.getInstance().getContacts(list -> {
                    if (list != null) {
                        for (ThirdPartyUserInfo info : list) {
                            try {
                                JSONObject itemJson = new JSONObject();
                                itemJson.put("tel", info.getIdentity());
                                itemJson.put("name", info.getNickname());
                                jsonObject.put(info.getPhoneHash(), itemJson);
                            } catch (JSONException e) {
                            }
                        }
                        callBack.onSuccess(jsonObject);
                    }
                    callBack.onSuccess(jsonObject);
                });
            } else {
                callBack.onSuccess(jsonObject);
            }
        } else {
            try {
                for (String key : map.keySet()) {
                    ThirdPartyUserInfo info = map.get(key);
                    if (info != null) {
                        JSONObject itemJson = new JSONObject();
                        itemJson.put("tel", info.getIdentity());
                        itemJson.put("name", info.getNickname());
                        jsonObject.put(info.getPhoneHash(), itemJson);
                    }
                }
            } catch (JSONException e) {
            }
            callBack.onSuccess(jsonObject);
        }
    }

    @Override
    public int getMineTopVipNewStylePageBgColor() {
        return MineTopUtil.INSTANCE.getPageBgColor();
    }

    @Override
    public IHomeRnNetAction getHomeRnNetAction() {
        return RecommendRnNetManager.INSTANCE.getHomeRnNetAction();
    }

    @Override
    public IRecommendRnView getRecommendRnItemView(Context context, String itemType) {
        return RecommendRnViewAdapter.getView(context, itemType);
    }

    @Override
    public void gotoWifiFragment() {
        PlayingSoundInfo soundInfo = PlayPageDataManager.getInstance().getSoundInfo();
        if (soundInfo == null) {
            return;
        }
        TrackM track = soundInfo.trackInfo2TrackM();

        WiFiConnectFragment fragment = new WiFiConnectFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable("track", track);
        bundle.putInt("type", WiFiConnectFragment.DEVICE_XIAOYA);
        fragment.setArguments(bundle);
        Activity mainAct = BaseApplication.getMainActivity();
        if (mainAct instanceof MainActivity) {
            ((MainActivity) mainAct).startFragment(fragment);
        }
        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .setSrcModule("外放设备选择面板")
                .setItem("button")
                .setItemId("小雅智能音箱")
                .setId("5274")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
    }

    @Override
    public boolean isNewSceneCard() {
        return RecommendFragmentTypeManager.INSTANCE.isNewSceneCard();
    }

    @Override
    public void downloadSkinPack(String skinId, String skinUrl, DownloadLiteManager.DownloadCallback downloadCallback) {
        PlayPageSkinDownloadManager.INSTANCE.downloadAndSetPlayPageSkin(skinId, skinUrl, downloadCallback);
    }

    @Override
    public boolean getSkinDownloadStatus(String skinId, String skinUrl) {
        return PlayPageSkinDownloadManager.INSTANCE.checkSkinPackValid(skinId);
    }

    @Override
    public void clearPlaypageSkin(){
        PlayPageSkinDownloadManager.INSTANCE.clearSkin();
    }

    @Override
    public boolean buySingleTrack(BaseFragment2 fragment, PlayingSoundInfo soundInfo) {
        PlayBuyViewUtil.buySingleTrack(fragment, soundInfo, false, null);
        return true;
    }

    @Override
    public void editAlarmSettingFragment(AlarmRecord alarm) {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            ((MainActivity) activity).startFragment(AddOrEditAlarmFragment.newInstance(alarm));
        }
    }

    @Override
    public void openQuickListenTempo(IQuickListenTempoDialogCallBack callBack) {
        SpeedDialogForQuickListen speedDialog = new SpeedDialogForQuickListen(new ISpeedSelectCallback() {
            @Override
            public void onSpeedSelected(@NonNull SpeedScaleModel speed, boolean isOnlyToCurAlbum) {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).setQuickListenTempo(speed.getSpeedValueFloat());
            }

            @Override
            public void onSelectCurAlbumStateChange(boolean isSelected) {
            }
        }, QuickListenForPlayProcessUtil.getQuickListenTempo(ToolUtil.getCtx()));
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof FragmentActivity) {
            Fragment fragment = FragmentUtil.getShowingFragmentByClass((FragmentActivity) activity, BaseFragment2.class);
            if (fragment instanceof BaseFragment2) {
                speedDialog.show(fragment.getParentFragmentManager(), SpeedDialogForQuickListen.class.getName());
            }
        }
    }

    @Override
    public void startPlayPageDebugFragment() {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            ((MainActivity) activity).startFragment(new PlaypageSkinDebugFragment());
        }
    }
}