package com.ximalaya.ting.android.main.playpage.playy.component.cover

import android.animation.Animator
import android.animation.ValueAnimator
import android.graphics.Bitmap
import android.text.TextUtils
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.doOnCancel
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.performance.PageStartOpt
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.afterLayout
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.internalservice.IPlayFragmentService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.YPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponent
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YPlayHeight
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.skin.SkinChangeListener
import com.ximalaya.ting.android.main.playpage.playy.view.PlayMediaBackground
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

/**
 * Created on 2023/3/6.
 * <AUTHOR>
 * @email <EMAIL>
 */
class YNormalCoversComponentY(
    private val playContainer: IPlayContainer,
    private val yCoverComponentsManager: YCoverComponentsManager
) : XBaseCoverComponent(), SkinChangeListener {

    private var normalCoversView: View? = null
    private var mediaBackground: PlayMediaBackground? = null
    private var noNetworkTipsView: View? = null
    private var mIvSoundCover: ImageView? = null
    private var mIvLoadingIv: ImageView? = null
    private var mTvNoNetworkTv: TextView? = null
    private var mBaseSize = 0
    private var aiTagIv: ImageView? = null

    private var skinCoverContainer: View? = null
    private var skinCoverImage: ImageView? = null
    private var skinCoverDecor: ImageView? = null

    private var yViewModel: YPlayViewModel? = null
    private var xViewModel: XPlayViewModel? = null

    private var rotationAnimator: ValueAnimator? = null

    override fun inflate(): View {
        val context = mFragment.requireContext()
        return PageStartOpt.PlayPageOpt
            .inflateView(context, R.layout.main_audio_play_component_normal_covers_y, FrameLayout(context), false)
    }

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)
        yViewModel = ViewModelProvider(fragment!!.requireActivity()).get(YPlayViewModel::class.java)
        xViewModel = ViewModelProvider(fragment!!.requireActivity()).get(XPlayViewModel::class.java)

        inflateAndInitUI()
        playContainer.registerSkinChangeListener(this)
        playContainer.addPlayStatusListener(this)
    }

    fun setBackgroundView(mediaBackground: PlayMediaBackground) {
        this.mediaBackground = mediaBackground

        if (PSkinManager.isEnabled) {
            this.mediaBackground?.setThemeColor(0)
        } else {
            yViewModel?.coverTheme?.backgroundColor?.also {
                this.mediaBackground?.setThemeColor(it)
            }
        }
    }

    override fun initUi() {
        //init normal covers view
        normalCoversView = findViewById(R.id.main_play_normal_cover_container)
        mIvLoadingIv = findViewById(R.id.main_no_network_loading)
        mIvSoundCover = findViewById(R.id.main_iv_sound_cover)
        aiTagIv = findViewById(R.id.main_iv_ai_tag)
        updateCoverSize()
        noNetworkTipsView = findViewById<View>(R.id.main_cover_no_network_tips)
        mTvNoNetworkTv = findViewById<TextView>(R.id.main_cover_no_network_retry)
        mTvNoNetworkTv?.setOnClickListener {
            PlayPageInternalServiceManager.getInstance().getService(
                IPlayFragmentService::class.java
            )?.refresh()
            doAnimation()
        }

        val viewLifeCycle = mFragment.viewLifecycleOwner
        yViewModel?.coverMainColor?.observe(viewLifeCycle) {
            if (!PSkinManager.isEnabled) {
                yViewModel?.coverTheme?.originalColor?.also {
                    this.mediaBackground?.setThemeColor(it)
                }
            }
        }

        //init skin cover
        skinCoverContainer = findViewById(R.id.main_play_skin_cover)
        skinCoverImage = findViewById(R.id.main_iv_skin_cover)
        skinCoverDecor = findViewById(R.id.main_iv_skin_cover_bg)

        xViewModel?.coverBitmap?.observe(playContainer as Fragment, Observer {
            if (it != null) {
                // If the cover bitmap is not null, set it to both the sound cover and skin cover
                mIvSoundCover?.setImageBitmap(it)
                skinCoverImage?.setImageBitmap(it)
            } else {
                // If the cover bitmap is null, set default cover images
                mIvSoundCover?.setImageResource(R.drawable.main_cover_default_album_light)
                skinCoverImage?.setImageResource(R.drawable.main_cover_default_album_light)
            }
        })
    }

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        // Update the skin cover view based on the new skin configuration
        if (newSkinConfig != null) {
            this.mediaBackground?.setThemeColor(0)
        } else {
            yViewModel?.coverTheme?.originalColor?.also {
                this.mediaBackground?.setThemeColor(it)
            }
        }
        updateSkin()
    }

    private fun updateSkin() {
        if (PSkinManager.isEnabled) {
            ViewStatusUtil.setVisible(View.GONE, skinCoverContainer)
            ViewStatusUtil.setVisible(View.GONE, normalCoversView)
//            val playBtnUrl = PSkinManager.getCoverDecorationUrl()
//            if (!TextUtils.isEmpty(playBtnUrl)) {
//                ImageManager.from(mContext).downloadBitmap(playBtnUrl, object : ImageManager.DisplayCallback {
//                    override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
//                        if (bitmap != null) {
//                            skinCoverDecor?.setImageBitmap(bitmap)
//                        }
//                    }
//                })
//            } else {
//                skinCoverDecor?.setImageResource(R.drawable.main_playpage_skin_cover_bg)
//            }
//
//            if (XmPlayerManager.getInstance(context).isPlaying) {
//                startRotation()
//            }
        } else {
            ViewStatusUtil.setVisible(View.GONE, skinCoverContainer)
            ViewStatusUtil.setVisible(View.VISIBLE, normalCoversView)
            rotationAnimator?.pause()
        }
    }

    private fun startRotation() {
        val skinContainer = skinCoverContainer
        if (skinContainer == null || skinContainer.visibility != View.VISIBLE) {
            return
        }
//        if (rotationAnimator == null) {
//            rotationAnimator = ValueAnimator.ofFloat(0f, 360f).apply {
//                repeatCount = ValueAnimator.INFINITE
//                duration = 6000
//                interpolator = LinearInterpolator()
//                addUpdateListener {
//                    skinContainer.rotation = it.animatedValue as Float
//                }
//
//                doOnCancel {
//                    skinContainer.rotation = 0f
//                }
//            }
//        }
//
//        val animator = rotationAnimator ?: return
//        if (!animator.isRunning) {
//            animator.start()
//            return
//        }
//
//        if (animator.isPaused) animator.resume()
    }

    private fun updateCoverSize() {
        val size = (170f + (260f - 170f) * YPlayHeight.scaleUpForMinimum).dp
        mIvSoundCover?.layoutParams?.width = size
        mIvSoundCover?.layoutParams?.height = size
        mBaseSize = size
    }

    private fun doAnimation() {
        mIvLoadingIv?.visibility = View.VISIBLE
        mTvNoNetworkTv?.visibility = View.GONE

        ValueAnimator.ofFloat(0f, 360f).apply {
            this.duration = 300
            this.addUpdateListener {
                mIvLoadingIv?.pivotX = (mIvLoadingIv?.width?.toFloat() ?: 0f) / 2
                mIvLoadingIv?.pivotY = (mIvLoadingIv?.height?.toFloat() ?: 0f) / 2
                mIvLoadingIv?.rotation = it.animatedValue as Float
            }
            this.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    mIvLoadingIv?.visibility = View.GONE
                    mTvNoNetworkTv?.visibility = View.VISIBLE
                }

                override fun onAnimationCancel(animation: Animator?) {
                    mIvLoadingIv?.visibility = View.GONE
                    mTvNoNetworkTv?.visibility = View.VISIBLE
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }
            })
            this.start()
        }
    }

    override fun getViewStubId(): Int {
        return R.id.main_vs_normal_covers
    }

    override fun onThemeColorChanged(foregroundColor: Int, backgroundColor: Int) {

    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        if (soundInfo?.fromPlayer == true) {
            ViewStatusUtil.setVisible(View.GONE, mIvSoundCover)
            ViewStatusUtil.setVisible(View.VISIBLE, noNetworkTipsView)
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, mIvSoundCover)
            ViewStatusUtil.setVisible(View.GONE, noNetworkTipsView)
        }

        if (soundInfo?.trackInfo?.isAiTrack == true) {
            ViewStatusUtil.setVisible(View.VISIBLE, aiTagIv)
        } else {
            ViewStatusUtil.setVisible(View.GONE, aiTagIv)
        }

//        mIvSoundCover?.setImageResource(R.drawable.main_cover_default_album_light)
//
//        ImageManager.from(context).displayImage(
//            mIvSoundCover, soundInfo?.trackInfo?.validCover,
//            R.drawable.main_cover_default_album_light)
//
//        ImageManager.from(context).displayImage(
//            skinCoverImage, soundInfo?.trackInfo?.validCover,
//            R.drawable.main_cover_default_album_light)
    }

    override fun onMyResumeOnly() {
        super.onMyResumeOnly()
    }

    override fun onPause() {
        super.onPause()
        rotationAnimator?.pause()
    }

    override fun onResume() {
        super.onResume()

        if (PSkinManager.isEnabled && XmPlayerManager.getInstance(context).isPlaying) {
            startRotation()
        }
    }

    override fun onPlayStart() {
        if (PSkinManager.isEnabled) {
            startRotation()
        }
    }

    override fun onPlayPause() {
        if (PSkinManager.isEnabled) {
            rotationAnimator?.pause()
        }
    }

    override fun onPlayStop() {
        if (PSkinManager.isEnabled) {
            rotationAnimator?.pause()
        }
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {

        if (ViewStatusUtil.viewIsRealShowing(mIvSoundCover)) {
            XMTraceApi.Trace()
                .setMetaId(49531)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currTrackId", "$curTrackId")
                .put("currAlbumId", "$curAlbumId")
                .put(XmRequestIdManager.CONT_ID, "$curTrackId")
                .put(XmRequestIdManager.CONT_TYPE, "newPlayCover")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                        XmRequestPage.PAGE_PLAY_PAGE
                    )
                )
                .createTrace()
        }
    }

    override fun onError(exception: XmPlayerException?): Boolean {
        return true
    }

    override fun onSoundPlayComplete() {
    }

    override fun show() {
        realShow(false)
    }

    private fun realShow(fadeIn: Boolean) {
        if (!isVisible) {
            // 新声音播放页-封面图  控件曝光
            XMTraceApi.Trace()
                .setMetaId(49531)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currTrackId", "$curTrackId")
                .put("currAlbumId", "$curAlbumId")
                .put(XmRequestIdManager.CONT_ID, "$curTrackId")
                .put(XmRequestIdManager.CONT_TYPE, "newPlayCover")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                        XmRequestPage.PAGE_PLAY_PAGE
                    )
                )
                .createTrace()
        }
        super.show()
    }
}