package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.constants.TrackPageFromSourceEnum
import com.ximalaya.ting.android.host.constants.TrackPageFromSourceUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.NewShowNotesManager
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.ShowTagManager
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.view.CornerRelativeLayout
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.find.util.ShowTagPlayUtil
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * Created by felix.chen on 2023/3/13.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18621868330
 */
class RecommendChasingForUpdateListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendChasingForUpdateListAdapterProviderStaggered.ChasingUpdateCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendChasingForUpdateListAdapterProviderStaggered.ChasingUpdateCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendChasingForUpdateListAdapterProviderStaggered.ChasingUpdateCardViewHolder, RecommendItemNew> {

    private var mOldState = RecyclerView.SCROLL_STATE_IDLE

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_chasing_for_update_list_card, parent, false)
    }

    class ChasingUpdateCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val rcvSocialList: RecyclerView = convertView.findViewById(R.id.main_rcv_album_list)
        var tvSocialListTittle: TextView = convertView.findViewById(R.id.main_tv_social_list_tittle)
        var tvMore: TextView = convertView.findViewById(R.id.main_iv_more)
        var strartSnapHelper: StartSnapHelper? = null
        var lastScreenWidth: Int = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        var firstVisiblePosition: Int = 0
        var spanCount = 2
        var uniqueId: String = ""
        init {
            resetSize()
        }

        fun resetSize() {
//            val context = BaseApplication.getMyApplicationContext()
        }
    }

    override fun onConfigurationChanged(holder: ChasingUpdateCardViewHolder?) {
        holder ?: return
        if (holder.lastScreenWidth == BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())) {
            return
        }
        holder.lastScreenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        holder.resetSize()
    }

    override fun createViewHolder(convertView: View?): ChasingUpdateCardViewHolder? {
        if (convertView == null) {
            return null
        }
        return ChasingUpdateCardViewHolder(convertView)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun bindViewHolder(
        holder: ChasingUpdateCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        if (!TextUtils.isEmpty(recommendCommonItem.ext?.extraInfo?.motName)) {
            val jsonObject = JSONObject()
            jsonObject.put("motName", recommendCommonItem.ext!!.extraInfo!!.motName)
            jsonObject.put("motPosition", (position + 1))
            RecommendFragmentNetManager.sMotFreshListMap?.put(recommendCommonItem.ext.extraInfo!!.motName!!, jsonObject)
        }
        holder.tvSocialListTittle.text = recommendCommonItem.title

        if (TextUtils.isEmpty(recommendCommonItem.landingPage)) {
            ViewStatusUtil.setVisible(View.INVISIBLE, holder.tvMore)
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.tvMore)
        }

        HomeMoreColorUtil.filterColor(holder.tvMore)
        holder.tvMore.setOnOneClickListener {
            if (TextUtils.isEmpty(recommendCommonItem.landingPage)) {
                return@setOnOneClickListener
            }
            var trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem?.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem?.ubtV2)
            trace1.createTrace()
            // 新首页-订阅更新卡片-更多  点击事件
            var trace = XMTraceApi.Trace()
                .click(62029) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("action", "click")

            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                "更多",
                "d01"
            )

            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
            ToolUtil.clickUrlAction(fragment, recommendCommonItem.landingPage!!, holder.tvMore)
        }
        val listSize = recommendCommonItem.subElements!!.size
        holder.spanCount = getSpanCount(listSize)
        if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() && position == 1) {
            // 习惯听小卡样式 只要数据达到了默认显示3行提高屏效
            if (RecommendFragmentTypeManager.isNewSceneCard()) {
                if (listSize > 2) {
                    holder.spanCount = 3
                }
            } else {
                // 习惯听大卡样式 下面是广告只显示2行样式
                if (holder.spanCount > 2) {
                    holder.spanCount = 2
                    RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
                        this.javaClass.simpleName,
                        holder.spanCount, position, recommendItemNew
                    )
                }
            }
        }
        val cardAlbumListAdapter = SocialListAlbumItemAdapter(
            dataAction,
            fragment,
            recommendCommonItem,
            recommendItemNew,
            recommendCommonItem.subElements!!,
            position,
            holder.spanCount,
            !TextUtils.isEmpty(recommendCommonItem.landingPage) && holder.spanCount > 1
        )
        cardAlbumListAdapter.mEnableMoreItem = cardAlbumListAdapter.enableJumpMore
        cardAlbumListAdapter.setRelaseJumpActivityListener {
            if (TextUtils.isEmpty(recommendCommonItem.landingPage)) {
                return@setRelaseJumpActivityListener
            }
            var trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem?.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem?.ubtV2)
            trace1.createTrace()
            // 新首页-订阅更新卡片-更多  点击事件
            var trace = XMTraceApi.Trace()
                .click(62029) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("action", "slide")

            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
            ToolUtil.clickUrlAction(fragment, recommendCommonItem.landingPage!!, holder.tvMore)
        }
        // 声音列表
        holder.rcvSocialList.adapter = cardAlbumListAdapter
        val layoutManager = GridLayoutManager(convertView?.context, holder.spanCount, GridLayoutManager.HORIZONTAL, false)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) holder.spanCount else 1
            }
        }
        holder.rcvSocialList.layoutManager = layoutManager
        if (holder.strartSnapHelper == null) {
            holder.strartSnapHelper = StartSnapHelper()
            holder.strartSnapHelper!!.attachToRecyclerView(holder.rcvSocialList)
            holder.strartSnapHelper!!.setContainerView(holder.rcvSocialList)
        }
        holder.rcvSocialList.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(recommendItemNew, position, holder)
                    holder.firstVisiblePosition = (holder.rcvSocialList.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                }
            }
        })
        if (recommendItemNew.xmRequestId != null && (recommendItemNew.xmRequestId + position).equals(holder.uniqueId)) {
            holder.rcvSocialList.scrollToPosition(holder.firstVisiblePosition)
        } else {
            holder.uniqueId = recommendItemNew.xmRequestId + position
        }

        // 有声音样式  缓存下
        val item = recommendCommonItem.subElements?.find { "Track" == it.bizType }
        ShowTagPlayUtil.setRecyclerViewTag(item != null, holder, holder.rcvSocialList)

    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ChasingUpdateCardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    var trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("modulePosition", (position + 1).toString())
                        .put("currPage", "newHomePage")
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    if (data?.isLocalCache == true) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }
                var childCount = holder!!.rcvSocialList.childCount
                for (i in 0 until childCount) {
                    val view = holder!!.rcvSocialList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        var trackId = subElement.refId ?: 0
                        val albumId = subElement.extraInfo?.albumId ?: 0L
                        // 新首页-订阅更新卡片  控件曝光
                        var trace = XMTraceApi.Trace()
                            .setMetaId(62028)
                            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                            .put("currPage", "newHomePage")
                            .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 可见区域占屏幕的比例
                            .put("trackId", trackId.toString())
                            .put("albumId", albumId.toString())
                            .put("positionNew", (index + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                            .put("contentId", trackId.toString())
                            .put("xmRequestId", data.xmRequestId)
                            .put("modulePosition", (position + 1).toString())
                            .put("contentType", subElement.contentType ?: "") // 传接口返回的 bizType
                        SpmTraceUtil.addSpmTraceInfo(
                            trace,
                            recommendCommonItem.ubtV2,
                            (position + 1).toString(),
                            contentTitle = subElement.title,
                            contentPosition = (index + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
                        if (data?.isLocalCache == true) {
                            trace.isLocalCache
                        }
                        trace.createTrace()

                        HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, view, index)
                    }
                }
            }
        }
    }

    class SocialListAlbumItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        // 页面
        private val fragment: BaseFragment2,
        // 卡片数据
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        // 专辑列表
        list: List<CommonSubElement>,
        var modulePosition: Int,
        var spanCount: Int,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 专辑列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            commonSubElementList.addAll(list)
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            return SocialListAlbumViewHolder(
                LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(
                    R.layout.main_item_recommend_chasing_for_update_list_item, parent, false
                )
            )
        }


        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is SocialListAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        fun onBindViewHolderInner(holder: SocialListAlbumViewHolder, position: Int) {
            val commonSubElement = commonSubElementList[position] ?: return
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            val remainder: Int = commonSubElementList.size % spanCount
            val start: Int = if (remainder == 0) commonSubElementList.size - spanCount else commonSubElementList.size - remainder
            val layoutParams = holder.cslContainerView.layoutParams
            val textViewContainerWithInPx: Int
            if (position >= start) {
                // 最后一列
                layoutParams.width = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
                textViewContainerWithInPx = getRpAdaptSize(375) - if (enableJumpMore) 37.dp else 0
            } else {
                layoutParams.width = getRpAdaptSize(337 - getOffset())
                textViewContainerWithInPx = getRpAdaptSize(337 - getOffset())
            }

            holder.crlCoverParent.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getPaddingSize()
                params.bottomMargin = params.topMargin
                params.width = RecommendCornerUtils.getSocialCoverSize().toInt()
                params.height = params.width
                this.layoutParams = params

                setCornerRadius(RecommendCornerUtils.getSocialCorner())
            }

            holder.showTagsLayout.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }

            holder.itemTitleTv.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)

            val coverSize = RecommendCornerUtils.getSocialCoverSizeDp()
            // 16 + 70 + 12 +54
            // other宽度16 + 70 + 12 + 16 = 114
            val otherWidth = if (NewShowNotesManager.userNewShowNotes()) {
                16 + coverSize + 12 + 54
            } else {
                16 + coverSize + 12 + 16
            }

            val containerWidthInPx = textViewContainerWithInPx - otherWidth.dp

            if (RecommendShowTagsUtilNew.canShowOneLine(
                    holder.itemTitleTv,
                    commonSubElement.title,
                    containerWidthInPx
                )
            ) {
                if (TextUtils.isEmpty(commonSubElement.summary)) {
                    ViewStatusUtil.setVisible(View.GONE, holder.itemSubtitle1Tv)
                } else {
                    holder.itemSubtitle1Tv.text = commonSubElement.summary
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.itemSubtitle1Tv)
                    RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitle1Tv)
                }
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.itemSubtitle1Tv)
            }

            if (commonSubElement.extraInfo?.showTags.isNullOrEmpty()) {
                val newList = mutableListOf<ShowTag>()
                if (!TextUtils.isEmpty(commonSubElement.extraInfo?.subTitle1)) {
                    val tag1 = ShowTag()
                    tag1.tag = commonSubElement.extraInfo?.subTitle1
                    tag1.type = RecommendShowTagsUtilNew.TYPE_ALBUM_TITLE
                    newList.add(tag1)
                }
                val subTitle2 = convertTimeNew(commonSubElement.extraInfo?.trackUpdateAt ?: 0L) + "更新"
                if (!TextUtils.isEmpty(subTitle2)) {
                    val tag2 = ShowTag()
                    tag2.tag = subTitle2
                    tag2.type = RecommendShowTagsUtilNew.TYPE_OTHER
                    newList.add(tag2)
                }
                ShowTagManager.bindTagsView(
                    holder.showTagsLayout,
                    newList,
                    containerWidthInPx,
                    commonSubElement.refId
                )
            } else {
                ShowTagManager.bindTagsView(
                    holder.showTagsLayout, commonSubElement.extraInfo?.showTags,
                    containerWidthInPx, commonSubElement.refId
                )
            }

            ShowTagPlayUtil.setHolderTrackItem(
                holder,
                commonSubElement.refId,
                holder.showTagsLayout
            )

            var hasValidServerColor = false
            val track = Track()
            track.dataId = commonSubElement.refId ?: 0
            val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
            if (isPlaying) {
                holder.ivPlayButton.setImageResource(R.drawable.host_pause_btn_n_fill_n_28)
            } else {
                holder.ivPlayButton.setImageResource(R.drawable.host_play_btn_inside_fill_n_28)
            }
            if (commonSubElement.cachedCoverColor != null && commonSubElement.cachedCoverColor != ColorUtil.INVALID_COLOR) {
                hasValidServerColor = true
                setPlayBgColor(commonSubElement.cachedCoverColor!!, holder)
            }
            ImageManager.from(BaseApplication.getMyApplicationContext()).displayImageNotIncludeDownloadCacheSizeInDp(
                holder.itemCoverIv,
                commonSubElement.cover,
                com.ximalaya.ting.android.host.R.drawable.host_default_album,
                70,
                70
            ) { lastUrl, bitmap ->
                if (bitmap != null && !hasValidServerColor) {
                    LocalImageUtil.getDomainColorForRecommend(
                        bitmap,
                        Color.BLACK
                    ) { color: Int ->
                        setPlayBgColor(color, holder)
                        commonSubElement.cachedCoverColor = color
                    }
                }
            }

            if (NewShowNotesManager.userNewShowNotes()) {
                holder.showNotePlayBtnBg.run {
                    val params = this.layoutParams as MarginLayoutParams
                    params.width = RecommendCornerUtils.getShowNoteSize()
                    params.height = params.width
                    this.layoutParams = params
                }

                holder.showNotePlayBtn.run {
                    val params = this.layoutParams
                    params.width = RecommendCornerUtils.getShowNotePlaySize()
                    params.height = params.width
                    this.layoutParams = params
                }

                RecommendCornerUtils.updateShowNoteRoundBg(holder.showNotePlayBtnBg)
            }

            if (NewShowNotesManager.userNewShowNotes()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.GONE, holder.vPlayButtonBg, holder.ivPlayButton)
                if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying &&
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId == commonSubElement.refId) {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, true)
                } else {
                    RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, false)
                }
                holder.showNotePlayBtnBgWrap.setOnClickListener {
                    onItemClickInner(commonSubElement, position, it, true)
                }
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.showNotePlayBtnBgWrap)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.vPlayButtonBg, holder.ivPlayButton)
            }
            holder.itemView.setOnClickListener {
                onItemClickInner(commonSubElement, position, it, false)
            }

            holder.itemView.setOnLongClickListener {
                val build = MoreFuncBuild.createTrackLongClickModel(
                    fragment, (commonSubElement.refId ?: 0), null
                )
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", commonSubElement.bizType ?: "")
                    put("contentId", commonSubElement.refId?.toString() ?: "")
                    put("modulePosition", (modulePosition + 1).toString())
                    put("positionNew", (position + 1).toString())
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap
                XmMoreFuncManager.checkShowMorePage(build)
                true
            }
        }

        private fun onItemClickInner(commonSubElement: CommonSubElement, position: Int, view: View?, isClickPlayBtn: Boolean) {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return
            }
            val trackId = commonSubElement.refId ?: 0
            val albumId = commonSubElement.extraInfo?.albumId ?: 0L
            var trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2)
            trace1.createTrace()
            // 新首页-订阅更新卡片  点击事件
            var trace = XMTraceApi.Trace()
                .click(62027) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("trackId", trackId.toString())
                .put("albumId", albumId.toString())
                .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 可见区域占屏幕的比例
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("contentId", trackId.toString())
                .put("modulePosition", (modulePosition + 1).toString())
                .put("contentType", commonSubElement.contentType ?: "")
                .put("area", if (isClickPlayBtn) "play" else "item")
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = commonSubElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()

            var lastLandingPage = NewShowNotesManager.appendPodListModeParam(commonSubElement.landingPage)
            lastLandingPage = TrackPageFromSourceUtil.appendPageFromSourceParam(lastLandingPage, TrackPageFromSourceEnum.SOURCE_CHASING_UPDATE)
            if (!isClickPlayBtn && NewShowNotesManager.userNewShowNotes()) {
                // 打开shownotes二级页
                NewShowNotesManager.startShowNotesDetailFragment(NewShowNotesManager.SOURCE_FROM_HOME, lastLandingPage, 0,
                    trackId, null)
            } else {
                if (isClickPlayBtn) {
                    val track = Track()
                    track.dataId = trackId
                    val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
                    if (isPlaying) {
                        PlayTools.pause(BaseApplication.getMyApplicationContext(), PauseReason.Business.RecommendChasingForUpdateList_SocialListAlbum)
                        return
                    }
                }
                ToolUtil.clickUrlAction(fragment, lastLandingPage, view)
            }
        }

        private fun setPlayBgColor(color: Int, holder: SocialListAlbumViewHolder) {
            val gradientDrawable = GradientDrawable()
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.cornerRadius = 13.dp.toFloat()
            gradientDrawable.setColor(color)
            holder.vPlayButtonBg.background = gradientDrawable
        }

        override fun getItemCount(): Int {
            if (enableJumpMore) {
                return commonSubElementList.size + 1
            }
            return commonSubElementList.size
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class SocialListAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var crlCoverParent: CornerRelativeLayout = view.findViewById(R.id.main_crl_item_cover)
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var itemCoverIv: ImageView = view.findViewById(R.id.main_iv_item_cover_track)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var showTagsLayout: LinearLayout = view.findViewById(R.id.main_show_tags_layout)

            var vPlayButtonBg: View = view.findViewById(R.id.main_v_bg_play)
            var ivPlayButton: ImageView = view.findViewById(R.id.main_iv_play_btn)
            var showNotePlayBtnBgWrap: View = view.findViewById(R.id.main_show_notes_play_layout_wrap)
            var showNotePlayBtnBg: View = view.findViewById(R.id.main_show_notes_play_layout)
            var showNotePlayBtn: ImageView = view.findViewById(R.id.main_iv_show_notes_play_btn)

            init {
                resetSize()
            }

            fun resetSize() {
                var layoutParams = cslContainerView.layoutParams
                layoutParams.width = getRpAdaptSize(341)
            }
        }
    }

    companion object {
        // 基准宽度
        private const val DESIGN_BASE_WITH = 375f

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        fun getSpanCount(subElementsSize: Int): Int {
            return if (subElementsSize <= 2) {
                1
            } else if (subElementsSize <= 4) {
                2
            } else {
                3
            }
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        fun convertTimeNew(l: Long): String? {
            val curTime = System.currentTimeMillis()
            if (l <= 0 || curTime < l) {
                return TimeHelper.countTimeNew(l)
            }
            val between = (curTime - l) / 1000 // 除以1000是为了转换成秒
            val day = between / (24 * 3600)
            val hour = between / 3600
            val minute = between / 60
            return if (!TimeHelper.isSameYear(l)) {
                SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(l)
            } else if (day >= 30) {
                SimpleDateFormat("MM-dd", Locale.getDefault()).format(l)
            } else if (day >= 1) {
                //小于1个月大于等于1天
                day.toString() + "天前"
            } else if (hour >= 1) {
                //小于1天大于等于1个小时
                hour.toString() + "小时前"
            } else if (minute >= 1) {
                //小于1个小时大于等于1分钟
                minute.toString() + "分钟前"
            } else {
                "刚刚"
            }
        }
    }
}