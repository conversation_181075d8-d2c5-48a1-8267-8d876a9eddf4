package com.ximalaya.ting.android.main.playpage.playy.component.cover.manager;

import android.content.Context;
import android.content.res.Configuration;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.viewpager2.widget.ViewPager2;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Consumer;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.PlayPageAdFreeManager;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.read.manager.TingTextToReaderManager;
import com.ximalaya.ting.android.host.util.PlayPageManuscriptViewUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.audiointerface.IAddListenerFunction;
import com.ximalaya.ting.android.main.playpage.audiointerface.IAudioPlayPageLifecycle;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAdaptationUtilKt;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.ISkinAdShowCallBack;
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.components.cover.CoverSwitchStatus;
import com.ximalaya.ting.android.main.playpage.internalservice.ICoverComponentsManagerService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel;
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer;
import com.ximalaya.ting.android.main.playpage.playy.ScreenChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment;
import com.ximalaya.ting.android.main.playpage.playy.XPlayPageStatus;
import com.ximalaya.ting.android.main.playpage.playy.XTabChangeCallback;
import com.ximalaya.ting.android.main.playpage.playy.YUtils;
import com.ximalaya.ting.android.main.playpage.playy.biz.YellowBarSizeChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponent;
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponentWithPlayStatusListener;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.YNormalCoversComponentY;
import com.ximalaya.ting.android.main.playpage.playy.component.danmu.XDanmakuComponent;
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.YFunctionEntriesComponentV2;
import com.ximalaya.ting.android.main.playpage.playy.component.immersive.XImmersive;
import com.ximalaya.ting.android.main.playpage.playy.component.navi.YNavigationBarComponent;
import com.ximalaya.ting.android.main.playpage.playy.component.tips.v2.YTipsComponentV2;
import com.ximalaya.ting.android.main.playpage.playy.component.video.YVideoComponent;
import com.ximalaya.ting.android.main.playpage.playy.media.DocComponentWrapper;
import com.ximalaya.ting.android.main.playpage.playy.media.StagePager;
import com.ximalaya.ting.android.main.playpage.playy.tabs.XPlayPageTab;
import com.ximalaya.ting.android.main.playpage.playy.view.PlayTypeIndicator;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Created by WolfXu on 2020-05-11.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class YCoverComponentsManager implements IAudioPlayPageLifecycle, ScreenChangeListener, XTabChangeCallback {
    private static final String TAG = "CoverManagerX";
    public static final int CATEGORY_BOOK = 3;


    public static final int COVER_ADD_HEIGHT_FOR_VERTICAL_AD_SHOW_DP = 175; // 当竖屏广告显示时，半屏区增加的高度
    public static int TOP_CONTAINER_HEIGHT_NOMAL;


    private ArrayList<ICoverChangeListener> mCoverChangeListeners = new ArrayList<>();
    private final List<XBaseCoverComponent> xComponents = new ArrayList<>();
    private final Map<YCoverComponentsEnum, XBaseCoverComponent> xComponentsMap = new HashMap<>();
    private YPlayFragment mFragment;
    private boolean mIsLargeDevice;
    private PlayingSoundInfo mSoundInfo;

    private XBaseCoverComponent mCurrentMutexCoverComponent;

    private YCoverPriorityConfig.CurrentStageConfig currentStageConfig;
    private boolean mHasFinishCreateComponents;

    @NotNull private final IPlayContainer playContainer;
    @NonNull private final XPlayViewModel xPlayViewModel;
    @NonNull private final ViewPager2 stagePagerView;

    @NotNull private final YVideoComponent yVideoComponent;


    private StagePager stagePager;

    private YNavigationBarComponent navigationBarComponent;
    private XDanmakuComponent danmakuComponent;
    private YFunctionEntriesComponentV2 functionEntriesComponentV2;

    private YTipsComponentV2 tipsComponentV2;

    private boolean keepUpdateConsistency = false;
    private boolean hideCover = false;


    private YCoverComponentsEnum lastShowEnum = null;

    public YCoverComponentsManager(
            @NotNull IPlayContainer playContainer,
            @NotNull XPlayViewModel xPlayViewModel,
            @NotNull ViewPager2 stagePagerView,
            @NotNull YVideoComponent yVideoComponent
    ) {
        this.playContainer = playContainer;
        this.xPlayViewModel = xPlayViewModel;
        this.stagePagerView = stagePagerView;
        this.yVideoComponent = yVideoComponent;

        TOP_CONTAINER_HEIGHT_NOMAL = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 368);
        playContainer.addScreenChangeListener(this);

        PlayPageInternalServiceManager.getInstance().registerService(ICoverComponentsManagerService.class, new ICoverComponentsManagerService() {
            @Override
            public void updateComponents() {
                YCoverComponentsManager.this.updateComponents();
            }

            @Override
            public boolean isFullscreen() {
                return false;
            }

            @Override
            public void updateFullscreenHeight() {
            }

            @Override public void setDanmakuOpen(boolean danmakuOpen) {
                YCoverComponentsManager.this.setDanmakuOpen(danmakuOpen);
            }
        });
    }


    public void setStageDanmaku(XDanmakuComponent danmakuComponent) {
        this.danmakuComponent = danmakuComponent;
        if (stagePager != null) {
            stagePager.setStageDanmaku(danmakuComponent);
        }
    }

    public static boolean isCoverFullScreen() {
        ICoverComponentsManagerService service = PlayPageInternalServiceManager.getInstance().getService(ICoverComponentsManagerService.class);
        if (service != null) {
            return service.isFullscreen();
        }
        return false;
    }

    @NonNull
    public IPlayContainer getPlayContainer() {
        return playContainer;
    }

    public void createComponents(YPlayFragment fragment) {
        mFragment = fragment;
        initCoverHeight(fragment);

        List<YCoverComponentsEnum> validComponents =  YCoverPriorityConfig.validComponents();
        for (YCoverComponentsEnum yCoverComponentsEnum : validComponents) {
            try {
                if (yCoverComponentsEnum == YCoverComponentsEnum.AD_COMPONENT) {
                    if (ConstantsOpenSdk.isDebug) {
                        if (BaseUtil.getIntSystemProperties("debug.xima.adfree") > 1) {
                            continue;
                        }
                    }
                }

                XBaseCoverComponent component = yCoverComponentsEnum.newComponent(this);
                if (component != null) {
                    component.onCreate(fragment);

                    if (component instanceof YNormalCoversComponentY) {
                        component.show();
                        component.hide();
                    }

                    if (needComponentListenPlayStatusEvenHidden(component)) {
                        playContainer.addPlayStatusListener((IXmPlayerStatusListener) component);
                    }
                    if (component instanceof IXmAdsStatusListener) {
                        fragment.addAdsStatusListener((IXmAdsStatusListener) component);
                    }

                    xComponents.add(component);
                    xComponentsMap.put(component.getCoverComponentsEnum(), component);
                }
            } catch (Exception e) {
                e.printStackTrace();
                if(ConstantsOpenSdk.isDebug) {
                    throw new RuntimeException("createComponents " + e);
                }
            }
        }
        mHasFinishCreateComponents = true;

//        XBaseCoverComponent docComponent = xComponentsMap.remove(YCoverComponentsEnum.DOC_ON_COVER_COMPONENT);
//        xComponents.remove(docComponent);
//        docComponent.show();

        //        View coverView = xComponentsMap.get(YCoverComponentsEnum.COVERS_COMPONENT_NEW).getView();
        View bottomRefView = fragment.findViewById(R.id.main_rv_func_root);
        PlayTypeIndicator playTypeIndicator = fragment.findViewById(R.id.main_play_type_indicator);

        ViewPager2 backgroundViewPager = fragment.findViewById(R.id.main_background_with_mode);
        ViewPager2 topViewPager = fragment.findViewById(R.id.main_top_background_with_mode);
        stagePager = new StagePager(
                playContainer,
                stagePagerView,
                backgroundViewPager,
                topViewPager,
                playTypeIndicator,
                bottomRefView,
                new YNormalCoversComponentY(playContainer, this),
                new DocComponentWrapper(playContainer, this, bottomRefView),
                yVideoComponent
        );
    }

    public void updateDocTranslate(boolean isOpen) {
        if (stagePager != null) {
            stagePager.updateDocTranslate(isOpen);
        }
    }

    private boolean needComponentListenPlayStatusEvenHidden(XBaseCoverComponent component) {
        return component instanceof XBaseCoverComponentWithPlayStatusListener && ((XBaseCoverComponentWithPlayStatusListener) component).needListenPlayStatusEvenHidden();
    }

    private void initCoverHeight(BaseFragment2 fragment) {
        Context context = null;
        if (fragment != null) {
            context = fragment.getContext();
        }
        if (context == null) {
            context = BaseApplication.getOptActivity();
        }

        mIsLargeDevice = AudioPlayPageAdaptationUtilKt.isLargeDevice();
    }

    @Override
    public void beforeFullScreenChanged(boolean fullScreen) {

    }

    @Override
    public void onFullScreenChanged(boolean fullScreen) {
        updateComponents();
    }

    @Override
    public void afterFullScreenChanged(boolean fullScreen) {

    }

    @Override
    public void onScreenStatusChanged(@NotNull XPlayPageStatus xPlayPageStatus) {

    }

    private void fakeNotify() {
        if (!ToolUtil.isEmptyCollects(mCoverChangeListeners) && lastShowEnum != YCoverComponentsEnum.MOT_CARD_COMPONENT) {
            lastShowEnum = YCoverComponentsEnum.MOT_CARD_COMPONENT;
            for (ICoverChangeListener coverChangeListener : mCoverChangeListeners) {
                coverChangeListener.onMutexCoverChanged(YCoverComponentsEnum.MOT_CARD_COMPONENT);
            }
        }
    }

    public void hideComponents() {
        hideCover = true;
        currentStageConfig = null;
        boolean hide = false;
        for (XBaseCoverComponent component : xComponentsMap.values()) {
            if (component.isVisible()) {
                component.hide();
                hide = true;
            }
        }


        fakeNotify();

        if (stagePager != null) {
            stagePager.onStageConfigUpdate(null);
        }
    }

    public void showComponents() {
        hideCover = false;
        updateComponents();
    }

    public void updateComponents() {
        Logger.d("CoverComponentsManager", "updateComponents");
        updateComponentsStatus(mSoundInfo);
    }

    public void updateAttachedComponentsOnly() {
        hookUpdateComponentsStatus(mSoundInfo, true);
    }

    private void updateComponentsStatus(PlayingSoundInfo soundInfo) {
        hookUpdateComponentsStatus(soundInfo, false);
    }

    private void hookUpdateComponentsStatus(PlayingSoundInfo soundInfo, boolean updateAttachedComponentsONly) {
        if (ConstantsOpenSdk.isDebug) {
            if (keepUpdateConsistency) {
                throw new RuntimeException("updateComponentsStatus 不允许递归调用");
            }
        }
        if (playContainer.isVideoMode() || hideCover) {
            //忽略视频模式
            return;
        }
        keepUpdateConsistency = true;
        updateComponentsStatus(soundInfo, updateAttachedComponentsONly);
        keepUpdateConsistency = false;
    }

    private void handStageExtraConfig(
            YCoverPriorityConfig.CurrentStageConfig config, PlayingSoundInfo soundInfo) {

        YCoverPriorityConfig.StageExtraConfig stageExtraConfig = null;
        XBaseCoverComponent component = null;

        if (config != null) {
            stageExtraConfig = config.getStageExtraConfig();

            YCoverPriorityConfig.TitleConfig titleConfig = stageExtraConfig.getTitleConfig();
            if (navigationBarComponent != null && titleConfig != null) {
                navigationBarComponent.updateConfig(titleConfig);
            }
            component = getMutexCoverComponentByEnum(config.getMainComponentEnum());
        }

        stagePager.updateSkin(config != null);

        if (functionEntriesComponentV2 != null) {
            if (stageExtraConfig == null || stageExtraConfig.getHasFunctionBar().invoke(component, soundInfo)) {
                functionEntriesComponentV2.showForCover();
            } else {
                functionEntriesComponentV2.hideForCover();
            }
        }


        if (tipsComponentV2 != null) {
            tipsComponentV2.setVisibilityByCover(stageExtraConfig == null || stageExtraConfig.getHasTips().invoke(component, soundInfo));
        }
    }

    public boolean curMainComponentCanShowDanMuBtn() {
        if (currentStageConfig == null) {
            return false;
        }
        YCoverPriorityConfig.StageExtraConfig stageExtraConfig = currentStageConfig.getStageExtraConfig();
        if (stageExtraConfig == null) {
            return false;
        }

        return stageExtraConfig.getShowDanMuEntry();
    }

    private boolean isAdFree(PlayingSoundInfo soundInfo) {
        if (soundInfo == null) return false;
        PlayingSoundInfo.AlbumInfo albumInfo = soundInfo.albumInfo;
        if (albumInfo == null) return false;
        return PlayPageAdFreeManager.isFreeStatic(albumInfo.albumId);
    }

    private void updateComponentsStatus(PlayingSoundInfo soundInfo, boolean updateAttachedComponentsONly) {
        if (mFragment == null || !mFragment.canUpdateUi() || !mHasFinishCreateComponents) {
            return;
        }

        YCoverPriorityConfig.CoverMatchConfig matchConfig = YCoverPriorityConfig.match(playContainer.isFullScreen(), coverMatchConfig -> {
            YCoverComponentsEnum xTempCoverComponentsEnums = coverMatchConfig.getMainComponentEnum();
            if (xTempCoverComponentsEnums == YCoverComponentsEnum.AD_COMPONENT) {
                if (isAdFree(soundInfo)) {
                    Log.d(TAG, "match ad free");
                    return Boolean.FALSE;
                }
            }

            // 舞台区切换声音（从播放页外切换）5秒内不出卡片
            if (YCoverPriorityConfig.INSTANCE.isStageAreaCardComponent(xTempCoverComponentsEnums)
                    && playContainer != null && !playContainer.canShowStageAreaCard()) {
                return Boolean.FALSE;
            }

            XBaseCoverComponent component = xComponentsMap.get(xTempCoverComponentsEnums);
            if (component == null || !component.needShowThisComponent(soundInfo)) {
                return Boolean.FALSE;
            }
            return coverMatchConfig.getExtraConditions().invoke(this);
        });

        if (matchConfig == null) {
            // 处理需要隐藏的
            for (YCoverComponentsEnum yCoverComponentsEnum : YCoverComponentsEnum.values()) {
                XBaseCoverComponent componentTobeHide = xComponentsMap.get(yCoverComponentsEnum);
                if (componentTobeHide != null && componentTobeHide.isVisible()) {
                    hideComponent(componentTobeHide);
                }
            }

            handStageExtraConfig(null, soundInfo);

            currentStageConfig = null;

            fakeNotify();

            if (stagePager != null) {
                stagePager.onStageConfigUpdate(null);
            }
            return;
        }

        // 处理附加组件
        List<YCoverPriorityConfig.Addition> additions = matchConfig.getAdditions();
        YCoverPriorityConfig.StageExtraConfig additionStageExtraConfig = null;
        YCoverComponentsEnum secondCoverComponentsEnum = null;
        boolean additionAsMain = false;

        if (additions != null) {
            for(YCoverPriorityConfig.Addition addition : additions) {
                YCoverComponentsEnum additionEnum = addition.getComponentEnum();
                // 舞台区切换声音（从播放页外切换）5秒内不出卡片
                if (YCoverPriorityConfig.INSTANCE.isStageAreaCardComponent(additionEnum)
                        && playContainer != null && !playContainer.canShowStageAreaCard()) {
                    continue;
                }
                XBaseCoverComponent additionComponent = xComponentsMap.get(additionEnum);
                if (additionComponent != null
                        && additionComponent.needShowThisComponent(soundInfo)
                        && addition.getExtraConditions().invoke(this)) {
                    secondCoverComponentsEnum = addition.getComponentEnum();
                    additionStageExtraConfig = addition.getStageExtraConfig();
                    additionAsMain = addition.getAsMain();
                    break;
                }
            }
        }

        YCoverPriorityConfig.CurrentStageConfig stageConfig =
                new YCoverPriorityConfig.CurrentStageConfig(
                        matchConfig.getMainComponentEnum(),
                        secondCoverComponentsEnum,
                        additionStageExtraConfig != null? additionStageExtraConfig : matchConfig.getStageExtraConfig()
                );

        if (ConstantsOpenSdk.isDebug) {
            try {
                Log.d(TAG, stageConfig.toString());
            } catch (Throwable t) {
                try {
                    Log.d(TAG, "getMainComponentEnum = " + stageConfig.getMainComponentEnum().name());
                } catch (Throwable e) {/*ignore*/}
            }
        }

        handStageExtraConfig(stageConfig, soundInfo);

        if (!stageConfig.equals(currentStageConfig)) {
            currentStageConfig = stageConfig;

            List<XBaseCoverComponent> tobeAttached = new ArrayList<>();

            XBaseCoverComponent secComponent = xComponentsMap.get(stageConfig.getAdditionComponentEnum());
            if (secComponent != null) {
                if (!secComponent.isVisible()) {
                    showComponent(soundInfo, secComponent);
                }
                tobeAttached.add(secComponent);
            }

            XBaseCoverComponent component = xComponentsMap.get(stageConfig.getMainComponentEnum());
            if (component != null) {
                if (!component.isVisible()) {
                    showComponent(soundInfo, component);
                }
                component.onComponentAttached(tobeAttached);
            }


            // 处理需要隐藏的
            for (YCoverComponentsEnum yCoverComponentsEnum : YCoverComponentsEnum.values()) {
                if (yCoverComponentsEnum != stageConfig.getAdditionComponentEnum()
                        && yCoverComponentsEnum != stageConfig.getMainComponentEnum()) {
                    XBaseCoverComponent componentTobeHide = xComponentsMap.get(yCoverComponentsEnum);
                    if (componentTobeHide != null && componentTobeHide.isVisible()) {
                        hideComponent(componentTobeHide);
                    }
                }
            }

            YCoverComponentsEnum mutexCover = stageConfig.getMainComponentEnum();
            if (additionAsMain && stageConfig.getAdditionComponentEnum() != null) {
                mutexCover = stageConfig.getAdditionComponentEnum();
            }
            mCurrentMutexCoverComponent = xComponentsMap.get(mutexCover);
            //notify
            if (!ToolUtil.isEmptyCollects(mCoverChangeListeners) && lastShowEnum != mutexCover) {
                lastShowEnum = mutexCover;
                for (ICoverChangeListener coverChangeListener : mCoverChangeListeners) {
                    coverChangeListener.onMutexCoverChanged(mutexCover);
                }
            }
        }

        if (stagePager != null) {
            stagePager.onStageConfigUpdate(matchConfig);
        }

        //hide immersive
        if (YCoverComponentsEnum.IMMERSIVE_SKIN_AD != stageConfig.getAdditionComponentEnum()
                && YCoverComponentsEnum.IMMERSIVE_SKIN_AD != stageConfig.getMainComponentEnum()) {
            XBaseCoverComponent componentTobeHide = xComponentsMap.get(YCoverComponentsEnum.IMMERSIVE_SKIN_AD);
            if (componentTobeHide != null ) {
                hideComponent(componentTobeHide);
            }
        }
    }

    public void hideComponent(XBaseCoverComponent component) {
        component.hide();
        if (playContainer != null && component instanceof IXmPlayerStatusListener
                && !needComponentListenPlayStatusEvenHidden(component)) {
            playContainer.removePlayStatusListener((IXmPlayerStatusListener) component);
        }
    }

    public void showComponent(PlayingSoundInfo soundInfo, XBaseCoverComponent component) {
        component.show();
        if (playContainer != null && component instanceof IXmPlayerStatusListener
                && !needComponentListenPlayStatusEvenHidden(component)) {
            playContainer.addPlayStatusListener((IXmPlayerStatusListener) component);
        }
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                if (mSoundInfo != null) {
                    component.onSoundInfoLoaded(mSoundInfo);
                }
            }
        });
    }

    @Override
    public void onMyResumeOnly() {
        notifyComponent(XBaseCoverComponent::onMyResumeOnly);
    }

    @Override
    public void onResume() {
        notifyComponent(XBaseCoverComponent::onResume);
    }

    @Override
    public void onPause() {
        notifyComponent(XBaseCoverComponent::onPause);
    }

    @Override
    public void onDestroy() {
        notifyComponent(XBaseCoverComponent::onDestroy);
        PlayPageInternalServiceManager.getInstance().unRegisterService(ICoverComponentsManagerService.class);
    }

    @Override
    public void onSoundInfoLoaded(@NonNull PlayingSoundInfo soundInfo) {
        mSoundInfo = soundInfo;
//        stagePager.onSoundInfoLoaded(soundInfo);

        if (playContainer.isVideoMode()) {
            //忽略视频模式
            return;
        }
        ISkinAdShowCallBack service =
                PlayPageInternalServiceManager.getInstance().getService(ISkinAdShowCallBack.class);
        if (service != null) {
            service.loadAdOnSoundInfoLoad(soundInfo);
        }
        
        updateComponentsStatus(soundInfo);
        
        for (XBaseCoverComponent component: xComponents) {
            if (component != null && component.isVisible()) {
                component.onSoundInfoLoaded(soundInfo);
            }
        }
    }

    public void onConfigurationChanged(Configuration newConfig) {
        if (stagePager != null) {
            stagePager.onConfigurationChanged(newConfig);
        }
    }

    @Override
    public void onTabSelect(@NonNull XPlayPageTab tab) {
        for (XBaseCoverComponent component: xComponents) {
            if (component != null && component instanceof  XTabChangeCallback) {
                ((XTabChangeCallback) component).onTabSelect(tab);
            }
        }
    }

    @Override
    public void onTabUnSelect(@NotNull XPlayPageTab tab) {

    }

    public PlayingSoundInfo getCurrentSoundInfo() {
        return mSoundInfo;
    }

    @Override
    public void onThemeColorChanged(int foregroundColor, int backgroundColor) {
        notifyComponent(component -> component.onThemeColorChanged(foregroundColor, backgroundColor));
    }

    private void notifyComponent(Consumer<XBaseCoverComponent> action) {
        for (XBaseCoverComponent component : xComponents) {
            action.accept(component);
        }

    }

    public void onDocSwitchChanged(boolean docShow) {
        for (XBaseCoverComponent component : xComponents) {
            if (component != null) {
                component.onDocSwitchChanged(docShow);
            }
        }

//        if (docShow) {
//            xPlayViewModel.requestPicData();
//        }
    }

    public int getCoverContainerHeight() {
        return TOP_CONTAINER_HEIGHT_NOMAL;
    }

    public YCoverComponentsEnum getCurrentCoverEnum() {
        if (mCurrentMutexCoverComponent != null)
            return mCurrentMutexCoverComponent.getCoverComponentsEnum();
        return null;
    }

    /**
     * 放大封面区高度
     */
    public void extendCoverHeight(int targetHeight) {
    }

    public void resetCoverHeightToNormal() {
    }

    public boolean isFullScreen() {
        return false;
    }

    public boolean isLargeDevice() {
        return mIsLargeDevice;
    }

    public boolean isDanmakuOpen() {
        return CoverSwitchStatus.danmaku(mSoundInfo);
    }

    public void setDanmakuOpen(boolean danmakuOpen) {
        boolean needUpdate = isDanmakuOpen() != danmakuOpen;
        PlayPageDataManager.getInstance().setDanmakuOpen(danmakuOpen);
        if (needUpdate) {
            CoverSwitchStatus.setDanmakuSwitch(mSoundInfo, danmakuOpen);
            updateComponentsStatus(mSoundInfo);
        }
    }

    public boolean needShowDoc() {
        return PlayPageManuscriptViewUtil.isDocTypeShown(mSoundInfo) && CoverSwitchStatus.docSwitcherForYPlay(mSoundInfo);
    }

    public void addCoverChangeListener(ICoverChangeListener coverChangeListener) {
        mCoverChangeListeners.add(coverChangeListener);
    }

    public void removeCoverChangeListener(ICoverChangeListener coverChangeListener) {
        mCoverChangeListeners.remove(coverChangeListener);
    }

    public @Nullable
    XBaseCoverComponent getMutexCoverComponentByEnum(YCoverComponentsEnum YCoverComponentsEnum) {
        return xComponentsMap.get(YCoverComponentsEnum);
    }

    public interface ICoverChangeListener {
        void onMutexCoverChanged(YCoverComponentsEnum currentCoverComponentEnum);
    }

    public XBaseCoverComponent getCurrentMutexCoverComponent() {
        return mCurrentMutexCoverComponent;
    }

    public XBaseCoverComponent getLastMutexCoverComponent() {
        return null;
    }

    public XImmersive getImmersive() {
        for (XBaseCoverComponent component : xComponents) {
            if (component != null && component.isVisible()
                    && component instanceof XImmersive && ((XImmersive) component).getImmersiveContentType() != null) {
                return (XImmersive) component;
            }
        }
        return null;
    }

    public IBottomBarShowChangeListener mBottomBarShowChangeListener;

    public IBottomBarShowChangeListener getBottomBarShowChangeListener(){
        return mBottomBarShowChangeListener;
    }

    public void setBottomBarShowChangeListener(IBottomBarShowChangeListener iBottomBarShowChangeListener){
        mBottomBarShowChangeListener = iBottomBarShowChangeListener;
    }

    public void setNavigationBarComponent(YNavigationBarComponent navigationBarComponent) {
        this.navigationBarComponent = navigationBarComponent;
    }

    public YNavigationBarComponent getNavigationBarComponent() {
        return navigationBarComponent;
    }

    public void setTipsComponentV2(YTipsComponentV2 tipsComponent) {
        this.tipsComponentV2 = tipsComponent;
    }

    public void setFunctionEntriesComponentV2(YFunctionEntriesComponentV2 functionEntriesComponentV2) {
        this.functionEntriesComponentV2 = functionEntriesComponentV2;
    }

    public interface  IBottomBarShowChangeListener{

        void showChange(boolean isShow);
    }

    public void registerYellowBarSizeChangeListener(YellowBarSizeChangeListener listener) {
    }

    public void unRegisterYellowBarSizeChangeListener(YellowBarSizeChangeListener listener) {
    }


    public int getYellowBarHeight() {
        return 0;
    }

    @Deprecated
    public boolean requestShowAdPermit(XAdType adType) {
        if (adType == XAdType.ForwardVideo) {
            if (playContainer.isVideoMode()) {
                return false;
            }

        }
        return true;
    }

    public boolean requestShowAdPermit(XAdType adType, long trackId) {
        if (mSoundInfo == null || mSoundInfo.trackInfo == null || mSoundInfo.trackInfo.trackId != trackId) {
            Log.d(TAG, trackId + " xplayinfo is not ready, ignore ad request");
            return false;
        }
        if (adType == XAdType.ForwardVideo) {
            if (playContainer.isVideoMode()) {
                return false;
            }

        }
        return true;
    }

    public void notifyStickyChanged(boolean isSticky) {
        for (XBaseCoverComponent component: xComponents) {
            if (component != null && component.isVisible()) {
                component.onStickyChanged(isSticky);
            }
        }
    }

    public void onPlayContainerScroll(int offset) {
        if (stagePager != null) {
            stagePager.onPlayContainerScroll(offset);
        }
    }

    public boolean isVideoMode() {
        if (stagePager != null) {
            return stagePager.isVideoMode();
        }

        return false;
    }
}
