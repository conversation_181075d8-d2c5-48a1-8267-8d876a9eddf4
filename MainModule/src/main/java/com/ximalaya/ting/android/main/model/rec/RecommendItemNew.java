package com.ximalaya.ting.android.main.model.rec;

import static com.ximalaya.ting.android.main.model.rec.RecommendItem.HAS_NEW_FOCUS_AB;

import android.os.Bundle;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
//import com.alibaba.gaiax.GXTemplateEngine;
import com.google.android.exoplayer2.util.Log;
import com.google.gson.Gson;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.feedad.IExpressFeedAd;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.model.XNpsQueryModel;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager;
import com.ximalaya.ting.android.main.model.album.MainAlbumMList;
import com.ximalaya.ting.android.main.playpage.dialog.voteshare.ReactNativeAdapter;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.HomeSpmDataUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by WolfXu on 2018/5/28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendItemNew {

    public static final String RECOMMEND_ITEM_MODULE = "MODULE"; // 模块
    public static final String RECOMMEND_ITEM_TRACK = "TRACK"; // 声音
    public static final String RECOMMEND_ITEM_ALBUM = "ALBUM"; // 专辑
    public static final String RECOMMEND_ITEM_LIVE = "LIVE"; // 直播
    public static final String RECOMMEND_SPECIAL = "SPECIAL"; // 听单
    public static final String RECOMMEND_NEW_SPECIAL = "NEW_SPECIAL"; // 新听单
    public static final String RECOMMEND_INTEREST_CARD = "INTEREST_CARD"; // 兴趣卡片
    public static final String RECOMMEND_ONE_KEY_LISTEN = "ONE_KEY_LISTEN_SCENE"; // 一键听
    public static final String RECOMMEND_ITEM_COLLECTION = "COLLECTION"; // 专辑系列
    public static final String RECOMMEND_HOT_COMMENT = "HOT_COMMENT"; // 热评
    public static final String RECOMMEND_XIMA_HOT_COMMENT = "XIMA_HOT_COMMENT"; //喜马热评
    public static final String RECOMMEND_RECENT_LISTEN = "RECENT_LISTEN"; // 最近在追/主播上新/直播开播
    public static final String RECOMMEND_LITTLE_PROGRAM = "LITTLE_PROGRAM"; // 小程序
    public static final String RECOMMEND_ITEM_CATEGORY_MODULE = "CATEGORY_MODULE"; // 频道页模块
    public static final String RECOMMEND_ITEM_PK = "PK";
    public static final String RECOMMEND_ITEM_CHILD_PROTECT_BANNER = "CHILD_PROTECT_BANNER"; // 青少年模式运营位
    public static final String RECOMMEND_SOURCE_GUESS_YOU_LIKE = "guessYouLike"; //属于猜你喜欢的专辑流
    public static final String RECOMMEND_ITEM_RANK_LIST = "rank2401"; // 排行榜列表
    public static final String RECOMMEND_ITEM_SOCIAL_LISTEN_LIST = "SocialListenList"; // 社会化听单
    public static final String RECOMMEND_ITEM_KID_ALL_LIKE = "IndexFeedModule/ChildLoveListenCard/Album"; // 儿童大家都爱听组件
    public static final String RECOMMEND_ITEM_SOCIAL_LISTEN_LIST_AD = "SocialListenListAd"; // 社会化听单-广告
    public static final String RECOMMEND_ITEM_HOT_LIVE_LIST = "SixLiveCard"; // 热门直播六卡
    public static final String RECOMMEND_ITEM_818_KOL_LIVE_LIST = "KolLiveCard"; // 818 重磅大咖直播
    public static final String RECOMMEND_ITEM_VIDEO_LIVE_LIST = "VideoLiveCard"; // 视频直播卡
    public static final String RECOMMEND_ITEM_CHASING_FOR_UPDATE = "ChasingForUpdate"; // 追更卡片
    public static final String RECOMMEND_ITEM_MOT_FOLLOW_UPDATE_LIST = "SocialListenList/MOTFollow"; // mot专辑
    public static final String RECOMMEND_ITEM_MOT_AUTHOR_UPDATE_LIST = "SocialListenList/MOTAuthor"; // mot专辑
    public static final String RECOMMEND_ITEM_EXPLOSIVE_CONTENT = "SocialListenList/IP/Album"; // 爆款内容
    public static final String RECOMMEND_ITEM_SCENE_LISTEN_CARD = "SceneListenCard"; // 场景听单
    public static final String RECOMMEND_ITEM_CHILD_IP_LIST = "ChildIpWall";
    public static final String RECOMMEND_ITEM_HOT_TAGS_2024 = "hot2024"; // 常听分类 标签找书

    public static final String RECOMMEND_ITEM_USER_RESEARCH_NEW = "IndexFeedModule/UserResearch"; //
    public static final String RECOMMEND_ITEM_USER_RESEARCH_NEW_HEADER = "userResearch"; //新首页用户调研header
    public static final String RECOMMEND_ITEM_AD_CENTER_BIG_PIC = "IndexFeedAd/BigPicture"; // 新中插大图
    public static final String RECOMMEND_ITEM_AD_CENTER_BIG_FEED = "IndexFeedAd/BigPictureFeed"; // 新中插信息流

    public static final String RECOMMEND_TYPE_IP_NEW_CONTAINER_LIST = "SocialListenList/IPList/Album"; // 重磅热推 平台上新容器
    public static final String RECOMMEND_TYPE_IP_1V4_NEW_CONTAINER_LIST = "SocialListenList/IP1V4/Album"; // 跟重磅热推/平台上新容器 样式一致 下面带的是声音

    public static final String RECOMMEND_ITEM_GUESS_YOU_LIKE_LIST = "IndexFeedModule/GuessYouLike"; // 猜你喜欢新卡  老卡用听单承接的

    public static final String RECOMMEND_ITEM_SLEEP_AID_CARD = "IndexFeedModule/SleepMot/Album"; // 助眠卡

    public static final String RECOMMEND_TYPE_TAG_LIST = "TagWall"; // 标签墙

    public static final String RECOMMEND_TYPE_INTEREST_CARD = "likeCollect"; // 兴趣卡片

    public static final String RECOMMEND_TYPE_ANCHOR_CARD = "AnchorList"; // 主播卡

    public static final String RECOMMEND_TYPE_FREE_LISTENER = "SocialListenList/FreeListen"; // 免费畅听

    public static final String RECOMMEND_ITEM_SHORT_VIDEO_LIST = "SocialListenList/VideoCard"; // 短视频列表

    public static final String RECOMMEND_ITEM_H5_CARD_LIST = "H5Card/ShortHot/Track"; // H5卡片列表
    public static final String RECOMMEND_ITEM_MIX_AD_BIG = "RecommendMixAdBig"; // 首页为你推荐信息流大图广告
    public static final String RECOMMEND_ITEM_HOT_RANK_LIST = "hotContentRank"; // 热点榜
    public static final String RECOMMEND_ITEM_SEARCH_CUSTOM_CARD = "customHomeModule"; // 搜索自定义卡片
    public static final int FROM_HEADER = 1;
    public static final int FROM_BODY = 2;

    public static final int DATA_FROM_STREAM = 1; //普通流
    public static final int DATA_FROM_CONTENT_CLUSTER = 2; //内容聚合模块

    private String itemType;
    private String sourceModuleType;
    private String originItemType;
    private Object item;
    private MainAlbumMList mainAlbumMList; //频道页信息流-单条数据
    private List<RecommendItemNew> hideList;
    private String ubtTraceId;

    private int vvPosition;
    private int viewType;

    private String srcTitle; // 所属筛选项的标题，埋点用；
    private String tabId; // 所属筛选项的id，埋点用；
    private int pageId; // 埋点用，请求次数
    private int indexInPage = -1; // 埋点用，请求到数据时，在当前数据列表中的位置，从1开始。
    private boolean srcInit = false;//该条数据是否来自刷新，大图信息流删除时使用
    private boolean hasShow; // 标记是否显示过
    private boolean shouldShowGradientBg; // 是否需要显示过渡背景
    private boolean isRecordToShowed;   // 是否已经展示上报过
    private boolean nextItemIsNormalFeedItem;
    private boolean prevItemIsNormalFeedItem;
    private boolean isFirstNormalFeedData;

    private long layoutId = -1;
    private boolean isDsl = false;
    private Object originData;
    private boolean isLastData; //最后一条数据如果是专辑或声音，背景底部有圆角
    private int moduleFrom; //区分header还是body
    private boolean notShowDislike; //不展示负反馈按钮
    private String parentModuleType; // 模块里带的专辑或声音，展示成信息流的样式
    private boolean isLastDataInModule;
    private boolean notRequestRealTimeData; //不调用实时推荐数据
    private int dataFrom = DATA_FROM_STREAM;
    private int positionInModule;
    private String contentClusterFilter = "";

    private String guessUbtTraceId;
    private int newPos; //服务端计算的位置
    private RecommendItemCardColor cardColor;
    private boolean nextIsSingle; // 混排模式使用
    private boolean prevIsSingle; // 混排模式使用
    private boolean isSpecialData; // 混排中，属于双排的数据如果是一个，则废弃
    private String picForCustomColor; // 调色用字段

    private JSONObject jsonObject; // 供VirtualView使用
    private String xmRequestId; // 请求id
    private int firstVisiblePosition; // 第一个可见的位置
    private int firstVisibleOffset; // 第一个可见的位置位移

    private boolean isLocalCache; // 是否本地缓存
    private int addStreamTitle; // 是否添加了标题，1添加了，0没有添加

    private ItemModel itemModel; // 存储itemmodel，避免猜你喜欢广告重复请求

    private IExpressFeedAd feedAd;
    private int position;

    private INativeAd nativeAd;

//    private GXTemplateEngine.GXTemplateData mTemplateData;

    // 常听分类 2种样式,fixedSix-6个固定   slideLeft-左滑
    private String displayClass;

    // 是否显示意见反馈布局
    private boolean isShowSatisfyView;

    public boolean isShowSatisfyView() {
        return isShowSatisfyView;
    }

    public void setShowSatisfyView(boolean showSatisfyView) {
        isShowSatisfyView = showSatisfyView;
    }

    public String getDisplayClass() {
        return displayClass;
    }

    public void setDisplayClass(String displayClass) {
        this.displayClass = displayClass;
    }

    public Object getOriginData() {
        return originData;
    }

    public String getParentModuleType() {
        return parentModuleType;
    }

    public void setParentModuleType(String parentModuleType) {
        this.parentModuleType = parentModuleType;
    }

    public boolean isLastDataInModule() {
        return isLastDataInModule;
    }

    public void setLastDataInModule(boolean lastDataInModule) {
        isLastDataInModule = lastDataInModule;
    }

    public void setLayoutId(long layoutId) {
        this.layoutId = layoutId;
    }

    public long getLayoutId() {
        return layoutId;
    }

    public boolean isDsl() {
        return isDsl;
    }

    public void setDsl(boolean dsl) {
        isDsl = dsl;
    }

    public String getGuessUbtTraceId() {
        return guessUbtTraceId;
    }

    public void setGuessUbtTraceId(String guessUbtTraceId) {
        this.guessUbtTraceId = guessUbtTraceId;
    }

    public String getOriginItemType() {
        return originItemType;
    }

    public void setOriginItemType(String originItemType) {
        this.originItemType = originItemType;
    }

    public int getModuleFrom() {
        return moduleFrom;
    }

    public void setModuleFrom(int moduleFrom) {
        this.moduleFrom = moduleFrom;
    }

    public static RecommendItemNew parseJson(JSONObject jsonObject, Gson gson) {
        return parseJson(jsonObject, gson, null, -1);
    }

    public static RecommendItemNew parseJson(JSONObject jsonObject, Gson gson, RecommendBodySpmInfo bodySpmInfo, int position) {
        try {
            RecommendItemNew recommendItemNew = new RecommendItemNew();
            if (jsonObject != null) {
                recommendItemNew.jsonObject = jsonObject;
                boolean isLocalCache = jsonObject.optBoolean("isLocalCache", false);
                recommendItemNew.setLocalCache(isLocalCache);
                String itemType = jsonObject.optString("itemType");
                String ubtTraceId = jsonObject.optString("ubtTraceId");
                String picForCustomColor = jsonObject.optString("picForCustomColor");
                String xmRequestId = RecommendFragmentNetManager.Companion.getInstance().getMXmRequestId();
                jsonObject.put("xmRequestId", xmRequestId);
                recommendItemNew.setXmRequestId(xmRequestId);
                recommendItemNew.setPicForCustomColor(picForCustomColor);
                recommendItemNew.setNewPos(jsonObject.optInt("p"));
                if (!(RECOMMEND_ITEM_LIVE.equals(itemType) || RECOMMEND_ITEM_ALBUM.equals(itemType) || RECOMMEND_ITEM_TRACK.equals(itemType))) {
                    recommendItemNew.setUbtTraceId(ubtTraceId);
                    // album 对sourceType会做额外处理
                }
                recommendItemNew.setItemType(itemType);
                recommendItemNew.setSourceModuleType(jsonObject.optString("sourceModuleType"));
                if (TextUtils.isEmpty(recommendItemNew.getSourceModuleType())) {
                    recommendItemNew.setSourceModuleType(itemType);
                }
                switch (recommendItemNew.getItemType()) {
                    case RECOMMEND_ITEM_MODULE:
                    case RECOMMEND_HOT_COMMENT:
                    case RECOMMEND_ITEM_AD_CENTER_BIG_PIC:
                    case RECOMMEND_ITEM_AD_CENTER_BIG_FEED:
                        recommendItemNew.setItem(RecommendModuleItem.parseData(jsonObject.optJSONObject("item"), gson, recommendItemNew));
                        break;
                    case RECOMMEND_ITEM_TRACK:
                        RecommendTrackItem trackItem = new RecommendTrackItem(jsonObject.optString("item"), gson);
                        recommendItemNew.setItem(trackItem);
                        if (jsonObject.has("hideList")) {
                            recommendItemNew.parseHideList(jsonObject.optJSONArray("hideList"), gson);
                        }
                        setCardColor(recommendItemNew, jsonObject);
                        HomeSpmDataUtil.setSpmData(trackItem, bodySpmInfo);
                        break;
                    case RECOMMEND_ITEM_PK:
                        RecommendPkItem recommendPkItem = new RecommendPkItem(jsonObject.optString("item"));
                        recommendItemNew.setItem(recommendPkItem);
                        break;
                    case RECOMMEND_ITEM_ALBUM:
                        RecommendAlbumItem albumItem = new RecommendAlbumItem(jsonObject.optString("item"), gson);
                        recommendItemNew.setItem(albumItem);
                        if (albumItem != null && albumItem.getSourceType() == 2) {
                            recommendItemNew.setUbtTraceId(" ");
                        }
                        setCardColor(recommendItemNew, jsonObject);
                        HomeSpmDataUtil.setSpmData(albumItem, bodySpmInfo);
                        break;
                    case RECOMMEND_ITEM_USER_RESEARCH_NEW:
                        recommendItemNew.setItem(gson.fromJson(jsonObject.optString("item"), RecommendUserSearchItem.class));
                        break;
                    case RECOMMEND_ITEM_USER_RESEARCH_NEW_HEADER:
                        JSONObject userSearchItemJson = jsonObject.optJSONObject("item");
                        if (userSearchItemJson != null && userSearchItemJson.has("list")) {
                            JSONArray list = userSearchItemJson.optJSONArray("list");
                            if (list != null && list.length() > 0) {
                                JSONObject extraInfoArrayObj = list.getJSONObject(0);
                                if (extraInfoArrayObj != null && extraInfoArrayObj.has("extraInfo")) {
                                    JSONObject extraInfo = extraInfoArrayObj.optJSONObject("extraInfo");
                                    if (extraInfo != null && extraInfo.has("data")) {
                                        recommendItemNew.setItem(gson.fromJson(extraInfo.optString("data"), XNpsQueryModel.class));
                                    }
                                }
                            }
                        }
                        break;
//                    case RECOMMEND_ITEM_CHASING_ALBUM_LIST:
//                        JSONObject itemJson = jsonObject.optJSONObject("item");
//                        if (itemJson != null && itemJson.has("list")) {
//                            JSONArray list = itemJson.optJSONArray("list");
//                            if (list != null && list.length() > 0) {
//                                recommendItemNew.setItem(gson.fromJson(list.getString(0), RecommendChasingList.class));
//                            }
//                        }
//                        break;
                    case RECOMMEND_ITEM_RANK_LIST:
                    case RECOMMEND_ITEM_HOT_TAGS_2024:
                    case RECOMMEND_TYPE_ANCHOR_CARD:
                    case RECOMMEND_ITEM_HOT_RANK_LIST:
                    case RECOMMEND_ITEM_SEARCH_CUSTOM_CARD:
                        if (jsonObject.has("displayClass")) {
                            recommendItemNew.displayClass = jsonObject.optString("displayClass");
                        }
                        JSONObject itemJsonRank = jsonObject.optJSONObject("item");
                        JSONArray jsonArrayRank = itemJsonRank.optJSONArray("list");
                        itemJsonRank.remove("list");
                        JSONObject rankAllJson = null;
                        if (jsonArrayRank != null && jsonArrayRank.length() > 0) {
                            rankAllJson = jsonArrayRank.optJSONObject(0);
                            JSONObject rankExtraInfo = rankAllJson.optJSONObject("extraInfo");
                            String rankStyle = "base";
                            if (rankExtraInfo != null) {
                                rankStyle = rankExtraInfo.optString("style", "base");
                            }
                            JSONObject ubtV2Json = rankAllJson.optJSONObject("ubtV2");
                            String title = rankAllJson.optString("title");
                            String contentType = rankAllJson.optString("contentType");
                            String bizType = rankAllJson.optString("bizType");
                            long rankCardId = rankAllJson.optLong("id");
                            JSONArray subElements = rankAllJson.optJSONArray("subElements");
                            itemJsonRank.put("list", subElements);
                            itemJsonRank.put("ubtV2", ubtV2Json);
                            itemJsonRank.put("title", title);
                            itemJsonRank.put("contentType", contentType);
                            itemJsonRank.put("bizType", bizType);
                            itemJsonRank.put("id", rankCardId);
                            itemJsonRank.put("style", rankStyle);
                        }
                        RecommendRankListItem recommendRankListItem = gson.fromJson(itemJsonRank.toString(), RecommendRankListItem.class);
                        if (recommendRankListItem != null && recommendRankListItem.getList() != null
                                && !recommendRankListItem.getList().isEmpty()) {
                            for (AlbumRank albumRank: recommendRankListItem.getList()) {
                                if (albumRank != null && albumRank.getSubElements() != null && !albumRank.getSubElements().isEmpty()) {
                                    for (RankSubElement albumSubElement: albumRank.getSubElements()) {
                                        if (albumSubElement != null && albumSubElement.getWrap() != null) {
                                            albumSubElement.getWrap().init();
                                            albumSubElement.setSocialTag(albumSubElement.getWrap().getSocialTag());
                                            albumSubElement.setCustomTitle(albumSubElement.getWrap().getCustomTitle());
                                            if ((RECOMMEND_ITEM_RANK_LIST.equals(recommendItemNew.getItemType()) || RECOMMEND_ITEM_HOT_TAGS_2024.equals(recommendItemNew.getItemType())) && albumSubElement.getExtraInfo() != null && albumSubElement.getExtraInfo().getSubRefInfo() != null && albumSubElement.getExtraInfo().getSubRefInfo().getAd() && albumSubElement.getExtraInfo().getSubRefInfo().getBusinessExtraInfo() != null && !TextUtils.isEmpty(albumSubElement.getExtraInfo().getSubRefInfo().getBusinessExtraInfo().getAdInfo())) {
                                                albumSubElement.getExtraInfo().getSubRefInfo().getBusinessExtraInfo()
                                                        .setAdInfoObject(gson.fromJson(albumSubElement.getExtraInfo().getSubRefInfo().getBusinessExtraInfo().getAdInfo(), Advertis.class));
                                            }
                                        }
                                    }
                                }
                            }
                            recommendRankListItem.setDislikeReasonNewV1(MainCommonRequest.getCommonDislikeReasonNewV1());
                            recommendRankListItem.setCardAdCount(-1);
                        }

                        if (recommendRankListItem != null && rankAllJson != null) {
                            recommendRankListItem.setExtraInfo(gson.fromJson(rankAllJson.optString("extraInfo"), ExtraInfo.class));
                        }

                        if (recommendRankListItem != null && rankAllJson != null) {
                            recommendRankListItem.setLandingPage(rankAllJson.optString("landingPage"));
                        }

                        recommendItemNew.setItem(recommendRankListItem);
                        break;
                    case RECOMMEND_ITEM_MOT_AUTHOR_UPDATE_LIST:
                    case RECOMMEND_ITEM_MOT_FOLLOW_UPDATE_LIST:
                    case RECOMMEND_ITEM_CHASING_FOR_UPDATE:
                    case RECOMMEND_ITEM_SOCIAL_LISTEN_LIST:
                    case RECOMMEND_ITEM_GUESS_YOU_LIKE_LIST:
                    case RECOMMEND_ITEM_SOCIAL_LISTEN_LIST_AD:
                    case RECOMMEND_ITEM_HOT_LIVE_LIST:
                    case RECOMMEND_ITEM_818_KOL_LIVE_LIST:
                    case RECOMMEND_ITEM_VIDEO_LIVE_LIST:
                    case RECOMMEND_ITEM_SCENE_LISTEN_CARD:
                    case RECOMMEND_ITEM_CHILD_IP_LIST:
                    case RECOMMEND_ITEM_EXPLOSIVE_CONTENT:
                    case RECOMMEND_TYPE_IP_NEW_CONTAINER_LIST:
                    case RECOMMEND_TYPE_IP_1V4_NEW_CONTAINER_LIST:
                    case RECOMMEND_TYPE_INTEREST_CARD:
                    case RECOMMEND_TYPE_TAG_LIST:
                    case RECOMMEND_TYPE_FREE_LISTENER:
                    case RECOMMEND_ITEM_SHORT_VIDEO_LIST:
                    case RECOMMEND_ITEM_H5_CARD_LIST:
                    case RECOMMEND_ITEM_SLEEP_AID_CARD:
                        JSONObject itemJson = jsonObject.optJSONObject("item");
                        if (itemJson != null && itemJson.has("list")) {
                            JSONArray list = itemJson.optJSONArray("list");
                            if (list != null && list.length() > 0) {
                                RecommendCommonItem recommendCommonItem = gson.fromJson(list.getString(0), RecommendCommonItem.class);
                                recommendCommonItem.setModuleId(itemJson.optLong("moduleId"));
                                recommendCommonItem.setDislikeReasonNewV1(MainCommonRequest.getCommonDislikeReasonNewV1());
                                recommendCommonItem.setCardAdCount(-1);
                                recommendItemNew.setItem(recommendCommonItem);
                                if ((RECOMMEND_ITEM_SOCIAL_LISTEN_LIST.equals(recommendItemNew.getItemType()) || RECOMMEND_ITEM_GUESS_YOU_LIKE_LIST.equals(recommendItemNew.getItemType()))
                                        && !ToolUtil.isEmptyCollects(recommendCommonItem.getSubElements())) {
                                    for (CommonSubElement subElement : recommendCommonItem.getSubElements()) {
                                        if (subElement != null && subElement.getExt() != null && subElement.getExt().getSubRefInfo() != null
                                                && subElement.getExt().getSubRefInfo().getAd() && subElement.getExt().getSubRefInfo().getBusinessExtraInfo() != null
                                                && !TextUtils.isEmpty(subElement.getExt().getSubRefInfo().getBusinessExtraInfo().getAdInfo())) {
                                            subElement.getExt().getSubRefInfo().getBusinessExtraInfo().setAdInfoObject(gson.fromJson(subElement.getExt().getSubRefInfo().getBusinessExtraInfo().getAdInfo(), Advertis.class));
                                        }
                                    }
                                }
                                if (RECOMMEND_ITEM_SOCIAL_LISTEN_LIST_AD.equals(recommendItemNew.getItemType()) && !ToolUtil.isEmptyCollects(recommendCommonItem.getSubElements())) {
                                    for (CommonSubElement subElement : recommendCommonItem.getSubElements()) {
                                        if (subElement != null && subElement.getExt() != null && !TextUtils.isEmpty(subElement.getExt().getAdInfo())) {
                                            subElement.getExt().setAdInfoObject(gson.fromJson(subElement.getExt().getAdInfo(), Advertis.class));
                                        }
                                    }
                                }

                                if (RECOMMEND_ITEM_H5_CARD_LIST.equals(recommendItemNew.getItemType())&& !ToolUtil.isEmptyCollects(recommendCommonItem.getSubElements())) {
                                    try {
                                        itemJson.put("position", position);
                                        String json = itemJson.toString();
                                        MMKVUtil.getInstance().saveString("RECOMMEND_ITEM_H5_CARD_LIST_2025", json);
                                        Bundle bundle = new Bundle();
                                        bundle.putString("data", json);
//                                        ReactNativeAdapter.saveSearchData(recommendItemNew.itemType, bundle);
                                        ReactNativeAdapter.saveSearchData("home_card_0317", bundle);
                                        if (ConstantsOpenSdk.isDebug) {
                                            Logger.d("home_h5", "saveDataTOLocal >>>\n" + json);
                                        }
                                    } catch (Exception e) {
                                            e.printStackTrace();
                                        Logger.d("home_h5", "saveDataTOLocal error: " + e.getMessage());
                                    }
                                }
                            }
                        }
                        break;
                    case RECOMMEND_SPECIAL:
                    case RECOMMEND_NEW_SPECIAL:
                        recommendItemNew.setItem(new RecommendSpecialItem(jsonObject.optJSONObject("item")));
                        break;
                    case RECOMMEND_ITEM_KID_ALL_LIKE:
                        recommendItemNew.setItem(RecommendKidAllLikeItem.Companion.parseJson(jsonObject.optJSONObject("item")));
                        break;
                    case RECOMMEND_INTEREST_CARD:
                        recommendItemNew.setItem(new Gson().fromJson(jsonObject.optString("item"), RecommendInterestCard.class));
                        break;
                    case RECOMMEND_ONE_KEY_LISTEN:
                        recommendItemNew.setItem(new Gson().fromJson(jsonObject.optString("item"), RecommendOneKeyListenItem.class));
                        break;
                    case RECOMMEND_ITEM_COLLECTION:
                        recommendItemNew.setItem(new RecommendCollectionItem(jsonObject.optJSONObject("item")));
                        break;
                    case RECOMMEND_ITEM_LIVE:
                        recommendItemNew.setItem(new RecommendLiveItem(jsonObject.optString("item")));
                        break;
                    case RECOMMEND_XIMA_HOT_COMMENT:
                        recommendItemNew.setItem(new RecommendHotCommentItemNew(jsonObject.optString("item")));
                        break;
                    case RECOMMEND_RECENT_LISTEN:
                        recommendItemNew.setItem(new RecommendRecentListen(jsonObject.optString("item")));
                        break;
                    case RECOMMEND_LITTLE_PROGRAM:
                        recommendItemNew.setItem(new RecommendLiteAppModel(jsonObject.optString("item")));
                        break;
                    case RECOMMEND_ITEM_CATEGORY_MODULE:
                        recommendItemNew.setMainAlbumMList(new MainAlbumMList(jsonObject.optString("item")));
                        break;
                    default:
                        // 不是上面的类型的，目前不用，返回null。
                        return recommendItemNew;
                }
                return recommendItemNew;
            }
        } catch (Exception e) {
            // catch住所有异常，避免一个数据解析时出错，导致所有数据无法加载
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(e);
            } else {
                try {
                    XDCSCollectUtil.statErrorToXDCS("MixDataError", e.getMessage() + "_____" + jsonObject.toString());
                } catch (Exception ex) {
                    Logger.e("cf_test", "MixDataError" + ex.getMessage());
                }
            }
        }
        return null;
    }

    private void setOriginData(Object originData) {
        this.originData = originData;
    }


    public MainAlbumMList getMainAlbumMList() {
        return mainAlbumMList;
    }

    public void setMainAlbumMList(MainAlbumMList mainAlbumMList) {
        this.mainAlbumMList = mainAlbumMList;
    }

    private void parseHideList(JSONArray jsonArray, Gson gson) {
        try {
            if (jsonArray != null && jsonArray.length() > 0) {
                hideList = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++) {
                    hideList.add(parseJson(jsonArray.optJSONObject(i), gson));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getCardColor() {
        return cardColor != null ? cardColor.getColor() : ColorUtil.INVALID_COLOR;
    }

    public void setCardColor(RecommendItemCardColor cardColor) {
        this.cardColor = cardColor;
    }

    private static void setCardColor(RecommendItemNew recommendItemNew, JSONObject jsonObject) {
        if (recommendItemNew == null || jsonObject == null) {
            return;
        }
        if (jsonObject.has("cardColor")) {
            JSONObject colorObject = jsonObject.optJSONObject("cardColor");
            if (colorObject != null) {
                RecommendItemCardColor cardColor = new RecommendItemCardColor(colorObject.optInt("r"),
                        colorObject.optInt("g"),
                        colorObject.optInt("b"));
                recommendItemNew.setCardColor(cardColor);
            }
        }
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray, String ubtTraceId, RecommendBodySpmInfo bodySpmInfo) {
        RecommendModelNew.StreamOptionInfo streamOptionInfo = new RecommendModelNew.StreamOptionInfo();
        streamOptionInfo.setUbtTraceId(ubtTraceId);
        streamOptionInfo.setFromRealTime(true);
        return parseRecommendItemList(jsonArray, null, streamOptionInfo, null, false, bodySpmInfo);
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray) {
        return parseRecommendItemList(jsonArray, null, null, null);
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray, String newUserTopBannerGroup, RecommendModelNew.StreamOptionInfo streamOptionInfo, Gson gson) {
        return parseRecommendItemList(jsonArray, newUserTopBannerGroup, streamOptionInfo, gson, false);
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray, String newUserTopBannerGroup
            , RecommendModelNew.StreamOptionInfo streamOptionInfo, Gson gson, boolean isLocalCache) {
        return parseRecommendItemList(jsonArray, newUserTopBannerGroup, streamOptionInfo, gson, isLocalCache, null);
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray, String newUserTopBannerGroup
            , RecommendModelNew.StreamOptionInfo streamOptionInfo, Gson gson, boolean isLocalCache,
                                                                RecommendBodySpmInfo bodySpmInfo) {
        if (gson == null) {
            gson = new Gson();
        }
        List<RecommendItemNew> list = new ArrayList<>();
        boolean hasNewUseNew = false;
        if (HAS_NEW_FOCUS_AB.equals(newUserTopBannerGroup)) {
            hasNewUseNew = true;
        }
        if (jsonArray != null && jsonArray.length() > 0) {

            RecommendItemNew focusRecommendItem = null;
            RecommendItemNew newUserFocusRecommendItem = null;

            boolean isFirstCenterBigAd = true;
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObjectInItem = jsonArray.optJSONObject(i);
                try {
                    jsonObjectInItem.put("isLocalCache", isLocalCache);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                RecommendItemNew recommendItem = null;
                try {
                    recommendItem = RecommendItemNew.parseJson(jsonObjectInItem, gson, bodySpmInfo, i+1);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (recommendItem != null) {
                    if (RECOMMEND_ITEM_AD_CENTER_BIG_PIC.equals(recommendItem.getItemType())) {
                        if (streamOptionInfo != null && streamOptionInfo.isOnlyBody()) {
                            // 将非首页的中插大图广告映射为中插信息流类型
                            recommendItem.setItemType(RECOMMEND_ITEM_AD_CENTER_BIG_FEED);
                        } else {
                            if (isFirstCenterBigAd) {
                                isFirstCenterBigAd = false;
                            } else {
                                // 将首页的非一个中插大图类型映射为中插信息流类型
                                recommendItem.setItemType(RECOMMEND_ITEM_AD_CENTER_BIG_FEED);
                            }
                        }
                    }
                     if (recommendItem.getItem() instanceof RecommendModuleItem) {
                        String moduleType = ((RecommendModuleItem) recommendItem.getItem()).getModuleType();
                        if (RecommendModuleItem.RECOMMEND_TYPE_FOCUS.equals(moduleType)) {
                            focusRecommendItem = recommendItem;
                        } else if (RecommendModuleItem.RECOMMEND_TYPE_NEW_USER_FOCUS.equals(moduleType)) {
                            newUserFocusRecommendItem = recommendItem;
                        }
                    }
                    if (streamOptionInfo != null) {
                        String itemType = recommendItem.getItemType();
                        if (RECOMMEND_ITEM_TRACK.equals(itemType) || RECOMMEND_ITEM_ALBUM.equals(itemType) || RECOMMEND_ITEM_LIVE.equals(itemType)
                                || RECOMMEND_SPECIAL.equals(itemType)) {
                            if (!TextUtils.isEmpty(streamOptionInfo.getUbtTraceId())) {
                                recommendItem.setUbtTraceId(streamOptionInfo.getUbtTraceId());
                            }
                            if (!TextUtils.isEmpty(streamOptionInfo.getGuessUbtTraceId())) {
                                recommendItem.setGuessUbtTraceId(streamOptionInfo.getGuessUbtTraceId());
                            }
                        }
                        // 这里置为空字符串
                        if (RECOMMEND_ITEM_ALBUM.equals(itemType)
                                && recommendItem.getItem() instanceof RecommendAlbumItem
                                && ((RecommendAlbumItem) recommendItem.getItem()).getSourceType() == 2) {
                            recommendItem.setUbtTraceId(" ");
                            recommendItem.setGuessUbtTraceId(" ");
                        }
                        if (streamOptionInfo.isFromRealTime() && RECOMMEND_ITEM_LIVE.equals(itemType)) {
                            JSONObject jsonObject = jsonArray.optJSONObject(i);
                            if (jsonObject != null && jsonObject.has("ubtTraceId")) {
                                recommendItem.setUbtTraceId(jsonObject.optString("ubtTraceId"));
                            }
                        }
                        if (streamOptionInfo.isFromStaggered()) {
                            try {
                                JSONObject jsonObject = jsonArray.optJSONObject(i);
                                if (jsonObject != null && jsonObject.has("ubtTraceId")) {
                                    recommendItem.setUbtTraceId(jsonObject.optString("ubtTraceId"));
                                }
                            } catch (Exception e) {
                                Logger.e(e);
                            }
                        }
                    }
                    list.add(recommendItem);
                }
            }

            if (hasNewUseNew && focusRecommendItem != null && newUserFocusRecommendItem != null) {
                list.remove(focusRecommendItem);
            } else if (!hasNewUseNew && newUserFocusRecommendItem != null) {
                list.remove(newUserFocusRecommendItem);
            }
        }
        return list;
    }

    /**
     * 信息流条，包括专辑条、声音条、直播条、听单条
     */
    public boolean isNormalFeedItem() {
        return RECOMMEND_ITEM_ALBUM.equals(itemType) || RECOMMEND_ITEM_TRACK.equals(itemType)
                || RECOMMEND_ITEM_LIVE.equals(itemType) || RECOMMEND_SPECIAL.equals(itemType);
    }

    public boolean isNormalFeedItemInCategoryFragment() {
        return RECOMMEND_ITEM_ALBUM.equals(itemType) || RECOMMEND_ITEM_TRACK.equals(itemType);
    }

    public boolean isFirstNormalFeedData() {
        return isFirstNormalFeedData;
    }

    public void setFirstNormalFeedData(boolean firstNormalFeedData) {
        isFirstNormalFeedData = firstNormalFeedData;
    }

    public boolean isNextItemIsNormalFeedItem() {
        return nextItemIsNormalFeedItem;
    }

    public void setNextItemIsNormalFeedItem(boolean nextItemIsNormalFeedItem) {
        this.nextItemIsNormalFeedItem = nextItemIsNormalFeedItem;
    }

    public boolean isPrevItemIsNormalFeedItem() {
        return prevItemIsNormalFeedItem;
    }

    public void setPrevItemIsNormalFeedItem(boolean prevItemIsNormalFeedItem) {
        this.prevItemIsNormalFeedItem = prevItemIsNormalFeedItem;
    }

    /**
     * 将普通信息流(一行一个)数据转换成大图信息流(一行两个)数据
     *
     * @param feeds 原始信息流数据
     * @return
     */
    public static List<RecommendPairItemNew> convertFeedData(List<RecommendItemNew> feeds) {
        List<RecommendPairItemNew> result = new ArrayList<>();
        for (int i = 0; i < feeds.size(); i = i + 2) {
            RecommendPairItemNew itemNew = new RecommendPairItemNew();
            itemNew.getPairItems().add(feeds.get(i));
            if ((i + 1) < feeds.size()) {
                itemNew.getPairItems().add(feeds.get(i + 1));
            }
            result.add(itemNew);
        }
        return result;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getUbtTraceId() {
        return ubtTraceId;
    }

    public void setUbtTraceId(String ubtTraceId) {
        this.ubtTraceId = ubtTraceId;
    }

    public int getNewPos() {
        return newPos;
    }

    public void setNewPos(int newPos) {
        this.newPos = newPos;
    }

    public String getSourceModuleType() {
        return sourceModuleType;
    }

    public void setSourceModuleType(String sourceModuleType) {
        this.sourceModuleType = sourceModuleType;
    }

    public Object getItem() {
        return item;
    }

    public void setItem(Object item) {
        this.item = item;
    }

    public String getSrcTitle() {
        return srcTitle;
    }

    public void setSrcTitle(String srcTitle) {
        this.srcTitle = srcTitle;
    }

    public int getVvPosition() {
        return vvPosition;
    }

    public void setVvPosition(int vvPosition) {
        this.vvPosition = vvPosition;
    }

    public int getViewType() {
        return viewType;
    }

    public void setViewType(int viewType) {
        this.viewType = viewType;
    }

    public String getStatPageAndIndex() {
        // 上报pageId从1开始，而保存时是从0开始的，所以加1
        return (pageId + 1) + "-" + indexInPage;
    }

    public int getPageId() {
        return pageId;
    }

    public void setPageId(int pageId) {
        this.pageId = pageId;
    }

    public String getTabId() {
        return tabId;
    }

    public void setTabId(String tabId) {
        this.tabId = tabId;
    }

    public List<RecommendItemNew> getHideList() {
        return hideList;
    }

    public void setHideList(List<RecommendItemNew> hideList) {
        this.hideList = hideList;
    }

    public boolean isSrcInit() {
        return srcInit;
    }

    public RecommendItemNew setSrcInit(boolean srcInit) {
        this.srcInit = srcInit;
        return this;
    }

    public boolean isHasShow() {
        return hasShow;
    }

    public void setHasShow(boolean hasShow) {
        this.hasShow = hasShow;
    }

    public void setIndexInPage(int indexInPage) {
        this.indexInPage = indexInPage;
    }

    public int getIndexInPage() {
        return indexInPage;
    }

    public boolean shouldShowGradientBg() {
        return shouldShowGradientBg;
    }

    public void setShouldShowGradientBg(boolean shouldShowGradientBg) {
        this.shouldShowGradientBg = shouldShowGradientBg;
    }

    public boolean isRecordToShowed() {
        return isRecordToShowed;
    }

    public void setRecordToShowed(boolean recordToShowed) {
        isRecordToShowed = recordToShowed;
    }

    public boolean isLastData() {
        return isLastData;
    }

    public void setLastData(boolean lastData) {
        isLastData = lastData;
    }

    public boolean isNotShowDislike() {
        return notShowDislike;
    }

    public void setNotShowDislike(boolean notShowDislike) {
        this.notShowDislike = notShowDislike;
    }

    public boolean isNotRequestRealTimeData() {
        return notRequestRealTimeData;
    }

    public void setNotRequestRealTimeData(boolean notRequestRealTimeData) {
        this.notRequestRealTimeData = notRequestRealTimeData;
    }

    public int getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(int dataFrom) {
        this.dataFrom = dataFrom;
    }

    public int getPositionInModule() {
        return positionInModule;
    }

    public void setPositionInModule(int positionInModule) {
        this.positionInModule = positionInModule;
    }

    public String getContentClusterFilter() {
        return contentClusterFilter;
    }

    public void setContentClusterFilter(String contentClusterFilter) {
        this.contentClusterFilter = contentClusterFilter;
    }

    public boolean isNextIsSingle() {
        return nextIsSingle;
    }

    public void setNextIsSingle(boolean nextIsSingle) {
        this.nextIsSingle = nextIsSingle;
    }

    public boolean isPrevIsSingle() {
        return prevIsSingle;
    }

    public void setPrevIsSingle(boolean prevIsSingle) {
        this.prevIsSingle = prevIsSingle;
    }

    public boolean isSpecialData() {
        return isSpecialData;
    }

    public void setSpecialData(boolean specialData) {
        isSpecialData = specialData;
    }

    public JSONObject getJsonObject() {
        return jsonObject;
    }

    public void setJsonObject(JSONObject jsonObject) {
        this.jsonObject = jsonObject;
    }

//    public GXTemplateEngine.GXTemplateData getTemplateData() {
//        if (mTemplateData == null) {
//            try {
//                mTemplateData = new GXTemplateEngine.GXTemplateData(JSON.parseObject(jsonObject.toString()));
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return mTemplateData;
//    }

//    public void updateTemplateData() {
//        try {
//            mTemplateData = new GXTemplateEngine.GXTemplateData(JSON.parseObject(jsonObject.toString()));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

//    public void setTemplateData(GXTemplateEngine.GXTemplateData templateData) {
//        mTemplateData = templateData;
//    }

    public String getPicForCustomColor() {
        return picForCustomColor;
    }

    public void setPicForCustomColor(String picForCustomColor) {
        this.picForCustomColor = picForCustomColor;
    }

    public ItemModel getItemModel() {
        return itemModel;
    }

    public void setItemModel(ItemModel itemModel) {
        this.itemModel = itemModel;
    }

    public String getXmRequestId() {
        return xmRequestId;
    }

    public void setXmRequestId(String xmRequestId) {
        this.xmRequestId = xmRequestId;
    }

    public int getFirstVisiblePosition() {
        return firstVisiblePosition;
    }

    public void setFirstVisiblePosition(int firstVisiblePosition) {
        this.firstVisiblePosition = firstVisiblePosition;
    }

    public int getFirstVisibleOffset() {
        return firstVisibleOffset;
    }

    public void setFirstVisibleOffset(int firstVisibleOffset) {
        this.firstVisibleOffset = firstVisibleOffset;
    }

    public boolean isLocalCache() {
        return isLocalCache;
    }

    public void setLocalCache(boolean localCache) {
        isLocalCache = localCache;
    }

    public int getAddStreamTitle() {
        return addStreamTitle;
    }

    public void setAddStreamTitle(int addStreamTitle) {
        this.addStreamTitle = addStreamTitle;
    }

    public IExpressFeedAd getFeedAd() {
        return feedAd;
    }

    public void setFeedAd(IExpressFeedAd feedAd) {
        this.feedAd = feedAd;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public INativeAd getNativeAd() {
        return nativeAd;
    }

    public void setNativeAd(INativeAd nativeAd) {
        this.nativeAd = nativeAd;
    }
}
