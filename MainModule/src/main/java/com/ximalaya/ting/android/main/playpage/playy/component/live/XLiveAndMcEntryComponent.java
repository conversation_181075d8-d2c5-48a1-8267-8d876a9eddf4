package com.ximalaya.ting.android.main.playpage.playy.component.live;

import static com.ximalaya.ting.android.host.manager.handler.HandlerManager.removeCallbacks;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PorterDuff;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieListener;
import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.LastAudioPlayListCache;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.play.RawResourceElement;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playModule.presenter.LiveEntryConfigManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAnimationManager;
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimateBiz;
import com.ximalaya.ting.android.main.playpage.audioplaypageV2.animate.AnimationManagerV2;
import com.ximalaya.ting.android.main.playpage.internalservice.ILiveEntryComponentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageLiveEntryManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager;
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer;
import com.ximalaya.ting.android.main.playpage.playy.ITopLayoutBgColorChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.PlayTypeChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.component.chatxmly.ChatXmlyBoboPlayPageEntryView;
import com.ximalaya.ting.android.main.playpage.playy.component.chatxmly.ChatXmlyTraceUtil;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager;
import com.ximalaya.ting.android.main.playpage.playy.manager.LiveEntryAnimatorManager;
import com.ximalaya.ting.android.main.playpage.playy.utils.PlayUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by WolfXu on 2020-06-05.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class XLiveAndMcEntryComponent implements PlayPageDataManager.IOnThemeColorChangedListener, ITopLayoutBgColorChangeListener, PlayTypeChangeListener, IXmPlayerStatusListener {
    private static final String TAG = XLiveAndMcEntryComponent.class.getSimpleName();
    private static final String DELAY_TASK_TAG = "PlayPageLiveEntryDelayTask";
    private static final String SHARE_TASK_TAG = "PlayPageLiveEntryDelayTask_SHARE";
    private static final long WAVE_ANIMATION_DURATION = 15000;
    private FragmentActivity mActivity;
    private BaseFragment2 mFragment;
    private boolean mHasInit;
    private PlayingSoundInfo mSoundInfo;
    private long mLastTrackId;
    private View mRootView;
    private PlayPageMinorData mPlayPageMinorData;
    private PlayPageMinorData.RightRecommendInfo mRightRecommendInfo;
    private TextView mTvLiveEntryRecReason;
    private FrameLayout mFlTitle;
    private View mVLiveEntryAvatarContainer;
    private RoundImageView mIvLiveEntryAvatar;
    private RoundImageView mIvAvatar2;
    private LottieAnimationView mLottieAnimationView;
    private LottieAnimationView mLottieBoBoView;
    private Runnable mLiveEntryAvatarPartHideTask;
    private ILoadLiveDataCallback mLoadLiveDataCallback;
    private boolean mIsLiveEntryAnimating;
    private String mCurLottieFileName = null;
    private LottieAnimationView mLottieLabel;
    private boolean mLastVisibleWhileTabChanged;
    private boolean mWaveAnimationIsRunningWhilePaused;
    private boolean isVipToastFinish;
    private boolean isShowLiveAfterVipFinish;
    private long mLastLiveAnimationStartTime; //上次展示直播入口动画的时间
    private final long mSoundSwitchAnimationCd; //切换声音时展示直播入口动画的cd值
    private long mSoundPauseAnimationCd; //暂停声音时展示直播入口动画的cd值
    private String mSoundPauseCdKey;
    private boolean isShowLiveAnimatorWhenPaused; //是否是暂停时展示直播入口动画

    private boolean mIsTipsShow;
    private long mMcLastExposeTime;
    private long mMcExposeCount;
    private final YCoverComponentsManager mYCoverComponentsManager;
    private final IPlayContainer mPlayContainer;
    private WeakReference<FrameLayout> titleRef;
    private WeakReference<RoundImageView> avatarRef;
    private WeakReference<View> avatarContainRef;
    private String mXmRequestId = "-1";
    private String mBoBoLandingPageUrl = "";

    private boolean isLiveInAnimating = false;
    private boolean isChatXmlyAnimating = false;
    private ChatXmlyBoboPlayPageEntryView chatXmlyBoboPlayPageEntryView;
    private boolean firstLoad = true;

    public XLiveAndMcEntryComponent(BaseFragment2 fragment, YCoverComponentsManager YCoverComponentsManager, IPlayContainer playContain) {
        mFragment = fragment;
        mYCoverComponentsManager = YCoverComponentsManager;
        mPlayContainer = playContain;
        if (mFragment != null) {
            mActivity = mFragment.getActivity();
        }
        mSoundSwitchAnimationCd = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_PLAYPAGE_LIVE_ANIMATION_CD, 10);
    }

    private void initIfNeeded() {
        if (!mHasInit) {
            if (mFragment != null) {
                mHasInit = true;
                ViewStub viewStub = mFragment.findViewById(R.id.main_vs_live_entry_x);
                if (viewStub != null) {
                    mRootView = viewStub.inflate();

                    mTvLiveEntryRecReason = mRootView.findViewById(R.id.main_tv_live_rec_reason);

                    mFlTitle = mRootView.findViewById(R.id.main_live_fl_title);
                    if (mFlTitle != null && mFlTitle.getBackground() != null) {
                        mFlTitle.getBackground().mutate().setColorFilter(0x33000000, PorterDuff.Mode.SRC_IN);
                    }

                    titleRef = new WeakReference<>(mFlTitle);
                    chatXmlyBoboPlayPageEntryView = mRootView.findViewById(R.id.main_view_chat_xmly_bobo_entry);
                    hideChatXmlyBoBoEntry();
                    mVLiveEntryAvatarContainer = mRootView.findViewById(R.id.main_vg_live_avatar);
                    avatarContainRef = new WeakReference<>(mVLiveEntryAvatarContainer);
                    mIvLiveEntryAvatar = mRootView.findViewById(R.id.main_iv_live_avatar);
                    mIvAvatar2 = mRootView.findViewById(R.id.main_iv_live_avatar2);
                    avatarRef = new WeakReference<>(mIvAvatar2);
                    if (mRightRecommendInfo != null && !TextUtils.isEmpty(mRightRecommendInfo.title)) {
                        mVLiveEntryAvatarContainer.setContentDescription(mRightRecommendInfo.title);
                    }
                    mLottieAnimationView = mRootView.findViewById(R.id.main_lottie_wave);
                    mLottieAnimationView.setVisibility(View.INVISIBLE);
                    mLottieBoBoView = mRootView.findViewById(R.id.main_lottie_bobo_entry);
                    mLottieBoBoView.setVisibility(View.GONE);
                    mLottieLabel = mRootView.findViewById(R.id.main_lottie_label);
                    setClickListener(mVLiveEntryAvatarContainer);
                    if (mFlTitle != null) {
                        setClickListener(mFlTitle);
                    }
                    mLottieBoBoView.setFailureListener(new LottieListener<Throwable>() {
                        @Override
                        public void onResult(Throwable result) {
                            mLottieBoBoView.setImageResource(R.drawable.main_icon_bobo_24);
                        }
                    });
                    mLottieBoBoView.setRepeatCount(2);
                    mLottieBoBoView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            // 新声音播放页-波波入口  点击事件
                            new XMTraceApi.Trace()
                                    .click(59656) // 用户点击时上报
                                    .put("currPage", "newPlay")
                                    .put("xmRequestId", mXmRequestId)
                                    .put("currTrackId", String.valueOf(mSoundInfo != null && mSoundInfo.trackInfo != null ? mSoundInfo.trackInfo.trackId : 0))
                                    .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0))
                                    .createTrace();
                            if (!UserInfoMannage.hasLogined()) {
                                UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext());
                                return;
                            }
                            ToolUtil.clickUrlAction(mFragment, mBoBoLandingPageUrl, mLottieBoBoView);
                        }
                    });
                }
                PlayPageInternalServiceManager.getInstance().registerService(ILiveEntryComponentService.class, mLiveEntryComponentService);
                PlayPageDataManager.getInstance().addThemeColorChangeListener(this);
                if (mPlayContainer!=null){
                    mPlayContainer.addTopLayoutBgColorChangeListener(this::onChangeTopLayoutBgColor);
                    mPlayContainer.addPlayTypeChangeListener(this);
                    mPlayContainer.addPlayStatusListener(this);
                }
            }
        }
    }

    private void setClickListener(View view) {
        view.setOnClickListener(v -> {
            if (OneClickHelper.getInstance().onClick(v)) {
                handleLiveEntryClick(v == mTvLiveEntryRecReason);
            }
        });
        AutoTraceHelper.bindDataCallback(view, new AutoTraceHelper.IDataProvider() {
            @Override
            public Object getData() {
                return mSoundInfo;
            }

            @Override
            public Object getModule() {
                return null;
            }

            @Override
            public String getModuleType() {
                return null;
            }
        });
    }

    private void handleLiveEntryClick(boolean isClickTips) {
        if (mRightRecommendInfo != null) {
            // 先处理点击事件埋点，再执行直播间跳转逻辑，保证埋点数据客户端时序正确
            PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
            if (mRightRecommendInfo.isMc()) {
                // 新声音播放页-顶部mc入口  点击事件
                new XMTraceApi.Trace()
                        .click(45376)
                        .put("currPage", "newPlay")
                        .put("roomId", String.valueOf(mRightRecommendInfo.roomId))
                        .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                        .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .createTrace();
            } else {
                if(mRightRecommendInfo.roomType == 4){
                    String des = "";
                    if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) { //点击都上报description
                       des = mRightRecommendInfo.recReason;
                    }
                    PlayPageLiveEntryManager.playPageLiveEntryClick(mRightRecommendInfo,
                            mXmRequestId,
                            String.valueOf(trackInfo != null ? trackInfo.trackId : 0),
                            String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0),
                            des,
                            isShowLiveAnimatorWhenPaused ? "pause" : "next");
                    return;
                }
                long liveAnchorId = 0L;
                if (!ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos)) {
                    PlayPageMinorData.UserLogo userLogo = mRightRecommendInfo.userLogos.get(0);
                    if (userLogo != null) {
                        liveAnchorId = userLogo.uid;
                    }
                }
                XMTraceApi.Trace traceApi = new XMTraceApi.Trace()
                        .setMetaId(17492)
                        .setServiceId("click")
                        .put("currPage", "newPlay")
                        .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                        .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0))
                        .put("categoryId", String.valueOf(trackInfo != null ? trackInfo.categoryId : 0))
                        .put("anchorId", String.valueOf(liveAnchorId))
                        .put("targetType", isClickTips? "tips" : "button")
                        .put("liveRoomType", String.valueOf(mRightRecommendInfo.bizType))
                        .put("roomId", String.valueOf(mRightRecommendInfo.roomId))
                        .put("liveId", String.valueOf(mRightRecommendInfo.recordId))
                        .put("strategy_track", TextUtils.isEmpty(mRightRecommendInfo.strategyTrack) ? "" : mRightRecommendInfo.strategyTrack)
                        .put("strategy_src", TextUtils.isEmpty(mRightRecommendInfo.strategySrc) ? "" : mRightRecommendInfo.strategySrc)
                        .put("rec_track", TextUtils.isEmpty(mRightRecommendInfo.recTrack) ? "" : mRightRecommendInfo.recTrack) // 推荐算法数据时上报
                        .put("rec_src", TextUtils.isEmpty(mRightRecommendInfo.recSrc) ? "" : mRightRecommendInfo.recSrc) // 推荐算法数据时上报
                        .put("ubtTraceId", TextUtils.isEmpty(mRightRecommendInfo.ubtTraceId) ? "" : mRightRecommendInfo.ubtTraceId) // 推荐算法数据时上报
                        .put("style", mRightRecommendInfo.getAnimationLabelStyleForStat())
                        .put("fullScreenMode", mYCoverComponentsManager.isFullScreen() ? "full":"half") // full 表示全屏，half 表示半屏
                        .put("trackForm", "track") // track 表示音频，video 表示视频
                        .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .put("isAnimated", isLiveInAnimating ? "1" : "0")
                        .put("playViewForm", "1") //  1 表示高唤起 2 表示低唤起
                        .put("titleType", String.valueOf(mRightRecommendInfo.titleSource))
                        .put("status", XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying() ? "play" : "pause");

                if (!TextUtils.isEmpty(mRightRecommendInfo.recReason)) { //点击都上报description
                    traceApi.put("description", mRightRecommendInfo.recReason);
                }
                traceApi.createTrace();
            }

            LastAudioPlayListCache.INSTANCE.saveAudioPlayListCache(mActivity);
            if (!TextUtils.isEmpty(mRightRecommendInfo.landingUrl)) {
                ToolUtil.clickUrlAction(mFragment, mRightRecommendInfo.landingUrl, null);
            } else if (mRightRecommendInfo.isLive() && mRightRecommendInfo.roomId > 0) {
                PlayTools.playLiveAudioByRoomIdWithPlaySource(mActivity, mRightRecommendInfo.roomId,
                        ILivePlaySource.SOURCE_MAIN_PLAY_PAGE_LIVE_ENTRY);
            }
        }
    }

    public void onFragmentResume() {
        if (canUpdateUi()) {
            if (mVLiveEntryAvatarContainer != null && mVLiveEntryAvatarContainer.getVisibility() == View.VISIBLE) {
                if (mSoundInfo != null) {
                    if (mRightRecommendInfo != null && mRightRecommendInfo.isMc()) {
                        // 新声音播放页-顶部mc入口  控件曝光
                        traceMcViewed(mSoundInfo.trackInfo);
                    } else {
                        Logger.d(TAG, "onFragmentResume");
                    }
                }
                traceShowBoboEntry();
            }
            if (!firstLoad) {
                refreshBoBoData();
            }
            firstLoad = false;
            registerVipAction();
        }
    }

    private void registerVipAction() {
        IntentFilter intentFilter = new IntentFilter(PlayUtil.VIP_TOAST_FINISH_ACTION);
        LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext()).registerReceiver(mReceiver, intentFilter);
    }

    /**
     * VipToastFinish 广播
     */
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (PlayUtil.VIP_TOAST_FINISH_ACTION.equals(intent.getAction())) {
                Logger.i(TAG, "receive vip toast finish broadcast");
                isVipToastFinish = true;
                if (isShowLiveAfterVipFinish) {
                    showV2(false);
                }
            }
        }
    };

    private void refreshBoBoData() {
        //刷新波波数据
        if (chatXmlyBoboPlayPageEntryView != null && chatXmlyBoboPlayPageEntryView.getVisibility() == View.VISIBLE) {
            if (mSoundInfo == null || mSoundInfo.trackInfo == null) {
                return;
            }
            long trackId = mSoundInfo.trackInfo.trackId;
            loadPlaySoundInfo(trackId);
        }
    }

    public void onFragmentPause() {
        if (canUpdateUi()) {
            cancelLiveEntryAllAnimation();
            LiveEntryAnimatorManager.INSTANCE.release();
            if (mLottieAnimationView != null && mLottieAnimationView.isAnimating()) {
                mWaveAnimationIsRunningWhilePaused = true;
            }
            toggleWave(false);
            LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext()).unregisterReceiver(mReceiver);
        }
    }

    public void onFragmentDestroy() {
        PlayPageInternalServiceManager.getInstance().unRegisterService(ILiveEntryComponentService.class);
        PlayPageDataManager.getInstance().removeThemeColorChangeListener(this);
        if (mPlayContainer!=null){
            mPlayContainer.removeTopLayoutBgColorChangeListener(this);
            mPlayContainer.removePlayTypeChangeListener(this);
            mPlayContainer.removePlayStatusListener(this);
        }
    }

    public void showLive(PlayingSoundInfo soundInfo, ILoadLiveDataCallback loadLiveDataCallback) {
        Logger.i(TAG, "showLive");
        isVipToastFinish = false;
        isShowLiveAfterVipFinish = false;
        mSoundInfo = soundInfo;
        mLoadLiveDataCallback = loadLiveDataCallback;
        initIfNeeded();
        if (!ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME,
                CConstants.Group_toc.ITEM_PLAYPAGE_LIVE_SWITCH, false)
                || soundInfo == null || soundInfo.trackInfo == null) {
            dealLiveData(false);
            return;
        }
        long trackId = soundInfo.trackInfo.trackId;
        if (trackId != 0 && trackId == mLastTrackId) {
            if (mVLiveEntryAvatarContainer == null || mVLiveEntryAvatarContainer.getVisibility() != View.VISIBLE) {
                dealLiveData(false);
            }
            return;
        }
        if (trackId < 0) {
            dealLiveData(false);
            return;
        }
        mLastTrackId = trackId;
        if (mPlayPageMinorData == null || trackId != mPlayPageMinorData.trackId) {
            loadLiveData(trackId);
        } else {
            showV2(false);

        }
    }

    private void dealLiveData(boolean showLive) {
        if (mLoadLiveDataCallback != null) {
            mLoadLiveDataCallback.showLiveEntry(showLive);
        }
        if (!showLive) {
            AudioPlayPageAnimationManager.INSTANCE.noNeedShow(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
        }
    }

    public static boolean needShowLiveAndMc(PlayPageMinorData.RightRecommendInfo mRightRecommendInfo) {
        return mRightRecommendInfo != null
            && LiveEntryConfigManager.getInstance().shouldLiveEntry()
            && !TextUtils.isEmpty(mRightRecommendInfo.landingUrl)
            && !ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos);
    }

    public void loadLiveData(long trackId) {
        PlayPageMinorDataManager.getInstance().getData(trackId, new IDataCallBack<PlayPageMinorData>() {
            @Override
            public void onSuccess(@Nullable PlayPageMinorData object) {
                mXmRequestId = XmRequestIdManager.getInstance(MainApplication.getMyApplicationContext()).getRequestId();
                mPlayPageMinorData = object;
                if (object != null) {
                    mRightRecommendInfo = object.rightRecommendInfo;
                    if (mRightRecommendInfo == null) {
                        dealLiveData(false);
                        return;
                    }
                    if (needShowLiveAndMc(mRightRecommendInfo)) {
                        showV2(false);
                    } else {
                        dealLiveData(false);
                    }
                } else {
                    dealLiveData(false);
                }
            }

            @Override
            public void onError(int code, String message) {
                dealLiveData(false);
            }
        });
    }

    private boolean needShowBoBoEntry() {
        if (mSoundInfo != null) {
            if (mSoundInfo.albumInfo != null && mSoundInfo.albumInfo.isSketchVideo()) {
                return false;
            }
            Map<String, List<RawResourceElement>> resourceMap = mSoundInfo.resourceMap;
            if (resourceMap != null) {
                List<RawResourceElement> rightLiveEntran = resourceMap.get("rightLiveEntran");
                return rightLiveEntran != null && !rightLiveEntran.isEmpty();
            }
        }
        return false;
    }

    private void showBoBoEntryUI() {
        if (mSoundInfo != null) {
            Map<String, List<RawResourceElement>> resourceMap = mSoundInfo.resourceMap;
            if (resourceMap != null) {
                List<RawResourceElement> rightLiveEntran = resourceMap.get("rightLiveEntran");
                if (rightLiveEntran != null && !rightLiveEntran.isEmpty()) {
                    RawResourceElement rawResourceElement = rightLiveEntran.get(0);
                    if (rawResourceElement != null) {
                        mBoBoLandingPageUrl = rawResourceElement.getLandingPage();
                        Map<String, Object> extInfo = rawResourceElement.getExtInfo();
                        if (extInfo != null) {
                            String statusText = (String) extInfo.get("statusText");
                            String openPicUrl = (String) extInfo.get("openPicUrl");
                            String closePicUrl = (String) extInfo.get("closePicUrl");
                            String waveUrl = (String) extInfo.get("waveUrl");
                            String text = (String) extInfo.get("text");
                            Boolean isNewChatXmly = false;
                            if (extInfo.containsKey("isNew")) {
                                isNewChatXmly = (Boolean) extInfo.get("isNew");
                            }
                            if (isNewChatXmly != null && isNewChatXmly) {
                                if (mLottieBoBoView != null) {
                                    mLottieBoBoView.setVisibility(View.GONE);
                                }
                                showChatXmlyBoboEntry(openPicUrl, closePicUrl, waveUrl, text, statusText,
                                        mBoBoLandingPageUrl);
                            } else {
                                String lottieJson = (String) extInfo.get("pic");
                                if (mLottieBoBoView != null) {
                                    mLottieBoBoView.setVisibility(View.VISIBLE);
                                    traceShowBoboEntry();
                                    try {
                                        mLottieBoBoView.setAnimationFromUrl(lottieJson);
                                        AnimationManagerV2.start(AnimateBiz.BOBO, new Runnable() {
                                            @Override
                                            public void run() {
                                                mLottieBoBoView.playAnimation();
                                                HandlerManager.onTagDestroy(SHARE_TASK_TAG);
                                                HandlerManager.postOnUiThreadDelayed(SHARE_TASK_TAG,
                                                        new Runnable() {
                                                            @Override
                                                            public void run() {
                                                                notifyLiveAnimationFinished();
                                                            }
                                                        }, 6000
                                                );
                                            }
                                        });

                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        mLottieBoBoView.setImageResource(R.drawable.main_icon_bobo_24);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            hideBoBoEntryUI();
        }
    }

    private void traceShowChatxmlyBoboEntry(String status, String style) {
        if (chatXmlyBoboPlayPageEntryView != null && chatXmlyBoboPlayPageEntryView.getVisibility() == View.VISIBLE) {
            if (mSoundInfo == null || mSoundInfo.trackInfo == null || mSoundInfo.albumInfo == null) {
                return;
            }
            ChatXmlyTraceUtil.INSTANCE.showTraceEntry(mSoundInfo.trackInfo.trackId,
                    mSoundInfo.albumInfo.albumId, mSoundInfo.albumInfo.categoryId, mXmRequestId, status,
                    style);
        }
    }

    private void loadPlaySoundInfo(long trackId) {
        if(mSoundInfo == null){
            return;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("trackId", trackId + "");
        CommonRequestM.getPlayPageInfoNew(trackId, map, new IDataCallBack<PlayingSoundInfo>() {
            @Override
            public void onSuccess(@Nullable PlayingSoundInfo data) {
                if (data != null && data.resourceMap != null && !data.resourceMap.isEmpty()) {
                    mSoundInfo.resourceMap = data.resourceMap;
                    if (mSoundInfo.playPageAbTestResult != null) {
                        //更新动效管理配置
                        AnimationManagerV2.initWithConfig(
                                PlayPageDataManager.getInstance().getCurTrackId(),
                                mSoundInfo.playPageAbTestResult.getPopupArrangementkey());
                    }
                    showBoBoEntryUI();
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private void traceShowBoboEntry() {
        // 新声音播放页-波波入口  控件曝光
        if (mLottieBoBoView.getVisibility() != View.VISIBLE) return;
        new XMTraceApi.Trace()
                .setMetaId(59657)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newPlay")
                .put("xmRequestId", mXmRequestId)
                .put("currTrackId", String.valueOf(mSoundInfo != null && mSoundInfo.trackInfo != null ? mSoundInfo.trackInfo.trackId : 0))
                .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0))
                .put(XmRequestIdManager.CONT_ID, String.valueOf(mSoundInfo != null && mSoundInfo.trackInfo != null ? mSoundInfo.trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_TYPE, "newPlayBoBo")
                .createTrace();
    }

    private void hideBoBoEntryUI() {
        if (mLottieBoBoView != null) {
            mLottieBoBoView.setVisibility(View.GONE);
            mLottieBoBoView.clearAnimation();
            AnimationManagerV2.end(AnimateBiz.BOBO);
        }
        if (chatXmlyBoboPlayPageEntryView != null) {
            hideChatXmlyBoBoEntry();
            chatXmlyBoboPlayPageEntryView.release();
        }
    }

    private void showV2(boolean isShowWhenPaused) {
        if (!isVipToastFinish) {
            Logger.i(TAG, "wait vip toast finish broadcast");
            isShowLiveAfterVipFinish = true;
            return;
        }
        if (mSoundInfo == null || mRightRecommendInfo == null || !canUpdateUi()) {
            return;
        }

        mLastVisibleWhileTabChanged = false;
        isShowLiveAnimatorWhenPaused = isShowWhenPaused;

        initIfNeeded();
        boolean shouldShowDynamicPart = LiveEntryConfigManager.getInstance().shouldLiveEntry();
        LiveEntryAnimatorManager.INSTANCE.release();
        chatXmlyBoboPlayPageEntryView.release();
        cancelLiveEntryAllAnimation();
        isChatXmlyAnimating = false;
        isLiveInAnimating = false;
        if (shouldShowDynamicPart) {

            dealLiveData(true);
            if (mVLiveEntryAvatarContainer != null && mVLiveEntryAvatarContainer.getVisibility() != View.VISIBLE) {
                mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
            }
            hideBoBoEntryUI();
            if (mRightRecommendInfo.isMc()) {
                // mc没有推荐理由时，用title来显示
                if (TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
                    mRightRecommendInfo.recReason = mRightRecommendInfo.title;
                }
            }
            PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
            mLiveEntryAvatarPartHideTask = () -> {
                if (canUpdateUi()) {
                    toggleWave(false);
                }
            };
            if (mVLiveEntryAvatarContainer != null) {
                mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
                mVLiveEntryAvatarContainer.setAlpha(1);
            }
            LiveEntryConfigManager.getInstance().recordDynamicPartShow();
            if (mTvLiveEntryRecReason != null) {
                mTvLiveEntryRecReason.setText(mRightRecommendInfo.recReason);
            }

            if (!ToolUtil.isEmptyCollects(mRightRecommendInfo.userLogos)) {
                PlayPageMinorData.UserLogo userLogo = mRightRecommendInfo.userLogos.get(0);
                if (userLogo != null) {
                    int avatarSize = BaseUtil.dp2px(mActivity, 22);
                    ViewGroup.LayoutParams layoutParams = mIvLiveEntryAvatar.getLayoutParams();
                    if (layoutParams != null && layoutParams.width != avatarSize || layoutParams != null && layoutParams.height != avatarSize) {
                        layoutParams.width = avatarSize;
                        layoutParams.height = avatarSize;
                        mIvLiveEntryAvatar.setLayoutParams(layoutParams);
                    }
                    ImageManager.from(mActivity).displayImage(mIvLiveEntryAvatar, userLogo.logo
                        , com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default);
                }
                int avatar2Visibility = View.GONE;
                if (mRightRecommendInfo.userLogos.size() >= 2) {
                    PlayPageMinorData.UserLogo userLogo2 = mRightRecommendInfo.userLogos.get(1);
                    if (userLogo2 != null) {
                        ImageManager.from(mActivity).displayImage(mIvAvatar2, userLogo2.logo
                            , com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default);
                        avatar2Visibility = View.VISIBLE;
                    }
                }
                mIvAvatar2.setVisibility(avatar2Visibility);
            }

            if (mRightRecommendInfo != null) {
                if (mRightRecommendInfo.hasAnimationLabel()) {
                    String reason = mRightRecommendInfo.recReason;
                    Logger.log(" ---start(AnimateBiz.RED_PACKET");
                    if (TextUtils.isEmpty(reason)){
                        showRedPacket();
                    } else {
                        showTips(isShowWhenPaused);
                    }
                } else  {
                    Logger.log(" ---start(AnimateBiz.LIVE_PUBLIC");
                    showTips(isShowWhenPaused);
                }
            }

            if (null != mRightRecommendInfo && mRightRecommendInfo.isMc()) {
                // 新声音播放页-顶部mc入口  控件曝光
                traceMcViewed(trackInfo);
            } else if (!isShowWhenPaused) { //暂停的时候不上报曝光埋点
                postOnUiThreadDelayed(new Runnable() {
                    @Override public void run() {
                        trackLiveShow(trackInfo, mRightRecommendInfo,false);
                    }
                }, 1000L);
            }

        } else {
            if (mVLiveEntryAvatarContainer != null) {
                mVLiveEntryAvatarContainer.setVisibility(View.INVISIBLE);
            }
            dealLiveData(false);
        }
    }



    private void trackLiveShow(PlayingSoundInfo.TrackInfo trackInfo, PlayPageMinorData.RightRecommendInfo rightRecommendInfo, boolean isExpand) {
        if (rightRecommendInfo == null) return;
        if(rightRecommendInfo.roomType == 4){
            String des = "";
            if (mTvLiveEntryRecReason != null
                    && mFlTitle.getVisibility() == View.VISIBLE
                    && !TextUtils.isEmpty(rightRecommendInfo.recReason) && isExpand) {
                des = rightRecommendInfo.recReason;
            }
            PlayPageLiveEntryManager.playPageLiveEntryShow(rightRecommendInfo,
                    mXmRequestId,
                    String.valueOf(trackInfo != null ? trackInfo.trackId : 0),
                    String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0),
                    des,
                    isShowLiveAnimatorWhenPaused ? "pause" : "next");
            return;
        }

        long liveAnchorId = 0L;
        if (!ToolUtil.isEmptyCollects(rightRecommendInfo.userLogos)) {
            PlayPageMinorData.UserLogo userLogo = rightRecommendInfo.userLogos.get(0);
            if (userLogo != null) {
                liveAnchorId = userLogo.uid;
            }
        }

        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(17493)
                .setServiceId("slipPage")
                .put("exploreType", "1")
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ? mSoundInfo.albumInfo.albumId : 0))
                .put("categoryId", String.valueOf(trackInfo != null ? trackInfo.categoryId : 0))
                .put("anchorId", String.valueOf(liveAnchorId))
                .put("liveRoomType", String.valueOf(rightRecommendInfo.bizType))
                .put("roomId", String.valueOf(rightRecommendInfo.roomId))
                .put("liveId", String.valueOf(rightRecommendInfo.recordId))
                .put("strategy_track", TextUtils.isEmpty(rightRecommendInfo.strategyTrack) ? "" : rightRecommendInfo.strategyTrack)
                .put("strategy_src", TextUtils.isEmpty(rightRecommendInfo.strategySrc) ? "" : rightRecommendInfo.strategySrc)
                .put("rec_track", TextUtils.isEmpty(rightRecommendInfo.recTrack) ? "" : rightRecommendInfo.recTrack) // 推荐算法数据时上报
                .put("rec_src", TextUtils.isEmpty(rightRecommendInfo.recSrc) ? "" : rightRecommendInfo.recSrc) // 推荐算法数据时上报
                .put("ubtTraceId", TextUtils.isEmpty(rightRecommendInfo.ubtTraceId) ? "" : rightRecommendInfo.ubtTraceId) // 推荐算法数据时上报
                .put("style", rightRecommendInfo.getAnimationLabelStyleForStat())
                .put("fullScreenMode", mYCoverComponentsManager.isFullScreen() ? "full" : "half") // full 表示全屏，half 表示半屏
                .put("trackForm", "track") // track 表示音频，video 表示视频
                .put(XmRequestIdManager.CONT_ID, String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_TYPE, "newPlayLive")
                .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
                .put("isAnimated", isExpand ? "1" : "0")
                .put("playViewForm", "1") //  1 表示高唤起 2 表示低唤起
                .put("titleType", String.valueOf(rightRecommendInfo.titleSource))
                .put("showTime", isShowLiveAnimatorWhenPaused ? "pause" : "next");




        if (mTvLiveEntryRecReason != null
            && mFlTitle.getVisibility() == View.VISIBLE
            && !TextUtils.isEmpty(rightRecommendInfo.recReason) && isExpand) {
            trace.put("description", rightRecommendInfo.recReason);
        }
        trace.createTrace();
    }

    private final Runnable mLiveAvatarRun = new Runnable() {
        @Override
        public void run() {
            int maxWidth = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 167f);
            int textWidth = (int) mTvLiveEntryRecReason.getPaint().measureText(mTvLiveEntryRecReason.getText().toString());
            int width;
            if (textWidth >= maxWidth) {
                width = maxWidth;
            } else {
                width = textWidth + BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 16f);
            }
            // 增加了右侧 > 图标所占的宽度
            width += BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 19f);

            if (titleRef.get() != null && avatarContainRef.get() != null && avatarRef.get() != null) {
                LiveEntryAnimatorManager.INSTANCE.startExpand(new LiveEntryAnimatorManager.IAnimatorCallback() {
                    @Override
                    public void onLiveMcEntryExpandAnimatorChange(boolean isStart) {
                        mPlayContainer.onLiveMcEntryExpandAnimatorChange(isStart);
                        if (isStart) {
                            if (!mRightRecommendInfo.isMc()) {
                                isLiveInAnimating = true;
                                trackLiveShow(mSoundInfo.trackInfo, mRightRecommendInfo,true);
                            }
                        }
                    }

                    @Override
                    public void onAnimatorEnd() {
                        showRedPacket();

                        HandlerManager.onTagDestroy(SHARE_TASK_TAG);
                        HandlerManager.postOnUiThreadDelayed(SHARE_TASK_TAG,
                                new Runnable() {
                                    @Override
                                    public void run() {
                                        notifyLiveAnimationFinished();
                                    }
                                }, 6000
                        );
                    }
                }, titleRef.get(), avatarContainRef.get(), width, mRightRecommendInfo.isMc() ? avatarRef.get() : null);
            }
        }
    };

    private void showRedPacket(){
        if (!canUpdateUi()) return;
        //红包逻辑
        if (mRightRecommendInfo.hasAnimationLabel() && mLottieLabel != null) {
            removeCallbacks(mLiveEntryAvatarPartHideTask);
            // 动画流程：
            // 直播头像（带标签）到福袋/红包出现 3s ，3s时直播头像开始消失 红包开始出现 3.5s完全展示，4s红包/福袋开始运动。
            // 存在到7.5s时开始消失 出现头像和气泡 8s时完全出现 后续存在的动画及时长与现在线上的保持一直就可以
            HandlerManager.postOnUiThreadDelayed(DELAY_TASK_TAG, () -> {
                showOrHideViewAnimation(mVLiveEntryAvatarContainer, false, new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        toggleWave(false);
                        mLottieAnimationView.setVisibility(View.INVISIBLE);
                    }
                });
                showOrHideViewAnimation(mLottieLabel, true);
                mLottieLabel.setVisibility(View.VISIBLE);
                startLabelAnimation();
            }, 500);
        }
    }

    private void showTips(boolean isShowWhenPaused) {
        mIsTipsShow = false;
        if (mLoadLiveDataCallback != null && !mLoadLiveDataCallback.shouldShowTips()) {
            return;
        }

        Logger.i(TAG, "show tips");
        long currentTime = System.currentTimeMillis();
        if (isShowWhenPaused) {
            if (currentTime - mLastLiveAnimationStartTime <= mSoundPauseAnimationCd * 1000) {
                Logger.i(TAG, "too fast to show tips when sound paused");
                return;
            }
        } else {
            if (currentTime - mLastLiveAnimationStartTime <= mSoundSwitchAnimationCd * 1000) {
                Logger.i(TAG, "too fast to show tips when sound switch");
                return;
            }
        }
        if (mRightRecommendInfo != null && !TextUtils.isEmpty(mRightRecommendInfo.recReason)) {
            mLastLiveAnimationStartTime = currentTime;
            postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, WAVE_ANIMATION_DURATION);
            if (mLottieAnimationView != null) {
                String lottieFileName = mRightRecommendInfo.isMc() ? "lottie/play_cover_living_lottie/myclub_wave.json"
                        : "lottie/main_audio_play_top_right_live_wave.json";
                if (!lottieFileName.equals(mCurLottieFileName)) {
                    mCurLottieFileName = lottieFileName;
                    mLottieAnimationView.setAnimation(lottieFileName);
                }
                mLottieAnimationView.playAnimation();
                mLottieAnimationView.setVisibility(View.VISIBLE);
                isLiveInAnimating = true;
            }
            mTvLiveEntryRecReason.setVisibility(View.VISIBLE);
            showOrHideViewAnimation(mLottieAnimationView, true);
            mIsTipsShow = true;
            setBackGroundColor();
            HandlerManager.removeCallbacks(mLiveAvatarRun);
            HandlerManager.postOnUIThreadDelay(mLiveAvatarRun, 1500);
        } else {
            notifyLiveAnimationFinished();
        }
    }

    private void notifyLiveAnimationFinished() {
        Context context = mPlayContainer.getContext();
        if (context != null) {
            LocalBroadcastManager.getInstance(context.getApplicationContext()).sendBroadcast(
                    new Intent("local_live_animation_finished")
            );
        }
    }


    private void startLabelAnimation() {
        if (mRightRecommendInfo == null || !mRightRecommendInfo.isLive() || mLottieLabel == null) {
            return;
        }
        String lottiePath = null;
        switch (mRightRecommendInfo.showLabelType) {
        case PlayPageMinorData.RightRecommendInfo.SHOW_RED_PACKET:
            lottiePath = "lottie/play_page_live/red_packet.json";
            break;
        case PlayPageMinorData.RightRecommendInfo.SHOW_LUCKY_BG:
            lottiePath = "lottie/play_page_live/lucky_bag.json";
            break;
        default:
            break;
        }
        //lottiePath = "lottie/play_page_live/red_packet.json";
        if (TextUtils.isEmpty(lottiePath)) {
            return;
        }
        mLottieLabel.setAnimation(lottiePath);
        mLottieLabel.playAnimation();
        HandlerManager.postOnUiThreadDelayed(DELAY_TASK_TAG, () -> {
            mLottieLabel.cancelAnimation();
            showOrHideViewAnimation(mLottieLabel, false, new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    mLottieLabel.setVisibility(View.INVISIBLE);
                }
            });
            showOrHideViewAnimation(mVLiveEntryAvatarContainer, true);
            mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
            mLottieAnimationView.setVisibility(View.VISIBLE);
            toggleWave(true);
            postOnUiThreadDelayed(mLiveEntryAvatarPartHideTask, 12000);
        }, 4500);
    }

    private void hideLabelAnimation() {
        if (mLottieLabel != null && mLottieLabel.getVisibility() == View.VISIBLE) {
            mLottieLabel.cancelAnimation();
            mLottieLabel.setVisibility(View.INVISIBLE);
            if (mVLiveEntryAvatarContainer != null) {
                mVLiveEntryAvatarContainer.setVisibility(View.VISIBLE);
                mVLiveEntryAvatarContainer.setAlpha(1);
            }
        }
    }

    private void traceMcViewed(PlayingSoundInfo.TrackInfo trackInfo) {
        if (mRightRecommendInfo == null || !mRightRecommendInfo.isMc()) {
            return;
        }
        new XMTraceApi.Trace()
                .setMetaId(45377)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("roomId", String.valueOf(mRightRecommendInfo.roomId))
                .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_ID, String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_TYPE, "newPlayMc")
                .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
                .createTrace();
        Logger.i(TAG, "traceMcViewed 45377 trackId: " + (trackInfo != null ? trackInfo.trackId : 0) + " roomId: " + mRightRecommendInfo.roomId);
        reportIfTraceTooFrequently();
    }

    private void reportIfTraceTooFrequently() {
        // 如果曝光埋点过于频繁，上报一下
        long curTime = System.currentTimeMillis();
        if (curTime - mMcLastExposeTime < 300 * 1000) {
            mMcExposeCount++;
        } else {
            mMcExposeCount = 0;
        }
        mMcLastExposeTime = curTime;
        if (mMcExposeCount > 50 && mSoundInfo != null && mSoundInfo.trackInfo != null) {
            String stackTrace = Log.getStackTraceString(new Throwable());
            Logger.i(TAG, "mc expose too frequently " + mSoundInfo.trackInfo.trackId + " " + stackTrace);
        }
    }

    public void hide(boolean hideAll) {
        if (mRootView != null && mRootView.getVisibility() == View.VISIBLE) {
            ViewStatusUtil.setVisible(View.INVISIBLE, mVLiveEntryAvatarContainer);
            cancelLiveEntryAllAnimation();
            LiveEntryAnimatorManager.INSTANCE.release();
            chatXmlyBoboPlayPageEntryView.release();
            if (needShowBoBoEntry() && !hideAll) {
                showBoBoEntryUI();
            } else {
                hideBoBoEntryUI();
            }
        }
    }

    public void clearData() {
        mLastTrackId = 0;
        mPlayPageMinorData = null;
        mIsLiveEntryAnimating = false;
    }

    private void cancelLiveEntryAllAnimation() {
        if (mLiveEntryAvatarPartHideTask != null) {
            removeCallbacks(mLiveEntryAvatarPartHideTask);
            mLiveEntryAvatarPartHideTask.run();
            mLiveEntryAvatarPartHideTask = null;
            hideLabelAnimation();
            AudioPlayPageAnimationManager.INSTANCE.end(AudioPlayPageAnimationManager.EAnimation.LIVE_MC);
        }
        if (mLiveAvatarRun != null) {
            HandlerManager.removeCallbacks(mLiveAvatarRun);
        }
        HandlerManager.onTagDestroy(DELAY_TASK_TAG);
    }

    private void toggleWave(boolean isStart) {
        if (mLottieAnimationView != null) {
            if (isStart) {
                mLottieAnimationView.setVisibility(View.VISIBLE);
                mLottieAnimationView.playAnimation();
            } else {
                mLottieAnimationView.cancelAnimation();
                mLottieAnimationView.setVisibility(View.INVISIBLE);
            }
        }
        mIsLiveEntryAnimating = isStart;
    }

    private void showOrHideViewAnimation(View view, boolean isShow) {
        showOrHideViewAnimation(view, isShow, null);
    }

    private void showOrHideViewAnimation(View view, boolean isShow, @Nullable Animator.AnimatorListener listener) {
        float startFraction = isShow ? 0 : 1;
        float endFraction = isShow ? 1 : 0;
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(view, "alpha", startFraction, endFraction);
        objectAnimator.setDuration(500);
        if (listener != null) {
            objectAnimator.addListener(listener);
        }
        objectAnimator.start();
        mIsLiveEntryAnimating = isShow;
    }

    public void release(){
        mFragment = null;
    }

    private void postOnUiThreadDelayed(Runnable runnable, long delay) {
        if (mFragment != null) {
            mFragment.postOnUiThreadDelayed(runnable, delay);
        }
    }

    private boolean canUpdateUi() {
        return mFragment != null && mFragment.canUpdateUi();
    }

    @Override
    public void onThemeColorChanged(int foregroundColor, int backgroundColor) {
        setBackGroundColor();
    }

    @Override
    public void onChangeTopLayoutBgColor(int mainColorId, int subColorId) {
        setBackGroundColor();
    }

    @Override
    public void onPlayTypeChanged(boolean isAudio) {
        setBackGroundColor();
    }

    @Override
    public void onPlayStart() {

    }

    @Override
    public void onPlayPause() {
        Logger.i(TAG, "onPlayPause mIsLiveEntryAnimating=" + mIsLiveEntryAnimating);

        if (TextUtils.isEmpty(mSoundPauseCdKey)) {
            mSoundPauseCdKey = ABTest.getString(CConstants.Group_toc.AB_LIVE_SOUND_PAUSE, "NONE");
            String animationCd = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_LIVE_PAUSE_ANIMATION_EFFECT, "");
            try {
                JSONObject jsonObject = new JSONObject(animationCd);
                mSoundPauseAnimationCd = jsonObject.optInt(mSoundPauseCdKey, 0);
            } catch (Exception e) {
                e.printStackTrace();
                CrashReport.postCatchedException(e);
            }
        }

        if (mSoundPauseAnimationCd > 0 && !mIsLiveEntryAnimating) {
            showV2(true);
        }

    }

    @Override
    public void onPlayStop() {

    }

    @Override
    public void onSoundPlayComplete() {

    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return false;
    }

    public interface ILoadLiveDataCallback {
        void showLiveEntry(boolean show);

        boolean shouldShowTips();
    }

    private final ILiveEntryComponentService mLiveEntryComponentService = new ILiveEntryComponentService() {
        @Override
        public void onLiveEntryAnimate(boolean isAnimate) {

        }

        @Override
        public boolean isLiveEntryAnimating() {
            return mIsLiveEntryAnimating;
        }
    };

    private void setBackGroundColor() {
        if (!canUpdateUi() || !mIsTipsShow) {
            return;
        }

//        if ( !PlayPageDataManager.getInstance().isAudioPage()) {
//            int backgroundColor = BaseApplication.getMyApplicationContext().getResources().getColor(R.color.main_color_14ffffff);
//            if (mFlTitle != null && mFlTitle.getBackground() != null) {
//                mFlTitle.getBackground().mutate().setColorFilter(backgroundColor, PorterDuff.Mode.SRC_IN);
//            }
//        } else  {
//            int backgroundColor = PlayPageDataManager.getInstance().getBackgroundColor();
//            int newColor = ColorUtil.covertColorToFixedSaturationAndLightnessWithAlpha(
//                    backgroundColor, 0xfa, 0.35f, 0.26f);
//            if (mFlTitle != null && mFlTitle.getBackground() != null) {
//                mFlTitle.getBackground().mutate().setColorFilter(newColor, PorterDuff.Mode.SRC_IN);
//            }
//        }
    }

    private void showChatXmlyBoboEntry(
            String openPicUrl,
            String closePicUrl,
            String waveUrl,
            String text,
            String statusText,
            String boBoLandingPageUrl
    ) {
        ViewStatusUtil.setVisible(View.VISIBLE, chatXmlyBoboPlayPageEntryView);
        if (!TextUtils.isEmpty(openPicUrl)) {
            chatXmlyBoboPlayPageEntryView.setOpenAvatarLottieJsonAndPlay(openPicUrl);
        }
        chatXmlyBoboPlayPageEntryView.setWaveUrl(waveUrl);
        if (!TextUtils.isEmpty(closePicUrl)) {
            chatXmlyBoboPlayPageEntryView.setCloseAvatarLottieJsonAndPlay(closePicUrl);
        }
        chatXmlyBoboPlayPageEntryView.setTitle(text);
        chatXmlyBoboPlayPageEntryView.setStatusText(statusText);
        if (!TextUtils.isEmpty(boBoLandingPageUrl)) {
            chatXmlyBoboPlayPageEntryView.setJumpChatXmlyUrl(boBoLandingPageUrl);
        }
        chatXmlyBoboPlayPageEntryView.hideAvatarOutlineView();
        chatXmlyBoboPlayPageEntryView.resetMarquee();
        traceShowChatxmlyBoboEntry(chatXmlyBoboPlayPageEntryView.getStatusText(), "收缩");
        AnimationManagerV2.start(AnimateBiz.BOBO, this::startChatXmlyExpandAnimation);
        //startChatXmlyExpandAnimation();
    }

    private void startChatXmlyExpandAnimation() {
        if (!ViewStatusUtil.isVisible(chatXmlyBoboPlayPageEntryView)) {
            return;
        }
        chatXmlyBoboPlayPageEntryView.startExpandAnimation(new LiveEntryAnimatorManager.IAnimatorCallback() {
            @Override
            public void onAnimatorEnd() {
                isChatXmlyAnimating = false;
                traceShowChatxmlyBoboEntry(chatXmlyBoboPlayPageEntryView.getStatusText(), "收缩");
                AnimationManagerV2.end(AnimateBiz.BOBO);

                HandlerManager.onTagDestroy(SHARE_TASK_TAG);
                HandlerManager.postOnUiThreadDelayed(SHARE_TASK_TAG,
                        new Runnable() {
                            @Override
                            public void run() {
                                notifyLiveAnimationFinished();
                            }
                        }, 6000
                );
            }

            @Override
            public void onLiveMcEntryExpandAnimatorChange(boolean isStart) {
                isChatXmlyAnimating = isStart;
                if (mPlayContainer != null) {
                    mPlayContainer.onLiveMcEntryExpandAnimatorChange(isStart);
                }
            }
        });
        traceShowChatxmlyBoboEntry(chatXmlyBoboPlayPageEntryView.getStatusText(), "展开");
    }

    private void hideChatXmlyBoBoEntry() {
        ViewStatusUtil.setVisible(View.GONE, chatXmlyBoboPlayPageEntryView);
    }
}
