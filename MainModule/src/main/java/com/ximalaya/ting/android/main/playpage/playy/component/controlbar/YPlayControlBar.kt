package com.ximalaya.ting.android.main.playpage.playy.component.controlbar

import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.transition.Fade
import androidx.transition.TransitionManager
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.timelinecard.CARD_TYPE_TALK
import com.ximalaya.ting.android.host.manager.play.timelinecard.PlayTimelineUtil
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData
import com.ximalaya.ting.android.host.model.play.PlayTimelineCard
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.safeAs
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.ui.DrawableUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseComponentWithPlayStatusListener
import com.ximalaya.ting.android.main.playpage.internalservice.ITimelineFloatingViewListener
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.IVideoPlayStatueListener
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.biz.SeekBarFloating
import com.ximalaya.ting.android.main.playpage.playy.component.controlbar.view.TouchableFrameLayout
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsEnum
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsManager
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.YFunctionEntriesComponentV2
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

class YPlayControlBar(
    private val playContainer: IPlayContainer
) : BaseComponentWithPlayStatusListener(), IXmAdsStatusListener, IVideoPlayStatueListener,
    YCoverComponentsManager.ICoverChangeListener {

    private lateinit var controlBar: YPlayControlBarComponent

    companion object {
        val TIME_FORMAT = "%s / %s"
    }

    var mFloatingProgressViewTimeText: String = ""
    private var mFloatingProgressView: View? = null

    private var audioContainerViewGroup: ViewGroup? = null
    private var mVFloatingProgressMaskGradient: View? = null
    private var mVFloatingProgressMask: View? = null
    private var mTvProgressFloating: TextView? = null

    private var mFloatingCardContainer: ConstraintLayout? = null
    private var mLlFloatingCardTitle: LinearLayoutCompat? = null
    private var mIvFloatingCardTag: ImageView? = null
    private var mTvFloatingCardTitle: TextView? = null
    private var mIvFloatingCardPicture: ImageView? = null

    private var bizPositionFrame: TouchableFrameLayout? = null

    private var mForegroundColor: Int = 0
    private var mBackgroundColor: Int = 0

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)
        controlBar = YPlayControlBarComponent.newInstance(fragment, playContainer, this@YPlayControlBar)
        playContainer.registerSkinChangeListener(controlBar)
        PlayPageInternalServiceManager.getInstance().registerService(
            ITimelineFloatingViewListener::class.java, mITimelineFloatingViewListener
        )
    }

    fun init(vAncestorView: ViewGroup) {
        controlBar.init(vAncestorView)
    }

    override fun onResume() {
        super.onResume()
        controlBar?.onResume()
    }

    override fun onPause() {
        super.onPause()
        controlBar.isDocScrolledFull = false
        HandlerManager.postOnUIThreadDelay(Runnable {
            showHideFullBar(true,false)
        },500)
        controlBar?.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        controlBar?.onDestroy()
        PlayPageInternalServiceManager.getInstance().unRegisterService(ITimelineFloatingViewListener::class.java)
        playContainer.unregisterSkinChangeListener(controlBar)
    }

    override fun onThemeColorChanged(foregroundColor: Int, backgroundColor: Int) {
        mForegroundColor = foregroundColor
        mBackgroundColor = backgroundColor
        if (isCurComponentPpt()){
            controlBar.onThemeColorChanged(foregroundColor, context.resources.getColor(R.color.main_color_000000))
        } else {
            controlBar.onThemeColorChanged(foregroundColor, backgroundColor)
        }

        updateFloatingCardView(mCurShowingFloatingCard, true)
        updateFloatingMaskColor()
    }

    fun showControlBarView(){
        if (!YUtils.isLandScape(context)) {
            controlBar.view?.visibility = View.VISIBLE
        }
    }

    fun hideControlBarView(){
        if (controlBar?.view?.visibility == View.VISIBLE){
            controlBar?.view?.visibility = View.GONE
        }
    }
    fun showSeekbarPart() {
        controlBar.showHideSeekbarGroup(View.VISIBLE)
    }

    private fun hideSeekbarPart() {
        controlBar.showHideSeekbarGroup(View.GONE)
    }

    private fun isCurComponentPpt() : Boolean {
        return false
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        if (soundInfo != null) {
            controlBar.onSoundInfoLoaded(soundInfo)
        }
    }

    fun onMinorDataLoaded(data: PlayPageMinorData) {
        controlBar.onMinorDataLoaded(data)
    }

    fun dismissTips() {
        controlBar.dismissTips()
    }

    override fun onPlayStart() {
        controlBar?.onPlayStart()
    }

    override fun onPlayPause() {
        controlBar?.onPlayPause()
    }

    override fun onPlayStop() {
        controlBar?.onPlayStop()
    }

    override fun onSoundPlayComplete() {
        controlBar?.onSoundPlayComplete()
    }

    override fun onSoundPrepared() {
        controlBar?.onSoundPrepared()
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        controlBar.onSoundSwitch(lastModel, curModel)
    }

    override fun onBufferingStart() {
        controlBar?.onBufferingStart()
    }

    override fun onBufferingStop() {
        controlBar?.onBufferingStop()
    }

    override fun onBufferProgress(percent: Int) {
        controlBar?.onBufferProgress(percent)
    }

    override fun onPlayProgress(currPos: Int, duration: Int) {
        controlBar?.onPlayProgress(currPos, duration)
    }

    override fun onError(exception: XmPlayerException?): Boolean {
        return controlBar?.onError(exception) ?: false
    }

    override fun onStartGetAdsInfo(playMethod: Int, duringPlay: Boolean, isPaused: Boolean) {
        (controlBar as? IXmAdsStatusListener)?.onStartGetAdsInfo(playMethod, duringPlay, isPaused)
    }

    override fun onGetAdsInfo(ads: AdvertisList?) {
        (controlBar as? IXmAdsStatusListener)?.onGetAdsInfo(ads)
    }

    override fun onAdsStartBuffering() {

    }

    override fun onAdsStopBuffering() {
    }

    override fun onStartPlayAds(ad: Advertis?, position: Int) {
        (controlBar as? IXmAdsStatusListener)?.onStartPlayAds(ad, position)
    }

    override fun onCompletePlayAds() {
        (controlBar as? IXmAdsStatusListener)?.onCompletePlayAds()
    }

    override fun onError(what: Int, extra: Int) {
    }

    private fun initFloatingProgressView() {
        if (mFloatingProgressView != null || mFragment == null) {
            return
        }
        val viewStub = mFragment.findViewById<ViewStub>(R.id.main_vs_floating_progress)
        if (viewStub != null) {
            mFloatingProgressView = viewStub.inflate()
            audioContainerViewGroup = mFragment?.findViewById(R.id.main_audio_container)
            mVFloatingProgressMaskGradient = mFragment?.findViewById<View>(R.id.main_v_floating_bg_mask_gradient)
            mVFloatingProgressMask = mFragment?.findViewById<View>(R.id.main_v_floating_progress_mask)
            mFloatingCardContainer = mFloatingProgressView!!.findViewById(R.id.main_cl_floating_card_title_container)
            mLlFloatingCardTitle = mFloatingProgressView!!.findViewById(R.id.main_ll_floating_card_title)
            mIvFloatingCardTag = mFloatingProgressView!!.findViewById(R.id.main_iv_floating_card_tag)
            mTvFloatingCardTitle = mFloatingProgressView!!.findViewById(R.id.main_tv_floating_card_title)
            mIvFloatingCardPicture = mFloatingProgressView!!.findViewById(R.id.main_iv_floating_card_picture)
            mTvProgressFloating = mFloatingProgressView!!.findViewById(R.id.main_tv_progress_floating)

            mTvProgressFloating?.setTypeface(
                Typeface.createFromAsset(
                ToolUtil.getCtx().resources.assets, "fonts/roboto_medium_numbers.ttf"
            ))

            bizPositionFrame = mFragment?.findViewById(R.id.main_floating_biz_container)
        }
    }

    fun updateFloatingProgressTv() {
        if (mTvProgressFloating?.visibility == View.VISIBLE && mFloatingProgressViewTimeText != null) {
            val spannableString = SpannableString(mFloatingProgressViewTimeText)
            val index: Int = mFloatingProgressViewTimeText.indexOf("/")
            if (index > 0) {
                spannableString.setSpan(
                    ForegroundColorSpan(0x4cffffff.toInt()),
                    index,
                    mFloatingProgressViewTimeText.length,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
            mTvProgressFloating?.text = spannableString
        }
    }

    private fun updateFloatingProgressView() {
        if (mFloatingProgressView?.visibility == View.VISIBLE) {
            updateFloatingProgressTv()
        }
    }

    private fun checkFloatingBizView() {
        val bizContainer = bizPositionFrame?: return
        val bizArbiter = playContainer.getBizArbiters()
        val floatingBiz = bizArbiter?.getBizResource(SeekBarFloating.PODCAST)
        val floatingBizView = floatingBiz?.provideView()
        if (floatingBizView != null) {
            bizContainer.anyTouch = {
                controlBar.cancelSeekbarWithDelay()
                false
            }
            controlBar.updateSeekBarRestoreDelay(2000)
            ViewStatusUtil.setVisible(View.GONE, mFloatingCardContainer)

            (floatingBizView.parent as? ViewGroup)?.removeView(floatingBizView)
            bizContainer.addView(floatingBizView, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT))
            ViewStatusUtil.setVisible(View.VISIBLE, bizContainer)
            floatingBiz.onShow()
        } else {
            controlBar.updateSeekBarRestoreDelay(500)
        }
    }

    private fun updateFloatingMaskColor() {
        if (mVFloatingProgressMaskGradient?.isShown == true && mVFloatingProgressMask?.isShown == true) {
            mVFloatingProgressMaskGradient?.background = DrawableUtil.GradientDrawableBuilder()
                .color(intArrayOf(
                    Color.TRANSPARENT,
                    mBackgroundColor
                ))
                .orientation(GradientDrawable.Orientation.TOP_BOTTOM)
                .build()
            mVFloatingProgressMask?.setBackgroundColor(mBackgroundColor)
        }
    }

    fun hideFunctionBar() {
        playContainer.getFunctionEntriesComponent()?.let {
            it.hideForSeek()
        }
    }

    fun showFunctionBar() {
        playContainer.getFunctionEntriesComponent()?.let {
            it.showForSeek()
        }
    }

    fun showFloatingProgressView() {
        audioContainerViewGroup?.also { parent ->
            TransitionManager.beginDelayedTransition(parent, Fade().apply {
                mFloatingProgressView?.also { addTarget(it) }
                mVFloatingProgressMask?.also { addTarget(it) }
                mVFloatingProgressMaskGradient?.also { addTarget(it) }
                bizPositionFrame?.also { addTarget(it) }
            })
        }
        hideFunctionBar()

        initFloatingProgressView()
        if (mFloatingProgressView == null) return

        ViewStatusUtil.setVisible(View.VISIBLE, mFloatingProgressView)
        ViewStatusUtil.setVisible(View.VISIBLE, mVFloatingProgressMask)
        ViewStatusUtil.setVisible(View.VISIBLE, mVFloatingProgressMaskGradient)
        checkFloatingBizView()
        updateFloatingMaskColor()


        if (mFloatingProgressView!!.height <= 0) {
            mFloatingProgressView!!.post { updateFloatingProgressView() }
        } else {
            updateFloatingProgressView()
        }
    }

    private var mCurShowingFloatingCard: PlayTimelineCard? = null

    fun updateFloatingCardView(playTimelineCard: PlayTimelineCard?, forceUpdate: Boolean = false) {
        if (mFloatingProgressView?.visibility == View.VISIBLE) {
            val bizArbiter = playContainer.getBizArbiters()
            if (bizArbiter?.getBizResource(SeekBarFloating.PODCAST)?.provideView() != null) return

            if (playTimelineCard == null) {
                if (mFloatingCardContainer?.visibility == View.VISIBLE) {
                    mFloatingCardContainer?.visibility = View.GONE
                    mCurShowingFloatingCard = null
//                    playContainer.updateCoverComponents()
                }
            } else if (mBackgroundColor != 0) {
                if (mFloatingCardContainer?.visibility != View.VISIBLE) {
                    mFloatingCardContainer?.visibility = View.VISIBLE
//                    playContainer.updateCoverComponents()
                }
//                playContainer.updateCoverComponents()
                if (mCurShowingFloatingCard != playTimelineCard || forceUpdate) {
                    mCurShowingFloatingCard = playTimelineCard
                    // 互动卡片-气泡  控件曝光
                    if (!forceUpdate) {
                        val curSound = PlayTools.getCurTrack(mContext)
                        val trackId = curSound?.dataId ?: curTrackId
                        val albumId = curSound?.album?.albumId ?: curAlbumId
                        XMTraceApi.Trace().setMetaId(53569).setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                            .put("currPage", "newPlay").put("exploreType", "newPlay") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                            .put(
                                "cardType", PlayTimelineUtil.getCardTag(playTimelineCard)
                            ) // 投票｜讨论｜测评｜人设等
                            .put("themeId", "${PlayTimelineUtil.getThemeId(playTimelineCard)}")
                            .put(XmRequestIdManager.CONT_ID, "$trackId")
                            .put(XmRequestIdManager.CONT_TYPE, "newPlayTimeCard")
                            .put(
                                XmRequestIdManager.XM_REQUEST_ID,
                                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                            )
                            .put("currAlbumId", "$albumId").put("currTrackId", "$trackId").createTrace()
                    }
                    val cardPictureUrl = playTimelineCard.getImageUrl()
                    if (cardPictureUrl.isNullOrEmpty()) {
                        mLlFloatingCardTitle?.visibility = View.VISIBLE
                        mIvFloatingCardPicture?.visibility = View.GONE
                        mIvFloatingCardTag?.setImageResource(
                            if (playTimelineCard.bizType == CARD_TYPE_TALK) R.drawable.main_ic_floating_card_tag_talk
                            else if (playTimelineCard.isPKVOte()) R.drawable.main_ic_floating_card_tag_pk
                            else R.drawable.main_ic_floating_card_tag_vote
                        )
                        mTvFloatingCardTitle?.text = playTimelineCard.title
                    } else {
                        mIvFloatingCardPicture?.visibility = View.VISIBLE
                        mLlFloatingCardTitle?.visibility = View.GONE
                        ImageManager.from(context).displayImage(mIvFloatingCardPicture, cardPictureUrl,
                            com.ximalaya.ting.android.host.R.drawable.host_bg_line_color2_dark_fill1_corner_4)
                    }
                }
            }
        }
    }

    fun hideFloatingProgress() {
        if (mFloatingProgressView?.visibility != View.VISIBLE) {
            return
        }

        audioContainerViewGroup?.also { parent ->
            TransitionManager.beginDelayedTransition(parent, Fade().apply {
                mFloatingProgressView?.also { addTarget(it) }
                mVFloatingProgressMask?.also { addTarget(it) }
                mVFloatingProgressMaskGradient?.also { addTarget(it) }
                bizPositionFrame?.also { addTarget(it) }
            })
        }

        showFunctionBar()
        mFloatingProgressView?.visibility = View.INVISIBLE
        mVFloatingProgressMask?.visibility = View.INVISIBLE
        mVFloatingProgressMaskGradient?.visibility = View.INVISIBLE
        bizPositionFrame?.visibility = View.GONE
        bizPositionFrame?.removeAllViews()
        bizPositionFrame?.anyTouch = null
    }

    private val mITimelineFloatingViewListener = object : ITimelineFloatingViewListener {
        override fun isVisible(): Boolean {
            return mFloatingCardContainer?.isShown == true
        }
    }

    fun doPlayListAnimator(doAnimator: Boolean) {
        controlBar?.doPlayListAnimator(doAnimator)
    }

    override fun onMutexCoverChanged(currentCoverComponentEnum: YCoverComponentsEnum?) {

    }

    private fun showHideFullBar(show: Boolean,trace:Boolean = true) {
        if (show) {
            if (controlBar.seekBarGroup?.visibility == View.GONE) {
                controlBar.isDocScrolledFull = false
                showSeekbarPart()
                if (trace) {
                    traceReadMode(false)
                }
            }
        } else {
            if (controlBar.seekBarGroup?.visibility == View.VISIBLE) {
                controlBar.isDocScrolledFull = true
                hideSeekbarPart()
                dismissTips()
                if (trace) {
                    traceReadMode(true)
                }
            }
        }
    }


    override fun onVideoStart(videoSourceUrl: String?) {
        controlBar?.onPlayStart()
    }

    override fun onVideoPause(videoSourceUrl: String?, playedTime: Long, duration: Long) {
        controlBar?.onPlayPause()
    }

    override fun onVideoStop(videoSourceUrl: String?, playedTime: Long, duration: Long) {
        controlBar?.onPlayStop()
    }

    override fun onVideoProgress(videoSourceUrl: String?, curPosition: Long, duration: Long) {
        controlBar?.onPlayProgress(curPosition.toInt(), duration.toInt())
    }

    override fun setVideoPlayView(view: IMediaPlayerControl?) {

    }

    fun updateNextAndPreBtnStatus() {
        if (::controlBar.isInitialized) controlBar.updateNextAndPreBtnStatus()
    }

    private fun traceReadMode(isRead:Boolean){
        // 新声音播放页-文稿模式切换  点击事件
        val curTrack = PlayTools.getCurTrack(mContext)
        val trackId = curTrack?.dataId ?:0
        val albumId = curTrack?.album?.albumId?:0
        XMTraceApi.Trace()
            .click(54369) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("currTrackId", trackId.toString())
            .put("currAlbumId", albumId.toString())
            .put("mode", if (isRead) "read" else "normal") // 记录切换后的模式，normal 表示普通模式，read 表示阅读模式
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE))
            .createTrace()
    }

    fun getPlayPrevBtn(): View? {
        return controlBar?.mIvPlayPrevBtn
    }

    fun getPlayNextBtn(): View? {
        return controlBar?.mIvPlayNextBtn
    }

    fun getBarPlayBtn(): ViewGroup? {
        return controlBar?.mVgPlayBtn
    }

    fun getPlayListBtn(): View? {
        return controlBar?.mIvPlayListBtn
    }

    fun getSeekBar(): View? {
        return controlBar?.seekBarGroup
    }

}