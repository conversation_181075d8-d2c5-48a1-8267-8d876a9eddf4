package com.ximalaya.ting.android.main.playpage.playy.skin

import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.ximalaya.ting.android.framework.download.DownloadLiteManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.manager.PlayPageSkinDownloadManager

class PlaypageSkinDebugFragment: BaseFragment2() {

    override fun getPageLogicName() = "SkinDebugFragment"

    private var skinStatusTextView: TextView? = null

    override fun initUi(savedInstanceState: Bundle?) {
        skinStatusTextView = findViewById(R.id.main_skin_download_status)

        val skin1Btn = findViewById<View>(R.id.main_playpage_skin_1)
        skin1Btn.setOnClickListener {
            setSkin(
                "Skin1",
                "https://aod.cos.tx.xmcdn.com/storages/693a-audiofreehighqps/0A/60/GAqhp50MU_i9ABAAAAPnCtur.json" // Replace with actual URL
            )
        }
        val skin2Btn = findViewById<View>(R.id.main_playpage_skin_2)
        skin2Btn.setOnClickListener {
            setSkin(
                "Skin2",
                "https://aod.cos.tx.xmcdn.com/storages/ae56-audiofreehighqps/CB/28/GAqh_aQMU8-WABAAAAPm8auJ.json" // Replace with actual URL
            )
        }

        val skin3Btn = findViewById<View>(R.id.main_playpage_skin_3)
        skin3Btn.setOnClickListener {
            setSkin(
                "Skin3",
                "https://aod.cos.tx.xmcdn.com/storages/e640-audiofreehighqps/CF/CF/GAqh9sAMVO2HAAMFGgPneqGC.json" // Replace with actual URL
            )
        }
    }

    private fun setSkin(name: String, url: String) {
        PlayPageSkinDownloadManager.downloadAndSetPlayPageSkin(
            name,
            url,
            object : DownloadLiteManager.DownloadCallback {
                override fun onError(url: String?) {
                    skinStatusTextView?.text = "Downloading error: $name"
                }

                override fun onProgressUpdate(url: String?, percent: Int) {
                    skinStatusTextView?.text = "Downloading $name: $percent%"
                }

                override fun onSuccess(url: String?) {
                    skinStatusTextView?.text = "Downloading success: $name"
                }
            }
        )
    }

    override fun loadData() {
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_playpage_skin_debug
    }
}