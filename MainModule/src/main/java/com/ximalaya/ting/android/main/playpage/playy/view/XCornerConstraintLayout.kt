package com.ximalaya.ting.android.main.playpage.playy.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.playpage.manager.YDomainColorUtil
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import kotlin.math.sqrt


/**
 * Created by <PERSON><PERSON> on 2022/11/15.
 */
class XCornerConstraintLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val TAG = "XCorner"

    private var themeColor = 0
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        setStyle(android.graphics.Paint.Style.FILL)
    }
    private val paint2 = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        setStyle(android.graphics.Paint.Style.FILL)
        setXfermode(PorterDuffXfermode(PorterDuff.Mode.DST_IN));
    }

    var linearGradient: LinearGradient? = null
    init {
        setWillNotDraw(false)
    }

    private val fixHeight = 685.dp
    private val maskAlpha = 0.6f
    private var cWidth = 0


    private fun generateDrawable(cwidth: Int, cheigth: Int, color: Int) {
        val radius = sqrt((cheigth * cheigth + cwidth * cwidth).toDouble()).toFloat()
        val radiusTop = sqrt((fixHeight * fixHeight + width * width).toDouble()).toFloat()
        val fixColor = YDomainColorUtil.fixMaskL(color)
        val gradient = LinearGradient(
            0f, 0f, cwidth.toFloat(), cheigth.toFloat(),
            intArrayOf(
                fixColor,
                ColorUtil.changeColorAlpha01(fixColor, maskAlpha),
                Color.TRANSPARENT,
                Color.TRANSPARENT
            ),
            floatArrayOf(
                0f,
                radiusTop / 2 /radius,
                radiusTop/radius,
                1f,
            ),
            Shader.TileMode.CLAMP
        )
        paint.setShader(gradient)

        val centerY = (height - 190.dp).toFloat() / height
        linearGradient = LinearGradient(
            0f,
            0f,
            0f,
            height.toFloat(),
            intArrayOf(
                Color.BLACK,
                ColorUtil.changeColorAlpha01(Color.BLACK, maskAlpha),
                Color.TRANSPARENT
            ),
            floatArrayOf(0f, centerY, 1f),  // color positions
            Shader.TileMode.CLAMP // tiling method
        ).apply {
            paint2.setShader(this)
        }


        invalidate()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        computeWidth()

        if (themeColor != 0 && paint.shader == null) {
            generateDrawable(cWidth, height, themeColor)
        }
    }

    private fun computeWidth() {
        val bHeight = height - fixHeight
        if (bHeight > 0) {
            val rWidth = (bHeight / fixHeight.toFloat()) * width
            cWidth = (rWidth + width).toInt()
        } else {
            cWidth = width
        }
    }

    fun setThemeColor(@ColorInt color: Int) {
        if (themeColor != color) {
            themeColor = color
            if (cWidth > 0) {
                generateDrawable(cWidth, height, themeColor)
            }
        }
    }

    fun setDarkMode(dark: Boolean) {
//        if (dark != darkMode) {
//            darkMode = dark
//            invalidate()
//        }
    }



    override fun setBackgroundColor(color: Int) {
    }

    override fun draw(canvas: Canvas) {
        if (themeColor != 0 && cWidth > 0 && !PSkinManager.isEnabled) {
            val saveLayer = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null, Canvas.ALL_SAVE_FLAG)
            canvas.drawRect(0f, 0f, cWidth.toFloat(), height.toFloat(), paint);
            canvas.drawRect(0f, 0f, cWidth.toFloat(), height.toFloat(), paint2);
            canvas.restoreToCount(saveLayer)
        }
        super.draw(canvas)
    }
}