package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.marginStart
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.framework.util.RpAdaptUtil.FOLD_SCREEN_WIDTH
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.NewShowNotesManager
import com.ximalaya.ting.android.host.manager.SkinManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.DomainColorUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.host.view.CornerRelativeLayout
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getRankOffset
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataPlayStatusStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataWithLifecircleStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.AlbumRank
import com.ximalaya.ting.android.main.model.rec.RankExtraData
import com.ximalaya.ting.android.main.model.rec.RankSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendRankListItem
import com.ximalaya.ting.android.main.playpage.playx.utils.getCurrentView
import com.ximalaya.ting.android.main.rankModule.AggregateRankUtil
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeMoreColorUtil
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.main.view.recommend.WrapContentNonSwipeableViewPager
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by bin.hou on 2023/3/13.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17316357791
 */
class RecommendRankListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendRankListAdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendRankListAdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataWithLifecircleStaggered<RecommendRankListAdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataPlayStatusStaggered<RecommendRankListAdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendRankListAdapterProviderStaggered.RankListCardViewHolder, RecommendItemNew> {
    private var mPreAlbumRank: AlbumRank? = null
    private var mModulePosition = -1
    private var mOldState = RecyclerView.SCROLL_STATE_IDLE
    private var mRankListCardViewHolder: RankListCardViewHolder? = null

    init {
        if (HomeRecommendPageLoadingOptimizationManager.isNeedForcePreloadRecommendPage()) {
            HomeRecommendPageLoadingOptimizationManager.addRecommendFragmentVisibleListeners(object :
                HomeRecommendPageLoadingOptimizationManager.IRecommendFragmentVisibleListener {
                override fun onRecommendFragmentVisible() {

                    mRankListCardViewHolder?.rankListVp?.disableFillNeighbourTab(false)
                    Log.d(
                        "PerformanceMonitor",
                        "onRecommendFragmentVisible, disableFillNeighbourTab false"
                    )
                    HomeRecommendPageLoadingOptimizationManager.removeRecommendFragmentVisibleListeners(
                        this
                    )
                }
            })
        }
    }

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(R.layout.main_item_recommend_rank_list, parent, false)
    }

    override fun onResume() {
        if (HomeRecommendPageLoadingOptimizationManager.isNeedForcePreloadRecommendPage()) {
            if (!HomeRecommendPageLoadingOptimizationManager.mIsHasAdShowed) {
                mRankListCardViewHolder?.rankListVp?.disableFillNeighbourTab(false)
                Log.d("PerformanceMonitor", "onResume, disableFillNeighbourTab false")
            }
        }
        mRankListCardViewHolder?.rankListVp?.adapter?.notifyDataSetChanged()
    }

    override fun onPause() {
    }

    override fun onPlayStart() {
        mRankListCardViewHolder?.rankListVp?.adapter?.notifyDataSetChanged()
    }

    override fun onPlayPause() {
        mRankListCardViewHolder?.rankListVp?.adapter?.notifyDataSetChanged()
    }

    override fun onSoundPlayComplete() {
        mRankListCardViewHolder?.rankListVp?.adapter?.notifyDataSetChanged()
    }

    interface IOnMyItemLongClickListener {
        fun onItemLongClick(removedAlbumRank: AlbumRank?)
    }

    interface IOnListTipShowListener {
        fun onItemShow(shouldShow: Boolean)
    }

    class RankListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {

        val rootView = convertView
        var contentView: View = convertView.findViewById(R.id.content_view)
        val rankListVp: WrapContentNonSwipeableViewPager =
            convertView.findViewById(R.id.main_vp_rank_list)
        val scrollNextLayout: View = convertView.findViewById(R.id.main_rank_scroll_next_layout)
        val rankListRcv: RecyclerView = convertView.findViewById(R.id.main_rcv_rank_tab)
        var tabShadowView: View = convertView.findViewById(R.id.main_view_tab_shadow)
        val tvMore: TextView = convertView.findViewById(R.id.main_iv_more)

        var recommendItemNew: RecommendItemNew? = null
        var mLastScreenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
        var isNeedForceUpdate: Boolean = false
        var isDraging: Boolean = false
        var lastSelectedPosition = 0

        init {
            resetSize()
        }

        fun resetSize() {
            val context = BaseApplication.getMyApplicationContext()
            val params = contentView.layoutParams
            params.width =
                if (BaseUtil.isFoldScreen(context) && isFoldScreenWithExpand()) getFoldItemWidth() else LayoutParams.MATCH_PARENT
        }
    }

    override fun onConfigurationChanged(holder: RankListCardViewHolder?) {
        holder ?: return
        holder.isNeedForceUpdate = true
    }

    override fun createViewHolder(convertView: View?): RankListCardViewHolder? {
        PerformanceMonitor.traceBegin("Rank_createViewHolder")
        if (convertView == null) {
            return null
        }
        val rankListCardViewHolder = RankListCardViewHolder(convertView)
        PerformanceMonitor.traceEnd("Rank_createViewHolder", 5)
        return rankListCardViewHolder;
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun bindViewHolder(
        holder: RankListCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        mRankListCardViewHolder = holder
        val recommendRankListItem = recommendItemNew.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }
        if (position == 0 && SkinManager.isNeedShowAtmosphereImage()) {
            holder.tabShadowView.visibility = View.GONE
        } else {
            holder.tabShadowView.visibility = View.VISIBLE
        }
        mModulePosition = position
        if (holder.recommendItemNew == recommendItemNew
            && (holder.recommendItemNew?.item as? RecommendRankListItem)?.innerListSelectedIndex
            == recommendRankListItem.innerListSelectedIndex
            && !holder.isNeedForceUpdate
            && !needForceUpdate
        ) {
            return
        }
        PerformanceMonitor.traceBegin("Rank_bindViewHolder_" + recommendRankListItem.title)
        holder.isNeedForceUpdate = false
        holder.recommendItemNew = recommendItemNew
        needForceUpdate = false

        // 听单专辑列表数据适配器
        SPAN_COUNT = if (RecommendFragmentTypeManager.isNewSceneCard() && position == 1) {
            3
        } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable()
            && position == 1
        ) {
            2
        } else {
            3
        }

        RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
            this.javaClass.simpleName,
            SPAN_COUNT,
            position,
            recommendItemNew
        )
        val cardTabAdapter = TingTabAdapter(rankingList, recommendItemNew, holder.rankListVp)
        cardTabAdapter.onTabSelectListener = object : OnTabSelectListener {

            private var isFromClick = false

            override fun onSelect(innerPosition: Int) {
                if (recommendRankListItem.innerListSelectedIndex > rankingList.size - 1 || recommendRankListItem.innerListSelectedIndex < 0) {
                    return
                }
                val preAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
                mPreAlbumRank = preAlbumRank
                recommendRankListItem.innerListSelectedIndex = innerPosition
                val curAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
                holder.rankListVp.setCurrentItem(innerPosition, true)
                val trace1 = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (mModulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                    .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
                SpmTraceUtil.addSpmTraceInfo(
                    trace1,
                    (recommendItemNew.item as? RecommendRankListItem)?.ubtV2,
                    (mModulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace1,
                    (recommendItemNew.item as? RecommendRankListItem)?.ubtV2
                )
                trace1.createTrace()

                val action = if (isFromClick) {
                    "click"
                } else {
                    "slipe"
                }
                isFromClick = false

                // 新首页-榜单-tab切换  点击事件
                val trace = XMTraceApi.Trace()
                    .click(61033) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (mModulePosition + 1).toString())
                    .put("tabName", preAlbumRank.title ?: "") // 根据实际文案，点击前 tabName
                    .put("xmRequestId", recommendItemNew.xmRequestId)
                    .put("Item", curAlbumRank.title ?: "") // 点击后的榜单名称
                    .put("rankId", curAlbumRank.id?.toString() ?: "") // 点击后的榜单id
                    .put("positionNew", (innerPosition + 1).toString()) // 双端统一从1开始，从左到右
                    .put("tabPosition", (innerPosition + 1).toString()) // 双端统一从1开始，计算tab相对位置
                    .put("action", action) // 客户端传。区分点击还是滑动切换

                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    recommendRankListItem.ubtV2,
                    (mModulePosition + 1).toString(),
                    curAlbumRank.title,
                    (innerPosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank.ubtV2)
                trace.createTrace()

                scrollToPositionTab(holder.rankListRcv, innerPosition, true)
            }

            override fun clickTab(innerPosition: Int) {
                isFromClick = true
            }
        }
        scrollToPositionTab(holder.rankListRcv, recommendRankListItem.innerListSelectedIndex, false)
        holder.rankListRcv.adapter = cardTabAdapter
        holder.rankListRcv.layoutManager = LinearLayoutManager(
            holder.rankListRcv.context, LinearLayoutManager.HORIZONTAL, false
        )
        holder.rankListRcv.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnTabItemShow(recommendItemNew, position, holder)
                }
            }
        })
        val rankListViewPagerAdapter =
            RankViewPagerAdapter(
                fragment,
                this,
                rankingList,
                recommendItemNew,
                mModulePosition,
                holder.rankListVp,
                object : IOnMyItemLongClickListener {
                    override fun onItemLongClick(removedAlbumRank: AlbumRank?) {
                    }
                }, object : IOnListTipShowListener {
                    override fun onItemShow(shouldShow: Boolean) {
                        if (!holder.isDraging) {
                            ViewStatusUtil.setVisible(
                                if (shouldShow) View.VISIBLE else View.GONE,
                                holder.scrollNextLayout
                            )
                        }
                    }
                }
            ) { moreClickInner(rankingList, recommendRankListItem, recommendItemNew, "slide") }

        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent() && PerformanceMonitor.isOptRankListViewPagerOpen()) {
            holder.rankListVp.disableFillNeighbourTab(true)
            Log.d("PerformanceMonitor", "bindViewHolde, disableFillNeighbourTab true")
        }

        // 听单专辑列表
        holder.rankListVp.adapter = rankListViewPagerAdapter

        holder.rankListVp.setOnPageChangeListener(
            ViewPageChangeListener(
                recommendRankListItem,
                rankingList,
                this, holder
            )
        )
        val indexOfPage = holder.rankListVp.currentItem
        if (indexOfPage != recommendRankListItem.innerListSelectedIndex) {
            holder.rankListVp.setCurrentItem(recommendRankListItem.innerListSelectedIndex, false)
        }
        holder.tvMore.setOnClickListener(View.OnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@OnClickListener
            }
            moreClickInner(rankingList, recommendRankListItem, recommendItemNew, "click")
        })
        HomeMoreColorUtil.filterColor(holder.tvMore)

        holder.scrollNextLayout.setOnClickListener {
            val curIndex = holder.rankListVp.currentItem
            if (curIndex < 0) {
                return@setOnClickListener
            }
            val maxIndex = rankingList.size - 1
            if (curIndex < maxIndex) {
                holder.rankListVp.setCurrentItem(curIndex + 1)
            } else {
                holder.tvMore.performClick()
            }
        }

        PerformanceMonitor.traceEnd("Rank_bindViewHolder_" + recommendRankListItem.title, 6)
    }

    private fun moreClickInner(
        rankingList: List<AlbumRank>, recommendRankListItem: RecommendRankListItem,
        recommendItemNew: RecommendItemNew, action: String = "click"
    ) {
        val curAlbumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
        if (curAlbumRank.landingPage.isNullOrEmpty()) {
            RecommendFragmentPageErrorManager.uploadDataError("排行榜iting为空", null)
            return
        }
        val trace1 = XMTraceApi.Trace()
            .click(62176) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("modulePosition", (mModulePosition + 1).toString()) // 客户端传
            .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
            .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
        SpmTraceUtil.addSpmTraceInfo(
            trace1,
            (recommendItemNew.item as? RecommendRankListItem)?.ubtV2,
            (mModulePosition + 1).toString()
        )
        RecommendNewUbtV2Manager.addUbtV2Data(
            trace1,
            (recommendItemNew.item as? RecommendRankListItem)?.ubtV2
        )
        trace1.createTrace()
        // 新首页-榜单-查看完整榜单  点击事件
        val trace = XMTraceApi.Trace()
            .click(61036) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("tabName", curAlbumRank.title ?: "") // 根据实际文案
            .put("xmRequestId", recommendItemNew.xmRequestId)
            .put("rankId", curAlbumRank.id?.toString() ?: "")
            .put("modulePosition", (mModulePosition + 1).toString())
            .put("action", action)
        SpmTraceUtil.addSpmTraceInfo(
            trace,
            recommendRankListItem.ubtV2,
            (mModulePosition + 1).toString(),
            tab1Title = curAlbumRank.title,
            tab1Position = (recommendRankListItem.innerListSelectedIndex + 1).toString(),
            tab2Title = "更多",
            tab2Position = "d01"
        )
        RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
        RecommendNewUbtV2Manager.addUbtV2Data(trace, curAlbumRank.ubtV2)
        trace.createTrace()

        AggregateRankUtil.clearRankMapData()
        if (rankingList.isNotEmpty()) {
            rankingList.forEach { albumRank ->
                if (albumRank.extraInfo != null && albumRank.extraInfo!!["rankType"] == "recRank") {
                    albumRank.subElements?.forEach { subElement ->
                        val businessExtraInfo =
                            if (subElement.extraInfo?.subRefInfo?.ad == true) subElement.extraInfo.subRefInfo!!.businessExtraInfo else null
                        val extraData = RankExtraData(subElement.ubt, businessExtraInfo)
                        AggregateRankUtil.addRankMapData(subElement.refId ?: 0L, extraData)
                    }
                    return@forEach
                }
            }
        }
        ToolUtil.clickUrlAction(fragment, curAlbumRank.landingPage, null)
    }

    private class ViewPageChangeListener(
        var recommendRankListItem: RecommendRankListItem?,
        var rankingList: List<AlbumRank>?,
        var recommendRankListAdapterProviderStaggered: RecommendRankListAdapterProviderStaggered,
        var holder: RankListCardViewHolder?
    ) : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
        }

        override fun onPageSelected(position: Int) {
            if (recommendRankListItem == null || rankingList == null || holder == null) {
                return
            }
            recommendRankListItem!!.innerListSelectedIndex = position
            holder!!.rankListRcv.adapter?.notifyDataSetChanged()
            (holder!!.rankListRcv.adapter as TingTabAdapter?)?.onTabSelectListener?.onSelect(
                position
            )
            if (holder!!.lastSelectedPosition > position) {
                ViewStatusUtil.setVisible(View.GONE, holder?.scrollNextLayout)
            } else {
                showGuideTipInner(position)
            }
            holder!!.lastSelectedPosition = position
            HandlerManager.postOnUIThreadDelay({
                recommendRankListAdapterProviderStaggered.traceOnAlbumItemShow(
                    holder?.recommendItemNew,
                    holder
                )
            }, 200)
        }

        private var mCurrentPosition = -1

        override fun onPageScrollStateChanged(state: Int) {
            if (state == ViewPager.SCROLL_STATE_IDLE) {
                holder?.isDraging = false
                if (holder != null && mCurrentPosition == holder!!.rankListVp.currentItem) {
                    showGuideTipInner(holder!!.rankListVp.currentItem)
                }
            } else if (state == ViewPager.SCROLL_STATE_DRAGGING) {
                if (holder?.isDraging == false) {
                    mCurrentPosition = holder!!.rankListVp.currentItem
                }
                holder?.isDraging = true
                ViewStatusUtil.setVisible(View.GONE, holder?.scrollNextLayout)
            }
        }

        private fun showGuideTipInner(position: Int) {
            val albumRank =
                (holder!!.rankListVp.adapter as? RankViewPagerAdapter?)?.rankingList?.getOrNull(
                    position
                )
            val isLastTab =
                (holder?.recommendItemNew?.item as? RecommendRankListItem)?.list?.size == (position + 1)
            if (albumRank != null) {
                val scrollPage = albumRank.scrollPosition / SPAN_COUNT + 1
                val totalPage = (albumRank.subElements?.size ?: 0) / SPAN_COUNT
                val mTipShow = (scrollPage == totalPage) && !isLastTab
                ViewStatusUtil.setVisible(
                    if (mTipShow) View.VISIBLE else View.GONE,
                    holder?.scrollNextLayout
                )
            }
        }
    }

    // 滚动到指定tab并且居中
    private fun scrollToPositionTab(recyclerView: RecyclerView, position: Int, smooth: Boolean) {
        val itemView = recyclerView.findViewHolderForLayoutPosition(position)?.itemView ?: return
        val itemWidth = itemView.width
        val offset =
            (recyclerView.width - itemWidth) / 2 - itemView.marginStart - 20.dp // 7 + 6 + 14/2为斜杠到文字的距离
        if (smooth) {
            recyclerView.smoothScrollToPositionWithOffset(position, offset)
        } else {
            val manager = recyclerView.layoutManager as? LinearLayoutManager ?: return
            manager.scrollToPositionWithOffset(position, offset)
        }
    }

    private fun traceOnAlbumItemShow(
        data: RecommendItemNew?,
        holder: RankListCardViewHolder?
    ) {
        if (holder == null || data == null) {
            return
        }
        val recommendRankListItem = data.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }
        val albumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
        val subElements = albumRank.subElements
        if (subElements.isNullOrEmpty()) {
            return
        }

        holder.rankListVp.getCurrentView()?.let {
            val albumListRv: RecyclerView = it.findViewById(R.id.main_rcv_ting_album_list)
            val childSize = albumListRv.childCount
            for (i in 0 until childSize) {
                val itemView = albumListRv.getChildAt(i) ?: continue
                if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                    val rankSubElement =
                        itemView.getTag(R.id.main_id_item_data) as? RankSubElement ?: continue
                    val index = itemView.getTag(R.id.main_id_data_index) as? Int ?: continue
                    val advertis = itemView.getTag(R.id.main_id_data_ad_info)

                    // 新首页-榜单-专辑卡片  曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(61032)
                        .setServiceId("slipPage") // 用户点击时上报
                        .put("currPage", "newHomePage")
                        .put("tabName", albumRank.title ?: "") // 根据实际文案
                        .put(
                            "exploreArea",
                            ViewStatusUtil.getViewVisibleAreaRealPercent(itemView).toString()
                        ) // 可见区域占屏幕的比例
                        .put("albumId", rankSubElement.refId?.toString() ?: "")
                        .put("contentType", "album")
                        .put("contentId", rankSubElement.refId?.toString() ?: "")
                        .put(
                            "positionNew",
                            (index + 1).toString()
                        ) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                        .put("xmRequestId", data.xmRequestId)
                        .put("rec_track", rankSubElement.ubt?.recTrack ?: "")
                        .put("rec_src", rankSubElement.ubt?.recSrc ?: "")
                        .put("ubtTraceId", rankSubElement.ubt?.traceId ?: "")
                        .put("rankId", albumRank.id?.toString() ?: "")
                        .put("modulePosition", (mModulePosition + 1).toString())
                        .put(
                            "socialTagId",
                            rankSubElement.getSocialTagId().toString()
                        ) // 传对应的社会化标签id，若无则不用传
                        .put(
                            "tabPosition",
                            ((holder.recommendItemNew!!.item as RecommendRankListItem).innerListSelectedIndex + 1).toString()
                        ) // 传对应的社会化标签名称，若无则不用传
                        .put("isAd", if (advertis != null) "true" else "false")
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendRankListItem.ubtV2,
                        (mModulePosition + 1).toString(),
                        albumRank.title,
                        ((holder.recommendItemNew!!.item as RecommendRankListItem).innerListSelectedIndex + 1).toString(),
                        contentTitle = rankSubElement.title,
                        contentPosition = (index + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank.ubtV2)
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                    if (ConstantsOpenSdk.isDebug && !data.isLocalCache && TextUtils.isEmpty(data.xmRequestId)) {
                        CustomToast.showToast("排行榜xmRequestId异常，请保存现场联系首页开发")
                    }

                    // 广告曝光上报
                    if (advertis != null && advertis is Advertis && !advertis.isShowedToRecorded && !data.isLocalCache) {
                        AdManager.adRecord(
                            BaseApplication.getMyApplicationContext(), advertis,
                            AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST
                            )
                                .positionNew(index + 1)
                                .modulePosition(mModulePosition + 1)
                                .build()
                        )
                        advertis.isShowedToRecorded = true
                    }
                    if (advertis != null && advertis is Advertis && !advertis.isRecordedSubPercent && !data.isLocalCache &&
                        ViewStatusUtil.getViewVisibleAreaRealPercent(itemView) >= AD_REPORT_SUB_PERCENT
                    ) {
                        // 做曝光50%的上报
                        AdManager.reportSubPercentShow(advertis, data.xmRequestId)
                        advertis.isRecordedSubPercent = true
                    }

                    HomeRealTimeTraceUtils.traceItemShow(data, recommendRankListItem, albumRank, rankSubElement, itemView, index)
                }
            }
        }
    }

    private fun traceOnTabItemShow(
        recommendItemNew: RecommendItemNew,
        modulePosition: Int,
        holder: RankListCardViewHolder
    ) {
        holder.rankListRcv.let {
            val childSize = holder.rankListRcv.childCount
            RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
            for (i in 0 until childSize) {
                val itemView: View = holder.rankListRcv.getChildAt(i) ?: continue
                if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                    val albumRank =
                        itemView.getTag(R.id.main_id_item_data) as? AlbumRank ?: continue
                    val index = itemView.getTag(R.id.main_id_data_index) as? Int ?: continue
                    // 新首页-榜单  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62251)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage") // 客户端传
                        .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传，去重用
                        .put("contentType", "albumRank") // 客户端传，去重用
                        .put("contentId", albumRank.id?.toString() ?: "") // 客户端传，去重用
                        .put(
                            "modulePosition",
                            (modulePosition + 1).toString()
                        ) // 客户端传，card 在流里的位置，从 1 开始计数
                        .put("tabPosition", (index + 1).toString()) // 传对应的社会化标签名称，若无则不用传
                        .put(
                            "exploreArea",
                            ViewStatusUtil.getViewVisibleAreaRealPercent(itemView).toString()
                        ) // 可见区域占屏幕的比例
                    if (recommendItemNew.isLocalCache) {
                        trace.isLocalCache
                    }
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        (recommendItemNew.item as? RecommendRankListItem)?.ubtV2,
                        (modulePosition + 1).toString(),
                        albumRank.title,
                        (index + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(
                        trace,
                        (recommendItemNew.item as? RecommendRankListItem)?.ubtV2
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank.ubtV2)
                    trace.createTrace()
                }
            }
        }
    }

    fun checkAdCunt(list: List<AlbumRank>?): Int {
        if (list.isNullOrEmpty()) {
            return 0
        }
        var adCount = 0
        for (albumRank in list) {
            val subElements = albumRank.subElements
            if (!subElements.isNullOrEmpty()) {
                for (subElement in subElements) {
                    val adInfo = subElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfo
                    if (!adInfo.isNullOrEmpty()) {
                        adCount++
                    }
                }
            }
        }
        return adCount
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: RankListCardViewHolder?
    ) {
        if (holder == null || data == null) {
            return
        }
        val recommendRankListItem = data.item
        if (recommendRankListItem !is RecommendRankListItem) {
            return
        }
        val rankingList = recommendRankListItem.list
        if (rankingList.isNullOrEmpty()) {
            return
        }
        if (recommendRankListItem.cardAdCount == -1) {
            recommendRankListItem.cardAdCount = checkAdCunt(rankingList)
        }
        val albumRank = rankingList[recommendRankListItem.innerListSelectedIndex]
        val subElements = albumRank.subElements
        if (subElements.isNullOrEmpty()) {
            return
        }
        if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
            // 新首页-首页大卡模块  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62177)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("xmRequestId", data.xmRequestId) // 客户端传
                .put("contentType", data.itemType) // 客户端传
                .put("contentId", recommendRankListItem.id.toString()) // 客户端传
                .put("card_adTopn", recommendRankListItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendRankListItem.ubtV2,
                (position + 1).toString()
            )
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendRankListItem.ubtV2)
            trace.createTrace()
        }
        traceOnAlbumItemShow(data, holder)
        traceOnTabItemShow(data, position, holder)
    }

    class TingTabAdapter(
        private val rankingList: List<AlbumRank>,
        private val recommendItemNew: RecommendItemNew?,
        private val rankListVp: WrapContentNonSwipeableViewPager,
    ) : RecyclerView.Adapter<TingTabAdapter.TabViewHolder>() {

        // 选中回掉
        var onTabSelectListener: OnTabSelectListener? = null

        override fun onCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): TabViewHolder {
            return TabViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.main_fra_recommend_rank_list_tab_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderInner(holder, position)
                }
            } else {
                onBindViewHolderInner(holder, position)
            }
        }

        fun onBindViewHolderInner(holder: TabViewHolder, position: Int) {
            val albumRank = rankingList[position]
            holder.itemView.setTag(R.id.main_id_item_data, albumRank)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            holder.tabTv.setTextIfChanged(albumRank.title)
            if (position == 0) {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
                holder.tabTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
            } else {
                holder.itemView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                holder.tabTv.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }
            holder.itemView.setOnOneClickListener {
                val item = recommendItemNew?.item as RecommendRankListItem
                if (item.innerListSelectedIndex == position) {
                    return@setOnOneClickListener
                }
                item.innerListSelectedIndex = position
                onTabSelectListener?.clickTab(position)
                rankListVp.setCurrentItem(position)
            }
            val selectTab = (recommendItemNew?.item as RecommendRankListItem).innerListSelectedIndex
            if (position == 0) {
                if (holder.vTitleSplit.layoutParams is LinearLayout.LayoutParams) {
                    (holder.vTitleSplit.layoutParams as LinearLayout.LayoutParams).rightMargin =
                        if (selectTab == 0) 10.dp else 8.dp
                }
                holder.vTitleSplit.visibility = View.INVISIBLE
            } else {
                if (holder.vTitleSplit.layoutParams is LinearLayout.LayoutParams) {
                    (holder.vTitleSplit.layoutParams as LinearLayout.LayoutParams).rightMargin =
                        10.dp
                }
                holder.vTitleSplit.visibility = View.VISIBLE
            }
            updateTabSelectStyle(holder, position, holder.view.context)
        }

        // 更新选中tab样式
        private fun updateTabSelectStyle(
            holder: TabViewHolder,
            position: Int,
            context: Context
        ) {
            val selectTab =
                (recommendItemNew?.item as RecommendRankListItem).innerListSelectedIndex
            // 是否选中
            if (selectTab == position) {
                holder.tabTv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
                holder.tabTv.setTextColor(context.resources.getColor(R.color.main_color_2c2c3c_ffffff))
                holder.tabTv.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
                holder.ivTitleLeft.visibility = View.VISIBLE
                holder.ivTitleRight.visibility = View.VISIBLE
            } else {
                holder.tabTv.setTextColor(context.resources.getColor(R.color.main_color_662c2c3c_8d8d91))
                holder.tabTv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
                holder.tabTv.typeface = Typeface.DEFAULT
                holder.ivTitleLeft.visibility = View.GONE
                holder.ivTitleRight.visibility = View.GONE
            }
        }

        override fun getItemCount(): Int {
            return rankingList.size
        }

        class TabViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
            var tabTv: TextView = view.findViewById(R.id.main_tv_ting_tab_title)
            var ivTitleLeft: ImageView = view.findViewById(R.id.main_iv_rank_title_left)
            var ivTitleRight: ImageView = view.findViewById(R.id.main_iv_rank_title_right)
            var vTitleSplit: View = view.findViewById(R.id.main_v_rank_title_line)
        }
    }

    class RankViewPagerAdapter( // tab数据
        private val fragment: BaseFragment2,
        private val recommendRankListAdapterProviderStaggered: RecommendRankListAdapterProviderStaggered,
        val rankingList: List<AlbumRank>?,
        private val recommendItemNew: RecommendItemNew?,
        var positionNew: Int,
        private var myViewPager: ViewPager?,
        private val myItemLongClickListener: IOnMyItemLongClickListener?,
        private val mTipShowListener: IOnListTipShowListener?,
        private val mJumpListener: HorizontalMoreBaseAdapter.RelaseJumpActivityListener?
    ) : PagerAdapter() {
        private var mOldState = RecyclerView.SCROLL_STATE_IDLE
        override fun getCount(): Int {
            return rankingList?.size ?: 0
        }

        override fun getItemPosition(rank: Any): Int {
            if (rankingList == null) {
                return POSITION_NONE
            }
            return if (rankingList.contains(rank)) {
                rankingList.indexOf(rank)
            } else {
                POSITION_NONE
            }
        }

        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view === `object`
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            PerformanceMonitor.traceBegin("Rank_instantiateItem_$position")
            val view = LayoutInflater.from(container.context)
                .inflate(R.layout.main_fra_recommend_rank_list_view_page_item, container, false)
            val albumRank = rankingList?.get(position)
            if (albumRank == null) {
                bindData(view, null, position)
                return view
            }
            val rankSubElements = albumRank.subElements
            if (!rankSubElements.isNullOrEmpty()) {
                bindData(view, albumRank, position)
            } else {
                val refId = albumRank.refId
                if (refId == null) {
                    bindData(view, null, position)
                } else {
                    MainCommonRequest.getRankListItem(albumRank, refId, albumRank.extraInfo,
                        object : IDataCallBack<AlbumRank> {
                            override fun onSuccess(data: AlbumRank?) {
                                if (!fragment.canUpdateUi()) {
                                    return
                                }
                                if (data == null || data.subElements.isNullOrEmpty() || rankingList.isNullOrEmpty()) {
                                    bindData(view, null, position)
                                    return
                                }
                                var find = false
                                for (rank in rankingList) {
                                    if (data.equals(rank)) {
                                        rank.subElements = data.subElements
                                        bindData(view, rank, position)
                                        find = true
                                        break
                                    }
                                }

                                if (!find) {
                                    bindData(view, null, position)
                                }
                            }

                            override fun onError(code: Int, message: String?) {
                                if (!fragment.canUpdateUi()) {
                                    return
                                }
                                bindData(view, null, position)
                            }
                        })
                }
            }
            container.addView(view)

            PerformanceMonitor.traceEnd("Rank_instantiateItem_$position", 17)
            return view
        }

        private fun bindData(view: View, albumRank: AlbumRank?, tabPosition: Int) {
            val albumListRv: RecyclerView = view.findViewById(R.id.main_rcv_ting_album_list)

            val rankSubElements = albumRank?.subElements
            val isLastTab =
                (recommendItemNew?.item as? RecommendRankListItem)?.list?.size == (tabPosition + 1)
            val cardAlbumListAdapter = TingCardAlbumItemAdapter(
                fragment,
                recommendRankListAdapterProviderStaggered,
                recommendItemNew!!.item as RecommendRankListItem,
                rankSubElements,
                positionNew,
                recommendItemNew,
                albumRank,
                myItemLongClickListener,
                SPAN_COUNT,
                myViewPager,
                isLastTab && !TextUtils.isEmpty(albumRank?.landingPage)
            )
            cardAlbumListAdapter.setRelaseJumpActivityListener {
                mJumpListener?.relaseJump()
            }
            cardAlbumListAdapter.mEnableMoreItem = cardAlbumListAdapter.enableJumpMore
            // 听单专辑列表
            albumListRv.adapter = cardAlbumListAdapter
            val gridLayoutManager =
                GridLayoutManager(view.context, SPAN_COUNT, GridLayoutManager.HORIZONTAL, false)
            gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) SPAN_COUNT else 1
                }
            }
            albumListRv.layoutManager = gridLayoutManager

            Logger.d("cf_test_ad", "排行榜绑定————————$SPAN_COUNT")
            if (albumRank == null) {
                return
            }

            if (!rankSubElements.isNullOrEmpty()) {
                val startSnapHelper = StartSnapHelper()
                startSnapHelper.attachToRecyclerView(albumListRv)
                startSnapHelper.setContainerView(albumListRv)
                val adapter = albumListRv.adapter
                HandlerManager.postOnUIThreadDelay({
                    traceOnItemShow(recommendItemNew, albumRank, albumListRv)
                    adapter?.notifyDataSetChanged()
                }, 200)
                albumListRv.addOnScrollListener(object :
                    RecyclerView.OnScrollListener() {
                    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                        super.onScrollStateChanged(recyclerView, newState)
                        if (newState == mOldState) {
                            return
                        }
                        mOldState = newState
                        if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                            var scrollPosition =
                                (albumListRv.layoutManager as GridLayoutManager).findFirstCompletelyVisibleItemPosition()
                            if (scrollPosition == -1) {
                                scrollPosition =
                                    (albumListRv.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                            }
                            if (albumRank.scrollPosition < scrollPosition) {
                                val lastCompletePosition =
                                    (albumListRv.layoutManager as GridLayoutManager).findLastCompletelyVisibleItemPosition()
                                val tipShow =
                                    (lastCompletePosition / SPAN_COUNT + 1) >= rankSubElements.size / SPAN_COUNT && !isLastTab
                                mTipShowListener?.onItemShow(tipShow)
                            }
                            albumRank.scrollPosition = scrollPosition
                            traceOnItemShow(recommendItemNew, albumRank, recyclerView)
                        } else {
                            mTipShowListener?.onItemShow(false)
                        }
                    }
                })
                albumListRv.scrollToPosition(albumRank.scrollPosition)
                if (myViewPager?.currentItem == tabPosition) {
                    mTipShowListener?.onItemShow((albumRank.scrollPosition / SPAN_COUNT + 1) >= rankSubElements.size / SPAN_COUNT && !isLastTab)
                }
            }
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            if (`object` is View) {
                try {
                    container.removeView(`object`)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        fun traceOnItemShow(
            recommendItemNew: RecommendItemNew,
            albumRank: AlbumRank,
            recyclerView: RecyclerView
        ) {
            val childSize = recyclerView.childCount
            for (i in 0 until childSize) {
                val itemView = recyclerView.getChildAt(i) ?: continue
                if (ViewStatusUtil.viewIsRealShowing(itemView)) {
                    val rankSubElement =
                        itemView.getTag(R.id.main_id_item_data) as? RankSubElement ?: continue
                    val index = itemView.getTag(R.id.main_id_data_index) as? Int ?: continue
                    val advertis = itemView.getTag(R.id.main_id_data_ad_info)
                    val recommendRankListItem = recommendItemNew.item as RecommendRankListItem
                    // 新首页-榜单-专辑卡片  曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(61032)
                        .setServiceId("slipPage") // 用户点击时上报
                        .put("currPage", "newHomePage")
                        .put(
                            "exploreArea",
                            ViewStatusUtil.getViewVisibleAreaRealPercent(itemView).toString()
                        ) // 可见区域占屏幕的比例
                        .put("tabName", albumRank.title ?: "") // 根据实际文案
                        .put("albumId", rankSubElement.refId?.toString() ?: "")
                        .put("contentType", "album")
                        .put("contentId", rankSubElement.refId?.toString() ?: "")
                        .put(
                            "positionNew",
                            (index + 1).toString()
                        ) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                        .put("xmRequestId", recommendItemNew.xmRequestId)
                        .put("rec_track", rankSubElement.ubt?.recTrack ?: "")
                        .put("rec_src", rankSubElement.ubt?.recSrc ?: "")
                        .put("ubtTraceId", rankSubElement.ubt?.traceId ?: "")
                        .put("modulePosition", (positionNew + 1).toString())
                        .put("rankId", albumRank.id?.toString() ?: "")
                        .put(
                            "socialTagId",
                            rankSubElement.getSocialTagId().toString()
                        ) // 传对应的社会化标签id，若无则不用传
                        .put(
                            "tabPosition",
                            (recommendRankListItem.innerListSelectedIndex + 1).toString()
                        ) // 传对应的社会化标签名称，若无则不用传
                        .put("isAd", if (advertis != null) "true" else "false")
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        (recommendItemNew.item as RecommendRankListItem).ubtV2,
                        (positionNew + 1).toString(),
                        albumRank.title,
                        (recommendRankListItem.innerListSelectedIndex + 1).toString(),
                        contentTitle = rankSubElement.title,
                        contentPosition = (index + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(
                        trace,
                        (recommendItemNew.item as RecommendRankListItem).ubtV2
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank.ubtV2)
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement.ubtV2)
                    if (recommendItemNew.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                    // 广告曝光上报
                    if (advertis != null && advertis is Advertis && !advertis.isShowedToRecorded && !recommendItemNew.isLocalCache) {
                        AdManager.adRecord(
                            BaseApplication.getMyApplicationContext(), advertis,
                            AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST
                            )
                                .positionNew(index + 1)
                                .modulePosition(positionNew + 1)
                                .build()
                        )
                        advertis.isShowedToRecorded = true
                    }
                    if (advertis != null && advertis is Advertis && !advertis.isRecordedSubPercent && !recommendItemNew.isLocalCache
                        && ViewStatusUtil.getViewVisibleAreaRealPercent(itemView) >= AD_REPORT_SUB_PERCENT
                    ) {
                        // 做曝光50%的上报
                        AdManager.reportSubPercentShow(advertis, recommendItemNew.xmRequestId)
                        advertis.isRecordedSubPercent = true
                    }

                    HomeRealTimeTraceUtils.traceItemShow(recommendItemNew, recommendRankListItem, albumRank, rankSubElement, itemView, index)
                }
            }
        }
    }

    class TingCardAlbumItemAdapter(
        private val fragment: BaseFragment2,
        private val recommendRankListAdapterProviderStaggered: RecommendRankListAdapterProviderStaggered,
        private val recommendRankingItem: RecommendRankListItem,
        // 专辑列表
        list: List<RankSubElement>?,
        var positionNew: Int,
        var recommendItemNew: RecommendItemNew?,
        var albumRank: AlbumRank?,
        private val myItemLongClickListener: IOnMyItemLongClickListener?,
        var spanCount: Int,
        myViewPager: ViewPager?,
        var enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 专辑列表
        private val rankSubElementList = mutableListOf<RankSubElement>()
        private var mTypeface: Typeface? = null

        init {
            if (myViewPager != null) {
                mInnerViewPager = myViewPager
            }
            list?.let {
                rankSubElementList.addAll(list)
            }
            mTypeface =
                Typeface.createFromAsset(
                    fragment.resources.assets,
                    "fonts/XmlyNumberV1.0-Regular.otf"
                )
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            return TingAlbumViewHolder(
                ViewPool.getInstance().getView(
                    HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                    R.layout.main_fra_recommend_rank_list_album_item,
                    parent,
                    false,
                    "RankList"
                )
            )
        }

        private fun onBindTrackViewHolder(
            holder: TingAlbumViewHolder,
            position: Int,
            rankSubElement: RankSubElement,
            isACardStyle: Boolean
        ) {
            if (position <= SPAN_COUNT - 1) {
                holder.tvTrackIndex.setTextColor(holder.tvTrackIndex.resources.getColor(R.color.host_color_xmRed))
            } else {
                holder.tvTrackIndex.setTextColor(holder.tvTrackIndex.resources.getColor(R.color.main_color_808d8d91))
            }
            var hasValidServerColor = false
            if (rankSubElement.cachedCoverColor != null && rankSubElement.cachedCoverColor != ColorUtil.INVALID_COLOR) {
                hasValidServerColor = true
                setPlayBgColor(rankSubElement.cachedCoverColor!!, holder)
            }

            if (NewShowNotesManager.userNewShowNotes()) {
                holder.showNotePlayBtnBg.run {
                    val params = this.layoutParams as MarginLayoutParams
                    params.width = RecommendCornerUtils.getShowNoteSize()
                    params.height = params.width
                    this.layoutParams = params
                }

                holder.showNotePlayBtn.run {
                    val params = this.layoutParams
                    params.width = RecommendCornerUtils.getShowNotePlaySize()
                    params.height = params.width
                    this.layoutParams = params
                }

                RecommendCornerUtils.updateShowNoteRoundBg(holder.showNotePlayBtnBg)
            }

            val track = Track()
            track.dataId = rankSubElement.refId ?: 0
            val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
            if (isACardStyle && NewShowNotesManager.userNewShowNotes()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.showNotePlayBtnWrap)
                ViewStatusUtil.setVisible(View.GONE, holder.vPlayButtonBg, holder.ivPlayButton)
                RecommendCornerUtils.updateShowNotePlayIcon(holder.showNotePlayBtn, isPlaying)
            } else {
                if (isPlaying) {
                    holder.ivPlayButton.setImageResource(R.drawable.main_pause_btn_inside_fill_n_24)
                } else {
                    holder.ivPlayButton.setImageResource(R.drawable.main_play_btn_inside_fill_n_24)
                }
                ViewStatusUtil.setVisible(View.GONE, holder.showNotePlayBtnWrap)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.vPlayButtonBg, holder.ivPlayButton)
            }
            ImageManager.from(holder.trackCoverIv.context)
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    holder.trackCoverIv,
                    rankSubElement.cover,
                    com.ximalaya.ting.android.host.R.drawable.host_default_album,
                    R.drawable.main_recommend_staggered_item_default_bg,
                    60,
                    60
                ) { _, bitmap ->
                    if (bitmap != null && !hasValidServerColor) {
                        DomainColorUtil.getDomainColorForRecommend(
                            bitmap,
                            Color.BLACK
                        ) { color: Int ->
                            setPlayBgColor(color, holder)
                            rankSubElement.cachedCoverColor = color
                        }
                    }
                }
        }

        fun setPlayBgColor(color: Int, holder: TingAlbumViewHolder) {
            val gradientDrawable = GradientDrawable()
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.cornerRadius = 14.dp.toFloat()
            gradientDrawable.setColor(color)
            holder.vPlayButtonBg.background = gradientDrawable
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrap(holder, position)
                }
            } else {
                onBindViewHolderWrap(holder, position)
            }
        }

        private fun onBindViewHolderWrap(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is TingAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = LayoutParams.MATCH_PARENT
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun updateCoverSize(isAlbum: Boolean, holder: TingAlbumViewHolder) {
            val padding = RecommendCornerUtils.getPaddingSize()
            if (isAlbum) {
                holder.cslContainerView.setPadding(0, padding, 0, padding)
                holder.albumCoverLayoutView.updateCornerSize(RecommendCornerUtils.getOldRankCorner())
                holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getCoverSize())
            } else {
                holder.cslTrackContainerView.setPadding(0, padding, 0, padding)
                holder.trackCoverParent?.setCornerRadius(RecommendCornerUtils.getOldRankCorner())
                val params = holder.trackCoverIv.layoutParams
                val size = RecommendCornerUtils.getCoverSize().toInt()
                params.width = size
                params.height = size
                holder.trackCoverIv.layoutParams = params
            }

            val params = holder.showTagParentView?.layoutParams as? MarginLayoutParams
            if (params != null) {
                params.topMargin = RecommendCornerUtils.getShowTagGapSize()
                holder.showTagParentView?.layoutParams = params
            }
        }

        fun onBindViewHolderInner(holder: TingAlbumViewHolder, position: Int) {
            val rankSubElement = rankSubElementList.getOrNull(position)
            if (rankSubElement == null) {
                updateCoverSize(true, holder)
                holder.cslContainerView.visibility = View.VISIBLE
                holder.cslTrackContainerView.visibility = View.GONE
                holder.tvAlbumIndex.setTextIfChanged((position + 1).toString())
                if (mTypeface != null) {
                    holder.tvAlbumIndex.typeface = mTypeface
                }

                if (position <= SPAN_COUNT - 1) {
                    holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.host_color_xmRed))
                } else {
                    holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.main_color_808d8d91))
                }

                holder.albumTitleTv.setBackgroundColor(holder.albumTitleTv.resources.getColor(R.color.main_color_f4f5f6_282828))
                holder.albumTitleTv.setTextIfChanged("")
                holder.albumCoverLayoutView.setAlbumCover("")

                holder.itemView.setOnClickListener(null)
                holder.itemView.setOnLongClickListener(null)
                return
            }
            holder.albumTitleTv.setBackgroundColor(Color.TRANSPARENT)
            RecommendCornerUtils.updateTitleColor(holder.albumTitleTv)

            holder.itemView.setTag(R.id.main_id_item_data, rankSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            holder.itemView.setOnLongClickListener {
                myItemLongClickListener?.onItemLongClick(albumRank)
                return@setOnLongClickListener true
            }
            val isAd = rankSubElement.extraInfo?.subRefInfo?.ad ?: false
            val adTagShow =
                rankSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            var advertis: Advertis? = null
            if (isAd) {
                advertis = rankSubElement.extraInfo?.subRefInfo?.businessExtraInfo?.adInfoObject
            }
            holder.itemView.setTag(R.id.main_id_data_ad_info, advertis)
            val mStyle = recommendRankingItem.style
            val isBaseStyle = "base" == mStyle
            holder.itemView.setOnOneClickListener {
                onItemClickInner(rankSubElement, position, holder.itemView, advertis, false)
            }
            val remainder: Int = rankSubElementList.size % SPAN_COUNT
            val start: Int =
                if (remainder == 0) rankSubElementList.size - SPAN_COUNT else rankSubElementList.size - remainder
            val layoutParams = holder.cslContainerView.layoutParams
            val tcLayoutParams =
                holder.llTextContainer.layoutParams as ConstraintLayout.LayoutParams
            val layoutParamsTrack = holder.cslTrackContainerView.layoutParams
            val tcTrackLayoutParams =
                holder.llTrackTextContainer.layoutParams as ConstraintLayout.LayoutParams
            val tcTrackPlayIconLayoutParams =
                holder.showNotePlayBtnWrap.layoutParams as ConstraintLayout.LayoutParams
            val textViewContainerWithInPx: Int
            val playIconRightMarginInDp: Int
            var tcRightMarginInDp = 6

            if (position >= start) {
                // 最后一列
                textViewContainerWithInPx =
                    RpAdaptUtil.rp2PxIn375(375) - if (enableJumpMore) 37.dp else 0
                layoutParams.width = RpAdaptUtil.rp2PxIn375(375) - if (enableJumpMore) 37.dp else 0
                layoutParamsTrack.width = layoutParams.width
                if (enableJumpMore) {
                    tcLayoutParams.rightMargin = 16.dp
                    tcRightMarginInDp = 16
                } else {
                    tcLayoutParams.rightMargin = 47.dp
                    tcRightMarginInDp = 47
                }
                tcTrackPlayIconLayoutParams.rightMargin = 37.dp
                if (NewShowNotesManager.userNewShowNotes()) {
                    tcTrackLayoutParams.rightMargin = 0
                    playIconRightMarginInDp = 37
                } else {
                    tcTrackLayoutParams.rightMargin = 16.dp
                    playIconRightMarginInDp = 16 + 37
                }
            } else {
                tcLayoutParams.rightMargin = 0
                tcTrackPlayIconLayoutParams.rightMargin = 0
                if (isBaseStyle) {
                    textViewContainerWithInPx = RpAdaptUtil.rp2PxIn375(318 - getRankOffset())
                    layoutParams.width = RpAdaptUtil.rp2PxIn375(318 - getRankOffset())
                } else {
                    textViewContainerWithInPx = RpAdaptUtil.rp2PxIn375(210)
                    layoutParams.width = RpAdaptUtil.rp2PxIn375(210)
                }

                layoutParamsTrack.width = layoutParams.width
                if (NewShowNotesManager.userNewShowNotes()) {
                    playIconRightMarginInDp = 0
                    tcTrackLayoutParams.rightMargin = 0
                } else {
                    tcTrackLayoutParams.rightMargin = 6.dp
                    playIconRightMarginInDp = 6
                }
            }

            holder.cslContainerView.layoutParams = layoutParams
            holder.llTextContainer.layoutParams = tcLayoutParams
            holder.cslTrackContainerView.layoutParams = layoutParamsTrack
            holder.llTrackTextContainer.layoutParams = tcTrackLayoutParams
            holder.showNotePlayBtnWrap.layoutParams = tcTrackPlayIconLayoutParams

            if (rankSubElement.bizType.equals("Album")) {
                updateCoverSize(true, holder)
                holder.cslContainerView.visibility = View.VISIBLE
                holder.cslTrackContainerView.visibility = View.GONE
                holder.tvAlbumIndex.setTextIfChanged((position + 1).toString())
                holder.albumTitleTv.setTextIfChanged(rankSubElement.title)
                if (mTypeface != null) {
                    holder.tvAlbumIndex.typeface = mTypeface
                }

                if (position <= SPAN_COUNT - 1) {
                    holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.host_color_xmRed))
                } else {
                    holder.tvAlbumIndex.setTextColor(holder.tvAlbumIndex.resources.getColor(R.color.main_color_808d8d91))
                }

                rankSubElement.wrap?.ltSubscriptTag?.tag?.let {
                    holder.albumCoverLayoutView.setAlbumTag(
                        it
                    )
                }
                rankSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

                holder.albumCoverLayoutView.setPodCastTagAutoSize(
                    rankSubElement.extraInfo?.other?.getBRTagUrl()
                )

                // tomato的是第三行是showTags2,base的是第二行是showTags2
                ViewStatusUtil.setVisible(
                    if (adTagShow) View.VISIBLE else View.GONE,
                    holder.ivAdTag
                )
                val adWidth = if (adTagShow) 24 else 0
                val otherWidth =
                    38 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + tcRightMarginInDp
                RecommendShowTagsUtilNew.bindTagsView(
                    holder.layoutShowTags, rankSubElement.extraInfo?.showTags,
                    (textViewContainerWithInPx - otherWidth.dp - adWidth.dp), "", ""
                )

                val albumTitleOneLine = RecommendShowTagsUtilNew.canShowOneLine(
                    holder.albumTitleTv, rankSubElement.title,
                    (textViewContainerWithInPx - otherWidth.dp)
                )

                if (rankSubElement.extraInfo?.showTags2 != null &&
                    rankSubElement.extraInfo.showTags2!!.isNotEmpty() && albumTitleOneLine
                ) {
                    RecommendShowTagsUtilNew.bindTagsView(
                        holder.layoutShowTags2,
                        rankSubElement.extraInfo.showTags2,
                        (textViewContainerWithInPx - otherWidth.dp),
                        "",
                        "",
                        RecommendShowTagsUtilNew.THEME_SHOWTAG2
                    )
                    RecommendCornerUtils.updateSubTitleMargin(holder.layoutShowTags2)
                } else {
                    holder.layoutShowTags2.visibility = View.GONE
                }

                holder.itemView.setOnLongClickListener {
                    val build = MoreFuncBuild.createAlbumLongClickModel(
                        fragment, rankSubElement.refId ?: 0,
                        object : IMoreFuncListener() {
                            override fun onSubscriptionClick(
                                isSubscription: Boolean,
                                btnText: String?
                            ) {

                            }
                        })
                    val trackMap = mutableMapOf<String, String?>().apply {
                        put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                        put("contentType", recommendItemNew?.itemType)
                        put("contentId", rankSubElement.refId?.toString() ?: "")
                        put("modulePosition", (positionNew + 1).toString())
                        put("positionNew", (position + 1).toString())

                        albumRank?.ubtV2?.let { it1 -> putAll(it1) }
                        rankSubElement.ubtV2?.let { it1 -> putAll(it1) }
                    }
                    build.trackMap = trackMap
                    XmMoreFuncManager.checkShowMorePage(build)
                    true
                }
            } else {
                updateCoverSize(false, holder)
                RecommendCornerUtils.updateTitleColor(holder.trackTitleTv)
                holder.trackTitleTv.setTextIfChanged(rankSubElement.title)
                holder.tvTrackIndex.setTextIfChanged((position + 1).toString())
                if (mTypeface != null) {
                    holder.tvTrackIndex.typeface = mTypeface
                }
                holder.cslContainerView.visibility = View.GONE
                holder.cslTrackContainerView.visibility = View.VISIBLE
                ViewStatusUtil.setVisible(
                    if (adTagShow) View.VISIBLE else View.GONE,
                    holder.ivAdTagTrack
                )
                val adWidth = if (adTagShow) 24 else 0
                val coverSize = RecommendCornerUtils.getCoverSizeDp()
                val otherWidth = if (NewShowNotesManager.userNewShowNotes()) {
                    38 + coverSize + 12 + 54 + playIconRightMarginInDp
                } else {
                    38 + coverSize + 12 + playIconRightMarginInDp
                }
                RecommendShowTagsUtilNew.bindTagsView(
                    holder.trackShowTagsLayout, rankSubElement.extraInfo?.showTags,
                    textViewContainerWithInPx - otherWidth.dp - adWidth.dp, "", ""
                )

                val trackTitleOneLine = RecommendShowTagsUtilNew.canShowOneLine(
                    holder.trackTitleTv, rankSubElement.title,
                    (textViewContainerWithInPx - otherWidth.dp)
                )
                if (rankSubElement.extraInfo?.showTags2 != null &&
                    rankSubElement.extraInfo.showTags2!!.isNotEmpty() && trackTitleOneLine
                ) {
                    RecommendShowTagsUtilNew.bindTagsView(
                        holder.trackShowTagsLayout2,
                        rankSubElement.extraInfo.showTags2,
                        (textViewContainerWithInPx - otherWidth.dp),
                        "",
                        "",
                        RecommendShowTagsUtilNew.THEME_SHOWTAG2
                    )
                    RecommendCornerUtils.updateSubTitleMargin(holder.trackShowTagsLayout2)
                } else {
                    holder.trackShowTagsLayout2.visibility = View.GONE
                }

                onBindTrackViewHolder(holder, position, rankSubElement, true)
                holder.showNotePlayBtnWrap.setOnClickListener {
                    onItemClickInner(rankSubElement, position, holder.itemView, advertis, true)
                }

                holder.itemView.setOnLongClickListener {
                    val build = MoreFuncBuild.createTrackLongClickModel(
                        fragment, rankSubElement.refId ?: 0,
                        object : IMoreFuncListener() {
                            override fun onSubscriptionClick(
                                isSubscription: Boolean,
                                btnText: String?
                            ) {

                            }
                        })
                    val trackMap = mutableMapOf<String, String?>().apply {
                        put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                        put("contentType", recommendItemNew?.itemType)
                        put("contentId", rankSubElement.refId?.toString() ?: "")
                        put("modulePosition", (positionNew + 1).toString())
                        put("positionNew", (position + 1).toString())

                        albumRank?.ubtV2?.let { it1 -> putAll(it1) }
                        rankSubElement.ubtV2?.let { it1 -> putAll(it1) }
                    }
                    build.trackMap = trackMap
                    XmMoreFuncManager.checkShowMorePage(build)
                    true
                }
            }
        }

        fun onItemClickInner(
            rankSubElement: RankSubElement,
            position: Int,
            view: View?,
            advertis: Advertis?,
            isClickPlayBtn: Boolean
        ) {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (positionNew + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put(
                    "card_adTopn",
                    ((recommendItemNew!!.item as RecommendRankListItem).cardAdCount.toString())
                )
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2,
                (positionNew + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2
            )
            trace1.createTrace()
            // 新首页-榜单-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(61031) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("tabName", albumRank?.title ?: "") // 根据实际文案
                .put("albumId", rankSubElement.refId?.toString() ?: "")
                .put("contentType", "album")
                .put("contentId", rankSubElement.refId?.toString() ?: "")
                .put(
                    "positionNew",
                    (position + 1).toString()
                ) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("xmRequestId", recommendItemNew?.xmRequestId)
                .put("rec_track", rankSubElement.ubt?.recTrack ?: "")
                .put("rec_src", rankSubElement.ubt?.recSrc ?: "")
                .put("ubtTraceId", rankSubElement.ubt?.traceId ?: "")
                .put("rankId", albumRank?.id?.toString() ?: "")
                .put("modulePosition", (positionNew + 1).toString())
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                ) // 可见区域占屏幕的比例
                .put(
                    "socialTagId",
                    rankSubElement.getSocialTagId().toString()
                ) // 传对应的社会化标签id，若无则不用传
                .put(
                    "tabPosition",
                    ((recommendItemNew!!.item as RecommendRankListItem).innerListSelectedIndex + 1).toString()
                ) // 传对应的社会化标签名称，若无则不用传
                .put("isAd", if (advertis != null) "true" else "false")
                .put("area", if (isClickPlayBtn) "play" else "item")

            SpmTraceUtil.addSpmTraceInfo(
                trace,
                (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2,
                (positionNew + 1).toString(),
                albumRank?.title,
                ((recommendItemNew!!.item as RecommendRankListItem).innerListSelectedIndex + 1).toString(),
                contentTitle = rankSubElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace,
                (recommendItemNew?.item as? RecommendRankListItem)?.ubtV2
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, albumRank?.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, rankSubElement.ubtV2)
            trace.createTrace()

            if (advertis != null) {
                // 广告点击上报
                AdManager.handlerAdClick(
                    BaseApplication.getMyApplicationContext(), advertis,
                    AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SITE_CLICK,
                        AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST
                    )
                        .positionNew(position + 1)
                        .modulePosition(positionNew + 1)
                        .build()
                )
                return
            }
            if (rankSubElement.bizType.equals("Track") && !isClickPlayBtn && NewShowNotesManager.userNewShowNotes()) {
                // 打开shownotes二级页
                NewShowNotesManager.startShowNotesDetailFragment(
                    NewShowNotesManager.SOURCE_FROM_HOME,
                    NewShowNotesManager.appendPodListModeParam(rankSubElement.landingPage),
                    0,
                    rankSubElement.refId ?: 0L,
                    null
                )
            } else {
                if (isClickPlayBtn) {
                    val track = Track()
                    track.dataId = rankSubElement.refId ?: 0L
                    val isPlaying = PlayTools.isCurrentTrackPlaying(fragment.activity, track)
                    if (isPlaying) {
                        PlayTools.pause(
                            BaseApplication.getMyApplicationContext(),
                            PauseReason.Business.RecommendRankList
                        )
                    } else {
                        jump(
                            fragment,
                            NewShowNotesManager.appendPodListModeParam(rankSubElement.landingPage)
                        )
                    }
                } else {
                    if (rankSubElement.bizType.equals("Track")) {
                        jump(
                            fragment,
                            NewShowNotesManager.appendPodListModeParam(rankSubElement.landingPage)
                        )
                    } else {
                        jump(fragment, rankSubElement.landingPage)
                    }
                }
            }
        }

        @SuppressLint("NotifyDataSetChanged")
        fun setNewList(list: List<RankSubElement>) {
            rankSubElementList.clear()
            rankSubElementList.addAll(list)
            notifyDataSetChanged()
        }

        override fun getItemCount(): Int {
            return if (rankSubElementList.isEmpty()) {
                if (enableJumpMore) spanCount * 2 + 1 else spanCount * 2
            } else {
                if (enableJumpMore) rankSubElementList.size + 1 else rankSubElementList.size
            }
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class TingAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var albumTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)
            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var tvAlbumIndex: TextView = view.findViewById(R.id.main_tv_album_index)
            var layoutShowTags2: LinearLayout = view.findViewById(R.id.main_layout_show_tag2)
            var cslContainerView: ConstraintLayout =
                view.findViewById(R.id.main_container_layout_album)
            var llTextContainer: LinearLayout = view.findViewById(R.id.main_ll_text_container)

            var cslTrackContainerView: ConstraintLayout =
                view.findViewById(R.id.main_container_layout_track)
            var llTrackTextContainer: LinearLayout =
                view.findViewById(R.id.main_ll_text_container_track)
            var tvTrackIndex: TextView = view.findViewById(R.id.main_tv_track_index)
            var trackTitleTv: TextView = view.findViewById(R.id.main_tv_track_title)
            var trackCoverIv: ImageView = view.findViewById(R.id.main_iv_track_cover)
            var trackShowTagsLayout: LinearLayout =
                view.findViewById(R.id.main_track_show_tags_layout)
            var trackShowTagsLayout2: LinearLayout =
                view.findViewById(R.id.main_track_show_tags_layout2)
            var vPlayButtonBg: View = view.findViewById(R.id.main_v_bg_play)
            var ivPlayButton: ImageView = view.findViewById(R.id.main_iv_play_btn)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)
            var ivAdTagTrack: ImageView = view.findViewById(R.id.main_iv_ad_tag_track)
            var showNotePlayBtnWrap: View = view.findViewById(R.id.main_show_notes_play_layout_wrap)
            var showNotePlayBtnBg: View = view.findViewById(R.id.main_show_notes_play_layout)
            var showNotePlayBtn: ImageView = view.findViewById(R.id.main_iv_show_notes_play_btn)
            var showTagParentView: ViewGroup? = view.findViewById(R.id.main_show_tags_and_ad_layout)
            var trackCoverParent: CornerRelativeLayout? =
                view.findViewById(R.id.main_v_cover_layout)

            // 图片封面宽度
            var coverWidth: Int = 0

            init {
                resetSize()
            }

            fun resetSize() {
                val layoutParams = cslContainerView.layoutParams
                layoutParams.width = RpAdaptUtil.rp2PxIn375(318)
            }
        }
    }

    companion object {
        private var SPAN_COUNT = 3
        private var hasForceUpdate = false
        var needForceUpdate = false
        private var AD_REPORT_SUB_PERCENT = ConfigureCenter.getInstance().getInt(
            CConstants.Group_ad.GROUP_NAME,
            CConstants.Group_ad.ITEM_HOME_AD_EXPOSE_PERCENT,
            50
        )

        fun setForceUpdateFromAd() {
            if (hasForceUpdate) {
                return
            }
            needForceUpdate = true
        }

        // 颜色转换
        // targetOpacityPercent 不透明度百分比，1完全不透明，0完全透明
        fun colorTransferOpacity(
            color: Int,
            targetOpacityPercent: Float,
            targetSaturation: Float,
            targetBrightness: Float
        ): Int {
            return ColorUtil.covertColorToFixedSaturationAndBrightness(
                color, (targetOpacityPercent * 255).toInt(), targetSaturation, targetBrightness
            )
        }

        // 获取折叠屏时卡片的宽度
        fun getFoldItemWidth(): Int {
            // 半屏宽度 - 左边距 - 右边距的一半
            val leftMargin = 16.dp
            return FOLD_SCREEN_WIDTH - leftMargin - leftMargin / 2
        }

        // 执行跳转
        fun jump(fragment: BaseFragment2, landingPage: String?) {
            if (landingPage.isNullOrBlank()) {
                return
            }
            ToolUtil.clickUrlAction(fragment, landingPage, null)
        }

        // 折叠屏是否展开
        private fun isFoldScreenWithExpand(): Boolean {
            val context = BaseApplication.getTopActivity()
            return BaseUtil.isCollapsibleScreenOnLandscapeMode(context) || BaseUtil.isCollapsibleScreenOnPortraitExpandMode(
                context
            )
        }
    }

    // tab切换事件
    interface OnTabSelectListener {

        fun onSelect(innerPosition: Int)

        fun clickTab(innerPosition: Int)
    }
}