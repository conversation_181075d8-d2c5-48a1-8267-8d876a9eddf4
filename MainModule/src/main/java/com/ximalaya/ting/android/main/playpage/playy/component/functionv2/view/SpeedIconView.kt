package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.view

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager

/**
 * Created on 2024/10/11.
 * <AUTHOR>
 * @email <EMAIL>
 */
class SpeedIconView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context) {

    var tvSpeed: TextView? = null
    private var tvSpeedUnit: TextView? = null

    init {
        // 面性和线性图标，分ab
        val layoutId = R.layout.main_layout_function_speed_icon_linear
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)

        val iconView = view.findViewById<ImageView>(R.id.main_iv_speed_icon)
        tvSpeed = view.findViewById(R.id.main_tv_speed)
        tvSpeedUnit = view.findViewById(R.id.main_tv_speed_unit)

        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            iconView.setColorFilter(PSkinManager.getBtnThemeColor())
            tvSpeed?.setTextColor(PSkinManager.getBtnThemeColor())
            tvSpeedUnit?.setTextColor(PSkinManager.getBtnThemeColor())
        }

        val typeface = Typeface.createFromAsset(
            context.resources.assets, "fonts" +
                    "/XmlyNumberV1.0-SemiBold.otf"
        )
        tvSpeed?.typeface = typeface
        tvSpeedUnit?.typeface = typeface
    }
}