package com.ximalaya.ting.android.main.playpage.playy.component.aduve;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.play.YellowZoneModelV2;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.CountDownTimeUtil;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.buyviewbar.IBuyViewChild;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.buyviewbar.IBuyViewComponentActionProvider;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.buyviewbar.IDataProvider;
import com.ximalaya.ting.android.main.util.CommonUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 接入 UVE 后的小黄条组件修改版V4
 * <p>
 * 区别在于，数据来源需要调整
 */
public class XNewYellowZoneViewV4 extends AbstractUveYellowZoneView {



    // 控件
    private View vBgColor;
    private ImageView vLogo;
    private LinearLayout vLogoArea;
    private View vLogoSplit;
    private TextView vCenterText;
    private CardView vBtnArea;
    private FrameLayout vBtnContainer;
    private TextView vBtn;
    private ImageView vCloseBtn;
    protected PlayingSoundInfo localSoundInfo;

    public XNewYellowZoneViewV4(Context context, IDataProvider dataProvider, IBuyViewComponentActionProvider actionProvider) {
        super(context, dataProvider, actionProvider);
    }


    @Override
    public boolean show() {
        PlayingSoundInfo soundInfo = mDataProvider.getPlayingSoundInfo();
        YellowZoneModelV2 localYellowZoneModel = yellowZoneModel;
        Logger.d("z_uve_yellow", "XNewYellowZoneViewV4 show >>> ");

        if (null == soundInfo || null == localYellowZoneModel) {
            Logger.d("z_uve_yellow", "XNewYellowZoneViewV4 show return, data is null >>> " + localYellowZoneModel + ", " + soundInfo);
            mActionProvider.hide();
            isShowing = false;
            return false;
        }
        localYellowZoneModel.getMarkPointExtraInfo();
        localSoundInfo = soundInfo;
        // 根据目前支持的类型来返回是否支持显示此类黄条
        boolean isShow = yellowZoneModel.isValid();
        Logger.d("z_uve_yellow", "XNewYellowZoneViewV3 show isValid: " + isShow);

        if (isShow) {
            setView(localSoundInfo, yellowZoneModel);
            if (yellowZoneModel.displaySceneId > 0) {
                YUveYellowBarComponent.markPoint(mActionProvider.getFragment(), mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrackId(), yellowZoneModel.displaySceneId);
            }
        } else {
            mActionProvider.hide();
        }
        isShowing = isShow;
        return isShow;
    }

    @Override
    protected int getViewHeight(YellowZoneModelV2 yellowZone) {
        return IBuyViewChild.BUY_VIEW_HEIGHT_52;
    }

    @Override
    protected View initView() {
        if (vBarView == null) {
            int layoutRes = R.layout.main_play_page_buy_view_yellow_zone_y_v4;
            vBarView = LayoutInflater.from(mContext).inflate(layoutRes, null);
            vBarView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (yellowZoneModel == null) {
                        return;
                    }
                    realDoClickArea(v, yellowZoneModel.getFirstButton());
                }
            });
            vBgColor = vBarView;
            vLogoArea = vBarView.findViewById(R.id.main_ll_logo_container);
            vLogo = vBarView.findViewById(R.id.main_iv_logo);
            vLogoSplit = vBarView.findViewById(R.id.main_vip_view_vertical_split);
            vCenterText = vBarView.findViewById(R.id.main_buy_view_yellow_zone_text);
            vBtnContainer = vBarView.findViewById(R.id.main_fl_btn_container);
            vBtnArea = vBarView.findViewById(R.id.main_buy_view_yellow_zone_btn_area);
            vBtn = vBarView.findViewById(R.id.main_buy_view_yellow_zone_btn);
            vCloseBtn = vBarView.findViewById(R.id.main_buy_view_yellow_zone_btn_close);
        }
        return vBarView;
    }

    private void setMainText() {
        if (null == yellowZoneModel || null == yellowZoneModel.text || null == vCenterText) {
            return;
        }
        long oneSec = TimeUnit.SECONDS.toMillis(1);
        long timeLeft = CountDownTimeUtil.getRealCountDownDuration(yellowZoneModel.getExpireTimeInMill(), yellowZoneModel.localBaseTimeStamp);
        if (0 < timeLeft && yellowZoneModel.text.contains("%s")) {
            ViewStatusUtil.setText(vCenterText, getCenterCountDownText(yellowZoneModel, timeLeft));
            if (null != countDown) {
                countDown.cancel();
            }
            PlayingSoundInfo playingSoundInfo = null == mDataProvider ? null : mDataProvider.getPlayingSoundInfo();
            countDown = new CountDown(playingSoundInfo, this, yellowZoneModel, timeLeft, oneSec);
            countDown.start();
        } else {
            ViewStatusUtil.setText(vCenterText, yellowZoneModel.text);
        }
    }

    @Override
    protected void realDoClickClose(View view, YellowZoneModelV2 yellowZoneModel) {
        if (yellowZoneModel == null) return;
        // 调用 UVE 埋点
        if (behaviorCallback != null) {
            Map<String, String> params = new HashMap<>();
            params.put("type", yellowZoneModel.type + "");
            behaviorCallback.onClose(true, params);
        }
        if (yellowZoneModel.sceneId > 0) {
            YUveYellowBarComponent.markPoint(mActionProvider.getFragment(), mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrackId(), yellowZoneModel.sceneId);
        }
        super.realDoClickClose(view, yellowZoneModel);
        Logger.d("z_uve_yellow", "realDoClickClose " + yellowZoneModel);
    }

    @Override
    protected void markPointOnShow(PlayingSoundInfo soundInfo, final YellowZoneModelV2 yellowZone, int timePoint) {
        super.markPointOnShow(soundInfo, yellowZone, timePoint);
        Logger.d("z_uve_yellow", "markPointOnShow " + yellowZone + ", timePoint:" + timePoint);
        if (timePoint == ON_INIT) {
            // 新声音播放页-MOT引导条  控件曝光
            if (yellowZone != null) {
                Map extraInfo = yellowZone.getMarkPointExtraInfo();
                if (extraInfo == null) {
                    extraInfo = new HashMap();
                }
                String url = null;
                YellowZoneModelV2.ButtonInfo buttonInfo = yellowZone.getFirstButton();
                if (buttonInfo != null) {
                    url = buttonInfo.url;
                }
                new XMTraceApi.Trace()
                        .setMetaId(64995)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newPlay")
                        .put("xmRequestId", XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .put("isClose", String.valueOf(yellowZone.isCloseAble())) // true 表示可关闭，close 表示不可关闭
                        .put("businessType", ""+yellowZone.businessType) // 区分 vip、vip 儿童，svip，ximi，单卖，免费专辑
                        .put("moduleId", "" + yellowZone.templateId) // 传对应的模版 id
                        .put("type", "" + yellowZone.type) // 区分对应的小黄条类型，整型数值
                        .put(XmRequestIdManager.CONT_TYPE, "yellowZone")
                        .put(XmRequestIdManager.CONT_ID, "" + yellowZone.type)
                        .put(extraInfo)
                        .put("url", url)
                        .createTrace();
                //调用 UVE 埋点
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        if (behaviorCallback != null && yellowZone != null) {
                            Map<String, String> params = new HashMap<>();
                            params.put("type", yellowZone.type + "");
                            behaviorCallback.onShow(params);
                        }
                    }
                }, 200);
            }
        }
    }

    @Override
    protected void markPointOnClick(View btn, PlayingSoundInfo soundInfo, YellowZoneModelV2 yellowZone, YellowZoneModelV2.ButtonInfo buttonInfo) {
        Logger.d("z_uve_yellow", "markPointOnClick " + yellowZone);
        //调用 UVE 埋点
        if (yellowZone != null) {
            if (behaviorCallback != null) {
                Map<String, String> params = new HashMap<>();
                params.put("type", yellowZone.type + "");
                behaviorCallback.onClick(params);
            }
            // 新声音播放页-MOT引导条  点击事件
            Map extraInfo = yellowZone.getMarkPointExtraInfo();
            if (extraInfo == null) {
                extraInfo = new HashMap();
            }
            String url = null;
            if (buttonInfo != null) {
                url = buttonInfo.url;
            }
            new XMTraceApi.Trace()
                    .click(64994) // 用户点击时上报
                    .put("currPage", "newPlay")
                    .put("xmRequestId", XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                    .put("isClose", String.valueOf(yellowZone.isCloseAble())) // true 表示可关闭，close 表示不可关闭
                    .put("businessType", ""+yellowZone.businessType) // 区分 vip、vip 儿童，svip，ximi，单卖，免费专辑
                    .put("moduleId", "" + yellowZone.templateId) // 传对应的模版 id
                    .put("type", yellowZone.type + "") // 区分对应的小黄条类型，整型数值
                    .put(extraInfo)
                    .put("url", url)
                    .createTrace();
        }
    }

    @Override
    protected void setData() {
        YellowZoneModelV2 yellowZone = yellowZoneModel;
        PlayingSoundInfo soundInfo = localSoundInfo;
        if (null == yellowZone || null == soundInfo) {
            return;
        }
        resetTextInfoArea();
        if (null != vBgColor) {
            vBgColor.setBackground(getBackgroundDrawable(yellowZone));
        }
        if (null != vCenterText) {
            vCenterText.setTextColor(getTextColor(yellowZone));
        }
        setMainText();
        if (null != vLogo && yellowZone.logo != null && !TextUtils.isEmpty(yellowZone.logo.logoUrl)) {
            ViewStatusUtil.setVisible(View.VISIBLE, vLogoArea);
            int targetHeight = BaseUtil.dp2px(mContext, 18);
            float scale = 0f;
            try{
                scale = Float.valueOf(yellowZone.logo.size);
            }catch (Exception e){
                Logger.e("z_uve_yellow", "setData scale error:" + yellowZone.logo.size);
            }
            if (scale > 0) {
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams((int) (targetHeight * scale), targetHeight);
                vLogo.setLayoutParams(lp);
                ImageManager.from(mContext).displayImage(vLogo, yellowZone.logo.logoUrl, -1);
            } else {
                ImageManager.from(mContext).downloadBitmap(yellowZone.logo.logoUrl, new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                        if (bitmap == null) {
                            ViewStatusUtil.setVisible(View.GONE, vLogoArea);
                        } else {
                            vLogo.setImageBitmap(bitmap);
                            int bw = bitmap.getWidth();
                            int bh = bitmap.getHeight();
                            if (bw > 0 && bh > 0) {
                                int targetWidth = (int) ((1f * targetHeight / bh) * bw);
                                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(targetWidth, targetHeight);
                                vLogo.setLayoutParams(lp);
                            }
                        }
                    }
                });
            }
            vLogoSplit.setBackgroundColor(getLogoSplitColor(yellowZone));
        } else {
            ViewStatusUtil.setVisible(View.GONE, vLogoArea);
        }
        List<YellowZoneModelV2.ButtonInfo> buttonInfoList = yellowZone.buttonList;
        if (ToolUtil.isEmptyCollects(buttonInfoList)) {
            ViewStatusUtil.setVisible(View.GONE, vBtnContainer);
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, vBtnContainer);
            if (null != vBtnArea) {
                vBtnArea.setCardBackgroundColor(getBtnBackgroundColor(yellowZone));
            }
            YellowZoneModelV2.ButtonInfo buttonInfo = CommonUtil.safelyGetItem(buttonInfoList, 0);
            if (null != vBtn && buttonInfo != null) {
                vBtn.setTextColor(getTextColor(yellowZone));
                vBtn.setAlpha(buttonInfo.actionId == -1 ? 0.5f : 1f);
                ViewStatusUtil.setText(vBtn, TextUtils.isEmpty(buttonInfo.text) ? "立即开通" : buttonInfo.text);
            }
        }
        ViewStatusUtil.setVisible(yellowZone.isCloseAble() ? View.VISIBLE : View.GONE, vCloseBtn);
        if (yellowZone.isCloseAble()) {
            vCloseBtn.setVisibility(View.VISIBLE);
            vCloseBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    realDoClickClose(v, yellowZone);
                }
            });
        } else {
            vCloseBtn.setVisibility(View.GONE);
        }
    }

    @Override
    protected void doOnTicking(YellowZoneModelV2 tickingYellowZoneModel, long millisUntilFinished) {
        if (null == tickingYellowZoneModel) {
            return;
        }
        ViewStatusUtil.setText(vCenterText, getCenterCountDownText(tickingYellowZoneModel, millisUntilFinished));
    }

    @Override
    protected void doOnCountDownFinish() {
        if (null == yellowZoneModel || null == yellowZoneModel.text || null == vCenterText) {
            return;
        }
        if (yellowZoneModel.text.contains("%s")) {
            ViewStatusUtil.setText(vCenterText, getCenterCountDownText(yellowZoneModel, 0));
        } else {
            ViewStatusUtil.setText(vCenterText, yellowZoneModel.text);
        }
    }

    private Drawable getBackgroundDrawable(YellowZoneModelV2 yellowZoneModel) {
        int solidColor = ColorUtil.changeColorAlpha(yellowZoneModel.getBackgroundColor(), 0x14);
        int strokeColor = ColorUtil.changeColorAlpha(yellowZoneModel.getBackgroundColor(), 0x1f);
        ViewStatusUtil.GradientDrawableBuilder builder = new ViewStatusUtil.GradientDrawableBuilder();
        return builder.cornerRadius(BaseUtil.dp2pxReturnFloat(mContext, 6))
                .color(solidColor)
                .stroke(BaseUtil.dp2px(mContext, 0.5f), strokeColor)
                .build();
    }

    private int getBtnBackgroundColor(YellowZoneModelV2 yellowZoneModel) {
        return ColorUtil.changeColorAlpha01(yellowZoneModel.getBackgroundColor(), 0.12f);
    }

    private int getTextColor(YellowZoneModelV2 yellowZoneModel) {
        return yellowZoneModel.getTextColor();
    }

    private int getLogoSplitColor(YellowZoneModelV2 yellowZoneModel) {
        return ColorUtil.changeColorAlpha01(yellowZoneModel.getTextColor(), 0.3f);
    }

    private CharSequence getCenterCountDownText(YellowZoneModelV2 yellowZone, long timeLeft) {
        if (null == yellowZone || TextUtils.isEmpty(yellowZone.text)) {
            return "";
        }
        String timePart = CountDownTimeUtil.buildCountDownStyle3(timeLeft);
        String headPart = yellowZone.text;
        SpannableString spannableString;
        if (TextUtils.isEmpty(timePart)) {
            spannableString = new SpannableString(timePart);
        } else {
            if (headPart.contains("%s")) {
                spannableString = new SpannableString(headPart.replace("%s", timePart));
            } else {
                spannableString = new SpannableString(headPart);
            }
        }
        return spannableString;
    }
}
