package com.ximalaya.ting.android.main.playpage.playx

import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.*
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.constant.SharedConstant
import com.ximalaya.ting.android.host.fragment.play.data.PlayPageDynamicState
import com.ximalaya.ting.android.host.fragment.play.data.Theme
import com.ximalaya.ting.android.host.manager.TempDataManager
import com.ximalaya.ting.android.host.manager.play.PlayerManager
import com.ximalaya.ting.android.host.manager.play.quality.TrackPlayQualityKt
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.play.*
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.other.VideoPlayParamsBuildUtil
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.main.model.anchor.AnchorVideoList
import com.ximalaya.ting.android.main.playpage.data.repository.PlayPageDynamicDataRepository
import com.ximalaya.ting.android.main.playpage.data.repository.PlayPageThemeRepository
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.YDocCoverDataManager
import com.ximalaya.ting.android.main.playpage.manager.shortdrama.ShortDramaManager
import com.ximalaya.ting.android.main.playpage.playx.model.ItingData
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.controlbar.YPlaySimpleControlBar
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayModeSwitcher
import com.ximalaya.ting.android.main.playpage.util.PlayPageTabUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.*
import java.util.concurrent.locks.Condition

class XPlayViewModel: ViewModel() {
    private val TAG = "XPlayViewModel"

    var isPlayPageY = false

    private val condition = Object()
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private val _coverBitmap = MutableLiveData<Bitmap?>()
    val coverBitmap: LiveData<Bitmap?> = _coverBitmap

    private val _currentTheme = MutableLiveData<Theme?>()
    val currentTheme: LiveData<Theme?> = _currentTheme
    private var currentThemeId = 0L
    private val themeRepository = PlayPageThemeRepository {
        _currentTheme.value = it
        currentThemeId = lastThemeId

        if (!isPlayPageY) {
            PlayPageDataManager.getInstance()
                .setThemeColor(it.foregroundColor, it.backgroundColor, it.bottomBarColor, it.originalColor)
        }

        synchronized(condition) {
            condition.notify()
        }
    }

    private val _dynamicState = MutableLiveData<PlayPageDynamicState?>()
    val dynamicState: LiveData<PlayPageDynamicState?> = _dynamicState

    val currentQuality = MutableLiveData<TrackQualities?>()


    private val _fromVideoChannelLiveData = MutableLiveData<Boolean?>()
    val fromVideoChannelLiveData: LiveData<Boolean?> get() = _fromVideoChannelLiveData


    private val _videoDetailLiveData = MutableLiveData<Pair<Long, VideoBaseInfo?>?>()
    val videoDetailLiveData: LiveData<Pair<Long, VideoBaseInfo?>?> get() = _videoDetailLiveData

    private val _noTab = MutableLiveData<Boolean>(false)
    val noTab: LiveData<Boolean> get() = _noTab


    val picSequenceData = MutableLiveData<PicSequenceData>()

    private var isSketchAlbum = false
        set(value) {
            field = value
            updateSketchAlbumInfo()
        }
    private var isFromSketchChannel = false
        set(value) {
            field = value
            updateSketchAlbumInfo()
        }

    var isFromBack = false
        private set

    var fromPlayBar = false
        private set


    private fun updateSketchAlbumInfo() {
        val isSketch = isSketchAlbum || isFromSketchChannel
        val originValue = _fromVideoChannelLiveData.value
        if (isSketch != originValue) {
            _fromVideoChannelLiveData.value = isSketch
        }
    }

    data class PendingTask(val trackId: Long, val task: (PlayingSoundInfo) -> Boolean)
    private val pendingTasks = mutableListOf<PendingTask>()
    private var coverPicsJob: Job? = null

    fun requestPicData() {
        coverPicsJob?.cancel()
        val soundInfo = soundInfoLiveData.value?: return
        if (!soundInfo.fromPlayer && PlayPageTabUtil.isPlayY(soundInfo)) {
            coverPicsJob = scope.launch {
                picSequenceData.value = YDocCoverDataManager.requestData(soundInfo)
            }
        }
    }

    private fun updateQualityAndEffect(soundInfo: PlayingSoundInfo) {
        val isWifi = NetworkUtils.getNetType(BaseApplication.getMyApplicationContext()) == NetworkUtils.NETWORK_TYPE_WIFI
        val quality = TrackPlayQualityKt.getQualityObjWithDowngrading(soundInfo.trackInfo?.trackQualityList?: emptyList(), isWifi)
        currentQuality.value = quality

        if (quality != null) {
            TrackPlayQualityKt.checkAndRestFullDepth(quality.qualityLevel)
        }
    }

    val soundInfoLiveData = object : MutableLiveData<PlayingSoundInfo?>() {
        override fun setValue(value: PlayingSoundInfo?) {
            if (value != null) {
                if (value.trackInfo?.isVideo == true && ShortDramaManager.isShortDrama(value.trackInfo2TrackM())) {
                    ShortDramaManager.loadCurrentUserRemainShortDramaRightCountIfNone(value.albumInfo!!.albumId)
                }

                if (!value.fromPlayer) {
                    isSketchAlbum = value.albumInfo?.type == 32
                    _noTab.value = YUtils.isNoTab(value)
                }

                val trackId = value.trackInfo?.trackId?: return
                val pendingIterator = pendingTasks.iterator()
                while (pendingIterator.hasNext()) {
                    val task = pendingIterator.next()
                    if (trackId == task.trackId || task.trackId < 0L) {
                        if (task.task(value)) {
                            pendingIterator.remove()
                        }
                    } else {
                        pendingIterator.remove()
                    }
                }

                updateQualityAndEffect(value)
            }
            super.setValue(value)
            requestPicData()
        }
    }

    private val dynamicDataRepository = PlayPageDynamicDataRepository {
        if (_dynamicState.value != it) {
            _dynamicState.value = it
            mergeSoundInfoWithDynamic(it)
        }
    }

    private fun mergeSoundInfoWithDynamic(dynamicState: PlayPageDynamicState) {
        soundInfoLiveData.value?.let {
            val otherInfo = it.otherInfo
            val track = it.trackInfo2TrackM()
            if (track != null && track.dataId == trackId) {
                it.trackInfo?.likes = dynamicState.likeCount.toInt()
                track.favoriteCount = dynamicState.likeCount.toInt()
                track.isCollect = track.isCollect
                otherInfo?.isLike = dynamicState.isLiked
                otherInfo?.likeCount = track.favoriteCount
                otherInfo?.isCollect = dynamicState.isCollect
                otherInfo?.collectNum = dynamicState.collectCount.toInt()

                if (track.isLike != dynamicState.isLiked) {
                    track.isLike = dynamicState.isLiked
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).updateTrackInPlayList(track)
                }
            }
        }
    }

    data class VideoArgument(
        val videoId: Long,
        val albumId: Long = 0L,
        val cover: String? = null,

        val anchorUid: Long = 0L,
        val anchorVideoList: AnchorVideoList? = null
    )

    data class XPlayModeArgument (
        val isFromPlayBar: Boolean = false,
        val isVideo: Boolean = false,
        val videoArgument: VideoArgument? = null,


        val isSound: Boolean = false
    )


    //handle iting for playpage
    private var _iTingLiveData = MutableLiveData<ItingData?>()
    val iTingLiveData: LiveData<ItingData?> = _iTingLiveData

    var iting: String? = ""
//        set(value) {
//            if (field != value) {
//                field = value
//                _iTingLiveData.value = ItingData.fromIting(field)
//            }
//        }

    fun setIting(value: String?, forceToNotify: Boolean = false) {
        if (forceToNotify || iting != value) {
            iting = value
            _iTingLiveData.value = ItingData.fromIting(value)
        }
    }

    private fun checkResetIting() {
        if (trackId != _iTingLiveData.value?.trackId && iting?.isNotBlank() == true) {
            setIting("")
        }
    }

    var trackId: Long = 0L
        set(value) {
            if (field != value) {
                val lastValue = field
                field = value
                _trackIdLiveData.value = value
                checkResetIting()
                themeRepository.reReset()
                Logger.d(TAG, "reReset theme")
                if (lastValue != 0L) {
                    Log.d(TAG, "pendingTasks cleared")
                    pendingTasks.removeAll { it.trackId != -1L }
                }
            }
        }
    val _trackIdLiveData = MutableLiveData<Long>()
    val trackIdLiveData :LiveData<Long> = _trackIdLiveData

    val minorLiveData = MutableLiveData<PlayPageMinorData?>()
    val playModeLiveData = MutableLiveData<XPlayModeArgument?>()

    private val videoClearScreenLiveData = MutableLiveData<Boolean?>()
    val videoClearStatusLiveData =  MediatorLiveData<Boolean?>().apply {
        var fromChannel = false
        var clearScreen = false

        fun update() {
            if (!fromChannel) clearScreen = false
            val newValue = if(fromChannel) clearScreen else null
            if (newValue != value) value = newValue
        }

        addSource(fromVideoChannelLiveData) {
            fromChannel = it == true
            update()
        }
        addSource(videoClearScreenLiveData) {
            clearScreen = it == true
            update()
        }
    }

    fun clearVideoScreen() {
        val originValue = videoClearScreenLiveData.value
        videoClearScreenLiveData.value = originValue != true
    }

    data class XVideoArgument(val isAllVideo:Boolean)

    private val internalSoundInfoObserver = object : Observer<PlayingSoundInfo?> {
        override fun onChanged(soundInfo: PlayingSoundInfo?) {
            if (soundInfo != null) {
                with(PlayPageDynamicState.fromSoundInfo(soundInfo)) {
                    if (_dynamicState.value != this) _dynamicState.value = this
                    dynamicDataRepository.observeWith(this)
                }
            }
        }
    }

    init {
        soundInfoLiveData.observeForever(internalSoundInfoObserver)
    }

    fun updateThemeData(bitmap: Bitmap?) {
        if (lastThemeId != trackId) {
            lastThemeId = trackId
        }
        themeRepository.doFetchColorTask(bitmap) { theme ->
            _currentTheme.value = theme
            currentThemeId = lastThemeId
            PlayPageDataManager.getInstance()
                .setThemeColor(theme.foregroundColor, theme.backgroundColor, theme.bottomBarColor, theme.originalColor)

            synchronized(condition) {
                condition.notify()
            }
        }
    }

    private var lastThemeId: Long = -1;

    fun getThemeColor(): Int {
        if (currentThemeId == trackId) {
            return _currentTheme.value?.backgroundColor?:PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
        }

        Log.d(TAG, "getThemeColor； currentThemeId = $currentThemeId; trackId = $trackId, wait...")
        synchronized(condition) {
            condition.wait(3 * 1000)
        }

        if (currentThemeId == trackId) {
            Log.d(TAG, "getThemeColor; return right...")
            return _currentTheme.value?.backgroundColor?: PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
        }
        Log.w(TAG, "still not right; return DEFAULT...")
        return _currentTheme.value?.backgroundColor?: PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
    }

    @JvmOverloads
    fun updateThemeDataY(bitmap: Bitmap?) {
        if (lastThemeId != trackId) {
            lastThemeId = trackId
            _coverBitmap.value = bitmap
        }
    }

    fun recoveryThemeData() {
    }

    fun updateDefaultTheme(backgroundColor: Int, foregroundColor: Int) {
        val theme = Theme(backgroundColor, foregroundColor, backgroundColor, backgroundColor, backgroundColor)
        _currentTheme.value = theme
        currentThemeId = trackId
        PlayPageDataManager.getInstance().setThemeColor(theme.foregroundColor, theme.backgroundColor, theme.bottomBarColor, theme.originalColor)
    }

    fun updateMediaTheme(theme: Theme, isVideo: Boolean) {
        Logger.d(TAG, "updateMediaTheme = ${Integer.toHexString(theme.backgroundColor)}; isVideo = $isVideo")
        themeRepository.updateMediaColor(theme, isVideo)?.also {
            _currentTheme.value = it
            currentThemeId = trackId
            PlayPageDataManager.getInstance().setThemeColor(it.foregroundColor, it.backgroundColor, it.bottomBarColor, it.originalColor)
        }
    }

    fun updateSkinTheme(skinColor: Int, isVideo: Boolean) {
        if (_currentTheme.value?.backgroundColor == skinColor && currentThemeId == trackId) {
            Log.d(TAG, "updateSkinTheme; no change")
            return
        }

        val theme = Theme(
            skinColor,
            skinColor,
            skinColor,
            skinColor,
            skinColor
        )
        themeRepository.updateMediaColor(theme, isVideo)?.also {
            _currentTheme.value = it
            currentThemeId = trackId
            PlayPageDataManager.getInstance().setThemeColor(it.foregroundColor, it.backgroundColor, it.bottomBarColor, it.originalColor)
        }
    }


    fun updateThemeByAd(foregroundColor: Int, color: Int, bottomColor: Int) {
        Logger.d(TAG, "updateThemeByAd = ${Integer.toHexString(color)}")
        themeRepository.updateAdColor(color, foregroundColor, bottomColor)?.also {
            _currentTheme.value = it
            currentThemeId = lastThemeId
            PlayPageDataManager.getInstance().setThemeColor(it.foregroundColor, it.backgroundColor, bottomColor, it.originalColor)
        }
    }

    fun updateStateFromPlayer() {
        // 比较当前播放器的声音和播放页声音的点赞是否状态一致
        val info = soundInfoLiveData.value ?: return
        val track = info.trackInfo2TrackM() ?: return
        val playableModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound
        if (playableModel is Track) {
            val curPlayTrack = playableModel
            if (curPlayTrack.dataId == track.dataId) {
                if (track.isLike != curPlayTrack.isLike) {
                    track.isLike = curPlayTrack.isLike
                    track.favoriteCount = curPlayTrack.favoriteCount
                    if (info.otherInfo != null) {
                        info.otherInfo!!.isLike = curPlayTrack.isLike
                        info.otherInfo!!.likeCount = curPlayTrack.favoriteCount
                    }
                }
            }
        }
    }

    fun clearPlayFragmentArgs() {
        playModeLiveData.value = null
    }

    fun parsePlayFragmentArgs(bundle: Bundle) {
        isFromBack = bundle.getBoolean(PlayerManager.BUNDLE_KEY_PLAY_CURRENT_FROM_BACK_PRESS, false)
        if (isFromBack) {
            Log.d(TAG, "back pressed，ignore")
            return
        }
        val focusTabType = PlayPageTabUtil.checkPlayPageTabType(
            TempDataManager.getInstance()
                .getInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, PlayPageTab.TYPE_NONE)
        )

        isFromSketchChannel = bundle.getBoolean(VideoPlayParamsBuildUtil.KEY_PLAY_FROM_VIDEO_CHANNEL)

        val isForceToAudio = bundle.getBoolean(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TO_AUDIO)
        val isForceToComment = bundle.getBoolean(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TO_COMMENT_FORCE)
        setIting(bundle.getString(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SCHEME), isForceToComment)

        fromPlayBar = bundle.getInt(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT) == SharedConstant.CHANNEL_PLAY_BAR
        if (fromPlayBar) {
            if (trackId == 0L) { //冷启动
                if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_USER_PLAY_LAST_IS_AUDIO)) {
                    Log.d(TAG, "肚脐眼：冷启动； 音频模式")
                    playModeLiveData.value = XPlayModeArgument(isSound = true)
                } else {
                    Log.d(TAG, "肚脐眼：冷启动； 目标视频模式")
                    pendingMode(-1L)
                }
            }
            return
        }


        val isContinuePlay = bundle.getBoolean(BundleKeyConstants.KEY_IS_CLICK_CONTINUS_PLAY)
        if (isContinuePlay) {
            val trackId = bundle.getLong(BundleKeyConstants.KEY_IS_CLICK_CONTINUS_PLAY_TRACK)
            if (trackId > 0L) {
                val temp_albumId = bundle.getLong(BundleKeyConstants.KEY_IS_CLICK_CONTINUS_PLAY_ALBUM)
                val isLastVideo = XUtils.isVideoModeLastShow(trackId)
                when (isLastVideo) {
                    true -> {
                        val continueVideoId = trackId
                        val albumId = temp_albumId.takeIf { it > 0 }
                            ?: soundInfoLiveData.value?.let {
                                if (it.trackInfo?.trackId == continueVideoId) {
                                    it.albumInfo?.albumId?: 0L
                                } else {
                                    0L
                                }
                            }?: 0L

                        if (albumId > 0L) {
                            //打开普通视频参数齐备，直接处理
                            Log.d(TAG, "续播-视频模式； trackId = $trackId, albumId = $albumId")
                            playModeLiveData.value = XPlayModeArgument(
                                isFromPlayBar = false,
                                isVideo = true,
                                videoArgument = VideoArgument(
                                    videoId = continueVideoId,
                                    albumId = albumId
                                )
                            )
                            return
                        }

                        //videoId 有，但是专辑 id 缺失，需要等 soundInfo 更新后补偿
                        Log.w(TAG, "续播 - 目标视频模式； trackId = $trackId, albumId = 没有")
                        pendingTasks.add(PendingTask(continueVideoId) {
                            val pendingAlbumId = it.albumInfo?.albumId ?: 0L
                            if (pendingAlbumId > 0L) {
                                playModeLiveData.value = XPlayModeArgument(
                                    isVideo = true,
                                    videoArgument = VideoArgument(
                                        videoId = continueVideoId,
                                        albumId = pendingAlbumId
                                    )
                                )
                            } else {
                                Log.e(TAG, "视频 pendingAlbumId 不合法, 忽略处理")
                            }
                            true
                        })
                    }
                    false -> {
                        Log.d(TAG, "续播 - 音频模式； trackId = $trackId")
                        playModeLiveData.value = XPlayModeArgument(
                            isSound = true
                        )
                    }
                    else -> {
                        Log.w(TAG, "续播 - 模式未知； trackId = $trackId")
                        pendingMode(trackId)
                    }
                }
                return
            }
        }

        //处理视频参数
        var videoTrack: Track? = bundle.getParcelable(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_TRACK)
        if (focusTabType == PlayPageTab.TYPE_VIDEO) {
//            playModeLiveData.value = XPlayModeArgument(
//                isFromPlayBar = false,
//                isVideo = true,
//                videoArgument = VideoArgument(
//                    videoId = -1,
//                    albumId = -1,
//                    cover = bundle.getString(VideoPlayParamsBuildUtil.KEY_PLAY_VIDEO_COVER)
//                )
//            )
//            return

            val itingTrackId = TempDataManager.getInstance().getLong(TempDataManager.DATA_PLAY_PAGE_OUTER_ITING_TRACK_ID)
            if (itingTrackId > 0L) {
                videoTrack = TrackM().apply { dataId = itingTrackId }
            } else {
                val currentSound: PlayableModel? = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound()
                if (currentSound is Track && currentSound.kind == PlayableModel.KIND_TRACK) {
                    videoTrack = currentSound
                }
            }
        }
        if (videoTrack != null) {
            val videoId = videoTrack.dataId
            if (videoId <= 0L) {
                Log.e(TAG, "视频缺失关键参数 videoId, 忽略处理")
            } else {
//                loadVideoInfo(videoId)
            }
            val albumId = videoTrack.album?.albumId?.takeIf { it > 0L }
                ?: bundle.getLong(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_ALBUM_ID).takeIf { it > 0 }
                ?: soundInfoLiveData.value?.let {
                    if (it.trackInfo?.trackId == videoId) {
                        it.albumInfo?.albumId?: 0L
                    } else {
                        0L
                    }
                }?: 0L

            if (albumId > 0L) {
                //打开普通视频参数齐备，直接处理
                PlayModeSwitcher.savePlayModeForAlbum(albumId, PlayModeSwitcher.PlayMode.VIDEO)
                playModeLiveData.value = XPlayModeArgument(
                    isFromPlayBar = false,
                    isVideo = true,
                    videoArgument = VideoArgument(
                        videoId = videoId,
                        albumId = albumId,
                        cover = bundle.getString(VideoPlayParamsBuildUtil.KEY_PLAY_VIDEO_COVER)
                    )
                )
                return
            }
            //videoId 有，但是专辑 id 缺失，需要等 soundInfo 更新后补偿
            pendingTasks.add(PendingTask(videoId) {
                val pendingAlbumId = it.albumInfo?.albumId ?: 0L
                if (pendingAlbumId > 0L) {
                    PlayModeSwitcher.savePlayModeForAlbum(pendingAlbumId, PlayModeSwitcher.PlayMode.VIDEO)
                    playModeLiveData.value = XPlayModeArgument(
                        isFromPlayBar = false,
                        isVideo = true,
                        videoArgument = VideoArgument(
                            videoId = videoId,
                            albumId = pendingAlbumId
                        )
                    )
                } else {
                    Log.e(TAG, "视频 pendingAlbumId 不合法, 忽略处理")
                }
                true
            })
            return
        }


        if (isForceToAudio) {
            val trackId = bundle.getLong("trackId")
            pendingAudio(trackId)
            return
        }

        pendingMode(-1L)
    }

    private fun pendingAudio(trackId: Long) {
        val soundInfo = soundInfoLiveData.value
        if (trackId == soundInfo?.trackInfo?.trackId) {
            //current sound
            if (!PlayPageTabUtil.isPlayY(soundInfo)) {
                playModeLiveData.value = XPlayModeArgument(
                    isSound = true
                )
                return
            }

            pendingTasks.add(PendingTask(trackId) {
                if (!it.fromPlayer) {
                    val pendingAlbumId = it.albumInfo?.albumId ?: 0L
                    val isVideo = soundInfo.trackInfo?.isVideo == true
                    if (PlayPageTabUtil.isPlayY(soundInfo) && isVideo
                        &&  PlayModeSwitcher.albumPlayMode(pendingAlbumId) == PlayModeSwitcher.PlayMode.VIDEO) {
                        Log.d(TAG, "PendingTask； 视频模式 trackId = $trackId")
//                    PlayModeSwitcher.savePlayModeForAlbum(pendingAlbumId, PlayModeSwitcher.PlayMode.VIDEO)
                        playModeLiveData.value = XPlayModeArgument(
                            isVideo = true,
                            videoArgument = VideoArgument(
                                videoId = trackId,
                                albumId = pendingAlbumId
                            )
                        )

                    } else {
                        playModeLiveData.value = XPlayModeArgument(
                            isSound = true
                        )
                    }
                    true
                } else {
                    false
                }
            })

        }
    }

    private fun pendingMode(trackId: Long) {
        Log.d(TAG, "模式未知； trackId = $trackId")
        val isVideoFirst =  MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_SETTING_VIDEO_PLAY_IMMERSIVE, true)
        if (!isVideoFirst) {
            Log.d(TAG, "视频优先设置关闭; 直接到音频")
            playModeLiveData.value = XPlayModeArgument(isSound = true)
            return
        }

        pendingTasks.add(PendingTask(trackId) {
            if (!it.fromPlayer) {
                val pendingAlbumId = it.albumInfo?.albumId ?: 0L
                //在模式未指定情况下，仅短剧优先播视频
                if (it.trackInfo?.isVideo == true && pendingAlbumId > 0L && it.otherInfo?.hasAdSkin != true
                    && (it.albumInfo?.type == 32
                            || PlayPageTabUtil.isPlayY(it) && PlayModeSwitcher.albumPlayMode(pendingAlbumId) == PlayModeSwitcher.PlayMode.VIDEO)
                    ) {
                    Log.d(TAG, "PendingTask； 视频模式 trackId = $trackId")
//                    PlayModeSwitcher.savePlayModeForAlbum(pendingAlbumId, PlayModeSwitcher.PlayMode.VIDEO)
                    playModeLiveData.value = XPlayModeArgument(
                        isVideo = true,
                        videoArgument = VideoArgument(
                            videoId = trackId,
                            albumId = pendingAlbumId
                        )
                    )
                } else {
                    Log.d(TAG, "PendingTask； 音频模式 trackId = $trackId")
                    playModeLiveData.value = XPlayModeArgument(
                        isSound = true
                    )
                }
                true
            } else {
                false
            }
        })
    }

    override fun onCleared() {
        super.onCleared()
        soundInfoLiveData.removeObserver(internalSoundInfoObserver)
        dynamicDataRepository.onCleared()
        scope.cancel()
    }
}