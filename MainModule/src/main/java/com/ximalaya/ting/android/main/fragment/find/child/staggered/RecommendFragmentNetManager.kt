package com.ximalaya.ting.android.main.fragment.find.child.staggered

import android.graphics.Color
import android.text.TextUtils
import android.util.Log
import com.google.gson.Gson
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.fragment.BaseFragment
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelper
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.dialog.interest.manager.HomeChooseInterestManager
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.RecommendPreLoadOptManager
import com.ximalaya.ting.android.host.manager.TopBgForSceneManager
import com.ximalaya.ting.android.host.manager.account.NewUserLoginManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.ad.AdPreviewManager
import com.ximalaya.ting.android.host.manager.adfree.AdResourceInfo
import com.ximalaya.ting.android.host.manager.adfree.CommonAdFreeManager
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.iting.ITingOutsideSourceManager
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.ad.AdReportModel
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd
import com.ximalaya.ting.android.host.model.ad.BannerModel
import com.ximalaya.ting.android.host.model.base.BaseModel
import com.ximalaya.ting.android.host.model.childprotect.ChildProtectModel
import com.ximalaya.ting.android.host.model.homepage.HomePageTabTheme
import com.ximalaya.ting.android.host.play.util.PlayDataUtils
import com.ximalaya.ting.android.host.service.xmremotecontrol.XmRemoteControlUtil
import com.ximalaya.ting.android.host.util.GrowthItingUtil
import com.ximalaya.ting.android.host.util.VersionUtil
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil
import com.ximalaya.ting.android.host.util.common.SceneInfoUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.kt.realString
import com.ximalaya.ting.android.host.util.template.XmTemplateDetail
import com.ximalaya.ting.android.host.view.BannerView
import com.ximalaya.ting.android.host.view.BaseBannerView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.SceneAnimalStatusUtils
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain
import com.ximalaya.ting.android.main.fragment.find.HomePageFragment
import com.ximalaya.ting.android.main.fragment.find.child.BigScreenAdManager
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageGaiaXManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageVirtualViewManager
import com.ximalaya.ting.android.main.fragment.find.child.recommendad.RecommendCenterBigPicAdManager
import com.ximalaya.ting.android.main.fragment.find.child.rn.util.SceneStyleUtil
import com.ximalaya.ting.android.main.fragment.myspace.util.HomePreviewUtil
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager.mode
import com.ximalaya.ting.android.main.model.rec.RecommendAlbumItem
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendIndexFeedVideoItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendKidAllLikeItem
import com.ximalaya.ting.android.main.model.rec.RecommendLiveItem
import com.ximalaya.ting.android.main.model.rec.RecommendModelNew
import com.ximalaya.ting.android.main.model.rec.RecommendModuleItem
import com.ximalaya.ting.android.main.model.rec.RecommendMyClubModel
import com.ximalaya.ting.android.main.model.rec.RecommendNewUserBenefitItem
import com.ximalaya.ting.android.main.model.rec.RecommendNotShowFeeds
import com.ximalaya.ting.android.main.model.rec.RecommendRankListItem
import com.ximalaya.ting.android.main.model.rec.RecommendRankingItem
import com.ximalaya.ting.android.main.model.rec.RecommendSpecialItem
import com.ximalaya.ting.android.main.model.rec.RecommendTrackItem
import com.ximalaya.ting.android.main.model.rec.UserGiftPendantModel
import com.ximalaya.ting.android.main.model.recommend.RecommendDiscoveryM
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.HomeModuleTitleUtils
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.util.HomeTraceItemUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil
import com.ximalaya.ting.android.template.TemplateManager
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.ManualExposureHelper
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.xmutil.NetworkType
import org.json.JSONArray
import org.json.JSONObject
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.util.concurrent.ConcurrentHashMap


/**
 * Created by changle.fang on 2021/10/26.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class RecommendFragmentNetManager {

    companion object {
        val instance = SingletonHolder.holder
        var sMotFreshListMap: ConcurrentHashMap<String, JSONObject>? = ConcurrentHashMap()
        // 直播曝光列表
        val liveExposureIds = mutableSetOf<Long>()
    }

    private object SingletonHolder {
        val holder = RecommendFragmentNetManager()
    }

    var mFragment: RecommendFragmentStaggered? = null
    var mIsRecommendDataLoading = false
    var mIsFirstLoad = true
    var mRecommendData: RecommendModelNew? = null
        set(value) {
            value?.run {
                SceneStyleUtil.checkNewSceneCardStyle(this)
            }
            field = value
        }
    var mContext = BaseApplication.getMyApplicationContext()
    var mNewestPageClicked = false // 最新加载的一页是否有点击过，只算专辑条、声音条和直播条的点击。
    var mHeaderUpdateCount = 0 // header数据的刷新次数
    var mHotPlayModuleCount = 0 // 热播榜模块个数
    var sLastPassFirstInstallParamTime = 0L
    var mLastStartTs = 0L
    var mLastRequestUidForStartTs = -1L
    var mNeedClearModuleHis = false // 账号状态变化时，或者接口返回清除时，下次请求接口清除模块历史数据
    var mLastSavedBody = arrayListOf<RecommendItemNew>()
    var mRefreshAddedItemNum = -1
    private var isFirstLoadPage = true
    var loadedAdCount = 0
    var loadedAnchorAdCount = 0
    var loadedCenterBigAdCount = 0
    var loadedInternalAnchorAdCount = 0
    var mChildProtectBannerData: RecommendItemNew? = null
    var mRecommendModelNewFromV4: RecommendModelNew? = null
    var mRecommendModelNewFromFocus: RecommendModelNew? = null
    var pageNumber = 1
    var mXmRequestId: String? = null
    var mPreLoadLocalRecommendData: RecommendModelNew? = null
    var mRefreshTraceHelper: TraceHelper? = null
    var refreshLoadMillis = 0L

    fun loadDataFromNet(
        isLoadMore: Boolean = false,
        onlyBody: Boolean = false,
        isFirstRequestAd: Boolean = false,
        callBack: IDataCallBack<RecommendModelNew?>? = null
    ) {
        loadDataFromNet(isLoadMore, onlyBody, isFirstRequestAd, false, callBack)
    }

    fun loadDataFromNet(
        isLoadMore: Boolean = false,
        onlyBody: Boolean = false,
        isFirstRequestAd: Boolean = false,
        isUserRefresh: Boolean = false, // 用户主动刷新
        callBack: IDataCallBack<RecommendModelNew?>? = null
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.isDebug()) {
            HomeRecommendPageLoadingOptimizationManager.log("____isLoadMore___" + isLoadMore + "isLoadMore___" + onlyBody + "____" + Log.getStackTraceString(Throwable()))
        }
        if (mIsRecommendDataLoading) {
            return
        }
        mRefreshTraceHelper = if (!isLoadMore && !isFirstRequestAd) {
            TraceHelper("首页刷新")
        } else {
            null
        }
        mRefreshTraceHelper?.postPageStartNode()
        logRecommendLoad("loadDataFromNet_start")
        TraceHelperManager.postNode(
                TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED,
                "准备请求数据")
        loadChildProtectBannerData(isLoadMore)
        mIsRecommendDataLoading = true
        val params: MutableMap<String, String?> = ConcurrentHashMap()
        val paramsQuery: MutableMap<String, String> = ConcurrentHashMap() // 拼在url后面的参数
        paramsQuery["appid"] = "0"
        paramsQuery["deviceId"] = DeviceUtil.getDeviceToken(mContext)
        paramsQuery["operator"] = NetworkType.getOperator(mContext).toString() + ""
        val string = SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_CODE)
        if (!TextUtils.isEmpty(string)) {
            paramsQuery[HttpParamsConstants.PARAM_CODE] = string
            params[HttpParamsConstants.PARAM_CODE] = string
        }
        paramsQuery["version"] = DeviceUtil.getVersion(mContext)
        if (UserInfoMannage.hasLogined()) {
            paramsQuery["uid"] = UserInfoMannage.getUid().toString() + ""
        }
        paramsQuery["scale"] = "1"
        paramsQuery["categoryId"] = "" + BannerView.RECOMMEND_CATEGORY_ID
        paramsQuery["device"] = "android"
        paramsQuery["network"] = CommonRequestM.getInstanse().netWorkType
        paramsQuery["xt"] = System.currentTimeMillis().toString()
        paramsQuery["channel"] = CommonRequestM.getInstanse().umengChannel
        if (ConstantsOpenSdk.isDebug) {
            // 方便mock数据
            paramsQuery["onlyBody"] = onlyBody.toString()
            paramsQuery["loadMore"] = isLoadMore.toString()
        }
        val offset = mRecommendData?.offset ?: 0
        params["streamOffSet"] = offset.toString()
        val showModules = mRecommendData?.showModules ?: ""
        if (!(TextUtils.isEmpty(showModules) || TextUtils.equals("null", showModules))) {
            params["showModules"] = showModules
        }
        params["onlyBody"] = onlyBody.toString()
        params["displayMode"] = RecommendFragmentAbManager.getDisplayMode()
        params["click"] = mNewestPageClicked.toString()

        // 请求的时候添加原始渠道
        if (!TextUtils.isEmpty(DeviceUtil.getOriginalChannel(mContext))) {
            params["originalChannel"] = DeviceUtil.getOriginalChannel(mContext)
        }
        if (BaseUtil.isFoldScreen(ToolUtil.getCtx())) {
            params["isFoldDevice"] = "1"
        }
        params["size"] = "18"

        val strInterestCard = MMKVUtil.getInstance().getString(
                PreferenceConstantsInHost.KEY_CUSTOMIZED_INTEREST_CARD_MODEL_NEW)
        if (!TextUtils.isEmpty(strInterestCard)) {
            try {
                val jsonObject = JSONObject(strInterestCard)
                if (jsonObject.has("gender")) {
                    params["gender"] = jsonObject.optString("gender")
                }
                if (jsonObject.has("ageRange")) {
                    params["ageRange"] = jsonObject.optString("ageRange")
                }
                if (jsonObject.has("interestedCategories")) {
                    params["interestedCategories"] = jsonObject.optString("interestedCategories")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        // 传这个字段主要是为了在刚设置完后服务端可能拿不到的情况做备用，所以只在新安装时传，后面不传
        if (ToolUtil.isFirstInstallApp(mContext)) {
            val newCodes = MmkvCommonUtil.getInstance(mContext).getStringCompat(
                    PreferenceConstantsInHost.KEY_CHOOSE_LIKE_SELECTED_ALL_CODES)
            if (!TextUtils.isEmpty(newCodes) && newCodes.length < 512) {
                params["newCodes"] = newCodes
            }
        }

        // 新安装首次启动时传一下firstInstall参数，方便服务端做处理，由于可能存在一些强制刷新的操作，所以30秒内请求都带一下这个参数
        if (ToolUtil.isFirstInstallApp(mContext)) {
            val time = System.currentTimeMillis()
            if (sLastPassFirstInstallParamTime == 0L || time - sLastPassFirstInstallParamTime < 30 * 1000) {
                sLastPassFirstInstallParamTime = time
                paramsQuery["firstInstall"] = "2"
            }
        }
        // 登录账号后，回到首页会先绘制，这时候又记录了模块数据，所以请求之前需要清除
        if (mNeedClearModuleHis) {
            mFragment?.mAdapter?.clearHasShowModules()
            mLastSavedBody?.clear()
            mNeedClearModuleHis = false
        }

        if (RecommendFragmentAbManager.MODE_MIX == mode) {
//            var dynamicParams: String? = ""
//            val templateInfo = TemplateManager.getInstance()
//                    .getTemplateInfo(RecommendPageVirtualViewManager.BUSINESS_NAME)
//            dynamicParams = (templateInfo?.templateDetail as? XmTemplateDetail)?.dynamicParams
//            if (!dynamicParams.isNullOrEmpty()) {
//                params["dynamicParams"] = dynamicParams
//            }
        }
        var playDataParams = PlayDataUtils.getAndRefreshSceneRequestParams(false)
        params.putAll(playDataParams)
//        params["appStartStatus"] = appStartStatus.toString()
//        if (appStartStatus == 1) {
//            appStartStatus = 0
//        }
        if (!onlyBody) {
            pageNumber = 1
            ScenePlayDataUtil.refreshRequestParams(playDataParams)
        }
        params["pageNum"] = pageNumber.toString()
        params["chasingGroup"] = ABTest.getString(CConstants.Group_toc.ITEM_CHASING_PRODUCT, "0")

        mFragment?.mFeedAdManager?.let {
            paramsQuery["screenId"] = it.screenId
            params["screenId"] = it.screenId
        }
        // 增加搜索相关的字段，确保和搜索逻辑一致。 只要是为了焦点标签模块中出搜索标签
        // com.ximalaya.ting.android.search.out.SearchModuleUtils
        val history = MMKVUtil.getInstance()
            .getString(PreferenceConstantsInHost.KEY_RECENT_SEARCH_WORDS_RECORD)
        if (!TextUtils.isEmpty(history)) {
            try {
                params["history"] = URLEncoder.encode(history, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        val albumExposureRecord = MMKVUtil.getInstance()
            .getString(PreferenceConstantsInOpenSdk.KEY_RECENT_ALBUM_EXPOSURE_RECORD)
        if (!TextUtils.isEmpty(albumExposureRecord)) {
            try {
                params["albumExposure"] = URLEncoder.encode(albumExposureRecord, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        val tracPlayRecord = MMKVUtil.getInstance()
            .getString(PreferenceConstantsInOpenSdk.KEY_RECENT_TRACK_PLAY_RECORD)
        if (!TextUtils.isEmpty(tracPlayRecord)) {
            try {
                params["trackPlay"] = URLEncoder.encode(tracPlayRecord, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }

        // 带上实验分组标志
        params["searchIntentionJumpParam"] = AdMakeVipLocalManager.getInstance().abExpScript ?: ""

        // 带上畅听人群画像标志 9.1.96版本
        params["sceneInfo"] = SceneInfoUtil.getSceneInfo()

        if (mLastRequestUidForStartTs != UserInfoMannage.getUid()) {
            mLastRequestUidForStartTs = UserInfoMannage.getUid()
            if (UserInfoMannage.hasLogined()) {
                mLastStartTs = MMKVUtil.getInstance().getLong("recommendLastStartTs_" + UserInfoMannage.getUid(), System.currentTimeMillis())
                MMKVUtil.getInstance().saveLong("recommendLastStartTs_" + UserInfoMannage.getUid(), System.currentTimeMillis())
            } else {
                mLastStartTs = System.currentTimeMillis()
            }
            sMotFreshListMap?.clear()
        } else if (mLastRequestUidForStartTs > 0L) {
            mLastStartTs = MMKVUtil.getInstance().getLong("recommendLastStartTs_" + UserInfoMannage.getUid(), System.currentTimeMillis())
            MMKVUtil.getInstance().saveLong("recommendLastStartTs_" + UserInfoMannage.getUid(), System.currentTimeMillis())
            if (sMotFreshListMap != null && sMotFreshListMap!!.size > 0) {
                val motFreshListArray = JSONArray()
                sMotFreshListMap!!.keys.forEach {
                    motFreshListArray.put(sMotFreshListMap!![it])
                }
                params["motFreshList"] = motFreshListArray.toString()
            }
        }
        if (mLastStartTs > 0L) {
            // 上次冷启动时间
            params["lastStartTs"] = mLastStartTs.toString()
        }
        val growthContentId = GrowthItingUtil.getGrowthContentId()
        if (!growthContentId.isNullOrEmpty()) {
            params["hasIting"] = "true"
            params["itingContentId"] = growthContentId
        }
        // 上报本地缓存ab值
        params["clientLocalCache"] = RecommendFragmentAbManager.getClientLocalCache()
        if (isLoadMore) {
            mRecommendData?.cardStreamParam?.let {
                if (it.isNotEmpty()) {
                    params["cardStreamParam"] = it
                }
            }
        }
        if (!isLoadMore && !onlyBody) {
            resetCenterBigPicAdOptimization()
            val adFreeType = CommonAdFreeManager.addFreeAdTypeRequestParam(AdResourceInfo(AppConstants.AD_POSITION_NAME_STAGGERED_HOME_AD, null))
            var filterModuleType = "focus"
            filterModuleType = if (adFreeType == null) {
                if (isHomeMixFocusControlNew()) {
                    "$filterModuleType"
                } else {
                    ""
                }
            } else {
                if (isHomeMixFocusControlNew()) {
                    "$filterModuleType,$adFreeType"
                } else {
                    adFreeType
                }
            }
            if (filterModuleType.isNotEmpty()) {
                params["filterModuleType"] = filterModuleType
            }
            if (isHomeMixFocusControlNew()) {
                clearRecommendItemNewCache()
                MainCommonRequest.getRecommendFeedOnlyFocusPic(paramsQuery, params,
                    AnchorAlbumAd.sRecommendRecordAd, mFragment?.mAdapter?.getHasShowModuleIds(isLoadMore, mLastSavedBody),
                    if (!isLoadMore) getNotShowFeeds() else null,
                    object : IDataCallBack<RecommendModelNew> {
                        override fun onSuccess(recommendModel: RecommendModelNew?) {
                            if (mFragment?.canUpdateUi() != true) {
                                return
                            }
                            mRecommendModelNewFromFocus = recommendModel
                            mergeRecommendItemNew()
                            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                                if (mRecommendData?.body?.isNotEmpty() == true) {
                                    setRecommendDataForView()
                                }
                            } else {
                                setRecommendDataForView()
                            }
                        }

                        override fun onError(code: Int, message: String?) {
                        }
                    })
            }
        }

        SceneInfoUtil.addNewFreeCrowdToParams(params)
        params["isAppleReview"] = "false"
        params["refreshStatus"] = if (isUserRefresh) {
            "1"
        } else {
            "0"
        }
        params["liveExposure"] = liveExposureIds.joinToString(",")
        liveExposureIds.clear()

        // 上报高德的授权信息
        val map = XmRemoteControlUtil.getAuthorizeBindDataForHome()
        var outAct = ""
        if (!map.isNullOrEmpty()) {
            try {
                outAct = Gson().toJson(map)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        params["outAct"] = outAct

        val isFromOutsideSource = if (isLoadMore) false else {
            ITingOutsideSourceManager.addOutsideSourceParams(mContext, params)
        }

        // 添加预览参数
        HomePreviewUtil.homeRequestMap?.forEach {
            params[it.key] = it.value
        }

        MainCommonRequest.getRecommendFeedStreamV4(paramsQuery, params,
            AnchorAlbumAd.sRecommendRecordAd, mFragment?.mAdapter?.getHasShowModuleIds(isLoadMore, mLastSavedBody),
            if (!isLoadMore) getNotShowFeeds() else null,
                object : IDataCallBack<RecommendModelNew> {
                    override fun onSuccess(recommendModel: RecommendModelNew?) {
                        logRecommendLoad("loadDataFromNet_onSuccess")
                        var originBodySize = recommendModel?.body?.size ?: 0
                        var originHeaderSize = recommendModel?.header?.size ?: 0
                        RecommendFragmentPageErrorManager.checkAndUploadEmptyError(recommendModel, isLoadMore)
                        if (!isLoadMore) {
                            val act = BaseApplication.getMainActivity() as? MainActivity?
                            act?.let {
                                it.cancelHomeTabRefreshDrawable()
                            }
                            if (!onlyBody) {
                                refreshLoadMillis = System.currentTimeMillis()
                            }
                            HomeRealTimeTraceUtils.resetData()
                        }
                        var lastBodySize = mRecommendData?.body?.size ?: 0
                        var lastDataHasBody = lastBodySize > 0
                        pageNumber ++
                        mIsRecommendDataLoading = false
                        callBack?.onSuccess(recommendModel)
                        val fragment = mFragment ?: return
                        val isFromRefresh = fragment.mIsRefreshing
                        fragment.mIsRefreshing = false
                        fragment.mIsShowRefreshState = false
                        // 返回为空也要更新cardStreamParam
                        mRecommendData?.cardStreamParam = recommendModel?.cardStreamParam

                        checkAutoScroll(isFromRefresh, isFromOutsideSource)

                        TraceHelperManager.postNode(
                                TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED,
                                "数据加载完成")
                        fragment.doAfterAnimation {
                            var isNoContent = recommendModel == null || recommendModel.ret != BaseModel.SUCCESS
                                    || (recommendModel.header.isNullOrEmpty() && recommendModel.body.isNullOrEmpty())
                            val headerSize = recommendModel?.header?.size ?: 0
                            val bodySize = recommendModel?.body?.size ?: 0
                            val totalSize = headerSize + bodySize
                            if (!isLoadMore && totalSize < RecommendFragmentPageErrorManager.getFirstScreenMinCardNum()
                                            && RecommendFragmentAbManager.is2024NewRecommendFragment()) {
                                isNoContent = true
                                Logger.e("cf_test", "服务端返回数据太少，并且是下拉刷新，丢弃该数据")
                            }
                            if (isNoContent) {
                                if (fragment.canUpdateUi()) {
                                    if (mRecommendData == null ||
                                            (mRecommendData?.header.isNullOrEmpty() && mRecommendData?.body.isNullOrEmpty())) {
                                        fragment.onPageLoadingCompleted(BaseFragment.LoadCompleteType.NOCONTENT)
                                    }
                                    fragment.mRecyclerView?.onRefreshComplete(true)
                                    if (isFirstRequestAd) {
                                        loadDataFromLocal()
                                    }
                                }
                                notifyTraceFinish()
                                return@doAfterAnimation
                            }
                            // 有数据返回  移除高德授权信息
                            XmRemoteControlUtil.removeAuthorizeBindDataForHome()

                            fragment.mUseLocalData = false
                            if (recommendModel?.isClearModuleHis == true) {
                                mNeedClearModuleHis = true
                            }

                            mNewestPageClicked = false
                            val giftTag1 = recommendModel?.giftTag ?: 0
                            if (giftTag1 >= 0 && UserInfoMannage.hasLogined()) {
                                val prefKey = getGiftTagPrefKey(UserInfoMannage.getUid())
                                SharedPreferencesUtil.getInstance(mContext).saveInt(prefKey, giftTag1)
                            }

                            // 更新用户状态是否是新用户
                            VersionUtil.setNewUser(recommendModel!!.isNewUser)
                            NewUserLoginManager.setIsNewUser(recommendModel.isNewUser)
                            if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInMain
                                            .KEY_IS_LOGIN_FROM_STRONG_LOGIN_GUIDE_DIALOG, false)) {
                                MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInMain
                                        .KEY_IS_LOGIN_FROM_STRONG_LOGIN_GUIDE_DIALOG, false)
                                if (!recommendModel.isNewUser) {
                                    CustomToast.showToast(R.string.main_text_can_not_receive_new_user_gift)
                                }
                            }

                            if (!recommendModel.header.isNullOrEmpty()) {
                                sMotFreshListMap?.clear()
                                for (headerIndex in recommendModel.header.indices) {
                                    val headerItem = recommendModel.header[headerIndex]
                                    if (headerItem.itemType == RecommendItemNew.RECOMMEND_ITEM_CHASING_FOR_UPDATE ||
                                        headerItem.itemType == RecommendItemNew.RECOMMEND_ITEM_MOT_FOLLOW_UPDATE_LIST ||
                                        headerItem.itemType == RecommendItemNew.RECOMMEND_ITEM_MOT_AUTHOR_UPDATE_LIST) {
                                        if (headerItem.item is RecommendCommonItem && !TextUtils.isEmpty((headerItem.item as RecommendCommonItem).ext?.extraInfo?.motName)) {
                                            val jsonObject = JSONObject()
                                            jsonObject.put("motName", (headerItem.item as RecommendCommonItem).ext!!.extraInfo!!.motName)
                                            jsonObject.put("motPosition", (headerIndex + 1))
                                            sMotFreshListMap?.put((headerItem.item as RecommendCommonItem).ext!!.extraInfo!!.motName!!, jsonObject)
                                        }
                                    }
                                }

                                HomeModuleTitleUtils.checkRequestAiTitle(recommendModel, isLoadMore, fragment)
                            }

                            val isFirstLoad = mRecommendData == null || mIsFirstLoad
                            saveLastBody(recommendModel)
                            var needReloadList = false // 是否整体刷新列表
                            if (mRecommendData == null || isFirstLoad) { // 第一次加载数据
                                mRecommendData = recommendModel
                                mRecommendModelNewFromV4 = recommendModel
                                mergeRecommendItemNew()
                                mHeaderUpdateCount++
                                mIsFirstLoad = false
                                saveDataToLocal(recommendModel.jsonStr)
                                needReloadList = true
                                RecommendFragmentRealTimeFeedManager.instance.clearRequestTimeMap()
                            } else {
                                if (recommendModel.code.isNotEmpty()) {
                                    mRecommendData?.code = recommendModel.code
                                }
                                mRecommendData?.offset = recommendModel.offset
                                mRecommendData?.showModules = recommendModel.showModules

                                if ((!recommendModel.header.isNullOrEmpty() || !recommendModel.body.isNullOrEmpty()) && !onlyBody) {
                                    mRecommendModelNewFromV4 = recommendModel
                                    mergeRecommendItemNew()
                                    mHeaderUpdateCount++
                                    needReloadList = true
                                }
                                if (!onlyBody) {
                                    mRecommendData?.streamOptionInfo = recommendModel.streamOptionInfo
                                    mRecommendData?.streamTitle = recommendModel.streamTitle
                                    mRecommendData?.streamSubTitle = recommendModel.streamSubTitle
                                    mRecommendData?.streamTitleStyle = recommendModel.streamTitleStyle
                                }
                                // 新首页feed流延迟加载
                                if (recommendModel.body.isNullOrEmpty() && !onlyBody && RecommendFragmentAbManager.is2024NewRecommendFragment()) {
                                    if (!recommendModel.header.isNullOrEmpty() && recommendModel.header.size >= 4) {
                                        mRecommendData?.body = recommendModel.body
                                    }
                                }
                                // 新的分页逻辑，header也要追加
                                if (!recommendModel.header.isNullOrEmpty()
                                    && isLoadMore
                                    && RecommendFragmentAbManager.is2024NewRecommendFragment()
                                    && mRecommendData?.body.isNullOrEmpty()) {
                                    mRecommendData?.header?.addAll(recommendModel.header)
                                }
                                if (!recommendModel.body.isNullOrEmpty()) {
                                    if (mRecommendData?.body == null) {
                                        mRecommendData?.body = recommendModel.body
                                    } else {
                                        if (isLoadMore) {
                                            mRecommendData?.body?.addAll(recommendModel.body)
                                        } else {
                                            mRecommendData?.body?.let {
                                                it.clear()
                                                it.addAll(0, recommendModel.body)
                                                fragment.mAdapter?.clearHasShownAdItems()
                                                needReloadList = true
                                            }
                                        }
                                    }
                                }
                            }

                            // 埋点所需数据处理
                            if (mRecommendData != null) {
                                RecommendFragmentStaggered.sUserTrackingAbTest = mRecommendData?.getProfileId() + "|" + mRecommendData?.getBucketId()
                            }

                            if (fragment.canUpdateUi()) {
                                if ((recommendModel?.body != null || recommendModel?.header != null) && !isFirstLoad && !isLoadMore) {
                                    fragment.mRefreshAddedItemNum = originBodySize + originHeaderSize
                                } else {
                                    // 第一次加载不显示更新了多少条内容
                                    fragment.mRefreshAddedItemNum = 0
                                }

                                if (needReloadList) {
                                    fragment.mRecommendData = mRecommendData
                                    setChildProtectBannerData(isLoadMore)
                                    setRecommendDataForView()
                                    if (fragment.getView() != null) {
                                        fragment.mTraceHelper.postPageEndNodeAfterRenderComplete(fragment.getView()) {
                                            HomeRecommendPageLoadingOptimizationManager.onHomeRecommendPageLoading(
                                                false
                                            )
                                            RecommendPreLoadOptManager.onRecommendDataDraw(fragment.activity as? MainActivity, false)
                                            fragment.onPageRenderComplete(true)
                                        }
                                        mRefreshTraceHelper?.postPageEndNodeAfterRenderComplete(fragment.view)
                                    } else {
                                        notifyTraceFinish()
                                    }
                                    HomeChooseInterestManager.loadHomeDataFinish()
                                } else {
                                    val loadMoreList = arrayListOf<Any>()
                                    if (RecommendFragmentAbManager.is2024NewRecommendFragment() && !lastDataHasBody) {
                                        addData(loadMoreList, recommendModel.header, true)
                                        var thisBodySize = recommendModel?.body?.size ?: 0
                                        var thisHeaderSize = recommendModel?.header?.size ?: 0
                                        if (thisBodySize > 0) {
                                            mFragment?.addTittleWithCircle(loadMoreList)
                                            var adapterDataSize = mFragment?.mAdapter?.mListData?.size ?: 0
                                            mFragment?.setFirstBodyItem(adapterDataSize + thisHeaderSize + 1)
                                        }
                                    }
                                    addData(loadMoreList, recommendModel.body, false)
                                    val isRefreshPreData = fixSingleNextLineBug(loadMoreList)
                                    val first =
                                        fragment.mRecyclerView?.findFirstVisiblePosition() ?: -1
                                    val last =
                                        fragment.mRecyclerView?.findLastVisiblePosition() ?: -1
                                    mFragment?.mAdapter?.setData(
                                        loadMoreList, false, isRefreshPreData, first, last
                                    )
                                }
                                fragment.onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK)
                                fragment.mRecyclerView?.onRefreshCompleteDelay(true)
                            } else {
                                notifyTraceFinish()
                            }

                            if (fragment.isRealVisable() && !isLoadMore && !onlyBody) {
                                AdManager.batchAdRecord(mContext, fragment.mFocusImages, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, AppConstants.AD_POSITION_NAME_FOCUS)
                                        .categoryId(BannerView.RECOMMEND_CATEGORY_ID).build())

                                AdManager.batchAdRecord(mContext, fragment?.mMixFocusList, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, AppConstants.AD_POSITION_NAME_FOCUS)
                                        .categoryId(BannerView.RECOMMEND_CATEGORY_ID).build())
                                HandlerManager.postOnUIThreadDelay({ // 焦点图上报新方式
                                    fragment.scrollCheckBannerRecord()
                                }, ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                                        CConstants.Group_ad.ITEM_RECOMMEND_DELAY_CHECK_TIME,
                                        PullToRefreshBase.SMOOTH_SCROLL_DURATION_MS).toLong())
                            }

                            fragment.postOnUiThread {
                                // 修复偶现的线上专辑上报问题
                                if (!HomeTraceItemUtil.isUseNewTrace(fragment)) {
                                    fragment.traceItemViewed()
                                }
                                fragment.mRecyclerView?.let {
                                    ManualExposureHelper.exposureViewsByRequest(fragment, it, !isLoadMore)
                                }
                                if (isFirstLoadPage) {
                                    isFirstLoadPage = false
                                    RecommendFragmentStaggered.mExploreType = 0
                                }
                                if (isFromRefresh && !isFromOutsideSource) {
                                    // 负数表示不滚动，习惯听的位置大于这个数也不滚动，位置从0开始算
                                    val configMaxIndex = ConfigureCenter.getInstance().getInt(
                                        CConstants.Group_toc.GROUP_NAME,
                                        CConstants.Group_toc.ITEM_HOME_AUTO_SCROLL_MAX_NUMBER,
                                        -1
                                    )
                                    if (configMaxIndex >= 0 && configMaxIndex <= (mFragment?.mAdapter?.mListData?.size
                                            ?: 0)
                                    ) {
                                        kotlin.run {
                                            mFragment?.mAdapter?.mListData?.forEachIndexed { index, anyItem ->
                                                if (index <= configMaxIndex &&
                                                    anyItem is RecommendItemNew &&
                                                    (anyItem.itemType == RecommendItemNew.RECOMMEND_ITEM_SCENE_LISTEN_CARD)
                                                ) {
                                                    mFragment?.smoothScroll2Position(index + 1)
                                                    HandlerManager.postOnUIThreadDelay({
                                                        SceneAnimalStatusUtils.hiddenSceneCardAnimal(
                                                            false
                                                        )
                                                    }, 500)
                                                    return@run
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            if (!isLoadMore) {
                                loadHomeAd(isFirstRequestAd)
                            }
                            if (isUserRefresh) {
                                mFragment?.doFreeListenAnimation()
                            }
                        }
                        if (!fragment.mHasUploadPageError) {
                            var pageName = if (RecommendFragmentAbManager.is2024NewRecommendFragment())
                                                RecommendFragmentPageErrorManager.PAGE_NAME_2024
                                            else
                                                RecommendFragmentPageErrorManager.PAGE_NAME_STAGGERED
                            RecommendFragmentPageErrorManager.uploadError(pageName, true)
                            fragment.mHasUploadPageError = true
                        }

                        TraceHelperManager.postNode(
                                TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED,
                                "数据绑定完成")
                    }

                    override fun onError(code: Int, message: String?) {
                        callBack?.onError(code, message)
                        if (!isLoadMore) {
                            val act = BaseApplication.getMainActivity() as? MainActivity?
                            act?.let {
                                it.cancelHomeTabRefreshDrawable()
                            }
                        }
                        logRecommendLoad("loadDataFromNet_" + code + "___" + message)
                        mIsRecommendDataLoading = false
                        val fragment = mFragment ?: return
                        fragment.mIsRefreshing = false
                        fragment.mIsShowRefreshState = false

                        if (fragment.canUpdateUi()) {
                            RecommendPreLoadOptManager.onRecommendRequestDataError(fragment.activity as? MainActivity)
                            fragment.onPageLoadingCompleted(BaseFragment.LoadCompleteType.NETWOEKERROR)
                            if (TextUtils.isEmpty(message)) {
                                CustomToast.showToast("当前无网络，请检查网络后重试", ToastManager.LENGTH_LONG.toLong())
                            } else {
                                CustomToast.showFailToast(message)
                            }
                            if (isFirstRequestAd) {
                                loadDataFromLocal()
                            } else {
                                setRecommendDataForView()
                            }
                            fragment.mRecyclerView?.onRefreshComplete(true)
                        }
                        if (!fragment.mHasUploadPageError) {
                            var pageName = if (RecommendFragmentAbManager.is2024NewRecommendFragment())
                                RecommendFragmentPageErrorManager.PAGE_NAME_2024
                            else
                                RecommendFragmentPageErrorManager.PAGE_NAME_STAGGERED
                            RecommendFragmentPageErrorManager.uploadError(pageName, false, code, message)
                            fragment.mHasUploadPageError = true
                        }
                    }
                })
        if (!onlyBody) {
            loadUserGiftPendant()
        }
    }

    private fun checkAutoScroll(isFromRefresh: Boolean, isFromOutsideSource: Boolean) {
        if (isFromRefresh && !isFromOutsideSource) {
            // 负数表示不滚动，习惯听的位置大于这个数也不滚动，位置从0开始算
            val configMaxIndex = ConfigureCenter.getInstance().getInt(
                CConstants.Group_toc.GROUP_NAME,
                CConstants.Group_toc.ITEM_HOME_AUTO_SCROLL_MAX_NUMBER,
                -1
            )
            if (configMaxIndex >= 0 && configMaxIndex <= (mFragment?.mAdapter?.mListData?.size
                    ?: 0)) {
                SceneAnimalStatusUtils.hiddenSceneCardAnimal(true)
            }
        } else {
            SceneAnimalStatusUtils.hiddenSceneCardAnimal( false)
        }
    }

    private fun isHomeMixFocusControlNew(): Boolean {
        return true
//        return ABTest.getString("home_mix_focus_control", "").equals("new")
    }

    private fun clearRecommendItemNewCache() {
        mRecommendModelNewFromV4 = null
        mRecommendModelNewFromFocus = null
    }

    private fun getRecommendItemType(recommendItemNew: RecommendItemNew): String {
        if (recommendItemNew.itemType != RecommendItemNew.RECOMMEND_ITEM_MODULE) {
            return recommendItemNew.itemType
        }
        if (recommendItemNew.item is RecommendModuleItem) {
            return (recommendItemNew.item as RecommendModuleItem).moduleType
        }
        return ""
    }

    private fun mergeRecommendItemNew() {
//        if (mRecommendModelNewFromV4 != null && BaseFragmentActivity2.sIsDarkMode) {
//            if (mRecommendModelNewFromV4?.header != null && mRecommendModelNewFromV4?.header!!.size > 0) {
//                var recommendItemNew = mRecommendModelNewFromV4?.header!![0]
//                if (RecommendItemNew.RECOMMEND_ITEM_SCENE_LISTEN_CARD_NEW.equals(recommendItemNew.itemType)) {
//                    TopBgForSceneManager.setIsHaveNewSceneCard(true)
//                } else {
//                    TopBgForSceneManager.setIsHaveNewSceneCard(false)
//                }
//            } else {
//                TopBgForSceneManager.setIsHaveNewSceneCard(false)
//            }
//        }
        if (mRecommendModelNewFromV4 != null && mRecommendModelNewFromFocus != null) {
            mRecommendData?.header = mRecommendModelNewFromV4?.header
            if (mRecommendModelNewFromFocus?.header?.isNotEmpty() == true) {
                mRecommendModelNewFromFocus?.header?.get(0)?.let {
                    var isNewZoneStyle = false
                    if (RecommendFragmentTypeManager.getItemViewType(it) == RecommendFragmentTypeManager.VIEW_TYPE_FOCUS) {
                        var bannerModelList: List<BannerModel>? = null
                        if (it.item is RecommendModuleItem
                            && ((it.item as? RecommendModuleItem)?.list is List<*>)) {
                            try {
                                bannerModelList = (it.item as RecommendModuleItem).list as? List<BannerModel>
                            } catch (e: Exception) {
                            }
                        }
                        if (bannerModelList != null) {
                            isNewZoneStyle = BaseBannerView.isNewZoneStyle(bannerModelList)
                            BaseBannerView.sIsHasNewZoneStyleBanner = isNewZoneStyle
                        }
                    }
                    if (isNewZoneStyle) {
                        var indexOfHulu = getHuluIndex(mRecommendModelNewFromV4?.header)
                        if (mRecommendData?.header == null) {
                            mRecommendData?.header = mRecommendModelNewFromFocus?.header
                        } else {
                            mRecommendData?.header?.add(indexOfHulu + 1, it)
                        }
                    } else {
                        if (mRecommendData?.header == null) {
                            mRecommendData?.header = mRecommendModelNewFromFocus?.header
                        } else {
                            mRecommendData?.header?.add(0, it)
                        }
                    }
                    val homePageFragment = mFragment?.parentFragment
                    if (homePageFragment is HomePageFragment) {
                        if (FoldableScreenCompatUtil.lastScreenWidthDp > 650) {
                            HandlerManager.postOnUIThreadDelay({
                                homePageFragment.updateAtmosphere()
                            }, 500)
                        } else {
                            homePageFragment.updateAtmosphere()
                        }
                    }
                }
                return
            }
        }
        if (mRecommendModelNewFromV4 != null) {
            mRecommendData?.header = mRecommendModelNewFromV4?.header
        }
    }

    private fun getHuluIndex(recommendItemNewList: List<RecommendItemNew>?): Int {
        if (recommendItemNewList == null || recommendItemNewList.isEmpty()) {
            return -1
        }
        var recommendItemNew: RecommendItemNew? = null
        recommendItemNewList.forEach {
            if (it != null && getRecommendItemType(it) == RecommendModuleItem.RECOMMEND_TYPE_SQUARE) {
                recommendItemNew = it
            }
        }
        if (recommendItemNew == null) {
            return -1
        }
        return recommendItemNewList.indexOf(recommendItemNew)
    }

    private fun loadChildProtectBannerData(isLoadMore: Boolean) {
        if (!ifShowChildProtectBanner() || !ChildProtectManager.isChildMode(mContext) || isLoadMore) {
            return
        }

        MainCommonRequest.loadChildProtectBannerInfo(object : IDataCallBack<ChildProtectModel> {
            override fun onSuccess(data: ChildProtectModel?) {
                if (data == null) {
                    return
                }

                mChildProtectBannerData = RecommendItemNew().apply {
                    this.itemType = RecommendItemNew.RECOMMEND_ITEM_CHILD_PROTECT_BANNER
                    this.item = data
                }
            }

            override fun onError(code: Int, message: String?) {
                CustomToast.showFailToast(message)
            }
        })
    }

    fun setChildProtectBannerData(isLoadMore: Boolean) {
        if (ifShowChildProtectBanner() && ChildProtectManager.isChildMode(mContext)
                && mChildProtectBannerData != null && !isLoadMore && mFragment != null
                && mFragment?.mRecommendData != null && mFragment?.mRecommendData?.header != null) {

            val header = mFragment?.mRecommendData?.header
            header?.add(0, mChildProtectBannerData)
            mFragment?.mRecommendData?.header = header
        }
    }

    fun ifShowChildProtectBanner(): Boolean {
        return MmkvCommonUtil.getInstance(mContext).getBoolean(
                PreferenceConstantsInOpenSdk.ITEM_IF_SHOW_CHILD_PROTECT_BANNER, false)
    }

    private fun loadHomeAd(firstRequestAd: Boolean) {
        mFragment?.loadHomeAd(firstRequestAd)
    }

    public fun setFirstLoad(isFirstLoad: Boolean) {
        mIsFirstLoad = isFirstLoad
    }

    /**
     * 获取新人礼包挂件数据
     */
    private fun loadUserGiftPendant() {
        MainCommonRequest.getUserGiftPendant(object : IDataCallBack<UserGiftPendantModel?> {
            override fun onSuccess(`object`: UserGiftPendantModel?) {
                val fragment = mFragment ?: return
                if (!fragment.canUpdateUi()) {
                    return
                }
                if (`object` != null && !TextUtils.isEmpty(`object`.pendantPic)) {
                    fragment.mUserGiftPendantModel = `object`
                    fragment.showNewUserGiftFloatingView(true)
                } else {
                    fragment.showNewUserGiftFloatingView(false)
                }
            }

            override fun onError(code: Int, message: String) {
                val fragment = mFragment ?: return
                if (!fragment.canUpdateUi()) {
                    return
                }
                fragment.showNewUserGiftFloatingView(false)
            }
        })
    }

    fun setRecommendDataForView() {
        loadedAdCount = 0
        loadedAnchorAdCount = 0
        loadedCenterBigAdCount = 0
        loadedInternalAnchorAdCount = 0
        logRecommendLoad("setRecommendDataForView")
        mFragment?.setRecommendDataForView()
    }


    private fun fixSingleNextLineBug(recommendList: ArrayList<Any>?): Boolean {
        try {
            if (recommendList.isNullOrEmpty()) {
                return false
            }

            val showLastItem = mFragment?.mAdapter?.mListData?.lastOrNull()
            if (showLastItem !is RecommendItemNew) {
                return false
            }

            val nextItem = recommendList.getOrNull(0)

            if (nextItem is RecommendItemNew) {
                // 修复本次新増的这行的isPrevIsSingle属性  这个不需要返回true 因为本身就是新增的  但是值计算不对
                nextItem.isPrevIsSingle = isSingleLineItem(showLastItem)
            }

            val oldIsNextIsSingle = showLastItem.isNextIsSingle
            showLastItem.isNextIsSingle = isSingleLineItem(nextItem)

            if (oldIsNextIsSingle != showLastItem.isNextIsSingle) {
//                Log.i("dqq1", "isNextIsSingle 变化了: ${showLastItem.isNextIsSingle}")
                return true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return false
    }

    fun addData(recommendList: ArrayList<Any>, itemList: List<RecommendItemNew>?, fromHead: Boolean) {
        val adIndexArray = StringBuilder()
        val needRecordAdIndex = loadedAdCount == 0
        val promotionAdPreviewModel = AdPreviewManager.checkHasRecommendPromotionAd()
        var hasFocusModule = false
        var isLocalCache = false
        itemList?.forEachIndexed { index, recommendItem ->
            isLocalCache = recommendItem.isLocalCache
            recommendItem.moduleFrom = if (fromHead) RecommendItemNew.FROM_HEADER else RecommendItemNew.FROM_BODY
            when (recommendItem.itemType) {
                RecommendItemNew.RECOMMEND_ITEM_MODULE -> {
                    (recommendItem.item as? RecommendModuleItem)?.let { moduleItem ->
                        when (moduleItem.moduleType) {
                            RecommendModuleItem.RECOMMEND_TYPE_FOCUS -> {
                                Logger.i("-------RecommendFocusAdapterProviderStaggered", "parse focus")
                                var adPreviewModel = AdPreviewManager.checkHasBannerPreview(false)
                                if (adPreviewModel != null) {
                                    val bannerList = arrayListOf<BannerModel>()
                                    bannerList.add(adPreviewModel)
                                    mFragment?.parseFocusImage(bannerList)
                                    recommendList.add(ItemModel(bannerList, RecommendFragmentTypeManager.VIEW_TYPE_FOCUS))
                                } else {
                                    (moduleItem.list as? List<BannerModel>)?.let { bannerModels ->
                                        if (!bannerModels.isNullOrEmpty()) {
                                            mFragment?.parseFocusImage(bannerModels)
                                            recommendList.add(ItemModel(bannerModels, RecommendFragmentTypeManager.VIEW_TYPE_FOCUS))
                                            hasFocusModule = true
                                        }
                                    }
                                }
                                promotionAdPreviewModel?.let {
                                    val newItem = RecommendItemNew()
                                    newItem.moduleFrom = RecommendItemNew.FROM_HEADER
                                    newItem.itemType = RecommendItemNew.RECOMMEND_ITEM_MODULE
                                    val module = RecommendModuleItem()
                                    module.moduleType = RecommendModuleItem.RECOMMEND_TYPE_PROMOTION_OPERATION
                                    module.list = listOf(promotionAdPreviewModel)
                                    module.bgColor = promotionAdPreviewModel.bgColor
                                    newItem.item = module
                                    setDataForPromotionOperation(module, recommendList, newItem)
                                }
                            }
                            RecommendModuleItem.RECOMMEND_TYPE_PROMOTION_OPERATION -> {
                                promotionAdPreviewModel
                                        ?: setDataForPromotionOperation(moduleItem, recommendList, recommendItem)
                            }
                            RecommendModuleItem.RECOMMEND_TYPE_AD_MIX -> {
                                // 单双混排新广告
                                var adType = moduleItem.bizType
                                if (adType == RecommendModuleItem.AD_BANNER) {
                                    mFragment?.mFeedAdManager?.let {
                                        val itemModel = it.insertFeedAd(loadedAdCount, recommendItem)
                                        if (itemModel != null) {
                                            recommendList.add(itemModel)
                                        }
                                        loadedAdCount++
                                    }
                                } else if (adType == RecommendModuleItem.AD_ANCHOR) {
                                    mFragment?.mFeedAnchorAdManager?.let {
                                        val nextItem = itemList.getOrNull(index + 1)
                                        recommendItem.isNextIsSingle = isSingleLineItem(nextItem)
                                        val prevItem = itemList.getOrNull(index - 1)
                                        recommendItem.isPrevIsSingle = isSingleLineItem(prevItem)
                                        if (needRecordAdIndex) {
                                            // 说明是第一屏的数据
                                            adIndexArray.append(index + 1)
                                            adIndexArray.append(",")
                                        }
                                        if (index == 1 || index == 3) {
                                            // 内循环插入到固定2/4位置
                                            val itemModel = it.insertInternalAnchorAd(loadedInternalAnchorAdCount, index, recommendItem)
                                            if (itemModel != null) {
                                                recommendList.add(itemModel)
                                                loadedInternalAnchorAdCount++
                                            } else {
                                            }
                                        } else {
                                            val itemModel = it.insertAnchorAd(loadedAnchorAdCount, recommendItem)
                                            if (itemModel != null) {
                                                recommendList.add(itemModel)
                                            }
                                            loadedAnchorAdCount++
                                        }
                                    }
                                } else {
                                }
                            }
                            else -> {
                            }
                        }
                    }
                }
                RecommendItemNew.RECOMMEND_ITEM_ALBUM -> {
                    if (recommendItem.item is RecommendAlbumItem) {
                        (recommendItem.item as? RecommendAlbumItem)?.let {
                            if (!it.albumTitle.realString().isNullOrBlank()) {
                                recommendList.add(recommendItem)
                            }
                        }
                    }
                    val nextItem = itemList.getOrNull(index + 1)
                    recommendItem.isNextIsSingle = isSingleLineItem(nextItem)
                    val prevItem = itemList.getOrNull(index - 1)
                    recommendItem.isPrevIsSingle = isSingleLineItem(prevItem)
                }
                RecommendItemNew.RECOMMEND_ITEM_TRACK -> {
                    if (recommendItem.item is RecommendTrackItem) {
                        (recommendItem.item as? RecommendTrackItem)?.let {
                            if (!it.trackTitle.realString().isNullOrBlank()) {
                                recommendList.add(recommendItem)
                            }
                        }
                    }
                    val nextItem = itemList.getOrNull(index + 1)
                    recommendItem.isNextIsSingle = isSingleLineItem(nextItem)
                    val prevItem = itemList.getOrNull(index - 1)
                    recommendItem.isPrevIsSingle = isSingleLineItem(prevItem)
                }
                RecommendItemNew.RECOMMEND_ITEM_CHILD_PROTECT_BANNER,
                RecommendItemNew.RECOMMEND_SPECIAL,
                RecommendItemNew.RECOMMEND_ITEM_SCENE_LISTEN_CARD,
                RecommendItemNew.RECOMMEND_ITEM_SOCIAL_LISTEN_LIST,
                RecommendItemNew.RECOMMEND_ITEM_GUESS_YOU_LIKE_LIST,
                RecommendItemNew.RECOMMEND_ITEM_SOCIAL_LISTEN_LIST_AD,
                RecommendItemNew.RECOMMEND_ITEM_HOT_LIVE_LIST,
                RecommendItemNew.RECOMMEND_ITEM_818_KOL_LIVE_LIST,
                RecommendItemNew.RECOMMEND_ITEM_VIDEO_LIVE_LIST,
                RecommendItemNew.RECOMMEND_ITEM_CHASING_FOR_UPDATE,
                RecommendItemNew.RECOMMEND_ITEM_MOT_FOLLOW_UPDATE_LIST,
                RecommendItemNew.RECOMMEND_ITEM_MOT_AUTHOR_UPDATE_LIST,
                RecommendItemNew.RECOMMEND_ITEM_EXPLOSIVE_CONTENT,
                RecommendItemNew.RECOMMEND_ITEM_CHILD_IP_LIST,
                RecommendItemNew.RECOMMEND_TYPE_IP_NEW_CONTAINER_LIST,
                RecommendItemNew.RECOMMEND_TYPE_IP_1V4_NEW_CONTAINER_LIST,
                RecommendItemNew.RECOMMEND_TYPE_TAG_LIST,
                RecommendItemNew.RECOMMEND_TYPE_INTEREST_CARD,
                RecommendItemNew.RECOMMEND_TYPE_FREE_LISTENER,
                RecommendItemNew.RECOMMEND_ITEM_SHORT_VIDEO_LIST,
                RecommendItemNew.RECOMMEND_ITEM_SLEEP_AID_CARD,
                RecommendItemNew.RECOMMEND_ITEM_H5_CARD_LIST -> {
                    if (recommendItem.item is RecommendCommonItem) {
                        (recommendItem.item as? RecommendCommonItem)?.let {
                            if (it.subElements?.isNotEmpty() == true) {
                                recommendList.add(recommendItem)
                            }
                        }
                    } else {
                        recommendList.add(recommendItem)
                    }
                }
                RecommendItemNew.RECOMMEND_ITEM_HOT_TAGS_2024,
                RecommendItemNew.RECOMMEND_ITEM_SEARCH_CUSTOM_CARD,
                RecommendItemNew.RECOMMEND_ITEM_RANK_LIST -> {
                    // 排行榜针对第一个tab下数据做判断 小于4条不显示  跟ios对齐
                    (recommendItem.item as? RecommendRankListItem)?.let {
                        val list = it.list?.getOrNull(0)?.subElements
                        if ((list?.size ?: 0) >= 4) {
                            recommendList.add(recommendItem)
                        }
                        if (recommendItem.itemType == RecommendItemNew.RECOMMEND_ITEM_RANK_LIST) {
                            if(recommendItem.displayClass == "fixedSlice"){
                                if ((list?.size ?: 0) < 4) {
                                    RecommendFragmentPageErrorManager.uploadDataError("排行榜数量不足", "fixedSlice")
                                }
                            }else{
                                if ((list?.size ?: 0) < MmkvCommonUtil.getInstance(ToolUtil.getCtx())
                                        .getInt(CConstants.Group_toc.ITEM_HOME_RANK_CARD_MIN_COUNT, 6)) {
                                    RecommendFragmentPageErrorManager.uploadDataError("排行榜数量不足", null)
                                }
                            }
                        }
                        if (recommendItem.itemType == RecommendItemNew.RECOMMEND_ITEM_HOT_TAGS_2024) {
                            if ((list?.size ?: 0) < 6) {
                                RecommendFragmentPageErrorManager.uploadDataError("热门标签数量不足", null)
                            }
                        }
                    }
                }
                RecommendItemNew.RECOMMEND_ITEM_HOT_RANK_LIST -> {
                    (recommendItem.item as? RecommendRankListItem)?.let {
                        val list = it.list?.getOrNull(0)?.subElements
                        if ((list?.size ?: 0) >= 3) {
                            recommendList.add(recommendItem)
                        }
                    }
                }
                RecommendItemNew.RECOMMEND_TYPE_ANCHOR_CARD -> {
                    // 针对第一个tab下数据做判断 小于4条不显示
                    (recommendItem.item as? RecommendRankListItem)?.let {
                        val list = it.list?.getOrNull(0)?.subElements
                        if ((list?.size ?: 0) >= 2) {
                            recommendList.add(recommendItem)
                        }
                    }
                }
                RecommendItemNew.RECOMMEND_ITEM_USER_RESEARCH_NEW -> {
                    recommendList.add(recommendItem)
                }
                RecommendItemNew.RECOMMEND_ITEM_KID_ALL_LIKE -> {
                    (recommendItem.item as? RecommendKidAllLikeItem)?.let {
                        recommendList.add(recommendItem)
                    }
                }
                RecommendItemNew.RECOMMEND_ITEM_USER_RESEARCH_NEW_HEADER -> {
                    recommendList.add(recommendItem)
                }
                RecommendItemNew.RECOMMEND_ITEM_AD_CENTER_BIG_PIC -> {
                    // 插入中插大图广告
                    var enable = ConfigureCenter.getInstance().getBool(
                        CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_AD_CENTER_BIG_PIC_AD_ENABLE, true
                    )
                    if (enable) {
                        recommendList.add(ItemModel(recommendItem, RecommendFragmentTypeManager.VIEW_TYPE_AD_CENTER_BIG_PIC))
                    }
                }
                RecommendItemNew.RECOMMEND_ITEM_AD_CENTER_BIG_FEED -> {
                    // 插入中插大图信息流广告
                    mFragment?.mCenterBigFeedAdManager?.let {
                        val itemModel = it.insertFeedAd(loadedCenterBigAdCount, recommendItem)
                        if (itemModel != null) {
                            recommendList.add(itemModel)
                        }
                        loadedCenterBigAdCount++
                    }
                }
                else -> {
//                    if (RecommendPageGaiaXManager.hasVVTemplateForItem(recommendItem)) {
//                        recommendList.add(recommendItem)
//                    } else if (RecommendPageVirtualViewManager.hasVVTemplateForItem(recommendItem)) {
//                        val prevItem = itemList.getOrNull(index - 1)
//                        recommendItem.isPrevIsSingle = isSingleLineItem(prevItem)
//                        // 获取上个Item的类型
//                        val prevItemType: String? = prevItem?.jsonObject?.optString("itemType", "")
//                        // 获取当前Item的类型
//                        val currentItemType: String? = recommendItem.jsonObject?.optString("itemType", "")
//                        // 上个与当前都是自动播放的时候，就false掉一个
//                        if (prevItemType?.equals("AnimationLive") == true && currentItemType?.equals("AnimationLive") == true) {
//                            val itemJson = recommendItem.jsonObject.getJSONObject("item")
//                            val otherJson = itemJson.getJSONObject("other")
//                            otherJson.put("autoPlay", false)
//                        }
//                        recommendList.add(recommendItem)
//                    }
                }
            }
        }
        val splashUnitAd = BigScreenAdManager.getInstance().mSplashUnitAd
        val bigScreenAd = BigScreenAdManager.getInstance().requestBigScreenAd
        // !isLocalCache ： 非缓存数据数据再加载巨幕，防止巨幕展示出来被新数据二次刷新掉
        if (!isLocalCache && fromHead && !hasFocusModule && (splashUnitAd != null || bigScreenAd != null || mFragment?.mFocusAdapterProvider?.isBigAdShowing == true)) {
            // 无焦点图模块时仍然添加焦点图模块，为了展示巨幕广告
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "addFocus")
            recommendList.add(0, ItemModel(null, RecommendFragmentTypeManager.VIEW_TYPE_FOCUS))
        }
        if (!fromHead) {
            filterDataForMixMode(recommendList)
        }
        if (needRecordAdIndex) {
            var sharedString = if (adIndexArray.isEmpty()) "" else adIndexArray.toString().subSequence(0, adIndexArray.length - 1).toString()
            SharedPreferencesUtil.getInstance(mContext).saveString(PreferenceConstantsInMain.KEY_HOME_ANCHOR_AD_INDEX_ARRAY, sharedString)
        }
    }

    private fun filterDataForMixMode(recommendList: java.util.ArrayList<Any>?) {
        recommendList ?: return
        if (RecommendFragmentAbManager.mode != RecommendFragmentAbManager.MODE_MIX) {
            return
        }
        recommendList.forEachIndexed { index, any ->
            (any as? RecommendItemNew)?.let {
                if (any.itemType == RecommendItemNew.RECOMMEND_ITEM_ALBUM || any.itemType == RecommendItemNew.RECOMMEND_ITEM_TRACK) {
                    val nextType = recommendList.getOrNull(index + 1)
                    any.isNextIsSingle = isSingleLineItem(nextType)
                    val prevType = recommendList.getOrNull(index - 1)
                    any.isPrevIsSingle = isSingleLineItem(prevType)
                }
            }
        }
    }

    private fun isSingleLineItem(data: Any?): Boolean {
        data ?: return false
        if (data is ItemModel<*> && data.viewType == RecommendFragmentTypeManager.VIEW_TYPE_AD_MIX_ANCHOR) {
            return true
        }
        if (data !is RecommendItemNew) {
            return false
        }
        if (data.itemType == RecommendItemNew.RECOMMEND_ITEM_MODULE && data.item is RecommendModuleItem
                && (data.item as RecommendModuleItem).moduleType == RecommendModuleItem.RECOMMEND_TYPE_AD_MIX) {
            return (data.item as RecommendModuleItem).bizType == RecommendModuleItem.AD_ANCHOR
        }
        return data.itemType == RecommendItemNew.RECOMMEND_ITEM_ALBUM
                || data.itemType == RecommendItemNew.RECOMMEND_ITEM_TRACK
    }

    private fun setDataForPromotionOperation(moduleItem: RecommendModuleItem, recommendList: ArrayList<Any>, recommendItem: RecommendItemNew) {
        val promotionAdList = moduleItem.list
        if (!promotionAdList.isNullOrEmpty()) {
            mFragment?.mHasPromotionOperationModule = true
            mFragment?.changeBottomOvalViewHeightForPromotion(true)
            recommendList.add(recommendItem)
            if (!TextUtils.isEmpty(moduleItem.bgColor) && moduleItem.bgColor.startsWith("#")) {
                mFragment?.apply {
                    try {
                        mPromotionOperationColor = Color.parseColor(moduleItem.bgColor)
                        mHomePageTabTheme = HomePageTabTheme()
                        mHomePageTabTheme?.searchBoxColor = HomePageTabTheme.FOREGROUND_COLOR_WHITE
                    } catch (e: Exception) {
                        mFragment?.getBgColorFromPromotionModel(promotionAdList[0])
                    }
                }
            } else {
                mFragment?.getBgColorFromPromotionModel(promotionAdList[0])
            }
        }
    }

    private fun loadDataFromLocal() {
        logRecommendLoad("loadDataFromLocal")
        LoadDataFromLocalTask(mFragment).myexec()
    }

    fun preLoadDataFromLocal() {
        PreLoadDataFromLocalTask(object : IDataCallBack<RecommendModelNew> {
            override fun onSuccess(data: RecommendModelNew?) {
                // TODO: 暂时注释掉
//                data?.header = data?.header?.subList(1, 5)
                mPreLoadLocalRecommendData = data
//                if (data != null) {
//                    mFragment?.setDataFromLocal(data)
//                    if (mFragment?.canUpdateUi() == true) {
//                        if (!ToolUtil.isEmptyCollects(data.header) || !ToolUtil.isEmptyCollects(
//                                data.body
//                            )
//                        ) {
//                            mFragment?.setIsLoadLocalData(true)
//                            mFragment?.onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK)
//                            mFragment?.setRecommendDataForView()
//                            mFragment?.traceItemViewed()
//                        }
//                    }
//                }
            }
            override fun onError(code: Int, message: String?) {
            }
        }).myexec()
    }

    private fun saveDataToLocal(jsonStr: String?) {
        SaveDataToLocalTask().myexec(jsonStr)
    }

    private fun saveLastBody(recommendModel: RecommendModelNew) {
        if (mLastSavedBody == null) {
            mLastSavedBody = arrayListOf()
        } else {
            mLastSavedBody.clear()
        }
        if (recommendModel != null && !ToolUtil.isEmptyCollects(recommendModel.body)) {
            mLastSavedBody.addAll(recommendModel.body)
        }
    }

    private fun notifyTraceFinish() {
        mFragment?.mTraceHelper?.notifyPageFailed()
        HomeRecommendPageLoadingOptimizationManager.onHomeRecommendPageLoading(false)
        mFragment?.cancelCheckIsRealVisibleToAbandonTraceTask()
    }

    private fun getGiftTagPrefKey(uid: Long): String {
        return PreferenceConstantsInMain.KEY_RECOMMEND_NEW_USER_GIFT_GIFT_TAG + "_" + uid
    }

    private fun getNotShowFeeds(): RecommendNotShowFeeds? {
        if (ToolUtil.isEmptyCollects(mLastSavedBody)) {
            return null
        }
        val notShowFeeds = RecommendNotShowFeeds()
        for (recommendItemNew in mLastSavedBody) {
            if (recommendItemNew.isHasShow) {
                continue
            }
            val data = recommendItemNew.item
            when (recommendItemNew.itemType) {
                RecommendItemNew.RECOMMEND_ITEM_ALBUM -> if (data is RecommendAlbumItem) {
                    notShowFeeds.putAlbumId(data.id)
                }
                RecommendItemNew.RECOMMEND_ITEM_TRACK -> if (data is RecommendTrackItem) {
                    notShowFeeds.putTrackId(data.dataId)
                }
                RecommendItemNew.RECOMMEND_ITEM_LIVE -> if (data is RecommendLiveItem) {
                    notShowFeeds.putLiveId(data.roomId)
                }
                RecommendItemNew.RECOMMEND_SPECIAL -> if (data is RecommendSpecialItem) {
                    notShowFeeds.putOtherId(data.specialId)
                }
                else -> {
                }
            }
        }
        return notShowFeeds
    }

    fun logRecommendLoad(msg: String) {
        Logger.logToFile("RecommendFragment:__  $msg")
    }

    var secondFloorLastFoldStatus = false

    fun secondFloorShow2LinesEnable(): Boolean{
        secondFloorLastFoldStatus = RecommendCenterBigPicAdManager.notifyCenterBigPicAdShowEnable() &&
                RecommendCenterBigPicAdManager.isCenterBigAdShowing()
        return secondFloorLastFoldStatus
    }

    private fun resetCenterBigPicAdOptimization() {
        var foldEnable = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(CConstants.Group_ad.ITEM_CENTER_BIG_AD_REFRESH_FOLD, true)
        if (ConstantsOpenSdk.isDebug) {
            foldEnable = "1".equals(ToolUtil.getSystemProperty("debug.center_ad.refresh_fold", "1"))
        }
        if (!foldEnable && mFragment?.mIsRefreshing == true) {
            return
        }
        refreshLoadMillis = 0L
        RecommendCenterBigPicAdManager.resetOptimizationData()
    }
}