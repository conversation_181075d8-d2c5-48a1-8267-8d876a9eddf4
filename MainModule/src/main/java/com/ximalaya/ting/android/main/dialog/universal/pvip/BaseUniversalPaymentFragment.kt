package com.ximalaya.ting.android.main.dialog.universal.pvip

import android.content.Context
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.core.view.updateLayoutParams
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.IPVipBottomActionListener
import com.ximalaya.ting.android.host.listener.IPVipSkuItemActionListener
import com.ximalaya.ting.android.host.listener.OnScrollerScrollListener
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.vip.IVipFunctionAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.vip.IVipFunctionAction.IVipBottomViewManager
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.vip.IVipFunctionAction.IVipSkuItemViewManager
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VipActionRouter
import com.ximalaya.ting.android.host.manager.share.IdleHorizontalScrollView
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.view.OnEdgeListenerNestedScrollView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.dialog.universal.DialogMaterial.ViewCollection
import com.ximalaya.ting.android.main.kachamodule.utils.SpannableStringUtils
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

/**
 * Created by mark on 2025/3/5 11:12
 */
abstract class BaseUniversalPaymentFragment(
    val mVipSkuShelfInfo: VipSkuShelfInfo,
    val dialogMaterial: DialogMaterialForPlatinumVip
) :
    BaseFragment2(), IPVipSkuItemActionListener, IPVipBottomActionListener,
    IUniversalPaymentChildPage {
    companion object {
        const val TAG = "BaseUniversalPaymentFragment"
    }

    abstract override fun getPageLogicName(): String
    abstract fun initSkuViewManager(
        fragment2: BaseFragment2,
        vipFuncRouter: IVipFunctionAction
    ): IVipSkuItemViewManager

    abstract fun initBottomViewManager(
        fragment2: BaseFragment2,
        vipFuncRouter: IVipFunctionAction
    ): IVipBottomViewManager

    abstract fun bindTitleInfo()

    protected lateinit var mScrollView: OnEdgeListenerNestedScrollView
    protected lateinit var mIvPageTopBg: ImageFilterView
    protected lateinit var mPageTitleContainer: LinearLayout
    protected lateinit var mTvPageIntro: TextView
    protected lateinit var mTvMoreMenu: TextView
    protected lateinit var mSkuScrollView: IdleHorizontalScrollView
    protected lateinit var mBottomBtnContainer: FrameLayout
    protected lateinit var mSkuContainer: LinearLayout
    protected lateinit var mTvSkuDescription: TextView
    protected lateinit var mExtraBehaviorContainer: LinearLayout
    protected var mVipSkuItemViewManager: IVipFunctionAction.IVipSkuItemViewManager? = null
    protected var mVipBottomViewManager: IVipFunctionAction.IVipBottomViewManager? = null
    protected var mChosenSku: VipSkuItem? = null
    protected var mIsScrollToTop: Boolean = true
    protected var mPageHeight: Int = 0
    protected var mPageHeightMeasureCount = 0

    override fun loadData() {
    }

    override fun closeDialog() {
        (parentFragment as? IUniversalPaymentPage)?.closeDialog()
    }


    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_vip_payment
    }

    override fun initUi(savedInstanceState: Bundle?) {
        mScrollView = findViewById(R.id.main_play_page_dialog_scroll_view)
        mIvPageTopBg = findViewById(R.id.main_iv_top_bg_img)
        mPageTitleContainer = findViewById(R.id.main_ll_title_container)
        mTvPageIntro = findViewById(R.id.main_vip_sub_title_tv)
        mTvMoreMenu = findViewById(R.id.main_vip_more_menu)
        mSkuScrollView = findViewById(R.id.main_hs_sku_scroll_container)
        mSkuContainer = findViewById(R.id.main_ll_sku_container)
        mTvSkuDescription = findViewById(R.id.main_tv_sku_description)
        mExtraBehaviorContainer = findViewById(R.id.main_ll_extra_behavior)
        mBottomBtnContainer = findViewById(R.id.main_fl_bottom_container)
        mSkuScrollView.updateLayoutParams<LinearLayout.LayoutParams> {
            width = getCompatScreenWidth(mContext)
        }
        initListener()
        bindTitleInfo()
        Router.getActionByCallback(
            Configure.BUNDLE_VIP,
            object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel) {
                    try {
                        val vipActionRouter =
                            Router.getActionRouter<VipActionRouter>(Configure.BUNDLE_VIP)
                                ?: return
                        val functionAction = vipActionRouter.functionAction ?: return
                        dialogMaterial.fragment?.let { baseFragment2 ->
                            mVipSkuItemViewManager =
                                initSkuViewManager(baseFragment2, functionAction)
                            mVipBottomViewManager =
                                initBottomViewManager(baseFragment2, functionAction)
                        }

                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {
                    // do Nothing for now
                }

                override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {
                    // do Nothing for now
                }
            }
        )
        mVipSkuItemViewManager?.setOnSkuItemActionListener(this)
        mVipSkuItemViewManager?.bindDataOnShelve(mSkuContainer)
        mChosenSku = mVipSkuItemViewManager?.chosenVipSku
        mVipBottomViewManager?.createRuleAndBuyButtonView()?.let {
            mBottomBtnContainer.addView(
                it,
                FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT
                )
            )
        }
        mVipBottomViewManager?.setOnBottomViewActionListener(this)
        mVipBottomViewManager?.onSkuItemSelected(mChosenSku)
        updateSkuDetailInfo(mChosenSku)
        mSkuScrollView.setOnScrollStateChangedListener(object :
            IdleHorizontalScrollView.ScrollViewListener {
            override fun onScrollChanged(scrollType: IdleHorizontalScrollView.ScrollType?) {
            }
        })
        dialogMaterial.setView(ViewCollection().apply {
            btnArea = mExtraBehaviorContainer
        })
        postMeasurePageHeight()
    }

    private fun initListener() {
        (parentFragment as? IUniversalPaymentPage)?.let { universalPaymentPage ->
            universalPaymentPage.getVerticalScrollListener()?.let {
                mScrollView.bindOnSubScrollerScrollListener(it)
            }
        }
    }

    private fun postMeasurePageHeight() {
        mPageHeight = 0
        mPageHeightMeasureCount = 0
        mPageTitleContainer.postDelayed({
            mPageHeight += mPageTitleContainer.height
            mPageHeightMeasureCount++
            notifyPageHeight()
        }, 1)
        mSkuContainer.postDelayed({
            mPageHeight += mSkuContainer.height
            mPageHeightMeasureCount++
            notifyPageHeight()
        }, 1)
        if (mTvSkuDescription.visibility == View.GONE) {
            mTvSkuDescription.visibility = View.INVISIBLE
        }
        mTvSkuDescription.postDelayed({
            mPageHeight += mTvSkuDescription.height
            mPageHeightMeasureCount++
            notifyPageHeight()
            if (mTvSkuDescription.visibility == View.GONE || mTvSkuDescription.visibility == View.INVISIBLE) {
                mTvSkuDescription.visibility = View.GONE
            }
        }, 1)
        mExtraBehaviorContainer.postDelayed({
            mPageHeight += mExtraBehaviorContainer.height
            mPageHeightMeasureCount++
            notifyPageHeight()
        }, 1)
        mBottomBtnContainer.postDelayed({
            mPageHeight += mBottomBtnContainer.height
            mPageHeightMeasureCount++
            notifyPageHeight()
        }, 1)
    }

    open fun updateSkuDetailInfo(item: VipSkuItem?) {
        item?.let { item ->
            val descriptionString: String? = item.getSalesRemarkTextNew()
            if (null != descriptionString && descriptionString.isNotEmpty()) {
                mTvSkuDescription.visibility = View.VISIBLE
                mTvSkuDescription.text = SpannableStringUtils.removeBreakLineTag(SpannableStringUtils.removeAllBTags(descriptionString))
                mTvSkuDescription.setOnClickListener {
                    traceSkuIntroClick()
                    showSkuDetailInfo(descriptionString)
                }
            } else {
                mTvSkuDescription.visibility = View.GONE
            }
        }
    }

    private fun showSkuDetailInfo(description: String) {
        val boldTexIndices =
            SpannableStringUtils.getBoldTextIndices(description)
        val descriptionAfterRemoveAllBTags =
            SpannableStringUtils.removeAllBTags(description)
        var stringBuilder =
            SpannableStringBuilder(descriptionAfterRemoveAllBTags)
        val result = if (!boldTexIndices.isNullOrEmpty()) {
            SpannableStringUtils.removeBTagsAndSetBoldText(
                stringBuilder,
                boldTexIndices
            )
        } else {
            stringBuilder
        }
        CommonInfoDialogFragment.newInstance(result)
            .show(childFragmentManager, "DescriptionInfoDialog")
    }

    private fun notifyPageHeight() {
        if (mPageHeightMeasureCount == 5) {
            (parentFragment as? IUniversalPaymentPage)?.let { universalPaymentPage ->
                universalPaymentPage.notifyPageHeightChanged(mPageHeight)
            }
        }
    }

    override fun onSkuItemSelected(item: VipSkuItem) {
        mChosenSku = item
        updateSkuDetailInfo(item)
        mVipBottomViewManager?.onSkuItemSelected(item)
    }

    override fun onSkuUpgradeMonthClick(view: View, item: VipSkuItem) {
    }

    override fun onResume() {
        super.onResume()
        if (!mIsScrollToTop) {
            mScrollView.setScrollInternalStatus()
        }
    }

    override fun onPause() {
        super.onPause()
        mIsScrollToTop = mScrollView.isScrolledToTop
    }

    override fun onConfirmBtnClick(item: VipSkuItem) {
        // 通知点击购买
    }

    override fun onSkuUpgradeMonthViewShow(item: VipSkuItem) {
    }

    override fun getUpgradeSkuSet(): Pair<VipSkuItem, VipSkuItem.UpgradeProductModel?>? {
        return null
    }

    private fun getCompatScreenWidth(context: Context): Int {
        var isFoldScreenInExpandMode = false
        if (BaseUtil.isFoldScreen(context)) {
            isFoldScreenInExpandMode =
                BaseUtil.dp2px(context, 600f) <= BaseUtil.getScreenWidth(context)
        }
        if (isFoldScreenInExpandMode) {
            return BaseUtil.dp2px(context, 375f)
        }
        return BaseUtil.getScreenWidth(context)
    }

    protected fun traceMoreMenuClick(item: String, tabName: String) {
        // 统一付费半浮层-更多套餐  弹框控件点击
        XMTraceApi.Trace()
            .setMetaId(31251)
            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
            .put("templateId", "") // 表示浮层id
            .put("businessID", "") // 表示浮层的业务id
            .put("trackId", "${mVipSkuShelfInfo.trackId}")
            .put("albumId", "${mVipSkuShelfInfo.albumId}")
            .put("vipStatus", "${UserInfoMannage.isVipUser()}")
            .put("sceneName", dialogMaterial.source)
            .put("tabName", tabName)
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_VIP_UNIVERSAL_CUSTOM_DIALOG)
            )
            .put("Item", item)
            .createTrace()
    }
    private fun traceSkuIntroClick() {
        // 统一付费半浮层-完整说明  弹框控件点击
        XMTraceApi.Trace()
            .setMetaId(67816)
            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
            .put("currPage", "")
            .put("tabName", "platinumVip")
            .put("trackId", "${mVipSkuShelfInfo.trackId}")
            .put("albumId", "${mVipSkuShelfInfo.albumId}")
            .put("vipStatus", "${UserInfoMannage.isVipUser()}")
            .put("sceneName", dialogMaterial.source)
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_VIP_UNIVERSAL_CUSTOM_DIALOG)
            )
            .createTrace()
    }
}

interface IUniversalPaymentPage {
    fun getVerticalScrollListener(): OnScrollerScrollListener?
    fun closeDialog()
    fun notifyPageHeightChanged(height: Int)
}

interface IUniversalPaymentChildPage {
}