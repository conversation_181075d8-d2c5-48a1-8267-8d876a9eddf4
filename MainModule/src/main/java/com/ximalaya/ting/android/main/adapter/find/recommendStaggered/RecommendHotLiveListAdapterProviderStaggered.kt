package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.text.Spannable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.text.buildSpannedString
import androidx.core.text.inSpans
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.socialModule.util.SocialTextUtil.SpaceSpan
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.IClickExportListener
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew.RECOMMEND_ITEM_HOT_LIVE_LIST
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.satisfy.SatisfactionHomeModuleManager
import com.ximalaya.ting.android.main.util.HomeRealTimeTraceUtils
import com.ximalaya.ting.android.main.view.live.LiveHotValueTagView
import com.ximalaya.ting.android.main.view.live.LiveLotteryTagView
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 新首页 - 热门直播卡片
 *
 * Created by zoey on 2024/04/09.
 * <AUTHOR>
 * @email <EMAIL>
 */
class RecommendHotLiveListAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendHotLiveListAdapterProviderStaggered.LiveListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewConfigurationChanged<RecommendHotLiveListAdapterProviderStaggered.LiveListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendHotLiveListAdapterProviderStaggered.LiveListCardViewHolder, RecommendItemNew> {

    private var oldScrollState = RecyclerView.SCROLL_STATE_IDLE

    override fun getView(
        layoutInflater: LayoutInflater?,
        position: Int,
        parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_live_list_card, parent, false
        )
    }

    override fun createViewHolder(convertView: View?): LiveListCardViewHolder? {
        return convertView?.let { LiveListCardViewHolder(it) }
    }

    override fun bindViewHolder(
        holder: LiveListCardViewHolder?,
        position: Int,
        data: RecommendItemNew?,
        convertView: View?
    ) {
        if (holder == null || data == null) return

        SatisfactionHomeModuleManager.checkShowSatisfyView(
            fragment, holder.itemView, data,
            SatisfactionHomeModuleManager.TYPE_LIVE_AUDIO ,position
        )

        val recommendCommonItem = data.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) return
        if (recommendCommonItem.subElements.isNullOrEmpty()) return

        if (recommendCommonItem.subElements!!.size < MmkvCommonUtil.getInstance(ToolUtil.getCtx())
                .getInt(CConstants.Group_toc.ITEM_HOME_LIVE_CARD_MIN_COUNT, 6)) {
            RecommendFragmentPageErrorManager.uploadDataError("直播数量不足", null)
        }
        if (recommendCommonItem.landingPage.isNullOrBlank()) {
            RecommendFragmentPageErrorManager.uploadDataError("直播iting为空", null)
        }
        if (!TextUtils.isEmpty(recommendCommonItem.subTitle)) {
            holder.tvLiveListTittle.text = recommendCommonItem.title
            holder.tvLiveListTittle.maxLines = 1
            holder.tvLiveListSubTittle.text = recommendCommonItem.subTitle
            ViewStatusUtil.setVisible(View.VISIBLE, holder.tvLiveListSubTittle)
        } else {
            holder.tvLiveListTittle.text = recommendCommonItem.title
            holder.tvLiveListTittle.maxLines = 2
            ViewStatusUtil.setVisible(View.GONE, holder.tvLiveListSubTittle)
        }

        val performMoreClick: (action: String?, targetView: View?) -> Unit = { action, targetView ->
            // 新首页-首页大卡模块  点击事件
            val trace = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId) // 客户端传
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem?.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            trace.createTrace()

            // 新首页-直播卡片-更多  点击事件
            val trace1 = XMTraceApi.Trace()
                .click(62467) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId ?: "") // 客户端传，去重用
                .put("action", action)
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem?.ubtV2,
                (position + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            if (data.isLocalCache) {
                trace1.isLocalCache
            }
            trace1.createTrace()

            ToolUtil.clickUrlAction(
                fragment, recommendCommonItem.landingPage ?: "", targetView ?: holder.tvGoForMore
            )
        }
        holder.tvLiveListTittle.setOnClickListener {
            if (recommendCommonItem.landingPage.isNullOrBlank()) return@setOnClickListener
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            performMoreClick("click", it)
        }
        holder.tvLiveListSubTittle.setOnClickListener {
            if (recommendCommonItem.landingPage.isNullOrBlank()) return@setOnClickListener
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            performMoreClick("click", it)
        }

        holder.tvGoForMore.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
            val isShowMore = !recommendCommonItem.landingPage.isNullOrBlank()
            var level1DisLikeTitle = recommendCommonItem.extraInfo?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick("click", null)
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(position)
                    SatisfactionHomeModuleManager.clickModuleDislike(
                        fragment, SatisfactionHomeModuleManager.TYPE_LIVE_AUDIO, data, position
                    )
                }
            }
            val moreFuncBuild = MoreFuncBuild.createLiveListMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener
            )
            moreFuncBuild.isShowLevel1DisLikeToast =
                !SatisfactionHomeModuleManager.isAllowModuleDislike(SatisfactionHomeModuleManager.TYPE_LIVE_AUDIO)

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (position + 1).toString())
                put("xmRequestId", data.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.extraInfo?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }

        val liveListAdapter = LiveListLiveItemAdapter(
            dataAction, fragment, recommendCommonItem, data,
            recommendCommonItem.subElements ?: emptyList(), position,
            holder.rcvLiveList,
            recommendCommonItem.landingPage.isNullOrEmpty().not()
        ).also {
            it.setRelaseJumpActivityListener {
                performMoreClick("slide", holder.rcvLiveList)
            }
        }
        holder.rcvLiveList.adapter = liveListAdapter
        holder.rcvLiveList.layoutManager = GridLayoutManager(
            convertView?.context, 3, GridLayoutManager.HORIZONTAL, false
        ).also {
            it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val type = liveListAdapter.getItemViewType(position)
                    val slideMoreType = type == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE
                    return if (slideMoreType) {
                        3
                    } else {
                        1
                    }
                }
            }
        }
        if (holder.startSnapHelper == null) {
            holder.startSnapHelper = StartSnapHelper().also {
                it.attachToRecyclerView(holder.rcvLiveList)
                it.setContainerView(holder.rcvLiveList)
            }
        }
        holder.rcvLiveList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == oldScrollState) return
                oldScrollState = newState
                if (oldScrollState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(data, position, holder)
                    holder.firstVisiblePosition = (holder.rcvLiveList.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                }
            }
        })
        if (data.xmRequestId != null && (data.xmRequestId + position) == holder.uniqueId) {
            holder.rcvLiveList.scrollToPosition(holder.firstVisiblePosition)
        } else {
            holder.uniqueId = data.xmRequestId + position
        }
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: LiveListCardViewHolder?
    ) {
        if (data == null || holder == null) return

        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) return

        fragment.postOnUiThread {
            if (fragment.canUpdateUi()) {
                if (ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                    // 新首页-首页大卡模块  控件曝光
                    val trace = XMTraceApi.Trace()
                        .setMetaId(62177)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "newHomePage")
                        .put("modulePosition", (position + 1).toString())
                        .put("xmRequestId", data.xmRequestId) // 客户端传
                        .put("contentType", data.itemType) // 客户端传
                        .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                    SpmTraceUtil.addSpmTraceInfo(
                        trace,
                        recommendCommonItem.ubtV2,
                        (position + 1).toString()
                    )
                    RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
                    if (data.isLocalCache) {
                        trace.isLocalCache
                    }
                    trace.createTrace()
                }
                for (i in 0 until holder.rcvLiveList.childCount) {
                    val view = holder.rcvLiveList.getChildAt(i)
                    if (ViewStatusUtil.viewIsRealShowing(view)) {
                        val subElement = view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                        val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                        traceItemShow(position, data, index, subElement, view, recommendCommonItem)
                    }
                }
            }
        }
    }

    override fun onConfigurationChanged(holder: LiveListCardViewHolder?) {}

    class LiveListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val tvLiveListTittle: TextView = convertView.findViewById(R.id.main_tv_live_list_tittle)
        val tvLiveListSubTittle: TextView = convertView.findViewById(R.id.main_tv_live_list_sub_title)
        val rcvLiveList: RecyclerView = convertView.findViewById(R.id.main_rcv_live_list)
        val tvGoForMore: View = convertView.findViewById(R.id.main_tv_more)

        var startSnapHelper: StartSnapHelper? = null
        var firstVisiblePosition = 0
        var uniqueId = ""
    }

    public class LiveListLiveItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        private val fragment: BaseFragment2,
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        list: List<CommonSubElement>,
        var positionNew: Int,
        val recyclerView: RecyclerView,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        // 直播列表
        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            mEnableMoreItem = enableJumpMore
            commonSubElementList.addAll(list)
        }

        class LiveItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val rootView: View = view.findViewById(R.id.main_csl_item_root_view)
            val itemCoverView: RoundImageView = view.findViewById(R.id.main_iv_item_cover)
            val itemPlaying: XmLottieAnimationView = view.findViewById(R.id.main_lottie_play_status)
            val itemTitleView: TextView = view.findViewById(R.id.main_tv_title)
            val itemSubNickname: TextView = view.findViewById(R.id.main_tv_sub_nickname_and_badge)
            val itemTagGroup: ViewGroup = view.findViewById(R.id.main_ll_bottom_tag)
            val itemLotteryTag: LiveLotteryTagView = view.findViewById(R.id.main_tag_lottery_view)
            val itemRankTag: TextView = view.findViewById(R.id.main_tag_rank_view)
            val itemCategory: TextView = view.findViewById(R.id.main_tv_category)
            val itemHotValue: LiveHotValueTagView = view.findViewById(R.id.main_tv_hot_value)

            init {
                resetSize()
            }

            fun resetSize() {
                rootView.updateLayoutParams {
                    width = getRpAdaptSize(323)
                }
            }
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            return LiveItemViewHolder(
                ViewPool.getInstance().getView(
                    HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                    R.layout.main_item_recommend_live_list_item,
                    parent,
                    false,
                    RECOMMEND_ITEM_HOT_LIVE_LIST
                )
            )
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        override fun getItemCount(): Int {
            return commonSubElementList.size.let {
                if (enableJumpMore) it + 1 else it
            }
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is LiveItemViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun onBindViewHolderInner(holder: LiveItemViewHolder, position: Int) {
            val element = commonSubElementList.getOrNull(position) ?: return
            holder.itemView.setTag(R.id.main_id_item_data, element)
            holder.itemView.setTag(R.id.main_id_data_index, position)

            // 一列三行，用于判断处于第几列
            val remainder = commonSubElementList.size % 3
            val start = commonSubElementList.size.let {
                if (remainder == 0) it - 3 else it - remainder
            }

            val itemLayoutParams = holder.rootView.layoutParams
            if (position >= start) {
                // 最后一列
                val slideMoreGap = if (enableJumpMore) 37.dp else 0
                itemLayoutParams.width = getRpAdaptSize(375) - slideMoreGap
            } else {
                itemLayoutParams.width = getRpAdaptSize(337 - getOffset())
            }

            ImageManager.from(
                BaseApplication.getMyApplicationContext()
            ).displayImageNotIncludeDownloadCacheSizeInDp(
                holder.itemCoverView, element.cover, R.drawable.host_default_album, 70, 70
            )
            holder.itemPlaying.playAnimation()
            holder.itemTitleView.text = element.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleView)
            holder.itemSubNickname.text = buildSpannedString {
                append("主播: ${element.anchor?.nickName}")
                inSpans(SpaceSpan(8.dp), Spannable.SPAN_INCLUSIVE_EXCLUSIVE) {
                    append(" ")
                }
                append(element.anchor?.summary ?: "")
            }
            // 标签优先级：抽奖>榜单>分区，热度常驻
            var visibleCount = 0
            val hotScoreVisible = element.extraInfo?.hotScore.isNullOrBlank().not()
            holder.itemHotValue.setContent(element.extraInfo?.hotScore)
            if (hotScoreVisible) {
                holder.itemHotValue.visibility = View.VISIBLE
                visibleCount++
            } else {
                holder.itemHotValue.visibility = View.GONE
            }

            val lotteryTagType = LiveLotteryTagView.TagType.convert(element.extraInfo?.lotteryType)
            val lotteryTagVisible = lotteryTagType != null
            if (lotteryTagVisible) {
                holder.itemLotteryTag.visibility = View.VISIBLE
                holder.itemLotteryTag.setTagInfo(lotteryTagType!!)
                visibleCount++
            } else {
                holder.itemLotteryTag.visibility = View.GONE
            }

            val rankDesc = element.extraInfo?.rankName
            val rankTagVisible = rankDesc.isNullOrBlank().not()
            if (rankTagVisible) {
                holder.itemRankTag.visibility = View.VISIBLE
                holder.itemRankTag.text = rankDesc
                holder.itemRankTag.updateLayoutParams<MarginLayoutParams> {
                    marginStart = if (lotteryTagVisible) 6.dp else 0
                }
                visibleCount++
            } else {
                holder.itemRankTag.visibility = View.GONE
            }

            val categoryDesc = element.extraInfo?.parentCategoryName
            val categoryVisible = categoryDesc.isNullOrBlank().not() && (visibleCount < 3)
            if (categoryVisible) {
                holder.itemCategory.visibility = View.VISIBLE
                holder.itemCategory.text = categoryDesc
                holder.itemCategory.updateLayoutParams<MarginLayoutParams> {
                    marginStart = if (lotteryTagVisible || rankTagVisible) 6.dp else 0
                }
            } else {
                holder.itemCategory.visibility = View.GONE
            }

            if (hotScoreVisible) {
                holder.itemHotValue.updateLayoutParams<MarginLayoutParams> {
                    val leftVisible = lotteryTagVisible || rankTagVisible || categoryVisible
                    marginStart = if (leftVisible) 6.dp else 0
                }
            }

            updateSize(holder)

            val onCardClick = object : IClickExportListener {
                override fun onClick(isReport: Boolean, view: View?) {
                    if (element.landingPage.isNullOrBlank()) return

                    if (isReport) {
                        // 新首页-首页大卡模块  点击事件
                        val bigBrace = XMTraceApi.Trace()
                            .click(62176) // 用户点击时上报
                            .put("currPage", "newHomePage")
                            .put("modulePosition", (positionNew + 1).toString()) // 客户端传
                            .put("xmRequestId", recommendItemNew?.xmRequestId ?: "") // 客户端传
                        SpmTraceUtil.addSpmTraceInfo(
                            bigBrace,
                            moduleItem?.ubtV2,
                            (positionNew + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(bigBrace, (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2)
                        if (recommendItemNew?.isLocalCache == true) {
                            bigBrace.isLocalCache
                        }
                        bigBrace.createTrace()

                        // 新首页-直播间卡片  点击事件
                        val trace = XMTraceApi.Trace()
                            .click(62465) // 用户点击时上报
                            .put("currPage", "newHomePage")
                            .put("modulePosition", (positionNew + 1).toString()) // 客户端传
                            .put("xmRequestId", recommendItemNew?.xmRequestId ?: "") // 客户端传，去重用
                            .put("positionNew", (position + 1).toString()) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                            .put("rec_src", moduleItem.ubt?.recSrc ?: "") // 服务端传。
                            .put("rec_track", moduleItem.ubt?.recTrack ?: "") // 服务端传。
                            .put("ubtTraceId", moduleItem.ubt?.traceId ?: "") // 服务端传。
                            .put("contentId", (element.id ?: 0).toString()) // 客户端传。去重使用
                            .put("contentType", element.bizType ?: "") // 客户端传。去重使用
                            .put("exploreArea", ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()) // 1-100 曝光面积占比，客户端传

                        LiveLotteryTagView.TagType.convert(element.extraInfo?.lotteryType)?.also { tag ->
                            trace.put("rewardName", tag.desc) // 奖励名称
                        }
                        element.extraInfo?.rankName?.also { rank ->
                            trace.put("rankName", rank) // 榜单名称
                        }
                        SpmTraceUtil.addSpmTraceInfo(
                            trace,
                            moduleItem.ubtV2,
                            (positionNew + 1).toString(),
                            contentTitle = element.title,
                            contentPosition = (position + 1).toString()
                        )
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
                        RecommendNewUbtV2Manager.addUbtV2Data(trace, element.ubtV2)
                        if (recommendItemNew?.isLocalCache == true) {
                            trace.isLocalCache
                        }
                        trace.createTrace()
                    }

                    ToolUtil.clickUrlAction(fragment, element.landingPage!!, view)
                }
            }

            val onCardLongClick = {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (positionNew + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                if (!moduleItem.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(moduleItem.ubtV2)
                }
                if (!element.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(element.ubtV2)
                }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = element.bizType ?: ""
                traceMap["contentId"] = element.refId?.toString() ?: ""
                // 负反馈接口请求传参
                requestMap[HttpParamsConstants.PARAM_LIVE_ID] = element.id?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] = element.anchor?.uid.toString()
                requestMap["card_contentType"] = moduleItem.contentType ?: ""
                requestMap["card_bizType"] = moduleItem.bizType ?: ""
                requestMap["card_id"] = moduleItem.id?.toString() ?: ""

                val disLikeLeve2Build = DisLikeLeve2Build()
                disLikeLeve2Build.isFromAd = false
                disLikeLeve2Build.anchorName = element.anchor?.nickName
                disLikeLeve2Build.requestMap = requestMap
                disLikeLeve2Build.traceMap = traceMap
                disLikeLeve2Build.onFeedBackListener = object :
                    NewXmFeedBackPopDialog.IOnFeedBackListener() {
                    override fun onDialogShow(showSuccess: Boolean) {
                    }

                    override fun onFeedBack(list: List<XmFeedInnerModel>) {
                        val sourceType = recommendItemNew?.itemType ?: return
                        MainCommonRequest.getSingleLiveListItem(sourceType, moduleItem, object :
                            IDataCallBack<CommonSubElement> {
                            override fun onSuccess(subElement: CommonSubElement?) {
                                if (subElement == null) {
                                    removeItem(position)
                                    return
                                }
                                commonSubElementList[position] = subElement
                                notifyItemChanged(position)
                                // 使用这个  当前在最后一列时  系统定位不准 会滚动到前一列
                                (recyclerView.layoutManager as? GridLayoutManager)?.findFirstVisibleItemPosition()?.also { firstPos ->
                                    recyclerView.scrollToPosition(firstPos)
                                }
                                traceItemShow(
                                    positionNew, recommendItemNew, position, subElement, holder.itemView, moduleItem
                                )
                            }

                            override fun onError(code: Int, message: String?) {
                                removeItem(position)
                            }
                        })
                    }

                    private fun removeItem(position: Int) {
                        commonSubElementList.removeAt(position)
                        notifyItemRemoved(position)
                        notifyItemRangeChanged(position, commonSubElementList.size - position)
                    }
                }

                val moreFunLister = object : IMoreFuncListener() {
                    override fun onTopModuleClick() {
                        onCardClick.onClick(false)
                    }
                }

                val build: MoreFuncBuild = MoreFuncBuild.createLiveLongClickModel(
                    fragment, element.refId, element.bizType, moreFunLister, true, disLikeLeve2Build
                )
                build.trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", element.bizType ?: "")
                    put("contentId", element.refId?.toString() ?: "")
                    put("modulePosition",(positionNew + 1).toString())
                    put("positionNew",(position + 1).toString())
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    element.ubtV2?.let { it1 -> putAll(it1) }
                }

                XmMoreFuncManager.checkShowMorePage(build)
            }

            holder.itemView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) return@setOnClickListener
                onCardClick.onClick(true, it)
            }

            // 长按负反馈
            holder.itemView.setOnLongClickListener {
                onCardLongClick.invoke()
                true
            }
        }

        private fun updateSize(holder: LiveItemViewHolder) {
            RecommendCornerUtils.updateLiveCardCorner(holder.itemCoverView)
            holder.itemCoverView.updateLayoutParams {
                width = RecommendCornerUtils.getLiveCoverSize().toInt()
                height = RecommendCornerUtils.getLiveCoverSize().toInt()
            }

            holder.rootView.run {
                setPadding(
                    paddingLeft, RecommendCornerUtils.getLivePaddingSize(true),
                    paddingRight, RecommendCornerUtils.getLivePaddingSize(false)
                )
            }

            holder.itemTagGroup.updateLayoutParams<MarginLayoutParams> {
                topMargin = RecommendCornerUtils.getLiveShowTagGapSize()
            }
        }
    }

    companion object {
        var sLastRandomBg = 0

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        fun traceItemShow(
            position: Int,
            data: RecommendItemNew,
            index: Int,
            subElement: CommonSubElement,
            view: View,
            recommendCommonItem: RecommendCommonItem
        ) {
            // 新首页-直播间卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62466)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", data.xmRequestId ?: "") // 客户端传，去重用
                .put(
                    "positionNew",
                    (index + 1).toString()
                ) // 客户端传。item 在 card 中的位置。双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "") // 服务端传。
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "") // 服务端传。
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "") // 服务端传。
                .put("contentId", (subElement.id ?: 0).toString()) // 客户端传。去重使用
                .put("contentType", subElement.bizType ?: "") // 客户端传。去重使用
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                ) // 1-100 曝光面积占比，客户端传

            LiveLotteryTagView.TagType.convert(
                subElement.extraInfo?.lotteryType
            )?.also {
                trace.put("rewardName", it.desc) // 奖励名称
            }
            subElement.extraInfo?.rankName?.also {
                trace.put("rankName", it) // 榜单名称
            }
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                contentTitle = subElement.title,
                contentPosition = (index + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            trace.createTrace()

            HomeRealTimeTraceUtils.traceItemShow(data, recommendCommonItem, subElement, view, index)
        }
    }
}