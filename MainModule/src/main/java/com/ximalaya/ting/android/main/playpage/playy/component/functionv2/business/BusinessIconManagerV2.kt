package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.view.children
import com.airbnb.lottie.LottieAnimationView
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.play.data.PlayPageDynamicState
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.component.functionv2.view.YFunctionLayoutV2
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.skin.SkinChangeListener
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created on 2024/5/30.
 * <AUTHOR>
 * @email <EMAIL>
 */
class BusinessIconManagerV2(
    private val functionContainer: YFunctionLayoutV2,
    private val fragment2: BaseFragment2,
    val playContainer: IPlayContainer,
): SkinChangeListener {
    private var mSoundInfo: PlayingSoundInfo? = null
    private var isAudio = true

    private val bizMap = mutableMapOf<View, FunctionBtnBiz>()

    init {
        bizMap[functionContainer.firstBizLayout] = buildBiz(
            functionContainer.firstBizLayout,
            { view, badgeOffset -> functionContainer.setFirstBadge(view, badgeOffset) },
            listOf(
                BusinessType.KID,
                BusinessType.TIMBRE,
                BusinessType.DOWNLOAD
            ), { view -> functionContainer.set1BottomView(view) })

        bizMap[functionContainer.likeFrameLayout] = buildBiz(
            functionContainer.likeFrameLayout,
            { view, badgeOffset -> functionContainer.set2Badge(view, badgeOffset) },
            listOf(
                BusinessType.CUSTOM_LIKE,
                BusinessType.LIKE
            ),
            { _ -> {} }
        )

        bizMap[functionContainer.biz3Layout] = buildBiz(
            functionContainer.biz3Layout,
            { view, badgeOffset -> functionContainer.set3Badge(view, badgeOffset) },
            listOf(
                BusinessType.COMMENT,
                BusinessType.COLLECT
            ),
            { view -> functionContainer.set3BottomView(view) }
        )

        bizMap.values.forEach {
            it.bizList.forEach { biz -> biz.second.onCreate() }
        }

        playContainer.registerSkinChangeListener(this)
    }

    private fun buildBiz(
        view: ViewGroup,
        updateBadge: (View?, BadgeOffset?) -> Unit,
        bizTyeList: List<BusinessType>,
        updateBottomText: (View?) -> Unit
    ): FunctionBtnBiz {
        return FunctionBtnBiz(
            view,
            updateBadge,
            bizTyeList.map {
                Pair(it, it.implProvider.invoke(fragment2, playContainer).apply {
                    manager = this@BusinessIconManagerV2
                })
            },
            updateBottomText
        )
    }

    fun onDestroy() {
        bizMap.values.forEach {
            it.bizList.forEach { biz -> biz.second.onDestroy() }
        }
    }

    fun onResume() {
        bizMap.values.forEach {
            it.bizList.forEach { biz -> biz.second.onResume() }
        }
    }

    fun onSoundInfoLoad(soundInfo: PlayingSoundInfo?) {
        bizMap.values.forEach {
            it.bizList.forEach { biz -> biz.second.onSoundInfoLoad(soundInfo) }
        }
    }

    // 更新显示的业务图标
    fun updateDisplayedIcon(soundInfo: PlayingSoundInfo?, isAudio: Boolean = true) {
        if (soundInfo == null) return
        this.mSoundInfo = soundInfo
        this.isAudio = isAudio

        bizMap.values.forEach { functionBiz ->
            updateBiz(functionBiz)
        }
    }

    private fun updateBiz(functionBiz: FunctionBtnBiz) {
        val soundInfo = mSoundInfo ?: return
        val biz = functionBiz.findCandidate(soundInfo, isAudio)
        val type = biz?.first
        Logger.log("f_tag updateDisplayedIcon firstCanShowType ==> ${biz?.first}")

        val bizContainer = functionBiz.container
        if (type != null) {
            functionBiz.currentBizType = type
            val iconView = biz.second.provideIcon(bizContainer.context, soundInfo)
            if (iconView == null) {
                bizContainer.removeAllViews()
            } else if (iconView.parent != bizContainer) {
                (iconView.parent as? ViewGroup)?.removeView(iconView)

                bizContainer.removeAllViews()
                bizContainer.addView(iconView)
            }
            val badgeView = biz.second.providerBadgeView(bizContainer.context, soundInfo)
            val bottomView = biz.second.providerBottomView(bizContainer.context, soundInfo)
            functionBiz.updateBadge(badgeView?.badgeView, badgeView?.offset)
            functionBiz.updateBottomText(
                bottomView?.bottomView,
            )
            biz.second.afterUpdate(soundInfo)


//            bottomView?.bottomView?.also {
//                if (it is TextView) {
//                    if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
//                        it.setTextColor(PSkinManager.getBtnThemeColor())
//                    } else {
//                        it.setTextColor(Color.WHITE)
//                    }
//                }
//            }
            updateThemeColor(functionContainer, PSkinManager.getBtnThemeColor(), PSkinManager.isEnabled)
        }
    }

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        updateThemeColor(functionContainer, PSkinManager.getBtnThemeColor(), PSkinManager.isEnabled)
    }

    private fun updateThemeColor(container: ViewGroup, @ColorInt color: Int, enable: Boolean) {
        container.children.forEach {
            when(it) {
                is XmLottieAnimationView -> {
                    if (enable) {
                        it.setForegroundColor(color)
                    } else {
                        it.clearForegroundColor()
                    }
                }
                is ImageView -> {
                    if (enable) {
                        it.setColorFilter(color)
                    } else {
                        it.clearColorFilter()
                    }
                }
                is TextView -> {
                    if (enable) {
                        it.setTextColor(color)
                    } else {
                        it.setTextColor(Color.WHITE)
                    }
                }
                is ViewGroup -> updateThemeColor(it, color, enable)
                else -> {}
            }
        }
    }

    fun getCurrentShowType(): BusinessType? {
        return bizMap[functionContainer.firstBizLayout]?.currentBizType
    }

    fun getTimbreTitle(): String {
        return bizMap[functionContainer.firstBizLayout]?.bizList?.firstOrNull {
            it.first == BusinessType.TIMBRE
        }?.second?.getBadgeTitle()?: ""
    }

    fun updateDynamicData(dynamic: PlayPageDynamicState) {
        bizMap.values.forEach {
            it.bizList.forEach { biz -> biz.second.updateDynamicData(dynamic) }
        }
    }

    fun requestRefresh(bizView: IBusinessView) {
        fragment2.postOnUiThread {
            bizMap.forEach { entry ->
                val functionBiz = entry.value
                val found = functionBiz.bizList.firstOrNull { it.second == bizView }
                if (found != null) {
                    updateBiz(functionBiz)
                }
            }
        }
    }
}