package com.ximalaya.ting.android.main.dialog.universal.pvip

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.text.TextUtils
import android.view.View
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.vip.IVipFunctionAction
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by mark on 2025/3/5 19:26
 */
class PVipPaymentFragmentForDialog(
    vipSkuShelfInfo: VipSkuShelfInfo,
    dialogMaterial: DialogMaterialForPlatinumVip
) : BaseUniversalPaymentFragment(vipSkuShelfInfo, dialogMaterial) {

    private var mSkuUpgradePair: Pair<VipSkuItem, VipSkuItem.UpgradeProductModel?>? = null

    override fun getPageLogicName(): String {
        return TAG
    }

    override fun initSkuViewManager(
        fragment2: BaseFragment2,
        vipFuncRouter: IVipFunctionAction
    ): IVipFunctionAction.IVipSkuItemViewManager {
        return vipFuncRouter.generatePVipSkuItemViewManager(
            mSkuContainer,
            fragment2,
            mVipSkuShelfInfo
        )
    }

    override fun initBottomViewManager(
        fragment2: BaseFragment2,
        vipFuncRouter: IVipFunctionAction
    ): IVipFunctionAction.IVipBottomViewManager {
        return vipFuncRouter.generatePVipBottomViewManager(
            fragment2, mVipSkuShelfInfo,
            HashMap<String, String>(
                dialogMaterial.paymentMaterial?.transmissionParams ?: emptyMap()
            )
        )
    }


    override fun bindTitleInfo() {
        ViewStatusUtil.setBackgroundDrawableRes(R.drawable.main_bg_pvip_payment_top, mIvPageTopBg)
        ViewStatusUtil.setVisible(View.GONE, mTvPageIntro, mTvMoreMenu)
        getTitleInfo()?.let {
            ViewStatusUtil.setText(mTvPageIntro, it)
            ViewStatusUtil.setVisible(View.VISIBLE, mTvPageIntro)
        }
        mVipSkuShelfInfo.localVipBehaviorAction?.action?.let {
            var moreMenuText = mVipSkuShelfInfo.localVipBehaviorAction?.moreShelfText
            if (moreMenuText.isNullOrEmpty()) {
                moreMenuText = "更多套餐"
            }
            ViewStatusUtil.setText(mTvMoreMenu, moreMenuText)
            ViewStatusUtil.setVisible(View.VISIBLE, mTvMoreMenu)
            mTvMoreMenu.setOnClickListener {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
                    return@setOnClickListener
                }
                traceMoreMenuClick(moreMenuText, "platinumVip")
                if (mVipSkuShelfInfo.localVipBehaviorAction?.action?.platinumVipUrl.isNullOrEmpty()) {
                    mVipSkuShelfInfo.localVipBehaviorAction?.performAction(this, null)
                } else {
                    dialogMaterial.fragment?.let { fragment ->
                        ToolUtil.clickUrlAction(
                            fragment,
                            mVipSkuShelfInfo.localVipBehaviorAction?.action?.platinumVipUrl!!,
                            it
                        )
                    }
                }
                closeDialog()
            }
        }
    }

    private fun getTitleInfo(): String? {
        val conf = ConfigureCenter.getInstance().getString(
            "toc",
            "vip_combine_dialog_materials", null
        )
        conf ?: return mVipSkuShelfInfo.localVipBehaviorAction?.subTitle
        val json = try {
            JSONObject(conf)
        } catch (e: Exception) {
            null
        }
        val subTitle = json?.optString("platinumVipSubTitle")
        if (TextUtils.isEmpty(subTitle)) {
            return mVipSkuShelfInfo.localVipBehaviorAction?.subTitle
        }
        return subTitle
    }

    override fun onSkuUpgradeMonthClick(view: View, item: VipSkuItem) {
        if (mChosenSku == item) {
            if (mSkuUpgradePair == null) {
                mSkuUpgradePair = Pair(item, null)
            }
            traceUpgradeItemClick()
            registerReceiver()
            PlatinumVipUpgradeMonthChooseDialog.showDialog(
                mVipSkuShelfInfo,
                dialogMaterial.source,
                item,
                mSkuUpgradePair,
                mPageHeight,
                childFragmentManager
            )
        }
    }

    private val mUpgradeMonthChooseReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (canUpdateUi()) {
                val itemId = intent?.getStringExtra(BundleKeyConstants.KEY_ITEM_ID)?:""
                val quantity = intent?.getIntExtra(PlatinumVipUpgradeMonthChooseDialog.KEY_QUANTITY,0)?:0
                if (intent?.action == PlatinumVipUpgradeMonthChooseDialog.ACTION) {
                    if (mChosenSku != null) {
                        mSkuUpgradePair = Pair(mChosenSku!!, mChosenSku!!.upgradeProductVos.firstOrNull {
                            it.itemId == itemId && it.quantity == quantity
                        })
                    }
                    mVipSkuItemViewManager?.updateUpgradeMonth()
                    mVipBottomViewManager?.updateUpgradeMonth()
                }
            }
        }
    }
    private var hasRegisterReceiver = AtomicBoolean(false)
    private fun registerReceiver() {
        if (hasRegisterReceiver.compareAndSet(false, true)) {
            LocalBroadcastManager.getInstance(mContext).registerReceiver(
                mUpgradeMonthChooseReceiver,
                IntentFilter(PlatinumVipUpgradeMonthChooseDialog.ACTION)
            )
        }
    }

    private fun unregisterReceiver() {
        if (hasRegisterReceiver.compareAndSet(true, false)) {
            LocalBroadcastManager.getInstance(mContext)
                .unregisterReceiver(mUpgradeMonthChooseReceiver)
        }
    }

    override fun onSkuUpgradeMonthViewShow(item: VipSkuItem) {
        // 统一半浮层-升级  控件曝光
        XMTraceApi.Trace()
            .setMetaId(67817)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put(
                "xmRequestId",
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_VIP_UNIVERSAL_CUSTOM_DIALOG)
            )
            .put("tabName", "platinumVip")
            .put("trackId", "" + mVipSkuShelfInfo.trackId)
            .put("albumId", "" + mVipSkuShelfInfo.albumId)
            .put(
                "sceneName",
                dialogMaterial.source
            ) // 后端透传product 自制页专辑购买 productTrackList 自制页声音列表 productMiddleBar 自制页中插条 albumTrackList 专辑页声音列表 newPlayAfterSample 新播放页试听结束 playAfterSample 老播放页试听结束 afterSample 全局试听结束 playBeforeSample 新老播放页小黄条 adLocking 广告页
            .put(XmRequestIdManager.CONT_TYPE, "upgradeMonth")
            .put(XmRequestIdManager.CONT_ID, item.itemId)
            .createTrace()
    }

    override fun onConfirmBtnClick(item: VipSkuItem) {
        // 通知点击购买
    }

    override fun getUpgradeSkuSet(): Pair<VipSkuItem, VipSkuItem.UpgradeProductModel?>? {
        return mSkuUpgradePair
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver()
    }

    private fun traceUpgradeItemClick() {
        // 统一半浮层-升级  弹框控件点击
        XMTraceApi.Trace()
            .setMetaId(67818)
            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
            .put("currPage", null)
            .put(
                "xmRequestId",
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_VIP_UNIVERSAL_CUSTOM_DIALOG)
            )
            .put("tabName", "platinumVip")
            .put("trackId", "" + mVipSkuShelfInfo.trackId)
            .put("albumId", "" + mVipSkuShelfInfo.albumId)
            .put(
                "sceneName",
                dialogMaterial.source
            ) // 后端透传product 自制页专辑购买 productTrackList 自制页声音列表 productMiddleBar 自制页中插条 albumTrackList 专辑页声音列表 newPlayAfterSample 新播放页试听结束 playAfterSample 老播放页试听结束 afterSample 全局试听结束 playBeforeSample 新老播放页小黄条 adLocking 广告页
            .createTrace()
    }
}