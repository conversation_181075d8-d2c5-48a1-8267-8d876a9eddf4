package com.ximalaya.ting.android.main.playpage.manager

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18321019958
 * @wiki
 * @des 播放页右上角直播入口管理器，目前是用来处理右上角是直播广告类型入口
 * @server
 * @since 2025/7/21
 */
object PlayPageLiveEntryManager {

    const val TAG = "PlayPageLiveEntryManager"

    const val ACTION_PLAY_LIVE_AD_ENTRY_SHOW = "action_play_live_entry_show"
    const val ACTION_PLAY_LIVE_AD_ENTRY_CLICK = "action_play_live_entry_click"
    const val KEY_ICON_URL = "icon_url"
    const val KEY_TITLE = "title"
    const val KEY_REQUEST_ID = "requestId"

    var currentLiveAdRequestId:String? = "-1"


    @JvmStatic
    fun playPageLiveEntryShow(
        rightRecommendInfo: PlayPageMinorData.RightRecommendInfo?,
        requestId: String?,
        trackId:String?,
        albumId:String?,
        description:String?,
        showTime:String?
    ) {
        val logo: String = rightRecommendInfo?.userLogos?.getOrNull(0)?.logo ?: ""
        val title = rightRecommendInfo?.recReason ?: ""
        Logger.d(TAG, "playPageLiveEntryShow $logo  \n $title")
        if(currentLiveAdRequestId != requestId){
            LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(
                Intent(ACTION_PLAY_LIVE_AD_ENTRY_SHOW).apply {
                    this.putExtra(KEY_ICON_URL, logo)
                    this.putExtra(KEY_TITLE, title)
                    this.putExtra(KEY_REQUEST_ID, requestId)
                }
            )
        }
        currentLiveAdRequestId = requestId;
        // 新声音播放页-直播入口广告  控件曝光
        XMTraceApi.Trace()
            .setMetaId(69307)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put("xmRequestId", requestId)
            .put(XmRequestIdManager.CONT_ID,trackId)
            .put(XmRequestIdManager.CONT_TYPE,"newPlayLive")
            .put(XmRequestIdManager.XM_REQUEST_ID,requestId)
            .put("currTrackId", trackId)
            .put("currAlbumId", albumId)
            .put("showTime", showTime) // 是因为暂停还是非暂停（进入/切换下一个声音）
            .put("description", description) // tips 若未被频控，能显示在则传 tips 文案，若被频控，无 tips 展示则不传该值,如果要取PV-CTR得取为空值的情况
            .put("picUrl", logo)
            .put("title", title)
            .createTrace()
    }

    @JvmStatic
    fun playPageLiveEntryClick(
        rightRecommendInfo: PlayPageMinorData.RightRecommendInfo?,
        requestId: String?,
        trackId:String?,
        albumId:String?,
        description:String?,
        showTime:String?
    ) {
        val logo: String = rightRecommendInfo?.userLogos?.getOrNull(0)?.logo ?: ""
        val title = rightRecommendInfo?.recReason ?: ""
        Logger.d(TAG, "playPageLiveEntryClick $logo  \n $title")
        LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).sendBroadcast(
            Intent(ACTION_PLAY_LIVE_AD_ENTRY_CLICK).apply {
                this.putExtra(KEY_ICON_URL, logo)
                this.putExtra(KEY_TITLE, title)
                this.putExtra(KEY_REQUEST_ID, requestId)
            }
        )

        // 新声音播放页-直播入口广告  点击事件
        XMTraceApi.Trace()
            .click(69306) // 用户点击时上报
            .put("currPage", "newPlay")
            .put(XmRequestIdManager.XM_REQUEST_ID, requestId)
            .put("currTrackId", trackId)
            .put("currAlbumId", albumId)
            .put("showTime", showTime) // 是因为暂停还是非暂停（进入/切换下一个声音）
            .put("description", description) // tips 若未被频控，能显示在则传 tips 文案，若被频控，无 tips 展示则不传该值,如果要取PV-CTR得取为空值的情况
            .put("picUrl", logo)
            .put("title", title)
            .createTrace()
    }
}