package com.ximalaya.ting.android.main.manager.myspace.footPrintV2;

import android.content.Context;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.history.AlbumFootPrintUtil;
import com.ximalaya.ting.android.host.manager.history.MyFootPrintAlbum;
import com.ximalaya.ting.android.host.manager.history.MyFootPrintModel;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.share.panel.SharePanelType;
import com.ximalaya.ting.android.host.model.ad.ShareAdRequestParams;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.model.dialog.ItemType;
import com.ximalaya.ting.android.host.model.mylisten.SubscribeFollowStatus;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.view.dialog.BottomWithSubscribeFollowDialog;
import com.ximalaya.ting.android.main.albumModule.other.SimilarRecommendFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.MyFootPrintFragmentNew;
import com.ximalaya.ting.android.main.fragment.myspace.child.MyFootPrintFragmentV2;
import com.ximalaya.ting.android.main.util.TraceUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by 5Greatest on 2021.02.04
 *
 * <AUTHOR>
 * On 2021/2/4
 */
public class MyFootPrintPresenterV2 {
    public static final int PAGE_ID_START = 1;

    private WeakReference<MyFootPrintFragmentV2> mFragmentReference;
    // 舔狗
    private MyFootPrintRequesterV2 mRequester;

    private boolean isLoadingMore = false;
    private AtomicInteger currentRequestPage = new AtomicInteger(0);
    private int maxPage = Integer.MAX_VALUE;
    private int totalCount = 0;

    private AtomicBoolean hasLogin = new AtomicBoolean(false);

    private boolean isDuringDelete = false;
    private List<MyFootPrintAlbum> orderedAlbums = new CopyOnWriteArrayList<>();
    private Map<Long, MyFootPrintAlbum> albumIdMap = new ConcurrentHashMap<>();
    private Set<MyFootPrintAlbum> selectedAlbums = new HashSet<>();

    // 要显示"今天"的albumId
    private long todayFirstId = AlbumFootPrintUtil.DEFAULT_ID;
    // 要显示"昨天"的albumId
    private long yesterdayFirstId = AlbumFootPrintUtil.DEFAULT_ID;
    // 要显示"更早"的albumId
    private long ancientFirstId = AlbumFootPrintUtil.DEFAULT_ID;

    public MyFootPrintPresenterV2(MyFootPrintFragmentV2 fragment) {
        this.mFragmentReference = new WeakReference<>(fragment);
        this.mRequester = new MyFootPrintRequesterV2(this);
    }

    ////////////////////////////////////////////////
    /////////     setter and getter    /////////////
    /////////           start          /////////////
    ////////////////////////////////////////////////

    public void setMaxPage(int maxPage) {
        this.maxPage = maxPage;
    }

    public int getMaxPage() {
        return maxPage;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public void setCurrentRequestPageId(int pageId) {
        currentRequestPage.set(pageId);
    }

    public int getCurrentRequestPage() {
        return currentRequestPage.get();
    }

    public boolean isDuringDelete() {
        return isDuringDelete;
    }

    public void setDuringDelete(boolean duringDelete) {
        isDuringDelete = duringDelete;
    }

    public Map<Long, MyFootPrintAlbum> getAlbumIdMap() {
        return albumIdMap;
    }

    public void setOrderedAlbums(List<MyFootPrintAlbum> orderedAlbums) {
        this.orderedAlbums = orderedAlbums;
    }

    public List<MyFootPrintAlbum> getOrderedAlbums() {
        return orderedAlbums;
    }

    public Set<MyFootPrintAlbum> getSelectedAlbums() {
        return selectedAlbums;
    }

    public void setTodayFirstId(long todayFirstId) {
        this.todayFirstId = todayFirstId;
    }

    public void setYesterdayFirstId(long yesterdayFirstId) {
        this.yesterdayFirstId = yesterdayFirstId;
    }

    public void setAncientFirstId(long ancientFirstId) {
        this.ancientFirstId = ancientFirstId;
    }

    public long getTodayFirstId() {
        return todayFirstId;
    }

    public long getYesterdayFirstId() {
        return yesterdayFirstId;
    }

    public long getAncientFirstId() {
        return ancientFirstId;
    }

    public boolean isHasLogin() {
        return hasLogin.get();
    }

    public AtomicBoolean getHasLogin() {
        return hasLogin;
    }

    ////////////////////////////////////////////////
    /////////     setter and getter    /////////////
    /////////            end           /////////////
    ////////////////////////////////////////////////

    public void requestDataFirstTime() {
        currentRequestPage.set(PAGE_ID_START);
        mRequester.requestFootPrintRecord(currentRequestPage.get(), new MyFootPrintRequesterV2.ICallBack() {
            @Override
            public void onSuccess(MyFootPrintModel object) {
                currentRequestPage.incrementAndGet();
                if (null != getFragment() && object != null) {
                    LiveFootPrintListFilterUtils.filterLiveFootPrint(object.albumInfoMobileResultList, () -> {
                        if (getFragment() != null) {
                            getFragment().getDataProcessManager().prepareData(object, true);
                            getFragment().updateUi(MyFootPrintFragmentNew.MSG_UPDATE_UI_ON_FIRST_TIME);
                            getFragment().onRefreshComplete(object.pageId < object.maxPageId);
                        }
                        return null;
                    });
                }
            }

            @Override
            public void onFail(int code, String msg) {
                if (null != getFragment()) {
                    getFragment().onPageLoadingCompleted(BaseFragment.LoadCompleteType.NETWOEKERROR);
                    getFragment().onRefreshComplete(false);
                }
            }
        });
    }

    public void requestMoreData(boolean clearOldRecord) {
        if (isLoadingMore || maxPage < currentRequestPage.get()) {
            return;
        }
        isLoadingMore = true;
        mRequester.requestFootPrintRecord(currentRequestPage.get(), new MyFootPrintRequesterV2.ICallBack() {
            private final boolean fClearOldRecord = clearOldRecord;

            @Override
            public void onSuccess(MyFootPrintModel object) {
                isLoadingMore = false;
                currentRequestPage.incrementAndGet();
                if (null != getFragment() && object != null) {
                    LiveFootPrintListFilterUtils.filterLiveFootPrint(object.albumInfoMobileResultList, () -> {
                        if (getFragment() != null) {
                            getFragment().getDataProcessManager().prepareData(object, fClearOldRecord);
                            getFragment().updateUi(MyFootPrintFragmentNew.MSG_UPDATE_UI_ON_AFTER_LOAD_MORE);
                        }
                        return null;
                    });
                }
            }

            @Override
            public void onFail(int code, String msg) {
                isLoadingMore = false;
            }
        });
    }

    public void checkShowBottomMoreDialog(MyFootPrintAlbum album, int position) {
        CommonRequestM.getSubscribeFollowStatus(album.itemId, album.uid, new IDataCallBack<SubscribeFollowStatus>() {
            @Override
            public void onSuccess(@Nullable SubscribeFollowStatus data) {
                if (data != null) {
                    data.setAnchorUid(album.uid);
                }
                showBottomMoreDialog(album, data, position);
            }

            @Override
            public void onError(int code, String message) {
                showBottomMoreDialog(album, null, position);
            }
        });
    }

    public void showBottomMoreDialog(MyFootPrintAlbum album, SubscribeFollowStatus data, int position) {
        if (BaseApplication.getMainActivity() == null) {
            return;
        }
        if (album.itemStatus == 2 && data != null) {
            data.setAnchorUid(0);
        }
        List<BaseDialogModel> models = new ArrayList<>();
        if (album.type == MyFootPrintAlbum.TYPE_ALBUM && album.itemStatus != 2) {
            models.add(new BaseDialogModel(ItemType.FIND_SIMILAR, 0));
            models.add(new BaseDialogModel(ItemType.SHARE, 1));
            models.add(new BaseDialogModel(ItemType.DELETE, 2));
        } else {
            models.add(new BaseDialogModel(ItemType.DELETE, 0));
        }
        AlbumM albumM = new AlbumM();
        albumM.setId(album.itemId);
        albumM.setAlbumTitle(album.itemTitle);
        albumM.setCoverUrlSmall(album.itemCoverUrl);
        BottomWithSubscribeFollowDialog bottomDialog = new BottomWithSubscribeFollowDialog(BaseApplication.getMainActivity(), "mySpace9.0", "历史-浏览历史", models, "", data, (dialog, model) -> {
            dialog.dismiss();
            switch (model.type) {
                case FIND_SIMILAR:
                    toSimilar(albumM);
                    TraceUtil.clickMoreTrace(album.type, String.valueOf(album.itemId), "", position + 1, TimeHelper.getTimeModuleTitle(album.createdAt), "找相似",
                            String.valueOf(album.liveStatus), String.valueOf(album.childId));
                    break;
                case SHARE:
                    shareAlbum(albumM);
                    TraceUtil.clickMoreTrace(album.type, String.valueOf(album.itemId), "", position + 1, TimeHelper.getTimeModuleTitle(album.createdAt), "分享",
                            String.valueOf(album.liveStatus), String.valueOf(album.childId));
                    break;
                case DELETE:
                    delSingleItem(album);
                    TraceUtil.clickMoreTrace(album.type, String.valueOf(album.itemId), "", position + 1, TimeHelper.getTimeModuleTitle(album.createdAt), "删除",
                            String.valueOf(album.liveStatus), String.valueOf(album.childId));
                    break;
                default:
                    break;
            }
        });
        bottomDialog.show();
        bottomDialog.setExtraInfo(album.type, album.itemId, position, TimeHelper.getTimeModuleTitle(album.createdAt),album.liveStatus,album.childId);
    }

    private void delSingleItem(MyFootPrintAlbum album) {
        if (getFragment() != null && getFragment().getDataProcessManager() != null) {
            getFragment().getDataProcessManager().operateDeleteSingle(album);
        }
    }

    private void toSimilar(Album album) {
        if (album == null) {
            return;
        }
        if (getFragment() != null) {
            getFragment().startFragment(SimilarRecommendFragment.newInstanceByAlbumId(album.getId(), "相似推荐"));
        }
    }

    private void shareAlbum(AlbumM album) {
        if (album == null) {
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(
                SharePanelType.FUNCTION_1, "mySpace9.0",
                ICustomShareContentType.SHARE_TYPE_ALBUM
        );
        contentModel.setAlbumModel(album);
        contentModel.mShareAdRequestParams =
                new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_ALBUM, album.getId() + "");
        new ShareManager(BaseApplication.getTopActivity(), contentModel).showSharePanelDialog();
    }

    public Context getContext() {
        if (null == getFragment()) {
            return BaseApplication.getMyApplicationContext();
        } else {
            return getFragment().getContext();
        }
    }

    private MyFootPrintFragmentV2 getFragment() {
        if (null == mFragmentReference
                || null == mFragmentReference.get()
                || !mFragmentReference.get().canUpdateUi()) {
            return null;
        }
        return mFragmentReference.get();
    }
}
