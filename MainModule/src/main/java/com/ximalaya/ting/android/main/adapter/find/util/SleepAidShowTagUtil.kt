package com.ximalaya.ting.android.main.adapter.find.util

import android.content.Context
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.AlbumRateColorUtil
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.ShowTagManager
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.read.utils.checkActivity

/**
 * 睡眠卡ShowTag工具类
 * 专门用于睡眠卡场景的标签显示，不处理角标类型
 */
object SleepAidShowTagUtil {

    // 标签类型常量
    const val TYPE_ALBUM_TITLE = "albumTitle"
    const val TYPE_COUNT_PLAY = "count/play"
    const val TYPE_OTHER = "other"
    const val TYPE_DURATION = "duration"
    const val TYPE_PLAY_COMPLETE = "play_complete"

    // 主题常量
    const val THEME_DEFAULT = 0
    const val THEME_WHITE = 1
    const val THEME_IP = 2

    // 优先级常量
    private const val PRIORITY_REMOVE = -1f // 优先移除
    private const val PRIORITY_PLAY_TIME = 2f // 播放时长
    private const val PRIORITY_PLAY_COUNT = 3f // 播放量
    const val PRIORITY_PLAY_COMPLETE = 4f // 播放完成

    // 间距常量
    private val gapWidth: Int = 8.dp // 控件间距
    private val durationGapWidth: Int = 11.dp // 播放时长控件间距

    // Drawable缓存，避免重复创建
    private var mAnchorNameDrawable: MutableMap<Int, Drawable> = mutableMapOf()
    private var mDurationDrawable: MutableMap<Int, Drawable> = mutableMapOf()
    private var mPlayDrawable: MutableMap<Int, Drawable> = mutableMapOf()

    /**
     * 睡眠卡ShowTag配置类
     */
    data class SleepAidShowTagConfig(
        val textSize: Float = 13f, // 默认字体大小
        val textColor: Int? = null, // 文本颜色，null表示使用主题默认颜色
        val themeEnum: Int = THEME_DEFAULT, // 主题
        val isBold: Boolean = true, // 是否加粗
        val trackId: Long? = null // 音轨ID
    )

    /**
     * 绑定睡眠卡ShowTag视图
     * @param warpLayout 容器布局
     * @param tags 标签列表
     * @param containerWidthInPx 容器宽度
     * @param config 配置参数
     * @return 是否成功绑定
     */
    fun bindSleepAidTagsView(
        warpLayout: LinearLayout?,
        tags: MutableList<ShowTag>?,
        containerWidthInPx: Int,
        config: SleepAidShowTagConfig = SleepAidShowTagConfig()
    ): Boolean {
        if (warpLayout == null) {
            return false
        }

        if (!warpLayout.context.checkActivity()) {
            return false
        }

        if (tags.isNullOrEmpty()) {
            ViewStatusUtil.setVisible(View.GONE, warpLayout)
            return false
        }

        // 过滤掉角标类型的标签
        val filteredTags = tags.filter { it.type != "jiaobiao" }.toMutableList()

        if (filteredTags.isEmpty()) {
            ViewStatusUtil.setVisible(View.GONE, warpLayout)
            return false
        }

        warpLayout.gravity = Gravity.CENTER_VERTICAL
        warpLayout.orientation = LinearLayout.HORIZONTAL
        warpLayout.removeAllViews()
        ViewStatusUtil.setVisible(View.VISIBLE, warpLayout)

        filteredTags.forEachIndexed { tagIndex, tag ->
            if (!tag.type.isNullOrEmpty() && !tag.tag.isNullOrEmpty()) {
                val context = warpLayout.context
                val childView = TextView(context)
                childView.gravity = Gravity.CENTER_VERTICAL or Gravity.START
                childView.maxLines = 1
                childView.textSize = config.textSize
                childView.includeFontPadding = false
                childView.contentDescription = tag.tag
                childView.typeface = if (config.isBold) {
                    Typeface.create("sans-serif-light", Typeface.BOLD)
                } else {
                    Typeface.defaultFromStyle(Typeface.NORMAL)
                }

                val layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
                )
                // 标签间隔
                layoutParams.marginEnd = if (tagIndex != filteredTags.size - 1) {
                    gapWidth
                } else {
                    0
                }
                warpLayout.addView(childView, layoutParams)

                val textColor =
                    config.textColor ?: getColor(context, getLabelColor(config.themeEnum))
                val spanUtils = SpanUtils.with(childView)

                when (tag.type) {
                    "socialTag", "customTitle", "summary", TYPE_ALBUM_TITLE -> {
                        tag.canZip = true
                        spanUtils
                            .append(tag.tag!!)
                            .setForegroundColor(textColor)

                        // 专辑标题类别都优先移除
                        tag.priority = PRIORITY_REMOVE
                        tag.curWidth = getRealWidth(childView, tag.tag)
                    }

                    "scoreTag", "award" -> {
                        childView.setPadding(0, 2f.dp, 0, 1.5f.dp)
                        childView.textSize = config.textSize - 0.5f
                        val scoreDouble = tag.value?.toDoubleOrNull() ?: 0.toDouble()
                        val recScoreInt = (scoreDouble * 100).toInt() // 推荐值百分数

                        val labelColor: Int = if (config.themeEnum == THEME_WHITE) {
                            getColor(context, R.color.host_color_99ffffff)
                        } else if (tag.type == "award") {
                            getColor(context, R.color.host_color_b08f68)
                        } else {
                            AlbumRateColorUtil.getScoreLabelColor(recScoreInt)
                        }

                        val mScoreTagLeftDrawable =
                            getDrawable(context, R.drawable.host_recommend_tags_score_left)
                                .mutate()
                        mScoreTagLeftDrawable.setBounds(0, 0, 8.dp, 14.dp)
                        val mScoreTagRightDrawable = getDrawable(
                            context, R.drawable.host_recommend_tags_score_right
                        ).mutate()
                        mScoreTagRightDrawable.setBounds(0, 0, 8.dp, 14.dp)
                        mScoreTagLeftDrawable.colorFilter =
                            PorterDuffColorFilter(labelColor, PorterDuff.Mode.SRC_IN)
                        mScoreTagRightDrawable.colorFilter =
                            PorterDuffColorFilter(labelColor, PorterDuff.Mode.SRC_IN)

                        spanUtils
                            .appendVerticalCenterImage(mScoreTagLeftDrawable, true)
                            .appendSpace(1)
                            .append(tag.tag!!)
                            .setForegroundColor(labelColor)
                            .setTypeface(Typeface.DEFAULT_BOLD)
                            .appendSpace(1)
                            .appendVerticalCenterImage(mScoreTagRightDrawable, true)

                        tag.curWidth = getRealWidth(childView, tag.tag) + 18.dp
                    }

                    "anchorName" -> {
                        var drawable = mAnchorNameDrawable[textColor]
                        if (drawable == null) {
                            drawable = getDrawable(
                                context,
                                R.drawable.host_recommend_author_name_tag_icon
                            ).mutate()
                            drawable.setBounds(0, 0, 10.dp, 10.dp)
                            mAnchorNameDrawable[textColor] = drawable
                        }
                        drawable.colorFilter = PorterDuffColorFilter(
                            textColor, PorterDuff.Mode.SRC_IN
                        )

                        spanUtils
                            .appendVerticalCenterImage(drawable, true)
                            .appendSpace(1.dp)
                            .append(tag.tag!!)
                            .setForegroundColor(textColor)

                        tag.curWidth = getRealWidth(childView, tag.tag) + 11.dp
                    }

                    TYPE_DURATION -> {
                        tag.priority = PRIORITY_PLAY_TIME

                        val duration = tag.value?.toLong() ?: 0L
                        val showName = ShowTagManager.getShowPlayTime(
                            tag.tag,
                            config.trackId,
                            duration * 1000L
                        )
                        createDuration(config.themeEnum, showName, spanUtils, textColor)
                        childView.setTag(R.id.host_id_recommend_show_tag_content, showName)
                        calculateDurationWidth(tag, showName, childView)
                    }

                    TYPE_COUNT_PLAY -> {
                        tag.priority = PRIORITY_PLAY_COUNT
                        var drawable = mPlayDrawable[textColor]
                        if (drawable == null) {
                            drawable = getDrawable(
                                context,
                                R.drawable.host_recommend_play_count_tag_icon
                            ).mutate()
                            drawable.setBounds(0, 0, 10.dp, 10.dp)
                            mPlayDrawable[textColor] = drawable
                        }
                        drawable.colorFilter = PorterDuffColorFilter(
                            textColor, PorterDuff.Mode.SRC_IN
                        )

                        try {
                            val playCount = tag.value?.toLong() ?: 0L
                            if (playCount > 0) {
                                tag.tag = if (RecommendShowTagsUtilNew.isUseNewPlayCount()) {
                                    StringUtil.getPlayCountStrFromShowTag(playCount)
                                } else {
                                    StringUtil.getFriendlyNumStrNew(playCount)
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }

                        spanUtils
                            .appendVerticalCenterImage(drawable, true)
                            .append(tag.tag!!)
                            .setForegroundColor(textColor)

                        childView.contentDescription = "播放量${tag.tag}"
                        tag.curWidth = getRealWidth(childView, tag.tag) + 10.dp
                    }

                    else -> {
                        spanUtils
                            .append(tag.tag!!)
                            .setForegroundColor(textColor)

                        tag.curWidth = getRealWidth(childView, tag.tag)
                    }
                }

                spanUtils.create()
                childView.setTag(R.id.host_id_recommend_show_tag_tv, tag)
            }
        }

        if (checkShowLabel(warpLayout, filteredTags, containerWidthInPx)) {
            warpLayout.setTag(R.id.host_id_recommend_show_tag_list, filteredTags)
            warpLayout.setTag(R.id.host_id_recommend_show_tag_theme, config.themeEnum)
            return true
        }

        warpLayout.removeAllViews()
        warpLayout.setTag(R.id.host_id_recommend_show_tag_list, null)
        warpLayout.setTag(R.id.host_id_recommend_show_tag_theme, null)
        ViewStatusUtil.setVisible(View.GONE, warpLayout)

        return false
    }

    /**
     * 计算播放时长标签宽度
     */
    fun calculateDurationWidth(showTag: ShowTag, showName: String, childView: View) {
        showTag.curWidth = getRealWidth(childView, showName) + durationGapWidth
    }

    /**
     * 创建播放时长标签
     */
    fun createDuration(
        themeEnum: Int,
        showName: String,
        spanUtils: SpanUtils,
        customTextColor: Int? = null
    ) {
        val context = BaseApplication.getMyApplicationContext()
        val textColor = customTextColor ?: getColor(context, getLabelColor(themeEnum))
        var drawable = mDurationDrawable[textColor]
        if (drawable == null) {
            drawable =
                getDrawable(context, R.drawable.host_recommend_author_duration_tag_icon).mutate()
            drawable.setBounds(0, 0, 10.dp, 10.dp)
            mDurationDrawable[textColor] = drawable
        }
        drawable.colorFilter = PorterDuffColorFilter(
            textColor, PorterDuff.Mode.SRC_IN
        )

        spanUtils
            .appendVerticalCenterImage(drawable, true)
            .appendSpace(1.dp)
            .append(showName)
            .setForegroundColor(textColor)
    }

    /**
     * 检查是否显示标签
     */
    fun checkShowLabel(
        warpLayout: LinearLayout?,
        tags: MutableList<ShowTag>,
        containerWidthInPx: Int
    ): Boolean {
        if (warpLayout == null || !warpLayout.context.checkActivity() || warpLayout.childCount == 0) {
            return false
        }

        if (tags.isEmpty()) {
            ViewStatusUtil.setVisible(View.GONE, warpLayout)
            return false
        }

        // 所有控件的宽度
        var totalTvWidth = tags.sumOf { it.curWidth?.toInt() ?: 0 }
        // 控件间隔
        totalTvWidth += gapWidth * (tags.size - 1).coerceAtLeast(0)

        if (containerWidthInPx - totalTvWidth >= 0) {
            resetEndViewMargin(warpLayout)
            return true
        }

        if (tags.size == 1) {
            // 只有一个能压缩则认为可以显示
            val childView = warpLayout.getChildAt(0) as? TextView
            if (childView != null) {
                childView.ellipsize = TextUtils.TruncateAt.END
                resetEndViewMargin(warpLayout)
                return true
            }

            ViewStatusUtil.setVisible(View.GONE, warpLayout)
            return false
        }

        val checkRemove = {
            var index = -1
            var priority: Float? = null

            for (i in tags.indices) {
                val tag = tags[i]
                val tagPriority = tag.priority ?: 0f
                // 找优先级最低的标签，同一优先级最右侧标签优先级最低
                if (priority == null || tagPriority <= priority) {
                    index = i
                    priority = tagPriority
                }
            }

            if (index != -1 && index < tags.size) {
                try {
                    tags.removeAt(index)
                    if (index < warpLayout.childCount) {
                        val view = warpLayout.getChildAt(index)
                        view?.let {
                            warpLayout.removeView(it)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        // 正常只会有一个，但是怕能压缩的有多个，优先取优先级低的
        val zipTag = tags.filter { it.canZip == true }.sortedBy { it.priority }.firstOrNull()
        val noZipTags = tags.filter { it != zipTag }
        val zipIndex = tags.indexOf(zipTag)

        // 不能压缩的控件宽度
        val noZipTagWidth = noZipTags.sumOf {
            it.curWidth?.toInt() ?: 0
        } + gapWidth * (noZipTags.size - 1).coerceAtLeast(0)

        if (noZipTagWidth > 0.85f * containerWidthInPx) {
            // 不能压缩的控件宽度大于0.85则认为剩余空间太少，需要移除，否则显示压缩控件
            checkRemove()
            return checkShowLabel(warpLayout, tags, containerWidthInPx)
        } else {
            if (zipIndex >= 0 && zipIndex < warpLayout.childCount) {
                val childView = warpLayout.getChildAt(zipIndex) as? TextView
                childView?.ellipsize = TextUtils.TruncateAt.END
                (childView?.layoutParams as? LinearLayout.LayoutParams)?.weight = 1f
            }
            resetEndViewMargin(warpLayout)
            return true
        }
    }

    /**
     * 重置最后一个视图的边距
     */
    private fun resetEndViewMargin(warpLayout: LinearLayout?) {
        if (warpLayout == null || warpLayout.childCount == 0) {
            return
        }

        val childView = warpLayout.getChildAt(warpLayout.childCount - 1)
        if (childView == null || !childView.context.checkActivity()) {
            return
        }

        val params = childView.layoutParams as MarginLayoutParams
        params.marginEnd = 0
        childView.layoutParams = params
    }

    /**
     * 获取Drawable资源
     */
    private fun getDrawable(context: Context, id: Int): Drawable {
        return ResourcesCompat.getDrawable(context.resources, id, context.theme)!!
    }

    /**
     * 获取颜色资源
     */
    private fun getColor(context: Context, id: Int): Int {
        return ResourcesCompat.getColor(context.resources, id, context.theme)
    }

    /**
     * 获取标签颜色
     */
    private fun getLabelColor(themeEnum: Int): Int {
        return when (themeEnum) {
            THEME_WHITE -> R.color.host_color_99ffffff
            THEME_IP -> R.color.host_color_8f8f8f_66666b
            else -> R.color.host_color_662c2c3c_cc8d8d91
        }
    }

    /**
     * 获取真实宽度
     */
    private fun getRealWidth(textView: View?, textString: String?): Float {
        if (textView !is TextView) {
            return 0f
        }
        if (TextUtils.isEmpty(textString)) {
            return 0f
        }
        val paint = textView.paint
        return paint.measureText(textString)
    }

    /**
     * 清除Drawable缓存
     */
    fun clearDrawableCache() {
        mAnchorNameDrawable.clear()
        mDurationDrawable.clear()
        mPlayDrawable.clear()
    }

    /**
     * 检查文本是否能在一行显示
     */
    fun canShowOneLine(textView: TextView?, textString: String?, containerWidthInPx: Int): Boolean {
        if (TextUtils.isEmpty(textString) || textView == null) {
            return true
        }
        val paint = textView.paint
        return paint.measureText(textString) <= containerWidthInPx
    }

    /**
     * 检查CharSequence是否能在一行显示
     */
    fun canShowOneLine(
        textView: TextView?,
        textString: CharSequence?,
        containerWidthInPx: Int
    ): Boolean {
        if (TextUtils.isEmpty(textString) || textView == null) {
            return true
        }
        val paint = textView.paint
        return paint.measureText(textString, 0, textString!!.length) <= containerWidthInPx
    }
} 