package com.ximalaya.ting.android.main.playpage.fragment;

import static com.ximalaya.ting.android.host.model.play.PlayPageTab.TYPE_INSTANT_SCRIP;

import android.animation.ArgbEvaluator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArraySet;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.earn.statistics.PushArrivedTraceManager;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.pageerrormonitor.XmPageErrorMonitor;
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelper;
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FragmentUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.chatxmly2.manager.ChatXmlyPreSoundPatchManager;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constant.SharedConstant;
import com.ximalaya.ting.android.host.constants.CommentConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.dialog.PlayPageSubscribeGuideDialogFragment;
import com.ximalaya.ting.android.host.fragment.offsale.OffSaleUtils;
import com.ximalaya.ting.android.host.fragment.play.XPlayPage;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.fragment.play.data.Theme;
import com.ximalaya.ting.android.host.listener.IFloatingPlayControlComponent;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.AchievementInfo;
import com.ximalaya.ting.android.host.manager.AlbumGuideSoundPatchManager;
import com.ximalaya.ting.android.host.manager.AlbumOffLineManager;
import com.ximalaya.ting.android.host.manager.ArriveTraceManager;
import com.ximalaya.ting.android.host.manager.IPlayCompleteShareListener;
import com.ximalaya.ting.android.host.manager.IXPlayPageDialogShowChangeListener;
import com.ximalaya.ting.android.host.manager.PlayCompleteManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteShareGuideManager;
import com.ximalaya.ting.android.host.manager.PlayPageDialogManager;
import com.ximalaya.ting.android.host.manager.PlayPageSubscribeListenTimeManager;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.UIConsistencyManager;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.BackUserPullUpManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdConversionUtil;
import com.ximalaya.ting.android.host.manager.ad.WindowAdManager;
import com.ximalaya.ting.android.host.manager.ad.playweb.AdPlayNativeWebManager;
import com.ximalaya.ting.android.host.manager.adfree.CommonAdFreeManager;
import com.ximalaya.ting.android.host.manager.appcomment.AppCommentManager;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.pay.PayResultManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager;
import com.ximalaya.ting.android.host.manager.play.soundEffect.TrackPlaySoundEffectManager;
import com.ximalaya.ting.android.host.manager.play.timelinecard.PlayTimelineCardManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.PlayRNShareManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.vip.VipStateChangeManager;
import com.ximalaya.ting.android.host.model.AiAlbumPermissionInfo;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.CopyrightExtendInfo;
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData;
import com.ximalaya.ting.android.host.model.play.PlayPageTab;
import com.ximalaya.ting.android.host.model.play.PlayPageTabAndSoundInfo;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.push.NotificationPermissionOpenManager;
import com.ximalaya.ting.android.host.receiver.NetWorkChangeReceiver;
import com.ximalaya.ting.android.host.service.DriveModeBluetoothManager;
import com.ximalaya.ting.android.host.service.TingLocalMediaService;
import com.ximalaya.ting.android.host.share.manager.FamilyShareManager;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.LockScreenUtil;
import com.ximalaya.ting.android.host.util.PlayPageStyleUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.other.VideoPlayParamsBuildUtil;
import com.ximalaya.ting.android.host.util.performance.PageStartOpt;
import com.ximalaya.ting.android.host.util.server.DownloadTools;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor;
import com.ximalaya.ting.android.host.util.startup.ViewPool;
import com.ximalaya.ting.android.host.view.CustomControlScrollViewPagerCanDisableFillNeighbourTab;
import com.ximalaya.ting.android.host.view.TopSlideView1;
import com.ximalaya.ting.android.host.view.dialog.LockScreenAuthorityOpenGuideConfirmDialog;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.commentModule.fragment.FloatingTrackCommentFragment;
import com.ximalaya.ting.android.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.android.main.manager.playPage.AppBackgroundSettingUtils;
import com.ximalaya.ting.android.main.manager.soundpatch.SoundPatchMainManager;
import com.ximalaya.ting.android.main.playModule.fragment.PlayCompleteRecommendFragment;
import com.ximalaya.ting.android.main.playModule.fragment.YPlayCompleteRecommendFragment;
import com.ximalaya.ting.android.main.playpage.adapter.PlayPageTabAdapter;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAnimationManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.CoverComponentsManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.IAdditionalSkinAdService;
import com.ximalaya.ting.android.main.playpage.component.FloatingControlBarComponent;
import com.ximalaya.ting.android.main.playpage.component.IShareTipsShowingListener;
import com.ximalaya.ting.android.main.playpage.component.LiveAndMcEntryComponent;
import com.ximalaya.ting.android.main.playpage.component.PlayFragmentBroadCastComponent;
import com.ximalaya.ting.android.main.playpage.component.ShareComponent;
import com.ximalaya.ting.android.main.playpage.component.ShareTipsComponent;
import com.ximalaya.ting.android.main.playpage.component.ShareVipEntryComponent;
import com.ximalaya.ting.android.main.playpage.constant.PlayPageLoadErrorConstants;
import com.ximalaya.ting.android.main.playpage.dialog.FreeListenDialogNew;
import com.ximalaya.ting.android.main.playpage.dialog.ListenTimeDialogManager;
import com.ximalaya.ting.android.main.playpage.dialog.OpenAIDocGuideDialogFragment;
import com.ximalaya.ting.android.main.playpage.dialog.PlayCompleteShareDialog;
import com.ximalaya.ting.android.main.playpage.dialog.VisibleOnlyToFriendDialog;
import com.ximalaya.ting.android.main.playpage.internalservice.IAdCoverHideService;
import com.ximalaya.ting.android.main.playpage.internalservice.IDocOnCoverComponentService;
import com.ximalaya.ting.android.main.playpage.internalservice.IPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.internalservice.IVideoPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.listener.PlayStatusListenerDispatcher;
import com.ximalaya.ting.android.main.playpage.manager.ISoundPatchFinishListener;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataSuppleManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDocTabManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageLoadingOptimizationManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageShareTipsManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayWidgetGuideManager;
import com.ximalaya.ting.android.main.playpage.manager.YDomainColorUtil;
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel;
import com.ximalaya.ting.android.main.playpage.playx.XUtils;
import com.ximalaya.ting.android.main.playpage.playx.listener.IReadBookInterceptBackService;
import com.ximalaya.ting.android.main.playpage.playx.manager.XPlayPageTingReaderManager;
import com.ximalaya.ting.android.main.playpage.playy.XPlayAdFragmentForAnchor;
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment;
import com.ximalaya.ting.android.main.playpage.playy.YPlayViewModel;
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew;
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayPageControlManager;
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayPageTabUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayPageTraceUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayTtsUtil;
import com.ximalaya.ting.android.main.share.manager.PLCShareManager;
import com.ximalaya.ting.android.main.util.other.CopyrightUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.main.view.album.AlbumGuideSubscriptionDialog;
import com.ximalaya.ting.android.main.view.comment.QuickBulletViewManager;
import com.ximalaya.ting.android.main.view.comment.QuickCommentViewManager;
import com.ximalaya.ting.android.main.view.dialog.PlayNoCopyRightDialog;
import com.ximalaya.ting.android.main.view.other.IViewOnVisibilityChangeListener;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.manager.WifiSleepMonitor;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.service.IFreeListenTimeListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorStatisticManager;
import com.ximalaya.ting.android.opensdk.util.Constants;
import com.ximalaya.ting.android.opensdk.util.MethodUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmpushservice.PushUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.IUbtSourceKeeper;
import com.ximalaya.ting.android.xmtrace.ubt.IUbtSourceSingleton;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kotlin.jvm.functions.Function1;

/**
 * Created by WolfXu on 2020-04-23.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class PlayFragmentNew extends BaseFragment2 implements IUbtSourceKeeper, IUbtSourceSingleton {
    private static final String TAG = "PlayFragmentNew";
    private static final String TAG_PLAY_COMPLETE_RECOMMEND_DIALOG_NEW = "playCompleteRecommendDialog";
    private static final int FRAGMENT_CONTAINER_ID = R.id.main_fl_fragment_container;
    public static boolean sIsPlayingVideoWhilePaused;

    private TopSlideView1 mVTopSlideView;
    private ImageView mIvBack;
    private CustomControlScrollViewPagerCanDisableFillNeighbourTab mViewPager;
    private View mVBg;
    private ViewGroup mVgTitleBar;

    private PlayPageTabAdapter mTabAdapter;
    private List<TabCommonAdapter.FragmentHolder> mFragmentHolderList;
    private PagerSlidingTabStrip mTab;
    private String mCurCoverUrl;
    private int mDefaultBgColor;
    private int mBackgroundColor;
    private int mTitleBarBottom;
    private ShareComponent mShareComponent;
    private ISoundPatchFinishListener mISoundPatchFinishListener;
    @SuppressWarnings("WeakerAccess")
    public FloatingControlBarComponent mFloatingControlBarComponent;
    @SuppressWarnings("WeakerAccess")
    public LiveAndMcEntryComponent mLiveAndMcEntryComponent;
    // private ReaderEntryComponent mReaderEntryComponent;
    private ShareVipEntryComponent mShareVipEntryComponent;
    private PlayFragmentBroadCastComponent mBroadCastComponent;
    private ShareTipsComponent shareTipsComponent;
    private Fragment mCurShowingFragment; // 当前显示的页面，包括：tab页或者弹出的评论页
    private boolean mIsChildProtectMode;
    private List<PlayPageTab> mTabList;
    private PlayingSoundInfo mSoundInfo;
    private Fragment mShowingTopFragment; // 显示在播放页之上的Fragment
    private Bundle mArgumentsForVideo;
    @PlayPageTab.TabType
    private int mFocusTabType = PlayPageTab.TYPE_NONE;
    private boolean mFloatingControlBarInStash;
    private IFloatingPlayControlComponent.ShowTypeEnum mFloatingControlBarShowType;
    private PlayingSoundInfo mLastSoundInfoWhilePaused;
    private boolean mLastAuthorizedWhilePaused;
    private final ArgbEvaluator mArgbEvaluator = new ArgbEvaluator();
    private int mFloatingControlBarNormalMarginBottom;
    private IAdCoverHideService mAdCoverHideService;
    private long mUid;
    private @NonNull final TraceHelper mTraceHelper = new TraceHelper("播放页");

    @NonNull
    private final TraceHelper mTraceHelper2 = new TraceHelper("播放页2");

    private boolean mHasSetTab;
    private boolean mHasLoadedData;
    private static boolean sIsHasPostRunnable;
    private static boolean sIsHasPostDialogRunnable;

    private boolean sIsOpenComment = false;
    private long mTaskId;
    private Set<ICoverLoadedListener> mCoverLoadedListenerSet;
    private @Nullable Runnable mCheckIsRealVisibleToAbandonTraceTask;
    private int mBackColor;
    @Nullable private List<String> mJumpToVideoTabAlbumIds;
    private boolean mNeedCheckToJumpToVideoTab;
    private boolean videoWhiteListChecked = false;

    private long mNoNeedStatBackBtnViewedTrackId;

    private View mTrackIntroDetailGuideV;

    private long pageShowStartTs = 0L;
    private long mLastResumeTime = 0L;
    private int mResumeCount = 0;
    private XPlayViewModel viewModel;
    private YPlayViewModel yPlayViewModel;
    private FrameLayout mFlTopLayout;
    @PlayPageTab.TabType
    private int mLastTabSelectType = PlayPageTab.TYPE_NONE;
    private boolean mHasDocTab = false;
    private boolean mHasTimeDocTab = false;
    private IXPlayPageDialogShowChangeListener mDialogShowChangeListener;

    private boolean isFromPlayBar = false;
    private boolean mHasGetAdFreeAbTest = false;
    private boolean isNeedShowFreeListenDialogOnResume;
    private boolean isFirstResume = true;
    private boolean isResume = false;

    private PageChangeListener mOnPageChangeListener = new PageChangeListener();
    private boolean alreadyInit;
    private long mCurrentAlbumPlaySecondTime;

    public PlayFragmentNew() {
        super(false, SlideView.TYPE_FRAMELAYOUT, null);
    }

    @Override
    protected String getPageLogicName() {
        return "新版播放页";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        PageStartOpt.playPageLoadBegin();
        PerformanceMonitor.traceBegin("playpage_open");
        TraceHelperManager.INSTANCE.setTraceHelper(TraceHelperManager.TraceHelperName.PLAY_PAGE, mTraceHelper);
        mTraceHelper.postPageStartNode();
        mTraceHelper.setOptOpen(PageStartOpt.PlayPageOpt.enable());

        mTraceHelper2.postPageStartNode();
        mTraceHelper2.setOptOpen(PageStartOpt.PlayPageOpt.enable());
        PageStartOpt.PlayPageOpt.setTraceHelper(mTraceHelper, mTraceHelper2);

        PlayFragmentNew_XmLifecycleBinder.bind(this, this.getLifecycle());
        ArriveTraceManager.onItingPageCreate(AppConstants.PAGE_SOUND, getPlayingTrackId());
        super.onCreate(savedInstanceState);
        PlayPageShareTipsManager.INSTANCE.init();

        ViewModelProvider provider = (new ViewModelProvider(requireActivity()));
        viewModel = provider.get(XPlayViewModel.class);
        yPlayViewModel = provider.get(YPlayViewModel.class);

        parseXPlayParameters(getArguments());
        PlayPageDataManager.getInstance().setxPlayVideModel(viewModel);
        PlayPageDataManager.getInstance().setyPlayViewModel(yPlayViewModel);
        PlayPageMinorDataManager.getInstance().setxPlayVideModel(viewModel);
        registerFreeListenTimeListener();
        registerStopPlayDueToFreeListenPermissionListener();
    }

    private void preloadXmlIfNeed() {
        if (!PageStartOpt.PlayPageOpt.enable()) {
            return;
        }
        List<ViewPool.InflateMany> tasks = Arrays.asList(
                new ViewPool.InflateMany(R.layout.main_play_page_skeleton_loading_view, 1),
                new ViewPool.InflateMany(R.layout.main_play_page_y, 1),
                new ViewPool.InflateMany(R.layout.main_audio_play_component_manuscript_y, 1),
                new ViewPool.InflateMany(R.layout.main_layout_yplay_video, 1),
                new ViewPool.InflateMany(R.layout.main_layout_doc_background_y, 1),
                new ViewPool.InflateMany(R.layout.main_audio_play_component_normal_covers_y, 1)
        );
        PageStartOpt.PlayPageOpt.preloadXmlIfNeed(tasks);
    }

    /**
     * 找到包含视频相关参数的Arguments
     */
    private Bundle getArgumentsContainsVideoArguments() {
        Bundle arguments = getArguments();
        if (arguments != null && arguments.containsKey(VideoPlayParamsBuildUtil.KEY_FOCUS_VIDEO_PLAY_TAB)) {
            return arguments;
        }
        Bundle arguments2 = getArguments2();
        if (arguments2 != null && arguments2.containsKey(VideoPlayParamsBuildUtil.KEY_FOCUS_VIDEO_PLAY_TAB)) {
            return arguments2;
        }
        return null;
    }

    private Bundle getCommonArgument() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            return arguments;
        }
        Bundle arguments2 = getArguments2();
        if (arguments2 != null) {
            return arguments2;
        }
        return null;
    }

    private boolean fromVideoChannel = false;
    private boolean needUpdateChannel = false;

    @Override
    public void setArguments(@Nullable Bundle args) {
        super.setArguments(args);
        parseXPlayParameters(args);
    }

    @Override
    public void setArguments2(@Nullable Bundle args) {
        super.setArguments2(args);
        parseXPlayParameters(args);
    }

    //xplayFragment 外部传参统一走这里
    private void parseXPlayParameters(Bundle args) {
        if (args != null && viewModel != null) {
            XUtils.logBundle(args);
            viewModel.parsePlayFragmentArgs(args);
        }
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 152533;
        isResume  = true;
        super.onMyResume();
        videoWhiteListChecked = false;

        pageShowStartTs = System.currentTimeMillis();

        mVTopSlideView.resumeContent();
        AlbumEventManage.addListener(collectListener);
        UserInfoMannage.getInstance().addLoginStatusChangeListener(loginStatusChangeListener);
        XmPlayerManager.getInstance(getContext()).addPlayerStatusListener(mPlayerStatusListener);
        XmPlayerManager.getInstance(getContext()).addPlayerStatusListener(mPlayerStatusListenerForTrace);

        if (isInTabAbGroupOnOldPlayPage()){
            mLastTabSelectType = mFocusTabType;
            //mLastTabSelectType 是为了防止iting跳到专辑页拿不到type而将mFocusTabType重置为TYPE_NONE
        }

        mFocusTabType = PlayPageTab.TYPE_NONE;
        int section = TempDataManager.getInstance().getInt(BundleKeyConstants.KEY_PLAY_FRAGMENT_SECTION);
        if (section == BundleKeyConstants.KEY_PLAY_FRAGMENT_SECTION_COMMENT) {
            mFocusTabType = PlayPageTab.TYPE_COMMENT;
            TempDataManager.getInstance().removeInt(BundleKeyConstants.KEY_PLAY_FRAGMENT_SECTION);
        } else if (section == BundleKeyConstants.KEY_PLAY_FRAGMENT_SECTION_INSTANT_SCRIPT) {
            mFocusTabType = PlayPageTab.TYPE_INSTANT_SCRIP;
            TempDataManager.getInstance().removeInt(BundleKeyConstants.KEY_PLAY_FRAGMENT_SECTION);
        }
        mFocusTabType = PlayPageTabUtil.checkPlayPageTabType(
                TempDataManager.getInstance().getInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, PlayPageTab.TYPE_NONE));
        TempDataManager.getInstance().removeInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION);

        sIsOpenComment = TempDataManager.getInstance().getBoolean(BundleKeyConstants.KEY_PLAY_COMMENT_FRAGMENT_TAB_NEED_AUTO_SHOW_KEYBOARD);
        if (TempDataManager.getInstance().containsLongKey(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TASK_ID)) {
            mTaskId = TempDataManager.getInstance().getLong(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TASK_ID);
            TempDataManager.getInstance().removeLong(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TASK_ID);
        }

        long playingTrackId = parseAndHandleArgumentsForVideo();
        Bundle tempArgumentsForVideo = mArgumentsForVideo;
        Bundle arguments = parseArgumentsForXPlay();

        if (playingTrackId == -1) {
            playingTrackId = getPlayingTrackId();
        }

        // 如果还没有声音，那可能是播放进程还没起来，先尝试取外部iting中的声音id，以加快播放页显示
        if (playingTrackId == -1 || playingTrackId == 0) {
            playingTrackId = TempDataManager.getInstance().getLong(TempDataManager.DATA_PLAY_PAGE_OUTER_ITING_TRACK_ID);
            Logger.i("ArriveTraceManagerTest", "PlayFragmentNew get track id from outer iting " + playingTrackId);
        }
        TempDataManager.getInstance().removeLong(TempDataManager.DATA_PLAY_PAGE_OUTER_ITING_TRACK_ID);

        PushArrivedTraceManager.INSTANCE.getInstance().showPageByManual("PlayFragmentNew",
                "track_id", playingTrackId + "");

        isFromPlayBar = isFromPlayBar();
        if (PlayPageDataManager.getInstance().getCurTrackId() == playingTrackId) {
            PlayTools.showPlayFragmentReq = false;
            checkTrackVisiblePermission();
        }

        if (viewModel != null) {
            viewModel.setTrackId(playingTrackId);
        }
        if (yPlayViewModel != null) yPlayViewModel.setTrackId(playingTrackId);

        // 播放的声音发生了改变，需要做一些处理
        if (PlayPageDataManager.getInstance().getCurTrackId() != playingTrackId) {
            // 如果需要跳转到视频tab，则先将播放页tab设为不可见,避免playFragemntNew onResume的时候调到AudioPlayFragment的onResume，触发广告的提前请求
            if (mFocusTabType == PlayPageTab.TYPE_VIDEO && isAudioPlayFragment(mCurShowingFragment) && !(mCurShowingFragment instanceof XPlayPage)) {
                mCurShowingFragment.setUserVisibleHint(false);
            }
            mNeedCheckToJumpToVideoTab = true;
            mUid = UserInfoMannage.getUid();
            onGotNewTrackId(playingTrackId);
            Logger.d(TAG, "播放的声音发生了改变");
            loadDataFromNet(false);
            if (isInTabAbGroupOnOldPlayPage()){
                changeTabByType(getTabIdByAbGroup());
            } else if (mFocusTabType == PlayPageTab.TYPE_NONE && mViewPager != null) {
                // 回到播放页时，声音改变了，且没有指定要跳转的tab，则回到声音tab
                gotoAudioPlayPageTop();
            }

            removeAllChildFragments();
        } else if (mUid != UserInfoMannage.getUid()) {
            mUid = UserInfoMannage.getUid();
            loadDataFromNet(true);
            if (isInTabAbGroupOnOldPlayPage()){
                changeTabByType(getTabIdByAbGroup());
            }  else  if (mFocusTabType == PlayPageTab.TYPE_NONE && mViewPager != null) {
                // 回到播放页时，uid改变了，且没有指定要跳转的tab，则回到声音tab
                gotoAudioPlayPageTop();
            }
        } else if (FreeListenConfigManager.isFreeListenV2Open() && mSoundInfo != null && mSoundInfo.albumInfo != null
                && mSoundInfo.albumInfo.supportFreeListen() && mSoundInfo.trackInfo != null
                && !mSoundInfo.trackInfo.isAuthorized && FreeListenTimeManager.getListenTime(getContext()) > 0) {
            // 畅听声音没有权限，且现在有畅听时长，则重新请求一下接口数据，应该要有权限了
            loadDataFromNet(true);
        } else if (mFocusTabType != PlayPageTab.TYPE_NONE) {
            // 如果声音没变，直接切换tab
            changeTabByType(mFocusTabType);
            mFocusTabType = PlayPageTab.TYPE_NONE;
        } else if (isFromPlayBar) {
            // 从肚脐眼进来的
            if (isInTabAbGroupOnOldPlayPage()){
                mFocusTabType = mLastTabSelectType;
                changeTabByType(mFocusTabType);
            } else if (!isCurrentTabOfType(PlayPageTab.TYPE_TTS)) {
                // 声音没变，且没指定tab，且不在视频页，回到声音页
                if (!isCurrentTabOfType(PlayPageTab.TYPE_VIDEO) || !shouldKeepVideoTab()) {
                    // TTS的声音跳到TTS页
                    if (PlayTtsUtil.isTtsTrack()) {
                        changeTabByType(PlayPageTab.TYPE_TTS);
                        // tts文稿全屏
                    } else {
                        gotoAudioPlayPageTop();
                    }
                } else {
                    // 让视频tab能开始播放
                    mArgumentsForVideo = new Bundle();
                    mArgumentsForVideo.putBoolean(VideoPlayParamsBuildUtil.KEY_FOCUS_VIDEO_PLAY_TAB, true);
                    tempArgumentsForVideo = mArgumentsForVideo;
                    changeTabByType(PlayPageTab.TYPE_VIDEO);
                }
            }
        }

        sIsPlayingVideoWhilePaused = false;
        checkOpenFloatingCommentPage();

        checkChildProtectMode();

        // 推荐播放的广播
        IntentFilter filter = new IntentFilter();
        filter.addAction(PlayCompleteRecommendManager.ACTION_ENTER_RECOMMEND);
        filter.addAction(PlayCompleteRecommendManager.ACTION_QUIT_RECOMMEND);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mPlayCompleteReceiver, filter);
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService != null) {
            freeFlowService.addProxyChanges(mProxyChange);
        }
        if (mBroadCastComponent != null) {
            mBroadCastComponent.doOnResume();
        }

        AudioPlayUtil.vipSkipAdOnResume(getContext());
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mNetworkBroadcastReceiver,
                new IntentFilter(NetWorkChangeReceiver.NETWORK_CHANGE_ACTION));

        doAfterAnimation(this::checkFreeFlow);

        PlayPageDataManager.getInstance().addAuthorizedStatusChangeListener(mOnAuthorizedStatusChangeListener);
        // 如果购买状态发生了变化，更新下tab
        if (mLastSoundInfoWhilePaused != null && mLastSoundInfoWhilePaused == mSoundInfo) {
            if (mSoundInfo.trackInfo != null && mSoundInfo.trackInfo.isAuthorized != mLastAuthorizedWhilePaused) {
                updateTab(mTabList);
            }
        }

        // check 是否弹出打开锁屏权限页面
        checkIfOpenLockscreenAuthorityGuideDialog();

        if (mShareComponent != null) {
            mShareComponent.onResume();
        }
        Track track = getPlayingTrack();
        PlayCompleteManager.INSTANCE.setIsOnScreen(true);
        WindowAdManager.setScreenOn(true);
        doAfterAnimation(() -> {
            Logger.i("AppBackgroundSettingUtils", "onReady");
            if (WifiSleepMonitor.getInstance().wifiCloseInSleepHappen(mContext)
                    && !sIsHasPostDialogRunnable
                    && mContext != null) {
                Logger.i("AppBackgroundSettingUtils", "wifi sleep happen, will dialog ");
                sIsHasPostDialogRunnable = true;
                AppBackgroundSettingUtils.checkWifiSleepHappen();
            } else {
                if (!sIsHasPostRunnable) {
                    sIsHasPostRunnable = true;
                    if (!AppBackgroundSettingUtils.checkHuaWeiSleepingMode()) {
                        if (PlayErrorStatisticManager.isHasHappenedPlayErrorInBg()) {
                            AppBackgroundSettingUtils.checkAndNotifySystemSetting();
                        }
                    }
                }
            }
            // 根据条件判断要不要出完播推荐弹窗
            if (PlayCompleteManager.INSTANCE.canShowPlayCompleteDialogWhenPlayFraResume(track)) {
                showPlayCompleteFragment(track, true);
            }
        });

        updateBackButton(true);
        updatePlayAdWebBarView();
        traceBackBtnViewed();
        addSoundPatchFinishListener();
        PlayCompleteShareGuideManager.INSTANCE.setPlayCompleteShareListener(new IPlayCompleteShareListener() {
            @Override
            public void showAnimate(@NonNull AchievementInfo achievementInfo) {
                if (getActivity() != null) {
                    PlayCompleteShareDialog.newInstance(getActivity(), achievementInfo).show(getChildFragmentManager(), null);
                }
            }
        });
        SoundPatchMainManager.getInstance().doOnPlayFragmentResume();
        reportIfResumeTooFrequently();

        boolean showAlbumGuideDialog = showAlbumGuideDialog();
        if (!showAlbumGuideDialog && !PlayPageDialogManager.INSTANCE.hasDialogShowIngOnPlayPage()) {
            boolean hadKill = PlayErrorStatisticManager.getSingleInstance().isLastPlayErrorByApplicationCrash();
            boolean isOpenNow = PushUtil.isSystemNotificationEnable(MainApplication.getMyApplicationContext());
            if (!isOpenNow) {
                NotificationPermissionOpenManager.checkToShowPlayFragmentNotificationPermissionDialog(getActivity(),
                        hadKill ? NotificationPermissionOpenManager.NotificationEntranceType.PLAY_PAGE_APP_CRASH :
                                NotificationPermissionOpenManager.NotificationEntranceType.PLAY_PAGE_NORMAL, "新声音播放页");
            }
        }
        //checkSubscribeUnlock(true);
        if (ListenTimeDialogManager.isNeedPullUpDialog()){
            ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.PLAY_PAGE_DIALOG, track, PlayFragmentNew.this, 10);
            ListenTimeDialogManager.setNeedPullUpDialog(false);
        } else if (isNeedShowFreeListenDialogOnResume && FreeListenTimeManager.getListenTime(getContext()) == 0) {
            FreeListenTimeManager.setIsNeedReplayWhenRewardTime(true);
            ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.PLAY_PAGE_DIALOG, track, PlayFragmentNew.this, 2);
        }
        isNeedShowFreeListenDialogOnResume = false;
        if (!FreeListenConfigManager.isCurrentTrackNeedListenTime() || mFocusTabType == PlayPageTab.TYPE_VIDEO) {
            FreeListenDialogNew.hideFreeListenDialog();
        }
        if (!isFirstResume) {
            ListenTimeDialogManager.showListenTimeDialogOnResume(this, track, FreeListenConfigManager.isCurrentTrackNeedListenTime());
        } else {
            if (FreeListenConfigManager.isCurrentTrackNeedListenTime() && FreeListenTimeManager.getListenTime(getContext()) == 0) {
                ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.PLAY_PAGE_DIALOG, getPlayingTrack(), PlayFragmentNew.this, 2);
            }
        }
        isFirstResume = false;
        ChatXmlyPreSoundPatchManager.INSTANCE.onPlayFragmentShow();
        ListenTimeDialogManager.onPlayFragmentResume();
    }

    private void checkSubscribeUnlock() {
        Track curTrack = PlayTools.getCurTrack(mContext);
        if (curTrack == null || mSoundInfo == null || mSoundInfo.trackInfo == null) {
            return;
        }
        if (curTrack.getDataId() != mSoundInfo.trackInfo.trackId) {
            return;
        }
        if (mSoundInfo.trackInfo.subscribeFreeAuthorized) {
            return;
        }
        if (!NetworkUtils.isNetworkAvaliable(mContext)) {
            return;
        }
        PlayTools.pause(mContext, PauseReason.Business.AudioPlayPage_CheckSubscribeUnlock);
        HandlerManager.postOnUIThreadDelay(() -> AudioPlayUtil.loadCheckShowSubscribeUnlock(PlayFragmentNew.this, mSoundInfo), 300);
    }

    private void getAdFreeAbTest() {
        if (mHasGetAdFreeAbTest) {
            return;
        }
        Boolean is28DaysRecall = CommonAdFreeManager.INSTANCE.is28DaysRecall();
        // 为null说明还没请求到数据，mHasGetAdFreeAbTest先不设为true，下次进播放页可以再次处理
        if (is28DaysRecall != null) {
            mHasGetAdFreeAbTest = true;
            if (is28DaysRecall) {
                String config = ABTest.getString(CConstants.NewAbTest.RECALL_SOUND_1HOUR_NOAD20230811, "yes");
                MmkvCommonUtil.getInstance(getContext()).saveString(PreferenceConstantsInHost.KEY_RECALL_SOUND_1HOUR_NOAD20230811, config);
            }
        }
    }

    private boolean showAlbumGuideDialog() {
        Activity activity = MainApplication.getMainActivity();
        if (!(activity instanceof FragmentActivity)) {
            return false;
        }

        if (ConstantsOpenSdk.isDebug) {
            Logger.log("PlayFragmentNew : " +
                    "   isRnPage=" + isRnPlayPage(PlayPageTab.TYPE_SOUND) +
                    "   isAudioPlayFragment=" + isAudioPlayFragment(mCurShowingFragment) +
                    "   getShowingTopChildFragment=" + getShowingTopChildFragment() +
                    "   splashAdShow=" + ViewUtil.isSplashAdShowing() +
                    "   hasDialogShow=" + ViewUtil.haveDialogIsShowing((FragmentActivity) activity));
        }

        if (!isRnPlayPage(PlayPageTab.TYPE_SOUND)
                && mSoundInfo == null || isAudioPlayFragment(mCurShowingFragment)
                && getShowingTopChildFragment() == null
                && !ViewUtil.isSplashAdShowing()
                && !ViewUtil.haveDialogIsShowing((FragmentActivity) activity)) {
            Track playingTrack = getPlayingTrack();
            if (playingTrack != null && playingTrack.getAlbum() != null) {
                long albumId = playingTrack.getAlbum().getAlbumId();
                if (AlbumGuideSoundPatchManager.canShowAlbumGuideDialog(albumId)) {
                    AlbumGuideSubscriptionDialog.showGuideDialog(albumId, new AlbumGuideSubscriptionDialog.IAlbumGuideCallBack() {
                        @Override
                        public void noAlbumShow() {

                        }

                        @Override
                        public void show(@NonNull AlbumM album) {
                            if (!(MainApplication.getMainActivity() instanceof MainActivity)) {
                                return;
                            }

                            if (ViewUtil.isSplashAdShowing() || ViewUtil.haveDialogIsShowing((MainActivity) MainApplication.getMainActivity())) {
                                return;
                            }

                            if (!canUpdateUi()) {
                                return;
                            }

                            try {
                                AlbumGuideSubscriptionDialog.Companion.newInstance(album).show(getChildFragmentManager(), "AlbumGuideSubscriptionDialog");
                            } catch (Throwable e) {
                                e.printStackTrace();
                            }
                        }
                    });
                    return true;
                }
            }
        }
        return false;
    }

    private void reportIfResumeTooFrequently() {
        // 如果onResume过于频繁，上报一下
        // 从魔镜用户细查看，有些用户会有特别频繁onResume的情况
        long curTime = System.currentTimeMillis();
        if (curTime - mLastResumeTime < 10 * 1000) {
            mResumeCount++;
        } else {
            mResumeCount = 0;
        }
        mLastResumeTime = curTime;
        if (mResumeCount > 10) {
            String stackTrace = Log.getStackTraceString(new Throwable());
            XDCSCollectUtil.statErrorToXDCS("playpage", "resume too frequently " + getPlayingTrackId() + " " + stackTrace);
            Logger.i(TAG, "resume too frequently " + getPlayingTrackId() + " " + stackTrace);
        }
    }


    /**
     * V1播放页，判断专辑在白名单，用户在实验组，是否直接进入长文稿tab
     */
    private boolean isInDocAbExperimentalGroupOnOldPlayPage(){
        Logger.d(TAG,"isInDocAbExperimentalGroupOnOldPlayPage TYPE_DOC "+mHasDocTab);
        if (mSoundInfo != null){
            PlayingSoundInfo.OtherInfo otherInfo = mSoundInfo.otherInfo;
            if (otherInfo!=null && otherInfo.tabExperienceType != null){
//                return true;
                //老播放页，专辑在实验组，有长文稿tab
                return !PlayPageTabUtil.isPlayX(mSoundInfo) && otherInfo.tabExperienceType.equals("summary") && mHasDocTab;
            }
        }
        return false;
    }

    /**
     * 时刻文稿是否在实验组内
     * @return
     */
    private boolean isInTimeDocAbGroupOnOldPlayPage() {
        if (mSoundInfo != null){
            PlayingSoundInfo.OtherInfo otherInfo = mSoundInfo.otherInfo;
            if (otherInfo!=null){
//                return true;
                //老播放页，专辑在实验组，有长文稿tab
                return !PlayPageTabUtil.isPlayX(mSoundInfo) && otherInfo.tabExperienceTypeId == TYPE_INSTANT_SCRIP && mHasTimeDocTab;
            }
        }
        return false;
    }

    private boolean isInTabAbGroupOnOldPlayPage() {
        if (mSoundInfo != null){
            PlayingSoundInfo.OtherInfo otherInfo = mSoundInfo.otherInfo;
            if (otherInfo!=null){
                if (otherInfo.tabExperienceTypeId != null && !PlayPageTabUtil.isPlayX(mSoundInfo)) {
                    return true;
                }
            }
        }
        return false;
    }

    private int getTabIdByAbGroup() {
        if (mSoundInfo != null){
            PlayingSoundInfo.OtherInfo otherInfo = mSoundInfo.otherInfo;
            if (otherInfo!=null){
                if (otherInfo.tabExperienceTypeId != null && !PlayPageTabUtil.isPlayX(mSoundInfo)) {
                    return otherInfo.tabExperienceTypeId;
                }
            }
        }
        return 0;
    }

    private boolean isInNeedJumpToVideoTabList() {
        if (mSoundInfo == null || mSoundInfo.albumInfo == null) {
            return false;
        }
        if (mSoundInfo.whiteListInfo != null) {
            if (mSoundInfo.whiteListInfo.priorPlayVideoAlbum) return true;
        }

        if (ToolUtil.isEmptyCollects(mJumpToVideoTabAlbumIds)) {
            String config = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME,
                    CConstants.Group_toc.ITEM_PLAYLET_LIST, "");
            if (!TextUtils.isEmpty(config)) {
                String[] albumIdStrArray = config.split(",");
                mJumpToVideoTabAlbumIds = Arrays.asList(albumIdStrArray);
            }
        }
        if (mJumpToVideoTabAlbumIds == null) {
            return false;
        }
        String albumId = String.valueOf(mSoundInfo.albumInfo.albumId);
        return mJumpToVideoTabAlbumIds.contains(albumId);
    }

    private void jumpToVideoImpl(Bundle argumentsForVideo) {
        long playingTrackId = parseAndHandleArgumentsForVideo(argumentsForVideo);
        if (PlayPageDataManager.getInstance().getCurTrackId() != playingTrackId) {
            mUid = UserInfoMannage.getUid();
            onGotNewTrackId(playingTrackId);
            loadDataFromNet(false);
            // 声音改变了，且没有指定要跳转的tab，则回到声音tab
            if (mFocusTabType == PlayPageTab.TYPE_NONE && mViewPager != null) {
                mViewPager.setCurrentItem(0);
            }
        } else if (mFocusTabType != PlayPageTab.TYPE_NONE) {
            // 如果声音没变，直接切换tab
            changeTabByType(mFocusTabType);
            mFocusTabType = PlayPageTab.TYPE_NONE;
        }
    }

    private void checkIfOpenLockscreenAuthorityGuideDialog() {
        doAfterAnimation(() -> {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                return;
            }
            boolean showed = MmkvCommonUtil.getInstance(mContext).getBoolean(AppConstants.KEY_LOCKSCREEN_AUTHORITY_OPEN_GUIDE_SHOW, false);
            if (!showed) {
                boolean needShow = !LockScreenUtil.isPermissionOverlayOpend(mContext)
                        && XmPlayerManager.getInstance(mContext).getHistoryTrackListSize() > 0;
                if (needShow && !ViewUtil.haveDialogIsShowing(getActivity())) {
                    HighValueFireworkManager.showAfterHighValueDirectly(new Runnable() {
                        @Override
                        public void run() {
                            LockScreenAuthorityOpenGuideConfirmDialog.Companion.show(mActivity);
                            MmkvCommonUtil.getInstance(mContext).saveBoolean(AppConstants.KEY_LOCKSCREEN_AUTHORITY_OPEN_GUIDE_SHOW, true);
                        }
                    });
                }
            }
        });
    }

    private boolean shouldKeepVideoTab() {
        if (XmPlayerManager.getInstance(getContext()).isPlaying()) {
            return false;
        }
        return sIsPlayingVideoWhilePaused;
    }

    private boolean isFromPlayBar() {
        int channel = 0;
        if (getArguments() != null && getArguments().containsKey(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT)) {
            channel = getArguments().getInt(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT);
            getArguments().remove(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT);
        } else if (getArguments2() != null && getArguments2().containsKey(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT)) {
            channel = getArguments2().getInt(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT);
            getArguments2().remove(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT);
        }
        return channel == SharedConstant.CHANNEL_PLAY_BAR;
    }

    private boolean isFromDownloadVideo() {
        if (mArgumentsForVideo != null && mArgumentsForVideo.getBoolean(
                VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_PLAY_DOWNLOAD_VIDEO, false)) {
            return true;
        }
        Fragment fragment = getTabFragment(1);
        return fragment instanceof VideoPlayTabFragment && ((VideoPlayTabFragment) fragment).isFromPlayDownloadVideoPage();
    }

    private long parseAndHandleArgumentsForVideo() {
        Bundle argsContainsVideo = getArgumentsContainsVideoArguments();
        return parseAndHandleArgumentsForVideo(argsContainsVideo);
    }

    /**
     * 是否是从全视频的专辑进入
     */
    private Bundle parseArgumentsForXPlay(){
        Bundle arguments = getArguments();
        if (arguments != null && arguments.containsKey(VideoPlayParamsBuildUtil.KEY_VIDEO_IS_ALL_VIDEO)) {
            return arguments;
        }
        Bundle arguments2 = getArguments2();
        if (arguments2 != null && arguments2.containsKey(VideoPlayParamsBuildUtil.KEY_VIDEO_IS_ALL_VIDEO)) {
            return arguments2;
        }
        return null;
    }

    private void parseTabInfoOnSoundSwitch() {
        mFocusTabType = PlayPageTabUtil.checkPlayPageTabType(
                TempDataManager.getInstance().getInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, PlayPageTab.TYPE_NONE));
        TempDataManager.getInstance().removeInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION);
    }

    private void checkOpenFloatingCommentPage() {
        if (getArguments() != null && getArguments().containsKey(BundleKeyConstants.KEY_OPEN_FLOATING_COMMENT_PAGE)) {
            boolean isXPlayPage = PlayPageTabUtil.isPlayX(mSoundInfo);
            FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForTrack(
                    CommentConstants.FROM_PAGE_AUDIO, PlayTools.getCurTrack(mContext), false,
                    getArguments().getLong(BundleKeyConstants.KEY_COMMENT_ID), null, 0L, isXPlayPage, isXPlayPage);
            getArguments().remove(BundleKeyConstants.KEY_OPEN_FLOATING_COMMENT_PAGE);
            getArguments().remove(BundleKeyConstants.KEY_COMMENT_ID);
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
                service.startFragmentOnPlayPage(fragment);
            }
        }
    }

    private long parseAndHandleArgumentsForVideo(Bundle argsContainsVideo) {
        long playingTrackId = -1;
        mArgumentsForVideo = null;
        if (argsContainsVideo != null) {
            // bundle 信息里包含原生落地页信息，先存一下
            MmkvCommonUtil.getInstance(getContext()).saveString(PreferenceConstantsInOpenSdk.ITEM_PLAY_PAGE_AD_VIDEO_INFO_GET,
                    argsContainsVideo.getString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_AD_TRACK_ID) +
                    argsContainsVideo.getBoolean(VideoPlayParamsBuildUtil.KEY_IS_OPEN_NATIVE_WEBVIEW, false));

            Logger.i("------msg"," ---- play get = " + MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext())
                    .getStringCompat(PreferenceConstantsInOpenSdk.ITEM_PLAY_PAGE_AD_VIDEO_INFO_GET));
            mArgumentsForVideo = new Bundle();
            mArgumentsForVideo.putAll(argsContainsVideo);
            boolean focusVideoTab = argsContainsVideo.getBoolean(VideoPlayParamsBuildUtil.KEY_FOCUS_VIDEO_PLAY_TAB);
            mFocusTabType = focusVideoTab ? PlayPageTab.TYPE_VIDEO : mFocusTabType;
            if (AudioPlayFragment.TRACE_PLAY_TAB_FIRST_JUMP_TO_VIDEO_FLAG == AudioPlayFragment.TRACE_PLAY_TAB_FIRST_JUMP_TO_VIDEO_FLAG_INIT) {
                AudioPlayFragment.TRACE_PLAY_TAB_FIRST_JUMP_TO_VIDEO_FLAG = AudioPlayFragment.TRACE_PLAY_TAB_FIRST_JUMP_TO_VIDEO_FLAG_TRIGGER_UNHANDLED;
            }
            Track track = argsContainsVideo.getParcelable(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_TRACK);
            List<Track> tracks =
                    argsContainsVideo.getParcelableArrayList(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_VIDEO_LIST);
            if (track != null) {
                if (tracks == null) {
                    tracks = new ArrayList<>();
                }
                if (!tracks.contains(track)) {
                    tracks.add(0, track);
                }
                int index = tracks.indexOf(track);
                XmPlayerManager.getInstance(mContext).setPlayList(tracks, index);
                // 设置到音频播放器没那么快能拿到，所以直接取这个id；
                playingTrackId = track.getDataId();
            }
            VideoPlayParamsBuildUtil.removeAllVideoPlayParams(argsContainsVideo);
            // 如果当前显示的是视频tab，则直接把参数给视频tab页
            if (mViewPager != null && mTabAdapter != null) {
                int curPosition = mViewPager.getCurrentItem();
                Class<?> curTabClazz = mTabAdapter.getFragmentClassAtPositon(curPosition);
                if (curTabClazz == PlayPageTabUtil.getClassByType(PlayPageTab.TYPE_VIDEO, false, mSoundInfo)) {
                    Fragment videoFragment = mTabAdapter.getFragmentAtPosition(curPosition);
                    if (videoFragment != null) {
                        if (videoFragment.getArguments() == null) {
                            videoFragment.setArguments(mArgumentsForVideo);
                        } else {
                            videoFragment.getArguments().putAll(mArgumentsForVideo);
                        }
//                        mArgumentsForVideo = null;
                    }
                }
            }
        } else {
            // bundle 清空
            MmkvCommonUtil.getInstance(getContext()).saveBoolean(PreferenceConstantsInOpenSdk.ITEM_PLAY_PAGE_AD_VIDEO_INFO_GET, false);
        }
        return playingTrackId;
    }

    private void traceForegroundDuration() {
        if (pageShowStartTs > 0) {
            PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
            long pageShowDuration = (System.currentTimeMillis() - pageShowStartTs) / 1000;
            if (pageShowDuration > 0) {
                new XMTraceApi.Trace()
                        .setMetaId(51000)
                        .setServiceId("others") // 新声音播放页大容器离开
                        .put("duration", "" + pageShowDuration) // 按秒统计
                        .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                        .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ?
                                mSoundInfo.albumInfo.albumId : 0))
                        .createTrace();
            }
            pageShowStartTs = System.currentTimeMillis();
        }
    }

    @Override
    public void onPause() {
        videoWhiteListChecked = true;
        mTraceHelper.abandon();
        mTraceHelper2.abandon();
        cancelCheckIsRealVisibleToAbandonTraceTask();
        super.onPause();
        Logger.d(TAG, "onPause, traceForegroundDuration ");
        traceForegroundDuration();
        pageShowStartTs = 0L;

        XmPlayerManager.getInstance(getContext()).removePlayerStatusListener(mPlayerStatusListener);
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mPlayCompleteReceiver);
        if (mBroadCastComponent != null) {
            mBroadCastComponent.doOnPause();
        }
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService != null) {
            freeFlowService.removeProxyChange(mProxyChange);
        }
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mNetworkBroadcastReceiver);
        PlayPageDataManager.getInstance().removeAuthorizedStatusChangeListener(mOnAuthorizedStatusChangeListener);
        mLastSoundInfoWhilePaused = mSoundInfo;
        mLastAuthorizedWhilePaused =
                mSoundInfo != null && mSoundInfo.trackInfo != null && mSoundInfo.trackInfo.isAuthorized;

        Fragment fragment = getCurTabFragment();
        if (fragment instanceof VideoPlayTabFragment) {
            sIsPlayingVideoWhilePaused = ((VideoPlayTabFragment) fragment).isPlayingVideo();
        } else if (fragment instanceof VideoPlayTabFragmentNew) {
            sIsPlayingVideoWhilePaused = ((VideoPlayTabFragmentNew) fragment).isPlaying();
        } else if (fragment instanceof YPlayFragment) {
            sIsPlayingVideoWhilePaused = ((YPlayFragment) fragment).isVideoMode();
        }
        FamilyShareManager.INSTANCE.dismissTip();
        if (mShareComponent != null) {
            mShareComponent.onPause();
        }
        if (shareTipsComponent != null) {
            shareTipsComponent.onPause();
        }
        PlayPageLoadingOptimizationManager.INSTANCE.onPlayPageLoading(false);
        AdPlayNativeWebManager.getInstance().hideNativePage();
        PlayCompleteManager.INSTANCE.setIsOnScreen(false);
        WindowAdManager.setScreenOn(false);
        mNoNeedStatBackBtnViewedTrackId = 0;
        removeSOundPatchFinishListener();
        PlayCompleteShareGuideManager.INSTANCE.setPlayCompleteShareListener(null);
        isResume = false;
//        unregisterStopPlayDueToFreeListenPermissionListener();
    }

    private void checkIsRealVisibleForTrace() {
        if (!isRealVisable()) {
            // 5秒后再次检查，如果还不是可见状态，则放弃上报
            mCheckIsRealVisibleToAbandonTraceTask = () -> {
                if (!isRealVisable()) {
                    mTraceHelper.abandon();
                    mTraceHelper2.abandon();
                }
            };
            postOnUiThreadDelayed(mCheckIsRealVisibleToAbandonTraceTask, 5000);
        }
    }

    private void cancelCheckIsRealVisibleToAbandonTraceTask() {
        if (mCheckIsRealVisibleToAbandonTraceTask != null) {
            removeCallbacks(mCheckIsRealVisibleToAbandonTraceTask);
            mCheckIsRealVisibleToAbandonTraceTask = null;
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        checkIsRealVisibleForTrace();
        PlayPageLoadingOptimizationManager.INSTANCE.onPlayPageLoading(true);
        mDefaultBgColor = PlayPageDataManager.DEFAULT_BACKGROUND_COLOR;
        mBackgroundColor = mDefaultBgColor;
        mFlTopLayout = findViewById(R.id.fl_play_page_top);
        mVgTitleBar = findViewById(R.id.main_vg_title_bar);
        mVgTitleBar.setClickable(true);// 避免事件穿透都标题栏下面去
        myFitStatusBar(mVgTitleBar);
        hideTopBar();
        initTopSlideView();
        initViewPager();
        mIvBack = findViewById(R.id.main_iv_back);
        mIvBack.setOnClickListener(mOnClickListener);
        mBackColor = mContext.getResources().getColor(com.ximalaya.ting.android.framework.R.color.framework_white_ffffff);
        mVBg = findViewById(R.id.main_v_background);
        updateBackground(mBackgroundColor);
        initComponents();
        registerPlayFragmentService();

        int playMode = PlayTools.getSavedPlayMode(mContext);
        XmPlayerManager.getInstance(mContext).setPlayMode(XmPlayListControl.PlayMode.getIndex(playMode));

        PlayPageDataManager.getInstance().preLoadAudioPlayFragmentMainPartView(mContext);

        AutoTraceHelper.bindPageDataCallback(this, new AutoTraceHelper.IDataProvider() {
            @Override
            public Object getData() {
                return PlayPageTraceUtil.componentTraceData(getContext(), mSoundInfo);
            }

            @Override
            public Object getModule() {
                return mTabList;
            }

            @Override
            public String getModuleType() {
                return null;
            }
        });

        AnchorFollowManage.getSingleton().addFollowListener(followAnchorListener);
        AppCommentManager.INSTANCE.init(this);
        mTraceHelper.postNode("初始化完成", false, false, "");
        mTraceHelper2.postNode("初始化完成", false, false, "");

        observerTheme();
    }

    private void initComponents() {
        PageStartOpt.PlayPageOpt.log("initComponents, enablePostUIOperate: " + PageStartOpt.PlayPageOpt.enablePostUIOperate());
        if (!PageStartOpt.PlayPageOpt.enablePostUIOperate()) {
            initComponentsInner();
        }
    }

    private void initComponentsInner() {
        if (alreadyInit) {
            return;
        }
        alreadyInit = true;
        initShareComponent();
        initLiveEntryComponent();
        initShareVipEntryComponent();
        initBroadCastComponent();
        initShareTipsComponent();
    }

    private AnchorFollowManage.IFollowAnchorListener followAnchorListener = (uid, follow) -> {
        PlayPageDataManager.getInstance().updateFollowStatus(uid, follow);
        if (!PlayPageDataManager.getInstance().checkFriendVisible()) {
            XmPlayerManager.getInstance(mContext).stop();
            checkTrackVisiblePermission();
        }
    };

    private void registerAdCoverHideService() {
        //等待前贴广告播放完成展示
        if (mAdCoverHideService != null) {
            return;
        }
        mAdCoverHideService = PlayPageInternalServiceManager.getInstance().getService(IAdCoverHideService.class);
        if (mAdCoverHideService != null) {
            mAdCoverHideService.registerAdCoverStateChange(new IAdCoverHideService.IAdCoverStateChangeListener() {
                final Runnable runnable = new Runnable() {
                    @Override
                    public void run() {
                        showTopRightEntryAfterAd(mSoundInfo);
                    }
                };

                @Override
                public void onAdCoverHide() {
                    removeCallbacks(runnable);
                    postOnUiThreadDelayed(runnable, 1000);
                }

                @Override
                public void noAdCover() {
                    showTopRightEntryAfterAd(mSoundInfo);
                }

                @Override
                public void onAdCoverShow() {
                }
            });
        }
    }

    private void registerPlayFragmentService() {
        PlayPageInternalServiceManager.getInstance().registerService(IPlayFragmentService.class, mPlayFragmentService);
    }

    private void myFitStatusBar(ViewGroup titleBar) {
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewGroup.LayoutParams titleBarLayoutParams = titleBar.getLayoutParams();
            if (titleBarLayoutParams instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams) titleBarLayoutParams).topMargin =
                        ((ViewGroup.MarginLayoutParams) titleBarLayoutParams).topMargin + BaseUtil.getStatusBarHeight(mContext);
                mTitleBarBottom =
                        titleBarLayoutParams.height + ((ViewGroup.MarginLayoutParams) titleBarLayoutParams).topMargin;
                titleBar.setLayoutParams(titleBarLayoutParams);
            }
        } else {
            ViewGroup.LayoutParams titleBarLayoutParams = titleBar.getLayoutParams();
            mTitleBarBottom = titleBarLayoutParams.height;
        }
    }

    private void initTopSlideView() {
        mVTopSlideView = findViewById(R.id.main_top_slid_view);
        mVTopSlideView.setTopShadowViewBackground(0);
        mVTopSlideView.setContentBackground(Color.TRANSPARENT);
        mVTopSlideView.setOnFinishListener(() -> {
            Activity activity = getActivity();
            boolean isMain = activity instanceof MainActivity;
            if (!isMain || !BackUserPullUpManager.handleBackUserPullUp(((MainActivity) activity))) {
                showPreFragment(true, false);
            }
            if (isMain) {
                ((MainActivity) activity).hidePlayFragment(this);
            }
            return true;
        });
        mVTopSlideView.setSlideListener(new SlideView.SlideListener() {
            @Override
            public void slideStart() {
                showPreFragment(true, true);
            }

            @Override
            public void slideEnd() {

            }

            @Override
            public void keepFragment() {
                //如果当前页面没有finish，把上一个页面隐藏掉
                hidePreFragment(true, true);
            }
        });
        // 当tab页中没有纵向滑动的控件时，让TopSlideView直接拦截事件。当显示弹出的页面时，不允许TopSlideView拦截事件
//        mVTopSlideView.setSlideMotionEventListener(motionEvent -> mShowingTopFragment == null);
    }

    private void initViewPager() {
        mViewPager = findViewById(R.id.main_view_pager);
        mViewPager.setScrollable(true);
        mViewPager.setOffscreenPageLimit(1);
        mViewPager.addOnPageChangeListener(mOnPageChangeListener);
        mTab = findViewById(R.id.main_tab);
        mTab.setOnTabClickListener(position -> {
            chencAndSetArgumentsForSelectedVideoTab(position);

            // 新声音播放页-顶部tab  点击事件
            long trackId = 0;
            long albumId = 0;
            Track track = XmPlayerManager.getInstance(mContext).getCurrSoundIgnoreKind(false);
            if (track != null) {
                trackId = track.getDataId();
                SubordinatedAlbum album = track.getAlbum();
                if (album != null) albumId = album.getAlbumId();
            }
            String item = mTabAdapter.getPageTitle(position) != null ? mTabAdapter.getPageTitle(position).toString() : "";
            new XMTraceApi.Trace()
                    .click(17491)
                    .put("Item", item)
                    .put("currPage", "newPlay")
                    .put("currentContentType", "声音")
                    .put("currentContentId", String.valueOf(trackId))
                    .put("currTrackId",String.valueOf(trackId))
                    .put("currAlbumId", albumId + "")
                    .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(
                            XmRequestPage.PAGE_PLAY_PAGE))
                    .createTrace();
        });
        UIConsistencyManager.getInstance().setThirdCategoryTabUIConsistency(getContext(), mTab);
    }

    private void initShareComponent() {
        mShareComponent = new ShareComponent(this);
        mShareComponent.init();
        mShareComponent.setShareClickListener(() -> {
            if (mCurShowingFragment instanceof BasePlayPageTabFragment) {
                return ((BasePlayPageTabFragment) mCurShowingFragment).onShareClick();
            }
            return false;
        });
    }

    private void initFloatingControlComponent() {
        mFloatingControlBarComponent = new FloatingControlBarComponent(this, new IFloatingPlayControlComponent.IFloatingControlBarActionListener() {
            @Override
            public void onCoverClicked() {
                gotoAudioPlayPageTop();
            }

            @Override
            public void onPlayLocationClicked() {

            }

            @Override
            public void onPlayOrPauseClicked() {

            }

            @Override
            public void onCloseClicked() {

            }
        }, true);
        mFloatingControlBarComponent.setShouldShowCloseBtn(false);
        addPlayStatusListener(mFloatingControlBarComponent);
        mFloatingControlBarNormalMarginBottom = BaseUtil.dp2px(getContext(), 10);
        mFloatingControlBarComponent.setMarginBottom(mFloatingControlBarNormalMarginBottom);
    }

    private void initLiveEntryComponent() {
        mLiveAndMcEntryComponent = new LiveAndMcEntryComponent(this);
    }

    private void initShareVipEntryComponent() {
        mShareVipEntryComponent = new ShareVipEntryComponent(this);
    }

    private void initShareTipsComponent() {
        shareTipsComponent = new ShareTipsComponent(this);
    }

    private void initBroadCastComponent() {
        mBroadCastComponent = new PlayFragmentBroadCastComponent(this, (operationType, optionalItem) -> {
            if (PlayFragmentBroadCastComponent.OPERATION_CHECK_AUTHORITY == operationType) {
                if (null == mSoundInfo || !(optionalItem instanceof Intent)) {
                    // 已购或者无须购买
                    return;
                }
                Intent intent = (Intent) optionalItem;
                PayResultManager.ResultData data = new PayResultManager.ResultData(PayResultManager.ResultData.FRAGMENT_TYPE_PLAY);
                data.playingSoundInfo = mSoundInfo;
                PayResultManager.checkPayResult(intent, data, (int type, String msg) -> {
                    Logger.logToFile("PlayFragmentNew 广播的type为 " + type + ", 广播的msg为 " + (null == msg ? "null" : msg));
                    if (PayResultManager.TYPE_BUY_VIP_SOUND == type
                            || PayResultManager.TYPE_BUY_VIP == type
                            || PayResultManager.TYPE_BUY_LEFT_ALL_TRACK == type
                            || PayResultManager.TYPE_BUY_VERTICAL_VIP_MASTER_CLASS == type
                            || type == PayResultManager.TYPE_BUY_VERTICAL_VIP) {
                        Logger.logToFile("PlayFragmentNew receive vip broadcast isFreeListenV2Ope =" + FreeListenConfigManager.isFreeListenV2Open() + " listenTime = " + FreeListenTimeManager.getListenTime(getContext()));
                        if (FreeListenConfigManager.isFreeListenV2Open() && FreeListenTimeManager.getListenTime(getContext()) != 0 && PayResultManager.TYPE_BUY_VIP == type) {
                            // 开通大会员，在畅听身份刷新前清除剩余畅听时长
                            FreeListenTimeManager.clearAllListenTime(6, 0, null);
                        }
                        VipStateChangeManager.updateFreeListenUserSceneInfo("PlayFragmentNew");
//                        AdMakeVipLocalManager.getInstance().requestIsTargetUser();
                        mSoundInfo.updateTrackAuthority(true);
                        if (isVisible()) {
                            PlayPageDataManager.getInstance().notifyAuthorizedStatusChanged();
                        }
                    } else if (PayResultManager.TYPE_FREE_LISTEN_UNLOCK_WITH_TIME == type
                            || PayResultManager.TYPE_FREE_LISTEN_UNLOCK_WITH_TIME_END == type) {
                        PlayPageDataManager.getInstance().notifyAuthorizedStatusChanged();
                    }
                }, null);
            }
        });
    }

    private void updateBackButton(boolean needChangeColor) {
        ViewStatusUtil.setVisible(View.VISIBLE, mIvBack);
        if (needChangeColor) {
            setBackColor();
        }
    }

    private void setBackColor() {
        if (mIvBack != null && mIvBack.getVisibility() == View.VISIBLE) {
            mIvBack.setColorFilter(new PorterDuffColorFilter(mBackColor, PorterDuff.Mode.SRC_IN));
        }
    }

    private static int setColorTransparency(float alphaScale, int color) {
        int alpha = (int)(alphaScale * 255);
        return (color & 0x00ffffff) | (alpha << 24);
    }

    @Override
    protected void loadData() {
        // 在onMyResume里面判断是否要加载数据，已经覆盖了第一次打开的情况，这里就不加载了
    }

    @Override
    protected View getLoadingView() {
        return View.inflate(getActivity(), R.layout.main_play_fragment_loading_view, null);
    }

    @Override
    protected View getNetworkErrorView() {
        return null;
    }

    @Override
    protected View getNoContentView() {
        return null;
    }

    private void loadDataFromNet(boolean force) {
        Logger.d(TAG, "loadDataFromNet");
        PerformanceMonitor.traceEnd("before loadData", 5001);
        PerformanceMonitor.traceBegin("loadData");

        mTraceHelper.postNode("开始加载数据", false, false, "");
        mTraceHelper2.postNode("开始加载数据", false, false, "");

        PageStartOpt.PlayPageOpt.setEnableInflateOnMainThread(true);    //接口请求期间允许主线程解析
        preloadXmlIfNeed();

        hideTopRightEntry();
        clearTopRightEntryData();
        if (mTabAdapter == null) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        Logger.i("ArriveTraceManagerTest", "PlayFragmentNew load sound info");
        PlayPageDataManager.getInstance().loadTabAndSoundInfo(new PlayPageDataManager.IPlayPageDataCallback() {
            @Override
            public void onSuccess(@Nullable PlayPageTabAndSoundInfo data) {
                Logger.d(TAG, "loadTabAndSoundInfo onSuccess");
                Logger.i("ArriveTraceManagerTest", "PlayFragmentNew load sound info success");
                notifyIsNewPlayPage(data);

                PerformanceMonitor.traceEnd("loadData", 5002);
                PerformanceMonitor.traceBegin("bindData");

                PageStartOpt.PlayPageOpt.onDataLoadSuccess();
                mTraceHelper.postNodeOnlyLocalDebug("数据加载成功回调");
                mHasLoadedData = true;
                doAfterAnimation(() -> {
                    if (canUpdateUi()) {
                        if (data != null && data.getSoundInfo() != null && data.getSoundInfo().albumInfo != null) {
                            mCurrentAlbumPlaySecondTime = data.getSoundInfo().albumInfo.realTimePlayDuration;
                        }
                        mTraceHelper.postNodeOnlyLocalDebug("动画结束后");
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        boolean noCopyRight = false;
                        if (data != null && data.getSoundInfo() != null && data.getSoundInfo().otherInfo != null
                                && data.getSoundInfo().otherInfo.isNoCopyright) {
                            noCopyRight = true;
                        }

                        PageStartOpt.traceBegin("handleOnDataLoaded");
                        handleOnDataLoaded(data, noCopyRight);
                        PageStartOpt.traceEnd("handleOnDataLoaded");

                        PageStartOpt.traceBegin("requestGetQuickData");
                        requestGetQuickData(data);
                        PlayPageDataSuppleManager.getInstance().setLastSoundInfo(data);

                        checkTrackVisiblePermission();

                        PageStartOpt.traceEnd("requestGetQuickData");
                    } else {
                        notifyTraceFinish();
                    }
                });
                XmPageErrorMonitor.INSTANCE.post(PlayPageLoadErrorConstants.PAGE_NAME,
                        (data != null) ? PlayPageLoadErrorConstants.RESULT_SUCCESS : PlayPageLoadErrorConstants.RESULT_NO_DATA);
                if (data != null && data.getSoundInfo() != null && data.getSoundInfo().trackInfo != null) {
                    ArriveTraceManager.onItingPageDataLoaded(AppConstants.PAGE_SOUND, data.getSoundInfo().trackInfo.trackId);
                }
            }

            @Override
            public void onError(int code, String message, @Nullable PlayPageTabAndSoundInfo object,
                                CopyrightExtendInfo copyrightExtendInfo) {
                Logger.d(TAG, "loadTabAndSoundInfo onError");
                if (code == 927) {
                    PlayTools.pause(getContext(), PauseReason.Business.AudioPlayPage_LoadData);
                }
                if (canUpdateUi()) {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                    Track track = getPlayingTrack();
                    boolean noCopyRightOrOffShelf = false;
                    Track playTrack = new Track();
                    playTrack.setDataId(PlayPageDataManager.getInstance().getCurTrackId());
                    if (track != null) {
                        if (code == 927) {
                            noCopyRightOrOffShelf = true;
                            boolean hasShowDialog = CopyrightUtil.showCopyRightTipsDialogIfNeeded(PlayFragmentNew.this, copyrightExtendInfo);
                            if (!hasShowDialog) {
                                CustomToast.showFailToast(message);
                            }
                        } else if (code == 76 || code == 924) {
                            noCopyRightOrOffShelf = true;
                            // 显示已下架弹窗

                            PlayNoCopyRightDialog playNoCopyRightDialog = PlayNoCopyRightDialog
                                    .newInstance(track.getDataId(), track.getRecSrc(), track.getRecTrack());
                            playNoCopyRightDialog.show(getChildFragmentManager(), PlayNoCopyRightDialog.TAG);

                        } else if (code == 929 && !RouteServiceUtil.getDownloadService().isDownloaded(playTrack)) {
                            noCopyRightOrOffShelf = true;
                            postOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    if (isRealVisable() && OffSaleUtils.isMatchAb()) {
                                        OffSaleUtils.tryShowOffSaleDialog(
                                                "newPlay",
                                                0,
                                                PlayPageDataManager.getInstance().getCurTrackId()
                                        );
                                    }
                                }
                            });

                        } else {
                            track.setHasCopyRight(true);
                            if (!LockScreenUtil.isLockOrAlarmScreenActivityShowing() && code != BaseCall.ERROR_CODE_DEFALUT) {
                                CustomToast.showFailToast(message);
                            }
                        }
                    }

                    handleOnDataLoaded(object, noCopyRightOrOffShelf);

                    // 这种情况播放页接口还在请求，等请求结束还会回调，这里不上报
                    if (code != PlayPageLoadErrorConstants.CODE_PRE_LOAD_FROM_CACHE_OR_PLAYER) {
                        boolean gotCache = object != null;
                        String extra = "gotCache=" + gotCache + ",ret=" + code;
                        boolean isNetworkAvaliable = NetworkUtils.isNetworkAvaliable(mContext);
                        XmPageErrorMonitor.INSTANCE.post(PlayPageLoadErrorConstants.PAGE_NAME, isNetworkAvaliable ? -2 : -1,
                                null, extra);
                    }
                } else {
                    notifyTraceFinish();
                }
            }

            @Override
            public void onError(int code, String message) {
                notifyTraceFinish();
            }
        }, force);

        if (PageStartOpt.PlayPageOpt.enablePostUIOperate()) {
            //开启优化后，加载数据的同步初始化 UI
            initComponentsInner();
        }
    }

    private VisibleOnlyToFriendDialog lastVisibleOnlyToFriendDialog = null;

    private void checkTrackVisiblePermission() {
        if (PlayPageTabUtil.isPlayX(mSoundInfo)) {
            return;
        }
        if (PlayPageTabUtil.isPlayY(mSoundInfo)) {
            return;
        }

        if (lastVisibleOnlyToFriendDialog != null && lastVisibleOnlyToFriendDialog.isVisible()
                && mSoundInfo != null && mSoundInfo.trackInfo != null
                && mSoundInfo.trackInfo.trackId == lastVisibleOnlyToFriendDialog.getCurrentSoundId()) {
           return;
        }

        if (lastVisibleOnlyToFriendDialog != null) {
            lastVisibleOnlyToFriendDialog.dismiss();
            lastVisibleOnlyToFriendDialog = null;
            if (ConstantsOpenSdk.isDebug) {
                Logger.d(TAG, " lastVisibleOnlyToFriendDialog trace=" + Log.getStackTraceString(new Throwable()));
            }
        }

        if (!PlayPageDataManager.getInstance().checkFriendVisible() && mSoundInfo != null && isVisible()) {
            lastVisibleOnlyToFriendDialog = VisibleOnlyToFriendDialog.show(getParentFragmentManager(), mSoundInfo, (refresh, trackId) -> {
                    lastVisibleOnlyToFriendDialog = null;
                    if (mSoundInfo != null && mSoundInfo.trackInfo != null && trackId == mSoundInfo.trackInfo.trackId) {
                        if (refresh) {
                            loadDataFromNet(true);
                            XmPlayerManager.getInstance(mContext).play();
                        } else {
                            FragmentUtil.dismissAllDialogs(getChildFragmentManager());
                            if (!handleBackEvent()) {
                                finishFragment();
                            }
                        }
                    }
                    return null;
                });
        }
    }

    private void notifyIsNewPlayPage(PlayPageTabAndSoundInfo data) {
        if (data != null && data.getSoundInfo() != null){
            boolean isNewPageStyle = PlayPageTabUtil.isNewAudioPage(data.getSoundInfo());
            XmPlayerManager.getInstance(getContext()).setIsNewPlayPage(isNewPageStyle);
            PlayPageStyleUtil.saveNewPageStyle(isNewPageStyle);
        }
    }

    private void notifyTraceFinish() {
        mTraceHelper.notifyPageFailed();
        mTraceHelper2.notifyPageFailed();
        PlayPageLoadingOptimizationManager.INSTANCE.onPlayPageLoading(false);
        cancelCheckIsRealVisibleToAbandonTraceTask();
    }

    private boolean hasVideoOfTrackId(long trackId) {
        IVideoPlayFragmentService videoPlayFragmentService =
                PlayPageInternalServiceManager.getInstance().getService(IVideoPlayFragmentService.class);
        if (videoPlayFragmentService != null) {
            return videoPlayFragmentService.hasVideo(trackId);
        }
        return false;
    }

    private void handleOnDataLoaded(@Nullable PlayPageTabAndSoundInfo data, boolean noCopyRightOrOffShelf) {
        if (data != null) {

            PageStartOpt.traceBegin("handleOnDataLoaded s1");

            if (isRealVisable() && data.getSoundInfo() != null && data.getSoundInfo().otherInfo != null
                    && data.getSoundInfo().otherInfo.expiringSoon && data.getSoundInfo().albumInfo != null) {
                if (PlayPageDialogManager.INSTANCE.hasDialogShowIngOnPlayPage()) {
                    mDialogShowChangeListener = isShow -> {
                        if (!isShow) {
                            HandlerManager.postOnUIThreadDelay(() -> {
                                if (canUpdateUi() && isRealVisable() && data.getSoundInfo() != null && data.getSoundInfo().otherInfo != null
                                        && data.getSoundInfo().otherInfo.expiringSoon && data.getSoundInfo().albumInfo != null) {
                                    AlbumOffLineManager.showOffLineDialog(this, "newPlay", data.getSoundInfo().albumInfo.albumId);
                                }
                            }, 1000);
                        }
                    };
                    PlayPageDialogManager.INSTANCE.addXPlayPageDialogShowChangeListener(mDialogShowChangeListener);
                } else {
                    HandlerManager.postOnUIThreadDelay(() -> {
                        if (canUpdateUi() && isRealVisable() && data.getSoundInfo() != null && data.getSoundInfo().otherInfo != null
                                && data.getSoundInfo().otherInfo.expiringSoon && data.getSoundInfo().albumInfo != null) {
                            AlbumOffLineManager.showOffLineDialog(this, "newPlay", data.getSoundInfo().albumInfo.albumId);
                        }
                    }, 2000);
                }
            }
            if (mTabAdapter != null) {
                mTabAdapter.setSoundInfo(data.getSoundInfo());
            }
            if (noCopyRightOrOffShelf) {
                // 下架或者无版权状态，只显示声音页
                data.setTabList(generateTabOnlySound());
            } else {
                if (ToolUtil.isEmptyCollects(data.getTabList())) {
                    // 说明是从已下载列表(声音或视频列表)进入，无网络/网络出错，此时手动加上视频tab
                    long currTrackId = PlayCommentUtil.getCurTrackId(data.getSoundInfo());
                    if ((mArgumentsForVideo != null && mArgumentsForVideo.getBoolean(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_PLAY_DOWNLOAD_VIDEO)) ||
                            (PlayCommentUtil.isVideoDownloadedByTrackId(currTrackId) && !NetworkUtils.isNetworkAvaliable(mContext))) {
                        if (mArgumentsForVideo == null) {
                            mArgumentsForVideo = new Bundle();
                        }
                        mArgumentsForVideo.putBoolean(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_PLAY_DOWNLOAD_VIDEO, true);
                        boolean isTrackIdArrayContainCurrTrack = false;
                        long[] trackIdArray = mArgumentsForVideo.getLongArray(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_DOWNLOADED_TRACK_ID_ARRAY);
                        if (trackIdArray != null) {
                            for (long id : trackIdArray) {
                                if (id == currTrackId) {
                                    isTrackIdArrayContainCurrTrack = true;
                                    break;
                                }
                            }
                        }
                        // 判断是不是在原来的已下载视频的trackId列表里
                        if (!isTrackIdArrayContainCurrTrack) {
                            trackIdArray = new long[]{PlayCommentUtil.getCurTrackId(data.getSoundInfo())};
                            mArgumentsForVideo.putLongArray(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_DOWNLOADED_TRACK_ID_ARRAY, trackIdArray);
                        }
                        data.setTabList(generateDefaultTabsWithVideo());
                    } else if (!NetworkType.isConnectTONetWork(mContext) && hasVideoOfTrackId(currTrackId)) {
                        data.setTabList(generateDefaultTabsWithVideo());
                    } else {
                        data.setTabList(generateDefaultTabs());
                    }
                }
            }
            PlayingSoundInfo soundInfo = data.getSoundInfo();
            long lastTrackId = 0;
            if (mSoundInfo != null && mSoundInfo.trackInfo != null) {
                lastTrackId = mSoundInfo.trackInfo.trackId;
            }
            long trackId = 0;
            if (soundInfo != null && soundInfo.trackInfo != null) {
                trackId = soundInfo.trackInfo.trackId;
            }

            mSoundInfo = soundInfo;

            checkSubscribeUnlock();

            if (!videoWhiteListChecked && mNeedCheckToJumpToVideoTab && soundInfo != null && soundInfo.trackInfo != null && soundInfo.trackInfo.isVideo &&
                    soundInfo.trackInfo.trackId != lastTrackId) {
                if (mFocusTabType == PlayPageTab.TYPE_NONE && isInNeedJumpToVideoTabList()) {
                    mFocusTabType = PlayPageTab.TYPE_VIDEO;

//                    if (viewModel != null) {
//                        Bundle tempArgumentsForVideo = new Bundle();
//                        tempArgumentsForVideo.putLong("xplay_init_video_id", soundInfo.trackInfo.trackId);
//                        tempArgumentsForVideo.putLong(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_ALBUM_ID, soundInfo.trackInfo.albumId);
//                        XPlayVideModel.XPlayPageArgument xPlayPageArgument = new XPlayVideModel.XPlayPageArgument(
//                                false, PlayPageTab.TYPE_VIDEO, tempArgumentsForVideo
//                        );
//                        viewModel.getXArgumentLiveData().setValue(xPlayPageArgument);
//                    }
                }

//                if (mFocusTabType == PlayPageTab.TYPE_VIDEO) {
//                    if (viewModel != null) {
//                        Bundle tempArgumentsForVideo = new Bundle();
//                        tempArgumentsForVideo.putLong("xplay_init_video_id", soundInfo.trackInfo.trackId);
//                        tempArgumentsForVideo.putLong(VideoPlayParamsBuildUtil.KEY_VIDEO_PLAY_ALBUM_ID, soundInfo.trackInfo.albumId);
//                        XPlayViewModel.XPlayPageArgument xPlayPageArgument = new XPlayViewModel.XPlayPageArgument(
//                                false, PlayPageTab.TYPE_VIDEO, fromVideoChannel, tempArgumentsForVideo
//                        );
//                        viewModel.getXArgumentLiveData().setValue(xPlayPageArgument);
//                    }
//                }
            }
            mNeedCheckToJumpToVideoTab = false;

            //xPlay 页面，仅保留声音tab
            if (PlayPageTabUtil.isPlayX(mSoundInfo) || PlayPageTabUtil.isPlayY(mSoundInfo)) {
                List<PlayPageTab> tabList = new ArrayList<>();
                PlayPageTab tab1 = new PlayPageTab();
                tab1.setName("声音");
                tab1.setType(PlayPageTab.TYPE_SOUND);
                tabList.add(tab1);
                data.setTabList(tabList);
            }

            PlayTimelineCardManager.INSTANCE.setTimelineCardList(mSoundInfo != null ? mSoundInfo.talkCards : null, mSoundInfo);

            PageStartOpt.traceEnd("handleOnDataLoaded s1");
            PageStartOpt.traceBegin("handleOnDataLoaded s2");

            updateTab(data.getTabList(), true);

            PageStartOpt.traceEnd("handleOnDataLoaded s2");
            PageStartOpt.traceBegin("handleOnDataLoaded s3");

            updateBackground(soundInfo);
            showOtherRightEntry(mSoundInfo);
//            showShareEntry(mSoundInfo);
//            updateShareComponentVisibility();
//            if (mShareComponent != null) {
//                mShareComponent.setSoundInfo(data.getSoundInfo());
//            }
            if (mFloatingControlBarComponent != null) {
                mFloatingControlBarComponent.setSoundInfo(data.getSoundInfo());
            }

            // 在data load完成后做一些操作
            checkAndShowFirstPageScrollTip();

            PageStartOpt.traceEnd("handleOnDataLoaded s3");
            PageStartOpt.traceBegin("handleOnDataLoaded s4");

            // 等tab加载完在出，因为有些tab不能出直播、mc等
            postOnUiThread(() -> showTopRightEntry(data.getSoundInfo()));
            uploadPLCShareZhuLi(data.getSoundInfo());

            PageStartOpt.traceEnd("handleOnDataLoaded s4");

            PerformanceMonitor.traceEnd("bindData", 5007);
            PerformanceMonitor.traceBegin("updateUI");

            mTraceHelper.postNode("数据绑定完成", false, false, "");
            mTraceHelper2.postNode("数据绑定完成", false, false, "");
            if (getView() != null) {
                mTraceHelper2.postPageEndNode();
                mTraceHelper.postPageEndNodeAfterRenderComplete(getView(), () -> {
                    PlayPageLoadingOptimizationManager.INSTANCE.onPlayPageLoading(false);

                    PerformanceMonitor.traceEnd("updateUI", 5004);
                    PageStartOpt.playPageLoadFinish();
                });
                tryShowOpenAiDocGuideDialog();
            } else {
                notifyTraceFinish();
            }
        } else {
            notifyTraceFinish();
        }
        hideTopBar();
        traceBackBtnViewed();
        getAdFreeAbTest();
    }

    private void hideTopBar(){
        if (mFlTopLayout!=null){
            mFlTopLayout.setVisibility(isRnPlayPage(PlayPageTab.TYPE_SOUND) ? View.GONE : View.VISIBLE);
        }
    }

    private void uploadPLCShareZhuLi(PlayingSoundInfo soundInfo) {
        PLCShareManager.INSTANCE.uploadPLCShareZhuLi(soundInfo);
    }

    private void showTopRightEntry(PlayingSoundInfo soundInfo) {
        // 互动开屏播放页不出直播、mc等右上角入口
        if (!isCurrentTabOfType(PlayPageTab.TYPE_SOUND) || XPlayPageRef.get() != null) {
            hideTopRightEntry();
            return;
        }
        registerAdCoverHideService();
        if (mAdCoverHideService != null && mAdCoverHideService.getCurAdState() == 3) {
            showTopRightEntryAfterAd(soundInfo);
        } else if (PlayPageTabUtil.isNewAudioPage(soundInfo)) {
            showTopRightEntryAfterAd(soundInfo);
        }
    }

    private void showTopRightEntryAfterAd(PlayingSoundInfo soundInfo) {
        if (!canUpdateUi()) {
            return;
        }

        // 不是声音播放页，不显示右上角入口
        if (!isAudioPlayFragment(mCurShowingFragment)) {
            return;
        }


        if (soundInfo != null && soundInfo.trackInfo != null &&
                soundInfo.trackInfo.trackId == PlayPageDataManager.getInstance().getCurTrackId()) {
            if (shouldShowShareTipsComponent()) {
                showShareTips(soundInfo, false);
            } else {
                showLiveAndMcEntry(soundInfo);
            }
        }
    }

    private void showShareTips(PlayingSoundInfo soundInfo, boolean isForce) {
        if (shareTipsComponent == null) {
            return;
        }
        shareTipsComponent.show(soundInfo, new IShareTipsShowingListener() {
            @Override
            public void isShowing(boolean showing) {
                if (showing) {
                    hideShareVipEntryComponent(true);
                    // 产品要求展示tips的时候强制展示动效
                    if (mShareComponent != null) {
                        PlayPageMinorDataManager.getInstance().getData(soundInfo.trackInfo.trackId, new IDataCallBack<PlayPageMinorData>() {
                            @Override
                            public void onSuccess(@Nullable PlayPageMinorData data) {
                                updateShareComponentVisibility();
                                if (mShareComponent != null) {
                                    mShareComponent.setSoundInfo(soundInfo, data);
                                    mShareComponent.showAnimationImmediately();
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                updateShareComponentVisibility();
                                if (mShareComponent != null) {
                                    mShareComponent.setSoundInfo(soundInfo, null);
                                    mShareComponent.showAnimationImmediately();
                                }
                            }
                        });
                    }
                } else if (!isForce) {
                    showLiveAndMcEntry(soundInfo);
                }
            }

            @Override
            public void onTipsClick() {
                boolean shouldShowShare = true;
                if (mCurShowingFragment instanceof BasePlayPageTabFragment) {
                    shouldShowShare = !((BasePlayPageTabFragment) mCurShowingFragment).onShareClick();
                }
                if (mShareComponent != null && shouldShowShare) {
                    mShareComponent.showShareDialog();
                }
            }
        });
    }

    private void addSoundPatchFinishListener() {
        if (mShareComponent != null && canUpdateUi()) {
            if (mISoundPatchFinishListener == null) {
                mISoundPatchFinishListener = new ISoundPatchFinishListener() {
                    @Override
                    public void onSoundPatchFinish(@NonNull PlayingSoundInfo soundInfo) {
                        if ((mAdCoverHideService != null && mAdCoverHideService.getCurAdState() == 3)
                                || PlayPageTabUtil.isNewAudioPage(PlayPageDataManager.getInstance().getSoundInfo())) {
                            if (shouldShowShareTipsComponent()) {
                                hideLiveAndMcEntryComponent();
                                hideShareVipEntryComponent(false);
                                hideTopRightEntry();
                                showShareTips(soundInfo, true);
                            }
                        }
                    }
                };
            }
            PlayPageShareTipsManager.INSTANCE.setSoundPatchFinishListener(mISoundPatchFinishListener);
        }
    }

    private void removeSOundPatchFinishListener() {
        PlayPageShareTipsManager.INSTANCE.setSoundPatchFinishListener(null);
    }

    private void showLiveAndMcEntry(PlayingSoundInfo soundInfo) {
        // 由于有广告回调会调用这个方法，可能在声音切换后而soundinfo没加载前就调用，这时候soundinfo和正在播放的声音是不一致的，不进行更新
        if (soundInfo != null && soundInfo.trackInfo != null &&
                soundInfo.trackInfo.trackId == PlayPageDataManager.getInstance().getCurTrackId()) {
            // 如果后面右上角再加新的话，需要改下实现方式。定义基类和优先级，按优先级遍历出。
            if (mLiveAndMcEntryComponent != null && !mIsChildProtectMode) {
                mLiveAndMcEntryComponent.showLive(soundInfo, new LiveAndMcEntryComponent.ILoadLiveDataCallback() {
                    @Override
                    public void showLiveEntry(boolean show) {
                        if (show) {
                            hideShareVipEntryComponent(true);
                        } else {
                            hideLiveAndMcEntryComponent();
                            showOtherRightEntry(soundInfo);
                        }
                    }

                    @Override
                    public boolean shouldShowTips() {
                        return shareTipsComponent == null || !shareTipsComponent.isTipsShowing();
                    }
                });
            } else {
                hideLiveAndMcEntryComponent();
                showOtherRightEntry(soundInfo);
            }
        }
    }

//    private void showShareEntry(PlayingSoundInfo soundInfo) {
//        if (soundInfo == null || soundInfo.trackInfo == null) {
//            return;
//        }
//        PlayPageMinorDataManager.getInstance().getData(soundInfo.trackInfo.trackId, new IDataCallBack<PlayPageMinorData>() {
//            @Override
//            public void onSuccess(@Nullable PlayPageMinorData data) {
////                data.shareRightVo = new PlayPageMinorData.ShareRightVo();
////                data.shareRightVo.isEfficientRightExist = true;
////                data.shareRightVo.shareRightType = 1;
//                updateShareComponentVisibility();
//                if (mShareComponent != null) {
//                    mShareComponent.setSoundInfo(soundInfo, data);
//                }
//            }
//
//            @Override
//            public void onError(int code, String message) {
//                updateShareComponentVisibility();
//                if (mShareComponent != null) {
//                    mShareComponent.setSoundInfo(soundInfo, null);
//                }
//            }
//        });
//    }

    private void showOtherRightEntry(PlayingSoundInfo soundInfo) {
        if (soundInfo == null || soundInfo.trackInfo == null) {
            hideTopRightEntry();
            return;
        }
        PlayPageMinorDataManager.getInstance().getData(soundInfo.trackInfo.trackId, new IDataCallBack<PlayPageMinorData>() {
            @Override
            public void onSuccess(@Nullable PlayPageMinorData data) {
                // if (!showReadEntry(soundInfo, data)) {
                    showShareVipEntry(soundInfo, data);
                // }
                updateShareComponentVisibility();
                if (mShareComponent != null) {
                    mShareComponent.setSoundInfo(soundInfo, data);
                }
            }

            @Override
            public void onError(int code, String message) {
                showShareVipEntry(soundInfo, null);
                updateShareComponentVisibility();
                if (mShareComponent != null) {
                    mShareComponent.setSoundInfo(soundInfo, null);
                }
            }
        });

    }

    // private boolean showReadEntry(PlayingSoundInfo soundInfo, PlayPageMinorData minorData) {
    //     if (!isCurrentTabOfType(PlayPageTab.TYPE_SOUND)) {
    //         return false;
    //     }
    //     if (mReaderEntryComponent != null && mReaderEntryComponent.needShow(minorData)) {
    //         mReaderEntryComponent.show(soundInfo, minorData);
    //         hideLiveAndMcEntryComponent();
    //         hideShareVipEntryComponent(true);
    //         return true;
    //     } else {
    //         hideTopRightEntry();
    //         return false;
    //     }
    // }

    private void showShareVipEntry(PlayingSoundInfo soundInfo, PlayPageMinorData minorData) {
        if (!isCurrentTabOfType(PlayPageTab.TYPE_SOUND)) {
            return;
        }
        if (mShareVipEntryComponent != null && mShareVipEntryComponent.needShow(soundInfo, minorData)
            && !(minorData != null && LiveAndMcEntryComponent.needShowLiveAndMc(minorData.rightRecommendInfo))) {
            mShareVipEntryComponent.show(soundInfo, minorData);
            hideLiveAndMcEntryComponent();
        } else {
            // hideTopRightEntry();
            hideShareVipEntryComponent(false);
            AudioPlayPageAnimationManager.INSTANCE.noNeedShow(AudioPlayPageAnimationManager.EAnimation.SHARE_VIP);
            AudioPlayPageAnimationManager.INSTANCE.noNeedShow(AudioPlayPageAnimationManager.EAnimation.FAMILY);
        }
    }

    private void hideTopRightEntry() {
        hideLiveAndMcEntryComponent();
        hideShareVipEntryComponent(false);
        hideShareTipsComponent();
    }

    private void clearTopRightEntryData() {
        if (mLiveAndMcEntryComponent != null) {
            mLiveAndMcEntryComponent.clearData();
        }
    }

    private void hideShareVipEntryComponent(boolean notifyNoNeedShow) {
        if (mShareVipEntryComponent != null) {
            mShareVipEntryComponent.hide();
        }
        if (notifyNoNeedShow) {
            AudioPlayPageAnimationManager.INSTANCE.noNeedShow(AudioPlayPageAnimationManager.EAnimation.SHARE_VIP);
            AudioPlayPageAnimationManager.INSTANCE.noNeedShow(AudioPlayPageAnimationManager.EAnimation.FAMILY);
        }
    }

    private void hideLiveAndMcEntryComponent() {
        if (mLiveAndMcEntryComponent != null) {
            mLiveAndMcEntryComponent.hide();
        }
    }

    private void hideShareTipsComponent() {
        if (shareTipsComponent != null) {
            shareTipsComponent.hide();
        }
    }

    private List<PlayPageTab> generateDefaultTabsWithVideo() {
        List<PlayPageTab> tabList = new ArrayList<>();
        PlayPageTab tab1 = new PlayPageTab();
        tab1.setName("声音");
        tab1.setType(PlayPageTab.TYPE_SOUND);
        tabList.add(tab1);
        PlayPageTab tab2 = new PlayPageTab();
        tab2.setName("视频");
        tab2.setType(PlayPageTab.TYPE_VIDEO);
        tabList.add(tab2);
        // 判断不是青少年模式，才添加 评论 tab
        if (!ChildProtectManager.isChildProtectOpen(mContext)) {
            PlayPageTab tab3 = new PlayPageTab();
            tab3.setName("评论");
            tab3.setType(PlayPageTab.TYPE_COMMENT);
            tabList.add(tab3);
        }
        return tabList;
    }

    private List<PlayPageTab> generateDefaultTabs() {
        List<PlayPageTab> tabList = new ArrayList<>();
        PlayPageTab tab1 = new PlayPageTab();
        tab1.setName("声音");
        tab1.setType(PlayPageTab.TYPE_SOUND);
        tabList.add(tab1);
        // 判断不是青少年模式，才添加 评论 tab
        if (!ChildProtectManager.isChildProtectOpen(mContext)) {
            PlayPageTab tab3 = new PlayPageTab();
            tab3.setName("评论");
            tab3.setType(PlayPageTab.TYPE_COMMENT);
            tabList.add(tab3);
        }
        return tabList;
    }

    private List<PlayPageTab> generateTabOnlySound() {
        List<PlayPageTab> tabList = new ArrayList<>();
        PlayPageTab tab1 = new PlayPageTab();
        tab1.setName("声音");
        tab1.setType(PlayPageTab.TYPE_SOUND);
        tabList.add(tab1);
        return tabList;
    }

    private boolean isTabCanShow(int tabType) {
        boolean canShow = true;
        switch (tabType) {
            case PlayPageTab.TYPE_COMMENT:
                // 评论tab在青少年模式下不能显示
                canShow = !mIsChildProtectMode;
                break;
            case PlayPageTab.TYPE_PPT:
            case PlayPageTab.TYPE_DOC:
            case PlayPageTab.TYPE_AI_DOC:
            case PlayPageTab.TYPE_TTS:
            case PlayPageTab.TYPE_LRC:
                canShow = false;
                break;
            default:
                break;
        }
//        if (mSoundInfo != null && mSoundInfo.otherInfo != null && mSoundInfo.otherInfo.displayInteractive) {
//            // 互动开屏播放页只显示声音和评论tab
//            if (tabType != PlayPageTab.TYPE_SOUND && tabType != PlayPageTab.TYPE_COMMENT && tabType != PlayPageTab.TYPE_INTERACTIVE) {
//                canShow = false;
//            }
//        }
        return canShow;
    }

    private void updateTab(List<PlayPageTab> tabList) {
        updateTab(tabList, false);
    }

    private void updateTab(List<PlayPageTab> tabList, boolean onDataChanged) {
        mHasSetTab = true;
        Class<?> lastActiveTabClass = null;
        if (mTabAdapter != null) {
            lastActiveTabClass = mTabAdapter.getFragmentClassAtPositon(mViewPager.getCurrentItem());
        }

        boolean hasDocTab = false;
        boolean hasAIDocTab = false;
        boolean hasReactivePlayTab = false;
        mHasDocTab = false;
        mHasTimeDocTab = false;
        boolean isFromDownlaodVideo = isFromDownloadVideo();
        if (!ToolUtil.isEmptyCollects(tabList)) {
            mTabList = tabList;
            List<TabCommonAdapter.FragmentHolder> lastFragmentHolderList = null;
            if (mFragmentHolderList == null) {
                mFragmentHolderList = new ArrayList<>();
            } else {
                lastFragmentHolderList = new ArrayList<>(mFragmentHolderList);
                mFragmentHolderList.clear();
            }
           boolean hasFocusType = false;
            List<PlayPageTab> canShowTabList = new ArrayList<>();
            for (PlayPageTab tab : tabList) {
                if (tab == null) {
                    continue;
                }
                if (isTabCanShow(tab.getType())) {
                    if (PlayPageTab.TYPE_DOC == tab.getType()) {
                        hasDocTab = true;
                        mHasDocTab = true;
                    } else if (TYPE_INSTANT_SCRIP == tab.getType()) {
                        mHasTimeDocTab = true;
                    } else if (PlayPageTab.TYPE_AI_DOC == tab.getType()) {
                        hasAIDocTab = true;
                    } else if (PlayPageTab.TYPE_INTERACTIVE == tab.getType()) {
                        hasReactivePlayTab = true;
                    }
                    canShowTabList.add(tab);

                    if (tab.getType() == mFocusTabType) {
                        hasFocusType = true;
                    }
                }
            }
            boolean showAIDoc = false;
            if (hasAIDocTab && hasDocTab) {
                showAIDoc = PlayPageDocTabManager.INSTANCE.isShowAIDoc();
            }
            Logger.d(TAG, "mFocusTabType = " + mFocusTabType);
            // 无冒泡tab且mFocusTabType为冒泡tab则还原
            if (!hasReactivePlayTab && mFocusTabType == PlayPageTab.TYPE_INTERACTIVE || !hasFocusType && mFocusTabType != PlayPageTab.TYPE_NONE) {
                mFocusTabType = PlayPageTab.TYPE_SOUND;
            }

            // if (ConstantsOpenSdk.isDebug && PlayTrackIntroDetailTabFragment.test()) {
            //固定添加在第一个 方便测试
            //     PlayPageTab playPageTab = new PlayPageTab();
            //     playPageTab.setType(PlayPageTab.TYPE_TRACK_DETAIL);
            //     playPageTab.setName("详情");
            //     canShowTabList.add(0, playPageTab);
            // }

            for (PlayPageTab tab : canShowTabList) {
                if (tab == null) {
                    continue;
                }

                if (hasAIDocTab && hasDocTab) {
                    if (showAIDoc && PlayPageTab.TYPE_DOC == tab.getType()) {
                        continue;
                    } else if (!showAIDoc && PlayPageTab.TYPE_AI_DOC == tab.getType()) {
                        continue;
                    }
                }

                Class<? extends Fragment> clazz = PlayPageTabUtil.getClassByType(tab.getType(), PlayTools.showPlayFragmentReq, mSoundInfo);
                if (clazz != null) {
                    Bundle arguments = new Bundle();
                    if (mTitleBarBottom > 0) {
                        arguments.putInt(BasePlayPageTabFragment.BUNDLE_KEY_TITLE_BAR_HEIGHT, mTitleBarBottom);
                    }
                    if (mTaskId > 0) {
                        arguments.putLong(BasePlayPageTabFragment.BUNDLE_KEY_TASK_ID, mTaskId);
                    }
                    if (sIsOpenComment) {
                        arguments.putBoolean(BasePlayPageTabFragment.BUNDLE_KEY_OPEN_COMMENT, sIsOpenComment);
                    }

                    PlayPageTabUtil.addArguments(arguments, tab);
                    if (tab.getType() == PlayPageTab.TYPE_VIDEO && mArgumentsForVideo != null) {
                        arguments.putAll(mArgumentsForVideo);
                    }

                    if (PlayPageTab.TYPE_DOC == tab.getType() && hasAIDocTab) {
                        arguments.putBoolean(BasePlayPageTabFragment.BUNDLE_KEY_HAS_AI_DOC, true);
                    } else if (PlayPageTab.TYPE_AI_DOC == tab.getType() && hasDocTab) {
                        arguments.putBoolean(BasePlayPageTabFragment.BUNDLE_KEY_HAS_DOC, true);
                    }
                    TabCommonAdapter.FragmentHolder newTab = new TabCommonAdapter.FragmentHolder(clazz, tab.getName(), arguments, tab.getType());
                    mFragmentHolderList.add(newTab);
                    statTabShow(tab);
                }
            }

            if (mTabAdapter == null) {
                if (mFocusTabType == PlayPageTab.TYPE_NONE) {
                    mFocusTabType = PlayPageTab.TYPE_SOUND;
                }
                if (isInTabAbGroupOnOldPlayPage()){
                    mFocusTabType = getTabIdByAbGroup();
                }
                mTabAdapter = new PlayPageTabAdapter(getChildFragmentManager(), mFragmentHolderList, mSoundInfo);
                mTabAdapter.setPageApiForTab(mPlayPageApiForTab);
                if (PlayPageLoadingOptimizationManager.INSTANCE.needDelayOtherTabCreate()) {
                    if (mFocusTabType == PlayPageTab.TYPE_NONE) {
                        mViewPager.disableFillNeighbourTab(true);
                        PlayPageLoadingOptimizationManager.INSTANCE.addPlayPageLoadingListenersH(() -> {
                            if (canUpdateUi()) {
                                mViewPager.disableFillNeighbourTab(false);
                            }
                        });
                        HandlerManager.postOnUIThreadDelay(() -> {
                            if (canUpdateUi()) {
                                mViewPager.disableFillNeighbourTab(false);
                            }
                        }, 5000);
                    }
                }
                mTraceHelper.postNodeOnlyLocalDebug("before setAdapter");

                Runnable bindAdapterRunnable = new Runnable() {
                    @Override
                    public void run() {
                        PageStartOpt.traceBegin("setAdapter");
                        mViewPager.setAdapter(mTabAdapter);
                        mTab.setViewPager(mViewPager);

                        PageStartOpt.traceEnd("setAdapter");
                    }
                };

                if (PageStartOpt.PlayPageOpt.enablePostUIOperate()) {
                    HandlerManager.postOnUIThread(bindAdapterRunnable);
                } else {
                    bindAdapterRunnable.run();
                }
            } else {
                int oldFocusTabType = PlayPageTab.TYPE_NONE;
                // int newFocusTabType = PlayPageTab.TYPE_NONE;
                int oldTab = mViewPager.getCurrentItem();
                if (!ToolUtil.isEmptyCollects(lastFragmentHolderList) && lastFragmentHolderList.size() > oldTab) {
                    TabCommonAdapter.FragmentHolder tab = lastFragmentHolderList.get(oldTab);
                    oldFocusTabType = tab.type;
                }
//                if (oldFocusTabType == PlayPageTab.TYPE_VIDEO && mFocusTabType == PlayPageTab.TYPE_NONE) {
//                    mFocusTabType = PlayPageTab.TYPE_SOUND;
//                }
                // 如果tab之前就存在，需要复用，tab位置可能发生变化，匹配一下新的位置
                if (!ToolUtil.isEmptyCollects(mFragmentHolderList) && !ToolUtil.isEmptyCollects(lastFragmentHolderList)) {
                    Map<WeakReference<Fragment>, Integer> tabsNewPositionMap = new HashMap<>();
                    for (int i = 0; i < mFragmentHolderList.size(); i++) {
                        TabCommonAdapter.FragmentHolder fragmentHolder = mFragmentHolderList.get(i);
                        if (oldFocusTabType == fragmentHolder.type && mFocusTabType == PlayPageTab.TYPE_NONE) {
                            mFocusTabType = oldFocusTabType;
                        }
                        for (int j = 0; j < lastFragmentHolderList.size(); j++) {
                            TabCommonAdapter.FragmentHolder oldFragmentHolder = lastFragmentHolderList.get(j);
                            // 目前不存在多个tab是同一个类的情况，类相同就认为是相同tab
                            if (fragmentHolder.fragment != null
                                    && fragmentHolder.fragment.equals(oldFragmentHolder.fragment)) {
                                fragmentHolder.realFragment = oldFragmentHolder.realFragment;
                                if (fragmentHolder.realFragment != null) {
                                    // 把arguments应用到原有的对象中
                                    if (fragmentHolder.realFragment.get() != null) {
                                        Fragment fragment = fragmentHolder.realFragment.get();
                                        addArgumentsToFragment(fragmentHolder.args, fragment);
                                    }
                                    tabsNewPositionMap.put(fragmentHolder.realFragment, i);
                                }
                                break;
                            }
                        }
                    }
                    mTabAdapter.setTabsNewPositionMap(tabsNewPositionMap);
                }
                mTabAdapter.notifyDataSetChanged();
                mTab.notifyDataSetChanged();
//                if (oldFocusTabType == PlayPageTab.TYPE_VIDEO && mFocusTabType == PlayPageTab.TYPE_SOUND) {
//                    mFocusTabType = PlayPageTab.TYPE_NONE;
//                }
            }
            // 触发一下tab控件的绘制，解决可能出现的绘制时由于tab的textView还没有算好位置，导致分割线位置不对的问题
            mTab.post(() -> mTab.invalidate());

        }

        if ((mFragmentHolderList == null || mFragmentHolderList.size() <= 1)) {
            ViewUtil.setViewVisibilitySafe(mTab, View.INVISIBLE);
        } else {
            ViewUtil.setViewVisibilitySafe(mTab, View.VISIBLE);
        }
        Logger.d(TAG, "2 mFocusTabType = " + mFocusTabType);
        if (isInTabAbGroupOnOldPlayPage()){
            mFocusTabType = getTabIdByAbGroup();
            changeTabByType(mFocusTabType);
        }  else if (mFocusTabType != PlayPageTab.TYPE_NONE && onDataChanged) {
            changeTabByType(mFocusTabType);
            mFocusTabType = PlayPageTab.TYPE_NONE;
        } else if (lastActiveTabClass != null) {
            // 如果之前的tab在新的tab列表中已经不存在，则回到第一页
            boolean lastActiveTabExistsNow = false;
            for (TabCommonAdapter.FragmentHolder holder : mFragmentHolderList) {
                if (lastActiveTabClass == holder.fragment) {
                    lastActiveTabExistsNow = true;
                    // ai文稿需要从tab数据中获取文稿的请求地址，所以tab数据拿到后通知ai文稿页面刷新一下
                    if (onDataChanged && holder.realFragment != null) {
                        Fragment fragment = holder.realFragment.get();
                    }
                    break;
                }
            }
            if (!lastActiveTabExistsNow) {
                // 如果上一个声音停在ai文稿或者文稿页，那当前声音继续停留在文稿或者ai文稿页
                if ((PlayPageTabUtil.isClassOfType(PlayPageTab.TYPE_AI_DOC, lastActiveTabClass, mSoundInfo)
                        || PlayPageTabUtil.isClassOfType(PlayPageTab.TYPE_DOC, lastActiveTabClass, mSoundInfo)) && (hasAIDocTab || hasDocTab)) {
                    lastActiveTabExistsNow = true;
                }
            }
            if (!lastActiveTabExistsNow) {
                mViewPager.post(() -> mViewPager.setCurrentItem(0));
            }
        }
    }

    private void addArgumentsToFragment(Bundle arguments, Fragment fragment) {
        try {
            if (fragment != null && arguments != null) {
                if (fragment.getArguments() != null) {
                    fragment.getArguments().clear();
                    fragment.getArguments().putAll(arguments);
                } else {
                    fragment.setArguments(arguments);
                }
            }
        } catch (Exception e) {
            Logger.e(e);
        }
    }

    private void statTabShow(PlayPageTab tab) {
        PlayingSoundInfo.TrackInfo trackInfo = mSoundInfo != null ? mSoundInfo.trackInfo : null;
        // 上报有哪些tab
        new XMTraceApi.Trace()
                .setMetaId(17541)
                .setServiceId("slipPage")
                .put("exploreType", "1")
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put("currAlbumId", String.valueOf(mSoundInfo != null && mSoundInfo.albumInfo != null ?
                        mSoundInfo.albumInfo.albumId : 0))
                .put("categoryId", String.valueOf(trackInfo != null ? trackInfo.categoryId : 0))
                .put("anchorId", String.valueOf(mSoundInfo != null && mSoundInfo.userInfo != null ?
                        mSoundInfo.userInfo.uid : 0))
                .put("Item", tab.getName())
                .put("tabId", String.valueOf(tab.getType()))
                .put(XmRequestIdManager.CONT_ID, String.valueOf(trackInfo != null ? trackInfo.trackId : 0))
                .put(XmRequestIdManager.CONT_TYPE, "newPlayTopTab")
                .put(
                        XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(
                                XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace();
    }

    private void updateBackground(PlayingSoundInfo soundInfo) {
        if (soundInfo != null && soundInfo.trackInfo != null && !TextUtils.isEmpty(soundInfo.trackInfo.getValidCover())) {
            mCurCoverUrl = soundInfo.trackInfo.getValidCover();
            ImageManager.from(getActivity()).downloadBitmap(mCurCoverUrl, (lastUrl, bitmap) -> {
                if (!TextUtils.isEmpty(lastUrl) && lastUrl.equals(mCurCoverUrl)) {
                    if (viewModel != null) {
                        if (PlayPageTabUtil.isPlayY(soundInfo) || XPlayPageRef.get() instanceof YPlayFragment) {
                            viewModel.updateThemeDataY(bitmap);
                            if (yPlayViewModel != null) {
                                yPlayViewModel.updateCoverDomainColor(mCurCoverUrl, bitmap);
                            }
                        } else {
                            viewModel.updateThemeData(bitmap);
                        }
                    }
                    if (mCoverLoadedListenerSet != null && bitmap != null) {
                        for (ICoverLoadedListener listener : mCoverLoadedListenerSet) {
                            listener.onCoverLoaded(bitmap, mCurCoverUrl);
                        }
                    }
                }
            });

        } else {
            mCurCoverUrl = null;
            mBackgroundColor = mDefaultBgColor;
            updateBackground(mBackgroundColor);

            if (viewModel != null) {
                viewModel.updateDefaultTheme(PlayPageDataManager.DEFAULT_BACKGROUND_COLOR, PlayPageDataManager.DEFAULT_FOREGROUND_COLOR);
            } else {
                PlayPageDataManager.getInstance().setThemeColor(PlayPageDataManager.DEFAULT_FOREGROUND_COLOR, mBackgroundColor, mDefaultBgColor, mBackgroundColor);
            }
        }
    }

    private void observerTheme() {
        if (viewModel != null) {
            viewModel.getCurrentTheme().observe(this, new Observer<Theme>() {
                @Override
                public void onChanged(Theme theme) {
                    if (theme != null) {
                        mBackgroundColor = theme.getBackgroundColor();
                        updateBackground(mBackgroundColor);
                        if (mFloatingControlBarComponent != null) {
                            mFloatingControlBarComponent.setProgressColor(theme.getDomainColor());
                        }
                        if (mDefaultBgColor != mBackgroundColor) {
                            try {
                                if (PlayPageTabUtil.isPlayY(mSoundInfo) && !(viewModel.getNoTab() != null && viewModel.getNoTab().getValue() != null && viewModel.getNoTab().getValue())) {
                                    PlayPageDataManager.getInstance().setCommentHighlightColor(YDomainColorUtil.INSTANCE.fixAbsorbCommentHighlightL(theme.getOriginalColor()));
                                } else {
                                    PlayPageDataManager.getInstance().setCommentHighlightColor(YDomainColorUtil.INSTANCE.fixNormalCommentHighlightL(theme.getOriginalColor()));
                                }
                                PlayPageDataManager.getInstance().setCommentHighlightColorV2(YDomainColorUtil.INSTANCE.fixNormalCommentHighlightL(theme.getOriginalColor()), YDomainColorUtil.INSTANCE.fixAbsorbCommentHighlightL(theme.getOriginalColor()));

                            } catch (Exception e) {
                                Logger.e(e);
                            }
                        }
                    }
                }
            });

        }
    }

    private void gotoAudioPlayPageTop() {
        if (mTabAdapter == null || mViewPager == null) {
            return;
        }

        Fragment yPlayFragment = mTabAdapter.getFragment(YPlayFragment.class);
        if (yPlayFragment instanceof YPlayFragment) {
//            ((YPlayFragment) yPlayFragment).gotoTop(true, false);
            return;
        }


        boolean isNoRemoveAllChildFragment = false;
        if (getShowingTopChildFragment() != null && getShowingTopChildFragment().getClass() == XPlayAdFragmentForAnchor.class) {
            isNoRemoveAllChildFragment = true;
        }
        if (ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME,
                CConstants.Group_android.ITEM_REMOVE_ALL_PAGE_ON_TO_TOP_PAGE, true) && !isNoRemoveAllChildFragment) {
            removeAllChildFragments();
        }

        boolean tryToSmoothScroll = true;
        Fragment curTabFragment = mTabAdapter.getFragmentAtPosition(mViewPager.getCurrentItem());
        if (!isAudioPlayFragment(curTabFragment)) {
            // 需要切换tab页，那音频播放页就不要平滑滚到顶部了
            tryToSmoothScroll = false;
            mViewPager.setCurrentItem(mTabAdapter.getPosition(PlayPageTabUtil.getClassByType(PlayPageTab.TYPE_SOUND, mSoundInfo)));
        }

        Fragment audioPlayFragment = mTabAdapter.getFragment(AudioPlayFragment.class);
        if (audioPlayFragment instanceof AudioPlayFragment) {
            ((AudioPlayFragment) audioPlayFragment).gotoTop(tryToSmoothScroll);
        }
    }

    private boolean hasTabByType(@PlayPageTab.TabType int tabType) {
        if (tabType == PlayPageTab.TYPE_NONE || mTabAdapter == null || mViewPager == null) {
            return false;
        }

        Class<? extends Fragment> clazz = PlayPageTabUtil.getClassByType(tabType, mSoundInfo);
        if (clazz != null) {
            return mTabAdapter.getPosition(clazz) >= 0;
        }
        return false;
    }

    private boolean changeTabByType(@PlayPageTab.TabType int tabType) {
        boolean result = changeTabByTypeWithArgs(tabType, mArgumentsForVideo);
        mArgumentsForVideo = null;
        return result;
    }

    private boolean changeTabByTypeWithArgs(@PlayPageTab.TabType int tabType, Bundle args) {
        if (tabType == PlayPageTab.TYPE_NONE || mTabAdapter == null || mViewPager == null) {
            return false;
        }

        Class<? extends Fragment> clazz = PlayPageTabUtil.getClassByType(tabType, false, mSoundInfo);
        if (clazz != null) {
            int position = mTabAdapter.getPosition(clazz);
            // 如果是切到视频tab，要看是否有需要传给视频tab的参数。
            // 如果视频tab已存在，则直接拿到fragment设置参数，否则设置参数到FragmentHolder中
            if (tabType == PlayPageTab.TYPE_VIDEO && position >= 0 && args != null) {
                setVideoFragmentArguments(position, args);
            }
            if (position >= 0 && position < mTabAdapter.getCount()) {
                mViewPager.setCurrentItem(position);
                return true;
            }
        }
        return false;
    }

    private void setVideoFragmentArguments(int position, Bundle args) {
        Fragment videoFragment = mTabAdapter.getFragmentAtPosition(position);
        if (videoFragment != null) {
            if (videoFragment.getArguments() == null) {
                videoFragment.setArguments(args);
            } else {
                videoFragment.getArguments().putAll(args);
            }
        } else {
            TabCommonAdapter.FragmentHolder fragmentHolder = mTabAdapter.getFragmentHolderAtPosition(position);
            if (fragmentHolder != null) {
                if (fragmentHolder.args == null) {
                    fragmentHolder.args = args;
                } else {
                    fragmentHolder.args.putAll(args);
                }
            }
        }
    }

    private boolean isAudioPlayFragment(Fragment fragment) {
        return PlayPageTabUtil.isFragmentOfType(PlayPageTab.TYPE_SOUND, fragment, mSoundInfo);
    }

    public int getTabSize() {
        return mTabAdapter != null ? mTabAdapter.getCount() : (mTabList != null ? mTabList.size() : 0);
    }

    public boolean isCurrentTabOfType(@PlayPageTab.TabType int type) {
        Fragment fragment = getCurTabFragment();
        if (type == PlayPageTab.TYPE_VIDEO) {
            if (fragment instanceof VideoPlayTabFragment || fragment instanceof VideoPlayTabFragmentNew) {
                return true;
            }
        }
        Class<? extends Fragment> clazz = PlayPageTabUtil.getClassByType(type, mSoundInfo);
        return clazz != null && clazz.isInstance(fragment);
    }

    /**
     * 是否是新版Rn播放页
     * @param type
     * @return
     */
    public boolean isRnPlayPage(@PlayPageTab.TabType int type){
        Class<? extends Fragment> clazz = PlayPageTabUtil.getClassByType(type, mSoundInfo);
        return clazz != null && clazz == YPlayFragment.class;
    }

    private void onCurrentShowingFragmentChanged(Fragment curShowingFragment) {
        mVTopSlideView.setInnerScrollView(null);
        if (mCurShowingFragment instanceof BasePlayPageTabFragment) {
            ((BasePlayPageTabFragment) mCurShowingFragment).setScrollViewListener(null);
            ((BasePlayPageTabFragment) mCurShowingFragment).setCommentInputLayoutVisibilityChangeListener(null);
        }
        cancelShowFloatingControlBarUnfoldTask();

        Fragment lastShowingFragment = mCurShowingFragment;
        mCurShowingFragment = curShowingFragment;
        boolean shouldHideFloatingControlBar = true;
        if (curShowingFragment instanceof BasePlayPageTabFragment) {
            mVTopSlideView.setInnerScrollView(((BasePlayPageTabFragment) curShowingFragment).getVerticalScrollView());
            if (((BasePlayPageTabFragment) curShowingFragment).canShowFloatingControlBar()) {
                if (isAudioPlayFragment(curShowingFragment)) {
                    int currentScrollY = ((BasePlayPageTabFragment) curShowingFragment).getCurrentScrollY();
                    if (currentScrollY > BaseUtil.getScreenHeight(getContext())) {
                        showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum.UNFOLD);
                        shouldHideFloatingControlBar = false;
                    }
                } else {
                    showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum.UNFOLD);
                    shouldHideFloatingControlBar = false;
                }
                if (mFloatingControlBarComponent != null) {
                    mFloatingControlBarComponent.setMarginBottom(((BasePlayPageTabFragment) curShowingFragment)
                            .getBottomBarHeight() + mFloatingControlBarNormalMarginBottom);
                }
                ((BasePlayPageTabFragment) curShowingFragment).setScrollViewListener(mCurShowingFragmentScrollViewListener);
                ((BasePlayPageTabFragment) curShowingFragment)
                        .setCommentInputLayoutVisibilityChangeListener(mOnCommentInputLayoutVisibilityChangeListener);
            }
            StatusBarManager.setStatusBarColor(getWindow(), ((BasePlayPageTabFragment) curShowingFragment).darkStatusBar());
            enableOrDisableTopSlideExit(((BasePlayPageTabFragment) curShowingFragment).isEnableTopSlideExit());

        }
        if (shouldHideFloatingControlBar) {
            hideFloatingControlBarComponent();
        }

        // 从视频页切到音频页，要开始播放音频。
        // 这里可能有两种情况：一是切换tab；而是切换声音后，新的声音没有视频而被切回到声音页。不过切换声音本来就是要播放，应该没有问题
        if (PlayPageTabUtil.isFragmentOfType(PlayPageTab.TYPE_VIDEO, lastShowingFragment, mSoundInfo)
                && isAudioPlayFragment(mCurShowingFragment) && !XmPlayerManager.getInstance(getContext()).isPlaying()) {
            XmPlayerManager.getInstance(getContext()).play();
        }
        // 声音页和 视频页 才会展示底部落地页bar， 评论页隐藏入口
        updatePlayAdWebBarView();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_play_new;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected boolean isPageBgDark() {
        return true;
    }

    @Override
    protected boolean darkStatusBar() {
        if (mCurShowingFragment instanceof BasePlayPageTabFragment) {
            return ((BasePlayPageTabFragment) mCurShowingFragment).darkStatusBar();
        }
        return false;
    }

    @Override
    protected boolean isFragmentCanPlay() {
        return true;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mDialogShowChangeListener != null) {
            PlayPageDialogManager.INSTANCE.removeXPlayPageDialogShowChangeListener(mDialogShowChangeListener);
        }
        AlbumEventManage.removeListener(collectListener);
        if (mBroadCastComponent != null) {
            mBroadCastComponent.unregisterReceiver(getContext());
        }
        PlayPageDataManager.getInstance().release();
        PlayPageMinorDataManager.getInstance().release();
        PlayPageInternalServiceManager.getInstance().release();
        PlayRNShareManager.INSTANCE.release();
        AdPlayNativeWebManager.getInstance().releasePage();
        if (mLiveAndMcEntryComponent != null) {
            mLiveAndMcEntryComponent.release();
        }
        if (mShareVipEntryComponent != null) {
            mShareVipEntryComponent.release();
        }
        PlayPageLoadingOptimizationManager.INSTANCE.onPlayPageDestroy();
        AnchorFollowManage.getSingleton().removeFollowListener(followAnchorListener);
        XmPlayerManager.getInstance(getContext()).removePlayerStatusListener(mPlayerStatusListener);
        XmPlayerManager.getInstance(getContext()).removePlayerStatusListener(mPlayerStatusListenerForTrace);
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(loginStatusChangeListener);
        PlayPageDialogManager.INSTANCE.release();
        removeSOundPatchFinishListener();
        PlayWidgetGuideManager.getInstance().release();
        unregisterFreeListenTimeListener();
        unregisterStopPlayDueToFreeListenPermissionListener();
        AppCommentManager.INSTANCE.release();
        XPlayPageTingReaderManager.getInstance().release();
    }

    private void handBackBtnClick() {
        if (!handleBackEvent()) {
            finishFragment();
        }
        // 新声音播放页-展开/收起（顶部）  点击事件
        new XMTraceApi.Trace()
                .click(45714)
                .put("currTrackId", String.valueOf(PlayPageDataManager.getInstance().getCurTrackId()))
                .put("status", CoverComponentsManager.isCoverFullScreen() ? "全屏" : "半屏")
                .put("Item", "收起")
                .put("currPage", "newPlay")
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(
                                XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace();
    }

    private void traceBackBtnViewed() {
        long trackId = PlayPageDataManager.getInstance().getCurTrackId();
        if (trackId > 0 && trackId != mNoNeedStatBackBtnViewedTrackId) {
            // 新声音播放页-展开/收起（顶部）  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(45715)
                    .setServiceId("slipPage")
                    .put("currTrackId", String.valueOf(trackId))
                    .put("status", CoverComponentsManager.isCoverFullScreen() ? "全屏" : "半屏")
                    .put("Item", "收起")
                    .put("currPage", "newPlay")
                    .put(XmRequestIdManager.CONT_ID, String.valueOf(trackId))
                    .put(XmRequestIdManager.CONT_TYPE, "newPlayExpand")
                    .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(
                            XmRequestPage.PAGE_PLAY_PAGE))
                    .createTrace();
            // 避免重复上报
            mNoNeedStatBackBtnViewedTrackId = trackId;
        }
    }

    private void requestGetQuickData(@Nullable PlayPageTabAndSoundInfo data) {
        if (data == null || data.getSoundInfo() == null) {
            return;
        }
        PlayingSoundInfo playingSoundInfo = data.getSoundInfo();
        if (PlayCommentUtil.getCurAlbumId(playingSoundInfo) !=
                PlayCommentUtil.getCurAlbumId(PlayPageDataSuppleManager.getInstance().getLastSoundInfo())) {
            // 请求拿到快速评论模板
            QuickCommentViewManager.Companion.requestQuickComment(PlayCommentUtil.getCurAlbumId(playingSoundInfo),
                    PlayCommentUtil.getCurTrackId(playingSoundInfo));
            // 请求拿到快速弹幕模板
            QuickBulletViewManager.Companion.requestQuickComment(PlayCommentUtil.getCurAlbumId(playingSoundInfo));
        }
    }

    private long track5100Id = 0L;
    private void handleOnSoundSwitch(PlayableModel curModel) {
        if (curModel == null || !PlayableModel.KIND_TRACK.equals(curModel.getKind())) {
            return;
        }
        if (curModel instanceof Track && curModel.getDataId() != PlayPageDataManager.getInstance().getCurTrackId()) {
            if (viewModel != null) {
                viewModel.clearPlayFragmentArgs();
            }
            parseTabInfoOnSoundSwitch();
            onGotNewTrackId(curModel.getDataId());
            PushArrivedTraceManager.INSTANCE.getInstance().showPageByManual("PlayFragmentNew",
                    "track_id", curModel.getDataId() + "", 1);
            Logger.d(TAG, "handleOnSoundSwitch");
            loadDataFromNet(false);
            // 上下首切换时增加时长模式弹窗提示
            Track track = (Track) curModel;
            if (FreeListenConfigManager.isCurrentTrackNeedListenTime() && FreeListenTimeManager.getListenTime(getContext()) == 0) {
                ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.PLAY_PAGE_DIALOG, track, PlayFragmentNew.this, 2);
            }
        }
        if (track5100Id != curModel.getDataId()) {
            track5100Id = curModel.getDataId();
            Logger.d(TAG, "handleOnSoundSwitch, traceForegroundDuration ");
            traceForegroundDuration();
        }
//        Bundle argsContainsVideo = getArgumentsContainsVideoArguments();
//        /**
//         * 如果 arguments 里有处理视频相关的信息， 并且 需要打开原生落地页， 就处理一下
//         */
//        if (argsContainsVideo != null
//                && TextUtils.equals(argsContainsVideo.getString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_AD_TRACK_ID), String.valueOf(curModel.getDataId()))
//                && argsContainsVideo.getBoolean(VideoPlayParamsBuildUtil.KEY_IS_OPEN_NATIVE_WEBVIEW, false)) {
//            // 从声音页，点击原生落地页广告时，会触发此方法，处理一下页面内的跳转
//            parseAndHandleArgumentsForVideo();
//            changeTabByType(mFocusTabType);
//            mFocusTabType = PlayPageTab.TYPE_NONE;
//        }

        updatePlayAdWebBarView();


        if (lastVisibleOnlyToFriendDialog != null && lastVisibleOnlyToFriendDialog.isVisible()
                && curModel.getDataId() == lastVisibleOnlyToFriendDialog.getCurrentSoundId()) {
            return;
        }

        if (lastVisibleOnlyToFriendDialog != null) {
            lastVisibleOnlyToFriendDialog.dismiss();
            lastVisibleOnlyToFriendDialog = null;
            if (ConstantsOpenSdk.isDebug) {
                Logger.d(TAG, " lastVisibleOnlyToFriendDialog trace=" + Log.getStackTraceString(new Throwable()));
            }
        }
    }

    private void onGotNewTrackId(long trackId) {
        PlayPageDataManager.getInstance().setCurTrackId(trackId, isRecommendSound(trackId));
        // trackId存到Arguments里，目前主要是方便可视化埋点取，加在这里不影响页面绑定的数据，之前的可视化埋点都不用重新设置
        if (getArguments() != null) {
            getArguments().putLong(BundleKeyConstantsInMain.KEY_TRACK_ID, trackId);
        } else {
            Bundle arguments = new Bundle();
            arguments.putLong(BundleKeyConstantsInMain.KEY_TRACK_ID, trackId);
            setArguments(arguments);
        }
        AutoTraceHelper.bindPageBundleData(this);
    }

    private long getPlayingTrackId() {
        Track track = getPlayingTrack();
        if (track != null) {
            if (PlayableModel.KIND_TRACK.equals(track.getKind())) {
                Logger.d(TAG, "getPlayingTrackId from player");
                return track.getDataId();
            } else {
                // 当前播放的不是声音类型，先返回之前的声音id
                return PlayPageDataManager.getInstance().getCurTrackId();
            }
        }
        return 0;
    }

    private boolean isRecommendSound(long trackId) {
        Track track = getPlayingTrack();
        if (track != null && track.getDataId() == trackId && PlayableModel.KIND_TRACK.equals(track.getKind())) {
            return track.isRecommendSound();
        }
        return false;
    }

    private Track getPlayingTrack() {
        PlayableModel playableModel =
                XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
        if (playableModel instanceof Track) {
            return (Track) playableModel;
        }
        return null;
    }

    private void addPlayStatusListener(IXmPlayerStatusListener listener) {
        mPlayerStatusListener.addXmPlayStatusListener(listener);
    }

    private void cancelShowFloatingControlBarUnfoldTask() {
        removeCallbacks(mShowFloatingControlBarUnfoldTask);
    }

    private void hideFloatingControlBarComponent() {
        hideFloatingControlBarComponent(false);
    }

    private void hideFloatingControlBarComponent(boolean forStash) {
        if (mFloatingControlBarComponent != null) {
            if (!forStash) {
                mFloatingControlBarShowType = null;
            }
            mFloatingControlBarComponent.hide();
        }
    }

    private void showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum type) {
        if (mFloatingControlBarComponent == null) {
            initFloatingControlComponent();
        }
        if (mFloatingControlBarComponent != null) {
            mFloatingControlBarShowType = type;
            if (!mFloatingControlBarInStash) {
                mFloatingControlBarComponent.setProgressColor(PlayPageDataManager.getInstance().getForegroundColor());
                mFloatingControlBarComponent.show(type);
            }
        }
    }

    private void stashFloatingControlBar() {
        mFloatingControlBarInStash = true;
        hideFloatingControlBarComponent(true);
    }

    private void unStashFloatingControlBar() {
        mFloatingControlBarInStash = false;
        if (mFloatingControlBarShowType != null) {
            showFloatingControlBarComponent(mFloatingControlBarShowType);
        }
    }

    private void checkChildProtectMode() {
        boolean mode = ChildProtectManager.isChildProtectOpen(mContext);
        if (mIsChildProtectMode == mode) {
            return;
        }
        mIsChildProtectMode = mode;

        updateTab(mTabList);
        updateShareComponentVisibility();
        updateVgTitleBarClickState();
    }

    private void updateShareComponentVisibility() {
        if (mShareComponent != null) {
            if (mIsChildProtectMode) {
                mShareComponent.hide();
            }
            if (mSoundInfo != null && mSoundInfo.trackInfo != null && mSoundInfo.trackInfo.type == Track.TYPE_TRAINING_CAMP) {
                // 训练营先不显示分享按钮
                mShareComponent.hide();
            } else {
                Fragment fragment = getCurTabFragment();
                if (fragment instanceof BasePlayPageTabFragment) {
                    if (((BasePlayPageTabFragment) fragment).isShowShareComponent()) {
                        mShareComponent.show();
                    } else {
                        mShareComponent.hide();
                    }
                }
            }
        }
    }

    private boolean shouldShowShareTipsComponent() {
        if (shareTipsComponent == null) {
            return false;
        }
        if (mIsChildProtectMode) {
            return false;
        }
        if (mSoundInfo != null && mSoundInfo.trackInfo != null && mSoundInfo.trackInfo.type == Track.TYPE_TRAINING_CAMP) {
            return false;
        } else {
            Fragment fragment = getCurTabFragment();
            if (fragment instanceof BasePlayPageTabFragment) {
                if (!((BasePlayPageTabFragment) fragment).isShowShareComponent()) {
                    return false;
                }
            }
        }
        return true;
    }

    private void updateVgTitleBarClickState() {
        Fragment fragment = getCurTabFragment();
        if (fragment instanceof BasePlayPageTabFragment) {
            mVgTitleBar.setClickable(((BasePlayPageTabFragment) fragment).isVgTitleBarNeedToInterceptClick());
        }
    }

    /**
     * 在返回事件触发时，先看看页面内有没有需要处理返回事件的地方
     *
     * @return 消费了返回事件就返回true，否则返回false
     */
    private boolean handleBackEvent() {
        PlayPageControlManager.INSTANCE.dispatchPlayPageBack();
        if (mCurShowingFragment instanceof AudioPlayFragment) {
            unSelectLrcView();
        }
        if (mCurShowingFragment instanceof BasePlayPageTabFragment
                && ((BasePlayPageTabFragment) mCurShowingFragment).onBackEvent()) {
            return true;
        }
        Fragment currTopChildFragment = getShowingTopChildFragment();
        if (currTopChildFragment != null) {
            if (!(currTopChildFragment instanceof BaseFragment2) || !((BaseFragment2) currTopChildFragment).onBackPressed()) {
                removeChildFragment(currTopChildFragment);
            }
            return true;
        }
        IReadBookInterceptBackService service =
                PlayPageInternalServiceManager.getInstance().getService(IReadBookInterceptBackService.class);
        if (service != null && service.onBackPressed()) {
            return true;
        }
        return getActivity() instanceof MainActivity
                && BackUserPullUpManager.handleBackUserPullUp((MainActivity) getActivity());
    }

    @Override
    public boolean onBackPressed() {
        return handleBackEvent();
    }

    private void removeAllChildFragments() {
        if (!isAdded()) {
            return;
        }
        List<Fragment> fragmentsToRemove = new ArrayList<>();
        for (Fragment fragment : getChildFragmentManager().getFragments()) {
            if (fragment.getId() == FRAGMENT_CONTAINER_ID) {
                fragmentsToRemove.add(fragment);
            }
        }
        FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
        for (Fragment fragment : fragmentsToRemove) {
            fragmentTransaction.remove(fragment);
        }
        fragmentTransaction.commitAllowingStateLoss();
        onCurrentShowingFragmentChanged(getCurTabFragment());
    }

    private void removeChildFragment(Fragment fragment) {
        if (!isAdded() || fragment == null || fragment.getId() != FRAGMENT_CONTAINER_ID) {
            return;
        }
        List<Fragment> childFragments = getChildFragmentManager().getFragments();
        if (childFragments.contains(fragment)) {
            boolean isTopFragment = !fragment.isHidden() && childFragments.get(childFragments.size() - 1) == fragment;
            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            int outAnim = fragment.getArguments() != null ? fragment.getArguments().getInt(BundleKeyConstants.KEY_FRAGMENT_OUT_ANIM, 0) : 0;
            if (outAnim != 0) {
                fragmentTransaction.setCustomAnimations(0, outAnim);
            }
            fragmentTransaction.remove(fragment).commitAllowingStateLoss();
            if (isTopFragment) {
                childFragments.remove(fragment);
                Fragment currTopChildFragment = getShowingTopChildFragment(childFragments);
                if (currTopChildFragment != null) {
                    onCurrentShowingFragmentChanged(currTopChildFragment);
                } else {
                    onCurrentShowingFragmentChanged(getCurTabFragment());
                }
            }
        }
    }

    @Nullable
    private Fragment getChildFragment(Function1<Fragment, Boolean> condition) {
        if (isAdded() && condition != null) {
            for (Fragment childFra : getChildFragmentManager().getFragments()) {
                if (condition.invoke(childFra)) {
                    return childFra;
                }
            }
        }
        return null;
    }

    @Nullable
    private Fragment getShowingTopChildFragment() {
        if (isAdded()) {
            List<Fragment> childFragments = getChildFragmentManager().getFragments();
            for (int i = childFragments.size() - 1; i >= 0; i--) {
                Fragment childFra = childFragments.get(i);
                if (childFra != null && childFra.getId() == FRAGMENT_CONTAINER_ID && !childFra.isHidden()) {
                    return childFra;
                }
            }
        }
        return null;
    }

    @Nullable
    private Fragment getShowingTopChildFragment(List<Fragment> childFragments) {
        if (childFragments == null) {
            return null;
        }
        for (int i = childFragments.size() - 1; i >= 0; i--) {
            Fragment childFra = childFragments.get(i);
            if (childFra != null && childFra.getId() == FRAGMENT_CONTAINER_ID && !childFra.isHidden()) {
                return childFra;
            }
        }
        return null;
    }

    @Nullable
    private Fragment getCurTabFragment() {
        return getTabFragment(mViewPager.getCurrentItem());
    }

    private Fragment getTabFragment(int position) {
        if (mTabAdapter != null && mViewPager != null) {
            return mTabAdapter.getFragmentAtPosition(position);
        }
        return null;
    }

    private void checkAndShowFirstPageScrollTip() {
        if (!canUpdateUi()) {
            return;
        }

        if (mViewPager != null && mViewPager.getCurrentItem() != 0) {
            return;
        }
        boolean show =
                MmkvCommonUtil.getInstance(mContext).getBoolean(BundleKeyConstantsInMain.KEY_PLAY_FRAG_PAGE_FIRST_SHOW, true);
        if (!show) {
            DriveModeBluetoothManager.getInstance().scanBlueToothInPlayPage();
            return;
        }
        MmkvCommonUtil.getInstance(mContext).saveBoolean(BundleKeyConstantsInMain.KEY_PLAY_FRAG_PAGE_FIRST_SHOW, false);
        postOnUiThreadDelayed(() -> {
            try {
                int distance = BaseUtil.getScreenWidth(mContext) / 3;
                MethodUtil.invokeDeclaredMethod(mViewPager, "smoothScrollTo",
                        new Object[]{distance, 0, 600},
                        new Class[]{int.class, int.class, int.class});
                // 滑出来之后停顿一会儿，加强引导效果
                postOnUiThreadDelayed(() -> {
                    try {
                        MethodUtil.invokeDeclaredMethod(mViewPager, "smoothScrollTo",
                                new Object[]{0, 0, 600},
                                new Class[]{int.class, int.class, int.class});
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    DriveModeBluetoothManager.getInstance().scanBlueToothInPlayPage();
                }, 1100);
            } catch (Exception e) {
                e.printStackTrace();
                DriveModeBluetoothManager.getInstance().scanBlueToothInPlayPage();
            }
        }, 50);
    }

    private void checkFreeFlow() {
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService != null) {
            boolean isNoFreeService = !freeFlowService.hasFlowNecessity()
                    || !NetworkType.isConnectMOBILE(mContext)
                    || (XmPlayerManager.getInstance(mContext).getCurrSound() != null
                    && !XmPlayerManager.getInstance(mContext).isOnlineSource());
            if (!isNoFreeService) {
                if (freeFlowService
                        .isUsingFreeFlow() && (NetworkUtils.getPlayType(XmPlayerManager.getInstance(mContext).getCurrSound()) != DownloadTools.TAIHE_SOUND_TYPE)) {
                    CustomToast.showToast("免流量播放");
                }
            }
        }
    }

    private void updateTitleBarColor(int color, int tabIndicatorColor) {
        mBackColor = color;
        if (mTab != null) {
            mTab.setActivateTextColor(color);
            if (color == Color.BLACK) {
                mTab.setIndicatorGradientColors(new int[]{0xFFFF4646, 0xFFFFA390});
            } else {
                mTab.setIndicatorGradientColors(new int[]{tabIndicatorColor, tabIndicatorColor});
            }
            mTab.setDeactivateTextColor(ColorUtil.changeColorAlpha(color, 128));
        }
        setBackColor();
        if (mShareComponent != null) {
            mShareComponent.setShareIconColor(color);
        }
    }

    private boolean mEnablePlayPageScrolling = true;
    private boolean mInterceptPlayPageScrolling = false;

    private void enableOrDisableTopSlideExit(boolean enable) {
        // 记录当前状态
        mEnablePlayPageScrolling = enable;
        // 直接拦截了  不让修改  不然边听边看拦截了  又会被别的动态修改了
        if (mInterceptPlayPageScrolling) {
            return;
        }
        if (mVTopSlideView != null) {
            mVTopSlideView.setEnablePlayPageScrolling(enable);
        }
    }

    public void interceptTopSlideExit(boolean intercept) {
        mInterceptPlayPageScrolling = intercept;
        if (mVTopSlideView != null) {
            if (intercept) {
                mVTopSlideView.setEnablePlayPageScrolling(false);
            } else {
                // 恢复状态
                mVTopSlideView.setEnablePlayPageScrolling(mEnablePlayPageScrolling);
            }
        }
    }

    private void chencAndSetArgumentsForSelectedVideoTab(int position) {
        Fragment fragment = mTabAdapter.getFragmentAtPosition(position);
        // 如果切换到视频tab页面，给视频tab设置bundle
        if (PlayPageTabUtil.isFragmentOfType(PlayPageTab.TYPE_VIDEO, fragment, mSoundInfo) && mArgumentsForVideo != null) {
            setVideoFragmentArguments(position, mArgumentsForVideo);
        }
    }

    /**
     * 更新播放页引导bar 状态
     */
    private void updatePlayAdWebBarView() {

        Bundle playArgument = getPlayAdWebArguments();

        if (playArgument != null) {
            long adTrackId = playArgument.getLong(AdPlayNativeWebManager.KEY_NATIVE_PLAY_TRACK_ID, -1);
            Advertis advertis = getAdvertisFromArgument(playArgument);
            Logger.v("--------msg", " ------ updatePlayAdWebBarView --- " + advertis.getAutoJumpTime());
            AdPlayNativeWebManager.getInstance().createNativePageWithAdModel(adTrackId, advertis);
            playArgument.remove(AdPlayNativeWebManager.KEY_NATIVE_PLAY_TRACK_ID);
            playArgument.remove(AdPlayNativeWebManager.KEY_NATIVE_PLAY_ADVERTIS);
        }

        // 声音页和 视频页 才会展示底部落地页bar， 评论页隐藏入口
        if (mFocusTabType == PlayPageTab.TYPE_SOUND || mFocusTabType == PlayPageTab.TYPE_VIDEO
                || isAudioPlayFragment(mCurShowingFragment) || PlayPageTabUtil.isFragmentOfType(PlayPageTab.TYPE_VIDEO, mCurShowingFragment, mSoundInfo)) {
            AdPlayNativeWebManager.getInstance().updateNativePageShowStatus(getPlayingTrackId());
        } else {
            AdPlayNativeWebManager.getInstance().updateNativePageShowStatus(-1);
        }
    }

    private Bundle getPlayAdWebArguments() {

        if (getArguments() != null
                && (getArguments().containsKey(AdPlayNativeWebManager.KEY_NATIVE_PLAY_TRACK_ID)
                || getArguments().containsKey(AdPlayNativeWebManager.KEY_NATIVE_PLAY_ADVERTIS))) {
            return getArguments();
        }
        if (getArguments2() != null
                && (getArguments2().containsKey(AdPlayNativeWebManager.KEY_NATIVE_PLAY_TRACK_ID)
                || getArguments2().containsKey(AdPlayNativeWebManager.KEY_NATIVE_PLAY_ADVERTIS))) {
            return getArguments2();
        }
        return null;
    }


    private Advertis getAdvertisFromArgument(Bundle playArgument) {
        if (playArgument == null) {
            return null;
        }
        Advertis advertis = null;

        try {
            Parcelable parcelable = playArgument.getParcelable(AdPlayNativeWebManager.KEY_NATIVE_PLAY_ADVERTIS);
            if (parcelable != null) {
                if (parcelable instanceof AdSDKAdapterModel) {
                    AdSDKAdapterModel adapterModel = (AdSDKAdapterModel) parcelable;
                    advertis = AdConversionUtil.translateSDKModelToAdvertis(adapterModel);
                } else {
                    advertis = (Advertis) parcelable;
                }
            }
            return advertis;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void unSelectLrcView() {
        IDocOnCoverComponentService docService =
                PlayPageInternalServiceManager.getInstance().getService(IDocOnCoverComponentService.class);
        if (docService != null) {
            docService.unSelectLrcView();
        }
    }

    private boolean needHideTopRightEntry(Fragment fragment) {
        if (fragment instanceof BasePlayPageTabFragment) {
            return ((BasePlayPageTabFragment) fragment).needHideTopRightEntry();
        }
        return true;
    }

    private void tryShowOpenAiDocGuideDialog() {
        if (mSoundInfo == null || mSoundInfo.albumInfo == null || mSoundInfo.albumInfo.albumId <= 0
                || mSoundInfo.userInfo == null  || mSoundInfo.userInfo.uid <= 0 || mSoundInfo.trackInfo == null) {
            return;
        }
        long uid = UserInfoMannage.getUid();
        if (uid <= 0) {
            return;
        }
        if (uid != mSoundInfo.userInfo.uid) {
            return;
        }
        if (ViewUtil.haveDialogIsShowing((FragmentActivity) mActivity)) {
            return;
        }
        long albumId = mSoundInfo.albumInfo.albumId;
        Runnable action = () -> {
            if (canUpdateUi()) {
                String key = PreferenceConstantsInMain.KEY_ANCHOR_HAS_ENTER_SELF_ALBUM_PLAY_PAGE_PREFIX + albumId;
                boolean hasEnterBefore = MmkvCommonUtil.getInstance(getContext()).getBoolean(key, false);
                if (!hasEnterBefore) {
                    MmkvCommonUtil.getInstance(getContext()).saveBoolean(key, true);
                    CommonRequestM.requestAlbumAiPermission(albumId, new IDataCallBack<AiAlbumPermissionInfo>() {
                        @Override
                        public void onSuccess(@Nullable AiAlbumPermissionInfo data) {
                            if (canUpdateUi() && data != null && data.isHasAiDocPermission() && !data.isExistAiDoc()) {
                                List<Integer> unSupportCategoryList = data.getUnSupportCategoryList();
                                if (unSupportCategoryList == null ||
                                        !unSupportCategoryList.contains(mSoundInfo.trackInfo.categoryId)) {
                                    showOpenAiDocGuideDialog(albumId);
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                        }
                    });
                }
            }
        };
        if (PlayPageLoadingOptimizationManager.INSTANCE.isPlayPageLoading()) {
            PlayPageLoadingOptimizationManager.INSTANCE.addPlayPageLoadingListenersH(action::run);
        } else {
            action.run();
        }
    }

    private void showOpenAiDocGuideDialog(long albumId) {
        OpenAIDocGuideDialogFragment.newInstance(albumId, PlayPageDataManager.getInstance().getCurTrackId()).
                show(getChildFragmentManager(), null);
    }

    public void showChooseSoundEffectDialog() {
        if (canUpdateUi()) {
            YPlayFragment.showSoundEffectDialog(false, ChooseTrackSoundEffectAiDialogXNew.TAB_QUALITY, mSoundInfo);
//            ChooseTrackSoundEffectDialog dialog = ChooseTrackSoundEffectDialog.newInstance(this, mSoundInfo, null, null);
//            if (null != dialog) {
//                dialog.show();
            TrackPlaySoundEffectManager.getInstance().markShowRedDotDone(TrackPlaySoundEffectManager.PLACE_PLAY_PAGE_ENTRY);
            TrackPlaySoundEffectManager.getInstance().markShowNewIconDone(TrackPlaySoundEffectManager.PLACE_PLAY_PAGE_ENTRY);
//            }
        }
    }

    private void doStartFragmentOnPlayPage(Fragment fragment) {
        if (isAdded() && fragment != null) {
            XPlayPage playPage = XPlayPageRef.get();
            if (playPage != null) playPage.dismissDialog();
            enableOrDisableTopSlideExit(false);

            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            for (Fragment childFra : getChildFragmentManager().getFragments()) { // 由于此处getFragments会复制一份List出来，所以不用担心移除过程中影响到正在迭代的List
                if (childFra.isHidden() && childFra.getId() == FRAGMENT_CONTAINER_ID) {
                    fragmentTransaction.remove(childFra);
                }
            }
            int inAnim = fragment.getArguments() != null ? fragment.getArguments().getInt(BundleKeyConstants.KEY_FRAGMENT_IN_ANIM, 0) : 0;
            if (inAnim != 0) {
                fragmentTransaction.setCustomAnimations(inAnim, 0);
            }
            fragmentTransaction.add(FRAGMENT_CONTAINER_ID, fragment);
            fragmentTransaction.commitAllowingStateLoss();
            onCurrentShowingFragmentChanged(fragment);
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            XUtils.exitLandScapeIfLandScape(getActivity());
        }
    }

    private void doShowFragmentOnPlayPage(Fragment fragment) {
        doShowFragmentOnPlayPage(fragment, false);
    }

    private void doShowFragmentOnPlayPage(Fragment fragment, boolean noShowRepeat) {
        if (!isAdded() || fragment == null) {
            return;
        }

        XPlayPage playPage = XPlayPageRef.get();
        if (playPage != null) playPage.dismissDialog();
        enableOrDisableTopSlideExit(false);

        XUtils.exitLandScapeIfLandScape(getActivity());
        FragmentManager childFragmentManager = getChildFragmentManager();
        List<Fragment> childFragments = childFragmentManager.getFragments();

        if (childFragments.contains(fragment)) {
            if (fragment.isHidden()) {
                FragmentTransaction fragmentTransaction = childFragmentManager.beginTransaction();
                int inAnim = fragment.getArguments() != null ? fragment.getArguments().getInt(BundleKeyConstants.KEY_FRAGMENT_IN_ANIM, 0) : 0;
                if (inAnim != 0) {
                    fragmentTransaction.setCustomAnimations(inAnim, 0);
                }
                fragmentTransaction.show(fragment).commitAllowingStateLoss();
                if (childFragments.get(childFragments.size() - 1) == fragment) {
                    onCurrentShowingFragmentChanged(fragment);
                }
            }
        } else {
            if (noShowRepeat && getShowingTopChildFragment() != null && getShowingTopChildFragment().getClass() == fragment.getClass()) {
                // fragment已经在顶部时不重复添加
                return;
            }
            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            for (Fragment childFra : getChildFragmentManager().getFragments()) { // 由于此处getFragments会复制一份List出来，所以不用担心移除过程中影响到正在迭代的List
                if (childFra.isHidden() && childFra.getId() == FRAGMENT_CONTAINER_ID) {
                    fragmentTransaction.remove(childFra);
                }
            }
            int inAnim = fragment.getArguments() != null ? fragment.getArguments().getInt(BundleKeyConstants.KEY_FRAGMENT_IN_ANIM, 0) : 0;
            if (inAnim != 0) {
                fragmentTransaction.setCustomAnimations(inAnim, 0);
            }
            fragmentTransaction.add(FRAGMENT_CONTAINER_ID, fragment);
            fragmentTransaction.commitAllowingStateLoss();
            onCurrentShowingFragmentChanged(fragment);
        }
    }

    private void doHideFragmentOnPlayPage(Fragment fragment) {
        if (!isAdded() || fragment == null) {
            return;
        }
        List<Fragment> childFragments = getChildFragmentManager().getFragments();
        if (childFragments.contains(fragment) && !fragment.isHidden()) {
            boolean isTopFragment = childFragments.get(childFragments.size() - 1) == fragment;
            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            int outAnim = fragment.getArguments() != null ? fragment.getArguments().getInt(BundleKeyConstants.KEY_FRAGMENT_OUT_ANIM, 0) : 0;
            if (outAnim != 0) {
                fragmentTransaction.setCustomAnimations(0, outAnim);
            }
            fragmentTransaction.hide(fragment).commitAllowingStateLoss();
            onCurrentShowingFragmentChanged(getCurTabFragment());
            if (isTopFragment) {
                childFragments.remove(fragment);
                Fragment currTopChildFragment = getShowingTopChildFragment(childFragments);
                if (currTopChildFragment != null) {
                    onCurrentShowingFragmentChanged(currTopChildFragment);
                } else {
                    onCurrentShowingFragmentChanged(getCurTabFragment());
                }
            }
        }
    }

    private void turnOffAdditionalSkinComponent() {
        IAdditionalSkinAdService service = PlayPageInternalServiceManager.getInstance().getService(IAdditionalSkinAdService.class);
        if (service != null) {
            service.onDataCallBack(null, isResumed());
        }
    }

    private final View.OnClickListener mOnClickListener = v -> {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (v == mIvBack) {
            handBackBtnClick();
        }
    };

    private class PageChangeListener implements ViewPager.OnPageChangeListener {
    // private final ViewPager.OnPageChangeListener mOnPageChangeListener = new ViewPager.OnPageChangeListener() {

        private boolean mFirstInit = true;
        int currentPosition = 0;

        public void resetInit() {
            mFirstInit = true;
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            // 第一次不会调onPageSelected，所以主动调一下
            if (mFirstInit && position == 0 && positionOffset == 0 && positionOffsetPixels == 0) {
                mFirstInit = false;
                handleOnPageSelected(0, false);
            }

            Fragment leftFragment = getTabFragment(position);
            Fragment rightFragment = getTabFragment(position + 1);
            if (leftFragment instanceof BasePlayPageTabFragment && rightFragment instanceof BasePlayPageTabFragment) {
                int startColor = ((BasePlayPageTabFragment) leftFragment).getPlayPageTitleBarColor();
                int targetColor = ((BasePlayPageTabFragment) rightFragment).getPlayPageTitleBarColor();
                int titleColor = (int) mArgbEvaluator.evaluate(positionOffset, startColor, targetColor);


                int startTabIndicatorColor = ((BasePlayPageTabFragment) leftFragment).getPlayPageTabIndicatorColor();
                int targetTabIndicatorColor = ((BasePlayPageTabFragment) rightFragment).getPlayPageTabIndicatorColor();
                int tabIndicatorColor = (int) mArgbEvaluator.evaluate(positionOffset, startTabIndicatorColor, targetTabIndicatorColor);
                updateTitleBarColor(titleColor, tabIndicatorColor);

                // 让tab页根据滚动位置做些处理，比如背景渐变等
                ((BasePlayPageTabFragment) leftFragment).onPageScrolled(1 - positionOffset,
                        ((BasePlayPageTabFragment) rightFragment).isPageBgDark());
                ((BasePlayPageTabFragment) rightFragment).onPageScrolled(positionOffset,
                        ((BasePlayPageTabFragment) leftFragment).isPageBgDark());

            }
        }

        @SuppressLint("WrongConstant")
        @Override
        public void onPageSelected(int position) {
            mFirstInit = false;
            handleOnPageSelected(position, true);
            if (isInTabAbGroupOnOldPlayPage()){
                //在AB实验组的专辑，需要记录当前
                if (mTabList!=null &&  mTabList.get(position) != null){
                    mFocusTabType = mTabList.get(position).getType();
                }
            }
        }

        private void handleOnPageSelected(int position, boolean callBySystem) {
            if (position > 0) {
                mViewPager.setOffscreenPageLimit(3);
            }
            if (mTabAdapter == null) {
                return;
            }
            Fragment fragment = mTabAdapter.getFragmentAtPosition(position);
            chencAndSetArgumentsForSelectedVideoTab(position);
            onCurrentShowingFragmentChanged(fragment);
            if (needHideTopRightEntry(fragment)) {
                if (mLiveAndMcEntryComponent != null) {
                    mLiveAndMcEntryComponent.setHideByTagChange();
                }
                // 隐藏右上角的导流入口
                hideTopRightEntry();
                unSelectLrcView();
            } else if (fragment instanceof AudioPlayFragment) {
                if (mLiveAndMcEntryComponent != null) {
                    mLiveAndMcEntryComponent.resumeShowIfNeeded();
                }
            }
            updateShareComponentVisibility();
            updateVgTitleBarClickState();
            if (callBySystem) {
                // 新声音播放页-滑动切换  滑动切换
                String direction = currentPosition < position? "左" : "右";
                String pageTitle = "";
                if(mFragmentHolderList != null && mFragmentHolderList.size() > position) {
                    pageTitle = mFragmentHolderList.get(position).title;
                }
                // 新声音播放页-滑动切换  滑动事件
                new XMTraceApi.Trace()
                    .setMetaId(41113)
                    .setServiceId("slip") // 滑动切换至另一页面时上报
                    .put("direction", direction)
                    .put("pageTitle", pageTitle) // 声音｜评论｜视频｜文稿......
                    .createTrace();
            }
            currentPosition = position;

            if (ConfigureCenter.getInstance().getBool(
                    CConstants.Group_ad.GROUP_NAME,  "pursue_skin_ad2", true)) {
                //沉浸式皮肤广告代码开关
                if (!(fragment instanceof AudioPlayFragment)) {
                    //切换走tab，隐藏追投广告
                    turnOffAdditionalSkinComponent();
                }
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {
            if (state == ViewPager.SCROLL_STATE_IDLE) {
                Fragment curFragment = getCurTabFragment();
                if (curFragment instanceof BasePlayPageTabFragment) {
                    ((BasePlayPageTabFragment) curFragment).onPageScrolled(1, true);
                }
            }
        }
    };

    private final PlayStatusListenerDispatcher mPlayerStatusListener = new PlayStatusListenerDispatcher() {
        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            handleOnSoundSwitch(curModel);
            super.onSoundSwitch(lastModel, curModel);
            updateBackButton(false);
        }

        @Override
        public void onPlayProgress(int currPos, int duration) {
            // 在播放页且播放列表的最后一条声音到最后10s时出完播推荐弹窗
            Track track = getPlayingTrack();
            if (PlayCompleteManager.INSTANCE.canShowPlayCompleteDialog(track, currPos, duration) && getShowingTopChildFragment() == null) {
                showPlayCompleteFragment(track, false);
            }
            super.onPlayProgress(currPos, duration);
        }
    };

    private final AlbumEventManage.CollectListener collectListener = new AlbumEventManage.CollectListener() {
        @Override
        public void onCollectChanged(boolean collect, long id) {
            if (mSoundInfo != null && mSoundInfo.trackInfo != null &&
                    mSoundInfo.trackInfo.albumId == id && collect) {
                mSoundInfo.trackInfo.subscribeFreeAuthorized = true;
                Track curTrack = PlayTools.getCurTrack(mContext);
                if (curTrack != null && curTrack.getAlbum() != null && curTrack.getAlbum().getAlbumId() == id) {
                    curTrack.setSubscribeFreeAuthorized(true);
                    XmPlayerManager.getInstance(mContext).updateTrackInPlayList(curTrack);
                }
            }
        }
    };

    // 由于上面那个在onPause被移除了，闭屏时没办法回调
    private final PlayStatusListenerDispatcher mPlayerStatusListenerForTrace = new PlayStatusListenerDispatcher() {
        @Override
        public void onSoundPlayComplete() {
            super.onSoundPlayComplete();

            Track track = getPlayingTrack();
            if (PlayCompleteManager.INSTANCE.canShowPlayCompleteDialogWhenPlayFraResume(track)) {
                mPlayCompleteTrackSystemTime = System.currentTimeMillis();
            }
        }

        @Override
        public void onPlayProgress(int currPos, int duration) {
            super.onPlayProgress(currPos, duration);
            mCurrentAlbumPlaySecondTime++;
        }

        @Override
        public void onPlayStart() {
            checkSubscribeUnlock();
        }
    };

    private final ILoginStatusChangeListener loginStatusChangeListener = new ILoginStatusChangeListener() {
        @Override
        public void onLogout(LoginInfoModelNew olderUser) {

        }

        @Override
        public void onLogin(LoginInfoModelNew model) {
            HandlerManager.postOnUIThreadDelay(() -> loadDataFromNet(true), 200);
        }

        @Override
        public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

        }
    };

    private long mPlayCompleteTrackSystemTime = 0; // 记录符合完播推荐弹窗显示的声音，播放完毕的系统时间，用于埋点
    private long getUntilTheTrackPlayCompleteTime(boolean isFromResume) {
        long untilTheTrackPlayCompleteTime = 0;
        if (isFromResume) {
            //闭屏，播放完亮屏时
            if (mPlayCompleteTrackSystemTime != 0) {
                // 播放完（包括后置贴片）
                untilTheTrackPlayCompleteTime = mPlayCompleteTrackSystemTime - System.currentTimeMillis();
            } else {
                long duration = XmPlayerManager.getInstance(mContext).getDuration();
                long curPos = XmPlayerManager.getInstance(mContext).getCurrentPosInner();
                if (curPos != 0) {
                    // 完播前，用户就亮屏了
                    untilTheTrackPlayCompleteTime = duration - curPos;
                } else {
                    // 播放完后，正在播放声音的后置贴片
                    untilTheTrackPlayCompleteTime = 0;
                }
            }
        } else {
            // 有可能亮屏时，声音播放完毕后，才出弹窗
            long duration = XmPlayerManager.getInstance(mContext).getDuration();
            long curPos = XmPlayerManager.getInstance(mContext).getCurrentPosInner();
            if (curPos != 0) {
                untilTheTrackPlayCompleteTime = duration - curPos;
            } else {
                untilTheTrackPlayCompleteTime = mPlayCompleteTrackSystemTime - System.currentTimeMillis();
            }
        }
        mPlayCompleteTrackSystemTime = 0;

        // 虽然走不进来，但还是做一次过滤，防止不合理的数据上报。
        long configMinerLeftTime = PlayCompleteManager.INSTANCE.getConfigMinerLeftTime();
        if (untilTheTrackPlayCompleteTime > configMinerLeftTime) {
            return configMinerLeftTime / 1000;
        }

        return untilTheTrackPlayCompleteTime / 1000;
    }

    private void showPlayCompleteFragment(Track track, boolean isFromResume) {
        if (!canUpdateUi() || track == null || DeviceUtil.isLandscape(mActivity)) {
            return;
        }
        BaseFragment2 playEndFragment;
        if (PlayCompleteManager.INSTANCE.useYMode()) {
            playEndFragment = YPlayCompleteRecommendFragment.Companion.newInstance(
                    track, PlayCompleteManager.INSTANCE.getYPlayEndDetailModel(), getUntilTheTrackPlayCompleteTime(isFromResume));
            doStartFragmentOnPlayPage(playEndFragment);
        } else {
            playEndFragment = PlayCompleteRecommendFragment.Companion.newInstance(
                    track, PlayCompleteManager.INSTANCE.getPlayEndDetailModel(), getUntilTheTrackPlayCompleteTime(isFromResume));
            doStartFragmentOnPlayPage(playEndFragment);
        }
        PlayCompleteManager.INSTANCE.setPlayCompleteDialogShowStatus(true);
    }

    private final IPlayPageApiForTab mPlayPageApiForTab = new IPlayPageApiForTab() {
        @Override
        public void showOrHideTitleBar(boolean show) {
            if (mVgTitleBar != null) {
                mVgTitleBar.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
            }
            hideTopBar();
        }

        @Override
        public void showFragmentOnPlayPage(Fragment fragment) {
            doShowFragmentOnPlayPage(fragment);
        }

        @Override
        public void showFragmentOnPlayPage(Fragment fragment, boolean noShowRepeat) {
            doShowFragmentOnPlayPage(fragment, noShowRepeat);
        }

        @Override
        public void hideFragmentOnPlayPage(Fragment fragment) {
            doHideFragmentOnPlayPage(fragment);
        }

        @Override
        public void removeFragment(Fragment fragment) {
            PlayFragmentNew.this.removeChildFragment(fragment);
        }

        @Override
        public boolean hasTopFragment() {
            return getShowingTopChildFragment() != null;
        }

        @Nullable
        @Override
        public Fragment getChildFragmentOnPlayPage(Function1<Fragment, Boolean> condition) {
            return getChildFragment(condition);
        }

        @Override
        public void enableOrDisableTopSlideExit(boolean enable) {
            PlayFragmentNew.this.enableOrDisableTopSlideExit(enable);
        }

        @Override
        public void setTitleBarColor(int color, int indicatorColor) {
            PlayFragmentNew.this.updateTitleBarColor(color, indicatorColor);
        }

        @Override
        public void notifyBottomBarHeightChanged(BasePlayPageTabFragment fragment, int height) {
            if (fragment == mCurShowingFragment && mFloatingControlBarComponent != null) {
                mFloatingControlBarComponent.setMarginBottom(height + mFloatingControlBarNormalMarginBottom);
            }
        }

        @Override
        public long getTaskIdAndReset() {
            long taskId = mTaskId;
            mTaskId = 0;
            return taskId;
        }

        @Override
        public void setTabScrollable(boolean scrollable) {
            if (mViewPager != null) {
                mViewPager.setScrollable(scrollable);
            }
        }

        @Override
        public void addCoverLoadedListener(ICoverLoadedListener listener) {
            if (mCoverLoadedListenerSet == null) {
                mCoverLoadedListenerSet = new ArraySet<>(1);
            }
            mCoverLoadedListenerSet.add(listener);
        }

        @Override
        public void removeCoverLoadedListener(ICoverLoadedListener listener) {
            mCoverLoadedListenerSet.remove(listener);
        }

        @Override
        public void doFetchImmersiveColorTask(int color,int mainColor) {
            mBackgroundColor = color;
            updateBackground(mBackgroundColor);
            int bottomViewBgColor = ColorUtil.covertColorToFixedSaturationAndLightness(mainColor, 0.32f, 0.27f);

//            PlayPageDataManager.getInstance().setThemeColor(mBackgroundColor, mBackgroundColor, bottomViewBgColor);
            if (mFloatingControlBarComponent != null) {
                mFloatingControlBarComponent.setProgressColor(color);
            }

            if (viewModel != null) {
                viewModel.updateThemeByAd(mBackgroundColor, mBackgroundColor, bottomViewBgColor);
            }
        }

        @Override
        public boolean isFromPlayBar() {
            return PlayFragmentNew.this.isFromPlayBar;
        }
    };

    private final IScrollViewListener mCurShowingFragmentScrollViewListener = new IScrollViewListener() {
        private int mLastScrollY;

        @Override
        public void onScrollYChange(int scrollY) {
            boolean shouldFoldOrUnFoldWhileScroll = true;
            // 音频播放页第一屏不出悬浮控制条，第二屏悬浮控制条出且保持展开状态
            if (isAudioPlayFragment(mCurShowingFragment)) {
                int screenHeight = BaseUtil.getScreenHeight(getContext());
                if (scrollY <= screenHeight) {
                    hideFloatingControlBarComponent();
                    shouldFoldOrUnFoldWhileScroll = false;
                } else if (scrollY <= screenHeight * 2) {
                    showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum.UNFOLD);
                    shouldFoldOrUnFoldWhileScroll = false;
                }
            }

            if (shouldFoldOrUnFoldWhileScroll) {
                // 下滑收起，上滑展开
                if (mLastScrollY < scrollY) {
                    showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum.FOLD);
                } else if (mLastScrollY > scrollY) {
                    showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum.UNFOLD);
                }
            }
            mLastScrollY = scrollY;
        }

        @Override
        public void onScrollStateChanged(int scrollState) {
            if (scrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE
                    && !(isAudioPlayFragment(mCurShowingFragment) && mLastScrollY <= BaseUtil.getScreenHeight(getContext()))) {
                cancelShowFloatingControlBarUnfoldTask();
                postOnUiThreadDelayed(mShowFloatingControlBarUnfoldTask, 500);
            } else {
                cancelShowFloatingControlBarUnfoldTask();
            }
        }
    };

    private final IViewOnVisibilityChangeListener mOnCommentInputLayoutVisibilityChangeListener
            = visibility -> {
        if (visibility == View.VISIBLE) {
            stashFloatingControlBar();
        } else {
            unStashFloatingControlBar();
        }
    };

    private final Runnable mShowFloatingControlBarUnfoldTask = () ->
            showFloatingControlBarComponent(FloatingControlBarComponent.ShowTypeEnum.UNFOLD);

    private final BroadcastReceiver mPlayCompleteReceiver = new BroadcastReceiver() {
        @SuppressLint("CheckTraceBindDataDetector")
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && intent.getAction() != null) {
                if (intent.getAction().equals(PlayCompleteRecommendManager
                        .ACTION_ENTER_RECOMMEND)) {
                    if (canUpdateUi()) {
                        CustomToast.showSuccessToast("已进入推荐模式");
                    }
                    PlayPageDataManager.getInstance().notifyPlayModeChange();
                } else if (intent.getAction().equals(PlayCompleteRecommendManager
                        .ACTION_QUIT_RECOMMEND)) {
                    if (canUpdateUi()) {
                        CustomToast.showSuccessToast("已退出推荐模式");
                    }
                    PlayPageDataManager.getInstance().notifyPlayModeChange();
                }
            }
        }
    };

    private final BroadcastReceiver mNetworkBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && intent.getAction() != null && NetWorkChangeReceiver
                    .NETWORK_CHANGE_ACTION.equals(intent.getAction())) {
                checkFreeFlow();
            }
        }
    };

    private final IPlayFragmentService mPlayFragmentService = new IPlayFragmentService() {
        @Override
        public boolean changeTab(int tabType) {
            return changeTabByType(tabType);
        }

        @Override
        public boolean changeTabWithArgs(int tabType, Bundle args) {
            return changeTabByTypeWithArgs(tabType, args);
        }

        @Override
        public boolean hasTab(int tabType) {
            return hasTabByType(tabType);
        }

        @Override
        public boolean checkIsCurrentTabOfType(int type) {
            return isCurrentTabOfType(type);
        }

        @Override
        public void updateTab() {
            PlayFragmentNew.this.updateTab(mTabList);
        }

        @Override public void updateTabIfNeeded(PlayPageTabAndSoundInfo playingSoundInfo) {
//            if (playingSoundInfo == null) return;
//            if (playingSoundInfo.getSoundInfo() == null) return;
//            if (mSoundInfo == null) return;
//            String videoStyle = playingSoundInfo.getSoundInfo().videoPlayType;
//            if (videoStyle != null && !videoStyle.equals(mSoundInfo.videoPlayType)) {
//                PlayFragmentNew.this.handleOnDataLoaded(playingSoundInfo, false);
//            }
        }

        @Override
        public void setShareComponentVisibility(boolean isVisible) {
            if (mShareComponent != null) {
                if (isVisible) {
                    mShareComponent.show();
                } else {
                    mShareComponent.hide();
                }
            }
        }

        @Override
        public boolean jumpToVideo(Bundle videoArguments) {
            if (isRealVisable()) {
                PlayFragmentNew.this.jumpToVideoImpl(videoArguments);
                return true;
            }
            return false;
        }

        @Override
        public Bundle getArgumentsForVideo() {
            return mArgumentsForVideo;
        }

        @Override
        public List<PlayPageTab> getPlayPageTabList() {
            return mTabList;
        }

        @Override
        public void hideTopRightEntry() {
            PlayFragmentNew.this.hideTopRightEntry();
        }

        @Override
        public void startFragmentOnPlayPage(Fragment fragment) {
            doStartFragmentOnPlayPage(fragment);
        }

        @Override
        public void showFragmentOnPlayPage(Fragment fragment, boolean noShowRepeat) {
            doShowFragmentOnPlayPage(fragment, noShowRepeat);
        }

        @Override
        public void removeFragment(Fragment fragment) {
            removeChildFragment(fragment);
        }

        @Override
        public void removeAllTopFragments() {
            removeAllChildFragments();
        }

        @Override
        public void hideFragment(Fragment fragment) {
            doHideFragmentOnPlayPage(fragment);
        }

        public void setSkinAdBackgroundColor(int color) {
            if (mVBg == null) {
                return;
            }

            if (viewModel != null) viewModel.updateThemeByAd(color, color, color);
//            skinAdBgColor = color;
//            if (skinAdBgColor != 0){
//                updateBackground(skinAdBgColor);
//            } else {
//                updateBackground(mBackgroundColor);
//            }
        }

        @Override
        public void setSkinAdThemeColor(int color, int bottomColor) {
            int foregroundColor;
            if (color != 0){
                foregroundColor = ColorUtil.covertColorToFixedSaturationAndLightness(color, 1f, 0.80f);
//                PlayPageDataManager.getInstance().setThemeColor(foregroundColor, color, bottomColor);
            } else {
                foregroundColor = ColorUtil.covertColorToFixedSaturationAndLightness(mBackgroundColor, 1f, 0.80f);
//                PlayPageDataManager.getInstance().setThemeColor(foregroundColor, mBackgroundColor, mBackgroundColor);
            }

            if (viewModel != null) {
                viewModel.updateThemeByAd(foregroundColor, color, bottomColor);
            }
        }

        @Override
        public boolean isAudioFragmentV2() {
            return false;
        }

        @Override
        public void finishFragment() {
            PlayFragmentNew.this.finishFragment();
        }

        @Override
        public void refresh() {
            loadDataFromNet(true);
        }
    };

    private int skinAdBgColor;
    private void updateBackground(int color){
        // 这里优先设置皮肤广告的背景色，防止皮肤广告展示过程中被其他组件更改了背景色
//        if (skinAdBgColor != 0) {
//            mVBg.setBackgroundColor(skinAdBgColor);
//        } else {
            mVBg.setBackgroundColor(color);
//        }
    }

    private final IFreeFlowService.IProxyChange mProxyChange = (isRemove, config) -> {
        if (!canUpdateUi()) {
            return;
        }

        if (!isRemove &&
                (NetworkUtils.getPlayType(XmPlayerManager.getInstance(mContext).getCurrSound()) != DownloadTools.TAIHE_SOUND_TYPE)) {
            CustomToast.showToast("免流量播放");
        }
    };

    private final PlayPageDataManager.IOnAuthorizedStatusChangeListener mOnAuthorizedStatusChangeListener = () -> {
        if (canUpdateUi()) {
//            updateTab(mTabList);
            loadDataFromNet(true);
        }
    };

     private IFreeListenTimeListener mFreeListenTimeListener = new IFreeListenTimeListener() {
         @Override
         public void onListenTimeChange(int remainTime, boolean byAddTime, int addedTime) {
             if (byAddTime && canUpdateUi()) {
                 loadDataFromNet(true);
             }
         }

         @Override
         public void onListenTimeEmpty() {
             if (!isResume) {
                 isNeedShowFreeListenDialogOnResume = true;
             } else {
                 FreeListenTimeManager.setIsNeedReplayWhenRewardTime(true);
                 ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.PLAY_PAGE_DIALOG, getPlayingTrack(), PlayFragmentNew.this, 2);
                 isNeedShowFreeListenDialogOnResume = false;
             }
         }

         @Override
         public void onEnterAllDayFreeListen() {
             if (canUpdateUi()) {
                 loadDataFromNet(true);
             }
         }

         @Override
         public void onExitAllDayFreeListen() {
             if (canUpdateUi()) {
                 loadDataFromNet(true);
             }
         }
     };

    private void registerFreeListenTimeListener() {
        FreeListenTimeManager.addListenTimeListener(getContext(), mFreeListenTimeListener);
    }

    private void unregisterFreeListenTimeListener() {
        FreeListenTimeManager.removeListenTimeListener(getContext(), mFreeListenTimeListener);
    }

     private TingLocalMediaService.IStopPlayDueToFreeListenPermissionListener  mStopPlayDueToFreeListenPermissionListener = new TingLocalMediaService.IStopPlayDueToFreeListenPermissionListener() {
        @Override
        public void onStopPlayDueToFreeListenPermission() {
            if (!isResume) {
                isNeedShowFreeListenDialogOnResume = true;
            } else {
                FreeListenTimeManager.setIsNeedReplayWhenRewardTime(true);
                ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.PLAY_PAGE_DIALOG, getPlayingTrack(), PlayFragmentNew.this, 2);
                isNeedShowFreeListenDialogOnResume = false;
            }
        }
    };

    private void registerStopPlayDueToFreeListenPermissionListener() {
        TingLocalMediaService.getInstance().addStopPlayDueToFreeListenPermissionListener(mStopPlayDueToFreeListenPermissionListener);
    }

    private void unregisterStopPlayDueToFreeListenPermissionListener() {
        TingLocalMediaService.getInstance().removeStopPlayDueToFreeListenPermissionListener(mStopPlayDueToFreeListenPermissionListener);
    }


    @Override
    public boolean shouldKeepUbtSource() {
        return true;
    }

    @Override
    public String keyForUbtSource() {
        PlayableModel playableModel = XmPlayerManager.getInstance(mContext).getCurrSound();
        if (playableModel instanceof Track && ((Track) playableModel).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) {
            return Constants.KEY_DAILY_NEWS;
        }
        return Constants.KEY_PLAY;
    }

    public interface IPlayPageApiForTab {
        void showOrHideTitleBar(boolean show);

        void showFragmentOnPlayPage(Fragment fragment);

        void showFragmentOnPlayPage(Fragment fragment, boolean noShowRepeat);

        void hideFragmentOnPlayPage(Fragment fragment);

        void removeFragment(Fragment fragment);

        boolean hasTopFragment();

        @Nullable
        Fragment getChildFragmentOnPlayPage(Function1<Fragment, Boolean>condition);

        void enableOrDisableTopSlideExit(boolean enable);

        void setTitleBarColor(@ColorInt int color, int indicatorColor);

        void notifyBottomBarHeightChanged(BasePlayPageTabFragment fragment, int height);

        long getTaskIdAndReset();

        void setTabScrollable(boolean scrollable);

        void addCoverLoadedListener(PlayFragmentNew.ICoverLoadedListener listener);

        void removeCoverLoadedListener(PlayFragmentNew.ICoverLoadedListener listener);

        void doFetchImmersiveColorTask(int color,int mainColor);

        boolean isFromPlayBar();
    }

    public interface IScrollViewListener {
        void onScrollYChange(int scrollY);

        void onScrollStateChanged(int scrollState);
    }

    public interface ICoverLoadedListener {
        void onCoverLoaded(@NonNull Bitmap bitmap, @NonNull String coverUrl);
    }

    public void doBack() {
        if (UserInfoMannage.hasLogined() && "1".equals(ABTest.getString("playpage_subscription_reminder", ""))) {
            try {
                String config = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME, "playpage_subscription_reminder", "");
                JSONObject jsonObject = new JSONObject(config);
                JSONObject valueConfig = jsonObject.optJSONObject("total_value_config");
                int minTime = valueConfig.optInt("album_duration", 3);
                int maxShowCount = valueConfig.optInt("album_total_display_times", 3);
                int maxShowOneDay = valueConfig.optInt("album_daily_times", 1);
                if (mCurrentAlbumPlaySecondTime >= minTime && mSoundInfo != null && mSoundInfo.trackInfo != null &&
                        mSoundInfo.albumInfo != null && !mSoundInfo.albumInfo.isFavorite &&
                        PlayPageSubscribeListenTimeManager.INSTANCE.hasShowToday(mSoundInfo.albumInfo.albumId) < maxShowOneDay) {
                    AlbumM album = mSoundInfo.toAlbumM();
                    int showCount = PlayPageSubscribeListenTimeManager.INSTANCE.getAlbumTipShowCount(mSoundInfo.albumInfo.albumId);
                    if (showCount < maxShowCount && album != null) {
                        PlayPageSubscribeGuideDialogFragment dialog = PlayPageSubscribeGuideDialogFragment.Companion.newInstance();
                        dialog.setAlbum(mSoundInfo.trackInfo.trackId, album, this, jsonObject, mSoundInfo.xmRequestId);
                        dialog.setDismissCallback(needDoAnimator -> {
                            if (!handleBackEvent()) {
                                finishFragment();
                            }
                        });
                        dialog.show(getChildFragmentManager(), "PlayPageSubscribeGuideDialogFragment");
                        return;
                    }
                }
            } catch (JSONException e) {
            }
        }
        if (!handleBackEvent()) {
            finishFragment();
        }
    }
}
