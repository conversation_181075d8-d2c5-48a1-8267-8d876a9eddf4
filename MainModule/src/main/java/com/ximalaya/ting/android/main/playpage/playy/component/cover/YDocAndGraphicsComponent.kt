package com.ximalaya.ting.android.main.playpage.playy.component.cover

import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.LinearGradient
import android.graphics.Shader
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.AbsListView
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.SeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.async.AsyncImage
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.cdnBlur
import com.ximalaya.ting.android.host.model.play.PicSequence
import com.ximalaya.ting.android.host.model.play.PicSequenceData
import com.ximalaya.ting.android.host.model.play.PlayTtsDocInfo
import com.ximalaya.ting.android.host.model.play.PlayTtsTrackTimbre
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil
import com.ximalaya.ting.android.host.util.common.IScreenConfigChangedListener
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.performance.PageStartOpt
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.remote.IScrollListenerCallBack
import com.ximalaya.ting.android.main.playpage.internalservice.IAttachDocOnCoverComponentService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.manager.YDocCoverDataManager
import com.ximalaya.ting.android.main.playpage.manager.YDomainColorUtil
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.YPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponentWithPlayStatusListener
import com.ximalaya.ting.android.main.playpage.playy.component.cover.manager.YCoverComponentsEnum
import com.ximalaya.ting.android.main.playpage.playy.component.function.IYFunctionBarComponentService
import com.ximalaya.ting.android.main.playpage.playy.listener.IYDocComponentService
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.view.DocBackgroundImageView
import com.ximalaya.ting.android.main.playpage.view.OneToOneDocVagueImageView
import com.ximalaya.ting.android.main.playpage.view.SixteenToNineDocVagueImageView
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlin.math.roundToLong

/**
 * Create by {jian.kang} on 2024/3/20
 * <AUTHOR>
 */
class YDocAndGraphicsComponent(
    private val bottomRefView: View,
    private val updateComponents: () -> Unit,
    private val isDocShowing: () -> Boolean
): XBaseCoverComponentWithPlayStatusListener(), IScreenConfigChangedListener {

    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private var mBackgroundIv: DocBackgroundImageView? = null
    private var mSixCoverIv: SixteenToNineDocVagueImageView? = null
    private var mOneToOneIv: OneToOneDocVagueImageView? = null
    private var mSixTagIv: ImageView? = null
    private var mOneTagIv: ImageView? = null
    private var mLoadBitmap: Boolean = false

    private var loadingView: View? = null
    private var loadingErrorView: View? = null

    private var docShow: Boolean = false
    private var mIsSeekTrackingTouch = false

    private var mLastShowDocTime = 0L
    private var mHasSwitchSound = true

    private var isPaused: Boolean = false

    private var mData: PicSequenceData? = null
    private var currentPic: PicSequence? = null

    private var mIsScrollToTop = false

    private var viewModel: XPlayViewModel? = null
    private var yViewModel: YPlayViewModel? = null

    private var mIs16 = false

    private var pendingThemeColor = 0

    private var lastTrackId = 0L

    companion object {
        private const val TAG = "YDocAndGraphics"
    }

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)

        viewModel = ViewModelProvider(fragment!!.requireActivity()).get(XPlayViewModel::class.java)
        yViewModel = ViewModelProvider(fragment!!.requireActivity()).get(YPlayViewModel::class.java)

        PlayPageInternalServiceManager.getInstance().registerService(
            IAttachDocOnCoverComponentService::class.java, mService)
        coverComponentsManager?.playContainer?.registerScrollListener(mScrollListener)
        FoldableScreenCompatUtil.addListener("YDocAndGraphicsComponent", this@YDocAndGraphicsComponent)

        inflateAndInitUI()

        viewModel?.picSequenceData?.observe(mFragment, object : Observer<PicSequenceData> {
            override fun onChanged(t: PicSequenceData?) {
                mData = t
                loadInit()
                updateComponents()
            }
        })
    }

    private fun loadInit() {
        val pic = mData?.picSequences?.firstOrNull()
        if (pic != null) {
            currentPic = pic
            pic.job?.cancel()
            loadImage(pic)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        PlayPageInternalServiceManager.getInstance().unRegisterService(
            IAttachDocOnCoverComponentService::class.java)
        coverComponentsManager?.playContainer?.removeScrollListener(mScrollListener)
        FoldableScreenCompatUtil.removeListener("YDocAndGraphicsComponent")

    }

    private val mService: IAttachDocOnCoverComponentService = object : IAttachDocOnCoverComponentService {
        override fun onLrcAdShow(adHeight: Int) {
        }

        override fun onLrcAdHide() {
        }

        override fun onSoundPatchAdChange(show: Boolean) {
        }

        override fun isSupportShowDoc(soundInfo: PlayingSoundInfo?): Boolean {
            return false
        }

        override fun onSeekBarProgressChanged(seekBar: SeekBar, progress: Int) {
        }

        override fun onSeekBarShow(show: Boolean) {
        }

        override fun onStartTrackingTouch() {
            mIsSeekTrackingTouch = true
        }

        override fun onStopTrackingTouch(progress: Int) {
            mIsSeekTrackingTouch = false
            updateTime(progress)
        }

        override fun unSelectLrcView() {
        }

        override fun setFullScreenState(fullScreen: Boolean) {
        }

        override fun onTtsTimbreChanged(playTtsTrackTimbre: PlayTtsTrackTimbre?) {
        }

        override fun getCurPlayTtsDocInfo(): PlayTtsDocInfo? {
            return null
        }

        override fun jumpNovelOrTTS() {
        }

        override fun onDocScrollHideShowStateChange(isShow: Boolean) {
        }

        override fun registerDocCoverStateChange(docCoverStateChangeListener: IAttachDocOnCoverComponentService.IDocCoverStateChangeListener?) {
        }

        override fun unregisterDocCoverStateChange(docCoverStateChangeListener: IAttachDocOnCoverComponentService.IDocCoverStateChangeListener?) {
        }

    }

    private val mScrollListener: IScrollListenerCallBack = IScrollListenerCallBack { view, scrollState ->

            if (scrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE) {
                if (!isCoverViewVisible()) {
                    traceOnCoverShowDuration()
                } else {
                    setLastShowDocTime()
                    traceOnCoverShowForResume()
                }

                val content = bottomRefView
                if (content != null && !PSkinManager.isEnabled) {
                    val location = IntArray(2)
                    content.getLocationOnScreen(location)
                    Logger.d(TAG, "location[1] = ${location[1]}; ${coverComponentsManager.playContainer.getTitleBarPlaceHolder()?.height} ")
                    val scrollToTop = (location[1] < (coverComponentsManager.playContainer.getTitleBarPlaceHolder()?.height ?: 0))
                    if (!scrollToTop && pendingThemeColor != 0) {
                        if (pendingThemeColor != yViewModel?.aiGMainColor?.value) {
                            yViewModel?.aiGMainColor?.value = pendingThemeColor
                        }
                        pendingThemeColor = 0
                    }
                    mIsScrollToTop = scrollToTop
                }
            }
        }


    private fun reset() {
        mData?.picSequences?.forEach {
            if (it.job?.isActive == true) {
                it.job?.cancel()
            }
        }
        mOneToOneIv?.reset()
        mSixCoverIv?.reset()
        mLoadBitmap = false
        pendingThemeColor = 0
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        // 单曲循环
//        mLoadBitmap = false
//        if (isVisible) {
//            updateComponents()
//        }
//        if (mBackgroundIv?.visibility == View.VISIBLE) {
//            mBackgroundIv?.reset()
//            mBackgroundIv?.visibility = View.GONE
//        }

        if (curModel != null) {
            lastTrackId = curModel.dataId
            reset()
        }


        super.onSoundSwitch(lastModel, curModel)
        if (lastModel?.dataId != curModel?.dataId) {
            if (!isPaused) {
                traceOnCoverShowDuration()
            }
            mLastShowDocTime = 0
            mHasSwitchSound = true
        } else {
            mHasSwitchSound = false
        }
    }

    override fun onPause() {
        super.onPause()
        isPaused = true
        traceOnCoverShowDuration()
    }

    override fun onResume() {
        super.onResume()
        setLastShowDocTime()
        if (isPaused) {
            isPaused = false
            traceOnCoverShowForResume()
        }

        val pos = XmPlayerManager.getInstance(mContext).playCurrPositon
        updateTime(pos)
    }

     override fun initUi() {
         loadingView = mContentView?.findViewById(R.id.main_g_loading)
         loadingErrorView = mContentView?.findViewById(R.id.main_load_error)
         loadingErrorView?.setOnClickListener {
             ViewStatusUtil.setVisible(View.VISIBLE, loadingView)
             ViewStatusUtil.setVisible(View.GONE, loadingErrorView)
             loadInit()
         }

         mBackgroundIv = mFragment?.requireView()?.findViewById(R.id.main_iv_doc_background)
         mOneToOneIv = mContentView?.findViewById(R.id.main_iv_cover_img_one)
         mSixCoverIv = mContentView?.findViewById(R.id.main_iv_cover_img_six)
         mSixTagIv = mContentView?.findViewById(R.id.main_iv_cover_tag_six)
         mOneTagIv = mContentView?.findViewById(R.id.main_iv_cover_tag_one)
         updateIvAspectRatioInFoldDevice(context?.resources?.configuration)
         updateOneToOneMarginTop()
         updateSixteenToNineMarginTop()
    }

    private fun updateOneToOneMarginTop() {
        if (mOneToOneIv != null && mOneToOneIv!!.layoutParams is ViewGroup.MarginLayoutParams) {
            val lp = mOneToOneIv?.layoutParams as ViewGroup.MarginLayoutParams
            lp.topMargin = 60.dp
            mOneToOneIv?.layoutParams = lp
            mOneToOneIv?.requestLayout()
        }
        if (mOneTagIv != null && mOneTagIv!!.layoutParams is ViewGroup.MarginLayoutParams) {
            val lp = mOneTagIv?.layoutParams as ViewGroup.MarginLayoutParams
            var titleV: View? = coverComponentsManager?.playContainer?.getTitleBarPlaceHolder()
            titleV?.post {
                val pos = IntArray(2)
                titleV?.getLocationOnScreen(pos)
                if (pos[1] != 0) {
                    lp.topMargin = (pos[1] - BaseUtil.getStatusBarHeight(mContext) + 8.dp)
                } else {
                    lp.topMargin = 58.dp
                }
            }
        }
    }

    private fun updateSixteenToNineMarginTop() {
        if (mSixCoverIv == null || mSixCoverIv!!.layoutParams !is ViewGroup.MarginLayoutParams) {
            return
        }

        if (!isDocShowing()) {
            val titleBarPlaceHolder =
                coverComponentsManager?.playContainer?.getTitleBarPlaceHolder()
            var titleHeight = titleBarPlaceHolder?.height ?: (BaseUtil.getStatusBarHeight(mContext) + BaseUtil.dp2px(mContext, 50f))

            val tipsView = coverComponentsManager?.playContainer?.getTipsContaineriView() ?: return
            val tipsPos = IntArray(2)

            tipsView?.getLocationOnScreen(tipsPos)

            var height = ((9.0f / 16.0f) * BaseUtil.getScreenWidth(mContext)).toInt()
            if (BaseUtil.isFoldScreen(mContext)) {
                height = 211.dp
            }
            val slot = ((tipsPos[1] - titleHeight - height) / 2).toInt()
            var marginTop = if (slot <= 0) {
                titleHeight
            } else {
                ((tipsPos[1] + titleHeight - height) / 2).toInt()
            }

            val lp = mSixCoverIv?.layoutParams as ViewGroup.MarginLayoutParams
            lp.topMargin = marginTop
            mSixCoverIv?.layoutParams = lp
        } else {
            val lp = mSixCoverIv?.layoutParams as ViewGroup.MarginLayoutParams
            lp.topMargin = (BaseUtil.getStatusBarHeight(mContext) + BaseUtil.dp2px(mContext, 68f))
            mSixCoverIv?.layoutParams = lp
        }
    }

    override fun inflate(): View {
        val context = mFragment.requireContext()
//        return LayoutInflater.from(context).inflate(
//            R.layout.main_layout_doc_background_y,
//            FrameLayout(context),
//            false
//        )

        return PageStartOpt.PlayPageOpt
            .inflateView(context, R.layout.main_layout_doc_background_y, FrameLayout(context), false)
    }

    override fun getViewStubId() = 0

    override fun needShowThisComponent(soundInfo: PlayingSoundInfo?): Boolean {
        if (soundInfo?.trackInfo?.trackId != lastTrackId) {
            lastTrackId = soundInfo?.trackInfo?.trackId?: 0L
            reset()
        }
        if (!YDocCoverDataManager.canShowCoverComponent(soundInfo)) {
            return false
        }

        val pics = mData?.picSequences

        if (pics.isNullOrEmpty() || soundInfo?.trackInfo?.trackId != mData?.trackId) {
            return false
        }

        if (mLoadBitmap) {
            return true
        } else {
            // 防止暂停播放时，无法触发
            val playPosition = XmPlayerManager.getInstance(mContext).playCurrPositon
            updateTime(playPosition)
        }

        return false
    }

    fun needShowThisComponentWithLoading(soundInfo: PlayingSoundInfo?): Boolean {
        if (soundInfo?.trackInfo?.trackId != lastTrackId) {
            lastTrackId = soundInfo?.trackInfo?.trackId?: 0L
            reset()
        }

        if (!YDocCoverDataManager.canShowCoverComponent(soundInfo)) {
            return false
        }

        val pics = mData?.picSequences

        if (pics.isNullOrEmpty()) {
            return false
        }
        // 防止暂停播放时，无法触发
        val playPosition = XmPlayerManager.getInstance(mContext).playCurrPositon
        updateTime(playPosition)

        if(!mLoadBitmap) {
            ViewStatusUtil.setVisible(View.VISIBLE, loadingView)
            ViewStatusUtil.setVisible(View.GONE, loadingErrorView)
        } else {
            ViewStatusUtil.setVisible(View.GONE, loadingView)
            ViewStatusUtil.setVisible(View.GONE, loadingErrorView)
        }

        return true
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
    }

    override fun onPlayProgress(currPos: Int, duration: Int) {
        super.onPlayProgress(currPos, duration)

        updateTime(currPos)
    }

    private fun updateTime(currPos: Int) {
        val soundInfo = PlayPageDataManager.getInstance().soundInfo
        // 预处理
        if (!YDocCoverDataManager.canShowCoverComponent(soundInfo) || isPaused) {
            return
        }

        val pics = mData?.picSequences

        if (pics.isNullOrEmpty()) {
            return
        }

        if (mData?.trackId == 0L || soundInfo?.trackInfo?.trackId != mData?.trackId) {
            return
        }

        Logger.w(TAG, "updateTime: currPos = $currPos;")

        pics.forEach { picSequence ->
            val end = picSequence.end?.takeIf { it > 0L }?: Long.MAX_VALUE
            if (picSequence.start <= currPos && end >= currPos) {
                if (currentPic != picSequence) {
                    currentPic = picSequence

                    if (picSequence.job?.isActive != true) {
                        loadImage(picSequence)
                    } else {
                        Logger.w(TAG, "loadImage: job is active, ignore")
                    }

                }
            } else {
                picSequence.job?.cancel()
                Logger.w(TAG, "picSequence.job?.cancel(); $picSequence")
            }
        }
    }

    private fun loadImage(picSequence: PicSequence) {
        Logger.d(TAG, "loadImage: $picSequence")

        picSequence.job = scope.launch {
            val normalReq = async {
                kotlin.runCatching {
                    AsyncImage.download(picSequence.url)
                }.getOrNull()
            }
            val blurReq = async {
                kotlin.runCatching {
                    AsyncImage.download(picSequence.url.cdnBlur())
                }.getOrNull()
            }

            Logger.d(TAG, "start await")
            val normalBitmap = normalReq.await()
            val blurBitmap = blurReq.await()
            Logger.d(TAG, "end await")

            if (normalBitmap != null && blurBitmap != null) {
                switchBackground(normalBitmap, blurBitmap, picSequence)

                if (isVisible && !PSkinManager.isEnabled) {
                    val color = YDomainColorUtil.getDomainOriginalColorAsync(normalBitmap)
                    Logger.d(TAG, "update color = ${Integer.toHexString(color)}")
                    if (!mIsScrollToTop) {
                        yViewModel?.aiGMainColor?.value = color
                    } else {
                        Logger.d(TAG, "update pending color")
                        pendingThemeColor = color
                    }
                } else {
                    Logger.d(TAG, "ignore update color")
                }

                ViewStatusUtil.setVisible(View.GONE, loadingView)
                ViewStatusUtil.setVisible(View.GONE, loadingErrorView)
            } else {
                Logger.d(TAG, "load error")

                ViewStatusUtil.setVisible(View.GONE, loadingView)
                ViewStatusUtil.setVisible(View.VISIBLE, loadingErrorView)
            }
        }
    }

    private fun switchBackground(bitmap: Bitmap, blurBitmap: Bitmap, picSequence: PicSequence) {
//        if (mIsSeekTrackingTouch) {
//            return
//        }

        if (bitmap == mSixCoverIv?.curBitmap || bitmap == mOneToOneIv?.curBitmap) {
            if (!isVisible) {
                mLoadBitmap = true
                updateComponents()
            }
            return
        }

        mLoadBitmap = true
        switchImageView(picSequence)
        mBackgroundIv?.switchBitmap(blurBitmap)
        if (mSixCoverIv?.visibility == View.VISIBLE) {
            mOneToOneIv?.clearBitmap()
            mSixCoverIv?.switchBitmap(bitmap, picSequence.start)
        } else {
            mSixCoverIv?.clearBitmap()
            mOneToOneIv?.switchBitmap(bitmap, picSequence.start)
        }

        updateComponents()
    }

    private fun switchImageView(picSequence: PicSequence) {
        mIs16 = (picSequence.picSize.picHeight.toFloat() / picSequence.picSize.picWidth.toFloat()) < 0.9f
        Logger.d(TAG, "mIs16--->$mIs16")

//        mBackgroundIv?.setBitmapMatrix(picSequence.picSize.picWidth,
//            picSequence.picSize.picHeight)
        if (mIs16) {
//            mSixCoverIv?.setBitmapMatrix(picSequence.picSize.picWidth,
//                picSequence.picSize.picHeight)
            updateSixteenToNineMarginTop()
        } else {
//            mOneToOneIv?.setBitmapMatrix(picSequence.picSize.picWidth,
//                picSequence.picSize.picHeight)
        }
        if (picSequence?.isAiPic == true) {
            mSixTagIv?.visibility = if (mIs16) View.VISIBLE else View.GONE
            mOneTagIv?.visibility = if (mIs16) View.GONE else View.VISIBLE
        } else {
            mSixTagIv?.visibility = View.GONE
            mOneToOneIv?.visibility = View.GONE
        }
        updateImageViewMaskStyle()
        mSixCoverIv?.visibility = if (mIs16) View.VISIBLE else View.GONE
        mOneToOneIv?.visibility = if (mIs16) View.GONE else View.VISIBLE
    }

    override fun hide() {
        traceOnCoverShowDuration()
        super.hide()
        if (!YDocCoverDataManager.canShowCoverComponent(PlayPageDataManager.getInstance().soundInfo)) {
            mBackgroundIv?.visibility = View.GONE
            mBackgroundIv?.reset()
        }
        mLoadBitmap = false
        mOneToOneIv?.visibility = View.GONE
        mSixCoverIv?.visibility = View.GONE
        mSixTagIv?.visibility = View.GONE
        mOneTagIv?.visibility = View.GONE
        mOneToOneIv?.reset()
        mSixCoverIv?.reset()

        ViewStatusUtil.setVisible(View.GONE, loadingView)
        ViewStatusUtil.setVisible(View.GONE, loadingErrorView)

        if (!PSkinManager.isEnabled) {
            yViewModel?.aiGMainColor?.value = PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
        }

        pendingThemeColor = 0
    }

    override fun show() {
        super.show()
        mBackgroundIv?.visibility = View.VISIBLE
        val playPosition = XmPlayerManager.getInstance(mContext).playCurrPositon
        updateTime(playPosition)
        if (mHasSwitchSound) {
            traceOnCoverShow()
        }
        setLastShowDocTime()
    }

    override fun needListenPlayStatusEvenHidden(): Boolean {
        return true
    }

    fun onDocShow(isDocShow: Boolean) {
        // 文稿显示的话
        docShow = isDocShow
        updateSixteenToNineMarginTop()

        updateImageViewMaskStyle()
    }

    private fun updateImageViewMaskStyle() {
        if (docShow) {
            mSixCoverIv?.post {
                if (mSixCoverIv != null && docShow) {
                    PlayPageInternalServiceManager.getInstance().getService(IYDocComponentService::class.java)?.apply {
                        if (getRootView() == null) {
                            updateSixIvMaskStyleWhenDocDismiss()
                            return@apply
                        }
                        getTwoLineLrcView()?.apply {
                            if (this.height == 0) {
                                this.post {
                                    if (this != null) {
                                        updateSixIvMaskStyleWhenDosShow(this)
                                    }
                                }
                            } else {
                                updateSixIvMaskStyleWhenDosShow(this)
                            }
                        } ?: run {
                            updateSixIvMaskStyleWhenDocDismiss()
                        }
                    }
                } else {
                    updateSixIvMaskStyleWhenDocDismiss()
                }
            }

            mOneToOneIv?.post {
                if (mOneToOneIv != null && docShow) {
                    PlayPageInternalServiceManager.getInstance().getService(IYDocComponentService::class.java)?.apply {
                        this.getTwoLineLrcView()?.apply {
                            if (rootView == null) {
                                updateOneIvMaskStyleWhenDocDismiss()
                                return@apply
                            }
                            getTwoLineLrcView()?.apply {
                                if (this.height == 0) {
                                    this.post {
                                        if (this != null) {
                                            updateOneIvMaskStyleWhenDosShow(this)
                                        } else {
                                            updateOneIvMaskStyleWhenDocDismiss()
                                        }
                                    }
                                } else {
                                    updateOneIvMaskStyleWhenDosShow(this)
                                }
                            } ?: run {
                                updateOneIvMaskStyleWhenDocDismiss()
                            }
                        }
                    }
                } else {
                    updateOneIvMaskStyleWhenDocDismiss()
                }
            }
        } else {
            updateSixIvMaskStyleWhenDocDismiss()
            updateOneIvMaskStyleWhenDocDismiss()
        }
    }

    private fun updateSixIvMaskStyleWhenDocDismiss() {
        mSixCoverIv?.post {
            if (mSixCoverIv != null) {
                val coverPos = IntArray(2)
                mSixCoverIv?.getLocationOnScreen(coverPos)
                val coverBottomY = coverPos[1] + mSixCoverIv?.height!!

                val tipsV = coverComponentsManager?.playContainer?.getTipsContaineriView() ?: return@post
                val tipsPos = IntArray(2)
                tipsV?.getLocationOnScreen(tipsPos)
                val tipsTopY = tipsPos[1];

                if (coverBottomY >= tipsTopY) {
                    val posMask = floatArrayOf(0f, 0.5f, 1.0f)
                    val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt())
                    mSixCoverIv?.showTopMask(true)
                    mSixCoverIv?.setBottomMask(70.dp, LinearGradient(0f, 0f, 0f, 70.dp.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
                } else {
                    mSixCoverIv?.showTopMask(false)
                    mSixCoverIv?.setBottomMask(0, null)
                }
            }
        }
    }

    private fun updateSixIvMaskStyleWhenDosShow(lrcV: View) {
        if (mSixCoverIv == null) {
            return
        }

        val lrcPosition = IntArray(2)
        lrcV.getLocationOnScreen(lrcPosition)
        val lrcBottomY = lrcPosition[1] + lrcV.height

        val coverPos = IntArray(2)
        mSixCoverIv?.getLocationOnScreen(coverPos)
        val coverBottomY = coverPos[1] + mSixCoverIv?.height!!

        Logger.d(TAG, "lrcPosition[1]: ${lrcPosition[1]}")
        mSixCoverIv?.showTopMask(true)

        if (lrcPosition[1] > coverBottomY + 40.dp) {
            mSixCoverIv?.setBottomMask(0, null)
            Logger.d(TAG, "mSixCoverIv > lrcBottomY--${lrcBottomY} --- coverBottomY --- ${coverBottomY}")
            Logger.d(TAG, "mSixCoverIv 40 > false")

        } else if (lrcPosition[1] > coverBottomY) {
            mSixCoverIv?.setBottomMask(80.dp, LinearGradient(0f, 0f, 0f, 80.dp.toFloat(), 0xff000000.toInt(), 0x00ffffff, Shader.TileMode.CLAMP))
            Logger.d(TAG, "mSixCoverIv > lrcBottomY--${lrcBottomY} --- coverBottomY --- ${coverBottomY}")
            Logger.d(TAG, "mSixCoverIv lrcPosition[1] > coverBottomY")
        } else if (lrcBottomY > coverBottomY) {

            val middle = (lrcV.height) / 4
            val bottomMaskHeight = 35.dp + middle
            val topProp = (35.dp.toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val posMask = floatArrayOf(0f, topProp, 1.0f)
            val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt())
            Logger.d(TAG, "mSixCoverIv > middle--${middle} --- bottomMaskHeight --- ${bottomMaskHeight} --- topProp--- ${topProp}")
            mSixCoverIv?.setBottomMask(bottomMaskHeight, LinearGradient(0f, 0f, 0f, bottomMaskHeight.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        } else if (lrcBottomY < coverBottomY) {
            val bottom = coverBottomY - lrcBottomY
            val middle = (lrcV.height) / 4
            val bottomMaskHeight = 35.dp + bottom + (middle * 3)

            val topMaskProp = (35.dp.toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val middleProp = topMaskProp + ((middle * 3).toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val posMask = floatArrayOf(0f, topMaskProp, middleProp, 1.0f)
            Logger.d(TAG, "mSixCoverIv < bottom--${bottom} --- middle--${middle} --- bottomMaskHeight --- ${bottomMaskHeight} --- middleProp--- ${middleProp}")
            val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt(), 0x00ffffff.toInt())
            mSixCoverIv?.setBottomMask(bottomMaskHeight, LinearGradient(0f, 0f, 0f, bottomMaskHeight.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        } else {

            val middle = (lrcV.height) / 4
            val bottomMaskHeight = 35.dp + middle * 3
            val topProp = (35.dp.toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val posMask = floatArrayOf(0f, topProp, 1.0f)
            val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt())
            Logger.d(TAG, "mSixCoverIv === middle--${middle} --- bottomMaskHeight --- ${bottomMaskHeight} --- topProp--- ${topProp}")
            mSixCoverIv?.setBottomMask(bottomMaskHeight, LinearGradient(0f, 0f, 0f, bottomMaskHeight.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        }

        Logger.d(TAG, "mSixCoverIv -------------------------------------")
    }

    private fun updateOneIvMaskStyleWhenDocDismiss() {
        if (mOneToOneIv != null) {
            val posMask = floatArrayOf(0f, 0.5f, 1.0f)
            val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt())
            mOneToOneIv?.setBottomMask(70.dp, LinearGradient(0f, 0f, 0f, 70.dp.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        }
    }

    private fun updateOneIvMaskStyleWhenDosShow(lrcV: View) {
        if (mOneToOneIv == null) {
            return
        }
        val lrcPosition = IntArray(2)
        lrcV.getLocationOnScreen(lrcPosition)
        val lrcBottomY = lrcPosition[1] + lrcV.height

        val coverPos = IntArray(2)
        mOneToOneIv?.getLocationOnScreen(coverPos)
        val coverBottomY = coverPos[1] + mOneToOneIv?.height!!

        if (lrcPosition[1] > coverBottomY) {
            val bottomPos = floatArrayOf(0f, 0.5f, 1.0f)
            val bottomColors = intArrayOf(0xff000000.toInt(), 0x33ffffff, 0x00ffffff)
            mOneToOneIv?.setBottomMask(80.dp, LinearGradient(0f, 0f, 0f, 80.dp.toFloat(), bottomColors, bottomPos, Shader.TileMode.CLAMP))
        } else if (lrcBottomY > coverBottomY) {

            val middle = (lrcV.height) / 4
            var bottomMaskHeight = 85.dp + coverBottomY - lrcBottomY + middle * 3
            var topProp = (85.dp.toFloat() / bottomMaskHeight.toFloat()).toFloat()
            var colors: IntArray? = null;
            if (topProp > 1.0) {
                topProp = 1.0f
                colors = intArrayOf(0xff000000.toInt(), 0x00ffffff.toInt(), 0x00ffffff.toInt())
            } else {
                colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt())
            }
            val posMask = floatArrayOf(0f, topProp, 1.0f)
            Logger.d(TAG, "mOneToOneIv > middle--${middle} --- bottomMaskHeight --- ${bottomMaskHeight} --- topProp--- ${topProp}")
            mOneToOneIv?.setBottomMask(bottomMaskHeight, LinearGradient(0f, 0f, 0f, bottomMaskHeight.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        } else if (lrcBottomY < coverBottomY) {
            val bottom = coverBottomY - lrcBottomY
            val middle = (lrcV.height) / 4
            val bottomMaskHeight = 85.dp + bottom + (middle * 3)

            val topMaskProp = (85.dp.toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val middleProp = topMaskProp + ((middle * 3).toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val posMask = floatArrayOf(0f, topMaskProp, middleProp, 1.0f)
            val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x01ffffff.toInt(), 0x00ffffff.toInt())
            Logger.d(TAG, "mOneToOneIv < topMaskProp--- ${topMaskProp}")
            Logger.d(TAG, "mOneToOneIv < bottom--${bottom} --- middle--${middle} --- bottomMaskHeight --- ${bottomMaskHeight} --- middleProp--- ${middleProp}")
            mOneToOneIv?.setBottomMask(bottomMaskHeight, LinearGradient(0f, 0f, 0f, bottomMaskHeight.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        } else {

            val middle = (lrcV.height) / 4
            val bottomMaskHeight = 85.dp + middle * 3
            val topProp = (85.dp.toFloat() / bottomMaskHeight.toFloat()).toFloat()
            val posMask = floatArrayOf(0f, topProp, 1.0f)
            val colors = intArrayOf(0xff000000.toInt(), 0x33ffffff.toInt(), 0x00ffffff.toInt())
            Logger.d(TAG, "mOneToOneIv -===== middle--${middle} --- bottomMaskHeight --- ${bottomMaskHeight} --- topProp--- ${topProp}")
            mOneToOneIv?.setBottomMask(bottomMaskHeight, LinearGradient(0f, 0f, 0f, bottomMaskHeight.toFloat(), colors, posMask, Shader.TileMode.CLAMP))
        }
        Logger.d(TAG, "--------------------------------------")
    }

    private fun traceOnCoverShow() {
        // 新声音播放页-AI 文稿模块  控件曝光
        val soundInfo = PlayPageDataManager.getInstance().soundInfo
        // 要展示文稿的时候，图组件不用上报
        if (YDocCoverDataManager.needShowDoc(coverComponentsManager, soundInfo)) {
            return
        }
        if (soundInfo?.trackInfo == null) {
            return
        }

        if (soundInfo?.albumInfo == null) {
            return
        }

        XMTraceApi.Trace()
            .setMetaId(49045)
            .setServiceId("slipPage")
            .put("currPage", "newPlay")
            .put("currAlbumId", "${soundInfo?.albumInfo?.albumId ?: 0}")
            .put("currTrackId", "${soundInfo?.trackInfo?.trackId ?: 0}")
            .put("docType", "图片")
            .put("subType", "")
            .put(XmRequestIdManager.CONT_ID, "$curTrackId")
            .put(XmRequestIdManager.CONT_TYPE, "newPlayDoc")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE
                )
            )
            .createTrace()
        Logger.d(TAG, "49045--gra--curTrackId--$curTrackId----docType--${getDocType(soundInfo)}----subtype---${if (YDocCoverDataManager.needShowDoc(coverComponentsManager, soundInfo)) getSubType() else ""}")
    }

    private fun traceOnCoverShowForResume() {
        if (coverComponentsManager?.currentCoverEnum == YCoverComponentsEnum.DOC_ON_COVER_COMPONENT) {
            return
        }

        val soundInfo = PlayPageDataManager.getInstance().soundInfo
        // 要展示文稿的时候，图组件不用上报
        if (YDocCoverDataManager.needShowDoc(coverComponentsManager, soundInfo)) {
            return
        }
        if (curTrackId == 0L || curAlbumId == 0L) {
            return
        }

        if (soundInfo?.albumInfo == null) {
            return
        }

        if (isVisible
            && (mOneToOneIv?.visibility == View.VISIBLE || mSixCoverIv?.visibility == View.VISIBLE)
            && isCoverViewVisible()) {
            // 新声音播放页-AI 文稿模块  控件曝光
            XMTraceApi.Trace()
                .setMetaId(49045)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currAlbumId", "${soundInfo?.albumInfo?.albumId ?: 0}")
                .put("currTrackId", "${soundInfo?.trackInfo?.trackId ?: 0}")
                .put("docType", "图片")
                .put("subType", "")
                .put(XmRequestIdManager.CONT_ID, "$curTrackId")
                .put(XmRequestIdManager.CONT_TYPE, "newPlayDoc")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                        XmRequestPage.PAGE_PLAY_PAGE
                    )
                )
                .createTrace()
            Logger.d(TAG, "49045--gra--curTrackId--$curTrackId---docType--${getDocType(soundInfo)}--subtype---${if (YDocCoverDataManager.needShowDoc(coverComponentsManager, soundInfo)) getSubType() else ""}")

        }

    }

    private fun traceOnCoverShowDuration() {
        // 新声音播放页-AI文稿模块消失  其他事件
        if (!isVisible || (mOneToOneIv?.visibility != View.VISIBLE && mSixCoverIv?.visibility != View.VISIBLE) || mLastShowDocTime == 0L) {
            return
        }

        val duration = System.currentTimeMillis() - mLastShowDocTime
        if (duration <= 0L) {
            return
        }

        val soundInfo = PlayPageDataManager.getInstance().soundInfo
        // 要展示文稿的时候，图组件不用上报
        if (YDocCoverDataManager.needShowDoc(coverComponentsManager, curSoundInfo)) {
            return
        }

        XMTraceApi.Trace()
            .setMetaId(49046)
            .setServiceId("others")
            .put("currAlbumId", "$curAlbumId")
            .put("currTrackId", "$curTrackId")
            .put("duration", "${(duration.toDouble() / 1000).roundToLong()}") // 按 s 计数
            .put("docType", "图片")
            .put("subType", "")
            .createTrace()
        Logger.d(TAG, "49046--gra--curTrackId--$curTrackId----docType--${getDocType(curSoundInfo)}---subtype---${if (YDocCoverDataManager.needShowDoc(coverComponentsManager, curSoundInfo)) getSubType() else ""}")

        mLastShowDocTime = 0L
    }

    private fun getDocType(soundInfo: PlayingSoundInfo?): String {
        return "${if (YDocCoverDataManager.needShowDoc(coverComponentsManager, soundInfo)) "图文" else "图片"}"
    }

    private fun getSubType(): String? {
        val soundInfo = PlayPageDataManager.getInstance().soundInfo
        if (soundInfo?.docInfo == null) {
            return "ASR"
        }
        val docType = soundInfo.docInfo!!.docType
        return if (docType == PlayingSoundInfo.DocInfo.TYPE_LRC) {
            "字幕"
        } else if (docType == PlayingSoundInfo.DocInfo.TYPE_TTS) {
            "TTS"
        } else if (docType == PlayingSoundInfo.DocInfo.TYPE_AI_DOC) {
            "ASR"
        } else {
            "ASR"
        }
    }

    private fun isCoverViewVisible(): Boolean {
        if (mOneToOneIv == null || mSixCoverIv == null) {
            return false
        }

        var curShowIv: View? = null
        if (mOneToOneIv!!.visibility == View.VISIBLE) {
            curShowIv = mOneToOneIv
        } else if (mSixCoverIv!!.visibility == View.VISIBLE) {
            curShowIv = mSixCoverIv
        }

        if (curShowIv == null) {
            return false
        }

        val positions = IntArray(2)
        curShowIv.getLocationOnScreen(positions)

        if (positions[1] == 0) {
            return false
        }
        val titleBarHeight = coverComponentsManager?.playContainer?.getTitleBarPositionY() ?: 0.dp
        return positions[1] + curShowIv.height >= titleBarHeight

    }

    private fun setLastShowDocTime() {
        if (mLastShowDocTime == 0L && isVisible) {
            mLastShowDocTime = System.currentTimeMillis()
        }
    }

    override fun onScreenWidthChanged(config: Configuration) {
        updateIvAspectRatioInFoldDevice(config)
    }

    private fun updateIvAspectRatioInFoldDevice(config: Configuration?) {
        if (config == null) {
            return
        }
        if (!BaseUtil.isFoldScreen(BaseApplication.getMyApplicationContext())) {
            return
        }

        if (mOneToOneIv == null || mSixCoverIv == null) {
            return
        }

        val screenWidth = BaseUtil.dp2px(context, config.screenWidthDp.toFloat())
        if (mContentView?.layoutParams is MarginLayoutParams) {
            mContentView?.layoutParams?.width = screenWidth
            mContentView?.requestLayout()
        }
        if (mOneToOneIv?.layoutParams is ConstraintLayout.LayoutParams) {
            val lp = mOneToOneIv?.layoutParams as ConstraintLayout.LayoutParams
            lp.width = screenWidth
            lp.height = BaseUtil.dp2pxWithOrigDensity(mContext, 375).toInt()
            lp.dimensionRatio = null
            lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
            lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            mOneToOneIv?.layoutParams = lp
            mOneToOneIv?.setFoldScreenBitmapMatrix(screenWidth)
        }

        if (mSixCoverIv?.layoutParams is ConstraintLayout.LayoutParams) {
            val lp = mSixCoverIv?.layoutParams as ConstraintLayout.LayoutParams
            lp.width = screenWidth
            lp.height = BaseUtil.dp2pxWithOrigDensity(mContext, 211).toInt()
            lp.dimensionRatio = null
            lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
            lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            mSixCoverIv?.layoutParams = lp
            mSixCoverIv?.setFoldScreenBitmapMatrix(screenWidth)
        }

        if (mBackgroundIv?.layoutParams is ConstraintLayout.LayoutParams) {
            val lp = mBackgroundIv?.layoutParams as ConstraintLayout.LayoutParams
            lp.width = screenWidth
            lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
            lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
            lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
            mBackgroundIv?.layoutParams = lp
            mBackgroundIv?.setFoldScreenBitmapMatrix(screenWidth)
        }
        updateImageViewMaskStyle()
    }

    private fun isFoldDeviceOnExpandMode(context: Context, configuration: Configuration? = null): Boolean {
        return BaseUtil.isCollapsibleScreenOnLandscapeMode(ToolUtil.getCtx())
                || BaseUtil.isCollapsibleScreenOnPortraitExpandMode(
            ToolUtil.getCtx(),
            configuration
        ) || 600f.dp <= BaseUtil.getScreenWidth(context)
    }
}