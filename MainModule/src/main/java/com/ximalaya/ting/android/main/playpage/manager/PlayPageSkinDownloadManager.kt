package com.ximalaya.ting.android.main.playpage.manager

import android.net.Uri
import android.util.Log
import androidx.annotation.WorkerThread
import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.download.DownloadLiteManager
import com.ximalaya.ting.android.framework.service.DownloadService
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.record.DownloadManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM.IRequestCallBack
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.hybridview.utils.MD5Tool
import com.ximalaya.ting.android.hybridview.utils.ZIPUtils
import com.ximalaya.ting.android.main.constant.MainUrlConstants
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.SimpleDownloadTask
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.storage.IStoragePathManager
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by mark on 2025/6/25 17:54
 */
object PlayPageSkinDownloadManager {
    const val TAG = "PlayPageSkinManager"
    private const val ERROR_CODE_UNZIP = "UNZIP_ERROR"
    private const val ERROR_CODE_DOWNLOAD = "DOWNLOAD_ERROR"
    private val FILE_DIR_PATH: String = (RouterServiceManager.getInstance()
        .getService(IStoragePathManager::class.java)?.innerStoragePath
        ?: DownloadService.getDiskCachePath(MainApplication.getMyApplicationContext())) + File.separator + "play_page_skin"

    @Volatile
    private var curSkinPackName: String? = null
    var currentPSkinConfig : PSkinConfig? = null
        private set
    @Volatile
    private var readyDecorateSkinName:String? = null

    @Volatile
    private var userSkinPrivilegePair: Pair<String,Boolean>? = null

    private var hasInitSkinPrivilegePair: AtomicBoolean = AtomicBoolean(false)

    private val notifyListenerSet = mutableSetOf<IOnSkinSelectedListener>()

    @JvmStatic
    fun isConfigOn() : Boolean {
        return ConfigureCenter.getInstance().getBool(
                CConstants.Group_android.GROUP_NAME,
                "playpage_skin_mode_switch",
                true
            ) || ToolUtil.getDebugSystemProperty("debug.playskin", "-1") == "1"
    }

    fun addOnSkinSelectedListener(listener: IOnSkinSelectedListener) {
        notifyListenerSet.add(listener)
    }

    fun removeOnSkinSelectedListener(listener: IOnSkinSelectedListener) {
        notifyListenerSet.remove(listener)
    }

    private fun notifyOnSkinSelected(skinName: String): Boolean {
        log(TAG, "notifyOnSkinSelected:$skinName readyDeocrateSkinName:$readyDecorateSkinName")
        if (readyDecorateSkinName == skinName) {
            curSkinPackName = skinName
            readyDecorateSkinName = null
            MMKVUtil.getInstance()
                .saveString(
                    PreferenceConstantsInMain.KEY_CURRENT_PLAYPAGE_SKIN_NAME,
                    skinName
                )
            HandlerManager.postOnUIThread {
                notifyListenerSet.forEach {
                    it.onSkinSelected(skinName)
                }
            }
            return true
        }
        return false
    }

    private fun getFileName(skinName:String):String{
        return MD5Tool.md5(skinName.toByteArray())
    }

    fun downloadAndSetPlayPageSkin(
        resourceName: String?,
        resourceUrl: String?,
        callback: DownloadLiteManager.DownloadCallback?
    ) {
        resourceName ?: return
        resourceUrl ?: return
        readyDecorateSkinName = resourceName
        if (checkSkinPackValid(resourceName)) {
            //  已经下载过，通知播放页设置当前播放器皮肤并持久化保存
            log(TAG, "SKIN:$resourceName has downloaded and unzipped")
            if (notifyOnSkinSelected(resourceName)) {
                callback?.onSuccess(resourceUrl)
            }
            return
        }
        val md5Name = getFileName(resourceName)
        val zipFileName = "$md5Name.zip"
        val unzipFileName = md5Name
        log(TAG, "startDownload,zipFileName:$zipFileName,unzipFileName:$unzipFileName")
        val exitZipfile = File(FILE_DIR_PATH + File.separator + zipFileName)
        if (exitZipfile.exists()) {
            try {
                exitZipfile.delete()
                log(TAG, "SKIN:$resourceName zip file delete success")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        val downloadTask = SimpleDownloadTask(
            ToolUtil.getCtx(),
            resourceUrl,
            FILE_DIR_PATH,
            zipFileName,
            object : SimpleDownloadTask.DownloadCallback {
                override fun onSuccess() {
                    //success,通知rn
                    val file = File(FILE_DIR_PATH + File.separator + zipFileName)
                    log(TAG, "SKIN:$resourceName download success, file:${file.absolutePath}")
                    if (file.exists()) {
                        //已经下载过，通知播放页设置当前播放器皮肤并持久化保存
                        log(TAG, "SKIN:$resourceName download success, start unzip")
                        ZIPUtils.unZip(
                            FILE_DIR_PATH + File.separator + zipFileName,
                            FILE_DIR_PATH + File.separator + unzipFileName
                        )
                        if (checkSkinPackValid(resourceName)) {
                            try {
                                file.delete()
                                log(TAG, "SKIN:$resourceName zip file delete success")
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            log(TAG, "SKIN:$resourceName download success, unzip success")
                            if (notifyOnSkinSelected(resourceName)) {
                                callback?.onSuccess(resourceUrl)
                            }
                        } else {
                            callback?.onError("$ERROR_CODE_UNZIP-$resourceName##$resourceUrl")
                        }
                    }
                    readyDecorateSkinName = null
                }

                override fun onFailed() {
                    //fail,通知rn
                    log(TAG, "SKIN:$resourceName download failed \n ${Log.getStackTraceString(Throwable())}")
                    callback?.onError("$ERROR_CODE_DOWNLOAD-$resourceName##$resourceUrl")
                    readyDecorateSkinName = null
                }

                override fun onProgress(progress: Int) {
                    //onProgress,通知rn
                    log(TAG, "SKIN:$resourceName download onProgress:$progress")
                    callback?.onProgressUpdate(resourceUrl, progress)
                }

            })
        DownloadManager.getInstance().download(downloadTask, true)
    }

    fun checkSkinPackValid(skinName: String): Boolean {
        val file = File(FILE_DIR_PATH + File.separator + getFileName(skinName))
        if (file.exists() && file.isDirectory && !file.list().isNullOrEmpty()) {
//            getConfigJson(skinName)
            return true
        }
        return false
    }

//    fun getCurPlayPageSkinFileDirPath(): String? {
//        val skinName = getCurPlayPageSkinName()
//        if (skinName.isNullOrEmpty()) {
//            return null
//        }
//        if (checkSkinPackValid(skinName)) {
//            curSkinPackName = skinName
//            return FILE_DIR_PATH + File.separator + getFileName(skinName)
//        }
//        return null
//    }

    fun clearSkin() {
        MMKVUtil.getInstance()
            .saveString(PreferenceConstantsInMain.KEY_CURRENT_PLAYPAGE_SKIN_NAME, "")
        curSkinPackName = null
        readyDecorateSkinName = null
    }

    fun getCurPlayPageSkinName(): String? {
        if (!UserInfoMannage.hasLogined()) {
            return null
        }
        if (!isConfigOn()) {
            return null
        }
        return if (curSkinPackName.isNullOrEmpty()) {
            MMKVUtil.getInstance()
                .getString(PreferenceConstantsInMain.KEY_CURRENT_PLAYPAGE_SKIN_NAME)
        } else {
            curSkinPackName
        }
    }

    fun checkCurPlaySkinUserHasPrivilege() {
        if (!UserInfoMannage.hasLogined()) {
            return
        }
        getCurPlayPageSkinName()?.let {
            val params = mutableMapOf<String, String>()
            params["decoratorCode"] = it
            MainCommonRequest.baseGetRequest(
                MainUrlConstants.getInstanse().getUserHasSkinPrivilegeUrl(),
                params,
                object : IDataCallBack<Boolean?> {
                    override fun onSuccess(data: Boolean?) {
                        updateUserSkinPrivilege(it,data)
                    }

                    override fun onError(code: Int, message: String?) {
                      updateUserSkinPrivilege(it,null)
                    }

                },
                object : IRequestCallBack<Boolean?> {
                    override fun success(content: String?): Boolean? {
                        var result: Boolean? = true
                        try {
                            val jsonObject = JSONObject(content)
                            if (jsonObject.has("data")) {
                                val tempResult = jsonObject.opt("data")
                                result = if (tempResult is Boolean) {
                                    tempResult
                                } else {
                                    null
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        return result
                    }
                })
        }
    }

    private fun updateUserSkinPrivilege(skinId: String, hasPrivilege: Boolean?) {
        if (hasPrivilege != null) {
            userSkinPrivilegePair = Pair(skinId, hasPrivilege)
            MMKVUtil.getInstance().saveString(
                PreferenceConstantsInMain.KEY_CURRENT_SKIN_USER_PRIVIELGE,
                "{\"skinId\":\"$skinId\",\"hasPrivilege\":${hasPrivilege}}"
            )
        } else {
            userSkinPrivilegePair = null
            MMKVUtil.getInstance().removeByKey(
                PreferenceConstantsInMain.KEY_CURRENT_SKIN_USER_PRIVIELGE
            )
        }
    }

    private fun getUserSkinPrivilege(): Pair<String, Boolean>? {
        if (userSkinPrivilegePair == null && hasInitSkinPrivilegePair.compareAndSet(false, true)) {
            val userSkinPrivilegePairStr = MMKVUtil.getInstance().getString(
                PreferenceConstantsInMain.KEY_CURRENT_SKIN_USER_PRIVIELGE
            )
            if (!userSkinPrivilegePairStr.isNullOrEmpty()) {
                try {
                    val jsonObject = JSONObject(userSkinPrivilegePairStr)
                    val skinId = jsonObject.optString("skinId")
                    val hasPrivilege = jsonObject.optBoolean("hasPrivilege")
                    if (!skinId.isNullOrEmpty()) {
                        userSkinPrivilegePair = Pair(skinId, hasPrivilege)
                    }
                } catch (e: Exception) {

                }
            }
        }
        return userSkinPrivilegePair
    }


    private fun log(tag: String, msg: String) {
        Logger.i(tag, msg)
    }

    interface IOnSkinSelectedListener {
        fun onSkinSelected(skinName: String)
    }

    @WorkerThread
    private fun getConfigJson(skinName: String): SkinRawConfig? {
        val file = File(FILE_DIR_PATH + File.separator + getFileName(skinName))
        if (file.exists() && file.isDirectory && file.list()?.contains("list.json") == true) {
            val jsonFile = File(file.absolutePath + File.separator + "list.json")
            runCatching {
                val jsonString = jsonFile.readText()
                log(TAG, "getConfigJson: $jsonString")
                return GsonUtils.parseJson(jsonString, SkinRawConfig::class.java)
            }.onFailure {
                log(TAG, "getConfigJson failed: ${it.message}")
            }
        } else {
            log(TAG, "getConfigJson: skin directory does not exist or is empty for skinName: $skinName")
        }
        return null
    }

    /**
     * 获取包含完整文件路径的皮肤配置
     * @return PSkinConfig
     */
    @WorkerThread
    fun getPSkinConfig(): PSkinConfig? {
        val skinName = getCurPlayPageSkinName()
        if (skinName.isNullOrEmpty()) {
            Logger.w(TAG, "getPSkinConfig: skinName is null or empty")
            return null
        }

        val skinUserHasPrivilege = getUserSkinPrivilege()
        if (skinUserHasPrivilege != null && skinUserHasPrivilege.first == skinName && !skinUserHasPrivilege.second) {
            Logger.w(TAG, "getPSkinConfig: user do not has skin($skinName) privilege")
            return null
        }

        val rawConfigJson = getConfigJson(skinName)
        if (rawConfigJson == null) {
            Logger.w(TAG, "getPSkinConfig: rawConfigJson is null for skinName: $skinName")
            return null
        }
        val path = FILE_DIR_PATH + File.separator + getFileName(skinName)
        
        val skinConfig = rawConfigJson.toPSkinConfig(skinName, path)
        if (isSkinFilesComplete(skinConfig)) {
            log(TAG, "getPSkinConfigWithFullPath: skin files are complete for $skinName")
            return skinConfig
        }
        log(TAG, "getPSkinConfigWithFullPath: skin files are incomplete for $skinName")
        return null
    }

    /**
     * 验证皮肤配置中的文件是否存在（基于 file URI）
     * @param PSkinConfig 包含 file URI 的皮肤配置对象
     * @return 包含验证结果的Map，key为文件属性名，value为文件是否存在
     */
    @WorkerThread
    private fun validateSkinFiles(PSkinConfig: PSkinConfig): Map<String, Boolean> {
        val result = mutableMapOf<String, Boolean>()
        
        // 验证文件类型的属性（排除颜色值属性）
        val fileProperties = listOf(
            "coverVagueImage" to PSkinConfig.coverVagueImage,
            "coverImage" to PSkinConfig.coverImage,
            "progressSlider" to PSkinConfig.progressSlider,
            "moveBackground" to PSkinConfig.moveBackground,
            "playButton" to PSkinConfig.playButton,
//            "cycleCoverBackground" to PSkinConfig.cycleCoverBackground
        )
        
        fileProperties.forEach { (propertyName, fileUri) ->
            try {
                val uri = Uri.parse(fileUri)
                val file = File(uri.path ?: "")
                val exists = file.exists() && file.isFile
                result[propertyName] = exists
                log(TAG, "validateSkinFiles: $propertyName -> $fileUri exists: $exists")
            } catch (e: Exception) {
                result[propertyName] = false
                log(TAG, "validateSkinFiles: invalid URI format: $fileUri, error: ${e.message}")
            }
        }
        
        return result
    }

    /**
     * 检查所有必需的皮肤文件是否完整存在（基于 file URI）
     * @param PSkinConfig 包含 file URI 的皮肤配置对象
     * @return true表示所有文件都存在，false表示有文件缺失
     */
    @WorkerThread
    private fun isSkinFilesComplete(PSkinConfig: PSkinConfig): Boolean {
        val validationResult = validateSkinFiles(PSkinConfig)
        val allFilesExist = validationResult.values.all { it }
        log(TAG, "isSkinFilesComplete: $allFilesExist, missing files: ${validationResult.filterValues { !it }.keys}")
        return allFilesExist
    }

    //{
//    "coverVagueImage": "coverVagueImage.jpeg",
//    "uiColor": "f4e1b8",
//    "coverImage": "coverImage.jpeg",
//    "progressSlider": "progressSlider.png",
//    "moveBackground": "moveBackground.mp4",
//    "maskLayerColor": "2b696b",
//    "playButton": "playButton.png"
//}

    private data class SkinRawConfig(
        @SerializedName("coverVagueImage") val coverVagueImage: String,
        @SerializedName("uiColor") val uiColor: String,
        @SerializedName("coverImage") val coverImage: String,
        @SerializedName("progressSlider") val progressSlider: String,
        @SerializedName("moveBackground") val moveBackground: String,
        @SerializedName("maskLayerColor") val maskLayerColor: String,
        @SerializedName("playButton") val playButton: String,
        @SerializedName("cycleCoverBackground") val cycleCoverBackground: String? = null
    ) {
        fun toPSkinConfig(skinName: String, basePath: String): PSkinConfig {
            return PSkinConfig(
                name = skinName,
                coverVagueImage = Uri.fromFile(File(basePath, coverVagueImage)).toString(),
                uiColorInt = parseHexColor(uiColor),
                coverImage = Uri.fromFile(File(basePath, coverImage)).toString(),
                progressSlider = Uri.fromFile(File(basePath, progressSlider)).toString(),
                moveBackground = Uri.fromFile(File(basePath, moveBackground)).toString(),
                maskLayerColorInt = parseHexColor(maskLayerColor),
                playButton = Uri.fromFile(File(basePath, playButton)).toString(),
                cycleCoverBackground = cycleCoverBackground?.let { Uri.fromFile(File(basePath, it)).toString() }
            )
        }
        
        /**
         * 将 hex 颜色字符串转换为 int 值
         * 支持格式：
         * - "FFAABB" (6位，自动添加 alpha FF)
         * - "AABBCCDD" (8位，包含 alpha)
         * @param hexColor hex 颜色字符串
         * @return 颜色的 int 值，解析失败返回 0
         */
        private fun parseHexColor(hexColor: String): Int {
            return try {
                val cleanColor = hexColor.replace("#", "").uppercase()
                when (cleanColor.length) {
                    6 -> {
                        // 6位色值，添加 FF alpha
                        val colorInt = cleanColor.toLong(16)
                        (0xFF000000L or colorInt).toInt()
                    }
                    8 -> {
                        // 8位色值，包含 alpha
                        cleanColor.toLong(16).toInt()
                    }
                    else -> {
                        // 格式不正确，返回默认值
                        0
                    }
                }
            } catch (e: Exception) {
                // 解析失败，返回默认值
                0
            }
        }
    }
}