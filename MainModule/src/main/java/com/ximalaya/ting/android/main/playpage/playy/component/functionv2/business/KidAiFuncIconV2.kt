package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.business

import android.content.Context
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.aiSound.KidAiSoundDialogXNew
import com.ximalaya.ting.android.main.playpage.playy.component.function.FunctionTraceUtil
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil

/**
 * Created on 2024/5/30.
 * <AUTHOR>
 * @email <EMAIL>
 */
class KidAiFuncIconV2(private val fragment2: BaseFragment2?) : IBusinessView(), View.OnClickListener {
    private var soundInfo: PlayingSoundInfo? = null

    override fun provideIcon(context: Context, soundInfo: PlayingSoundInfo): View? {
        return ImageView(context).apply {
            val drawableId = R.drawable.main_ic_function_ai_child_linear
            setImageResource(drawableId)
            layoutParams = FrameLayout.LayoutParams(26.dp, 26.dp)
            contentDescription = "亲子换声"
            alpha = 0.55f


            if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
                setColorFilter(PSkinManager.getBtnThemeColor())
            }

            setOnClickListener(this@KidAiFuncIconV2)
        }
    }

    override fun shouldDisplay(soundInfo: PlayingSoundInfo, isAudio: Boolean): Boolean {
        this.soundInfo = soundInfo
        if (!isAudio) return false
        return isAiChildTimbre(soundInfo)
    }

    override fun getBottomText(): String {
        return "AI换声"
    }

    override fun providerBottomView(context: Context, soundInfo: PlayingSoundInfo): BottomView? {
        return BottomView(
            TextView(context).apply {
                text = "AI换声"
                alpha = 0.4f
                textSize = 10f
                isSingleLine = true
            }
        )
    }

    override fun onCreate() {
    }

    override fun onDestroy() {
    }

    private fun isAiChildTimbre(soundInfo: PlayingSoundInfo?): Boolean {
        return soundInfo?.otherInfo?.showChildAiTimbre == true
    }

    private fun showKidAiSoundDialog() {
        val dialog = KidAiSoundDialogXNew.newInstance()
        fragment2?.childFragmentManager?.let {
            dialog.show(it, KidAiSoundDialogXNew::class.java.simpleName)
            if (soundInfo?.otherInfo?.showChildAiTimbre == true) {
                MMKVUtil.getInstance()
                    .saveBoolean(PreferenceConstantsInHost.KEY_IS_CLICK_AI_SOUND_ENTRY, true)
            }
        }
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        showKidAiSoundDialog()
        FunctionTraceUtil.traceClick49674(
            soundInfo?.trackInfo?.trackId,
            soundInfo?.trackInfo?.albumId,
            "亲子换声",
            "亲子换声",
            true
        )
        FunctionTraceUtil.traceClick47131(
            soundInfo?.trackInfo?.trackId,
            soundInfo?.trackInfo?.albumId,
            "波波音色", soundInfo?.trackInfo?.categoryId.toString()
        )
    }
}