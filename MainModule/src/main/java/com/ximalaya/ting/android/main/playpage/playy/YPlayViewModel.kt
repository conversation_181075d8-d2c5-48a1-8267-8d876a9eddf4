package com.ximalaya.ting.android.main.playpage.playy

import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ximalaya.ting.android.host.fragment.play.data.Theme
import com.ximalaya.ting.android.host.util.view.PaletteCache
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.YDomainColorUtil
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class YPlayViewModel: ViewModel() {
    private val TAG = "YPlayViewModel"
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var coverJob: Job? = null


    var coverTheme: Theme? = null
    var docTheme: Theme? = null

    var trackId: Long = 0L
        set(value) {
            if (field != value) {
                val lastValue = field
                field = value

                coverTheme = null
                docTheme = null
            }
        }

    val coverMainColor = object : MutableLiveData<Int>(PlayPageDataManager.DEFAULT_BACKGROUND_COLOR) {
        override fun setValue(value: Int?) {
            if (value != null) {//&& value != getValue()
//                val domainColor = if (PSkinManager.isEnabled && PSkinManager.getThemeColor() != Color.TRANSPARENT)
//                    PSkinManager.getThemeColor() else YDomainColorUtil.getColorFromMainColor(value)
                val domainColor = YDomainColorUtil.getColorFromMainColor(value)
                coverTheme = Theme(domainColor, domainColor, domainColor, domainColor, domainColor, value)

                if (!PSkinManager.isEnabled) {
                    super.setValue(value)
                }
            }
        }
    }

    val aiGMainColor = object : MutableLiveData<Int>(PlayPageDataManager.DEFAULT_BACKGROUND_COLOR) {
        override fun setValue(value: Int?) {
            if (value != null && value != PlayPageDataManager.DEFAULT_BACKGROUND_COLOR) {//value != getValue()
//                val domainColor = if (PSkinManager.isEnabled && PSkinManager.getThemeColor() != Color.TRANSPARENT)
//                    PSkinManager.getThemeColor() else YDomainColorUtil.getColorFromMainColor(value)
                val domainColor = YDomainColorUtil.getColorFromMainColor(value)
                docTheme = Theme(domainColor, domainColor, domainColor, domainColor, domainColor, value)
                if (!PSkinManager.isEnabled) {
                    super.setValue(value)
                }
            }
        }
    }

    fun updateCoverDomainColor(url: String?, coverBitmap: Bitmap?) {
        coverJob?.cancel()

//        if (PSkinManager.isEnabled) {
//            // If skin is enabled, use the skin's theme color
//            coverMainColor.value = PSkinManager.currentSkinConfig?.baseConfig?.themeColor
//                ?: PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
//            return
//        }

        if (coverBitmap == null) {
            coverMainColor.value = PlayPageDataManager.DEFAULT_BACKGROUND_COLOR
            return
        }

        val cacheColor = PaletteCache.get(url)
        if (cacheColor != null) {
            coverMainColor.value = cacheColor
            return
        }

        coverJob = scope.launch {
            val mainColor = YDomainColorUtil.getDomainOriginalColorAsync(coverBitmap, url)
            coverMainColor.value = mainColor
        }
    }

    override fun onCleared() {
        super.onCleared()
        scope.cancel()
    }
}