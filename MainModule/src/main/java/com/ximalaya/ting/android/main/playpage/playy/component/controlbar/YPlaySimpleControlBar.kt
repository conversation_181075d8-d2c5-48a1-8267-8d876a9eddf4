package com.ximalaya.ting.android.main.playpage.playy.component.controlbar

import android.Manifest
import android.annotation.SuppressLint
import android.app.Service
import android.content.res.ColorStateList
import android.graphics.*
import android.graphics.drawable.*
import android.view.Gravity
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.text.TextUtils
import android.util.Log
import android.view.*
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.util.forEach
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModelProvider
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.model.KeyPath
import com.airbnb.lottie.value.LottieValueCallback
import com.tmall.wireless.vaf.virtualview.Helper.StringUtils.getFriendlyNumberTime
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.AudioInfoTraceUtil
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction
import com.ximalaya.ting.android.host.manager.play.quality.TrackPlayQualityKt
import com.ximalaya.ting.android.host.manager.play.quality.TrackQualityChangeListener
import com.ximalaya.ting.android.host.manager.play.soundEffect.TrackPlaySoundEffectManager
import com.ximalaya.ting.android.host.manager.play.timelinecard.ITimelineCardDataCallBack
import com.ximalaya.ting.android.host.manager.play.timelinecard.PlayTimelineCardManager
import com.ximalaya.ting.android.host.manager.play.timelinecard.PlayTimelineUtil
import com.ximalaya.ting.android.host.manager.soundpatch.SoundPatchController
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData
import com.ximalaya.ting.android.host.model.play.PlayTimelineCard
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.VibratorUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.host.util.ui.AnimationUtil
import com.ximalaya.ting.android.host.view.CustomTipsView
import com.ximalaya.ting.android.host.view.ScaleableSeekBar
import com.ximalaya.ting.android.host.view.XmLottieDrawable
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.kachamodule.fragment.kachanote.KachaNoteDetailContainerFragment
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayUtil
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseComponentWithPlayStatusListener
import com.ximalaya.ting.android.main.playpage.internalservice.*
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager
import com.ximalaya.ting.android.main.playpage.playx.XPlayViewModel
import com.ximalaya.ting.android.main.playpage.playy.*
import com.ximalaya.ting.android.main.playpage.playy.component.function.FunctionTraceUtil
import com.ximalaya.ting.android.main.playpage.playy.component.sound.quality.YSoundQualityTips
import com.ximalaya.ting.android.main.playpage.playy.component.tips.v2.TipPriority
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew
import com.ximalaya.ting.android.main.playpage.playy.listener.UnableToPlayStatusAggregator
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.main.playpage.playy.skin.SkinChangeListener
import com.ximalaya.ting.android.main.playpage.playy.view.ScaleTouchListener
import com.ximalaya.ting.android.main.playpage.util.PlayTtsUtil
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils
import com.ximalaya.ting.android.main.transition.TransitionWithOverLay
import com.ximalaya.ting.android.main.util.LockScreenGuidePushUtil.showLockScreenGuidePush
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.opensdk.player.soundEffect.SoundEffectPlayerConstant
import com.ximalaya.ting.android.opensdk.player.soundpatch.ICommercialSoundPatchControlStatusCallBack
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.io.File
import java.lang.ref.WeakReference
import java.util.*

/**
 * Created by yang.zhang on 2023-03-08.
 */
open class YPlaySimpleControlBar(
    private val playContainer: IPlayContainer,
    private val onTouchProgress: (Boolean) -> Unit = {}
) : BaseComponentWithPlayStatusListener(),
    IXmAdsStatusListener, UnableToPlayListener,
    ISeekBarComponentService,
    ICommercialSoundPatchControlStatusCallBack,
    SkinChangeListener,
    TrackQualityChangeListener, TrackPlaySoundEffectManager.ISoundEffectStatusCallBack {

        private val TAG = "YPlaySimpleControlBar"

    enum class KeyPointBiz(val index: Int) {
        INVALID(0),
        PODCAST(1),
        TIMELINE(2),
        KACHA(3)
    }

    private var keyPointBiz: KeyPointBiz? = null

    private var mYPlayControlBar: YPlayControlBar? = null

    var mVgPlayBtn: ViewGroup? = null
    protected var mIvPlayBtnCenterIcon: ImageView? = null
    protected var mIvPlayBtnBg: ImageView? = null
    protected var mVLoadingStatus: View? = null
    private var mTvProgress: TextView? = null
    private var mTvDuration: TextView? = null
    private var mTvSoundQuality: TextView? = null

    private var mPlayToPauseLottieDrawable: XmLottieDrawable? = null
    private var mPauseToPlayLottieDrawable: XmLottieDrawable? = null
    private var targetSeekPosition = -1

    var seekBarGroup: ViewGroup? = null
        private set
    protected var mSeekBar: ScaleableSeekBar? = null
    protected var shouldHideSeekbar: Boolean = false

    private var minorData: PlayPageMinorData? = null
    private var mIsMarkTipsViewShowing = false
    private var mMarkTipsView: CustomTipsView? = null

    private var canShowKacha = false
    var isDocScrolledFull = false // 文稿是否是阅读模式，即文稿全屏

    private var isLastPPT = false
    private var viewModel: XPlayViewModel? = null
    private var mSoundEffectDialogReferenceNew: WeakReference<ChooseTrackSoundEffectAiDialogXNew?>? = null


    open fun init(vAncestorView: ViewGroup) {
//        mContentView = vAncestorView.findViewById(R.id.main_play_simple_control)
//        initUi()
    }

    open fun initUi() {
        if (mFragment != null) {
            viewModel = ViewModelProvider(mFragment.requireActivity()).get(XPlayViewModel::class.java)
        }
        TrackPlayQualityKt.addListener(this)
        seekBarGroup = findViewById(R.id.main_vg_seek_bar)
        mSeekBar = findViewById(R.id.main_seek_bar)
        mSeekBar?.enableScaleAnimation = true

        mTvProgress = findViewById<TextView>(R.id.main_tv_progress)
        mTvDuration = findViewById<TextView>(R.id.main_tv_duration)
        mTvSoundQuality = findViewById<TextView>(R.id.main_tv_sound_quality)
        mTvSoundQuality?.post {
            val quality = mTvSoundQuality
            val parent = mContentView
            if (quality != null && parent != null) {
                val rect = TransitionWithOverLay.getTargetRect(quality, (parent) as ViewGroup)
                ((parent) as ViewGroup).touchDelegate = TouchDelegate(Rect(
                    rect.left.toInt() - 6.dp,
                    rect.top.toInt() - 2.dp,
                    rect.right.toInt() + 6.dp,
                    rect.bottom.toInt() + 16.dp,
                ), quality)
            }
        }

        mTvSoundQuality?.setOnClickListener {
            // 新声音播放页-音质显示条  点击事件
            XMTraceApi.Trace()
                .click(64344) // 用户点击时上报
                .put("currPage", "newPlay")
                .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .put("currAlbumId", curAlbumId.toString())
                .put("currTrackId", curTrackId.toString())
                .put("Item", mTvSoundQuality?.text?.toString()?: "")
                .createTrace()

            if (mTvSoundQuality?.visibility == View.VISIBLE
                && !XPlayCommercialRelatedUtils.checkToShowSHQPrivilegeFloatDialog(
                    curSoundInfo,
                    mFragment,
                    false
                )
            ) {
                showSoundEffectDialog()
                traceAiOrEffectClick(mTvSoundQuality)
                FunctionTraceUtil.traceClick49674(curTrackId, curAlbumId, "音效", "", true)
            }
        }

        setSeekBarMax(100)
        if (XmPlayerManager.getInstance(context).isPlaying) {
            setCanSeek(true)
        }
        mSeekBar?.setOnSeekBarChangeListener(mOnSeekBarChangeListener)


        PlayTimelineCardManager.setDataCallBack(object : ITimelineCardDataCallBack {
            override fun onTimelineCardCallBack(list: List<PlayTimelineCard>?) {
                if (keyPointBiz == KeyPointBiz.TIMELINE || keyPointBiz == KeyPointBiz.INVALID) {
                    keyPointBiz = KeyPointBiz.TIMELINE
                    mSeekBar?.clearPlayTimelineCardPoints()
                    mSeekBar?.setPlayTimelineCardPoints(filterTimelineKeyPoints(list))
                }
            }
        })
        if (keyPointBiz == KeyPointBiz.TIMELINE) {
            mSeekBar?.clearPlayTimelineCardPoints()
            mSeekBar?.setPlayTimelineCardPoints(filterTimelineKeyPoints(PlayTimelineCardManager.getAllTimelineCards()))
        }

        mSeekBar?.setOnScaleStateChangeListener(object : ScaleableSeekBar.IOnScaleStateChangeListener {
            override fun onScaleStateChanged(scaled: Boolean) {
                handleOnScaleStateChanged(scaled)
                if (playContainer.isAudioMode()) {
                    PlayPageInternalServiceManager.getInstance()
                        .getService(ISeekBarScaleStateListener::class.java)
                        ?.onSeekBarScaleStateChange(scaled)
                }
            }
        })

        mSeekBar?.setOnUserSeekListener(object : ScaleableSeekBar.IOnUserSeekListener {
            override fun onSeekTouchRelease(position: Int) {
                mYPlayControlBar?.updateFloatingCardView(null)
                PlayPageInternalServiceManager.getInstance()
                    .getService(ISeekBarUserTouchListener::class.java)?.onSeekTouchRelease(position)
            }

            override fun onSeekTouchProgress(position: Int) {
                checkToUpdateFloatingCard(position)
                PlayPageInternalServiceManager.getInstance()
                    .getService(ISeekBarUserTouchListener::class.java)
                    ?.onSeekTouchProgress(position)
            }
        })

        mVgPlayBtn = findViewById(R.id.main_vg_play_btn)
        mIvPlayBtnCenterIcon = findViewById(R.id.main_iv_play_btn_center_icon)
        mIvPlayBtnBg = findViewById(R.id.main_play_btn_bg)
        mVLoadingStatus = findViewById(R.id.main_iv_play_btn_loading)
        loadLottieResourcesForPlayBtnCenterIcon()
        setOnClickListenerAndBindAutoTraceData(mVgPlayBtn)
        mVgPlayBtn?.setAccessibilityDelegate(object : View.AccessibilityDelegate() {
            override fun onInitializeAccessibilityNodeInfo(host: View, info: AccessibilityNodeInfo) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                info.isSelected = false
            }
        })

        playContainer.addUnableToPlayListener(this)
        SoundPatchController.registerSoundPatchControlStatusCallBack(this)

        isLastPPT = isPPT()
        playContainer.getCoverManager()?.addCoverChangeListener {
            if (isLastPPT != isPPT()) {
                isLastPPT = !isLastPPT
                curSoundInfo = curSoundInfo
            }
        }
        TrackPlaySoundEffectManager.getInstance()
            .registerStatusListener(this)
    }

    private val rgbMask = 0x00ffffff

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            val skinColor = PSkinManager.getBtnThemeColor()

            val backgroundColorWithAlpha = 0x14000000.toInt() or (skinColor and rgbMask)
            val secondaryProgressColorWithAlpha = 0x66000000.toInt() or (skinColor and rgbMask)
            val progressColorWithAlpha = 0xcc000000.toInt() or (skinColor and rgbMask)

            mSeekBar?.backgroundTintList = ColorStateList.valueOf(backgroundColorWithAlpha)
            mSeekBar?.secondaryProgressTintList = ColorStateList.valueOf(secondaryProgressColorWithAlpha)
            mSeekBar?.progressTintList = ColorStateList.valueOf(progressColorWithAlpha)
            mSeekBar?.updatePointColor(skinColor)
            setPlayBtnCenterIconColor(PSkinManager.getBtnThemeColor())
            val seekThumb = PSkinManager.getSeekThumbUrl()
            if (!seekThumb.isNullOrBlank()) {
                ImageManager.from(mContext).downloadBitmap(seekThumb, object : ImageManager.DisplayCallback {
                    override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
                        if (bitmap != null) {
                            mSeekBar?.setBigThumbDrawable(BitmapDrawable(bitmap))
                        }
                    }
                })
            }

            val playBtnUrl = PSkinManager.getPlayBtnUrl()
            if (!TextUtils.isEmpty(playBtnUrl)) {
                ImageManager.from(mContext).downloadBitmap(playBtnUrl, object : ImageManager.DisplayCallback {
                    override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
                        if (bitmap != null) {
                            mIvPlayBtnBg?.setImageBitmap(bitmap)
                        }
                    }
                })

                mIvPlayBtnBg?.updateLayoutParams<ViewGroup.LayoutParams> {
                    width = 72.dp
                    height = 72.dp
                }
            }


            mTvProgress?.setTextColor(PSkinManager.getBtnThemeColor())
            mTvDuration?.setTextColor(PSkinManager.getBtnThemeColor())
            mTvSoundQuality?.setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            val skinColor = Color.WHITE
            val backgroundColorWithAlpha = 0x14000000.toInt() or (skinColor and rgbMask)
            val secondaryProgressColorWithAlpha = 0x66000000.toInt() or (skinColor and rgbMask)
            val progressColorWithAlpha = 0xcc000000.toInt() or (skinColor and rgbMask)

            mSeekBar?.backgroundTintList = ColorStateList.valueOf(backgroundColorWithAlpha)
            mSeekBar?.secondaryProgressTintList = ColorStateList.valueOf(secondaryProgressColorWithAlpha)
            mSeekBar?.progressTintList = ColorStateList.valueOf(progressColorWithAlpha)

            mSeekBar?.updatePointColor(skinColor)
            mSeekBar?.setBigThumbDrawable(null)

            clearPlayBtnCenterIconColor()
            mIvPlayBtnBg?.setImageResource(R.drawable.main_bg_round_d8d8d8)
            mIvPlayBtnBg?.updateLayoutParams<ViewGroup.LayoutParams> {
                width = 60.dp
                height = 60.dp
            }
            mTvProgress?.setTextColor(skinColor)
            mTvDuration?.setTextColor(skinColor)
            mTvSoundQuality?.setTextColor(skinColor)
        }
    }

    override fun onPause() {
        super.onPause()
        mMarkTipsView?.dismissTips()
    }

    open fun fullScreen(xPlayPageStatus: XPlayPageStatus) {
        mSeekBar?.cancelScale()
        enableKacha(xPlayPageStatus.isFullScreen)
    }

    open fun onPlayTypeChanged(isAudio: Boolean) {
        updateSoundQualityViewVisible(isAudio)
        updateQualityName()
        if (!isAudio) {
            enableSeekBar(true)
            enablePlayBtn(true)
        }
    }

    private var tipsJob: Job? = null

    private fun updateSoundQualityViewVisible(isAudio: Boolean) {
        if (!isAudio) {
            ViewStatusUtil.setVisible(View.GONE, mTvSoundQuality)
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, mTvSoundQuality)
        }
    }

    private fun isAudioMode(): Boolean {
        return playContainer.isAudioMode()
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        updateQualityName()

        val qualities = soundInfo?.trackInfo?.trackQualityList

        if (qualities.isNullOrEmpty() || playContainer.isVideoMode()) {
        } else {
            if (qualities.find { it.qualityLevel == Track.TRACK_QUALITY_NORMAL } != null) {
                tipsJob = playContainer.scope().launch {
                    YSoundQualityTips.tryShowQualityTips(context, playContainer)
                }
            }
        }
        updateSoundQualityViewVisible(playContainer.isAudioMode())

        if (mTvSoundQuality?.visibility == View.VISIBLE) {
            // 新声音播放页-音质显示条  控件曝光
            XMTraceApi.Trace()
                .setMetaId(64343)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newPlay")
                .put("Item", "${mTvSoundQuality?.text?:""}")
                .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .put(XmRequestIdManager.CONT_ID, (soundInfo?.trackInfo?.trackId?: 0).toString())
                .put(XmRequestIdManager.CONT_TYPE, "mTvSoundQuality")
                .put("currAlbumId", curAlbumId.toString())
                .put("currTrackId", curTrackId.toString())
                .createTrace()
        }
    }

    override fun onQualityChange(wifiLevel: Int, cellularLevel: Int) {
        updateQualityName()
    }

    private fun updateQualityName() {
        if (isEffectOn()) {
            val soundEffectId = TrackPlaySoundEffectManager.getInstance().trackSoundEffectId
            val effectName = TrackPlaySoundEffectManager.getInstance().getNameById(soundEffectId)
            mTvSoundQuality?.text = effectName
        } else {
            if (curSoundInfo?.trackInfo?.trackQualityList.isNullOrEmpty()) {
                mTvSoundQuality?.text = "音效"
            } else {
                isWifi = NetworkUtils.getNetType(mContext) == NetworkUtils.NETWORK_TYPE_WIFI
                mTvSoundQuality?.text = TrackPlayQualityKt.getQualityNameWithDowngrading(curSoundInfo?.trackInfo?.trackQualityList, isWifi)
            }
        }
    }

    private fun isEffectOn(): Boolean {
        return TrackPlaySoundEffectManager.getInstance().currentStatus == SoundEffectPlayerConstant.STATUS_ON
    }

    fun dismissTips() {
        mMarkTipsView?.dismissTips()
    }

    private fun enableKacha(enable: Boolean) {
        canShowKacha = enable
        if (keyPointBiz == KeyPointBiz.KACHA || keyPointBiz == KeyPointBiz.INVALID) {
            if (!canShowKacha) {
                dismissTips()
                mSeekBar?.clearKeyPoints()
            } else {
                showKachaTrackMarks(curSoundInfo)
            }
        }
    }

    fun onMinorDataLoaded(data: PlayPageMinorData) {
        minorData = data
        if (canShowKacha && (keyPointBiz == KeyPointBiz.KACHA || keyPointBiz == KeyPointBiz.INVALID)) {
            showKachaTrackMarks(curSoundInfo)
        }
    }

    private fun vibrateForPodcast() {
        val seekBar = mSeekBar?: return
        if (keyPointBiz == KeyPointBiz.PODCAST) {
            val cprogress = seekBar.progress
            val max = seekBar.max
            if (cprogress < 0 || max <= 0) return
            val fProgress = (seekBar.width * cprogress.toFloat() / max).toInt()
            var minDist = -1
            mSeekBar?.getKeyPoints()?.forEach { key, value ->
                val dist = Math.abs(value.position - fProgress)
                if (minDist < 0) {
                    minDist = dist
                } else if (dist < minDist) {
                    minDist = dist
                }
            }
            if (minDist < 10) {
                mutableMapOf<String, Int>().apply {
                    put(Manifest.permission.VIBRATE, -1)
                }.let {
                    mFragment?.checkPermission(it, object : IMainFunctionAction.IPermissionListener {
                        override fun havedPermissionOrUseAgree() {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                (mContext.getSystemService(Service.VIBRATOR_SERVICE) as? Vibrator)
                                    ?.vibrate(VibrationEffect.createOneShot(20, 40))
                            } else {
                                VibratorUtil.vibrate(mContext, 20)
                            }
                        }

                        override fun userReject(noRejectPermiss: MutableMap<String, Int>?) {
                        }
                    })
                }
            }
        }
    }

    private fun checkMarkTipsShow(currPos: Int, offset: Int, duration: Int) {
        if (canShowKacha && (keyPointBiz == KeyPointBiz.KACHA || keyPointBiz == KeyPointBiz.INVALID)) {
            val trackMarks = minorData?.trackMarks
            if (mIsMarkTipsViewShowing || trackMarks.isNullOrEmpty() || isDocScrolledFull) {
                return
            }
            for (model in trackMarks) {
                if (Math.abs(model.markTime * 1000 - currPos) <= offset) {
                    showMarkTipsView(model, duration, -1, false)
                    break
                }
            }
        }

    }

    private fun showMarkTipsView(model: PlayingSoundInfo.TrackMarkModel, duration: Int, offset: Int, demo: Boolean) {
        var offset = offset
        if (duration <= 0 || model.markTime < 0) {
            return
        }
        val seekBar = mSeekBar?: return
        mIsMarkTipsViewShowing = true
        if (offset == -1) {
            val seekBarLocation = IntArray(2)
            seekBar.getLocationOnScreen(seekBarLocation)
            val width = seekBar.width - seekBar.paddingStart - seekBar.paddingEnd
            val base = (width * (model.markTime * 1000f / duration)).toInt()
            offset = base + seekBarLocation[0] + seekBar.paddingStart + seekBar.mKeyPointRadius.toInt()
        }
        val tipsMap: MutableList<CustomTipsView.Tips> = ArrayList(1)
        val progressStr = TimeHelper.toTime(model.markTime.toDouble())
        val tips = CustomTipsView.Tips.Builder(
            progressStr, mSeekBar,
            "tips_view_sound_note_play_tip_new"
        )
            .setDirection(CustomTipsView.UP)
            .setOffset(BaseUtil.dp2px(context, -5f))
            .setAutoSaveKeyToSp(false)
            .setOffsetX(offset)
            .setWillRemoveOnShowed(false)
            .setDelay(5000)
            .setDismissCallback {
                mIsMarkTipsViewShowing = false
                if (demo) {
                    mSeekBar?.removeKeyPoint(model.markId)
                }
            }
            .create()
        createIfNotExistMarkTipsView()
        mMarkTipsView?.getContentView()?.setTag(model.markId)
        tipsMap.add(tips)
        mMarkTipsView?.apply {
            dismissTips()
            setTipsMap(tipsMap)
            showAllTips()
        }
    }

    private fun createIfNotExistMarkTipsView() {
        val view = LayoutInflater.from(mContext).inflate(R.layout.main_view_kacha_note_tip, null)
        mMarkTipsView = CustomTipsView(activity, CustomTipsView.THEME_DARK, view, true)
        view.setOnClickListener { v: View? ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            if (mMarkTipsView?.getContentView()?.getTag() is Long) {
                val trackId = curTrackId
                if (trackId <= 0) {
                    return@setOnClickListener
                }
                val id = mMarkTipsView?.getContentView()?.getTag() as Long
                XmPlayerManager.getInstance(mContext).pause(PauseReason.Business.TrackMarkProgress)
                val detailFrag = KachaNoteDetailContainerFragment.newInstance(id)
                detailFrag.setCallbackFinish { cls, fid, params ->
                    if (trackId == curTrackId && cls == KachaNoteDetailContainerFragment::class.java && params != null && params.size == 2 && params[1] is Int && params[1] as Int == 1 && params[0] is Long && params[0] as Long == id
                    ) {
                        val trackMarks = minorData?.trackMarks
                        if (!trackMarks.isNullOrEmpty()) {
                            for (model in trackMarks) {
                                if (model != null && model.markId == id) {
                                    trackMarks.remove(model)
                                    mSeekBar?.removeKeyPoint(model.markId)
                                    mMarkTipsView?.dismissTips()
                                    break
                                }
                            }
                        }
                    }
                }
                startFragment(detailFrag)
            }
        }
    }

    override fun onTrackMarksChanged() {
        minorData = PlayPageMinorDataManager.getInstance().playPageMinorData
        showKachaTrackMarks(curSoundInfo)
    }

    override fun getSeekBarTop() = 0

    override fun setImmerskinShow(isShow: Boolean) {

    }

    override fun setImmersiveBgShow(isShow: Boolean) {

    }

    override fun addFloatingProgressVisibilityListener(listener: ISeekBarComponentService.OnFloatingProgressVisibilityChanged?) {

    }

    override fun removeFloatingProgressVisibilityListener(listener: ISeekBarComponentService.OnFloatingProgressVisibilityChanged?) {

    }

    private fun showKachaTrackMarks(soundInfo: PlayingSoundInfo?) {
        if (keyPointBiz == KeyPointBiz.KACHA || keyPointBiz == KeyPointBiz.INVALID) {
            mSeekBar?.clearKeyPoints()
            if (soundInfo != null && minorData?.trackMarks.isNullOrEmpty()) {
                keyPointBiz = KeyPointBiz.KACHA
                val duration: Int = getSoundDuration(soundInfo)
                if (duration == 0) return
                val points = minorData?.trackMarks?.map {
                    ScaleableSeekBar.KeyPoint().apply {
                        id = it.markId
                        progress = it.markTime * 1000 / duration.toFloat()
                    }
                }?: return
                mSeekBar?.setKeyPoints(points)
            }
        }
    }

    private fun getSoundDuration(soundInfo: PlayingSoundInfo): Int {
        val timeInPlayer = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).duration
        if (timeInPlayer > 0) {
            return timeInPlayer
        }
        return if (soundInfo.trackInfo == null) {
            0
        } else (soundInfo.trackInfo?.duration?:0) * 1000
    }


    private fun isPPT() = false

    override fun setCurSoundInfo(soundInfo: PlayingSoundInfo?) {
        super.setCurSoundInfo(soundInfo)
        mSeekBar?.clearPlayTimelineCardPoints()
        mSeekBar?.clearKeyPoints()

        val showNotes = soundInfo?.showNotes
        if (showNotes?.isNotEmpty() == true && !isPPT()) {
            keyPointBiz = KeyPointBiz.PODCAST
            var duration = (soundInfo.trackInfo?.duration ?: 0) * 1000
            if (duration == 0) {
                duration = XmPlayerManager.getInstance(mContext).duration
            }
            val points = showNotes.mapNotNull {
                if (it.startAt == 0L || duration - it.startAt < 1000) {
                    null
                } else {
                    ScaleableSeekBar.KeyPoint(id = it.id, progress = it.startAt.toFloat() / duration)
                }
            }
            mSeekBar?.setKeyPoints(points)
        } else {
            val points = filterTimelineKeyPoints(PlayTimelineCardManager.getAllTimelineCards())
            if (points.isEmpty()) {
                keyPointBiz = KeyPointBiz.INVALID
            } else {
                keyPointBiz = KeyPointBiz.TIMELINE
                mSeekBar?.setPlayTimelineCardPoints(points)
            }
        }
    }

    protected fun filterTimelineKeyPoints(timelineCards: List<PlayTimelineCard>?): List<ScaleableSeekBar.KeyPoint> {
        val keyPoints = mutableListOf<ScaleableSeekBar.KeyPoint>()
        if (timelineCards == null || timelineCards.isEmpty()) return keyPoints
        if (!PlayTimelineCardManager.isTrackMatched(curTrackId)) {
            return keyPoints
        }
        var duration = (curSoundInfo?.trackInfo?.duration ?: 0) * 1000
        if (duration == 0) {
            duration = XmPlayerManager.getInstance(mContext).duration
        }

        if (duration != 0) {
            for (card in timelineCards) {
                val p = ScaleableSeekBar.KeyPoint()
                p.id = PlayTimelineUtil.getThemeId(card)
                p.progress = card.startAt * 1f / duration
                keyPoints.add(p)
            }
        }
        return keyPoints
    }

    private fun checkToUpdateFloatingCard(position: Int) {
        val timelineCard = PlayTimelineCardManager.getAllTimelineCards().firstOrNull {
            it.startAt <= position && it.endAt >= position
        }
        mYPlayControlBar?.updateFloatingCardView(timelineCard)
    }

    private fun setCanSeek(canSeek: Boolean) {
        mSeekBar?.isCanSeek = canSeek
        if (!canSeek) {
            mYPlayControlBar?.hideFloatingProgress()
        }
    }

    private fun updateTimeTvUi(progress: Int, d: Int): String {
        var duration = d
        if (duration == 0) {
            duration = (curSoundInfo?.trackInfo?.duration ?:0) * 1000
        }
        val progressStr = TimeHelper.toTime((progress / 1000f).toDouble())
        val durationStr = TimeHelper.toTime((duration / 1000f).toDouble())
        mTvProgress?.text = progressStr
        mTvDuration?.text = durationStr

        return String.format(Locale.getDefault(), YPlayControlBar.TIME_FORMAT, progressStr, durationStr)
    }

    private fun handleOnScaleStateChanged(scaled: Boolean) {
        if (scaled) {
            if (playContainer.isVideoMode()) {
                mYPlayControlBar?.hideFunctionBar()
                playContainer.videoComponent()?.showFloatingProgress()
            } else {
                mYPlayControlBar?.showFloatingProgressView()
            }

            mTvProgress?.visibility = View.INVISIBLE
            mTvDuration?.visibility = View.INVISIBLE
            mTvSoundQuality?.visibility = View.GONE
        } else {
//            if (playContainer.isVideoMode()) {
//                mYPlayControlBar?.showFunctionBar()
//                playContainer.videoComponent()?.hideFloatingProgress()
//            } else {
//                mYPlayControlBar?.hideFloatingProgress()
//            }
            mYPlayControlBar?.showFunctionBar()
            playContainer.videoComponent()?.hideFloatingProgress()
            mYPlayControlBar?.hideFloatingProgress()

            mTvProgress?.visibility = View.VISIBLE
            mTvDuration?.visibility = View.VISIBLE
            updateSoundQualityViewVisible(!playContainer.isVideoMode())
        }
    }

    private val mOnSeekBarChangeListener: SeekBar.OnSeekBarChangeListener = object : SeekBar.OnSeekBarChangeListener {

        override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
            var duration = seekBar.max
            if (duration <= 0 || duration == 100) {
                duration = XmPlayerManager.getInstance(mContext).duration // ms
            }
            val timeStr = updateTimeTvUi(progress, duration)
            if (playContainer.isVideoMode()) {
                playContainer.videoComponent()?.updateFloatingProgress(progress, timeStr)
                return
            }

            mYPlayControlBar?.mFloatingProgressViewTimeText = timeStr
            mYPlayControlBar?.updateFloatingProgressTv()

            val docService = PlayPageInternalServiceManager.getInstance().getService(
                IDocOnCoverComponentService::class.java
            )
            if (fromUser) {
                docService?.onSeekBarProgressChanged(seekBar, progress)
            }
            val attachDocService = PlayPageInternalServiceManager.getInstance().getService(
                IAttachDocOnCoverComponentService::class.java
            )
            if (fromUser) {
                attachDocService?.onSeekBarProgressChanged(seekBar, progress)
            }
            val seekBarListener = PlayPageInternalServiceManager.getInstance().getService(
                ISeekBarTrackingTouchListener::class.java
            )
            seekBarListener?.onProgressChanged(seekBar, progress, fromUser)
            val service = PlayPageInternalServiceManager.getInstance().getService(
                ISeekBarDragListener::class.java
            )
            service?.onProgressChanged(seekBar, progress, fromUser)
        }

        override fun onStartTrackingTouch(seekBar: SeekBar) {
            if (playContainer.isVideoMode()) {
                return
            }
            mYPlayControlBar?.showFloatingProgressView()
            val docService = PlayPageInternalServiceManager.getInstance().getService(
                IDocOnCoverComponentService::class.java
            )
            docService?.onStartTrackingTouch()
            val seekBarListener = PlayPageInternalServiceManager.getInstance().getService(
                ISeekBarTrackingTouchListener::class.java
            )
            val attachDocService = PlayPageInternalServiceManager.getInstance().getService(
                IAttachDocOnCoverComponentService::class.java
            )
            attachDocService?.onStartTrackingTouch()
            if (seekBarListener != null && seekBar != null) {
                seekBarListener.onStartTrackingTouch(seekBar)
            }
            val service = PlayPageInternalServiceManager.getInstance().getService(
                ISeekBarDragListener::class.java
            )
            service?.onStartTrackingTouch(seekBar)
            onTouchProgress(true)
        }

        override fun onStopTrackingTouch(seekBar: SeekBar) {

            val targetSeekPosition = (seekBar.progress.toFloat() / seekBar.max * XmPlayerManager.getInstance(context).duration).toInt()
            val curPlayPosition = XmPlayerManager.getInstance(context).playCurrPositon
            Logger.d(TAG, "onStopTrackingTouch targetSeekPosition = $targetSeekPosition")
            handleOnStopTrackingTouch(targetSeekPosition)

            if (playContainer.isVideoMode()) {
                return
            }

            showLockScreenGuidePush(mFragment)
            val seekBarListener = PlayPageInternalServiceManager.getInstance().getService(
                ISeekBarTrackingTouchListener::class.java
            )
            if (seekBarListener != null && seekBar != null) {
                seekBarListener.onStopTrackingTouch(seekBar)
            }
            traceControlViewClick("进度条", curPlayPosition, targetSeekPosition)
            val service = PlayPageInternalServiceManager.getInstance().getService(
                ISeekBarDragListener::class.java
            )
            service?.onStopTrackingTouch(seekBar)

            val docService = PlayPageInternalServiceManager.getInstance().getService(
                IDocOnCoverComponentService::class.java
            )
            if (seekBar != null) {
                docService?.onStopTrackingTouch(seekBar!!.progress)
            }
            val attachDocService = PlayPageInternalServiceManager.getInstance().getService(
                IAttachDocOnCoverComponentService::class.java
            )
            if (seekBar != null) {
                attachDocService?.onStopTrackingTouch(seekBar!!.progress)
            }

            onTouchProgress(false)
        }
    }

    private fun setSeekBarMax(max: Int) {
        // 传进来的值大部分情况是从播放器取的，没有权限的情况下，播放器拿的duration是0，从接口信息取duration
        var max = max
        val soundInfo = curSoundInfo
        if (max == 0 && soundInfo != null && soundInfo.trackInfo != null) {
            max = soundInfo.trackInfo!!.duration * 1000
        }
        if (max == 0) {
            max = 100
        }
        mSeekBar?.max = max
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setOnClickListenerAndBindAutoTraceData(view: View?) {
        val clickView = view?: return
        clickView.setOnClickListener(mOnClickListener)
        if (clickView != mVgPlayBtn) {
            clickView.setOnTouchListener(ScaleTouchListener(clickView))
        }

        AutoTraceHelper.bindDataCallback(clickView, object : AutoTraceHelper.IDataProvider {
            override fun getData(): Any? {
                return curSoundInfo
            }

            override fun getModule(): Any? {
                return null
            }

            override fun getModuleType(): String? {
                return null
            }
        })
    }

    protected open fun handleOnPlayProgress(currPos: Int, duration: Int) {
        if (mSeekBar?.isCanSeek != true) {
            setCanSeek(true)
        }

//        if (targetSeekPosition >= 0) {
//            Logger.log("f_tag targetSeekPosition >= 0 ")
//            return
//        }
        if (canShowKacha && keyPointBiz == KeyPointBiz.KACHA) checkMarkTipsShow(currPos, 2000, duration)
        setSeekBarMax(duration)
        mSeekBar?.progress = currPos

        mYPlayControlBar?.updateFloatingProgressTv()
    }

    private fun handleOnStopTrackingTouch(seekPosition: Int) {
        mSeekBar?.progress = seekPosition
        if (playContainer.isVideoMode()) {
            playContainer.videoComponent()?.seekTo(seekPosition.toLong())
            return
        }
        AudioPlayUtil.seekTo(context, seekPosition)
        val soundInfo = curSoundInfo
        val trackInfo = soundInfo?.trackInfo
        XMTraceApi.Trace()
            .setMetaId(17632)
            .setServiceId("playDrog")
            .put("currPage", "newPlay")
            .put("trackId", (trackInfo?.trackId ?: 0).toString())
            .put(
                "albumId",
                (if (soundInfo != null && soundInfo.albumInfo != null) soundInfo.albumInfo!!.albumId else 0).toString()
            )
            .put("categoryId", (trackInfo?.categoryId ?: 0).toString())
            .put(
                "anchorId",
                (if (soundInfo != null && soundInfo.userInfo != null) soundInfo.userInfo!!.uid else 0).toString()
            )
            .put("trackForm", "track") // track 表示音频，video 表示视频
            .put("fullScreenMode", if (playContainer.isFullScreen()) "full" else "half") // full 表示全屏，half 表示半屏
            .put("currTrackId",(trackInfo?.trackId ?: 0).toString())
            .put("currAlbumId",(if (soundInfo != null && soundInfo.albumInfo != null) soundInfo.albumInfo!!.albumId else 0).toString())
            .put(XmRequestIdManager.CONT_ID, (trackInfo?.trackId ?: 0).toString())
            .put(XmRequestIdManager.CONT_TYPE, "newPlaySeekbar")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE
                )
            )
            .createTrace()
    }

    private fun clearPlayBtnCenterIconColor() {
        mPlayToPauseLottieDrawable?.addValueCallback(
            KeyPath("**"), LottieProperty.COLOR_FILTER,
            LottieValueCallback<ColorFilter>(null)
        )
        mPauseToPlayLottieDrawable?.addValueCallback(
            KeyPath("**"), LottieProperty.COLOR_FILTER,
            LottieValueCallback<ColorFilter>(null)
        )
    }

    private fun setPlayBtnCenterIconColor(color: Int) {
        if (mPlayToPauseLottieDrawable != null) {
            mPlayToPauseLottieDrawable?.addValueCallback(
                KeyPath("**"), LottieProperty.COLOR_FILTER,
                LottieValueCallback<ColorFilter>(PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN))
            )
        }
        if (mPauseToPlayLottieDrawable != null) {
            mPauseToPlayLottieDrawable?.addValueCallback(
                KeyPath("**"), LottieProperty.COLOR_FILTER,
                LottieValueCallback<ColorFilter>(PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN))
            )
        }
    }

    private fun loadLottieResourcesForPlayBtnCenterIcon() {
        if (mIvPlayBtnCenterIcon == null) {
            return
        }
        mPlayToPauseLottieDrawable = XmLottieDrawable()
        mPlayToPauseLottieDrawable?.scale = 0.5f
        val playToPauseLottiePath = "lottie" + File.separator + "play_page_y_play_btn_pause_to_play.json"
        LottieCompositionFactory.fromAsset(context, playToPauseLottiePath)
            .addListener { composition: LottieComposition? ->
                mPlayToPauseLottieDrawable?.composition = composition
                // 如果另一个也加载完成了，就设到控件上
                if (mPauseToPlayLottieDrawable != null && mPauseToPlayLottieDrawable?.composition != null) {
                    setPlayBtnCenterIconToLottie()
                }
            }
        mPauseToPlayLottieDrawable = XmLottieDrawable()
        mPauseToPlayLottieDrawable?.scale = 0.5f
        val pauseToPlayLottiePath = "lottie" + File.separator + "play_page_y_play_btn_play_to_pause.json"
        LottieCompositionFactory.fromAsset(context, pauseToPlayLottiePath)
            .addListener { composition: LottieComposition? ->
                mPauseToPlayLottieDrawable?.composition = composition
                // 如果另一个也加载完成了，就设到控件上
                if (mPlayToPauseLottieDrawable != null && mPlayToPauseLottieDrawable?.composition != null) {
                    setPlayBtnCenterIconToLottie()
                }
            }
    }

    private fun setPlayBtnCenterIconToLottie() {
        if (mIvPlayBtnCenterIcon == null) {
            return
        }
        val stateListDrawable = StateListDrawable()
        stateListDrawable.addState(intArrayOf(android.R.attr.state_selected), mPlayToPauseLottieDrawable)
        stateListDrawable.addState(intArrayOf(), mPauseToPlayLottieDrawable)
        if (mIvPlayBtnCenterIcon?.isSelected == true) {
            mPlayToPauseLottieDrawable?.progress = 1f
        } else {
            mPauseToPlayLottieDrawable?.progress = 1f
        }

        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            setPlayBtnCenterIconColor(PSkinManager.getBtnThemeColor())
        }

        mIvPlayBtnCenterIcon?.setImageDrawable(stateListDrawable)
    }

    private fun updatePlayStatus(targetPlaying: Boolean? = null) {
        if (mVgPlayBtn == null) {
            return
        }
        val lastSelected = mVgPlayBtn?.isSelected
        val isPlaying = if (targetPlaying != null) {
            targetPlaying
        } else if (playContainer.isVideoMode()){
            playContainer.isPlaying()
        } else {
            XmPlayerManager.getInstance(context).isPlaying
        }
        // selected状态表示播放中
        Log.d("controlbar", "isPlaying = $isPlaying")
        mVgPlayBtn?.isSelected = isPlaying
        mVgPlayBtn?.contentDescription =
            getString(if (isPlaying) R.string.main_pause else R.string.main_iv_cd_play)
        if (lastSelected != mVgPlayBtn?.isSelected) {
            startPlayCenterIconLottie()
        } else {
            if (mIvPlayBtnCenterIcon != null && mIvPlayBtnCenterIcon?.drawable is StateListDrawable) {
                val drawable = mIvPlayBtnCenterIcon?.drawable?.current
                if (drawable is XmLottieDrawable && drawable.progress != 1f) {
                    drawable.progress = 1f
                }
            }
        }
        val isLoading = if (playContainer.isVideoMode()) {
            false
        } else {
            XmPlayerManager.getInstance(context).isLoading
        }
        showLoadingView(isLoading)
    }

    private fun startPlayCenterIconLottie() {
        if (mIvPlayBtnCenterIcon != null && mIvPlayBtnCenterIcon?.drawable is StateListDrawable) {
            val drawable = mIvPlayBtnCenterIcon?.drawable?.current
            if (drawable is XmLottieDrawable) {
                drawable.playAnimation()
            }
        }
    }

    private fun updatePlayBtnStatusIfNeededWhilePlayProgressChanged() {
        // 进度变化时，如果播放按钮不是处于播放状态，可能是有问题的，尝试更新下
        if (mVgPlayBtn?.isSelected != true) {
            updatePlayStatus()
        }
    }

    private fun showLoadingView(show: Boolean) {
        // 暂停状态没必要显示转圈动效
        if (show && mVgPlayBtn?.isSelected == true) {
            // 已经在动画中，不要重复
            if (mVLoadingStatus?.visibility != View.VISIBLE) {
                AnimationUtil.rotateView(context, mVLoadingStatus)
                mVLoadingStatus?.visibility = View.VISIBLE
            }
        } else {
            mVLoadingStatus?.visibility = View.INVISIBLE
            AnimationUtil.stopAnimation(mVLoadingStatus)
        }
    }

    override fun onResume() {
        super.onResume()
        updatePlayStatus()
        handleOnPlayProgress(
            XmPlayerManager.getInstance(context).playCurrPositon,
            XmPlayerManager.getInstance(context).duration
        )
        val effectDialogNew = mSoundEffectDialogReferenceNew?.get()
        if (effectDialogNew?.isVisible == true) {
            effectDialogNew.dismiss()
        }
        traceShowSoundEffectOrAiSound()

        updateQualityName()
    }

    override fun onPlayStart() {
        super.onPlayStart()
        showLoadingView(false)
        updatePlayStatus(true)
    }

    override fun onPlayProgress(currPos: Int, duration: Int) {
        super.onPlayProgress(currPos, duration)
        updatePlayBtnStatusIfNeededWhilePlayProgressChanged()
        handleOnPlayProgress(currPos, duration)
    }

    override fun onPlayPause() {
        super.onPlayPause()
        updatePlayStatus()
    }

    override fun onPlayStop() {
        super.onPlayStop()
        updatePlayStatus()
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        super.onSoundSwitch(lastModel, curModel)

        tipsJob?.cancel()
        playContainer.dismissTips(TipPriority.TIP_CELLULAR_SOUND_QUALITY.id)

        mMarkTipsView?.dismissTips()
        minorData = null

        if (curModel != null && (lastModel != null && lastModel.dataId != curModel.dataId)) {
            shouldHideSeekbar = false
            if (!isDocScrolledFull) {
                showHideSeekbarGroup(View.VISIBLE)
            }
            enablePlayBtn(true)
            updatePlayStatus()

            if (curModel is Track) {
                setSeekBarMax(curModel.duration * 1000)
            } else {
                setSeekBarMax(100)
            }
            mSeekBar?.progress = 0
            setCanSeek(false)
            mSeekBar?.clearKeyPoints()
            mSeekBar?.clearPlayTimelineCardPoints()
        } else {
            mSeekBar?.progress = 0
            setCanSeek(false)
        }
    }

    override fun onSoundPlayComplete() {
        super.onSoundPlayComplete()
        updatePlayStatus()
    }

    override fun onBufferingStart() {
        super.onBufferingStart()
        showLoadingView(true)
    }

    override fun onBufferingStop() {
        super.onBufferingStop()
        showLoadingView(false)
    }

    override fun onError(exception: XmPlayerException?): Boolean {
        updatePlayStatus()
        return super.onError(exception)
    }

    override fun onError(what: Int, extra: Int) {
        showLoadingView(false)
    }

    private fun checkCopyright(): Boolean {
        val soundInfo = curSoundInfo
        return if (soundInfo?.trackInfo2TrackM() != null) {
            soundInfo.trackInfo2TrackM().isHasCopyRight
        } else true
    }

    private fun enablePlayBtn(enable: Boolean) {
        //正在播放有声广告时，播放按钮不可点击
        var finalEnable = enable
        if (UserInteractivePlayStatistics.Optimizer_StartPlayWhileAdRequest.enable()) {
            var adPlaying = XmPlayerManager.getInstance(context).isAdPlaying
            UserInteractivePlayStatistics.loge("enablePlayBtn adPlaying:$adPlaying")
            if (adPlaying) {
                adPlaying = MMKVUtil.getInstance().getBoolean("isPlaySoundPatchAd_1031", false)
                UserInteractivePlayStatistics.loge("enablePlayBtn isPlaySoundPatchAd_1031:$adPlaying")
            }

            finalEnable = enable && !adPlaying
        }

        mVgPlayBtn?.isEnabled = finalEnable
        mVgPlayBtn?.alpha = if (finalEnable) 0.85f else 0.2f
    }

    private fun enableSeekBar(enable: Boolean = true){
        seekBarGroup?.alpha = if (enable) 1f else  0.2f
        seekBarGroup?.isEnabled = enable
        mSeekBar?.isClickable = enable
        mTvSoundQuality?.isClickable = enable
    }

    fun showHideSeekbarGroup(visibility:Int) {
        ViewStatusUtil.setVisible(visibility, seekBarGroup)
    }

    override fun onStatusChange(ableToPlay: Boolean, reason: Set<UnableToPlayStatusAggregator.UnablePlayReason>) {
        enablePlayBtn(ableToPlay || reason.contains(UnableToPlayStatusAggregator.UnablePlayReason.OffShell))
        Log.d("Playbar", "onStatusChange $ableToPlay")

        if (reason.contains(UnableToPlayStatusAggregator.UnablePlayReason.Membership)) {
            //无版权、下架
            enableSeekBar(false)
        } else {
            enableSeekBar(true)
        }

    }

    //////// previous ads
    override fun onStartGetAdsInfo(playMethod: Int, duringPlay: Boolean, isPaused: Boolean) {
        if (!XmPlayerManager.getInstance(context).isPlaying) {
//            updateNextAndPreBtnStatus()
            if (!isPaused && !duringPlay) {
                showLoadingView(true)
            }
        }
    }

    override fun onGetAdsInfo(ads: AdvertisList) {}
    override fun onAdsStartBuffering() {}
    override fun onAdsStopBuffering() {}
    override fun onStartPlayAds(ad: Advertis, position: Int) {
        updatePlayStatus()
        showLoadingView(false)
    }

    override fun onCompletePlayAds() {
        try {
            updatePlayStatus()
        } catch (e: Exception) {
            Logger.e(e)
        }
    }


    private fun statPlayBtnClick(isSelected: Boolean) {
        // 新声音播放页-播放/暂停  点击事件
        val playableModel = XmPlayerManager.getInstance(mContext).currSound
        var trackFromPlayer: Track? = null
        if (playableModel is Track) {
            trackFromPlayer = playableModel
        }
        val traceApi = XMTraceApi.Trace()
            .click(17451) // 用户点击时上报
            .put("Item", if (isSelected) "播放" else "暂停") // 暂停｜播放
            .put("anchorId", curAnchorId.toString())
            .put("currAlbumId", curAlbumId.toString())
            .put("currTrackId", curTrackId.toString())
            .put("currPage", "newPlay")
            .put("status", "正常")
            .put("trackForm", "track") // track 表示音频，video 表示视频
            .put("fullScreenMode", if (playContainer.isFullScreen()) "full" else "half") // full 表示全屏，half 表示半屏
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
        if (trackFromPlayer != null) {
            if (!TextUtils.isEmpty(trackFromPlayer.ubtTraceId)) {
                traceApi.put("ubtTraceId", trackFromPlayer.ubtTraceId)
            }
            if (!TextUtils.isEmpty(trackFromPlayer.recTrack)) {
                traceApi.put("rec_track", trackFromPlayer.recTrack)
            }
            if (!TextUtils.isEmpty(trackFromPlayer.recSrc)) {
                traceApi.put("rec_src", trackFromPlayer.recSrc)
            }
        }
        traceApi.createTrace()
    }

    private val mOnClickListener = View.OnClickListener { v: View? ->
        if (!OneClickHelper.getInstance().onClick(v)) {
            return@OnClickListener
        }
        if (!checkCopyright()) {
            CustomToast.showFailToast(PlayPageDataManager.getInstance().noCopyrightMsg)
            return@OnClickListener
        } else if (v === mVgPlayBtn) {
            val isSelected = mVgPlayBtn?.isSelected
            if (playContainer.isVideoMode()) {
                playContainer.videoComponent()?.playOrPause()
            } else {
                if (isSelected == false) {
                    UserInteractivePlayStatistics.clickPlay(curTrackId, "clickPlay")
                }
                statPlayBtnClick(isSelected == true)
                AudioPlayUtil.playOrPause(mFragment, context, curSoundInfo)
                showLockScreenGuidePush(mFragment)
                traceControlViewClick(if (isSelected == true) "播放" else "暂停")
            }
        }
        v?.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
    }

    /**
     * 播控区域点击埋点
     * @param clickType 进度条|后退15秒|前进15秒|播放列表|倍速播放|咔嚓|音效|更多
     */
    private fun traceControlViewClick(clickType: String, curPlayPosition: Int = -1, targetProgress: Int = -1) {
        val coverComponentsManagerService = PlayPageInternalServiceManager.getInstance().getService(
            ICoverComponentsManagerService::class.java
        )
        var isCoverFullscreen = false
        if (coverComponentsManagerService != null) {
            isCoverFullscreen = coverComponentsManagerService.isFullscreen()
        }
        XMTraceApi.Trace()
            .click(49674) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("Item", clickType)
            .put("screenDirection", if (isCoverFullscreen) "全屏" else "") // 全屏则传全屏，其他情况传空
            .put("currAlbumId", curAlbumId.toString() + "")
            .put("currTrackId", curTrackId.toString() + "")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .apply {
                AudioInfoTraceUtil.interceptTrace(this)
                if (curPlayPosition >= 0) {
                    put("listenProgress", getFriendlyNumberTime(curPlayPosition.toLong() / 1000).toString())
                }
                if (targetProgress >= 0) {
                    put("targetProgress", getFriendlyNumberTime(targetProgress.toLong() / 1000).toString())
                }
            }
            .createTrace()
    }

    companion object {
        @JvmStatic
        var isWifi = true
        fun newInstance(fragment: BaseFragment2?, playContainer: IPlayContainer, controlBar: YPlayControlBar): YPlaySimpleControlBar {
            val component = YPlaySimpleControlBar(playContainer)
            component.onCreate(fragment)
            component.setXPlayControlBar(controlBar)
            return component
        }
    }

    open fun setXPlayControlBar(yPlayControlBar: YPlayControlBar) {
        mYPlayControlBar = yPlayControlBar
    }

    override fun onDestroy() {
        super.onDestroy()

        PlayTimelineCardManager.setDataCallBack(null)
        SoundPatchController.unregisterSoundPatchControlStatusCallBack(this)
        TrackPlaySoundEffectManager.getInstance()
            .unregisterStatusListener(this)
        TrackPlayQualityKt.removeListener(this)
    }

    override fun onPlayingSoundPatchStart() {

    }

    override fun onPlayingSoundPatchStop() {
        updatePlayStatus()
    }

    override fun onNotPlayingSoundPatchStart() {

    }

    override fun onNotPlayingSoundPatchStop() {

    }

    private fun showSoundEffectDialog() {
        mSoundEffectDialogReferenceNew?.get()?.dismiss()
//        val soundInfo = curSoundInfo ?: return
//
//        val albumId = soundInfo.albumInfo?.albumId?:return
//        val trackId = soundInfo.trackInfo?.trackId?:return
//
//        val dialog = YSoundQualityDialog.newInstance(albumId, trackId)//ChooseTrackSoundEffectAiDialogXNew.newInstance(false)

        val showEffect = curSoundInfo?.trackInfo?.trackQualityList.isNullOrEmpty() || !isAudioMode() || isEffectOn()
        val dialog = YPlayFragment.showSoundEffectDialog(
            false,
            if(showEffect) ChooseTrackSoundEffectAiDialogXNew.TAB_EFFECT else ChooseTrackSoundEffectAiDialogXNew.TAB_QUALITY)
        mSoundEffectDialogReferenceNew = WeakReference(dialog)
//        dialog.show(
//            childFragmentManager,
//            ChooseTrackSoundEffectAiDialogXNew::class.java.simpleName
//        )
    }

    private fun traceAiOrEffectClick(v: View?) {
        traceSoundEffect()
    }

    private fun traceSoundEffect() {
        FunctionTraceUtil.soundEffectClickTrace(
            "track",
            curTrackId.toString(),
            curAlbumId.toString(),
            if (playContainer.isFullScreen()) "full" else "half"
        )
    }

    private fun traceShowSoundEffect() {
        // 新声音播放页-音效  控件曝光
        FunctionTraceUtil.soundEffectExposureTrace(
            curTrackId.toString(),
            curAlbumId.toString(),
            if (playContainer.isFullScreen()) "full" else "half"
        )
    }

    private fun traceShowSoundEffectOrAiSound() {
        traceShowSoundEffect()
    }

    private fun isAiSound(soundInfo: PlayingSoundInfo?): Boolean {
        if (soundInfo != null) {
            return PlayTtsUtil.isTtsOrIsTimbre(soundInfo, false)
        }
        return false
    }

    override fun onStatus(
        status: Int,
        validType: Int,
        soundEffectId: Long,
        isInitialNotify: Boolean
    ) {
        updateQualityName()
    }
}