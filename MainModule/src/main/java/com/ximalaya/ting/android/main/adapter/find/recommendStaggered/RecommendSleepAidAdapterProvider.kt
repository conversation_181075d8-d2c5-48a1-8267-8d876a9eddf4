package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.annotation.SuppressLint
import android.graphics.PorterDuff
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.main.adapter.find.util.SleepAidShowTagUtil
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.Custom3DAlbumView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.ColorValueModel
import com.ximalaya.ting.android.main.adapter.find.util.DisLikeReqUtil
import com.ximalaya.ting.android.main.adapter.find.util.IRequestCallback
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils.getNewRankBgCorner
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.SceneCardUtil
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.SleepAidColorUtils
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataPlayStatusStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.util.FeedTrackPlayUtil
import com.ximalaya.ting.android.main.util.HomeModuleTitleUtils
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 助眠卡
 */
class RecommendSleepAidAdapterProvider(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<RecommendSleepAidAdapterProvider.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataPlayStatusStaggered<RecommendSleepAidAdapterProvider.ViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<RecommendSleepAidAdapterProvider.ViewHolder, RecommendItemNew> {

    private var mListCardViewHolder: ViewHolder? = null

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_sleep_aid_card,
            parent,
            false
        )
    }

    override fun onPlayStart() {
    }

    override fun onPlayPause() {
    }

    override fun onSoundPlayComplete() {

    }

    class ViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        var tvTitle: TextView = convertView.findViewById(R.id.main_tv_module_title)

        // val ivRefresh: ImageView = convertView.findViewById(R.id.main_tv_refresh)
        val ivMore: ImageView = convertView.findViewById(R.id.main_tv_more)

        val rlContentRootView: ViewGroup = convertView.findViewById(R.id.main_rl_content_root_view)
        val custom3DAlbumView: Custom3DAlbumView = convertView.findViewById(R.id.main_3d_album_view)
        val tvHintTitle: TextView = convertView.findViewById(R.id.main_tv_hint_title)
        val ivHintFlag: ImageView = convertView.findViewById(R.id.main_iv_hint_flag)
        val tvAlbumTitle: TextView = convertView.findViewById(R.id.main_tv_main_album_title)
        val llShowTag: LinearLayout = convertView.findViewById(R.id.main_ll_show_tag)
        val viewLine: View = convertView.findViewById(R.id.main_view_gap_line)

        val tvContinuePlay: TextView = convertView.findViewById(R.id.main_tv_continue_play)
        val ivPlayLine: ImageView = convertView.findViewById(R.id.main_iv_play_line)
        val tvPlayInfo: TextView = convertView.findViewById(R.id.main_tv_play_info)

        var showNotePlayBtnBgWrap: View =
            convertView.findViewById(R.id.main_show_notes_play_layout_wrap)
        var showNotePlayBtnBg: View = convertView.findViewById(R.id.main_show_notes_play_layout)
        var showNotePlayBtn: ImageView = convertView.findViewById(R.id.main_iv_show_notes_play_btn)

        var firstVisiblePosition: Int = 0
    }

    override fun createViewHolder(convertView: View?): ViewHolder? {
        if (convertView == null) {
            return null
        }
        val viewHolder = ViewHolder(convertView)
        return viewHolder
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun bindViewHolder(
        holder: ViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            HandlerManager.postOnUIThread {
                onBindViewHolderInner(holder, position, recommendItemNew)
            }
        } else {
            onBindViewHolderInner(holder, position, recommendItemNew)
        }
    }

    fun onBindViewHolderInner(
        holder: ViewHolder?,
        modulePosition: Int,
        recommendItemNew: RecommendItemNew?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }
        mListCardViewHolder = holder
        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem == null || recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }

        val context = holder.rootView.context

        holder.tvTitle.text = recommendCommonItem.title

        val coverWidth = 29.dp + holder.custom3DAlbumView.getWholeViewWidth()

        val lineParams = holder.viewLine.layoutParams as MarginLayoutParams
        lineParams.topMargin = holder.custom3DAlbumView.getCoverSize().toInt() + 31.dp
        lineParams.leftMargin = (coverWidth + 8.dp).toInt()
        holder.viewLine.layoutParams = lineParams

        // 设置3D专辑封面
        val firstElement = recommendCommonItem.subElements?.firstOrNull()
        if (firstElement != null) {

            val ext = firstElement.ext
            val trackId = ext?.nextTrackId

            RemoteTypefaceManager.setRemoteTypeface(RemoteTypefaceManager.RemoteTypeface.SourceHanSerifBold, holder.tvHintTitle)
            RemoteTypefaceManager.setRemoteTypeface(RemoteTypefaceManager.RemoteTypeface.SourceHanSerifBold, holder.tvAlbumTitle)

            holder.tvAlbumTitle.text = firstElement.title
            holder.tvHintTitle.text = firstElement.subTitle ?: "助眠专区 · 深夜常听"

            // 32 整体的左右padding  专辑图片左边阴影 29 26标题左右margin
            val maxWidth: Int =
                (getRpAdaptSize(375) - 32.dp - coverWidth - 26.dp).toInt()

            val canShowOneLine = SleepAidShowTagUtil.canShowOneLine(
                holder.tvAlbumTitle, holder.tvAlbumTitle.text, maxWidth
            )

            val setShowTag = { cacheColorModel: ColorValueModel ->
                val showTag = ext?.showTags
                if (!showTag.isNullOrEmpty() && canShowOneLine) {
                    // 使用睡眠卡专用的ShowTag工具类
                    val config = SleepAidShowTagUtil.SleepAidShowTagConfig(
                        textSize = 13f, // 可以根据需要调整字体大小
                        textColor = cacheColorModel.textColor, // 使用睡眠卡的颜色（直接色值）
                        trackId = trackId
                    )
                    SleepAidShowTagUtil.bindSleepAidTagsView(
                        holder.llShowTag,
                        showTag.toMutableList(),
                        maxWidth,
                        config
                    )
                } else {
                    holder.llShowTag.visibility = View.GONE
                }
            }

            val setColor = { cacheColorModel: ColorValueModel ->
                holder.tvHintTitle.setTextColor(cacheColorModel.textColor)
                holder.ivHintFlag.setColorFilter(cacheColorModel.textColor)
                holder.tvAlbumTitle.setTextColor(cacheColorModel.textColor)
                holder.tvContinuePlay.setTextColor(cacheColorModel.textColor)
                holder.tvPlayInfo.setTextColor(SceneCardUtil.getAlphaColor(0.5f, cacheColorModel.textColor))
                holder.ivPlayLine.setColorFilter(
                    SceneCardUtil.getAlphaColor(
                        0.8f,
                        cacheColorModel.textColor
                    ),
                    PorterDuff.Mode.SRC_IN
                )
                holder.viewLine.setBackgroundColor(
                    SceneCardUtil.getAlphaColor(
                        0.1f,
                        cacheColorModel.textColor
                    )
                )
                holder.showNotePlayBtn.setColorFilter(
                    cacheColorModel.playBtnColor, PorterDuff.Mode.SRC_IN
                )

                (holder.showNotePlayBtnBg.background as? GradientDrawable)?.setColor(cacheColorModel.playBtnBgColor)

                (holder.rlContentRootView.background as? GradientDrawable)?.let {
                    it.setColor(cacheColorModel.bgColor)
                    it.cornerRadius = getNewRankBgCorner().toFloat()
                }

                setShowTag(cacheColorModel)
            }

            val isPlaying = XmPlayerManager.getInstance(context).isPlaying &&
                    XmPlayerManager.getInstance(context).currSound?.dataId == trackId

            if (isPlaying) {
                holder.showNotePlayBtn.setImageResource(R.drawable.host_ic_show_note_pause)
            } else {
                holder.showNotePlayBtn.setImageResource(R.drawable.host_ic_show_note_play)
            }

            if (recommendCommonItem.validCacheColor()) {
                setColor(recommendCommonItem.cacheColorModel!!)
            } else {
                setColor(SleepAidColorUtils.getDefaultColor())
            }

            holder.custom3DAlbumView.setAlbumCover(firstElement.cover ?: "") { _, bitmap ->
                if (!recommendCommonItem.validCacheColor()) {
                    SleepAidColorUtils.getSleepAidColor(bitmap, object : IRequestCallback {
                        override fun onSuccess(data: ColorValueModel) {
                            recommendCommonItem.cacheColorModel = data
                            setColor(recommendCommonItem.cacheColorModel!!)
                        }
                    })
                }
            }

            holder.tvPlayInfo.text = ext?.nextTrackTitle ?: ""

            holder.showNotePlayBtnBg.run {
                val params = this.layoutParams as MarginLayoutParams
                params.width = RecommendCornerUtils.getShowNoteSize()
                params.height = params.width
                this.layoutParams = params
            }

            holder.showNotePlayBtn.run {
                val params = this.layoutParams
                params.width = RecommendCornerUtils.getShowNotePlaySize()
                params.height = params.width
                this.layoutParams = params
            }

            val click = { isPlay: Boolean ->

                val trace = XMTraceApi.Trace()
                    .click(62176) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                    .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                    .put("card_adTopn", recommendCommonItem.cardAdCount.toString())

                SpmTraceUtil.addSpmTraceInfo(
                    trace,
                    (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                    (modulePosition + 1).toString()
                )
                RecommendNewUbtV2Manager.addUbtV2Data(
                    trace, (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
                )
                trace.createTrace()

                val area = if (isPlay) {
                    "play"
                } else {
                    "item"
                }

                // 新首页-社会化标注-声音/专辑/直播/视频卡片  点击事件
                val trace1 = XMTraceApi.Trace()
                    .click(60896) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("positionNew", "1")
                    .put("contentId", firstElement.id.toString()) // 客户端传。去重使用
                    .put("contentType", firstElement.contentType) // 客户端传。去重使用
                    .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                    .put(
                        "exploreArea",
                        ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView).toString()
                    )
                    .put("modulePosition", (modulePosition + 1).toString())
                    .put("card_adTopn", "0")
                    .put("area", area) // 客户端传。区分是条还是播放按钮
                    .put(
                        "card_isAiTitle",
                        HomeModuleTitleUtils.isAiTitle(recommendCommonItem.id)
                    )

                trace1.createTrace()

                if (isPlay) {
                    ToolUtil.clickUrlAction(
                        fragment,
                        ext?.nextTrackLandingPage ?: "",
                        holder.showNotePlayBtnBgWrap
                    )
                } else {
                    ToolUtil.clickUrlAction(
                        fragment,
                        firstElement.landingPage ?: "",
                        holder.rlContentRootView
                    )
                }

            }

            holder.rlContentRootView.setOnClickListener {
                click(false)
            }

            holder.showNotePlayBtnBgWrap.setOnClickListener {
                click(true)
            }

            FeedTrackPlayUtil.setTrackTag(holder.itemView, trackId)
        }

        val exportMore = { action: String ->
            // 新首页-社会化标注-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(61824) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("moduleName", recommendCommonItem.title) // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.moduleId?.toString() ?: "") // 例如：1000000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("action", action)
                .put(
                    "card_isAiTitle",
                    HomeModuleTitleUtils.isAiTitle(recommendCommonItem.id)
                )
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
        }

        val performMoreClick = {
            ScenePlayDataUtil.saveDataForRn(recommendItemNew)

            ToolUtil.clickUrlAction(
                fragment, recommendCommonItem.landingPage ?: "", holder.ivMore
            )
        }

        holder.tvTitle.setOnClickListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())

            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1, (recommendItemNew.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            exportMore("click")
            performMoreClick()
        }

        holder.ivMore.setOnClickListener {
            exportMore("click")
            val isShowMore = !recommendCommonItem.landingPage.isNullOrEmpty()
            var level1DisLikeTitle = recommendCommonItem.ext?.disLikeTip
            if (level1DisLikeTitle.isNullOrEmpty()) {
                level1DisLikeTitle = "减少推荐：${recommendCommonItem.title ?: ""}"
            }
            val listener = object : IMoreFuncListener() {
                override fun onMoreClick(btnText: String?) {
                    performMoreClick()
                }

                override fun onLevel1DisLikeClick(btnText: String?) {
                    dataAction?.remove(modulePosition)
                }
            }

            val moreFuncBuild = MoreFuncBuild.createSocialListenMoreModel(
                fragment,
                isShowMore,
                level1DisLikeTitle,
                listener
            )

            val trackMap = mutableMapOf<String, String?>().apply {
                put("modulePosition", (modulePosition + 1).toString())
                put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                put("contentType", recommendCommonItem.bizType ?: "")
                put("contentId", recommendCommonItem.id?.toString() ?: "")
                recommendCommonItem.ubtV2?.let { it1 -> putAll(it1) }
            }
            moreFuncBuild.trackMap = trackMap
            val contentType = recommendCommonItem.contentType ?: ""
            val bizType = recommendCommonItem.bizType ?: ""
            val cardId = recommendCommonItem.id?.toString() ?: ""
            val trigger = recommendCommonItem.ext?.extraInfo?.triggerId ?: ""
            val reqList = DisLikeReqUtil.createDisLikeReqList(
                DisLikeReqUtil.SOURCE_NEW_HOME_CARD,
                recommendCommonItem.subElements,
                contentType,
                bizType,
                cardId,
                trigger
            )
            moreFuncBuild.disLikeReqList = reqList

            XmMoreFuncManager.checkShowMorePage(moreFuncBuild)
        }
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        fragment.postOnUiThread {
            if (!fragment.canUpdateUi()) {
                return@postOnUiThread
            }

            if (!ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                return@postOnUiThread
            }

            // 新首页-首页大卡模块  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62177)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("xmRequestId", data.xmRequestId) // 客户端传
                .put("contentType", data.itemType) // 客户端传
                .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()

            val itemModel = recommendCommonItem.subElements?.getOrNull(0) ?: return@postOnUiThread

            // 新首页-社会化标注-声音/专辑/直播/视频卡片  控件曝光
            val trace1 = XMTraceApi.Trace()
                .setMetaId(60897)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("positionNew", "1")
                .put("contentId", itemModel.id.toString()) // 客户端传。去重使用
                .put("contentType", itemModel.contentType) // 客户端传。去重使用
                .put("xmRequestId", data.xmRequestId) // 客户端传
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView).toString()
                )
                .put("modulePosition", (position + 1).toString())
                .put("card_adTopn", "0")

            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                contentTitle = itemModel.title,
                contentPosition = (1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, itemModel.ubtV2)
            if (data.isLocalCache) {
                trace1.isLocalCache
            }

            trace1.createTrace()
        }
    }

    companion object {

        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }
    }
}