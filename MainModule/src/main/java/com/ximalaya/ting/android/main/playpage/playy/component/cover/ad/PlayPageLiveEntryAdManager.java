package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.ad.manager.AdStateReportManager;
import com.ximalaya.ting.android.ad.manager.PreloadSDkAdManager;
import com.ximalaya.ting.android.ad.manager.ThirdAdLoadManager;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.adsdk.AdSDK;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.INativeAdLoadListener;
import com.ximalaya.ting.android.adsdk.external.XmLoadAdParams;
import com.ximalaya.ting.android.adsdk.record.trace.AdTrace;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdConversionUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.BackgroundListenerAdManager;
import com.ximalaya.ting.android.host.manager.ad.CacheDspAdManager;
import com.ximalaya.ting.android.host.manager.ad.IThirdNativeAdLoadCallback;
import com.ximalaya.ting.android.host.manager.ad.ThirdAdLoadParams;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.trace.SoundPatchAdTrace;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.adModule.manager.PlayAdStateRecordManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.AudioPlayAdUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager;
import com.ximalaya.ting.android.xmutil.Logger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 播放页直播入口广告管理器
 * 负责处理直播入口广告的曝光、点击和激励广告展示
 */
public class PlayPageLiveEntryAdManager {
    private static final String TAG = "PlayPageLiveEntryAdManager";

    // ADX slotId
    private static final String ADX_SLOT_ID = "311";

    // 广播Action常量 - 需要与PlayPageLiveEntryManager中的常量保持一致
    private static final String ACTION_PLAY_LIVE_AD_ENTRY_SHOW = "action_play_live_entry_show";
    private static final String ACTION_PLAY_LIVE_AD_ENTRY_CLICK = "action_play_live_entry_click";
    private static final String KEY_ICON_URL = "icon_url";
    private static final String KEY_TITLE = "title";
    private static final String KEY_REQUEST_ID = "requestId";

    private static volatile PlayPageLiveEntryAdManager sInstance;
    private final Map<String, List<Advertis>> mAdMaterialCache = new ConcurrentHashMap<>();
    private final Map<String, String> mRequestIdToIconUrlMap = new ConcurrentHashMap<>();
    private final Map<String, String> mRequestIdToTitleMap = new ConcurrentHashMap<>();

    private BroadcastReceiver mBroadcastReceiver;
    private boolean mIsRegistered = false;

    private PlayPageLiveEntryAdManager() {
        initBroadcastReceiver();
    }

    public static PlayPageLiveEntryAdManager getInstance() {
        if (sInstance == null) {
            synchronized (PlayPageLiveEntryAdManager.class) {
                if (sInstance == null) {
                    sInstance = new PlayPageLiveEntryAdManager();
                }
            }
        }
        return sInstance;
    }

    /**
     * 初始化广播接收器
     */
    private void initBroadcastReceiver() {
        mBroadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null) {
                    return;
                }

                String action = intent.getAction();
                Logger.w(TAG, "liveMc广播接收成功 ac" + action);
                if (ACTION_PLAY_LIVE_AD_ENTRY_SHOW.equals(action)) {
                    handleAdShow(intent);
                } else if (ACTION_PLAY_LIVE_AD_ENTRY_CLICK.equals(action)) {
                    handleAdClick(intent);
                }
            }
        };
    }

    /**
     * 注册广播接收器
     */
    public void registerBroadcastReceiver() {
        if (mIsRegistered) {
            Logger.d(TAG, "liveMc广播已经注册");
            return;
        }

        try {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ACTION_PLAY_LIVE_AD_ENTRY_SHOW);
            intentFilter.addAction(ACTION_PLAY_LIVE_AD_ENTRY_CLICK);

            LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext())
                    .registerReceiver(mBroadcastReceiver, intentFilter);

            mIsRegistered = true;
            Logger.e(TAG, "liveMc注册广播成功");
        } catch (Exception e) {
            Logger.e(TAG, "liveMc注册广播失败", e);
        }
    }

    /**
     * 注销广播接收器
     */
    public void unregisterBroadcastReceiver() {
        if (!mIsRegistered || mBroadcastReceiver == null) {
            return;
        }

        try {
            LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext())
                    .unregisterReceiver(mBroadcastReceiver);
            mIsRegistered = false;
        } catch (Exception e) {
        }
    }

    /**
     * 处理广告曝光事件
     */
    private void handleAdShow(Intent intent) {
        String requestId = intent.getStringExtra(KEY_REQUEST_ID);
        String iconUrl = intent.getStringExtra(KEY_ICON_URL);
        String title = intent.getStringExtra(KEY_TITLE);

        Logger.d(TAG, "Handleadshow - requestId: " + requestId + ", iconUrl: " + iconUrl + ", title: " + title);

        // 发送adtrace埋点
        traceAdShow(requestId, iconUrl, title);

        if (requestId != null) {
            // 缓存请求ID对应的信息
            mRequestIdToIconUrlMap.put(requestId, iconUrl);
            mRequestIdToTitleMap.put(requestId, title);

            // 请求ADX物料
            requestAdxMaterial(requestId);
        }
    }

    /**
     * 处理广告点击事件
     */
    private void handleAdClick(Intent intent) {
        String requestId = intent.getStringExtra(KEY_REQUEST_ID);
        String iconUrl = intent.getStringExtra(KEY_ICON_URL);
        String title = intent.getStringExtra(KEY_TITLE);

        Logger.d(TAG, "handleAdClick - requestId: " + requestId + ", iconUrl: " + iconUrl + ", title: " + title);

        // 发送adtrace埋点
        traceAdClick(requestId, iconUrl, title);

        if (requestId != null) {
            // 尝试从缓存中获取物料
            List<Advertis> advertisList = mAdMaterialCache.get(requestId);
            if (advertisList != null && !advertisList.isEmpty()) {
                // 处理广告物料
                handleAdvertisList(advertisList, requestId);
            } else {
                Logger.w(TAG, "通过 requestId 没有找到: " + requestId);
                // 如果没有找到，找到map中的第一个作为兜底
                if (!mAdMaterialCache.isEmpty()) {
                    // 获取缓存中的第一个物料列表
                    List<Advertis> firstAdvertisList = mAdMaterialCache.values().iterator().next();
                    if (firstAdvertisList != null && !firstAdvertisList.isEmpty()) {
                        Logger.d(TAG, "使用缓存中的第一个物料作为兜底，requestId: " + requestId);
                        handleAdvertisList(firstAdvertisList, requestId);
                    } else {
                        Logger.w(TAG, "缓存中的第一个物料列表为空，重新请求ADX物料");
                        requestAdxMaterial(requestId);
                    }
                }
            }
        }
    }

    /**
     * 请求ADX物料
     */
    private void requestAdxMaterial(String requestId) {
        Logger.d(TAG, "请求adx 广告 slotId: " + ADX_SLOT_ID + ", requestId: " + requestId);

        // 创建请求参数
        XmLoadAdParams xmLoadAdParams = new XmLoadAdParams(ADX_SLOT_ID);
        xmLoadAdParams.setNeedToRecordShowOb(false);

        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("requestId", requestId);

        // 添加通用参数
        addCommonRequestParams(requestMap);

        xmLoadAdParams.setRequestParams(requestMap);

        // 请求广告
        AdSDK.getInstance().loadNativeAd(ToolUtil.getCtx(), xmLoadAdParams, new INativeAdLoadListener<INativeAd>() {
            @Override
            public void onNativeAdLoad(@NonNull List<INativeAd> list) {
                Logger.d(TAG, "ADX 物料请求成功  count: " + (list != null ? list.size() : 0));

                if (list != null && !list.isEmpty()) {
                    // 转换所有物料
                    List<Advertis> advertisList = new ArrayList<>();
                    for (INativeAd nativeAd : list) {
                        if (nativeAd != null) {
                            // 使用项目中已有的转换方法
                            Advertis advertis = AdConversionUtil.translateSDKAdModelToAdvertis(nativeAd.getAdSDKAdapterModel());
                            Logger.d(TAG, "translateSDKAdModelToAdvertis advertis: " + advertis);
                            if (advertis != null) {
                                advertisList.add(advertis);
                            }
                        }
                    }

                    if (!advertisList.isEmpty()) {
                        // 缓存物料列表
                        mAdMaterialCache.put(requestId, advertisList);
                        Logger.d(TAG, "adx 物料 requestId: " + requestId + ", count: " + advertisList.size());
                    }
                }
            }

            @Override
            public void onLoadError(int errorCode, String errorMessage) {
                Logger.e(TAG, "onLoadError, errorCode: " + errorCode + ", errorMessage: " + errorMessage);
            }
        });
    }

    /**
     * 处理广告物料列表
     * 优先使用第一个物料请求激励视频，失败时使用其他xm物料作为兜底
     */
    private void handleAdvertisList(List<Advertis> advertisList, String requestId) {
        if (advertisList == null || advertisList.isEmpty()) {
            Logger.w(TAG, "handleAdvertisList Advertis 为空 requestId: " + requestId);
            return;
        }

        // 获取第一个物料用于激励视频
        Advertis firstAdvertis = advertisList.get(0);
        if (firstAdvertis != null) {
            // 尝试展示激励广告
            showRewardAd(firstAdvertis, requestId, advertisList);
        } else {
            // 第一个物料为空，尝试其他物料
            tryOtherAdvertis(advertisList, requestId);
        }
    }

    /**
     * 尝试其他广告物料作为兜底
     */
    private void tryOtherAdvertis(List<Advertis> advertisList, String requestId) {
        for (int i = 1; i < advertisList.size(); i++) {
            Advertis advertis = advertisList.get(i);
            if (advertis != null && isXmAdvertis(advertis)) {
                // 直接跳转到xm物料的reallink
                jumpToRealLink(advertis, requestId);
                return;
            }
        }
    }

    /**
     * 判断是否为xm物料
     */
    private boolean isXmAdvertis(Advertis advertis) {
        // 根据项目中的逻辑判断是否为xm物料
        return advertis != null && !AdManager.isThirdAd(advertis);
    }

    /**
     * 跳转到reallink
     */
    private void jumpToRealLink(Advertis advertis, String requestId) {
        String realLink = advertis.getRealLink();
        if (realLink != null && !realLink.isEmpty()) {
            Logger.d(TAG, "Jump to realLink for requestId: " + requestId + ", realLink: " + realLink);
            try {
                Activity topActivity = BaseApplication.getMainActivity();
                AdManager.handlerAdClick(topActivity, advertis, AdReportModel.newBuilder(AppConstants
                        .AD_LOG_TYPE_SITE_CLICK, advertis.getPositionName()).build());
                //曝光，产品要求加上
                AdReportModel.Builder builder = AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SITE_SHOW, advertis.getPositionName())
                        .sdkType(AdManager.getSDKType(advertis) + "")
                        .dspPositionId(advertis.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .sourceName("playPageLiveEntry")
                        .showStyle(advertis.getShowstyle() + "");
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        advertis, builder.build());
            } catch (Exception e) {
                Logger.e(TAG, "兜底 打开 : " + realLink, e);
            }
        }
    }

    /**
     * 展示激励广告
     */
    private void showRewardAd(Advertis advertis, String requestId, List<Advertis> advertisList) {
        Logger.d(TAG, "showRewardAd 展示 ad --- requestId: " + requestId);

        if (advertis == null) {
            Logger.e(TAG, "Advertis 为 null ---requestId: " + requestId);
            return;
        }

        showDialog();

        ThirdAdLoadParams thirdAdLoadParams =
                new ThirdAdLoadParams(advertis.getPositionName(), System.currentTimeMillis());

        // 请求视频
        if (AdManager.isVideoAd(advertis)) {
            thirdAdLoadParams.setVideoParams(true,
                    IAdConstants.IVideoAdConfig.MIN_VIDEO_DURATION,
                    ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                            CConstants.Group_ad.ITEM_SOUND_PATCH_VIDEO_MAX_DURATION_TIME,
                            IAdConstants.IVideoAdConfig.MAX_VIDEO_DURATION));
        }

        ThirdAdLoadManager.loadThirdAd(advertisList,
                thirdAdLoadParams,
                new IThirdNativeAdLoadCallback() {
                    @Override
                    public void loadThirdNativeAdSuccess(Advertis backAdvertis,
                                                         @NonNull AbstractThirdAd nativeADDataRef) {
                        Logger.d(TAG, "loadThirdNativeAdSuccess success for requestId: " + requestId );
                        AdTrace.report("liveMc", null, null, "adRealShow");
                        dismissDialog();
                    }

                    @Override
                    public void loadThirdNativeAdError(@Nullable Advertis backAdvertis, boolean timeout) {
                        Logger.w(TAG, "loadThirdNativeAdError --- requestId: " + requestId );
                        dismissDialog();
                        if (advertisList != null && advertisList.size() > 1) {
                            AdTrace.report("liveMc", null, null, "adShowFail");
                            tryOtherAdvertis(advertisList, requestId);
                        }
                    }
                });
    }

    private LoadingDialog loadingDialog;

    public void showDialog() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity != null) {
            loadingDialog = new LoadingDialog(topActivity);
            loadingDialog.setTitle("努力加载中");
            loadingDialog.showIcon(true);
            loadingDialog.show();
        }
    }

    private void dismissDialog() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
    }

    /**
     * 发送广告曝光埋点
     */
    private void traceAdShow(String requestId, String iconUrl, String title) {
        // 使用AdTrace发送曝光埋点
        try {
            HashMap<String, String> params = new HashMap<>();
            params.put("iconUrl", iconUrl);
            params.put("title", title);
            AdTrace.report("liveMc", null, params, "adEntryShow");
            Logger.d(TAG, "收到展示广告广播，adTrace上报 requestId: " + requestId);
        } catch (Exception e) {

        }
    }

    /**
     * 发送广告点击埋点
     */
    private void traceAdClick(String requestId, String iconUrl, String title) {
        // 使用AdTrace发送点击埋点
        try {
            HashMap<String, String> params = new HashMap<>();
            params.put("iconUrl", iconUrl);
            params.put("title", title);
            AdTrace.report("liveMcAd", null, params, "adEntryClick");
            Logger.d(TAG, "Ad show trace sent for requestId: " + requestId);
        } catch (Exception e) {
            Logger.e(TAG, "Failed to send ad click trace", e);
        }
    }

    /**
     * 添加通用请求参数
     */
    private void addCommonRequestParams(Map<String, String> requestMap) {
        // 这里可以添加一些通用的请求参数
        // 比如设备信息、用户信息等
        requestMap.put("device", "android");
        requestMap.put("version", "1.0.0");
        // 可以根据需要添加更多参数
    }

    /**
     * 清理缓存
     */
    private void cleanupCache(String requestId) {
        mAdMaterialCache.remove(requestId);
        mRequestIdToIconUrlMap.remove(requestId);
        mRequestIdToTitleMap.remove(requestId);
        Logger.d(TAG, "Cache cleaned for requestId: " + requestId);
    }

    /**
     * 清理所有缓存
     */
    public void clearAllCache() {
        mAdMaterialCache.clear();
        mRequestIdToIconUrlMap.clear();
        mRequestIdToTitleMap.clear();
        Logger.d(TAG, "All cache cleared");
    }

    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return mAdMaterialCache.size();
    }

    /**
     * 销毁管理器
     */
    public void destroy() {
        unregisterBroadcastReceiver();
        clearAllCache();
        sInstance = null;
        Logger.d(TAG, "PlayPageLiveEntryAdManager destroyed");
    }
} 