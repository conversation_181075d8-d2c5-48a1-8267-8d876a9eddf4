package com.ximalaya.ting.android.main.fragment.find.child.staggered

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.FixHomeFeedShowBugUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.recommendStaggered.RecommendFocusAdapterProviderStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataPlayStatusStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataToListenStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataWithLifecircleStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewConfigurationChanged
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendFragmentTraceViewManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageGaiaXManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageVirtualViewManager
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager
import com.ximalaya.ting.android.main.model.rec.RecommendAlbumItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendModuleItem
import com.ximalaya.ting.android.main.util.SceneListenAnimaUtil
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter

/**
 * Created by changle.fang on 2021/10/26.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class RecommendFragmentStaggeredAdapter(val mFragment: BaseFragment2) : AbRecyclerViewAdapter<RecyclerView.ViewHolder>() {

    var mListData = arrayListOf<Any>()
    private val mHasShownAdItemSet: HashSet<RecommendItemNew> = HashSet()
    private val mHasShowModuleIdList: HashSet<Long> = HashSet()
    private var mViewTypes = hashMapOf<Int, Any?>()

    interface IDataAction {
        fun remove(position: Int, needAddData: Boolean = false)
        fun remove(position: Int, needAddData: Boolean = false, moduleType: String)

        fun refresh(pos: Int)

        fun headSize(): Int

        fun isPersonalRecOpen(): Boolean

        fun updateAd(position: Int, needRemove: Boolean)

        fun notifyDateSetChanged()
    }

    // 需要占一行的模块
    private var mFullSpanItemList = arrayListOf(
//            20000, // footer
            RecommendFragmentTypeManager.VIEW_TYPE_LOADING,
            RecommendFragmentTypeManager.VIEW_TYPE_NETWORK_ERROR,
            RecommendFragmentTypeManager.VIEW_TYPE_FOCUS,
            RecommendFragmentTypeManager.VIEW_TYPE_TITLE_WITH_CYCLE,
            RecommendFragmentTypeManager.VIEW_TYPE_CHILD_PROTECT_BANNER,
            RecommendFragmentTypeManager.VIEW_TYPE_USER_RESEARCH_NEW,
            RecommendFragmentTypeManager.VIEW_TYPE_USER_RESEARCH_NEW_HEADER,
            RecommendFragmentTypeManager.VIEW_TYPE_RANK_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_RANK_LIST_NEW,
            RecommendFragmentTypeManager.VIEW_TYPE_SOCIAL_LISTEN_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_KID_ALL_LIKE,
            RecommendFragmentTypeManager.VIEW_TYPE_SOCIAL_LISTEN_LIST_AD,
            RecommendFragmentTypeManager.VIEW_TYPE_SOCIAL_LISTEN_LIST_PLAYLET_AD,
            RecommendFragmentTypeManager.VIEW_TYPE_HOT_LIVE_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_818_KOL_LIVE_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_VIDEO_LIVE_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_CHASING_FOR_UPDATE,
            RecommendFragmentTypeManager.VIEW_TYPE_MOT_ALBUM_UPDATE_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_EXPLOSIVE_CONTENT,
            RecommendFragmentTypeManager.VIEW_TYPE_CHILD_IP_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_SCENE_LISTEN_CARD,
            RecommendFragmentTypeManager.VIEW_TYPE_IP_NEW_CONTAINER_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_IP_CONTAINER_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_IP_1V4_NEW_CONTAINER_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_TAG_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_HOT_TAGS_2024,
            RecommendFragmentTypeManager.VIEW_TYPE_TAG_FIND_BOOK,
            RecommendFragmentTypeManager.VIEW_TYPE_INTEREST_CARD,
            RecommendFragmentTypeManager.VIEW_TYPE_GUESS_YOU_LIKE_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_ANCHOR_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_FREE_LISTENER,
            RecommendFragmentTypeManager.VIEW_TYPE_SHORT_VIDEO_CARD_LIST,
            RecommendFragmentTypeManager.VIEW_TYPE_SLEEP_AID_CARD,
    )

    init {
        if (RecommendFragmentAbManager.mode == RecommendFragmentAbManager.MODE_MIX) {
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_TRACK)
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_ALBUM)
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_AD_BANNER)
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_AD_MIX_ANCHOR)
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_AD_BANNER_REALTIME)
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_AD_CENTER_BIG_PIC)
            mFullSpanItemList.add(RecommendFragmentTypeManager.VIEW_TYPE_AD_CENTER_BIG_FEED)
        }
    }

    fun setData(
        data: List<Any>?,
        isRefresh: Boolean = false,
        isRefreshPreData: Boolean = false,
        firstIndex: Int = -1,
        lastIndex: Int = -1
    ) {
        data ?: return
//        if (mListData.isNotEmpty() && HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
//            //避免广告展示时多次刷新
//            PerformanceMonitor.importantLog("setData skipped >>>>>>> ")
//            return
//        }
        if (isRefresh) {
            mListData.clear()
        }
        var startPos = mListData.size
        mListData.addAll(data)
        if (isRefresh) {
            notifyDataSetChangedLog()
        } else {
            // item的isNextIsSingle在add时候写死了  最后一行就是错的  然后加载更多有数据了需要重新更新下
            if (isRefreshPreData && startPos > 0) {
                val refreshIndex = startPos - 1
                if (FixHomeFeedShowBugUtil.isFixHomeShowBug() && firstIndex >= 0 && lastIndex >= 0
                    && refreshIndex >= firstIndex && refreshIndex <= lastIndex
                ) {
                    notifyItemChanged(refreshIndex, "fixNextIsSingle")
                } else {
                    startPos--
                }
            }
            notifyItemRangeInserted(startPos, data.size)
        }
    }

    fun notifyDataSetChangedLog() {
        notifyDataSetChanged()
        PerformanceMonitor.importantLog("notifyDataSetChanged >>>>>>> ")

        if (PerformanceMonitor.isTraceOpen()) {
            android.util.Log.d("PerformanceMonitor", "notifyDataSetChangedLog: " + android.util.Log.getStackTraceString(Throwable()))
        }
    }

    fun addData(data: List<Any>?, index: Int) {
        data ?: return
        mListData.addAll(index, data)
        notifyItemRangeInserted(index, data.size)
        notifyItemRangeChanged(index, mListData.size - index)
    }

    fun removeData(position: Int) {
        mListData.removeAt(position)
        notifyItemRemoved(position)
        notifyItemRangeChanged(position, mListData.size - position)
    }

    fun updateAd(position: Int, needRemove: Boolean) {
        try {
            if (needRemove) {
                notifyItemChanged(position - 1) // 额外更新前一个item，以刷新展示分割线
                removeData(position)
            } else {
                // 更新前一个item,以刷新展示分割线
                notifyItemChanged(position - 1)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun replaceData(position: Int, data: List<Any>?) {
        data ?: return
        mListData.removeAt(position)
        mListData.addAll(position, data)
        notifyItemChanged(position)
    }

    fun getAdapterProvider(viewType: Int): Any? {
        var typeAdapter = mViewTypes.get(viewType)
        if (typeAdapter == null) {
            typeAdapter = RecommendFragmentTypeManager.getTypeAdapterProvider(viewType)
            if (viewType == RecommendFragmentTypeManager.VIEW_TYPE_FOCUS && mFragment is RecommendFragmentStaggered && typeAdapter is RecommendFocusAdapterProviderStaggered) {
                mFragment.updateFocusAdapterProvider(typeAdapter)
            }
            mViewTypes[viewType] = typeAdapter
        }
        return typeAdapter
    }

    fun traceOnItemShow(position: Int, convertView: View?) {
        try {
//            if (RecommendPageGaiaXManager.onExposure(convertView)) {
//                return
//            }
//            if (TemplateManager.getInstance().onExposure(convertView)) {
//                return
//            }
            val viewType = getItemViewType(position)
            val typeAdapter = getAdapterProvider(viewType)
            if (typeAdapter is IMulitViewTypeViewAndDataTraceStaggered<*, *>) {
                var holder: RecyclerView.ViewHolder? = null
                if (convertView?.tag is RecyclerView.ViewHolder) {
                    holder = convertView.tag as? RecyclerView.ViewHolder
                }
                (typeAdapter as? IMulitViewTypeViewAndDataTraceStaggered<RecyclerView.ViewHolder, Any>)?.traceOnItemShow(getItem(position), position, holder)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun traceOnItemShowDelay(position: Int, convertView: View?) {
        var visiblePercent = ViewStatusUtil.getViewVisibleAreaPercent(convertView)
        if (visiblePercent == 0) {
            return
        }
        try {
            var itemModel = getItem(position)
            if (itemModel is RecommendItemNew && itemModel.item is RecommendAlbumItem && convertView != null) {
                // 新首页-专辑条（0711 验证使用）  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(55662)
                    .setServiceId("slipPage")
                    .put("currPage", "newHomePage")
                    .put("exploreArea", visiblePercent.toString())
                    .put("waittime", RecommendFragmentTraceViewManager.sTraceViewDelayTime.toString())
                    .put("albumId", (itemModel.item as RecommendAlbumItem).id.toString())
                    .put(XmRequestIdManager.XM_REQUEST_ID, itemModel.xmRequestId)
                    .put(XmRequestIdManager.CONT_TYPE, "album")
                    .put(XmRequestIdManager.CONT_ID, (itemModel.item as RecommendAlbumItem).id.toString())
                    .createTrace()
            }
        } catch (e: Exception) {
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        var typeAdapter = getAdapterProvider(viewType)
        if (typeAdapter is IMulitViewTypeViewAndDataStaggered<*, *>) {
            var view: View
            try {
                view = if (typeAdapter.layoutId > 0) {
                            LayoutInflater.from(parent.context).inflate(typeAdapter.layoutId, parent, false)
                        } else {
                            typeAdapter.getView(LayoutInflater.from(parent.context), 0, parent)
                        }
            } catch (e: Exception) {
                view = View(parent.context)
                XDCSCollectUtil.statErrorToXDCS("onCreateViewHolder", "____" + typeAdapter.javaClass.name)
            }
            val holder = typeAdapter.createViewHolder(view)
            view.tag = holder
            PerformanceMonitor.traceEnd("SA_onCreateViewHolder_" + typeAdapter.javaClass.simpleName, 1)
            return holder
        }
        return DefaultViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.main_item_recommend_default_two_column, parent, false))
    }

    override fun getItem(position: Int): Any? {
        return mListData?.getOrNull(position)
    }

    override fun getItemCount(): Int {
        return mListData?.size ?: 0
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val itemData = getItem(position)
        val typeAdapter = getAdapterProvider(getItemViewType(position))
        PerformanceMonitor.traceBegin("SA_onBindViewHolder_" + typeAdapter?.javaClass?.simpleName)
        setParams(holder)
        if (itemData is RecommendItemNew) {
            itemData.isHasShow = true
            saveModuleHistory(itemData)
        } else if (itemData is ItemModel<*> && itemData.getObject() is RecommendItemNew) {
            (itemData.getObject() as? RecommendItemNew)?.isHasShow = true
            saveModuleHistory(itemData.getObject() as? RecommendItemNew)
        } else if (itemData is ItemModel<*> && itemData.getTag() is RecommendItemNew) {
            // 处理下广告的逻辑
            val recommendItem = itemData.getTag()
            if (recommendItem is RecommendItemNew &&
                    RecommendItemNew.RECOMMEND_ITEM_MODULE == recommendItem.itemType && recommendItem.item is RecommendModuleItem
                    && (RecommendModuleItem.RECOMMEND_TYPE_AD == (recommendItem.item as RecommendModuleItem).moduleType || RecommendModuleItem.RECOMMEND_TYPE_AD_MIX == (recommendItem.item as RecommendModuleItem).moduleType)) {
                recommendItem.isHasShow = true
                mHasShownAdItemSet.add(recommendItem)
                addShowModuleId((recommendItem.item as RecommendModuleItem).moduleId)
            }
        }
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
//            PerformanceMonitor.importantLog("RecommendFragmentAdapter bindViewHolder post")

            HandlerManager.postOnUIThread {
                try {
                    (typeAdapter as? IMulitViewTypeViewAndDataStaggered<RecyclerView.ViewHolder, Any>)?.bindViewHolder(holder, position, itemData, holder.itemView)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            try {
                (typeAdapter as? IMulitViewTypeViewAndDataStaggered<RecyclerView.ViewHolder, Any>)?.bindViewHolder(holder, position, itemData, holder.itemView)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        PerformanceMonitor.traceEnd("SA_onBindViewHolder_" + typeAdapter?.javaClass?.simpleName, 2)
    }

    private fun setParams(holder: RecyclerView.ViewHolder) {
        var layoutParams = holder.itemView.layoutParams
        if (layoutParams is StaggeredGridLayoutManager.LayoutParams) {
            layoutParams.isFullSpan = false
            val position = holder.layoutPosition
            // todo: 也可以考虑从模板里面去取，那么可以直接通过ViewHolder拿到这个信息
            // 如果是上拉加载或者下拉刷新以及头部模块，则设置setFullSpan为true，那么它就会占一行
            val itemViewType = getItemViewType(position)
            val data = getItem(position)
//            val isFullSpan = RecommendPageVirtualViewManager.isFullSpan(itemViewType) ?: RecommendPageGaiaXManager.isFullSpan(itemViewType)
//            if (isFullSpan != null) {
//                layoutParams.isFullSpan = isFullSpan
//            } else {
//            }
            if (mFullSpanItemList.contains(itemViewType)) {
                layoutParams.isFullSpan = true
            } else if (data is RecommendItemNew && data.moduleFrom == RecommendItemNew.FROM_HEADER) {
                layoutParams.isFullSpan = true
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        val itemData = getItem(position) ?: return RecommendFragmentTypeManager.VIEW_TYPE_DEFAULT
        return RecommendFragmentTypeManager.getItemViewType(itemData)
    }

    private fun saveModuleHistory(recommendItemNew: RecommendItemNew?) {
        recommendItemNew ?: return
        if (recommendItemNew.moduleFrom != RecommendItemNew.FROM_BODY) {
            return
        }
        if (RecommendItemNew.RECOMMEND_ITEM_MODULE == recommendItemNew.itemType) {
            (recommendItemNew.item as? RecommendModuleItem)?.let {
                addShowModuleId(it.moduleId)
            }
        }
    }

    private fun addShowModuleId(moduleId: Long) {
        if (moduleId > 0) {
            mHasShowModuleIdList.add(moduleId)
        }
    }

    fun clearHasShowModules() {
        mHasShowModuleIdList.clear()
    }

    fun getHasShownAdItemNum(): Int {
        return mHasShownAdItemSet.size
    }

    fun clearHasShownAdItems() {
        mHasShownAdItemSet.clear()
    }

    // 由于有预加载，可能出现上滑拉下一页接口时，部分moduleid未添加到浏览历史里，这时候需要将上一页moduleid都添加进去
    fun getHasShowModuleIds(isLoadMore: Boolean, lastSavedBody: List<RecommendItemNew?>?): String? {
        if (isLoadMore && !ToolUtil.isEmptyCollects(lastSavedBody)) {
            for (recommendItemNew in lastSavedBody!!) {
                saveModuleHistory(recommendItemNew)
            }
        }
        val builder = StringBuilder()
        for (moduleId in mHasShowModuleIdList) {
            builder.append(moduleId).append(",")
        }
        if (!TextUtils.isEmpty(builder)) {
            builder.deleteCharAt(builder.length - 1)
        }
        return builder.toString()
    }

    fun onResume() {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataWithLifecircleStaggered<*, *>) {
                // TODO: 看下广告加载的时候有没有执行、耗时
                (viewTypeAndData as? IMulitViewTypeViewAndDataWithLifecircleStaggered<RecyclerView.ViewHolder, Any>)?.onResume()
            }
        }
        SceneListenAnimaUtil.onResume()
    }

    fun onPause() {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataWithLifecircleStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataWithLifecircleStaggered<RecyclerView.ViewHolder, Any>)?.onPause()
            }
        }
        SceneListenAnimaUtil.onPause()
    }

    fun onToListenDeleted(deletedId: Long) {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataToListenStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataToListenStaggered<RecyclerView.ViewHolder, Any>)?.isToListenDeleted(deletedId)
            }
        }
    }

    fun onToListenAdded(addedTrack: SimpleTrackForToListen, index: Int) {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataToListenStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataToListenStaggered<RecyclerView.ViewHolder, Any>)?.isToListenAdded(addedTrack, index)
            }
        }
    }

    fun onPlayStart() {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataPlayStatusStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataPlayStatusStaggered<RecyclerView.ViewHolder, Any>)?.onPlayStart()
            }
        }
    }

    fun onPlayPause() {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataPlayStatusStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataPlayStatusStaggered<RecyclerView.ViewHolder, Any>)?.onPlayPause()
            }
        }
    }

    fun onSoundPlayComplete() {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataPlayStatusStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataPlayStatusStaggered<RecyclerView.ViewHolder, Any>)?.onSoundPlayComplete()
            }
        }
    }

    fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataPlayStatusStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataPlayStatusStaggered<RecyclerView.ViewHolder, Any>)?.onSoundSwitch(lastModel, curModel)
            }
        }
    }

    fun onPlayProgress(currPos: Int, duration: Int) {
        for (entry in mViewTypes) {
            val viewTypeAndData = entry.value
            if (viewTypeAndData is IMulitViewTypeViewAndDataPlayStatusStaggered<*, *>) {
                (viewTypeAndData as? IMulitViewTypeViewAndDataPlayStatusStaggered<RecyclerView.ViewHolder, Any>)?.onPlayProgress(currPos, duration)
            }
        }
    }

    fun onConfigurationChanged(position: Int, convertView: View?) {
        try {
            val viewType = getItemViewType(position)
            val typeAdapter = getAdapterProvider(viewType)
            if (typeAdapter is IMulitViewTypeViewConfigurationChanged<*, *>) {
                var holder: RecyclerView.ViewHolder? = null
                if (convertView?.tag is RecyclerView.ViewHolder) {
                    holder = convertView.tag as? RecyclerView.ViewHolder
                }
                (typeAdapter as? IMulitViewTypeViewConfigurationChanged<RecyclerView.ViewHolder, Any>)?.onConfigurationChanged(holder)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    class DefaultViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        init {
            view.setTag(R.id.main_staggered_default_holder, true)
        }
    }
}