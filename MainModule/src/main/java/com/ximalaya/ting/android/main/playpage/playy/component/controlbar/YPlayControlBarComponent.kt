package com.ximalaya.ting.android.main.playpage.playy.component.controlbar

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.os.IBinder
import android.view.HapticFeedbackConstants
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updatePadding
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.model.KeyPath
import com.airbnb.lottie.value.LottieValueCallback
import com.tmall.wireless.vaf.virtualview.Helper.StringUtils.getFriendlyNumberTime
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.AudioInfoTraceUtil
import com.ximalaya.ting.android.host.manager.PlanTerminateManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.PlayerManager
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.host.view.XmLottieDrawable
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayUtil
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.ISkinAdDataChangeCallBack
import com.ximalaya.ting.android.main.playpage.audioplaypage.isLargeDevice
import com.ximalaya.ting.android.main.playpage.dialog.kidvip.KidSleepVipDialogManager
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayControlComponentService
import com.ximalaya.ting.android.main.playpage.internalservice.ICoverComponentsManagerService
import com.ximalaya.ting.android.main.playpage.internalservice.ISeekBarComponentService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.PlayTypeChangeListener
import com.ximalaya.ting.android.main.playpage.playy.XPlayPageStatus
import com.ximalaya.ting.android.main.playpage.playy.YUtils
import com.ximalaya.ting.android.main.playpage.playy.component.controlbar.view.TouchableViewGroup
import com.ximalaya.ting.android.main.playpage.playy.listener.UnableToPlayStatusAggregator
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinConfig
import com.ximalaya.ting.android.main.playpage.playy.skin.PSkinManager
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager.IOnPlayModeChange
import com.ximalaya.ting.android.opensdk.player.service.IXmDataCallback
import com.ximalaya.ting.android.opensdk.player.service.IXmPlanTerminateListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl.PlayMode
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import kotlin.math.max
import kotlin.math.min

/**
 * Created by yang.zhang on 2023-03-08.
 */
class YPlayControlBarComponent(
    private val playContainer: IPlayContainer,
) : YPlaySimpleControlBar(playContainer), PlayTypeChangeListener, IOnPlayModeChange  {
    private var mXPlayControlBarWithoutSeekBar: LinearLayout?= null
    var mIvPlayPrevBtn: XmLottieAnimationView? = null
    var mIvPlayNextBtn: XmLottieAnimationView? = null
    var mIvPlayListBtn: ImageView? = null
    private var mVgBackwardBtn: XmLottieAnimationView? = null
    private var mVgForwardBtn: XmLottieAnimationView? = null

    private var mPlayListMask:View?=null

    private var mIvTimerOff: ImageView? = null
    private var mVgTimerOff: ConstraintLayout? = null
    private var mTvTimerOff: TextView? = null

    private var mPlayToPauseLottieDrawable: XmLottieDrawable? = null
    private var mPauseToPlayLottieDrawable: XmLottieDrawable? = null
    private var controlViewGroup: TouchableViewGroup? = null

    private var currentPlayMode: PlayMode = XmPlayerManager.getInstance(context).playMode
    private var leftTime: Int = 0
    private var leftSeries: Int = 0

    override fun init(vAncestorView: ViewGroup) {
        controlViewGroup = vAncestorView.findViewById(R.id.main_play_control)
        controlViewGroup?.setOnClickListener {  }
        mContentView = controlViewGroup
        initUi()
        playContainer.addPlayTypeChangeListener(this)

        XmPlayerManager.getInstance(context).addPlayModeChangeListner(this)
    }

    override fun initUi() {
        super.initUi()
        mIvPlayPrevBtn = findViewById(R.id.main_iv_play_prev_btn)
        mIvPlayNextBtn = findViewById(R.id.main_iv_play_next_btn)
        mVgBackwardBtn = findViewById(R.id.main_vg_play_backward_btn)
        mVgForwardBtn = findViewById(R.id.main_vg_play_forward_btn)
        mIvTimerOff = findViewById<ImageView>(R.id.main_iv_timer_off)
        mVgTimerOff = findViewById(R.id.main_vg_timer_off_btn)
        mTvTimerOff =  findViewById<TextView>(R.id.main_tv_timer_off)
        mPlayListMask =  findViewById<TextView>(R.id.main_iv_play_list_btn_audio_mask_view)

        updateColor(backgroundColor)
        mXPlayControlBarWithoutSeekBar = findViewById(R.id.main_vg_control_bar)
        mIvPlayListBtn = findViewById(R.id.main_iv_play_list_btn_audio)

        setOnClickListenerAndBindAutoTraceData(mPlayListMask)
        ViewUtil.expandClickArea(mContext, mPlayListMask, 5, 5, 5, 5)

        setOnClickListenerAndBindAutoTraceData(mIvPlayPrevBtn)
//        mIvPlayPrevBtn?.setOnTouchListener(ScaleTouchListener(mIvPlayPrevBtn))
        setOnClickListenerAndBindAutoTraceData(mIvPlayNextBtn)
//        mIvPlayNextBtn?.setOnTouchListener(ScaleTouchListener(mIvPlayNextBtn))
        setOnClickListenerAndBindAutoTraceData(mVgBackwardBtn)
        setOnClickListenerAndBindAutoTraceData(mVgForwardBtn)


        setOnClickListenerAndBindAutoTraceData(mTvTimerOff)
        setOnClickListenerAndBindAutoTraceData(mIvTimerOff)
        ViewUtil.expandClickArea(mContext, mIvTimerOff, 5, 5, 5, 5)
        PlayPageInternalServiceManager.getInstance().registerService(
            IAudioPlayControlComponentService::class.java,
            IAudioPlayControlComponentService { updateNextAndPreBtnStatus() })
        if (isLargeDevice()) {
            adapterForLargeDevice()
        }

        PlayPageInternalServiceManager.getInstance().registerService(
            ISeekBarComponentService::class.java, this
        )
        enableTimeOff(playContainer.isAudioMode())
    }

    override fun onSkinChanged(newSkinConfig: PSkinConfig?) {
        super.onSkinChanged(newSkinConfig)
        if (PSkinManager.isEnabled && PSkinManager.getBtnThemeColor() != 0) {
            mIvPlayPrevBtn?.setForegroundColor(PSkinManager.getBtnThemeColor())
            mIvPlayNextBtn?.setForegroundColor(PSkinManager.getBtnThemeColor())
            mIvPlayListBtn?.setColorFilter(PSkinManager.getBtnThemeColor())
            mVgBackwardBtn?.setForegroundColor(PSkinManager.getBtnThemeColor())
            mVgForwardBtn?.setForegroundColor(PSkinManager.getBtnThemeColor())
            mIvTimerOff?.setColorFilter(PSkinManager.getBtnThemeColor())
            mTvTimerOff?.setTextColor(PSkinManager.getBtnThemeColor())
        } else {
            mIvPlayPrevBtn?.clearForegroundColor()
            mIvPlayNextBtn?.clearForegroundColor()
            mIvPlayListBtn?.clearColorFilter()
            mVgBackwardBtn?.clearForegroundColor()
            mVgForwardBtn?.clearForegroundColor()
            mIvTimerOff?.clearColorFilter()
            mTvTimerOff?.setTextColor(Color.WHITE)
        }
    }

    fun updateSeekBarRestoreDelay(delay: Long) {
        mSeekBar?.setRestoreTimeMS(delay)
    }

    fun cancelSeekbarWithDelay() {
        mSeekBar?.cancelWithDelay()
    }

    override fun onDestroy() {
        super.onDestroy()

        PlayPageInternalServiceManager.getInstance().unRegisterService(IAudioPlayControlComponentService::class.java)
        PlayPageInternalServiceManager.getInstance().unRegisterService(ISeekBarComponentService::class.java)
        XmPlayerManager.getInstance(context).removePlayModeChangeListner(this)
    }

    private fun adapterForLargeDevice() {
        if (mContentView != null) {
            val layoutParams = mContentView?.layoutParams
            if (layoutParams is MarginLayoutParams) {
                layoutParams.topMargin = BaseUtil.dp2px(
                    context, 7f
                )
            }
        }
    }

    private fun setOnClickListenerAndBindAutoTraceData(view: View?) {
        val clickView = view?: return
        clickView.setOnClickListener(mOnClickListener)
        AutoTraceHelper.bindDataCallback(clickView, object : AutoTraceHelper.IDataProvider {
            override fun getData(): Any? {
                return curSoundInfo
            }

            override fun getModule(): Any? {
                return null
            }

            override fun getModuleType(): String? {
                return null
            }
        })
    }

    private fun updateColor(backgroundColor: Int) {
        setPlayBtnCenterIconColor(backgroundColor)
    }

    fun setPlayBtnCenterIconColor(color: Int) {
        if (mPlayToPauseLottieDrawable != null) {
            mPlayToPauseLottieDrawable?.addValueCallback(
                KeyPath("**"), LottieProperty.COLOR_FILTER,
                LottieValueCallback<ColorFilter>(PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN))
            )
        }
        if (mPauseToPlayLottieDrawable != null) {
            mPauseToPlayLottieDrawable?.addValueCallback(
                KeyPath("**"), LottieProperty.COLOR_FILTER,
                LottieValueCallback<ColorFilter>(PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN))
            )
        }
    }

    fun updateNextAndPreBtnStatus() {
        if (!canUpdateUi()) {
            return
        }
        var hasNext = XmPlayerManager.getInstance(mContext).hasNextSound()
        var hasPrevious = XmPlayerManager.getInstance(mContext)
            .hasPreSound()
        val playMode = XmPlayerManager.getInstance(mContext).playMode
        if (playMode == PlayMode.PLAY_MODEL_LIST_LOOP
            && !XmPlayerManager.getInstance(mContext).playList.isEmpty()
        ) {
            hasNext = true
            hasPrevious = true
        } else if (isFromOneKeyPlay) {
            hasNext = true
        }

        playContainer.updateNextAndPreBtnStatus(hasNext, hasPrevious)
        if (mIvPlayNextBtn != null && mIvPlayPrevBtn != null) {
            mIvPlayNextBtn?.isEnabled = hasNext
            mIvPlayNextBtn?.alpha = if(hasNext) 1f else 0.2f

            mIvPlayPrevBtn?.isEnabled = hasPrevious
            mIvPlayPrevBtn?.alpha = if(hasPrevious) 1f else 0.2f
        }
    }

    private val isFromOneKeyPlay: Boolean
        private get() {
            val currPlayableModel = XmPlayerManager.getInstance(activity)
                .currSound
            return if (null != currPlayableModel) {
                // 一键听声音
                PlayableModel.KIND_TRACK == currPlayableModel.kind && (currPlayableModel as Track).playSource == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY
            } else false
        }

    override fun onThemeColorChanged(foregroundColor: Int, backgroundColor: Int) {
        super.onThemeColorChanged(foregroundColor, backgroundColor)
        updateColor(backgroundColor)
    }


    override fun onResume() {
        super.onResume()
        updateNextAndPreBtnStatus()
        // 解决一开始播放列表可能还没加载，状态不对的问题。也试过直接监听播放列表改变，但是不行，还没分析。
        HandlerManager.postOnUIThreadDelay(mUpdateNextAndPreBtnTask, 500)
        XmPlayerManager.getInstance(mContext).addPlayListChangeListener(mXmDataCallback)
        XmPlayerManager.getInstance(mContext).addPlanTerminateListener(mPlanTerminateListener)
        if(!PlanTerminateManager.isTiming() || playContainer.isVideoMode() || PlanTerminateManager.isTimerContinuePlaying()) {
            resetTimerOffUi()
        }
    }

    override fun onPause() {
        super.onPause()
        PlayPageInternalServiceManager.getInstance().unRegisterService(ISkinAdDataChangeCallBack::class.java)
        HandlerManager.removeCallbacks(mUpdateNextAndPreBtnTask)
        XmPlayerManager.getInstance(mContext).removePlanTerminateListener(mPlanTerminateListener)
        XmPlayerManager.getInstance(mContext).removePlayListChangeListener(mXmDataCallback)
    }

    override fun onPlayStart() {
        super.onPlayStart()
    }

    override fun fullScreen(xPlayPageStatus: XPlayPageStatus) {
        super.fullScreen(xPlayPageStatus)
        val full = xPlayPageStatus.isFullScreen

        setXPlayControlBarWithoutSeekBarMargin(full)

        if (full) {
            ViewStatusUtil.setVisible(View.VISIBLE, seekBarGroup)
        } else if (shouldHideSeekbar) {
            ViewStatusUtil.setVisible(View.GONE, seekBarGroup)
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, seekBarGroup)
        }
    }

    fun setXPlayControlBarWithoutSeekBarMargin(full: Boolean) {
        if (mXPlayControlBarWithoutSeekBar?.layoutParams is MarginLayoutParams) {
            (mXPlayControlBarWithoutSeekBar?.layoutParams as MarginLayoutParams)?.topMargin = if (full) 28.dp else 7.dp
        }
    }


    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        super.onSoundSwitch(lastModel, curModel)
        HandlerManager.postOnUIThreadDelay(mUpdateNextAndPreBtnTask, 500)
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        if(!PlanTerminateManager.isTiming() || playContainer.isVideoMode()) {
            resetTimerOffUi()
        }

        if (soundInfo != null && YUtils.isNoTab(soundInfo)) {
            controlViewGroup?.updatePadding(bottom = 36.dp)
        } else {
            controlViewGroup?.updatePadding(bottom = 18.dp)
        }
    }

    override fun onStatusChange(
        ableToPlay: Boolean,
        reason: Set<UnableToPlayStatusAggregator.UnablePlayReason>,
    ) {
        if (playContainer.isAudioMode()) {
            super.onStatusChange(ableToPlay, reason)
        }
        enableTimeOff(reason.isEmpty() && playContainer.isAudioMode())
    }

    override fun onPlayTypeChanged(isAudio: Boolean) {
        super.onPlayTypeChanged(isAudio)
        enableTimeOff(isAudio)
    }

    override fun onPlayModeChange(playMode: PlayMode) {
        currentPlayMode = playMode
        if (leftTime > 0 && leftSeries > 0) {
            updateTimerOffUi(leftTime, leftSeries, currentPlayMode)
        }
//        updateTimerOffUi(0, series, currentPlayMode)
//        if (playMode == PlayMode.PLAY_MODEL_RANDOM) {
//            resetTimerOffUi()
//        }
    }

    private val mPlanTerminateListener: IXmPlanTerminateListener =
        object : IXmPlanTerminateListener {
            override fun asBinder(): IBinder? {
                return null
            }

            override fun onTimeout(type: Int) {
                resetTimerOffUi()
            }

            override fun onLeftTimeChanged(leftTime: Int, type: Int) {
                <EMAIL> = leftTime
                if (canUpdateUi() && currentPlayMode != PlayMode.PLAY_MODEL_RANDOM) {
                    updateTimerOffUi(leftTime, leftSeries, currentPlayMode)
                }
            }

            override fun onLeftSeriesChanged(series: Int, type: Int) {
                <EMAIL> = series
                if (canUpdateUi() && currentPlayMode == PlayMode.PLAY_MODEL_RANDOM) {
                    updateTimerOffUi(leftTime, series, currentPlayMode)
                }
            }
            override fun onCancel() {
                resetTimerOffUi()
            }
        }


    private fun enableTimeOff(enable: Boolean) {
        mVgTimerOff?.isClickable = enable

        mTvTimerOff?.isClickable = enable
        mIvTimerOff?.isClickable = enable

        if (enable) {
            mIvTimerOff?.alpha = 1f
            mTvTimerOff?.alpha = 1f
        } else {
            mIvTimerOff?.alpha = 0.15f
            mTvTimerOff?.alpha = 0.15f
        }
    }

    private fun checkCopyright(): Boolean {
        val soundInfo = curSoundInfo
        return if (soundInfo?.trackInfo2TrackM() != null) {
            soundInfo.trackInfo2TrackM().isHasCopyRight
        } else true
    }

    override fun onStartGetAdsInfo(playMethod: Int, duringPlay: Boolean, isPaused: Boolean) {
        if (!XmPlayerManager.getInstance(context).isPlaying) {
            updateNextAndPreBtnStatus()
            if (!isPaused && !duringPlay) {
//                showLoadingView(true)
            }
        }
    }

    private val mOnClickListener = View.OnClickListener { v: View? ->
        if (!OneClickHelper.getInstance().onClick(v)) {
            return@OnClickListener
        }
        if (!checkCopyright() &&v != mIvPlayPrevBtn && v!=mIvPlayNextBtn) {
            CustomToast.showFailToast(PlayPageDataManager.getInstance().noCopyrightMsg)
            return@OnClickListener
        }
        val isVideo = playContainer.isVideoMode()

        if (v === mIvPlayListBtn || v === mPlayListMask) {
            //播放列表
            playContainer.showPlayList()
//            playContainer.showPlayList(YPlayListAndHistoryFragment.newInstance().apply {
//                setDismissCallback { updateNextAndPreBtnStatus()  }
//            })
            traceControlViewClick("播放列表")
        } else if (v === mIvPlayPrevBtn) {
            mIvPlayPrevBtn?.playAnimation()
            // 新声音播放页-上一集  点击事件
            XMTraceApi.Trace()
                .click(17452) // 用户点击时上报
                .put("trackId", curTrackId.toString())
                .put("albumId", curAlbumId.toString())
                .put("categoryId", curSoundInfo?.trackInfo?.categoryId?.toString())
                .put("anchorId", curAnchorId.toString())
                .put("currTrackId", curTrackId.toString())
                .put("currAlbumId", curAlbumId.toString())
                .put("currPage", "newPlay")
                .put("trackForm", if (isVideo) "video" else "track") // track 表示音频，video 表示视频
                .put("fullScreenMode", if (playContainer.isFullScreen()) "full" else "half") // full 表示全屏，half 表示半屏
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                )
                .createTrace()
            if (isVideo) {
                XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).playPre(false)
            } else {
                UserInteractivePlayStatistics.clickPlay(-1, "clickPre")
                AudioPlayUtil.playPrev(context)
            }
            traceControlViewClick("上一曲")
        } else if (v === mIvPlayNextBtn) {
            mIvPlayNextBtn?.playAnimation()
            // 新声音播放页-下一集  点击事件
            XMTraceApi.Trace()
                .click(17453) // 用户点击时上报
                .put("trackId", curTrackId.toString())
                .put("albumId", curAlbumId.toString())
                .put("categoryId", curSoundInfo?.trackInfo?.categoryId?.toString())
                .put("anchorId", curAnchorId.toString())
                .put("currTrackId", curTrackId.toString())
                .put("currAlbumId", curAlbumId.toString())
                .put("currPage", "newPlay")
                .put("trackForm", if (isVideo) "video" else "track") // track 表示音频，video 表示视频
                .put("fullScreenMode", if (playContainer.isFullScreen()) "full" else "half") // full 表示全屏，half 表示半屏
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                )
                .createTrace()
            if (isVideo) {
                XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).playNext(false)
            } else {
                UserInteractivePlayStatistics.clickPlay(-1, "clickNext")
                AudioPlayUtil.playNext(context)
            }
            traceControlViewClick("下一曲")
        } else if (v === mVgBackwardBtn) {
            mVgBackwardBtn?.playAnimation()
            if (isVideo) {
                playContainer.videoComponent()?.backward15Second()
            } else {
                handleBackwardClicked()
            }

        } else if (v === mVgForwardBtn) {
            mVgForwardBtn?.playAnimation()
            if (isVideo) {
                playContainer.videoComponent()?.forward15Second()
            } else {
                handleForwardClicked()
            }
        } else if (v === mVgTimerOff || v === mIvTimerOff || v === mTvTimerOff) {
            if (isVideo) {
                CustomToast.showToast("视频不支持定时")
            } else {
                playContainer.openTimingOffPanel()
            }
        }
        v?.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
    }

    private var isSleeping = false

    private fun resetTimerOffUi() {
        <EMAIL> = 0
        <EMAIL> = 0

        mIvTimerOff?.tag = null
        if (canUpdateUi()) {
            mTvTimerOff.setTextIfChanged("")
            mIvTimerOff?.contentDescription = getString(R.string.main_timing_shutdown)

            isSleeping = curSoundInfo?.otherInfo?.isSleeping == true && KidSleepVipDialogManager.isSleepShowTime()
            mIvTimerOff?.setImageResource(if (isSleeping) R.drawable.main_ic_y_babysleep_n_n_line_regular_26 else R.drawable.main_ic_y_time_off_line_regular_26)
        }
    }

    private fun updateTimerOffUi(leftTime: Int, leftSeries: Int, playMode: PlayMode) {

        if (playMode == PlayMode.PLAY_MODEL_RANDOM) {
            if (leftSeries > 0) {
                mTvTimerOff?.text = String.format("播完%d集", leftSeries)
                mIvTimerOff?.contentDescription = String.format("定时播完%d集", leftSeries)
            } else {
                mTvTimerOff?.text = ""
                mIvTimerOff?.contentDescription = ""
            }
        } else {
            val timeFormatted = TimeHelper.toTime(leftTime.toDouble(), 0)
            if (timeFormatted == null || timeFormatted.isEmpty()) {
                mTvTimerOff?.text = ""
            } else {
                mTvTimerOff?.text = timeFormatted
            }

            if (timeFormatted != null) {
                val timeArray = timeFormatted.split(":".toRegex()).toTypedArray()
                if (timeArray.size == 2) {
                    mIvTimerOff?.contentDescription = String.format("定时%s分%s秒", timeArray[0], timeArray[1])
                }
            }
        }

        val timerIv = mIvTimerOff?: return
        val drawableRes= if (isSleeping) {
            R.drawable.main_new_ic_baby_sleep_time_on_line_regular_26
        } else {
            R.drawable.main_new_ic_time_on_line_regular_26
        }
        if (timerIv.tag != drawableRes) {
            timerIv.setImageResource(drawableRes)
        }
    }

    private fun handleForwardClicked() {
        val pos = XmPlayerManager.getInstance(context).playCurrPositon + 15 * 1000
        val targetPos = min(pos.toDouble(), XmPlayerManager.getInstance(context).duration.toDouble()).toInt()
        traceControlViewClick("前进15秒", pos, targetPos)
        AudioPlayUtil.forward15Second(context)
        // 暂停状态下进度改变没有回调，主动通知一下SeekBarComponent去更新
    }

    private fun handleBackwardClicked() {
        var pos = XmPlayerManager.getInstance(context).playCurrPositon - 15 * 1000
        val targetPos = max(pos.toDouble(), 0.0).toInt()
        traceControlViewClick("后退15秒", pos, targetPos)
        AudioPlayUtil.backward15Second(context)
    }

    /**
     * 播控区域点击埋点
     * @param clickType 进度条|后退15秒|前进15秒|播放列表|倍速播放|咔嚓|音效|更多
     */
    private fun traceControlViewClick(clickType: String, curPlayPosition: Int = -1, targetProgress: Int = -1) {
        val coverComponentsManagerService = PlayPageInternalServiceManager.getInstance().getService(
            ICoverComponentsManagerService::class.java
        )
        var isCoverFullscreen = false
        if (coverComponentsManagerService != null) {
            isCoverFullscreen = coverComponentsManagerService.isFullscreen()
        }
        XMTraceApi.Trace()
            .click(49674) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("Item", clickType)
            .put("screenDirection", if (isCoverFullscreen) "全屏" else "") // 全屏则传全屏，其他情况传空
            .put("currAlbumId", curAlbumId.toString() + "")
            .put("currTrackId", curTrackId.toString() + "")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .apply {
                AudioInfoTraceUtil.interceptTrace(this)

                if (curPlayPosition >= 0) {
                    put("listenProgress", getFriendlyNumberTime(curPlayPosition.toLong() / 1000).toString())
                }
                if (targetProgress >= 0) {
                    put("targetProgress", getFriendlyNumberTime(targetProgress.toLong() / 1000).toString())
                }
            }
            .createTrace()
    }

    private val mUpdateNextAndPreBtnTask = Runnable { updateNextAndPreBtnStatus() }
    private val mXmDataCallback: IXmDataCallback = object : IXmDataCallback {

        override fun onListChange() {
            updateNextAndPreBtnStatus()
        }


        override fun onDataReady(list: List<Track>?, hasMorePage: Boolean, isNextPage: Boolean) {
        }

        override fun onError(code: Int, message: String, isNextPage: Boolean) {
        }

        override fun asBinder(): IBinder? {
            return null
        }
    }

    fun doPlayListAnimator(doAnimator: Boolean) {
        if (doAnimator) {
            realDoPlayListAnimator()
        }
        updateNextAndPreBtnStatus()
    }

    private fun realDoPlayListAnimator() {
        if (mIvPlayListBtn != null) {
            AnimatorSet().apply {
                playSequentially(
                    ValueAnimator.ofFloat(1.0f, 1.4f).apply {
                        this.duration = 288
                        this.addUpdateListener {
                            mIvPlayListBtn?.scaleX = it.animatedValue as Float
                            mIvPlayListBtn?.scaleY = it.animatedValue as Float
                        }
                    },
                    ValueAnimator.ofFloat(1.4f, 1.0f).apply {
                        this.duration = 200
                        this.addUpdateListener {
                            mIvPlayListBtn?.scaleX = it.animatedValue as Float
                            mIvPlayListBtn?.scaleY = it.animatedValue as Float
                        }
                    }
                )
            }.start()
        }
    }

    companion object {
        fun newInstance(
            fragment: BaseFragment2?,
            playContainer: IPlayContainer,
            controlBar: YPlayControlBar
        ): YPlayControlBarComponent {
            val component = YPlayControlBarComponent(playContainer)
            component.onCreate(fragment)
            component.setXPlayControlBar(controlBar)
            return component
        }
    }
}