# To enable ProGuard in your project, edit project.properties
# to define the proguard.config property as described in that file.
#
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in ${sdk.dir}/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the ProGuard
# include property in project.properties.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-keepclassmembers class * extends android.webkit.WebChromeClient {
    public void openFileChooser(...);
}

-ignorewarnings
-optimizationpasses 5
-dontusemixedcaseclassnames #【混淆时不会产生形形色色的类名 】
-dontskipnonpubliclibraryclasses #【指定不去忽略非公共的库类。 】
-dontpreverify #【不预校验】
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/* #【优化】
#开启 shrink 模式，某些地方还有问题 某些没有被直接引用的方法或者类会被优化掉 比如native层的反射
#开启压缩，同样会将仅有远程下载的bundle调用的host 代码压缩掉，这部分代码预计比较多也很难全部找出来，暂时关闭压缩
#-dontshrink
# ProGuard configurations for Bugtags
-keepattributes LineNumberTable,SourceFile

#keep kotlin ref etc
-keep interface kotlin.reflect.jvm.internal.impl.builtins.BuiltInsLoader
-keep class kotlin.reflect.jvm.internal.impl.serialization.deserialization.builtins.BuiltInsLoaderImpl
-keep public class kotlin.reflect.jvm.internal.impl.serialization.deserialization.builtins.* { public *; }
-keep class kotlin.reflect.jvm.internal.impl.load.java.FieldOverridabilityCondition
-keep class kotlin.reflect.jvm.internal.impl.load.java.** {
    *;
}
# 保留 ServiceLoader 加载的类（防止 SPI 机制失效）
-keepnames class kotlin.reflect.jvm.internal.impl.load.java.** {
    public <init>(...);
}
#end keep kotlin ref etc
#android默认项
-keep public class * extends android.widget.BaseAdapter
-keep public class * extends androidx.fragment.app.Fragment
-keep public class * extends androidx.fragment.app.FragmentActivity
-keep public class * extends android.app.Fragment
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
#保留所有自定义的view 的类名 可视化埋点需要
-keep class * extends android.view.View

#子项目化各模块入口类
-keep class com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication{*;}
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication {*;}
#-keep public class com.ximalaya.ting.android.host.HostApplication {*;}
-keep public class com.ximalaya.ting.android.host.hybridviewmodule.HybridViewApplication{*;}
#子项目化各模块入口类 end

# 首页 keep 全部keep的原因待查
-keep public class com.ximalaya.ting.android.host.activity.MainActivity {*;}
# end 首页

# application 反射调用 by com.ximalaya.ting.android.xmnetmonitor.core.NetworkMonitorAspect
-keep public class com.ximalaya.ting.android.framework.BaseApplication {*;}
#
#崩溃日志上报
#官方推荐混淆规则：
#-dontwarn com.tencent.bugly.**
#-keep public class com.tencent.bugly.**{*;}
-keep public class com.tencent.bugly.crashreport.CrashReport {*;}
#
# host 下数据model
-dontwarn com.ximalaya.ting.android.host.data.model.**
-keep class com.ximalaya.ting.android.host.data.model.** {*;}
# end
# open sdk 下model
-keep public class com.ximalaya.ting.android.opensdk.model.**{*;}
# end

# 这里有底层反射调用，方法名不能混淆
#-keep class com.ximalaya.ting.android.encryptservice.DeviceTokenUtil {*;}
-keep class com.ximalaya.ting.android.xmutil.BaseDeviceUtil {*;}
#
#** androidx 下面的混淆
#支持包的keep 需进一步调查是否有需要
-keep public class androidx.core.content.ContextCompat {*;}
#-keep class androidx.appcompat.widget.** {*;}
# 一下两个类被反射调用
-keep public class androidx.appcompat.widget.TintContextWrapper {
    <fields>;
}
-keep class androidx.appcompat.widget.ResourcesWrapper {
    <fields>;
}
# end

# plugin 和 patch ：
-keep class com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel {
    *;
}
-keep class com.ximalaya.ting.android.host.model.plugin.** {*;}
-keep class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keepclasseswithmembers class com.ximalaya.ting.android.host.manager.bundleframework.BundleManager.BundleInfoManager{
   public static com.ximalaya.ting.android.host.manager.bundleframework.BundleManager.BundleInfoManager getInstance();
   public  void syncInstallBundleByName(...);
}
# end

#** xmloader相关类 类加载相关
-keep class com.ximalaya.ting.android.xmloader.XMPatchLoader{*;}
# 主application
-keep public class com.ximalaya.ting.android.host.MainApplication {*;}

# end

# 所有bundle 的入口类，需要反射调用
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication{*;}
# end
# router 自动生成类 需反射
# router 自动生成的类派生自这些接口
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.ISyringe{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IInterceptor{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IInterceptorGroup{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IPolicy{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IProvider{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IProviderGroup{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IRouteGroup{*;}
-keep class * implements com.ximalaya.ting.android.framework.arouter.facade.template.IRouteRoot{*;}


#
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication {*;}
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.listener.IActionRouter {*;}
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction {*;}
-keep class com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant {*;}
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction

#代码压缩可能会将远程下载bundle 需要用到的代码的去掉
-keepclassmembers class  com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {
    public static <methods>;
}
-keepclassmembers class * implements com.ximalaya.ting.android.host.manager.bundleframework.listener.IActionRouter {
    public  <methods>;
}
#router
#
# 打包期间慢函数插入类 由于慢函数插入是在混淆之后，因此要保留类名和方法名
-keep class com.ximalaya.ting.android.host.systrace.TraceTag{public static <methods>;}
# end

# 动画解析 keep 构造方法
-keep public class android.support.rastermill.FrameSequence{
    <init>(...);
}
#所有native方法
-keepclasseswithmembernames class * {
    native <methods>;
}
# 插件activitiy 找不到，抛出异常类，
-keep class com.ximalaya.ting.android.framework.BundleClassNotFoundException
#
# okhttp 缓存 图片缓存使用了动态代理和反射
# by com.ximalaya.ting.android.framework.manager.ImageManager
-keep class okhttp3.Cache{ *;}
-keep class okhttp3.internal.cache.InternalCache{ *;}
#end

# 动态代理
-keep class * implements java.lang.reflect.InvocationHandler{*;}
# end

# plugin 和 patch ：
-keep class com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel {
    *;
}
-keep class com.ximalaya.ting.android.host.model.plugin.** {*;}
# end

#xdcs 底层还有反射调用 by com.ximalaya.ting.android.player.cdn.CdnUtil
-keepclasseswithmembernames class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {
    public static void statErrorToXDCS(...);
}
# 保留一下成员 免得被优化掉
-keepclasseswithmembers class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {
    public static void statErrorToXDCS(...);
}

# haha 反射调用 by com.ximalaya.ting.android.mm.internal.analyzer.HahaHelper
-keep class com.squareup.haha.perflib.ArrayInstance{*;}
# end
#反射调用 by 可视化埋点滑动监控
-keepclassmembernames class androidx.recyclerview.widget.RecyclerView {
    private java.util.List mScrollListeners;
}
-keepclassmembernames class androidx.viewpager.widget.ViewPager {
    private java.util.List mOnPageChangeListeners;
}
-keepclassmembernames class com.ximalaya.ting.android.host.view.VerticalViewPager {
    private int mCurItem;
}
# end

# 继承view view的自定义控件构造方法
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    }
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

#end
# java 序列化
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
# android 序列化
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
#end

# commonrequestM 反射调用：
-keepclasseswithmembers class com.ximalaya.ting.android.host.manager.request.CommonRequestM {
    public static void getLocalUserInfo(...);
    public static void updateTrackForPlay(...);
    public static java.lang.String getChargeDownloadUrl(...);
    public static com.ximalaya.ting.android.host.manager.request.CommonRequestM getInstanse();
    public java.lang.String getUserAgent(...);
    public static com.ximalaya.ting.android.host.model.track.TrackM getTrackInfoDetailSync(...);
    public static void getTrackInfoDetailForPlay(...);
}
#end
# 反射调用 com.ximalaya.ting.android.host.manager.ad.AdManager
-keepclasseswithmembernames class com.ximalaya.ting.android.host.manager.ad.AdManager {
    public static boolean checkAdContent;
}
#
# 播放器java层model
-keep class com.ximalaya.ting.android.player.model.** {*;}
-keepclasseswithmembers class com.ximalaya.ting.android.player.XMediaplayerJNI {
    public int dataStreamInputFunCallBackT(...);
}
# 保持类名，其父类有个方法需要c层反射调用
-keep class com.ximalaya.ting.android.player.XMediaPlayer {
   *;
}
# 反射调用adapter 构造方法
-keep class * extends com.ximalaya.ting.android.framework.adapter.AbstractAdapter {
    <init>(...);
}
# end
# 有些属性被反射调用
-keepclasseswithmembernames class  androidx.fragment.app.DialogFragment {
    private boolean mDismissed;
    private boolean mShownByMe;
}
#
#跨bundle 调用，代码压缩可能会去掉
#-keepclassmembers class * extends com.ximalaya.ting.android.xmlymmkv.util.BaseMMKVUtil {
#    public <methods>;
#}
#

-dontwarn org.litepal.**
-keep class org.litepal.** { *;}


#-----opensdk start----------
-keep class com.ximalaya.ting.android.opensdk.model.** { *; }
-keep class com.ximalaya.ting.android.opensdk.player.advertis.followheart.FollowHeartConfig {*;}
-keep class com.ximalaya.ting.android.opensdk.datatrasfer.OkHttpDataCallback { *; }
#-----opensdk end----------

#-----framework start----------
#友盟sdk 本身自己混淆过 这里keep 整个包名，避免再次混淆，造成方法找不到
-dontwarn com.umeng.**
-keep class com.umeng.** {*;}
-keep class com.uc.** {*;}
-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

#-----
#-keepclassmembers class * {
#   public <init> (org.json.JSONObject);
#}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#skd 本身混淆了
-dontwarn com.alipay.**
-keep class com.alipay.** { *;}
-dontwarn org.json.alipay.**
-keep class org.json.alipay.** { *;}


# 仅keep 其下面的native 方法
-dontwarn tv.cjump.jni.**

#腾讯其他包也可能因此被keep
-dontwarn dalvik.**
-dontwarn com.tencent.smtt.**
-keep class com.tencent.smtt.** {*;}
-keep class com.tencent.tbs.** {*;}

#官方文档混淆配置
-keep class com.tencent.mm.opensdk.** {*;}
-keep class com.tencent.wxop.** {*;}
-keep class com.tencent.mm.sdk.** {*;}
-keep class com.tencent.connect.** {*;}


#百度下面包混淆比较彻底
-dontwarn com.baidu.**
-keep class com.baidu.**{ *;}

#@辛乐 小米授权sdk sdk 已经混淆过
-dontwarn com.xiaomi.**
-keep class com.xiaomi.** { *;}
-dontwarn miui.net.**
-keep class miui.net.** { *;}

# gson 底层反射调用
-keep class com.google.gson.** {*;}
#

#华为sdk混淆比较彻底
-keep class com.huawei.hms.**{*;}
-keep class com.xiaomi.assemble.control.HmsPushManager{public static *;}
-keep class com.xiaomi.assemble.control.HmsPushService

# oppo 推送 未见使用
#-keep class com.coloros.mcssdk.**{*;}
-keep class com.xiaomi.assemble.control.COSPushManager{
    public static *;
}
-keep class com.xiaomi.assemble.control.COSPushMessageService
-keep class com.ximalaya.ting.android.xmpushservice.model.** { *; }


#@陈飞
# Youzan SDK 7.0 start
-dontwarn com.youzan.sdk.***
-keep class com.youzan.sdk.**{*;}

-dontwarn com.youzan.**
-keep class com.youzan.jsbridge.** { *; }
-keep class com.youzan.spiderman.** { *; }
-keep class com.youzan.androidsdk.** { *; }
-keep class com.youzan.x5web.** { *; }
-keep class com.youzan.androidsdkx5.** { *; }
-keep class dalvik.system.VMStack { *; }
-keep class com.tencent.smtt.** { *; }
-dontwarn  com.tencent.smtt.**
# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn com.squareup.okhttp.**
-keep class okio.**{*;}
-keep class com.squareup.okhttp.** { *; }
-keep interface com.squareup.okhttp.** { *; }
# IM
-keep class com.youzan.mobile.zanim.model.** { *; }

-dontwarn java.nio.file.*
-dontwarn javax.annotation.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# Youzan SDK  end

# Image Loader
-keep class com.squareup.picasso.**{*;}
-keep class com.bumptech.glide.Glide
-keep class com.facebook.drawee.backends.pipeline.Fresco



#model keep
-keep class com.ximalaya.ting.android.host.model.** { *; }
-keep class com.ximalaya.ting.android.host.data.model.** { *; }
-keep class com.ximalaya.ting.android.host.socialModule.DyncTextViewBaseItem$TextItemData { *; }
-keep class com.ximalaya.ting.android.host.socialModule.DyncVideoViewBaseItem$VideoNodeData {*;}
#@陈飞  请移动到相应的二方库下面
-keep class com.ximalaya.ting.android.upload.model.** { *; }

#@陈飞 这里都是静态方法，请检查是否需要整个类keep
#-keep class com.ximalaya.ting.android.host.util.XDCSDataUtil { *; }

#@陈飞 这里看起来并非model 请检查
#-keep class com.ximalaya.ting.android.host.manager.device.** { *; }
-keep class com.ximalaya.ting.android.host.xdcs.model.** { *; }

#-keep class com.ximalaya.ting.android.host.activity.web.WebActivityDuiBaMall$* { *; }

#@宏果 请检查是否整个包名都需要keep
#-keep class com.ximalaya.ting.android.host.fragment.other.web.** { *; }
-keep class * extends com.tencent.smtt.sdk.WebViewClient { *; }
-keep class * extends android.webkit.WebViewClient { *; }
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}


#** 进一步确认
-keep public class **.R$string { *; }
-keep public class **.R$layout { *; }
#-keep public class com.ximalaya.ting.android.R$* { *; }

-keepclasseswithmembers class **.R$string {
    public static <fields>;
}

-keepclasseswithmembers class **.R$layout {
    public static <fields>;
}

-keepattributes Signature,Exceptions,InnerClasses,SourceFile,LineNumberTable
-keepattributes *Annotation*

#native 类名和方法
-keepclassmembers,includedescriptorclasses class * {
    native <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers class * extends com.ximalaya.ting.android.framework.adapter.HolderAdapter {
   public <init> (android.content.Context ,java.util.List);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}



#model 类
-keep class com.ximalaya.ting.android.xchat.model.**{ *;}
-keep class com.ximalaya.ting.android.xchat.model.newxchat.**{ *;}
-keep class com.ximalaya.ting.android.xchat.struct.**{ *;}
-keep class LOVE.** { *; }
-keep class PK.** { *; }


#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
-keep class com.bumptech.** {  *;}

#-keep class **.R$* {*;}
-keepclassmembers class * {void onEvent*(**);}
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);}
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();}
#-keep class **.R$* {*;}

#腾讯bugly
#sdk 内部混淆
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}


#科大讯飞 语音搜索 begin
#@洪静 sdk
-keep class com.iflytek.**{*;}
-keepattributes Signature
#科大讯飞 语音搜索 end


-dontwarn com.umeng.**
-keep class com.umeng.** { *;}


#sdk 本身混淆了
-dontwarn com.androidwiimusdk.**
-keep class com.androidwiimusdk.** { *;}


-dontwarn com.actions.ibluz.**
-keep class com.actions.ibluz.** {*;}
-dontwarn com.androidwiimusdk.**
-keep class com.androidwiimusdk.** {*;}

#----组件框架统计模块混淆规则 begin ------- 暂时保留，暂无jar对应库代码
-keep class * implements com.ximalya.ting.android.statisticsservice.NoProguard{
    protected <methods>;
    public <methods>;
    public <fields>;
}
#@李宏果  请检查是否需要keep整个包名 数据model需要保留
-keep class com.ximalya.ting.android.statisticsservice.bean.**{
 *;
}


#----组件框架统计模块混淆规则 end ------
#jsskd 反射调用
-keep class * implements com.ximalaya.ting.android.hybridview.NoProguard {
    protected <methods>;
    public <methods>;
    public <fields>;
}
#

#头条sdk；sdk 混淆本身比较彻底
-keep class com.bytedance.**{*;}
-keep class com.ss.android.common.**{*;}

#火山asr sdk
-keep class com.bytedance.speech.speechengine.SpeechEngineImpl {*;}

#--MainBundle--start #
#@徐华福
-keep class com.ximalaya.ting.android.host.model.OnlineDubTemplateItemModel$* { *; }
-keep class com.ximalaya.ting.android.live.video.host.data.** {*;}
#live host
-keep class com.ximalaya.ting.android.live.host.data.** {*;}

#-- RnBundle --start
#@苏明 请移动到对应的bundle 下
-keep,allowobfuscation @interface com.ximalaya.reactnative.utils.NoProguard
-keep @com.ximalaya.reactnative.utils.NoProguard class * { *; }
-keepclassmembers class * {
 @com.ximalaya.reactnative.utils.NoProguard *;
}
-keep class com.ximalya.ting.android.statisticsservice.RNNoProguard
-keep class * implements com.ximalya.ting.android.statisticsservice.RNNoProguard {
    public <fields>;
    public <methods>;
    protected <methods>;
}


-keep class * extends java.lang.annotation.Annotation { *; }
-keep interface * extends java.lang.annotation.Annotation { *; }

-keep,allowobfuscation @interface com.facebook.proguard.annotations.DoNotStrip
-keep,allowobfuscation @interface com.facebook.proguard.annotations.KeepGettersAndSetters
-keep,allowobfuscation @interface com.facebook.common.internal.DoNotStrip

# Do not strip any method/class that is annotated with @DoNotStrip
-keep @com.facebook.proguard.annotations.DoNotStrip class *
-keep @com.facebook.common.internal.DoNotStrip class *
-keepclassmembers class * {
 @com.facebook.proguard.annotations.DoNotStrip *;
 @com.facebook.common.internal.DoNotStrip *;
}
-keepclassmembers @com.facebook.proguard.annotations.DoNotStrip class * { *; }
-keepclassmembers @com.facebook.common.internal.DoNotStrip class * { *; }

-keepclassmembers @com.facebook.proguard.annotations.KeepGettersAndSetters class * {
 void set*(***);
 *** get*();
}

-keep class * extends com.facebook.react.bridge.JavaScriptModule { *; }
-keep class * extends com.facebook.react.bridge.NativeModule { *; }
-keepclassmembers class *  { @com.facebook.react.uimanager.UIProp <fields>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactProp <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>; }

-keep class com.facebook.jni.** { *; }
-keep class * extends com.facebook.react.LazyReactPackage { *; }


# 银联  begin
-keep public class com.android.vending.licensing.ILicensingService

-keep  public class com.unionpay.uppay.net.HttpConnection {
	public <methods>;
}
-keep  public class com.unionpay.uppay.net.HttpParameters {
	public <methods>;
}
-keep  public class com.unionpay.uppay.model.BankCardInfo {
	public <methods>;
}
-keep  public class com.unionpay.uppay.model.PAAInfo {
	public <methods>;
}
-keep  public class com.unionpay.uppay.model.ResponseInfo {
	public <methods>;
}
-keep  public class com.unionpay.uppay.model.PurchaseInfo {
	public <methods>;
}
-keep  public class com.unionpay.uppay.util.DeviceInfo {
	public <methods>;
}
-keep  public class java.util.HashMap {
	public <methods>;
}
-keep  public class java.lang.String {
	public <methods>;
}
-keep  public class java.util.List {
	public <methods>;
}
-keep  public class com.unionpay.uppay.util.PayEngine {
	public <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

-keep class org.simalliance.openmobileapi.** {*;}
-keep class org.simalliance.openmobileapi.service.** {*;}

-keep class com.unionpay.** {*;}
-keep class com.UCMobile.PayPlugin.** {*;}
-keep class cn.gov.pbc.tsm.client.mobile.android.bank.** {*;}


-keep class com.jdpaysdk.author.web.PayJsFunction {*;}
-keep class com.jdpaysdk.author.JDPayAuthor {*;}
-keep class com.jdpaysdk.author.Constants {*;}
-keep class com.jdpaysdk.author.entity.CPOrderParam {*;}
-keepclassmembers class com.jdpaysdk.author.JDPayAuthor.** {
    public *;
    private *;
}
# entity
-keepclassmembers class com.jdpaysdk.author.entity.** {
    public *;
    private *;
}
-keepclassmembers class com.jdpaysdk.author.protocol.** {
    public *;
    private *;
}
-keep class com.jdpaysdk.author.protocol.** { *; }
-keep class com.jdpaysdk.author.** { *; }

# 银联end

# RN DEV
-keep public class com.facebook.react.devsupport.DevSupportManagerImpl {*;}
-keep public class com.ximalaya.ting.android.reactnative.modules.thirdParty.svg.** {*;}

### RN thirdparty start
-keep public class com.ximalaya.ting.android.reactnative.modules.thirdParty.fastImage.* {*;}
-keep public class com.ximalaya.ting.android.reactnative.modules.thirdParty.fastImage.** {*;}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
### RN thirdparty end

# 信令
-keep class KSONG.** { *; }

#-- RnBundle -- end
#--RnUnionPayBundle start---

-keep class com.ximalaya.ting.android.rnunionpay.RNUnionPayApplication {*;}
#--RnUnionPayBundle end---



#---log调试日志---#
#仅存在于debug 下，某开关打开才有此类，此类被反射，不会发布到线上
-keep class com.ximalaya.android.loglibrary.**{ *; }
-keep class com.ximalaya.android.loglibrary.core.*{ *; }
#---log调试日志---#


#---IM SDK二方库---#
#@赵武卫 请检查是否需要整个包都keep 并移动到相应的而方库下
#二方库使用的pb消息不能混淆
-keep class IMC.** { *; }
-keep class XMC.** { *; }

-keep class com.ximalaya.ting.android.im.base.model.** {*;}
-keep class com.ximalaya.ting.android.im.base.sendrecmanage.timeoutmonitor.ImTimeOutTask {*;}
-keep class com.ximalaya.ting.android.im.base.socketmanage.heartbeat.HeartBeatModule {*;}
-keep class com.ximalaya.ting.android.im.base.utils.CalChecksumInfo {*;}
-keep class com.ximalaya.ting.android.im.base.utils.EncodedMessageInfo {*;}
#XChat
-keep class com.ximalaya.ting.android.im.xchat.model.** {*;}
-keep class com.ximalaya.ting.android.im.xchat.db.model.** {*;}
#XPush
-keep class com.ximalaya.ting.android.im.xpush.model.** {*;}

#---IM SDK二方库---#


#---adid sdk --#
# SDK本身混淆比较彻底
-keep class com.bun.miitmdid.core.** {*;}
#---adid sdk --#

#---个推 sdk---#
#sdk 混淆比较彻底
-dontwarn com.sdk.plus.**
-keep class com.sdk.plus.** { *; }
#---个推 sdk---#


#--- 小程序 二方库---#
#@苏明 移动到对应的bundle
-keep @interface com.ximalaya.android.liteapp.utils.NoProguard
-keep @com.ximalaya.android.liteapp.utils.NoProguard class * { *; }
-keepclassmembers class * {
 @com.ximalaya.android.liteapp.utils.NoProguard *;
}

-keep @interface com.ximalaya.android.liteapp.utils.KeepAttr
-keep @com.ximalaya.android.liteapp.utils.KeepAttr class * { *; }
-keepclassmembers class * {
 @com.ximalaya.android.liteapp.utils.KeepAttr <fields>;
}

-keep interface com.ximalaya.android.liteapp.liteprocess.jsbridge.JSField {*;}
-keep interface com.ximalaya.android.liteapp.liteprocess.jsbridge.JSInterface {*;}

-keepclassmembers class *  { @com.ximalaya.android.liteapp.liteprocess.jsbridge.JSField <fields>; }
-keepclassmembers class *  { @com.ximalaya.android.liteapp.liteprocess.jsbridge.JSInterface <methods>; }

##### v8
#@苏明 确认是否需要keep整个包名
-keep class com.eclipsesource.v8.** {*;}
-keep interface com.eclipsesource.v8.** {*;}

#--- 小程序 二方库---#
#@苏明 移动到对应的二方库 检查是否需要keep 整个类下的所有东西
-keep class com.ximalaya.ting.android.shareservice.AbstractShareType {*;}


#-----穿山甲----start #
#SDK 混淆比较彻底
# ---- 穿山甲 --#
-keep class com.bytedance.sdk.openadsdk.** { *; }

-keep class com.bytedance.frameworks.** { *; }
-keep class ms.bd.c.Pgl.**{*;}
-keep class com.bytedance.mobsec.metasec.ml.**{*;}
-keep class com.ss.android.**{*;}
-keep class com.bytedance.embedapplog.** {*;}
-keep class com.bytedance.embed_dr.** {*;}
-keep class com.bykv.vk.** {*;}
# ---- 穿山甲 --#
#-----穿山甲----end #



#Dex一致性校验开关是否打开，给jni反射调用 不可混淆
-keep class com.ximalaya.ting.android.host.manager.configurecenter.ConfigureCenterUtil {*;}
# end


#kotlin 展示keep
#-keep class kotlin.** {*;}
#-keep class kotlin.Unit {*;}
#-keep class kotlin.reflect.KProperty {*;}

#-------read相关-------start #
-keep class com.ximalaya.ting.android.host.read.bean.** { *; }
#-------read相关-------end #

#-------wx支付相关-------start #
-keep class com.tencent.mm.opensdk.** {
   *;
}
-keep class com.tencent.wxop.** {
   *;
}
-keep class com.tencent.mm.sdk.** {
   *;
}

-keep class com.ximalaya.ting.android.pay.wxpay.XMWXPayEntryActivity$*{ *; }
-keep class com.ximalaya.ting.android.pay.wxpay.XMWXPayEntryActivity{ *; }

-keep class com.ximalaya.ting.android.pay.wxpay.ReflectWXPayActionFactory { *; }
-keep class com.ximalaya.ting.android.pay.wxpay.WxPayRequest { *; }


-keep class com.ximalaya.ting.android.host.manager.pay.PayActionHelper$*{ *; }
-keep class com.ximalaya.ting.android.host.manager.pay.PayActionHelper{ *; }
#-------wx支付相关-------end #
#保留位于View类中的get和set方法
-keepclassmembers public class * extends android.view.View{
    void set*(***);
    *** get*();
}
#保留在Activity中以View为参数的方法不变
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}
#保留实现了Parcelable的类名不变，
-keep class * implements android.os.Parcelable{
    public static final android.os.Parcelable$Creator *;
}
 #保留R$*类中静态成员的变量名
-keepclassmembers class **.R$*{
    public static <fields>;
}

#
-keep class android.taobao.windvane.**{*;}
-keep class com.taobao.securityjni.**{*;}
-keep class com.taobao.wireless.security.**{*;}
-keep class com.ut.secbody.**{*;}
-keep class com.taobao.dp.**{*;}
-keep class com.alibaba.wireless.security.**{*;}
-keep class com.alibaba.security.rp.**{*;}
-keep class com.alibaba.security.cloud.**{*;}
-keep class com.alibaba.security.realidentity.**{*;}
-keep class com.alibaba.security.biometrics.**{*;}
-keep class com.alibaba.sdk.android.**{*;}
-keep class android.taobao.windvane.**{*;}

 -keep class com.ximalaya.ting.android.host.manager.freeflow.listencard.UnicomKingModel {*;}
 -keep class com.ximalaya.ting.android.host.manager.freeflow.FreeFlowService {*;}
 -keep public class com.sina.weibo.sdk.**{*;}
 # 绑定需要的model
 -keep public class com.ximalaya.ting.android.loginservice.bindstrategy.AuthorizationInfo {*;}
 -keep public class com.ximalaya.ting.android.loginservice.bindstrategy.BindFailMsg {*;}

# 智能硬件相关混淆配置
-keep class com.ximalaya.ting.android.smartdevice.framework.data.** { *; }
-keep class com.ximalaya.ting.android.smartdevice.framework.util.bleconnect.** { *; }
-keep class com.ximalaya.ting.android.smartdevice.bleconnect.** {*;}
-keep class com.ximalaya.ting.android.smartdevice.model.** {*;}
-keep class com.ximalaya.ting.android.smartdevice.tws.** {*;}
-keep class com.ximalaya.ting.android.smartdevice.rubikscube.** {*;}
#小魔盒——杰理OTA
-keep class com.jieli.jl_bt_ota.** {*;}


#播放进程业务插桩类
-keep public class com.ximalaya.ting.android.host.manager.PlayServiceInitStub  {*;}
-keep class com.sina.** {*;}

#新版分享面板数据model
-keep public class com.ximalaya.ting.android.host.share.model.**{*;}

-keep class com.ximalaya.ting.android.xmutil.ProcessUtil
-keep class com.ximalaya.ting.android.xmutil.LoggerFileKeeper
-keep class com.ximalaya.ting.android.host.view.lrcview.LrcEntry


#
-keep class com.ximalaya.ting.android.encryptcheck.** {
   <init>();
}
-keep class com.ximalaya.ting.android.opensdk.player.advertis.followheart.FollowHeartConfig$PromptStrategy{
   <init>();
}
-keep class * extends com.ximalaya.ting.android.im.base.interf.base.IXmImService {
   <init>();
}

# kotlin
-keep class kotlin.Metadata { *; }
-keepattributes RuntimeVisibleAnnotations

#推啊 bean
-keep class com.ximalaya.ting.android.host.manager.ad.tuiaad.bean.** { *; }

#三方小游戏 bean
-keep class com.ximalaya.ting.android.host.manager.ad.thirdgamead.bean.** { *; }

#首页信息可视化广告埋点
-keep class com.ximalaya.ting.android.host.manager.ad.FeedAdWrapper { *; }


-keep class com.ximalaya.ting.android.lifecycle.** {*;}
-keep class * implements com.ximalaya.ting.android.lifecycle.annotation.XmFragmentLifecycleCallback{*;}
-keep class * implements com.ximalaya.ting.android.lifecycle.annotation.XmLifecycleBinder{*;}
-keepclassmembers class * {
 com.ximalaya.ting.android.lifecycle.annotation.XmFragmentLifecycleCallback *;
}

-keep class com.ximalaya.ting.android.xmutil.**{*;}

-keep class com.ximalaya.ting.android.adsdk.**{*;}

-dontwarn com.ximalaya.ting.android.adsdk.**

#-keep class com.ximalaya.ting.android.host.download.engine.BaseTaskDownloadEngine{*;}

# 娱乐派对 BaseRecyclerHolder 子类通过反射生成，构造函数需要 Keep 住
# Start

-keep class * extends com.ximalaya.ting.android.live.common.view.widget.recylerview.BaseRecyclerHolder {
  <init>(...);
}
-keep class * extends com.ximalaya.ting.android.live.common.view.widget.recylerview.LiveBaseRecyclerAdapter {
  <init>(...);
}

# 娱乐派对 BaseRecyclerHolder 子类通过反射生成，构造函数需要 Keep 住
# Start
-keep class com.ximalaya.ting.android.live.common.sound.effect.adapter.PiaEffectAdapter$EffectHolder {
    <init>(...);
}

# End

-keep class *  extends androidx.recyclerview.widget.RecyclerView$ViewHolder {
    <init>(...);
}

# 三方 SlidingTab
-keep class com.astuetz.PagerSlidingTabStrip{*;}

#建行sdk
-keep class com.ccbsdk.**{*;}
-keep class a.a.a.**{*;}
-keep class com.a.a.**{*;}
-keep class com.bangcle.**{*;}
-keep class b.a.a.**{*;}
-keep class com.fort.**{ *;}

#okhttp request for realcallhook
-keep class okhttp3.Request{*;}

# UbtSource
-keep class com.ximalaya.ting.android.opensdk.player.ubt.TraceModel { *; }

#mixplayer
-keep class com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixTrack { *; }

-keep class com.ximalaya.ting.android.framework.crash.CrashDetail { *; }
-keep class com.ximalaya.ting.android.framework.crash.CrashEvent { *; }

-keep class com.mob.**{*;}
-keep class com.ximalaya.ting.android.login.model.** { *; }

#动态化模板
-keep class com.ximalaya.ting.android.template.model.**{*;}
-keep class com.ximalaya.ting.android.host.util.template.**{*;}

#数盟sdk
 -keep class cn.shuzilm.core.** {*;}
 #xuid sdk
 -keep class com.ximalaya.xuid.nativelib.NativeLib {*;}

 #热修复埋点modle keep
 -keep class com.ximalaya.ting.android.framework.hotfixtrace.HotFixTrace {*;}

#新用户
-keep class com.ximalaya.ting.android.host.manager.growth.XmGrowthManagerHelper{*;}

#robust

-keepclassmembers class **{ public static com.meituan.robust.ChangeQuickRedirect *; }

#华为捐赠
-ignorewarnings
-keep class com.huawei.agconnect.**{*;}
-ignorewarnings
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keep class com.huawei.hianalytics.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}

-keep class com.google.android.material.appbar.AppBarLayout {*;}
-keep class com.google.android.material.appbar.ViewOffsetBehavior {*;}
-keep class com.ximalaya.ting.android.host.fragment.other.web.OfflineResourceConfigCenterProvider {*;}


# Lock Watcher
-keep class com.example.lockmonitor.lockWatch.**{ *;}


# readbundle拆分增加混淆规则 #
#--ReadBundle--start
-keep class com.ximalaya.ting.android.read.bean.** { *; }
-keep class com.ximalaya.ting.android.read.bean.ItemModel {*;}
-keep class com.ximalaya.ting.android.read.request.** { *; }

-keep class com.ximalaya.ting.android.host.read.request.ReadTokenInterceptor*{ *; }
-keep class com.ximalaya.ting.android.host.read.request.ReadTokenInterceptor{ *; }

-keep class com.ximalaya.ting.android.read.ReadApplication*{ *; }
-keep class com.ximalaya.ting.android.read.ReadApplication{ *; }

-keep public class * extends android.widget.BaseAdapter
-keep class com.ximalaya.ting.android.read.adapter.** {*;}
-keep class com.ximalaya.ting.android.framework.adapter.HolderAdapter{ *; }
-keep class com.ximalaya.ting.android.read.bean.** { *; }
-keep class com.ximalaya.ting.android.read.bean.ItemModel {*;}
-keep class com.ximalaya.ting.android.host.read.bean.** { *; }
-keep class com.ximalaya.ting.android.host.read.request.ReadTokenInterceptor{ *; }
-keep class com.ximalaya.ting.android.read.widgets.epub.entity.** { *; }

-keep public class * extends android.widget.BaseAdapter
-keep class com.ximalaya.ting.android.read.adapter.** {*;}
-keep class com.ximalaya.ting.android.framework.adapter.HolderAdapter{ *; }

-keep class com.ximalaya.ting.android.read.widgets.localReader.bean.** { *; }
-keep class com.ximalaya.ting.android.read.widgets.localReader.data.** { *; }

-keep class com.ximalaya.ting.android.framework.util.reflection.** { *; }

#--ReadBundle--end

#--移动精准达sdk-start
-keep class com.cmic.** {*;}
#--移动精准达sdk-end

#--爱加密，清场sdk
-dontwarn com.ijm.detect.drisk.unexp.**
-keep class com.ijm.detect.drisk.unexp.** { *;}

#抖音
-keep class com.bytedance.sdk.open.aweme.**
-keep class com.ximalaya.ting.android.douyincallback.**

-dontwarn com.bytedance.frameworks.baselib.network.http.ok3.**
-keep class com.bytedance.frameworks.baselib.network.http.ok3.** { *; }
#全域rtc sdk
-keep class com.ximalaya.pubsubsdk.model.** {*;}
-keep class XMC.** { *; }
#cronet网络库
-keep class org.chromium.** {*;}
-dontwarn org.chromium.**
-keep class J.** { *; }

-keep class androidx.core.util.** { *;}
-keep class com.lihang.** { *;}
#-keep class kotlin.jvm.functions.** {*;}

# 或者使用更简洁的写法，同时保持ViewPager及其所有内部类
-keep class androidx.viewpager.widget.ViewPager$LayoutParams { *; }