package com.ximalaya.ting.android.live.lifecycle;

import android.os.Bundle;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.model.live.PersonLiveBase;

/**
 * 业务组件接口
 *
 * <AUTHOR>
 * @since 2022/10/17
 */
public interface IBizComponent<T> {

    /**
     * 绑定直播间上下文数据
     *
     * @param hostData 直播间上下文数据
     */
    void bindData(@NonNull T hostData);


    /**
     * 切换到新直播间回调
     *
     * @param newRoomId 新直播间 id
     * @param newArgs   新直播间参数
     */
    void onSwitchRoom(long newRoomId, Bundle newArgs);

    /**
     * 切换到同一直播间回调
     */
    void onSwitchSameRoom();

    /**
     * 手机横竖屏切换回调
     *
     * @param orientation Configuration.ORIENTATION_LANDSCAPE or Configuration.ORIENTATION_PORTRAIT
     * @param isSameOrientation
     */
    void onOrientationChange(int orientation, boolean isSameOrientation);

    /**
     * 交友模式等玩法容器显示时定义为半屏模式，无玩法容器时定义为全屏模式
     *
     * @param halfMode true 半屏状态  false 全屏状态
     */
    void updateComponentViewOnScreenModeChange(boolean halfMode);

    /**
     * 键盘输入状态回调
     *
     * @param isInput 输入状态 false 隐藏状态
     */
    void onUserInputStatusChange(boolean isInput);

    /**
     * 礼物面板可见性回调
     *
     * @param show true 显示 false 隐藏
     */
    void onGiftDialogShowStateChange(boolean show);

    /**
     * 背景主题色
     *
     * @param mainColor 背景主题色
     */
    void setMainColor(int mainColor);

    /**
     * 背景主题色偏暗
     *
     * @param mainDarkColor 背景主题色偏暗
     */
    void setMainDarkColor(int mainDarkColor);

    /**
     * 收到服务端推送的直播状态变化消息
     *
     * @param status 直播状态变化消息
     */
    void onReceiveRoomStatusChange(@PersonLiveBase.LiveStatus int status);
}
