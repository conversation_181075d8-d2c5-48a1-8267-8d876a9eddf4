/**
 * XmPlayerService.java
 * com.ximalaya.ting.android.player.service
 * <p/>
 * <p/>
 * ver     date      		author
 * ---------------------------------------
 * 2015-4-2 		chadwii
 * <p/>
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.player.service;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.pm.ServiceInfo;
import android.content.res.Configuration;
import android.media.AudioTimestamp;
import android.net.Uri;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.support.v4.media.session.MediaSessionCompat;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;

import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.analytics.ExoPlayerProcessStore;
import com.google.android.exoplayer2.upstream.DataSpec;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sina.util.dnscache.Tools;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.exoplayer.MediaCacheManager;
import com.ximalaya.ting.android.exoplayer.datasource.XMHttpDataSource;
import com.ximalaya.ting.android.exoplayer.model.PlayErrorSavedModel;
import com.ximalaya.ting.android.host.manager.ad.adrtb.AdRtbItemModel;
import com.ximalaya.ting.android.manager.HandlerManager;
import com.ximalaya.ting.android.opensdk.R;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.constants.TicketConstantsKt;
import com.ximalaya.ting.android.opensdk.datatrasfer.CommonRequestForMain;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.opensdk.httputil.HttpUrlUtil;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeCollectUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeoutMonitor;
import com.ximalaya.ting.android.opensdk.manager.WifiSleepMonitor;
import com.ximalaya.ting.android.opensdk.model.BluetoothStateModel;
import com.ximalaya.ting.android.opensdk.model.PlayErrorModel;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.ad.AdActionModel;
import com.ximalaya.ting.android.opensdk.model.ad.AdPreviewModel;
import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.advertis.TrackAudioAdInfo;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.configure.ConfigWrapItem;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.live.schedule.Schedule;
import com.ximalaya.ting.android.opensdk.model.performance.XmLivePerformanceStatistic;
import com.ximalaya.ting.android.opensdk.model.soundEffect.SoundEffectInfo;
import com.ximalaya.ting.android.opensdk.model.soundpatch.SimpleSoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel;
import com.ximalaya.ting.android.opensdk.model.task.TaskStatusInfo;
import com.ximalaya.ting.android.opensdk.model.track.PlayInfoErrorResponseInfo;
import com.ximalaya.ting.android.opensdk.model.track.PlayUrlInfo;
import com.ximalaya.ting.android.opensdk.model.track.PlayUrlModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.xdcs.CdnConfigModel;
import com.ximalaya.ting.android.opensdk.model.xdcs.StatToServerFactoryImplForMain;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManagerForPlayer;
import com.ximalaya.ting.android.opensdk.player.advertis.AIgcAdExposureFrequencyHelper;
import com.ximalaya.ting.android.opensdk.player.advertis.ContainPlayAdManager;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListenerExpand;
import com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager.PlayAdsCallback;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationChannelUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationColorUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationStyleUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appwidget.AutoStopWidgetProvider4x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.BaseAppWidgetProvider;
import com.ximalaya.ting.android.opensdk.player.appwidget.DailyNewsWidgetProvider4x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.PlayBoxWidgetProvider2x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.PlayBoxWidgetProvider4x2;
import com.ximalaya.ting.android.opensdk.player.appwidget.WidgetProvider;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.check.PlayerExceptionCheckManager;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.CrossProcessTransferValueManager;
import com.ximalaya.ting.android.opensdk.player.manager.DataSourceChangeManager;
import com.ximalaya.ting.android.opensdk.player.manager.FreeListenManagerForPlay;
import com.ximalaya.ting.android.opensdk.player.manager.IAdFree;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundEffectStatusDispatcher;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundPatchAdStateListener;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundPatchStatusDispatcher;
import com.ximalaya.ting.android.opensdk.player.manager.LivePutIntoHistoryManager;
import com.ximalaya.ting.android.opensdk.player.manager.MainAppBuzStub;
import com.ximalaya.ting.android.opensdk.player.manager.NextSoundPatchManager;
import com.ximalaya.ting.android.opensdk.player.manager.PcdnManager;
import com.ximalaya.ting.android.opensdk.player.manager.PlanTerminateManagerForPlay;
import com.ximalaya.ting.android.opensdk.player.manager.PlanTerminateManagerForPlayForQuickListen;
import com.ximalaya.ting.android.opensdk.player.manager.PlayProgressManager;
import com.ximalaya.ting.android.opensdk.player.manager.PlayerProcessImportanceWatchDog;
import com.ximalaya.ting.android.opensdk.player.manager.PlayerToListenManager;
import com.ximalaya.ting.android.opensdk.player.manager.QuickListenForPlayProcessUtil;
import com.ximalaya.ting.android.opensdk.player.manager.SendPlayStaticManager;
import com.ximalaya.ting.android.opensdk.player.manager.SkipHeadTailManager;
import com.ximalaya.ting.android.opensdk.player.manager.SoundPatchManager;
import com.ximalaya.ting.android.opensdk.player.manager.TempoManagerForPlayer;
import com.ximalaya.ting.android.opensdk.player.manager.TrackInfoDataManager;
import com.ximalaya.ting.android.opensdk.player.manager.TrackInfoPrepareManager;
import com.ximalaya.ting.android.opensdk.player.manager.TrackPlayForSearchManager;
import com.ximalaya.ting.android.opensdk.player.manager.TrackUrlChooseManager;
import com.ximalaya.ting.android.opensdk.player.mediacontrol.IMediaControl;
import com.ximalaya.ting.android.opensdk.player.playList.PlayListManager;
import com.ximalaya.ting.android.opensdk.player.receive.FilterCarBluetoothDevice;
import com.ximalaya.ting.android.opensdk.player.receive.NotificationLikeManager;
import com.ximalaya.ting.android.opensdk.player.receive.ScreenStatusReceiver;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl.PlayMode;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixTrack;
import com.ximalaya.ting.android.opensdk.player.soundEffect.SoundEffectPlayerManager;
import com.ximalaya.ting.android.opensdk.player.soundpatch.SoundPatchArbitrateManager;
import com.ximalaya.ting.android.opensdk.player.soundpatch.SoundPatchFrameworkManager;
import com.ximalaya.ting.android.opensdk.player.statistic.AppExitInfoManager;
import com.ximalaya.ting.android.opensdk.player.statistic.FfmpegDecodeErrorManager;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorManagerKt;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorStatisticManager;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayExceptionCollector;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayStatisticsUploaderManager;
import com.ximalaya.ting.android.opensdk.player.statistic.RealTimeStatistics;
import com.ximalaya.ting.android.opensdk.player.statistic.TrafficStatisticManager;
import com.ximalaya.ting.android.opensdk.player.statistic.XmStatisticsManager;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.XmPlayStatisticsManager;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.mix.XmMixStatisticsManager;
import com.ximalaya.ting.android.opensdk.player.ubt.IUbtSourceSynchronizer;
import com.ximalaya.ting.android.opensdk.player.ubt.TrackUbtSourceManager;
import com.ximalaya.ting.android.opensdk.player.ubt.UbtSourceProvider;
import com.ximalaya.ting.android.opensdk.push.PushGuardPlayerManager;
import com.ximalaya.ting.android.opensdk.push.PushNotificationFilterManager;
import com.ximalaya.ting.android.opensdk.startup.IStageMonitor;
import com.ximalaya.ting.android.opensdk.startup.PlayServiceStartUpMonitor;
import com.ximalaya.ting.android.opensdk.usetrace.UseTraceCollector;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.ChatXmlyUtilInPlayer;
import com.ximalaya.ting.android.opensdk.util.FileUtilBase;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.ModelUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.NetMonitorUtil;
import com.ximalaya.ting.android.opensdk.util.OnPlayErrorRetryUtilForPlayProcess;
import com.ximalaya.ting.android.opensdk.util.PlayUrlUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.opensdk.util.ToListenUtil;
import com.ximalaya.ting.android.opensdk.util.TrackUtil;
import com.ximalaya.ting.android.player.DownloadThread;
import com.ximalaya.ting.android.player.IDomainServerIpCallback;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.player.MediadataCrytoUtil;
import com.ximalaya.ting.android.player.PlayCacheByLRU;
import com.ximalaya.ting.android.player.PlayerUtil;
import com.ximalaya.ting.android.player.StaticConfig;
import com.ximalaya.ting.android.player.XMediaPlayer;
import com.ximalaya.ting.android.player.XMediaPlayerConstants;
import com.ximalaya.ting.android.player.XMediaplayerImpl;
import com.ximalaya.ting.android.player.XMediaplayerJNI;
import com.ximalaya.ting.android.player.cdn.CdnConstants;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.player.liveflv.IFlvDataCallback;
import com.ximalaya.ting.android.player.xdcs.IStatToServerFactory;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.base.IXmDataChangedCallback;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay;
import com.ximalaya.ting.android.routeservice.service.push.IWidgetService;
import com.ximalaya.ting.android.routeservice.service.stat.IPlayStat;
import com.ximalaya.ting.android.routeservice.service.stat.IPushGuardUbtTrace;
import com.ximalaya.ting.android.routeservice.service.trace.ITrace;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.statistic.audio.error.XmPlayErrorStatistic;
import com.ximalaya.ting.android.statistic.audio.performance.PlayPerformanceModel;
import com.ximalaya.ting.android.statistic.audio.performance.XmPlayPerformanceStatistic;
import com.ximalaya.ting.android.util.OsUtil;
import com.ximalaya.ting.android.util.PlayErrorUtil;
import com.ximalaya.ting.android.voicewakecallback.IVoiceWakePlayDataCallback;
import com.ximalaya.ting.android.voicewakecallback.IVoiceWakeRecordListener;
import com.ximalaya.ting.android.voicewakecallback.PlayDataOutputManager;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.lang.reflect.Method;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.core.app.NotificationCompat;
import androidx.core.util.Pair;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static android.R.attr.id;
import static com.ximalaya.ting.android.opensdk.player.manager.TrackUrlChooseManager.useMiddleAdUrl;

/**
 * ClassName:XmPlayerService
 *
 * <AUTHOR>
 * @Date 2015-4-2
 * @see
 * @since Ver 1.1
 */
public class XmPlayerService extends Service {
    private static final String TAG = "XmPlayerService";

    private Context mAppCtx;

    private RemoteCallbackList<IMixPlayerEventDispatcher> mMixPlayerDispatcher = new MyRemoteCallbackList<IMixPlayerEventDispatcher>();
    private RemoteCallbackList<IXmFlvDataCallback> mFlvDataCallBack = new MyRemoteCallbackList<>();
    private RemoteCallbackList<ISoundPatchStatusDispatcher> mSoundPatchDispatcher = new MyRemoteCallbackList<ISoundPatchStatusDispatcher>();
    private RemoteCallbackList<ISoundEffectStatusDispatcher> mSoundEffectDispatcher = new MyRemoteCallbackList<ISoundEffectStatusDispatcher>();

    private SharedPreferences mPlayLastPlayTrackInAlbum;

    private XmPlayerImpl mPlayerImpl;
    public XmPlayerControl mPlayerControl;
    public XmPlayListControl mListControl;
    public List<Track> mOriginalDailyNewsTracks = new ArrayList<>();

    public XmPlayerAudioFocusControl mPlayerAudioFocusControl;

    private String mAppSecret;

    private XmPlayerConfig mPlayerConfig;

    private static Service mService;

    private XmStatisticsManager mStatisticsManager;

    private XmPlayStatisticsManager mXmPlayStatisticsManager;

    private PlayableModel mLastModel;

    private int mLastDuration;

    private XmAdsManager mAdsManager;

    private IXmCommonBusinessDispatcher mIXmCommonBusinessDispatcher;
    private NotificationManager mNotificationManager;
    private Notification mNotification;
    private int mNotificationId;

    private boolean isDLNAState = false;
    private boolean isContinuePlay = false;
    private boolean isContinuePlayWhileAuditionTrackPlayComplete = false;
    private IMediaControl mMediaControl;
    private int c = 0;
    private IStatToServerFactory mIStatToServerFactory;

    private XMediaPlayer.OnPlayDataOutputListener mOnPlayDataOutputListener;

    private IDomainServerIpCallback mIDomainServerIpCallback;
    private IFlvDataCallback mFlvDataCallbackForPlay;
    private CopyOnWriteArrayList<BaseAppWidgetProvider> mBaseAppWidgetProviders;
    private BaseAppWidgetProvider mAutoStopWidgetProvider;
    private BaseAppWidgetProvider mDailyNewsWidgetProvider;
    private volatile boolean hasInitNotification = false;
    public Config mConfig;
    private IHistoryManagerForPlay mHistoryManager;

    private BroadcastReceiver mScreenBroadcastReceiver;

    private AdPreviewModel mAdPreviewModel;

    private boolean isAutoPlayAfterGetPlayUrl = false;
    private static Set<IServiceLifeCallBack> sServiceLifeCallBacks = new CopyOnWriteArraySet<>();
    private final Set<IUseStatusChangeCallBackForPlayProcess> mUseStatusChangeCallBacks = new CopyOnWriteArraySet<>();
    private boolean isSetWakeMode;
    private boolean mIsElderlyMode = false;
    private boolean notificationCancled;
    private MediaSessionCompat mSessionCompat;
    private boolean isFromXmPlayerManagerPlay = false;
    public long lastRequestTime;
    public long lastContainPlayRequestTime;
    private boolean mCanPlayOneKeyPatch = true;
    private boolean mCanPlayEndPlayRecommendPatch = true;
    private PlayableModel lastPlayModel = null;
    private static IOnPlayModeChangeListener mPlayModeChangeListener;
    private static IAdFreeDataChange sAdFreeDataChange;

    private static ISoundPatchAdStateListener sSoundPatchAdStateListener;

    private RealTimeStatistics realTimeStatistics;
    private static IAdFree adFreeManager;
    public static boolean hasPlayed = false;    // 是否已经播放过了吗
    private boolean isVideoPlaying = false;
    private boolean isVideoMode = false;
    public boolean hasLoadedHistory = false;
    private final Set<XmPlayerManager.IOnHistoryListLoadSuccess> mOnHistoryListLoadSuccess = new CopyOnWriteArraySet<>();

    private IXmPlayerStatusListener mPlayerStatusListener = new IXmPlayerStatusListener() {

        @Override
        public void onSoundPrepared() {
            Logger.logToFile("onSoundPrepared XmPlayerService 192: ");
            mLastDuration = mPlayerControl.getDuration();
            SendPlayStaticManager.getInstance().onSoundPrepared();
            if (mMediaControl != null) {
                mMediaControl.onSoundPrepared();
            }
            if (mListControl != null) {
                PlayableModel model = mListControl.getCurrentPlayableModel();
                if (model instanceof Track) {
                    // 上报异常声音
                    traceSuspiciousPlay((Track) model);
                }
            }

            recordLiveTrackInfoToUrl();

        }

        private void recordLiveTrackInfoToUrl() {

            PlayableModel playableModel = getCurrPlayModel();
            if (playableModel == null || !playableModel.isKindOfLive()) {
                return;
            }

            String playUrl = null;
            if (playableModel instanceof Track) {
                playUrl = TrackUrlChooseManager.getInstance().getTrackUrl((Track) playableModel, false);
            } else {
                playUrl = getCurPlayUrl();
            }

            long trackId = playableModel.getDataId();
            int flvPlayType = 0;
            if (playableModel instanceof Track) {
                flvPlayType =  XmPlayListControl.getPlayType((Track) playableModel);
            }

            if (!TextUtils.isEmpty(playUrl)) {
                ExoPlayerProcessStore.recordTrackInfoToUrl(playUrl, flvPlayType, trackId);
            }
        }

        private String getTrackInfo(PlayableModel model) {
            if (model == null) {
                return "{0}";
            }
            if (model instanceof Track) {
                return "{id=" + model.getDataId() + ", title=" + ((Track) model).getTrackTitle() + "}";
            }
            return "{id=" + model.getDataId() + ", class=" + model.getClass() + "}";
        }

        /**
         * for 多次切歌导致getCurrentPos不准的情况
         */
//        private int mPlayedPosition = 0;
        @Override
        public void onSoundSwitch(PlayableModel lastModel,
                                  PlayableModel curModel) {
            Logger.logToFile("onSoundSwitch XmPlayerService 221 lastModel=" + getTrackInfo(lastModel) + ", curModel=" + getTrackInfo(curModel));
            if (XmPlayerService.getSoundPatchAdStateListener() != null) {
                XmPlayerService.getSoundPatchAdStateListener().onAdEnd();
            }
            if (lastModel instanceof Track) {
                ((Track) lastModel).setHasUpdatePcdnDomain(false);
            }
            lastPlayModel = lastModel;

            PlayerExceptionCheckManager.getInstance().onSoundSwitch(lastModel, curModel);

            if (curModel != null) {
                TrackUbtSourceManager.put(curModel.getDataId(), TrackUbtSourceManager.getUbtSource());
                PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).
                        onSoundSwitch(curModel.getDataId());
            }

            PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).checkAndSavePlayOver40(curModel, lastModel);
            if (mLastModel != null) {
                PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).syncMermoryProgressToMMKV(mLastModel.getDataId());
            }

            Logger.i(TAG, DownloadThread.downloadedSize + "");
            SharedPreferencesUtil.getInstance(mAppCtx).saveString(PreferenceConstantsInOpenSdk.OPENSDK_KEY_DOWNLOAD_SIZE, "" + DownloadThread.downloadedSize);
            DownloadThread.downloadedSize = 0;

            long startTime = System.currentTimeMillis();
            SendPlayStaticManager.getInstance().onSoundSwitch(lastModel, curModel);
            long broadcastTime = System.currentTimeMillis();
            apmTrace(lastModel);
            long apmTraceTime = System.currentTimeMillis();

            if (lastModel != null && lastModel instanceof Track) {
                Track lastSound = (Track) lastModel;
                int position = mPlayerControl.getCurrentPos();
                mStatisticsManager.onStopTrack(lastSound, position);
                mXmPlayStatisticsManager.onPlayStop(lastSound, position, true, curModel);
                if (lastSound.isNeedPauseAtDesPos()) {
                    lastSound.setNeedPauseAtDesPos(false);
                }
            }

            // 续播的第一条声音被切换后,移除续播标记,埋点不在上报续播类型
            if (lastModel != null) {
                MMKVUtil.getInstance().removeByKey("KEY_IS_FROM_AUTO_PLAY_" + lastModel.getDataId());
            }

            if (mStatisticsManager != null) {
                mStatisticsManager.onSoundSwitch();
            }
            if (mXmPlayStatisticsManager != null) {
                mXmPlayStatisticsManager.onSoundSwitch(lastModel, curModel);
            }

            long statisticsTime = System.currentTimeMillis();

            if (mMediaControl != null) {
                mMediaControl.onSoundSwitch();
            }

            if (curModel != null) {  //curModel 为 null 表明列表已播完
                // 在切歌前 先判断下MixTrack是否有在播放 如果有的话 不能更新通知栏
                if (MixPlayerService.getMixService() == null || MixPlayerService.getMixService().getPlaySource() == null) {
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(
                        mListControl, mNotificationManager, mNotification,
                            mNotificationId, isDark);
                    // 修复直播和普通声音切换的时候封面偶先没有变的问题
                    boolean shouldUpdateNotificationAgain = false;
                    if (curModel.isKindOfLive() && lastModel != null && !lastModel.isKindOfLive()) {
                        shouldUpdateNotificationAgain = true;
                    } else if (!curModel.isKindOfLive() && lastModel != null && lastModel.isKindOfLive()) {
                        shouldUpdateNotificationAgain = true;
                    } else if (lastModel == null) {
                        shouldUpdateNotificationAgain = true;
                    }
                    if (shouldUpdateNotificationAgain && !notificationCancled) {
                        getTimeHander().postDelayed(() -> {
                            boolean isDark1 = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                            XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(
                                    mListControl, mNotificationManager, mNotification,
                                    mNotificationId, isDark1);
                        }, 2000);
                    }
                }
            }

            long notificationTime = System.currentTimeMillis();

            OnPlayErrorRetryUtilForPlayProcess.resetMediaDecodeErrorOnSoundSwitch();
            if (!ToListenUtil.closeRecommend()) {
                preRequestToListenTrackList(curModel, lastModel);
            }

            long preRequestTime = System.currentTimeMillis();

            PlayerToListenManager.getInstance().handleOnSoundSwitch(curModel, lastModel);

            long toListenTime = System.currentTimeMillis();

            NextSoundPatchManager.getInstance().onSoundSwitch(curModel, lastModel);

            long nextSoundPatchTime = System.currentTimeMillis();

            ChatXmlyUtilInPlayer.onSoundSwitch(lastModel, curModel);

            Logger.logToFile("onSoundSwitch XmPlayerService 222 broadcastTime=" + (broadcastTime - startTime)
                    + ",apmTraceTime=" + (apmTraceTime - broadcastTime)
                    + ",statisticsTime=" + (statisticsTime - apmTraceTime)
                    + ",notificationTime=" + (notificationTime - statisticsTime)
                    + ",preRequestTime=" + (preRequestTime - notificationTime)
                    + ",toListenTime=" + (toListenTime - preRequestTime)
                    + ",nextSoundPatchTime=" + (nextSoundPatchTime - toListenTime));
            if (curModel != null && curModel instanceof Track) {
                Track track = (Track) curModel;
                MmkvCommonUtil.getInstance(getContext()).saveString("play_bar_cover", track.getValidCover());
            }

            if (curModel instanceof Track) {
                QuickListenForPlayProcessUtil.saveQuickListenCollectId((Track) curModel);
            }
        }

        private void preRequestToListenTrackList(PlayableModel curModel, PlayableModel lastModel) {
            boolean lastTrackIsToListen = false;
            boolean currentTrackIsToListen = false;
            if (lastModel instanceof Track && ((Track) lastModel).getIsFromToListenTrack() == 1) {
                lastTrackIsToListen = true;
            }
            if (curModel instanceof Track && ((Track) curModel).getIsFromToListenTrack() == 1) {
                currentTrackIsToListen = true;
            }
            if (!lastTrackIsToListen && currentTrackIsToListen) {
                // 上一首不是待播 这一首是待播 预先请求待播推荐列表
                // 待播列表播完，开播推荐列表开播
                IXmCommonBusinessDispatcher iXmCommonBusinessDispatcher = getIXmCommonBusinessDispatcher();
                if (iXmCommonBusinessDispatcher != null) {
                    try {
                        iXmCommonBusinessDispatcher.preRequestToListenRecommend();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onPlayStop() {
            Logger.logToFile("onPlayStop XmPlayerService 271:");
            PlayableModel model = mListControl.getCurrentPlayableModel();
            PlayerExceptionCheckManager.getInstance().onPlayStop(model);

            SendPlayStaticManager.getInstance().onPlayStop();

            Track track = (Track) model;
            int position = mPlayerControl.getCurrentPos();
            mStatisticsManager.onStopTrack(track, position);
            mStatisticsManager.onPlayStop();
            mXmPlayStatisticsManager.onPlayStop(model, position, false, null);
            mMediaControl.stopPlay();
            PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(false);
        }

        @Override
        public void onPlayStart() {
            IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
            if (pushGuardUbtTrace != null) {
                pushGuardUbtTrace.changeFirstPlayFlag();
                pushGuardUbtTrace.traceNoPermissionUser(true);
            }
            if (XmPlayerService.getSoundPatchAdStateListener() != null) {
                XmPlayerService.getSoundPatchAdStateListener().onAdEnd();
            }
            PlayableModel currPlayModel = getCurrPlayModel();
            int duration = mPlayerControl.getDuration();
            int currentPos = mPlayerControl.getCurrentPos();
            float tempo = mPlayerControl.getTempo();
            PlayerExceptionCheckManager.getInstance().onPlayStart(currPlayModel, currentPos, duration, tempo);
            if (PushGuardPlayerManager.getInstance().shouldStartPlayTraceFlag) {
                PushGuardPlayerManager.getInstance().shouldStartPlayTraceFlag = false;
                if (pushGuardUbtTrace != null && currPlayModel != null) {
                    pushGuardUbtTrace.traceRecommendStartPlay(true, currPlayModel.getDataId());
                }
            }
            PushGuardPlayerManager.getInstance().resetPushGuardFlagAfterPlay();

//            UserInteractivePlayStatistics.onPlayStart(currPlayModel != null ? currPlayModel.getDataId() : -1);

            if (currPlayModel != null) {
                boolean hasTrackCache = false;
                if (PlayableModel.KIND_TRACK.equals(currPlayModel.getKind())) {
                    TrackUbtSourceManager.put(currPlayModel.getDataId(), TrackUbtSourceManager.getUbtSource());
                }
                try {
                    XmPlayerControl xmPlayerControl = getPlayControl();
                    if (xmPlayerControl != null) {
                        DataSpec dataSpec = xmPlayerControl.getFileBufferDataSpec();
                        if (dataSpec != null) {
                            Uri uri = Uri.parse(xmPlayerControl.getCurPlayUrl());
                            if (dataSpec.uri.toString().contains(MD5.md5(uri.getPath()))) {
                                hasTrackCache = true;
                            }
                        }
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }

                ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
                Map<String, String> result = null;
                if (commonBusiService != null) {
                    result = commonBusiService.getResultForPlayer("play");
                }
                if (result == null) {
                    result = new HashMap<>();
                }
                String useOptDataSource = String.valueOf(DataSourceChangeManager.getInstance().useOptDataSource());
                if (!TextUtils.isEmpty(useOptDataSource)) {
                    result.put("first_frame_open", useOptDataSource);
                }
                String failReason = String.valueOf(DataSourceChangeManager.getInstance().getFailReason());
                if (!TextUtils.isEmpty(failReason)) {
                    result.put("first_frame_fail_reason", failReason);
                }
                String curUseFirstFrame = mPlayerControl != null ? String.valueOf(mPlayerControl.isUseFirstFramePcdn()) : "";
                if (!TextUtils.isEmpty(curUseFirstFrame)) {
                    result.put("cur_use_info", curUseFirstFrame);
                }
                String exoPlayUrl = mPlayerControl != null ? mPlayerControl.getExoPlayUrl() : null;
                if (!TextUtils.isEmpty(exoPlayUrl)) {
                    try {
                        Uri uri = Uri.parse(exoPlayUrl);
                        if (!TextUtils.isEmpty(uri.getPath())) {
                            result.put("exo_path", uri.getPath());
                        }
                        if (!TextUtils.isEmpty(uri.getHost())) {
                            result.put("exo_host", uri.getHost());
                        }
                        Pair<String, String> pair = PcdnManager.getSingleInstance().getPCDNDomain(uri.getHost(), 3);
                        if (pair.first != null && pair.first.equals(uri.getHost()) && !TextUtils.isEmpty(pair.second)) {
                            result.put("cur_pcdn_fail", pair.second);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                XmPlayPerformanceStatistic.getInstanse().endCdnRequest(currPlayModel.getDataId(), hasTrackCache, result);
                if (currPlayModel instanceof Track && currPlayModel.isKindOfLive()) {
                    XmLivePerformanceStatistic.getInstance().bufferReadyPlay((Track) currPlayModel);
                    if (((Track)currPlayModel).getLiveFlvType() == XMediaplayerImpl.TYPE_AUDIO) {
                        XmLivePerformanceStatistic.getInstance().startPlay((Track) currPlayModel);
                    }
                }
                if (currPlayModel instanceof Track && PlayableModel.KIND_TRACK.equals(currPlayModel.getKind())) {
                    try {
                        TrackPlayForSearchManager.Companion.getInstance().addTrackPlayRecord(currPlayModel.getDataId());
                    } catch (Exception e) {
                        e.printStackTrace();
                        Logger.i(TAG, "TrackPlayForSearchManager addTrackPlayRecord error:" + e.getMessage());
                    }
                }
            }
            int bitrate = 0;
            float downloadSpeed = 0;
            if (mPlayerControl!= null) {
                bitrate = mPlayerControl.getCurMediaSourceBitRate();
                downloadSpeed = mPlayerControl.getDownloadSpeed();
            }
            XmPlayErrorStatistic.getInstance().postSuccessRecord(XmPlayerService.this, bitrate, downloadSpeed, (currPlayModel instanceof Track) ? XmPlayListControl.getPlayType((Track) currPlayModel) : 0, getCurPlayUrl());
            String msg = "trackId: " + (currPlayModel != null ? currPlayModel.getDataId() : "0") + " onPlayStart XmPlayerService 294:" + (mPlayerControl != null ? mPlayerControl.getCurPlayUrl() : "");
            Logger.logToFile(msg);
            UseTraceCollector.log(msg);
            getTimeHander().removeCallbacks(setWakeModeRunnable);
            // 将MixPlayer里的声音置为null
            MixPlayerService.getMixService().clearPlayInfo();
            Logger.i(TAG, "MixPlayerService clearPlayInfo");
            notificationCancled = false;

            setNotification();

            Logger.i(TAG, "startForegroundService");
            startForegroundService();
            PlayerProcessImportanceWatchDog.getSingleInstance().checkImportance(mAppCtx);

            addScreenChangeBroadCast();
//            if (mListControl != null && mListControl.getCurrListSize() == 0) {
//                PlayerToListenManager.getInstance().handleOnPlayStart(currPlayModel);
//            }

            Logger.logToFile("XmPlayerService onPlayStart check mPlayStartCallBack");
            // 口播广告需要和声音无缝连接,所以需要在playStart的时候暂停
            if (mPlayStartCallBack != null && mPlayStartCallBack.size() > 0) {
                for (XmAdsManager.IPlayStartCallBack playStartCallBack : mPlayStartCallBack) {
                    if (playStartCallBack.onPlayStart()) {
                        Logger.logToFile("XmAdsManager.IPlayStartCallBack onPlayStart");
                        return;
                    }
                }
            }
            Logger.logToFile("SendPlayStaticManager onPlayStart-start");
            SendPlayStaticManager.getInstance().onPlayStart();
            Logger.logToFile("SendPlayStaticManager onPlayStart-end");
            LivePutIntoHistoryManager.INSTANCE.putLastPlayingLiveIntoHistoryIfNeeded(lastPlayModel, currPlayModel);
            saveModelToHistory(lastPlayModel, currPlayModel);

            if (mHistoryManager != null) {
                mHistoryManager.savePlayList();
            }

            boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
            boolean isLive = false;
            if (mListControl != null && mListControl.getCurrentPlayableModel() != null) {
                String kind = mListControl.getCurrentPlayableModel().getKind();
                isLive = (PlayableModel.KIND_LIVE_FLV.equals(kind)) ||
                        (PlayableModel.KIND_ENT_FLY.equals(kind)) ||
                        (PlayableModel.KIND_LIVE_COURSE.equals(kind));
            }
            boolean isNeedSkipUpdateMode = mAdsManager != null && mAdsManager.isInteractSoundAd(mAdsManager.getCurSoundAdList());
            if (hasInitNotification && !isNeedSkipUpdateMode) {
                XmNotificationCreater.getInstanse(mAppCtx)
                        .updateModelDetail(mListControl, mNotificationManager, mNotification, mNotificationId, isDark);
            }
            XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtStart(mNotificationManager, mNotification,
                    mNotificationId, isDark, isLive, isSleepPlayModel(), isSketchVideo());
            sendPlayerStartBroadCast();
            Track track = (Track) mListControl.getCurrentPlayableModel();
            int position = mPlayerControl.getCurrentPos();
            boolean online = mPlayerControl.isOnlineSource();
            mStatisticsManager.onPlayTrack(track, online, position, getCurPlayUrl(), mCurrPlayMethod);
            mStatisticsManager.onPlayStart(position, mPlayerControl.isPlayingRadio());
            int nextType = mCurrPlayMethod == PlayerConstants.PLAY_METHOD_MANUAL_PLAY ? 4 : mCurrPlayMethod;
            int bitrateKbps = mPlayerControl.getCurMediaBitrate();
            String curUrl = getCurPlayUrl();
            long contentLen = getContentLen(track, curUrl);
            mXmPlayStatisticsManager.onPlayStart(track, online, position, curUrl, nextType, bitrateKbps, contentLen);
            mMediaControl.startPlay();

            MyAsyncTask.execute(() -> saveLastPlayTrackInAlbum(track));

            PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(true);
            OnPlayErrorRetryUtilForPlayProcess.resertPlayErrorSign();

            if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_CHILD_PROTECT_TIP_IS_SHOWING, false)) {
                pausePlay(false, PauseReason.Common.CHILD_PROTECT_PAUSE);
            } else if (UserInteractivePlayStatistics.Optimizer_StartPlayWhileAdRequest.enable()) {
                boolean playSoundPatchAd = XmAdsManager.getInstance(XmPlayerService.getPlayerSrvice()).isPlaySoundPatchAd();
                UserInteractivePlayStatistics.loge("onPlayStart  playSoundPatchAd: " + playSoundPatchAd + ", isAdsActive: " + mAdsManager.isAdsActive());
                if (mAdsManager.isAdsActive() && playSoundPatchAd) {
                    pausePlayWithoutAd(false, PauseReason.Common.SOUND_AD_PLAYING);
                }
            }

            IPlayStat playStat = RouterServiceManager.getInstance().getService(IPlayStat.class);
            if (playStat != null) {
                playStat.statStartPlay(currPlayModel);
            }

            if (getPlayControl() != null) {
                getPlayControl().resetShouldPlayForce();
            }
        }


        @Override
        public void onPlayProgress(int currPos, int duration) {
            Logger.logToFile("onPlayProgress XmPlayerService 336:   currPost=" + currPos + "   duration=" + duration);
            synchronized (XmPlayerService.class) {
                PlayableModel curr = mListControl.getCurrentPlayableModel();

                if (curr == null) {
                    return;
                }
                PlayerToListenManager.getInstance().onPlayProgress(curr, currPos, duration);

                PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice())
                        .saveProgress(mListControl, mPlayerControl, currPos, duration, false);

                SendPlayStaticManager.getInstance().onPlayProgress(currPos, duration);
                if (mMediaControl != null) {
                    mMediaControl.updateProcess(currPos);
                }
                mStatisticsManager.checkIsSeek(currPos, duration);
                if (mPlayerControl != null) {
                    mStatisticsManager.onPlayProgress(currPos, duration, mPlayerControl.isPlayingRadio());
                }
                if (mXmPlayStatisticsManager != null) {
                    mXmPlayStatisticsManager.onPlayProgress(curr, currPos, duration);
                }
                checkIsPauseTime();
                checkNeedPauseCurTrack(curr, currPos);

                // 因为可能会有中插广告,所以可能执行暂停操作
                if (mPlayProgressCallBack != null && mPlayProgressCallBack.size() > 0) {

                    for (XmAdsManager.IPlayProgressCallBack playProgressCallBack : mPlayProgressCallBack) {
                        if (playProgressCallBack.onPlayProgress(currPos, duration)) {
                            if (mPlayerConfig != null) {
                                mPlayerControl.pause(false, PauseReason.Ad.PLAY_AD_IN_PROGRESS);
                            }
                        }
                    }

                }

                int beforeAdTime = XmAdsManager.AD_ADVANCE_TIME;
                if (curr instanceof Track && duration > XmAdsManager.AD_ADVANCE_TIME) {
                    SubordinatedAlbum album = ((Track) curr).getAlbum();
                    if (album != null) {
                        int tail = SkipHeadTailManager.getInstance().getTail(album.getAlbumId());
                        if (duration > tail + XmAdsManager.AD_ADVANCE_TIME) {
                            beforeAdTime = tail + XmAdsManager.AD_ADVANCE_TIME;
                        }
                    }
                }

                NextSoundPatchManager.getInstance().onPlayProgress(getContext(), curr, currPos, duration);

                // 在结束之前的20秒请求广告
                if (duration > beforeAdTime && ((duration - currPos) < beforeAdTime
                        && PlayableModel.KIND_TRACK.equals(curr.getKind()))) {
                    if (lastRequestAdDataId == curr.getDataId()) {
                        return;
                    }
                    lastRequestAdDataId = curr.getDataId();
                    if (getPlayListControl().getPlayMode() == PlayMode.PLAY_MODEL_LIST ||
                            getPlayListControl().getPlayMode() == PlayMode.PLAY_MODEL_LIST_LOOP) {
                        int nextIndex = getPlayListControl().getNextIndex(false);
                        if (nextIndex >= 0) {
                            PlayableModel nextPlayModel = getPlayListControl().getPlayableModel(nextIndex);
                            mAdsManager.onlyGetAdsInfo(nextPlayModel, PlayerConstants.PLAY_METHOD_AUTO_SWITCH);
                            mAdsManager.preRequestHeadSoundAd(nextPlayModel);
                        }
                    }
                }

                if ((duration - currPos) < TrackInfoPrepareManager.TRACK_INFO_ADVANCE_TIME) { //更新下一首音频的播放地址
                    TrackInfoPrepareManager.getInstance(getPlayerSrvice()).startPrepareNextTrackBuffer(true);
                }

                ChatXmlyUtilInPlayer.onPlayProgress(curr, currPos, duration);
            }
        }

        private long lastRequestAdDataId;

        @Override
        public void onPlayPause() {
            Logger.logToFile("onPlayPause XmPlayerService 393:");
            PlayableModel curr = mListControl.getCurrentPlayableModel();
            PlayerExceptionCheckManager.getInstance().onPlayPause(curr);
            ContainPlayAdManager.getInstance().onPlayPause();
            SendPlayStaticManager.getInstance().onPlayPause();
            boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
            XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                    mNotificationManager, mNotification, mNotificationId, isDark, isSleepPlayModel());
            mMediaControl.pausePlay();
            PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(false);

            if (mPlayPauseCallBack != null && mPlayPauseCallBack.size() > 0) {
                for (XmAdsManager.IPlayPauseCallBack playPauseCallBack : mPlayPauseCallBack) {
                    playPauseCallBack.onPlayPause();
                }
            }
            int currPosition = getPlayCurrPosition();
            PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).saveProgress(mListControl, mPlayerControl,
                currPosition, getDuration(), true);

            PlayableModel currReal = mPlayerControl.getRealPlayableModelInPlayer();
            if (curr != null && curr.equals(currReal)) {
                saveModelToHistory(null, curr);
            }
            if (curr instanceof Track) {
                ((Track) curr).setNeedPauseAtDesPos(false);
            }
            mXmPlayStatisticsManager.onPlayPause(curr, currPosition);

            IPlayStat playStat = RouterServiceManager.getInstance().getService(IPlayStat.class);
            if (playStat != null) {
                playStat.statPausePlay(curr);
            }
            sendPlayerPauseBroadCast();
        }

        private boolean isShowVideoAdvertis(Track track) {
            if (track != null) {
                // 5 表示广告解锁
                return CrossProcessTransferValueManager.isAdMarkTargetUser && track.isShowVideoAdverts() && "5".equals(track.getPermissionSource());
            }

            return false;
        }

        @Override
        public void onSoundPlayComplete() {
            Logger.logToFile("onSoundPlayComplete XmPlayerService 417:");
            synchronized (XmPlayerService.class) {
                Track track = (Track) mLastModel;
                PlanTerminateManagerForPlay.getInstance().beforeSoundPlayComplete();
                PlanTerminateManagerForPlayForQuickListen.getInstance().beforeSoundPlayComplete();
                PlayerExceptionCheckManager.getInstance().onSoundPlayComplete(track);
                // 试听的声音不执行试听结束
                if (!(track != null && track.isAudition() && isShowVideoAdvertis(track))) {
                    PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).
                            saveProgressOnComplete(mListControl, mPlayerControl);
                } else if(track != null && isShowVideoAdvertis(track)) {
                    PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).setLastNeedCompleteTrackId(track.getDataId());
                }

                if (track != null && track.isAudition()) {
                    SendPlayStaticManager.getInstance().onAudioAuditionOver(track);
                }

                int curPos = mPlayerControl.getCurrentPos();
                boolean isEnd = mListControl.getNextIndex(false) < 0;
                mXmPlayStatisticsManager.onSoundPlayComplete(track, isEnd, curPos);

                if (mPlayCompleteCallBack != null && mPlayCompleteCallBack.size() > 0) {
                    for (XmAdsManager.IPlayCompleteCallBack playCompleteCallback : mPlayCompleteCallBack) {
                        if (playCompleteCallback.onComplete()) {
                            handleComplete();
                            return;
                        }
                        Logger.logToFile("onSoundPlayComplete XmPlayerService 418:");
                    }

                    UseTraceCollector.log(TAG + "______PlayCompleteCallBack_NO_NULL___");
                } else {
                    Logger.logToFile("onSoundPlayComplete XmPlayerService 419:");
                    handleComplete();
                }

                IPlayStat playStat = RouterServiceManager.getInstance().getService(IPlayStat.class);
                if (playStat != null) {
                    playStat.statComplete(track);
                }
            }
        }

        @Override
        public boolean onError(XmPlayerException exception) {
            Logger.logToFile("onError XmPlayerService 475: exception=" + exception);
            // 这里可能是内部回调，暂不考虑这个的影响
            PlayErrorSavedModel errorModel1 = PlayErrorUtil.sLastPlayErrorModel;

            String realPlayUrl = null;
            if (errorModel1 != null) {
                realPlayUrl = errorModel1.playUrl;
            }
            if (realPlayUrl == null && mPlayerControl != null) {
                realPlayUrl = mPlayerControl.getCurPlayUrl();
            }

            PcdnManager.getSingleInstance().addFailCount(realPlayUrl, errorModel1);
//            BaiduPcdnManager.getInstance().addFailCount(realPlayUrl, errorModel1);
            // 如果发现可以自动重试,则在重试之前不进行回调onError
            PlayableModel currPlayModel = getCurrPlayModel();
            if (currPlayModel != null) {
                // 播放错误移除播放地址缓存
                TrackInfoPrepareManager.getInstance(XmPlayerService.getPlayerSrvice()).removeCacheByTrackId(currPlayModel.getDataId());
            }
            long trackId = 0;
            if (currPlayModel != null) {
                trackId = currPlayModel.getDataId();
            }

            OnPlayErrorRetryUtilForPlayProcess.IOnErrorCallback onErrorCallback = new OnPlayErrorRetryUtilForPlayProcess.IOnErrorCallback() {
                @Override
                public void retryPlay() {
                    PlayerExceptionCheckManager.getInstance().onError(currPlayModel, exception, true);
                }
            };

            if (!OnPlayErrorRetryUtilForPlayProcess.onPlayError(exception, trackId, onErrorCallback)) {
                Logger.logToFile("onError XmPlayerService 475-1: real error");
                PlayStatisticsUploaderManager.getInstance().setIsAudioPlaying(false);
                PlayerExceptionCheckManager.getInstance().onError(currPlayModel, exception, false);
                synchronized (XmPlayerService.class) {
                    //update notification status
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                            mNotificationManager, mNotification, mNotificationId, isDark, isSleepPlayModel());

                    if(currPlayModel != null) {
                        onTrackPlayErrorForLast(currPlayModel, exception, PlayErrorUtil.sLastPlayErrorModel);
                    }

                    SendPlayStaticManager.getInstance().onError(exception);

                    String playUrl = null;
                    if (mPlayerControl != null) {
                        playUrl = mPlayerControl.getCurPlayUrl();
                    }
                    if (NetworkType.isConnectTONetWork(XmPlayerService.this)) {
                        String module = "PlayError";
                        if (exception.getPlayerType() == XmPlayerException.PLAYER_TYPE_EXO) {
                            String msg = exception.getMessage();
                            if (!TextUtils.isEmpty(msg)) {
                                if (msg.toLowerCase().contains("unable to connect") || msg.toLowerCase().contains("socket")) {
                                    module = "PlayError1";
                                } else if (msg.contains("com.google.android.exoplayer2.ext.ffmpeg.FfmpegDecoderException: Error decoding (see logcat).")) {
                                    // 后面还有decoder 注意下顺序
                                    module = PlayErrorManagerKt.ERROR_FFMPEG_DECODE;
                                } else if (msg.contains("UnrecognizedInputFormatException")) {
                                    module = "PlayError2";
                                } else if (msg.contains(XMHttpDataSource.CONTENT_ERROR_TAG)) {
                                    module = "PlayError3";
                                } else if (msg.toLowerCase().contains("decoder") || msg.toLowerCase().contains("mediacodec")) {
                                    module = "PlayError4";
                                } else if (msg.contains("errorFileSavePath")) {
                                    module = "PlayError5";
                                } else if (msg.contains("exoplayer")) {
                                    module = "PlayError6";
                                } else {
                                    module = "PlayError0";
                                }
                            } else {
                                module = "PlayError0";
                            }
                        }
                        Logger.d("zimotag", "XmPlayerService onError: " + module);

                        final String finalModule = module;
                        final PlayableModel finalPlayableModel = getCurrPlayModel();
                        final String finalPlayUrl = playUrl;
                        final PlayErrorSavedModel errorModel = PlayErrorUtil.sLastPlayErrorModel;
                        PlayErrorUtil.sLastPlayErrorModel = null;
                        MyAsyncTask.execute(new Runnable() {
                            @Override
                            public void run() {
                                StringBuilder ipString = new StringBuilder();
                                try {
                                    String host = Tools.getHostName(finalPlayUrl);
                                    InetAddress[] addressList = InetAddress.getAllByName(host);
                                    for (InetAddress inetAddress : addressList) {
                                        ipString.append(inetAddress.getHostAddress());
                                        ipString.append("#");
                                    }
                                } catch (Throwable t) {
                                    t.printStackTrace();
                                }

                                String errorStr =
                                        "exception=" + exception + ";playUrl=" + finalPlayUrl + ";ipList" +
                                                "=" + ipString.toString() +
                                        ";track=" + finalPlayableModel;
                                BluetoothStateModel model = FilterCarBluetoothDevice.getBluetoothStateModel();
                                if (model != null) {
                                    errorStr += model.toString();
                                }
                                CdnUtil.statToXDCSError(finalModule, errorStr);

                                // 统一上报
                                if (errorModel != null) {
                                    errorModel.module = finalModule;
                                    errorModel.errorMsg = errorStr;

                                    String jsonStr = errorModel.toJsonString();
                                    Logger.d("zimotag", "XmPlayerService onError json: " + jsonStr);
                                    XmLogger.log("apm", "playException", jsonStr);
                                } else {
                                    // 这里会为空 先暂时上报一部分数据 后面再排查下
                                    // 原因：1. 可能使用的不是exo；2. 在播放过程中，还没有到播放器中已经回调了失败
                                    boolean useExo = XmMediaPlayerFactory.isUseExo(XmPlayerService.this);
                                    if (useExo) {
                                        PlayErrorSavedModel model1 = new PlayErrorSavedModel(XmPlayerService.this);
                                        model1.module = finalModule;
                                        model1.errorMsg = errorStr;
                                        model1.playUrl = finalPlayUrl;
                                        model1.rootCauseName = "modelNull";
                                        model1.playState = getPlayerStatus();
                                        model1.errorMs = System.currentTimeMillis();
                                        PlayErrorSavedModel.LocalExceptionModel localExceptionModel = new PlayErrorSavedModel.LocalExceptionModel();
                                        model1.localExceptionModel = localExceptionModel;
                                        if (exception != null) {
                                            localExceptionModel.what = exception.getWhat();
                                            localExceptionModel.extra = exception.getExtra();
                                            localExceptionModel.isFromServiceError = exception.isFromServiceError();
                                            localExceptionModel.cause = exception.getMessage();
                                            localExceptionModel.mRequestError = exception.getRequestError();
                                            localExceptionModel.mPlayerType = exception.getPlayerType();
                                        } else {
                                            localExceptionModel.what = -1;
                                        }
                                        XmLogger.log("apm", "playException", model1.toJsonString());
                                        Logger.d("zimotag", "XmPlayerService onError json: " + (errorModel == null));
                                    }
                                }

                                // 分类上报
                                PlayExceptionCollector.postPlayErrorData(exception, finalModule, XmPlayerService.this);
                                FfmpegDecodeErrorManager.INSTANCE.getINSTANCE().onError(finalPlayableModel, finalPlayUrl, finalModule);

                                // 统一上报，逻辑过于复杂，数据总量也有问题
                                PlayErrorStatisticManager.getSingleInstance().postErrorEvent(
                                        PlayErrorModel.PLAY_ERROR, errorStr,
                                        System.currentTimeMillis() + "");
                            }
                        });

                    }
                }
                mMediaControl.stopPlay();
            }

            return false;
        }

        private void onTrackPlayErrorForLast(PlayableModel playableModel, XmPlayerException exception, PlayErrorSavedModel playErrorSavedModel) {
            boolean needUpload = false;
            int code = -1;
            if (exception != null) {
                code = exception.getWhat();
                if(code == XmPlayerException.ERROR_NO_PLAY_URL
                        || code == XmPlayerException.ERROR_SAVE_PATH_NO_EXIT
                        || code == ExoPlaybackException.TYPE_SOURCE
                        || code == ExoPlaybackException.TYPE_RENDERER
                        || code == ExoPlaybackException.TYPE_UNEXPECTED
                        || code == ExoPlaybackException.TYPE_REMOTE
//                            || code == ExoPlaybackException.TYPE_OUT_OF_MEMORY
//                            || code == ExoPlaybackException.TYPE_TIMEOUT
                ) {
                    needUpload = true;
                }
            }

            int bitrate = 0;
            float downloadSpeed = 0;
            if (mPlayerControl!= null) {
                bitrate = mPlayerControl.getCurMediaSourceBitRate();
                downloadSpeed = mPlayerControl.getDownloadSpeed();
            }
            String causeName = null;
            String message = null;
            String trace = null;
            String rootCauseName = null;
            String rootMessage = null;
            String rootTrace = null;
            if (playErrorSavedModel != null) {
                causeName = playErrorSavedModel.causeName;
                rootCauseName = playErrorSavedModel.rootCauseName;
                message = playErrorSavedModel.module;
                if (playErrorSavedModel.exceptionModel != null) {
                    rootMessage = playErrorSavedModel.exceptionModel.message;
                    rootTrace = playErrorSavedModel.exceptionModel.detail;
                }
            }
            boolean postSuccess = XmPlayErrorStatistic.getInstance().onTrackPlayErrorForLast(playableModel.getDataId(), needUpload, code, bitrate, downloadSpeed,
                    (playableModel instanceof Track) ? XmPlayListControl.getPlayType((Track) playableModel) : 0, getCurPlayUrl(),
                    PlayerConstants.ERROR_FROM_CDN_ERROR_3, causeName, message, trace, rootCauseName, rootMessage, rootTrace);
            if (!postSuccess && exception != null) {
                IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                if (xdcsPost != null) {
                    xdcsPost.statErrorToXDCS("trackErrorStatic", "errorCode = " + exception.getWhat());
                }
            }
        }

        @Override
        public void onBufferProgress(int position) {
            SendPlayStaticManager.getInstance().onBufferProgress(position);

            mStatisticsManager.stuckStatistics(position, mPlayerControl.getDuration(), mPlayerControl.getCurrentPos());
        }

        @Override
        public void onBufferingStart() {
            if (mMediaControl != null) {
                mMediaControl.onBufferingStart();
            }
            PlayableModel currPlayModel = getCurrPlayModel();
            PlayerExceptionCheckManager.getInstance().onBufferingStart(currPlayModel);
            SendPlayStaticManager.getInstance().onBufferingStart();
        }

        @Override
        public void onBufferingStop() {
            if (mMediaControl != null) {
                mMediaControl.onBufferingStop();
            }
            PlayableModel currPlayModel = getCurrPlayModel();
            PlayerExceptionCheckManager.getInstance().onBufferingStop(currPlayModel);
            SendPlayStaticManager.getInstance().onBufferingStop();
        }

        @Override
        public void onRenderingStart() {
            PlayableModel currPlayModel = getCurrPlayModel();
            if (currPlayModel instanceof Track && currPlayModel.isKindOfLive() && ((Track)currPlayModel).getLiveFlvType() == XMediaplayerImpl.TYPE_VIDEO) {
                XmLivePerformanceStatistic.getInstance().startPlay((Track) currPlayModel);
            }
            SendPlayStaticManager.getInstance().onRenderingStart();
        }

        @Override
        public void onRotationChanged(int rotationAngle) {
            SendPlayStaticManager.getInstance().onRotationChanged(rotationAngle);
        }

        @Override
        public void onVideoSizeChanged(int width, int height) {
            SendPlayStaticManager.getInstance().onVideoSizeChanged(width, height);
        }
    };

    private XMediaPlayer.OnLiveStatusListener mLiveStatusListener = new XMediaPlayer.OnLiveStatusListener() {
        @Override
        public void onChaseFrameStart() {
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null && mPlayerControl != null) {
                xdcsPost.statErrorToXDCS("ChaseFrameStart", "ChaseFrameStart:bufferDuration="
                        + mPlayerControl.getTotalBufferDuration() + "  url=" + mPlayerControl.getCurPlayUrl());
            }
        }

        @Override
        public void onChaseFrameStop() {
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null && mPlayerControl != null) {
                xdcsPost.statErrorToXDCS("ChaseFrameStop", "ChaseFrameStop:bufferDuration="
                        + mPlayerControl.getTotalBufferDuration() + "  url=" + mPlayerControl.getCurPlayUrl());
            }
        }


    };

    /**
     * 上报track时长与播放时长相差大于一分钟的播放数据
     */
    public void traceSuspiciousPlay(Track track) {
        if (track == null || mPlayerControl == null) {
            return;
        }
        // 暂时只上报主播自己播放的异常声音数据
        if (!CommonRequestForMain.isLoginUsersTrack(track)) {
            return;
        }
        long trackDuration = track.getDuration();
        long playerDuration = trackDuration;
        String playUrl = null;
        try {
            playerDuration = mPlayerControl.getDuration() / 1000;
            playUrl = mPlayerControl.getCurPlayUrl();
        } catch (Exception e) {
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null) {
                xdcsPost.statErrorToXDCS("AnchorTrackPlayErr", Log.getStackTraceString(e));
            }
        }
        // 不上报下载到本地的音频和付费声音， 与清理缓存的判断条件保持一致
        if (playUrl == null || !playUrl.startsWith("http") || track.isPayTrack()) {
            return;
        }
        // 时长误差大于一分钟
        if (Math.abs(trackDuration - playerDuration) > 60) {
            // 当前声音异常，上报
            StringBuilder sbXd = new StringBuilder("player duration is suspicious：");
            sbXd.append(track.getDataId());
            sbXd.append(";");
            sbXd.append(CommonRequestForMain.allowCleanAnchorPlayCache());
            sbXd.append(";");
            if (!TextUtils.isEmpty(playUrl)) {
                sbXd.append(playUrl);
                sbXd.append(";");
                try {
                    sbXd.append(isCachedPlayUrl(playUrl));
                } catch (Exception ignored) { }
                sbXd.append(";");
            }
            sbXd.append(trackDuration);
            sbXd.append("-");
            sbXd.append(playerDuration);
            sbXd.append(";");

            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null) {
                xdcsPost.statErrorToXDCS("AnchorTrackPlayErr", sbXd.toString());
                Logger.d(TAG, "post xdcs suspicious track data: " + sbXd.toString());
            }
        }
    }

    private boolean isCachedPlayUrl(String playUrl) throws Exception {
        if (playUrl == null) {
            return false;
        }
        DataSpec dataSpec = new DataSpec.Builder().setUri(playUrl).build();
        String key = dataSpec.key != null ? dataSpec.key : MD5.md5(dataSpec.uri.getPath());
        Cache cache = MediaCacheManager.get().getCache();
        if (cache == null) {
            return false;
        }
        Set<String> keys = cache.getKeys();
        if (keys != null && keys.contains(key)) {
            return true;
        }
        return false;
    }

    /**
     * 清理主播自己的声音缓存，线上配置控制开关
     */
    public void cleanAnchorPlayCache(@Nullable String playUrl, @Nullable Track track) {
        if (TextUtils.isEmpty(playUrl) || track == null) {
            return;
        }
        if (!CommonRequestForMain.isLoginUsersTrack(track)) {
            return;
        }
        // 获取是否清理缓存开关
        if (!CommonRequestForMain.allowCleanAnchorPlayCache()) {
            return;
        }
        // 不清理下载到本地的音频和付费声音
        if (playUrl == null || !playUrl.startsWith("http") || track.isPayTrack()) {
            return;
        }
        // 清理缓存
        boolean delete = MediaCacheManager.get().deleteCache(playUrl);
        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
        if (xdcsPost != null) {
            xdcsPost.statErrorToXDCS("AnchorTrackCleanCache-"+ delete, playUrl);
        }
        Logger.d(TAG, "Remove player cache: " + playUrl + ", result: " + delete);
    }

    private void apmTrace(PlayableModel lastModel) {
        if (lastModel == null) {
            return;
        }
        try {
            Class<?> apmClazz = Class.forName("com.ximalaya.ting.android.apm.XmApm");
            Method getInstance = apmClazz.getDeclaredMethod("getInstance");
            Method putData = apmClazz.getDeclaredMethod("postApmData", String.class, Map.class);
            Map<String, Object> data = new HashMap<>();
            data.put("key", "listen");
            data.put("data", lastModel.getDataId() + "");
            data.put("time", System.currentTimeMillis());
            XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
            if(xmPlayerService != null) {
                XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
                if(xmPlayerControl != null) {
                    data.put("extra", xmPlayerControl.getPlayedDuration() + "");
                }
            }

            putData.invoke(getInstance.invoke(null), "appData", data);
        } catch (Exception e) {
            Logger.d(TAG, "record listen fail with e: " + e.toString());
        }
    }

    public void handleComplete() {
        // 必须放置到前面  因为前面的回调会先执行主进程然后执行play进程 ,主进程可能会设置mPauseTimeInMills这个值
        boolean willPlay = true;
        if (mPauseTimeInMills == PLAN_PAUSE_ON_COMPLETE) {
            if (!(QuickListenForPlayProcessUtil.isXimaTenSound() && QuickListenForPlayProcessUtil.nextIsXimaTenSound())) {
                mPauseTimeInMills = PLAN_NORMAL;
                willPlay = false;
            }
        }

        // 获取index,应该写在item.onSoundPlayComplete回调之前，确保getNextIndex判断PlayMode代码生效，
        // 防止在用户在onSoundPlayComplete改变PlayMode
        int index = mListControl.getNextIndex(false);
        //播放完当前这条声音，及时将下一条声音的index保存到历史记录中，防止进程被杀，不自动跳集
        PlayableModel nextTrack = mListControl.getPlayableModel(index);
        mHistoryManager.savePlayIndex(index, nextTrack);
        SendPlayStaticManager.getInstance().onSoundPlayComplete();
        boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
        XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                mNotificationManager, mNotification, mNotificationId, isDark, isSleepPlayModel());
        sendPlayCompleteBroadCast();

        Track track = (Track) mLastModel;

        if (mStatisticsManager != null && track != null && mPlayerControl != null) {
            mStatisticsManager.onStopTrack(track, mPlayerControl.getDuration());
        }

        if (track != null) {
            boolean isPlayNext = true;
            //如果是部分试听音频 不用播放下一首
            if (track.isAudition() && !isContinuePlayWhileAuditionTrackPlayComplete) {
                isPlayNext = false;
            } else if (track.isStopPlayWhileComplete()) {
                isPlayNext = false;
            } else if (track.isXimiFirstAudition() && !isContinuePlayWhileAuditionTrackPlayComplete) {
                isPlayNext = false;
            }
            UseTraceCollector.log(TAG+":__handleComplete XmPlayerService 420:   " + track.isAudition() + "   "
                    + isContinuePlayWhileAuditionTrackPlayComplete + "   " + track.isStopPlayWhileComplete()
                    + "    isPlayNext=" + isPlayNext);
            Logger.logToFile("handleComplete XmPlayerService 420:   " + track.isAudition() + "   "
                    + isContinuePlayWhileAuditionTrackPlayComplete + "   " + track.isStopPlayWhileComplete()
                    + "    isPlayNext=" + isPlayNext);

            if (!isPlayNext) {
                if (track.getSampleDuration() > ((Track) track).getDuration() * 1000 * 0.4f) {
                    PlayProgressManager.getInstance(this).savePlayOver40TrackToMMKV(track);
                }

                // 在dlna状态下没有下一曲是不能回调onSoundSwitch的
                if (isDLNAState) {
                    return;
                }

                // 为了解决主进程异步调用onSoundPlayComplete，这时候index可能已经改变，导致获取到的currentPlayTrack可能是下首歌的track的bug
                if (mPlayerStatusListener != null) {
                    mPlayerStatusListener.onSoundSwitch(mLastModel, null);
                }
                return;
            }
        }
        if (track != null && willPlay) {
            PlayErrorStatisticManager.getSingleInstance().onPlayerStatusChange(PlayErrorStatisticManager.PlayerStatus.READY_FOR_PLAY_NEXT);
        }

        Logger.logToFile("handleComplete XmPlayerService 421: willPlay=" + willPlay + "   index=" + index);

        // 在dlna 状态下并且不播放时不回调onSoundSwitch
        if (isDLNAState && !willPlay) {
            return;
        }

        if (index >= 0) {
            play(index, willPlay, PlayerConstants.PLAY_METHOD_AUTO_SWITCH);
        } else {
            // 播放完单首声音，把playmodel置为正常状态
            if (PlayMode.PLAY_MODEL_SINGLE.equals(mListControl
                    .getPlayMode())) {
                mListControl.setPlayMode(PlayMode.PLAY_MODEL_LIST);
            }

            PlayProgressManager.getInstance(this).savePlayOver40TrackToMMKV(mLastModel);

            // 为了解决付费收听的最后一曲要弹框的问题
            if (mPlayerStatusListener != null) {
                mPlayerStatusListener.onSoundSwitch(mLastModel, null);
            }
        }
    }


    private IXmAdsStatusListener mAdsListener = new IXmAdsStatusListenerExpand() {

        @Override
        public void onStartPlayAds(Advertis ad, int position) {
            if (sSoundPatchAdStateListener != null) {
                sSoundPatchAdStateListener.onAdPlaying();
            }
            Logger.logToFile("mAdsListener onStartPlayAds XmPlayerService 556: position=" + position + ",ad=" + ad);
            synchronized (XmPlayerService.class) {
                PlayableModel currPlayModel = getCurrPlayModel();
                if (currPlayModel != null) {
                    XmPlayPerformanceStatistic.getInstanse().endAdRequest(currPlayModel.getDataId(),
                            XmAdsManager.getInstance(XmPlayerService.getPlayerSrvice()).isHasAdCache());

                    XmPlayPerformanceStatistic.getInstanse().startAdShow(currPlayModel.getDataId());
                }

                // 播放的通知栏在miui10开始播放的时候加上
                if (BaseUtil.isOverMIUI10()) {
                    setNotification();
                }

                if (MixPlayerService.getMixService().isMixPlaying()) {
                    MixPlayerService.getMixService().pause();
                }

                if (mMediaControl != null) {
                    Logger.d("zhangkk", "onStartPlayAds");
                    mMediaControl.setAdsPlayStatus(true);
                }

                SendPlayStaticManager.getInstance().onStartPlayAds(ad, position);

                SoundPatchFrameworkManager.getInstance().tryToProduceFollowAdFunction(XmPlayerService.getPlayerSrvice(), currPlayModel);

                checkIsPauseTime();

                sendPlayerStartBroadCast();

                if (mAdsManager != null && mAdsManager.isInteractSoundAd(mAdsManager.getCurSoundAdList()) && !TextUtils.isEmpty(ad.getLockScrThumbnail())) {
                    // 闭屏请求到的声音互动广告
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateRemoteViewOnInteractSoundAdPlay(ad.getLockScrThumbnail(), ad.getName(),
                            mNotificationManager, mNotification, mNotificationId, isDark);
                }
            }
        }

        @Override
        public void onStartGetAdsInfo(int playMethod, boolean duringPlay, boolean isPaused) {
            if (!duringPlay && !isPaused) {
                if (sSoundPatchAdStateListener != null) {
                    sSoundPatchAdStateListener.onAdRequestBegin();
                }
            }
            Logger.logToFile("mAdsListener onStartGetAdsInfo XmPlayerService 580: playMethod=" + playMethod + ",duringPlay=" + duringPlay + ",isPaused=" + isPaused);
            synchronized (XmPlayerService.class) {
                SendPlayStaticManager.getInstance().onStartGetAdsInfo(playMethod, duringPlay, isPaused);
            }
        }

        @Override
        public void onGetAdsInfo(AdvertisList ads) {
            Logger.logToFile("mAdsListener onGetAdsInfo XmPlayerService 598: " + ads);
            synchronized (XmPlayerService.class) {
                SendPlayStaticManager.getInstance().onGetAdsInfo(ads);
            }
        }

        @Override
        public void onError(int what, int extra) {
            Logger.logToFile("mAdsListener onError XmPlayerService 618: what=" + what + ",extra=" + extra);
            synchronized (XmPlayerService.class) {
                SendPlayStaticManager.getInstance().onError(what, extra);
                if (mMediaControl != null) {
                    Logger.d("zhangkk", "onError");
                    mMediaControl.setAdsPlayStatus(false);
                }
            }
        }

        @Override
        public void onCompletePlayAds() {
            Logger.logToFile("mAdsListener onCompletePlayAds XmPlayerService 636:");

            UseTraceCollector.log("XmAdsManager___onCompletePlayAds__");

            synchronized (XmPlayerService.class) {
                PlayableModel currPlayModel = getCurrPlayModel();
                if (currPlayModel != null) {
                    XmPlayPerformanceStatistic.getInstanse().endAdRequest(currPlayModel.getDataId(),
                            XmAdsManager.getInstance(XmPlayerService.getPlayerSrvice()).isHasAdCache());

                    XmPlayPerformanceStatistic.getInstanse().endAdShow(currPlayModel.getDataId());
                }

                SendPlayStaticManager.getInstance().onCompletePlayAds();

                if (mAdsManager != null) {
                    mAdsManager.onAdPlayComplete();
                }

                if (mMediaControl != null) {
                    Logger.d("zhangkk", "onCompletePlayAds");
                    mMediaControl.setAdsPlayStatus(false);
                }
                if (mAdsManager != null && mAdsManager.isInteractSoundAd(mAdsManager.getCurSoundAdList())
                        && !TextUtils.isEmpty(mAdsManager.getCurrAdvertis().getLockScrThumbnail())) {
                    // 闭屏请求到的声音互动广告
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(
                            mListControl, mNotificationManager, mNotification,
                            mNotificationId, isDark);
                }
            }
        }

        @Override
        public void onAdsStopBuffering() {
            Logger.logToFile("mAdsListener onAdsStopBuffering XmPlayerService 654:");
            synchronized (XmPlayerService.class) {
                SendPlayStaticManager.getInstance().onAdsStopBuffering();
            }
        }

        @Override
        public void onAdsStartBuffering() {
            Logger.logToFile("mAdsListener onAdsStartBuffering XmPlayerService 672:");
            synchronized (XmPlayerService.class) {
                SendPlayStaticManager.getInstance().onAdsStartBuffering();
            }
        }


        @Override
        public void onGetForwardVideo(List<Advertis> advertis) {
            Logger.logToFile("mAdsListener onGetForwardVideo XmPlayerService 673: " + advertis);
            synchronized (XmPlayerService.class) {
                SendPlayStaticManager.getInstance().onGetForwardVideo(advertis);

                if (getAdManager() != null) {
                    getAdManager().uploadForwardVideoModel(XmPlayerService.this, advertis);
                }
            }
        }

    };

    private IXmDataChangedCallback mHistoryChangedCallback = new IXmDataChangedCallback() {
        @Override
        public void onDataInit() {
            SendPlayStaticManager.getInstance().onHistoryInitCallBack();
        }

        @Override
        public void onDataChanged() {
            SendPlayStaticManager.getInstance().onHistoryChangeCallBack();

            // 筛选上一个专辑和这个专辑不一样才发广播
            PlayableModel playableModel = getPlayableModel();
            PlayableModel lastPlayableModel = lastPlayModel;
            if (playableModel instanceof Track && lastPlayableModel instanceof Track) {
                Track playableModelTrack = (Track) playableModel;
                Track lastPlayableModelTrack = (Track) lastPlayableModel;
                long currentAlbumId = playableModelTrack.getAlbum() != null ? playableModelTrack.getAlbum().getAlbumId() : 0;
                long lastAlbumId = lastPlayableModelTrack.getAlbum() != null ? lastPlayableModelTrack.getAlbum().getAlbumId() : 0;
                if (currentAlbumId > 0 && currentAlbumId == lastAlbumId) {
                    return;
                }
            }
            IWidgetService iWidgetService = RouterServiceManager.getInstance().getService(IWidgetService.class);
            if (iWidgetService != null) {
                iWidgetService.sendHistoryBroadCast();
            }
        }
    };
    private int mNotificationType = 1;

    private static final String KEY_SET_NOTIFICATION_ANYWAY = "set_notification_anyway";
    private static final String KEY_NOTIFICATION_FROM_PUSH_GUARD = "notification_from_push_guard";

    public static final Intent getIntent(Context ctx, boolean setNotificationAnyway) {
        Intent intent = new Intent(ctx, XmPlayerService.class);

        intent.putExtra(KEY_SET_NOTIFICATION_ANYWAY, setNotificationAnyway);

        return intent;
    }

    public static final Intent getIntentFromPush(Context ctx, boolean setNotificationAnyway, boolean fromGuard) {
        Intent intent = new Intent(ctx, XmPlayerService.class);
        intent.putExtra(KEY_SET_NOTIFICATION_ANYWAY, setNotificationAnyway);
        intent.putExtra(KEY_NOTIFICATION_FROM_PUSH_GUARD, fromGuard);
        return intent;
    }

    private IVoiceWakeRecordListener mVoiceWakeRecordListener = new IVoiceWakeRecordListener() {
        @Override
        public void setPlayDataOutputListener(IVoiceWakePlayDataCallback voiceWakePlayDataCallback) {
            setPlayDataOutPutListener(new XMediaPlayer.OnPlayDataOutputListenerNew() {
                @Override
                public void onPlayDataOutputAndFrameCount(byte[] bytes, int i) {
                    if (voiceWakePlayDataCallback != null) {
                        voiceWakePlayDataCallback.onPlayDataOutputAndFrameCount(bytes, i);
                    }
                }

                @Override
                public void onPlayDataOutput(byte[] bytes) {
                }
            });
        }

        @Override
        public void removePlayDataOutPutListener() {
            setPlayDataOutPutListener(null);
        }

        @Override
        public long getStartTime() {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                return 0;
            }
            AudioTimestamp timeStamp = new AudioTimestamp();
            XmPlayerControl playerControl = getPlayControl();
            if (playerControl != null && playerControl.getAudioTrackTimeStamp(timeStamp)) {
                return (long) (timeStamp.nanoTime - timeStamp.framePosition * PlayerConstants.PLAYER_PER_FRAME);
            }
            return 0;
        }
    };

    @SuppressLint("NewApi")
    @Override
    public void onCreate() {
        super.onCreate();
        StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.onCreate, this);
        mService = this;
        UseTraceCollector.log("XmPlayerService: onCreate start");
        Logger.i(TAG, "---onCreate -- 1");
        createSimpleNotificationForStartProcess();
        StartServiceTimeoutMonitor.startStageRecord("service_start", this);
        StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.setNotification, this);
        setNotification();
        StartServiceTimeCollectUtil.endPlayerSelection(this);
        StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.initPlayerService, this);
        initPlayerService();
        StartServiceTimeCollectUtil.endPlayerSelection(this);
        PlayServiceStartUpMonitor.getInstance().setIsStarted(true);
        PlayDataOutputManager.Companion.getInstance().setVoiceWakeRecordListener(mVoiceWakeRecordListener);
        Logger.i(TAG, "---onCreate");
        UseTraceCollector.log("XmPlayerService: onCreate end");
        StartServiceTimeCollectUtil.endPlayerSelection(this);
        IPushGuardUbtTrace pushGuardUbtTrace = RouterServiceManager.getInstance().getService(IPushGuardUbtTrace.class);
        if (pushGuardUbtTrace != null) {
            pushGuardUbtTrace.traceNoPermissionUser(false);
        }
    }

    private void initPlayerService() {
        UseTraceCollector.log("XmPlayerService: initPlayerService start");
        mService = this;
        XMediaPlayerConstants.resetCacheDir(mAppCtx);
        SharedPreferencesUtil.getInstanceForPlayer(this);
        DozeMonitor.getInstance().init(this);
        WifiSleepMonitor.getInstance().init(this.getApplicationContext());
        if (mIDomainServerIpCallback == null) {
            mIDomainServerIpCallback = new IDomainServerIpCallback() {
                @Override
                public String[][] getStaticDomainServerIp(String url) {
//                        return DNSCache.getInstance().getDomainServerIpString(url);
                    return null;
                }

                @Override
                public String getNewDownloadUrlIfExpire(long dataId, String playUrl) {
                    if (TextUtils.isEmpty(playUrl))
                        return null;
                    PlayableModel playableModel = getPlayableModel();

                    if (playableModel == null) {
                        return null;
                    }

                    Track curTrack = (Track) playableModel;

                    if (dataId > 0 && dataId != curTrack.getDataId()) {
                        if (getPlayListControl() != null) {
                            List<Track> playList = getPlayListControl().getPlayList();
                            if (playList != null && !playList.isEmpty()) {
                                for (Track track : playList) {
                                    if (track != null && dataId == track.getDataId())  {
                                        curTrack = track;
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    Track trackInfo = CommonRequestForMain.getTrackInfo(curTrack);
                    if (trackInfo != null) {
                        // 如果有中插广告的地址，这里直接返回下
                        if (trackInfo != null && trackInfo.getMiddleInsertAdInfo() != null && useMiddleAdUrl()) {
                            TrackAudioAdInfo middleAdInfo = trackInfo.getMiddleInsertAdInfo();
                            String trackTimbreType = TrackUrlChooseManager.getInstance().getTrackByTimbreType();
                            PlayUrlModel model = middleAdInfo.getCurAdPlayUrlModel(trackInfo, trackTimbreType, canInterceptReturnMiddleAdUrl(trackInfo));
                            if (model != null && !TextUtils.isEmpty(model.url)) {
                                mPlayerControl.updateCurPlayUrl(model.url);
                                return model.url;
                            }
                        }

                        // 1. 判断是否tts
                        // 2. 判断选择音色有没有对应的url
                        // 3. 如果有直接返回
                        String trackTimbreType = TrackUrlChooseManager.getInstance().getTrackByTimbreType();
                        boolean fixTimbreSwitchError = MmkvCommonUtil.getInstance(mAppCtx).getBoolean(PreferenceConstantsInOpenSdk.KEY_FIX_TTS_TIMBRE_SWITCH_ERROR, true);
                        if (trackTimbreType != null && fixTimbreSwitchError && TrackUrlChooseManager.getInstance().isTrackHasTimbres(trackInfo) &&
                                trackInfo.getPlayUrlInfoList() != null && trackInfo.getPlayUrlInfoList().size() > 0) {
                            String trackUrl = null;
                            boolean found = false;
                            for (PlayUrlInfo urlInfo : trackInfo.getPlayUrlInfoList()) {
                                if (urlInfo != null) {
                                    trackUrl = PlayUrlUtil.getDecodedUrl(urlInfo);
                                    Logger.logToFile("TraceTimbre getNewDownloadUrlIfExpire >>>>>>> PlayUrlInfo type = " + urlInfo.getType() + ", trackUrl = " + trackUrl);
                                    if (trackTimbreType.equals(urlInfo.getType())) {
                                        if (!TextUtils.isEmpty(trackUrl)) {
                                            found = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                            if (xdcsPost != null) {
                                String msg = String.format("timbreType = %s, trackId = %s, found = %s, trackUrl = %s", trackTimbreType,
                                        trackInfo.getDataId(), found, trackUrl);
                                xdcsPost.statErrorToXDCS("tts_timbre_switch_error_type", msg);
                            }
                            if (found) {
                                Logger.logToFile("TraceTimbre getNewDownloadUrlIfExpire >>>>>>> found in loaded playUrlInfoList, trackTimbreType = " + trackTimbreType +
                                        ", trackUrl = " + trackUrl);
                                mPlayerControl.updateCurPlayUrl(trackUrl);
                                return trackUrl;
                            }
                        }

                        if (TrackUrlChooseManager.getInstance().isTrackHasChildAiTimbres(trackInfo) &&
                                trackInfo.getChildAiTimbrePlayUrlList() != null && trackInfo.getChildAiTimbrePlayUrlList().size() > 0) {
                            final String trackChildAiTimbreType = TrackUrlChooseManager.getInstance().getTrackByChildAiTimbreType();
                            PlayUrlModel model = TrackUrlChooseManager.getInstance().getTrackUrlForChildAiTimbre(trackInfo, trackChildAiTimbreType);
                            boolean found = model != null && !TextUtils.isEmpty(model.url);
                            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                            if (xdcsPost != null) {
                                String msg = String.format("timbreType = %s, found=%s, trackId = %s, model=%s",
                                        trackChildAiTimbreType, found, trackInfo.getDataId(), model);
                                xdcsPost.statErrorToXDCS("childAi_timbre_403_error", msg);
                            }
                            if (model != null && !TextUtils.isEmpty(model.url)) {
                                mPlayerControl.updateCurPlayUrl(model.url);
                                return model.url;
                            }
                        }

                        String trackUrl = TrackUrlChooseManager.getInstance().getTrackUrl(trackInfo, true);
                        if (!TextUtils.isEmpty(trackUrl)) {
                            Logger.logToFile("XmPlayService getNewDownloadUrlIfExpire dataId=" + dataId + ",trackUrl=" + trackUrl + ",trackInfo=" + trackInfo);
                            mPlayerControl.updateCurPlayUrl(trackUrl);
                            return trackUrl;
                        }
                    }

                    return null;
                }

                @Override
                public void setBadIp(String domain, String ipUrl, String url) {
//                        DNSCache.getInstance().setBadIp(domain, ipUrl, url);
                }
            };
            StaticConfig.setDomainServerIpCallback(mIDomainServerIpCallback);

            if (mFlvDataCallbackForPlay == null) {
                mFlvDataCallbackForPlay = new IFlvDataCallback() {
                    @Override
                    public void dataOutput(int type, byte[] bytes) {
                        if (mPlayerControl != null && mPlayerControl.getMediaPlayer() != null) {
                            flvDataOutputCallback(type, bytes, mPlayerControl.getMediaPlayer().getTotalBufferedDuration());
                        } else {
                            flvDataOutputCallback(type, bytes, 0);
                        }
                    }
                };
            }
        }
        PlayProgressManager.getInstance(this);
        TrafficStatisticManager.getInstance().init(this);
        StaticConfig.setUseragent(CommonRequestForMain.getUserAgent());
        if (mAppCtx == null) {
            mAppCtx = this.getApplicationContext();
        }
        if (mBaseAppWidgetProviders == null) {
            mBaseAppWidgetProviders = new CopyOnWriteArrayList<>();
            mBaseAppWidgetProviders.add(new WidgetProvider());
            mBaseAppWidgetProviders.add(new PlayBoxWidgetProvider2x2());
            mBaseAppWidgetProviders.add(new PlayBoxWidgetProvider4x2());
            mAutoStopWidgetProvider = new AutoStopWidgetProvider4x2();
            mBaseAppWidgetProviders.add(mAutoStopWidgetProvider);
        }
        mDailyNewsWidgetProvider = new DailyNewsWidgetProvider4x2();
        if (mPlayerConfig == null) {
            mPlayerConfig = XmPlayerConfig.getInstance(mAppCtx);
        }
        if (mPlayerControl == null) {
            mPlayerControl = new XmPlayerControl(mAppCtx);
            mPlayerControl.setPlayerStatusListener(mPlayerStatusListener);
            mPlayerControl.setOnPlayDataOutputListener(mOnPlayDataOutputListener);
            mPlayerControl.setPlaySeekListener(mPlaySeekListener);
            mPlayerControl.setOnLiveStatusListener(mLiveStatusListener);
        }
        if (mListControl == null) {
            mListControl = new XmPlayListControl();
        }
        if (mPlayerImpl == null) {
            mPlayerImpl = new XmPlayerImpl();
        }
        if (mPlayLastPlayTrackInAlbum == null) {
            mPlayLastPlayTrackInAlbum = getSharedPreferences(PreferenceConstantsInOpenSdk.OPENSDK_FILENAME_PLAY_TRACK_HISTORY_RECORD,
                    Context.MODE_PRIVATE);
        }
        TrackUrlChooseManager.getInstance().init();

        if (mPlayerAudioFocusControl == null) {
            mPlayerAudioFocusControl = new XmPlayerAudioFocusControl(mAppCtx);
        }
        mStatisticsManager = XmStatisticsManager.getInstance();
        mStatisticsManager.setContext(this);
        mXmPlayStatisticsManager = XmPlayStatisticsManager.Companion.getInstance();
        XmMixStatisticsManager.Companion.getInstance();
        mAdsManager = XmAdsManager.getInstance(mAppCtx);
        mAdsManager.setAdsStatusListener(mAdsListener);
        mNotificationManager = SystemServiceManager.getNotificationManager(mAppCtx);
        if (mMediaControl == null) {
            try {
                mMediaControl = IMediaControl.getMediaControl(this);
                mMediaControl.initMediaControl();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        initHistoryManager();

        if (mIStatToServerFactory == null) {
            mIStatToServerFactory = new StatToServerFactoryImplForMain();
            StaticConfig.setIStatToServerFactory(mIStatToServerFactory);
        }

        addScreenChangeBroadCast();

        // service注册成功后会执行，获取音频焦点，暂停其他应用的音频播放
        // 通过mPlayerAudioFocusControl.setAudioFocusAtStartState()该方法是有bug的，酷狗音乐无法播放
        // 该操作放到MainActivity中执行
        // mPlayerAudioFocusControl.setAudioFocusAtStartStateAtTransient();

        OnPlayErrorRetryUtilForPlayProcess.register(this);
        SkipHeadTailManager.getInstance().init();
        TempoManagerForPlayer.getInstance().init();
        PlayErrorStatisticManager.getSingleInstance().init(this);
        XmAppHelper.runOnOnWorkThreadDelayed(new Runnable() {
            @Override
            public void run() {
                AppExitInfoManager.getSingleInstance().initialize(XmPlayerService.this);
            }
        }, 1000);
        ScreenStatusReceiver.init(this);
        SoundPatchManager.getInstance().init();
        SoundPatchArbitrateManager.getInstance().init();
        SoundEffectPlayerManager.getInstance().init();
        MainAppBuzStub.initMainAppBuz();
        updateDarkMode();

        if (sServiceLifeCallBacks != null) {
            for (IServiceLifeCallBack serviceLifeCallBack : sServiceLifeCallBacks) {
                serviceLifeCallBack.onServiceCreate();
            }
        }

        TrackInfoPrepareManager.getInstance(this).init();

        FilterCarBluetoothDevice.register(this);
        UseTraceCollector.log("XmPlayerService: initPlayerService end");
    }

    private boolean canInterceptReturnMiddleAdUrl(Track track) {
        if(track == null){
            return false;
        }
        boolean enable = AIgcAdExposureFrequencyHelper.getInstance().checkExposureFrequency();
        if (enable) {
            AIgcAdExposureFrequencyHelper.getInstance().putNoNeedExposureTrackId(track.getDataId());
        }
        return enable;
    }

    public void flvDataOutputCallback(int type, byte[] bytes, long bufferDuration) {
        synchronized (XmPlayerService.class) {
            if (mFlvDataCallBack != null) {
                int N = mFlvDataCallBack.beginBroadcast();
                for (int i = 0; i < N; i++) {
                    IXmFlvDataCallback l = mFlvDataCallBack
                            .getBroadcastItem(i);
                    try {
                        l.dataOutput(type, bytes, bufferDuration);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                mFlvDataCallBack.finishBroadcast();
            }
        }
    }

    public void flvMetaDataInfoCallback(int width, int height, double audiobitrate, double videobitrate) {
        synchronized (XmPlayerService.class) {
            if (mFlvDataCallBack != null) {
                int N = mFlvDataCallBack.beginBroadcast();
                for (int i = 0; i < N; i++) {
                    IXmFlvDataCallback l = mFlvDataCallBack
                            .getBroadcastItem(i);
                    try {
                        l.onMetaInfoCallback(width, height, audiobitrate, videobitrate);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                mFlvDataCallBack.finishBroadcast();
            }
        }
    }

    private void addScreenChangeBroadCast() {
        if (mScreenBroadcastReceiver == null) {
            mScreenBroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    //屏幕亮或者屏幕锁定,解决部分手机例如oppo，锁屏播放器通知栏控制条不可见的bug
                    if (Intent.ACTION_SCREEN_ON.equals(action)) {
                        XmNotificationCreater.getInstanse(context).reNotify(mNotificationManager, mNotificationId);
                    }
                }
            };
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            try {
                registerReceiver(mScreenBroadcastReceiver, filter);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void removeScreenChangeBroadCast() {
        if (mScreenBroadcastReceiver != null) {
            try {
                unregisterReceiver(mScreenBroadcastReceiver);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mScreenBroadcastReceiver = null;
        }
    }

    public void updateViewStateAtPause() {
        boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
        XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                mNotificationManager, mNotification, mNotificationId, isDark, isSleepPlayModel());
    }

    private void initHistoryManager() {
        if (mHistoryManager == null) {
            mHistoryManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
            if (mHistoryManager != null) {
                if (MixPlayerService.getMixService().getPlaySource() != null) {
                    // TODO 更新通知栏
                    // 初始化的时候不需要取之前的历史记录
                } else {
                    mHistoryManager.setHistoryPlayListToPlayer(false);
                }
                if (mHistoryChangedCallback != null) {
                    mHistoryManager.registerOnHistoryUpdateListener(mHistoryChangedCallback);
                }
            }
        }
    }

    public void onSessionCreated(MediaSessionCompat session){
        Logger.d(TAG, "new session created");
        mSessionCompat = session;
        XmNotificationCreater.getInstanse(mAppCtx).setMediaSession(mSessionCompat);
        if (hasInitNotification) {
            int currentDarkMode = mAppCtx.getApplicationContext().getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
            boolean isDarkSystem = currentDarkMode == Configuration.UI_MODE_NIGHT_YES;
            if (NotificationColorUtils.isSystemDarkModeIsOpen() != isDarkSystem) {
                NotificationColorUtils.setSystemDarkMode(isDarkSystem);
                NotificationColorUtils.forceUpadteColorModel = true;
            }
            XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(mListControl, mNotificationManager,
                mNotification, mNotificationId, NotificationColorUtils.isDarkNotificationBar(mAppCtx));
        }
    }

    @Nullable
    public static XmPlayerService getPlayerSrvice() {
        return (XmPlayerService) mService;
    }

    public XmPlayerImpl getPlayerImpl() {
        return mPlayerImpl;
    }

    public String getHistoryPos(String historyIds) {
        try {
            if (mPlayerImpl != null) {
                return mPlayerImpl.getHistoryPos(historyIds);
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return "";
    }

    public void changeNotificationStyle(int style) {
        NotificationStyleUtils.changeStyle(style, mAppCtx);
        MmkvCommonUtil.getInstance(mAppCtx).saveInt(NotificationStyleUtils.NOTIFICATION_STYLE, style);
        hasInitNotification = false;
        setNotification();

        boolean isLive = false;
        if (mListControl != null && mListControl.getCurrentPlayableModel() != null) {
            String kind = mListControl.getCurrentPlayableModel().getKind();
            isLive = PlayableModel.KIND_LIVE_FLV.equals(kind)
                            || PlayableModel.KIND_ENT_FLY.equals(kind)
                    || PlayableModel.KIND_LIVE_COURSE.equals(kind);
        }

        if (mAppCtx != null && mNotificationManager != null) {
            XmNotificationCreater.getInstanse(mAppCtx).updateViewState(mNotificationManager, mNotification,
                mNotificationId, NotificationColorUtils.isDarkNotificationBar(mAppCtx), isSleepPlayModel(),
                isLive, !isPlaying(), isSketchVideo());
        }
    }

    public void setVolume(float arg0, float arg1) {
        if (mPlayerControl == null) {
            return;
        }
        mPlayerControl.setVolume(arg0, arg1);
    }

    public boolean isPlayingTrack() {
        boolean isTrackPlay = false;
        if (mPlayerControl != null) {
            // isTrackPlay = mPlayerControl.isPlaying();
            isTrackPlay = mPlayerControl.getPlayerState() == PlayerConstants.STATE_STARTED;
        }
        return isTrackPlay;
    }

    public int getPlayerState() {
        if (mPlayerControl != null) {
            return mPlayerControl.getPlayerState();
        }
        return -1;
    }

    public boolean isPlaying() {
        boolean isTrackPlay = false;
        if (mPlayerControl != null) {
            // isTrackPlay = mPlayerControl.isPlaying();
            isTrackPlay = mPlayerControl.getPlayerState() == PlayerConstants.STATE_STARTED;
        }
        boolean isAdPlay = false;
        if (mAdsManager != null) {
            isAdPlay = mAdsManager.isAdsPlaying();
        }

        // 这里需要注意下贴片的播放状态
        if (SoundPatchManager.getInstance().isPlaying()) {
            return true;
        }

        return isTrackPlay || isAdPlay;
    }

    public boolean isBuffering() {
        if (mPlayerControl != null) {
            return mPlayerControl.isBuffering() || getPlayerStatus() == PlayerConstants.STATE_PREPARING;
        }
        return false;
    }

    public boolean isAdPlaying() {
        if (mAdsManager != null) {
            return mAdsManager.isAdsPlaying();
        }

        return false;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        UseTraceCollector.log("XmPlayerService: onStartCommand start");
        StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.onStartCommand, this);
        initPlayerService();

        boolean setNotificationAnyway = intent != null
                && intent.getBooleanExtra(KEY_SET_NOTIFICATION_ANYWAY, false);

        if (PushGuardPlayerManager.getInstance().mIsFirstFromPushGuard == -1 && intent != null && intent.hasExtra(KEY_NOTIFICATION_FROM_PUSH_GUARD)) {
            boolean fromGuard = intent.getBooleanExtra(KEY_NOTIFICATION_FROM_PUSH_GUARD, false);
            PushGuardPlayerManager.getInstance().setFromPushGuard(fromGuard);
            if (fromGuard) {
                PushGuardPlayerManager.getInstance().init(mAppCtx);
            }
            PushGuardPlayerManager.getInstance().mIsFirstFromPushGuard = fromGuard ? 1 : 0;
        } else {
            PushGuardPlayerManager.getInstance().mIsFirstFromPushGuard = 0;
        }

        Logger.i(TAG, "---onStartCommand " + setNotificationAnyway);

        if (setNotificationAnyway) {

            setNotification();
        }

        UseTraceCollector.log("XmPlayerService: onStartCommand end");
        StartServiceTimeCollectUtil.endPlayerSelection(this);
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.onBind, this);
        initPlayerService();
        Logger.i(TAG, "onBind " + mPlayerImpl.hashCode());
        UseTraceCollector.log("XmPlayerService: onBind " + mPlayerImpl.hashCode());
        StartServiceTimeCollectUtil.endPlayerSelection(this);
        return mPlayerImpl;
    }

    public void closeNotification() {
        if (mNotificationManager != null) {
            try {
                stopForeground(true);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mNotificationManager.cancel(mNotificationId);
            notificationCancled = true;
            Logger.log("process closeNotification mNotificationId:" + mNotificationId);
        }
    }

    @Override
    public void onDestroy() {
        Logger.i(TAG, "---onDestroy");
        SustainedListenService.unbindSustainedListenerService(this);
        super.onDestroy();
        hasPlayed = false;
        lastPlayModel = null;
        mPlayerAudioFocusControl.setAudioFocusAtStopState(); // service destroy后，需要释放播放音频焦点，这样可以让其他应用的音频继续播放
        TrafficStatisticManager.getInstance().release();
        closeNotification();
        sendPlayerPauseBroadCast();
        stopPlay(PauseReason.StopBusiness.XmPlayServiceDestroy);
        MixPlayerService.getMixService().onDestroy();
        stopForeground(true);
        mMediaControl.release();
        mPlayerControl.release();
        mStatisticsManager.release();
        mAdsManager.release();
        mListControl.release();
        XmNotificationCreater.release();
        mService = null;
        SendPlayStaticManager.getInstance().onDestory();
        mMixPlayerDispatcher.kill();
        mFlvDataCallBack.kill();
        StaticConfig.release();
        mIDomainServerIpCallback = null;
        FileUtilBase.release();
        PlayCacheByLRU.release();
        MediadataCrytoUtil.release();
        XmPlayerManagerForPlayer.release();
        SharedPreferences sp = mAppCtx.getSharedPreferences("plugin_share_file", Context.MODE_MULTI_PROCESS);
        if (sp != null) {
            if (sp.getBoolean("need_exit_process_play", false)) {

                SharedPreferences.Editor editor = sp.edit();
                editor.putBoolean("need_exit_process_play", false);
                boolean isSuccess = editor.commit();
                Logger.i("ApplicationManager", "kill process play : plugin_share_file " + isSuccess);
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(0);
            }
        }
        if (mHistoryManager != null) {
            mHistoryManager.release();
            mHistoryManager.unRegisterOnHistoryUpdateListener(mHistoryChangedCallback);
        }

        if (mPlayerAudioFocusControl != null) {
            mPlayerAudioFocusControl.release();
            mPlayerAudioFocusControl = null;
        }

        SkipHeadTailManager.getInstance().release();
        TempoManagerForPlayer.getInstance().release();
        removeScreenChangeBroadCast();
        OnPlayErrorRetryUtilForPlayProcess.unregister(this);
        SoundPatchManager.getInstance().release();
        SoundPatchArbitrateManager.getInstance().release();
        PlayErrorStatisticManager.getSingleInstance().release();
        if (sServiceLifeCallBacks != null) {
            for (IServiceLifeCallBack serviceLifeCallBack : sServiceLifeCallBacks) {
                serviceLifeCallBack.onServiceDestory();
            }
        }
        TrackInfoPrepareManager.release();
        SoundEffectPlayerManager.getInstance().release();
        MainAppBuzStub.releaseMainAppBuz();
        PlayDataOutputManager.Companion.getInstance().setVoiceWakeRecordListener(null);
        FilterCarBluetoothDevice.unRegister(this);
    }

    private boolean isNewList(List<? extends PlayableModel> list) {
        return mLastModel == null || list == null || !list.contains(mLastModel);
    }

    private long mMaxSaveTime = 1000L * 60 * 60 * 24 * 30 * 3;//3个月
    private String mSeparator = "__xm__";
    private final Object mSaveHistoryLock = new Object();

    @WorkerThread
    private void saveLastPlayTrackInAlbum(Track track) {
        synchronized (mSaveHistoryLock) {
            if (track == null || !PlayableModel.KIND_TRACK.equals(track.getKind()) || track.getAlbum() == null) {
                return;
            }
            Editor editor = mPlayLastPlayTrackInAlbum.edit();
            Map<String, ?> map = mPlayLastPlayTrackInAlbum.getAll();
            //记录大于500条，将最近收听时间超过3个月的专辑进行一次清理
            if (map != null && map.size() > 500) {
                try {
                    for (Map.Entry<String, ?> entry : map.entrySet()) {
                        Object value = entry.getValue();
                        if (value == null || TextUtils.isEmpty(value.toString())) {
                            editor.remove(entry.getKey());
                            continue;
                        }

                        String[] split = value.toString().split(mSeparator);
                        if (split.length > 1) {
                            long trackSaveTime = 0;
                            try {
                                trackSaveTime = Long.parseLong(split[1]);
                            } catch (NumberFormatException e) {
                                e.printStackTrace();
                                editor.remove(entry.getKey());
                                continue;
                            }
                            if (System.currentTimeMillis() - trackSaveTime > mMaxSaveTime) {
                                editor.remove(entry.getKey());
                            }
                        } else {
                            editor.remove(entry.getKey());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (track.getAlbum() == null) {
                return;
            }
            editor.putString("" + track.getAlbum().getAlbumId(), new GsonBuilder().serializeSpecialFloatingPointValues().create().toJson(track) + mSeparator + System.currentTimeMillis());
            // 大于等于android2.3
            editor.apply();
        }
    }

    private String getLastPlayTrackInAlbumInner(String albumId) {
        try {
            String trackStr = mPlayLastPlayTrackInAlbum.getString(albumId, null);
            if (TextUtils.isEmpty(trackStr))
                return null;
            return trackStr.split(mSeparator)[0];
        } catch (Exception e) {
            return null;
        }
    }

    private Runnable setWakeModeRunnable = new Runnable() {
        @Override
        public void run() {
            if (mPlayerControl != null) {
                mPlayerControl.setWakeMode(false);
            }
        }
    };

    public void playTrack(final Track track, final boolean autoPlay) {
        playTrack(track, autoPlay, false);
    }

    private Pair<Track, long[]> mPair = null;

    public void playTrack(final Track track, final boolean autoPlay, final boolean onChooseTrackQualityChanged) {
        // 保证在切歌过程中,保持wifi唤醒,和cpu运转
        if (mPlayerControl != null) {
            mPlayerControl.setWakeMode(true);
            getTimeHander().removeCallbacks(setWakeModeRunnable);
            getTimeHander().postDelayed(setWakeModeRunnable, 10_000);
        }
        mPair = new Pair<>(track, new long[] {System.currentTimeMillis(), 1});
        Logger.logToFile("XmPlayerService playTrack getTrackUrl begin autoPlay=" + autoPlay + "," + TrackUtil.trackToStr(track));
        TrackUrlChooseManager.getInstance().getTrackUrl(track,
                new TrackUrlChooseManager.IPlayUrlGetCallBack() {
                    @Override
                    public void onStartGet() {
                        SendPlayStaticManager.getInstance().onRequestPlayUrlBegin();
                    }

                    @Override
                    public void onSuccess(String url) {
                        onSuccess(new PlayUrlModel(0, url, 0f, null, null));
                    }

                    @Override
                    public void onSuccess(String url, float gain) {
                        onSuccess(new PlayUrlModel(0, url, gain, null, null));
                    }

                    @Override
                    public void onSuccess(PlayUrlModel model) {
                        PlayableModel curPlayModel = getCurrPlayModel();
                        long playingDataId = curPlayModel != null ? curPlayModel.getDataId() : 0;
                        Logger.logToFile("XmPlayerService playTrack getTrackUrl playingDataId=" + playingDataId + ",model=" + model);

                        if (model != null && model.trackId > 0 && model.trackId != playingDataId) {
                            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                            if (xdcsPost != null) {
                                xdcsPost.statErrorToXDCS("PlayingTrackNotEqual", "playingDataId equal ERROR model=" + model + ",track=" + curPlayModel);
                            }
                            boolean checkPlayingTrackId = checkPlayingTrackId();
                            Logger.logToFile("playingDataId equal ERROR checkPlayingTrackId=" + checkPlayingTrackId + ",model=" + model + ",track=" + curPlayModel);
                            if (checkPlayingTrackId) {
                                return;
                            }
                        }

                        String url = null;
                        float gain = 0f;
                        if (model != null) {
                            url = model.url;
                            gain = model.gain;
                        }

                        if (track != null && track.getChildAlbumInWhiteList() && model != null) {
                            boolean success = !TextUtils.isEmpty(url);
                            boolean isDownload = url != null && !url.startsWith("http");
                            boolean urlIsChildAiTimbre = model.type != null
                                    && model.type.equals(TrackUrlChooseManager.getInstance().getTrackByChildAiTimbreType());
                            if (!isDownload && urlIsChildAiTimbre) {
                                // 这里设置noacc的目的是不允许参与后续的pcdn和sdk
                                try {
                                    url = Uri.parse(url).buildUpon().appendQueryParameter("noacc", "1").build().toString();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            SendPlayStaticManager.getInstance().onChildAiTimbreUrlGet(success, isDownload, model.type);
                        }

                        float volumeGain = getVolumeGain(track, url);
                        boolean useNewGain = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_USE_NEW_GAIN, true);
                        if (track != null) {
                            Logger.logToFile("TrackUrlChooseManager onSuccess 1 id=" + track.getDataId()
                                    + ",title=" + track.getTrackTitle() + ",url=" + url
                                    + ",volumeGain=" + volumeGain + ", gain=" + gain + ",useNewGain=" + useNewGain);
                        }
                        // 播放纪晓岚41集、42集后，会有缓存，这时切换到别的声音，再切回到纪晓岚专辑，因为有缓存的原因，track的url不会解密处理，因此会出现拿不到增益值
                        if (useNewGain) {
                            if (Math.abs(volumeGain) < 0.1) {
                                volumeGain = gain;
                            }
                        }
                        // 在返回之前 设置的autoPlay是一直生效的 如果XmPlayManager 触发play 则无效操作
                        if (track != null && (track.isAntiLeech() || track.isPayTrack())
                                && url != null && url.startsWith("http")
                                && !url.contains(XMediaPlayerConstants.IS_CHARGE)) {

                            Logger.logToFile("playTrack add 15 appendIsCharge = " + url + "   " + track);

                            try {
                                Uri uri = Uri.parse(url);
                                url = uri.buildUpon().appendQueryParameter(XMediaPlayerConstants.IS_CHARGE, "true").build().toString();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        SendPlayStaticManager.getInstance().onRequestPlayUrlSuccess();

                        boolean startPlay = autoPlay || isAutoPlayAfterGetPlayUrl;
                        isAutoPlayAfterGetPlayUrl = false;
                        Logger.logToFile("playTrack add 15 " + startPlay + ", " + autoPlay + " ," + url + "   curPlayMethod=" + mCurrPlayMethod);

                        boolean noAcc = false;
                        try {
                            noAcc = "1".equals(Uri.parse(url).getQueryParameter("noacc"));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        boolean baiduReplaced = false;
//                        if (!noAcc && BaiduPcdnManager.getInstance().canUse()) {
//                            String urlReWrite = BaiduPcdnManager.getInstance().getUrl(url, EdgeManager.TYPE.VOD);
//                            baiduReplaced = url != null && !url.equals(urlReWrite);
//                            if (baiduReplaced) {
//                                url = urlReWrite;
//                                Logger.log("XmPlayerService : pcdnUpdate for baidu " + url);
//                            }
//                        }

                        String pcdnFailReason = "none";
                        boolean pcdnPreloadAndMethodAutoSwitch = track != null && track.hasUpdatePcdnDomain() && url != null && mCurrPlayMethod == PlayerConstants.PLAY_METHOD_AUTO_SWITCH;
                        boolean isUsePcdnInCache = isUsePcdnInCache();
                        boolean urlHasPreloaded = isUsePcdnInCache && MediaCacheManager.get().isDataSourcePreloaded(url, 0.99f);
                        if (!noAcc) {
                            if (pcdnPreloadAndMethodAutoSwitch || urlHasPreloaded) {
                                if (!baiduReplaced) {
                                    try {
                                        pcdnFailReason = "can-replace";
                                        Uri uri = Uri.parse(url);
                                        Pair<String, String> pair = PcdnManager.getSingleInstance().getPCDNDomain(uri.getHost(), 4);
                                        url = url.replaceFirst(uri.getHost(), pair.first);
                                        pcdnFailReason = pair.second;
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                Logger.log("XmPlayerService : pcdnUpdate  url=" + url);
                            } else {
                                pcdnFailReason = (track != null && track.hasUpdatePcdnDomain()) + "-" + mCurrPlayMethod + "-" + isUsePcdnInCache;
                            }
                        } else {
                            pcdnFailReason = "noacc";
                        }
                        Logger.logToFile("playTrack pcdnUpdate autoSwitch: " + pcdnPreloadAndMethodAutoSwitch + ", urlPreload: " + urlHasPreloaded +
                                ", new url = " + url + ", old url = " + getCurPlayUrl() + ",noAcc=" + noAcc + ",pcdnFailReason=" + pcdnFailReason);

                        if (startPlay || (url != null && !TextUtils.equals(url, getCurPlayUrl()))) {
                            playTrackInner(url, track, startPlay, volumeGain, pcdnFailReason);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        mPair = new Pair<>(track, new long[] {System.currentTimeMillis(), 2});
                        if (track != null && track.getChildAlbumInWhiteList()) {
                            SendPlayStaticManager.getInstance().onChildAiTimbreUrlGet(false, false, null);
                        }

                        SendPlayStaticManager.getInstance().onRequestPlayUrlError(code, message);

                        PlayInfoErrorResponseInfo playInfoErrorResponseInfo =
                                new PlayInfoErrorResponseInfo(code,
                                        code == BaseCall.ERROR_CODE_DEFALUT ? "网络异常，请稍后重试" : message);
                        if (code == BaseCall.ERROR_CODE_DEFALUT) {
                            playInfoErrorResponseInfo.setRequestErrorMessage(message);
                        }

                        if (code != BaseCall.ERROR_CODE_DEFALUT) {
                            playInfoErrorResponseInfo.setFromServiceError(true);
                        }
                        track.setPlayInfoErrorResponseInfo(playInfoErrorResponseInfo);

                        if (code == DTransferConstants.NO_AUTHORIZED_CODE) {
                            track.setAuthorized(false);
                        }

                        playTrackInner(null, track, autoPlay, XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT, "requestError");

                        if (code == DTransferConstants.NO_AUTHORIZED_CODE) {
                            mPlayerStatusListener.onSoundSwitch(track, null);
                        }
                    }
                }, true, false, false, onChooseTrackQualityChanged);
    }

    private boolean checkPlayingTrackId() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_CHECK_PLAYING_TRACK_ID, false);
    }

    public void playTrackInner(String playUrl, Track track, final boolean autoPlay, float volumeGain, String pcdnFailReason) {
        int start = PlayProgressManager.getInstance(this).getSoundHistoryPos(track.getDataId());//单位毫秒
        int historyStart = start;
        SubordinatedAlbum album = track.getAlbum();
        int uploadVal = -1;
        if(album != null) {
            long albumId = album.getAlbumId();
            Context context = XmPlayerService.getPlayerSrvice();
            if (context == null) {
                context = mAppCtx;
            }
            int skipHeader = QuickListenForPlayProcessUtil.getQuickListenSkipHead(context, track);
            Logger.d(TAG, "playTrackInner QuickListenSkipHead=" + skipHeader);
            if (skipHeader == -1 && !QuickListenForPlayProcessUtil.isQuickListen()) {
                skipHeader = SkipHeadTailManager.getInstance().getHead(albumId);
            }

            uploadVal = skipHeader;
            if (start < skipHeader) {
                start = skipHeader;
            }
        }
        if (track.isAudition() && start > track.getSampleDuration() * 1000) {
            start = 0;
        }

        if (start < 0 || start > track.getDuration() * 1000) {
            start = 0;
        }

        if (start > 0 && Math.abs(start / 1000 - track.getDuration()) <= 2 && checkOffset()) {
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null) {
                xdcsPost.statErrorToXDCS("TrackStartOffset0",
                        "track offset=" + start + ",duration=" + track.getDuration()
                                + ",historyStart=" + historyStart + ",uploadVal=" + uploadVal);
            }

            start = 0;
        }

        // 如果是配音秀视频不进行播放
        if (track.getType() == Track.TYPE_DUBSHOW) {
            closeNotification();
            if (track.isVideo()) {
                playUrl = null;
            }
        }

        if (track.getType() == Track.TYPE_TAIHE && !TextUtils.isEmpty(playUrl) && playUrl.startsWith("http")) {
            playUrl += PlayerUtil.TAIHE_TAG;
        }

        if (Logger.isDebug) {
            Logger.logToFile("XmPlayerService : initAndPlay autoPlay = " + autoPlay + "   " + track + "    " + Log.getStackTraceString(new Throwable()));
        }

        // 在声音播放之前，清理主播自己的声音缓存， 验证播放异常轻音乐问题
        cleanAnchorPlayCache(playUrl, track);

        long time = System.currentTimeMillis();
        boolean result;
        if (autoPlay) {
            if (track.isKindOfLive()) {
                XmLivePerformanceStatistic.getInstance().setMediaSource(track, playUrl);
            }
            PlayPerformanceModel model = XmPlayPerformanceStatistic.getInstanse().getCurrentPlayPerformanceModel();
            if (model != null && model.trackId == track.getDataId() && !model.hasStatisticOver) {
                if (model.extra != null) {
                    if (TextUtils.isEmpty(pcdnFailReason)) {
                        pcdnFailReason = "none";
                    }
                    model.extra.put("pcdn_fail", pcdnFailReason);
                    Logger.logToFile("XmPlayerService playTrackInner pcdn_fail=" + pcdnFailReason);
                }
            }
            XmPlayPerformanceStatistic.getInstanse().startCdnRequest(track.getDataId(), playUrl);
            hasPlayed = true;
            result = mPlayerControl.initAndPlay(playUrl, start, track);
        } else {
            result = mPlayerControl.initAndNotAutoPlay(playUrl, start, track);
        }
        if (result) {
            // 计算音量增益的值
            double volumeGain1 = getVolumeGainForUrl(track, playUrl);
            boolean openVolumeGain = openVolumeGain();
            Logger.logToFile( "XmPlayerService playTrackInner volumeGain: " + volumeGain + ", " + volumeGain1 + ", openVolumeGain: " + openVolumeGain);
            if (openVolumeGain) {
                if (volumeGain != XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT) {
                    volumeGain1 = volumeGain;
                }
            }
            if (useLocalGain()) {
                int localGain = localVolumeGain();
                volumeGain1 += localGain;
                Logger.logToFile("XmPlayerService playTrackInner localVolumeGain=" + localGain + ",volumeGain1=" + volumeGain1);
            }
            mPlayerControl.setVolumeGain((float) volumeGain1);
        }
        if (!result) {
            mLastModel = null;
        }
        Logger.logToFile("XmPlayerService playTrackInner autoPlay=" + autoPlay + ",result=" + result + ",cost=" + (System.currentTimeMillis() - time)
                + ",track=" + track + ",trace=" + Log.getStackTraceString(new Throwable()));
    }

    private boolean useLocalGain() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_USE_LOCAL_GAIN, false);
    }

    private int localVolumeGain() {
        return MMKVUtil.getInstance().getInt(PreferenceConstantsInOpenSdk.KEY_LOCAL_VOLUME_GAIN_GLOBAL, 0);
    }

    private boolean isUsePcdnInCache() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_USING_PCDN_IN_CACHE, true);
    }

    private boolean openVolumeGain() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KET_OPEN_VOLUME_FOR_BASE_INFO, true);
    }

    private boolean isUploadBaiduUrlNotReplacedTrace() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.UPLOAD_BAIDU_URL_TRACE, true);
    }

    private boolean checkOffset() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_CHECK_AUDIO_START_TIME, false);
    }

    public float getVolumeGain(Track track, String url) {
        if (track == null || TextUtils.isEmpty(url)
                || track.getPlayUrlInfoList() == null || track.getPlayUrlInfoList().size() == 0) {
            return XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT;
        }
        String path = getUrlPath(url);
        if (path == null || path.length() == 0) {
            return XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT;
        }

        List<PlayUrlInfo> urlInfoList = track.getPlayUrlInfoList();
        for (PlayUrlInfo info : urlInfoList) {
            if (info != null && info.getDecodeUrl() != null) {
                if (path.equals(getUrlPath(info.getDecodeUrl()))) {
                    return info.getVolumeGain();
                }
            }
        }
        return XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT;
    }

    public long getContentLen(Track track, String url) {
        if (track == null || TextUtils.isEmpty(url)
                || track.getPlayUrlInfoList() == null || track.getPlayUrlInfoList().size() == 0) {
            return 0;
        }
        String path = getUrlPath(url);
        if (path == null || path.length() == 0) {
            return 0;
        }

        List<PlayUrlInfo> urlInfoList = track.getPlayUrlInfoList();
        for (PlayUrlInfo info : urlInfoList) {
            if (info != null && info.getDecodeUrl() != null) {
                if (path.equals(getUrlPath(info.getDecodeUrl()))) {
                    return info.getFileSize();
                }
            }
        }
        return 0;
    }

    private String getUrlPath(String url) {
        try {
            return Uri.parse(url).getPath();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 信息流音量增益
    private double getVolumeGainForUrl(Track track, String url) {
        if (track == null || TextUtils.isEmpty(url)) {
            return XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT;
        }
        if (!TextUtils.isEmpty(track.getPlayUrl64M4a()) && url.startsWith(track.getPlayUrl64M4a())) {
            return track.getAacv164VolumeGain();
        }
        if (!TextUtils.isEmpty(track.getPlayUrl24M4a()) && url.startsWith(track.getPlayUrl24M4a())) {
            return track.getAacv224VolumeGain();
        }
        return XMediaplayerImpl.VOLUMEN_GAIN_DEFAULT;
    }

    public boolean playPre() {
        return playPre(true);
    }

    public boolean playPre(boolean willPlay) {
        PlayerToListenManager.getInstance().ignorePlayerToListenLogic = true;
        int index = mListControl.getPreIndex();
        Logger.logToFile("PlayCoreLog playPre index : " + index + "   " + Log.getStackTraceString(new Throwable()));
        if (index >= 0) {
            return play(index, willPlay, PlayerConstants.PLAY_METHOD_MANUAL_PRE);
        }
        return false;
    }

    public boolean playNext() {
       return playNext(true);
    }

    public boolean playNext(boolean willPlay) {
        int index = mListControl.getNextIndex(true);
        Logger.logToFile("PlayerToListenManager PlayCoreLog playNext index:" + index + "   " + Log.getStackTraceString(new Throwable()));
        if (index >= 0) {
            if (isPlayingPodCastList()) {
                if (mLastModel instanceof Track &&
                        (((Track) mLastModel).getIsFromToListenTrack() == 1 ||
                                ((Track) mLastModel).getIsLastModelFromToListenTrack() == 1 ||
                                isPlayingPodCastList())
                        && ToListenUtil.isListenComplete(true, getContext(), (Track) mLastModel)) {
                    ToListenUtil.deleteTrackDataById(mLastModel.getDataId());
                    Logger.logToFile("PlayerToListenManager delete last complete1");
                }
                // 播客声音切到待播
                String[] toListenList = ToListenUtil.getMMKVUtil().getAllKeys();
                if (toListenList != null && toListenList.length > 0) {
                    Logger.logToFile("PlayerToListenManager  playNext 播客声音切到待播1");
                    PlayerToListenManager.getInstance().playToListenListAfterPlayNormalTrack(this, true, false, true, true);
                    return true;
                }
            }
            PlayerToListenManager.getInstance().handleOnPlayNext(mListControl.getCurrentPlayableModel());
            return play(index, willPlay, PlayerConstants.PLAY_METHOD_MANUAL_NEXT);
        } else if (isPlayingPodCastList()) {
            if (mLastModel instanceof Track &&
                    (((Track) mLastModel).getIsFromToListenTrack() == 1 ||
                    ((Track) mLastModel).getIsLastModelFromToListenTrack() == 1 ||
                            isPlayingPodCastList())
                    && ToListenUtil.isListenComplete(true, getContext(), (Track) mLastModel)) {
                ToListenUtil.deleteTrackDataById(mLastModel.getDataId());
                Logger.logToFile("PlayerToListenManager delete last complete2");
            }
            // 播客声音切到待播
            String[] toListenList = ToListenUtil.getMMKVUtil().getAllKeys();
            if (toListenList != null && toListenList.length > 0) {
                Logger.logToFile("PlayerToListenManager  playNext 播客声音切到待播2");
                PlayerToListenManager.getInstance().playToListenListAfterPlayNormalTrack(this, true, false, true, true);
                return true;
            }
        } else if (PlayerToListenManager.getInstance().isPlayingToListenTrack() && mOriginalDailyNewsTracks.size() > 0) {
            // 开播普通声音
            Logger.logToFile("PlayerToListenManager  playNext 开播普通声音");
            PlayerToListenManager.getInstance().handleOnPlayNext(mListControl.getCurrentPlayableModel());
            PlayerToListenManager.getInstance().playToListenListAfterPlayNormalTrack(this, false, true, true, false);
            return true;
        }
        return false;
    }

    public boolean playCurrent() {
        int index = mListControl.getCurrIndex();
        if (index >= 0) {
            return play(index);
        }
        return false;
    }

    public boolean pausePlay(boolean shouldStopForeground) {
        return pausePlay(shouldStopForeground, PauseReason.NONE);
    }

    public boolean pausePlay(boolean shouldStopForeground, int reason) {
        Logger.logToFile("PlayCoreLog pausePlay0: reason=" + reason + Log.getStackTraceString(new Throwable()));

        if (shouldStopForeground && XmNotificationCreater.useMediaStyleNotification()) {
            if (!SystemUtil.isNeedUseMediaStyleForHuawei()) {
                stopForeground(false);
                Logger.i(TAG, "stopForegroundService");
            }
        }

        if (mPlayerAudioFocusControl != null) {
            mPlayerAudioFocusControl.resetIsStopAudioByFocus();
        }

        if (mAdsManager.isAdsActive() || mAdsManager.isAdsPlaying()) {

            mAdsManager.pauseAd();
            if (mPlayerStatusListener != null) {
                mPlayerStatusListener.onPlayPause();
            }

            return true;
        }

        if (SoundPatchManager.getInstance().isPlaying()) {
            SoundPatchManager.getInstance().pausePlay();

            if (mPlayerStatusListener != null) {
                mPlayerStatusListener.onPlayPause();
            }

            return true;
        }


        return mPlayerControl.pause(reason);
    }

    public boolean pausePlayWithoutAd(boolean shouldStopForeground, int reason) {
        Logger.logToFile("PlayCoreLog pausePlay0: reason=" + reason + Log.getStackTraceString(new Throwable()));

        if (shouldStopForeground && XmNotificationCreater.useMediaStyleNotification()) {
            if (!SystemUtil.isNeedUseMediaStyleForHuawei()) {
                stopForeground(false);
                Logger.i(TAG, "stopForegroundService");
            }
        }

        if (mPlayerAudioFocusControl != null) {
            mPlayerAudioFocusControl.resetIsStopAudioByFocus();
        }

        if (SoundPatchManager.getInstance().isPlaying()) {
            SoundPatchManager.getInstance().pausePlay();

            if (mPlayerStatusListener != null) {
                mPlayerStatusListener.onPlayPause();
            }

            return true;
        }


        return mPlayerControl.pause(reason);
    }

    @Deprecated
    public boolean stopPlay() {
        return stopPlay(PauseReason.STOP_NONE);
    }

    public boolean stopPlay(int reason) {
        Logger.logToFile("PlayCoreLog stopPlay:" + Log.getStackTraceString(new Throwable()) + ":stopPlay,reason=" + reason);
        if (mPlayerAudioFocusControl != null) {
            mPlayerAudioFocusControl.setAudioFocusAtStopState();
            mPlayerAudioFocusControl.resetIsStopAudioByFocus();
        }
        LivePutIntoHistoryManager.INSTANCE.recordLastPlayingLive(mLastModel);
        mLastModel = null;
        return mPlayerControl.stop(reason);
    }

    public void setTempo(float tempo) {
        if (mPlayerControl != null) {
            mPlayerControl.setTempo(tempo);
        }
        if (mMediaControl != null) {
            mMediaControl.setTempo(tempo);
        }
        XmAdsManager adManager = getAdManager();
        if (adManager != null) {
            adManager.setTempo(tempo);
        }
    }

    public void setQuickListenTempo(float tempo) {
        if (mPlayerControl != null) {
            mPlayerControl.setQuickListenTempo(tempo);
        }
        if (mMediaControl != null) {
            mMediaControl.setTempo(tempo);
        }
    }

    public void requestAudioFocusControl() {
        mPlayerAudioFocusControl.setAudioFocusAtStartState();
    }

    public boolean startPlay(boolean autoPlay) {
        return startPlay(autoPlay, false);
    }

    public boolean startPlay(boolean autoPlay, boolean adPlayed) {
        Logger.logToFile("PlayCoreLog startPlay:" + Log.getStackTraceString(new Throwable()) + ":startPlay");
        mPlayerAudioFocusControl.setAudioFocusAtStartState();

        int playerStatue = mAdsManager.getPlayerStatue();
        boolean adPaused = playerStatue == MiniPlayer.STATE_PAUSED;
        boolean playAd = adPaused || !UserInteractivePlayStatistics.Optimizer_StartPlayWhileAdRequest.enable();

        if (mAdsManager != null && mAdsManager.isAdsActive() && playAd) {
            Logger.logToFile("startPlay 0");

            if (playerStatue == MiniPlayer.STATE_PREPARED || playerStatue == MiniPlayer.STATE_PAUSED) {
                if (mAdsManager.getAdsPlayer() != null) {
                    mAdsManager.playAd(true);
                    Logger.logToFile("startPlay 1");
                    if (mPlayerStatusListener != null)
                        mPlayerStatusListener.onPlayStart();

                    return true;
                }
            } else {
                Logger.logToFile("startPlay 2");
            }
            return false;
        } else if (SoundPatchManager.getInstance().isPlayPaused()) {
            SoundPatchManager.getInstance().startPlay();
            SoundPatchArbitrateManager.getInstance().setSkipHandlerOnPlayStart(true);
            if (mPlayerStatusListener != null) {
                mPlayerStatusListener.onPlayStart();
            }
            return false;
        }

        if (mPlayerControl == null) {
            Logger.logToFile("startPlay 3");
            return false;
        }

        boolean ret = false;
        if (autoPlay && mPlayerControl.getPlayerState() == PlayerConstants.STATE_PREPARING) {
            Logger.logToFile("startPlay 4");
            mPlayerControl.setShouldPlay(true);
        } else {

            if (mListControl != null) {
                int index = mListControl.getCurrIndex();
                PlayableModel playableModel = mListControl.getPlayableModel(index);
                if (playableModel instanceof Track) {
                    Track curPlayTrack = (Track) playableModel;
                    UserInteractivePlayStatistics.playerProcessStartPlay(curPlayTrack.getDataId());
                }
            }

            ret = mPlayerControl.play(true);
            Logger.logToFile("startPlay 5 ret:" + ret);
            // 播放失败，重新播放当前首歌曲
            if (!ret) {
                int index = mListControl.getCurrIndex();
                Logger.logToFile("startPlay 6 index:" + index);
                if (index >= 0) {
                    Logger.logToFile("startPlay 7");
                    ret = play(index, true, PlayerConstants.PLAY_METHOD_MANUAL_PLAY, adPlayed);
                }
            }
        }
        return ret;
    }

    public void openHarmonyOsNotification() {
        if (mMediaControl != null) {
            mMediaControl.openHarmonyOsNotification(mAppCtx);
        }
    }

    public boolean willNotPlay() {
        return !mPlayerControl.shouldPlay();
    }

    public boolean startPlay() {
        return startPlay(false);
    }

    public boolean playByTrackId(long trackId) {
        if (mListControl == null) {
            return false;
        }
        int index = mListControl.getTrackIndex(trackId);
        return play(index);
    }

    public boolean play(int index) {
        return play(index, true);
    }

    public boolean play(int index, boolean autoPlay) {
        Logger.logToFile("play index & autoPlay: " + index + ", " + autoPlay);
        return play(index, autoPlay, PlayerConstants.PLAY_METHOD_MANUAL_PLAY);
    }

    @Deprecated
    public boolean playRadio(Radio radio) {
        mPlayerAudioFocusControl.setAudioFocusAtStartState();
        if (radio == null) {
            return false;
        }
        try {
            if (!radio.equals(mLastModel)) {
                mPlayerControl.stop(PauseReason.StopBusiness.RadioStop);
                mListControl.setRadio(radio);
                mPlayerStatusListener.onSoundSwitch(mLastModel, radio);
                mPlayerControl.initAndPlay(TrackUrlChooseManager.getInstance().getRadioUrl(radio), 0, radio);
                mLastModel = radio;
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private boolean play(int index, boolean autoPlay, final int playMethod) {
        return play(index, autoPlay, playMethod, false);
    }

    private boolean play(int index, boolean autoPlay, final int playMethod, boolean adPlayed) {
        Logger.logToFile("PlayCoreLog play 0:  autoPlay: " + autoPlay + ", index=" + index + ", playMethod: " + playMethod
                + ", adPlayed: " + adPlayed + ", " + Log.getStackTraceString(new Throwable()));

        notificationCancled = false;
        startForegroundService();

        if (index >= 0 && index < mListControl.getCurrListSize()) {
            Track tempPlayTrack = (Track) mListControl.getPlayableModel(index);
            if (tempPlayTrack != null && !TextUtils.isEmpty(tempPlayTrack.getKind()) &&
                    PlayableModel.KIND_IGNORE_DATA.equals(tempPlayTrack.getKind())) {
                // 播放到指定类型的声音时，直接跳过
                int lastIndex = mListControl.getPlayList().indexOf(mLastModel);
                if (lastIndex > index) {
                    if (index - 1 >= 0) {
                        index--;
                    } else {
                        return false;
                    }
                } else {
                    if (index + 1 < mListControl.getCurrListSize()) {
                        index++;
                    } else {
                        return false;
                    }
                }
            }
            Track curPlayTrack = (Track) mListControl.getPlayableModel(index);
            mListControl.setCurrIndex(index);

            if (curPlayTrack == null) {
                Logger.logToFile(TAG + "Get current model return null, play fail");
                    mLastModel = null;
                Logger.logToFile("play 1:");
                    IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                    if (xdcsPost != null) {
                        xdcsPost.statErrorToXDCS("playFragmentBlack", "play(int index):_____curPlayTrack == null____"
                                +index +"_____"+mListControl.getCurrListSize() + "___"+Log.getStackTraceString(new Throwable()));
                    }
                    return false;
                }

                // 如果不是自动播放并且播放的类型不是track 且播放的不是chatxmly的声音 不进行预缓存声音
                if (!autoPlay && !PlayableModel.KIND_TRACK.equals(curPlayTrack.getKind()) && !curPlayTrack.isKindOfChatXmly()) {
                    return false;
                }

                int indexOfLastModel = mListControl.getPlayList().indexOf(mLastModel);
                PlayableModel lastModelNew = null;
                if (indexOfLastModel >= 0) {
                    lastModelNew = mListControl.getPlayList().get(indexOfLastModel);
                }
                if (lastModelNew instanceof Track && mLastModel instanceof Track) {
                    ((Track) lastModelNew).setIsLastModelFromToListenTrack(((Track) mLastModel).getIsFromToListenTrack());
                }
                if (lastModelNew == null) {
                    lastModelNew = mLastModel;
                }
                if (lastModelNew instanceof Track && ((Track) lastModelNew).getAlbum() != null) {
                    Logger.log("Test statistic track" + ((Track) lastModelNew).getAlbum().getRecSrc());
                }

                boolean sourceIsChange = mLastModel == null || !curPlayTrack.equals(mLastModel);
                boolean isChatXmlyPlayUrlChange = false;
                if (!sourceIsChange && curPlayTrack.isKindOfChatXmly() && mLastModel instanceof Track && mLastModel.isKindOfChatXmly()) {
                    isChatXmlyPlayUrlChange = curPlayTrack.getPlayUrl64() != null
                            && !curPlayTrack.getPlayUrl64().equals(((Track) mLastModel).getPlayUrl64());
                }
                final boolean finnalIsChatXmlyPlayUrlChange = isChatXmlyPlayUrlChange;

                Logger.logToFile("play 1-1: " + (mLastModel == null) + ", " + (!curPlayTrack.equals(mLastModel))
                        + (mLastModel == null ? 0 : mLastModel.getDataId()) + ", " + curPlayTrack.getDataId() + ", index=" + index);

//                if (autoPlay) {
//                    XmPlayPerformanceStatistic.getInstanse().startPlay(curPlayTrack.getDataId(),
//                            XmPlayListControl.isLiveSource(curPlayTrack) ? 4 : XmPlayListControl.getPlaySourceByTrack(curPlayTrack),
//                            !curPlayTrack.isPaid());
//                }

                if (PlayerToListenManager.getInstance().isPlayingToListenTrack() && index > 0) {
                    Logger.logToFile("PlayerToListenManager play handlePodCastIndex");
                    mListControl.handlePodCastIndex();
                }
                mCurrPlayMethodFixTrackSoundSwitch=playMethod;
                PlayableModel finalLastModelNew = lastModelNew;
                final AdActionModel actionModel = new AdActionModel();
                actionModel.beginPlay(curPlayTrack);
                Runnable runnable = new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (mLastModel == null || !curPlayTrack.equals(mLastModel)
                                    || curPlayTrack.isAuthorized() != ((Track) mLastModel).isAuthorized() || finnalIsChatXmlyPlayUrlChange) {
                                mPlayerControl.pause(false, PauseReason.Common.ON_SOUND_SWITCH);
                                long beginTime = System.currentTimeMillis();
                                mPlayerStatusListener.onSoundSwitch(finalLastModelNew, curPlayTrack);
                                actionModel.onSoundSwitch(System.currentTimeMillis() - beginTime);
                                if (autoPlay) {
                                    UserInteractivePlayStatistics.playerProcessStartPlay(curPlayTrack.getDataId());

                                    XmPlayPerformanceStatistic.getInstanse().startPlay(curPlayTrack.getDataId(),
                                            XmPlayListControl.getPlayType(curPlayTrack), !curPlayTrack.isPayTrack(),
                                            TrackUrlChooseManager.getInstance().getTrackUrl(curPlayTrack, false));
                                }
                                mLastModel = curPlayTrack;
                                Logger.logToFile("play 2:");
                                playTrackPrivate(curPlayTrack, autoPlay, playMethod, sourceIsChange, adPlayed, actionModel);
                            } else {
                                long beginTime = System.currentTimeMillis();
                                if (mLastModel != null
                                        && curPlayTrack.getAlbum() != null
                                        && ((Track) mLastModel).getAlbum() != null
                                        && curPlayTrack.getAlbum().getAlbumId() != ((Track) mLastModel).getAlbum().getAlbumId()) {
                                    mPlayerStatusListener.onSoundSwitch(finalLastModelNew, curPlayTrack);
                                } else if (mLastModel != null && curPlayTrack.equals(mLastModel)) {
                                    Logger.logToFile("PlayerToListenManager no onSoundSwitch force handleOnSoundSwitch");
                                    PlayerToListenManager.getInstance().ignorePlayerToListenLogic = false;
                                }
                                actionModel.onSoundSwitch(System.currentTimeMillis() - beginTime);

                                if (autoPlay) {

                                    UserInteractivePlayStatistics.playerProcessStartPlay(curPlayTrack.getDataId());

                                    XmPlayPerformanceStatistic.getInstanse().startPlay(curPlayTrack.getDataId(),
                                            XmPlayListControl.getPlayType(curPlayTrack), !curPlayTrack.isPayTrack(),
                                            TrackUrlChooseManager.getInstance().getTrackUrl(curPlayTrack, false));
                                }
                                Logger.logToFile("play 3:");
                                mLastModel = curPlayTrack;
                                if (autoPlay) {
                                    if (mAdsManager.isAdsActive()) {
                                        int playerStatue = mAdsManager.getPlayerStatue();
                                        if (playerStatue == MiniPlayer.STATE_PREPARED || playerStatue == MiniPlayer.STATE_PAUSED) {
                                            mAdsManager.playAd(true);
                                            Logger.logToFile("play 3:1");
                                            return;
                                        } else if (playerStatue == MiniPlayer.STATE_STARTED) {
                                            Logger.logToFile("play 3:2");
                                            return;
                                        }

                                        Logger.logToFile("play 4:");
                                    }
                                } else {
                                    Logger.logToFile("play 5:");
                                }
                                boolean ret = autoPlay && mPlayerControl.play(true);
                                if (!ret) {
                                    ret = playTrackPrivate(mLastModel, autoPlay, playMethod, sourceIsChange, adPlayed, actionModel);
                                } else {
                                    if (autoPlay) {
                                        actionModel.endAdPlay(null);
                                    }
                                    // 这里的track可能会被主进程的声音替换，导致track的信息不完整
                                    // 走到这里说明autoPlay=true
                                    if (curPlayTrack != null && !curPlayTrack.isUpdateStatusNoCheck() && checkCanUpdate() && !curPlayTrack.isKindOfLive() && !curPlayTrack.isKindOfChatXmly()
                                            && !curPlayTrack.isKindOfAiAgentRadio()) {
                                        // 这里需要更新下baseInfo
                                        TrackInfoDataManager.getInstance().getTrackInfo(curPlayTrack, null);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            mLastModel = null;
                            CdnUtil.statToXDCSError(CdnConstants.PLAY_INFO, "play(0):" + Log.getStackTraceString(e));
                            if (ConstantsOpenSdk.isDebug) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                };

                if (isFromXmPlayerManagerPlay && curPlayTrack.equals(mLastModel)) {
                    ContainPlayAdManager.getInstance().onStartPlayCheckCanPlayAd(XmPlayerService.getPlayerSrvice(), actionModel, new ContainPlayAdManager.IContainPlayCallBack() {
                        @Override
                        public void startPlay() {
                            actionModel.containAdPlay();
                            runnable.run();
                        }
                    });
                } else {
                    runnable.run();
                }
                return true;
        } else {
            Logger.logToFile(TAG + "Index Out Of Bound, index:" + index + ", total:" + mListControl.getCurrListSize());
            mLastModel = null;
        }
        return false;
    }

    public boolean isPlayingPodCastList() {
        try {
            if (mListControl != null && mListControl.getParams() != null && mListControl.getParams().get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM) != null) {
                String playListModeType = mListControl.getParams().get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM);
                return !TextUtils.isEmpty(playListModeType) && DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN.equals(playListModeType);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isPodCastListPlaySortUpMode() {
        // 播客是否向上播  默认都是向下播
        try {
            if (mListControl != null && mListControl.getParams() != null && mListControl.getParams().get(DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT) != null) {
                String playListSortMode = mListControl.getParams().get(DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT);
                return !TextUtils.isEmpty(playListSortMode) && DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT_UP.equals(playListSortMode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    private void updatePodCastListPlaySortUpMode(boolean sortUp) {
        try {
            if (mListControl != null && mListControl.getOriginParams() != null) {
                mListControl.getOriginParams().put(DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT,
                        sortUp ? DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT_UP : DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT_DOWN);
                mListControl.permutePlayList();
                updateNotification();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean checkCanUpdate() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_NEED_CHECK_REQUEST_BASE_INFO, true);
    }

    public void setPlayList(Map<String, String> param, List<Track> list) {
        mListControl.setPlayList(param, list);
        PlayerToListenManager.getInstance().mContinueToPlayToListenTrack = false;
    }

    public int getPlayListSize() {
        List<Track> list = mListControl.getPlayList();
        return list == null ? 0 : list.size();
    }

    public boolean isLossAudioFocus = false;

    public boolean isLossAudioFocus() {
        synchronized (XmPlayerService.class) {
            return isLossAudioFocus;
        }
    }

    public void setLossAudioFocus(boolean is) {
        synchronized (XmPlayerService.class) {
            isLossAudioFocus = is;
        }
    }

    private int mCurrPlayMethod = PlayerConstants.PLAY_METHOD_AUTO_SWITCH;

    //fix soundSwitch无法获取到本次切换状态，边听边看使用
    private int mCurrPlayMethodFixTrackSoundSwitch = PlayerConstants.PLAY_METHOD_AUTO_SWITCH;

    private boolean playTrackPrivate(PlayableModel model, boolean autoPlay,
                                     int playMethod, boolean sourceIsChange, boolean adPlayed, AdActionModel actionModel) throws Exception {
        Logger.logToFile("playTrackPrivate : " + TrackUtil.trackToStr(model) + ",autoPlay=" + autoPlay + ",playMethod=" + playMethod + ",adPlayed=" + adPlayed);
        mCurrPlayMethod = playMethod;
        mCurrPlayMethodFixTrackSoundSwitch=playMethod;
        setLossAudioFocus(false);
        if (autoPlay) {
            mPlayerAudioFocusControl.setAudioFocusAtStartState();
        }

        if (Logger.isDebug) {
            Logger.logToFile("playTrackPrivate : " + Log.getStackTraceString(new Throwable()));
        }

        if (mMediaControl != null) { //直播不受耳机线控逻辑处理
            mMediaControl.setIsWireControl(!(model != null && model.isKindOfLive()));
        }

        Logger.logToFile("play 6_0 mPlayerControl.resetMediaPlayer:");
        mPlayerControl.resetMediaPlayerToIdle();
        if (model instanceof Track) {
            final Track sound = (Track) model;
            if (autoPlay) {
                Logger.logToFile("play 6: " + TrackUtil.trackToStr(sound));
                PlayAdsCallback playAdsCallback = new PlayAdsCallback() {

                    @Override
                    public void adPlay(int curPos, int duration) {
                        Logger.logToFile("play 6-1 adPlay");
                        if (actionModel != null) {
                            actionModel.adPlayed(duration, curPos);
                        }
                    }

                    @Override
                    public void onFinish(boolean result) {
                        String trace = Log.getStackTraceString(new Throwable());
                        Logger.logToFile("play 7: " + result + "    " + isLossAudioFocus() + ",trace=" + trace); // 音贴完播或有声视频完播结束时播放专辑声音

                        UserInteractivePlayStatistics.stepEnd(sound.getDataId(), "XmAdsManager_playAds");

                        if (sSoundPatchAdStateListener != null) {
                            sSoundPatchAdStateListener.onAdEnd();
                        }
                        if (actionModel != null) {
                            actionModel.endAdPlay(trace);
                        }
                        try {
                            if (isLossAudioFocus()) {
                                setLossAudioFocus(false);
                                playTrack(sound, result);
                            } else {
                                playTrack(sound, result);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            mLastModel = null;
                            Logger.logToFile("play 8:");
                            CdnUtil.statToXDCSError(CdnConstants.PLAY_INFO, "playAdsCallback:" + e.toString());
                        }
                    }
                };
                boolean isFeedPlayList = false;
                if (mListControl != null && mListControl.getParams() != null &&
                        mListControl.getParams().get(ConstantsOpenSdk.KEY_PLAY_PAGE_TYPE) != null) {
                    String playPageType = mListControl.getParams().get(ConstantsOpenSdk.KEY_PLAY_PAGE_TYPE);
                    if (!TextUtils.isEmpty(playPageType)) {
                        try {
                            if (Integer.parseInt(playPageType) == ConstantsOpenSdk.PLAY_PAGE_TYPE_FEED) {
                                // 流播放页的声音不加广告
                                isFeedPlayList = true;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                if (sound.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_FEED_TRACK) {
                    isFeedPlayList = true;
                }
                boolean isDailyNewsTrack = sound.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY ||
                        sound.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4;
                if (isDLNAState
                        || PlayableModel.KIND_RADIO.equals(model.getKind())
                        || Track.TYPE_DUBSHOW == sound.getType()
                        || PlayableModel.KIND_LIVE_FLV.equals(model.getKind())
                        || PlayableModel.KIND_LIVE_COURSE.equals(model.getKind())
                        || PlayableModel.KIND_ENT_FLY.equals(model.getKind())
                        || PlayableModel.KIND_MYCLUB_FLV.equals(model.getKind())
                        || (mAdsManager != null && mAdsManager.isChannelJumpOver())
                        || (!sourceIsChange && !PlayableModel.KIND_SCHEDULE.equals(model.getKind()))
                        || ((sound.getTrackFrom() == 1 || sound.getTrackFrom() == 2 || sound.getTrackFrom() == 4) && MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_ITEM_TRACK_XIMA_TEN_REMOVE_AD, true))
                        || adPlayed
                        || isDailyNewsTrack
                        || isFeedPlayList
                        || isAdFree(sound)
                        || (SoundPatchConstants.isRemoteServiceConnected && MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_GAODE_REMOVE_AD, true))
                ) {
                    if (mAdsManager != null) {
                        mAdsManager.resetChannelJumpOverState();
                        mAdsManager.stopCurrentAdPlay(false);
                        mAdsManager.onPlayAdsInterrupt();
                    }
                    Logger.logToFile("play 11:");
                    playAdsCallback.onFinish(true);
                } else {
                    // 防止从专辑页进入播放页之后再次请求广告
                    if (System.currentTimeMillis() - lastRequestTime
                            > MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_SOUND_PATCH_REQUEST_TIME, 1000)) {
                        Logger.logToFile("play 12: " + playAdsCallback);
                        UserInteractivePlayStatistics.stepBegin("XmAdsManager_playAds");

                        if (UserInteractivePlayStatistics.Optimizer_StartPlayWhileAdRequest.enable()) {
                            UserInteractivePlayStatistics.logOpt("Optimizer_StartPlayWhileAdRequest 2 >>>");
                            playAdsCallback.onFinish(true);
                            playAdsCallback = new PlayAdsCallback() {

                                @Override
                                public void onFinish(boolean result) {
                                    try {
                                        boolean playing = isPlayingTrack();
                                        if (playing) {
                                            UserInteractivePlayStatistics.loge("ad finish in service, isPlayingTrack");
                                            return;
                                        }
                                        PlayableModel playableModel = getCurrPlayModel();
                                        if (playableModel != null && sound != null && playableModel.getDataId() == sound.getDataId()) {
                                            boolean isPrepared = mPlayerControl.getPlayerState() == PlayerConstants.STATE_PREPARED;
                                            Pair<Track, long[]> pair = mPair;
                                            boolean isError = pair != null && pair.first != null && pair.second != null && pair.second.length == 2
                                                    && playableModel.getDataId() == pair.first.getDataId()
                                                    && (pair.second[1] == 1 || pair.second[1] == 2) && Math.abs(pair.second[0] - System.currentTimeMillis()) < 500;
                                            boolean directPlayWhilePrepared = UserInteractivePlayStatistics.directPlayWhilePrepared();
                                            UserInteractivePlayStatistics.loge("ad finish, playing: " + playing
                                                    + ", isPrepared:" + isPrepared + ",isError=" + isError + ", directPlayWhilePrepared: " + directPlayWhilePrepared);

                                            if (isError && directPlayWhilePrepared) {
                                                return;
                                            }

                                            if (isPrepared && directPlayWhilePrepared) {
                                                UserInteractivePlayStatistics.loge("ad finish, startPlay >>>");
                                                startPlay(true);
                                                return;
                                            }
                                        }

                                        if (isLossAudioFocus()) {
                                            setLossAudioFocus(false);
                                            playTrack(sound, result);
                                        } else {
                                            playTrack(sound, result);
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        mLastModel = null;
                                        Logger.logToFile("play 8:");
                                        CdnUtil.statToXDCSError(CdnConstants.PLAY_INFO, "playAdsCallback:" + e.toString());
                                    }
                                }

                                @Override
                                public void adPlay(int curPos, int duration) {}
                            };
                        }

                        mAdsManager.playAds(sound, playMethod, playAdsCallback, false, false, false);
                    } else {
                        playAdsCallback.onFinish(true);
                    }
                    lastRequestTime = System.currentTimeMillis();
                }
            } else {
                Logger.logToFile("play 9:");
                try {
                    playTrack(sound, false);
                } catch (Exception e) {
                    Logger.logToFile("play 10:");
                    mLastModel = null;
                    e.printStackTrace();
                    CdnUtil.statToXDCSError(CdnConstants.PLAY_INFO, "playTrack:" + e.toString());
                }
            }
            return true;
        } else if (model instanceof Radio) {
            Radio radio = (Radio) model;
            mPlayerControl.init(TrackUrlChooseManager.getInstance().getRadioUrl(radio), 0, radio);
            return true;
        }
        return false;
    }

    public XmAdsManager getAdManager() {
        return mAdsManager;
    }

    public long updateGDTRTBToken() {
        if (mIXmCommonBusinessDispatcher != null) {
            try {
               return mIXmCommonBusinessDispatcher.updateGDTRTBToken();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public long updateCSJRtbToken(int thirdAdType, String dspId, int adType) {
        if (mIXmCommonBusinessDispatcher != null) {
            try {
                return mIXmCommonBusinessDispatcher.updateCSJRTBToken(thirdAdType, dspId, adType);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        return 0;
    }

    @Nullable
    public String syncBatchGetToken(List<AdRtbItemModel> itemModelList, int thirdAdType) {
        if (mIXmCommonBusinessDispatcher != null) {
            try {
                return mIXmCommonBusinessDispatcher.syncBatchGetToken(itemModelList, thirdAdType);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    @Nullable
    public String syncBatchGetGdtSdkInfo(List<AdRtbItemModel> itemModelList) {
        if (mIXmCommonBusinessDispatcher != null) {
            try {
                return mIXmCommonBusinessDispatcher.syncBatchGetGdtSdkInfo(itemModelList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    class XmPlayerImpl extends IXmPlayer.Stub {
        public XmPlayerImpl() {
            MixPlayerService.getMixService().onCreateService(getApplicationContext());
            MixPlayerService.getMixService().addPlayerStatusListener(mixPlayerStatusListener);
        }

        @Override
        public void updateXmPlayResource(String xmPlayResource) throws RemoteException {
            XmStatisticsManager.getInstance().xmCurPlayResource = xmPlayResource;
        }

        @Override
        public void updateXmResourceParams(Map params) throws RemoteException {
            XmStatisticsManager.getInstance().mXmResourceMap = params;
        }

        @Override
        public void setTempo(float tempo) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().setTempo(tempo);
        }

        @Override
        public void setQuickListenTempo(float tempo) throws RemoteException {
            QuickListenForPlayProcessUtil.saveQuickListenTempo(tempo);
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().setQuickListenTempo(tempo);
        }

        @Override
        public void stop(int reason) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().stopPlay(reason);
        }

        @Override
        public void stopPlay() throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().stopPlay(PauseReason.STOP_NONE);
        }

        @Override
        public void resetPodCastListMode(boolean newPodCastListMode) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            mListControl.resetPodCastListMode(newPodCastListMode);
        }

        @Override
        public boolean isPodCastListPlaySortUp() throws RemoteException {
            if (getPlayerSrvice() == null)
                return false;
            return isPodCastListPlaySortUpMode();
        }

        @Override
        public void updatePodCastListPlaySortUpCurrent(boolean sortUp) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            updatePodCastListPlaySortUpMode(sortUp);
        }

        @Override
        public void playPre(boolean willPlay) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().playPre(willPlay);
        }

        @Override
        public void playNext(boolean willPlay) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().playNext(willPlay);
        }

        @Override
        public void setPlayIndex(int index) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            boolean play = getPlayerSrvice().play(index, false);
            // 因为只会在playstart的时候才会保存播放列表, 所以这里也进行一次保存
            if (mHistoryManager != null) {
                mHistoryManager.savePlayList();
            }
        }

        @Override
        public void play(int index) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            isFromXmPlayerManagerPlay = true;
            getPlayerSrvice().play(index);
            isFromXmPlayerManagerPlay = false;

            if (mService != null) {
                StartServiceTimeCollectUtil.uploadCollectData(mService.getApplicationContext());
            }
        }

        @Override
        public void playByTrackId(long trackId) throws RemoteException {
            if (getPlayerSrvice() == null) {
                return;
            }
            ignorePlayerToListenLogic(true);
            isFromXmPlayerManagerPlay = true;
            getPlayerSrvice().playByTrackId(trackId);
            isFromXmPlayerManagerPlay = false;

            if (mService != null) {
                StartServiceTimeCollectUtil.uploadCollectData(mService.getApplicationContext());
            }
        }

        @Override
        public void pause(int reason) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().pausePlay(true, reason);
        }

        @Override
        public void pausePlay() throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().pausePlay(true, PauseReason.NONE);
        }

        @Override
        public void pausePlayAndCloseNotification() throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            if (MixPlayerService.getMixService().isMixPlaying()) {
                MixPlayerService.getMixService().pause();
                return;
            }
            getPlayerSrvice().pausePlay(true, PauseReason.Common.PAUSE_AND_CLOSE_NOTIFICATION);
            getTimeHander().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (getPlayerSrvice() != null && !getPlayerSrvice().isPlaying()) {
                        getPlayerSrvice().closeNotification();
                        getPlayerSrvice().removeScreenChangeBroadCast();
                    }
                }
            }, 200);
        }

        @Override
        public void reShowClosedNotification() throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().reShowClosedNotification();
        }

        @Override
        public void startPlay() throws RemoteException {
            XmPlayerService playerSrvice = getPlayerSrvice();
            if (playerSrvice == null) {
                return;
            }

            // 广告正在活动中
            if (mAdsManager != null && mAdsManager.isAdsActive()) {
                playerSrvice.startPlay();
            } else {
                ContainPlayAdManager.getInstance().onStartPlayCheckCanPlayAd(getPlayerSrvice(), null,
                        new ContainPlayAdManager.IContainPlayCallBack() {
                            @Override
                            public void startPlay() {
                                XmPlayerService playerSrvice = getPlayerSrvice();
                                if (playerSrvice != null && !playerSrvice.isPlaying()) {
                                    playerSrvice.startPlay(false, true);
                                }
                            }
                        });
            }

        }

        @Override
        public void registePlayerListener(IXmPlayerEventDispatcher l)
                throws RemoteException {
            Logger.i(TAG, "Process " + Binder.getCallingPid()
                    + "has register PlayerListener");
            SendPlayStaticManager.getInstance().registePlayerListener(l);
        }

        @Override
        public void unregistePlayerListener(IXmPlayerEventDispatcher l)
                throws RemoteException {
            SendPlayStaticManager.getInstance().unregistePlayerListener(l);
        }

        @Override
        public void setPlayMode(String mode) throws RemoteException {
            if (mListControl != null) {
                if (PlayerToListenManager.getInstance().isPlayingToListenTrack()) {
                    return;
                }
                mListControl.setPlayMode(PlayMode.valueOf(mode));
            }

            if (mPlayModeChangeListener != null) {
                mPlayModeChangeListener.onPlayModeChange();
            }
        }

        @Override
        public String getPlayMode() throws RemoteException {
            if (mListControl != null) {
                return mListControl.getPlayMode().toString();
            } else {
                return "";
            }
        }

        @SuppressWarnings("unchecked")
        @Override
        public void setPlayList(@SuppressWarnings("rawtypes") Map param,
                                List<Track> list) throws RemoteException {
            if (getPlayerSrvice() == null)
                return;
            getPlayerSrvice().setPlayList(param, list);
        }

        @Override
        public void addPlayList(List<Track> list) throws RemoteException {
            if (mListControl != null) {
                mListControl.addPlayList(list);
            }
        }

        @Override
        public int getPlayerStatus() throws RemoteException {
            if (mAdsManager != null && mPlayerControl != null) {
                if (mAdsManager.isAdsActive()) {
                    boolean isAdPlay = false;
                    if (mAdsManager != null) {
                        isAdPlay = mAdsManager.isAdsPlaying();
                    }

                    return isAdPlay ? PlayerConstants.STATE_STARTED : PlayerConstants.STATE_PAUSED;
                }
                return mPlayerControl.getPlayerState();
            } else {
                return PlayerConstants.STATE_ERROR;
            }
        }

        @Override
        public int getCurrIndex() throws RemoteException {
            if (mListControl != null) {
                return mListControl.getCurrIndex();
            } else {
                return -1;
            }
        }

        @Override
        public Track getTrack(int index) throws RemoteException {
            return (Track) mListControl.getPlayableModel(index);
        }

        @Override
        public Track getNextTrack() throws RemoteException {
            return (Track) mListControl.getPlayableModel(mListControl.getCurrIndex() + 1);
        }

        @Override
        public Track getPreTrack() throws RemoteException {
            return (Track) mListControl.getPlayableModel(mListControl.getCurrIndex() - 1);
        }

        @Override
        public int getLimitTime() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                return adManager.getFreeTime(XmPlayerService.this);
            }
            return 0;
        }

        @Override
        public void setLimitTime(int limitTime) throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.setLimitTime(XmPlayerService.this, limitTime);
            }
        }

        @Override
        public void registerFreeListenTimeListener(IXmPlayerFreeListenTimeListener listener) {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.registerFreeListenTimeListener(listener);
            }
        }

        @Override
        public int getListenTime() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                return adManager.getListenTime(XmPlayerService.this);
            }
            return 0;
        }

        @Override
        public void addListenTime(int remainTime, int addedTime) throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.addListenTime(XmPlayerService.this, remainTime, addedTime);
            }
        }

        @Override
        public void clearAllListenTime() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.clearAllListenTime();
            }
        }

        @Override
        public void registerListenTimeListener(IXmPlayerListenTimeListener listener) throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.registerListenTimeListener(listener);
            }
        }

        @Override
        public int getTotalListenTime() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                return adManager.getTotalListenTime(XmPlayerService.this);
            }
            return 0;
        }

        @Override
        public void syncTimeToServer(ISyncTimeCallback callback) throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.syncTimeToServer(XmPlayerService.this, callback);
            } else if (callback != null) {
                callback.onSyncFailed();
            }
        }

        @Override
        public boolean isNoNeedExposureAigcById(long trackId) throws RemoteException {
            return AIgcAdExposureFrequencyHelper.getInstance().isNoNeedExposure(trackId);
        }

        @Override
        public List<Advertis> getForwardAd() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                return adManager.getForwardAd();
            }

            return null;
        }

        @Override
        public AdvertisList getCurSoundAdList() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                return adManager.getCurSoundAdList();
            }
            return null;
        }

        @Override
        public Advertis getCurSoundAd() throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                return adManager.getCurrAdvertis();
            }
            return null;
        }

        @Override
        public void setCurAdSoundType(int soundType){
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                Advertis advertis = adManager.getCurrAdvertis();
                if (advertis != null) {
                    advertis.setSoundType(soundType);
                }
            }
        }

        @Override
        public void setCurAdHasDoneRtb(long responseId) throws RemoteException {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                AdvertisList adList = adManager.getCurSoundAdList();
                if (adList != null && adList.getResponseId() == responseId) {
                    List<Advertis> advertisList = adList.getAdvertisList();
                    if (advertisList != null && !advertisList.isEmpty()){
                        for (Advertis advertis : advertisList) {
                            if (advertis != null){
                                advertis.setDoneRtb(true);
                            }
                        }
                    }
                }
            }
        }


        @Override
        public void setElderlyMode(boolean elderlyMode) throws RemoteException {
            mIsElderlyMode = elderlyMode;
        }

        @Override
        public void changeNotificationStyle(int style) {
            if (getPlayerSrvice() == null) {
                return;
            }
            notificationCancled = false;
            getPlayerSrvice().changeNotificationStyle(style);
        }

        @Override
        public void updateDecoderState() throws RemoteException {
            if(mPlayerControl != null) {
                mPlayerControl.updateDecoderState();
            }
        }

        @Override
        public void onElderlyModelChange() throws RemoteException {
            CrossProcessTransferValueManager.elderlyModeChange = true;
        }

        @Override
        public void onAccessibilityModeChange() throws RemoteException {
            CrossProcessTransferValueManager.accessibilityModeChange = true;
        }

        @Override
        public void onVideoAdShow(boolean hasVideoCache) throws RemoteException {
            PlayableModel currPlayModel = getCurrPlayModel();
            if (currPlayModel != null) {
                XmPlayPerformanceStatistic.getInstanse().endAdRequest(currPlayModel.getDataId(), hasVideoCache);

                XmPlayPerformanceStatistic.getInstanse().startAdShow(currPlayModel.getDataId());
            }
        }

        @Override
        public boolean canSoundAdDuringRequest(boolean isPaused) throws RemoteException {
            return canRequestAd(isPaused);
        }

        @Override
        public boolean isCurTrackNeedPauseAtDesPos() throws RemoteException {
            PlayableModel curr = mListControl.getCurrentPlayableModel();
            if (curr instanceof Track) {
                return ((Track) curr).isNeedPauseAtDesPos();
            }
            return false;
        }


        @Override
        public void initXiMaPipePush() throws RemoteException {
            PushNotificationFilterManager.getInstance().init(mAppCtx);
        }

        @Override
        public void setCurTrackNeedPauseAtDesPos(boolean isNeedPauseAtDesPos, int endPlayPos) throws RemoteException {
            PlayableModel curr = mListControl.getCurrentPlayableModel();
            if (curr instanceof Track) {
                Track track = (Track) curr;
                track.setNeedPauseAtDesPos(isNeedPauseAtDesPos);
                track.setEndPlayPos(endPlayPos);
            }
        }

        @Override
        public boolean isCurTrackNeedLoadPlayCompleteData() throws RemoteException {
            PlayableModel curr = mListControl.getCurrentPlayableModel();
            if (curr instanceof Track) {
                return ((Track) curr).isNeedLoadPlayCompleteData();
            }
            return true;
        }

        @Override
        public void preDownloadFile(String url) throws RemoteException {
            if (mAdsManager != null && !TextUtils.isEmpty(url)) {
                mAdsManager.onlyDownloadFile(url);
            }
        }

        @Override
        public void setPlayScene(int playScene) throws RemoteException {
            mPlayScene = playScene;
        }

        @Override
        public int getPlayScene() throws RemoteException {
            return mPlayScene;
        }

        @Override
        public void useStatusChange(boolean hasLogin, boolean isVip) throws RemoteException {
            if (mUseStatusChangeCallBacks != null && mUseStatusChangeCallBacks.size() > 0) {
                for (IUseStatusChangeCallBackForPlayProcess userStatusChange : mUseStatusChangeCallBacks) {
                    userStatusChange.useStatusChange(hasLogin, isVip);
                }
            }
        }


        @Override
        public Track getCurTrack() throws RemoteException {
            if (mListControl == null)
                return null;
            return (Track) mListControl.getPlayableModel(mListControl.getCurrIndex());
        }

        @Override
        public Track getRequestTrackModel() throws RemoteException {
            return TrackInfoDataManager.getInstance().getCachedTrack();
        }

        @Override
        public void beforePlayToListen(boolean deleteCurrentNormalTrack) throws RemoteException {
            saveOriginPlayListBeforeToListen(deleteCurrentNormalTrack);
        }

        @Override
        public void ignorePlayerToListenLogic(boolean ignore) throws RemoteException {
            PlayerToListenManager.getInstance().ignorePlayerToListenLogic = ignore;
        }

        @Override
        public void continueToPlayToListenTrack(boolean continueToPlayToListenTrack) throws RemoteException {
            PlayerToListenManager.getInstance().mContinueToPlayToListenTrack = continueToPlayToListenTrack;
        }

        @Override
        public boolean getContinueToPlayToListenTrack() throws RemoteException {
            return PlayerToListenManager.getInstance().mContinueToPlayToListenTrack;
        }

        @Override
        public int getFirstFromPushGuard() throws RemoteException {
            return PushGuardPlayerManager.getInstance().mIsFirstFromPushGuard;
        }

        @Override
        public int getTrackSkipTailFromCache(long albumId) throws RemoteException {
            return SkipHeadTailManager.getInstance().getTail(albumId);
        }

        @Override
        public int getTrackIndex(long trackId) throws RemoteException {
            if (mListControl != null) {
                return mListControl.getTrackIndex(trackId);
            }
            return -1;
        }

        @Override
        public void setAutoPlayAfterGetPlayUrl(boolean autoPlay) {
            isAutoPlayAfterGetPlayUrl = autoPlay;
        }

        @Override
        public int getPlaySourceType() throws RemoteException {
            return mListControl.getPlaySource();
        }

        @Override
        public Radio getRadio() throws RemoteException {
            return mListControl.getRadio();
        }

        @Override
        public void seekTo(int ms) throws RemoteException {
            if (mAdsManager.isAdsActive()) {
                return;
            }
            int mPlaySource = mListControl.getPlaySource();
            if (mPlaySource == XmPlayListControl.PLAY_SOURCE_RADIO/*
             * ||
             * mPlaySource
             * ==
             * XmPlayListControl
             * .
             * PLAY_SOURCE_PROGRAM
             */) {
                return;
            }
            mPlayerControl.seekTo(ms);
            if (mMediaControl != null) {
                mMediaControl.updateProcess(ms);
            }
        }

        @Override
        public int getDuration() throws RemoteException {
            return mPlayerControl.getDuration();
        }

        @Override
        public boolean hasPreSound() throws RemoteException {
            if (PlayerToListenManager.getInstance().isPlayingToListenTrack()) {
                return false;
            }
            int N = mListControl.getCurrListSize();
            if (N <= 1) {
                return false;
            }
            int index = mListControl.getCurrIndex();
            if (index <= 0) {
                return false;
            }
            return true;
        }

        @Override
        public boolean hasNextSound() throws RemoteException {
            int N = mListControl.getCurrListSize();
            if (isPlayingPodCastList()) {
                String[] toListenList = ToListenUtil.getMMKVUtil().getAllKeys();
                if (N <= 1 && toListenList != null && toListenList.length > 0) {
                    return true;
                }
            } else if (PlayerToListenManager.getInstance().isPlayingToListenTrack()) {
                if (N <= 1 && mOriginalDailyNewsTracks.size() > 0) {
                    return true;
                }
            }

            if (N <= 1) {
                return false;
            }
            int index = mListControl.getCurrIndex();
            if (++index < N || mListControl.isLoading()) {
                return true;
            }
            return false;

        }

        @Override
        public void setAppSecret(String secret) throws RemoteException {
            Logger.i(TAG, "setAppSecret " + secret);
            mAppSecret = secret;
        }

        @Override
        public void setCanPlayOneKeyPatch(boolean canPlayOneKeyPatch) throws RemoteException {
            Logger.i(TAG, "canPlayOneKeyPatch " + canPlayOneKeyPatch);
            mCanPlayOneKeyPatch = canPlayOneKeyPatch;
        }

        @Override
        public void setCanPlayEndPlayRecommendPatch(boolean canPlayEndPlayRecommendPatch) throws RemoteException {
            Logger.i(TAG, "canPlayEndPlayRecommendPatch " + canPlayEndPlayRecommendPatch);
            mCanPlayEndPlayRecommendPatch = canPlayEndPlayRecommendPatch;
        }

        @Override
        public List<Track> getPlayList(int position) throws RemoteException {
            List<Track> tracks = mListControl.getPlayList();
            if (tracks == null || tracks.size() < PlayerConstants.MaxNum) {
                return tracks;
            }

            int num = tracks.size();

            int start = position * PlayerConstants.MaxNum;
            int end = start + PlayerConstants.MaxNum;

            if (num > start) {
                return tracks.subList(start, end > num ? (start + num
                        % PlayerConstants.MaxNum) : end);
            }
            return null;
        }

        @Override
        public void setNotification(int id, Notification notification)
                throws RemoteException {
            try {
                if (notification != null && mService != null) {
                    Logger.i(TAG, "setNotification");
                    startForegroundService(id, notification);
                    mNotification = notification;
                    mNotificationId = id;
                    if (mAppCtx != null && mListControl != null
                            && mNotificationManager != null) {
                        boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                        XmNotificationCreater.getInstanse(mAppCtx)
                                .updateModelDetail(mListControl,
                                        mNotificationManager, mNotification,
                                        mNotificationId, isDark);
                        XmNotificationCreater.getInstanse(mAppCtx)
                                .updateViewStateAtPause(mNotificationManager,
                                        mNotification, mNotificationId, isDark, false);
                    }
                }
            } catch (Exception e) {
                //TODO xdcs统计
                CdnUtil.statToXDCSError(CdnConstants.PLAY_INFO, "setNotification:" + e.toString());
            }
        }

        @Override
        public void playRadio(Radio radio) throws RemoteException {
            if (getPlayerSrvice() == null) {
                return;
            }
            getPlayerSrvice().playRadio(radio);
        }

        @Override
        public void registeAdsListener(IXmAdsEventDispatcher l)
                throws RemoteException {
            Logger.i(TAG, "Process " + Binder.getCallingPid()
                    + "has register AdsListener");
            SendPlayStaticManager.getInstance().registeAdsListener(l);
        }

        @Override
        public void unregisteAdsListener(IXmAdsEventDispatcher l)
                throws RemoteException {
            SendPlayStaticManager.getInstance().unregisteAdsListener(l);
        }

        @Override
        public void setPageSize(int pageSize) throws RemoteException {
        }

        @Override
        public void setPageId(int pageId) throws RemoteException {
            mListControl.setPageId(pageId);
        }

        @Override
        public boolean isOnlineSource() throws RemoteException {

            if (mPlayerControl != null) {
                return mPlayerControl.isOnlineSource();
            }

            return false;
        }

        @Override
        public void clearPlayCache() throws RemoteException {

            if (mPlayerControl != null) {
                PlayerUtil.cleanUpCacheSound(mPlayerControl.getCurPlayUrl());
            }

        }

        @Override
        public void registeCommonBusinessListener(IXmCommonBusinessDispatcher l)
                throws RemoteException {

            mIXmCommonBusinessDispatcher = l;

            Logger.d(TAG, "registeCommonBusinessListener");

        }

        @Override
        public void updateSubscribeStatus(long albumId, boolean collect) throws RemoteException {
            if (mListControl != null) {
                mListControl.updateSubscribeStatus(albumId, collect);
            }
        }

        @Override
        public void updateTrackCollectStatus(long trackId, boolean collect) throws RemoteException {
            if (mListControl != null) {
                mListControl.updateTrackCollectStatus(trackId, collect);
            }
        }

        @Override
        public void updateTrackDailyNewsModel(Track updateTrack) throws RemoteException {
            if (mListControl != null) {
                mListControl.updateTrackDailyNewsModel(updateTrack);
            }
        }

        @Override
        public void updateTrackDailyVoteVOModel(Track track) throws RemoteException {
            if (mListControl != null) {
                mListControl.updateTrackDailyVoteVOModel(track);
            }
        }

        @Override
        public int getPlayListSize() throws RemoteException {
            if (getPlayerSrvice() == null) {
                return 0;
            }
            return getPlayerSrvice().getPlayListSize();
        }

        @Override
        public Map<String, String> getParam() throws RemoteException {
            return mListControl.getParams();
        }

        @Override
        public Map<String, String> getOriginParams() throws RemoteException {
            return mListControl.getOriginParams();
        }

        @Override
        public boolean isPlaying() throws RemoteException {
            if (getPlayerSrvice() == null) {
                return false;
            }
            return getPlayerSrvice().isPlaying();

        }

        @Override
        public boolean isPlayingConsiderMixPlayer() throws RemoteException {
            if (getPlayerSrvice() == null) {
                return false;
            }
            if (getPlayerSrvice().isPlaying()) {
                return true;
            }
            return MixPlayerService.getMixService().isMixPlaying();
        }

        @Override
        public boolean isAdsActive() throws RemoteException {
            if (mAdsManager != null) {
                return mAdsManager.isAdsActive();
            }
            return false;
        }

        @Override
        public void getNextPlayList() throws RemoteException {
            mListControl.getNextPlayList(false);
        }

        @Override
        public void getPrePlayList() throws RemoteException {
            mListControl.getPrePlayList(false);
        }

        @Override
        public int getCurrentPlayMethod() throws RemoteException {
            return mCurrPlayMethodFixTrackSoundSwitch;
        }

        @Override
        public void tracePushGuardFlag(boolean report) throws RemoteException {
            if (!report && OsUtil.isHarmonyOs() && getCurrPlayModel() == null && PushGuardPlayerManager.getInstance().mIsFromPushGuard) {
                // 鸿蒙设备原来没有声音 推荐之后 从桌面打开 需要清空推荐的通知栏
                XmNotificationCreater.getInstanse(mAppCtx).clearMediaSession();
            }
            PushGuardPlayerManager.getInstance().traceGuardRealClick(report, false, "image");
        }

        @Override
        public boolean getPushGuardFlag() throws RemoteException {
            return PushGuardPlayerManager.getInstance().mIsFromPushGuard;
        }

        @Override
        public void change2RecommendListForPush(boolean playForce) throws RemoteException {
            PushGuardPlayerManager.getInstance().playListWithRecommend(playForce, 0);
        }

        @Override
        public void setPlayListChangeListener(IXmDataCallback l)
                throws RemoteException {

            mListControl.setPlayListChangeListener(l);

        }

        @Override
        public void setPlanTerminateListener(IXmPlanTerminateListener l)
                throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().setPlanTerminateListener(l);
        }

        @Override
        public void setPlanTerminateListenerForQuickListen(IXmPlanTerminateListener listener) throws RemoteException {
            PlanTerminateManagerForPlayForQuickListen.getInstance().setPlanTerminateListener(listener);
        }

        @Override
        public String getPlanTerminateContinueStr() throws RemoteException {
            return PlanTerminateManagerForPlay.getInstance().getContinueStrForPlay();
        }

        @Override
        public int[] getLastPlanTerminateType() throws RemoteException {
            return PlanTerminateManagerForPlay.getInstance().getLastPlanTerminateTypeForPlay();
        }

        @Override
        public void setPlanTerminateMode(int mode) throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().setModeForPlay(mode);
        }

        @Override
        public void startPlanTerminateTimer(int type) throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().startTimerForPlay(type);
        }

        @Override
        public void startPlanTerminateCustomTimer(long time) throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().startCustomTimerForPlay(time);
        }

        @Override
        public int getPlanTerminatePlayTimes() throws RemoteException {
            return PlanTerminateManagerForPlay.getInstance().getPlayTimesForPlay();
        }

        @Override
        public int getPlanTerminateTimerType() throws RemoteException {
            return PlanTerminateManagerForPlay.getInstance().getTimerTypeForPlay();
        }

        @Override
        public int getPlanTerminateLeftSeries() throws RemoteException {
            return PlanTerminateManagerForPlay.getInstance().getLeftSeriesForPlay();
        }

        @Override
        public void setPlayCompleteWithTimeMode(boolean playComplete) throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().setPlayCompleteWithTimeMode(playComplete);
        }

        @Override
        public void planTerminateCountDown() throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().countDownForPlay();
        }

        @Override
        public void planTerminateReset() throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().resetForPlay();
        }

        @Override
        public void planTerminateRelease() throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().releaseForPlay();
        }

        @Override
        public void planTerminateForceCancel() throws RemoteException {
            PlanTerminateManagerForPlay.getInstance().forceCancelForPlay();
        }

        @Override
        public void tryToStartFreeListenTerminator() throws RemoteException {
            FreeListenManagerForPlay.getInstance().tryToStartFreeListenTerminator();
        }

        @Override
        public void setUnlockTimeOutSoundPatchUrl(String url) throws RemoteException {
            FreeListenManagerForPlay.getInstance().preLoadSoundPatch(url);
        }

        @Override
        public void updateUnlockPermissionExpireSecond(long newPermissionExpireSecond, long newLocalBaseTimeStamp) throws RemoteException {
            FreeListenManagerForPlay.getInstance().updateUnlockPermissionExpireSecond(newPermissionExpireSecond, newLocalBaseTimeStamp);
        }

        @Override
        public boolean permutePlayList() throws RemoteException {
            Logger.logToFile("permutePlayList");
            boolean premute = mListControl.permutePlayList();
            if (mHistoryManager != null) {
                mHistoryManager.savePlayList();
            }
            return premute;
        }

        @Override
        public void requestSoundAd(boolean isPaused) throws RemoteException {
            Logger.logToFile("requestSoundAd isPaused=" + isPaused);

            Track curPlayTrack = null;
            if (mListControl != null) {
                curPlayTrack = (Track) mListControl.getPlayableModel(mListControl.getCurrIndex());
            }

            if (curPlayTrack == null && mLastModel instanceof Track) {
                curPlayTrack = (Track) mLastModel;
            }

            if (canRequestAd(isPaused) && curPlayTrack != null) {
                lastRequestTime = System.currentTimeMillis();
                mAdsManager.playAds((Track) curPlayTrack, PlayerConstants.PLAY_METHOD_MANUAL_PLAY, null, true, isPaused, false);
            } else {
                lastRequestTime = System.currentTimeMillis();
            }
        }

        private boolean canRequestAd(boolean isPaused) {
            boolean adStyleIsRight = false;
            try {
                adStyleIsRight = !(isAdsActive() && (getCurSoundAd() == null || getCurSoundAd().getSoundType() != Advertis.TYPE_ONLY_SOUND));
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 两次请求广告超过间隔时间
            boolean overIntervalTime =
                    System.currentTimeMillis() - lastRequestTime > MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_SOUND_PATCH_REQUEST_TIME, 1000);
            // 二次曝光请求距离上次续播场景是否超过间隔时间
            boolean overIntervalContainTime = System.currentTimeMillis() - lastContainPlayRequestTime > MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_CONTINUE_SOUND_PATCH_REQUEST_TIME, 5000) || isPaused;

            Logger.logToFile("requestSoundAd playAds intervalTime = " + (System.currentTimeMillis() - lastRequestTime)  + "  containTime=" + (System.currentTimeMillis() - lastContainPlayRequestTime) + "    " + MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_SOUND_PATCH_REQUEST_TIME, 1000) + "    " + MmkvCommonUtil.getInstance(XmPlayerService.getPlayerSrvice()).getInt(PreferenceConstantsInOpenSdk.KEY_INTERVAL_CONTINUE_SOUND_PATCH_REQUEST_TIME, 5000));

            return adStyleIsRight && mAdsManager != null
                    && overIntervalTime && overIntervalContainTime;
        }

        @Override
        public void requestFocus(long millis) throws RemoteException {
            if (getPlayerSrvice() != null) {
                getPlayerSrvice().requestFocusImpl(millis, false);
            }
        }

        @Override
        public void pausePlayInMillis(long millis) throws RemoteException {
            mPauseTimeInMills = millis;
            if (PLAN_PAUSE_ON_COMPLETE == millis) {
                SoundPatchArbitrateManager.setForceStop(true);
            }
        }

        @Override
        public boolean isAdPlaying() throws RemoteException {
            if (mAdsManager != null) {
                return mAdsManager.isAdsPlaying();
            }
            return false;
        }

        @Override
        public boolean isAdPause() throws RemoteException {
            if (mAdsManager != null) {
                return mAdsManager.isAdsPaused();
            }
            return false;
        }

        @Override
        public boolean isTrackPlaying() throws RemoteException {
            boolean isTrackPlay = false;
            if (mPlayerControl != null) {
                isTrackPlay = mPlayerControl.getPlayerState() == PlayerConstants.STATE_STARTED;
            }
            return isTrackPlay;
        }

        @Override
        public boolean haveNextPlayList() throws RemoteException {
            if (mListControl != null) {
                return mListControl.hasNextPageUseFirstLoad();
            }
            return false;
        }

        @Override
        public boolean havePrePlayList() throws RemoteException {
            if (mListControl != null) {
                return mListControl.hasPrePageUseFirstLoad();
            }
            return false;
        }

        @Override
        public boolean isBuffering() throws RemoteException {
            if (mPlayerControl != null) {
                return mPlayerControl.isBuffering() || getPlayerStatus() == PlayerConstants.STATE_PREPARING;
            }
            return false;
        }

        @Override
        public void setProxyNew(Config config) throws RemoteException {
            Logger.e(TAG, "代理 setProxyNew " + config);
            mConfig = config;

            FreeFlowServiceUtil.setUnicomAuthenticator(config);
            mPlayerControl.setProxy(FreeFlowServiceUtil.toHttpConfig(config));
            BaseCall.getInstanse().setHttpConfig(config);
            FileUtilBase.setProxy(XmPlayerService.this, config);
            HttpUrlUtil.mConfig = config;

            if (config == null || !config.useCache) {
                FreeFlowServiceUtil.removeUnicomAuthenticator();
            }
        }

        @Override
        public void exitSoundAd() throws RemoteException {
            // 包名验证
            String packageName = null;
            String[] packages = getPackageManager().getPackagesForUid(getCallingUid());
            if (packages != null && packages.length > 0) {
                packageName = packages[0];
            }
            String selfPageName = "com.ximalaya.ting.android";

            if (!TextUtils.isEmpty(selfPageName) && selfPageName.equals(packageName)) {
                if (mAdsManager != null) {
                    mAdsManager.exitPlayAds(isPlaying() || mAdsManager.isInteractSoundAd(mAdsManager.getCurSoundAdList()));
                }
            }
        }

        @Override
        public boolean isQuickListen() throws RemoteException {
            if (mListControl != null) {
                return mListControl.isQuickListen();
            }
            return false;
        }

        @Override
        public long getQuickListenTabId() throws RemoteException {
            if (mListControl != null) {
                return mListControl.getQuickListenTabId();
            }
            return -1;
        }

        @Override
        public boolean isQuickListenRecommendTab() throws RemoteException {
            if (mListControl != null) {
                return mListControl.isQuickListenRecommendTab();
            }
            return false;
        }

        @Override
        public boolean isLoading() throws RemoteException {
            if (mAdsManager != null && mPlayerControl != null) {
                if (mPlayerControl.getPlayerState() == PlayerConstants.STATE_PREPARING) {
                    return true;
                }

                boolean enableOpt = UserInteractivePlayStatistics.Optimizer_StartPlayWhileAdRequest.enable();
                Logger.d("UIPS_", "isLoading >>> isAdsActive: " + mAdsManager.isAdsActive()
                        + ", isLastIsDuringPlay:" + mAdsManager.isLastIsDuringPlay()
                        + ", playerStatue:" + mAdsManager.getPlayerStatue()
                        + ", enableOpt:" + enableOpt
                );

                if (enableOpt) {
                    //开启优化后，广告加载不影响 loading
                    return false;
                }

                if (mAdsManager.isAdsActive()) {
                    if (mAdsManager.isLastIsDuringPlay()
                            || mAdsManager.getPlayerStatue() == MiniPlayer.STATE_STARTED
                            || mAdsManager.getPlayerStatue() == MiniPlayer.STATE_PAUSED) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
            return false;
        }

        @Override
        public boolean isPlayListSet() throws RemoteException {
            if (mListControl != null) {
                return mListControl.getOriginParams() != null;
            }
            return false;
        }

        @Override
        public void updateTrackInfo(Track track) throws RemoteException {
            if (track == null) {
                return;
            }
            List<Track> playList = mListControl.getPlayList();
            if (playList == null) {
                return;
            }
            int index = playList.indexOf(track);
            if (index < 0) {
                return;
            }
            Track targetTrack = playList.get(index);
            TrackInfoDataManager.getInstance().getTrackInfo(targetTrack, null);
        }

        @Override
        public boolean updateTrackInPlayList(Track track) throws RemoteException {
            if (track == null) {
                return false;
            }
            int index = mListControl.getPlayList().indexOf(track);
            if (index < 0) {
                return false;
            }

            Track indexTrack = mListControl.getPlayList().get(index);
            boolean needUpdate = false;
            if (indexTrack.isLike() != track.isLike()) {
                needUpdate = true;
            }

            mListControl.updateTrackInPlayList(index, track);
            if (mLastModel != null && mLastModel.getDataId() == track.getDataId()) {
                // 修复playsource丢失问题
                if (mLastModel instanceof Track &&
                        (track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_NONE
                                || track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_OTHER)) {
                    int playSource = ((Track) mLastModel).getPlaySource();
                    if(playSource > 0) {
                        track.setPlaySource(playSource);
                    }
                }
                mLastModel = track;
            }


            if (mHistoryManager != null) {
                mHistoryManager.savePlayList();
            }

            //更新通知栏，修复通知栏没有nickName问题
            boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
            XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(
                mListControl, mNotificationManager, mNotification,
                    mNotificationId, isDark);

            if (needUpdate) {
                NotificationLikeManager.INSTANCE.onTrackLikeStateChange();
            }

            return true;
        }

        @Override
        public boolean updateTrackListInPlayList(List<Track> trackList) throws RemoteException {
            if (trackList == null || trackList.size() <= 0) {
                return false;
            }

            boolean hasChange = false;
            for (Track track : trackList) {
                int index = mListControl.getPlayList().indexOf(track);
                if (index < 0) {
                    continue;
                }

                hasChange = true;

                mListControl.updateTrackInPlayList(index, track);
                if (mLastModel != null && mLastModel.getDataId() == track.getDataId()) {
                    // 修复playsource丢失问题
                    if (mLastModel instanceof Track &&
                            (track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_NONE
                                    || track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_OTHER)) {
                        int playSource = ((Track) mLastModel).getPlaySource();
                        if (playSource > 0) {
                            track.setPlaySource(playSource);
                        }
                    }
                    mLastModel = track;
                }

            }

            if (hasChange) {
                if (mHistoryManager != null) {
                    mHistoryManager.savePlayList();
                }

                //更新通知栏，修复通知栏没有nickName问题
                boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(
                        mListControl, mNotificationManager, mNotification,
                        mNotificationId, isDark);

                return true;
            } else {
                return false;
            }
        }

        @Override
        public int getPlayCurrPosition() throws RemoteException {
            return mPlayerControl.getCurrentPos();
        }

        @Override
        public boolean getPlayListOrder() throws RemoteException {
            return mListControl.getPlayListOrder();
        }

        @Override
        public void updateTrackDownloadUrlInPlayList(Track track)
                throws RemoteException {
            int index = mListControl.getPlayList().indexOf(track);
            if (index < 0) {
                return;
            }
            mListControl
                    .getPlayList()
                    .get(index)
                    .setDownloadedSaveFilePath(
                            track.getDownloadedSaveFilePath());
        }

        @Override
        public String getCurPlayUrl() throws RemoteException {
            if (mPlayerControl != null) {
                return mPlayerControl.getCurPlayUrl();
            }
            return null;
        }

        private boolean isLocalFile(String path) {
            if (TextUtils.isEmpty(path)) {
                return false;
            }
            Uri uri = Uri.parse(path);
            if ("asset".equals(uri.getScheme())) {
                return false;
            }
            String scheme = uri.getScheme();
            return TextUtils.isEmpty(scheme) || "file".equals(scheme);
        }

        @Override
        public String getCurTrackHttpUrl() throws RemoteException {
            String url = null;
            if (mPlayerControl != null) {
                url = mPlayerControl.getCurPlayUrl();
            }
            if (url == null || isLocalFile(url)) {
                PlayableModel model = getCurrPlayModel();
                if (model instanceof Track) {
                    url = PlayUrlUtil.getTrackUrlByListOrder(((Track) model).getPlayUrlInfoList());
                }
            }
            return url;
        }

        @Override
        public boolean bindSurface(Surface surface) throws RemoteException {
            mPlayerControl.bindSurface(surface);
            return false;
        }

        @Override
        public boolean releaseSurface() throws RemoteException {
            mPlayerControl.releaseSurface();
            return false;
        }

        @Override
        public boolean removeSurface(Surface surface) throws RemoteException {
            mPlayerControl.removeSurface(surface);
            return false;
        }

        @Override
        public void setFlvtype(int type) throws RemoteException {
            mPlayerControl.setFlvType(type);
        }

        @Override
        public void setFlvLoadControl(boolean isEnable) throws RemoteException {
            mPlayerControl.setFlvLoadControl(isEnable);
        }

        @Override
        public void onVideoPlayStart() throws RemoteException {
            isVideoPlaying = true;
            boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
            XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtStart(mNotificationManager, mNotification, mNotificationId, isDark, false, isSleepPlayModel(), isSketchVideo());
            XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(mListControl, mNotificationManager, mNotification,
                    mNotificationId, isDark);
            if (mMediaControl != null) {
                mMediaControl.startPlay();
            }
        }

        @Override
        public void onVideoPlayPause() throws RemoteException {
            isVideoPlaying = false;
            boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
            XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(mNotificationManager, mNotification, mNotificationId, isDark, isSleepPlayModel());
            if (mMediaControl != null) {
                mMediaControl.pausePlay();
            }
        }

        @Override
        public void setVideoMode(boolean videoMode) throws RemoteException {
            isVideoMode = videoMode;
        }


        @Override
        public void setVolume(float leftVolume, float rightVolume)
                throws RemoteException {
            mPlayerControl.setVolume(leftVolume, rightVolume);
            return;
        }

        @Override
        public float getVolume() throws RemoteException {
            if (mPlayerControl != null) {
                return mPlayerControl.getVolume();
            }
            return XMediaplayerJNI.VOLUME_DEFAULT;
        }

        @Override
        public void setDLNAState(boolean dlnaState) throws RemoteException {
            isDLNAState = dlnaState;
            if (mPlayerControl != null) {
                mPlayerControl.setDLNAState(dlnaState);
            }
        }

        @Override
        public void setPlayCdnConfigureModel(CdnConfigModel cdnConfigure) throws RemoteException {
            CdnUtil.setCdnConfigModel(cdnConfigure);
        }

        @Override
        public void resetPlayList() throws RemoteException {
            if (mListControl != null) {
                mListControl.resetPlayList();
            }
            boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
            XmNotificationCreater.getInstanse(mAppCtx).updateModelDetail(
                mListControl, mNotificationManager, mNotification,
                    mNotificationId, isDark);
            if (mAdsManager != null) {
                mAdsManager.stopCurrentAdPlay(true);
            }
        }

        @Override
        public void resetPlayListForRestoreToMainPlayer() throws RemoteException {
            resetPlayList();
            // 退出直播时，如果播放历史中没有声音类型，那就得清空播放列表。这种情况下需要把直播加到播放历史，但是直播切换直播间也会调用resetPlayList，只能先加个方法区分了
            LivePutIntoHistoryManager.INSTANCE.putLastPlayingLiveIntoHistoryIfNeeded(mLastModel, null);
        }

        @Override
        public void removeListByIndex(int index) throws RemoteException {
            if (mListControl != null) {
                mListControl.removeListByIndex(index);
            }
        }

        @Override
        public void removeListByTrackId(long trackId) throws RemoteException {
            if (mListControl != null) {
                mListControl.removeListByTrackId(trackId);
            }
        }

        @Override
        public String getHistoryPos(String historyIds) throws RemoteException {
            return PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).
                    getSoundHistoryPos(historyIds);
        }

        @Override
        public void setHistoryPosById(long historyId, int hisPosition) throws RemoteException {
            saveSoundHistoryPos(historyId, hisPosition);
        }

        @Override
        public String getLastPlayTrackInAlbum(String albumId) throws RemoteException {
            return getLastPlayTrackInAlbumInner(albumId);
        }

        @Override
        public void needContinuePlay(boolean flag) throws RemoteException {
            isContinuePlay = flag;
            if (mPlayerAudioFocusControl != null) {
                mPlayerAudioFocusControl.needContinuePlay(flag);
            }
        }

        @Override
        public void setRecordModel(RecordModel model) throws RemoteException {
            if (mStatisticsManager != null) {
                mStatisticsManager.setRecordModel(model);
            }
            if (mXmPlayStatisticsManager != null) {
                mXmPlayStatisticsManager.setRecordModel(model);
            }
        }

        @Override
        public boolean isDLNAState() throws RemoteException {
            if (mPlayerControl != null) {
                isDLNAState = mPlayerControl.isDLNAState();
                return isDLNAState;
            } else {
                return isDLNAState;
            }
        }

        @Override
        public void setPlayerProcessRequestEnvironment(int environment) throws RemoteException {
            BaseConstants.environmentId = environment;
        }

        @Override
        public void updateLoginInfoOnChange(boolean isLogin) throws RemoteException {
            if (mListControl != null) {
                List<Track> playList = mListControl.getPlayList();
                if (playList != null) {
                    for (Track track : playList) {
                        if (track != null) {
                            // 登录状态发生变化后将声音的更新状态恢复下
                            track.setUpdateStatus(false);
                            track.setAuthorized(false);
                        }
                    }
                }
            }
        }

        /**
         * 播放历史相关
         *
         * @param sync
         */
        @Override
        public void syncCloudHistory(boolean sync) {
            ICloudyHistory cloudyHistory = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
            if (cloudyHistory != null) {
                cloudyHistory.syncCloudHistory(sync);
            }
        }

        /**
         * 播放历史相关
         */
        @Override
        public void clearAllPlayHistory(boolean clearCloudy) {
            ICloudyHistory cloudyHistory = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
            if (cloudyHistory != null) {
                cloudyHistory.clearAllPlayHistory(clearCloudy);
            }
        }

        /**
         * 播放历史相关
         */
        @Override
        public void deletePlayHistory(HistoryModel historyModel) {
            ICloudyHistory cloudyHistory = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
            if (cloudyHistory != null) {
                cloudyHistory.deletePlayHistory(historyModel);
            }
        }
        /**
         * 播放历史相关
         */
        @Override
        public void batchDeleteHistory(List<HistoryModel> historyModels) {
            ICloudyHistory cloudyHistory = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
            if (cloudyHistory != null) {
                cloudyHistory.batchDeleteHistory(historyModels);
            }
        }


        /**
         * 播放历史相关
         */
        @Override
        public void markAllHistoryDeleted(boolean onlyRadio) {
            if (mHistoryManager != null) {
                mHistoryManager.markAllHistoryDeleted(onlyRadio);
            }
        }

        /**
         * 播放历史相关
         */
        @Override
        public void deleteRadioHistory(Radio radio) {
            if (mHistoryManager != null) {
                mHistoryManager.deleteRadioHistory(radio);
            }
        }

        @Override
        public Track getTrackByHistory(long albumId) {
            if (mHistoryManager != null) {
                return mHistoryManager.getTrackByHistory(albumId);
            }
            return null;
        }

        @Override
        public HistoryModel getHistoryModelByHistory(long albumId) throws RemoteException {
            if (mHistoryManager != null) {
                return mHistoryManager.getHistoryModelByHistory(albumId);
            }
            return null;
        }

        @Override
        public void updateAdFreeData() throws RemoteException {
            if (sAdFreeDataChange != null) {
                sAdFreeDataChange.onAdFreeDataChange();
            }
        }

        @Override
        public long getCurrentPosInner() {
            if (mPlayerControl != null) {
                return mPlayerControl.getCurrentPosInner();
            }
            return 0;
        }

        @Override
        public Radio getHistoryInfoByRadioID(long radioId) {
            if (mHistoryManager != null) {
                return mHistoryManager.getHistoryInfoByRadioID(radioId);
            }
            return null;
        }

        @Override
        public List<HistoryModel> getTrackList() {
            if (mHistoryManager != null) {
                return mHistoryManager.getTrackList();
            }
            return null;
        }

        @Override
        public List<HistoryModel> getTrackListHis(int size) {
            if (mHistoryManager != null) {
                return mHistoryManager.getTrackListHis(size);
            }
            return null;
        }

        @Override
        public List<Radio> getHisRadioList() {
            if (mHistoryManager != null) {
                return mHistoryManager.getHisRadioList();
            }
            return null;
        }

        @Override
        public void putSoundHistory(Track track) {
            if (mHistoryManager != null) {
                mHistoryManager.putSound(track);
            }
        }

        @Override
        public void saveSoundHistoryPos(long trackId, int playPos) {
            PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).saveSoundHistoryPos(trackId, playPos, true);
        }

        @Override
        public void onVideoPlayEnd(Track track, int breakSec, int playDurationSec) {
            mStatisticsManager.onVideoPlayEnd(track, breakSec, playDurationSec);
        }

        @Override
        public void onSwitchInAudio(int switchInSec) throws RemoteException {
            mStatisticsManager.onSwitchInAudio(switchInSec);
        }

        @Override
        public void onSwitchOutAudio(int switchOutSec) throws RemoteException {
            mStatisticsManager.onSwitchOutAudio(switchOutSec);
        }

        @Override
        public Map<String, String> getDubPlayStatistics() {
            return mStatisticsManager.getDubPlayStatistics();
        }

        @Override
        public void setTrackToNext(Track track) throws RemoteException {
            if (getPlayListControl() != null) {
                getPlayListControl().setTrackToNext(track);
            }
        }

        @Override
        public void putTrackAfterTarget(long targetTrackId, Track insertTrack) throws RemoteException {
            if (getPlayListControl() != null) {
                getPlayListControl().putTrackAfterTarget(targetTrackId, insertTrack);
            }
        }

        @Override
        public void putTracksAfterTarget(long targetTrackId, List<Track> insertTrack) throws RemoteException {
            if (getPlayListControl() != null) {
                getPlayListControl().putTracksAfterTarget(targetTrackId, insertTrack);
            }
        }

        @Override
        public void setMediaSessionBgViewShow(boolean show) throws RemoteException {
            if (XmPlayerConfig.getInstance(XmPlayerService.this) != null) {
                XmPlayerConfig.getInstance(XmPlayerService.this).setMediaSessionBgView(show);
            }
        }

        @Override
        public void setFlvDataCallBack(IXmFlvDataCallback l) throws RemoteException {
            synchronized (XmPlayerService.class) {
                if (l != null) {
                    mFlvDataCallBack.register(l);
                    StaticConfig.setFlvDataCallback(mFlvDataCallbackForPlay);
                } else {
                    StaticConfig.setFlvDataCallback(null);
                }
            }
        }

        @Override
        public void registePlayHistoryListener(IXmPlayHistoryListener listener) {
            SendPlayStaticManager.getInstance().registePlayHistoryListener(listener);
        }

        @Override
        public int getTrackBufferPercentage() {
            if (mPlayerControl != null) {
                return mPlayerControl.getBufferPercent();
            } else {
                return 0;
            }
        }

        @Override
        public void setNotificationType(int type) {
         /*   try{
                mNotificationType=type;
                hasInitNotification=false;

                if(BaseUtil.isAppForeground(XmPlayerService.this)) {

                    XmPlayerService.this.setNotification();
                }
            }catch (Exception e){
                Logger.e("XmPlayerService------------------------e-----------",e.toString());
            }*/
        }

        public void setCurAdVideoPlayCurPos(int curPos, int duration) throws RemoteException {
            if (mAdsManager != null) {
                mAdsManager.setCurAdVideoPlayCurPos(curPos, duration);
            }
        }

        @Override
        public void setNotificationAfterKilled() throws RemoteException {

            XmPlayerService.this.setNotification();
        }

        @Override
        public void setVolumeBalance(boolean isOpen) throws RemoteException {
            if (mPlayerControl != null) {
                mPlayerControl.setVolumeBalance(isOpen);
            }
        }

        @Override
        public void setAudioFocusAtStartStateAtTransient() throws RemoteException {
            if (mPlayerAudioFocusControl != null) {
                mPlayerAudioFocusControl.setAudioFocusAtStartStateAtTransient();
            }
        }

        @Override
        public void setLoggerEnable(boolean loggerEnable, boolean playerLoggerEnable) throws RemoteException {
            Logger.isDebug = loggerEnable;
            XMediaPlayerConstants.isDebug = playerLoggerEnable;
            XMediaPlayerConstants.isDebugPlayer = playerLoggerEnable;
        }

        @Override
        public void setNetMonitorEnable(boolean enable) {
            if (enable) {
                NetMonitorUtil.startNetMonitor(XmPlayerService.getPlayerSrvice(), false);
            } else {
                NetMonitorUtil.closeNetMonitor();
            }
        }

        @Override
        public void setContinuePlayWhileAuditionTrackPlayComplete(boolean flag) throws RemoteException {
            isContinuePlayWhileAuditionTrackPlayComplete = flag;
        }

        @Override
        public boolean isContinuePlayWhileAuditionTrackPlayComplete() throws RemoteException {
            return isContinuePlayWhileAuditionTrackPlayComplete;
        }

        @Override
        public float getTempo() throws RemoteException {
            if (mPlayerControl != null) {
                return mPlayerControl.getTempo();
            }

            return XMediaplayerJNI.TEMPO_DEFAULT;
        }

        @Override
        public void setSkipNextPlayedSound(boolean skip) {
            if (mListControl != null) {
                mListControl.setSkipNextPlayedSound(skip);
            }
        }

        @Override
        public void clearPlayList() {
            if (mHistoryManager != null) {
                mHistoryManager.clearPlayList();
            }
        }

        @Override
        public void clearAllLocalHistory() {
            if (mHistoryManager != null) {
                mHistoryManager.clearAllLocalHistory();
            }
        }

        @Override
        public void setPlayFragmentIsShowing(boolean isShowing) throws RemoteException {
            XmAdsManager.isPlayFragmentShowing = isShowing;
            XmAdsManager.lastPlayFragmentShowTime = System.currentTimeMillis();
        }

        @Override
        public void stagePlayList() throws RemoteException {
            PlayListManager.INSTANCE.stagePlayList();
        }

        @Override
        public boolean isInRecommendPlayList() throws RemoteException {
            if (mListControl != null) {
                PlayableModel curModel = mListControl.getPlayableModel(mListControl.getCurrIndex());
                if (curModel instanceof Track) {
                    return ((Track) curModel).isRecommendSound();
                }
            }
            return false;
        }

        @Override
        public void resetPlayer() throws RemoteException {
            if (mPlayerControl != null) {
                mPlayerControl.resetMediaPlayer();
            }
        }


        @Override
        public void setCheckAdContent(boolean willCheck) throws RemoteException {
            XmAdsManager.checkAdContent = willCheck;
        }

        @Override
        public void onVideoAdCompleted(int curPos, int duration) throws RemoteException {
            Logger.logToFile("onVideoAdCompleted curPos=" + curPos + ",duration=" + duration);
            if (mAdsManager != null) {
                mAdsManager.onVideoAdCompleted(curPos, duration);
            }
        }

        @Override
        public void onVideoAdPlayStart(boolean isDuringPlay) throws RemoteException {
            Logger.logToFile("onVideoAdPlayStart isDuringPlay=" + isDuringPlay);
            if (mAdsManager != null) {
                mAdsManager.onVideoAdPlayStart(isDuringPlay);
            }

            if (!isDuringPlay) {
                if (sSoundPatchAdStateListener != null) {
                    sSoundPatchAdStateListener.onAdPlaying();
                }
            }
        }

        public int getAlbumSortByAlbumId(long albumId) {
            if (mHistoryManager != null) {
                return mHistoryManager.getAlbumSortByAlbumId(albumId);
            }
            return IHistoryManagerForPlay.ASC_ORDER;
        }

        public void putAlbumSortByAlbumId(long albumId, int order) {
            if (mHistoryManager != null) {
                mHistoryManager.putAlbumSortByAlbumId(albumId, order);
            }
        }

        public int getHistoryTrackListSize() {
            if (mHistoryManager != null) {
                return mHistoryManager.getHistoryTrackListSize();
            }
            return 0;
        }

        @Override
        public void insertPlayListHead(List<Track> list) throws RemoteException {
            if (mListControl != null) {
                mListControl.insertPlayListHead(list);
            }
        }

        @Override
        public long getCurrentTrackPlayedDuration() {
            XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
            if(xmPlayerService != null) {
                XmPlayerControl xmPlayerControl = xmPlayerService.getPlayControl();
                if(xmPlayerControl != null) {
                    return xmPlayerControl.getPlayedDuration();
                }
            }
            return 0;
        }

        @Override
        public void setIsUsingFreeFlow(boolean isUsingFreeFlow) throws RemoteException {
            IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
            if (freeFlowService != null) {
                freeFlowService.setIsUsingFreeFlow(isUsingFreeFlow);
            }
        }

        @Override
        public void setIsFullScreenDoc(boolean isFullScreenDoc) throws RemoteException {
            if (mAdsManager != null) {
                mAdsManager.setIsFullScreenDoc(isFullScreenDoc);
            }
        }

        @Override
        public void setIsScreenDoc(boolean isScreenDoc) throws RemoteException {
            if (mAdsManager != null) {
                mAdsManager.setIsFullScreenDoc(isScreenDoc);
            }
        }

        @Override
        public void setPlayerScene(int playerScene) throws RemoteException {
            if (mAdsManager != null) {
                mAdsManager.setPlayerScene(playerScene);
            }
        }
        @Override
        public void setIsNewPlayPage(boolean isNewPlayPage) throws RemoteException {
//            if (mAdsManager != null) {
//                mAdsManager.setIsNewPlayPage(isNewPlayPage);
//            }
        }

        @Override
        public void onCoverAdClose(boolean isFromUser) throws RemoteException {
            if (mAdsManager != null) {
                mAdsManager.onCoverAdClose(isFromUser);
            }
        }

        @Override
        public void setFreeFlowType(int type) throws RemoteException {
            IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
            if (freeFlowService != null) {
                freeFlowService.saveChooseMobileType(type);
            }
        }

        @Override
        public void stopCurrentSoundAd() throws RemoteException {
            if (mAdsManager != null) {
                mAdsManager.stopCurrentSoundAd();
            }
        }

        // ******** MixPlayer begin ********
        IMixPlayerStatusListener mixPlayerStatusListener = new IMixPlayerStatusListener() {
            private static final String TAG = "MixPlayer111Service";

            @Override
            public void onMixStart() {
                Logger.d(TAG, "XmPlayerImpl onMixStart: ");
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixStart();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
                try {
                    // 保存播放信息 -- 持久化
                    MixPlayerService.getMixService().savePlayInfo();
                    Logger.i(TAG, "MixPlayerService savePlayInfo");
                    // 通知栏更改
                    if (XmPlayerService.getPlayerSrvice() != null) {
                        XmPlayerService.getPlayerSrvice().setNotification();
                    }

                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    boolean isMixMode = true;

                    if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
                        if (mMediaControl != null) {
                            mMediaControl.startPlay();
                        }
                        MixTrack playSource = MixPlayerService.getMixService().getPlaySource();
                        XmNotificationCreater.getInstanse(mAppCtx).updateModelDetailForMixPlayer(playSource, mNotificationManager, mNotification, mNotificationId, isDark);

                        XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtStart(mNotificationManager,
                                mNotification, mNotificationId, isDark, false, isMixMode, false);
                        sendPlayerStartBroadCast();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onMixPause() {
                Logger.d(TAG, "XmPlayerImpl onMixPause: ");
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixPause();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
                if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
                    if (mMediaControl != null) {
                        mMediaControl.pausePlay();
                    }
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                            mNotificationManager, mNotification, mNotificationId, isDark, true);
                    sendPlayerPauseBroadCast();
                }
            }

            @Override
            public void onMixStop() {
                Logger.d(TAG, "XmPlayerImpl onMixStop: ");
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixStop();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
//                    if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
//                        boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
//                        XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
//                                mNotificationManager, mNotification, mNotificationId, isDark, true);
//                    }
            }

            @Override
            public void onMixProgressUpdate(int percent) {
                Logger.d(TAG, "XmPlayerImpl onMixProgressUpdate: " + percent);
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixProgressUpdate(percent);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
            }

            @Override
            public void onMixComplete() {
                Logger.d(TAG, "XmPlayerImpl onMixComplete: ");
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixComplete();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
                if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                            mNotificationManager, mNotification, mNotificationId, isDark, true);
                    sendPlayCompleteBroadCast();
                }
            }

            @Override
            public void onMixError(String url, int code, String msg) {
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixError(url, code, msg);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
                if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) {
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    XmNotificationCreater.getInstanse(mAppCtx).updateViewStateAtPause(
                            mNotificationManager, mNotification, mNotificationId, isDark, true);
                }
            }

            @Override
            public void onMixStatusChanged(double key, boolean isPlaying, String state, long curPosition) {
                Logger.d(TAG, "XmPlayerImpl onMixStatusChanged isPlaying=" + isPlaying + ", state=" + state + ", curPosition=" + curPosition);
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixStatusChanged(key, isPlaying, state, curPosition);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
                XmMixStatisticsManager.Companion.getInstance().onMixStatusChanged(key, isPlaying, curPosition, state);
                mStatisticsManager.onMixStatusChanged(key, isPlaying, curPosition);
            }

            @Override
            public void notifyMixProgressChanged(double key, int curPos, int duration) {
                XmMixStatisticsManager.Companion.getInstance().onMixProgressUpdate(key, curPos, duration);
            }

            @Override
            public void onMixSoundComplete(double key) {
                Logger.d(TAG, "XmPlayerImpl onMixSoundComplete: ");
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixSoundComplete(key);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
                XmMixStatisticsManager.Companion.getInstance().onMixSoundComplete(key);
                mStatisticsManager.onMixSoundComplete(key);
            }

            @Override
            public void onMixTrackCleared() {
                synchronized (XmPlayerService.class) {
                    int N = mMixPlayerDispatcher.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        IMixPlayerEventDispatcher item = mMixPlayerDispatcher
                                .getBroadcastItem(i);
                        try {
                            item.onMixTrackCleared();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    mMixPlayerDispatcher.finishBroadcast();
                }
            }
        };

        @Override
        public void createMixPlayerService() throws RemoteException {
//            MixPlayerService.getMixService().onCreateService(getApplicationContext());
//            MixPlayerService.getMixService().addPlayerStatusListener(mixPlayerStatusListener);
        }

        @Override
        public void destroyMixPlayerService() throws RemoteException {
            MixPlayerService.getMixService().onDestroy();
        }

        @Override
        public void newMixPlayer(double key) throws RemoteException {
            MixPlayerService.getMixService().createPlayerService(key);
        }

        @Override
        public void setMixDataSource(double key, String url) throws RemoteException {
            MixPlayerService.getMixService().setDataSource(key, url);
        }

        @Override
        public void setMixDataSourceInfo(Map params, double key) throws RemoteException {
            MixPlayerService.getMixService().setDataSource(key, params);
        }

        @Override
        public Map<String, Object> getMixDataSourceInfo(double key) throws RemoteException {
            return MixPlayerService.getMixService().getDataSourceInfo(key);
        }

        @Override
        public void setMixVolume(double key, float left, float right) throws RemoteException {
            MixPlayerService.getMixService().setVolume(key, left, right);
        }

        @Override
        public void setMixLooper(double key, boolean isLoop) throws RemoteException {
            MixPlayerService.getMixService().setLooper(key, isLoop);
        }

        @Override
        public void setMixSpeed(double key, float speed) throws RemoteException {
            MixPlayerService.getMixService().setSpeed(key, speed);
        }

        @Override
        public void startMixPlayer(double key) throws RemoteException {
            MixPlayerService.getMixService().start(key);
        }

        @Override
        public void pauseMixPlayer(double key) throws RemoteException {
            MixPlayerService.getMixService().pause(key);
        }

        @Override
        public void stopMixDelay(long mills) throws RemoteException {
            MixPlayerService.getMixService().stopDelay(mills);
        }

        @Override
        public void seekToMixPlayer(double key, int seek) throws RemoteException {
            MixPlayerService.getMixService().seekTo(key, seek);
        }

        @Override
        public void stopMixPlayer(double key) throws RemoteException {
            MixPlayerService.getMixService().stop(key);
        }

        @Override
        public void setMixStartPosition(double key, int start) throws RemoteException {
            MixPlayerService.getMixService().setStartPosition(key, start);
        }

        @Override
        public long getMixCurPosition(double key) throws RemoteException {
            return MixPlayerService.getMixService().getCurPosition(key);
        }

        @Override
        public long getMixDuration(double key) throws RemoteException {
            return MixPlayerService.getMixService().getDuration(key);
        }

        @Override
        public String getMixPlaySource(double key) throws RemoteException {
            return MixPlayerService.getMixService().getPlaySource(key);
        }

        @Override
        public List getMixPlayerKeys() throws RemoteException {
            return MixPlayerService.getMixService().getAllPlayerKeys();
        }

        @Override
        public Map getDelayMillsInfo() throws RemoteException {
            return MixPlayerService.getMixService().getDelayMillsInfo();
        }

        @Override
        public int getMixCurPercent() throws RemoteException {
            return MixPlayerService.getMixService().getMixCurPositon();
        }

        @Override
        public void setMixConfig(Map params) throws RemoteException {
            MixPlayerService.getMixService().setMixPlayerConfig(params);
            if (mAppCtx != null && mNotificationManager != null) {
                MixTrack playSource = MixPlayerService.getMixService().getPlaySource();
                XmNotificationCreater.getInstanse(mAppCtx).updateModelDetailForMixPlayer(playSource, mNotificationManager,
                        mNotification, mNotificationId, NotificationColorUtils.isDarkNotificationBar(mAppCtx));
                XmNotificationCreater.getInstanse(mAppCtx).updateViewStateForMix(mNotificationManager, mNotification,
                        mNotificationId, NotificationColorUtils.isDarkNotificationBar(mAppCtx), !isMixerPlaying());
            }
        }

        @Override
        public MixTrack getMixPlayTrack() throws RemoteException {
            return MixPlayerService.getMixService().getPlaySource();
        }

        @Override
        public void clearMixPlayTrack() throws RemoteException {
            MixPlayerService.getMixService().clearPlayInfo();
        }


        @Override
        public boolean isMixPlaying(double key) throws RemoteException {
            return MixPlayerService.getMixService().isPlaying(key);
        }

        @Override
        public boolean isMixerPlaying() throws RemoteException {
            return MixPlayerService.getMixService().isMixPlaying();
        }

        @Override
        public void playMixerPlayer() throws RemoteException {
            MixPlayerService.getMixService().start();
        }

        @Override
        public void pauseMixerPlayer() throws RemoteException {
            MixPlayerService.getMixService().pause();
        }

        @Override
        public void releaseMixPlayer(double key) throws RemoteException {
            MixPlayerService.getMixService().releasePlayerService(key);
        }

        @Override
        public void registeMixPlayerListener(IMixPlayerEventDispatcher listener) throws RemoteException {
            Logger.i(TAG, "Process " + Binder.getCallingPid() + "has register IMixPlayerEventDispatcher");
            if (listener != null && mMixPlayerDispatcher != null) {
                synchronized (XmPlayerService.class) {
                    mMixPlayerDispatcher.register(listener, new MyRemoteCallbackList.ProcessCookie(Binder
                            .getCallingPid(), Binder.getCallingUid()));
                }
            }
        }

        @Override
        public void unregisteMixPlayerListener(IMixPlayerEventDispatcher listener) throws RemoteException {
            if (listener != null && mMixPlayerDispatcher != null) {
                mMixPlayerDispatcher.unregister(listener);
            }
        }

        @Override
        public void configureItem(ConfigWrapItem item) throws RemoteException {
            if (item != null) {
                item.onHandleItem();
            }
        }

        @Override
        public void notifyFestivalTaskService(TaskStatusInfo info) throws RemoteException {
            if (info != null) {
                TaskStatusInfo.notifyInfo(info);
            }
        }

        @Override
        public void setPreviewAdSource(AdPreviewModel adPreviewModel) throws RemoteException {
            mAdPreviewModel = adPreviewModel;
        }

        @Override
        public void setChannelJumpOver(boolean jumpOver) throws RemoteException {

            String packageName = null;
            String[] packages = getPackageManager().getPackagesForUid(getCallingUid());
            if (packages != null && packages.length > 0) {
                packageName = packages[0];
            }
            String selfPageName = "com.ximalaya.ting.android";

            if (!TextUtils.isEmpty(selfPageName) && selfPageName.equals(packageName)) {
                if (mAdsManager != null) {
                    mAdsManager.setChannelJumpOver(jumpOver);
                }
            }

        }

        @Override
        public void setValueToPlayProcess(String key, String value) throws RemoteException {
            String packageName = null;
            String[] packages = getPackageManager().getPackagesForUid(getCallingUid());
            if (packages != null && packages.length > 0) {
                packageName = packages[0];
            }
            String selfPageName = "com.ximalaya.ting.android";

            if (!(!TextUtils.isEmpty(selfPageName) && selfPageName.equals(packageName))) {
                return;
            }

            CrossProcessTransferValueManager.onValueBack(XmPlayerService.this, key, value);
        }

        @Override
        public void updateOnGetListenTime() throws RemoteException {
            XmPlayerService.this.updateOnGetListenTime();
        }

        @Override
        public void updateSkipHeadTail(long albumId, int head, int tail) throws RemoteException {
            SkipHeadTailManager.getInstance().update(albumId, head, tail);
        }

        @Override
        public void setTrackPlayQualityLevel(int trackQuality, int cellularQuality) throws RemoteException {
            if (TrackUrlChooseManager.getInstance().getChooseTrackQuality() != trackQuality || TrackUrlChooseManager.getInstance().getChooseCellularQuality() != cellularQuality) {
                Logger.logToFile("setTrackPlayQualityLevel set trackQuality=" + trackQuality + ",cellularQuality=" + cellularQuality);
                TrackUrlChooseManager.getInstance().setChooseTrackQuality(trackQuality, cellularQuality);
                playTrackOnTrackPlayQualityLevelChange();
            } else {
                Logger.logToFile("setTrackPlayQualityLevel fail trackQuality=" + trackQuality + ",cellularQuality=" + cellularQuality);
            }
        }

        @Override
        public void setTrackByTimbreType(String type, boolean forcePlay) throws RemoteException {
            playTrackOnTrackTimbreTypeChange(type, forcePlay);
        }

        @Override
        public void setCommercialSoundPatchWorkMode(int mode) {
            SoundPatchArbitrateManager.getInstance().setWorkMode(mode);
        }

        @Override
        public void registerCommercialSoundPatch(int type, SimpleSoundPatchInfo simpleInfo) {
            SoundPatchArbitrateManager.getInstance().registerSoundPatch(type, simpleInfo);
        }

        @Override
        public void playCommercialSoundPatchByType(int type, String paramsJsonString) {
            SoundPatchArbitrateManager.getInstance().playSoundPatchByType(type, paramsJsonString);
        }

        @Override
        public boolean isPlayingCommercialSoundPatch() {
            return SoundPatchArbitrateManager.getInstance().isPlayingSoundPatch();
        }

        @Override
        public boolean needBlockPlayNextOrPrevBtn() throws RemoteException {
            return SoundPatchArbitrateManager.getInstance().needBlockPlayNextOrPrevBtn();
        }

        @Override
        public void stopCommercialSoundPatch() {
            SoundPatchArbitrateManager.getInstance().stopSoundPatch();
        }

        @Override
        public void resetCommercialSoundPatchOnVideoAdPlay() {
            SoundPatchArbitrateManager.getInstance().resetOnVideoAdPlay();
        }

        @Override
        public void addSoundPatch(SoundPatchInfo soundPatchInfo) throws RemoteException {
            SoundPatchManager.getInstance().addSoundPatchInfo(soundPatchInfo);
        }

        @Override
        public void appendSoundPatch(SoundPatchInfo soundPatchInfo) throws RemoteException {
            SoundPatchManager.getInstance().appendSoundPatchInfo(soundPatchInfo);
        }

        @Override
        public void removeSoundPatch(SoundPatchInfo soundPatchInfo) throws RemoteException {
            SoundPatchManager.getInstance().removeSoundPatchInfo(soundPatchInfo);
        }

        @Override
        public void stopSoundPatch() throws RemoteException {
            SoundPatchManager.getInstance().stopPlay();
        }

        @Override
        public void registerSoundPatchStateListener(ISoundPatchStatusDispatcher dispatch) throws RemoteException {
            if (dispatch != null) {
                synchronized (XmPlayerService.class) {
                    mSoundPatchDispatcher.register(
                            dispatch,
                            new MyRemoteCallbackList.ProcessCookie(Binder
                                    .getCallingPid(), Binder.getCallingUid()));
                }
            }
        }

        @Override
        public void playFreeListenTimeRunOutSoundPatch(boolean isForceUseOnScreenSoundPatch) {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.playFreeListenTimeRunOutSoundPatch(isForceUseOnScreenSoundPatch);
            }
        }

        @Override
        public void startAllDayListenTimeOutCountDown() {
            XmAdsManager adManager = getAdManager();
            if (adManager != null) {
                adManager.startAllDayListenTimeOutCountDown();
            }
        }

        @Override
        public void registerBaseInfoListener(IXmPlayerBaseInfoRequestDispatcher dispatch) throws RemoteException {
            if(dispatch != null) {
                synchronized (XmPlayerService.class) {
                    SendPlayStaticManager.getInstance().registerBaseInfoRequestListener(dispatch);
                }
            }
        }

        // ******** MixPlayer end ********
        @Override
        public void setUbtSourceSynchronizer(IUbtSourceSynchronizer synchronizer) throws RemoteException {
            if (synchronizer != null) {
                UbtSourceProvider.getInstance().setUbtSourceSynchronizer(synchronizer);
            }
        }

        @Override
        public void onUbtSourceChanged(String type) throws RemoteException {
            UbtSourceProvider.getInstance().onUbtSourceChanged(type);
        }

        @Override
        public void registerSoundEffectStatusListener(ISoundEffectStatusDispatcher dispatch) throws RemoteException {
            if (dispatch != null) {
                synchronized (XmPlayerService.class) {
                    mSoundEffectDispatcher.register(
                            dispatch,
                            new MyRemoteCallbackList.ProcessCookie(Binder
                                    .getCallingPid(), Binder.getCallingUid()));
                }
            }
        }

        @Override
        public SoundEffectInfo requireInitialSoundEffectInfo(long uid, boolean isVip) throws RemoteException {
            return SoundEffectPlayerManager.getInstance().requireInitialSoundEffectInfo(uid, isVip);
        }

        @Override
        public boolean useSoundEffect(int type, long effectId, String effectSourceDir) throws RemoteException {
            return SoundEffectPlayerManager.getInstance().setSoundEffect(type, effectId, effectSourceDir);
        }

        @Override
        public void resetToNormalSoundEffect() throws RemoteException {
            SoundEffectPlayerManager.getInstance().resetToNormalSoundEffect();
        }

        @Override
        public boolean isCurEffectConfigCanUsed() throws RemoteException {
            if (mPlayerControl != null){
                return mPlayerControl.isCurEffectConfigCanUsed();
            }
            return false;
        }


        @Override
        public long getPlayDurationById(String id) throws RemoteException {
            if (realTimeStatistics != null) {
                return realTimeStatistics.getPlayDurationById(id);
            }
            return 0l;
        }

        @Override
        public void setRemoteControlState(boolean open) throws RemoteException {
            if (mPlayerControl != null) {
                mPlayerControl.setRemoteControlState(open);
            }
        }

        @Override
        public void updatePlayProgressChange(int curPos, int duration) {
            if (mPlayerControl != null) {
                mPlayerControl.updatePlayProgressChange(curPos, duration);
            }
        }

        @Override
        public void startPlanTerminateTimerForQuickListen(int type) throws RemoteException {
            PlanTerminateManagerForPlayForQuickListen.getInstance().startTimerForPlay(type);
        }

        @Override
        public void startPlanTerminateCustomTimerForQuickListen(long time) throws RemoteException {
            PlanTerminateManagerForPlayForQuickListen.getInstance().startCustomTimerForPlay(time);
        }

        @Override
        public int getPlanTerminateTimerTypeForQuickListen() throws RemoteException {
            return PlanTerminateManagerForPlayForQuickListen.getInstance().getTimerTypeForPlay();
        }

        @Override
        public void planTerminateReleaseForQuickListen() throws RemoteException {
            PlanTerminateManagerForPlayForQuickListen.getInstance().releaseForPlay();
        }

        @Override
        public void planTerminateForceCancelForQuickListen() throws RemoteException {
            PlanTerminateManagerForPlayForQuickListen.getInstance().forceCancelForPlay();
        }

        @Override
        public int indexOfTrackId(long trackId) throws RemoteException {
            if (mListControl == null) {
                return -1;
            }
            int index = mListControl.getTrackIndex(trackId);
            return index;
        }

    }

    public void playTrackOnTrackPlayQualityLevelChange() {
        if (mListControl == null || mAdsManager == null) {
            return;
        }

        //清理播放url缓存
        TrackInfoPrepareManager.getInstance(getPlayerSrvice()).clearCacheUrl();

        Track curTrack = (Track) mListControl.getPlayableModel(mListControl.getCurrIndex());

        if (!mAdsManager.isAdsActive() && curTrack != null) {
            boolean autoPlay = isPlaying() && !mAdsManager.isAdsPlaying();
            Logger.logToFile("playTrackOnTrackPlayQualityLevelChange playTrack autoPlay=" + autoPlay);
            playTrack(curTrack, autoPlay);
        }
    }

    public void playTrackOnTrackTimbreTypeChange(String type, boolean forcePlay) {
        if (mListControl == null || mAdsManager == null) {
            return;
        }
        Logger.e("ToListen", "playTrackOnTrackTimbreTypeChange forcePlay = " + forcePlay);
        Logger.logToFile("playTrackOnTrackTimbreTypeChange type=" + type + ", forcePlay=" + forcePlay);

        //清理播放url缓存
        TrackInfoPrepareManager.getInstance(getPlayerSrvice()).clearCacheUrl();

        Track curTrack = (Track) mListControl.getPlayableModel(mListControl.getCurrIndex());

        if (!mAdsManager.isAdsActive() && curTrack != null) {
            if (curTrack.isToListenRecommend() || curTrack.getIsFromToListenTrack() == 1) {
                if (SoundPatchManager.getInstance().isPlaying()) {
                    SoundPatchManager.getInstance().stopPlay();
                }
                forcePlay = true;
            }
            boolean isTimerStoppedForPlay = PlanTerminateManagerForPlay.getInstance().isTimerStoppedForPlay();
            if (isTimerStoppedForPlay) {
                Logger.logToFile("playTrackOnTrackTimbreTypeChange isTimerStoppedForPlay = " + isTimerStoppedForPlay);
                forcePlay = false;
            }

            if (forcePlay) {
                playTrack(curTrack, forcePlay);
            } else {
                boolean autoPlay = isPlaying() && !mAdsManager.isAdsPlaying() && !isTimerStoppedForPlay;
                Logger.logToFile("playTrackOnTrackTimbreTypeChange autoPlay=" + autoPlay);
                playTrack(curTrack, autoPlay);
            }
        }
    }

    //播放场景
    public static final int PLAY_SCENE_TYPE_DEFAULT = 0;    // 默认
    public static final int PLAY_SCENE_TYPE_DRIVE_MODE = 1;    // 驾驶模式
    public static final int PLAY_SCENE_TYPE_HICAR = 2;         // hicar模式
    private int mPlayScene = PLAY_SCENE_TYPE_DEFAULT;

    public int getPlayScene() {
        return mPlayScene;
    }

    private void sendPlayerStartBroadCast() {
        if (MixPlayerService.getMixService().isMixPlaying()) {
            return;
        }
        if (mBaseAppWidgetProviders != null && mAppCtx != null && mBaseAppWidgetProviders.size() > 0) {
            Intent intent = new Intent(ConstantsOpenSdk.START_PLAYER_ACTION);
            for (BaseAppWidgetProvider provider : mBaseAppWidgetProviders) {
                if (provider != null) {
                    provider.onReceive(mAppCtx, intent);
                }
            }
            mDailyNewsWidgetProvider.onReceive(mAppCtx, intent);
        }
    }

    private void sendPlayerPauseBroadCast() {
        if (MixPlayerService.getMixService().isMixPlaying()) {
            return;
        }
        if (mBaseAppWidgetProviders != null && mAppCtx != null && mBaseAppWidgetProviders.size() > 0) {
            Intent intent = new Intent(ConstantsOpenSdk.PAUSE_PLAYER_ACTION);
            for (BaseAppWidgetProvider provider : mBaseAppWidgetProviders) {
                if (provider != null) {
                    provider.onReceive(mAppCtx, intent);
                }
            }
            mDailyNewsWidgetProvider.onReceive(mAppCtx, intent);
        }
    }

    public BaseAppWidgetProvider getAutoStopWidgetProvider() {
        return mAutoStopWidgetProvider;
    }

    private void sendPlayCompleteBroadCast() {
        if (MixPlayerService.getMixService().isMixPlaying()) {
            return;
        }
        if (mBaseAppWidgetProviders != null && mAppCtx != null && mBaseAppWidgetProviders.size() > 0) {
            Intent intent = new Intent(ConstantsOpenSdk.STATE_COMPLETE_ACTION);
            for (BaseAppWidgetProvider provider : mBaseAppWidgetProviders) {
                if (provider != null) {
                    provider.onReceive(mAppCtx, intent);
                }
            }
            mDailyNewsWidgetProvider.onReceive(mAppCtx, intent);
        }
    }

    public void updateNotification() throws RemoteException {
        boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
        XmNotificationCreater.getInstanse(mAppCtx)
                .updateModelDetail(mListControl, mNotificationManager, mNotification, mNotificationId, isDark);
    }

    public void sendPlayLikeStateChange() {
        if (mBaseAppWidgetProviders != null && mAppCtx != null && mBaseAppWidgetProviders.size() > 0) {
            Intent intent = new Intent(ConstantsOpenSdk.STATE_LIKE_CHANGE_ACTION);
            for (BaseAppWidgetProvider provider : mBaseAppWidgetProviders) {
                if (provider != null) {
                    provider.onReceive(mAppCtx, intent);
                }
            }
        }
    }

    public void sendPushRecommendDataChange() {
        if (mBaseAppWidgetProviders != null && mAppCtx != null && mBaseAppWidgetProviders.size() > 0) {
            Intent intent = new Intent(ConstantsOpenSdk.STATE_PUSH_RECOMMEND_CHANGE_ACTION);
            for (BaseAppWidgetProvider provider : mBaseAppWidgetProviders) {
                if (provider != null) {
                    provider.onReceive(mAppCtx, intent);
                }
            }
        }
    }

    public PlayableModel getPlayableModel() {
        PlayableModel model = mListControl.getCurrentPlayableModel();
        return model;
    }

    public PlayMode getPlayMode() {
        if (mListControl != null) {
            return mListControl.getPlayMode();
        } else {
            return PlayMode.PLAY_MODEL_LIST;
        }
    }

    public boolean isContinuePlay() {
        return isContinuePlay;
    }

    public boolean isOnlineResource() {
        return mPlayerControl.isOnlineSource();
    }

    public boolean isPlayingRadio() {
        return mPlayerControl != null && mPlayerControl.isPlayingRadio();
    }

    public boolean isPlayingLive() {
        return mPlayerControl != null && mPlayerControl.isPlayingLive();
    }

    public XmPlayListControl getPlayListControl() {
        return mListControl;
    }

    public XmPlayerControl getPlayControl() {
        return mPlayerControl;
    }

    public String getCurPlayUrl() {
        if (mPlayerControl != null) {
            return mPlayerControl.getCurPlayUrl();
        }
        return null;
    }

    // 定时
    public static final int PLAN_PAUSE_ON_COMPLETE = -1; // 当前歌曲播放完毕时停止播放
    public static final int PLAN_NORMAL = 0;        // 没有设置计划

    private long mPauseTimeInMills = PLAN_NORMAL;  // 在多少毫秒之后暂停声音

    // 检查是否要停止声音
    private void checkIsPauseTime() {
        long time = mPauseTimeInMills - System.currentTimeMillis();
        if (mPauseTimeInMills > 0 && time <= 0) {
            mPauseTimeInMills = PLAN_NORMAL;
            pausePlay(false, PauseReason.Common.PLAN_PAUSE);
        }
    }

    private void checkNeedPauseCurTrack(PlayableModel curr, int currPos) {
        if (curr instanceof Track) {
            Track track = (Track) curr;
            if (track.isNeedPauseAtDesPos() && track.getEndPlayPos() > 0
                    && currPos >= track.getEndPlayPos() * 1000) {
                track.setNeedPauseAtDesPos(false);
                pausePlay(false, PauseReason.Common.PLAN_PAUSE_TRACK);
            }
        }
    }

    private Handler mTimeHandler;

    public Handler getTimeHander() {
        if (mTimeHandler == null) {
            mTimeHandler = new Handler(Looper.getMainLooper());
        }
        return mTimeHandler;
    }

    public void showToast(String message, long duration) {
        try {
            if (mIXmCommonBusinessDispatcher != null)
                mIXmCommonBusinessDispatcher.showToast(message, duration);
        } catch (RemoteException e) {
            Logger.i(TAG, "show toast error " + e.toString());
        }
    }

    public void closeApp() {

        try {
            if (mIXmCommonBusinessDispatcher != null)
                mIXmCommonBusinessDispatcher.closeApp();
        } catch (RemoteException e) {
            Logger.i(TAG, "close app " + e.toString());
        }

        try {
            XmPlayerManager.unBind();
            XmPlayerManagerForPlayer.unBind();
            stopSelf();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //防止主进程被杀死 导致X按钮不能退出
        if (mService != null && !(BaseUtil.isProcessRunning(this, "com.ximalaya.ting.android"))) {
            Logger.i(TAG, "close app use stopself");
            try {
                stopSelf();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static final String TARGET_CLASS_NAME = "com.ximalaya.ting.android.host.activity.MainActivity";

    public void startForegroundService() {
        Notification lastNotification = XmNotificationCreater.getInstanse(this).getLastNotification();
        startForegroundService(id, lastNotification);
        bindSustainListenService();
    }

    private void bindSustainListenService() {
        boolean isStartSustainedListenService = MMKVUtil.getInstance().getBoolean("is_start_sustained_listen_service");
        if (isStartSustainedListenService) {
            XmAppHelper.runOnWorkThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        SustainedListenService.bindSustainedListenerService(XmPlayerService.this.getApplicationContext());
                    } catch (Exception e) {
                    }
                }
            });
        }
    }

    public boolean isCanPlayOneKeyPatch() {
        return mCanPlayOneKeyPatch;
    }

    public boolean isCanPlayEndPlayRecommendPatch() {
        return mCanPlayEndPlayRecommendPatch;
    }

    public void startForegroundService(int id, Notification notification) {

        if (mService == null || notificationCancled) {
            return;
        }
        StartServiceTimeoutMonitor.startStageRecord("start_foreground_start", this);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                if (notification != null) {
                    mService.startForeground(id, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK);
                } else if (mNotification != null) {
                    mService.startForeground(id, mNotification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK);
                } else {
//                    CdnUtil.statToXDCSError("SystemNotificationError", "startForegroundService notification== null");
                }
            } else {
                if (notification != null) {
                    mService.startForeground(id, notification);
                } else if (mNotification != null) {
                    mService.startForeground(id, mNotification);
                } else {
//                    CdnUtil.statToXDCSError("SystemNotificationError", "startForegroundService notification== null");
                }
            }
            PlayServiceStartUpMonitor.getInstance().onstageEnd(IStageMonitor.sPlayServiceStartUp);
            StartServiceTimeoutMonitor.startStageRecord("start_foreground_finish", this);
        } catch (Throwable t) {
            String stackTrace = Log.getStackTraceString(t);
            UseTraceCollector.log("startForegroundService___error:___" + stackTrace);
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
            if (xdcsPost != null) {
                xdcsPost.statErrorToXDCS("startForegroundService", "startForegroundService(int id, Notification notification):_____" + stackTrace
                        + "————————isScreenOn:" + SystemUtil.isScreenOn(mAppCtx));
            }
        }
        StartServiceTimeCollectUtil.endSelectionFromPlayerProcess();
    }


    /**
     * 通知栏置顶方案
     * */
    public void pushRemoveAndAddNewNotification() throws RemoteException {
        Context context = getContext();
        if (isPlaying() || notificationCancled || context == null || NotificationStyleUtils.isSystemStyle()) {
            return;
        }
        if (mNotificationManager != null) {
            mNotificationManager.cancel(mNotificationId);
        }
        getTimeHander().postDelayed(() -> {
            if (context == null) {
                return;
            }
            mNotification = null;
            hasInitNotification = false;
            XmNotificationCreater.getInstanse(context).setInitNotification(null);
            setNotification();
        }, 1000);
    }

    public Context getContext() {
        Context context = null;
        if (mService != null) {
            context = mService.getApplicationContext();
        }
        if (context == null) {
            context = getApplicationContext();
        }
        if (context == null && mAppCtx != null) {
            context = mAppCtx;
        }
        return context;
    }
    public void setNotification() {
        Logger.i(TAG, "call setNotification " + hasInitNotification);

        if (hasInitNotification) {

            startForegroundService();
            return;
        }
        if (getContext() == null) {
            return;
        }
        hasInitNotification = true;

        boolean usePreInit = MmkvCommonUtil.getInstance(getContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_PRE_INIT_NOTIFICATION_SWITCH, false);
        if (usePreInit) {
            XmNotificationCreater.getInstanse(getContext()).setInitListener(new INotificationInitListener() {
                @Override
                public void initCallback(Notification notification) {
                    if (notification != null) {
                        doAfterNotificationInit(notification);
                    } else {
                        initNotification();
                    }
                }
            });
        } else {
            initNotification();
        }
    }

    private void initNotification() {
        if (getContext() == null) {
            return;
        }
        Notification notification = XmNotificationCreater.getInstanse(getContext()).getInitNotification();
        if (notification == null) {
            StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.reflectActivity, this);
            Class<?> clazz = null;
            try {
                clazz = Class.forName(TARGET_CLASS_NAME, false, XmPlayerService.class.getClassLoader());
            } catch (Exception e) {
                e.printStackTrace();
            }
            StartServiceTimeCollectUtil.endPlayerSelection(this);
            if (clazz == null) {
                Logger.i(TAG, "setNotification but clazz == null");
                return;
            }
            notification = XmNotificationCreater.getInstanse(getContext()).initNotification(getContext(), clazz, mNotificationType);
        }
        doAfterNotificationInit(notification);
    }

    private void doAfterNotificationInit(Notification notification) {
        try {
            if (notification != null && mService != null) {
                Logger.i(TAG, "setNotification");
                startForegroundService(id, notification);
                mNotification = notification;
                mNotificationId = id;
                if (mAppCtx != null && mListControl != null
                        && mNotificationManager != null) {
                    boolean isDark = NotificationColorUtils.isDarkNotificationBar(mAppCtx);
                    if (MixPlayerService.getMixService() != null && MixPlayerService.getMixService().getPlaySource() != null) { // 至此说明混音播放器里面有歌曲，通知栏应该显示混音播放器的内容
                        Logger.i(TAG, "init MixPlayTrack Notification");
                        MixTrack playSource = MixPlayerService.getMixService().getPlaySource();
                        XmNotificationCreater.getInstanse(mAppCtx).updateModelDetailForMixPlayer(playSource, mNotificationManager, mNotification, mNotificationId, isDark);
                        XmNotificationCreater.getInstanse(mAppCtx)
                                .updateViewStateAtPause(mNotificationManager,
                                        mNotification, mNotificationId, isDark, true);
                    } else {
                        Logger.i(TAG, "init Common Track Notification");
                        XmNotificationCreater.getInstanse(mAppCtx)
                                .updateModelDetail(mListControl,
                                        mNotificationManager, mNotification,
                                        mNotificationId, isDark);
                        XmNotificationCreater.getInstanse(mAppCtx)
                                .updateViewStateAtPause(mNotificationManager,
                                        mNotification, mNotificationId, isDark, isSleepPlayModel());
                    }
                }
            } else {

                Logger.i(TAG, "notification == null ? " + (notification == null) + " mService == null ? " + (mService == null));
            }
        } catch (Exception e) {
            //TODO xdcs统计
            CdnUtil.statToXDCSError(CdnConstants.PLAY_INFO, "setNotification:" + e.toString());
        }
    }

    private boolean isSleepPlayModel() {
        PlayableModel model = getCurrPlayModel();
        if (model != null) {
            return TextUtils.equals(model.getKind(), PlayableModel.KIND_MODE_SLEEP);
        }
        return false;
    }

    public boolean isSketchVideo() {
        boolean bool = ConfigureCenter.getInstance().getBool("android", "sketch_vdieo_media_control_open", false);
        if (bool) {
            return false;
        }
        PlayableModel model = getCurrPlayModel();
        if (model != null && model instanceof Track) {
            ((Track) model).getType();
            SubordinatedAlbum album = ((Track) model).getAlbum();
            if (album != null) {
                return album.isSketchVideoAlbum();
            }
        }
        return false;
    }

    private void reShowClosedNotification() {
        if (notificationCancled) {
            notificationCancled = false;
            setNotification();
            addScreenChangeBroadCast();
        }
    }

    public PlayableModel getCurrPlayModel() {
        if (mListControl != null) {
            return mListControl.getCurrentPlayableModel();
        }
        return null;
    }

    public PlayableModel getLastPlayModel() {
        if (mListControl != null) {
            return mListControl.getLastPlayableModel();
        }
        return lastPlayModel;
    }

    //四维图新车机户联方案播放数据输出
    public void setPlayDataOutPutListener(XMediaPlayer.OnPlayDataOutputListener dataOutputListener) {
        if (mPlayerControl != null) {
            mPlayerControl.setOnPlayDataOutputListener(dataOutputListener);
        }
        mOnPlayDataOutputListener = dataOutputListener;
    }


    // 保存历史
    private void saveModelToHistory(PlayableModel lastModel, PlayableModel curModel) {
        if (curModel == null) {
            return;
        }


        if (mHistoryManager == null) {
            return;
        }

        //更新广播的最后播放时间,广播是Schedule
        if (lastModel != null && PlayableModel.KIND_SCHEDULE.equalsIgnoreCase(lastModel.getKind()) && lastModel.getDataId() > 0) {
            mHistoryManager.updateRadioEndTime(lastModel.getDataId(), System.currentTimeMillis());
        }

        String playKind = curModel.getKind();
        if (PlayableModel.KIND_TRACK.equalsIgnoreCase(playKind)) {
            //2016-12-28，活动直播的kind也是track但是所以可能会保存但服务端不会记录，故排除
            Track track = (Track) curModel;
            if (track.getAlbum() == null) {
                return;
            }
            if (track.getType() == Track.TYPE_DUBSHOW) {  //配音秀不进播放历史
                return;
            }
//            if (track.getType() == Track.TYPE_ONE_KEY_LISTEN) { // 一键听不进入播放历史
//                return;
//            }

            if (track.isDoNotAddToHistoryThisTime()) {
                track.setDoNotAddToHistoryThisTime(false);
            } else {
                mHistoryManager.putSound((Track) curModel);
            }
        } else if (PlayableModel.KIND_SCHEDULE.equalsIgnoreCase(curModel.getKind())) {
            mHistoryManager.putRadio(getRadio(curModel));
        } else if (PlayableModel.KIND_RADIO.equalsIgnoreCase(curModel.getKind())) {
            Radio radio = ModelUtil.trackToRadio((Track) curModel);
            if (!radio.isActivityLive()) {
                mHistoryManager.putRadio(radio);
            }
        } else if (PlayableModel.KIND_MODE_SLEEP.equalsIgnoreCase(curModel.getKind())) {
            mHistoryManager.putSound((Track) curModel);
        } else if (PlayableModel.KIND_MYCLUB_REPLAY_TRACK.equalsIgnoreCase(curModel.getKind())) {
            mHistoryManager.putSound((Track) curModel);
        } else if (curModel.isKindOfLive() && curModel instanceof Track) {
            HandlerManager.getMainHandler().postDelayed(() -> {
                Logger.d(TAG, "put live to history,liveRoomId: " + ((Track) curModel).getLiveRoomId());
                if (((Track) curModel).getLiveRoomId() > 0) {
                    mHistoryManager.putSound((Track) curModel);
                }
            }, 1000);
        }
    }

    private Radio getRadio(PlayableModel playableModel) {
        Schedule schedule = ModelUtil.trackToSchedule((Track) playableModel);

        Radio radio = new Radio();

        radio.setDataId(schedule.getRadioId());
        radio.setKind(PlayableModel.KIND_SCHEDULE);
        radio.setRadioName(schedule.getRadioName());
        radio.setProgramName(schedule.getRelatedProgram().getProgramName());
        radio.setScheduleID(schedule.getDataId());
        radio.setCoverUrlSmall(schedule.getRelatedProgram().getBackPicUrl());
        radio.setCoverUrlLarge(schedule.getRelatedProgram().getBackPicUrl());
        radio.setUpdateAt(System.currentTimeMillis());
        radio.setRate24AacUrl(schedule.getRelatedProgram().getRate24AacUrl());
        radio.setRate24TsUrl(schedule.getRelatedProgram().getRate24AacUrl());
        radio.setRate64AacUrl(schedule.getRelatedProgram().getRate64AacUrl());
        radio.setRate64TsUrl(schedule.getRelatedProgram().getRate64TsUrl());
        radio.setRadioPlayCount(schedule.getRadioPlayCount());
        radio.setProgramId(schedule.getRelatedProgram().getProgramId());
        radio.setChannelId(schedule.getChannelId());
        radio.setChannelName(schedule.getChannelName());
        radio.setChannelPic(schedule.getChannelPic());
        return radio;
    }

    public void mediaSessionSeek(int ms) {
        if (mPlayerControl != null && mPlayerControl.isRemoteControl()) {
            IXmCommonBusinessDispatcher callback = getIXmCommonBusinessDispatcher();
            if (callback != null) {
                try {
                    callback.mediasessionClick(ms);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void seekTo(int ms) {
        if (getPlayerImpl() != null) {
            try {
                getPlayerImpl().seekTo(ms);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public int getDuration() {
        if (getPlayerImpl() != null) {
            try {
                return getPlayerImpl().getDuration();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public int getPlayCurrPosition() {
        if (getPlayerImpl() != null) {
            try {
                return getPlayerImpl().getPlayCurrPosition();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public void pausePlayInMillis(long millis) {
        if (getPlayerImpl() != null) {
            try {
                getPlayerImpl().pausePlayInMillis(millis);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void requestFocusImpl(long millis, boolean isPlayer) {
        if (mPlayerAudioFocusControl != null) {
            mPlayerAudioFocusControl.requestAudioFocus(millis, isPlayer);
        }
    }

    public int getCurrIndex() {
        if (getPlayerImpl() != null) {
            try {
                return getPlayerImpl().getCurrIndex();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public boolean isXimaEnd(long dataId) {
        if (mListControl != null) {
            return mListControl.isXimaEnd(dataId);
        }
        return false;
    }

    public Track getTrack(int index) {
        if (getPlayerImpl() != null) {
            try {
                return getPlayerImpl().getTrack(index);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public PlayMode getXmPlayMode() {
        if (getPlayListControl() != null) {
            try {
                return getPlayListControl().getPlayMode();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return PlayMode.PLAY_MODEL_LIST;
    }

    // 通知更新进度
    public void notifProgress(int currPos, int duration) {
        if (mPlayerStatusListener != null) {
            mPlayerStatusListener.onPlayProgress(currPos, duration);
        }
    }

    private XmPlayerControl.IPlaySeekListener mPlaySeekListener = new XmPlayerControl.IPlaySeekListener() {
        @Override
        public void onSeekComplete(int seekToPosition) {
            if (mStatisticsManager != null) {
                mStatisticsManager.onSeekComplete(seekToPosition);
            }
            notifProgress(seekToPosition, getDuration());
            PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).saveProgress(mListControl, mPlayerControl,
                    seekToPosition, getDuration(), true);

        }
    };
    // 口播广告需要和声音无缝连接,所以需要在playStart的时候暂停
    private Set<XmAdsManager.IPlayStartCallBack> mPlayStartCallBack = new CopyOnWriteArraySet<>();

    public void setPlayStartCallback(XmAdsManager.IPlayStartCallBack playStartCallback) {
        if (playStartCallback == null) {
            return;
        }

        mPlayStartCallBack.add(playStartCallback);
    }

    public void removePlayStartCallback(XmAdsManager.IPlayStartCallBack playStartCallback) {
        if (playStartCallback == null) {
            return;
        }

        mPlayStartCallBack.remove(playStartCallback);
    }

    private Set<XmAdsManager.IPlayProgressCallBack> mPlayProgressCallBack = new CopyOnWriteArraySet<>();

    public void setPlayProgressCallBack(XmAdsManager.IPlayProgressCallBack playProgressCallBack) {
        if (playProgressCallBack == null) {
            return;
        }
        mPlayProgressCallBack.add(playProgressCallBack);
    }

    public void removePlayProgressCallBack(XmAdsManager.IPlayProgressCallBack playProgressCallBack) {
        if (playProgressCallBack == null) {
            return;
        }

        mPlayProgressCallBack.remove(playProgressCallBack);
    }

    private Set<XmAdsManager.IPlayCompleteCallBack> mPlayCompleteCallBack = new CopyOnWriteArraySet<>();

    public void setPlayCompleteCallBack(XmAdsManager.IPlayCompleteCallBack playCompleteCallBack) {
        if (playCompleteCallBack == null) {
            return;
        }
        mPlayCompleteCallBack.add(playCompleteCallBack);
    }

    public void removePlayCompleteCallback(XmAdsManager.IPlayCompleteCallBack playCompleteCallBack) {
        if (playCompleteCallBack == null) {
            return;
        }

        mPlayCompleteCallBack.remove(playCompleteCallBack);
    }

    private Set<XmAdsManager.IPlayPauseCallBack> mPlayPauseCallBack = new CopyOnWriteArraySet<>();

    public void setPlayPauseCallBack(XmAdsManager.IPlayPauseCallBack playPauseCallBack) {
        if (playPauseCallBack == null) {
            return;
        }
        mPlayPauseCallBack.add(playPauseCallBack);
    }

    public void removePlayPauseCallback(XmAdsManager.IPlayPauseCallBack playPauseCallBack) {
        if (playPauseCallBack == null) {
            return;
        }

        mPlayPauseCallBack.remove(playPauseCallBack);
    }

    public void playPauseNoNotif(int reason) {
        if (mPlayerControl != null) {
            Logger.logToFile("playPauseNoNotif: " + Log.getStackTraceString(new Throwable()) + ":playPauseNoNotif");
            mPlayerControl.pause(false, reason);
        }
    }

    public void addHistoryLoadSuccessListener(XmPlayerManager.IOnHistoryListLoadSuccess listener) {
        if (listener == null) {
            return;
        }
        mOnHistoryListLoadSuccess.add(listener);
    }

    public void removeHistoryLoadSuccessListener(XmPlayerManager.IOnHistoryListLoadSuccess listener) {
        if (listener == null) {
            return;
        }
        mOnHistoryListLoadSuccess.remove(listener);
    }

    public void onLoadedHistorySuccess() {
        hasLoadedHistory = true;

        for (XmPlayerManager.IOnHistoryListLoadSuccess listener : mOnHistoryListLoadSuccess) {
            listener.onHistoryListLoadSuccess();
        }
    
        if (mIXmCommonBusinessDispatcher != null) {
            try {
                mIXmCommonBusinessDispatcher.onSetHistoryToPlayer();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 这里一定要考虑service回收重启的情况可以先参考之前人写的方式
    public static void addPlayerStatusListenerOnPlayProcees(IXmPlayerStatusListener playerStatusListener) {
        SendPlayStaticManager.getInstance().addPlayerStatusListener(playerStatusListener);
    }

    public static void removePlayerStatusListenerOnPlayProcess(IXmPlayerStatusListener playerStatusListener) {
        SendPlayStaticManager.getInstance().removePlayerStatusListener(playerStatusListener);
    }

    public AdPreviewModel getAdPreviewModel() {
        return mAdPreviewModel;
    }

    public void setAdPreviewModel(AdPreviewModel adPreviewModel) {
        mAdPreviewModel = adPreviewModel;
    }

    public IXmCommonBusinessDispatcher getIXmCommonBusinessDispatcher() {
        return mIXmCommonBusinessDispatcher;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (getApplication() == null
                || getApplication().getResources() == null
                || getApplication().getResources().getConfiguration() == null) {
            return;
        }

        int currentDarkMode = getApplication().getResources().getConfiguration().uiMode
                & Configuration.UI_MODE_NIGHT_MASK;
        boolean isDark = currentDarkMode == Configuration.UI_MODE_NIGHT_YES;

        if (NotificationColorUtils.isSystemDarkModeIsOpen() != isDark) {
            NotificationColorUtils.setSystemDarkMode(isDark);
            NotificationColorUtils.forceUpadteColorModel = true;
            if (mNotificationManager != null && mNotification != null) {
                try {
                    if (NotificationStyleUtils.isSystemStyle()) {
                        changeNotificationStyle(NotificationStyleUtils.currStyle);
                    } else if (!notificationCancled) {
                        XmNotificationCreater.getInstanse(mAppCtx).
                                updateRemoteViewDetailOnDarkChange(mNotificationManager, mNotification,
                                        mNotificationId, NotificationColorUtils.isDarkNotificationBar(mAppCtx));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }

    public boolean isContinuePlayWhileAuditionTrackPlayComplete() {
        return isContinuePlayWhileAuditionTrackPlayComplete;
    }

    public RemoteCallbackList<ISoundPatchStatusDispatcher> getSoundPatchDispatcher() {
        return mSoundPatchDispatcher;
    }

    public RemoteCallbackList<ISoundEffectStatusDispatcher> getSoundEffectDispatcher() {
        return mSoundEffectDispatcher;
    }

    private void updateDarkMode() {
        int currentDarkMode = getApplication().getResources().getConfiguration().uiMode
                & Configuration.UI_MODE_NIGHT_MASK;
        boolean isDark = currentDarkMode == Configuration.UI_MODE_NIGHT_YES;
        if (NotificationColorUtils.isSystemDarkModeIsOpen() != isDark) {
            NotificationColorUtils.setSystemDarkMode(isDark);
            NotificationColorUtils.forceUpadteColorModel = true;
            if (mNotificationManager != null && mNotification != null) {
                try {
                    if (!notificationCancled) {
                        XmNotificationCreater.getInstanse(mAppCtx).
                                updateRemoteViewDetailOnDarkChange(mNotificationManager, mNotification,
                                        mNotificationId, NotificationColorUtils.isDarkNotificationBar(mAppCtx));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void addServiceLife(IServiceLifeCallBack serviceLiftCallBack) {
        if (serviceLiftCallBack == null) {
            return;
        }

        sServiceLifeCallBacks.add(serviceLiftCallBack);
    }

    public static void removeServiceLife(IServiceLifeCallBack serviceLifeCallBack) {
        if (serviceLifeCallBack == null) {
            return;
        }

        sServiceLifeCallBacks.remove(serviceLifeCallBack);
    }

    public void addUseStatusChangeCallBackForPlayProcess(IUseStatusChangeCallBackForPlayProcess callBack) {
        mUseStatusChangeCallBacks.add(callBack);
    }

    public void removeUseStatusChangeCallBackForPlayProcess(IUseStatusChangeCallBackForPlayProcess callBack) {
        if (callBack != null) {
            mUseStatusChangeCallBacks.remove(callBack);
        }
    }

    public IDomainServerIpCallback getIDomainServerIpCallback() {
        return mIDomainServerIpCallback;
    }

    public boolean isElderlyMode() {
        return mIsElderlyMode;
    }


    public int getPlayerStatus() {
        XmPlayerImpl playerImpl = getPlayerImpl();
        if (playerImpl != null) {
            try {
                return playerImpl.getPlayerStatus();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        return PlayerConstants.STATE_ERROR;
    }

    public boolean hasNextSound() {
        XmPlayerImpl playerImpl = getPlayerImpl();
        if (playerImpl != null) {
            try {
                return playerImpl.hasNextSound();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public boolean hasPreSound() {
        XmPlayerImpl playerImpl = getPlayerImpl();
        if (playerImpl != null) {
            try {
                return playerImpl.hasPreSound();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public void removeListByIndex(int removeIndex) {
        XmPlayerImpl playerImpl = getPlayerImpl();
        if (playerImpl != null) {
            try {
                playerImpl.removeListByIndex(removeIndex);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public void setTrackToNext(Track track) {
        if (getPlayListControl() != null) {
            getPlayListControl().setTrackToNext(track);
        }
    }

    public void removeListByTrackId(long trackId) {
        XmPlayerImpl playerImpl = getPlayerImpl();
        if (playerImpl != null) {
            try {
                playerImpl.removeListByTrackId(trackId);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public void setPlayMode(PlayMode playMode) {
        if (mListControl != null) {
            mListControl.setPlayMode(playMode);
        }
    }

    public Track getTrackByHistory(long albumId) {
        XmPlayerImpl playerImpl = getPlayerImpl();
        if (playerImpl != null) {
            return playerImpl.getTrackByHistory(albumId);
        }

        return null;
    }

    public Track getLastPlayTrackInAlbum(long albumId) {
        XmPlayerImpl playerImpl = getPlayerImpl();
        try {
            if (playerImpl != null) {
                String trackInAlbum = playerImpl.getLastPlayTrackInAlbum(albumId + "");
                return new Gson().fromJson(trackInAlbum, Track.class);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }

        return null;
    }

    public interface IOnPlayModeChangeListener {
        void onPlayModeChange();
    }

    public static void setPlayModeChangeListener(IOnPlayModeChangeListener listener) {
        mPlayModeChangeListener = listener;
    }

    public interface IAdFreeDataChange {
        void onAdFreeDataChange();
    }

    public static void setAdFreeDataChangeListener(IAdFreeDataChange listener) {
        sAdFreeDataChange = listener;
    }

    public static void setSoundPatchAdStateListener(ISoundPatchAdStateListener listener) {
        sSoundPatchAdStateListener = listener;
    }

    public static ISoundPatchAdStateListener getSoundPatchAdStateListener() {
        return sSoundPatchAdStateListener;
    }

    public void updateRemoteViewOnLikeStateChange() {
        XmNotificationCreater.getInstanse(XmPlayerService.getPlayerSrvice())
                .updateRemoteViewOnLikeStateChange(mNotificationManager, mNotification, mNotificationId);
    }

    public void updateOnGetListenTime() {
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcastSync(
                new Intent(PlayerConstants.TING_KEY_ACTION_GET_LISTEN_TIME));
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcastSync(
                new Intent(PlayerConstants.TING_KEY_ACTION_GET_LISTEN_TIME_NEW));
    }

    public void setRealTimeStatistics(RealTimeStatistics realTimeStatistics) {
        this.realTimeStatistics = realTimeStatistics;
    }

    public static void setAdFreeManager(IAdFree adFreeManager) {
        XmPlayerService.adFreeManager = adFreeManager;
    }

    private static boolean isAdFree(Track sound) {
        if (sound == null) return false;
        SubordinatedAlbum album = sound.getAlbum();
        if (album == null) return false;
        return adFreeManager != null && adFreeManager.isFree(album.getAlbumId());
    }

    private void createSimpleNotificationForStartProcess() {
        Context context = getContext();
        if (context == null) {
            return;
        }

        boolean openSwitch = MmkvCommonUtil.getInstance(context).getBoolean(
                PreferenceConstantsInOpenSdk.KEY_SEND_SIMPLE_NOTIFICATION_SWITCH);
        if (!openSwitch) {
            return;
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return;
        }

        StartServiceTimeCollectUtil.beginPlayerSection(StartServiceTimeCollectUtil.Stage.newIntNotification, this);
        NotificationCompat.Builder builder = NotificationChannelUtils.newPlayerNotificationBuilder(context);
        // 设置通知标题和内容
        Notification notification = builder.setContentTitle("喜马拉雅")
                .setContentText("正在运行")
                .setSmallIcon(R.drawable.notify_notification_icon) // 设置通知小图标（需要在drawable文件夹下放置对应的图标）
                .setAutoCancel(false)
                .setOngoing(true)
                .setGroupSummary(false)
                .setGroup(XmNotificationCreater.NOTIFICATION_GROUP)
                .setPriority(Notification.PRIORITY_MAX)
                .build();// 点击通知后自动取消

        // 发送通知
        startForegroundService(id, notification);
        StartServiceTimeCollectUtil.endPlayerSelection(this);
    }

    public void saveOriginPlayListBeforeToListen(boolean deleteCurrentNormalTrack) {
        if (mListControl == null) {
            return;
        }
        if (mListControl.getPlayList() == null) {
            return;
        }
        CopyOnWriteArrayList<Track> trackList = new CopyOnWriteArrayList<Track>(mListControl.getPlayList());
        for (Track track : trackList) {
            if (track.isToListenRecommend() || track.getIsFromToListenTrack() == 1 || !PlayableModel.KIND_TRACK.equals(track.getKind())) {
                mOriginalDailyNewsTracks.clear();
                ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalTrackList");
                ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalParams");
                ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalPlayListMode");
                ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalIndex");
                ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalPlayingId");
                return;
            }
        }
        Map<String, String> params = new HashMap<>();
        if (mListControl.getParams() != null) {
            params.putAll(mListControl.getParams());
        }
        int index = mListControl.getCurrIndex();
//        if (deleteCurrentNormalTrack && trackList.size() > 0) {
//            // 播客丢弃当前声音
//            Logger.logToFile("PlayerToListenManager 开播待播列表前，播客丢弃当前声音");
//            trackList.remove(0);
//            index = 0;
//        }
        mOriginalDailyNewsTracks.clear();
        mOriginalDailyNewsTracks.addAll(trackList);
        if (mListControl.getCurrentPlayableModel() instanceof Track) {
            long saveTrackId = -1L;
            int saveIndex = 0;
            if (getPlayMode() != XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP && ToListenUtil.isListenComplete(true, getContext(), (Track) mListControl.getCurrentPlayableModel())) {
                // 已播完，下次开播下一首
                if (mListControl.getNextPlayTrack() != null) {
                    saveTrackId = mListControl.getNextPlayTrack().getDataId();
                    saveIndex =  index + 1;
                } else {
                    saveTrackId = mListControl.getCurrentPlayableModel().getDataId();
                    saveIndex =  index;
                }
            } else {
                saveTrackId = mListControl.getCurrentPlayableModel().getDataId();
                saveIndex =  index;
            }
            ToListenUtil.getCommonMMKVUtil().saveLong("ToListenOriginalPlayingId", saveTrackId);
            ToListenUtil.getCommonMMKVUtil().saveInt("ToListenOriginalIndex", saveIndex);
        }
        String playListMode = params.get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM);
        ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalPlayListMode", playListMode);
        if (Looper.myLooper() == Looper.getMainLooper()) {
            MyAsyncTask.execute(() -> {
                Gson gson = new Gson();
                ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalTrackList", gson.toJson(trackList));
                ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalParams", gson.toJson(params));
                Logger.logToFile("PlayerToListenManager saveOriginPlayListBeforeToListen done async");
            });
        } else {
            Gson gson = new Gson();
            ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalTrackList", gson.toJson(trackList));
            ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalParams", gson.toJson(params));
            Logger.logToFile("PlayerToListenManager saveOriginPlayListBeforeToListen done");
        }
    }

    public void clearNormalData() {
//        ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalIndex");
//        ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalTrackList");
//        ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalParams");
        ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalPlayListMode");
        mOriginalDailyNewsTracks.clear();
    }

    public void setPlayIndex(int index) {
        play(index, false);
        // 因为只会在playstart的时候才会保存播放列表, 所以这里也进行一次保存
        if (mHistoryManager != null) {
            mHistoryManager.savePlayList();
        }
    }

    public void savePlayList() {
        if (mHistoryManager != null) {
            mHistoryManager.savePlayList();
        }
    }

    public boolean isVideoPlaying() {
        return isVideoPlaying;
    }

    public boolean isVideoMode() {
        boolean canControlByNotification = ConfigureCenter.getInstance().getBool("android", "can_video_play_by_notification", false);
        if (!canControlByNotification) {
            isVideoMode = false;
        }
        return isVideoMode;
    }

    public String getTicket(int ticketType) {
        String ticket = null;
        ITrace service = RouterServiceManager.getInstance().getService(ITrace.class);
        if (service != null) {
            ticket = service.getTicket(ticketType);
            Logger.d(TAG, "getTicket ticketType=" + ticketType + ",ticket=" + ticket);
        } else {
            Logger.d(TAG, "getTicket ticketType=" + ticketType + ",service=null");
        }
        if (TextUtils.isEmpty(ticket)) {
            IXmCommonBusinessDispatcher callback = getIXmCommonBusinessDispatcher();
            if (callback != null) {
                try {
                    ticket = callback.getNewTicket(ticketType);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            } else {
                Logger.d(TAG, "XmPlayerService getTicket callback null");
            }
        }
        if (TextUtils.isEmpty(ticket)) {
            ticket = TicketConstantsKt.DEFAULT_TICKET;
        }
        return ticket;
    }

    public void completePlay() {
        IXmCommonBusinessDispatcher callback = getIXmCommonBusinessDispatcher();
        if (callback != null) {
            try {
                callback.completePlay();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }
}
