package com.ximalaya.ting.android.opensdk.player.service;

import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.List;

/**
 * Created by roc on 2017/6/2.
* <AUTHOR>
 */

public interface IXmCommonBusinessHandle {

    String getDownloadPlayPath(Track track);
    void isOldTrackDownload(Track track);
    void closeApp();
    boolean userIsVip();
    boolean isUseNewPlayFragment();
    boolean isPlayFragmentForeground();
    boolean lockScreenActivityIsShowing();

    long updateGDTRTBToken();
    long updateGDTRTBSdkInfo(String dspId);

    long updateCSJRTBToken(int thirdAdType, String dspId, int adType);

    void batchAdRecord(List thirdAdList, List adReportModel);

    String queryConfigCenter(int queryType, String groupName, String itemName);

    String syncBatchGetToken(List adRtbModelList, int thirdAdType);

    String syncBatchGetGdtSdkInfo(List adRtbModelList);

    void callForTraceMarkPoint(int metaId, String paramJson);

    void callForShowUniversalPaymentActionDialog(Track track);
    void notifyFreeListenTimeOut();
    boolean isPlayingFreeListenAd();

    String getNewTicket(int type);

    void setThirdUidAndAppKey(String thirdUid, String appKey);

    void completePlay();
}
