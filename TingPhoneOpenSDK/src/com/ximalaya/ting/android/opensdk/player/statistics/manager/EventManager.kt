package com.ximalaya.ting.android.opensdk.player.statistics.manager

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService
import com.ximalaya.ting.android.opensdk.player.statistics.model.PlayStatisticsRecord
import com.ximalaya.ting.android.opensdk.player.statistics.util.PlayStatisticsUtil
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger

/**
 * <AUTHOR>
 * @date 2023/3/7 22:03
 */
private const val TAG = "XmStatisticsEventManager"

private const val MSG_TRACK_UPLOAD = 1

class EventManager private constructor() {
    companion object {
        private val sEventManager = EventManager()

        fun getInstance() = sEventManager
    }

    private var mHandler: Handler? = null
    private var mCurPlayRecord: PlayStatisticsRecord? = null
    private var mIntervalCheckTime = 5 * 60_000L
    var mXdcsPlayedPosition = 0L
    var mProcessKilledUploaded = false

    init {
        mHandler = object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                when (msg.what) {
                    MSG_TRACK_UPLOAD -> heartBeat(mCurPlayRecord)
                }
            }
        }
        val intervalTime = MMKVUtil.getInstance().getInt(
            PreferenceConstantsInOpenSdk.KEY_HEART_BEAT_INTERVAL_TIME, 5 * 60
        ).toLong()
        mIntervalCheckTime = if (intervalTime > 0) intervalTime * 1000 else 5 * 60_000
        Logger.d(TAG, "mIntervalCheckTime=$mIntervalCheckTime")
    }

    private fun postCheck(record: PlayStatisticsRecord?) {
        mCurPlayRecord = null
        mHandler?.removeMessages(MSG_TRACK_UPLOAD)
        record ?: return
        mCurPlayRecord = record
        mHandler?.sendEmptyMessageDelayed(MSG_TRACK_UPLOAD, mIntervalCheckTime)
    }

    private fun clearHeartBeatCheck() {
        mCurPlayRecord = null
        mHandler?.removeMessages(MSG_TRACK_UPLOAD)
    }

    private fun processOnStartPlay(record: PlayStatisticsRecord?, startPos: Int) {
        record ?: return
        record.totalPlayedDuration += record.playedDuration
        record.totalListenedDuration += record.listenedDuration
        record.playedDuration = 0
        record.listenedDuration = 0F

        record.curRecordUploaded = false
        // 记住下开始播放的时间
        record.updateStartTime(startPos)

        record.clearPauseReason()

        // 心跳检测
        postCheck(record)
    }

    private fun savePlayRecord(record: PlayStatisticsRecord?) {
        PlayStatisticsRecordManager.getInstance().savePlayRecord(record)
    }

    private fun startPlay(event: String, record: PlayStatisticsRecord?) {
        PlayStatisticsUtil.getStatisticsCallback()?.startPlay(event, record)
    }

    private fun stopPlay(event: String, record: PlayStatisticsRecord?, playedDuration: Long = 0) {
        PlayStatisticsUtil.getStatisticsCallback()?.stopPlay(event, record, playedDuration)
    }

    private fun processStarted(event: String, record: PlayStatisticsRecord?, playedDuration: Long) {
        PlayStatisticsUtil.getStatisticsCallback()?.processStarted(event, record, playedDuration)
    }

    // 起播
    fun onPlayStart(record: PlayStatisticsRecord?, position: Int) {
        Logger.i(TAG, "EventManager 起播，跳过头尾=${record?.headSkip}, head=${record?.head}, ${record?.trackTitle}")
        processOnStartPlay(record, position)
        savePlayRecord(record)
        startPlay("启播", record)
    }

    // 暂停 -> 起播
    fun pauseToStart(record: PlayStatisticsRecord?, position: Int) {
        Logger.i(TAG, "EventManager 暂停 -> 起播 ${record?.trackTitle}")
        processOnStartPlay(record, position)
        savePlayRecord(record)
        startPlay("暂停_播放", record)
    }

    // 杀进程 -> 继续播放
    fun processKilledToStart(record: PlayStatisticsRecord?, startPos: Int) {
        Logger.i(TAG, "EventManager 杀进程 -> 继续播放 ${record?.trackTitle}")
        processOnStartPlay(record, startPos)
        savePlayRecord(record)
        startPlay("杀进程_继续播放", record)
    }

    private fun processOnPausePlay(record: PlayStatisticsRecord?, pausePos: Int) {
        record ?: return
        record.totalPlayedDuration += record.playedDuration
        record.totalListenedDuration += record.listenedDuration
        record.playedDuration = 0
        record.listenedDuration = 0F
        // 记住下开始播放的时间
        record.updateEndTime(pausePos)
    }

    // 杀进程 -> 结束播放
    fun processKilled(record: PlayStatisticsRecord?) {
        if (record?.curRecordUploaded == false) {
            record.curRecordUploaded = true
            Logger.i(TAG, "EventManager 杀进程 -> 结束播放 ${record?.trackTitle}")
            stopPlay("杀进程", record)
            mProcessKilledUploaded = true
        }
        savePlayRecord(record)
        clearHeartBeatCheck()
    }

    // 进程启动，如果上次的播放状态不是正在播放的状态，则上报埋点
    fun processStarted(record: PlayStatisticsRecord?, playedDuration: Long) {
//        if (record?.curRecordUploaded == false) {
            record?.curRecordUploaded = true
            Logger.i(TAG, "EventManager 杀进程 -> 结束播放 2 ${record?.trackTitle}")
            processStarted("杀进程_启动", record, playedDuration)
//        }
//        savePlayRecord(record)
//        clearHeartBeatCheck()
    }

    // 起播 -> 暂停
    fun startToPause(record: PlayStatisticsRecord?, position: Int, uploadTrace: Boolean = true) {
        Logger.i(TAG, "EventManager 起播 -> 暂停 ${record?.trackTitle}")
        record?.updateEndTime(position)
        if (uploadTrace) {
            stopPlay("暂停", record)
        }
        // 这个要在上报埋点后执行
        processOnPausePlay(record, position)
        savePlayRecord(record)
        clearHeartBeatCheck()
    }

    // 切歌
    fun soundSwitch(record: PlayStatisticsRecord?, position: Int?, uploadTrace: Boolean = true) {
        Logger.i(TAG, "EventManager 切歌 ${record?.trackTitle}")
        if (position != null && position > 0) {
            record?.updateEndTime(position)
        }
        savePlayRecord(record)
        if (uploadTrace) {
            stopPlay("切歌", record, mXdcsPlayedPosition)
        }
        clearHeartBeatCheck()
    }

    // 播放完
    fun playComplete(record: PlayStatisticsRecord?, tailSkip: Boolean, tail: Int) {
        Logger.i(TAG, "EventManager 播放完，跳过头尾=$tailSkip, tail=$tail, ${record?.trackTitle}")
        savePlayRecord(record)
        stopPlay("播放完", record)
        clearHeartBeatCheck()
    }

    fun heartBeat(record: PlayStatisticsRecord?) {
        record ?: return
        val isPlaying = XmPlayerService.getPlayerSrvice()?.isPlaying == true
        val check = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_HEART_BEAT_CHECK_PLAYING, false)
        if (check && !isPlaying) {
            val curTrack = XmPlayerService.getPlayerSrvice()?.currPlayModel as? Track?
            val trackMsg = "id=${curTrack?.dataId},trackTitle=${curTrack?.trackTitle}"
            Logger.e(TAG, "heartBeat error = $trackMsg")
            if (ConstantsOpenSdk.isDebug || PlayStatisticsUtil.getStatisticsCallback()?.isBetaVersion == true) {
                try {
                    val xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost::class.java)
                    xdcsPost?.statErrorToXDCS("HeartBeatErrorUpload", "$trackMsg")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            Logger.i(TAG, "EventManager 心跳上报")
            mHandler?.sendEmptyMessageDelayed(MSG_TRACK_UPLOAD, mIntervalCheckTime)
            savePlayRecord(record)
            PlayStatisticsUtil.getStatisticsCallback()?.heartBeat("心跳上报", record, mIntervalCheckTime)
        }
    }

    // 更新进度
    fun updateProgress(
        record: PlayStatisticsRecord,
        playedDuration: Long, listenedNatureDuration: Float, duration: Int
    ) {
        if (duration <= 0) {
            return
        }
        Logger.d(
            TAG, "updateProgress playedDuration=${(playedDuration + record.lastPlayedDuration) / 1000}, " +
                    "listenedNatureDuration=${(listenedNatureDuration + record.lastListenedDuration) / 1000}, ${record.trackTitle}"
        )
        var checkTime = true
        val playDuration = record.lastPlayedDuration + playedDuration
        if (!record.validPlay) {
            if (playDuration >= 240_000 || (playDuration / 1000f / duration >= 0.4f)) {
                checkTime = false
                record.validPlay = true
                validPlay(record)
            }
        }
        if (!record.completePlay) {
            if ((duration <= 600 && playDuration / 1000f / duration >= 0.9f) || (duration >= 600 && playDuration >= 540_000)) {
                checkTime = false
                record.completePlay = true
                completePlay(record)
            }
        }
        PlayStatisticsRecordManager.getInstance().savePlayRecord(record, checkTime)
    }

    // 有效播放
    // 一次播放记录中，声音播放时长>240s;或者播放时长/声音时长大于40%
    private fun validPlay(record: PlayStatisticsRecord) {
        Logger.i(TAG, "EventManager 有效播放统计 ${record?.trackTitle}")
        PlayStatisticsUtil.getStatisticsCallback()?.validPlay(record)
    }

    // 完播埋点
    // 一次播放记录中，如果声音本身<=600s,播放时长/声音时长大于90%；或者声音本身>600s,播放时长超过540s
    private fun completePlay(record: PlayStatisticsRecord) {
        Logger.i(TAG, "EventManager 播放完统计 ${record?.trackTitle}")
        PlayStatisticsUtil.getStatisticsCallback()?.completePlay(record)

        XmPlayerService.getPlayerSrvice()?.completePlay()
    }

    fun clearRecord() {
        Logger.i(TAG, "EventManager clearRecord")
        PlayStatisticsRecordManager.getInstance().clearRecord()
    }
}