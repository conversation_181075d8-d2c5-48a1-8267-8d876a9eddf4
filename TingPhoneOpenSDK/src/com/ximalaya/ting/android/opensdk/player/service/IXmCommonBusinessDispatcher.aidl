/***********************************************************************
 * Module:  IXmPlayerEventDispather.aidl
 * Author:  chadwii
 * Purpose: Defines the Interface IXmPlayerEventDispather
 ***********************************************************************/
package com.ximalaya.ting.android.opensdk.player.service;

import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.SkipHeadTailModel;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.manager.ad.adrtb.AdRtbItemModel;

interface IXmCommonBusinessDispatcher
{
	
	String getDownloadPlayPath(in Track track);
    void isOldTrackDownload(in Track track);
    void closeApp();
    void onSetHistoryToPlayer();
    // 超高清音质没有授权
    void onHightPlusNoAuthorized(in Track track);
    boolean userIsVip();
    boolean isUseNewPlayFragment();
    boolean lockScreenActivityIsShowing();
    // 视频广告，只有主进程可以得到相关信息，
    // return: 是否成功起播了视频广告
    boolean checkAndPlayPausedVideoAd();
    // return: 是否正在播放视频广告
    boolean isPlayingVideoAd();
    // return: 是否在播放畅听广告
    boolean isPlayingFreeListenAd();
    // 应用在前台，并且在显示播放页
    boolean isPlayFragmentForeground();
    void onSkipHeadTailModelFetched(in SkipHeadTailModel skipHeadTailModel);

    long updateGDTRTBToken();
    long updateGDTRTBSdkInfo(String dspId);

    long updateCSJRTBToken(int thirdAdType, String dspId, int adType);

    // 同步批量获取token
    String syncBatchGetToken(in List<AdRtbItemModel> adRtbModelList, int thirdAdType);

    String syncBatchGetGdtSdkInfo(in List<AdRtbItemModel> adRtbModelList);

    void batchAdRecord(in List thirdAdList,in List adReportModel);

    String queryConfigCenter(int queryType, in String groupName, in String itemName);

    void callForTraceMarkPoint(int metaId, String paramJson);

    void callForShowUniversalPaymentActionDialog(in Track track);
    void notifyFreeListenTimeOut();
    void playToListenRecommendList(boolean playToListenRecommendList);
    void addToListenForLastTrack(in Track track);
    void deleteLastUnCompleteToListen(boolean isToListen, long trackId);
    void preRequestToListenRecommend();

    String getNewTicket(int type);

    void setThirdUidAndAppKey(String thirdUid, String appKey);

    void showToast(String message, long duration);

    void mediasessionClick(int ms);

    void completePlay();
}