package com.ximalaya.ting.android.opensdk.player;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;
import android.widget.Toast;

import com.google.gson.Gson;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.firework.FireworkApi;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.SharedModelConstants;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeCollectUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeoutFixUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeoutMonitor;
import com.ximalaya.ting.android.opensdk.model.BaseInfoOnErrorModel;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.SkipHeadTailModel;
import com.ximalaya.ting.android.opensdk.model.ad.AdPreviewModel;
import com.ximalaya.ting.android.opensdk.model.ad.SoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.configure.ConfigWrapItem;
import com.ximalaya.ting.android.opensdk.model.dailyNews.DailyVoteVO;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.live.schedule.Schedule;
import com.ximalaya.ting.android.opensdk.model.soundEffect.SoundEffectInfo;
import com.ximalaya.ting.android.opensdk.model.soundpatch.SimpleSoundPatchInfo;
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel;
import com.ximalaya.ting.android.opensdk.model.task.TaskStatusInfo;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.xdcs.CdnConfigModel;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListenerExpand;
import com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.flv.FlvData;
import com.ximalaya.ting.android.opensdk.player.flv.FlvMetaInfo;
import com.ximalaya.ting.android.opensdk.player.flv.IFlvDataOutput;
import com.ximalaya.ting.android.opensdk.player.flv.SEIParserUtil;
import com.ximalaya.ting.android.opensdk.player.manager.CrossProcessTransferValueManager;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundEffectStatusDispatcher;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundPatchStatusDispatcher;
import com.ximalaya.ting.android.opensdk.player.manager.ISoundPatchStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.IAidlRecordNew;
import com.ximalaya.ting.android.opensdk.player.service.IFreeListenTimeListener;
import com.ximalaya.ting.android.opensdk.player.service.IListenTimeListener;
import com.ximalaya.ting.android.opensdk.player.service.IMixPlayerEventDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IMixPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmAdsEventDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessHandle;
import com.ximalaya.ting.android.opensdk.player.service.IXmDataCallback;
import com.ximalaya.ting.android.opensdk.player.service.IXmFlvDataCallback;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlanTerminateListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayHistoryListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayer;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerBaseInfoRequestDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerBaseInfoRequestListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerEventDispatcher;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerFreeListenTimeListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerListenTimeListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListenerExtension;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerTrackInfoListener;
import com.ximalaya.ting.android.opensdk.player.service.SyncTimeCallbackAdapter;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl.PlayMode;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixTrack;
import com.ximalaya.ting.android.opensdk.player.soundEffect.ISoundEffectStatusCallBackForMainManager;
import com.ximalaya.ting.android.opensdk.player.soundpatch.ICommercialSoundPatchControlStatusCallBack;
import com.ximalaya.ting.android.opensdk.player.soundpatch.ICommercialSoundPatchOperationCallBack;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics;
import com.ximalaya.ting.android.opensdk.player.ubt.ISourceSynchronizer;
import com.ximalaya.ting.android.opensdk.player.ubt.IUbtSourceProvider;
import com.ximalaya.ting.android.opensdk.player.ubt.IUbtSourceSynchronizer;
import com.ximalaya.ting.android.opensdk.player.ubt.TraceModel;
import com.ximalaya.ting.android.opensdk.player.ubt.UbtSourceEmptyCollector;
import com.ximalaya.ting.android.opensdk.usetrace.UseTraceCollector;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.Constants;
import com.ximalaya.ting.android.opensdk.util.EasyConfigure;
import com.ximalaya.ting.android.opensdk.util.HandlerPlayerProcessDiedUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.ModelUtil;
import com.ximalaya.ting.android.opensdk.util.MyClubUtil;
import com.ximalaya.ting.android.opensdk.util.PlayListMMKVUtil;
import com.ximalaya.ting.android.opensdk.util.PlayModeUtilKt;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.opensdk.util.ToListenUtil;
import com.ximalaya.ting.android.opensdk.util.TrackUtil;
import com.ximalaya.ting.android.player.PlayerUtil;
import com.ximalaya.ting.android.player.XMediaplayerImpl;
import com.ximalaya.ting.android.player.XMediaplayerJNI;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.base.IXmDataChangedCallback;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

//import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/**
 * XmPlayerManager.java
 *
 *
 *   ver     date      		author
 * ---------------------------------------
 *   		 2015-4-2 		chadwii
 *
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

/**
 * ClassName:XmPlayerManager
 *
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-4-2 下午4:09:14
 *
 * @seew
 */
public class XmPlayerManager {
	private static final String TAG = "XmPlayerServiceManager";

	private static final int MSG_PLAY_START = 1;
	private static final int MSG_PLAY_PAUSE = 2;
	private static final int MSG_PLAY_STOP = 3;
	private static final int MSG_PLAY_COMPLETE = 4;
	private static final int MSG_SOUND_PREPARED = 5;
	private static final int MSG_BUFFER_STATUS_CHANGE = 6;
	private static final int MSG_PROGRESS_CHANGE = 7;
	private static final int MSG_SOUND_CHANGE = 8;
	private static final int MSG_BUFFER_PROGRESS_CHANGE = 9;
	private static final int MSG_PLAY_ERROR = 10;

	private static final int MSG_START_GET_ADS_INFO = 12;
	private static final int MSG_GET_ADS_INFO = 13;
	private static final int MSG_ADS_BUFFERING_START = 14;
	private static final int MSG_ADS_BUFFERING_STOP = 15;
	private static final int MSG_START_PLAY_ADS = 16;
	private static final int MSG_COMPLETE_PLAY_ADS = 17;
	private static final int MSG_PLAY_ADS_ERROR = 18;

	private static final int MSG_OLD_CHARGE_TRACK_NEED_REDOWNLOAD = 19;
	private static final int MSG_CLOSE_APP = 20;
	private static final int MSG_HISTORY_CHANGED = 21;
	private static final int MSG_FLV_DATA_OUT = 22;

	private static final int MSG_MIX_START = 24;
	private static final int MSG_MIX_PAUSE = 25;
	private static final int MSG_MIX_STOP = 26;
	private static final int MSG_MIX_COMPLETE = 27;
	private static final int MSG_MIX_ERROR = 28;
	private static final int MSG_MIX_PROGRESS_UPDATE = 29;
	private static final int MSG_MIX_STATUS_UPDATE = 30;
	private static final int MSG_MIX_SOUND_COMPLETE = 31;

	private static final int MSG_LOAD_HISTORY_SUCCESS = 32;    // 加载历史成功

	private static final int MSG_SOUND_PATCH_START = 33;
	private static final int MSG_SOUND_PATCH_COMPLETE = 34;
	private static final int MSG_SOUND_PATCH_ERROR = 35;
	private static final int MSG_ON_HIGHTPLUS_NO_AUTHORIZED = 36;    // 超高清音质没有授权

	private static final int MSG_ON_REQUEST_PLAY_URL_BEGIN = 37;    // 开始请求播放地址
	private static final int MSG_ON_REQUEST_PLAY_URL_SUCCESS = 38;    // 请求播放地址成功
	private static final int MSG_ON_REQUEST_PLAY_URL_FAIL = 39;    // 请求播放地址成功

	private static final int MSG_ON_GET_FORWARD_VIDEO = 40;    // 前插视频
	private static final int MSG_ON_AUDIO_AUDITION_OVER = 41;    // 试听结束

	private static final int MSG_BSAE_INFO_SUCCESS = 42;    // baseinfo请求成功
	private static final int MSG_BSAE_INFO_ON_ERROR = 43;    // baseinfo请求失败

	private static final int MSG_COMMERCIAL_SOUND_PATCH_STATUS_CHANGE = 44;         // 商业化贴片状态变更
	private static final int MSG_COMMERCIAL_SOUND_PATCH_RE_REGISTER = 45;            // 重新注册商业化贴片

	private static final int MSG_SKIP_HEAD_TAIL_MODEL_FETCH = 46; // 获取到跳过片头片尾数据

	private static final int MSG_MIX_TRACK_CLEARED = 47;

	private static final int MSG_PLAN_TERMINATE_ON_TIME_OUT = 48;
	private static final int MSG_PLAN_TERMINATE_ON_LEFT_TIME_CHANGED = 49;
	private static final int MSG_PLAN_TERMINATE_ON_LEFT_SERIES_CHANGED = 50;
	private static final int MSG_PLAN_TERMINATE_ON_CANCEL = 51;

	private static final int MSG_ON_RENDERING_START = 52;
	private static final int MSG_ON_VIDEO_SIZE_CHANGED = 53;
	private static final int MSG_ON_VIDEO_ROTATION_CHANGED = 54;

	private static final int MSG_SOUND_EFFECT_START = 55;
	private static final int MSG_SOUND_EFFECT_ERROR = 56;
	private static final int MSG_SOUND_EFFECT_END = 57;
	private static final int MSG_SOUND_EFFECT_INITIAL_INFO = 58;

	private static final int MSG_FLV_META_DATA_INFO = 59;

	private static final int MSG_ON_CHILD_AI_TIMBRE_URL_GET = 60; // 儿童音色地址获取状态通知

	private static final int MSG_FREE_LISTEN_TIME_CHANGE = 61;
	private static final int MSG_FREE_LISTEN_TIME_EMPTY = 62;
	private static final int MSG_FREE_LISTEN_ALL_DAY_EXPIRE = 63;
	private static final int MSG_HISTORY_INITED = 64;

    private static final int MSG_PLAN_TERMINATE_ON_TIME_OUT_FOR_QUICK_LISTEN = 65;
    private static final int MSG_PLAN_TERMINATE_ON_LEFT_TIME_CHANGED_FOR_QUICK_LISTEN = 66;
    private static final int MSG_PLAN_TERMINATE_ON_LEFT_SERIES_CHANGED_FOR_QUICK_LISTEN = 67;
    private static final int MSG_PLAN_TERMINATE_ON_CANCEL_FOR_QUICK_LISTEN = 68;
	private static final int MSG_LISTEN_TIME_CHANGE = 69;

	private volatile static XmPlayerManager sInstance;
	private static final byte[] sLock = new byte[0];
	private IXmPlayer mOldPlayerStub;
	private IXmPlayer mPlayerStub;
	private Context mAppCtx;
	private boolean mConnected = false;
	private boolean mBindRet = false;
	private UIHandler mUiHandler;

	private Boolean mConnectedBool = null;

	private static int sPageSize = DTransferConstants.DEFAULT_PAGE_SIZE;

	private static Config sHttpConfig;
	private boolean checkAdContent;
	private boolean mHandleHistoryListChanged;
	private boolean mIsTingTextVisible = false;

	// private IXmAutoLoadCallback mAutoLoadCallback;
	private List<IXmPlayerStatusListener> mPlayerStatusListeners = new CopyOnWriteArrayList<IXmPlayerStatusListener>();
	private List<IFreeListenTimeListener> mFreeListenTimeListeners = new CopyOnWriteArrayList<>();
	private List<IListenTimeListener> mListenTimeListeners = new CopyOnWriteArrayList<>();
	private List<IMixPlayerStatusListener> mMixPlayerStatusListeners = new CopyOnWriteArrayList<IMixPlayerStatusListener>();
	private List<IXmAdsStatusListener> mAdsStatusListeners = new CopyOnWriteArrayList<IXmAdsStatusListener>();
	private List<ISoundPatchStatusListener> mSoundPatchStatusListeners = new CopyOnWriteArrayList<ISoundPatchStatusListener>();
	private List<ISoundEffectStatusCallBackForMainManager> mSoundEffecttatusListeners = new CopyOnWriteArrayList<ISoundEffectStatusCallBackForMainManager>();
	private List<ICommercialSoundPatchControlStatusCallBack> mSoundPatchControlStatusCallBacks = new CopyOnWriteArrayList<>();
	private List<IXmPlayerBaseInfoRequestListener> mBaseInfoRequestListeners = new CopyOnWriteArrayList<IXmPlayerBaseInfoRequestListener>();
	private List<IXmDataChangedCallback> mHistoryChangedCallbacks = new CopyOnWriteArrayList<>();
	private List<IXmPlayerTrackInfoListener> mPlayerTrackInfoListeners = new CopyOnWriteArrayList<IXmPlayerTrackInfoListener>();
	private CopyOnWriteArrayList<IOnPlayListChange> mPlayListChanges = new CopyOnWriteArrayList<>();
	private final Set<IOnHistoryListLoadSuccess> mHistoryListLoadSuccess = new CopyOnWriteArraySet<>();
	private final Set<IOnPlayModeChange> mOnPlayModeChanges = new CopyOnWriteArraySet<>();
	private final Set<IOnPlayOrderChange> mOnPlayOrderChanges = new CopyOnWriteArraySet<>();

	private final List<IFlvDataOutput> mFlvDataOutputList = new CopyOnWriteArrayList<>();
	private ICommercialSoundPatchOperationCallBack mCommercialSoundPatchOperationCallBack;
	private IUbtSourceProvider mUbtSourceProvider;
	private ISourceSynchronizer mUbtSourceSynchronizer;

	public static boolean ignoreRequestFocus = false;
	private int trackPlayQuality = Track.TRACK_DEFUALT_QUALITY;
	private int mCellularQuality = Track.TRACK_DEFUALT_QUALITY;

	/**
	 * tts和儿童都在使用
	 */
	private String trackByTimbreType;
	public static final String ACTION_HIGHTPLUS_NO_AUTHORIZED = "action_hightplus_no_authorized";
	public static final String INTENT_ACTION_SKIP_HEAD_TAIL_FETCH = "intent_action_skip_head_tail_fetch";
	public static final String KEY_SKIP_HEAD_TAIL_FETCH = "key_skip_head_tail_fetch";

	private int playSource;
	private long bookId;
	private boolean isPlayingForEBook;	//是否正在播放，本地小说用
	private boolean needUpdateAdFreeData = false;
	private boolean mTempPodCastPlayListMode = false;

	private ServiceConnection mConn = new ServiceConnection() {

		@Override
		public void onServiceDisconnected(ComponentName name) {
			Logger.i(TAG, "onServiceDisconnected");
			UseTraceCollector.log("XmPlayerManager: onServiceDisconnected");
			mConnected = false;
			mConnectedBool = false;

			try {
				mAppCtx.unbindService(mConn);
			} catch (Exception e) {
				//https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/24896946?pid=1
				e.printStackTrace();
			}
			mBindRet = false;

			try {
				if (mDisconnectListenerSet.size() > 0) {
					IDisconnectListener[] arr = new IDisconnectListener[mDisconnectListenerSet.size()];
					mDisconnectListenerSet.toArray(arr);
					for (int i = 0; i < arr.length; i++) {
						IDisconnectListener l = arr[i];
						l.onDisconnected();
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}


			HandlerPlayerProcessDiedUtil.onServiceDisconnected();

			// 如果是播放进程自己主动杀掉，并且在5s内，那么会自动播放
			long lastKillTime = MmkvCommonUtil.getInstance(mAppCtx).getLong(
					PreferenceConstantsInOpenSdk.KEY_KILL_PROCESS_FOR_UNLIMITED_LOADING_TIME,
					-1);
			if (System.currentTimeMillis() - lastKillTime <= 5_000) {
				HandlerPlayerProcessDiedUtil.mLastIsPlaying = true;
				HandlerPlayerProcessDiedUtil.restartService = true;
				HandlerPlayerProcessDiedUtil.restartTime = System.currentTimeMillis();
			}

			if (HandlerPlayerProcessDiedUtil.mLastIsPlaying || BaseUtil.isAppForeground(mAppCtx)) {
				init(true);
			}
		}

		@Override
		public void onServiceConnected(ComponentName name, IBinder service) {
			try {
				Logger.i(TAG, "onServiceConnected progress:"+ android.os.Process.myPid());
				UseTraceCollector.log("XmPlayerManager: onServiceConnected");
				mConnected = true;
				mBindRet = true;
				mConnectedBool = true;

                mPlayerStub = IXmPlayer.Stub.asInterface(service);
				mPlayerStub.registerFreeListenTimeListener(mFreesListenerStub);
				mPlayerStub.registerListenTimeListener(mListenTimeListenerStub);
				mPlayerStub.registePlayerListener(mListenerStub);
				mPlayerStub.registeAdsListener(mAdsListenerStub);
				mPlayerStub.registerSoundPatchStateListener(mSoundPatchStatusDispatcher);
				mPlayerStub.registerSoundEffectStatusListener(mSoundEffectStatusDispatcher);
				mPlayerStub.registerBaseInfoListener(mBaseInfoRequestDispatcher);
				mPlayerStub.registePlayHistoryListener(mHistoryListenerStub);
				mPlayerStub.registeMixPlayerListener(mMixListenerStub);
				mPlayerStub.setVolumeBalance(volumnBalanceIsOpen);
//				mPlayerStub.setTrackPlayQualityLevel(trackPlayQuality, mCellularQuality);
				if (!mFlvDataOutputList.isEmpty()) {
					mPlayerStub.setFlvDataCallBack(mFlvDataCallbackStub);
				}

				try {
					mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_OAID , OAID);
					mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_ENCRYPT_OAID , ENCRYPT_OAID);
					if(isChildMode != null) {
						mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_CHILD_PROTECT_IS_OPEN , isChildMode + "");
					}

					if(soundPatchTimeoutMs != null) {
						mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SOUND_PATCH_TIMEOUT_MS ,
								soundPatchTimeoutMs + "");
					}

					if(followHeartConfig != null) {
						mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_FOLLOW_HEART_CONFIG ,
								followHeartConfig + "");
					}

					if(progressInterval != null) {
						mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_SAVE_PROGRESS ,
								progressInterval + "");
					}

					if(useWakeLockConfig != null) {
						mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_USE_WAKELOCK_CONFIG ,
								useWakeLockConfig + "");
					}

					if(playErrRetry != null) {
						mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_PLAY_ERR_RETRY ,
								playErrRetry + "");
					}

                    if (listenTaskIntervalTime != null) {
                        mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_LISTEN_TASK_INTERVAL_TIME, listenTaskIntervalTime + "");
                    }

                    if (listenDataIntervalTimeNew != null) {
                        mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_LISTEN_TASK_INTERVAL_TIME_NEW, listenDataIntervalTimeNew + "");
                    }

                    if (mIsTargetUser != null) {
                        mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.ITEM_AD_MARK_TARGET_USER, mIsTargetUser + "");
                    }

					if(mLastLeftVolume != null && mLastRightVolume != null) {
						mPlayerStub.setVolume(mLastLeftVolume, mLastRightVolume);
					}

                    if(mThirdUidValue != null && mThirdUidAppKey != null) {
                        mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_THIRD_UID_THIRD_UID_VALUE, mThirdUidValue);
                        mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_THIRD_UID_APPKEY, mThirdUidAppKey);
                        mThirdUidValue = null;
                        mThirdUidAppKey = null;
                    }
				} catch (Exception e) {
					e.printStackTrace();

					if (ConstantsOpenSdk.isDebug) {
						throw e;
					}
				}

				if(BaseUtil.isMainProcess(mAppCtx)) {
					mPlayerStub.setPlayerProcessRequestEnvironment(mEnvironment);
					mPlayerStub
							.registeCommonBusinessListener(mIXmCommonBusinessDispatcherStub);
					mPlayerStub.setPlayListChangeListener(mDataCallbackSub);
					mPlayerStub.setPlanTerminateListener(mPlanTerminateCallbackSub);
                    mPlayerStub.setPlanTerminateListenerForQuickListen(mPlanTerminateCallbackSubForQuickListen);
				}

				if(mTempo != null) {
					mPlayerStub.setTempo(mTempo);
				}

                if(mQuickListenTempo != null) {
                    mPlayerStub.setQuickListenTempo(mQuickListenTempo);
                }

				mPlayerStub.setElderlyMode(isElderlyModel);

				try {
					mPlayerStub.setPlayFragmentIsShowing(XmAdsManager.isPlayFragmentShowing);
				} catch (Exception e) {
					e.printStackTrace();
				}

				setPlayerProxy();
				setDefaultPageSize();
				Logger.i(TAG, "onServiceConnected123");

				try {
					IConnectListener[] arr = new IConnectListener[mConnectListenerSet.size()];
					mConnectListenerSet.toArray(arr);
					for (int i = 0; i < arr.length; i++) {
						IConnectListener l = arr[i];
						l.onConnected();
					}
					mConnectListenerSet.clear();
				} catch (Exception e) {
					e.printStackTrace();
				}

				if(mConnectListener != null){
					mConnectListener.onConnected();
				}

                mCurModel = mPlayerStub.getCurTrack();
				printCurModel("onServiceConnected");

				boolean isCheckAdContent = checkAdContent;
				if(ConstantsOpenSdk.isDebug) {
					isCheckAdContent = SharedPreferencesUtil.getInstance(mAppCtx).getBoolean(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_USE_CHECK_AD_CONTENT, true) && checkAdContent;
				}
				mPlayerStub.setCheckAdContent(isCheckAdContent);

				// 产品说这个逻辑去掉
//				// 在连接service成功后，获取焦点，执行清场操作，之前放在playService中进行操作，但是service被后台杀死后，然后重启会造成其他播放器停止播放，所以放在界面显示时在显示
//				if (!mPlayerStub.isPlaying() && !mPlayerStub.isAdPlaying() && !ignoreRequestFocus) {
//					mPlayerStub.setAudioFocusAtStartStateAtTransient();
//				}

				//miui10以上的手机有智能预加载功能所以不会在onCreate中设置通知栏。
				//由于小米系统的问题，进程退出通知栏不会消失所以重启后通知栏会显示异常。
				//这里通过onServiceDisconnected记录异常退出的情况，并重新设置一次通知栏。

				HandlerPlayerProcessDiedUtil.onServiceConnectedAndLastIsKilled(mAppCtx);
				//在被杀重启时，不判断前后台状态，直接添加通知栏。
				//避免startForeground调用太晚导致的crash.

				if (mChannelJumpOver) {
					mPlayerStub.setChannelJumpOver(mChannelJumpOver);
					mChannelJumpOver = false;
				}
				mPlayerStub.setUbtSourceSynchronizer(mSynchronizerStub);

				if (needUpdateAdFreeData) {
					mPlayerStub.updateAdFreeData();
				}
				needUpdateAdFreeData = false;
			} catch (Exception e) {
				e.printStackTrace();

				if(ConstantsOpenSdk.isDebug) {
					throw new RuntimeException(e);
				}

                CdnUtil.statToXDCSError("player_connect_error" ,e.getMessage());
			}

		}
	};

	/**
	 * 测试环境下打印当前播放的track信息
	 * @param tag
	 */
	private void printCurModel(String tag) {
		if (!ConstantsOpenSdk.isDebug) {
			return;
		}
		Track track = (Track) mCurModel;
		if (track == null) {
			Logger.d("z_read", tag + " mCurModel is null  " + Log.getStackTraceString(new Throwable()));
		} else {
			Logger.d("z_read", tag + " playSource: " + track.getPlaySource() + ", kind: " + track.getKind() + Log.getStackTraceString(new Throwable()));
		}
	}

	public interface IConnectListener {
		public void onConnected();
	}


	public interface IDisconnectListener {
		public void onDisconnected();
	}


	private Set<IConnectListener> mConnectListenerSet = new HashSet<>();

	public void addOnConnectedListerner(IConnectListener l) {
		if (l == null) {
			return;
		}
		if (isConnected()) {
			l.onConnected();
			return;
		}
		mConnectListenerSet.add(l);
	}

	public void removeOnConnectedListerner(IConnectListener l) {
		mConnectListenerSet.remove(l);
	}

	private Set<IDisconnectListener> mDisconnectListenerSet = new HashSet<>();

	public void addOnDisconnectedListerner(IDisconnectListener l) {
		if (l == null) {
			return;
		}
		mDisconnectListenerSet.add(l);
	}

	public void removeOnDisConnectedListerner(IDisconnectListener l) {
		mDisconnectListenerSet.remove(l);
	}


	private IConnectListener mConnectListener;
	/**
	 * 因为开放平台很多第三方已经接入所以做个兼容
	 */
	@Deprecated
	public void setOnConnectedListerner(IConnectListener l) {
		mConnectListener = l;
	}

	private IXmCommonBusinessDispatcher.Stub mIXmCommonBusinessDispatcherStub = new IXmCommonBusinessDispatcher.Stub() {

		@Override
		public String getDownloadPlayPath(Track track) throws RemoteException {

			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle
						.getDownloadPlayPath(track);
			}

			return null;
		}

		@Override
		public void isOldTrackDownload(Track track) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_OLD_CHARGE_TRACK_NEED_REDOWNLOAD);
			msg.obj = track;
			msg.sendToTarget();
		}

		@Override
		public void closeApp() throws RemoteException {
            //清除未处理消息，加快关闭速度
			mUiHandler.removeCallbacksAndMessages(null);
			Message msg = mUiHandler.obtainMessage(MSG_CLOSE_APP);
			msg.sendToTarget();
		}

		@Override
		public void onSetHistoryToPlayer() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_LOAD_HISTORY_SUCCESS);
			msg.sendToTarget();
		}

		@Override
		public void onHightPlusNoAuthorized(Track track) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ON_HIGHTPLUS_NO_AUTHORIZED);
			msg.obj = track;
			msg.sendToTarget();
		}

		@Override
		public boolean userIsVip() throws RemoteException {

			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle
						.userIsVip();
			}

			return false;
		}

		@Override
		public boolean isUseNewPlayFragment() throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.isUseNewPlayFragment();
			}
			return ConstantsOpenSdk.DEFALUT_USE_NEW_PLAY_FRA;
		}

		@Override
		public boolean lockScreenActivityIsShowing() throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.lockScreenActivityIsShowing();
			}
			return false;
		}

		@Override
		public boolean checkAndPlayPausedVideoAd() throws RemoteException {
			if (mVideoAdState == MiniPlayer.STATE_PAUSED) {
				LocalBroadcastManager.getInstance(mAppCtx).sendBroadcast(new Intent(PlayerConstants.ACTION_PLAY_VIDEO_AD));
				return true;
			}
			return false;
		}

		@Override
		public boolean isPlayingVideoAd() throws RemoteException {
			return mVideoAdState == MiniPlayer.STATE_STARTED;
		}

		@Override
		public boolean isPlayingFreeListenAd() throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.isPlayingFreeListenAd();
			}
			return false;
		}

		@Override
		public void onSkipHeadTailModelFetched(SkipHeadTailModel skipHeadTailModel) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SKIP_HEAD_TAIL_MODEL_FETCH);
			msg.obj = skipHeadTailModel;
			msg.sendToTarget();
		}

		@Override
		public long updateGDTRTBToken() throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.updateGDTRTBToken();
			}
			return 0;
		}

		@Override
		public long updateGDTRTBSdkInfo(String dspId) throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.updateGDTRTBSdkInfo(dspId);
			}
			return 0;
		}

		@Override
		public long updateCSJRTBToken(int thirdAdType, String dspId, int adType) throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.updateCSJRTBToken(thirdAdType, dspId, adType);
			}
			return 0;
		}

		@Override
		public String syncBatchGetToken(List adRtbModelList, int thirdAdType) throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.syncBatchGetToken(adRtbModelList, thirdAdType);
			}
			return null;
		}

		@Override
		public String syncBatchGetGdtSdkInfo(List adRtbModelList) throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.syncBatchGetGdtSdkInfo(adRtbModelList);
			}
			return null;
		}

		@Override
        public void batchAdRecord(List thirdAdList, List adReportModel) throws RemoteException {
            if (mIXmCommonBusinessHandle != null) {
                mIXmCommonBusinessHandle.batchAdRecord(thirdAdList, adReportModel);
            }
        }

        @Override
		public String queryConfigCenter(int queryType, String groupName, String itemName) {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.queryConfigCenter(queryType, groupName, itemName);
			}
			return null;
		}

		@Override
		public void callForTraceMarkPoint(int metaId, String paramJson) throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				mIXmCommonBusinessHandle.callForTraceMarkPoint(metaId, paramJson);
			}
		}

		@Override
		public void callForShowUniversalPaymentActionDialog(Track track) throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				mIXmCommonBusinessHandle.callForShowUniversalPaymentActionDialog(track);
			}
		}

		@Override
		public void playToListenRecommendList(boolean isPlayingRecommend) throws RemoteException {
			afterClearAllToListen(isPlayingRecommend, true);
		}

		@Override
		public void preRequestToListenRecommend() throws RemoteException {
			ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
			if (iCommonBusiService != null) {
				iCommonBusiService.preRequestToListenRecommend();
			}
		}

		@Override
		public void deleteLastUnCompleteToListen(boolean isToListen, long trackId) throws RemoteException {
			ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
			if (iCommonBusiService != null) {
				iCommonBusiService.deleteLastUnCompleteToListen(isToListen, trackId);
			}
		}

		@Override
		public void addToListenForLastTrack(Track track) throws RemoteException {
			ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
			if (iCommonBusiService != null) {
				iCommonBusiService.addToListenForLastTrack(track);
			}
		}

		@Override
		public void notifyFreeListenTimeOut() throws RemoteException {
			if (null != mIXmCommonBusinessHandle) {
				mIXmCommonBusinessHandle.notifyFreeListenTimeOut();
			}
		}

		@Override
		public boolean isPlayFragmentForeground() throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.isPlayFragmentForeground();
			}
			return false;
		}

		@Override
		public String getNewTicket(int type) {
			if (mIXmCommonBusinessHandle != null) {
				return mIXmCommonBusinessHandle.getNewTicket(type);
			}
			return null;
		}

        @Override
        public void setThirdUidAndAppKey(String thirdUid, String appKey) throws RemoteException {
            if (mIXmCommonBusinessHandle != null) {
                mIXmCommonBusinessHandle.setThirdUidAndAppKey(thirdUid, appKey);
            }
        }

        @Override
        public void showToast(String message, long duration) throws RemoteException {
            ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
            if (iCommonBusiService != null) {
                iCommonBusiService.showToast(message, duration);
            }
        }

		@Override
		public void mediasessionClick(int ms) throws RemoteException {
			ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
			if (iCommonBusiService != null) {
				iCommonBusiService.clientFlowSeekTo(ms);
			}
		}

		@Override
		public void completePlay() throws RemoteException {
			if (mIXmCommonBusinessHandle != null) {
				mIXmCommonBusinessHandle.completePlay();
			}
		}
	};

	private IXmCommonBusinessHandle mIXmCommonBusinessHandle;

	public void setCommonBusinessHandle(IXmCommonBusinessHandle l) {
		mIXmCommonBusinessHandle = l;
	}

	private IXmPlayerListenTimeListener.Stub mListenTimeListenerStub = new IXmPlayerListenTimeListener.Stub() {
		@Override
		public void onListenTimeChange(int remainTime) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_LISTEN_TIME_CHANGE);
			msg.arg1 = remainTime;
			msg.sendToTarget();
		}
	};

	private IXmPlayerFreeListenTimeListener.Stub mFreesListenerStub = new IXmPlayerFreeListenTimeListener.Stub() {
		@Override
		public void onListenTimeChange(int remainTime, boolean byAddTime, int addedTime) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_FREE_LISTEN_TIME_CHANGE);
			msg.arg1 = remainTime;
			if (byAddTime) {
				msg.arg2 = 1;
			} else {
				msg.arg2 = 0;
			}
			msg.obj = addedTime;
			msg.sendToTarget();
		}

		@Override
		public void onListenTimeEmpty() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_FREE_LISTEN_TIME_EMPTY);
			msg.sendToTarget();
		}

		@Override
		public void onAllDayFreeListenExpire() {
			Message msg = mUiHandler.obtainMessage(MSG_FREE_LISTEN_ALL_DAY_EXPIRE);
			msg.sendToTarget();
		}
	};

	private ISoundPatchStatusDispatcher.Stub mSoundPatchStatusDispatcher = new ISoundPatchStatusDispatcher.Stub() {
		@Override
		public void onSoundPatchStartPlaySoundPatch(SoundPatchInfo soundPatchInfo) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_PATCH_START);
			msg.obj = soundPatchInfo;
			msg.sendToTarget();
		}

		@Override
		public void onSoundPatchCompletePlaySoundPatch(SoundPatchInfo soundPatchInfo) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_PATCH_COMPLETE);
			msg.obj = soundPatchInfo;
			msg.sendToTarget();
		}

		@Override
		public void onSoundPatchError(SoundPatchInfo soundPatchInfo, int what, int extra) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_PATCH_ERROR);
			msg.arg1 = extra;
			msg.arg2 = what;
			msg.obj = soundPatchInfo;
			msg.sendToTarget();
		}

		@Override
		public void onCommercialSoundPatchStatusChange(int type) {
			Message msg = mUiHandler.obtainMessage(MSG_COMMERCIAL_SOUND_PATCH_STATUS_CHANGE);
			msg.arg1 = type;
			msg.sendToTarget();
		}

		@Override
		public void onCommercialSoundPatchNeedReRegister() {
			Message msg = mUiHandler.obtainMessage(MSG_COMMERCIAL_SOUND_PATCH_RE_REGISTER);
			msg.sendToTarget();
		}

	};

	private ISoundEffectStatusDispatcher.Stub mSoundEffectStatusDispatcher = new ISoundEffectStatusDispatcher.Stub() {
		@Override
		public void onEffectStart(SoundEffectInfo info) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_EFFECT_START);
			msg.obj = info;
			msg.sendToTarget();
		}

		@Override
		public void onEffectEnd(SoundEffectInfo info) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_EFFECT_END);
			msg.obj = info;
			msg.sendToTarget();
		}

		@Override
		public void onEffectError(SoundEffectInfo info) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_EFFECT_ERROR);
			msg.obj = info;
			msg.sendToTarget();
		}

		@Override
		public void onMangerInPlayerInitialed(SoundEffectInfo info) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_EFFECT_INITIAL_INFO);
			msg.obj = info;
			msg.sendToTarget();
		}
	};

	private IXmPlayerBaseInfoRequestDispatcher.Stub mBaseInfoRequestDispatcher = new IXmPlayerBaseInfoRequestDispatcher.Stub() {
		@Override
		public void onTrackBaseInfoBackSuccess(Track track) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_BSAE_INFO_SUCCESS);
			msg.obj = track;
			msg.sendToTarget();
		}

		@Override
		public void onTrackBaseInfoBackError(BaseInfoOnErrorModel baseInfoOnErrorModel) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_BSAE_INFO_ON_ERROR);
			msg.obj = baseInfoOnErrorModel;
			msg.sendToTarget();
		}
	};

	private IXmAdsEventDispatcher.Stub mAdsListenerStub = new IXmAdsEventDispatcher.Stub() {

		@Override
		public void onStartPlayAds(Advertis ad, int position)
				throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_START_PLAY_ADS);
			msg.arg1 = position;
			msg.obj = ad;
			msg.sendToTarget();
		}

		@Override
		public void onStartGetAdsInfo(int playMethod, boolean duringPlay ,boolean paused) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_START_GET_ADS_INFO);
			msg.arg1 = playMethod;
			msg.arg2 = duringPlay ? 1 : 0;
			msg.obj = paused;
			msg.sendToTarget();
		}

		@Override
		public void onGetAdsInfo(AdvertisList ads) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_GET_ADS_INFO);
			msg.obj = ads;
			msg.sendToTarget();
		}

		@Override
		public void onError(int what, int extra) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAY_ADS_ERROR);
			msg.arg1 = what;
			msg.arg2 = extra;
			msg.sendToTarget();
		}

		@Override
		public void onGetForwardVideo(List<Advertis> advertis) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ON_GET_FORWARD_VIDEO);
			msg.obj = advertis;
			msg.sendToTarget();
		}

		@Override
		public void onCompletePlayAds() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_COMPLETE_PLAY_ADS);
			msg.sendToTarget();
		}

		@Override
		public void onAdsStopBuffering() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ADS_BUFFERING_STOP);
			msg.sendToTarget();
		}

		@Override
		public void onAdsStartBuffering() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ADS_BUFFERING_START);
			msg.sendToTarget();
		}
	};

	private IXmPlayerEventDispatcher.Stub mListenerStub = new IXmPlayerEventDispatcher.Stub() {
		@Override
		public void onSoundPrepared() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_PREPARED);
			msg.sendToTarget();
		}

		@Override
		public void onPlayStop() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAY_STOP);
			msg.sendToTarget();
		}

		@Override
		public void onPlayStart() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAY_START);
			msg.sendToTarget();
		}

		@Override
		public void onPlayProgress(int currPos, int duration)
				throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PROGRESS_CHANGE);
			msg.arg1 = currPos;
			msg.arg2 = duration;
			msg.sendToTarget();
		}

		@Override
		public void onPlayPause() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAY_PAUSE);
			msg.sendToTarget();
		}

		@Override
		public void onSoundPlayComplete() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAY_COMPLETE);
			msg.sendToTarget();
		}

		@Override
		public void onError(XmPlayerException exception) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAY_ERROR);
			msg.obj = exception;
			msg.sendToTarget();
		}

		@Override
		public void onRequestPlayUrlBegin() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ON_REQUEST_PLAY_URL_BEGIN);
			msg.sendToTarget();
		}

		@Override
		public void onRequestPlayUrlSuccess() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ON_REQUEST_PLAY_URL_SUCCESS);
			msg.sendToTarget();
		}

		@Override
		public void onChildAiTimbreUrlGet(int success, int isDownload, String type) {
			Message msg = mUiHandler.obtainMessage(MSG_ON_CHILD_AI_TIMBRE_URL_GET);
			msg.arg1 = success;
			msg.arg2 = isDownload;
			msg.obj = type;
			msg.sendToTarget();
		}

		@Override
		public void onRequestPlayUrlError(int code, String message) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ON_REQUEST_PLAY_URL_FAIL);
			msg.arg1 = code;
			msg.obj = message;
			msg.sendToTarget();
		}

		@Override
		public void onAudioAuditionOver(Track lastTrack) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_ON_AUDIO_AUDITION_OVER);
			msg.obj = lastTrack;
			msg.sendToTarget();
		}

		@Override
		public void onBufferProgress(int position) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_BUFFER_PROGRESS_CHANGE);
			msg.arg1 = position;
			msg.sendToTarget();
		}

		public void onBufferingStart() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_BUFFER_STATUS_CHANGE);
			msg.obj = true;
			msg.sendToTarget();
		}

		@Override
		public void onBufferingStop() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_BUFFER_STATUS_CHANGE);
			msg.obj = false;
			msg.sendToTarget();
		}

		@Override
		public void onSoundSwitch(Track lastTrack, Track curTrack)
				throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_SOUND_CHANGE);
			msg.obj = new Object[] { lastTrack, curTrack };
			msg.sendToTarget();
		}

		@Override
		public void onRenderingStart() {
			mUiHandler.sendEmptyMessage(MSG_ON_RENDERING_START);
		}

		@Override
		public void onRotationChanged(int rotationAngle) {
			Message msg = mUiHandler.obtainMessage(MSG_ON_VIDEO_ROTATION_CHANGED);
			msg.arg1 = rotationAngle;
			msg.sendToTarget();
		}

		@Override
		public void onVideoSizeChanged(int width, int height) {
			Message msg = mUiHandler.obtainMessage(MSG_ON_VIDEO_SIZE_CHANGED);
			msg.arg1 = width;
			msg.arg2 = height;
			msg.sendToTarget();
		}

	};

	private IMixPlayerEventDispatcher mMixListenerStub = new IMixPlayerEventDispatcher.Stub() {
		@Override
		public void onMixStart() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_MIX_START);
			msg.sendToTarget();
		}

		@Override
		public void onMixPause() throws RemoteException {
			mUiHandler.obtainMessage(MSG_MIX_PAUSE).sendToTarget();
		}

		@Override
		public void onMixStop() throws RemoteException {
			mUiHandler.obtainMessage(MSG_MIX_STOP).sendToTarget();
		}

		@Override
		public void onMixProgressUpdate(int percent) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_MIX_PROGRESS_UPDATE);
			msg.arg1 = percent;
			msg.sendToTarget();
		}

		@Override
		public void onMixComplete() throws RemoteException {
			mUiHandler.obtainMessage(MSG_MIX_COMPLETE).sendToTarget();
		}

		@Override
		public void onMixError(String url, int code, String msg) throws RemoteException {
			Message message = mUiHandler.obtainMessage(MSG_MIX_ERROR);
			Bundle data = new Bundle();
			data.putString("url", url);
			data.putString("msg", msg);
			data.putInt("code", code);
			message.setData(data);
			message.sendToTarget();
		}

		@Override
		public void onMixStatusChanged(double key, boolean isPlaying, String state, long curPosition) throws RemoteException {
			Message message = mUiHandler.obtainMessage(MSG_MIX_STATUS_UPDATE);
			Bundle data = new Bundle();
			data.putDouble("key", key);
			data.putBoolean("isPlaying", isPlaying);
			data.putLong("curPosition", curPosition);
			data.putString("state", state);
			message.setData(data);
			message.sendToTarget();
		}

		@Override
		public void onMixSoundComplete(double key) throws RemoteException {
			Message message = mUiHandler.obtainMessage(MSG_MIX_SOUND_COMPLETE);
			Bundle data = new Bundle();
			data.putDouble("key", key);
			message.setData(data);
			message.sendToTarget();
		}

		@Override
		public void onMixTrackCleared() {
			mUiHandler.obtainMessage(MSG_MIX_TRACK_CLEARED).sendToTarget();
		}
	};

	private IXmPlayHistoryListener.Stub mHistoryListenerStub = new IXmPlayHistoryListener.Stub() {
		@Override
		public void onPlayHistoryInited() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_HISTORY_INITED);
			msg.sendToTarget();
		}

		@Override
		public void onPlayHistoryChanged() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_HISTORY_CHANGED);
			msg.sendToTarget();
		}
	};

	private IXmFlvDataCallback.Stub mFlvDataCallbackStub = new IXmFlvDataCallback.Stub() {
		@Override
		public void dataOutput(int type, byte[] bytes, long bufferDuration) throws RemoteException {
			SEIParserUtil.SEIInfo seiInfo = SEIParserUtil.parseSEI(type, bytes);
			Message msg = mUiHandler.obtainMessage(MSG_FLV_DATA_OUT, type, 0, new FlvData(seiInfo, bufferDuration));
			msg.sendToTarget();
		}

		@Override
		public void onMetaInfoCallback(int width, int height, double audiobitrate, double videobitrate) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_FLV_META_DATA_INFO, new FlvMetaInfo(width, height, audiobitrate, videobitrate));
			msg.sendToTarget();
		}


	};

	public static XmPlayerManager getInstance(Context appCtx) {
		if (ConstantsOpenSdk.isDebug) {
			Context context = appCtx;
			if (context == null && sInstance != null) {
				context = sInstance.mAppCtx;
			}
			if (context != null && !BaseUtil.isMainProcess(context)) {
				Thread.dumpStack();
				throw new RuntimeException("only main process can use this method");
			}
		}
		if (sInstance == null) {
			synchronized (sLock) {
				if (sInstance == null) {
					sInstance = new XmPlayerManager(appCtx);
				}
			}
		}
		return sInstance;
	}

	public void setHttpConfig(Config config) {
		sHttpConfig = config;
		if (!isConnectedStatus()) {
			return;
		}
		try {
			setPlayerProxy();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isServiceConnected() {
		return isConnectedStatus();
	}

	private void setPlayerProxy() throws RemoteException {
		if (mPlayerStub != null) {
			Config httpConfig = sHttpConfig;
			mPlayerStub.setProxyNew(httpConfig);
		}
	}
	private synchronized boolean isConnectedStatus() {
		if (!mConnected||mPlayerStub==null||mPlayerStub.asBinder()==null||!mPlayerStub.asBinder().isBinderAlive()) {
			return false;
		}
		return true;
	}
	private boolean checkConnectionStatus() {
		if (!mConnected||mPlayerStub==null||mPlayerStub.asBinder()==null||!mPlayerStub.asBinder().isBinderAlive()) {
			Logger.i(TAG, "checkConnectionStatus disconnected");
			init(true);
			return false;
		}
		return true;
	}

	private XmPlayerManager(Context ctx) {
		mAppCtx = ctx.getApplicationContext();
		mUiHandler = new UIHandler(Looper.getMainLooper());
	}

	public boolean isConnected() {
		return mConnected;
	}

	public void addPlayerStatusListener(IXmPlayerStatusListener l) {
		if (l != null && !mPlayerStatusListeners.contains(l)) {
			mPlayerStatusListeners.add(l);
			try {
				if(isConnectedStatus()) {
					//主动调用一次onBufferProgress，防止添加监听前播放器已经缓冲了100，导致后面不再回调onBufferProgress
					if (XmAdsManager.isPlayFragmentShowing) {
						mListenerStub.onBufferProgress(mPlayerStub.getTrackBufferPercentage());
						if (mPlayerStub.isPlaying()) {
							mListenerStub.onPlayProgress(mPlayerStub.getPlayCurrPosition(),
									mPlayerStub.getDuration());
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void removePlayerStatusListener(IXmPlayerStatusListener l) {
		if (null != l && mPlayerStatusListeners != null)
			mPlayerStatusListeners.remove(l);
	}

	public boolean containPlayerStatusListener(IXmPlayerStatusListener l) {
		return mPlayerStatusListeners.contains(l);
	}

	public void addAdsStatusListener(IXmAdsStatusListener l) {
		if (l != null && !mAdsStatusListeners.contains(l)) {
			mAdsStatusListeners.add(l);
		}
	}

	public void removeAdsStatusListener(IXmAdsStatusListener l) {
		if (null != l && mAdsStatusListeners != null) {
			mAdsStatusListeners.remove(l);
		}
	}

	public void addHistoryChangedCallback(IXmDataChangedCallback l) {
		if (l != null && !mHistoryChangedCallbacks.contains(l)) {
			mHistoryChangedCallbacks.add(l);
		}
	}

	public void removeHistoryChangedCallback(IXmDataChangedCallback l) {
		if (null != l && mHistoryChangedCallbacks != null) {
			mHistoryChangedCallbacks.remove(l);
		}
	}

	public void addPlayerTrackInfoListener(IXmPlayerTrackInfoListener listener) {
		if (listener != null && mPlayerTrackInfoListeners != null
				&& !mPlayerTrackInfoListeners.contains(listener)) {
			mPlayerTrackInfoListeners.add(listener);
		}
	}

	public void removePlayerTrackInfoListener(IXmPlayerTrackInfoListener listener) {
		if (listener != null && mPlayerTrackInfoListeners != null) {
			mPlayerTrackInfoListeners.remove(listener);
		}
	}

	IFreeFlowService.IProxyChange mIProxyChange = new IFreeFlowService.IProxyChange() {
		@Override
		public void proxyChange(boolean isRemove, Config config) {
			setHttpConfig(config);
		}
	};

	private boolean inAppInitialization = false;

	public void setIfInAppInitialization(boolean inAppInitialization) {
		this.inAppInitialization = inAppInitialization;
	}

	private boolean mHasStartRecordStartForegroundOverTime = false;
	public synchronized void recordStartForegroundOverTime() {
		if(!mHasStartRecordStartForegroundOverTime) {
			mHasStartRecordStartForegroundOverTime = true;
			XmAppHelper.runOnOnWorkThreadDelayed(new Runnable() {
				@Override
				public void run() {
					if(!mConnected) {
						Logger.i(TAG, "start foreground service over time");
						IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
						if (xdcsPost != null) {
							xdcsPost.statErrorToXDCS("StartFServiceMonitor", "startfserviceovertime");
						}
					}
				}
			}, 15000);
		}
	}

	public void init(boolean forPlay) {
		IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
		if (freeFlowService != null) {
			freeFlowService.addProxyChanges(mIProxyChange);
		}
		Logger.logToFile("XmPlayerManager init forPlay: " + forPlay + ", inAppInitialization: " + inAppInitialization + ", mBindRet: " + mBindRet + ", trace=" + Log.getStackTraceString(new Throwable()));

		if(inAppInitialization) {
			return;
		}
        try{
			boolean isStartForeground = false;
			if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
				Logger.logToFile("XmPlayerManager init 1");
				//被杀死、处于前台、需要播放或已经启动过了service才启动service.
				if(forPlay || BaseUtil.isAppForeground(mAppCtx)) {
					Logger.logToFile("XmPlayerManager init 1-1");
					StartServiceTimeCollectUtil.beginSelectionFromMainProcess();
					if (StartServiceTimeoutFixUtil.isAppForeground(mAppCtx)) {
						Logger.logToFile("XmPlayerManager init 1-1-1");
						mAppCtx.startService(XmPlayerService.getIntent(mAppCtx,true));
					} else {
						Logger.logToFile("XmPlayerManager init 1-1-2");
						mAppCtx.startForegroundService(XmPlayerService.getIntent(mAppCtx,true));
					}
					StartServiceTimeoutMonitor.checkStart(mAppCtx);
					isStartForeground = true;
					recordStartForegroundOverTime();

					if (mConnectedBool != null && !mConnectedBool) {
						try {
							Logger.logToFile("XmPlayerManager addOnConnectedListerner mIConnectListener");
							addOnConnectedListerner(mIConnectListener);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}else{
					Logger.logToFile("XmPlayerManager init 1-2");
					return;
				}
			}else {
				Logger.logToFile("XmPlayerManager init 2");
				mAppCtx.startService(XmPlayerService.getIntent(mAppCtx,false));
			}
			if(!mBindRet) {
				mBindRet = mAppCtx.bindService(XmPlayerService.getIntent(mAppCtx, isStartForeground),
						mConn, Context.BIND_AUTO_CREATE);
			}
			UseTraceCollector.log("XmPlayerManager: init bindRet: " + mBindRet);
			Logger.logToFile(TAG + " Bind ret " + mBindRet);
		}catch(Exception e){
			e.printStackTrace();
			IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
			if (xdcsPost != null) {
				xdcsPost.statErrorToXDCS("playFragmentBlack", "mAppCtx.startService/bindService:_____" + Log.getStackTraceString(e)
				+ "————————isScreenOn:" + SystemUtil.isScreenOn(mAppCtx));
			}
			UseTraceCollector.log("XmPlayerManager: init exception: " + e);
		}
		addPlayerStatusListener(HandlerPlayerProcessDiedUtil.mPlayerStatusListener);
	}

	private final IConnectListener mIConnectListener = new IConnectListener() {
		@Override
		public void onConnected() {
			try {
				removeOnConnectedListerner(mIConnectListener);
				int playMode = getSavedPlayMode(mAppCtx);
				Logger.logToFile("XmPlayerManager mIConnectListener setPlayMode playMode=" + playMode);
				setPlayMode(PlayMode.getIndex(playMode));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	};

	/**
	 * for player process
	 */
	public static void unBind() {
		Logger.i(TAG, "unBind release");
		if(sInstance!=null) {
			if (sInstance.mBindRet || (sInstance.mPlayerStub != null && sInstance.mPlayerStub.asBinder() != null && sInstance.mPlayerStub.asBinder().isBinderAlive())) {
				sInstance.mAppCtx.unbindService(sInstance.mConn);
				sInstance.mBindRet = false;
				sInstance.mConnectedBool = false;
			}
			sInstance.mPlayerStatusListeners.clear();
			sInstance.mFreeListenTimeListeners.clear();
			sInstance.mAdsStatusListeners.clear();
			sInstance.mHistoryChangedCallbacks.clear();
			sInstance.mConnectListenerSet.clear();
			sInstance.mConnectListener = null;
			sInstance.mCurModel = null;
			sInstance.mConnected = false;
			sInstance.mPlayerStub = null;
			sInstance.mOldPlayerStub = null;
			sInstance = null;
		}
	}

	public static void release() {
		Logger.i(TAG, "release");
		if(sInstance!=null) {
			sInstance.pause(PauseReason.Common.RELEASE);
			sInstance.stop();
			if (sInstance.mBindRet ||
					(sInstance.mPlayerStub != null
							&& sInstance.mPlayerStub.asBinder() != null
							&& sInstance.mPlayerStub.asBinder().isBinderAlive())) {
				sInstance.mAppCtx.unbindService(sInstance.mConn);
				sInstance.mBindRet = false;
				sInstance.mConnectedBool = false;
			}
			sInstance.mAppCtx.stopService(XmPlayerService.getIntent(sInstance.mAppCtx,false));
			sInstance.mPlayerStatusListeners.clear();
			sInstance.mFreeListenTimeListeners.clear();
			sInstance.mAdsStatusListeners.clear();
			sInstance.mHistoryChangedCallbacks.clear();
			sInstance.mConnectListenerSet.clear();
			sInstance.mConnectListener = null;
			sInstance.mHistoryListLoadSuccess.clear();
			sInstance.mCurModel = null;
			sInstance.mConnected = false;
			sInstance.mPlayerStub = null;
			sInstance.mOldPlayerStub = null;
			IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
			if (freeFlowService != null) {
				freeFlowService.removeProxyChange(sInstance.mIProxyChange);
			}
		}
	}

	public void clearCurTrackCache() {
		mCurModel = null;
	}

	public void addPlayListChange(IOnPlayListChange onPlayListChange) {
		if (!mPlayListChanges.contains(onPlayListChange)){
			mPlayListChanges.add(onPlayListChange);
		}
	}

	public void removePlayListChange(IOnPlayListChange onPlayListChange) {
		if (mPlayListChanges.contains(onPlayListChange)) {
			mPlayListChanges.remove(onPlayListChange);
		}
	}

	public void addHistoryListLoadSuccess(IOnHistoryListLoadSuccess loadSuccess) {
		mHistoryListLoadSuccess.add(loadSuccess);
	}

	public void removeHistoryListLoadSuccess(IOnHistoryListLoadSuccess loadSuccess) {
		mHistoryListLoadSuccess.remove(loadSuccess);
	}

	public interface IOnPlayListChange {
		void onPlayListChange();
	}

	public interface IOnHistoryListLoadSuccess {
		void onHistoryListLoadSuccess();
	}

	public interface IOnPlayModeChange {
		void onPlayModeChange(PlayMode playMode);
	}

	public interface IOnPlayOrderChange {
		void onPlayOrderChange(boolean isAsc);
	}

	/**
	 * 判断该音效是否可用
	 */
	public boolean isCurEffectConfigCanUsed(){
		if (!checkConnectionStatus()){
			return false;
		}
		try {
			return mPlayerStub.isCurEffectConfigCanUsed();
		} catch (RemoteException e) {
			e.printStackTrace();
		}
		return false;
	}


	/**
	 * 改变播放列表顺序
	 *
	 * @return true 成功改变 false 改变失败
	 *
	 */
	public boolean permutePlayList() {
		if (!checkConnectionStatus()) {
			return false;
		}
		boolean result = false;
		try {
			result = mPlayerStub.permutePlayList();

			boolean isAsc = mPlayerStub.getPlayListOrder();
			for (IOnPlayOrderChange listener : mOnPlayOrderChanges) {
				listener.onPlayOrderChange(isAsc);
			}

			ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
			if (commonBusiService != null && commonBusiService.isInTransferState()) {
				commonBusiService.permutePlayList();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	public boolean getPlayListOrder() {
		if (!isConnectedStatus()) {
			return true;
		}
		try {
			return mPlayerStub.getPlayListOrder();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return true;
	}

	public void getNextPlayList() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.getNextPlayList();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void syncCloudHistory(boolean sync) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.syncCloudHistory(sync);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void clearAllPlayHistory(boolean deleteCloud) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.clearAllPlayHistory(deleteCloud);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void deletePlayHistory(HistoryModel historyModel) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.deletePlayHistory(historyModel);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	public void batchDeleteHistory(List<HistoryModel> historyModels) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.batchDeleteHistory(historyModels);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	public void markAllHistoryDeleted(boolean onlyRadio){
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.markAllHistoryDeleted(onlyRadio);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void deleteRadioHistory(Radio radio){
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.deleteRadioHistory(radio);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public Track getTrackByHistory(long albumId){
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getTrackByHistory(albumId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public HistoryModel getHistoryModelByHistory(long albumId){
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getHistoryModelByHistory(albumId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public Radio getHistoryInfoByRadioID(long albumId){
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getHistoryInfoByRadioID(albumId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public List<HistoryModel> getTrackList() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getTrackList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public List<HistoryModel> getTrackList(int size) {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getTrackListHis(size);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public int getHistoryTrackListSize(){
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			return mPlayerStub.getHistoryTrackListSize();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}


	public List<Radio> getHisRadioList(){
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getHisRadioList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

    public void clearAllLocalHistory(){
        if (!isConnectedStatus()) {
            return ;
        }
        try {
			mPlayerStub.clearAllLocalHistory();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void putSoundHistory(Track track){
		if (!isConnectedStatus()) {
			return ;
		}
		try {
			mPlayerStub.putSoundHistory(track);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void saveSoundHistoryPos(long trackId, int playPos) {
		if (!isConnectedStatus()) {
			return ;
		}
		try {
			mPlayerStub.saveSoundHistoryPos(trackId, playPos);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void onVideoPlayEnd(Track track, int breakSec, int playDurationSec){
		if(!isConnected()){
			return;
		}
		Logger.logToFile("XmPlayerManager onVideoPlayEnd breakSec=" + breakSec + ",playDurationSec=" + playDurationSec + ",track=" + TrackUtil.trackToStr(track));

		try{
			mPlayerStub.onVideoPlayEnd(track, breakSec, playDurationSec);
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	public void onSwitchInAudio(int switchInSec){
		if(!isConnected()){
			return;
		}

		try{
			mPlayerStub.onSwitchInAudio(switchInSec);
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	public void showNotificationOnResume(){

		if(!isConnected()){
			return;
		}

		try {

			mPlayerStub.setNotificationAfterKilled();
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	@Nullable
	public Map<String, String> getDubPlayStatistics(){
		if(!isConnected()){
			return null;
		}

		try{
			return mPlayerStub.getDubPlayStatistics();
		}catch (Exception e){
			e.printStackTrace();
		}

		return null;
	}

	public void setNotificationType(int type){
        if(!isConnected()){
            return;
        }

        try{
            mPlayerStub.setNotificationType(type);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
	public void onSwitchOutAudio(int switchOutSec){
		if(!isConnected()){
			return;
		}

		try{
			mPlayerStub.onSwitchOutAudio(switchOutSec);
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	public void getPrePlayList() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.getPrePlayList();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void clearPlayList(){
		if (!isConnectedStatus()) {
			return;
		}
		try {
			clearCurTrackCache();
			mPlayerStub.clearPlayList();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 一键听跳过已经播放过的下一首的辅助方法，用来控制开闭屏开关，单独设置不起作用，
	 * 需配合SKIP_PLAYED_OR_COMPLETED_SOUND参数使用
	 * */
	public void setSkipNextPlayedSound(boolean skip) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setSkipNextPlayedSound(skip);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getAlbumSortByAlbumId(long albumId){
		if (!isConnectedStatus()) {
			return IHistoryManagerForMain.ASC_ORDER;
		}
		try {
			return mPlayerStub.getAlbumSortByAlbumId(albumId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return IHistoryManagerForMain.ASC_ORDER;
	}

	public void putAlbumSortByAlbumId(long albumId, int order){
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.putAlbumSortByAlbumId(albumId, order);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}



	private Set<IXmDataCallback> mDataCallbackList = new HashSet<>();

	private Set<IXmPlanTerminateListener> mPlanTerminateCallbackList = new CopyOnWriteArraySet<>();

	public void addPlanTerminateListener(IXmPlanTerminateListener listener) {
		mPlanTerminateCallbackList.add(listener);
	}

	public void removePlanTerminateListener(IXmPlanTerminateListener listener) {
		mPlanTerminateCallbackList.remove(listener);
	}

	private final IXmPlanTerminateListener.Stub mPlanTerminateCallbackSub = new IXmPlanTerminateListener.Stub() {

		@Override
		public void onTimeout(int type) throws RemoteException {
			Logger.i(TAG, "定时关闭触发");
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_TIME_OUT);
			msg.arg1 = type;
			msg.sendToTarget();
		}

		@Override
		public void onLeftTimeChanged(int leftTime, int type) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_LEFT_TIME_CHANGED);
			msg.arg1 = leftTime;
			msg.arg2 = type;
			msg.sendToTarget();
		}

		@Override
		public void onLeftSeriesChanged(int series, int type) throws RemoteException {
			Logger.i(TAG, "剩余集数变化：" + series);
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_LEFT_SERIES_CHANGED);
			msg.arg1 = series;
			msg.arg2 = type;
			msg.sendToTarget();
		}

		@Override
		public void onCancel() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_CANCEL);
			msg.sendToTarget();
		}
	};

	private Set<IXmPlanTerminateListener> mPlanTerminateCallbackListForQuickListen = new CopyOnWriteArraySet<>();

	public void addPlanTerminateListenerForQuickListen(IXmPlanTerminateListener listener) {
		mPlanTerminateCallbackListForQuickListen.add(listener);
	}

	public void removePlanTerminateListenerForQuickListen(IXmPlanTerminateListener listener) {
		mPlanTerminateCallbackListForQuickListen.remove(listener);
	}

	private final IXmPlanTerminateListener.Stub mPlanTerminateCallbackSubForQuickListen = new IXmPlanTerminateListener.Stub() {

		@Override
		public void onTimeout(int type) throws RemoteException {
			Logger.i(TAG, "定时关闭触发");
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_TIME_OUT_FOR_QUICK_LISTEN);
			msg.arg1 = type;
			msg.sendToTarget();
		}

		@Override
		public void onLeftTimeChanged(int leftTime, int type) throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_LEFT_TIME_CHANGED_FOR_QUICK_LISTEN);
			msg.arg1 = leftTime;
			msg.arg2 = type;
			msg.sendToTarget();
		}

		@Override
		public void onLeftSeriesChanged(int series, int type) throws RemoteException {
			Logger.i(TAG, "剩余集数变化：" + series);
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_LEFT_SERIES_CHANGED_FOR_QUICK_LISTEN);
			msg.arg1 = series;
			msg.arg2 = type;
			msg.sendToTarget();
		}

		@Override
		public void onCancel() throws RemoteException {
			Message msg = mUiHandler.obtainMessage(MSG_PLAN_TERMINATE_ON_CANCEL_FOR_QUICK_LISTEN);
			msg.sendToTarget();
		}
	};

	private IXmDataCallback.Stub mDataCallbackSub = new IXmDataCallback.Stub() {

		@Override
		public void onListChange() throws RemoteException {
			for (IXmDataCallback callback : mDataCallbackList) {
				if (callback == null) {
					continue;
				}
				callback.onListChange();
			}
		}

		@Override
		public void onDataReady(List<Track> list, boolean hasMorePage,
				boolean isNextPage) throws RemoteException {
			for (IXmDataCallback callback : mDataCallbackList) {
				if (callback == null) {
					continue;
				}
				try {
					// 业务方需要注意 这里list可能是null，kotlin容易出问题
					callback.onDataReady(list, hasMorePage, isNextPage);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		@Override
		public void onError(int code, String message, boolean isNextPage)
				throws RemoteException {
			for (IXmDataCallback callback : mDataCallbackList) {
				if (callback == null) {
					continue;
				}
				callback.onError(code, message, isNextPage);
			}
		}

	};

	public void addPlayListChangeListener(IXmDataCallback l) {
		mDataCallbackList.add(l);
	}

	public void removePlayListChangeListener(IXmDataCallback l) {
		mDataCallbackList.remove(l);
	}

	public int getPlayerStatus() {
		if (!isConnectedStatus()) {
			return PlayerConstants.STATE_ERROR;
		}
		try {
			return mPlayerStub.getPlayerStatus();
		} catch (Exception e) {
			e.printStackTrace();
			return PlayerConstants.STATE_ERROR;
		}
	}

	public int getCurrentIndex() {
		if (!isConnectedStatus()) {
			return -1;
		}
		try {
			return mPlayerStub.getCurrIndex();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	private PlayableModel trackToOther(Track track) {
		if (!checkConnectionStatus()) {
			return null;
		}

		int source = -1;
		try {
			source = mPlayerStub.getPlaySourceType();
		} catch (Exception e) {
			e.printStackTrace();
		}

		if (track != null) {
			if (source == XmPlayListControl.PLAY_SOURCE_TRACK) {
				if (PlayableModel.KIND_SCHEDULE.equals(track.getKind())) {
					return ModelUtil.trackToSchedule(track);
				}
				return track;
			} else if (source == XmPlayListControl.PLAY_SOURCE_RADIO) {
				if (PlayableModel.KIND_RADIO.equals(track.getKind())) {
					return ModelUtil.trackToRadio(track);
				} else if (PlayableModel.KIND_SCHEDULE.equals(track.getKind())) {
					return ModelUtil.trackToSchedule(track);
				}
			}
		}
		return null;
	}

    /**
     * getCurrSound:(获取当前的播放model，可能是track、radio或者schedule中的节目)
     */
    public PlayableModel getCurrSound() {
        return getCurrSound(true);
    }


    @Nullable
	public Track getCurrSoundIgnoreKind(boolean useCache) {
		if (!isConnectedStatus()) {
			return null;
		}
		if (mCurModel != null && useCache) {
			return (Track) mCurModel;
		}
		try {
			Track track = mPlayerStub.getCurTrack();
			return track;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 正在播放待播声音的时候，如果删光了待播声音，需要调用该方法
	 * */
	public void afterClearAllToListen(boolean isPlayingRecommend, boolean autoPlay) {
		String[] toListenArray = ToListenUtil.getMMKVUtil().getAllKeys();
		if (toListenArray != null && toListenArray.length > 0) {
			// 还有待播数据  不自动播推荐待播声音
			return;
		}
		// 播完待播列表，播推荐待播声音
//		Logger.d("ToListen", "start: 播完待播列表，播推荐待播声音");
//		ICommonBusiService iCommonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
//		if (iCommonBusiService != null) {
//			iCommonBusiService.playRecommendToListenList(autoPlay, 0, isPlayingRecommend);
//		}
	}

	/**
	 * 下一首是否直接切换成待播
	 * */
	public void setContinueToPlayToListenTrack(boolean continueToPlayToListenTrack) {
//		if (!isConnectedStatus()) {
//			return;
//		}
//		if (isPlayingToListenTracks()) {
//			return;
//		}
//		if (isPlayingRecommendToListenTracks()) {
//			return;
//		}
//		try {
//			mPlayerStub.continueToPlayToListenTrack(continueToPlayToListenTrack);
//			if (continueToPlayToListenTrack) {
//				setPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_LIST);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
	}

	public boolean getContinueToPlayToListenTrack() {
		if (!isConnectedStatus()) {
			return false;
		}
		if (isPlayingToListenTracks()) {
			return false;
		}
		try {
			return mPlayerStub.getContinueToPlayToListenTrack();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * 是否在播放待播声音
	 */
	public boolean isPlayingToListenTracks() {
		PlayableModel track = getCurrSound(false);
		if (track instanceof Track) {
			return ((Track) track).getIsFromToListenTrack() == 1;
		}
		return false;
	}

	/**
	 * 播放待播前，保存当前播放列表
	 * */
	public void beforePlayToListen(boolean deleteCurrentNormalTrack) {
		if (!isConnectedStatus()) {
			return;
		}
		if (isPlayingToListenTracks()) {
			return;
		}
		if (isPlayingRecommendToListenTracks()) {
			return;
		}
		try {
			mPlayerStub.beforePlayToListen(deleteCurrentNormalTrack);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isPlayingRecommendToListenTracks() {
		PlayableModel track = getCurrSound(false);
		if (track instanceof Track) {
			return ((Track) track).isToListenRecommend();
		}
		return false;
	}

	public void ignorePlayerToListenLogic(boolean ignore) {
		Logger.e("sjc", "ignorePlayerToListenLogic = " + ignore + "   , " + Log.getStackTraceString(new Throwable()));
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.ignorePlayerToListenLogic(ignore);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getTrackSkipTailFromCache(long albumId) {
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			return mPlayerStub.getTrackSkipTailFromCache(albumId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	/**
	 * getCurrSound:(获取当前的播放model，可能是track、radio或者schedule中的节目)
	 * 一般情况下不要使用此方法
	 */
	public PlayableModel getCurrSound(boolean useCache) {
		if (!isConnectedStatus()) {
			return null;
		}
		if (mCurModel != null && useCache) {
			return trackToOther((Track) mCurModel);
		}

		if (isLocalReadTrack()) {
			//正在播放本地小说时，不刷新 mCurModel，否则会导致正在播放的小说变成其他声音
			Logger.d("z_read", "getCurrSound without cache, but isLocalReadTrack");
			return mCurModel;
		}
		try {
			Track track = mPlayerStub.getCurTrack();
			mCurModel = track;

			printCurModel("getCurrSound");
			return trackToOther(track);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public Track getRequestTrackModel() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			Track track = mPlayerStub.getRequestTrackModel();
			return track;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	private boolean isLocalReadTrack() {
		if (mCurModel == null) {
			return false;
		}

		if (checkLocalReadTrackSwitch == null) {
			checkLocalReadTrackSwitch = EasyConfigure.getBoolean("get_curr_track_use_local_read", false);
		}
		if (!checkLocalReadTrackSwitch) {
			return false;
		}

		if (mCurModel instanceof Track) {
			Track currPlayableModel = (Track) mCurModel;
			return currPlayableModel.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_LOCAL_EBOOK_READING
					|| Objects.equals(currPlayableModel.getKind(), PlayableModel.KIND_LOCAL_EBOOK);
		}
		return false;
	}


	/**
	 * 拿到目前正在播放的微课 直播声音
	 * @param useCache
	 * @return
	 */
	public PlayableModel getCurrWeikeSound(boolean useCache) {
		if (!isConnectedStatus()) {
			return null;
		}
		PlayableModel weikeTrack = null;

		if (mCurModel != null && useCache) {
			weikeTrack = mCurModel;
		}
		try {
			weikeTrack = mPlayerStub.getCurTrack();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

		if (weikeTrack != null && weikeTrack.isWeikeTrack) {
			return weikeTrack;
		} else {
			return null;
		}
	}




	public void setDLNAState(boolean isDLNAState) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setDLNAState(isDLNAState);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isDLNAState() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.isDLNAState();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean bindSurface(Surface surface) {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.bindSurface(surface);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean removeSurface(Surface surface) {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.removeSurface(surface);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean releaseSurface() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.releaseSurface();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public void setFlvType(@XMediaplayerImpl.FlvType int type)  {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setFlvtype(type);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// report为true，则上报埋点，并且将播放器的flag置为false
	public void tracePushGuardFlag(boolean report)  {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.tracePushGuardFlag(report);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean getPushGuardFlag()  {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.getPushGuardFlag();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public void change2RecommendListForPush(boolean playForce) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.change2RecommendListForPush(playForce);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setFlvLoadControl(boolean isEnable)  {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setFlvLoadControl(isEnable);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static boolean isPlayModelMyclubLive(PlayableModel playableModel) {
		return playableModel != null && PlayableModel.KIND_MYCLUB_FLV.equals(playableModel.getKind());
	}

	public void play() {
		play(false);
	}

	public void play(boolean forcePlay) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.logToFile("ForwardVideoManager : play = " + mVideoAdState);
			if(mVideoAdState == MiniPlayer.STATE_PAUSED) {
				LocalBroadcastManager.getInstance(mAppCtx).sendBroadcastSync(new Intent(PlayerConstants.ACTION_PLAY_VIDEO_AD));
				return;
			}
			Logger.logToFile("XmPlayerManager : play forcePlay=" + forcePlay + ", " + Log.getStackTraceString(new Throwable()));

			PlayableModel model = getCurrSound();
			if (model != null && PlayableModel.KIND_TRACK.equals(model.getKind()) && mUbtSourceProvider != null) {
				String key = Constants.KEY_PLAY;
				if (model instanceof Track && (((Track) model).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY ||
						((Track) model).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4)) {
					key = Constants.KEY_DAILY_NEWS;
				}
				String ubtSource = mUbtSourceProvider.getPlayingSource(key);
				if (TextUtils.isEmpty(ubtSource) || ubtSource.contains("\"ubtSource\":\"[]\"")) {
					UbtSourceEmptyCollector.getInstance().collect(mAppCtx);
				}
				setUbtSource(ubtSource, false);
			}
			boolean canPlay = true;
			if (isPlayModelMyclubLive(model)) {
				// 付费且没权限的，不播放，先进房间
				if (!forcePlay && model instanceof Track && ((Track) model).isPaid() && !((Track) model).isAuthorized()) {
					canPlay = false;
					MyClubUtil.startRoom(mAppCtx, (Track) model);
				}
			}
			if (canPlay) {
				if (model != null) {
					UserInteractivePlayStatistics.callPlayerStart(model.getDataId());
				}
				mPlayerStub.startPlay();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 在XmPlayService中 playTrack如果是加密声音需要经过网络请求后才会播放，如果在请求过程中执行XmPlayManager.play操作是无效的。
	 * 此时可以通过该方法 set 在网络请求后再判断下这个值。
	 * 不过还是有一个坑，如果在网络请求后set 是无效的 还会影响后面播放器列表的播放，故还要在onPlayStart以及onSoundSwitch时set为false
	 * @param autoPlay
	 */
	public void setAutoPlayAfterGetPlayUrl(boolean autoPlay) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.setAutoPlayAfterGetPlayUrl(autoPlay);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 获取trackId所在的列表位置
	public int getTrackIndex(long trackId) {
		if (!isConnectedStatus()) {
			return -1;
		}
		try {
			return mPlayerStub.getTrackIndex(trackId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	public int getPlayCurrPositon() {
		int playCurr = 0;
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			playCurr = mPlayerStub.getPlayCurrPosition();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return playCurr;
	}

	public int getBufferedProgress() {
		if (!isConnectedStatus())
			return 0;

		try {
			return mPlayerStub.getTrackBufferPercentage();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public void playByTrackId(long trackId) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.logToFile("XmPlayManager playByTrackId " + trackId + ", log: " + Log.getStackTraceString(new Throwable()));
			mPlayerStub.playByTrackId(trackId);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void play(int index) {
		if (!checkConnectionStatus()) {
			return;
		}
		if (index > 0) {
			ignorePlayerToListenLogic(true);
		}
		try {
			Logger.logToFile("XmPlayManager play " + index + ", log: " + Log.getStackTraceString(new Throwable()));
			mPlayerStub.play(index);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setPlayIndex(int index) {
		if (!checkConnectionStatus()) {
			return;
		}
		if (index > 0) {
			ignorePlayerToListenLogic(true);
		}
		try {
			Logger.logToFile("XmPlayerManager setPlayIndex index=" + index + ",trace=" + Log.getStackTraceString(new Throwable()));
			mPlayerStub.setPlayIndex(index);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Deprecated
	// 需要传入具体原因，应该使用 pause(int reason)
	public void pause() {
		pause(PauseReason.NONE);
	}

	public void pause(int reason) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.log("ForwardVideoManager : isPlaying  pause 2 =  " + mVideoAdState);
			if(mVideoAdState == MiniPlayer.STATE_STARTED) {
				LocalBroadcastManager.getInstance(mAppCtx).sendBroadcastSync(new Intent(PlayerConstants.ACTION_PAUSE_VIDEO_AD));
				return;
			}

			Logger.logToFile("XmPlayerManager : pause " + Log.getStackTraceString(new Throwable()));

			mPlayerStub.pause(reason);

            if (XmAdsManager.getInstance(mAppCtx).isAdsPlaying()) {
				XmAdsManager.getInstance(mAppCtx).pauseAd();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void pauseAndCloseNotification() {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			if(mVideoAdState == MiniPlayer.STATE_STARTED) {
				LocalBroadcastManager.getInstance(mAppCtx).sendBroadcastSync(new Intent(PlayerConstants.ACTION_PAUSE_VIDEO_AD));
				return;
			}

			mPlayerStub.pausePlayAndCloseNotification();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	public void reShowClosedNotification() {
		if (!isConnected()) {
			return;
		}
		try {
			mPlayerStub.reShowClosedNotification();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void stop() {
		stop(PauseReason.STOP_NONE);
	}

	public void stop(int reason) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.logToFile("XmPlayerManager : stop " + Log.getStackTraceString(new Throwable()));

			mPlayerStub.stop(reason);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void playPre() {
		playPre(true);
	}

	public void playPre(boolean willPlay) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.logToFile("XmPlayerManager : playPre " + Log.getStackTraceString(new Throwable()));

			mPlayerStub.playPre(willPlay);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 设置ubtSource相关信息到播放进程
	 * @param ubtSource json格式字符串，方便后续数据添加和删除
	 * @param updateCurrentTrack
	 */
	private void setUbtSource(String ubtSource, boolean updateCurrentTrack) {
		try {
			String key = updateCurrentTrack ?
					CrossProcessTransferValueManager.KEY_SET_PARAM_UBT_SOURCE_UPDATE_CURRENT_TRACK :
					CrossProcessTransferValueManager.KEY_SET_PARAM_UBT_SOURCE;
			mPlayerStub.setValueToPlayProcess(key, ubtSource);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void updateUbtSource(String cacheKey, boolean replacePageUbtSource) {
	    updateUbtSource(cacheKey, replacePageUbtSource, false);
    }

	public void updateUbtSource(String cacheKey, boolean replacePageUbtSource, boolean updateCurrentTrack) {
		if (mUbtSourceProvider != null) {
			String ubtSource = mUbtSourceProvider.getUbtSource(cacheKey, replacePageUbtSource);
			setUbtSource(ubtSource, updateCurrentTrack);
			if (!TextUtils.isEmpty(ubtSource)) {
				try {
					JSONObject object = new JSONObject(ubtSource);
					if (TextUtils.isEmpty(object.optString("ubtTraceId")) && ubtSource.contains("38745") && !ubtSource.contains("3047")) {
						UbtSourceEmptyCollector.getInstance().collect(mAppCtx);
					}
//					if (TextUtils.isEmpty(ubtSource) || ubtSource.contains("\"ubtSource\":\"[]\"")) {
//						UbtSourceEmptyCollector.getInstance().collect(mAppCtx);
//					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			try {
				mPlayerStub.onUbtSourceChanged(cacheKey);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setUbtSourceProvider(IUbtSourceProvider provider) {
		this.mUbtSourceProvider = provider;
	}

	public void setSourceSynchronizer(ISourceSynchronizer synchronizer) {
		this.mUbtSourceSynchronizer = synchronizer;
	}

	@Deprecated
    public void setUbtTraceId(java.lang.String ubtTraceId, java.lang.String ubtPrevTraceId) {
        try {
            mPlayerStub.setValueToPlayProcess("ubtTraceId", ubtTraceId);
            mPlayerStub.setValueToPlayProcess("ubtPrevTraceId", ubtPrevTraceId);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void playNext() {
		playNext(true);
	}

	public void playNext(boolean willPlay) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.logToFile("XmPlayerManager : playNext1 " + Log.getStackTraceString(new Throwable()));
			mPlayerStub.playNext(willPlay);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setNextPlayIndex() {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			if (mPlayerStub != null) {
				int currentIndex = mPlayerStub.getCurrIndex();
				Logger.logToFile("XmPlayerManager setNextPlayIndex index=" + (currentIndex + 1) + ",trace=" + Log.getStackTraceString(new Throwable()));
				mPlayerStub.setPlayIndex(currentIndex + 1);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setPrePlayIndex() {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			if (mPlayerStub != null) {
				int currentIndex = mPlayerStub.getCurrIndex();
				Logger.logToFile("XmPlayerManager setPrePlayIndex index=" + (currentIndex - 1) + ",trace=" + Log.getStackTraceString(new Throwable()));
				mPlayerStub.setPlayIndex(currentIndex - 1);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setPlayMode(PlayMode mode) {
		setPlayMode(mode, false);
	}

	public void setPlayMode(PlayMode mode, boolean withoutNotify) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			//需求：一键听的播放顺序固定为顺序播放, setPlayMode的地方太多，无法控制。所有播放一键听时，在这里做过滤
			if (isFromOneKeyPlay(getCurrSound())) {
				mPlayerStub.setPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_LIST.toString());
				Logger.logToFile("XmPlayerManager setPlayMode,isFromOneKeyPlay=true: " + mode + "," + Log.getStackTraceString(new Throwable()));
				return;
			}

			ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
			if (!withoutNotify && commonBusiService != null && commonBusiService.isInTransferState()) {
				if (getPlayMode() != mode) {
					commonBusiService.setPlayMode(PlayModeUtilKt.playModeToStr(mode));
				}
			}

			mPlayerStub.setPlayMode(mode.toString());
			Logger.logToFile("XmPlayerManager setPlayMode: " + mode + "," + Log.getStackTraceString(new Throwable()));
			if (mOnPlayModeChanges != null) {
				for (IOnPlayModeChange listener : mOnPlayModeChanges) {
					listener.onPlayModeChange(mode);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isFromOneKeyPlay(PlayableModel playableModel) {
		return playableModel instanceof Track &&
				(((Track) playableModel).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY ||
				((Track) playableModel).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) &&
				PlayableModel.KIND_TRACK.equals(playableModel.getKind());
	}

	/**
	 * tempo 倍速0.6 0.8 1.0 1.2 1.4 1.6 1.8
	 * 参数默认值SoundTouch.TEMPO_DEFAULT
	 * @param tempo Normal tempo = 1.0, smaller values represent slower tempo, larger faster tempo.
	 */
	private Float mTempo;
	public void setTempo(float tempo){
		mTempo = tempo;

		if (!checkConnectionStatus()) {
			return;
		}

		boolean willChange = false;
		ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
		if (commonBusiService != null && commonBusiService.isInTransferState()) {
			willChange = Math.abs(getTempo() - tempo) > 0.1;
		}

		try {
			Logger.log("setTempo tempo:"+tempo);
			mPlayerStub.setTempo(tempo);
		} catch (Exception e) {
			e.printStackTrace();
		}

		if (willChange) {
			commonBusiService.setTempo(tempo);
		}
	}

    private Float mQuickListenTempo;
    public void setQuickListenTempo(float tempo) {
        mQuickListenTempo = tempo;

        if (!checkConnectionStatus()) {
            return;
        }

        try {
            Logger.log("setQuickListenTempo tempo:"+tempo);
            mPlayerStub.setQuickListenTempo(tempo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public PlayMode getPlayMode() {
		if (!isConnectedStatus()) {
			return PlayMode.PLAY_MODEL_LIST;
		}
		try {
			return PlayMode.valueOf(mPlayerStub.getPlayMode());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return PlayMode.PLAY_MODEL_LIST;
	}

	/**
	 * 内部调用函数，外部不需要调用，如需设置pagesize,请调用CommonRequest.getInstanse().
	 * setDefaultPagesize();
	 */
	public void setAutoLoadPageSizeInner(int pageSize) {
		if (sPageSize == pageSize) {
			return;
		}
		sPageSize = pageSize;
		try {
			// 这里做这个判断是因为有第三方只想设置请求的默认页数 但是这个设置的时候会将xmPlayService启动起来
			// 这个时候第三方再想用 setOnConnectedListerner 这个回调的时候就不起作用了.
			boolean b = !mConnected || mPlayerStub == null || mPlayerStub.asBinder() == null || !mPlayerStub.asBinder().isBinderAlive();
			if(!b) {
				setDefaultPageSize();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void setDefaultPageSize() throws RemoteException {
		if (!checkConnectionStatus()) {
			return;
		}
		if (mPlayerStub != null) {
			mPlayerStub.setPageSize(sPageSize);
		}
	}

	/**
	 * 获取播放列表
	 *
	 * 注：大数据的跨进程通信，尽量减少该函数调用次数
	 *
	 * @return
	 */
	public List<Track> getPlayList() {
		if (!isConnectedStatus()) {
			return null;
		}
		mCachedPlayList = new ArrayList<Track>();
		try {
			int i = 0;
			while (true) {
				List<Track> tracks = mPlayerStub.getPlayList(i);
				if (tracks == null) {
					return mCachedPlayList;
				}
				mCachedPlayList.addAll(tracks);
				if (tracks.size() < PlayerConstants.MaxNum) {
					return mCachedPlayList;
				}
				i++;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mCachedPlayList;
	}

	private List<Track> mCachedPlayList;  //TODO by easoll: 缓存播放列表


	public int getPlayListSize() {
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			return mPlayerStub.getPlayListSize();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public Track getNextTrack() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getNextTrack();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public int getCurrentPlayMethod() {
		if (!checkConnectionStatus()) {
			return -1;
		}
		try {
			return mPlayerStub.getCurrentPlayMethod();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	public Track getPreTrack() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getPreTrack();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public Track getTrack(int index) {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getTrack(index);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public void setPlayScene(int playScene) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setPlayScene(playScene);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private boolean isElderlyModel;
	public void setElderlyMode(boolean elderlyMode) {
		isElderlyModel = elderlyMode;
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setElderlyMode(elderlyMode);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getPlayScene() {
		if (!isConnectedStatus()) {
			return XmPlayerService.PLAY_SCENE_TYPE_DEFAULT;
		}
		try {
			return mPlayerStub.getPlayScene();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return XmPlayerService.PLAY_SCENE_TYPE_DEFAULT;
	}

	/**
	 * playSchedule:(设置直播播放排期表并且播放) -1 时直接播放排期表中的当前直播
	 * 该方法为了在直播播放页实现点击"上一首"播放排期表中上一首回听
	 */
	public boolean playSchedule(List<Schedule> schedules, int index) {
		if (!checkConnectionStatus()) {
			return false;
		}
		if (schedules == null) {
			return false;
		}
		try {
			mPlayerStub.setPlayList(null, ModelUtil.toTrackList(schedules));
			if (index == -1) {
				for (int i = 0; i < schedules.size(); i++) {
					String time = schedules.get(i).getStartTime() + "-"
							+ schedules.get(i).getEndTime();
					if (BaseUtil.isInTime(time) == 0) {
						index = i;
						break;
					}
				}
			}
			Logger.logToFile("playSchedule " + index + ", log: " + Log.getStackTraceString(new Throwable()));
			mPlayerStub.play(index);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * playRadio:(把radio当作track) 该方法中没有播放排期表
	 * @deprecated 已过时,请使用 playLiveRadioForSDK
	 */
	public boolean playRadio(Radio radio) {
		if (!checkConnectionStatus()) {
			return false;
		}
		if (radio == null) {
			return false;
		}
		CommonTrackList commonTrackList = new CommonTrackList();
		List<Track> trackList = new ArrayList<Track>();
		trackList.add(ModelUtil.radioToTrack(radio, false));
		commonTrackList.setTracks(trackList);
		commonTrackList.setTotalCount(1);
		commonTrackList.setTotalPage(1);
		playList(commonTrackList, 0);
		return true;
	}

	/**
	 * 播放本地直播
	 */
	public boolean playActivityRadio(Radio radio) {
		if (!checkConnectionStatus()) {
			return false;
		}
		if (radio == null) {
			return false;
		}
		CommonTrackList commonTrackList = new CommonTrackList();
		List<Track> trackList = new ArrayList<Track>();
		trackList.add(ModelUtil.radioToTrack(radio, true));
		commonTrackList.setTracks(trackList);
		commonTrackList.setTotalCount(1);
		commonTrackList.setTotalPage(1);
		playList(commonTrackList, 0);
		return true;
	}

	/**
	 * 设置播放列表，并且自动播放
	 * 需要注意 <<<<<在使用前需要注意全局资源位是否被提前设置好，如果没有，推荐使用PlayTools.playCommonList或者PlayTools.playList方法>>>>>>
	 * @param list
	 * @param startIndex
	 */
	public void playList(List<Track> list, int startIndex) {
		if (!checkConnectionStatus()) {
			return;
		}
		if (list == null || list.size() == 0) {
			Logger.i(TAG, "Empty TrackList");
			return;
		}
		setPlayList(null, list, startIndex, true);
	}

	/**
	 * 设置播放列表，并且自动播放
	 * 需要注意 <<<<<在使用前需要注意全局资源位是否被提前设置好，如果没有，推荐使用PlayTools.playCommonList或者PlayTools.playList方法>>>>>>
	 * @param list
	 * @param startIndex
	 */
	public void playList(CommonTrackList list, int startIndex) {
		if (!checkConnectionStatus()) {
			return;
		}
		if (list == null || list.getTracks() == null
				|| list.getTracks().size() == 0) {
			return;
		}
		UserInteractivePlayStatistics.callPlayerStart(list.getTracks(), startIndex);

		setPlayList(list.getParams(), list.getTracks(), startIndex, true);
	}

	/**
	 * 设置播放列表和播放声音的index,但是不会自动播放
	 *
	 * @param list
	 * @param startIndex
	 */
	public void setPlayList(List<Track> list, int startIndex) {
		if (!checkConnectionStatus()) {
			return;
		}
		if (list == null || list.size() == 0) {
			return;
		}
		setPlayList(null, list, startIndex, false);
	}

	/**
	 * 设置播放列表和播放声音的index,但是不会自动播放
	 *
	 * @param commonTrackList
	 * @param startIndex
	 */
	public void setPlayList(CommonTrackList commonTrackList, int startIndex) {
		if (!checkConnectionStatus()) {
			return;
		}
		if (commonTrackList == null || commonTrackList.getTracks() == null
				|| commonTrackList.getTracks().size() == 0) {
			return;
		}
		setPlayList(commonTrackList.getParams(), commonTrackList.getTracks(),
				startIndex, false);
	}

	public boolean isPlayingFeedTracks() {
		Map<String, String> listParam = getPlayListOriginParams();
		if (listParam != null) {
			String playPageType = listParam.get(ConstantsOpenSdk.KEY_PLAY_PAGE_TYPE);
			if (playPageType == null || TextUtils.isEmpty(playPageType)) {
				return false;
			}
			try {
				int playPageTypeInt = Integer.parseInt(playPageType);
				return playPageTypeInt == ConstantsOpenSdk.PLAY_PAGE_TYPE_FEED;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return false;
	}

	public int getSavedPlayMode(Context context) {
		return PlayListMMKVUtil.getInstance(context).getIntCompatFromTingDataSp(
				PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PLAY_MODE, XmPlayListControl.PlayMode.PLAY_MODEL_LIST.ordinal());
	}

	public void setTempPodCastPlayListMode(boolean tempPodCastPlayListMode) {
		mTempPodCastPlayListMode = tempPodCastPlayListMode;
	}

	/**
	 * 当前是否在播放播客类型的声音（根据播放来源判断）
	 * 如果要判断播放页的播放列表是否为播客类型，请使用 ToListenUtil.isPlayingPodCastPlayList()
	 * */
	public boolean isPlayingPodCastPlayList() {
		if (!checkConnectionStatus()) {
			return false;
		}
		try {
			Map<String, String> params = getPlayListParams();
			if (params != null) {
				String str = params.get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM);
				if (!TextUtils.isEmpty(str) && TextUtils.equals(str, DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN)) {
					return true;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	// 设置播放的列表
	private void setPlayList(Map<String, String> paraMap, List<Track> tracks,
			int startIndex, boolean willPlay) {

		if (!checkConnectionStatus()) {
			return;
		}
		ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
		if (commonBusiService != null && willPlay) {
			commonBusiService.checkNeedsaveAudioPlayListCache();
		}
		try {
			if (tracks == null) {
				return;
			}
			if (mTempPodCastPlayListMode) {
				if (paraMap == null) {
					paraMap = new HashMap<>();
				}
				paraMap.put(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM, DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN);
				paraMap.put(DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT, DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT_DOWN);
			}
			if (!mTempPodCastPlayListMode && paraMap != null && DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN.equals(paraMap.get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM))) {
				mTempPodCastPlayListMode = true;
			}
			setTempPodCastPlayListMode(false);
			ignorePlayerToListenLogic(true);
//			boolean isOldListPodCastMode = isPlayingPodCastPlayList();
//			boolean isOldListToListenMode = isPlayingToListenTracks();
//			boolean isNewListToListenMode = false;
//			boolean isNewListPodCastMode = false;
//			if (tracks.size() > 0) {
//				isNewListToListenMode = tracks.get(0).getIsFromToListenTrack() == 1;
//			}
//			if (paraMap != null) {
//				// 播客播放列表采用顺序播放
//				String playPageType = paraMap.get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM);
//				if (playPageType != null && !TextUtils.isEmpty(playPageType)) {
//					try {
//						if (DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN.equals(paraMap.get(DTransferConstants.PARAM_SUB_PLAY_LIST_ITEM))) {
//							isNewListPodCastMode = true;
//						}
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//				}
//			}
//			if (isNewListToListenMode || isNewListPodCastMode) {
//				setPlayMode(XmPlayListControl.PlayMode.PLAY_MODEL_LIST);
//			} else if (isOldListPodCastMode || isOldListToListenMode) {
//				// 从待播、播客改回普通列表
//				int playMode = getSavedPlayMode(mAppCtx);
//				setPlayMode(XmPlayListControl.PlayMode.getIndex(playMode));
//			}

			if (willPlay && startIndex < tracks.size()) {
				Track track = tracks.get(startIndex);
				if (PlayableModel.KIND_TRACK.equals(track.getKind())) {
					String type = Constants.KEY_PLAY;
					if (track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY || track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) {
						type = Constants.KEY_DAILY_NEWS;
					}
					updateUbtSource(type, track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4 || Constants.KEY_PLAY.equals(type));
				}
			}

			if (FireworkApi.getInstance().isInFireworkEarning()) {
				PlayableModel model = getCurrSound();
				if (model != null) {
					boolean same = false;
					for (Track track : tracks) {
						if (track.getDataId() == model.getDataId()) {
							same = true;
							break;
						}
					}
					if (!same) {
						if (model instanceof Track) {
							Track track = (Track) model;
							if (track.getAlbum() != null) {
								FireworkApi.getInstance().playEnd(track.getAlbum().getAlbumId());
							}
						} else {
							FireworkApi.getInstance().playEnd(0);
						}
					}
				}
			}

			int maxNum = PlayerConstants.MaxNum;
			int num = tracks.size();

			long startTime = System.currentTimeMillis();

			if (num < maxNum) {
				mPlayerStub.setPlayList(paraMap, tracks);
			} else {
				for (int i = 0; i < num / maxNum; i++) {
					if (i == 0) {
						mPlayerStub.setPlayList(paraMap,
								tracks.subList(i * maxNum, (i + 1) * maxNum));
					} else {
						mPlayerStub.addPlayList(tracks.subList(i * maxNum,
								(i + 1) * maxNum));
					}
				}
				int remainder = num % maxNum;
				if (remainder != 0) {
					int lastNum = maxNum * (num / maxNum);
					mPlayerStub.addPlayList(tracks.subList(lastNum, lastNum
							+ remainder));
				}
			}

			if(mPlayListChanges != null) {
				for (IOnPlayListChange playListChange : mPlayListChanges) {
				    playListChange.onPlayListChange();
				}
			}

			Logger.d("zimotag", "XmPlayerManager setPlayList: " + (System.currentTimeMillis() - startTime));

			if (willPlay) {
				Logger.logToFile("XmPlayManager setPlayList play " + startIndex + ",num=" + num + ", log: " + Log.getStackTraceString(new Throwable()));
				mPlayerStub.play(startIndex);
			} else {
				Logger.logToFile("XmPlayerManager setPlayList setPlayIndex index=" + startIndex + ", trace=" + Log.getStackTraceString(new Throwable()));
				mPlayerStub.setPlayIndex(startIndex);
			}
		} catch (Exception e) {
			e.printStackTrace();
			IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
			if (xdcsPost != null) {
				xdcsPost.statErrorToXDCS("playFragmentBlack", "setPlayList:_____" + Log.getStackTraceString(e));
			}
		}
	}

	@SuppressWarnings("unchecked")
	public CommonTrackList getCommonTrackList() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			CommonTrackList commonTrackList = new CommonTrackList();
			commonTrackList.setParams(mPlayerStub.getParam());
			commonTrackList.setTracks(getPlayList());
			return commonTrackList;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public boolean isOnlineSource() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.isOnlineSource();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean isPlaying() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			// 因为视频广告的状态不能放到播放器所以这样写
			if(mVideoAdState == MiniPlayer.STATE_STARTED) {
				return true;
			}
			if (mPlayerStub.isPlayingConsiderMixPlayer()) {
				return true;
			}
			return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean isPlayServicePlaying() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			if (mPlayerStub.isPlayingConsiderMixPlayer()) {
				return true;
			}
			return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean isAdsActive() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			// 因为视频广告的状态不能放到播放器所以这样写
			if(mVideoAdState == MiniPlayer.STATE_STARTED) {
				return true;
			}
			return mPlayerStub.isAdsActive();
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean hasPreSound() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.hasPreSound();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return true;
	}

	public boolean hasNextSound() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.hasNextSound();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return true;
	}

	public void setCanPlayOneKeyPatch(boolean canPlayOneKeyPatch) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setCanPlayOneKeyPatch(canPlayOneKeyPatch);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setCanPlayEndPlayRecommendPatch(boolean canPlayEndPlayRecommendPatch) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setCanPlayEndPlayRecommendPatch(canPlayEndPlayRecommendPatch);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getDuration() {
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			return mPlayerStub.getDuration();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public void seekToByPercent(float percent) {
		seekTo((int) (getDuration() * percent));
	}

	public void seekTo(int pos) {
		if (!checkConnectionStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager : seekTo pos=" + pos + "  " + Log.getStackTraceString(new Throwable()));
		ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
		if (commonBusiService != null && commonBusiService.isInTransferState()) {
			commonBusiService.seekTo(pos);
			updatePlayProgressChange(pos, getDuration());
			return;
		}
		try {
			// 如果视频广告处在这些状态下 不进行播放器seek操作
			if (mVideoAdState == MiniPlayer.STATE_PREPARED || mVideoAdState == MiniPlayer.STATE_STARTED ||
					mVideoAdState == MiniPlayer.STATE_PAUSED) {
				Logger.i(TAG, "mVideoAdState:" + mVideoAdState);
				return;
			}
			mPlayerStub.seekTo(pos);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void clearPlayCache() {

		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.clearPlayCache();
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public static long getPlayCacheSize() {
		return PlayerUtil.getPlayCacheSize();
	}

	private void handleSoundPrepared() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onSoundPrepared();
		}
	}

	public void updateTrackInfo(Track track) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.updateTrackInfo(track);
			Logger.logToFile("XmPlayerManager", "updateTrackInfo " + TrackUtil.trackToStr(track));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void updateTrackInPlayList(Track track) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			boolean isupdated = mPlayerStub.updateTrackInPlayList(track);
            if(isupdated) {
                // 这里为了解决支付成功 更新了列表 但是mCurModel 还没有变 getCurrSound()还是上次的mCurModel
                mCurModel = null;

				printCurModel("updateTrackInPlayList");
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void updateTrackListInPlayList(List<Track> trackList) {
        if (!isConnectedStatus()) {
            return;
        }

        try {
            if (trackList == null || trackList.size() == 0) {
                return;
            }

            int maxNum = PlayerConstants.MaxNum;
            int num = trackList.size();

            boolean isupdated = false;
            if (num < maxNum) {
                isupdated = mPlayerStub.updateTrackListInPlayList(trackList);
            } else {
                for (int i = 0; i < num / maxNum; i++) {
                    if (mPlayerStub.updateTrackListInPlayList(trackList.subList(i * maxNum,
                            (i + 1) * maxNum))) {
                        isupdated = true;
                    }
                }

                int remainder = num % maxNum;
                if (remainder != 0) {
                    int lastNum = maxNum * (num / maxNum);
                    if (mPlayerStub.updateTrackListInPlayList(trackList.subList(lastNum,
                            lastNum + remainder))) {
                        isupdated = true;
                    }
                }
            }

            if (isupdated) {
                // 这里为了解决支付成功 更新了列表 但是mCurModel 还没有变 getCurrSound()还是上次的mCurModel
                mCurModel = null;
				printCurModel("updateTrackListInPlayList");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

	public void updateTrackDownloadUrlInPlayList(Track track) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.updateTrackDownloadUrlInPlayList(track);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * getCurrPlayType:(获取当前的play类型，分为直播和点播) 点播分为 track以及schedule中的回听
	 * 直播分为radio以及schedule中的直播 区分当前的播放进度条是否可以拖动
	 */
	public int getCurrPlayType() {
		if (!isConnectedStatus()) {
			return -1;
		}
		try {
			return mPlayerStub.getPlaySourceType();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	public String getCurPlayUrl() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getCurPlayUrl();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public String getCurTrackHttpUrl() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getCurTrackHttpUrl();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	private Float mLastLeftVolume;
	private Float mLastRightVolume;
	public void setVolume(float leftVolume, float rightVolume) {
		mLastLeftVolume = leftVolume;
		mLastRightVolume = rightVolume;
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			if(Logger.isDebug) {
				Logger.logToFile("XmPlayerManager : setVolume leftVolume=" + leftVolume + "  " + Log.getStackTraceString(new Throwable()));
			}
			mPlayerStub.setVolume(leftVolume, rightVolume);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public float getVolume() {
		if (!isConnectedStatus()) {
			return XMediaplayerJNI.VOLUME_DEFAULT;
		}
		try {
			return mPlayerStub.getVolume();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return XMediaplayerJNI.VOLUME_DEFAULT;
	}

	public void setCdnConfigModel(CdnConfigModel cdnConfigModel) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setPlayCdnConfigureModel(cdnConfigModel);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private PlayableModel mCurModel;
	private Boolean checkLocalReadTrackSwitch;

	private void handleSoundChange(PlayableModel lastModel,
			PlayableModel curModel) {
		mCurModel = curModel;

		printCurModel("handleSoundChange");

		mVideoAdState = MiniPlayer.STATE_IDLE;
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onSoundSwitch(lastModel, curModel);
		}
	}

	private void handlePlayStop() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onPlayStop();
		}
	}

	private void handlePlayStart() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onPlayStart();
		}
	}

	private void handlePlayProgressChange(int currPos, int duration) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onPlayProgress(currPos, duration);
		}
	}

	private void handleOnRenderingStart() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onRenderingStart();
		}
	}

	private void handleOnRotationChanged(int rotationAngle) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onRotationChanged(rotationAngle);
		}
	}

	private void handleOnVideoSizeChanged(int width, int height) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onVideoSizeChanged(width, height);
		}
	}

	private void handlePlayPause() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onPlayPause();
		}
	}

	private void handlePlayComplete() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onSoundPlayComplete();
		}
	}

	private void handlePlayError(XmPlayerException exception) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onError(exception);
		}
	}

	private void handleBufferChange(int position) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onBufferProgress(position);
		}
	}

	private void handleBufferStatusChange(boolean buffering) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			if (buffering) {
				l.onBufferingStart();
			} else {
				l.onBufferingStop();
			}
		}
	}

	private void handleStartGetAdsInfo(int playMethod, boolean duringPlay ,boolean paused) {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onStartGetAdsInfo(playMethod, duringPlay, paused);
		}
	}

	private void handleGetAdsInfo(AdvertisList ads) {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onGetAdsInfo(ads);
		}
	}

	private void handleGetForwardVideo(List<Advertis> advertis) {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			if(l instanceof IXmAdsStatusListenerExpand) {
				((IXmAdsStatusListenerExpand) l).onGetForwardVideo(advertis);
			}
		}
	}

	private void handleAdsBufferingStart() {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onAdsStartBuffering();
		}
	}

	private void handleAdsBufferingStop() {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onAdsStopBuffering();
		}
	}

	private void handleStartPlayAds(Advertis ad, int position) {
		if(ad != null) {
			Logger.log("XmPlayerManager : handleStartPlayAds " + ad.getAdSoundTime());
		}
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onStartPlayAds(ad, position);
		}
	}

	private void handleCompletePlayAds() {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onCompletePlayAds();
		}
	}

	private void handlePlayAdsError(int what, int extra) {
		for (IXmAdsStatusListener l : mAdsStatusListeners) {
			l.onError(what, extra);
		}
	}

	private void handleHistoryChanged(){
		mHandleHistoryListChanged = true;
		for(IXmDataChangedCallback l: mHistoryChangedCallbacks){
			l.onDataChanged();
		}
	}

	private void handleHistoryInited() {
		for (IXmDataChangedCallback l : mHistoryChangedCallbacks) {
			l.onDataInit();
		}
	}

	public boolean handleHistoryListChanged() {
		return mHandleHistoryListChanged;
	}

	private class UIHandler extends Handler {
		public UIHandler(Looper mainLooper) {
			super(mainLooper);
		}

		@Override
		public void handleMessage(Message msg) {
			switch (msg.what) {
				case MSG_PLAY_START:
					handlePlayStart();
					break;

				case MSG_PLAY_PAUSE:
					handlePlayPause();
					break;

				case MSG_PLAY_STOP:
					handlePlayStop();
					break;

				case MSG_PLAY_COMPLETE:
					handlePlayComplete();
					break;

				case MSG_SOUND_PREPARED:
					handleSoundPrepared();
					break;

				case MSG_BUFFER_STATUS_CHANGE:
					if (msg.obj instanceof Boolean) {
						handleBufferStatusChange((Boolean) msg.obj);
					}
					break;

				case MSG_PROGRESS_CHANGE:
					handlePlayProgressChange(msg.arg1, msg.arg2);
					break;

				case MSG_SOUND_CHANGE:
					Object[] args = (Object[]) msg.obj;
					handleSoundChange((PlayableModel) args[0],
							(PlayableModel) args[1]);
					break;
				case MSG_BUFFER_PROGRESS_CHANGE:
					handleBufferChange(msg.arg1);
					break;

				case MSG_PLAY_ERROR:
					handlePlayError((XmPlayerException) msg.obj);
					break;

				case MSG_START_GET_ADS_INFO:
					handleStartGetAdsInfo(msg.arg1 ,msg.arg2 == 1 ,msg.obj instanceof Boolean ? (boolean) msg.obj : false);
					break;

				case MSG_GET_ADS_INFO:
					handleGetAdsInfo((AdvertisList) msg.obj);
					break;
				case MSG_ON_GET_FORWARD_VIDEO:
					handleGetForwardVideo((List<Advertis>) msg.obj);
					break;

				case MSG_ADS_BUFFERING_START:
					handleAdsBufferingStart();
					break;

				case MSG_ADS_BUFFERING_STOP:
					handleAdsBufferingStop();
					break;

				case MSG_START_PLAY_ADS:
					handleStartPlayAds((Advertis) msg.obj, msg.arg1);
					break;

				case MSG_COMPLETE_PLAY_ADS:
					handleCompletePlayAds();
					break;

				case MSG_PLAY_ADS_ERROR:
					handlePlayAdsError(msg.arg1, msg.arg2);
					break;
				case MSG_OLD_CHARGE_TRACK_NEED_REDOWNLOAD:
					handleOldChargeTrackNeedRedownload((Track) msg.obj);
					break;
				case MSG_CLOSE_APP:
					handleCloseApp();
					break;
				case MSG_HISTORY_CHANGED:
					handleHistoryChanged();
					break;
				case MSG_HISTORY_INITED:
					handleHistoryInited();
					break;
				case MSG_FLV_DATA_OUT:
					if(msg.obj instanceof FlvData) {
						handleFlvDataOut(msg.arg1, ((FlvData) msg.obj).seiInfo, ((FlvData) msg.obj).bufferDuration);
					}
					break;
				case MSG_MIX_START:
					handleMixPlayerStart();
					break;
				case MSG_MIX_PAUSE:
				    handleMixPlayerPause();
					break;
                case MSG_MIX_STOP:
					handleMixPlayerStop();
					break;
                case MSG_MIX_COMPLETE:
					handleMixPlayerComplete();
					break;
                case MSG_MIX_ERROR:
                    Bundle mixError = msg.getData();
                    if (mixError != null) {
                        handleMixPlayerError(mixError.getString("url"), mixError.getInt("code"), mixError.getString("msg"));
                    }
                    break;
				case MSG_MIX_PROGRESS_UPDATE:
					onMixProgressUpdate(msg.arg1);
                    break;
				case MSG_MIX_STATUS_UPDATE:
					Bundle mixStus = msg.getData();
					if (mixStus != null) {
						double key = mixStus.getDouble("key");
						long curPosition = mixStus.getLong("curPosition");
						boolean isPlaying = mixStus.getBoolean("isPlaying", false);
						String state = mixStus.getString("state");
						handleMixPlayerStusUpdate(key, isPlaying, state, curPosition);
					}
                    break;
				case MSG_MIX_SOUND_COMPLETE:
					Bundle completeStus = msg.getData();
					if (completeStus != null) {
						double key = completeStus.getDouble("key");
						handleMixPlayerSoundComplete(key);
					}
					break;
			    case MSG_MIX_TRACK_CLEARED:
			    	handleMixPlayerCleared();
				    break;

				case MSG_PLAN_TERMINATE_ON_TIME_OUT:
					handlePlanTerminateOnTimeOut(msg.arg1);
					break;
				case MSG_PLAN_TERMINATE_ON_LEFT_TIME_CHANGED:
					handlePlanTerminateOnTimeChanged(msg.arg1, msg.arg2);
					break;
				case MSG_PLAN_TERMINATE_ON_LEFT_SERIES_CHANGED:
					handlePlanTerminateOnTimeSeriesChange(msg.arg1, msg.arg2);
					break;
				case MSG_PLAN_TERMINATE_ON_CANCEL:
					handlePlanTerminateOnCancel();
					break;

                case MSG_PLAN_TERMINATE_ON_TIME_OUT_FOR_QUICK_LISTEN:
					handlePlanTerminateOnTimeOutForQuickListen(msg.arg1);
					break;
				case MSG_PLAN_TERMINATE_ON_LEFT_TIME_CHANGED_FOR_QUICK_LISTEN:
					handlePlanTerminateOnTimeChangedForQuickListen(msg.arg1, msg.arg2);
					break;
				case MSG_PLAN_TERMINATE_ON_LEFT_SERIES_CHANGED_FOR_QUICK_LISTEN:
					handlePlanTerminateOnTimeSeriesChangeForQuickListen(msg.arg1, msg.arg2);
					break;
				case MSG_PLAN_TERMINATE_ON_CANCEL_FOR_QUICK_LISTEN:
					handlePlanTerminateOnCancelForQuickListen();
					break;

				case MSG_LOAD_HISTORY_SUCCESS:
					handleLoadHistorySuccess();
					break;
				case MSG_SOUND_PATCH_START:
					if(msg.obj instanceof SoundPatchInfo) {
						handleSoundPatchStart((SoundPatchInfo) msg.obj);
					}
					break;
				case MSG_SOUND_PATCH_COMPLETE:
					if(msg.obj instanceof SoundPatchInfo) {
						handleSoundPatchComplete((SoundPatchInfo) msg.obj);
					}
					break;
				case MSG_SOUND_PATCH_ERROR:
					if(msg.obj instanceof SoundPatchInfo) {
						handleSoundPatchError((SoundPatchInfo) msg.obj, msg.arg2, msg.arg1);
					}
					break;
				case MSG_ON_HIGHTPLUS_NO_AUTHORIZED:
					handleHightPlusNoAuthorized();
					break;
				case MSG_ON_REQUEST_PLAY_URL_BEGIN:
					handleRequestPlayUrlBegin();
					break;
				case MSG_ON_REQUEST_PLAY_URL_SUCCESS:
					handleRequestPlayUrlSuccess();
					break;
				case MSG_ON_REQUEST_PLAY_URL_FAIL:
					handleRequestPlayUrlFail(msg.arg1 , (String) msg.obj);
					break;
				case MSG_ON_AUDIO_AUDITION_OVER:
					handleAudioAuditionOver((Track) msg.obj);
					break;
				case MSG_BSAE_INFO_SUCCESS:
					handleBaseInfoRequestSuccess((Track) msg.obj);
					break;
				case MSG_BSAE_INFO_ON_ERROR:
					handleBaseInfoRequestOnError((BaseInfoOnErrorModel) msg.obj);
					break;
				case MSG_COMMERCIAL_SOUND_PATCH_STATUS_CHANGE:
					handleCommercialSoundPatchStatusChange(msg.arg1);
					break;
				case MSG_COMMERCIAL_SOUND_PATCH_RE_REGISTER:
					handleCommercialSoundPatchReRegister();
					break;
				case MSG_SKIP_HEAD_TAIL_MODEL_FETCH:
					handleSkipHeadTailModelFetch((SkipHeadTailModel)msg.obj);
					break;
				case MSG_ON_RENDERING_START:
					handleOnRenderingStart();
					break;
				case MSG_ON_VIDEO_SIZE_CHANGED:
					handleOnVideoSizeChanged(msg.arg1, msg.arg2);
					break;
				case MSG_ON_VIDEO_ROTATION_CHANGED:
					handleOnRotationChanged(msg.arg1);
					break;
				case MSG_SOUND_EFFECT_START:
					handleSoundEffectStart(msg.obj);
					break;
				case MSG_SOUND_EFFECT_ERROR:
					handleSoundEffectError(msg.obj);
					break;
				case MSG_SOUND_EFFECT_END:
					handleSoundEffectEnd(msg.obj);
					break;
				case MSG_SOUND_EFFECT_INITIAL_INFO:
					handleSoundEffectInitialInfo(msg.obj);
					break;
				case MSG_FLV_META_DATA_INFO:
					handleFlvMetaInfo(msg.obj);
					break;
				case MSG_ON_CHILD_AI_TIMBRE_URL_GET:
					handleChildAtTimbreUrlGet(msg.arg1 == 1, msg.arg2 == 1, (String) msg.obj);
					break;
				case MSG_FREE_LISTEN_TIME_CHANGE:
					handleFreeListenTimeChange(msg.arg1, msg.arg2 == 1, (int) msg.obj);
					break;
				case MSG_FREE_LISTEN_TIME_EMPTY:
					handleFreeListenTimeEmpty();
					break;
				case MSG_FREE_LISTEN_ALL_DAY_EXPIRE:
					for (IFreeListenTimeListener listener : mFreeListenTimeListeners) {
						listener.onExitAllDayFreeListen();
					}
				break;
				case MSG_LISTEN_TIME_CHANGE:
					handleListenTimeChange(msg.arg1);
					break;
				default:
					break;
			}
		}

	}

	private void handleFreeListenTimeChange(int time, boolean byAddTime, int addedTime) {
		for (IFreeListenTimeListener listener : mFreeListenTimeListeners) {
			listener.onListenTimeChange(time, byAddTime, addedTime);
		}
	}

	private void handleFreeListenTimeEmpty() {
		for (IFreeListenTimeListener listener: mFreeListenTimeListeners) {
			listener.onListenTimeEmpty();
		}
	}

	private void handleListenTimeChange(int totalTime) {
		for (IListenTimeListener listener : mListenTimeListeners) {
			listener.onListenTimeChange(totalTime);
		}
	}

	private void handleBaseInfoRequestSuccess(Track track) {
		for (IXmPlayerBaseInfoRequestListener baseInfoRequestListener : mBaseInfoRequestListeners) {
			baseInfoRequestListener.onTrackBaseInfoBackSuccess(track);
		}
	}

	private void handleBaseInfoRequestOnError(BaseInfoOnErrorModel baseInfoOnErrorModel) {
		for (IXmPlayerBaseInfoRequestListener baseInfoRequestListener : mBaseInfoRequestListeners) {
			baseInfoRequestListener.onTrackBaseInfoBackError(baseInfoOnErrorModel);
		}
	}

	private void handleAudioAuditionOver(Track playableModel) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			if(l instanceof IXmPlayerStatusListenerExtension) {
				((IXmPlayerStatusListenerExtension) l).onAudioAuditionOver(playableModel);
			}
		}
	}

	private void handleRequestPlayUrlFail(int code, String msg) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			if(l instanceof IXmPlayerStatusListenerExtension) {
				((IXmPlayerStatusListenerExtension) l).onRequestPlayUrlError(code ,msg);
			}
		}
	}

	private void handleRequestPlayUrlSuccess() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			if(l instanceof IXmPlayerStatusListenerExtension) {
				((IXmPlayerStatusListenerExtension) l).onRequestPlayUrlSuccess();
			}
		}
	}

	private void handleRequestPlayUrlBegin() {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			if(l instanceof IXmPlayerStatusListenerExtension) {
				((IXmPlayerStatusListenerExtension) l).onRequestPlayUrlBegin();
			}
		}
	}

	private void handleSkipHeadTailModelFetch(SkipHeadTailModel skipHeadTailModel) {
		Intent intent = new Intent(INTENT_ACTION_SKIP_HEAD_TAIL_FETCH);
		intent.putExtra(KEY_SKIP_HEAD_TAIL_FETCH, skipHeadTailModel);
		LocalBroadcastManager.getInstance(mAppCtx).sendBroadcast(intent);
	}

	private void handleHightPlusNoAuthorized() {
		LocalBroadcastManager.getInstance(mAppCtx).sendBroadcast(new Intent(ACTION_HIGHTPLUS_NO_AUTHORIZED));
	}

	public void addSoundPatchStatusListener(ISoundPatchStatusListener l) {
		if (l != null && !mSoundPatchStatusListeners.contains(l)) {
			mSoundPatchStatusListeners.add(l);
		}
	}

	public void removeSoundPatchStatusListener(ISoundPatchStatusListener l) {
		if (null != l && mSoundPatchStatusListeners != null) {
			mSoundPatchStatusListeners.remove(l);
		}
	}

	public void addSoundEffectStatusListener(ISoundEffectStatusCallBackForMainManager l) {
		if (null != l && mSoundEffecttatusListeners != null) {
			mSoundEffecttatusListeners.add(l);
		}
	}

	public void removeSoundEffectStatusListener(ISoundEffectStatusCallBackForMainManager l) {
		if (null != l && mSoundEffecttatusListeners != null) {
			mSoundEffecttatusListeners.remove(l);
		}
	}

	public void addCommercialSoundPatchStatusCallBack(ICommercialSoundPatchControlStatusCallBack callBack) {
		if (null != callBack && !mSoundPatchControlStatusCallBacks.contains(callBack)) {
			mSoundPatchControlStatusCallBacks.add(callBack);
		}
	}

	public void removeCommercialSoundPatchStatusCallBack(ICommercialSoundPatchControlStatusCallBack callBack) {
		if (null != callBack && null != mSoundPatchControlStatusCallBacks) {
			mSoundPatchControlStatusCallBacks.remove(callBack);
		}
	}

	public void addBaseInfoRequestListener(IXmPlayerBaseInfoRequestListener l) {
		if (l != null && !mBaseInfoRequestListeners.contains(l)) {
			mBaseInfoRequestListeners.add(l);
		}
	}

	public void removeBaseInfoRequestListener(IXmPlayerBaseInfoRequestListener l) {
		if (null != l && mBaseInfoRequestListeners != null) {
			mBaseInfoRequestListeners.remove(l);
		}
	}

	public void addPlayModeChangeListner(IOnPlayModeChange l) {
		if (null != l && mOnPlayModeChanges != null) {
			mOnPlayModeChanges.add(l);
		}
	}

	public void removePlayModeChangeListner(IOnPlayModeChange l) {
		if (null != l && mOnPlayModeChanges != null) {
			mOnPlayModeChanges.remove(l);
		}
	}

	public void addPlayOrderChangeListener(IOnPlayOrderChange l) {
		if (null != l && mOnPlayOrderChanges != null) {
			mOnPlayOrderChanges.add(l);
		}
	}

	public void removePlayOrderChangeListener(IOnPlayOrderChange l) {
		if (null != l && mOnPlayOrderChanges != null) {
			mOnPlayOrderChanges.remove(l);
		}
	}


	private void handleSoundPatchError(SoundPatchInfo obj, int what, int arg1) {
		if(mSoundPatchStatusListeners != null) {
			for (ISoundPatchStatusListener listener : mSoundPatchStatusListeners) {
			    listener.onSoundPatchError(obj , what ,arg1);
			}
		}
	}

	private void handleSoundPatchComplete(SoundPatchInfo obj) {
		if(mSoundPatchStatusListeners != null) {
			for (ISoundPatchStatusListener listener : mSoundPatchStatusListeners) {
				listener.onSoundPatchCompletePlaySoundPatch(obj);
			}
		}
	}

	private void handleSoundPatchStart(SoundPatchInfo obj) {
		if (mSoundPatchStatusListeners != null) {
			for (ISoundPatchStatusListener listener : mSoundPatchStatusListeners) {
				listener.onSoundPatchStartPlaySoundPatch(obj);
			}
		}
	}

	private void handleCommercialSoundPatchStatusChange(int type) {
		if (null != mSoundPatchControlStatusCallBacks) {
			for (ICommercialSoundPatchControlStatusCallBack callBack : mSoundPatchControlStatusCallBacks) {
				if (null != callBack) {
					switch (type) {
						case ICommercialSoundPatchControlStatusCallBack.ON_PLAYING_SOUND_PATCH_START:
							callBack.onPlayingSoundPatchStart();
							break;
						case ICommercialSoundPatchControlStatusCallBack.ON_PLAYING_SOUND_PATCH_STOP:
							callBack.onPlayingSoundPatchStop();
							break;
						case ICommercialSoundPatchControlStatusCallBack.ON_NOT_PLAYING_SOUND_PATCH_START:
							callBack.onNotPlayingSoundPatchStart();
							break;
						case ICommercialSoundPatchControlStatusCallBack.ON_NOT_PLAYING_SOUND_PATCH_STOP:
							callBack.onNotPlayingSoundPatchStop();
							break;
						default:
							break;
					}
				}
			}
		}
	}

	private void handleCommercialSoundPatchReRegister() {
		if (null != mCommercialSoundPatchOperationCallBack) {
			mCommercialSoundPatchOperationCallBack.reRegister();
		}
	}

	private void handleLoadHistorySuccess() {
		if (mHistoryListLoadSuccess != null) {
			for (IOnHistoryListLoadSuccess loadSuccess : mHistoryListLoadSuccess) {
				loadSuccess.onHistoryListLoadSuccess();
			}
		}
	}

	private void handleFlvDataOut(int type , SEIParserUtil.SEIInfo seiInfo, long bufferDuration) {
		for (IFlvDataOutput flvDataOutput : mFlvDataOutputList) {
			flvDataOutput.dataOutput(type, seiInfo, bufferDuration);
		}
	}

	private void handleCloseApp() {

		if (mIXmCommonBusinessHandle != null) {
			mIXmCommonBusinessHandle.closeApp();
		}

	}

	private void handleOldChargeTrackNeedRedownload(Track track) {

		if (mIXmCommonBusinessHandle != null) {
			mIXmCommonBusinessHandle.isOldTrackDownload(track);
		}

	}


	public void addMixPlayerStatusListener(IMixPlayerStatusListener listener) {
	    if (listener != null && !mMixPlayerStatusListeners.contains(listener)) {
	        mMixPlayerStatusListeners.add(listener);
        }
    }

    public void removeMixPlayerStatusListener(IMixPlayerStatusListener listener) {
	    if (listener != null) {
	        mMixPlayerStatusListeners.remove(listener);
        }
    }

	private void handleMixPlayerStart() {
	    for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
	        if (listener != null) {
                listener.onMixStart();
            }
        }
    }

    private void handleMixPlayerPause() {
        for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
            if (listener != null) {
                listener.onMixPause();
            }
        }
    }

    private void handleMixPlayerStop() {
        for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
            if (listener != null) {
                listener.onMixStop();
            }
        }
    }

    private void handleMixPlayerComplete() {
        for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
            if (listener != null) {
                listener.onMixComplete();
            }
        }
    }

    private void handleMixPlayerStusUpdate(double key, boolean isPlaying, String state, long curPosition) {
        for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
            if (listener != null) {
                listener.onMixStatusChanged(key, isPlaying, state, curPosition);
            }
        }
    }

    private void handleMixPlayerSoundComplete(double key) {
        for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
            if (listener != null) {
                listener.onMixSoundComplete(key);
            }
        }
    }

    private void handleSoundEffectStart(Object msg) {
		if (!(msg instanceof SoundEffectInfo)) {
			return;
		}
		SoundEffectInfo realMsg = (SoundEffectInfo) msg;
		for (ISoundEffectStatusCallBackForMainManager listener : mSoundEffecttatusListeners) {
			if (listener != null) {
				listener.onEffectStart(realMsg);
			}
		}
	}

	private void handleSoundEffectError(Object msg) {
		if (!(msg instanceof SoundEffectInfo)) {
			return;
		}
		SoundEffectInfo realMsg = (SoundEffectInfo) msg;
		for (ISoundEffectStatusCallBackForMainManager listener : mSoundEffecttatusListeners) {
			if (listener != null) {
				listener.onEffectError(realMsg);
			}
		}
	}

	private void handleSoundEffectEnd(Object msg) {
		if (!(msg instanceof SoundEffectInfo)) {
			return;
		}
		SoundEffectInfo realMsg = (SoundEffectInfo) msg;
		for (ISoundEffectStatusCallBackForMainManager listener : mSoundEffecttatusListeners) {
			if (listener != null) {
				listener.onEffectEnd(realMsg);
			}
		}
	}

	private void handleSoundEffectInitialInfo(Object msg) {
		if (!(msg instanceof SoundEffectInfo)) {
			return;
		}
		SoundEffectInfo realMsg = (SoundEffectInfo) msg;
		for (ISoundEffectStatusCallBackForMainManager listener : mSoundEffecttatusListeners) {
			if (listener != null) {
				listener.onMangerInPlayerInitialed(realMsg);
			}
		}
	}

	private void handleFlvMetaInfo(Object msg) {
		if (!(msg instanceof FlvMetaInfo)) {
			return;
		}
		FlvMetaInfo info = (FlvMetaInfo) msg;
		for (IFlvDataOutput flvDataOutput : mFlvDataOutputList) {
			flvDataOutput.metaDataInfoOutput(info.getWidth(), info.getHeight(), info.getAudiobitrate(), info.getVideobitrate());
		}
	}

	private void handleChildAtTimbreUrlGet(boolean success, boolean isDownload, String type) {
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			if(l instanceof IXmPlayerStatusListenerExtension) {
				((IXmPlayerStatusListenerExtension) l).onChildAiTimbreUrlGet(success, isDownload, type);
			}
		}
	}

	private void onMixProgressUpdate(int percent) {
		for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
			if (listener != null) {
				listener.onMixProgressUpdate(percent);
			}
		}
	}

	private void handleMixPlayerError(String url, int code, String msg) {
        for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
            if (listener != null) {
                listener.onMixError(url, code, msg);
            }
        }
    }

	private void handlePlanTerminateOnTimeOut(int type) {
		try {
			for (IXmPlanTerminateListener callback : mPlanTerminateCallbackList) {
				if (callback == null) {
					continue;
				}
				callback.onTimeout(type);
			}
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	private void handlePlanTerminateOnTimeChanged(int leftTime, int type) {
		try {
			for (IXmPlanTerminateListener callback : mPlanTerminateCallbackList) {
				if (callback == null) {
					continue;
				}
				callback.onLeftTimeChanged(leftTime, type);
			}
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	private void handlePlanTerminateOnTimeSeriesChange(int series, int type) {
		try {
			for (IXmPlanTerminateListener callback : mPlanTerminateCallbackList) {
				if (callback == null) {
					continue;
				}
				callback.onLeftSeriesChanged(series, type);
			}
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	private void handlePlanTerminateOnCancel() {
		try {
			for (IXmPlanTerminateListener callback : mPlanTerminateCallbackList) {
				if (callback == null) {
					continue;
				}
				callback.onCancel();
			}
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

    private void handlePlanTerminateOnTimeOutForQuickListen(int type) {
        try {
            for (IXmPlanTerminateListener callback : mPlanTerminateCallbackListForQuickListen) {
                if (callback == null) {
                    continue;
                }
                callback.onTimeout(type);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private void handlePlanTerminateOnTimeChangedForQuickListen(int leftTime, int type) {
        try {
            for (IXmPlanTerminateListener callback : mPlanTerminateCallbackListForQuickListen) {
                if (callback == null) {
                    continue;
                }
                callback.onLeftTimeChanged(leftTime, type);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private void handlePlanTerminateOnTimeSeriesChangeForQuickListen(int series, int type) {
        try {
            for (IXmPlanTerminateListener callback : mPlanTerminateCallbackListForQuickListen) {
                if (callback == null) {
                    continue;
                }
                callback.onLeftSeriesChanged(series, type);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private void handlePlanTerminateOnCancelForQuickListen() {
        try {
            for (IXmPlanTerminateListener callback : mPlanTerminateCallbackListForQuickListen) {
                if (callback == null) {
                    continue;
                }
                callback.onCancel();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private void handleMixPlayerCleared() {
		for (IMixPlayerStatusListener listener : mMixPlayerStatusListeners) {
			if (listener != null) {
				listener.onMixTrackCleared();
			}
		}
	}

    public void createMixPlayerService() {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.createMixPlayerService();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void destroyMixPlayerService() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.destroyMixPlayerService();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setMixPlayerConfig(Map params) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setMixConfig(params);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void newMixPlayer(double key) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.newMixPlayer(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setMixDataSource(double key, String url) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setMixDataSource(key, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void setMixDataSource(double key, Map<String, Object> params) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setMixDataSourceInfo(params, key);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public Map<String, Object> getMixDataSourceInfo(double key) {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getMixDataSourceInfo(key);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

    public void setMixVolume(double key, float left, float right) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setMixVolume(key, left, right);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setMixLooper(double key, boolean isLoop) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setMixLooper(key, isLoop);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void setMixSpeed(double key, float speed) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setMixSpeed(key, speed);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void startMixPlayer(double key) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
			updateUbtSource(Constants.KEY_SLEEP, false);
        	needContinuePlay(false);
            mPlayerStub.startMixPlayer(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void pauseMixPlayer(double key) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.pauseMixPlayer(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void seekToMixPlayer(double key, int seek) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.seekToMixPlayer(key, seek);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void stopMixPlayer(double key) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.stopMixPlayer(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setMixStartPosition(double key, int start) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setMixStartPosition(key, start);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public long getMixCurPosition(double key) {
        if (!isConnectedStatus()) {
            return 0;
        }
        try {
            return mPlayerStub.getMixCurPosition(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public long getMixDuration(double key) {
        if (!isConnectedStatus()) {
            return 0;
        }
        try {
            return mPlayerStub.getMixDuration(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public List getMixPlayerKeys() {
        if (!isConnectedStatus()) {
            return null;
        }
        try {
            return mPlayerStub.getMixPlayerKeys();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Map getRestDelayMills() {
        if (!isConnectedStatus()) {
            return null;
        }
        try {
            return mPlayerStub.getDelayMillsInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public int getMixCurPercent() {
        if (!isConnectedStatus()) {
            return 0;
        }
        try {
            return mPlayerStub.getMixCurPercent();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public MixTrack getMixPlayTrack() {
        if (!isConnectedStatus()) {
            return null;
        }
        try {
            return mPlayerStub.getMixPlayTrack();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

	public void clearMixPlayTrack() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.clearMixPlayTrack();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public boolean isMixPlaying(double key) {
        if (!isConnectedStatus()) {
            return false;
        }
        try {
            return mPlayerStub.isMixPlaying(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isMixerPlaying() {
        if (!isConnectedStatus()) {
            return false;
        }
        try {
            return mPlayerStub.isMixerPlaying();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

	public void playMixPlayer() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.playMixerPlayer();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void pauseMixPlayer() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.pauseMixerPlayer();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void stopMixDelay(long mills) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.stopMixDelay(mills);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void releaseMixPlayer(double key) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.releaseMixPlayer(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void resetPodCastListMode(boolean newPodCastListMode) {
		if (!isConnectedStatus()) {
			return;
		}
		if (isPlayingToListenTracks()) {
			if (newPodCastListMode) {
				ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalPlayListMode", DTransferConstants.PARAM_SUB_PLAY_LIST_TO_LISTEN);
			} else {
				ToListenUtil.getCommonMMKVUtil().removeByKey("ToListenOriginalPlayListMode");
			}
		}
		try {
			mPlayerStub.resetPodCastListMode(newPodCastListMode);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isPodCastListPlaySortUpCurrent() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.isPodCastListPlaySortUp();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean updatePodCastListPlaySortUpCurrent(boolean sortUp) {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			mPlayerStub.updatePodCastListPlaySortUpCurrent(sortUp);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}


    public void resetPlayList() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.resetPlayList();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void resetPlayListForRestoreToMainPlayer() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.resetPlayListForRestoreToMainPlayer();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void removeListByIndex(int index) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.removeListByIndex(index);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void removeListByTrackId(long trackId) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.removeListByTrackId(trackId);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void needContinuePlay(boolean flag) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.needContinuePlay(flag);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private RecordModel mCurRecordModel = null;

	public RecordModel getCurRecordModel() {
		return mCurRecordModel;
	}

	public void setRecordModel(RecordModel model) {
		mCurRecordModel = model;
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setRecordModel(model);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getHistoryPos(long trackId) {
		if (!isConnectedStatus()) {
			if (Logger.isDebug) {
				Logger.logToFile("XmPlayerManager HistoryPos result: NO Connect!");
			}
			return SharedModelConstants.PLAY_NO_HISTORY;
		}

		try {
			String result = mPlayerStub.getHistoryPos(String.valueOf(trackId));
			if (TextUtils.isEmpty(result))
				return SharedModelConstants.PLAY_NO_HISTORY;
			if (Logger.isDebug) {
				Logger.logToFile("XmPlayerManager HistoryPos result:" + result);
			}
			return Integer.parseInt(result);

		} catch (Exception e) {
			e.printStackTrace();
			if (Logger.isDebug) {
				Logger.logToFile("XmPlayerManager HistoryPos exception: " + e.getCause() + "  " + e.getMessage());
			}
			return SharedModelConstants.PLAY_NO_HISTORY;
		}
	}

	public String getHistoryPosForTrackIds(String trackIds) {
		if (!isConnectedStatus()) {
			return String.valueOf(SharedModelConstants.PLAY_NO_HISTORY);
		}

		try {
			String result = mPlayerStub.getHistoryPos(trackIds);
			if(TextUtils.isEmpty(result))
				return String.valueOf(SharedModelConstants.PLAY_NO_HISTORY);
			Logger.log("XmPlayerManager getHistoryPosForTrackIds result:"+result);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			return String.valueOf(SharedModelConstants.PLAY_NO_HISTORY);
		}
	}

	public void setHistoryPos(long trackId,int historyPos){
		if (isConnectedStatus()) {
			try{
				mPlayerStub.setHistoryPosById(trackId,historyPos);
			}catch (Exception e){
				e.printStackTrace();
			}
		}
	}

	public void updateHistoryPosInList(List<? extends Track> list) {
		if (!isConnectedStatus()||list==null||list.isEmpty()) {
			return;
		}
		StringBuilder historyIds = new StringBuilder();
		boolean firstTime = true;
		for (Track track: list) {
			if (firstTime) {
				firstTime = false;
			} else {
				historyIds.append(",");
			}
			historyIds.append(track.getDataId());
		}

		try {
			String result = mPlayerStub.getHistoryPos(historyIds.toString());
			if(TextUtils.isEmpty(result))
				return;

			String[] historyIdsArra = result.split(",");

			if(historyIdsArra.length!=list.size()){
				if(ConstantsOpenSdk.isDebug)
					throw new RuntimeException("track list not equal length with callback data length");
				return;
			}

			int i=0;
			for (Track track: list) {
				track.setLastPlayedMills(Integer.parseInt(historyIdsArra[i]));
				i++;
			}
			Logger.log("HistoryPos result:"+result);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public Track getLastPlayTrackInAlbum(long albumId){
		if (!isConnectedStatus()||albumId <= 0) {
			return null;
		}

		try {
			Track track = mPlayerStub.getTrackByHistory(albumId);
			if(track!=null) {
				return track;
			}

			String result = mPlayerStub.getLastPlayTrackInAlbum(String.valueOf(albumId));
			if(TextUtils.isEmpty(result)) {
				return null;
			}
			Logger.log("History getLastPlayTrackInAlbum:"+result);
			return new Gson().fromJson(result,Track.class);

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	private static int mEnvironment = 1;

	/**
	 * 设置播放进程的请求环境,可以防止主进程被杀死后台自启时，
	 * Application onCreate初始化sInstance单例
	 * （此时用户并没有启动App，不会用到播放器）
	 * 从而创建不必要的播放进程，浪费系统资源
	 * @param environment
     */
	public static void setPlayerProcessRequestEnvironment(int environment) {
		if(sInstance!=null) {
		    sInstance.mEnvironment = environment;
			if (!sInstance.isConnectedStatus()) {
				return;
			}
			try {
				sInstance.mPlayerStub.setPlayerProcessRequestEnvironment(environment);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			mEnvironment = environment;
		}
	}

	/**
	 * 更新登录状态
	 * @param islogin
     */
	public void updateLoginInfoOnChange(boolean islogin) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.updateLoginInfoOnChange(islogin);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void updateXmPlayResource(String xmPlayResource){
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.updateXmPlayResource(xmPlayResource);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	public void updateXmResourceParams(Map<String,String> map){
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.updateXmResourceParams(map);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void requestSoundAd(boolean isPaused) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			Logger.logToFile("XmPlayerManager requestSoundAd isPaused=" + isPaused
					+ ", trace=" + Log.getStackTraceString(new Throwable()));
			mPlayerStub.requestSoundAd(isPaused);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void requestFocus(long mills) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.requestFocus(mills);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void pausePlayInMillis(long mills) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			Logger.logToFile("XmPlayerManager pausePlayInMillis mills=" + mills
					+ ", trace=" + Log.getStackTraceString(new Throwable()));
			mPlayerStub.pausePlayInMillis(mills);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isAdPlaying() {
		if(!isConnectedStatus()) {
			return false;
		}
		try {
			// 因为视频广告的状态不能放到播放器所以这样写
			if(mVideoAdState == MiniPlayer.STATE_STARTED) {
				return true;
			}
			return mPlayerStub.isAdPlaying() || XmAdsManager.getInstance(mAppCtx).isAdsPlaying();
		} catch (RemoteException e) {
			e.printStackTrace();
		}

		return false;
	}

	public boolean isSoundAdPause() {
		if(!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.isAdPause();
		} catch (RemoteException e) {
			e.printStackTrace();
		}

		return false;
	}

	public boolean isAdPrepared() {
		if (!isConnectedStatus()) {
			return false;
		}

		if (mVideoAdState == MiniPlayer.STATE_PREPARED) {
			return true;
		}

		return false;
	}

	private boolean commendAdPlaying = false;

	/**
	 * 新版评论页视频广告是否播放中
	 */
	public boolean isCommendAdPlaying() {
		return commendAdPlaying;
	}

	public void setCommendAdPlayStatus(boolean playStatus) {
		commendAdPlaying = playStatus;
	}

	public boolean isTrackPlaying() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			return mPlayerStub.isTrackPlaying();
		} catch (RemoteException e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean hasNextPlayList() {
		if(!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.haveNextPlayList();
		} catch (RemoteException e) {
			e.printStackTrace();
		}

		return false;
	}

	public boolean hasPrePlayList() {
		if(!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.havePrePlayList();
		} catch (RemoteException e) {
			e.printStackTrace();
		}

		return false;
	}

	public boolean isBuffering() {
		if(!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.isBuffering();
		} catch (RemoteException e) {
			e.printStackTrace();
		}

		return false;
	}


	public void exitSoundAds() {
		if(!checkConnectionStatus()) {
			return;
		}

		try {
			mPlayerStub.exitSoundAd();
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public boolean isLoading() {
		if(!checkConnectionStatus()) {
			return false;
		}

		try {
			// 如果是视频广告正在播放或者正在暂停状态 不会有加载状态
			if(mVideoAdState == MiniPlayer.STATE_STARTED || mVideoAdState == MiniPlayer.STATE_PAUSED) {
				return false;
			}
			return mPlayerStub.isLoading();
		} catch (RemoteException e) {
			e.printStackTrace();
		}
		return false;
	}

    // 播放列表是否设置了
    public boolean isPlayListSet() {
        if (!checkConnectionStatus()) {
            return false;
        }
        try {
            return mPlayerStub.isPlayListSet();
        } catch (RemoteException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

	public boolean isQuickListen() {
		if (!checkConnectionStatus()) {
			return false;
		}

		try {
			return mPlayerStub.isQuickListen();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public long getQuickListenTabId() {
		if (!checkConnectionStatus()) {
			return -1;
		}

		try {
			return mPlayerStub.getQuickListenTabId();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	public boolean isQuickListenRecommendTab() {
		if (!checkConnectionStatus()) {
			return false;
		}

		try {
			return mPlayerStub.isQuickListenRecommendTab();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

    public Map<String, String> getPlayListParams() {
        if (!checkConnectionStatus()) {
            return null;
        }

        try {
            return (Map<String, String>) mPlayerStub.getParam();
        } catch (RemoteException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

	public Map<String, String> getPlayListOriginParams() {
		if (!checkConnectionStatus()) {
			return null;
		}

		try {
			return (Map<String, String>) mPlayerStub.getOriginParams();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

    public void addTracksToPlayList(List<Track> tracks) {
        if (!checkConnectionStatus()) {
            return;
        }
		Logger.logToFile("XmPlayerManager addTracksToPlayList size=" + (tracks != null ? tracks.size() : "null")
				+ ", trace=" + Log.getStackTraceString(new Throwable()));

        try {
            mPlayerStub.addPlayList(tracks);
        } catch (RemoteException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setPageId(int pageId) {
		if (!checkConnectionStatus()) {
			return;
		}

		try {
			mPlayerStub.setPageId(pageId);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    // 在播放列表的头部插入trackList
	public void insertTracksToPlayListHead(List<Track> tracks) {
		if (!checkConnectionStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager insertTracksToPlayListHead size=" + (tracks != null ? tracks.size() : "null")
				+ ", trace=" + Log.getStackTraceString(new Throwable()));

		try {
			mPlayerStub.insertPlayListHead(tracks);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	/**
	 * TODO 删除播放列表中的tracks 知识直播 微课专用
	 * TODO 删除播放列表中的tracks 知识直播 微课专用
	 * TODO 删除播放列表中的tracks 知识直播 微课专用
	 * @param weikeTrackId 微课trackID
	 */
	public void deleteWeikeTrackInPlayList(String weikeTrackId) {
		if (!checkConnectionStatus()) {
			return;
		}
	}

	public void stagePlayList() {
		if (!checkConnectionStatus()) {
			return;
		}

		try {
			mPlayerStub.stagePlayList();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isInRecommendPlayList() {
		if (!isConnectedStatus()) {
			return false;
		}
		try {
			boolean res = mPlayerStub.isInRecommendPlayList();
			return res;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public void setIsTingTextVisible(boolean isTingTextVisible) {
		mIsTingTextVisible = isTingTextVisible;
	}

    public void setPlayFragmentIsShowing(boolean isShowing) {
		// 播放页被边听边看覆盖了，此时不设置
		if (isShowing && mIsTingTextVisible) {
			Logger.log("XmPlayerManager : setPlayFragmentIsShowing ting text showing");
			return;
		}
		Logger.log("XmPlayerManager : setPlayFragmentIsShowing " + System.currentTimeMillis());
		XmAdsManager.isPlayFragmentShowing = isShowing;
        if (!checkConnectionStatus()) {
            return;
        }

        try {
            mPlayerStub.setPlayFragmentIsShowing(isShowing);
        } catch (RemoteException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void resetPlayer() {
		if (!checkConnectionStatus()) {
			return;
		}

		try {
			mPlayerStub.resetPlayer();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	public void setCheckAdContent(boolean willCheck) {
		checkAdContent = willCheck;
		if(isConnectedStatus()) {
			try {
				mPlayerStub.setCheckAdContent(willCheck);
			} catch (RemoteException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}

	public void onVideoAdPlayCompleted(int curPos ,int videoDuration) {
		if(!checkConnectionStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager onVideoAdPlayCompleted curPos=" + curPos
				+ ",videoDuration=" + videoDuration + Log.getStackTraceString(new Throwable()));
		try {
			mVideoAdState = MiniPlayer.STATE_COMPLETED;
			mPlayerStub.onVideoAdCompleted(curPos , videoDuration);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void onVideoAdPlayStart(boolean isDuringPlay) {
		if(!checkConnectionStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager onVideoAdPlayStart isDuringPlay=" + isDuringPlay
				+ Log.getStackTraceString(new Throwable()));
		try {
			mPlayerStub.onVideoAdPlayStart(isDuringPlay);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public long getCurrentTrackPlayedDuration(){
		if(!checkConnectionStatus()){
			return 0;
		}

		try {
			return mPlayerStub.getCurrentTrackPlayedDuration();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return 0;
	}


	public void setVideoAdPlayStart(Advertis advertis) {
		mVideoAdState = MiniPlayer.STATE_STARTED;
		Logger.logToFile("XmPlayerManager ForwardVideoManager :  setVideoAdState start " + mVideoAdState
				+ "   " + Log.getStackTraceString(new Throwable()));
		handleStartPlayAds(advertis, 0);
		resetCommercialSoundPatchOnVideoAdPlay();
	}

	// 视频广告状态
	private int mVideoAdState = 0;
	public void setVideoAdState(int videoAdState) {
		int lastVideoAdState = mVideoAdState;
		if (videoAdState == MiniPlayer.STATE_CLOSED) {
			mVideoAdState = MiniPlayer.STATE_COMPLETED;
		} else {
			mVideoAdState = videoAdState;
		}
		if(lastVideoAdState == MiniPlayer.STATE_STARTED && videoAdState == MiniPlayer.STATE_MUTE) {
			handlePlayPause();
		}

		Logger.logToFile("XmPlayerManager ForwardVideoManager :  setVideoAdState " + videoAdState + "   " + Log.getStackTraceString(new Throwable()));
		if(mVideoAdState == MiniPlayer.STATE_PAUSED) {
			handlePlayPause();
		} else if(mVideoAdState == MiniPlayer.STATE_COMPLETED && videoAdState != MiniPlayer.STATE_CLOSED) {
			handleCompletePlayAds();
		} else if(mVideoAdState == MiniPlayer.STATE_ERR) {
			handlePlayAdsError(0, 0);
		}
	}

	public int getVideoAdState() {
		return mVideoAdState;
	}

	public void setFreeFlowType(int type) {
		if(!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.setFreeFlowType(type);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 是否正在使用免流
	public void setIsUsingFreeFlow(boolean isUsingFreeFlow) {
		if(!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.setIsUsingFreeFlow(isUsingFreeFlow);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 是否正在展示全屏字幕文稿
	public void setIsFullScreenDoc(boolean isFullScreenDoc) {
		try {
			mPlayerStub.setIsFullScreenDoc(isFullScreenDoc);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setIsScreenDoc(boolean isScreenDoc) {
		try {
			mPlayerStub.setIsScreenDoc(isScreenDoc);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 是否正在展示全屏字幕文稿
	public void setPlayerScene(boolean isFull, boolean hasDoc) {
		try {
			int playerScene = 0;
			if (isFull && hasDoc){
				playerScene = 1;
			} else if (!isFull && hasDoc){
				playerScene = 2;
			} else if (isFull){
				playerScene = 3;
			} else {
				playerScene = 4;
			}
			mPlayerStub.setPlayerScene(playerScene);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 关闭当前声音流广告
	public void stopCurrentSoundAd() {
		try {
			mPlayerStub.stopCurrentSoundAd();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 是否是新版播放页
	public void setIsNewPlayPage(boolean isNewPlayPage) {
		try {
			mPlayerStub.setIsNewPlayPage(isNewPlayPage);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 贴片关闭事件
	public void onCoverAdClose(boolean isFromUser) {
		try {
			mPlayerStub.onCoverAdClose(isFromUser);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 设置track到下一曲，不考虑播放来源
	 * @param track
	 */
	public void setTrackToNextIgnorePlaySource(Track track) {
		if (track != null) {
			track.setIgnorePlaySource(true);
		}
		setTrackToNext(track);
	}

	/**
	 * 设置track到下一曲
	 * @param track
	 */
	public void setTrackToNext(Track track) {
		if(!checkConnectionStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager setTrackToNext: " + track + ", trace=" + Log.getStackTraceString(new Throwable()));
		try {
			mPlayerStub.setTrackToNext(track);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void putTracksAfterTarget(long targetTrackId, List<Track> insertTracks) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.putTracksAfterTarget(targetTrackId, insertTracks);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	/**
	 * 添加insertTrack到targetTrackId声音的后面
	 * 播客类型声音向上播的时候  需要注意是不是想要放到指定的位置
	 * @param targetTrackId
	 * @param insertTrack
	 */
	public void putTrackAfterTarget(long targetTrackId, Track insertTrack) {
		if(!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.putTrackAfterTarget(targetTrackId, insertTrack);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setMediaSessionBgViewShow(boolean show) {
		if (!checkConnectionStatus()) {
			return;
		}
		try {
			mPlayerStub.setMediaSessionBgViewShow(show);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void addFlvDataOutput(IFlvDataOutput flvDataOutput) {
		if (flvDataOutput == null || mFlvDataOutputList.contains(flvDataOutput))
			return;
		mFlvDataOutputList.add(flvDataOutput);
		if (mFlvDataOutputList.size() == 1) {
			if (!checkConnectionStatus()) {
				return;
			}
			try {
				mPlayerStub.setFlvDataCallBack(mFlvDataCallbackStub);
			} catch (RemoteException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void removeFlvDataOutput(IFlvDataOutput flvDataOutput) {
		if (mFlvDataOutputList.isEmpty())
			return;
		mFlvDataOutputList.remove(flvDataOutput);
		if (mFlvDataOutputList.isEmpty()) {
			if (!isConnectedStatus()) {
				return;
			}
			try {
				mPlayerStub.setFlvDataCallBack(null);
			} catch (RemoteException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setCommercialSoundPatchOperationCallBack(ICommercialSoundPatchOperationCallBack callBack) {
		this.mCommercialSoundPatchOperationCallBack = callBack;
	}

	public void setVideoAdPlayPosAndDuration(int position, int duration) {
		if (!checkConnectionStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager setVideoAdPlayPosAndDuration position=" + position + ",duration=" + duration);
		try {
			mPlayerStub.setCurAdVideoPlayCurPos(position, duration);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 音量平衡
	private boolean volumnBalanceIsOpen = false;
	public void setVolumnBalance(boolean isOpen) {
		this.volumnBalanceIsOpen = isOpen;

		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setVolumeBalance(isOpen);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setLoggerEnableForPlayProcess(boolean loggerEnable, boolean playerLoggerEnable) {
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setLoggerEnable(loggerEnable ,playerLoggerEnable);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void startNetMonitor() {
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setNetMonitorEnable(true);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void closeNetMonitor() {
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setNetMonitorEnable(false);
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void configureItem(ConfigWrapItem item) {
		if (!isConnectedStatus()) {
			return;
		}
        try {
            mPlayerStub.configureItem(item);
        } catch (RemoteException e) {
            e.printStackTrace();
        } catch(Exception e){}
    }

	public void notifyFestivalTaskService(TaskStatusInfo info) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.notifyFestivalTaskService(info);
		} catch (RuntimeException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    public void setContinuePlayWhileAuditionTrackPlayComplete(boolean flag){
		try {
			mPlayerStub.setContinuePlayWhileAuditionTrackPlayComplete(flag);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isContinuePlayWhileAuditionTrackPlayComplete() {
		try {
			return mPlayerStub.isContinuePlayWhileAuditionTrackPlayComplete();
		} catch (RemoteException e) {
			e.printStackTrace();
		}
		return false;
	}

	public float getTempo() {
		if (!isConnectedStatus()) {
			return XMediaplayerJNI.TEMPO_DEFAULT;
		}

		try {
			return mPlayerStub.getTempo();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return XMediaplayerJNI.TEMPO_DEFAULT;
	}

	public void setAdPreview(AdPreviewModel adPreview) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setPreviewAdSource(adPreview);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private boolean mChannelJumpOver = false;
	public void setChannelJumpOver(boolean jumpOver) {
		mChannelJumpOver = jumpOver;
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setChannelJumpOver(mChannelJumpOver);
			mChannelJumpOver = false;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private String OAID = null;
	private String ENCRYPT_OAID = null;
	public void setOAID(String oaid, String encryptOAID) {
		this.OAID = oaid;
		this.ENCRYPT_OAID = encryptOAID;

		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_OAID , oaid);
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_ENCRYPT_OAID , encryptOAID);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private Boolean isChildMode;
	public void isChildMode(boolean isChildMode) {
		this.isChildMode = isChildMode;

		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_CHILD_PROTECT_IS_OPEN ,
					isChildMode + "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setChildModeSelectedAgeRange(String ageRange) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_CHILD_PROTECT_AGE_RANGE, ageRange);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

	public void updateOnGetListenTime() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.updateOnGetListenTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void updateSkipHeadTail(long albumId, int head, int tail) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.updateSkipHeadTail(albumId, head, tail);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setTrackPlayQuality(int trackPlayQuality, int cellularQuality) {
		this.trackPlayQuality = trackPlayQuality;
		this.mCellularQuality = cellularQuality;

		if (!isConnectedStatus()) {
			return;
		}

		Logger.logToFile("setTrackPlayQuality trackPlayQuality=" + trackPlayQuality + ",cellularQuality=" + cellularQuality + ",trace=" + Log.getStackTraceString(new Throwable()));

		try {
			mPlayerStub.setTrackPlayQualityLevel(trackPlayQuality, cellularQuality);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setTrackByTimbreType(String type) {
		setTrackByTimbreType(type, false);
	}

	public void setTrackByTimbreType(String type, boolean forcePlay) {
		this.trackByTimbreType = type;

		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("setTrackByTimbreType type=" + type + ",forcePlay=" + forcePlay + ",trace=" + Log.getStackTraceString(new Throwable()));

		try {
			mPlayerStub.setTrackByTimbreType(trackByTimbreType, forcePlay);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 播放畅听音贴
	 * @param isForceUseOnScreenSoundPatch 是否强制播放亮屏音贴
	 */
	public void playFreeListenTimeRunOutSoundPatch(boolean isForceUseOnScreenSoundPatch) {
		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("playFreeListenTimeRunOutSoundPatch isForceUseOnScreenSoundPatch=" + isForceUseOnScreenSoundPatch
				+ ",trace=" + Log.getStackTraceString(new Throwable()));
		try {
			mPlayerStub.playFreeListenTimeRunOutSoundPatch(isForceUseOnScreenSoundPatch);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 开启全天畅听倒计时
	 */
	public void startAllDayListenTimeOutCountDown() {
		try {
			mPlayerStub.startAllDayListenTimeOutCountDown();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setCommercialSoundPatchWorkMode(int mode) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setCommercialSoundPatchWorkMode(mode);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void registerCommercialSoundPatch(int type, SimpleSoundPatchInfo simpleInfo) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.registerCommercialSoundPatch(type, simpleInfo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @param paramsJsonString 参数构成的字符串，json格式，可为空
	 */
	public void playCommercialSoundPatchByType(int type, String paramsJsonString) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.playCommercialSoundPatchByType(type, paramsJsonString);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isPlayingCommercialSoundPatch() {
		if (!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.isPlayingCommercialSoundPatch();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean needBlockPlayNextOrPrevBtn() {
		if (!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.needBlockPlayNextOrPrevBtn();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public void stopCommercialSoundPatch() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.stopCommercialSoundPatch();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void resetCommercialSoundPatchOnVideoAdPlay() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.resetCommercialSoundPatchOnVideoAdPlay();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void initXiMaPipePush() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.initXiMaPipePush();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void addSoundPatch(SoundPatchInfo soundPatchInfo) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.addSoundPatch(soundPatchInfo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 在声音贴片后面追加声音贴片,只支持在开头和结尾追加
	public void appendSoundPatch(SoundPatchInfo soundPatchInfo) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.appendSoundPatch(soundPatchInfo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	public void removeSoundPatch(SoundPatchInfo soundPatchInfo) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.removeSoundPatch(soundPatchInfo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void stopSoundPatchPlay() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.stopSoundPatch();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private Integer soundPatchTimeoutMs;
	public void soundPatchTimeoutMs(int soundPatchTimeoutMs) {
		this.soundPatchTimeoutMs = soundPatchTimeoutMs;

		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SOUND_PATCH_TIMEOUT_MS ,
					soundPatchTimeoutMs + "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public SoundEffectInfo requireInitialSoundEffectInfo(long uid, boolean isVip) {
		if (!isConnectedStatus()) {
			return null;
		}

		try {
			return mPlayerStub.requireInitialSoundEffectInfo(uid, isVip);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public void useSoundEffect(int type, long effectId, String sourceDir) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.useSoundEffect(type, effectId, sourceDir);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void resetToNormalSoundEffect() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.resetToNormalSoundEffect();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private String followHeartConfig;
	public void setFollowHeartConfig(String config) {
		this.followHeartConfig = config;

		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_FOLLOW_HEART_CONFIG ,
					followHeartConfig);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private Integer progressInterval;
	public void setSaveProgressInterval(int progressInterval) {
		this.progressInterval = progressInterval;

		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_SAVE_PROGRESS ,
					progressInterval + "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private Boolean useWakeLockConfig;
	public void setUseWakeLockConfig(boolean useWakeLockConfig) {
		this.useWakeLockConfig = useWakeLockConfig;

		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_USE_WAKELOCK_CONFIG ,
					useWakeLockConfig + "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getLimitTime() {
		if (!isConnectedStatus()) {
			return 0;
		}

		try {
			return mPlayerStub.getLimitTime();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return 0;
	}

	public void setLimitTime(int limitTime) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setLimitTime(limitTime);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getListenTime() {
		try {
			return mPlayerStub.getListenTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public void addListenTime(int remainTime, int addedTime) {
		if (mPlayerStub == null) {
			// 播放器进程未初始化，先写本地持久化值
			MmkvCommonUtil.getInstance(mAppCtx).saveInt("free_listen_remain_time", remainTime);
			for (IFreeListenTimeListener listener : mFreeListenTimeListeners) {
				listener.onListenTimeChange(remainTime, true, addedTime);
			}
			return;
		}
		try {
			mPlayerStub.addListenTime(remainTime, addedTime);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void clearAllListenTime() {
		if (mPlayerStub == null) {
			// 播放器进程未初始化，先写本地持久化值
			MmkvCommonUtil.getInstance(mAppCtx).saveInt("played_not_sync_time", 0);
			MmkvCommonUtil.getInstance(mAppCtx).saveInt("free_listen_remain_time", 0);
			return;
		}
		try {
			mPlayerStub.clearAllListenTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void addFreeTimeListener(IFreeListenTimeListener l) {
		if (l != null && !mFreeListenTimeListeners.contains(l)) {
			mFreeListenTimeListeners.add(l);
		}
	}

	public void removeFreeTimeListener(IFreeListenTimeListener l) {
		if (l != null) {
			mFreeListenTimeListeners.remove(l);
		}
	}

	/**
	 * 获取总收听时长（新版本）
	 */
	public int getTotalListenTime() {
		try {
			if (mPlayerStub != null) {
				return mPlayerStub.getTotalListenTime();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public void addListenTimeListener(IListenTimeListener l) {
		if (l != null && !mListenTimeListeners.contains(l)) {
			mListenTimeListeners.add(l);
		}
	}

	public void removeListenTimeListener(IListenTimeListener l) {
		if (l != null) {
			mListenTimeListeners.remove(l);
		}
	}

	/**
	 * 跨进程同步时长到服务端
	 */
	public void syncTimeToServer(SyncTimeCallbackAdapter.ISyncTimeCallback callback) {
		try {
			if (mPlayerStub != null) {
				// 使用适配器将回调转换为 AIDL 接口
				SyncTimeCallbackAdapter adapter = new SyncTimeCallbackAdapter(callback);
				mPlayerStub.syncTimeToServer(adapter);
			} else if (callback != null) {
				callback.onSyncFailed();
			}
		} catch (Exception e) {
			e.printStackTrace();
			if (callback != null) {
				callback.onSyncFailed();
			}
		}
	}

	public void onEnterAllDayFreeListen() {
		for (IFreeListenTimeListener listener : mFreeListenTimeListeners) {
			listener.onEnterAllDayFreeListen();
		}
	}

	public List<Advertis> getForwardAdvertis() {
		if(!isConnectedStatus()) {
			return null;
		}

		try {
			return mPlayerStub.getForwardAd();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;

	}
	public void onPlayerObtainTrackInfo(Track track) {
		for (IXmPlayerTrackInfoListener listener : mPlayerTrackInfoListeners) {
			listener.onXmPlayerObtainTrackInfo(track);
		}
	}

	@Nullable
	public AdvertisList getCurSoundAdList() {
		if(!isConnectedStatus()) {
			return null;
		}

		try {
			return mPlayerStub.getCurSoundAdList();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	public void setCurAdSoundType(int soundType){
		if(!isConnectedStatus()) {
			return ;
		}

		try {
			mPlayerStub.setCurAdSoundType(soundType);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public boolean isNoNeedExposureAigcById(long trackId){
		if(!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.isNoNeedExposureAigcById(trackId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public void setCurAdHasDoneRtb(long responseId){
		if(!isConnectedStatus()) {
			return ;
		}

		try {
			mPlayerStub.setCurAdHasDoneRtb(responseId);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	@Nullable
	public Advertis getCurSoundAd() {
		if(!isConnectedStatus()) {
			return null;
		}

		try {
			return mPlayerStub.getCurSoundAd();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	public void useStatusChange(boolean hasLogin, boolean isVip) {
		if(!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.useStatusChange(hasLogin, isVip);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void updateSubscribeStatus(long albumId, boolean collect) {
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.updateSubscribeStatus(albumId, collect);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 更新播放器声音收藏状态
	 * **/
	public void updateTrackCollectStatus(long trackId, boolean collect) {
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.updateTrackCollectStatus(trackId, collect);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 更新播放器声音热点数据
	 * **/
	public void updateTrackDailyNewsModel(Track updateTrack) {
		if(!isConnectedStatus() || updateTrack == null) {
			return;
		}
		try {
			mPlayerStub.updateTrackDailyNewsModel(updateTrack);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 更新播放器声音DailyVoteVO数据
	 * **/
	public void updateTrackDailyVoteVOModel(DailyVoteVO dailyVoteVO) {
		if(!isConnectedStatus() || dailyVoteVO == null) {
			return;
		}
		Track track = new Track();
		track.setVoteVo(dailyVoteVO);
		try {
			mPlayerStub.updateTrackDailyVoteVOModel(track);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setUseAidlOptimization(boolean useAidlOptimization) {
		if(!isConnectedStatus()) {
			return;
		}

		if(!useAidlOptimization) {
			mPlayerStub = mOldPlayerStub;
		}
	}

	private Boolean playErrRetry = true;
	public void setPlayErrRetry(boolean playErrRetry) {
		this.playErrRetry = playErrRetry;
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_PLAY_ERR_RETRY ,
					playErrRetry + "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private Integer listenTaskIntervalTime;

    public void setListenTaskSaveIntervalTime(int intervalTime) {
        listenTaskIntervalTime = intervalTime;
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_LISTEN_TASK_INTERVAL_TIME,
                    intervalTime + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Integer listenDataIntervalTimeNew;
    public void setListenDataSaveIntervalTimeNew(int intervalTime) {
        listenDataIntervalTimeNew = intervalTime;
        if(!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_LISTEN_TASK_INTERVAL_TIME_NEW,
                    intervalTime + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void changeNotificationStyle(int style) {
		if(!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.changeNotificationStyle(style);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 更新解码状态
	public void updateDecoderState() {
        if(!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.updateDecoderState();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 老年模式更新
	public void onElderlyModelChange() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.onElderlyModelChange();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 无障碍模式更新
	public void onAccessibilityModeChange() {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.onAccessibilityModeChange();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void onVideoAdShow(boolean hasVideoCache) {
		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager onVideoAdShow hasVideoCache=" + hasVideoCache);

		try {
			mPlayerStub.onVideoAdShow(hasVideoCache);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean canSoundAdDuringRequest(boolean isPausedAd) {
        if (!isConnectedStatus()) {
            return false;
        }

        try {
            return mPlayerStub.canSoundAdDuringRequest(isPausedAd);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    public boolean isCurTrackNeedPauseAtDesPos() {
		if (!isConnectedStatus()) {
			return false;
		}

		try {
			return mPlayerStub.isCurTrackNeedPauseAtDesPos();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

    public void setCurTrackNeedPauseAtDesPos(boolean isNeedPauseAtDesPos, int endPlayPos) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.setCurTrackNeedPauseAtDesPos(isNeedPauseAtDesPos, endPlayPos);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean isCurTrackNeedLoadPlayCompleteData() {
		if (!isConnectedStatus()) {
			return true;
		}

		try {
			return mPlayerStub.isCurTrackNeedLoadPlayCompleteData();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public void preDownloadFile(String url) {
		if (!isConnectedStatus()) {
			return;
		}

		try {
			mPlayerStub.preDownloadFile(url);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// *** 定时关闭 start ****
	public void setPlanTerminateListener(IXmPlanTerminateListener listener) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setPlanTerminateListener(listener);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

	public String getPlanTerminateContinueStr() {
		if (!isConnectedStatus()) {
			return "";
		}
		try {
			return mPlayerStub.getPlanTerminateContinueStr();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}

	public int[] getLastPlanTerminateType() {
		if (!isConnectedStatus()) {
			return null;
		}
		try {
			return mPlayerStub.getLastPlanTerminateType();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public void setPlanTerminateMode(int mode) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setPlanTerminateMode(mode);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void startPlanTerminateTimer(int type) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.startPlanTerminateTimer(type);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void startPlanTerminateCustomTimer(long time) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.startPlanTerminateCustomTimer(time);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getPlanTerminatePlayTimes() {
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			return mPlayerStub.getPlanTerminatePlayTimes();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public int getPlanTerminateTimerType() {
		if (!isConnectedStatus()) {
			return -1;
		}
		try {
			return mPlayerStub.getPlanTerminateTimerType();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	public int getPlanTerminateLeftSeries() {
		if (!isConnectedStatus()) {
			return -1;
		}
		try {
			return mPlayerStub.getPlanTerminateLeftSeries();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	public void planTerminateCountDown() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.planTerminateCountDown();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setPlayCompleteWithTimeMode(boolean playComplete) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setPlayCompleteWithTimeMode(playComplete);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void planTerminateReset() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.planTerminateReset();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void planTerminateRelease() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.planTerminateRelease();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void planTerminateForceCancel() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.planTerminateForceCancel();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void updateUnlockPermissionExpireSecond(long newPermissionExpireSecond, long newLocalBaseTimeStamp) {
    	if (!isConnectedStatus()) {
    		return;
		}
		try {
			mPlayerStub.updateUnlockPermissionExpireSecond(newPermissionExpireSecond, newLocalBaseTimeStamp);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setUnlockTimeOutSoundPatchUrl(String url) {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setUnlockTimeOutSoundPatchUrl(url);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void tryToStartFreeListenTerminator() {
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.tryToStartFreeListenTerminator();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	Boolean mIsTargetUser;
	public void setAdMarkTargetUser(boolean isTargetUser) {
        mIsTargetUser = isTargetUser;
        if (!isConnectedStatus()) {
            return;
        }

        try {
            mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.ITEM_AD_MARK_TARGET_USER, isTargetUser + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	// *** 定时关闭 end ****

	public void syncSessionIdToPlayerProcess() {
		try {
			mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_SET_SESSION_OF_MAIN, String.valueOf(CrossProcessTransferValueManager.mainSessionId));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private IUbtSourceSynchronizer.Stub mSynchronizerStub = new IUbtSourceSynchronizer.Stub() {
		@Override
		public void onUbtSourceChanged(String type, String ubtSource, boolean shouldReplacePlaying) throws RemoteException {
			if (mUbtSourceSynchronizer != null) {
				mUbtSourceSynchronizer.onUbtSourceChanged(type, ubtSource, shouldReplacePlaying);
			}
		}

		@Override
		public void sendTrace(TraceModel trace) throws RemoteException {
			if (mUbtSourceSynchronizer != null) {
				mUbtSourceSynchronizer.sendTrace(trace);
			}
		}
	};

	public void updateAdFreeData() {
		if (!isConnectedStatus()) {
			needUpdateAdFreeData = true;
			return;
		}

		try {
			mPlayerStub.updateAdFreeData();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @return 获取准确的时间
	 */
	public long getCurrentPosInner() {
		if (!isConnectedStatus()) {
			return 0;
		}
		try {
			return mPlayerStub.getCurrentPosInner();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	/**
	 * 用于没有播放其他声音时，设置是否在播放小说 tts
	 * @param playSource
	 */
	public void setPlaySource(int playSource) {
		this.playSource = playSource;
	}

	public int getPlaySource() {
		return playSource;
	}

	public long getBookId() {
		return bookId;
	}

	public void setBookId(long bookId) {
		this.bookId = bookId;
	}

	public boolean isPlayingForEBook() {
		return isPlayingForEBook;
	}

	public void setPlayingForEBook(boolean playingForEBook) {
		isPlayingForEBook = playingForEBook;
	}


	/**
	 *  基于任务的端上实时播放统计
	 *  该方法返回未过期任务的播放时长
	 * @param id 实时统计的任务id 服务端@吴佳飞
	 * @return 对应任务 id 的本地播放时长
	 */
	public long getPlayDurationByTaskId(String id) {
		if (!isConnectedStatus()) {
			return 0L;
		}
		try {
			return mPlayerStub.getPlayDurationById(id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0L;
	}

	public void onVideoPlayStart() {
		if (!canControlByNotification()) {
			return;
		}
		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager onVideoPlayStart");
		try {
			mPlayerStub.onVideoPlayStart();
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	public void onVideoPlayPause() {
		if (!canControlByNotification()) {
			return;
		}
		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager onVideoPlayPause");
		try {
			mPlayerStub.onVideoPlayPause();
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	public void setVideoMode(boolean isVideoMode){
		if (!canControlByNotification()) {
			return;
		}
		if (!isConnectedStatus()) {
			return;
		}
		try {
			mPlayerStub.setVideoMode(isVideoMode);
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	private boolean canControlByNotification() {
		return ConfigureCenter.getInstance().getBool("android", "can_video_play_by_notification", false);
	}

    private String mThirdUidAppKey;
    private String mThirdUidValue;
    public void setThirdAppKeyAndValue(String appKey, String uidValue) {
        mThirdUidAppKey = appKey;
        mThirdUidValue = uidValue;
        if(!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_THIRD_UID_APPKEY, appKey);
            mPlayerStub.setValueToPlayProcess(CrossProcessTransferValueManager.KEY_THIRD_UID_THIRD_UID_VALUE, uidValue);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	public void setRemoteControlState(boolean open) {
		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager setRemoteControlState " + open);
		try {
			mPlayerStub.setRemoteControlState(open);
		} catch (RemoteException e) {
			e.printStackTrace();
		}
	}

	public void updatePlayProgressChange(int currPos, int duration) {
		if (!isConnectedStatus()) {
			return;
		}
		Logger.logToFile("XmPlayerManager updatePlayProgressChange " + currPos);
		try {
			mPlayerStub.updatePlayProgressChange(currPos, duration);
		} catch (RemoteException e) {
			e.printStackTrace();
		}
		for (IXmPlayerStatusListener l : mPlayerStatusListeners) {
			l.onPlayProgress(currPos, duration);
		}
	}



    public void startPlanTerminateTimerForQuickListen(int type) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.startPlanTerminateTimerForQuickListen(type);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void startPlanTerminateCustomTimerForQuickListen(long time) {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.startPlanTerminateCustomTimerForQuickListen(time);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getPlanTerminateTimerTypeForQuickListen() {
        if (!isConnectedStatus()) {
            return -1;
        }
        try {
            return mPlayerStub.getPlanTerminateTimerTypeForQuickListen();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    public void planTerminateReleaseForQuickListen() {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.planTerminateReleaseForQuickListen();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void planTerminateForceCancelForQuickListen() {
        if (!isConnectedStatus()) {
            return;
        }
        try {
            mPlayerStub.planTerminateForceCancelForQuickListen();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int indexOfTrackId(long trackId) {
        if (!isConnectedStatus()) {
            return -1;
        }
        try {
            return mPlayerStub.indexOfTrackId(trackId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }
}
