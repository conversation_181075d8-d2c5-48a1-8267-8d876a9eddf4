package com.ximalaya.ting.android.live.host.utils;

import static com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo.FROM_LIVE;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.firework.FireworkApi;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.live.util.LiveDataReportUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.kidmode.CategoryRecommendKidEntryManager;
import com.ximalaya.ting.android.host.manager.kidmode.KidsHelper;
import com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo;
import com.ximalaya.ting.android.host.util.VersionUtil;
import com.ximalaya.ting.android.host.view.dialog.SimpleDialog;
import com.ximalaya.ting.android.live.common.lib.base.constants.RoomDelayConstants;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCore;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreJumpHelper;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreLogger;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreManager;
import com.ximalaya.ting.android.live.host.request.CommonRequestForLiveHost;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Map;

/**
 * LiveHostCommonUtil.
 *
 * <AUTHOR>
 */
public class LiveHostCommonUtil {

    public static boolean isLiveRoomInSlideGesture = false;

    public static boolean isActivityEnable(Activity activity) {
        if (activity != null) {
            return !activity.isDestroyed() && !activity.isFinishing();
        } else {
            return false;
        }
    }

    /**
     * 检查是否青少年/儿童模式，是的话打开青少年/儿童模式界面并返回true
     *
     * @param context 上下文
     * @return true 打开了青少年/儿童模式，false 为没有打开青少年/儿童模式
     */
    public static boolean checkChildrenModeOpen(Context context) {
        if (ChildProtectManager.isChildProtectOpen(context)) {
            ChildProtectInfo childProtectInfo = new ChildProtectInfo();
            childProtectInfo.form = FROM_LIVE;
            ChildProtectManager.openForbidPlayPage(childProtectInfo);
            return true;
        }

        if (KidsHelper.isInKisMode(context)) {
            CategoryRecommendKidEntryManager.go2KidModeHomePage();
            return true;
        }

        return false;
    }

    public static boolean showDisconnectMicWarning(Context activity, final ILiveFunctionAction.IActionCallback iAction) {
        return showDisconnectMicWarning(activity, iAction, true);
    }

    /**
     * 对应下面 showDisconnectMicWarning()，不弹窗，存检测是否有连麦的最小化房间
     *
     * @return 是否有连麦的最小化房间
     */
    public static boolean hasMicRoom() {
        if (RoomCoreJumpHelper.hasAnchorRoom()) return true;
        RoomCore room = RoomCoreManager.getInstance().getCurrentRoomCore();
        return room != null && room.isMicActive();
    }

    /**
     * 连麦用户切换直播间的弹窗拦截，由RoomCore统一处理，要切换房间，按下麦、是否要结束房间、断链、切换房间的时序进行
     * 弹框由RoomCore统一处理，文案默认一
     * //todo 参数后续需要传入roomId和bizType,如果是切换房间的操作，需要判断目标房间和当前是否是同一个房间，如果是同一个房间，则不弹窗
     **/
    @SuppressLint("SwitchIntDef")
    public static boolean showDisconnectMicWarning(Context activity, final ILiveFunctionAction.IActionCallback iAction, boolean jumpNewRoom) {

        RoomCore room = RoomCoreManager.getInstance().getCurrentRoomCore();

        if (room == null) {
            RoomCoreLogger.logFile("checkOpenCalling RoomCoreManager.getInstance().getCurrentRoomCore() null");
            if (RoomCoreJumpHelper.hasAnchorRoom()) {
                RoomCoreLogger.logFile("checkOpenCalling 此时有主播端房间，展示主播端不能离开页面弹框");
                RoomCoreJumpHelper.showAVAnchorRoomDisConnectMicWaringBeforeJumpToOtherPage(() -> {

                }, () -> {

                });
                return true;
            }
            RoomCoreLogger.log("checkOpenCalling  roomCore null,iAction.action()");
            if (iAction != null) {
                iAction.action();
            }
            return false;
        }
        if (room.isMicActive()) {
            RoomCoreLogger.logFile("有连麦的房间，展示断麦拦截弹框");
            RoomCoreJumpHelper.showDisConnectMicWaringBeforeJumpToOtherPage(() -> {
                //如果在连麦中跳到专辑页的话，这时会下麦并拉直播间的flv流，同时又会播专辑的声音，会导致播专辑的声音，但是播放进程的track却是直播的track，
                // 导致肚脐眼、通知栏是直播的，但是播放器播的又是专辑的声音，暂时只能延迟450ms等下麦播直播后，再去播专辑顶掉直播
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        if (iAction != null) {
                            iAction.action();
                        }
                    }
                }, RoomDelayConstants.DELAY_TIME_JUMP_FROM_MICING_ROOM_TO_NEW_ROOM * 3);
            }, () -> {
            }, jumpNewRoom);

            return true;
        } else {
            if (iAction != null) {
                iAction.action();
            }
        }
        return false;
    }

    public static void disconnectMicAndLeaveRoom(Runnable nextAction) {
        RoomCore room = RoomCoreManager.getInstance().getCurrentRoomCore();
        RoomCoreJumpHelper.leaveMicAndExitRoom(room, nextAction);
    }

    public static boolean hasDialogOrDialogFragmentShowing() {
        boolean dialogFragmentShowing = ViewUtil.haveDialogIsShowing((FragmentActivity) MainApplication.getTopActivity());
        boolean dialogShow = FireworkApi.getInstance().isNtDialogIsShowing();
        return dialogFragmentShowing || dialogShow;
    }

    public static void forbiddenUser(final Context ctx, final boolean forbidden, final
    Map<String, String> params, final ILiveRequestCallback<Integer> callback, final RetryCallback
                                             clickCallback, boolean showNotice) {
        if (forbidden && showNotice) {
            if (null == ctx) {
                return;
            }
            final SimpleDialog.LiveDialogBuilder builder =
                    new SimpleDialog.LiveDialogBuilder(ctx);
            builder.setOkInterface("禁言", new SimpleDialog.IDialogInterface() {
                        @Override
                        public void onExecute() {
                            if (clickCallback != null) {
                                clickCallback.onOkClick();
                            }
                            forbiddenUser(ctx, forbidden, params, callback);
                        }
                    }).setCancelInterface("取消", new SimpleDialog.IDialogInterface() {
                        @Override
                        public void onExecute() {
                            if (clickCallback != null) clickCallback.onCancelClick();
                        }
                    }).setShowCloseBtn(false).setMessage("确定将该用户禁言?")
                    .build()
                    .show();
        } else {
            if (clickCallback != null) clickCallback.onOkClick();
            forbiddenUser(ctx, forbidden, params, callback);
        }
    }

    public static void forbiddenUser(
            Context ctx,
            boolean forbidden,
            Map<String, String> params,
            final ILiveRequestCallback<Integer> callback
    ) {
        CommonRequestForLiveHost.forbiddenUserByUidAndRecord(forbidden, params, new
                IDataCallBack<Integer>() {
                    @Override
                    public void onSuccess(Integer object) {
                        if (callback != null) {
                            callback.onSuccess(object);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (callback != null) {
                            callback.onError(code, message);
                        }
                    }
                });
    }

    public static void deleteAdmin(
            final Context context,
            final Map<String, String> params,
            final ILiveDataCallback<Integer> callback
    ) {
        CommonRequestForLiveHost.deletePersonLiveAdminsByUidAndRoomId(params, new
                IDataCallBack<Integer>() {
                    @Override
                    public void onSuccess(Integer object) {
                        if (object == 0) {
                            if (callback != null && callback.canUpdateMyUi()) {
                                callback.onSuccess(object);
                            }
                        } else {
                            callback.onCancel();
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        callback.onCancel();
                    }
                });
    }

    /**
     * 新安装用户上报激活应用事件
     */
    public static void reportNewInstallUserActivateAppEvent() {
        Logger.e("LiveHostCommonUtil", "reportNewInstallUserActivateAppEvent, isNewInstall = " + VersionUtil.isNewInstall());
        if (VersionUtil.isNewInstall() && LiveSettingManager.getReportActivateEventSwitch()) {
            CommonRequestForLiveHost.reportNewInstallUserActivateAppEvent(new IDataCallBack<String>() {
                @Override
                public void onSuccess(@Nullable String data) {
                    Logger.e("LiveHostCommonUtil", "reportNewInstallUserActivateAppEvent, data = " + data);
                    Activity act = MainApplication.getTopActivity();
                    if (LiveHostCommonUtil.isActivityEnable(act) && LiveCommonITingUtil.isITingScheme(data)) {
                        LiveDataReportUtil.INSTANCE.setHasHandleITing(true);
                        LiveCommonITingUtil.handleITing(act, data);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    Logger.e("LiveHostCommonUtil", "reportNewInstallUserActivateAppEvent, code = " + code + ", message = " + message);
                }
            });
        }
    }

    public interface RetryCallback {
        void onOkClick();

        void onCancelClick();
    }

    public interface ILiveDataCallback<T> {
        void onSuccess(T object);

        void onCancel();

        boolean canUpdateMyUi();
    }

    public interface ILiveRequestCallback<T> {
        void onSuccess(T object);

        void onError(int code, String message);

        boolean canUpdateMyUi();
    }


}
