package com.ximalaya.ting.android.live.host.utils.calendar;

import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Color;
import android.net.Uri;
import android.provider.CalendarContract;
import android.text.TextUtils;

import com.tencent.bugly.crashreport.CrashReport;

import java.util.TimeZone;

/**
 * 日历操作工具类
 */
public class LiveCalendarHelper {
    private static final String CALENDAR_URL = "content://com.android.calendar/calendars";
    private static final String CALENDAR_EVENT_URL = "content://com.android.calendar/events";
    private static final String CALENDAR_REMINDER_URL = "content://com.android.calendar/reminders";

    private static final String CALENDARS_NAME = "table";
    private static final String CALENDARS_ACCOUNT_NAME = "<EMAIL>";
    private static final String CALENDARS_ACCOUNT_TYPE = "com.android.table";
    private static final String CALENDARS_DISPLAY_NAME = "日历提醒";
    public static final String DATETIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm";

    /**
     * 添加事件
     *
     * @param context 上下文
     */
    public static boolean addCalendarEvent(Context context, LiveCalendarAlarmInfo alarmInfo) {
        // 获取日历账户的id
        try {
            int calId = checkAndAddCalendarAccount(context);
            if (calId < 0) {
                // 获取账户id失败直接返回，添加日历事件失败
                return false;
            }

            return addEvent(context, calId, alarmInfo);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }

    private static boolean addEvent(Context context, int calId, LiveCalendarAlarmInfo alarmInfo) {
        if (context == null || alarmInfo == null) {
            return false;
        }
        ContentValues event = new ContentValues();
        if (alarmInfo.title != null) {
            event.put("title", alarmInfo.title);
        }
        if (alarmInfo.description != null) {
            event.put("description", alarmInfo.description);
        }
        // 插入账户的id
        event.put("calendar_id", calId);
        event.put(CalendarContract.Events.DTSTART, alarmInfo.startTime);
        event.put(CalendarContract.Events.DTEND, alarmInfo.endTime);
        event.put(CalendarContract.Events.HAS_ALARM, 1);//设置有闹钟提醒

        TimeZone timeZone = TimeZone.getDefault();
        String timeZoneID = timeZone.getID();
        if (timeZoneID == null) {
            timeZoneID = "Asia/Shanghai";
        }
        event.put(CalendarContract.Events.EVENT_TIMEZONE, timeZoneID);  //这个是时区，必须有

        //添加事件
        Uri newEvent = context.getContentResolver()
                .insert(Uri.parse(CALENDAR_EVENT_URL), event);
        if (newEvent == null) {
            // 添加日历事件失败直接返回
            return false;
        }

        //事件提醒的设定
        ContentValues values = new ContentValues();
        values.put(CalendarContract.Reminders.EVENT_ID, ContentUris.parseId(newEvent));

        long advanceMinute = 1;
        if (alarmInfo.advanceMinute > 0) {
            advanceMinute = alarmInfo.advanceMinute;
        }
        values.put(CalendarContract.Reminders.MINUTES, advanceMinute);
        values.put(CalendarContract.Reminders.METHOD, CalendarContract.Reminders.METHOD_ALERT);

        Uri uri = context.getContentResolver().insert(Uri.parse(CALENDAR_REMINDER_URL), values);
        return uri != null;
    }

    //检查是否已经添加了日历账户，如果没有添加先添加一个日历账户再查询
    private static int checkAndAddCalendarAccount(Context context) {
        if (context == null) {
            return -1;
        }
        int oldId = checkCalendarAccount(context);
        if (oldId >= 0) {
            return oldId;
        } else {
            long addId = addCalendarAccount(context);
            if (addId >= 0) {
                return checkCalendarAccount(context);
            } else {
                return -1;
            }
        }
    }

    private static int checkCalendarAccount(Context context) {
        if (context == null) {
            return -1;
        }
        Cursor userCursor = null;
        try {
            userCursor = context.getContentResolver().query(Uri.parse(CALENDAR_URL), null, null, null, null);

            if (userCursor == null)//查询返回空值
                return -1;
            int count = userCursor.getCount();
            if (count > 0) {//存在现有账户，取第一个账户的id返回
                userCursor.moveToFirst();
                return userCursor.getInt(userCursor.getColumnIndex(CalendarContract.Calendars._ID));
            } else {
                return -1;
            }
        } finally {
            if (userCursor != null) {
                userCursor.close();
            }
        }
    }

    private static long addCalendarAccount(Context context) {
        if (context == null) {
            return -1;
        }
        TimeZone timeZone = TimeZone.getDefault();
        ContentValues value = new ContentValues();
        value.put(CalendarContract.Calendars.NAME, CALENDARS_NAME);

        value.put(CalendarContract.Calendars.ACCOUNT_NAME, CALENDARS_ACCOUNT_NAME);
        value.put(CalendarContract.Calendars.ACCOUNT_TYPE, CALENDARS_ACCOUNT_TYPE);
        value.put(CalendarContract.Calendars.CALENDAR_DISPLAY_NAME, CALENDARS_DISPLAY_NAME);
        value.put(CalendarContract.Calendars.VISIBLE, 1);
        value.put(CalendarContract.Calendars.CALENDAR_COLOR, Color.BLUE);
        value.put(CalendarContract.Calendars.CALENDAR_ACCESS_LEVEL, CalendarContract.Calendars.CAL_ACCESS_OWNER);
        value.put(CalendarContract.Calendars.SYNC_EVENTS, 1);
        value.put(CalendarContract.Calendars.CALENDAR_TIME_ZONE, timeZone.getID());
        value.put(CalendarContract.Calendars.OWNER_ACCOUNT, CALENDARS_ACCOUNT_NAME);
        value.put(CalendarContract.Calendars.CAN_ORGANIZER_RESPOND, 0);

        Uri calendarUri = Uri.parse(CALENDAR_URL);
        calendarUri = calendarUri.buildUpon()
                .appendQueryParameter(CalendarContract.CALLER_IS_SYNCADAPTER, "true")
                .appendQueryParameter(CalendarContract.Calendars.ACCOUNT_NAME, CALENDARS_ACCOUNT_NAME)
                .appendQueryParameter(CalendarContract.Calendars.ACCOUNT_TYPE, CALENDARS_ACCOUNT_TYPE)
                .build();

        Uri result = context.getContentResolver().insert(calendarUri, value);
        long id = result == null ? -1 : ContentUris.parseId(result);
        return id;
    }

    public static void deleteCalendarEvent(Context context, String title) {
        if (context == null) {
            return;
        }
        Cursor eventCursor = context.getContentResolver().query(Uri.parse(CALENDAR_EVENT_URL), null, null, null, null);
        try {
            if (eventCursor == null)//查询返回空值
                return;
            if (eventCursor.getCount() > 0) {
                //遍历所有事件，找到title跟需要查询的title一样的项
                for (eventCursor.moveToFirst(); !eventCursor.isAfterLast(); eventCursor.moveToNext()) {
                    String eventTitle = eventCursor.getString(eventCursor.getColumnIndex("title"));
                    if (!TextUtils.isEmpty(title) && title.equals(eventTitle)) {
                        int id = eventCursor.getInt(eventCursor.getColumnIndex(CalendarContract.Calendars._ID));//取得id
                        Uri deleteUri = ContentUris.withAppendedId(Uri.parse(CALENDAR_EVENT_URL), id);
                        int rows = context.getContentResolver().delete(deleteUri, null, null);
                        if (rows == -1) {
                            //事件删除失败
                            return;
                        }
                    }
                }
            }
        } catch (Throwable e) {
            CrashReport.postCatchedException(e);
        } finally {
            if (eventCursor != null) {
                eventCursor.close();
            }
        }
    }


    public static boolean queryCalendarEvent(Context context, String title) {
        if (context == null) {
            return false;
        }
        Cursor eventCursor = null;
        try {
            eventCursor = context.getContentResolver().query(Uri.parse(CALENDAR_EVENT_URL), null, null, null, null);
            if (eventCursor == null)//查询返回空值
                return false;
            if (eventCursor.getCount() > 0) {
                //遍历所有事件，找到title跟需要查询的title一样的项
                for (eventCursor.moveToFirst(); !eventCursor.isAfterLast(); eventCursor.moveToNext()) {
                    String eventTitle = eventCursor.getString(eventCursor.getColumnIndex("title"));
                    if (!TextUtils.isEmpty(title) && title.equals(eventTitle)) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Throwable e) {
            return false;
        } finally {
            if (eventCursor != null) {
                eventCursor.close();
            }
        }
    }

    /**
     * 设置日期提醒需要传递的数据
     */
    public static class LiveCalendarAlarmInfo {
        public String title;
        /**
         * 一加手机只有标题，没有描述，看下其他手机？
         */
        public String description;
        /**
         * 开始时间，单位 ms
         */
        public long startTime;
        /**
         * 结束时间，单位 ms
         */
        public long endTime;
        /**
         * 提前多久提醒，单位：分钟
         */
        public long advanceMinute;
    }
}