package com.ximalaya.ting.android.live.host.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil;
import com.ximalaya.ting.android.live.host.R;
import com.ximalaya.ting.android.live.host.data.paid.PaidLiveItemInfo;

import java.util.List;

/**
 * 已购直播 列表adapter
 *
 * <AUTHOR>
 * @date 18/8/16
 */

public class PaidLivePageListAdapter extends BaseAdapter {

    private Context mContext;
    private List<PaidLiveItemInfo> mData;

    private LayoutInflater mInflater;

    private int mCoverWidth;
    private int mAvatarWidth;


    //构造
    public PaidLivePageListAdapter(Context context,
                                   List<PaidLiveItemInfo> data) {
        mContext = context;
        mData = data;
        mInflater = LayoutInflater.from(context);

        mCoverWidth = BaseUtil.dp2px(context, 80);
        mAvatarWidth = BaseUtil.dp2px(context, 20);
    }


    public List<PaidLiveItemInfo> getData() {
        return mData;
    }

    @Override
    public int getCount() {
        return mData == null ? 0 : mData.size();
    }

    @Override
    public PaidLiveItemInfo getItem(int position) {

        return ToolUtil.isEmptyCollects(mData) ? null : mData.get(position);

    }

    @Override
    public long getItemId(int position) {
        return position;
    }




    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {

        final ViewHolder holder;

        if (convertView == null) {
            holder = new ViewHolder();

            convertView = mInflater.inflate(R.layout.live_host_item_layout_bought_live_list, parent, false);
            holder.container = convertView.findViewById(R.id.live_container_item);
            holder.tvTime = convertView.findViewById(R.id.live_tv_live_time);

            holder.ivCover = convertView.findViewById(R.id.live_iv_cover);
            holder.tvLiveGoingState = convertView.findViewById(R.id.live_tv_status);

            holder.tvTitle = convertView.findViewById(R.id.live_tv_title);

            holder.tvBtnEnter = convertView.findViewById(R.id.live_btn_goto_live);
            holder.tvNotEnter = convertView.findViewById(R.id.live_tv_live_not_enter);

            holder.ivHostAvatar = convertView.findViewById(R.id.live_iv_host_avatar);
            holder.tvHostName = convertView.findViewById(R.id.live_tv_host_name);


            convertView.setTag(holder);


        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (position >= mData.size()) {
            return convertView;
        }

        final PaidLiveItemInfo model = mData.get(position);

        if (model != null) {

            //时间戳显示
            showTimeStamp(model, holder);

            //显示课程封面
            showCourseCover(model, holder);

            //直播状态角标
            showLiveStatusCornerImg(model, holder);

            //显示课程标题
            holder.tvTitle.setText(model.name);

            //显示进入直播的按钮布局
            showLiveEnterButton(model, holder);

            //显示主播信息
            showLiveHostInfo(model, holder);


            holder.container.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListItemCallback != null) {
                        mListItemCallback.onClickCourseItem(holder, model, position);
                    }
                }
            });

            holder.tvBtnEnter.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListItemCallback != null) {
                        mListItemCallback.onClickBtnEnter(holder, model, position);
                    }
                }
            });


        }


        return convertView;
    }


    /**
     * 显示课程显示时间
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showTimeStamp(PaidLiveItemInfo model, ViewHolder holder) {

        if (holder.tvTime == null) {
            return;
        }

        holder.tvTime.setVisibility(View.VISIBLE);

        switch (model.status) {

            case PersonLiveBase.LIVE_STATUS_NOTICE: {//即将开始
                holder.tvTime.setText(LiveTimeUtil.convertTimeToPreData(model.startAt));
                break;
            }

            case PersonLiveBase.LIVE_STATUS_ING: {//直播中
                holder.tvTime.setText(LiveTimeUtil.convertTimeToPreData(model.startAt));

                break;
            }

            case PersonLiveBase.LIVE_STATUS_END: {//直播结束 有无回放

                holder.tvTime.setText(LiveTimeUtil.convertTimeToPreData(model.startAt));
                break;
            }


            default:
                holder.tvTime.setVisibility(View.INVISIBLE);
                break;
        }
    }

    /**
     * 显示课程封面
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showCourseCover(PaidLiveItemInfo model, ViewHolder holder) {

        ImageManager.from(mContext)
                .displayImage(null, holder.ivCover, model.coverMiddle, com.ximalaya.ting.android.live.common.R.drawable.live_common_ic_user_info_head_default, 0
                        , mCoverWidth, mCoverWidth
                        , null, null, true);
    }


    /**
     * 显示课程状态角标
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showLiveStatusCornerImg(PaidLiveItemInfo model, ViewHolder holder) {

        if (holder.tvLiveGoingState == null) {
            return;
        }

        holder.tvLiveGoingState.setVisibility(View.VISIBLE);

        switch (model.status) {

            case PersonLiveBase.LIVE_STATUS_NOTICE: {//即将开始
                holder.tvLiveGoingState.setBackgroundResource(R.drawable.live_bg_tag_paid_notice);
                holder.tvLiveGoingState.setText("即将开播");
                break;
            }

            case PersonLiveBase.LIVE_STATUS_ING: {//直播中
                holder.tvLiveGoingState.setBackgroundResource(R.drawable.live_bg_tag_paid_living);
                holder.tvLiveGoingState.setText("直播中");
                break;
            }

            case PersonLiveBase.LIVE_STATUS_END: {//直播结束 有无回放

                if (model.playbackStatus == 2) {//已经完成回放生成
                    holder.tvLiveGoingState.setBackgroundResource(R.drawable.live_bg_tag_paid_playback);
                    holder.tvLiveGoingState.setText("有回放");
                } else {
                    holder.tvLiveGoingState.setBackgroundResource(R.drawable.live_bg_tag_paid_live_end);
                    holder.tvLiveGoingState.setText("已结束");
                }
                break;
            }


            default:
                holder.tvLiveGoingState.setVisibility(View.INVISIBLE);
                break;
        }

    }



    /**
     * 显示主播信息
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showLiveEnterButton(PaidLiveItemInfo model, ViewHolder holder) {

        if (holder.tvBtnEnter == null || holder.tvNotEnter == null) {
            return;
        }

        switch (model.status) {

            case PersonLiveBase.LIVE_STATUS_NOTICE: {

                if (model.isBooking) {//已经预约

                    holder.tvBtnEnter.setVisibility(View.GONE);
                    holder.tvNotEnter.setVisibility(View.VISIBLE);

                    holder.tvNotEnter.setText("已预约");

                } else {//没有预约

                    holder.tvBtnEnter.setVisibility(View.VISIBLE);
                    holder.tvNotEnter.setVisibility(View.GONE);

                    holder.tvBtnEnter.setBackgroundResource(R.drawable.live_bg_btn_paid_notice);
                    holder.tvBtnEnter.setTextColor(Color.parseColor("#FF4F32"));
                    holder.tvBtnEnter.setText("去预约");

                }

                break;
            }


            case PersonLiveBase.LIVE_STATUS_ING: {

                holder.tvBtnEnter.setVisibility(View.VISIBLE);
                holder.tvNotEnter.setVisibility(View.GONE);

                holder.tvBtnEnter.setBackgroundResource(R.drawable.live_bg_btn_paid_living);
                holder.tvBtnEnter.setTextColor(Color.parseColor("#FFFFFF"));
                holder.tvBtnEnter.setText("进直播");

                break;
            }

            case PersonLiveBase.LIVE_STATUS_END: {//已经结束
                if (model.playbackStatus != 2) {//没有回放
                    holder.tvBtnEnter.setVisibility(View.GONE);
                    holder.tvNotEnter.setVisibility(View.VISIBLE);
                    holder.tvNotEnter.setText("回放生成中");
                } else {//已经回放
                    holder.tvBtnEnter.setVisibility(View.VISIBLE);
                    holder.tvNotEnter.setVisibility(View.GONE);

                    holder.tvBtnEnter.setBackgroundResource(R.drawable.live_bg_btn_paid_playback);
                    holder.tvBtnEnter.setTextColor(Color.parseColor("#2875FF"));
                    holder.tvBtnEnter.setText("看回放");
                }
                break;
            }

            default:
                break;
        }

    }


    /**
     * 显示课程进入按钮
     * @param model 数据集合
     * @param holder 控件holder
     */
    private void showLiveHostInfo(PaidLiveItemInfo model, ViewHolder holder) {

        if (holder.ivHostAvatar == null || holder.tvHostName == null) {
            return;
        }

        ImageManager.from(mContext)
                .displayImage(null, holder.ivHostAvatar, model.avatar, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88, 0
                        , mAvatarWidth, mAvatarWidth
                        , null, null, true);

        holder.tvHostName.setText(model.nickname);

    }


    public void clearData() {
        mData.clear();
        notifyDataSetChanged();
    }

    public void addListData(List<PaidLiveItemInfo> courseList) {
        mData.addAll(courseList);
        notifyDataSetChanged();
    }

    public void removeItem(PaidLiveItemInfo model) {
        if (mData != null && mData.size() > 0) {
            boolean isDel = mData.remove(model);
            if (isDel) {
                notifyDataSetChanged();
            }
        }
    }


    public static class ViewHolder {

        View container;

        TextView tvTime;

        ImageView ivCover;
        TextView tvLiveGoingState;

        TextView tvTitle;//第1行

        TextView tvBtnEnter;
        TextView tvNotEnter;

        ImageView ivHostAvatar;
        TextView tvHostName;

    }

    private IOnClickListItemCallback mListItemCallback;

    public void setOnClickListItemCallback(IOnClickListItemCallback callback) {
        mListItemCallback = callback;
    }

    public interface IOnClickListItemCallback {

        void onClickCourseItem(ViewHolder holder, PaidLiveItemInfo model, int pos);

        void onClickBtnEnter(ViewHolder holder, PaidLiveItemInfo model, int pos);

    }

}
