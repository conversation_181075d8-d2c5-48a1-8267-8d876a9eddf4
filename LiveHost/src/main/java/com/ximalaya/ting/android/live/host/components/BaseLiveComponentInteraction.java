package com.ximalaya.ting.android.live.host.components;

import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.live.biz.mode.data.PrivateChatViewModel;
import com.ximalaya.ting.android.live.common.chatlist.view.gradient.GradientScene;
import com.ximalaya.ting.android.live.common.input.emoji.IEmojiItem;
import com.ximalaya.ting.android.live.common.input.model.HotWordModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.host.R;
import com.ximalaya.ting.android.live.host.components.biggift.IBigGiftInteraction;
import com.ximalaya.ting.android.live.host.components.bullet.IBulletComponentInteraction;
import com.ximalaya.ting.android.live.host.components.chatlist.IBaseChatListComponent;
import com.ximalaya.ting.android.live.host.components.enterroom.ILiveEnterAndGiftPopInteraction;
import com.ximalaya.ting.android.live.host.components.enterroom.high.ILiveEnterAnimComponentInteraction;
import com.ximalaya.ting.android.live.host.components.exitroom.IRoomExitInteraction;
import com.ximalaya.ting.android.live.host.components.guide.common.ICommonGuideComponent;
import com.ximalaya.ting.android.live.host.components.hotword.IHotWordComponentInteraction;
import com.ximalaya.ting.android.live.host.components.input.IInputPaneInteraction;
import com.ximalaya.ting.android.live.host.components.input.IInputPanelComponent;
import com.ximalaya.ting.android.live.host.components.popdialog.ICommonPopComponentInteraction;
import com.ximalaya.ting.android.live.host.components.privatechat.IPrivateChatComponentInteraction;
import com.ximalaya.ting.android.live.host.components.recommend.IRecommendLiveComponentInteraction;
import com.ximalaya.ting.android.live.host.components.red_pack_heavenly.IRedPackHeavenlyInteraction;
import com.ximalaya.ting.android.live.host.components.redpack.IRedPackComponentInteraction;
import com.ximalaya.ting.android.live.host.components.rightarea.IRoomRightAdComponentInteraction;
import com.ximalaya.ting.android.live.host.components.roombackground.IRoomBackgroundInteraction;
import com.ximalaya.ting.android.live.host.components.roomloading.IRoomLoadingInteraction;
import com.ximalaya.ting.android.live.host.components.slideclearscreen.ISlideClearScreenComponentInteraction;
import com.ximalaya.ting.android.live.host.components.subscriberoom.ISubRoomComponentInteraction;
import com.ximalaya.ting.android.live.host.components.subscriberoom.RoomSubscribeItem;
import com.ximalaya.ting.android.live.host.components.vote.ILiveVoteComponentInteraction;
import com.ximalaya.ting.android.live.host.fragment.room.BaseRoomFragment;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreLogger;
import com.ximalaya.ting.android.live.host.view.RoomBackgroundView;
import com.ximalaya.ting.android.live.lifecycle.BaseComponentInteraction;
import com.ximalaya.ting.android.live.lifecycle.IComponentHost;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

/**
 * 公共组件通信接口。
 *
 * <AUTHOR>
 * @since 2022/11/9
 */
public abstract class BaseLiveComponentInteraction extends BaseComponentInteraction implements
        IBaseLiveHostInteraction,
        IBigGiftInteraction,
        IBulletComponentInteraction,
        IHotWordComponentInteraction,
        IRoomRightAdComponentInteraction,
        ICommonPopComponentInteraction,
        IPrivateChatComponentInteraction,
        IRoomLoadingInteraction,
        IRoomBackgroundInteraction,
        ILiveEnterAnimComponentInteraction,
        IRedPackComponentInteraction,
        IInputPaneInteraction,
        ILiveEnterAndGiftPopInteraction,
        ILiveVoteComponentInteraction,
        ISlideClearScreenComponentInteraction,
        IRedPackHeavenlyInteraction,
        IRoomExitInteraction,
        IRecommendLiveComponentInteraction, ISubRoomComponentInteraction {

    protected BaseRoomFragment<?> mBaseRoomFragment;

    @Override
    public void setComponentHost(IComponentHost<?> componentHost) {
        super.setComponentHost(componentHost);
        if (componentHost.getHostContext().getFragment() instanceof BaseRoomFragment) {
            mBaseRoomFragment = (BaseRoomFragment<?>) componentHost.getHostContext().getFragment();
        }
    }

    @Nullable
    @Override
    public ViewStub getRedPackHeavenlyBigAnimationViewStub() {
        return mBaseRoomFragment.findViewById(R.id.live_redpack_heavenly_big_stub);
    }

    @Override
    public void sendMessage(String content) {
        mBaseRoomFragment.sendMessage(content);
    }

    @Override
    public void sendImgMsg(String imgUri) {
        mBaseRoomFragment.sendImgMsg(imgUri);
    }

    @Override
    public void sendEmojiMsg(IEmojiItem emojiItem) {
        mBaseRoomFragment.sendEmojiMsg(emojiItem);
    }

    @Override
    public void showUserInfoPop(long uid) {
        mBaseRoomFragment.showUserInfoCard(uid);
    }


    @Override
    public void showGiftPanel() {
        mBaseRoomFragment.showGiftPanel();
    }

    @Override
    public void showFansClubDialogFragment(String url) {
        mBaseRoomFragment.showFansClubDialogFragment(url);
    }

    @Override
    public void showGiftPanel(long targetUid) {
        mBaseRoomFragment.showGiftPanel(targetUid);
    }

    @Override
    public int getLiveMediaType() {
        return mBaseRoomFragment.getLiveMediaType();
    }

    @Override
    @BaseScrollConstant.LiveRoomBizType
    public int getRoomBizType() {
        return mBaseRoomFragment.getRoomBizType();
    }

    @Override
    public int getPlaySource() {
        return mBaseRoomFragment.getPlaySource();
    }

    @Override
    public ViewGroup getRootView() {
        return mBaseRoomFragment.getRootView();
    }

    @Override
    public void showInputPanel() {

    }

    @Override
    public PrivateChatViewModel getPrivateChatViewModel() {
        return mBaseRoomFragment.getPrivateChatViewModel();
    }

    @Override
    public void dispatchHotWordModel(HotWordModel model) {

    }

    @Override
    public void selectHotWord(String hotWord) {

    }

    @Override
    public void performHotWordBtnClick() {

    }

    @Override
    public void onHotWordViewVisibleChange(boolean show) {

    }

    @Override
    public void onTopOperationViewShow(boolean show) {

    }

    @Override
    public void onBottomOperationViewShow(boolean show) {

    }

    @Override
    public void onRoomRetryLoadDetailBtnClick() {
        mBaseRoomFragment.onRoomRetryLoadDetailBtnClick();
    }

    @Override
    public void onRoomLoadErrorViewShow(boolean show) {

    }

    @Override
    public boolean hasDialogShowing() {
        return mBaseRoomFragment.hasDialogShowing();
    }

    @Override
    public boolean isKeyboardShowing() {
        IInputPanelComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_INPUT_PANEL);
        if (comp != null) return comp.isKeyboardPanelShowed();

        return false;
    }

    @Override
    public void showRoomImgBackground(@Nullable String url, boolean withCover, boolean replaceRecommendBgImagePath) {
        mBaseRoomFragment.handleImgBackground(url, withCover, replaceRecommendBgImagePath);
    }

    @Override
    public void showSpecialBackgroundRes(int res) {
        mBaseRoomFragment.showSpecialBackgroundRes(res);
    }

    @Override
    public boolean isMp4BackgroundPreShow() {
        return mBaseRoomFragment.isMp4BackgroundPreShow();
    }

    @Nullable
    @Override
    public ViewGroup getRoomMp4BackgroundViewParent() {
        return null;
    }

    @Nullable
    @Override
    public RoomBackgroundView getHomeRoomMp4BackgroundView() {
        if(mBaseRoomFragment != null && mBaseRoomFragment.getRoomParentFragment() != null){
           return mBaseRoomFragment.getRoomParentFragment().getHomeRoomMp4BackgroundView();
        }
        return null;
    }

    @Override
    public void showUserInfoPopInPrivateChat(long uid) {
        mBaseRoomFragment.showUserInfoCard(uid);
    }

    @Override
    public void doTopGradualEffect(@GradientScene int scene, int targetHeight) {
        IBaseChatListComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST);
        if (comp != null) comp.doTopGradualEffect(scene, targetHeight);
    }

    @Override
    public void restoreTopGradualEffect(@GradientScene int scene) {
        IBaseChatListComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST);
        if (comp != null) comp.restoreTopGradualEffect(scene);
    }

    @Override
    public PlayerConstants.ResolutionRatio getVideoSizeRatio() {
        return null;
    }

    @Override
    public int getVideoHeight() {
        return 0;
    }

    public boolean isNeedAnonymity() {
        return false;
    }

    @Override
    public int getVideoWidth() {
        return 0;
    }

    @Override
    public boolean isPiaPanelExpanded() {
        return false;
    }

    @Nullable
    @Override
    public ViewStub getRedPackBigAnimationViewStub() {
        return mBaseRoomFragment.findViewById(R.id.live_redpack_big_stub);
    }

    @Nullable
    @Override
    public ViewStub getVoteBigAnimationViewStub() {
        return null;
    }

    @Override
    public int getRoomMode() {
        return 0;
    }

    @Override
    public void sendWordsRedPacket(@Nullable String words) {
        mBaseRoomFragment.sendMessage(words);
    }

    @Override
    public boolean isFollowCurrentAnchorOrPresident() {
        return mBaseRoomFragment.isFollowHost();
    }

    @Override
    public boolean interceptAllChatListGuide() {
        return mBaseRoomFragment.interceptAllChatListGuide();
    }

    @Override
    public boolean isExchangeGuideAnimationShowing() {
        return mBaseRoomFragment.isExchangeGuideAnimationShowing();
    }

    @Override
    public void followCurrentAnchorOrPresident(int sceneType) {
        mBaseRoomFragment.doFollowHost(sceneType);
    }

    @Override
    public void followUser(long targetUid, int sceneType, @Nullable Runnable doAfterFollowedSuccess) {
        mBaseRoomFragment.followUser(targetUid, sceneType, doAfterFollowedSuccess);
    }

    @Override
    public LiveData<GiftInfoCombine> getGiftInfoLiveData() {
        if (mBaseRoomFragment == null) {
            return null;
        }
        BaseGiftLoader giftLoader = mBaseRoomFragment.getGiftLoader();
        if (giftLoader == null) {
            return null;
        }
        return giftLoader.getGiftInfoCombineLiveData();
    }

    @Override
    public void keyboardShowStateChange(boolean show) {
        mBaseRoomFragment.keyboardShowStateChange(show);
    }

    @Override
    public LiveUserInfo getCurrentUserInfo() {
        return mBaseRoomFragment.getCurrentUserInfo();
    }

    @Override
    public String getRandomHotWords() {
        return null;
    }

    @Override
    public void setHotWordsVisibility(int visibility) {

    }

    @Override
    public View getRoomBottomLayout() {
        return null;
    }

    @Override
    public void listScrollToBottom() {
        mBaseRoomFragment.listScrollToBottom();
    }

    @Override
    public void requestPlayMode(int playMode) {

    }

    @Override
    public void onBottomOperationViewClicked() {

    }

    @Override
    public BaseVirtualRoom getRoomCore() {
        if (mBaseRoomFragment != null) {
            return mBaseRoomFragment.getRoomCore();
        }
        CustomToast.showDebugFailToast("getRoomCore error ,mBaseRoomFragment null");
        if (ConstantsOpenSdk.isDebug) {
            RoomCoreLogger.logFile(getClass().getSimpleName() + "getRoomCore: " + Log.getStackTraceString(new Throwable()));
        }
        return null;
    }

    @Override
    public void showRecallGiftPopGuide(@Nullable Runnable dismissCallback) {
        if (mBaseRoomFragment != null) {
            ICommonGuideComponent comp = mBaseRoomFragment.getComponentSafety(
                    IBaseRoomCompConfig.COMPONENT_COMMON_GUIDE
            );
            if (comp != null) comp.showRecallGiftGuide(dismissCallback);
        }
    }

    @Override
    public void onCloseRoomClick() {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.handRoomCloseBtnClick();
        }
    }

    @Override
    public void onMiniRoomClick() {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.handRoomMiniBtnClick();
        }
    }

    @Override
    public boolean isRoomMiniSupport() {
        return mBaseRoomFragment != null && mBaseRoomFragment.isRoomMiniSupport();
    }

    @Override
    public int restoreClearScreenBottomMargin() {
        return 0;
    }

    @Override
    public void handleRequestMinimizeRoom() {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.handleRequestMinimizeRoom();
        }
    }

    @Override
    public void finishFragment() {
        if (mBaseRoomFragment != null) {
            mBaseRoomFragment.finishFragment();
        }
    }

    @Override
    public void onSubscribePanelShow() {

    }

    @Override
    public void onRoomPreviewDelete(RoomSubscribeItem item) {

    }

    @Override
    public void onSubScribeSelectUpdate(@Nullable RoomSubscribeItem item) {

    }

    @Override
    public void onMp4BackgroundPrepared() {
        if(mBaseRoomFragment != null && mBaseRoomFragment.getRoomParentFragment() != null){
            mBaseRoomFragment.getRoomParentFragment().onMp4BackgroundPrepared();
        }
    }

    @Override
    public void onMp4BackgroundPlayStart() {
        if(mBaseRoomFragment != null && mBaseRoomFragment.getRoomParentFragment() != null){
            mBaseRoomFragment.getRoomParentFragment().onMp4BackgroundPlayStart();
        }
    }
}
