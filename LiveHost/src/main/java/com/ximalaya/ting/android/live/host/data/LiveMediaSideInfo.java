package com.ximalaya.ting.android.live.host.data;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 媒体附加信息
 *
 * <AUTHOR>
 */
public class LiveMediaSideInfo {

    /**
     * 音量信息
     */
    public static final int TYPE_VOLUME = 1;

    /**
     * K 歌歌词同步信息
     */
    public static final int TYPE_KTV_LYRICS_SYNC = 2;

    /**
     * 聊天室话题分段 Token 信息
     */
    public static final int TYPE_TOPIC_TOKEN = 3;

    /**
     * 连麦&PK 信息
     */
    public static final int TYPE_MIC_PK = 4;

    /**
     * 首映室进度信息
     */
    public static final int TYPE_PREMIERE_PROGRESS = 5;

    /**
     * ASR 信息
     */
    public static final int TYPE_ASR = 6;

    /**
     * 音效信息
     */
    public static final int TYPE_SOUND_EFFECT = 7;

    @IntDef({TYPE_VOLUME, TYPE_KTV_LYRICS_SYNC, TYPE_TOPIC_TOKEN, TYPE_MIC_PK,
            TYPE_PREMIERE_PROGRESS, TYPE_ASR, TYPE_SOUND_EFFECT})
    @Retention(RetentionPolicy.SOURCE)
    @interface Type {

    }

    @Type
    private int type;
    private MediaSideInfoContent content;

    public static class MediaSideInfoContent {

        /**
         * 音量大小
         */
        public double volume;

        /**
         * 用户uid
         */
        public long uid;

        /**
         * 当前推流时间
         */
        public long currentPosition;

        /**
         * 推流总时长
         */
        public long totalDuration;

        public MediaSideInfoContent(double volume, long uid) {
            this.volume = volume;
            this.uid = uid;
        }

        @NonNull
        @Override
        public String toString() {
            return "MediaSideInfoContent{" +
                    "volume=" + volume +
                    ", uid=" + uid +
                    '}';
        }
    }

    @Type
    public int getType() {
        return type;
    }

    public LiveMediaSideInfo setType(@Type int type) {
        this.type = type;
        return this;
    }

    public MediaSideInfoContent getContent() {
        return content;
    }

    public LiveMediaSideInfo setContent(MediaSideInfoContent content) {
        this.content = content;
        return this;
    }

    @NonNull
    @Override
    public String toString() {
        return "LiveMediaSideInfo{" +
                "type=" + type +
                ", content=" + content +
                '}';
    }
}
