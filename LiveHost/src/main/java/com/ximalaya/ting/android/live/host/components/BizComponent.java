package com.ximalaya.ting.android.live.host.components;

import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.live.lifecycle.CompLogger;
import com.ximalaya.ting.android.live.lifecycle.ComponentGroup;
import com.ximalaya.ting.android.live.lifecycle.IBizComponent;
import com.ximalaya.ting.android.live.lifecycle.IHostDispatchData;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * BizComponent
 *
 * @Date 2024/8/1
 * @Email <EMAIL>
 * <AUTHOR>
 */
public class BizComponent<T> extends ComponentGroup implements IBizComponent<T> {
    @Nullable
    private T mHostData;
    @Nullable
    private T mHostSaveData;

    private long mCurrentRoomId;

    private int mOrientation;

    //视频直播分为三种模式：
    // land - 横屏直播 16：9
    // port - 竖屏直播 9：16
    // full - 全屏直播 手机旋转为全屏模式
    protected boolean mIsFull;

    private boolean isHalfMode;

    private boolean shouldUpdateResolution;

    /**
     * 用户是否正在输入，正在输入时，不展示某些组件
     */
    private boolean mUserIsInInput = false;

    private boolean mGiftPanelShow = false;

    private boolean isReadingHostDataAndBindData = false;

    public boolean isUserInInput() {
        return mUserIsInInput;
    }

    public boolean isShouldUpdateResolution() {
        return shouldUpdateResolution;
    }

    public void setShouldUpdateResolution(boolean shouldUpdateResolution) {
        this.shouldUpdateResolution = shouldUpdateResolution;
    }

    public T getHostData() {
        if (mHostData == null) {
            Log.e(getClass().getSimpleName(), "getHostData null");
        }
        return mHostData;
    }

    public void setRoomId(long roomId) {
        mCurrentRoomId = roomId;
    }

    public long getRoomId() {
        if (mCurrentRoomId <= 0) {
            Log.e(getClass().getSimpleName(), "getRoomId <=0");
        }
        return mCurrentRoomId;
    }

    public boolean isGiftPanelShow() {
        return mGiftPanelShow;
    }

    public boolean isFull() {
        return mIsFull;
    }

    public int getOrientation() {
        return mOrientation;
    }

    public boolean isHalfMode() {
        return isHalfMode;
    }

    protected void readDispatchDataFromHost() {
        if (getComponentHost() != null) {
            try {
                mHostSaveData = getComponentHost().getDispatchData().getValue(IHostDispatchData.KEY_HOST_DATA);
                if (isLayoutInflateWhenRealUse()) {
                    afterReadBindData();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                isHalfMode = getComponentHost().getDispatchData().getValue(IHostDispatchData.KEY_HALF_MODE);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                mUserIsInInput = getComponentHost().getDispatchData().getValue(IHostDispatchData.KEY_INPUT_SHOW);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                mOrientation = getComponentHost().getDispatchData().getValue(IHostDispatchData.KEY_ORIENTATION);
                this.mIsFull = mOrientation == Configuration.ORIENTATION_LANDSCAPE;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        readDispatchDataFromHost();
    }

    @Override
    public void bindData(@NonNull T hostData) {
        this.mHostData = hostData;
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchHostData(hostData);
        }
    }

    protected void initRoomArguments(long newRoomId, Bundle newArgs) {
        mCurrentRoomId = newRoomId;
        setArguments(newArgs);
    }

    @Override
    public void onSwitchRoom(long newRoomId, Bundle newArgs) {
        CompLogger.i(TAG_LIFE_CYCLE, "component = " + getClass().getSimpleName() + " onSwitchRoom()");
        clearPaddingActions();
        resetData();
        resetView();
        initRoomArguments(newRoomId, newArgs);
        loadData();
        if (getChildComponentHost() instanceof ILiveRoomCompHost<?>) {
            ((ILiveRoomCompHost<?>) getChildComponentHost()).dispatchRoomSwitch(newRoomId, newArgs);
        }
    }

    @Override
    public void onSwitchSameRoom() {
        CompLogger.i(TAG_LIFE_CYCLE, "component = " + getClass().getSimpleName() + " onSwitchSameRoom()");
        if (getChildComponentHost() instanceof ILiveRoomCompHost<?>) {
            ((ILiveRoomCompHost<?>) getChildComponentHost()).dispatchSameRoomSwitch();
        }
    }


    @Override
    public boolean onBackPressed() {
        if (getChildComponentHost() instanceof ILiveRoomCompHost<?>) {
            return getChildComponentHost().dispatchBackPress() || super.onBackPressed();
        }
        return super.onBackPressed();
    }

    @Override
    public void onUserInputStatusChange(boolean isInput) {
        mUserIsInInput = isInput;
        if (getChildComponentHost() instanceof ILiveRoomCompHost<?>) {
            ((ILiveRoomCompHost<?>) getChildComponentHost()).dispatchInputKeyboardStatusChange(isInput);
        }
    }

    @Override
    public void onGiftDialogShowStateChange(boolean show) {
        mGiftPanelShow = show;
    }

    @Override
    public void onReceiveRoomStatusChange(@PersonLiveBase.LiveStatus int status) {

    }

    @Override
    public void onOrientationChange(int orientation, boolean isSameOrientation) {
        this.mIsFull = orientation == Configuration.ORIENTATION_LANDSCAPE;
        this.mOrientation = orientation;
        if (getChildComponentHost() instanceof ILiveRoomCompHost<?>) {
            ((ILiveRoomCompHost<?>) getChildComponentHost()).dispatchOrientationChange(orientation, isSameOrientation);
        }
    }

    @Override
    public void updateComponentViewOnScreenModeChange(boolean halfMode) {
        this.isHalfMode = halfMode;
        if (getChildComponentHost() instanceof ILiveRoomCompHost<?>) {
            ((ILiveRoomCompHost<?>) getChildComponentHost()).dispatchRoomHalfModeChange(halfMode);
        }
    }

    @Override
    public void onCreateView(@Nullable View view) {
        super.onCreateView(view);
        afterReadBindData();
    }

    protected void afterReadBindData() {
        // read from host a noNull host data ,try bind once
        if (mHostSaveData != null && mHostSaveData != mHostData) {
            isReadingHostDataAndBindData = true;
            bindData(mHostSaveData);
            isReadingHostDataAndBindData = false;
            mHostSaveData = null;
        }
    }

    @Override
    public void runAfterViewInflate() {
        if (isReadingHostDataAndBindData) {
            CustomToast.showDebugFailToast("bindData 中创建视图，请使用有回调的方法 runAfterViewInflate(Runnable callback)");
            Logger.e(TAG, "bindData 中创建视图，请使用有回调的方法 runAfterViewInflate(Runnable callback) \n"
                    + Log.getStackTraceString(new Throwable()));
        }
        super.runAfterViewInflate();
    }

    @Override
    protected void showCallRunAfterViewInflateWarning() {
        if (isReadingHostDataAndBindData) {
            return;
        }
        super.showCallRunAfterViewInflateWarning();
    }

    @Nullable
    public FragmentManager getChildFragmentManager() {
        return getFragment() != null ? getFragment().getChildFragmentManager() : null;
    }

    @Nullable
    public Fragment getFragment() {
        return getComponentHost() != null ? getComponentHost().getHostContext().getFragment() : null;
    }

    public boolean canUpdateUi() {
        return getComponentHost() != null && getComponentHost().getHostContext().canUpdateUi();
    }


    @Override
    public void resetData() {
        super.resetData();
        mHostData = null;
    }

    @Override
    public void resetView() {
        throw new RuntimeException("resetView should be implemented by self");
    }
}
