package com.ximalaya.ting.android.live.host.components;

import android.content.Context;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.mvi.ILiveBaseViewModel;
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereInfo;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereStatus;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.host.fragment.room.BaseRoomFragment;
import com.ximalaya.ting.android.live.host.fragment.room.IRoomParentFragment;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.lifecycle.BizViewModel;
import com.ximalaya.ting.android.live.lifecycle.ComponentContext;
import com.ximalaya.ting.android.live.lifecycle.IComponentHost;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * 组件基类
 *
 * <AUTHOR>
 * @since 2022/11/9
 */
public class BaseLiveComponent<T extends BaseRoomDetail> extends BizComponent<T> implements ILoginStatusChangeListener {

    private boolean officialRoomFlag = false;

    private boolean premiereRoomFlag = false;

    @LiveMediaType
    private int liveMediaType;

    protected boolean piaModeOn;

    @PersonLiveBase.LiveStatus
    private int liveStatus;

    private LiveUserInfo mCurrentLoginUserInfo;

    private LiveUserInfo mCurrentAnchorUserInfo;

    protected PremiereInfo mPremiereInfo;

    @Override
    public void onCreate() {
        super.onCreate();
        if (listenUserLoginStatus()) {
            UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (listenUserLoginStatus()) {
            UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
        }
    }

    @Override
    protected void readDispatchDataFromHost() {
        super.readDispatchDataFromHost();
        if (getComponentHost() != null) {
            try {
                officialRoomFlag = getComponentHost().getDispatchData().getValue(LiveDispatchData.KEY_OfficialRoomFlag);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                premiereRoomFlag = getComponentHost().getDispatchData().getValue(LiveDispatchData.KEY_PremiereRoomFlag);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                mPremiereInfo = getComponentHost().getDispatchData().getValue(LiveDispatchData.KEY_PremiereInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (mPremiereInfo != null) {
                updatePremiereInfo(mPremiereInfo);
            }
            try {
                piaModeOn = getComponentHost().getDispatchData().getValue(LiveDispatchData.KEY_isPiaMode);
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                setCurrentLoginUserInfo(getComponentHost().getDispatchData().getValue(LiveDispatchData.KEY_CURRENT_USER_INFO));
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                setCurrentAnchorUserInfo(getComponentHost().getDispatchData().getValue(LiveDispatchData.KEY_CURRENT_ANCHOR_INFO));
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    public long getHostUid() {
        if (getHostData() != null) {
            return getHostData().getHostUid();
        }
        return 0;
    }

    public String getHostNickname() {
        if (getHostData() != null) {
            return getHostData().getAnchorName();
        }
        return "";
    }

    public long getLiveId() {
        if (getHostData() != null) {
            return getHostData().getLiveId();
        }
        return 0;
    }

    protected long getChatId() {
        if (getHostData() != null) {
            return getHostData().getChatId();
        }
        return 0;
    }

    @BaseScrollConstant.LiveRoomBizType
    public int getRoomBizType() {
        if (getHostData() != null) {
            return getHostData().getRoomBizType();
        }
        return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_UNSET;
    }

    public long getRoomCurrentPresideUid() {
        if (getFragment() instanceof BaseRoomFragment) {
            return ((BaseRoomFragment<?>) getFragment()).getRoomCurrentPresideUid();
        }

        return 0;
    }

    public boolean isOfficialRoomFlag() {
        if (getFragment() instanceof BaseRoomFragment) {
            return ((BaseRoomFragment<?>) getFragment()).isOfficialRoomFlag();
        }

        return false;
    }

    public String getLiveAdCid() {
        if (getFragment() instanceof BaseRoomFragment) {
            return ((BaseRoomFragment<?>) getFragment()).getLiveAdCid();
        } else {
            return "";
        }
    }

    public String getLiveAdPayload() {
        if (getFragment() instanceof BaseRoomFragment) {
            return ((BaseRoomFragment<?>) getFragment()).getLiveAdPayload();
        } else {
            return "";
        }
    }

    @Override
    public void setComponentHost(IComponentHost<? extends ComponentContext> componentContainer) {
        super.setComponentHost(componentContainer);
    }

    @Override
    public void bindData(@NonNull T hostData) {
        super.bindData(hostData);
        BizViewModel viewModel = getViewModel();
        if (viewModel instanceof ILiveBaseViewModel) {
            ((ILiveBaseViewModel<?, ?>) viewModel).bindData(hostData);
        }
        liveMediaType = hostData.getMediaType();
        setRoomId(hostData.getRoomId());
        liveStatus = hostData.getStatus();
    }

    @Override
    public void onReceiveRoomStatusChange(@PersonLiveBase.LiveStatus int status) {
        setLiveStatus(status);

        //直播结束信令
        if (status == PersonLiveBase.LIVE_STATUS_END) {
            onReceiveRoomEndStatusChange();
        }
    }

    public void onReceiveRoomEndStatusChange() {

    }

    @PersonLiveBase.LiveStatus
    public int getLiveStatus() {
        return liveStatus;
    }

    private void setLiveStatus(@PersonLiveBase.LiveStatus int status) {
        if (getHostData() != null) {
            getHostData().setLiveStatus(status);
            liveStatus = status;
        }
    }

    public Context getContext() {
        Context Context = super.getContext();
        if (null == Context) {
            Context = MainApplication.getMyApplicationContext();
        }
        return Context;
    }

    public void onNetworkChanged(boolean networkAvailable, boolean isWifi) {
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchNetworkChanged(networkAvailable, isWifi);
        }
    }

    public void onPhoneCallStateChanged(boolean isCalling) {
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchPhoneCallStateChanged(isCalling);
        }
    }

    public boolean isOfficialLive() {
        return this.officialRoomFlag;
    }

    /**
     * 是否是首映室
     * Note：业务不再维护，即将移除
     *
     * @return true 首映室 false 非首映室
     */
    @Deprecated
    public boolean isPremiere() {
        return this.premiereRoomFlag;
    }

    public boolean isScrollContainerHasAnimationPlaying() {
        return (getFragment() instanceof IRoomParentFragment) && ((IRoomParentFragment) getFragment()).hasSomeAnimationPlaying();
    }

    public void setOfficialRoomFlag(boolean officialRoomFlag) {
        boolean old = this.officialRoomFlag;
        this.officialRoomFlag = officialRoomFlag;
        if (old != this.officialRoomFlag) {
            //值变了
            if (this.officialRoomFlag) {
                officialLiveStart();
            } else {
                officialLiveEnd();
            }
        }
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchOfficialRoomFlag(officialRoomFlag);
        }
    }

    /**
     * Note：业务不再维护，即将移除
     *
     * @param premiereRoomFlag 是否首映室
     */
    @Deprecated
    public void setPremiereRoomFlag(boolean premiereRoomFlag) {
        this.premiereRoomFlag = premiereRoomFlag;
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchPremiereRoomFlag(premiereRoomFlag);
        }
    }

    /**
     * Note：业务不再维护，即将移除
     *
     * @param premiereInfo 首映室信息
     */
    @Deprecated
    public void updatePremiereInfo(PremiereInfo premiereInfo) {
        mPremiereInfo = premiereInfo;
        updatePremiere();
    }

    /**
     * Note：业务不再维护，即将移除
     */
    @Deprecated
    public void updatePremiere() {

    }

    /**
     * Note：业务不再维护，即将移除
     */
    @Deprecated
    public PremiereInfo getPremiereInfo() {
        return mPremiereInfo;
    }

    /**
     * Note：业务不再维护，即将移除
     */
    @Deprecated
    @PremiereStatus
    public int getPremiereStatus() {
        if (getPremiereInfo() != null) {
            return getPremiereInfo().getPremiereStatus();
        }
        return PremiereStatus.PREMIERE_NO;
    }

    /**
     * Note：业务不再维护，即将移除
     */
    @Deprecated
    public boolean isPremiereUI() {
        switch (getPremiereStatus()) {
            case PremiereStatus.PREMIERE_PRE:
            case PremiereStatus.PREMIERE_ING:
            case PremiereStatus.PREMIERE_END_LIVING:
                return true;
            default:
                return false;
        }
    }


    public void officialLiveStart() {

    }

    public void officialLiveEnd() {

    }

    public int getLiveMediaType() {
        return liveMediaType;
    }

    protected void setLiveMediaType(int mediaType) {
        liveMediaType = mediaType;
    }

    /**
     * 功能开关是否打开
     */
    protected boolean isFunctionSwitchOpen() {
        return !isFull() || getLiveMediaType() != LiveMediaType.TYPE_VIDEO;
    }

    public void startFragment(Fragment fragment) {
        if (getFragment() instanceof BaseFragment2) {
            ((BaseFragment2) getFragment()).startFragment(fragment);
        } else if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).startFragment(fragment);
        }
    }

    @Override
    public void resetData() {
        super.resetData();
        mPremiereInfo = null;
    }

    @Override
    public void resetView() {

    }

    /**
     * 当前用户是在主播端页面
     **/
    public boolean isFromHostFragment() {
        return getComponentHost().getHostContext().isFromHostFragment();
    }


    /**
     * 当前用户是否是主播身份 ，和 isAnchorVisitor 区分
     **/
    public boolean isAnchor() {
        return getComponentHost().getHostContext().isAnchor();
    }

    public void onPiaModeChanged(boolean piaModeOn) {
        this.piaModeOn = piaModeOn;
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchPiaModeChanged(piaModeOn);
        }
    }

    public boolean isPiaMode() {
        return piaModeOn;
    }

    @Override
    @SuppressWarnings("unchecked")
    public LiveRoomComponentHost<? extends LiveRoomComponentContext> getComponentHost() {
        return (LiveRoomComponentHost<LiveRoomComponentContext>) super.getComponentHost();
    }

    @Nullable
    public final IBaseLiveHostInteraction getHostInteraction() {
        try {
            return getComponentInteraction(IBaseLiveHostInteraction.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    protected void makeClickTrack(int metaId) {
        new XMTraceApi.Trace()
                .click(metaId)
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {

    }

    @Override
    public void onLogin(LoginInfoModelNew model) {

    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

    }

    protected boolean listenUserLoginStatus() {
        return false;
    }

    public LiveUserInfo getCurrentLoginUserInfo() {
        return mCurrentLoginUserInfo;
    }

    /**
     * @deprecated 信息不完善，UGC、课程直播、个播主播端使用会有问题
     * <p>
     * todo: 如果要用这个字段信息，还需要补充：UGC、课程直播的主播信息分发
     */
    public LiveUserInfo getCurrentAnchorUserInfo() {
        return mCurrentAnchorUserInfo;
    }

    public void setCurrentLoginUserInfo(LiveUserInfo currentLoginUserInfo) {
        if (mCurrentLoginUserInfo == currentLoginUserInfo) {
            return;
        }
        this.mCurrentLoginUserInfo = currentLoginUserInfo;
        onCurrentUserInfoChange(mCurrentLoginUserInfo);
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchCurrentUserInfoChange(currentLoginUserInfo);
        }
    }

    public void setCurrentAnchorUserInfo(LiveUserInfo anchorUserInfo) {
        if (mCurrentAnchorUserInfo == anchorUserInfo) {
            return;
        }
        this.mCurrentAnchorUserInfo = anchorUserInfo;
        onCurrentAnchorUserInfoChange(mCurrentAnchorUserInfo);
        if (getChildComponentHost() != null) {
            getChildComponentHost().dispatchCurrentAnchorInfoChange(anchorUserInfo);
        }
    }

    protected void onCurrentUserInfoChange(LiveUserInfo currentLoginUserInfo) {
        BizViewModel viewModel = getViewModel();
        if (viewModel instanceof ILiveBaseViewModel) {
            ((ILiveBaseViewModel<?, ?>) viewModel).onCurrentUserInfoChange(currentLoginUserInfo);
        }
    }

    protected void onCurrentAnchorUserInfoChange(LiveUserInfo currentAnchorUserInfo) {
    }

    public boolean isRoomChange(long roomId) {
        return roomId > 0 && getRoomId() != roomId;
    }

    protected boolean disableSomeDialogForSomeConditions() {
        return isPremiere() || isScrollContainerHasAnimationPlaying();
    }

    public @Nullable
    BaseVirtualRoom getRoomCore() {
        try {
            return getComponentInteraction(IBaseLiveHostInteraction.class).getRoomCore();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 优先使用异步方法 runAfterViewInflate(Runnable viewInflateCallback)
     * 同步使用本方法
     */
    @Override
    public void runAfterViewInflate() {
        super.runAfterViewInflate();
        if (ConstantsOpenSdk.isDebug && Looper.myLooper() != Looper.getMainLooper()) {
            LiveHelper.postDebugCrash("runAfterViewInflate 不能在子线程调用 " + Thread.currentThread().getName());
        }
    }

    @Override
    public void runAfterViewInflate(Runnable viewInflateCallback) {
        super.runAfterViewInflate(viewInflateCallback);
        if (ConstantsOpenSdk.isDebug && Looper.myLooper() != Looper.getMainLooper()) {
            LiveHelper.postDebugCrash("runAfterViewInflate 不能在子线程调用 " + Thread.currentThread().getName());
        }
    }

    @Nullable
    @Override
    public ILiveRoomCompHost<?> getChildComponentHost() {
        return (ILiveRoomCompHost<?>) super.getChildComponentHost();
    }
}
