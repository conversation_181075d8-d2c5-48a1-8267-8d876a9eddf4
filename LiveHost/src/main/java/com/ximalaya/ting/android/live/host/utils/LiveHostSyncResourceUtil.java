package com.ximalaya.ting.android.live.host.utils;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.im.xchat.util.UnBoxUtil;
import com.ximalaya.ting.android.live.biz.manager.LiveMicEmotionManager;
import com.ximalaya.ting.android.live.common.input.manager.LiveCommonEmojiManager;
import com.ximalaya.ting.android.live.common.lib.LiveTemplateManager;
import com.ximalaya.ting.android.live.common.lib.icons.LiveIconsManager;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;

import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 直播资源同步工具类。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @since 2020-02-05
 */
public class LiveHostSyncResourceUtil {

    private static final String TAG = "LiveHostSyncResourceUtil";

    /**
     * 进入直播间，10分钟同步一次
     */
    private static final long THRESHOLD_ROOM_10_MINUTE = 10 * 60 * 1000;

    /**
     * 记录不同 bizType 对应的上次同步资源时间
     */
    private static final ConcurrentHashMap<Integer, Long> sLastSyncResourceBizType2TimestampMap = new ConcurrentHashMap<>();

    /**
     * 记录当前的业务类型
     */
    private static int mBizType = -1;

    /**
     * 进入直播间，10分钟同步一次
     */
    public static void syncResource(
            final long roomId,
            final @BaseScrollConstant.LiveRoomBizType int bizType
    ) {
        long currentTimestamp = System.currentTimeMillis();
        // 1、当业务类型上下文切换时，开始同步模版资源；
        // 2、或者同业务类型，当前时间 - 上次同步时间 >= 10分钟时，开始同步模版资源
        long lastBizType2Timestamp = UnBoxUtil.unBoxValueSafely(sLastSyncResourceBizType2TimestampMap.get(bizType));
        if (mBizType != bizType || currentTimestamp - lastBizType2Timestamp >= THRESHOLD_ROOM_10_MINUTE) {
            MyAsyncTask.execute(new Runnable() {
                @Override
                public void run() {
                    if (bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE) {
                        // 初始化全局图标，课程直播目前只包括财富等级图标、粉丝团图标
                        LiveIconsManager.getInstance().preloadIconsForCourseLive(MainApplication.getMyApplicationContext());
                        // 查询模版信息，不下载模版资源包，按需下载礼物资源包
                        LiveTemplateManager.getInstance().fetchTemplateInfo(bizType);
                    } else {
                        // 初始化全局图标，目前包括财富等级图标、荣耀勋章图标、粉丝团图标、PK 排位等级图片
                        LiveIconsManager.getInstance().preloadIcons(MainApplication.getMyApplicationContext(), roomId);
                        // 预加载模版，并在Wi-Fi条件下下载模版资源包
                        LiveTemplateManager.getInstance().fetchTemplateInfoAndDownloadSpecificResourceInWifi(bizType);
                        // 预加载麦上表情
                        LiveMicEmotionManager.getInstance().loadMicEmotion();
                    }
                    // 加载全量礼物文件信息
                    LiveTemplateManager.getInstance().preloadGiftInfoByCndFile(bizType);
                }
            });
            sLastSyncResourceBizType2TimestampMap.put(bizType, currentTimestamp);
            mBizType = bizType;
        }
    }

    /**
     * 记录不同 bizType 对应的上次同步资源时间
     */
    private static final ConcurrentHashMap<Integer, Long> sLastSyncEmojiBizType2TsMap = new ConcurrentHashMap<>();
    /**
     * 记录当前的房间id
     */
    private static long sEmojiLastRoomId = -1;
    /**
     * 记录当前的业务类型
     */
    private static int sEmojiLastBizId = -1;

    /**
     * 进入直播间，10分钟同步一次
     * 注意：课程直播不支持表情包，不需要请求
     */
    public static void syncEmojiResource(
            final long roomId,
            final long hostUid,
            final @BaseScrollConstant.LiveRoomBizType int bizType) {
        if (bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE) {
            return;
        }

        long currentTimestamp = System.currentTimeMillis();
        // 1、当房间切换时，开始同步模版资源；
        // 2、当业务类型上下文切换时，开始同步模版资源；
        // 3、或者同业务类型，当前时间 - 上次同步时间 >= 10分钟时，开始同步模版资源
        long lastBizType2Ts = UnBoxUtil.unBoxValueSafely(sLastSyncEmojiBizType2TsMap.get(bizType));
        if (roomId != sEmojiLastRoomId || bizType != sEmojiLastBizId
                || currentTimestamp - lastBizType2Ts >= THRESHOLD_ROOM_10_MINUTE) {
            MyAsyncTask.execute(new Runnable() {
                @Override
                public void run() {
                    // 预加载表情包
                    LiveCommonEmojiManager.getInstance().loadData(roomId, hostUid, bizType);
                }
            });
            sLastSyncEmojiBizType2TsMap.put(bizType, currentTimestamp);
            sEmojiLastRoomId = roomId;
            sEmojiLastBizId = bizType;
        }
    }


    /**
     * 随机数，单位毫秒
     * @return 随机数，单位毫秒
     */
    public static long getRandomMillis() {
        return (new Random().nextInt(3) + 2) * 1000L;
    }
}
