package com.ximalaya.ting.android.live.host.components;

import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.live.biz.mode.data.PrivateChatViewModel;
import com.ximalaya.ting.android.live.common.chatlist.view.gradient.GradientScene;
import com.ximalaya.ting.android.live.common.input.emoji.IEmojiItem;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.lifecycle.IComponentHostInteraction;

/**
 * 组件基础交互接口，
 *
 * <AUTHOR>
 */
public interface IBaseLiveHostInteraction extends IComponentHostInteraction {

    void showUserInfoPop(long uid);

    default void showUserInfoPop(final long targetId, boolean fromPk, long roomId, long matchedHostId) {}

    int getGiftPanelHeight();

    void listScrollToBottom();

    void doTopGradualEffect(@GradientScene int scene, int targetHeight);

    void restoreTopGradualEffect(@GradientScene int scene);

    void sendMessage(String content);

    void sendImgMsg(String imgUri);

    void sendEmojiMsg(IEmojiItem emojiItem);

    void showGiftPanel();

    void showGiftPanel(long targetUid);

    int getLiveMediaType();

    @BaseScrollConstant.LiveRoomBizType
    int getRoomBizType();

    int getPlaySource();

    boolean hasDialogShowing();

    boolean isKeyboardShowing();

    boolean isPiaPanelExpanded();

    boolean isFollowCurrentAnchorOrPresident();

    /**
     * @return 是否拦截所有聊天 C 区域引导展示，为 true 时，触发引导展示将不会插入队列中
     */
    boolean interceptAllChatListGuide();

    boolean isExchangeGuideAnimationShowing();

    void followCurrentAnchorOrPresident(int sceneType);

    void followUser(long targetUid, int sceneType, @Nullable Runnable doAfterFollowedSuccess);

    /**
     * @return 房间礼物信息 LiveData
     */
    @Nullable
    LiveData<GiftInfoCombine> getGiftInfoLiveData();

    View getRootView();

    PrivateChatViewModel getPrivateChatViewModel();

    BaseVirtualRoom getRoomCore();

    boolean isRoomMiniSupport();

    void  handleRequestMinimizeRoom();

    void  finishFragment();

    void requestPlayMode(int playMode);
}
