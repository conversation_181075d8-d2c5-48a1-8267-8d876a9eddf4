package com.ximalaya.ting.android.framework.player;

import android.content.Context;

import com.ximalaya.ting.android.downloadservice.base.IDownloadService;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmCommonBusinessHandle;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

import androidx.annotation.NonNull;


/**
 * 主进程播放监听服务
 *
 * <AUTHOR>
 */
public class LocalMediaService implements
        IXmPlayerStatusListener, IXmCommonBusinessHandle {

    protected XmPlayerManager mPlayerManager;
    protected Context mContext;
    public static String TAG = "LocalMediaService";

    public void init(@NonNull Context context,@NonNull XmPlayerManager playerManager){
        mContext = context.getApplicationContext();
        mPlayerManager = playerManager;

        mPlayerManager.addPlayerStatusListener(this);
        mPlayerManager.setCommonBusinessHandle(this);
    }

    @Override
    public void onPlayStart() {

    }

    @Override
    public void onPlayPause() {
    }

    @Override
    public void onPlayStop() {
    }

    @Override
    public void onSoundPlayComplete() {

    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {
    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return true;
    }


    @Override
    public String getDownloadPlayPath(Track track)  {
        return RouterServiceManager.getInstance().getService(IDownloadService.class).getDownloadSavePath(track);
    }

    @Override
    public void isOldTrackDownload(final Track track) {

    }

    @Override
    public void closeApp() {
    }

    @Override
    public boolean userIsVip() {
        return false;
    }

    @Override
    public boolean isUseNewPlayFragment() {
        return ConstantsOpenSdk.DEFALUT_USE_NEW_PLAY_FRA;
    }

    @Override
    public boolean isPlayFragmentForeground() {
        return false;
    }

    @Override
    public boolean lockScreenActivityIsShowing() {
        return false;
    }

    @Override
    public long updateGDTRTBToken() {
        return 0;
    }

    @Override
    public long updateGDTRTBSdkInfo(String dspId) {
        return 0;
    }

    @Override
    public long updateCSJRTBToken(int thirdAdType, String dspId, int adType) {
        return 0;
    }

    @Override
    public void batchAdRecord(List thirdAdList, List adReportModel) {

    }

    @Override
    public String queryConfigCenter(int queryType, String groupName, String itemName) {
        return null;
    }

    @Override
    public String syncBatchGetToken(List adRtbModelList, int thirdAdType) {
        return null;
    }

    @Override
    public String syncBatchGetGdtSdkInfo(List adRtbModelList) {
        return null;
    }

    @Override
    public void callForTraceMarkPoint(int metaId, String paramJson) {

    }

    @Override
    public void callForShowUniversalPaymentActionDialog(Track track) {

    }

    @Override
    public void notifyFreeListenTimeOut() {

    }

    @Override
    public boolean isPlayingFreeListenAd() {
        return false;
    }

    @Override
    public String getNewTicket(int type) {
        return null;
    }

    @Override
    public void setThirdUidAndAppKey(String thirdUid, String appKey) {

    }

    @Override
    public void completePlay() {

    }

    /**
     * 应用退出的时候调用
     */
    protected PlayableModel mCurModel;

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

        if(mContext==null){
            Logger.d("TAG","LocalMediaService shall init first");
            return;
        }
        saveModel(curModel);
        mCurModel = curModel;
    }

    // 保存历史
    private void saveModel(PlayableModel playableModel) {

        if(mContext==null){
            Logger.d("TAG","LocalMediaService shall init first");
            return;
        }

        if (playableModel == null) {
            return;
        }
        String playKind = playableModel.getKind();
        if (PlayableModel.KIND_TRACK.equalsIgnoreCase(playKind)) {
            //2016-12-28，活动直播的kind也是track但是所以可能会保存但服务端不会记录，故排除
            if(((Track)playableModel).getAlbum()==null){
                return;
            }
//            mContext.getContentResolver().notifyChange(DataContentObserver.getUriByType(DataContentObserver.TypeListenerChange), null);
        }
    }
}
