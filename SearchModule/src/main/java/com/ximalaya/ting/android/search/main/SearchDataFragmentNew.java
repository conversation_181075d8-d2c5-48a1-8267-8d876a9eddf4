package com.ximalaya.ting.android.search.main;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.chatxmly.util.AIAgentSearchUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager;
import com.ximalaya.ting.android.host.manager.UIConsistencyManager;
import com.ximalaya.ting.android.host.manager.ai.AiForceLoginManager;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.search.SearchManager;
import com.ximalaya.ting.android.host.model.search.SearchHotWord;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.ui.DrawableUtil;
import com.ximalaya.ting.android.host.view.StickyNavLayout;
import com.ximalaya.ting.android.host.view.other.MyViewPager;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.search.R;
import com.ximalaya.ting.android.search.SearchConstants;
import com.ximalaya.ting.android.search.ad.SearchBrandAdBgManager;
import com.ximalaya.ting.android.search.adapter.SearchTabCommonAdapter;
import com.ximalaya.ting.android.search.base.BaseSearchFragment;
import com.ximalaya.ting.android.search.base.ISearchContext;
import com.ximalaya.ting.android.search.base.ISearchDataContext;
import com.ximalaya.ting.android.search.manager.SearchImmersiveBannerManager;
import com.ximalaya.ting.android.search.manager.SearchLabelManager;
import com.ximalaya.ting.android.search.manager.SearchWidgetGuideManager;
import com.ximalaya.ting.android.search.model.MainLabel;
import com.ximalaya.ting.android.search.model.SearchLabel;
import com.ximalaya.ting.android.search.model.SearchRiskTips;
import com.ximalaya.ting.android.search.model.SearchType;
import com.ximalaya.ting.android.search.page.AiSearchTabFragment;
import com.ximalaya.ting.android.search.page.SearchAiTabFragment;
import com.ximalaya.ting.android.search.page.sub.SearchChosenFragment;
import com.ximalaya.ting.android.search.request.SearchCommonRequest;
import com.ximalaya.ting.android.search.utils.SearchTraceUtils;
import com.ximalaya.ting.android.search.utils.SearchUiUtils;
import com.ximalaya.ting.android.search.utils.SearchUtils;
import com.ximalaya.ting.android.search.wrap.SearchDataContextWrapper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by ervin.li on 2018/10/30.
 * 新版本搜索结果页
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchDataFragmentNew extends BaseFragment2 implements ISearchDataContext, ViewPager.PageTransformer {
    private static final String TAG = SearchDataFragmentNew.class.getSimpleName();
    private ViewGroup mHeadView;
    private View mTopLineView;

    private StickyNavLayout mStickyNavLayout;
    private PagerSlidingTabStrip mIndicator;
    private MyViewPager mViewPager;
    private ISearchContext mSearchContext;
    private SearchTabCommonAdapter mPagerAdapter;
    private View mVImmersiveBannerMask;
    private ImageView mIvImmersiveBannerBg;
    private ViewGroup mVgImmersiveBannerBg;
    private View mVImmersiveBannerSolid;
    private View mSearchVMaskBg;

    private int mType;
    private int mCategoryId;
    private String mStrategy;
    private String mKeyWord;
    private boolean mSpellCheck = true;
    ;
    private String mSubTab;
    private String mFocusTab;
    private boolean isFirstUpdateTopView = true;
    private TextView mHeadRiskTips;
    private List<SearchType> mSearchTypes;
    private int mLastPosition;
    private int mImmersiveBannerBgColor = Color.TRANSPARENT;
    private int mPagerScrollingPosition = -1;

    private boolean enableAiHidePlayBtn = false;

    private ImageView wenAiIconIv;
    private ImageView wenAiShadow;

    private String requestId;

    @Override
    protected String getPageLogicName() {
        return SearchDataFragmentNew.class.getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        if (getActivity() != null && getActivity().getWindow() != null) {
            getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                    | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        }
        enableAiHidePlayBtn = ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME, "chatxml_ai_search_switch_hidden_tabBar", false)
                && ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "key_chatxml_ai_search_switch", true);
        parseArgs(getArguments());
        uiInit();
        listenerInit();
        Logger.d("lhg", "data fragment initUi");
        SearchWidgetGuideManager.getInstance().updateSearchResultCount(false);
        SearchImmersiveBannerManager.INSTANCE.addOnColorChangeListener(mOnColorChangeListener);
        SearchImmersiveBannerManager.INSTANCE.addOnBannerImageListener(mBannerImageListener);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SearchImmersiveBannerManager.INSTANCE.removeOnColorChangeListener(mOnColorChangeListener);
        SearchImmersiveBannerManager.INSTANCE.removeOnBannerImageListener(mBannerImageListener);
    }

    private void hideSoftInput() {
        if (mSearchContext != null) {
            mSearchContext.showSoftInput(false);
        } else if (mActivity != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (inputMethodManager != null && inputMethodManager.isActive()) {
                inputMethodManager.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
    }

    public String getKeyWord() {
        return mKeyWord;
    }

    private void parseArgs(Bundle args) {
        if (args != null) {
            mKeyWord = args.getString(SearchConstants.SEARCH_KEYWORD);
            mType = args.getInt(SearchConstants.SEARCH_TYPE);
            mSubTab = args.getString(SearchConstants.SEARCH_DATA_SUB_TAB);
            mStrategy = args.getString(SearchConstants.KEY_STRATEGY);
            mFocusTab = args.getString(SearchConstants.KEY_FOCUS_TAB);
            mCategoryId = args.getInt(BundleKeyConstants.KEY_CATEGORY_ID, SearchConstants.NO_CATEGORY_ID);
            mSpellCheck = args.getBoolean(SearchConstants.KEY_SPELL_CHECK, true);
        }
    }

    private void uiInit() {
        mHeadView = findViewById(R.id.host_id_stickynavlayout_topview);
        mStickyNavLayout = findViewById(R.id.search_search_result_sticky_nav);
        mIndicator = findViewById(R.id.search_id_stickynavlayout_indicator_tab);
        View mIndicatorLayout = findViewById(R.id.host_id_stickynavlayout_indicator);
        if (SearchUiUtils.isNewNoDividerUi() && mIndicatorLayout != null && mIndicatorLayout.getLayoutParams() != null) {
            mIndicatorLayout.getLayoutParams().height = BaseUtil.dp2px(mContext, 32);
            mIndicator.setUnderlineHeight(0);
        }
        //mIndicator.setTextSize(16);
        mIndicator.setDisallowInterceptTouchEventView(mSearchContext != null ? mSearchContext.getSlideView() : null);
        UIConsistencyManager.getInstance().setTabUIConsistency(getContext(), mIndicator, true, true, false);
        mViewPager = findViewById(R.id.host_id_stickynavlayout_content);
        mViewPager.setPageTransformer(false, this);
        mTopLineView = findViewById(R.id.host_id_stickynavlayout_topview_line);
        mTopLineView.setVisibility(View.INVISIBLE);
        mVImmersiveBannerMask = findViewById(R.id.search_v_immersive_banner_mask);
        mIvImmersiveBannerBg = findViewById(R.id.search_iv_immersive_banner_bg);
        mVgImmersiveBannerBg = findViewById(R.id.search_vg_immersive_banner_bg);
        mVImmersiveBannerSolid = findViewById(R.id.search_v_immersive_banner_bg_solid);
        mSearchVMaskBg = findViewById(R.id.search_v_mask_bg);
        mVgImmersiveBannerBg.post(() -> {
            int[] location = new int[2];
            mViewPager.getLocationOnScreen(location);
            mIvImmersiveBannerBg.getLayoutParams().height = SearchImmersiveBannerManager.INSTANCE.coverHeight(mContext);
            mVImmersiveBannerSolid.getLayoutParams().height = SearchImmersiveBannerManager.INSTANCE.statusBarHeight(mContext);
            ViewGroup.LayoutParams layoutParams = mVgImmersiveBannerBg.getLayoutParams();
            if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = -location[1];
                mVgImmersiveBannerBg.setLayoutParams(layoutParams);
            }
        });
        wenAiIconIv = findViewById(R.id.search_iv_wen_ai);
        wenAiShadow = findViewById(R.id.search_shadow_wen_ai);
        wenAiIconIv.setOnClickListener(v -> {
            // 触发RN卡片的AI图标点击事件
            triggerRNCommonCardAiIconClick();
            // 原有的AI聊天逻辑
            HashMap<String, String> map = new HashMap<>();
            map.put("orgSug", mKeyWord);
            ChildXmlyTipManager.INSTANCE.goChildChatXmlyWithParams("searchResultAi", map);
        });
    }

    private void setIndicatorPadding(int tabNum) {
        if (mIndicator != null) {
            int padding;
            if (tabNum <= 4) {
                padding = 28;
            } else if (tabNum == 5) {
                padding = 20;
            } else {
                padding = 15;
            }
            mIndicator.setTabPaddingLeftRight(BaseUtil.dp2px(mContext, padding));
        }
    }

    private boolean mHasRegisterTopColorChangeReceiver = false;

    private void unregisterTopColorChangeReceiver() {
        if (mHasRegisterTopColorChangeReceiver) {
            mHasRegisterTopColorChangeReceiver = false;
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mTopColorChangeReceiver);
        }
    }

    private void registerTopColorChangeReceiver() {
        if (mHasRegisterTopColorChangeReceiver) {
            return;
        }
        mHasRegisterTopColorChangeReceiver = true;
        IntentFilter filter = new IntentFilter();
        filter.addAction(SearchBrandAdBgManager.ACTION_AD_CHANGE_COLOR);
        filter.addAction(SearchBrandAdBgManager.ACTION_REMOVE_AD_CHANGE_COLOR);
        filter.addAction(SearchBrandAdBgManager.ACTION_TAB_SCROLL_CHANGE_COLOR);
        filter.addAction(SearchBrandAdBgManager.ACTION_LIST_SCROLL_CHANGE_COLOR);
        filter.addAction(SearchBrandAdBgManager.ACTION_FILTER_BTN_CHANGE_COLOR);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mTopColorChangeReceiver, filter);
    }

    private final BroadcastReceiver mTopColorChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // SearchBrandAdBgManager.printLog("SearchDataFragmentNew onReceive");
            if (intent != null) {
                int mainColor = intent.getIntExtra("color", Color.TRANSPARENT);
                float offset = intent.getFloatExtra("offset", 1);
                if (mainColor == Color.TRANSPARENT || offset <= 0.1f) {
                    mTopLineView.setBackgroundColor(getResources().getColor(R.color.host_color_lineColor2_black));
                    mIndicator.setActivateTextColor(getResources().getColor(R.color.search_color_111111_cfcfcf));
                    mIndicator.setDeactivateTextColor(getResources().getColor(R.color.search_color_666666_cfcfcf));
                    mIndicator.setIndicatorColorResource(R.color.search_color_F86442);
                } else {
                    int dividerColor = ColorUtil.overlayColors(mainColor,
                            getResources().getColor(R.color.host_color_lineColor2_black), 1 - offset);
                    mTopLineView.setBackgroundColor(dividerColor);
                    int activateColor = ColorUtil.overlayColors(Color.WHITE,
                            getResources().getColor(R.color.search_color_111111_cfcfcf), 1 - offset);
                    mIndicator.setActivateTextColor(activateColor);
                    int deactivateColor = ColorUtil.overlayColors(ColorUtil.changeColorAlpha01(Color.WHITE, 0.6f),
                            getResources().getColor(R.color.search_color_666666_cfcfcf), 1 - offset);
                    // offset在 0-1 范围对应 deactivateColor 透明度 1-0.6
                    mIndicator.setDeactivateTextColor(ColorUtil.changeColorAlpha01(deactivateColor, 0.6f + (1 - offset) * 0.4f));
                    int indicatorColor = ColorUtil.overlayColors(Color.WHITE,
                            getResources().getColor(R.color.search_color_F86442), 1 - offset);
                    mIndicator.setIndicatorColor(indicatorColor);
                }
            }
        }
    };

    //是否更改过沉浸式图片的位置
    private boolean isUpdateImmersiveBannerParams = false;
    private final SearchImmersiveBannerManager.IBannerImageListener mBannerImageListener = (bitmap, mainColor) -> {
        if (!canUpdateUi()) {
            return;
        }
//        Logger.i(TAG, "mBannerImageListener: bitmap = " + bitmap);
        if (mIvImmersiveBannerBg != null) {
            mIvImmersiveBannerBg.setImageBitmap(bitmap);
            if (mainColor != -1 && !isUpdateImmersiveBannerParams) {
                changeImmersiveBannerParams(mainColor);
            } else if (mainColor == -1 && isUpdateImmersiveBannerParams) {
                resetImmersiveBannerParams();
            }
        }
    };

    private void changeImmersiveBannerParams(int mainColor) {
        if (mIvImmersiveBannerBg == null || mVImmersiveBannerSolid == null || mSearchVMaskBg == null) {
            return;
        }
        isUpdateImmersiveBannerParams = true;
        mIvImmersiveBannerBg.setScaleType(ImageView.ScaleType.FIT_XY);
        ViewGroup.LayoutParams layoutParams = mIvImmersiveBannerBg.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            layoutParams.height = BaseUtil.getScreenWidth(mContext) * 9 / 16;
            mIvImmersiveBannerBg.setLayoutParams(layoutParams);

            int height = mVImmersiveBannerSolid.getLayoutParams().height;
            mVImmersiveBannerSolid.getLayoutParams().height = height + BaseUtil.dp2px(mContext, 66f);

            mSearchVMaskBg.setVisibility(View.VISIBLE);
            int color =
                    ColorUtil.covertColorToFixedSaturationAndLightness(
                            mainColor, 0.50f, 0.35f);
            //半屏蒙层
            Drawable mMaskDrawable = new DrawableUtil.GradientDrawableBuilder()
                    .color(new int[]{ColorUtil.changeColorAlpha(color, 0), color})
                    .orientation(GradientDrawable.Orientation.BOTTOM_TOP)
                    .build();
            mSearchVMaskBg.setBackground(mMaskDrawable);
        }
    }

    private void resetImmersiveBannerParams() {
        if (mIvImmersiveBannerBg == null || mVImmersiveBannerSolid == null || mSearchVMaskBg == null) {
            return;
        }

        mSearchVMaskBg.setVisibility(View.GONE);
        ViewGroup.LayoutParams layoutParams = mIvImmersiveBannerBg.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            layoutParams.height = SearchImmersiveBannerManager.INSTANCE.coverHeight(mContext);
            mIvImmersiveBannerBg.setLayoutParams(layoutParams);
            mIvImmersiveBannerBg.setScaleType(ImageView.ScaleType.CENTER_CROP);
            mVImmersiveBannerSolid.getLayoutParams().height = SearchImmersiveBannerManager.INSTANCE.statusBarHeight(mContext);
        }

    }

    private final SearchImmersiveBannerManager.IOnColorChangeListener mOnColorChangeListener = new SearchImmersiveBannerManager.IOnColorChangeListener() {
        @Override
        public void onColorChange(int mainColor, float tabScrollOffset, float listScrollOffset, int listScrollY) {
            if (!canUpdateUi()) {
                return;
            }
            if (isChoseTabFragment(mViewPager.getCurrentItem()) && SearchImmersiveBannerManager.INSTANCE.isInImmersiveMode()) {
                wenAiIconIv.setImageResource(listScrollOffset > 0.9f ? R.drawable.search_ic_wen_ai : R.drawable.search_ic_wen_ai_white);
            }
            float offset = tabScrollOffset * (1 - listScrollOffset);
            Logger.i(TAG, "onColorChange: mainColor = " + mainColor + " tabScrollOffset " + tabScrollOffset + " listScrollOffset = " + listScrollOffset);
            if (mainColor == Color.TRANSPARENT || tabScrollOffset <= 0.1f) {
                if (mVImmersiveBannerMask != null && mVImmersiveBannerMask.getVisibility() == View.VISIBLE) {
                    mVImmersiveBannerMask.setVisibility(View.INVISIBLE);
                }
            } else {
                if (mVImmersiveBannerMask != null) {
                    mVImmersiveBannerMask.setVisibility(View.VISIBLE);
                    mVImmersiveBannerMask.setAlpha(listScrollOffset);
                }
            }
            if (mVgImmersiveBannerBg != null && mIvImmersiveBannerBg != null && mVImmersiveBannerSolid != null) {
                if (mainColor == Color.TRANSPARENT || listScrollOffset >= 1) {
                    if (mVgImmersiveBannerBg.getVisibility() == View.VISIBLE) {
                        mVgImmersiveBannerBg.setVisibility(View.INVISIBLE);
                    }
                } else {
                    if (mVgImmersiveBannerBg.getVisibility() != View.VISIBLE) {
                        mVgImmersiveBannerBg.setVisibility(View.VISIBLE);
                    }
                    if (mImmersiveBannerBgColor != mainColor) {
                        mImmersiveBannerBgColor = mainColor;
                        mVImmersiveBannerSolid.setBackgroundColor(mainColor);
                    }
                    if ((mPagerScrollingPosition == 0 || mViewPager.getCurrentItem() == 0) && mPagerAdapter != null &&
                            mPagerAdapter.getCount() > 0 && isAiTabFragment(0)) {
                        mVgImmersiveBannerBg.setTranslationX(-BaseUtil.getScreenWidth(mContext) * (tabScrollOffset - 1));
                    } else {
                        mVgImmersiveBannerBg.setTranslationX(BaseUtil.getScreenWidth(mContext) * (tabScrollOffset - 1));
                    }
                    mVgImmersiveBannerBg.setTranslationY(-listScrollY);
                }
            }

            if (mainColor == Color.TRANSPARENT || offset <= 0.1f) {
                mTopLineView.setVisibility(View.VISIBLE);
                mIndicator.setActivateTextColor(getResources().getColor(R.color.search_color_111111_cfcfcf));
                mIndicator.setDeactivateTextColor(getResources().getColor(R.color.search_color_666666_cfcfcf));
                mIndicator.setIndicatorColorResource(R.color.search_color_F86442);
            } else {
                mTopLineView.setVisibility(View.INVISIBLE);
                int activateColor = ColorUtil.overlayColors(Color.WHITE,
                        getResources().getColor(R.color.search_color_111111_cfcfcf), 1 - offset);
                mIndicator.setActivateTextColor(activateColor);
                int deactivateColor = ColorUtil.overlayColors(ColorUtil.changeColorAlpha01(Color.WHITE, 0.6f),
                        getResources().getColor(R.color.search_color_666666_cfcfcf), 1 - offset);
                // offset在 0-1 范围对应 deactivateColor 透明度 1-0.6
                mIndicator.setDeactivateTextColor(ColorUtil.changeColorAlpha01(deactivateColor, 0.6f + (1 - offset) * 0.4f));
                int indicatorColor = ColorUtil.overlayColors(Color.WHITE,
                        getResources().getColor(R.color.search_color_F86442), 1 - offset);
                mIndicator.setIndicatorColor(indicatorColor);
            }
        }
    };

    @Override
    public void onResume() {
        super.onResume();
        UserTrackCookie.getInstance().setXmContent(SearchTraceUtils.getSearchType(mType), "search", mKeyWord);
        traceEnterPageForAccessibilityMode();
        registerTopColorChangeReceiver();
        checkHideAiPlayBtn();
        if (wenAiIconIv.getVisibility() == View.VISIBLE) {
            AIAgentSearchUtil.INSTANCE.eTraceIconShow(mKeyWord, requestId);
        }
    }

    @Override
    public void onPause() {
        Fragment fragment = null;
        if (mPagerAdapter != null) {
            fragment = mPagerAdapter.getFragmentAtPosition(mLastPosition);
        }
        if (fragment instanceof BaseSearchFragment) {
            ((BaseSearchFragment) fragment).dismissSortDialog();
        }
        super.onPause();
        traceExitPageForAccessibilityMode();
        unregisterTopColorChangeReceiver();
    }

    private void listenerInit() {
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                mPagerScrollingPosition = position;
                if (mSearchContext != null) {
                    if (position == 0) {
                        mSearchContext.setSlide(positionOffset >= 0);
                    } else {
                        mSearchContext.setSlide(false);
                    }
                }
                // position 在0-1之间滑动才需要改变颜色
                if (mPagerAdapter != null && mPagerAdapter.getCount() > 0 && (isAiTabFragment(0))) {
                    if (position <= 2) {
                        // position = 0   直接用positionOffset = 0.99722224
                        // position = 1  && positionOffset == 0     offset = 1.0
                        // position = 1  && positionOffset != 0     offset = 1 - positionOffset
                        // position = 2 offset = 0
                        float offset = 0;
                        if (position == 0) {
                            offset = positionOffset;
                        } else if (position == 1 && positionOffset == 0) {
                            offset = 1;
                        } else if (position == 1) {
                            offset = 1 - positionOffset;
                        } else if (position == 2) {
                            offset = 0;
                        }
//                        Logger.i("ZZB1", "1 onPageScrolled position = " + position
//                                + ", positionOffset = " + positionOffset + ", offset = " + offset);
                        SearchBrandAdBgManager.INSTANCE.sendTabScrollColorChangeBroad(offset, getContext(), getChoseFragmentImmersiveFrom());
                        SearchImmersiveBannerManager.INSTANCE.onTabScrolled(offset);
                    }
                } else {
                    if (position < 1 || (position == 1 && positionOffset == 0)) {
                        float offset = 1 - (position > 0 ? 1 : positionOffset);
//                        Logger.i("ZZB1", "2 onPageScrolled position = " + position
//                                + ", positionOffset = " + positionOffset + ", offset = " + offset);
                        SearchBrandAdBgManager.INSTANCE.sendTabScrollColorChangeBroad(offset, getContext(), getChoseFragmentImmersiveFrom());
                        SearchImmersiveBannerManager.INSTANCE.onTabScrolled(offset);
                    }
                }
            }

            @Override
            public void onPageSelected(int position) {
                // Logger.i("ZZB1", "3 onPageSelected position = " + position);
                if (position > 1) { // 第三个tab开始重置沉浸式背景
                    SearchBrandAdBgManager.INSTANCE.sendTabScrollColorChangeBroad(0, getContext(), getChoseFragmentImmersiveFrom());
                    SearchImmersiveBannerManager.INSTANCE.onTabScrolled(0);
                }
                // 先将上个页面的排序筛选弹窗收起
                if (mLastPosition != position) {
                    Fragment fragment = mPagerAdapter.getFragmentAtPosition(mLastPosition);
                    if (fragment instanceof BaseSearchFragment) {
                        ((BaseSearchFragment) fragment).dismissSortDialog();
                    }
                }
                // 更新当前position下筛选按钮状态，设置为未选中状态
                mLastPosition = position;
                hideSoftInput();
                if (ToolUtil.isEmptyCollects(mSearchTypes) || mSearchTypes.size() < position)
                    return;
                SearchType searchType = mSearchTypes.get(position);
                if (searchType != null) {
                    String core = searchType.getTypeValue();
                    SearchTraceUtils.traceWithSearchInfo("searchResult", "tab", "button", SearchTraceUtils.getTraceItemId(core), null, XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_SEARCH_PAGE_CLICK);
                }
                if (mStickyNavLayout != null) {
                    mStickyNavLayout.resetCurrentPageStatus();
                    // 其他非RN tab还原
                    mStickyNavLayout.disableNullDeal(false);
                    if (isAiTabFragment(position)) {
                        // RN类型Ai搜禁用手势拦截
                        mStickyNavLayout.disableNullDeal(true);
                    }
                }
                checkHideAiPlayBtn();
                checkShowImmersiveAiIcon();
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    private void checkHideAiPlayBtn() {
        if (mViewPager == null || mPagerAdapter == null) {
            return;
        }
        if (!enableAiHidePlayBtn) {
            return;
        }
        if (isAiTabFragment(mViewPager.getCurrentItem())) {
            hidePlayButton();
        } else {
            showPlayButton();
        }
    }

    private boolean isAiTabFragment(int position) {
        if (mPagerAdapter == null) {
            return false;
        }
        Fragment fragment = mPagerAdapter.getFragmentAtPosition(position);
        SearchType searchType = mSearchTypes.get(position);
        boolean isAiType = searchType != null && searchType.getTypeValue().equals(SearchConstants.CORE_AI_SEARCH);
        boolean isAiFragment = fragment instanceof SearchAiTabFragment || fragment instanceof AiSearchTabFragment;
        return isAiType && isAiFragment;
    }

    private boolean isChoseTabFragment(int position) {
        if (mPagerAdapter == null) {
            return false;
        }
        Fragment fragment = mPagerAdapter.getFragmentAtPosition(position);
        SearchType searchType = mSearchTypes.get(position);
        boolean isChoseType = searchType != null && searchType.getTypeValue().equals(SearchConstants.CORE_CHOSEN);
        boolean isChoseFragment = fragment instanceof SearchChosenFragment;
        return isChoseType && isChoseFragment;
    }

    @Override
    protected void loadData() {
        loadSearchTab(mKeyWord);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.search_fra_search_result_new;
    }

    private void loadSearchTab(String kw) {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        requestId = XmRequestIdManager.getInstance(mContext).getRequestId();

        HashMap<String, Object> params = new HashMap<>();
        try {
            params.put("kw", URLEncoder.encode(kw, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        params.put("plan", "c");
        params.put("spellchecker", mSpellCheck + "");
        params.put("search_version", SearchActionRouter.SEARCH_VERSION);
        params.put("core", "all");
        params.put("page", "1");
        params.put("paidFilter", "false");
        params.put("live", "true");
        params.put("rows", "3");
        params.put("categoryId", String.valueOf(-1));
        params.put("condition", "relation");
        params.put("needSemantic", "true");
        params.put("voiceAsinput", "false");
        params.put("newDevice", SearchManager.isNewDevice + "");
        params.put("recall", "tab");
        if (!TextUtils.isEmpty(mFocusTab)) {
            HashMap<String, Object> extraParam = new HashMap<>();
            extraParam.put("forceTab", mFocusTab);
            params.put("extraParam", extraParam);
        }
        SearchCommonRequest.getSearchCoreList(params, new IDataCallBack<List<SearchType>>() {
            @Override
            public void onSuccess(@Nullable List<SearchType> searchTypeList) {
                if (!canUpdateUi()) return;
                updateUi(searchTypeList);
                if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                    postOnUiThreadDelayed(() -> {
                        if (mIndicator != null && mIndicator.getChildAt(0) instanceof ViewGroup) {
                            View firstTab = ((ViewGroup) mIndicator.getChildAt(0)).getChildAt(0);
                            if (firstTab != null) {
                                firstTab.setFocusableInTouchMode(true);
                                firstTab.requestFocus();
                            }
                        }
                    }, 200);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) return;
                updateUi(null);
            }
        });
    }

    private void updateUi(List<SearchType> searchTypeList) {
        //有iting直达直接跳转,搜词直达需求，产品觉得这里跳转体验不好
        //没有iting直达就走正常逻辑
        List<SearchType> searchTypes;
        mSearchTypes = searchTypes = !ToolUtil.isEmptyCollects(searchTypeList) ? searchTypeList
                : SearchType.createSearchDefaultTypes(isCategorySearch());
        ArrayList<TabCommonAdapter.FragmentHolder> fragmentList = createTabList(searchTypes);
        if (!ToolUtil.isEmptyCollects(searchTypes) && SearchConstants.CORE_CHOSEN.equals(mSubTab)) {
            // 精选tab下才使用服务端的数据
            for (SearchType searchType : searchTypes) {
                if (searchType != null && searchType.isSelected()) {
                    mSubTab = searchType.getTypeValue();
                    break;
                }
            }
        }
        if (!ToolUtil.isEmptyCollects(fragmentList)) {
            mPagerAdapter = new SearchTabCommonAdapter(getChildFragmentManager(),
                    fragmentList);
            mPagerAdapter.setSearchDataContext(new SearchDataContextWrapper(this, this));
            mViewPager.setAdapter(mPagerAdapter);
            mViewPager.setOffscreenPageLimit(fragmentList.size());
            setIndicatorPadding(fragmentList.size());
            mIndicator.setViewPager(mViewPager);
            uploadFirstTabClickTrace(searchTypes);
            gotoSearchDataSubFragment(mSubTab);
            onPageLoadingCompleted(LoadCompleteType.OK);
        } else {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        }

        if (canShowAiIcon()) {
            wenAiIconIv.setVisibility(View.VISIBLE);
            wenAiShadow.setVisibility(View.VISIBLE);
            AIAgentSearchUtil.INSTANCE.eTraceIconShow(mKeyWord, requestId);
        } else {
            wenAiIconIv.setVisibility(View.GONE);
            wenAiShadow.setVisibility(View.GONE);
        }
    }

    private boolean canShowAiIcon() {
        boolean hasAiTab = false;
        for (int i = 0; i < mSearchTypes.size(); i++) {
            if (mSearchTypes.get(i).getTypeValue().equals(SearchConstants.CORE_AI_SEARCH)) {
                hasAiTab = true;
            }
        }
        return AIAgentSearchUtil.INSTANCE.isAiIconEnable(hasAiTab);
    }

    private void uploadFirstTabClickTrace(List<SearchType> searchTypes) {
        if (ToolUtil.isEmptyCollects(searchTypes)) return;
        SearchType searchType = searchTypes.get(0);
        SearchTraceUtils.traceWithSearchInfo("searchResult", "tab", "button", SearchTraceUtils.getTraceItemId(searchType != null ? searchType.getTypeValue() : "searchMatching"), null, XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_SEARCH_PAGE_CLICK);
    }


    private ArrayList<TabCommonAdapter.FragmentHolder> createTabList(List<SearchType> searchTypes) {
        if (searchTypes != null && !ToolUtil.isEmptyCollects(searchTypes)) {
            ArrayList<TabCommonAdapter.FragmentHolder> tabs = new ArrayList<>();
            for (int index = 0; index < searchTypes.size(); index++) {
                SearchType searchType = searchTypes.get(index);
                if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                    if (!(SearchConstants.CORE_CHOSEN.equals(searchType.getTypeValue())
                            || SearchConstants.CORE_ALBUM.equals(searchType.getTypeValue())
                            || SearchConstants.CORE_TRACK.equals(searchType.getTypeValue())
                            || SearchConstants.CORE_ANCHOR.equals(searchType.getTypeValue())
                            || SearchConstants.CORE_USER.equals(searchType.getTypeValue())
                            || SearchConstants.CORE_LIVEROOM.equals(searchType.getTypeValue()))) {
                        continue;
                    }
                }
                if (AiForceLoginManager.INSTANCE.isForceHide() && SearchConstants.CORE_AI_SEARCH.equals(searchType.getTypeValue())) {
                    continue;
                }
                Bundle bundle = new Bundle();
                bundle.putAll(getArguments());
                TabCommonAdapter.FragmentHolder holder = SearchUtils.createTabFragmentHolder(searchType, bundle);
                if (holder != null) {
                    tabs.add(holder);
                }
            }
            return tabs;
        }
        return null;
    }

    public void setSearchContext(ISearchContext searchContext) {
        this.mSearchContext = searchContext;
    }


    @Override
    public ViewGroup getTopHeader() {
        return mHeadView;
    }

    @Override
    public SlideView getContainerSlideView() {
        if (getSlideView() != null) {
            return getSlideView();
        } else if (mSearchContext != null) {
            return mSearchContext.getSlideView();
        }
        return null;
    }

    @Override
    public void reSearch(String kw, boolean spellCheck, boolean needSemantic, int typeFrom) {
        if (mSearchContext != null) {
            mSearchContext.reSearch(kw, spellCheck, false, needSemantic, typeFrom);
        }
    }

    @Override
    public void reSearchAndSwitchTab(String kw, boolean spellCheck, String subTab, int typeFrom) {
        if (mSearchContext != null) {
            mSearchContext.reSearchAndSwitchTab(kw, spellCheck, subTab, typeFrom);
        }
    }

    @Override
    public void gotoFragment(BaseFragment fragment) {
        if (fragment != null) {
            startFragment(fragment);
        }
    }

    @Override
    public void gotoSearchDataSubFragment(String core) {
        if (!ToolUtil.isEmptyCollects(mSearchTypes)) {
            for (int index = 0; index < mSearchTypes.size(); index++) {
                SearchType searchType = mSearchTypes.get(index);
                if (searchType != null && searchType.getTypeValue().equals(core)) {
                    if (mViewPager != null) {
                        mViewPager.setCurrentItem(index);
                    } else {
                        Logger.e(TAG, "gotoSearchDataSubFragment mViewPager is Null");
                    }
                    break;
                }
            }
        }
    }

    @Override
    public void gotoSearchDataAlbumFragmentWithLabel(String core, List<SearchLabel> labelList, MainLabel mainLabel) {
        if (!ToolUtil.isEmptyCollects(mSearchTypes)) {
            for (int index = 0; index < mSearchTypes.size(); index++) {
                SearchType searchType = mSearchTypes.get(index);
                if (searchType != null && searchType.getTypeValue().equals(core)) {
                    if (mViewPager != null) {
                        SearchLabelManager.INSTANCE.setSearchLabels(labelList);
                        SearchLabelManager.INSTANCE.setMainLabel(mainLabel);
                        SearchLabelManager.INSTANCE.setNeedRefresh(true);
                        mViewPager.setCurrentItem(index);
                    } else {
                        Logger.e(TAG, "gotoSearchDataSubFragment mViewPager is Null");
                    }
                    break;
                }
            }
        }
    }


    @Override
    public BaseFragment2 getCurrentPage() {
        if (mViewPager != null && mPagerAdapter != null) {
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(mViewPager.getCurrentItem());
            return fragment instanceof BaseFragment2 ? (BaseFragment2) fragment : null;
        }
        return null;
    }

    @Override
    public void gotoFragment(BaseFragment fragment, View view) {
        startFragment(fragment, view);
    }

    @Override
    public BaseFragment2 getCurrentFragment() {
        return this;
    }

    @Override
    public Context getAppContext() {
        return mContext;
    }

    @Override
    public int getTypeFrom() {
        return mType;
    }

    @Override
    public void showHotSearchDetailFragment(int categoryId) {
        if (mSearchContext != null) {
            mSearchContext.showHotSearchDetailFragment(categoryId);
        }
    }

    @Override
    public void onItemClick(View v, SearchHotWord model, int type, int pageId, int position) {
        if (mSearchContext != null) {
            mSearchContext.onItemClick(v, model, type, pageId, position);
        }
    }

    @Override
    public void setViewPagerSlideStatus(boolean canSlide) {
        if (mViewPager != null) {
            mViewPager.setCanSlide(canSlide);
        }
        if (mLastPosition == 0 && mSearchContext != null) {
            mSearchContext.setSlide(canSlide);
        }
    }

    protected boolean isCategorySearch() {
        return false;
    }

    @Override
    public void updateTopHeadRiskTips(SearchRiskTips searchRiskTips) {
        if (searchRiskTips != null && isFirstUpdateTopView) {
            isFirstUpdateTopView = false;
            checkHeadRiskTipsInit(searchRiskTips, mHeadView);
        }
    }

    @Override
    public void checkShowImmersiveAiIcon() {
        if (mViewPager == null) {
            return;
        }
        if (!canShowAiIcon()) {
            wenAiShadow.setVisibility(View.GONE);
            wenAiIconIv.setVisibility(View.GONE);
            return;
        }
        if (isChoseTabFragment(mViewPager.getCurrentItem()) && SearchImmersiveBannerManager.INSTANCE.isInImmersiveMode()) {
            wenAiIconIv.setImageResource(R.drawable.search_ic_wen_ai_white);
            wenAiShadow.setVisibility(View.GONE);
            wenAiIconIv.setBackgroundColor(Color.TRANSPARENT);
        } else {
            wenAiIconIv.setImageResource(R.drawable.search_ic_wen_ai);
            wenAiShadow.setVisibility(View.VISIBLE);
            wenAiIconIv.setBackgroundColor(getColorSafe(R.color.host_color_f8f8f8_131313));
        }
    }

    private void checkHeadRiskTipsInit(SearchRiskTips searchRiskTips, final ViewGroup headContainer) {
        if (headContainer == null) return;
        mHeadRiskTips = headContainer.findViewById(R.id.search_tv_search_risk_tips);
        String content = searchRiskTips != null ? searchRiskTips.getContent() : null;
        String color = searchRiskTips != null ? searchRiskTips.getBackgroudColor() : null;
        if (!TextUtils.isEmpty(content) && !TextUtils.isEmpty(color)) {
            mHeadRiskTips.setText(content);
            mHeadRiskTips.setBackgroundColor(color.equals("gray") ? getResources().getColor(R.color.search_color_f3f4f5_1e1e1e) : getResources().getColor(R.color.search_color_fffbe5_1e1e1e));
            SearchUiUtils.setVisible(View.VISIBLE, headContainer, mHeadRiskTips);
            if (searchRiskTips.isDisappear()) {
                new Handler().postDelayed(new Runnable() {

                    @Override
                    public void run() {
                        if (canUpdateUi()) {
                            int height = headContainer.getMeasuredHeight();
                            ValueAnimator valueAnimator = ValueAnimator.ofInt(0, -height);
                            valueAnimator.setDuration(200);
                            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                                @Override
                                public void onAnimationUpdate(ValueAnimator animation) {
                                    int value = (int) animation.getAnimatedValue();
                                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) headContainer.getLayoutParams();
                                    layoutParams.topMargin = value;
                                    headContainer.setLayoutParams(layoutParams);
                                }
                            });
                            valueAnimator.addListener(new AnimatorListenerAdapter() {
                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    super.onAnimationEnd(animation);
                                    SearchUiUtils.setVisible(View.GONE, headContainer, mHeadRiskTips);
                                }
                            });
                            valueAnimator.start();
                        }
                    }
                }, 2000);
            }
        } else {
            SearchUiUtils.setVisible(View.GONE, headContainer);
        }
        new XMTraceApi.Trace()
                .setMetaId(16725)
                .setServiceId("exposure")
                .put("currPage", "searchChosen")
                .put("searchWord", mKeyWord)
                .put("keyWord", mKeyWord)
                .put("moduleType", "风险提示")
                .createTrace();
    }

    @Override
    public void transformPage(@NonNull View page, float position) {
        Logger.d(getPageLogicName(), "position:" + position);
    }

    private void traceEnterPageForAccessibilityMode() {
        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            // 无障碍模式-搜索结果页  页面展示
            new XMTraceApi.Trace()
                    .pageView(58567, "globalAccessibilitySearch") // 页面出现在用户视野时上报一条埋点，包括离开页面后返回、息屏后亮屏等
                    .put("currPage", "globalAccessibilitySearch")
                    .createTrace();
        }
    }

    private void traceExitPageForAccessibilityMode() {
        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            // 无障碍模式-搜索结果页  页面离开
            new XMTraceApi.Trace()
                    .pageExit2(58568) // 页面离开在用户视野时上报一条埋点，包括锁屏、回到主页面等
                    .put("currPage", "globalAccessibilitySearch")
                    .createTrace();
        }
    }

    public SearchChosenFragment getChoseFragment() {
        if (mViewPager != null && mPagerAdapter != null) {
            Fragment fragment = mPagerAdapter.getFragment(SearchChosenFragment.class);
            if (fragment instanceof SearchChosenFragment) {
                return (SearchChosenFragment) fragment;
            }
        }
        return null;
    }

    private String getChoseFragmentImmersiveFrom() {
        SearchChosenFragment fragment = getChoseFragment();
        if (fragment != null) {
            return fragment.getImmersiveFrom();
        }
        return "";
    }

    /**
     * 触发RN通用卡片的AI图标点击事件
     */
    private void triggerRNCommonCardAiIconClick() {
        SearchChosenFragment fragment = getChoseFragment();
        if (fragment != null) {
            fragment.triggerRNCommonCardAiIconClick();
        }
    }
}