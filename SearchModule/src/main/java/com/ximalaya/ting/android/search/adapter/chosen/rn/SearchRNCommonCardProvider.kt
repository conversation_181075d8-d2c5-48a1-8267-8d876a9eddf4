package com.ximalaya.ting.android.search.adapter.chosen.rn

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.autosize.AutoSizeConfig
import com.ximalaya.ting.android.framework.startup.IOnAppStatusChangedListener
import com.ximalaya.ting.android.framework.startup.XmStartUpActivityLifecycle
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter.ILoadBundleListener
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.util.search.SearchReactNativeSetting
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.search.R
import com.ximalaya.ting.android.search.adapter.SearchRecyclerViewMultiTypeAdapter2
import com.ximalaya.ting.android.search.base.BaseSearchRecyclerViewAdapterProxyAndDataWithLifeCircle
import com.ximalaya.ting.android.search.base.ISearchDataContext
import com.ximalaya.ting.android.search.model.SearchItem
import com.ximalaya.ting.android.search.page.sub.SearchChosenFragment
import com.ximalaya.ting.android.search.utils.SearchTraceUtils
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import org.json.JSONArray
import org.json.JSONObject
import kotlin.math.roundToInt

/**
 * 使用 RN 实现的通用卡片
 */
class SearchRNCommonCardProvider(
    searchDataContext: ISearchDataContext?,
    val adapter: SearchRecyclerViewMultiTypeAdapter2
) : BaseSearchRecyclerViewAdapterProxyAndDataWithLifeCircle<SearchRNCommonCardProvider.ViewHolder?, SearchItem?>(
    searchDataContext
),
    ReactNativeAdapter.PageHeightListener {
    private val handler = Handler(Looper.getMainLooper())
    private val viewSizeListenerDataMap: MutableMap<String, SearchItem> = mutableMapOf()
    private val isFoldScreen: Boolean =
        BaseUtil.isFoldScreen(MainApplication.getInstance().realApplication)
    private val positionViewHolderMap: MutableMap<Int, ViewHolder> = mutableMapOf()

    //原有的隐藏加载逻辑作为兜底，最晚 X 秒执行
    private val hideLoadingOldLogicMaxDelayTime =
        SearchReactNativeSetting.getBackupLoadingDelayTime()

    //新的隐藏加载逻辑执行延迟
    private val hideLoadingDelayTime = SearchReactNativeSetting.getHideLoadingDelayTime()

    /**
     * 是否使用 FragmentLoading 那样的加载进度
     */
    fun useFragmentLoadingView(): Boolean {
        return SearchReactNativeSetting.useFragmentLoadingView()
    }

    override fun getView(layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup): View {
        val currentFragment: BaseFragment2 = adapter.adapterCurrentAttachFragment

        val xmReactView = ReactNativeAdapter.newXMReactView(
            currentFragment,
            SearchChosenFragment::class.java.simpleName
        )

        initView(xmReactView)
        return xmReactView
    }

    /**
     * 根据不同配置，设置不同的高度
     */
    private fun initView(xmReactView: View?) {
        if (xmReactView == null) {
            return
        }

        if (useFragmentLoadingView()) {
            // TODO: 动态卡片是否使用全屏加载策略？
            initViewForFragmentLoadingLogic(xmReactView)
        }
    }

    /**
     * 和 Fragment loading 一样的全屏加载进度和背景色
     * 1.高度先设置为全屏
     * 2.先设置为白色，加载完设置为透明
     */
    private fun initViewForFragmentLoadingLogic(xmReactView: View) {
        debugLog("initViewForFragmentLoadingLogic >> ")
        xmReactView.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            getDefaultHeight()
        )

        //设置背景色和 fragment 一致
        xmReactView.setBackgroundColor(
            ContextCompat.getColor(
                getContext(),
                R.color.search_color_ffffff_1e1e1e
            )
        )
    }

    private fun getDefaultHeight(): Int {
        return BaseUtil.getScreenHeight(getContext())
    }

    override fun bindView(
        holder: ViewHolder?,
        data: SearchItem?,
        extra: Any?,
        convertView: View?,
        position: Int
    ) {
        if (holder == null || data == null) {
            return
        }

        if (data.bundleName == null || data.jsonObject == null) {
            return
        }

        if (ConstantsOpenSdk.isDebug) {
            debugLog("rnCardHeight: " + data.rnCardHeight + ",nativeHeight: " + holder.nativeHeight + ", holder: " + holder)
        }

        if (data.rnCardHeight > 0 && data.rnCardHeight != holder.nativeHeight) {
            holder.nativeHeight = data.rnCardHeight
            updateViewHeight(holder.reactView, data.rnCardHeight)
        }

        if (SearchReactNativeSetting.isRnLoadError(holder.bundleName)) {
            //加载失败，强制设置高度为 1，实现隐藏
            updateViewHeight(holder.reactView, 1)
            SearchReactNativeSetting.saveRnLoadError(holder.bundleName, false)
            return
        }

        if (ReactNativeAdapter.sameBundle(holder.reactView, data.bundleName!!)) {

            val sameData = holder.data == data
            debugLog("bindView, already loaded, sameData? $sameData")
            if (sameData) {
                //除了比较业务还需要比较数据是否一致
                debugLog("bindView, already loaded, no need load again")
                return
            }
        }

        //用于和 js 通信的唯一标识，bundle + initPage （不会有同一类型多张卡片的情况，iOS 加 position 会导致复用失效，所以先去掉）
//        val identifyKey = data.bundleName + "&" + data.initPage + "&" + position
        val identifyKey = data.bundleName + "&" + data.initPage

        var positionToJS: Int = position
        if (data.positionInList >= 0) {
            positionToJS = data.positionInList
        }

        holder.bundleName = data.bundleName
        holder.currentPosition = positionToJS
        holder.data = data
        holder.viewSizeListenerKey = identifyKey

        positionViewHolderMap[position] = holder

        val bundle = processData(data, positionToJS)
        ReactNativeAdapter.saveSearchData(identifyKey, bundle)

        val wrapBundle = Bundle()
        wrapBundle.putInt("position", positionToJS)
        wrapBundle.putString("initPage", data.initPage)
        wrapBundle.putBoolean("searchResultUI", true)
        wrapBundle.putString("setInitData", "1")
        wrapBundle.putBundle("businessData", bundle)

        viewSizeListenerDataMap[identifyKey] = data

        var currentActivity = activity
        if (currentActivity == null) {
            currentActivity = BaseApplication.getTopActivity()
        }

        ReactNativeAdapter.loadBundleCommon(
            holder.reactView,
            currentActivity,
            data.bundleName!!,
            identifyKey,
            wrapBundle,
            loadBundleListener,
            holder
        )
    }

    private val loadBundleListener: ILoadBundleListener =
        object : ILoadBundleListener {

            /**
             * 加载成功，取色
             */
            override fun onLoadBundleSucceed() {

                debugLog("load_ s1, onLoadBundleSucceed")

                //1.恢复背景

                // TODO: 处理非首页的卡片 判断 position

                if (useFragmentLoadingView()) {
                    handler.removeCallbacks(hideLoadingRunnable)
                    handler.postDelayed(hideLoadingRunnable, hideLoadingOldLogicMaxDelayTime)
                }
            }

            /**
             * 加载失败，缩小错误布局，下次走原生
             */
            override fun onLoadBundleError(bundleName: String) {

                //异常后是否要进行补救
                val hideIfError = SearchReactNativeSetting.hideIfError()
                if (ConstantsOpenSdk.isDebug) {
                    ToastCompat.makeText(
                        getContext(),
                        "debug提示：RN 加载失败 $hideIfError $bundleName",
                        ToastCompat.LENGTH_SHORT
                    )
                        .show()
                }

                if (hideIfError) {
                    SearchReactNativeSetting.saveRnLoadError(bundleName, true)
                    Handler(Looper.getMainLooper()).post {
                        adapter.notifyDataSetChanged()
                    }
                }
            }

        }

    /**
     * 给的值都是以 375 作为屏幕宽度
     */
    private fun updateViewHeight(xmReactView: View?, viewHeight: Int) {
        if (xmReactView == null) {
            return
        }

        //RN 那边计算好像素高度，直接使用
        var dp2PixHeight = viewHeight

        try {
            val px = BaseUtil.dp2px(activity, viewHeight.toFloat())
            val initDensity = AutoSizeConfig.getInstance().initDensity
            val nowDensity = context?.resources?.displayMetrics?.density
            releaseLog("updateViewHeight >> getPxWithInitDensity initDensity: $initDensity nowDensity:$nowDensity  px:$px")

            //改成默认打开，返回 true 表示不使用 RN 的整体高度，通过客户端再计算一次
            val disableFixAutoSizeSetting = ConfigureCenter.getInstance()
                .getBool(CConstants.Group_android.GROUP_NAME, "search_card_rn_height_fix_autosize")

            releaseLog("updateViewHeight >> $viewHeight, disableFixAutoSizeSetting: $disableFixAutoSizeSetting")

            if (disableFixAutoSizeSetting) {
                dp2PixHeight = BaseUtil.dp2px(activity, viewHeight.toFloat())
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }

        xmReactView.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            dp2PixHeight
        )

        releaseLog("updateViewHeight >> $viewHeight, $dp2PixHeight")
    }

    private fun getPxWithInitDensity(dipValue: Int): Int {
        val scale = AutoSizeConfig.getInstance().initDensity
        releaseLog("updateViewHeight >> getPxWithInitDensity initDensity: $scale")
        if (scale <= 0) {
            return BaseUtil.dp2px(activity, dipValue.toFloat())
        }
        return (dipValue * scale + 0.5f).roundToInt()
    }

    override fun onHeightChanged(viewSizeListenerKey: String, newHeight: Int) {
        if (adapter == null) {
            return
        }
        val data = viewSizeListenerDataMap[viewSizeListenerKey]
        debugLog("load_ s2, onHeightChanged: $viewSizeListenerKey, height: $newHeight, data: $data")

        if (data != null) {
            data.rnCardHeight = newHeight

            handler.post {
                handler.removeCallbacks(hideLoadingRunnable)
                handler.postDelayed(hideLoadingRunnable, hideLoadingDelayTime)

                if (adapter != null) {
                    adapter.notifyDataSetChanged()
                }
            }
        }

    }

    private fun processData(data: SearchItem, position: Int): Bundle {
        val bundle = Bundle()
        bundle.putInt("position", position)     //等于 0 时需要展示一个顶部渐变
        bundle.putBoolean("isFoldScreen", isFoldScreen)   //是否折叠屏
        bundle.putString("searchWord", SearchTraceUtils.getKeyWord())
        bundle.putString("tagName", labelForTrace)
        bundle.putString(XmRequestIdManager.XM_REQUEST_ID, data.xmRequestId)
        bundle.putBoolean("searchResultUI", true)
        bundle.putInt("useNewPlayIcon", 1)

        if (data.jsonObject != null) {
            jsonToBundle(bundle, data.jsonObject!!)
        }
        return bundle
    }


    private fun jsonToBundle(bundle: Bundle, jsonObject: JSONObject): Bundle {
        val iterable = jsonObject.keys()
        while (iterable.hasNext()) {
            val key = iterable.next()
            val value = jsonObject.get(key)

            try {
                when (value) {
                    is String -> {
                        bundle.putString(key, value)
                    }

                    is Boolean -> {
                        bundle.putBoolean(key, value)
                    }

                    is Long -> {
                        bundle.putLong(key, value)
                    }

                    is Float -> {
                        bundle.putFloat(key, value)
                    }

                    is Double -> {
                        bundle.putDouble(key, value)
                    }

                    is Int -> {
                        bundle.putInt(key, value)
                    }

                    is JSONObject -> {
                        bundle.putBundle(key, jsonToBundle(Bundle(), value))
                    }

                    is JSONArray -> {
                        bundle.putStringArray(key, jsonArrayToStringArray(value))
                    }
                }
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        return bundle
    }

    private fun jsonArrayToStringArray(value: JSONArray): Array<String?> {

        val result = arrayOfNulls<String>(value.length())

        for (i in 0 until value.length()) {
            val string = value.getString(i)
            if (string != null) {
                result[i] = string
            }
        }

        return result
    }

    private fun debugLog(s: String) {
        if (ConstantsOpenSdk.isDebug) {
            Log.e("z_search", s)
        }
    }

    private fun releaseLog(s: String) {
        Log.e("z_search", s)
    }

    override fun getLayoutId(): Int {
        //useless
        return R.layout.search_item_top_album
    }

    override fun buildHolder(convertView: View): ViewHolder {
        var context = getContext()
        if (context == null) {
            context = BaseApplication.getTopActivity()
        }
        return ViewHolder(convertView, getDefaultHeight(), context, this)
    }

    /**
     * 不同类型 RN 卡片的 holder
     */
    class ViewHolder(
        view: View, defaultHeight: Int, context: Context,
        listener: ReactNativeAdapter.PageHeightListener
    ) : RecyclerView.ViewHolder(view),
        IRNFunctionRouter.ReactViewSizeChangeListener {

        var nativeHeight: Int = -1

        var data: SearchItem? = null
        var viewSizeListenerKey: String? = null
        var bundleName: String? = null
        var currentPosition: Int = -1
        val reactView: View = view
        var context: Context? = null
        val appLifeCycle = XmStartUpActivityLifecycle()
        var listener: ReactNativeAdapter.PageHeightListener? = null

        private val scrollListener = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                debugLog("sendEventToJS scrollViewDidEndScroll >>> $this")
                ReactNativeAdapter.sendEventToJS(
                    reactView,
                    "scrollViewDidEndScroll",
                    getRNEventValue()
                )
            }
        }

        private val fragmentDestroyListener = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                debugLog("fragmentDestroyListener  >>> $this")
                var curContext = context
                if (curContext == null) {
                    curContext = BaseApplication.getTopActivity()
                }
                LocalBroadcastManager.getInstance(curContext!!).unregisterReceiver(scrollListener)
                appLifeCycle.removeOnAppStatusChangedListener(appLifeCycleListener)
                LocalBroadcastManager.getInstance(curContext).unregisterReceiver(this)
            }
        }

        private val appLifeCycleListener = object : IOnAppStatusChangedListener {
            override fun onForeground(it: Intent?) {
                debugLog("sendEventToJS applicationDidBecomeActive >>> " + getRNEventValue())
//                ReactNativeAdapter.sendEventToJS(reactView, "applicationDidBecomeActive", getRNEventValue())
            }

            override fun onBackground(it: Intent?, fromPause: Boolean) {
            }
        }

        fun getRNEventValue(): MutableMap<String, Any> {
            val map = mutableMapOf<String, Any>()
            if (this.data?.initPage != null) {
                map["initPage"] = this.data?.initPage!!
            }
            return map
        }

        init {
            debugLog("init , addListener for $this")
            this.context = context
            this.nativeHeight = defaultHeight
            this.listener = listener
            if (BaseApplication.sInstance != null && BaseApplication.sInstance.realApplication != null) {
                BaseApplication.sInstance.realApplication.registerActivityLifecycleCallbacks(
                    appLifeCycle
                )
            }
            addListener(context)
        }

        private fun addListener(context: Context) {

            SearchReactNativeSetting.reactNativeCardShowed = true

            val intentFilter = IntentFilter(SearchReactNativeSetting.ACTION_SCROLL)
            LocalBroadcastManager.getInstance(context)
                .registerReceiver(this.scrollListener, intentFilter)


            val intentFilter2 = IntentFilter(SearchReactNativeSetting.ACTION_DESTROY)
            LocalBroadcastManager.getInstance(context)
                .registerReceiver(this.fragmentDestroyListener, intentFilter2)

            appLifeCycle.addOnAppStatusChangedListener(appLifeCycleListener)
        }

        private fun debugLog(s: String) {
            if (ConstantsOpenSdk.isDebug) {
                Log.e("z_search", s)
            }
        }

        override fun onHeightChanged(newHeight: Int) {
            if (listener == null) {
                return
            }

            var key = viewSizeListenerKey
            if (key == null) {
                key = bundleName
            }
            listener!!.onHeightChanged(key!!, newHeight)
        }

    }

    private val hideLoadingRunnable = Runnable {
        debugLog("hideLoadingRunnable exec >> " + positionViewHolderMap.size)
        positionViewHolderMap.forEach {
            val viewHolder = it.value
            viewHolder.reactView.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    override fun onResume(data: SearchItem?, position: Int, holder: ViewHolder?) {
        debugLog("sendEventToJS applicationDidOnResume >>> " + position + ", holder: " + holder + ", " + holder?.getRNEventValue())
        if (holder?.reactView != null) {
            ReactNativeAdapter.sendEventToJS(
                holder.reactView,
                "applicationDidOnResume",
                holder.getRNEventValue()
            )
        }
    }

    override fun onPause(data: SearchItem?, position: Int, holder: ViewHolder?) {
        debugLog("onPause >>> $position")
    }

    /**
     * AI图标点击事件
     */
    fun onAiIconClick() {
        debugLog("onAiIconClick >>> 发送search_ai_icon_click事件")
        // 遍历所有ViewHolder，向每个RN卡片发送事件
        positionViewHolderMap.forEach { (_, viewHolder) ->
            if (viewHolder.reactView != null) {
                ReactNativeAdapter.sendEventToJS(
                    viewHolder.reactView,
                    "search_ai_icon_click",
                    viewHolder.getRNEventValue()
                )
            }
        }
    }

}