package com.ximalaya.ting.android.vip.util.purchase.laber.type

import android.os.Bundle
import android.text.TextUtils
import androidx.fragment.app.Fragment
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.hybrid.providerSdk.payment.AutoRenewAction
import com.ximalaya.ting.android.host.manager.ElderlyModeManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.pay.PayActionHelper
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.routeservice.service.pay.IPayAction
import com.ximalaya.ting.android.util.ToolUtil
import com.ximalaya.ting.android.vip.constant.VipBundleConstants
import com.ximalaya.ting.android.vip.dialog.VipPaymentDialog
import com.ximalaya.ting.android.vip.model.material.PurchaseMaterial
import com.ximalaya.ting.android.vip.util.creater.ReturnUrlCreateUtil
import com.ximalaya.ting.android.vip.util.purchase.IVipGoodsCallBack
import com.ximalaya.ting.android.vip.util.purchase.VipPurchaseUtil
import com.ximalaya.ting.android.vip.util.purchase.laber.VipRnUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by 5Greatest on 2021.05.19
 *
 * <AUTHOR>
 *   On 2021/5/19
 */
class AutoRenewVip {
    companion object {
        /**
         * 判断是否是自动订阅
         * */
        fun isAutoRenew(material: PurchaseMaterial): Boolean {
            return material.item?.autoRenew == true
        }

        /**
         * 选择自动订阅的选项
         * */
        fun engage(material: PurchaseMaterial, callBack: IVipGoodsCallBack?): Int {
            if (material.vipProductType != VipBundleConstants.VipProductTypeConstants.PLATINUM_VIP) {
                engageSubscribeVipSkuByRn(material, callBack)
                return VipPurchaseUtil.RESULT_SUC
            }
            material?.fromFragment?.let {
                val dialog: VipPaymentDialog? = VipPaymentDialog.getDialog(it, material)
                dialog?.let { realDialog ->
                    realDialog.show(it.childFragmentManager, VipPaymentDialog.TAG)
                    return VipPurchaseUtil.RESULT_SUC
                }
            }
            return VipPurchaseUtil.RESULT_FAIL
        }

        private fun engageSubscribeVipSkuByRn(
            material: PurchaseMaterial,
            callBack: IVipGoodsCallBack?
        ) {
            val fragment = material.fromFragment
            fragment ?: return
            val skuItem = material.item as? VipSkuItem
            skuItem ?: return
            // 连续订阅
            val bundle = Bundle()
            bundle.putString("bundle", "subscribe_checkstand_rn")
            bundle.putString("pageName", "PopupCheckStand")
            val contextJson = JSONObject()
            val signContext = JSONObject()
            contextJson.put("source", "vip_sub")
            contextJson.put("orderSourceType", 18)
            contextJson.put("orderSourceValue", "vip")
            contextJson.put("executionEnvType", 1)
            contextJson.put("jointVipPromoter", 1)
            contextJson.put("orderFrom", 1)
            contextJson.put("orderSource", material.idMaterial.orderSource ?: "")
            try {
                val eldMode = if (ElderlyModeManager.getInstance().isElderlyMode()) "1" else "0"
                contextJson.put("specialModeStatus", eldMode);
            } catch (e: Exception) {
                e.printStackTrace()
            }
            material.transmissionParams?.let {
                transmissionParams->
                if (transmissionParams.entries.size > 0) {
                    val orderSourceValue: String? = transmissionParams.get("orderSourceValue")
                    if (!orderSourceValue.isNullOrEmpty()) {
                        var decodedOrderSourceValue = orderSourceValue
                        if (orderSourceValue.contains("\\\"")) {
                            decodedOrderSourceValue = orderSourceValue.replace("\\\"", "\"")
                        }
                        try {
                            val `object` = JSONObject(decodedOrderSourceValue)
                            if (`object`.has("EXTENSION")) {
                                val extension = `object`.optString("EXTENSION", "")
                                if (!extension.isNullOrEmpty()) {
                                    contextJson.put("orderItemExtension", extension)
                                }
                            }
                        } catch (e: JSONException) {
                            e.printStackTrace()
                        }
                    }
                    for ((key, value) in transmissionParams.entries) {
                        if (key.isNotEmpty() && value.isNotEmpty()
                        ) {
                            contextJson.put(key, value)
                        }
                    }
                }
            }
            val promotionItems = skuItem.buildSubscribePromotionItemString();
            if (!TextUtils.isEmpty(promotionItems)) {
                signContext.put("promotionItems", promotionItems);
            }
            signContext.put("source", contextJson.optString("source"))
            signContext.put("sceneType", "TIME_LIMITED")
            if (!TextUtils.isEmpty(skuItem.returnUrl)) {
                signContext.put("returnUrl", skuItem.returnUrl)
            } else {
                val useNew = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME,"vip_subscribe_return_url_new",false)
                if (useNew) {
                    signContext.put(
                        "returnUrl",
                        BaseUtil.chooseEnvironmentUrl("https://m.ximalaya.com/vip/alipay/callback?groupId=VIP_SUBSCRIBE_GROUP")
                    )
                } else {
                    signContext.put(
                        "returnUrl",
                        ReturnUrlCreateUtil.createReturnUrl(
                            material.vipProductType,
                            skuItem,
                            material.idMaterial.albumId,
                            material.idMaterial.trackId
                        )
                    )
                }
            }
            signContext.put("showUrl", "")
            signContext.put("promotionType", "TIME_LIMITED")
            bundle.putString("domain", "1")
            bundle.putString("sceneType", "TIME_LIMITED")
            bundle.putString("source", "vip_sub")
            bundle.putString("orderContext", contextJson.toString())
            bundle.putString("signContext", signContext.toString())
            bundle.putString("itemId", skuItem.itemId.toString())
            bundle.putLong("albumId",material.idMaterial.albumId)
            bundle.putLong("trackId",material.idMaterial.trackId)
            bundle.putString("description", skuItem.salesRemarkTextNew)
            bundle.putString("transparent", "1")
            bundle.putString("animate", "0")
            bundle.putString("vipType", "vip")
            if (fragment.activity != null && fragment.canUpdateUi()) {
                VipRnUtil.showVIPSubscribeCheckStand(
                    fragment,
                    bundle,
                ) { code: Int, s: String? ->
                    if (code == VipPurchaseUtil.RESULT_SUC) {
                        callBack?.onResult(VipPurchaseUtil.RESULT_SUC, "")
                    } else {
                        if (ConstantsOpenSdk.isDebug && !TextUtils.isEmpty(s)) {
                            callBack?.onResult(code, s)
                        }
                    }
                }
            }
        }

        /**
         * 连续订阅Vip
         * 接口文档
         * https://thoughts.ximalaya.com/workspaces/5dc4c8a54cfb7900134fcc8f/docs/60a1fd84e9e40f00011a86fe
         * http://gitlab.ximalaya.com/business/business-subscription/wikis/sign
         * */
        fun buyAutoRenewVip(
            channel: Int,
            dialog: Fragment,
            params: String?,
            callBack: IPayAction.PayCallBack
        ): Boolean {
            if (checkLoginStatus()) {
                when (channel) {
                    VipPurchaseUtil.CHANNEL_ALI_PAY -> {
                        val operator: PayActionHelper =
                            PayActionHelper(BaseApplication.getMainActivity(), dialog)
                        operator.autoRenewAlipay(false, params, callBack)
                        return true
                    }
                    VipPurchaseUtil.CHANNEL_HUA_BEI -> {
                        val operator: PayActionHelper =
                            PayActionHelper(BaseApplication.getMainActivity(), dialog)
                        operator.autoRenewAlipay(false, params, callBack)
                        return true
                    }
                    VipPurchaseUtil.CHANNEL_WE_CHAT_PAY -> {
                        val operator: PayActionHelper =
                            PayActionHelper(BaseApplication.getMainActivity(), dialog)
                        operator.autoRenewWechat(true, params, callBack, false)
                        return true
                    }
                    VipPurchaseUtil.CHANNEL_UNION_PAY -> {
                        val operator: AutoRenewAction = AutoRenewAction()
                        operator.autoRenewUnionPay(false, params, callBack)
                        return true
                    }
                    VipPurchaseUtil.CHANNEL_CMCC_PAY -> {
                        val operator: AutoRenewAction = AutoRenewAction()
                        operator.autoRenewCMCCPay(params, callBack)
                        return true
                    }
                }
            }
            return false
        }

        private fun checkLoginStatus(needCheck: Boolean = true): Boolean {
            return if (needCheck) UserInfoMannage.hasLogined() else true
        }

        /**
         * 微信连续订阅比较特殊，需要手动注入 returnUrl
         * */
        fun processWeChatParam(params: String?, returnUrl: String?): String? {
            params ?: return null
            returnUrl ?: return params
            try {
                val json: JSONObject = JSONObject(params)
                if (json.has("params")) {
                    val param: JSONObject? = json.optJSONObject("params")
                    param?.let {
                        it.put("returnUrl", returnUrl)
                        json.put("params", it)
                        return json.toString()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return params
        }
    }
}