package com.ximalaya.ting.android.vip.manager.purchaseDialog

import android.graphics.Color
import android.view.View
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo
import com.ximalaya.ting.android.vip.R

/**
 * 会员购买和统一弹窗合并以后，顶部布局管理
 * 包括：标题、SKU 列表、展开信息
 */
class PVipBottomViewManagerImpl(
    val baseFragment: BaseFragment2,
    val data: VipSkuShelfInfo,
    val transmissionParams: HashMap<String, String>
) : BaseVipBottomViewMamanger(baseFragment, data, transmissionParams) {
    override fun getTabName(): String {
        return TAB_TYPE_PLATINUM_VIP
    }

    override fun createRuleAndBuyButtonView(): View {
        val view = super.createRuleAndBuyButtonView()
        val bgDrawableResId = com.ximalaya.ting.android.host.R.drawable.host_bg_platinum_vip_button
        val textColor = Color.parseColor("#794A16")
        buttonContainer?.setBackgroundResource(bgDrawableResId)
        buttonViewTv?.setTextColor(textColor)
        tvPrice?.setTextColor(textColor)
        protocolCheckbox?.setImageResource(R.drawable.host_vip_purchase_protocol_checkbox_v4)
        return view
    }

    override fun updateButtonText(vipSkuItem: VipSkuItem) {
        val upgradeMonthSet = bottomActionListener?.getUpgradeSkuSet()
        if (upgradeMonthSet != null) {
            if (upgradeMonthSet.first == vipSkuItem) {
                setButtonViewText(upgradeMonthSet.first, upgradeMonthSet.second)
            } else {
                super.updateButtonText(vipSkuItem)
            }
        } else {
            super.updateButtonText(vipSkuItem)
        }
    }

    override fun updateUpgradeMonth() {
        selectedVipSkuItem?.let {
            updateButtonText(it)
        }
    }
}