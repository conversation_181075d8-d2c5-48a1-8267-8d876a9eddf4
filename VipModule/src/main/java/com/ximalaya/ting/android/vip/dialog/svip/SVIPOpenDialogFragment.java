package com.ximalaya.ting.android.vip.dialog.svip;


import static java.lang.Math.sin;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Paint;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.utils.widget.ImageFilterView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.google.gson.Gson;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.DynamicImageProcessor;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseScrollDialogfragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil;
import com.ximalaya.ting.android.host.util.common.IScreenConfigChangedListener;
import com.ximalaya.ting.android.host.util.common.SpanUtils;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.vip.R;
import com.ximalaya.ting.android.vip.constant.VipUrlConstants;
import com.ximalaya.ting.android.vip.manager.VipPurchaseNetManager;
import com.ximalaya.ting.android.vip.model.svip.Agreement;
import com.ximalaya.ting.android.vip.model.svip.DialogBehavior;
import com.ximalaya.ting.android.vip.model.svip.Properties;
import com.ximalaya.ting.android.vip.model.svip.SVIPDialogData;
import com.ximalaya.ting.android.vip.model.svip.SVIPSkuItem;
import com.ximalaya.ting.android.vip.util.SpannableStringUtils;
import com.ximalaya.ting.android.vip.util.purchase.IVipGoodsCallBack;
import com.ximalaya.ting.android.vip.util.purchase.VipPurchaseUtil;
import com.ximalaya.ting.android.vip.util.purchase.laber.VipRnUtil;
import com.ximalaya.ting.android.vip.view.VipExpandableContentTextView;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.SoftReference;
import java.lang.ref.WeakReference;
import java.util.List;

import kotlin.Triple;
import kotlin.Unit;
import kotlin.jvm.functions.Function2;

/**
 * <AUTHOR>
 * @time 2024/11/4 15:12
 * @description: SVIP开通vip弹窗
**/
public class SVIPOpenDialogFragment extends BaseScrollDialogfragment implements View.OnClickListener, IScreenConfigChangedListener {
    private final static String TAG = "SVIPOpenDialogFragment";
    private static SoftReference<SVIPOpenDialogFragment> sSoftReference;
    public boolean mIsShowing = true;
    private PayParams mPayParams;
    private SVIPDialogData mSVIPDialogData;
    private SVIPSkuManager mSVIPSkuManager;

    private RelativeLayout mRootLay;
    private View mMoreListView;
    private ImageFilterView mIvDialogTitleBg;
    private ImageFilterView mIvDialogTitle;
    private TextView mTvDialogSubTitle;
    private LinearLayout mSkuItemsContainer;
    private ImageFilterView mIvPrivilegeDetail;
    private LinearLayout mExtraActionsContainer;
    private RelativeLayout mPurchaseDescriptionContainer;
    private VipExpandableContentTextView mTvPurchaseDescription;
    private ConstraintLayout mBottomContainer;
    private LinearLayout mLlRuleContainer;
    private ImageView mCbRule;
    private TextView mTvRule;
    private View mRuleCheckClickArea;
    private View mVLeftButton;
    private View mVCenterButton;
    private View mVRightButton;
    private TextView mTvBuyButton;
    private TextView mTvPriceSymbol;
    private TextView mTvPrice;
    private TextView mTvPriceUnit;
    private TextView mTvPriceIntroSingLine;
    private TextView mTvPriceIntroDoubleLines;

    private int mBottomExtraWidth = 0;
    private MyLayoutListener mLayoutListener;
    private String sceneTypeFromConfig;


    public SVIPOpenDialogFragment(PayParams payParams) {
        super();
        this.mPayParams = payParams;
    }

    public static void showDialog(MainActivity mainActivity,
                                  String sceneType,
                                  String utmsource, String orderUpType,
                                  String orderUpValue, String orderSubType,
                                  String orderSubValue, String pcreditNumInterest, String type) {

        if (mainActivity == null || mainActivity.getSupportFragmentManager() == null) {
            return;
        }

        if (sSoftReference != null && sSoftReference.get() != null) {
            sSoftReference.get().dismissAllowingStateLoss();
        }

        PayParams payParams = new PayParams(utmsource, orderUpType, orderUpValue, orderSubType, orderSubValue, pcreditNumInterest, type);
        payParams.sceneType = sceneType;
        SVIPOpenDialogFragment dialogFragment = new SVIPOpenDialogFragment(payParams);

        dialogFragment.show(mainActivity.getSupportFragmentManager(), "MasterVipOpenDialogFragment");

        sSoftReference = new SoftReference<>(dialogFragment);
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public float contentRatio() {
        return 0.58f;
    }

    @Override
    public void initUi() {
        sceneTypeFromConfig = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME, "svip_scenetype", "");
        if (TextUtils.isEmpty(sceneTypeFromConfig)) {
            sceneTypeFromConfig = "rn";
        }
        mRootLay = findViewById1(R.id.vip_frag_svip_open);
        mMoreListView = findViewById1(R.id.vip_inner_scroll_view);
        mIvDialogTitleBg = findViewById1(R.id.vip_bg_svip_dialog_title);
        mIvDialogTitle = findViewById1(R.id.vip_iv_top_title);
        mTvDialogSubTitle = findViewById1(R.id.vip_tv_subtitle);
        mSkuItemsContainer = findViewById1(R.id.vip_ll_svip_skus_container);
        mPurchaseDescriptionContainer = findViewById1(R.id.vip_item_purchase_description);
        mTvPurchaseDescription = findViewById1(R.id.vip_item_purchase_description_text);
        mIvPrivilegeDetail = findViewById1(R.id.vip_iv_privilege_detail);
        mExtraActionsContainer = findViewById1(R.id.vip_ll_extra_action_entrance);
        mBottomContainer = findViewById1(R.id.vip_cl_bottom_info);
        mCbRule = findViewById1(R.id.vip_iv_purchase_dialog_protocol_checkbox);
        mTvRule = findViewById1(R.id.vip_tv_purchase_dialog_button_rule);
        mRuleCheckClickArea = findViewById1(R.id.vip_v_purchase_dialog_protocol_checkbox_click_area);
        mTvBuyButton = findViewById1(R.id.vip_svip_buy_tv);
        mTvPriceSymbol = findViewById1(R.id.vip_tv_price_symbol);
        mTvPrice = findViewById1(R.id.vip_price_tv);
        mTvPriceUnit = findViewById1(R.id.vip_price_unit_tv);
        mTvPriceIntroSingLine = findViewById1(R.id.vip_tv_desc_single_line);
        mTvPriceIntroDoubleLines = findViewById1(R.id.vip_tv_desc_double_line);
        mLlRuleContainer = findViewById1(R.id.vip_ll_rule);
        mVRightButton = findViewById1(R.id.vip_master_buy_action_tv);
        mVLeftButton = findViewById1(R.id.vip_view_master_price_bg);
        mVCenterButton = findViewById1(R.id.vip_master_middle_image);
        mRuleCheckClickArea.setOnClickListener(this);
        mVLeftButton.setOnClickListener(this);
        mVCenterButton.setOnClickListener(this);
        mVRightButton.setOnClickListener(this);
        mSVIPSkuManager = new SVIPSkuManager(getNoNullContext(), mSkuItemsContainer, new SVIPSkuManager.ISVIPSkuSelectedListener() {
            @Override
            public void onSkuSelected(@NonNull SVIPSkuItem sku) {
                updateViewWhileSkuChanged(sku);
                traceClickSkuItem(sku);
            }
        });
        FoldableScreenCompatUtil.INSTANCE.addListener("SVIPOpenDialogFragment", this);
        mBottomContainer.getViewTreeObserver().addOnGlobalLayoutListener(mLayoutListener = new MyLayoutListener(this));
    }

    private Context getNoNullContext(){
        Context context = getContext();
        if(context == null){
            context = MainApplication.getMyApplicationContext();
        }
        return context;
    }

    private Resources getNoNullResources() {
        if (getActivity() != null) {
            return getActivity().getResources();
        }
        return getNoNullContext().getResources();
    }


    private static class MyLayoutListener implements ViewTreeObserver.OnGlobalLayoutListener {
        private WeakReference<SVIPOpenDialogFragment> fragmentWeakReference;

        public MyLayoutListener(SVIPOpenDialogFragment fragment) {
            this.fragmentWeakReference = new WeakReference<>(fragment);
        }

        @Override
        public void onGlobalLayout() {
            if (fragmentWeakReference != null && fragmentWeakReference.get() != null) {
                fragmentWeakReference.get().updateBottomDescriptionOtherWidth();
            }
        }
    }

    @Override
    public void onDestroyView() {
        if (mBottomContainer != null && mBottomContainer.getViewTreeObserver() != null && mLayoutListener != null) {
            mBottomContainer.getViewTreeObserver().removeOnGlobalLayoutListener(mLayoutListener);
        }
        FoldableScreenCompatUtil.INSTANCE.removeListener("SVIPOpenDialogFragment");
        super.onDestroyView();
    }

    @Override
    public void loadData() {
        if (!canUpdateUi()) {
            return;
        }
        XmRequestPage.INSTANCE.resetPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG);
        String url = VipUrlConstants.Companion.getInstance().getSVIPPurchaseDialogDataUrl();
        StringBuilder extraParamStr = new StringBuilder();
        if (mPayParams != null) {
            if (!TextUtils.isEmpty(mPayParams.type)) {
                extraParamStr.append("type=").append(mPayParams.type);
            }
            if (!TextUtils.isEmpty(mPayParams.orderUpValue)) {
                if (extraParamStr.length() > 0) {
                    extraParamStr.append("&");
                }
                extraParamStr.append("albumId=").append(mPayParams.orderUpValue);
            }
            if (!TextUtils.isEmpty(mPayParams.orderSubValue)) {
                if (extraParamStr.length() > 0) {
                    extraParamStr.append("&");
                }
                extraParamStr.append("trackId=").append(mPayParams.orderSubValue);
            }
            if (!TextUtils.isEmpty(mPayParams.utmsource)) {
                if (extraParamStr.length() > 0) {
                    extraParamStr.append("&");
                }
                extraParamStr.append("utmSource=").append(mPayParams.utmsource);
            }
        }
        if (!TextUtils.isEmpty(extraParamStr)) {
            url = url + "?" + extraParamStr.toString();
        }
        VipPurchaseNetManager.Companion.requestSVIPPurchaseDialogInfo(url, new IDataCallBack<SVIPDialogData>() {
            @Override
            public void onSuccess(@Nullable SVIPDialogData data) {
                if (data == null) {
                    ViewStatusUtil.setVisible(View.INVISIBLE, mRootLay);
                    return;
                }

                ViewStatusUtil.setVisible(View.VISIBLE, mRootLay);

                mSVIPDialogData = data;

                setupUI(data);
            }

            @Override
            public void onError(int code, String message) {
                dismissAllowingStateLoss();
            }
        });

    }

    private void setupUI(SVIPDialogData data) {
        if (data == null || data.getData() == null || data.getData().size() <= 0) {
            // 空数据，这里应该兜底显示什么？
            return;
        }
        traceAllMasterService();
        mExtraActionsContainer.removeAllViews();
        for (DialogBehavior behavior : data.getData()) {
            if (behavior != null) {
                switch (behavior.getType()) {
                    case "svip":
                        if (behavior.getShelfInfo() != null && behavior.getShelfInfo().getProducts() != null) {
                            mSVIPSkuManager.createSkuList(behavior.getShelfInfo().getProducts());
                            for (SVIPSkuItem item : behavior.getShelfInfo().getProducts()) {
                                traceShowSkuItem(item);
                            }
                        } else {
                            mSkuItemsContainer.setVisibility(View.GONE);
                        }
                        break;
                    case "url":
                        createUrlBehaviorView(behavior);
                        break;
                }

            }
        }

    }

    private void createUrlBehaviorView(final DialogBehavior behavior) {
        if (!TextUtils.equals("url", behavior.getType())) {
            return;
        }
        View view = LayoutInflater.from(getNoNullContext()).inflate(R.layout.vip_item_svip_other_behavior, null);
        TextView tvBehaviorName = view.findViewById(R.id.vip_tv_behavior_name);
        ViewStatusUtil.setText(tvBehaviorName, behavior.getLabelText());
        ViewStatusUtil.setOnClickListener(view, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(behavior.getJumpUrl())) {
                    if (OneClickHelper.getInstance().onClick(v)) {
                        traceClickAllMasterService();
                        if (BaseApplication.getMainActivity() instanceof MainActivity) {
                            ToolUtil.clickUrlAction((MainActivity) BaseApplication.getMainActivity(), behavior.getJumpUrl(), v);
                        }
                        dismissAllowingStateLoss();
                    }
                }
            }
        });
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(getNoNullContext(), 44));
        params.bottomMargin = BaseUtil.dp2px(getNoNullContext(), 10);
        mExtraActionsContainer.addView(view, params);
    }

    private void updateViewWhileSkuChanged(SVIPSkuItem selectedItem) {
        if (!TextUtils.isEmpty(selectedItem.getHeaderBgPic())) {
            ImageManager.from(getNoNullContext()).displayImage(mIvDialogTitleBg, selectedItem.getHeaderBgPic(), -1);
        }

        if (!TextUtils.isEmpty(selectedItem.getHeaderTitlePic())) {
            final String headerTitlePic = selectedItem.getHeaderTitlePic();
            //mIvDialogTitle,
            ImageManager.from(getNoNullContext()).downloadBitmap(selectedItem.getHeaderTitlePic(), new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (TextUtils.equals(lastUrl, headerTitlePic) || TextUtils.equals(lastUrl, DynamicImageProcessor.getSingleInstance().fitNoWebpImage(headerTitlePic, 0, 0))) {
                        if (bitmap != null) {
                            int bmWidth = bitmap.getWidth();
                            int bmHeight = bitmap.getHeight();
                            float scale = bmHeight * 1f / BaseUtil.dp2px(getNoNullContext(), 22);
                            if (scale > 0f) {
                                int imageWidth = (int) (bmWidth / scale);
                                int imageHeight = BaseUtil.dp2px(getNoNullContext(), 22);
                                ViewGroup.LayoutParams layoutParams = mIvDialogTitle.getLayoutParams();
                                if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                                    layoutParams.width = imageWidth;
                                    layoutParams.height = imageHeight;
                                } else {
                                    layoutParams = new ConstraintLayout.LayoutParams(imageWidth, imageHeight);
                                }
                                mIvDialogTitle.setLayoutParams(layoutParams);
                                mIvDialogTitle.setImageBitmap(bitmap);
                            } else {
                                mIvDialogTitle.setImageBitmap(bitmap);
                            }
                        }
                    }
                }
            });
        }
        if (TextUtils.isEmpty(selectedItem.getHeaderSubtitle())) {
            mTvDialogSubTitle.setText("开通SVP会员畅听");
        } else {
            mTvDialogSubTitle.setText(selectedItem.getHeaderSubtitle());
        }
        if (TextUtils.isEmpty(selectedItem.getSalesRemark())) {
            mPurchaseDescriptionContainer.setVisibility(View.GONE);
        } else {
            mPurchaseDescriptionContainer.setVisibility(View.VISIBLE);
            mTvPurchaseDescription.setExpanded(false);
            String salesRemark = TextUtils.isEmpty(selectedItem.getSalesRemarkV2()) ? selectedItem.getSalesRemark() : selectedItem.getSalesRemarkV2();
            if (salesRemark == null) {
                salesRemark = "";
            }
            final List<Triple<Integer, Integer, String>> boldTextIndices = SpannableStringUtils.getBoldTextIndices(salesRemark);
            CharSequence descriptionAfterRemoveAllBTags = SpannableStringUtils.removeAllBTags(salesRemark);
            mTvPurchaseDescription.setContent(descriptionAfterRemoveAllBTags, new VipExpandableContentTextView.ITextIntercept() {
                @Override
                public SpannableStringBuilder interceptSpannableStringBuilder(boolean isExpanded, SpannableStringBuilder stringBuilder) {
                    if(stringBuilder == null){
                        return null;
                    }
                    if (boldTextIndices != null && !boldTextIndices.isEmpty()) {
                        return SpannableStringUtils.removeBTagsAndSetBoldText(
                                stringBuilder,
                                boldTextIndices
                        );
                    } else {
                        return stringBuilder;
                    }
                }
            });
        }
//        RightsIntro rightsIntro = VipBundleCommonUtil.Companion.safelyGetItemFromList(selectedItem.getRightsIntro(), 0);
//        if (rightsIntro != null) {
//            final String rightIntroUrl;
//            if (BaseFragmentActivity.sIsDarkMode) {
//                if (VipFragmentUtil.FoldScreenUtils.Companion.isFoldScreenInExpandMode(getNoNullContext())) {
//                    rightIntroUrl = rightsIntro.getWidthDark();
//                } else {
//                    rightIntroUrl = rightsIntro.getDark();
//                }
//            } else {
//                if (VipFragmentUtil.FoldScreenUtils.Companion.isFoldScreenInExpandMode(getNoNullContext())) {
//                    rightIntroUrl = rightsIntro.getWidthNormal();
//                } else {
//                    rightIntroUrl = rightsIntro.getNormal();
//                }
//            }
//            ImageManager.from(getNoNullContext()).downloadBitmap(rightIntroUrl, new ImageManager.DisplayCallback() {
//                @Override
//                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
//                    if (canUpdateUi()) {
//                        if (TextUtils.equals(lastUrl, rightIntroUrl) || TextUtils.equals(lastUrl, DynamicImageProcessor.getSingleInstance().fitNoWebpImage(rightIntroUrl, 0, 0))) {
//                            if (bitmap != null) {
//                                int bmWidth = bitmap.getWidth();
//                                int bmHeight = bitmap.getHeight();
//                                int imageWidth = BaseUtil.getScreenWidth(getNoNullContext()) - getNoNullResources().getDimensionPixelOffset(com.ximalaya.ting.android.host.R.dimen.host_default_side_margin) * 2;
//                                float scale = bmWidth > 0 ? (imageWidth * 1f / bmWidth) : 1f;
//                                int imageHeight = (int) (bmHeight * scale);
//                                if (bmWidth > 0) {
//                                    //此时可以按照计算的宽高设置图片，
//                                    mIvPrivilegeDetail.setImageBitmap(bitmap);
//                                    ViewGroup.LayoutParams layoutParams = mIvPrivilegeDetail.getLayoutParams();
//                                    if (layoutParams instanceof LinearLayout.LayoutParams) {
//                                        ((LinearLayout.LayoutParams) layoutParams).width = imageWidth;
//                                        ((LinearLayout.LayoutParams) layoutParams).height = imageHeight;
//                                    } else {
//                                        layoutParams = new LinearLayout.LayoutParams(imageWidth, imageHeight);
//                                        ((LinearLayout.LayoutParams) layoutParams).topMargin = BaseUtil.dp2px(getNoNullContext(), 20);
//                                        ((LinearLayout.LayoutParams) layoutParams).leftMargin = getNoNullResources().getDimensionPixelOffset(com.ximalaya.ting.android.host.R.dimen.host_default_side_margin);
//                                        ((LinearLayout.LayoutParams) layoutParams).rightMargin = getNoNullResources().getDimensionPixelOffset(com.ximalaya.ting.android.host.R.dimen.host_default_side_margin);
//                                    }
//                                    mIvPrivilegeDetail.setLayoutParams(layoutParams);
//                                }
//                            }
//                        }
//                    }
//                }
//            });
//        }
        if (selectedItem.getAgreement() != null) {
            SpanUtils spanUtils = SpanUtils.with(mTvRule).append("开通前请阅读");
            for (Agreement agreement : selectedItem.getAgreement()) {
                final Agreement finalAgreement = agreement;
                if (agreement != null) {
                    spanUtils.append("《" + agreement.getAgreementText() + "》 ").setClickSpan(getNoNullResources().getColor(R.color.vip_color_b3b3b3_8d8d91), false, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (!TextUtils.isEmpty(finalAgreement.getAgreementUrl())) {
                                if (OneClickHelper.getInstance().onClick(v)) {
                                    if (BaseApplication.getMainActivity() instanceof MainActivity) {
                                        ToolUtil.clickUrlAction((MainActivity) BaseApplication.getMainActivity(), finalAgreement.getAgreementUrl(), v);
                                        dismissAllowingStateLoss();
                                    }
                                }
                            }
                        }
                    });
                }
            }
            spanUtils.create();
        }
        ViewStatusUtil.setText(mTvPrice, selectedItem.getPayPrice());
        ViewStatusUtil.setText(mTvPriceUnit, selectedItem.getPriceUnit());
        final Properties properties = selectedItem.getProperties();
        mBottomContainer.post(new Runnable() {
            @Override
            public void run() {
                if (properties != null) {
                    if (calculateTextIsSingleLine(mTvPriceIntroSingLine, properties.getPurchaseButtonTips())) {
                        ViewStatusUtil.setText(mTvPriceIntroSingLine, properties.getPurchaseButtonTips());
                        ViewStatusUtil.setVisible(View.VISIBLE, mTvPriceIntroSingLine);
                        ViewStatusUtil.setVisible(View.INVISIBLE, mTvPriceIntroDoubleLines);
                    } else {
                        ViewStatusUtil.setText(mTvPriceIntroDoubleLines, properties.getPurchaseButtonTips());
                        ViewStatusUtil.setVisible(View.INVISIBLE, mTvPriceIntroSingLine);
                        ViewStatusUtil.setVisible(View.VISIBLE, mTvPriceIntroDoubleLines);
                    }
                    ViewStatusUtil.setText(mTvBuyButton, properties.getPurchaseButtonText());
                }
            }
        });


    }

    private void updateBottomDescriptionOtherWidth() {
        int width = 0;
        if (mTvPriceSymbol != null) {
            width += mTvPriceSymbol.getMeasuredWidth() + BaseUtil.dp2px(getNoNullContext(), 18);
        }
        if (mTvPrice != null) {
            width += mTvPrice.getMeasuredWidth() + BaseUtil.dp2px(getNoNullContext(), 4);
        }
        if (mTvPriceUnit != null) {
            width += mTvPriceUnit.getMeasuredWidth();
        }
        if (mVRightButton != null) {
            width += mVRightButton.getMeasuredWidth();
        }
        width += BaseUtil.dp2px(getNoNullContext(), 30) + getNoNullResources().getDimensionPixelOffset(com.ximalaya.ting.android.host.R.dimen.host_default_side_margin) * 2;
        mBottomExtraWidth = width;
    }

    private boolean calculateTextIsSingleLine(TextView textView, String text) {
        Paint paint = new Paint();
        paint.setTextSize(textView.getTextSize());
        float width = paint.measureText(text);
        int screenWidth = BaseUtil.getScreenWidth(getNoNullContext());
        int otherWidth = mBottomExtraWidth;
        int singleTextMaxWidth = screenWidth - otherWidth;
        if (singleTextMaxWidth >= width) {
            return true;
        }
        return false;
    }


    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.vip_dialog_frag_svip_open;
    }

    @Override
    public View getInnerScrollView() {
        return mMoreListView;
    }


    @Override
    protected void statCancel() {
    }


    @Override
    public void show(FragmentManager manager, String tag) {
        super.show(manager, tag);
        mIsShowing = true;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        mIsShowing = true;
        return super.show(transaction, tag);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mIsShowing = false;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.vip_v_purchase_dialog_protocol_checkbox_click_area) {
            if (OneClickHelper.getInstance().onClick(v)) {
                mCbRule.setSelected(!mCbRule.isSelected());
                traceRuleCheckBoxClick();
            }
        } else if (id == R.id.vip_master_buy_action_tv
                || id == R.id.vip_view_master_price_bg
                || id == R.id.vip_master_middle_image) {
            clickTrace();
            placeOrderAndPay();
        }
    }


    private void placeOrderAndPay() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getNoNullContext());
            return;
        }
        // 调用支付 RN 弹窗
        final SVIPSkuItem selectedItem = mSVIPSkuManager.getMSelectedSkuItem();
        if (selectedItem == null) {
            return;
        }
        final Runnable payRunuable = new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    if (selectedItem.getDurationType() != null && selectedItem.getDurationType() == 6) {
                        // 连续订阅
                        Bundle bundle = new Bundle();
                        bundle.putString("bundle","subscribe_checkstand_rn");
                        bundle.putString("pageName","PopupCheckStand");
                        JSONObject contextJson = new JSONObject();
                        JSONObject signContext = new JSONObject();
                        if (selectedItem.getContext() != null) {
                            contextJson.put("utmsource", selectedItem.getContext().getUtmsource());
                            contextJson.put("source", "svip_sub");
                            contextJson.put("pcreditNumInterest", selectedItem.getContext().getPcreditNumInterest());
                            contextJson.put("masterclassTag", selectedItem.getContext().getMasterclassTag());
                            contextJson.put("orderUpValue", selectedItem.getContext().getOrderUpValue());
                            contextJson.put("orderUpType", selectedItem.getContext().getOrderUpType());
                        }
                        if (SVIPOpenDialogFragment.this.mPayParams != null) {
                            contextJson.put("utmsource", mPayParams.utmsource);
                            contextJson.put("source", "svip_sub");
                            contextJson.put("orderUpType", mPayParams.orderUpType);
                            contextJson.put("orderUpValue", mPayParams.orderUpValue);
                            contextJson.put("orderSubType", mPayParams.orderSubType);
                            contextJson.put("orderSubValue", mPayParams.orderSubValue);
                            if (mPayParams.pcreditNumInterest != null) {
                                contextJson.put("pcreditNumInterest", mPayParams.pcreditNumInterest);
                            }
                            contextJson.put("orderSourceType", 1);
                            contextJson.put("orderSourceValue", "masterClass");
                            contextJson.put("executionEnvType", 1);
                            signContext.put("source",contextJson.optString("source"));
                            signContext.put("sceneType", sceneTypeFromConfig);
                            if(!TextUtils.isEmpty(mPayParams.domain)){
                                bundle.putString("domain", mPayParams.domain);
                            }else {
                                bundle.putString("domain", "1");
                            }
                            bundle.putString("sceneType", sceneTypeFromConfig);
                            bundle.putString("source","svip_sub");
                        }
                        if (!TextUtils.isEmpty(selectedItem.getReturnUrl())) {
                            signContext.put("returnUrl", selectedItem.getReturnUrl());
                        } else {
                            signContext.put("returnUrl", BaseUtil.chooseEnvironmentUrl("https://m.ximalaya.com/vip/alipay/callback?groupId=SVIP_SUBSCRIBE_GROUP"));
                        }
                        signContext.put("showUrl","");
                        signContext.put("promotionType","TIME_LIMITED");
//                        Escaper escaper = Escapers.builder().addEscape('"', "\\\"")
//                                .addEscape('\"', "\\\"").build();
//                        bundle.putString("orderContext", escaper.escape(contextJson.toString()));
//                        bundle.putString("signContext", escaper.escape(signContext.toString()));
                        String salesRemark = TextUtils.isEmpty(selectedItem.getSalesRemarkV2()) ? selectedItem.getSalesRemark() : selectedItem.getSalesRemarkV2();
                        if (salesRemark == null) {
                            salesRemark = "";
                        }
                        bundle.putString("description", salesRemark);
                        bundle.putString("orderContext", contextJson.toString());
                        bundle.putString("signContext", signContext.toString());
                        bundle.putString("itemId", String.valueOf(selectedItem.getItemId()));
                        bundle.putString("transparent", "1");
                        bundle.putString("animate", "0");
                        bundle.putString("vipType", "masterclass_vip");
                        if (getActivity() != null && canUpdateUi()) {
                            VipRnUtil.showSVIPSubscribeCheckStand(getActivity(), bundle,0, new Function2<Integer, String, Unit>() {
                                @Override
                                public Unit invoke(Integer code, String s) {
                                    if (code == VipPurchaseUtil.RESULT_SUC) {
                                        SVIPOpenDialogFragment.this.dismissAllowingStateLoss();
                                    } else {
                                        if (ConstantsOpenSdk.isDebug && !TextUtils.isEmpty(s)) {
                                            ToastManager.showToast(s);
                                        }
                                    }
                                    return null;
                                }
                            });
                        }
                        Logger.d(TAG, "bundle: " + bundle.toString());
                    } else {
                        // 单购
                        if (selectedItem.getContext() != null) {
                            JSONObject contextJson = new JSONObject();
                            if (SVIPOpenDialogFragment.this.mPayParams != null) {

                                if (selectedItem.getContext() != null) {
                                    contextJson.put("utmsource", selectedItem.getContext().getUtmsource());
                                    contextJson.put("pcreditNumInterest", selectedItem.getContext().getPcreditNumInterest());
                                    contextJson.put("masterclassTag", selectedItem.getContext().getMasterclassTag());
                                    contextJson.put("orderUpValue", selectedItem.getContext().getOrderUpValue());
                                    contextJson.put("orderUpType", selectedItem.getContext().getOrderUpType());
                                }
                                contextJson.put("utmsource", mPayParams.utmsource);
                                contextJson.put("orderUpType", mPayParams.orderUpType);
                                contextJson.put("orderUpValue", mPayParams.orderUpValue);
                                contextJson.put("orderSubType", mPayParams.orderSubType);
                                contextJson.put("orderSubValue", mPayParams.orderSubValue);
                                if (mPayParams.pcreditNumInterest != null) {
                                    contextJson.put("pcreditNumInterest", mPayParams.pcreditNumInterest);
                                }
                                contextJson.put("orderSourceType", 1);
                                contextJson.put("orderSourceValue", "masterClass");
                                contextJson.put("executionEnvType", 1);
                            }
                            String json = new Gson().toJson(selectedItem.getOrderItems());
                            jsonObject.put("orderItems", json);
                            jsonObject.put("appIdentifier", selectedItem.getAppIdentifier());
                            jsonObject.put("tradeType", 1);
                            jsonObject.put("domain", 1);
                            jsonObject.put("orderFrom", 1);
                            jsonObject.put("executionEnvType", 1);
                            jsonObject.put("context", contextJson);
                        }
                        if (!TextUtils.isEmpty(selectedItem.getReturnUrl())) {
                            jsonObject.put("returnUrl", selectedItem.getReturnUrl());
                        } else {
                            jsonObject.put("afterPaySuccess", "close_directly");
                        }
                        VipRnUtil.Companion.engageWithParamFromNet(jsonObject.toString(), jsonObject, new IVipGoodsCallBack() {
                            @Override
                            public void onResult(int code, @Nullable String msg) {
                            }
                        });
                        Logger.d(TAG, "jsonObject: " + jsonObject.toString());
                        SVIPOpenDialogFragment.this.dismissAllowingStateLoss();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        };
        if (isAgreementChecked()) {
            payRunuable.run();
        } else {
            startRuleShakeAnimation();
            ToastManager.showToast("请先阅读并勾选协议");
        }
    }

    private boolean isAgreementChecked() {
        if (mCbRule.getVisibility() != View.VISIBLE) {
            return true;
        }
        return mCbRule.isSelected();
    }

    private ValueAnimator mShakeAnimator = null;

    private void startRuleShakeAnimation() {
        if (mShakeAnimator == null) {
            double start = Math.PI * -2;
            double end = Math.PI * 2;
            mShakeAnimator = ValueAnimator.ofFloat((float) start, (float) end)
                    .setDuration(500);
            mShakeAnimator.setInterpolator(new LinearInterpolator());
            mShakeAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    float animatedvalue = (float) animation.getAnimatedValue();
                    double tx = BaseUtil.dp2px(getNoNullContext(), 12) * sin(animatedvalue);
                    mLlRuleContainer.setTranslationX(Math.round(tx));
                }
            });

        }
        mShakeAnimator.cancel();
        mShakeAnimator.start();
    }

    @Override
    public void onScreenWidthChanged(@NonNull Configuration config) {
        updateViewWhileSkuChanged(mSVIPSkuManager.getMSelectedSkuItem());
    }

    public static class PayParams {
        String utmsource;
        String orderUpType;
        String orderUpValue;
        String orderSubType;
        String orderSubValue;
        String pcreditNumInterest;
        String type;//note 发笔记跳转
        public String domain = "1";
        public String sceneType = "";

        public PayParams(String utmsource, String orderUpType, String orderUpValue,
                         String orderSubType, String orderSubValue, String pcreditNumInterest, String type) {
            this.utmsource = utmsource;
            this.orderUpType = orderUpType;
            this.orderUpValue = orderUpValue;
            this.orderSubType = orderSubType;
            this.orderSubValue = orderSubValue;
            this.pcreditNumInterest = pcreditNumInterest;
            this.type = type;
        }
    }

    private String getItemStr() {
        String item = "";
        if (mSVIPSkuManager.getMSelectedSkuItem() != null && mSVIPSkuManager.getMSelectedSkuItem().getProperties() != null) {
            item = mSVIPSkuManager.getMSelectedSkuItem().getProperties().getPurchaseButtonText();
        }
        return item;
    }

    private void traceAllMasterService() {
        // 会员购买半浮层-查看全部大师课服务  控件曝光
        String utmsource = mPayParams != null ? mPayParams.utmsource : "";
        String item = getItemStr();
        new XMTraceApi.Trace()
                .setMetaId(55580)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "")
                .put("utmsource", utmsource)
                .put("item", item)
                .put("trackId", mPayParams.orderSubValue + "")
                .put("albumId", mPayParams.orderUpValue + "")
                .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG)
                )
                .put(XmRequestIdManager.CONT_TYPE, "svip")
                .put(XmRequestIdManager.CONT_ID, "0")
                .createTrace();
    }

    private void traceClickAllMasterService() {
        // 会员购买半浮层-查看全部大师课服务  弹框控件点击
        String utmsource = mPayParams != null ? mPayParams.utmsource : "";
        String item = getItemStr();
        new XMTraceApi.Trace()
                .setMetaId(55581)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "")
                .put("utmsource", utmsource)
                .put("item", item)
                .put("albumId", mPayParams.orderUpValue + "")
                .put("trackId", mPayParams.orderSubValue + "")
                .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG)
                )
                .createTrace();
    }

    private void clickTrace() {
        String price = "";
        String originPrice = "";
        String itemId = "";
        if (mSVIPSkuManager != null && mSVIPSkuManager.getMSelectedSkuItem() != null) {
            price = mSVIPSkuManager.getMSelectedSkuItem().getPayPrice();
            originPrice = mSVIPSkuManager.getMSelectedSkuItem().getOriginPrice();
            itemId = mSVIPSkuManager.getMSelectedSkuItem().getItemId() + "";
        }
        // 会员购买半浮层-大师课购买按钮  弹框控件点击
        String utmsource = mPayParams != null ? mPayParams.utmsource : "";
        String item = getItemStr();
        new XMTraceApi.Trace()
                .setMetaId(55613)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "")
                .put("spuId", "")
                .put("utmsource", utmsource)
                .put("item", item)
                .put("price", price)
                .put("item_id",itemId)
                .put("originPrice", originPrice)
                .put("trackId", mPayParams.orderSubValue + "")
                .put("albumId", mPayParams.orderUpValue + "")
                .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG)
                )
                .createTrace();
    }

    private void traceRuleCheckBoxClick() {
        new XMTraceApi.Trace()
                .setMetaId(56515)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "")
                .put("trackId", mPayParams.orderSubValue + "")
                .put("albumId", "" + mPayParams.orderUpValue)
                .put("Item", isAgreementChecked() ? "选中" : "取消") // 记录对应点击后的状态
                .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG)
                )
                .createTrace();
    }

    /**
     * 曝光 会员购买半浮层-豆腐块
     */
    private void traceShowSkuItem(SVIPSkuItem item) {
        if (item == null) return;
        // 会员购买半浮层-豆腐块  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(31248)
                .setServiceId("slipPage")
                .put("templateId", "-1")
                .put("businessID", "-1")
                .put("item_id", item.getItemId() + "")
                .put("currPage", "")
                .put("price", item.getPayPrice())
                .put("originPrice", item.getOriginPrice())
                .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG)
                )
                .put(XmRequestIdManager.CONT_TYPE, "svipSku")
                .put(XmRequestIdManager.CONT_ID, "" + item.getItemId())
                .createTrace();
    }

    /**
     * 点击 会员购买半浮层-豆腐块
     */
    private void traceClickSkuItem(SVIPSkuItem item) {
        if (item == null) return;
        // 会员购买半浮层-豆腐块  弹框控件点击
        new XMTraceApi.Trace()
                .setMetaId(31247)
                .setServiceId("dialogClick")
                .put("templateId", "-1")
                .put("businessID", "-1")
                .put("item_id", item.getItemId() + "")
                .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_SVIP_OPEN_DIALOG)
                )
                .createTrace();
    }

}
