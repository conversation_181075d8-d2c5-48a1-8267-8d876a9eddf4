package com.ximalaya.ting.android.vip.manager.combine

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem.SkuDecorates
import com.ximalaya.ting.android.host.util.CountDownTimeUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.ui.DrawableUtil.GradientDrawableBuilder
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.vip.R
import com.ximalaya.ting.android.vip.manager.purchaseDialog.BaseVipSkuItemViewManager
import com.ximalaya.ting.android.vip.util.DialogUtil
import com.ximalaya.ting.android.vip.util.SpannableStringUtils
import com.ximalaya.ting.android.vip.util.VipBundleCommonUtil
import com.ximalaya.ting.android.vip.util.VipFragmentUtil
import com.ximalaya.ting.android.vip.view.VipExpandableContentTextView
import com.ximalaya.ting.android.vip.view.VipRunNumberTextView
import com.ximalaya.ting.android.xmutil.Logger
import java.math.BigDecimal
import kotlin.math.roundToInt

/**
 * Created by 5Greatest on 2021.05.08
 *
 * <AUTHOR>
 *   On 2021/5/8
 */
private val DP_90: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 90f)
private val DP_58: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 58f)
private val DP_10: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 10f)
private val DP_130: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 130f)
private val SKU_HEIGHT_UNSELECTED: Int =
    BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 175f)
public val SKU_HEIGHT: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 189f)
private val SKU_WIDTH_UNSELECTED: Int =
    BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 130f)
private val SKU_WIDTH: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 141f)
private val SKU_SCALE = 1.08f

//private const val DEFAULT_TITLE: String = "开通会员，可免费畅听本专辑"
private const val DEFAULT_TITLE: String = "畅听8w+专辑、4w+有声书、免音贴广告等特权"
private const val DEFAULT_SEE_MORE: String = "更多优惠商品"
private const val DEFAULT_CONFIRM_TEXT: String = "更多优惠商品"

val MARGIN_WIDTH: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), (8).toFloat())
val ITEM_WIDTH: Int = DP_130

abstract class CombineVipPurchaseItemManager(private val mPresenter: CombineVipPurchaseDataPresenter) :
    GoodsInfoProvider {

    /**
     * 获取要用来绘制内容的数据内容
     * */
    protected fun getTargetShelveItem(): VipSkuItem? {
        return mPresenter.chosenItem
    }

    /**
     * 绘制货架区域
     * */
    abstract fun bindDataOnShelve(shelveView: View?)

    /**
     * 绘制售卖文案区域
     * */
    protected fun bindDataOnDescription(descriptionView: View?) {
        ViewStatusUtil.setVisible(View.GONE, descriptionView)
        getTargetShelveItem()?.let { item ->
            val descriptionString: CharSequence? = item.getSalesRemarkTextNew()
            if (null != descriptionString && descriptionString.isNotEmpty()) {
                val descriptionTextView =
                    descriptionView?.findViewById<View>(R.id.vip_item_purchase_description_text)
                if (!(descriptionTextView is VipExpandableContentTextView)) {
                    return
                }

                ViewStatusUtil.setVisible(View.VISIBLE, descriptionView)
                descriptionTextView.setExpanded(false)
                descriptionTextView.movementMethod = LinkMovementMethod.getInstance()
                val boldTexIndices =
                    SpannableStringUtils.getBoldTextIndices(descriptionString.toString())
                val descriptionAfterRemoveAllBTags =
                    SpannableStringUtils.removeAllBTags(descriptionString)
                descriptionTextView.setContent(descriptionAfterRemoveAllBTags,
                    object : VipExpandableContentTextView.ITextIntercept {
                        val fItem: VipSkuItem? = item
                        override fun interceptSpannableStringBuilder(
                            isExpanded: Boolean,
                            stringBuilder: SpannableStringBuilder?
                        ): SpannableStringBuilder? {
                            return if (null == stringBuilder) {
                                null
                            } else {
                                val result = if (!boldTexIndices.isNullOrEmpty()) {
                                    SpannableStringUtils.removeBTagsAndSetBoldText(
                                        stringBuilder,
                                        boldTexIndices
                                    )
                                } else {
                                    stringBuilder
                                }
                                val presentDetail: VipSkuItem.VipPresentDetail? =
                                    VipBundleCommonUtil.safelyGetItemFromList(
                                        fItem?.presentDetails,
                                        0
                                    )
                                if (!StringUtil.isEmpty(presentDetail?.agreementName)
                                    && !StringUtil.isEmpty(presentDetail?.agreementUrl)
                                    && result.contains(VipSkuItem.TEXT_AGREEMENT_HEAD)
                                    && result.contains(VipSkuItem.TEXT_AGREEMENT_TAIL)
                                ) {
                                    var startIndex =
                                        result.lastIndexOf(VipSkuItem.TEXT_AGREEMENT_HEAD)
                                    if (0 <= startIndex) {
                                        startIndex += VipSkuItem.TEXT_AGREEMENT_HEAD.length
                                    }
                                    val endIndex =
                                        result.lastIndexOf(VipSkuItem.TEXT_AGREEMENT_TAIL)
                                    if (0 <= startIndex && 0 <= endIndex && endIndex > startIndex) {
                                        result.setSpan(object : ClickableSpan() {
                                            override fun onClick(widget: View) {
                                                VipFragmentUtil.StartOrJump.jumpToUrl(
                                                    mPresenter.getFragment(),
                                                    presentDetail?.agreementUrl,
                                                    widget
                                                )
                                            }

                                            override fun updateDrawState(ds: TextPaint) {
                                                super.updateDrawState(ds)
                                                ds.color =
                                                    BaseApplication.getMyApplicationContext().resources.getColor(
                                                        R.color.host_color_666666_888888
                                                    )
                                                ds.isUnderlineText = false
                                            }
                                        }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                                    }
                                }
                                result
                            }
                        }
                    })
            }
        }
    }

    /**
     * 绘制声明区域
     * */
    private fun bindDataOnClaim(claimView: View?) {
        ViewStatusUtil.setVisible(View.GONE, claimView)
        mPresenter.dialogModel?.disclaimer?.let { claim ->
            if (!StringUtil.isEmpty(claim.text)) {
                if (claimView is TextView) {
                    ViewStatusUtil.setVisible(View.VISIBLE, claimView)
                    val claimText: String = claim.text.replace("\\n", "\n")
                    ViewStatusUtil.setText(claimView, claimText)
                }
            }
            if (!StringUtil.isEmpty(claim.link)) {
                ViewStatusUtil.setOnClickListener(claimView, object : View.OnClickListener {
                    private val url: String? = claim.link
                    override fun onClick(v: View?) {
                        Util.handleUrl(url, v)
                    }
                })
            }
        }
    }

    override fun getCover(item: VipSkuItem?): String? {
        return item?.productBackground
    }

    override fun getGoodsTag(item: VipSkuItem?): String? {
        item?.subProduct?.properties?.label?.let {
            return it
        }
        return item?.properties?.label ?: item?.activityPopupDto?.label
    }

    override fun getGoodsName(item: VipSkuItem?): String? {
        item?.subProduct?.properties?.simpleName?.let {
            return it
        }
        var name: String? = item?.properties?.simpleName
        if (StringUtil.isEmpty(name)) {
            name = item?.name
        }
        return name
    }

    override fun getGoodsDescription(item: VipSkuItem?): String? {
        item?.subProduct?.description?.let {
            return it
        }
        return item?.description
    }

    override fun getGoodsNowPrice(item: VipSkuItem?): Double? {
        item?.subProduct?.unitPrice?.let {
            return it
        }
        return item?.unitPrice ?: (-1).toDouble()
    }

    override fun getDiscountPrice(item: VipSkuItem?, ignoreUseCoupon: Boolean): Double? {
        if (ignoreUseCoupon) {
            item?.subProduct?.discountPrice?.let {
                return it
            }
            return item?.discountPrice ?: (-1).toDouble()
        } else {
            item?.subProduct?.useCoupon?.let { useCoupon ->
                if (useCoupon) {
                    item.subProduct?.discountPrice?.let { discountPrice ->
                        return discountPrice
                    }
                }
            }
            if (true == item?.useCoupon) {
                return item.discountPrice ?: (-1).toDouble()
            }
            return (-1).toDouble()
        }
    }

    override fun getPromotionPrice(item: VipSkuItem?): Double? {
        item?.subProduct?.promotionPrice?.let {
            return it
        }
        return item?.promotionPrice ?: (-1).toDouble()
    }

    override fun getGoodsOriginPrice(item: VipSkuItem?): Double? {
        item?.subProduct?.properties?.marketingPrice?.let {
            return it
        }
        return item?.properties?.marketingPrice ?: (-1).toDouble()
    }

    override fun getGoodsLabel(item: VipSkuItem?): String? {
        item?.subProduct?.properties?.labelText?.let {
            return it
        }
        return item?.properties?.labelText
    }

    override fun getLabelText(item: VipSkuItem?): String? {
        val label = getGoodsLabel(item)
        if (!StringUtil.isEmpty(label)) {
            return label;
        }

        return getActivityPropertyLabel(item)
    }

    override fun getActivityPropertyLabel(item: VipSkuItem?): String? {
        return item?.activityProperty?.labelText ?: item?.activityPopupDto?.labelText
    }

    override fun getCanUseCoupon(item: VipSkuItem?): Boolean {
        val temp = item?.subProduct?.useCoupon ?: false
        if (temp) {
            return true
        }
        return item?.useCoupon ?: false
    }

    override fun isSpecialDiscount(item: VipSkuItem?): Boolean {
        return false
    }

    override fun isPurchaseFreeDay(item: VipSkuItem?): Boolean {
        return false
    }

    class Util {
        companion object {
            private fun parseColor(colorString: String?, defaultColor: Int): Int {
                colorString ?: return defaultColor
                return try {
                    Color.parseColor(colorString)
                } catch (e: Exception) {
                    e.printStackTrace()
                    defaultColor
                }
            }

            fun createPVipSkuItemView(
                context: Context,
                item: VipSkuItem,
                provider: GoodsInfoProvider,
                isSelected: Boolean,
                listener: BaseVipSkuItemViewManager.SkuItemClickListener,
                cssStyle: CssStyle
            ): View {
                val goodsItemView: View = LayoutInflater.from(context)
                    .inflate(R.layout.vip_item_combine_pvip_multi_sku_item, null)
                val layoutStyle = LayoutStyle.platinumSkuLayoutStyle()
                val layoutParams: LinearLayout.LayoutParams =
                    LinearLayout.LayoutParams(
                        if (isSelected)
                            layoutStyle.selectedWidth
                        else layoutStyle.width,
                        if (isSelected)
                            layoutStyle.selectedHeight
                        else layoutStyle.height
                    )
                goodsItemView.layoutParams = layoutParams
                return decoratePVipSkuItemView(
                    goodsItemView,
                    item,
                    provider,
                    isSelected,
                    listener,
                    cssStyle
                )
            }

            fun createNewSkuItemView(
                context: Context,
                item: VipSkuItem,
                provider: GoodsInfoProvider,
                isSelected: Boolean,
                listener: View.OnClickListener?
            ): View {
                val goodsItemView: View = LayoutInflater.from(context)
                    .inflate(R.layout.vip_item_combine_vip_multi_sku_item, null)
                val layoutParams: LinearLayout.LayoutParams =
                    LinearLayout.LayoutParams(
                        getTargetWidth(
                            isSelected,
                            item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true
                        ),
                        getTargetHeight(
                            isSelected,
                            item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true
                        )
                    )
                goodsItemView.layoutParams = layoutParams
                return decorateNewSkuItemView(goodsItemView, item, provider, isSelected, listener)
            }

            private fun decoratePVipSkuItemView(
                goodsItemView: View,
                item: VipSkuItem,
                provider: GoodsInfoProvider,
                isSelected: Boolean,
                listener: BaseVipSkuItemViewManager.SkuItemClickListener,
                cssStyle: CssStyle
            ): View {
                ViewStatusUtil.setTag(goodsItemView, R.id.vip_id_tag_contain_model, item)
                val skinBg: ImageView = goodsItemView.findViewById(R.id.vip_purchase_dialog_bg_skin)
                val tagTvContainer: LinearLayout =
                    goodsItemView.findViewById(R.id.vip_purchase_item_tag_high_priority_container)
                val tagIcon: ImageView =
                    goodsItemView.findViewById(R.id.vip_purchase_item_tag_high_priority_icon)
                val tagTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_tag_high_priority)
                val nowPriceTv: VipRunNumberTextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_2)
                val nowPriceTvIcon: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_icon)
                val priceSuffixTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_suffix)
                val mask: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_dialog_multi_mask)
                val name: String = provider.getGoodsName(item) ?: ""
                val nameTv: TextView? =
                    goodsItemView.findViewById<TextView>(R.id.vip_purchase_item_name)
                val originPriceTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_origin_price)
                val sellingPointBgView: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_selling_point_bg)
                val descriptionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_description)
                val promotionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_promotion)
                val sellingPointContainer: FrameLayout? =
                    goodsItemView.findViewById(R.id.vip_purchase_selling_point_container)
                val upgradeMonthTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_upgrade_month)
                val isPurchaseFreeDay = provider.isPurchaseFreeDay(item)
                val skuDecorates = item.skuDecorates
                var hasLabel = false
                var hasSkinBg = false
                var hasSellingBackground = false
                nameTv?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    13.dpFloat
                )
                nowPriceTvIcon?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    getTargetSkuPriceSymbolTextSize(true)
                )
                nowPriceTv?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    getTargetSkuPriceTextSize(true)
                )
                setDinTypeFaceSemiBold(goodsItemView.context, nowPriceTv)
                originPriceTv?.setPadding(
                    0,
                    0,
                    0,
                    8.dp
                )
                skuDecorates?.let {
                    // 文字角标的优先级大于图片角标
                    ViewStatusUtil.setVisible(View.GONE, tagTvContainer, tagIcon, tagTv)
                    it.subscriptText?.let { textLabel ->
                        if (!StringUtil.isEmpty(textLabel)) {
                            ViewStatusUtil.setText(tagTv, textLabel)
                            ViewStatusUtil.setVisible(View.VISIBLE, tagTvContainer, tagTv)
                            ViewStatusUtil.setTag(
                                tagTvContainer,
                                R.id.vip_id_tag_contain_model,
                                textLabel
                            )
                            hasLabel = true
                        }
                        if (hasLabel) {
                            if (!StringUtil.isEmpty(it.subscriptLogo)) {
                                ViewStatusUtil.setVisible(View.VISIBLE, tagIcon)
                                ImageManager.from(goodsItemView.context)
                                    .displayImage(tagIcon, it.subscriptLogo, -1)
                            } else {
                                ViewStatusUtil.setVisible(View.GONE, tagIcon)
                            }
                            tagTvContainer.background = GradientDrawableBuilder().cornerRadius(
                                4.dpFloat,
                                0f,
                                4.dpFloat,
                                4.dpFloat
                            )
                                .color(
                                    parseColor(
                                        it.subscriptBackgroundColor,
                                        0xFFFF4444.toInt()
                                    )
                                )
                                .build()
                            tagTv?.setTextColor(
                                parseColor(
                                    it.subscriptTextColor,
                                    0xFFFFFFFF.toInt()
                                )
                            )
                        }
                        tagTvContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
                            height = 17.dp
                        }
                        tagTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, 9.dpFloat)
                        tagIcon.updateLayoutParams<LinearLayout.LayoutParams> {
                            height = 9.dp
                        }
                    }
                    var skinUrl = it.backgroundImage
                    if (ConstantsOpenSdk.isDebug) {
                        val prop = ToolUtil.getSystemProperty("debug.shixin.skin", "-1");
                        if (!"-1".equals(prop)) {
                            skinUrl = prop
                        }
                    }
                    if (!StringUtil.isEmpty(skinUrl)) {
                        ImageManager.from(goodsItemView.context)
                            .displayImage(skinBg, skinUrl, -1)
                        hasSkinBg = true
                        ViewStatusUtil.setVisible(View.VISIBLE, skinBg)
                    } else {
                        ViewStatusUtil.setVisible(View.GONE, skinBg)
                    }
                    val sellPointBackgroundColor = try {
                        Color.parseColor(it.sellingPointBackgroundColor ?: "")
                    } catch (e: Exception) {
                        e.printStackTrace()
                        null
                    }
                    if (sellPointBackgroundColor != null) {
                        hasSellingBackground = true
                        descriptionTv?.background = GradientDrawableBuilder().cornerRadius(
                            0f,
                            8.dpFloat,
                            0f,
                            8.dpFloat
                        ).color(
                            sellPointBackgroundColor
                        ).build()
                        promotionTv?.background = GradientDrawableBuilder().cornerRadius(
                            0f,
                            8.dpFloat,
                            0f,
                            8.dpFloat
                        ).color(
                            sellPointBackgroundColor
                        ).build()
                    } else {
                        ViewStatusUtil.setBackgroundColorRes(
                            com.ximalaya.ting.android.host.R.color.host_color_translucent_00ffffff,
                            descriptionTv, promotionTv
                        )
                        it.sellingPointBackgroundColor = null
                    }
                    if (!hasLabel) {
                        val tagIv: View? =
                            goodsItemView.findViewById(R.id.vip_purchase_item_tag_low_priority)

                        if (tagIv is ImageView) {
                            val tag: String? = it.labelImg
                            tagIv.updateLayoutParams<FrameLayout.LayoutParams> {
                                height = 17.dp
                            }
                            if (!StringUtil.isEmpty(tag)) {
                                ImageManager.from(goodsItemView.context)
                                    .displayImage(tagIv, tag, -1)
                                ViewStatusUtil.setVisible(View.VISIBLE, tagIv)
                                ViewStatusUtil.setVisible(View.GONE, tagTvContainer)
                                ViewStatusUtil.setTag(
                                    tagTvContainer,
                                    R.id.vip_id_tag_contain_model,
                                    tag
                                )
                                hasLabel = true
                            } else {
                                ViewStatusUtil.setVisible(View.GONE, tagIv)
                            }
                        }
                    }
                }

                ViewStatusUtil.setText(nameTv, name)
                originPriceTv?.paint?.let {
                    it.flags = it.flags or Paint.STRIKE_THRU_TEXT_FLAG
                }
                var timeLimitedDiscountAmount: Double = 0.0
                when (item.skuDecorates.decorateType) {
                    SkuDecorates.SKU_DECORATE_TYPE_PURCHASE_FREE_DAY, SkuDecorates.SKU_DECORATE_TYPE_SPECIAL_DISCOUNT -> {

                        val strikePrice: Double =
                            provider.getGoodsOriginPrice(item) ?: (-1).toDouble()
                        val nowPrice: Double =
                            provider.getDiscountPrice(item, true) ?: (-1).toDouble()
                        val unitPrice: Double = provider.getGoodsNowPrice(item) ?: (-1).toDouble()
                        if (0 < strikePrice && 0 < nowPrice && nowPrice < strikePrice) {
                            ViewStatusUtil.setVisible(
                                View.VISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                            nowPriceTv?.setPendingNumbers(
                                getBigDecimalFromDouble(strikePrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            ViewStatusUtil.setText(
                                originPriceTv,
                                "￥" + StringUtil.subZeroAndDot(strikePrice) + " "
                            )
                            timeLimitedDiscountAmount = strikePrice - nowPrice
                        } else if (0 < nowPrice) {
                            ViewStatusUtil.setVisible(View.INVISIBLE, originPriceTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, nowPriceTv, nowPriceTvIcon)
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                        } else if (isPurchaseFreeDay && unitPrice > 0) {  //为了避免影响老逻辑，增加 isPurchaseFreeDay 判断
                            ViewStatusUtil.setVisible(
                                View.VISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                            if (strikePrice > 0) {
                                ViewStatusUtil.setText(
                                    originPriceTv,
                                    StringUtil.subZeroAndDot(strikePrice)
                                )
                            } else {
                                ViewStatusUtil.setVisible(View.INVISIBLE, originPriceTv)
                            }
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(unitPrice),
                                getBigDecimalFromDouble(unitPrice)
                            )
                        } else {
                            ViewStatusUtil.setVisible(
                                View.INVISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                        }

                        val description: String? = provider.getGoodsDescription(item)
                        val hasTimeLimit =
                            bindTimeLimitData(goodsItemView, item, isSelected, description)
                        if ((item.applyTimeLimited && !hasTimeLimit) && !StringUtil.isEmpty(
                                description
                            )
                        ) {
                            ViewStatusUtil.setText(descriptionTv, description)
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                        }
                        ViewStatusUtil.setVisible(View.VISIBLE, sellingPointBgView)
                        ViewStatusUtil.setBackgroundDrawableRes(
                            if (isSelected) cssStyle.descriptionSelectedBgDrawableId else cssStyle.descriptionBgDrawableId,
                            sellingPointBgView
                        )
                    }
                    else -> {
                        val originPrice: Double =
                            provider.getGoodsOriginPrice(item) ?: (-1).toDouble()
                        val description: String? = provider.getGoodsDescription(item)
                        val descriptionRelative = if (!StringUtil.isEmpty(description)) {
                            ViewStatusUtil.setText(descriptionTv, description)
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                            true
                        } else {
                            ViewStatusUtil.setVisible(View.GONE, descriptionTv)
                            false
                        }
                        val promotionPrice: Double =
                            provider.getPromotionPrice(item) ?: (-1).toDouble()
                        val useCoupon: Boolean = provider.getCanUseCoupon(item)
                        val promotionRelative: Boolean = if (0 < promotionPrice && useCoupon) {
                            ViewStatusUtil.setText(
                                promotionTv,
                                "用券已减${StringUtil.subZeroAndDot(promotionPrice)}元"
                            )
                            ViewStatusUtil.setVisible(View.VISIBLE, promotionTv)
                            true
                        } else {
                            ViewStatusUtil.setVisible(View.GONE, promotionTv)
                            false
                        }
                        val afterText = if (descriptionRelative) description
                        else if (promotionRelative) "用券已减${StringUtil.subZeroAndDot(promotionPrice)}元"
                        else ""
                        val hasTimeLimit =
                            bindTimeLimitData(goodsItemView, item, isSelected, afterText)
                        ViewStatusUtil.setVisible(View.VISIBLE, sellingPointBgView)
                        ViewStatusUtil.setBackgroundDrawableRes(
                            if (isSelected) cssStyle.descriptionSelectedBgDrawableId else cssStyle.descriptionBgDrawableId,
                            sellingPointBgView
                        )
                        if (hasTimeLimit) {
                            ViewStatusUtil.setVisible(View.GONE, promotionTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                        } else if (promotionRelative) {
                            ViewStatusUtil.setVisible(View.GONE, descriptionTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, promotionTv)
                        } else if (descriptionRelative) {
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                            ViewStatusUtil.setVisible(View.GONE, promotionTv)
                        } else {
                            ViewStatusUtil.setVisible(
                                View.GONE,
                                sellingPointBgView,
                                promotionTv,
                                descriptionTv
                            )
                        }
                        val nowPrice: Double = if (promotionRelative) {
                            // 使用优惠券的情况
                            val temp: Double =
                                provider.getDiscountPrice(item, false) ?: (-1).toDouble()
                            if (0 > temp) {
                                provider.getGoodsNowPrice(item) ?: (-1).toDouble()
                            } else {
                                temp
                            }
                        } else {
                            // 不使用优惠券的情况
                            provider.getGoodsNowPrice(item) ?: (-1).toDouble()
                        }
                        if (0 < originPrice && 0 < nowPrice && nowPrice < originPrice) {
                            ViewStatusUtil.setVisible(
                                View.VISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                            nowPriceTv?.setPendingNumbers(
                                getBigDecimalFromDouble(originPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            ViewStatusUtil.setText(
                                originPriceTv,
                                "￥" + StringUtil.subZeroAndDot(originPrice) + " "
                            )
                            timeLimitedDiscountAmount = originPrice - nowPrice
                        } else if (0 < nowPrice) {
                            ViewStatusUtil.setVisible(View.INVISIBLE, originPriceTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, nowPriceTv, nowPriceTvIcon)
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                        } else {
                            ViewStatusUtil.setVisible(
                                View.INVISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                        }
                    }
                }
                if (!item.upgradeProductVos.isNullOrEmpty()) {
                    listener.onSkuUpgradeMonthViewShow()
                    if (isSelected) {
                        item.upgradeProductVos?.firstOrNull()?.let {
                            ViewStatusUtil.setText(
                                upgradeMonthTv,
                                "${it.months ?: 1}个月"
                            )
                            nowPriceTv?.text = it.monthlyPrice
                        }
                        ViewStatusUtil.setVisible(View.VISIBLE, upgradeMonthTv)
                    } else {
                        ViewStatusUtil.setVisible(View.INVISIBLE, upgradeMonthTv)
                    }
                    ViewStatusUtil.setVisible(View.VISIBLE, priceSuffixTv)
                    ViewStatusUtil.setText(priceSuffixTv, "/月")
                    upgradeMonthTv?.setOnClickListener {
                        listener.onSkuUpgradeMonthClick(it)
                    }
                    ViewStatusUtil.setVisible(View.INVISIBLE, sellingPointContainer)
                } else {
                    ViewStatusUtil.setVisible(View.GONE, priceSuffixTv)
                    ViewStatusUtil.setVisible(View.VISIBLE, sellingPointContainer)
                    ViewStatusUtil.setVisible(View.INVISIBLE, upgradeMonthTv)
                }

                val moneySavedTagArea: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                ViewStatusUtil.setVisible(View.INVISIBLE, moneySavedTagArea)
//                val savedMoneyAmount: Double =
//                    item.timeLimitedAmount + (if (0 < item.discountAmount) item.discountAmount else 0.0)
                val moneySaveTagImageView =
                    moneySavedTagArea?.findViewById<ImageView>(R.id.vip_purchase_item_time_limited_discount_bg)
                val debugSwitch =
                    ToolUtil.getDebugSystemProperty("debug.mark.red_packet", "0") != "0"
                if (((item.applyTimeLimited || item.isEnvelopeActivityProduct) && 0 < timeLimitedDiscountAmount) || debugSwitch) {
                    moneySavedTagArea?.setTag(
                        R.id.vip_id_tag_contain_model,
                        timeLimitedDiscountAmount
                    )
                    val moneySavedText: String =
                        "立减\n¥${StringUtil.subZeroAndDot(timeLimitedDiscountAmount)}"
                    ViewStatusUtil.setVisible(View.VISIBLE, moneySavedTagArea)
                    val moneySaveTagTextView =
                        moneySavedTagArea?.findViewById<TextView>(R.id.vip_purchase_item_time_limited_discount_value)
                    ViewStatusUtil.setText(moneySaveTagTextView, moneySavedText)
                    ViewStatusUtil.setTag(
                        moneySavedTagArea,
                        R.id.vip_id_tag_contain_model,
                        moneySavedText
                    )
                    var discountBgResId = R.drawable.vip_ic_time_limited_discount_bg_new
                    var textColor = Color.parseColor("#929292")
                    if (isSelected) {
                        discountBgResId =
                            R.drawable.vip_ic_time_limited_discount_bg_new_selected
                        textColor = Color.parseColor("#B90C0C")
                    }
                    ViewStatusUtil.setImageRes(moneySaveTagImageView, discountBgResId)
                    ViewStatusUtil.setTextColor(moneySaveTagTextView, textColor)
                    moneySavedTagArea?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        width = 40.dp
                        height = 40.dp
                    }
                    moneySaveTagImageView?.updateLayoutParams<FrameLayout.LayoutParams> {
                        width = 53.dp
                        height = 53.dp
                    }
                    moneySaveTagTextView?.updateLayoutParams<FrameLayout.LayoutParams> {
                        width = 40.dp
                        height = 40.dp
                        topMargin = -(5.dp)
                        leftMargin = BaseUtil.dp2px(goodsItemView.context, 3.5f)
                    }
                    moneySaveTagTextView?.setTextSize(
                        TypedValue.COMPLEX_UNIT_PX, 9.dpFloat
                    )
                } else {
                    ViewStatusUtil.setVisible(View.INVISIBLE, moneySavedTagArea)
                }

                //文字选中时的颜色，大师课、有背景、无背景时不一样
                if (hasSkinBg) {
                    ViewStatusUtil.setAlphaWithFloat(skinBg, if (isSelected) 1f else 0.15f)
                    ViewStatusUtil.setVisible(View.GONE, mask)
                } else {
                    ViewStatusUtil.setBackgroundDrawableRes(cssStyle.selectedBgDrawableId, mask)
                    ViewStatusUtil.setVisible(if (isSelected) View.VISIBLE else View.GONE, mask)
                }

                ViewStatusUtil.setTextColor(
                    Color.parseColor(
                        if (isSelected) {
                            if (hasSkinBg) "#292B33" else cssStyle.textSelectedColor
                        } else (if (BaseFragmentActivity2.sIsDarkMode) "#ffffff" else cssStyle.textColor)
                    ),
                    nameTv, nowPriceTv, nowPriceTvIcon, priceSuffixTv
                )
                sellingPointBgView?.updateLayoutParams<FrameLayout.LayoutParams> {
                    height = 26.dp
                }
                descriptionTv?.updateLayoutParams<FrameLayout.LayoutParams> {
                    height = 26.dp
                }
                promotionTv?.updateLayoutParams<FrameLayout.LayoutParams> {
                    height = 26.dp
                }
                descriptionTv?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX, 13.dpFloat
                )
                promotionTv?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX, 13.dpFloat
                )
                if (hasSellingBackground) {
                    setSellingPointViewAlpha(descriptionTv, if (isSelected) 1f else 0.08f)
                    setSellingPointViewAlpha(promotionTv, if (isSelected) 1f else 0.08f)
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8f8f8f")
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8f8f8f")
                    )
                } else {
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(
                            if (isSelected) cssStyle.textSelectedColor else {
                                if (BaseFragmentActivity2.sIsDarkMode) "#8f8f8f" else "#802C2C3C"
                            }
                        )
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(
                            if (isSelected) cssStyle.textSelectedColor else {
                                if (BaseFragmentActivity2.sIsDarkMode) "#8f8f8f" else "#802C2C3C"
                            }
                        )
                    )
                }
                ViewStatusUtil.setOnClickListener(goodsItemView, listener)

                return goodsItemView
            }

            private fun decorateNewSkuItemView(
                goodsItemView: View,
                item: VipSkuItem,
                provider: GoodsInfoProvider,
                isSelected: Boolean,
                listener: View.OnClickListener?
            ): View {
                ViewStatusUtil.setTag(goodsItemView, R.id.vip_id_tag_contain_model, item)
                val skinBg: ImageView = goodsItemView.findViewById(R.id.vip_purchase_dialog_bg_skin)
                val tagTvContainer: LinearLayout =
                    goodsItemView.findViewById(R.id.vip_purchase_item_tag_high_priority_container)
                val tagIcon: ImageView =
                    goodsItemView.findViewById(R.id.vip_purchase_item_tag_high_priority_icon)
                val tagTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_tag_high_priority)
                val nowPriceTv: VipRunNumberTextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_2)
                val nowPriceTvIcon: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_icon)
                val priceSuffixTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_suffix)
                val mask: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_dialog_multi_mask)
                val name: String = provider.getGoodsName(item) ?: ""
                val nameTv: TextView? =
                    goodsItemView.findViewById<TextView>(R.id.vip_purchase_item_name)
                val originPriceTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_origin_price)
                val sellingPointBgView: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_selling_point_bg)
                val descriptionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_description)
                val promotionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_promotion)
                val isPurchaseFreeDay = provider.isPurchaseFreeDay(item)
                val skuDecorates = item.skuDecorates
                var hasLabel = false
                var hasSkinBg = false
                var hasSellingBackground = false
                nameTv?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    getTargetSkuNameTextSize(item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true)
                )
                nowPriceTvIcon?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    getTargetSkuPriceSymbolTextSize(item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true)
                )
                nowPriceTv?.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    getTargetSkuPriceTextSize(item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true)
                )
                originPriceTv?.setPadding(
                    0,
                    0,
                    0,
                    if (item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true) 8.dp else 0
                )
                skuDecorates?.let {
                    // 文字角标的优先级大于图片角标
                    ViewStatusUtil.setVisible(View.GONE, tagTvContainer, tagIcon, tagTv)
                    it.subscriptText?.let { textLabel ->
                        if (!StringUtil.isEmpty(textLabel)) {
                            ViewStatusUtil.setText(tagTv, textLabel)
                            ViewStatusUtil.setVisible(View.VISIBLE, tagTvContainer, tagTv)
                            ViewStatusUtil.setTag(
                                tagTvContainer,
                                R.id.vip_id_tag_contain_model,
                                textLabel
                            )
                            hasLabel = true
                        }
                        if (hasLabel) {
                            if (!StringUtil.isEmpty(it.subscriptLogo)) {
                                ViewStatusUtil.setVisible(View.VISIBLE, tagIcon)
                                ImageManager.from(goodsItemView.context)
                                    .displayImage(tagIcon, it.subscriptLogo, -1)
                            } else {
                                ViewStatusUtil.setVisible(View.GONE, tagIcon)
                            }
                            tagTvContainer.background = GradientDrawableBuilder().cornerRadius(
                                4.dpFloat,
                                0f,
                                4.dpFloat,
                                4.dpFloat
                            )
                                .color(
                                    parseColor(
                                        it.subscriptBackgroundColor,
                                        0xFFFF4444.toInt()
                                    )
                                )
                                .build()
                            tagTv?.setTextColor(
                                parseColor(
                                    it.subscriptTextColor,
                                    0xFFFFFFFF.toInt()
                                )
                            )
                        }
                        if (item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true) {
                            tagTvContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
                                height = 17.dp
                            }
                            tagTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, 9.dpFloat)
                            tagIcon.updateLayoutParams<LinearLayout.LayoutParams> {
                                height = 9.dp
                            }
                        } else {
                            tagTvContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
                                height = 18.dp
                            }
                            tagTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, 10.dpFloat)
                            tagIcon.updateLayoutParams<LinearLayout.LayoutParams> {
                                height = 10.dp
                            }
                        }

                    }
                    var skinUrl = it.backgroundImage
                    if (ConstantsOpenSdk.isDebug) {
                        val prop = ToolUtil.getSystemProperty("debug.shixin.skin", "-1");
                        if (!"-1".equals(prop)) {
                            skinUrl = prop
                        }
                    }
                    if (!StringUtil.isEmpty(skinUrl)) {
                        ImageManager.from(goodsItemView.context)
                            .displayImage(skinBg, skinUrl, -1)
                        hasSkinBg = true
                        ViewStatusUtil.setVisible(View.VISIBLE, skinBg)
                    } else {
                        ViewStatusUtil.setVisible(View.GONE, skinBg)
                    }
                    val sellPointBackgroundColor = try {
                        Color.parseColor(it.sellingPointBackgroundColor ?: "")
                    } catch (e: Exception) {
                        e.printStackTrace()
                        null
                    }
                    if (sellPointBackgroundColor != null) {
                        hasSellingBackground = true
                        descriptionTv?.background = GradientDrawableBuilder().cornerRadius(
                            0f,
                            8.dpFloat,
                            0f,
                            8.dpFloat
                        ).color(
                            sellPointBackgroundColor
                        ).build()
                        promotionTv?.background = GradientDrawableBuilder().cornerRadius(
                            0f,
                            8.dpFloat,
                            0f,
                            8.dpFloat
                        ).color(
                            sellPointBackgroundColor
                        ).build()
                    } else {
                        ViewStatusUtil.setBackgroundColorRes(
                            com.ximalaya.ting.android.host.R.color.host_color_translucent_00ffffff,
                            descriptionTv, promotionTv
                        )
                        it.sellingPointBackgroundColor = null
                    }
                    if (!hasLabel) {
                        val tagIv: View? =
                            goodsItemView.findViewById(R.id.vip_purchase_item_tag_low_priority)

                        if (tagIv is ImageView) {
                            val tag: String? = it.labelImg
                            if (item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true) {
                                tagIv.updateLayoutParams<FrameLayout.LayoutParams> {
                                    height = 17.dp
                                }
                            } else {
                                tagIv.updateLayoutParams<FrameLayout.LayoutParams> {
                                    height = 18.dp
                                }
                            }
                            if (!StringUtil.isEmpty(tag)) {
                                if (item.isSVIPItem) {
                                    //svip 的图片特殊处理
                                    setTagForSVIP(goodsItemView.context, tagIv, tag)
                                } else {
                                    ImageManager.from(goodsItemView.context)
                                        .displayImage(tagIv, tag, -1)
                                    ViewStatusUtil.setVisible(View.VISIBLE, tagIv)
                                }
                                ViewStatusUtil.setVisible(View.GONE, tagTvContainer)
                                ViewStatusUtil.setTag(
                                    tagTvContainer,
                                    R.id.vip_id_tag_contain_model,
                                    tag
                                )
                                hasLabel = true
                            } else {
                                ViewStatusUtil.setVisible(View.GONE, tagIv)
                            }
                        }
                    }
                }

                ViewStatusUtil.setText(nameTv, name)
                originPriceTv?.paint?.let {
                    it.flags = it.flags or Paint.STRIKE_THRU_TEXT_FLAG
                }
                var timeLimitedDiscountAmount: Double = 0.0
                when (item.skuDecorates.decorateType) {
                    SkuDecorates.SKU_DECORATE_TYPE_PURCHASE_FREE_DAY, SkuDecorates.SKU_DECORATE_TYPE_SPECIAL_DISCOUNT -> {

                        val strikePrice: Double =
                            provider.getGoodsOriginPrice(item) ?: (-1).toDouble()
                        val nowPrice: Double =
                            provider.getDiscountPrice(item, true) ?: (-1).toDouble()
                        val unitPrice: Double = provider.getGoodsNowPrice(item) ?: (-1).toDouble()
                        if (0 < strikePrice && 0 < nowPrice && nowPrice < strikePrice) {
                            ViewStatusUtil.setVisible(
                                View.VISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                            nowPriceTv?.setPendingNumbers(
                                getBigDecimalFromDouble(strikePrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            ViewStatusUtil.setText(
                                originPriceTv,
                                "￥" + StringUtil.subZeroAndDot(strikePrice) + " "
                            )
                            timeLimitedDiscountAmount = strikePrice - nowPrice
                        } else if (0 < nowPrice) {
                            ViewStatusUtil.setVisible(View.INVISIBLE, originPriceTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, nowPriceTv, nowPriceTvIcon)
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                        } else if (isPurchaseFreeDay && unitPrice > 0) {  //为了避免影响老逻辑，增加 isPurchaseFreeDay 判断
                            ViewStatusUtil.setVisible(
                                View.VISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                            if (strikePrice > 0) {
                                ViewStatusUtil.setText(
                                    originPriceTv,
                                    StringUtil.subZeroAndDot(strikePrice)
                                )
                            } else {
                                ViewStatusUtil.setVisible(View.INVISIBLE, originPriceTv)
                            }
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(unitPrice),
                                getBigDecimalFromDouble(unitPrice)
                            )
                        } else {
                            ViewStatusUtil.setVisible(
                                View.INVISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                        }
                        val description: String? = provider.getGoodsDescription(item)
                        val hasTimeLimit =
                            bindTimeLimitData(goodsItemView, item, isSelected, description)
                        if ((item.applyTimeLimited && !hasTimeLimit) && !StringUtil.isEmpty(
                                description
                            )
                        ) {
                            ViewStatusUtil.setText(descriptionTv, description)
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                        }

                        ViewStatusUtil.setVisible(View.VISIBLE, sellingPointBgView)
                        if (item.isSVIPItem) {
                            ViewStatusUtil.setBackgroundDrawableRes(
                                if (isSelected) R.drawable.vip_bag_rect_dce2f1_radius_8 else R.drawable.vip_bg_bottom_svip_sku_unselected,
                                sellingPointBgView
                            )
                        } else {
                            ViewStatusUtil.setBackgroundDrawableRes(
                                if (isSelected) R.drawable.vip_bag_rect_fffed9c7_radius_8 else R.drawable.vip_bg_bottom_sku_limit_tip,
                                sellingPointBgView
                            )
                        }
                    }
                    else -> {
                        val originPrice: Double =
                            provider.getGoodsOriginPrice(item) ?: (-1).toDouble()
                        val description: String? = provider.getGoodsDescription(item)
                        val descriptionRelative = if (!StringUtil.isEmpty(description)) {
                            ViewStatusUtil.setText(descriptionTv, description)
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                            true
                        } else {
                            ViewStatusUtil.setVisible(View.GONE, descriptionTv)
                            false
                        }
                        val promotionPrice: Double =
                            provider.getPromotionPrice(item) ?: (-1).toDouble()
                        val useCoupon: Boolean = provider.getCanUseCoupon(item)
                        val promotionRelative: Boolean = if (0 < promotionPrice && useCoupon) {
                            ViewStatusUtil.setText(
                                promotionTv,
                                "用券已减${StringUtil.subZeroAndDot(promotionPrice)}元"
                            )
                            ViewStatusUtil.setVisible(View.VISIBLE, promotionTv)
                            true
                        } else {
                            ViewStatusUtil.setVisible(View.GONE, promotionTv)
                            false
                        }
                        val afterText = if (descriptionRelative) description
                        else if (promotionRelative) "用券已减${StringUtil.subZeroAndDot(promotionPrice)}元"
                        else ""
                        val hasTimeLimit =
                            bindTimeLimitData(goodsItemView, item, isSelected, afterText)
                        ViewStatusUtil.setVisible(View.VISIBLE, sellingPointBgView)
                        if (item.isSVIPItem) {
                            ViewStatusUtil.setBackgroundDrawableRes(
                                if (isSelected) R.drawable.vip_bag_rect_dce2f1_radius_8 else R.drawable.vip_bg_bottom_svip_sku_unselected,
                                sellingPointBgView
                            )
                        } else {
                            ViewStatusUtil.setBackgroundDrawableRes(
                                if (isSelected) R.drawable.vip_bag_rect_fffed9c7_radius_8 else R.drawable.vip_bg_bottom_sku_limit_tip,
                                sellingPointBgView
                            )
                        }
                        if (hasTimeLimit) {
                            ViewStatusUtil.setVisible(View.GONE, promotionTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                        } else if (promotionRelative) {
                            ViewStatusUtil.setVisible(View.GONE, descriptionTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, promotionTv)
                        } else if (descriptionRelative) {
                            ViewStatusUtil.setVisible(View.VISIBLE, descriptionTv)
                            ViewStatusUtil.setVisible(View.GONE, promotionTv)
                        } else {
                            ViewStatusUtil.setVisible(
                                View.GONE,
                                sellingPointBgView,
                                promotionTv,
                                descriptionTv
                            )
                        }
                        val nowPrice: Double = if (promotionRelative) {
                            // 使用优惠券的情况
                            val temp: Double =
                                provider.getDiscountPrice(item, false) ?: (-1).toDouble()
                            if (0 > temp) {
                                provider.getGoodsNowPrice(item) ?: (-1).toDouble()
                            } else {
                                temp
                            }
                        } else {
                            // 不使用优惠券的情况
                            provider.getGoodsNowPrice(item) ?: (-1).toDouble()
                        }
                        if (0 < originPrice && 0 < nowPrice && nowPrice < originPrice) {
                            ViewStatusUtil.setVisible(
                                View.VISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                            nowPriceTv?.setPendingNumbers(
                                getBigDecimalFromDouble(originPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                            ViewStatusUtil.setText(
                                originPriceTv,
                                "￥" + StringUtil.subZeroAndDot(originPrice) + " "
                            )
                            timeLimitedDiscountAmount = originPrice - nowPrice
                        } else if (0 < nowPrice) {
                            ViewStatusUtil.setVisible(View.INVISIBLE, originPriceTv)
                            ViewStatusUtil.setVisible(View.VISIBLE, nowPriceTv, nowPriceTvIcon)
                            nowPriceTv?.startAnimator(
                                getBigDecimalFromDouble(nowPrice),
                                getBigDecimalFromDouble(nowPrice)
                            )
                        } else {
                            ViewStatusUtil.setVisible(
                                View.INVISIBLE,
                                originPriceTv,
                                nowPriceTv,
                                nowPriceTvIcon
                            )
                        }
                    }
                }

                val moneySavedTagArea: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                ViewStatusUtil.setVisible(View.INVISIBLE, moneySavedTagArea)
//                val savedMoneyAmount: Double =
//                    item.timeLimitedAmount + (if (0 < item.discountAmount) item.discountAmount else 0.0)
                val moneySaveTagImageView =
                    moneySavedTagArea?.findViewById<ImageView>(R.id.vip_purchase_item_time_limited_discount_bg)
                val debugSwitch =
                    ToolUtil.getDebugSystemProperty("debug.mark.red_packet", "0") == "1"
                if (((item.applyTimeLimited || item.isEnvelopeActivityProduct) && 0 < timeLimitedDiscountAmount) || debugSwitch) {
                    moneySavedTagArea?.setTag(
                        R.id.vip_id_tag_contain_model,
                        timeLimitedDiscountAmount
                    )
                    val moneySavedText: String =
                        "立减\n¥${StringUtil.subZeroAndDot(timeLimitedDiscountAmount)}"
                    ViewStatusUtil.setVisible(View.VISIBLE, moneySavedTagArea)
                    val moneySaveTagTextView =
                        moneySavedTagArea?.findViewById<TextView>(R.id.vip_purchase_item_time_limited_discount_value)
                    ViewStatusUtil.setText(moneySaveTagTextView, moneySavedText)
                    ViewStatusUtil.setTag(
                        moneySavedTagArea,
                        R.id.vip_id_tag_contain_model,
                        moneySavedText
                    )
                    var discountBgResId = R.drawable.vip_ic_time_limited_discount_bg_new
                    var textColor = Color.parseColor("#929292")
                    if (isSelected) {
                        discountBgResId =
                            R.drawable.vip_ic_time_limited_discount_bg_new_selected
                        textColor = Color.parseColor("#B90C0C")
                    }
                    ViewStatusUtil.setImageRes(moneySaveTagImageView, discountBgResId)
                    ViewStatusUtil.setTextColor(moneySaveTagTextView, textColor)
                    if (item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true) {
                        moneySavedTagArea?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                            width = 40.dp
                            height = 40.dp
                        }
                        moneySaveTagImageView?.updateLayoutParams<FrameLayout.LayoutParams> {
                            width = 53.dp
                            height = 53.dp
                        }
                        moneySaveTagTextView?.updateLayoutParams<FrameLayout.LayoutParams> {
                            width = 40.dp
                            height = 40.dp
                            topMargin = -(5.dp)
                            leftMargin = BaseUtil.dp2px(goodsItemView.context, 3.5f)
                        }
                        moneySaveTagTextView?.setTextSize(
                            TypedValue.COMPLEX_UNIT_PX, 9.dpFloat
                        )
                    } else {
                        moneySavedTagArea?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                            width = 44.dp
                            height = 44.dp
                        }
                        moneySaveTagImageView?.updateLayoutParams<FrameLayout.LayoutParams> {
                            width = 57.dp
                            height = 57.dp
                        }
                        moneySaveTagTextView?.updateLayoutParams<FrameLayout.LayoutParams> {
                            width = 44.dp
                            height = 44.dp
                            topMargin = -(4.dp)
                            leftMargin = BaseUtil.dp2px(goodsItemView.context, 2.5f)
                        }
                        moneySaveTagTextView?.setTextSize(
                            TypedValue.COMPLEX_UNIT_PX, 10.dpFloat
                        )
                    }
                } else {
                    ViewStatusUtil.setVisible(View.INVISIBLE, moneySavedTagArea)
                }

                //文字选中时的颜色，大师课、有背景、无背景时不一样
                var textSelectedColor = "#461717"
                if (item.isSVIPItem) {
                    //SVIP 有一个"起"字，价格的文字颜色也不一样
                    ViewStatusUtil.setVisible(View.VISIBLE, priceSuffixTv)

                    textSelectedColor = "#292B33";
                } else {
                    ViewStatusUtil.setVisible(View.GONE, priceSuffixTv)

                    if (hasSkinBg) {
                        textSelectedColor = "#292B33"
                    }
                }
                if (hasSkinBg) {
                    ViewStatusUtil.setAlphaWithFloat(skinBg, if (isSelected) 1f else 0.3f)
                    ViewStatusUtil.setVisible(View.GONE, mask)
                } else {
                    if (isSelected) {
                        ViewStatusUtil.setVisible(View.VISIBLE, mask)
                    }
                }

                ViewStatusUtil.setTextColor(
                    Color.parseColor(if (isSelected) textSelectedColor else (if (BaseFragmentActivity2.sIsDarkMode) "#ffffff" else "#292B33")),
                    nameTv, nowPriceTv, nowPriceTvIcon, priceSuffixTv
                )
                if (item.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify == true) {
                    sellingPointBgView?.updateLayoutParams<FrameLayout.LayoutParams> {
                        height = 26.dp
                    }
                    descriptionTv?.updateLayoutParams<FrameLayout.LayoutParams> {
                        height = 26.dp
                    }
                    promotionTv?.updateLayoutParams<FrameLayout.LayoutParams> {
                        height = 26.dp
                    }
                    descriptionTv?.setTextSize(
                        TypedValue.COMPLEX_UNIT_PX, 13.dpFloat
                    )
                    promotionTv?.setTextSize(
                        TypedValue.COMPLEX_UNIT_PX, 13.dpFloat
                    )
                }
                if (hasSellingBackground || item?.isSVIPItem == true) {
                    setSellingPointViewAlpha(descriptionTv, if (isSelected) 1f else 0.3f)
                    setSellingPointViewAlpha(promotionTv, if (isSelected) 1f else 0.3f)
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8F8F8F")
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8F8F8F")
                    )
                } else {
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(if (isSelected) "#461717" else "#8F8F8F")
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(if (isSelected) "#461717" else "#8F8F8F")
                    )
                }
                ViewStatusUtil.setOnClickListener(goodsItemView, listener)

                return goodsItemView
            }

            private fun setTagForSVIP(context: Context?, tagIv: ImageView, tagUrl: String?) {
                context ?: return
                ImageManager.from(context)
                    .downloadBitmap(tagUrl, object : ImageManager.DisplayCallback {
                        override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
                            bitmap ?: return

                            Logger.d(
                                "z_svip", "setTagForSVIP , url: " + tagUrl
                                        + ", width: " + bitmap.width + ", height: " + bitmap.height
                            )

                            if (bitmap.height == 0) {
                                return
                            }
                            //固定高度，计算宽度
                            val radio = bitmap.width * 1.0f / bitmap.height
                            val height = BaseUtil.dp2px(context, 18f)
                            val width = height * radio

                            tagIv.layoutParams.width = width.toInt()
                            tagIv.setImageBitmap(bitmap)
                            ViewStatusUtil.setVisible(View.VISIBLE, tagIv)
                        }
                    })
            }

            private fun setSellingPointViewAlpha(sellingPointView: TextView?, alpha: Float) {
                sellingPointView?.background ?: return
                if (alpha in 0.0..1.0) {
                    sellingPointView.background.mutate().alpha = (alpha * 255).toInt()
                }
            }

            private fun getBigDecimalFromDouble(value: Double): BigDecimal {
                val result =
                    if (0f == (value % 1).toFloat()) BigDecimal(value.toInt()) else BigDecimal(value.toString())
                return result
            }

            private fun bindTimeLimitData(
                goodsItemView: View, item: VipSkuItem,
                isSelected: Boolean, afterText: String?
            ): Boolean {
                //倒计时
                val expireTime = item.timeLimitedExpireTime
                var timeLeft = CountDownTimeUtil.getRealCountDownDuration(
                    (expireTime - item.localStartTimeOnDataGot),
                    item.localBaseTimeStamp
                )

                if (timeLeft <= 0) {
                    timeLeft = item.purchaseFreeDayCountDown()
                }
                if (timeLeft <= 0) {
                    timeLeft = item.activityPurchaseFreeDayTimeLimitGetCountDown()
                }

                val descriptionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_description)

                if (descriptionTv != null && timeLeft > 0) {
                    descriptionTv.visibility = View.VISIBLE
                    descriptionTv.setPadding(2.dp, 0, 2.dp, 0)

                    val countDown: CombineVipPurchaseMultiManager.TimeLimitedDiscountCountDown =
                        CombineVipPurchaseMultiManager.TimeLimitedDiscountCountDown(
                            timeLeft,
                            afterText,
                            descriptionTv
                        )
                    countDown.setTimeInfo(timeLeft, descriptionTv)
                    countDown.start()

                    updateCountDownStyle(item, goodsItemView, isSelected);
                    return true
                } else {
                    //隐藏倒计时
                    return false
                }
            }

            /**
             * 修改底部倒计时选中与未选中的样式，区别点：文字颜色
             */
            private fun updateCountDownStyle(
                item: VipSkuItem?,
                goodsItemView: View,
                selected: Boolean
            ) {
                item ?: return

                val expireTime = item.timeLimitedExpireTime
                var timeLeft = CountDownTimeUtil.getRealCountDownDuration(
                    (expireTime - item.localStartTimeOnDataGot),
                    item.localBaseTimeStamp
                )

                if (timeLeft <= 0) {
                    timeLeft = item.purchaseFreeDayCountDown()
                }
                if (timeLeft <= 0) {
                    //无倒计
                    return
                }

                goodsItemView ?: return

                val descriptionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_description)
                descriptionTv ?: return
                //根据是否选中，修改倒计时文字颜色

                var color = Color.parseColor("#8F8F8F")
                if (selected) {
                    color = Color.parseColor("#292B33")
                }
                descriptionTv.setTextColor(color)
            }

            fun decorateSelectedPVipSkuItemView(
                goodsItemView: View,
                item: VipSkuItem?,
                isSelected: Boolean,
                cssStyle: CssStyle,
                upgradeMonthPair: Pair<VipSkuItem, VipSkuItem.UpgradeProductModel?>? = null
            ) {
                val hasSkinBg = item?.skuDecorates?.backgroundImage.isNullOrEmpty().not()
                val hasSellingBackGround =
                    item?.skuDecorates?.sellingPointBackgroundColor.isNullOrEmpty().not()
                toggleHeight(
                    goodsItemView, isSelected,
                    LayoutStyle.platinumSkuLayoutStyle()
                )
                val nameTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_name)
                val nowPriceTv: VipRunNumberTextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_2)
                val nowPriceTvIcon: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_icon)
                val priceSuffixTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_suffix)
                val sellingPointContainer: FrameLayout? =
                    goodsItemView.findViewById(R.id.vip_purchase_selling_point_container)
                val upgradeMonthTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_upgrade_month)
                ViewStatusUtil.setVisible(View.GONE, priceSuffixTv)
                if (hasSkinBg) {
                    val skinBg: ImageView =
                        goodsItemView.findViewById(R.id.vip_purchase_dialog_bg_skin)

                    ViewStatusUtil.setAlphaWithFloat(skinBg, if (isSelected) 1f else 0.15f)
                } else {
                    val mask: View? =
                        goodsItemView.findViewById(R.id.vip_purchase_dialog_multi_mask)
                    ViewStatusUtil.setVisible(if (isSelected) View.VISIBLE else View.GONE, mask)
                }

                ViewStatusUtil.setTextColor(
                    Color.parseColor(
                        if (isSelected) {
                            if (hasSkinBg) "#292B33" else cssStyle.textSelectedColor
                        } else
                            (if (BaseFragmentActivity2.sIsDarkMode) "#ffffff" else cssStyle.textColor)
                    ),
                    nameTv, nowPriceTv, nowPriceTvIcon, priceSuffixTv
                )

                val descriptionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_description)
                val promotionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_promotion)

                val sellingPointBgView: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_selling_point_bg)

                ViewStatusUtil.setBackgroundDrawableRes(
                    if (isSelected) cssStyle.descriptionSelectedBgDrawableId else cssStyle.descriptionBgDrawableId,
                    sellingPointBgView
                )
                if (hasSellingBackGround) {
                    setSellingPointViewAlpha(
                        descriptionTv,
                        if (isSelected) 1f else 0.08f
                    )
                    setSellingPointViewAlpha(promotionTv, if (isSelected) 1f else 0.08f)
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8F8F8F")
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8F8F8F")
                    )
                } else {
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(
                            if (isSelected) cssStyle.textSelectedColor else {
                                if (BaseFragmentActivity2.sIsDarkMode) "#8f8f8f" else "#802C2C3C"
                            }
                        )
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(
                            if (isSelected) cssStyle.textSelectedColor else {
                                if (BaseFragmentActivity2.sIsDarkMode) "#8f8f8f" else "#802C2C3C"
                            }
                        )
                    )
                }

                if (!item?.upgradeProductVos.isNullOrEmpty()) {
                    if (isSelected) {
                        if (upgradeMonthPair?.first == item && upgradeMonthPair?.second != null) {
                            ViewStatusUtil.setText(
                                upgradeMonthTv,
                                "${upgradeMonthPair.second?.text }"
                            )
                        } else {
                            ViewStatusUtil.setText(
                                upgradeMonthTv,
                                "${item?.upgradeProductVos?.first()?.text}"
                            )
                        }
                        nowPriceTv?.text = upgradeMonthPair?.second?.monthlyPrice
                        ViewStatusUtil.setVisible(View.VISIBLE, upgradeMonthTv)
                    } else {
                        ViewStatusUtil.setVisible(View.INVISIBLE, upgradeMonthTv)
                    }
                    ViewStatusUtil.setVisible(View.VISIBLE, priceSuffixTv)
                    ViewStatusUtil.setText(priceSuffixTv,"/月")
                    ViewStatusUtil.setVisible(View.INVISIBLE, sellingPointContainer)
                } else {
                    ViewStatusUtil.setVisible(View.GONE, priceSuffixTv)
                    ViewStatusUtil.setVisible(View.VISIBLE, sellingPointContainer)
                    ViewStatusUtil.setVisible(View.INVISIBLE, upgradeMonthTv)
                }

                val moneySavedTagArea: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                if (moneySavedTagArea?.visibility == View.VISIBLE) {
                    val moneySaveTagTextView =
                        moneySavedTagArea?.findViewById<TextView>(R.id.vip_purchase_item_time_limited_discount_value)
                    val moneySaveTagImageView =
                        goodsItemView.findViewById<ImageView>(R.id.vip_purchase_item_time_limited_discount_bg)

                    var discountBgResId = R.drawable.vip_ic_time_limited_discount_bg_new
                    var textColor = Color.parseColor("#929292")
                    if (isSelected) {
                        discountBgResId = R.drawable.vip_ic_time_limited_discount_bg_new_selected
                        textColor = Color.parseColor("#B90C0C")
                    }
                    ViewStatusUtil.setImageRes(moneySaveTagImageView, discountBgResId)
                    ViewStatusUtil.setTextColor(moneySaveTagTextView, textColor)
                }

            }

            fun decorateSelectedNewSkuItemView(
                goodsItemView: View,
                item: VipSkuItem?,
                isSelected: Boolean
            ) {
                val hasSkinBg = item?.skuDecorates?.backgroundImage.isNullOrEmpty().not()
                val hasSellingBackGround =
                    item?.skuDecorates?.sellingPointBackgroundColor.isNullOrEmpty().not()
                toggleHeight(
                    goodsItemView, isSelected,
                    item?.localVipSkuShelfInfo?.localVipBehaviorAction?.isSkuSimplify ?: false
                )
                val nameTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_name)
                val nowPriceTv: VipRunNumberTextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_2)
                val nowPriceTvIcon: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_icon)
                val priceSuffixTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_now_price_suffix)

                var textSelectedColor = "#461717"
                if (item?.isSVIPItem == true) {
                    //SVIP 有一个"起"字，价格的文字颜色也不一样
                    ViewStatusUtil.setVisible(View.VISIBLE, priceSuffixTv)
                    textSelectedColor = "#292B33";
                } else {
                    ViewStatusUtil.setVisible(View.GONE, priceSuffixTv)
                    if (hasSkinBg) {
                        textSelectedColor = "#292B33"
                    }
                }

                if (hasSkinBg) {
                    val skinBg: ImageView =
                        goodsItemView.findViewById(R.id.vip_purchase_dialog_bg_skin)

                    ViewStatusUtil.setAlphaWithFloat(skinBg, if (isSelected) 1f else 0.3f)

                } else {
                    val mask: View? =
                        goodsItemView.findViewById(R.id.vip_purchase_dialog_multi_mask)
                    ViewStatusUtil.setVisible(if (isSelected) View.VISIBLE else View.GONE, mask)
                }

                ViewStatusUtil.setTextColor(
                    Color.parseColor(if (isSelected) textSelectedColor else (if (BaseFragmentActivity2.sIsDarkMode) "#ffffff" else "#292B33")),
                    nameTv, nowPriceTv, nowPriceTvIcon, priceSuffixTv
                )

                val descriptionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_description)
                val promotionTv: TextView? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_promotion)

                val sellingPointBgView: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_selling_point_bg)

                if (item?.isSVIPItem == true) {
                    ViewStatusUtil.setBackgroundDrawableRes(
                        if (isSelected) R.drawable.vip_bag_rect_dce2f1_radius_8 else R.drawable.vip_bg_bottom_svip_sku_unselected,
                        sellingPointBgView
                    )
                } else {
                    ViewStatusUtil.setBackgroundDrawableRes(
                        if (isSelected) R.drawable.vip_bag_rect_fffed9c7_radius_8 else R.drawable.vip_bg_bottom_sku_limit_tip,
                        sellingPointBgView
                    )
                }
                if (hasSellingBackGround || item?.isSVIPItem == true) {
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8F8F8F")
                    )
                    setSellingPointViewAlpha(
                        descriptionTv,
                        if (isSelected) 1f else 0.3f
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(if (isSelected) "#292B33" else "#8F8F8F")
                    )
                    setSellingPointViewAlpha(promotionTv, if (isSelected) 1f else 0.3f)
                } else {
                    ViewStatusUtil.setTextColor(
                        descriptionTv,
                        Color.parseColor(if (isSelected) "#461717" else "#8F8F8F")
                    )
                    ViewStatusUtil.setTextColor(
                        promotionTv,
                        Color.parseColor(if (isSelected) "#461717" else "#8F8F8F")
                    )
                }

                val moneySavedTagArea: View? =
                    goodsItemView.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                if (moneySavedTagArea?.visibility == View.VISIBLE) {
                    val moneySaveTagTextView =
                        moneySavedTagArea?.findViewById<TextView>(R.id.vip_purchase_item_time_limited_discount_value)
                    val moneySaveTagImageView =
                        goodsItemView.findViewById<ImageView>(R.id.vip_purchase_item_time_limited_discount_bg)

                    var discountBgResId = R.drawable.vip_ic_time_limited_discount_bg_new
                    var textColor = Color.parseColor("#929292")
                    if (isSelected) {
                        discountBgResId = R.drawable.vip_ic_time_limited_discount_bg_new_selected
                        textColor = Color.parseColor("#B90C0C")
                    }
                    ViewStatusUtil.setImageRes(moneySaveTagImageView, discountBgResId)
                    ViewStatusUtil.setTextColor(moneySaveTagTextView, textColor)
                }

            }

            private var simplifySkuHeight: Int = 0
                get() {
                    if (field == 0) {
                        field = (simplifySkuWidth * 1.312).roundToInt()
                    }
                    return field
                }
            private var simplifySkuSelectedHeight: Int = 0
                get() {
                    if (field == 0) {
                        field = (simplifySkuHeight * simplifySkuScale).roundToInt()
                    }
                    return field
                }
            private var simplifySkuWidth: Int = 0
                get() {
                    if (field == 0) {
                        field =
                            (VipFragmentUtil.FoldScreenUtils.getFoldScreenCompatWidth(ToolUtil.getCtx()) - 48.dp) / 3
                    }
                    return field
                }
            private var simplifySkuScale: Float = 1.099f
            private fun getTargetScale(isSelected: Boolean, isSkuSimplify: Boolean): Float {
                return if (isSelected) {
                    if (isSkuSimplify)
                        simplifySkuScale
                    else
                        SKU_SCALE
                } else
                    1f
            }

            private fun getTargetSkuNameTextSize(isSkuSimplify: Boolean): Float {
                return if (isSkuSimplify) {
                    12.dpFloat
                } else {
                    16.dpFloat
                }
            }

            private fun getTargetSkuPriceSymbolTextSize(isSkuSimplify: Boolean): Float {
                return if (isSkuSimplify) {
                    16.dpFloat
                } else {
                    20.dpFloat
                }
            }

            private fun getTargetSkuPriceTextSize(isSkuSimplify: Boolean): Float {
                return if (isSkuSimplify) {
                    30.dpFloat
                } else {
                    40.dpFloat
                }
            }

            fun getTargetHeight(isSelected: Boolean, isSkuSimplify: Boolean): Int {
                return if (isSkuSimplify) {
                    if (isSelected) {
                        simplifySkuSelectedHeight
                    } else {
                        simplifySkuHeight
                    }
                } else {
                    if (isSelected) {
                        SKU_HEIGHT
                    } else {
                        SKU_HEIGHT_UNSELECTED
                    }
                }
            }

            private fun getTargetWidth(isSelected: Boolean, isSkuSimplify: Boolean): Int {
                return if (isSkuSimplify) {
                    simplifySkuWidth
                } else {
                    if (isSelected) {
                        SKU_WIDTH
                    } else {
                        SKU_WIDTH_UNSELECTED
                    }
                }
            }

            fun setDinTypeFaceSemiBold(context: Context, tv: TextView?) {
                tv ?: return
                try {
                    val tf = Typeface.createFromAsset(
                        context.resources.assets,
                        "fonts/XmlyNumberV1.0-SemiBold.otf"
                    )
                    tv.typeface = tf
                } catch (e: Exception) {

                }
            }

            private fun toggleHeight(
                goodsItemView: View,
                isSelected: Boolean,
                layoutStyle: LayoutStyle
            ) {
                // 获取当前布局的高度
                val currentHeight = goodsItemView.height

                var newHeight = layoutStyle.getTargetHeight(isSelected)
                var newWidth = layoutStyle.getTargetWidth(isSelected)
                var newScale = layoutStyle.getTargetScale(isSelected)
                if (currentHeight == newHeight) {
                    return
                }
                goodsItemView.let {
                    it.updateLayoutParams<ViewGroup.LayoutParams> {
                        height = newHeight
                        width = newWidth
                    }
                    it.findViewById<TextView>(R.id.vip_purchase_item_now_price_icon)
                        ?.let { nowPriceIcon ->
                            nowPriceIcon.scaleX = newScale
                            nowPriceIcon.scaleY = newScale
                        }
//                    it.findViewById<TextView>(R.id.vip_purchase_item_now_price)?.let { nowPrice ->
//                        nowPrice.scaleX = newScale
//                        nowPrice.scaleY = newScale
//                    }
                }
            }

            private fun toggleHeight(
                goodsItemView: View,
                isSelected: Boolean,
                isSkuSimplify: Boolean
            ) {
                // 获取当前布局的高度
                val currentHeight = goodsItemView.height

                var newHeight = getTargetHeight(isSelected, isSkuSimplify)
                var newWidth = getTargetWidth(isSelected, isSkuSimplify)
                var newScale = getTargetScale(isSelected, isSkuSimplify)
                if (currentHeight == newHeight) {
                    return
                }
                goodsItemView.let {
                    it.updateLayoutParams<ViewGroup.LayoutParams> {
                        height = newHeight
                        width = newWidth
                    }
                    it.findViewById<TextView>(R.id.vip_purchase_item_now_price_icon)
                        ?.let { nowPriceIcon ->
                            nowPriceIcon.scaleX = newScale
                            nowPriceIcon.scaleY = newScale
                        }
//                    it.findViewById<TextView>(R.id.vip_purchase_item_now_price)?.let { nowPrice ->
//                        nowPrice.scaleX = newScale
//                        nowPrice.scaleY = newScale
//                    }
                }
            }

            fun handleUrl(url: String?, view: View?) {
                if (DialogUtil.jumpUrl(url)) {
                }
            }
        }
    }
}

interface GoodsInfoProvider {
    //    fun getLayoutId(type: Int): Int
    fun getCover(item: VipSkuItem?): String?
    fun getGoodsTag(item: VipSkuItem?): String?
    fun getGoodsName(item: VipSkuItem?): String?
    fun getGoodsDescription(item: VipSkuItem?): String?
    fun getGoodsNowPrice(item: VipSkuItem?): Double?
    fun getGoodsOriginPrice(item: VipSkuItem?): Double?
    fun getGoodsLabel(item: VipSkuItem?): String?
    fun getLabelText(item: VipSkuItem?): String?

    //活动角标文案
    fun getActivityPropertyLabel(item: VipSkuItem?): String?
    fun getPromotionPrice(item: VipSkuItem?): Double?
    fun getDiscountPrice(item: VipSkuItem?, ignoreUseCoupon: Boolean): Double?
    fun getCanUseCoupon(item: VipSkuItem?): Boolean
    fun isSpecialDiscount(item: VipSkuItem?): Boolean

    //购买免费赠送天数
    fun isPurchaseFreeDay(item: VipSkuItem?): Boolean
}

class CssStyle(
    val textColor: String,
    val textSelectedColor: String,
    val selectedBgDrawableId: Int,
    val descriptionBgDrawableId: Int,
    val descriptionSelectedBgDrawableId: Int
) {
    companion object {
        private var vipSkuCssStyle: CssStyle? = null
        private var pVipSkuCssStyle: CssStyle? = null

        fun vipSkuCssStyle(): CssStyle {
            if (vipSkuCssStyle == null) {
                vipSkuCssStyle = CssStyle(
                    "#2C2C3C",
                    "#461717",
                    R.drawable.vip_bg_vip_sku_item_selected,
                    R.drawable.vip_bg_vip_sku_item_bottom,
                    R.drawable.vip_bg_vip_sku_item_bottom_selected
                )
            }
            return vipSkuCssStyle!!
        }

        fun platinumVipSkuCssStyle(): CssStyle {
            if (pVipSkuCssStyle == null) {
                pVipSkuCssStyle = CssStyle(
                    "#2C2C3C",
                    "#794A16",
                    R.drawable.vip_bg_pvip_sku_item_selected,
                    R.drawable.vip_bg_vip_sku_item_bottom,
                    R.drawable.vip_bg_pvip_sku_item_bottom_selected
                )
            }
            return pVipSkuCssStyle!!
        }
    }
}


class LayoutStyle(
    val layoutId: Int,
    val width: Int,
    val height: Int,
    val selectedWidth: Int,
    val selectedHeight: Int,
    val selectedScale: Float
) {
    // var textSelectedColor = "#461717"
    //                if (item?.isSVIPItem == true) {
    //                    //SVIP 有一个"起"字，价格的文字颜色也不一样
    //                    ViewStatusUtil.setVisible(View.VISIBLE, priceSuffixTv)
    //                    textSelectedColor = "#292B33";
    //                } else {
    //                    ViewStatusUtil.setVisible(View.GONE, priceSuffixTv)
    //                    if (hasSkinBg) {
    //                        textSelectedColor = "#292B33"
    //                    }
    //                }

    companion object {
        private var platinumSkuLayoutStyle: LayoutStyle? = null
//        private var newSkuLayoutStyle: LayoutStyle? = null
//        private var oldSkuLayoutStyle: LayoutStyle? = null
//        fun newSkuLayoutStyle(): LayoutStyle {
//            if (newSkuLayoutStyle == null) {
//                val width: Int =
//                    (VipFragmentUtil.FoldScreenUtils.getFoldScreenCompatWidth(ToolUtil.getCtx()) - 48.dp) / 3
//                val height: Int = (width * 1.312).roundToInt()
//                val scale: Float = 1.099f
//                val selectedHeight = (height * scale).roundToInt()
//                newSkuLayoutStyle = LayoutStyle(
//                    layoutId = 0,
//                    width = width,
//                    height = height,
//                    selectedWidth = width,
//                    selectedHeight = selectedHeight,
//                    selectedScale = scale
//                )
//            }
//            return newSkuLayoutStyle!!
//        }
//
//        fun oldSkuLayoutStyle(): LayoutStyle {
//            if (oldSkuLayoutStyle == null) {
//                val width: Int =
//                    BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 130f)
//                val height: Int = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 175f)
//                val selectedHeight = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 189f)
//                val selectedWidth = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 141f)
//                val scale: Float = 1.08f
//                oldSkuLayoutStyle = LayoutStyle(
//                    layoutId = 0,
//                    width = width,
//                    height = height,
//                    selectedWidth = selectedWidth,
//                    selectedHeight = selectedHeight,
//                    selectedScale = scale
//                )
//            }
//            return oldSkuLayoutStyle!!
//        }

        fun platinumSkuLayoutStyle(): LayoutStyle {
            if (platinumSkuLayoutStyle == null) {
                val width: Int =
                    (VipFragmentUtil.FoldScreenUtils.getFoldScreenCompatWidth(ToolUtil.getCtx()) - 48.dp) / 3
                val height: Int = (width * 1.3486).roundToInt()
                val scale: Float = 1f
                platinumSkuLayoutStyle = LayoutStyle(
                    layoutId = 0,
                    width = width,
                    height = height,
                    selectedWidth = width,
                    selectedHeight = height,
                    selectedScale = scale
                )
            }
            return platinumSkuLayoutStyle!!
        }
    }

    fun getTargetWidth(isSelected: Boolean): Int {
        return if (isSelected) {
            selectedWidth
        } else {
            width
        }
    }

    fun getTargetHeight(isSelected: Boolean): Int {
        return if (isSelected) {
            selectedHeight
        } else {
            height
        }
    }

    fun getTargetScale(isSelected: Boolean): Float {
        return if (isSelected) {
            selectedScale
        } else {
            1f
        }
    }
}