package com.ximalaya.ting.android.vip.manager.purchaseDialog

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.gson.JsonObject
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.IPVipBottomActionListener
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.vip.IVipFunctionAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.model.payment.UniversalPayment
import com.ximalaya.ting.android.host.model.payment.behavior.CommonBehavior
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.IdMaterial
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem.UpgradeProductModel
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.SpanUtils.*
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.ui.AnimationUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.vip.R
import com.ximalaya.ting.android.vip.constant.VipBundleConstants
import com.ximalaya.ting.android.vip.constant.VipUrlConstants
import com.ximalaya.ting.android.vip.manager.combine.CombineVipPurchaseMarkPointManager
import com.ximalaya.ting.android.vip.manager.promotions.CpcManager
import com.ximalaya.ting.android.vip.model.material.PurchaseMaterial
import com.ximalaya.ting.android.vip.util.DialogUtil
import com.ximalaya.ting.android.vip.util.purchase.IVipGoodsCallBack
import com.ximalaya.ting.android.vip.util.purchase.VipPurchaseUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by mark on 2025/3/17 20:40
 */
abstract class BaseVipBottomViewMamanger(
    private val baseFragment: BaseFragment2,
    private val data: VipSkuShelfInfo,
    private val transmissionParams: HashMap<String, String>
) : IVipFunctionAction.IVipBottomViewManager {
    companion object {
        private const val FAIL_TOAST: String = "参数错误，请稍后重试"
        const val TAB_TYPE_VIP = "vip"
        const val TAB_TYPE_PLATINUM_VIP = "platinumVip"
    }

    protected var albumId: Long = 0
    protected var trackId: Long = 0
    var rootView: View? = null
    var protocolContainer: View? = null
    var protocolCheckbox: ImageView? = null
    var ruleCheckBoxTextView: TextView? = null
    var ruleCheckBoxClickView: View? = null
    var buttonContainer: LinearLayout? = null
    var tvPrice: TextView? = null
    var buttonViewTv: TextView? = null
    var privilegeTipsTv: TextView? = null
    var multiAccountTv: TextView? = null

    val paddingTop10 = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 10f)
    val paddingTop13 = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 13f)

    protected var selectedVipSkuItem: VipSkuItem? = null
    protected var bottomActionListener: IPVipBottomActionListener? = null
    abstract fun getTabName():String

    override fun createRuleAndBuyButtonView(): View {
        val view: View = LayoutInflater.from(BaseApplication.getMyApplicationContext())
            .inflate(R.layout.vip_item_pvip_combine_bottom, null)

        rootView = view
        protocolContainer = view.findViewById(R.id.vip_vg_purchase_dialog_protocol)
        protocolCheckbox = view.findViewById(R.id.vip_iv_purchase_dialog_protocol_checkbox)
        ruleCheckBoxTextView = view.findViewById(R.id.vip_tv_purchase_dialog_button_rule)
        ruleCheckBoxClickView = view.findViewById(R.id.vip_v_purchase_dialog_click_mask)
        buttonContainer = view.findViewById(R.id.vip_btn_container)
        tvPrice = view.findViewById(R.id.vip_tv_price_span)
        buttonViewTv = view.findViewById(R.id.vip_purchase_dialog_button_text)
        privilegeTipsTv = view.findViewById(R.id.vip_purchase_dialog_button_privilege)
        multiAccountTv = view.findViewById(R.id.vip_tv_purchase_dialog_multi_account)
        bindPrivilegeTips()
        bindMultiAccountView()
        buttonContainer?.setOnClickListener {
            if (OneClickHelper.getInstance().onClick(it)) {
                onBuyButtonClicked()
            }
        }
        return view
    }

    override fun setOnBottomViewActionListener(listener: IPVipBottomActionListener?) {
        bottomActionListener = listener
    }

    private fun bindMultiAccountView() {
        multiAccountTv ?: return
        val multiAccountInfo = data?.multiAccount
        multiAccountInfo ?: return
        val currentAccount = multiAccountInfo.get("currentAccount")
        val commonAccount = multiAccountInfo.get("commonAccount")
        if (commonAccount == null) {
            ViewStatusUtil.setVisible(View.GONE, multiAccountTv)
            return
        }
        XMTraceApi.Trace()
            .setMetaId(44841)
            .setServiceId("dialogView")
            .put("currPage", "")
            .createTrace()
        ViewStatusUtil.setVisible(View.VISIBLE, multiAccountTv)
        SpanUtils.with(multiAccountTv).append("当前登录账号不是您的常用账号").appendSpace(6.dp).append("点击查看")
            .setForegroundColor(
                Color.parseColor("#FC8859")
            ).create()
        multiAccountTv?.setOnClickListener {
            XMTraceApi.Trace()
                .setMetaId(44842)
                .setServiceId("dialogClick")
                .put("currPage", "")
                .put("Item", "点击查看")
                .createTrace()
            doOnChangeAccountClicked(multiAccountInfo)
        }
    }

    /**
     * 绘制按钮文案区域
     * */
    open fun bindTextDataOnButton() {
        selectedVipSkuItem ?: return
        buttonContainer ?: return
        updateButtonText(selectedVipSkuItem!!)
    }

    protected fun setButtonViewText(item: VipSkuItem, chosenUpgradeProduct: UpgradeProductModel?) {
        val discountPrice = item.discountPrice
        val unitPrice = item.unitPrice
        val payPrice = item.payPrice
        var singleMonthPrice: Double = 0.0
        if (discountPrice > 0) {
            singleMonthPrice = discountPrice
        }
        if (unitPrice > 0) {
            singleMonthPrice = if (singleMonthPrice > 0) {
                singleMonthPrice.coerceAtMost(unitPrice)
            } else {
                unitPrice
            }
        }
        if (payPrice > 0) {
            singleMonthPrice = if (singleMonthPrice > 0) {
                singleMonthPrice.coerceAtMost(payPrice)
            } else {
                payPrice
            }
        }
        val singlePriceStr = StringUtil.subZeroAndDot("${singleMonthPrice * 1}")
        val quantityPriceStr = chosenUpgradeProduct?.price ?: singlePriceStr

        val btnText: String? = item.properties?.purchaseButtonText
        if (!StringUtil.isEmpty(btnText)) {
            if (btnText?.contains(singlePriceStr) == true) {
                val array = btnText.split(singlePriceStr)
                val sb = StringBuilder()
                val spanUtils = SpanUtils.with(tvPrice)
                array.forEachIndexed { index, s ->
                    if (s == "￥" || s == "¥") {
                        spanUtils.append(s).setFontSize(15, true)
                        spanUtils.append(quantityPriceStr).setFontSize(25, true)
                    } else {
                        sb.append(s)
                    }
                }
                spanUtils.create()
                ViewStatusUtil.setText(buttonViewTv, sb.toString())
                ViewStatusUtil.setVisible(View.VISIBLE, tvPrice)
                setDinTypeFaceSemiBold(getContext(),tvPrice)
            } else {
                ViewStatusUtil.setText(buttonViewTv, btnText)
                ViewStatusUtil.setVisible(View.GONE, tvPrice)
            }
        } else {
            ViewStatusUtil.setVisible(View.GONE, tvPrice)
            ViewStatusUtil.setText(buttonViewTv, "立即购买")
        }
    }
    fun setDinTypeFaceSemiBold(context: Context?, tv: TextView?) {
        context?:return
        tv ?: return
        try {
            val tf = Typeface.createFromAsset(
                context.resources.assets,
                "fonts/XmlyNumberV1.0-Medium.otf"
            )
            tv.typeface = tf
        } catch (e: Exception) {

        }
    }
    open fun updateButtonText(vipSkuItem: VipSkuItem) {
        setButtonViewText(vipSkuItem, null)
    }

    /**
     * 绘制privilegeTipsTv
     * */
    private fun bindPrivilegeTips() {
        privilegeTipsTv ?: return
        data?.privilegeTips ?: return
        ViewStatusUtil.setVisible(View.INVISIBLE, privilegeTipsTv)
        data.privilegeTips.privilegeTipsText?.let {
            if (it.isNotEmpty()) {
                ViewStatusUtil.setVisible(View.VISIBLE, privilegeTipsTv)
                if (it.contains("%day")) {
                    val texts: List<String?> = it.split("%day")
                    if (1 < texts.size) {
                        val spanUtils = SpanUtils.with(privilegeTipsTv)
                        texts.forEachIndexed { index, s ->
                            if (index == 1) {
                                spanUtils.append("${data.privilegeTips.expireDays ?: 0}")
                            }
                            spanUtils.append(s.toString())
                        }
                        spanUtils.create()
                    } else {
                        ViewStatusUtil.setText(privilegeTipsTv, it)
                    }
                } else {
                    ViewStatusUtil.setText(privilegeTipsTv, it)
                }
            }
        }
    }

    open fun bindRuleArea() {
        rootView ?: return
        selectedVipSkuItem ?: return
        protocolCheckbox ?: return
        ruleCheckBoxTextView ?: return

        ViewStatusUtil.setVisible(View.VISIBLE, protocolCheckbox)
        ViewStatusUtil.setVisible(View.VISIBLE, ruleCheckBoxClickView)
        protocolContainer?.setPadding(0, paddingTop10, 0, 32.dp)

        val vipRulePair1: Pair<String, String> =
            Pair("《会员服务协议》", VipUrlConstants.getInstance().vipServiceAgreementUrl)
        val list = mutableListOf<Pair<String, String>>(vipRulePair1)
        val vipRulePair2: Pair<String, String>? =
            if (selectedVipSkuItem?.properties?.autoRenew == true) Pair(
                "《自动续费服务规则》",
                VipUrlConstants.getInstance().autoRenewRuleAgreementUrl
            ) else null
        if (vipRulePair2 != null) {
            list.add(vipRulePair2)
        }

        val context = BaseApplication.getMyApplicationContext();
        val vipRulePair3: Pair<String, String>? =
            if (selectedVipSkuItem?.present == true) {
                val size = selectedVipSkuItem!!.presentDetails?.size ?: 0
                if (size == 1) {
                    selectedVipSkuItem!!.presentDetails.first().let {
                        Pair("《${it.agreementName}》", it.agreementUrl)
                    }
                } else if (size > 1) {
                    val ids = StringBuilder("")
                    selectedVipSkuItem!!.presentDetails.forEachIndexed { index, VipPresentDetail ->
                        if (index == 0) {
                            ids.append("${VipPresentDetail.presentItemId}")
                        } else {
                            ids.append(",").append("${VipPresentDetail.presentItemId}")
                        }
                    }
                    Pair(
                        "联合会员服务协议",
                        VipUrlConstants.getInstance().getMultiVipServiceAgreementUrl(ids.toString())
                    )
                } else null
            } else null
        if (vipRulePair3 != null) {
            list.add(vipRulePair3)
        }
        val spanUtils = SpanUtils.with(ruleCheckBoxTextView)
        list.forEachIndexed { index, pair ->
            if (index == 0) {
                spanUtils.append("开通前请阅读")
            } else if (index == list.size - 1) {
                spanUtils.append("及")
            }
            spanUtils.append(pair.first).setClickSpan(
                ContextCompat.getColor(
                    context, R.color.host_color_999999_8d8d91
                ), false
            ) {
                if (DialogUtil.jumpUrl(pair.second)
                ) {
                    closeDialog()
                }
            }
        }
        spanUtils.create()

        // 设个空的点击处理，屏蔽开通按钮的处理
        rootView!!.findViewById<View>(R.id.vip_v_purchase_dialog_click_mask)
            ?.setOnClickListener { }
        val protocolCheckboxClickArea: View? =
            rootView!!.findViewById(R.id.vip_v_purchase_dialog_protocol_checkbox_click_area)

        val hasAgree = MmkvCommonUtil.getInstance(context).getBoolean(
            PreferenceConstantsInHost.KEY_HAS_AGREE_VIP_PURCHASE_DIALOG_PROTOCOL, false
        )

        protocolCheckbox?.isSelected = hasAgree
        protocolCheckboxClickArea?.setOnClickListener {
            if (protocolCheckbox != null) {
                protocolCheckbox?.isSelected = !protocolCheckbox!!.isSelected
                MmkvCommonUtil.getInstance(context).saveBoolean(
                    PreferenceConstantsInHost.KEY_HAS_AGREE_VIP_PURCHASE_DIALOG_PROTOCOL,
                    protocolCheckbox!!.isSelected
                )

                var item = "协议展开"
                if (protocolCheckbox!!.isSelected) {
                    item = "协议勾选"
                }
//                val behavior: CommonBehavior? = selectedVipSkuItem?.local
                val behavior: CommonBehavior? = null
                // 统一付费半浮层-协议勾选｜展开  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(56442)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("currPage", UniversalPayment.findCurrPage(behavior?.localDialogSource))
                    .put("businessType", behavior?.type)
                    .put("sceneName", behavior?.localDialogSource ?: "")
                    .put("productId", selectedVipSkuItem?.productId ?: "")
                    .put("trackId", "-1")
                    .put("albumId", "-1")
                    .put("item", item)
                    .put("tabName", getTabName())
                    .createTrace()
            }
        }
    }

    private fun closeDialog() {
        bottomActionListener?.closeDialog()
    }

    override fun onSkuItemSelected(item: VipSkuItem?) {
        selectedVipSkuItem = item
        bindTextDataOnButton()
        bindRuleArea()
    }

    override fun updateUpgradeMonth() {
    }

    open fun onBuyButtonClicked() {

        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getContext())
            closeDialog()
            return
        }
        if (!checkHasAgreeProtocol()) {
            return
        }
        val chosen: VipSkuItem? = selectedVipSkuItem

        if (null == chosen || data?.vipCategoryId == null || data?.vipSpuId == null) {
            CustomToast.showFailToast(FAIL_TOAST)
            return
        }
        CpcManager.recordVipSkuItem(chosen)
        val upgradePair = bottomActionListener?.getUpgradeSkuSet()
        if (upgradePair?.first == chosen && upgradePair.second != null) {
            transmissionParams["quantity"] = "${upgradePair.second!!.quantity}"
            transmissionParams["itemId"] = upgradePair.second!!.itemId
        }
        bottomActionListener?.onConfirmBtnClick(chosen)
        CombineVipPurchaseMarkPointManager.markPointOnClickConfirmBtnNew(chosen, data.dataAnalysis, getTabName())
        VipPurchaseUtil.checkIsQualified(data.vipCategoryId, data.vipSpuId, chosen, object :
            IVipGoodsCallBack {
            override fun onResult(code: Int, msg: String?) {
                if (0 < code) {
                    val material: PurchaseMaterial = PurchaseMaterial(
                        fromFragment = baseFragment,
                        chosen,
                        buildIdMaterial()
                    )
                    if (getTabName() == TAB_TYPE_PLATINUM_VIP) {
                        material.vipProductType =
                            VipBundleConstants.VipProductTypeConstants.PLATINUM_VIP
                    }
                    material.transmissionParams = transmissionParams

                    Logger.d("z_vip", "processVipPurchase s2 >>> ")

                    val purchaseResult: Int = VipPurchaseUtil.processVipPurchase(material, object :
                        IVipGoodsCallBack {

                        override fun onResult(code: Int, msg: String?) {
                            if (0 < code) {
                                closeDialog()
                            } else {
                                CustomToast.showFailToast(FAIL_TOAST)
                            }
                        }
                    })
                    when {
                        (0 < purchaseResult) ->
                            closeDialog()
                        (0 > purchaseResult) ->
                            CustomToast.showFailToast(FAIL_TOAST)
                    }
                } else {
                    if (!StringUtil.isEmpty(msg)) {
                        CustomToast.showFailToast(msg!!)
                    }
                }
            }
        })

    }

    private fun buildIdMaterial(): IdMaterial {
        val vipCategoryId = if ((data?.vipCategoryId ?: 0) > 0) {
            data?.vipCategoryId!!
        } else {
            selectedVipSkuItem?.localVipCategoryId ?: -1
        }
        val vipSpuId = if ((data?.vipSpuId ?: 0) > 0) {
            data?.vipSpuId!!
        } else {
            selectedVipSkuItem?.localVipSpuId ?: -1
        }
        val idMaterial = IdMaterial(
            albumId,
            trackId,
            vipCategoryId,
            vipSpuId
        )
        idMaterial.isNeedResumeNativeDialog = false
        return idMaterial;
    }

    private fun getContext(): Context? {
        return BaseApplication.getMyApplicationContext()
    }

    private fun checkHasAgreeProtocol(): Boolean {
        if (noNeedProtocol()) {
            return true
        }
        if (protocolCheckbox?.isSelected == true) {
            return true
        }
        CustomToast.showToast("请阅读并勾选协议")
        if (protocolContainer != null) {
            AnimationUtil.startShakeAnimationForVipPurchaseProtocol(protocolContainer)
        }
        return false
    }

    private fun noNeedProtocol(): Boolean {
        return selectedVipSkuItem?.isSVIPItem == true
    }

    private fun doOnChangeAccountClicked(accountsInfo: JsonObject) {
        baseFragment ?: return
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
            closeDialog()
            return
        }

        try {
            if (ChildProtectManager.isChildBind(baseFragment.context) && ChildProtectManager.isChildProtectOpen(
                    baseFragment.context
                )
            ) {
                CustomToast.showToast("此设备已开启青少年模式，无法切换账号")
            } else {
                closeDialog()
                Router.getActionByCallback(
                    Configure.BUNDLE_LOGIN,
                    object : Router.IBundleInstallCallback {
                        override fun onInstallSuccess(bundleModel: BundleModel) {
                            if (baseFragment != null) {
                                var changeAccountFragment: BaseFragment2? = null
                                try {
                                    changeAccountFragment =
                                        Router.getActionRouter<LoginActionRouter>(Configure.BUNDLE_LOGIN)!!
                                            .fragmentAction.changeAccountFragment
                                    val bundle = Bundle()
//                                    bundle.putBoolean("forceToMySpaceIfSuccess",false)
                                    changeAccountFragment?.arguments = bundle
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                                if (changeAccountFragment != null) {
                                    baseFragment.startFragment(changeAccountFragment)
                                }
                            }
                        }

                        override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {}
                        override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {}
                    })
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }
}