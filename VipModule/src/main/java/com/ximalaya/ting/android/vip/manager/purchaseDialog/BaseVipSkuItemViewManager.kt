package com.ximalaya.ting.android.vip.manager.purchaseDialog

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.widget.LinearLayout
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.IPVipSkuItemActionListener
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.vip.IVipFunctionAction
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.vip.R
import com.ximalaya.ting.android.vip.manager.combine.CombineVipPurchaseDataPresenter
import com.ximalaya.ting.android.vip.manager.combine.CombineVipPurchaseItemManager
import com.ximalaya.ting.android.vip.manager.combine.CombineVipPurchaseMarkPointManager
import com.ximalaya.ting.android.vip.util.VipBundleCommonUtil
import com.ximalaya.ting.android.vip.view.VipRunNumberTextView

/**
 * Created by mark on 2025/3/17 16:49
 */
abstract class BaseVipSkuItemViewManager(
    val skuContainer: LinearLayout,
    val fragment: BaseFragment2,
    val data: VipSkuShelfInfo
) : IVipFunctionAction.IVipSkuItemViewManager, CombineVipPurchaseItemManager(
    CombineVipPurchaseDataPresenter(fragment, data)
) {

    protected var skuItemActionListener: IPVipSkuItemActionListener? = null

    protected var mChosenVipSku: VipSkuItem? = null
        protected set
    protected var itemAnimatorPhase1TagPartSet: AnimatorSet? = null

    inner class SkuItemClickListener(private val item: VipSkuItem):View.OnClickListener {
        override fun onClick(v: View?) {
            onSkuItemSelected(item)
            CombineVipPurchaseMarkPointManager.markPointOnClickGoodsItemNew(
                item,
                data.dataAnalysis,
                getTabName()
            )
        }

        fun onSkuUpgradeMonthClick(v: View) {
            skuItemActionListener?.onSkuUpgradeMonthClick(v, item)
        }
        fun onSkuUpgradeMonthViewShow(){
            skuItemActionListener?.onSkuUpgradeMonthViewShow(item)
        }
    }
    abstract fun createSkuItemView(item: VipSkuItem): View?

    abstract fun decorateSkuItemView(view: View, item: VipSkuItem, isSelected:Boolean)

    abstract fun getTabName():String
    override fun bindDataOnShelve(view: View?) {
        CombineVipPurchaseMarkPointManager.markPointOnShowCombineVipSkuPanelNew(
            data.trackId,
            data.albumId,
            getTabName()
        )
        data.vipSkuItem?.let { vipSkuItems ->
            // 弹窗创建默认自动选中第一个sku
            mChosenVipSku = VipBundleCommonUtil.safelyGetItemFromList(vipSkuItems, 0)
            vipSkuItems.forEachIndexed { index, vipSkuItem ->
                createSkuItemView(vipSkuItem)?.let { goodsView ->
                    val layoutParams = goodsView.layoutParams
                    if (layoutParams is LinearLayout.LayoutParams
                        && 0 != index
                    ) {
                        layoutParams.leftMargin =
                            com.ximalaya.ting.android.vip.manager.combine.MARGIN_WIDTH
                    }
                    skuContainer.addView(goodsView, layoutParams)

                    var hasShownBigPromotionAnimation = false
                    val moneySavedTagArea: View? =
                        goodsView.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                    if (index == 0) {
                        if (moneySavedTagArea?.getTag(R.id.vip_id_tag_contain_model) != null) {
                            moneySavedTagArea.visibility = View.INVISIBLE
                        }
                        //当前选中的第一个item
                        if (vipSkuItem.needShow818BigPromotionAnimation()) {
                            hasShownBigPromotionAnimation = true
                            vipSkuItem.showBigPromotionAnimation = true
                            show818BigPromotionAnimation(goodsView)
                        } else if (vipSkuItem.needShowBigPromotionAnimation()) {
                            hasShownBigPromotionAnimation = true
                            vipSkuItem.showBigPromotionAnimation = true
                            showBigPromotionAnimation(goodsView)
                        } else {
                            if (moneySavedTagArea?.getTag(R.id.vip_id_tag_contain_model) != null) {
                                moneySavedTagArea.visibility = View.VISIBLE
                            }
                        }
                    }
                    if (!hasShownBigPromotionAnimation) {
                        //买赠优先于限免
                        if (vipSkuItem.needShowPurchaseFreeDayPopWindow()) {
                            vipSkuItem.showPurchaseFreeDayPopWindow = true
                            showPurchaseFreeDayAnim(goodsView)
                        } else if (vipSkuItem.canShowTimeLimitedPopWindow()) {
                            vipSkuItem.showTimeLimitedPopWindow = false
                            //播放动画
                            showTimeLimitedDiscount()
                        }
                    }
                }
                traceSkuItemShow(vipSkuItem)
            }
        }
    }

    private fun traceSkuItemShow(item: VipSkuItem) {
        CombineVipPurchaseMarkPointManager.markPointOnShowGoodsItemNew(
            item,
            data.dataAnalysis,
            getTabName()
        )
    }

    override fun setOnSkuItemActionListener(listener: IPVipSkuItemActionListener?) {
        skuItemActionListener = listener
    }

    private fun showPurchaseFreeDayAnim(view: View?) {
        itemAnimatorPhase1TagPartSet?.cancel()
        view ?: return
        val tagView: View = findPossibleTagView(view) ?: return

        itemAnimatorPhase1TagPartSet = AnimatorSet()
        val startDelay = 600L
        val duration = 667L
        val scaleXAnimator: ObjectAnimator =
            ObjectAnimator.ofFloat(tagView, "scaleX", 1f, 1.4f, 1f, 1.3f, 1f).setDuration(duration)
        val scaleYAnimator: ObjectAnimator =
            ObjectAnimator.ofFloat(tagView, "scaleY", 1f, 1.4f, 1f, 1.3f, 1f).setDuration(duration)
        val tagAlphaAnimator: ObjectAnimator =
            ObjectAnimator.ofFloat(tagView, "alpha", 0f, 1f).setDuration(duration)
        tagView.alpha = 0f
        tagView.scaleX = 1f
        tagView.scaleY = 1f
        tagView.pivotX = 0f
        tagView.pivotY = 8.dpFloat
        scaleXAnimator.startDelay = startDelay
        scaleYAnimator.startDelay = startDelay
        tagAlphaAnimator.startDelay = startDelay
        itemAnimatorPhase1TagPartSet?.interpolator = LinearInterpolator()
        itemAnimatorPhase1TagPartSet?.removeAllListeners()
        itemAnimatorPhase1TagPartSet?.playTogether(scaleXAnimator, scaleYAnimator, tagAlphaAnimator)
        itemAnimatorPhase1TagPartSet?.start()
        ViewStatusUtil.setVisible(View.VISIBLE, tagView)
    }

    private fun findPossibleTagView(view: View): View? {
        val highContainer: View? =
            view.findViewById(R.id.vip_purchase_item_tag_high_priority_container)
        val highInner: View? = view.findViewById(R.id.vip_purchase_item_tag_high_priority)
        val high = highContainer ?: highInner
        if (null != (high?.getTag(R.id.vip_id_tag_contain_model))) {
            return high
        }

        val low: View? = view.findViewById(R.id.vip_purchase_item_tag_low_priority)
        if (null != (low?.getTag(R.id.vip_id_tag_contain_model))) {
            return low
        }

        return null
    }
    private fun show818BigPromotionAnimation(view: View?) {
        view?.let {
            it.postDelayed(object : Runnable {
                override fun run() {
                    val nowPriceTv: VipRunNumberTextView? =
                        it.findViewById(R.id.vip_purchase_item_now_price_2)
                    val moneySavedTagArea: View? =
                        it.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                    val redpacketLottieView: XmLottieAnimationView? =
                        it.findViewById(R.id.main_iv_red_packet_lottie)
                    redpacketLottieView?.visibility = View.VISIBLE
                    redpacketLottieView?.setAnimation("lottie/red_pack_818/data.json")
                    redpacketLottieView?.imageAssetsFolder = "lottie/red_pack_818/images"
                    redpacketLottieView?.addAnimatorListener(object : Animator.AnimatorListener {
                        override fun onAnimationStart(animation: Animator?) {
                        }

                        override fun onAnimationEnd(animation: Animator?) {
                            if (moneySavedTagArea?.getTag(R.id.vip_id_tag_contain_model) != null) {
                                moneySavedTagArea.visibility = View.VISIBLE
                                val animatorSet = AnimatorSet()
                                val scaleXAnimator =
                                    ObjectAnimator.ofFloat(moneySavedTagArea, "scaleX", 1.2f, 1.0f)
                                val scaleYAnimator =
                                    ObjectAnimator.ofFloat(moneySavedTagArea, "scaleY", 1.2f, 1.0f)
                                val alphaAnimator =
                                    ObjectAnimator.ofFloat(moneySavedTagArea, "alpha", 0f, 1f)
                                animatorSet.duration = 200
                                animatorSet.interpolator = AccelerateDecelerateInterpolator()
                                animatorSet.playTogether(
                                    scaleXAnimator,
                                    scaleYAnimator,
                                    alphaAnimator
                                )
                                animatorSet.start()
                            }
                        }

                        override fun onAnimationCancel(animation: Animator?) {
                        }

                        override fun onAnimationRepeat(animation: Animator?) {
                        }

                    })
                    redpacketLottieView?.playAnimation()
                    nowPriceTv?.startPendingAnimation()
                }

            }, 500)
        }
    }
    private fun showBigPromotionAnimation(view: View?) {
        view?.let {
            it.postDelayed(object : Runnable {
                override fun run() {
                    val nowPriceTv: VipRunNumberTextView? =
                        it.findViewById(R.id.vip_purchase_item_now_price_2)
                    val moneySavedTagArea: View? =
                        it.findViewById(R.id.vip_purchase_item_time_limited_discount_tag)
                    val redpacketLottieView: XmLottieAnimationView? =
                        it.findViewById(R.id.main_iv_red_packet_lottie)
                    redpacketLottieView?.visibility = View.VISIBLE
                    redpacketLottieView?.addAnimatorListener(object : Animator.AnimatorListener {
                        override fun onAnimationStart(animation: Animator?) {
                        }

                        override fun onAnimationEnd(animation: Animator?) {
                            if (moneySavedTagArea?.getTag(R.id.vip_id_tag_contain_model) != null) {
                                moneySavedTagArea.visibility = View.VISIBLE
                                val animatorSet = AnimatorSet()
                                val scaleXAnimator =
                                    ObjectAnimator.ofFloat(moneySavedTagArea, "scaleX", 1.2f, 1.0f)
                                val scaleYAnimator =
                                    ObjectAnimator.ofFloat(moneySavedTagArea, "scaleY", 1.2f, 1.0f)
                                val alphaAnimator =
                                    ObjectAnimator.ofFloat(moneySavedTagArea, "alpha", 0f, 1f)
                                animatorSet.duration = 200
                                animatorSet.interpolator = AccelerateDecelerateInterpolator()
                                animatorSet.playTogether(
                                    scaleXAnimator,
                                    scaleYAnimator,
                                    alphaAnimator
                                )
                                animatorSet.start()
                            }
                        }

                        override fun onAnimationCancel(animation: Animator?) {
                        }

                        override fun onAnimationRepeat(animation: Animator?) {
                        }

                    })
                    redpacketLottieView?.playAnimation()
                    nowPriceTv?.startPendingAnimation()
                }

            }, 500)
        }
    }

    private fun showTimeLimitedDiscount() {
        //暂不支持动画
    }

    private fun onSkuItemSelected(vipSkuItem: VipSkuItem) {
        if (vipSkuItem == mChosenVipSku) {
            return
        }
        mChosenVipSku = vipSkuItem
        skuItemActionListener?.onSkuItemSelected(vipSkuItem)
        data.vipSkuItem?.let { vipSkuItems ->
            val selectedIndex: Int = vipSkuItems.indexOf(vipSkuItem) ?: -1
            vipSkuItems.forEachIndexed { index, vipSkuItem ->
                val child: View = skuContainer.getChildAt(index)
                child?.let {
                    decorateSkuItemView(it,vipSkuItem,index == selectedIndex)
                }
            }
        }
    }
    protected fun updateShelf(){
        mChosenVipSku?:return
        data.vipSkuItem?.let { vipSkuItems ->
            val selectedIndex: Int = vipSkuItems.indexOf(mChosenVipSku) ?: -1
            vipSkuItems.forEachIndexed { index, vipSkuItem ->
                val child: View = skuContainer.getChildAt(index)
                child?.let {
                    decorateSkuItemView(it,vipSkuItem,index == selectedIndex)
                }
            }
        }
    }

    override fun isSpecialDiscount(item: VipSkuItem?): Boolean {
        item ?: return false

        return item.applyTimeLimited
    }

    override fun isPurchaseFreeDay(item: VipSkuItem?): Boolean {
        item ?: return false
        val isInPurchaseFreeDayActivity =
            item.activityPurchaseFreeDayTimeLimitGetCountDown() > 0 || item.purchaseFreeDayCountDown() > 0;
        return isInPurchaseFreeDayActivity
    }

    override fun getChosenVipSku(): VipSkuItem? {
        return mChosenVipSku
    }

    override fun updateUpgradeMonth() {
    }
}