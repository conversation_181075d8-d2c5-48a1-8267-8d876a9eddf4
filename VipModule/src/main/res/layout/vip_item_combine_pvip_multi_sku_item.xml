<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="130dp"
    android:layout_height="175dp"
    android:clipChildren="false"
    android:clipToPadding="false"
    tools:background="#000000">

    <!--    未选中的背景-->
    <ImageView
        android:id="@+id/vip_purchase_dialog_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="5dp"
        android:background="@drawable/vip_bg_vip_sku_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--    选中以后的背景-->
    <View
        android:id="@+id/vip_purchase_dialog_multi_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="5dp"
        android:background="@drawable/vip_bg_vip_sku_item_selected"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="5dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/vip_purchase_dialog_bg_skin"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="gone"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <include
        layout="@layout/vip_view_time_limited_discount_value_new"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginTop="5dp"
        android:visibility="invisible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/vip_purchase_item_tag_high_priority_container"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="1dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/vip_purchase_item_tag_high_priority_icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginVertical="4dp"
            android:layout_marginLeft="3dp"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/vip_purchase_item_tag_high_priority"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxWidth="91dp"
            android:maxLines="1"
            android:paddingLeft="3dp"
            android:paddingRight="4dp"
            android:textColor="@color/host_color_ffffff"
            android:textSize="10dp"
            android:visibility="gone"
            tools:text="限时优惠"
            tools:visibility="gone" />
    </LinearLayout>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="24dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/vip_purchase_item_tag_low_priority"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:gravity="center_vertical"
            android:maxWidth="91dp"
            android:scaleType="fitStart"
            android:visibility="gone"
            tools:background="#666666"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/vip_purchase_item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingLeft="@dimen/host_x12"
        android:paddingRight="@dimen/host_x12"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:textFontWeight="400"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@+id/vip_price_ll"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="连续包月" />

    <LinearLayout
        android:id="@+id/vip_price_ll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="2dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="2dp"
        android:gravity="bottom|center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/vip_purchase_item_origin_price"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vip_purchase_item_name">

        <TextView
            android:id="@+id/vip_purchase_item_now_price_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="¥"
            android:textColor="@color/vip_color_fffa5f42"
            android:textSize="20dp"
            android:textStyle="bold" />

        <com.ximalaya.ting.android.vip.view.VipRunNumberTextView
            android:id="@+id/vip_purchase_item_now_price_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/vip_color_fffa5f42"
            android:textSize="32dp"
            android:textStyle="bold"
            android:visibility="visible"
            app:vip_rnt_auto="false"
            app:vip_rnt_decimal_text_size="20sp"
            app:vip_rnt_duration="1150"
            tools:text="20"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/vip_purchase_item_now_price_suffix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:includeFontPadding="false"
            android:text="起"
            android:textColor="#292B33"
            android:textFontWeight="400"
            android:textSize="12dp"
            android:visibility="gone"
            tools:textColor="@color/host_color_white_90"
            tools:visibility="visible" />

    </LinearLayout>

    <TextView
        android:id="@+id/vip_purchase_item_origin_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:ellipsize="end"
        android:gravity="center|top"
        android:maxLines="1"
        android:paddingLeft="3dp"
        android:paddingRight="3dp"
        android:paddingBottom="8dp"
        android:textColor="#9f9f9f"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vip_price_ll"
        tools:text="￥498"
        tools:visibility="invisible" />

    <TextView
        android:id="@+id/vip_purchase_upgrade_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@drawable/vip_bg_platinum_upgrade_month"
        android:drawableRight="@drawable/host_ic_filter_arrow_down"
        android:drawablePadding="6dp"
        android:drawableTint="#794A16"
        android:paddingHorizontal="10dp"
        app:layout_constrainedWidth="true"
        android:paddingVertical="4dp"
        android:layout_marginHorizontal="@dimen/host_x12"
        tools:text="1个月"
        android:ellipsize="end"
        android:maxLines="1"
        android:visibility="gone"
        tools:visibility="visible"
        android:textColor="#794A16"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/vip_purchase_item_origin_price" />

    <FrameLayout
        android:id="@+id/vip_purchase_selling_point_container"
        android:layout_width="0dp"
        android:layout_height="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="invisible">

        <View
            android:id="@+id/vip_purchase_item_selling_point_bg"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_gravity="bottom" />

        <TextView
            android:id="@+id/vip_purchase_item_description"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_gravity="bottom"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/host_x12"
            android:textColor="@color/host_color_regularTextColor"
            android:textSize="14dp"
            android:visibility="gone"
            tools:background="#f6f5f5"
            tools:text="次月续费¥20"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/vip_purchase_item_promotion"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_gravity="bottom|center"
            android:drawableRight="@drawable/host_ic_filter_arrow_down"
            android:drawablePadding="0dp"
            android:drawableTint="#000000"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingLeft="@dimen/host_x12"
            android:paddingRight="@dimen/host_x12"
            android:textColor="@color/host_color_regularTextColor"
            android:textSize="14dp"
            android:visibility="gone"
            tools:background="@drawable/vip_bg_bottom_sku_limit_tip"
            tools:text="用券已减14元"
            tools:visibility="visible" />
    </FrameLayout>


    <com.ximalaya.ting.android.host.view.XmLottieAnimationView
        android:id="@+id/main_iv_red_packet_lottie"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="5dp"
        android:scaleType="centerCrop"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="false"
        app:lottie_fileName="lottie/redpacket.json"
        app:lottie_repeatCount="0" />

</androidx.constraintlayout.widget.ConstraintLayout>